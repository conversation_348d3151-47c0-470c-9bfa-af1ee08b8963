#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar e corrigir o cálculo de horas trabalhadas
"""

import sys
import os
from datetime import datetime, time

# Adicionar o diretório do projeto ao path
sys.path.insert(0, '/var/www/controle-ponto')

def calcular_horas_trabalhadas_correto(entrada, saida_almoco, retorno_almoco, saida):
    """
    Função corrigida para calcular horas trabalhadas
    """
    try:
        # Converter strings para objetos time se necessário
        def str_to_time(t):
            if isinstance(t, str):
                return datetime.strptime(t, '%H:%M').time()
            return t
        
        entrada = str_to_time(entrada)
        saida_almoco = str_to_time(saida_almoco)
        retorno_almoco = str_to_time(retorno_almoco)
        saida = str_to_time(saida)
        
        # Converter time para datetime para cálculos
        def time_to_datetime(t):
            return datetime.combine(datetime.today(), t)
        
        entrada_dt = time_to_datetime(entrada)
        saida_almoco_dt = time_to_datetime(saida_almoco)
        retorno_almoco_dt = time_to_datetime(retorno_almoco)
        saida_dt = time_to_datetime(saida)
        
        # Período manhã: entrada até saída para almoço
        periodo_manha_seconds = (saida_almoco_dt - entrada_dt).total_seconds()
        periodo_manha_horas = periodo_manha_seconds / 3600
        
        # Período tarde: retorno do almoço até saída
        periodo_tarde_seconds = (saida_dt - retorno_almoco_dt).total_seconds()
        periodo_tarde_horas = periodo_tarde_seconds / 3600
        
        total_horas = periodo_manha_horas + periodo_tarde_horas
        
        # Converter para horas e minutos para verificação
        total_minutos = int(total_horas * 60)
        horas_int = total_minutos // 60
        minutos_int = total_minutos % 60
        
        print(f"📊 CÁLCULO DETALHADO:")
        print(f"   Entrada: {entrada}")
        print(f"   Saída Almoço: {saida_almoco}")
        print(f"   Retorno Almoço: {retorno_almoco}")
        print(f"   Saída: {saida}")
        print(f"")
        print(f"   Período Manhã: {periodo_manha_seconds/60:.1f} min = {periodo_manha_horas:.3f}h")
        print(f"   Período Tarde: {periodo_tarde_seconds/60:.1f} min = {periodo_tarde_horas:.3f}h")
        print(f"   Total: {total_horas:.3f}h = {horas_int}h{minutos_int:02d}min")
        print(f"   Total arredondado: {round(total_horas, 2)}h")
        
        return round(total_horas, 2)
        
    except Exception as e:
        print(f"❌ Erro no cálculo: {e}")
        return 0.0

def testar_funcao_atual():
    """Testa a função atual do sistema"""
    try:
        from app_ponto_admin import calcular_horas_trabalhadas_dia
        
        registro = {
            'entrada': time(8, 43),
            'saida_almoco': time(12, 56),
            'retorno_almoco': time(14, 10),
            'saida': time(17, 55)
        }
        
        resultado = calcular_horas_trabalhadas_dia(registro)
        print(f"🔍 FUNÇÃO ATUAL DO SISTEMA: {resultado}h")
        return resultado
        
    except Exception as e:
        print(f"❌ Erro ao testar função atual: {e}")
        return None

def main():
    """Função principal"""
    print("🧮 TESTE DE CÁLCULO DE HORAS TRABALHADAS")
    print("=" * 60)
    
    # Dados do exemplo problemático
    entrada = "08:43"
    saida_almoco = "12:56"
    retorno_almoco = "14:10"
    saida = "17:55"
    
    print(f"📋 DADOS DE TESTE:")
    print(f"   Entrada: {entrada}")
    print(f"   Saída Almoço: {saida_almoco}")
    print(f"   Retorno Almoço: {retorno_almoco}")
    print(f"   Saída: {saida}")
    print()
    
    # Teste com função corrigida
    print("🔧 FUNÇÃO CORRIGIDA:")
    resultado_correto = calcular_horas_trabalhadas_correto(entrada, saida_almoco, retorno_almoco, saida)
    print()
    
    # Teste com função atual
    print("🔍 FUNÇÃO ATUAL:")
    resultado_atual = testar_funcao_atual()
    print()
    
    # Comparação
    if resultado_atual is not None:
        diferenca = abs(resultado_correto - resultado_atual)
        print(f"📊 COMPARAÇÃO:")
        print(f"   Função Corrigida: {resultado_correto}h")
        print(f"   Função Atual: {resultado_atual}h")
        print(f"   Diferença: {diferenca:.3f}h")
        
        if diferenca > 0.01:  # Diferença significativa
            print(f"   ⚠️ DIFERENÇA DETECTADA! Correção necessária.")
        else:
            print(f"   ✅ Cálculos estão corretos.")
    
    # Teste adicional: verificar se 7.27h está correto
    print(f"\n🔍 ANÁLISE DO PROBLEMA REPORTADO:")
    print(f"   Valor reportado pelo sistema: 7.27h")
    print(f"   Valor esperado: 7.97h (7h58min)")
    print(f"   Diferença: {7.97 - 7.27:.2f}h = {(7.97 - 7.27) * 60:.0f} minutos")
    
    # Converter 7.27h para horas:minutos
    total_min_reportado = int(7.27 * 60)
    h_reportado = total_min_reportado // 60
    m_reportado = total_min_reportado % 60
    print(f"   7.27h = {h_reportado}h{m_reportado:02d}min")
    
    # Verificar se o problema está na conversão
    print(f"\n💡 POSSÍVEL CAUSA:")
    if resultado_atual == 7.27:
        print(f"   A função está retornando 7.27h, que corresponde a 7h16min")
        print(f"   Isso sugere erro no cálculo dos períodos ou na conversão")
    else:
        print(f"   A função atual retorna {resultado_atual}h, diferente do reportado")

if __name__ == "__main__":
    main()
