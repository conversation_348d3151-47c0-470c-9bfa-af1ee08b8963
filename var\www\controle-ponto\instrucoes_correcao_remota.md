# 🔧 INSTRUÇÕES PARA CORREÇÃO NO SERVIDOR REMOTO

**Data:** 05/06/2025  
**Problema:** P<PERSON>gina `/registro-ponto/manual` mostra "Nenhum funcionário encontrado"  
**Servidor:** 10.19.208.31  

## 📋 PASSO A PASSO DA CORREÇÃO

### 1. 🗄️ EXECUTAR DIAGNÓSTICO SQL

```bash
# No servidor remoto, execute:
mysql -u root -p controle_ponto < /caminho/para/correcao_funcionarios.sql
```

### 2. 🔧 CORRIGIR CÓDIGO PYTHON

**Arquivo:** `/var/www/controle-ponto/app_registro_ponto.py`  
**Função:** `pagina_registro_manual()` (aproximadamente linha 350-365)

**PROBLEMA IDENTIFICADO:**
A query atual usa `WHERE f.ativo = TRUE` mas no MySQL 8.0 deveria ser `WHERE f.ativo = 1`

**SUBSTITUIR ESTA QUERY:**
```python
cursor.execute("""
    SELECT 
        f.id,
        f.nome_completo,
        f.cpf,
        f.matricula_empresa,
        f.cargo,
        f.setor,
        f.foto_3x4,
        e.nome_fantasia AS empresa,
        ht.nome_horario AS nome_horario
    FROM funcionarios f
    LEFT JOIN empresas e ON f.empresa_id = e.id
    LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
    WHERE f.ativo = TRUE
    ORDER BY f.nome_completo
""")
```

**POR ESTA QUERY CORRIGIDA:**
```python
cursor.execute("""
    SELECT 
        f.id,
        f.nome_completo,
        COALESCE(f.cpf, 'N/A') as cpf,
        COALESCE(f.matricula_empresa, 'N/A') as matricula_empresa,
        COALESCE(f.cargo, 'N/A') as cargo,
        COALESCE(f.setor, f.setor_obra, 'N/A') as setor,
        f.foto_3x4,
        COALESCE(e.nome_fantasia, 'Empresa Padrão') AS empresa,
        COALESCE(ht.nome_horario, 'Horário Padrão') AS nome_horario
    FROM funcionarios f
    LEFT JOIN empresas e ON f.empresa_id = e.id
    LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
    WHERE (f.ativo = 1 OR f.status_cadastro = 'Ativo')
    ORDER BY f.nome_completo
""")
```

### 3. 🧪 ADICIONAR ROTA DE TESTE TEMPORÁRIA

**Adicione no final do arquivo `app_registro_ponto.py`:**

```python
# ✅ ROTA TEMPORÁRIA PARA TESTAR CORREÇÃO
@registro_ponto_bp.route('/manual-teste')
@require_login
def pagina_registro_manual_teste():
    """Versão de teste da página manual com query corrigida"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Query corrigida
        cursor.execute("""
            SELECT 
                f.id,
                f.nome_completo,
                COALESCE(f.cpf, 'N/A') as cpf,
                COALESCE(f.matricula_empresa, 'N/A') as matricula_empresa,
                COALESCE(f.cargo, 'N/A') as cargo,
                COALESCE(f.setor, f.setor_obra, 'N/A') as setor,
                f.foto_3x4,
                'Empresa Padrão' AS empresa,
                'Horário Padrão' AS nome_horario
            FROM funcionarios f
            WHERE (f.ativo = 1 OR f.status_cadastro = 'Ativo')
            ORDER BY f.nome_completo
        """)
        
        funcionarios_raw = cursor.fetchall()
        conn.close()
        
        funcionarios = []
        for f in funcionarios_raw:
            funcionario = {
                'id': f[0],
                'nome_completo': f[1],
                'cpf': f[2],
                'cpf_exibicao': f[2][:3] + '.***.***-' + f[2][-2:] if f[2] != 'N/A' else 'N/A',
                'matricula_empresa': f[3],
                'cargo': f[4],
                'setor': f[5],
                'foto_url': '/static/images/funcionario_sem_foto.svg',
                'empresa': f[6],
                'horario_trabalho': f[7]
            }
            funcionarios.append(funcionario)
        
        from datetime import datetime
        context = {
            'titulo': 'Registro Manual - TESTE CORRIGIDO',
            'funcionarios': funcionarios,
            'total_funcionarios': len(funcionarios),
            'data_atual': datetime.now().strftime('%d/%m/%Y'),
            'hora_atual': datetime.now().strftime('%H:%M'),
            'nivel_acesso': 'admin'
        }
        
        return render_template('registro_ponto/manual.html', **context)
        
    except Exception as e:
        import traceback
        return f"<h1>Erro no teste: {str(e)}</h1><pre>{traceback.format_exc()}</pre>"
```

### 4. 🔄 REINICIAR SERVIDOR WEB

**Após fazer as alterações, execute:**

```bash
# Para Apache
sudo systemctl restart apache2
# OU
sudo service apache2 restart

# Para Nginx
sudo systemctl restart nginx
# OU  
sudo service nginx restart
```

### 5. 🧪 TESTAR A CORREÇÃO

1. **Primeiro teste a rota temporária:**
   - Acesse: `http://10.19.208.31/registro-ponto/manual-teste`
   - Deve mostrar a lista de funcionários

2. **Se funcionar, teste a página original:**
   - Acesse: `http://10.19.208.31/registro-ponto/manual`
   - Deve mostrar a lista de funcionários

3. **Após confirmar que funciona, remova a rota de teste temporária**

## 🎯 RESUMO DO PROBLEMA

- **Causa:** Query SQL usava `ativo = TRUE` (boolean) ao invés de `ativo = 1` (integer)
- **Solução:** Mudança para `ativo = 1` e adição de `COALESCE` para campos NULL
- **Backup:** Arquivos originais foram salvos com extensão `.backup_YYYYMMDD_HHMMSS`

## ⚠️ OBSERVAÇÕES IMPORTANTES

1. **Backup automático:** O sistema criará backup do arquivo original antes da alteração
2. **Teste primeiro:** Use sempre a rota `/manual-teste` antes de alterar a principal
3. **Logs:** Monitore os logs do servidor durante o teste
4. **Rollback:** Se algo der errado, restaure o backup

---

**📧 Em caso de dúvidas, relate o erro e contexto completo para análise.** 