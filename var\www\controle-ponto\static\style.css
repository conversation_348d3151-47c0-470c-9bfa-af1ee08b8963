* {
    box-sizing: border-box;
}
body {
    margin: 0;
    background: linear-gradient(180deg, #0b0c10, #1f2833);
    font-family: 'Segoe UI', Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    color: #fff;
}
.container {
    background-color: #1f2833;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,255,255,0.2);
    width: 100%;
    max-width: 600px;
}
h2 {
    text-align: center;
    color: #66fcf1;
    font-size: 24px;
}
h3 {
    color: #66fcf1;
    font-size: 18px;
    margin-top: 20px;
}
label {
    display: block;
    margin-top: 15px;
    font-weight: bold;
}
input, select, textarea {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    border: none;
    border-radius: 5px;
    background-color: #c5c6c7;
    color: #0b0c10;
}
button {
    margin-top: 20px;
    width: 100%;
    padding: 12px;
    background-color: #45a29e;
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    font-size: 16px;
}
button:hover {
    background-color: #3b8c88;
}
.erro {
    margin-top: 15px;
    color: #ff4d4d;
    text-align: center;
}
.mensagem {
    margin-top: 15px;
    color: #66fcf1;
    text-align: center;
}
.nav {
    margin-bottom: 20px;
}
a {
    color: #66fcf1;
    text-decoration: none;
    margin-right: 10px;
}
a:hover {
    color: #45a29e;
}
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}
th, td {
    padding: 10px;
    text-align: left;
    border: 1px solid #66fcf1;
    color: #fff;
}
th {
    background-color: #45a29e;
}
td form {
    display: flex;
    gap: 10px;
}
td form select {
    width: 100px;
}
td form button {
    width: auto;
    padding: 5px 10px;
    margin-top: 0;
}
.action-buttons {
    display: flex;
    gap: 10px;
}
.action-buttons button {
    width: auto;
    padding: 5px 10px;
    margin-top: 0;
}
.popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #1f2833;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,255,255,0.2);
    z-index: 1000;
    width: 300px;
}
.popup-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.popup-content h3 {
    margin: 0;
    text-align: center;
}
.popup-content input {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 5px;
    background-color: #c5c6c7;
    color: #0b0c10;
}
.popup-content button {
    width: 100%;
    padding: 10px;
    background-color: #45a29e;
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
}
.popup-content button:hover {
    background-color: #3b8c88;
}
.popup-content .close {
    background-color: #ff4d4d;
    padding: 8px; /* Tamanho reduzido */
    font-size: 14px; /* Tamanho da fonte reduzido */
}
.popup-content .close:hover {
    background-color: #e04343;
}
.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
}
@media (max-width: 480px) {
    .container {
        padding: 20px;
    }
    h2 {
        font-size: 20px;
    }
    button {
        padding: 10px;
        font-size: 14px;
    }
}

/* ========================================
   ESTILOS PARA SISTEMA BIOMÉTRICO ANTI-SIMULAÇÃO
   ======================================== */

/* Alerta de simulação detectada - crítico */
.alerta-simulacao {
    animation: pulse-critical 2s infinite;
    border-left: 5px solid #dc3545 !important;
    box-shadow: 0 0 15px rgba(220, 53, 69, 0.3);
    font-weight: bold;
}

.alerta-simulacao.alert-danger {
    background-color: #f8d7da !important;
    border-color: #dc3545 !important;
    color: #721c24 !important;
}

.alerta-simulacao.alert-warning {
    background-color: #fff3cd !important;
    border-color: #ffc107 !important;
    color: #856404 !important;
}

/* Status crítico para simulação */
.status.critical {
    background-color: #dc3545 !important;
    color: white !important;
    font-weight: bold;
    animation: pulse-critical 2s infinite;
    border: 2px solid #a71e2a;
}

.status.warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
    font-weight: bold;
    animation: pulse-warning 3s infinite;
}

/* Scanner em estado crítico */
.scanner.critical-error {
    border: 3px solid #dc3545 !important;
    background-color: #f8d7da !important;
    animation: shake 0.5s infinite;
}

.scanner.critical-error::before {
    content: "🚨";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 48px;
    z-index: 10;
}

.scanner.warning {
    border: 3px solid #ffc107 !important;
    background-color: #fff3cd !important;
    animation: pulse-warning 2s infinite;
}

.scanner.warning::before {
    content: "⚠️";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 48px;
    z-index: 10;
}

/* Animações */
@keyframes pulse-critical {
    0% { 
        box-shadow: 0 0 5px rgba(220, 53, 69, 0.5);
    }
    50% { 
        box-shadow: 0 0 20px rgba(220, 53, 69, 0.8);
    }
    100% { 
        box-shadow: 0 0 5px rgba(220, 53, 69, 0.5);
    }
}

@keyframes pulse-warning {
    0% { 
        box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
    }
    50% { 
        box-shadow: 0 0 15px rgba(255, 193, 7, 0.8);
    }
    100% { 
        box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* Códigos de template com scroll */
.alerta-simulacao code {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-all;
}

/* Instruções de simulação */
.instrucoes.simulacao-detectada {
    background-color: #f8d7da;
    border: 2px solid #dc3545;
    color: #721c24;
    font-weight: bold;
}

.instrucoes.template-suspeito {
    background-color: #fff3cd;
    border: 2px solid #ffc107;
    color: #856404;
    font-weight: bold;
}
