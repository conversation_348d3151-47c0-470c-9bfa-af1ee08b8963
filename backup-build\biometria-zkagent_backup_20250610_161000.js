/**
 * Cliente JavaScript para ZKAgent Biométrico - VERSÃO VISUAL MELHORADA
 * Comunicação com serviço ZKAgent na porta 5001
 * 
 * Autor: Sistema RLPONTO-WEB
 * Data: Junho 2025
 */

class ZKAgentBiometria {
    constructor() {
        // SEMPRE conecta local primeiro - ZKAgent roda no PC do usuário
        this.zkagentUrl = 'http://localhost:5001';
        this.isCapturing = false;
        this.callbacks = {};
        this.templates = {
            dedo1: null,
            dedo2: null
        };
        this.qualidades = {
            dedo1: 0,
            dedo2: 0
        };
        
        console.log('🔐 ZKAgent URL: SEMPRE LOCAL → http://localhost:5001');
        console.log('🔐 Estratégia: CONEXÃO DIRETA (Hardware local)');
    }

    /**
     * Detecta URL do ZKAgent - SEMPRE LOCAL
     * O ZKAgent DEVE rodar no PC onde está o leitor ZK4500
     */
    detectarUrlZKAgent() {
        // SEMPRE localhost - o leitor está no PC do usuário
        return 'http://localhost:5001';
    }

    /**
     * Faz requisição EXCLUSIVAMENTE no ZKAgent LOCAL
     * Hardware ZK4500 está conectado no PC local, não no servidor!
     */
    async makeRequest(endpoint, options = {}) {
        const url = `http://localhost:5001${endpoint}`;
        console.log('🔄 Conectando ZKAgent LOCAL:', url);
        
        try {
            const response = await fetch(url, {
                ...options,
                mode: 'cors',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });
            
            console.log('✅ ZKAgent Local Response:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`ZKAgent LOCAL retornou ${response.status}: ${response.statusText}`);
            }
            
            return response;
            
        } catch (error) {
            console.error('❌ ZKAgent LOCAL falhou:', error.message);
            
            // ✅ CORREÇÃO: Sem fallback para servidor remoto!
            // O hardware ZK4500 está LOCAL, não remoto!
            if (error.message.includes('Failed to fetch') || error.message.includes('ERR_CONNECTION_REFUSED')) {
                throw new Error(`❌ ZKAgent LOCAL não está rodando!\n\n🔧 SOLUÇÕES:\n1. Verifique se ZKAgent está ativo na bandeja do sistema\n2. Execute: 'curl http://localhost:5001/test'\n3. Se não responder, reinicie o ZKAgent Professional\n4. Verifique se a porta 5001 não está bloqueada\n5. Hardware ZK4500 deve estar conectado NO SEU PC\n\n📍 O ZKAgent DEVE rodar localmente porque o hardware está em seu computador!`);
            }
            
            // Outros erros específicos
            throw new Error(`ZKAgent LOCAL: ${error.message}\n\nVerifique:\n1. ZKAgent Professional está rodando?\n2. Hardware ZK4500 conectado localmente?\n3. Porta 5001 disponível?\n4. Execute: curl http://localhost:5001/test`);
        }
    }

    /**
     * Testa conexão com ZKAgent
     */
    async testarConexao() {
        try {
            const response = await this.makeRequest('/test', {
                method: 'GET',
                timeout: 5000
            });
            
            const responseText = await response.text();
            console.log('ZKAgent response:', responseText);
            
            // Tenta parsear como JSON primeiro, senão verifica se contém "ok"
            try {
                const data = JSON.parse(responseText);
                return data.status === 'ok';
            } catch (jsonError) {
                // Se não for JSON, verifica se a resposta contém "ok"
                return responseText.toLowerCase().includes('ok');
            }
            
        } catch (error) {
            console.error('Erro ao testar ZKAgent:', error);
            return false;
        }
    }

    /**
     * Verifica dispositivos biométricos conectados
     */
    async verificarDispositivos() {
        try {
            const response = await this.makeRequest('/list-devices', {
                method: 'GET',
                timeout: 5000
            });
            
            const responseText = await response.text();
            console.log('ZKAgent list-devices response:', responseText);
            
            // Tenta parsear como JSON primeiro
            try {
                const data = JSON.parse(responseText);
                return data.devices || 0;
            } catch (jsonError) {
                // Se não for JSON, procura por número na resposta
                const match = responseText.match(/(\d+)/);
                return match ? parseInt(match[1]) : 0;
            }
            
        } catch (error) {
            console.error('Erro ao verificar dispositivos:', error);
            return 0;
        }
    }

    /**
     * Captura impressão digital - NUNCA ACEITA SIMULAÇÃO (exceto modo desenvolvimento)
     */
    async capturarDigital() {
        if (this.isCapturing) {
            throw new Error('Captura já em andamento');
        }

        this.isCapturing = true;
        
        try {
            console.log('🔄 Tentativa 1: Captura biométrica REAL');
            let response = await this.makeRequest('/capture', {
                method: 'POST',
                body: JSON.stringify({
                    timeout: 10
                })
            });
            
            let responseText = await response.text();
            console.log('ZKAgent capture response (tentativa 1):', responseText);
            
            if (!response.ok) {
                console.log('🔄 Tentativa 2: Captura com timeout 30s');
                response = await this.makeRequest('/capture', {
                    method: 'POST',
                    body: JSON.stringify({
                        timeout: 30
                    })
                });
                responseText = await response.text();
                console.log('ZKAgent capture response (tentativa 2):', responseText);
            }
            
            // Tenta parsear como JSON primeiro
            try {
                const data = JSON.parse(responseText);
                
                // Verifica se há erro na resposta
                if (data.error) {
                    // Tratamento específico para diferentes tipos de erro
                    if (data.error.includes('Falha ao abrir o dispositivo') || data.error.includes('OpenDevice falhou')) {
                        throw new Error(`HARDWARE ZK4500: ${data.error}\n\nSoluções:\n1. Desconecte e reconecte o cabo USB\n2. Execute como administrador: Gerenciador de Dispositivos\n3. Verifique se outro programa está usando o ZK4500\n4. Reinicie o serviço ZKAgent\n5. Teste: curl localhost:5001/capture`);
                    }
                    
                    // Tratamento específico para erro -8 (falha na captura)
                    else if (data.error.includes('Falha na captura: -8') || data.error.includes('captura: -8')) {
                        throw new Error(`ERRO SDK ZK4500: Falha na captura biométrica (código -8)\n\nCausas prováveis:\n1. Dedo não posicionado corretamente no sensor\n2. Sensor sujo - limpe com álcool isopropílico\n3. SDK ZKFinger incompatível com hardware\n4. Driver ZK4500 com problemas\n\nSoluções:\n1. Limpe o sensor e tente novamente\n2. Posicione o dedo firmemente no centro\n3. Reinstale o SDK ZKFinger mais recente\n4. Verifique driver no Gerenciador de Dispositivos`);
                    }
                    
                    // Outros erros SDK
                    else if (data.error.includes('Falha na captura:') && data.error.match(/-?\d+/)) {
                        const errorCode = data.error.match(/-?\d+/)[0];
                        throw new Error(`ERRO SDK ZK4500: ${data.error}\n\nCódigo: ${errorCode}\nVerifique:\n1. Hardware ZK4500 conectado\n2. Driver funcionando\n3. SDK ZKFinger atualizado\n4. Teste: curl localhost:5001/capture`);
                    }
                    
                    // Erro genérico
                    throw new Error(`ZKAgent: ${data.error}`);
                }
                
                // 🌐 DETECÇÃO DO NOSSO SIMULADOR ESPECÍFICO 🌐
                if (data.device === 'ZK4500-SIMULADO' || (data.message && data.message.includes('SIMULAD'))) {
                    console.log('🌐 SIMULADOR DETECTADO - Modo desenvolvimento ativo');
                    
                    // Retorna dados simulados com flag especial
                    return {
                        success: true,
                        template: data.template,
                        quality: data.quality || 0,
                        image: data.image,
                        timestamp: data.timestamp || new Date().toISOString(),
                        simulationMode: true, // Flag especial
                        user: data.user || null,
                        identified: data.identified || false
                    };
                }
                
                // ⚠️ VERIFICAÇÃO RIGOROSA ANTI-SIMULAÇÃO (outros simuladores) ⚠️
                if (data.template && typeof data.template === 'string') {
                    // Decodifica base64 para verificar se é simulado
                    try {
                        const decoded = atob(data.template);
                        
                        // Lista completa de indicadores de simulação
                        const indicadoresSimulacao = [
                            'MODO_COMPATIVEL',
                            'SIMULACAO', 
                            'TEMPLATE_SIM',
                            'DEMO_MODE',
                            'FAKE_TEMPLATE',
                            'ZKAgent_Professional',
                            'Instale_ZKFinger_SDK',
                            'modo_compativel',
                            'simulation',
                            'template_simulado',
                            'fallback_mode'
                        ];
                        
                        // Verifica se contém qualquer indicador de simulação
                        const isSimulacao = indicadoresSimulacao.some(indicador => 
                            decoded.toLowerCase().includes(indicador.toLowerCase())
                        );
                        
                        if (isSimulacao) {
                            // ❌ REJEITA COMPLETAMENTE TEMPLATES SIMULADOS (exceto nosso simulador)
                            throw new Error(`SIMULAÇÃO_DETECTADA:${decoded}`);
                        }
                        
                        // Verificação adicional: templates reais têm características específicas
                        // Templates ZK4500 reais geralmente são binários com tamanho específico
                        if (decoded.length < 100 || decoded.includes(':')) {
                            // Templates muito pequenos ou com ':' são suspeitos
                            console.warn('⚠️ Template suspeito detectado:', decoded.substring(0, 100));
                            throw new Error(`TEMPLATE_SUSPEITO:${decoded}`);
                        }
                        
                    } catch (decodeError) {
                        // Se falhar ao decodificar, pode ser template real binário válido
                        if (decodeError.message.startsWith('SIMULAÇÃO_DETECTADA:') || 
                            decodeError.message.startsWith('TEMPLATE_SUSPEITO:')) {
                            throw decodeError; // Re-propaga erros de simulação
                        }
                        console.log('✅ Template não é base64 texto (provavelmente template real binário)');
                    }
                }
                
                // Verifica resposta de sucesso com dados reais
                if (data.template || data.success) {
                    console.log('✅ Captura REAL realizada com sucesso');
                    return {
                        success: true,
                        template: data.template,
                        quality: data.quality || data.size || 0,
                        image: data.image,
                        timestamp: data.timestamp || new Date().toISOString()
                    };
                } else {
                    throw new Error('Resposta de captura sem dados válidos: ' + responseText);
                }
                
            } catch (jsonError) {
                // Verifica se é erro de simulação detectada
                if (jsonError.message.startsWith('SIMULAÇÃO_DETECTADA:')) {
                    const templateSimulado = jsonError.message.replace('SIMULAÇÃO_DETECTADA:', '');
                    throw new Error(`ZKAGENT_SIMULACAO:${templateSimulado}`);
                }
                
                if (jsonError.message.startsWith('TEMPLATE_SUSPEITO:')) {
                    const templateSuspeito = jsonError.message.replace('TEMPLATE_SUSPEITO:', '');
                    throw new Error(`ZKAGENT_SUSPEITO:${templateSuspeito}`);
                }
                
                // Se o erro foi de parsing JSON, verifica se é erro conhecido
                if (jsonError.message.includes('ZK4500') || jsonError.message.includes('SDK') || jsonError.message.includes('HARDWARE')) {
                    throw jsonError;
                }
                
                // Erro de parsing JSON - resposta inválida
                throw new Error(`Resposta ZKAgent inválida - não é JSON válido: ${responseText}\n\nFormato esperado: JSON com 'template' ou 'error'\nRecebido: ${responseText.substring(0, 100)}...\n\nVerifique:\n1. ZKAgent está funcionando corretamente\n2. Hardware ZK4500 conectado\n3. SDK ZKFinger instalado`);
            }
            
        } catch (error) {
            console.error('Erro na captura:', error);
            throw error;
        } finally {
            this.isCapturing = false;
        }
    }

    /**
     * Salva template biométrico para um dedo específico
     */
    salvarTemplate(numeroImpressao, template, qualidade) {
        if (numeroImpressao === 1) {
            this.templates.dedo1 = template;
            this.qualidades.dedo1 = qualidade;
        } else if (numeroImpressao === 2) {
            this.templates.dedo2 = template;
            this.qualidades.dedo2 = qualidade;
        }
    }

    /**
     * Obtém templates salvos
     */
    getTemplates() {
        return {
            dedo1: this.templates.dedo1,
            dedo2: this.templates.dedo2,
            qualidade1: this.qualidades.dedo1,
            qualidade2: this.qualidades.dedo2
        };
    }

    /**
     * Limpa templates salvos
     */
    limparTemplates() {
        this.templates.dedo1 = null;
        this.templates.dedo2 = null;
        this.qualidades.dedo1 = 0;
        this.qualidades.dedo2 = 0;
    }

    /**
     * Registra callback para eventos
     */
    on(evento, callback) {
        if (!this.callbacks[evento]) {
            this.callbacks[evento] = [];
        }
        this.callbacks[evento].push(callback);
    }

    /**
     * Dispara evento
     */
    emit(evento, dados) {
        if (this.callbacks[evento]) {
            this.callbacks[evento].forEach(callback => {
                try {
                    callback(dados);
                } catch (error) {
                    console.error(`Erro no callback ${evento}:`, error);
                }
            });
        }
    }

    /**
     * Diagnóstico completo do sistema ZKAgent + ZK4500 LOCAL
     */
    async diagnosticarSistema() {
        const diagnostico = {
            timestamp: new Date().toISOString(),
            zkagent_status: null,
            devices_count: null,
            device_details: null,
            connection_method: 'DIRETA_LOCAL',
            error_details: null,
            zkagent_url: 'http://localhost:5001'
        };

        try {
            console.log('🔍 === DIAGNÓSTICO SISTEMA BIOMÉTRICO LOCAL ===');
            console.log('🔍 ZKAgent deve estar rodando em: localhost:5001');
            
            // 1. Testar conexão ZKAgent LOCAL
            console.log('🔍 1. Testando ZKAgent local...');
            try {
                const statusResponse = await this.makeRequest('/test');
                const statusText = await statusResponse.text();
                diagnostico.zkagent_status = statusText;
                console.log('🔍 ✅ ZKAgent Local Status:', statusText);
            } catch (e) {
                diagnostico.zkagent_status = `Erro: ${e.message}`;
                console.log('🔍 ❌ ZKAgent Local FALHOU:', e.message);
            }
            
            // 2. Listar dispositivos LOCAL
            console.log('🔍 2. Verificando dispositivos ZK4500 locais...');
            try {
                const devicesResponse = await this.makeRequest('/list-devices');
                const devicesText = await devicesResponse.text();
                diagnostico.devices_count = devicesText;
                console.log('🔍 ✅ Dispositivos locais:', devicesText);
            } catch (e) {
                diagnostico.devices_count = `Erro: ${e.message}`;
                console.log('🔍 ❌ Dispositivos locais FALHOU:', e.message);
            }
            
            // 3. Verificar detalhes do dispositivo LOCAL (opcional)
            console.log('🔍 3. Detalhes do hardware local...');
            try {
                const detailsResponse = await this.makeRequest('/device-info');
                const detailsText = await detailsResponse.text();
                diagnostico.device_details = detailsText;
                console.log('🔍 ✅ Device Info local:', detailsText);
            } catch (e) {
                console.log('🔍 ⚠️ Device Info não disponível (opcional):', e.message);
                diagnostico.device_details = 'Não disponível (opcional)';
            }
            
            // 4. Teste de captura LOCAL
            console.log('🔍 4. Testando captura biométrica local...');
            try {
                const captureResponse = await this.makeRequest('/capture', {
                    method: 'POST',
                    body: JSON.stringify({
                        debug: true,
                        timeout: 3 // timeout curto para diagnóstico
                    })
                });
                const captureText = await captureResponse.text();
                console.log('🔍 Resultado captura local:', captureText);
                diagnostico.capture_test = captureText;
            } catch (e) {
                console.log('🔍 ❌ Captura local FALHOU:', e.message);
                diagnostico.capture_test = `Erro: ${e.message}`;
            }

            console.log('🔍 === DIAGNÓSTICO LOCAL COMPLETO ===');
            console.log('🔍 Resumo: ZKAgent + ZK4500 devem estar no PC local');
            console.log(diagnostico);
            
            return diagnostico;
            
        } catch (error) {
            console.error('🔍 Erro no diagnóstico local:', error);
            diagnostico.error_details = error.message;
            return diagnostico;
        }
    }

    /**
     * Tentativa de recuperação automática do ZK4500 LOCAL
     */
    async tentarRecuperacao() {
        console.log('🔄 === RECUPERAÇÃO AUTOMÁTICA ZK4500 LOCAL ===');
        
        try {
            // 1. Verificar se ZKAgent local está respondendo
            console.log('🔄 1. Verificando ZKAgent local...');
            const statusOk = await this.testarConexao();
            
            if (!statusOk) {
                console.log('🔄 ❌ ZKAgent local não responde - verifique instalação');
                return false;
            }
            
            // 2. Reinicializar dispositivo local (se endpoint existir)
            console.log('🔄 2. Tentando reset do dispositivo...');
            try {
                await this.makeRequest('/reset', { method: 'POST' });
                await this.sleep(3000);
                console.log('🔄 ✅ Reset executado');
            } catch (e) {
                console.log('🔄 ⚠️ Reset não disponível:', e.message);
            }
            
            // 3. Verificar se dispositivo voltou
            console.log('🔄 3. Verificando dispositivos após recuperação...');
            const devices = await this.verificarDispositivos();
            console.log('🔄 Dispositivos após recuperação:', devices);
            
            if (devices > 0) {
                console.log('🔄 ✅ Dispositivo detectado - tentando captura teste...');
                try {
                    await this.capturarDigital();
                    console.log('🔄 ✅ Recuperação bem-sucedida!');
                    return true;
                } catch (e) {
                    console.log('🔄 ❌ Captura ainda falha:', e.message);
                    return false;
                }
            }
            
            console.log('🔄 ❌ Nenhum dispositivo detectado após recuperação');
            return false;
            
        } catch (error) {
            console.error('🔄 Erro na recuperação local:', error);
            return false;
        }
    }
}

/**
 * Gerenciador do Modal Visual de Biometria
 */
class ModalBiometriaVisual {
    constructor() {
        this.zkAgent = new ZKAgentBiometria();
        this.etapaAtual = 0; // 0 = inicial, 1 = conexão, 2 = dedo1, 3 = dedo2, 4 = concluído
        this.templates = { dedo1: null, dedo2: null };
        this.qualidades = { dedo1: 0, dedo2: 0 };
        
        this.init();
    }

    init() {
        // Elementos do modal
        this.modal = document.getElementById('modalBiometria');
        this.statusText = document.getElementById('statusBiometria');
        this.scannerArea = document.getElementById('scannerArea');
        this.scannerPulse = document.getElementById('scannerPulse');
        this.instructionCard = document.getElementById('instructionCard');
        this.capturasResultado = document.getElementById('capturasResultado');
        this.btnIniciar = document.getElementById('btnIniciarCaptura');
        this.btnSalvar = document.getElementById('btnSalvarBiometria');
    }

    /**
     * Abre o modal
     */
    abrir() {
        this.modal.style.display = 'flex';
        this.resetarEstados();
        this.atualizarProgresso(1);
        this.atualizarStatus('Clique em "Iniciar Captura" para começar', 'default');
    }

    /**
     * Fecha o modal
     */
    fechar() {
        this.modal.style.display = 'none';
        this.resetarEstados();
    }

    /**
     * Reseta todos os estados visuais
     */
    resetarEstados() {
        this.etapaAtual = 0;
        this.templates = { dedo1: null, dedo2: null };
        this.qualidades = { dedo1: 0, dedo2: 0 };
        
        // Reset visual
        this.scannerArea.className = 'scanner-area';
        this.capturasResultado.style.display = 'none';
        this.btnIniciar.style.display = 'inline-block';
        this.btnSalvar.style.display = 'none';
        
        this.resetarResultados();
    }

    /**
     * Atualiza a barra de progresso
     */
    atualizarProgresso(etapa) {
        // Remove classes anteriores
        for (let i = 1; i <= 4; i++) {
            const step = document.getElementById(`step-${i}`);
            step.classList.remove('active', 'completed');
        }
        
        // Marca etapas concluídas
        for (let i = 1; i < etapa; i++) {
            document.getElementById(`step-${i}`).classList.add('completed');
        }
        
        // Marca etapa atual
        if (etapa <= 4) {
            document.getElementById(`step-${etapa}`).classList.add('active');
        }
        
        this.etapaAtual = etapa;
    }

    /**
     * Atualiza o status principal
     */
    atualizarStatus(mensagem, tipo = 'default') {
        this.statusText.textContent = mensagem;
        this.statusText.className = `status-text ${tipo}`;
        
        if (tipo === 'error' || tipo === 'critical') {
            this.statusText.innerHTML = `<span class="loading-spinner" style="border-top-color: #dc3545;"></span>${mensagem}`;
        } else if (tipo === 'info') {
            this.statusText.innerHTML = `<span class="loading-spinner"></span>${mensagem}`;
        } else if (tipo === 'simulation') {
            // Ícone especial para simulação
            this.statusText.innerHTML = `🌐 ${mensagem}`;
            this.statusText.style.color = '#17a2b8'; // Azul info
            this.statusText.style.fontWeight = 'bold';
        } else if (tipo === 'warning') {
            this.statusText.innerHTML = `⚠️ ${mensagem}`;
            this.statusText.style.color = '#ffc107'; // Amarelo warning
        } else if (tipo === 'success') {
            this.statusText.innerHTML = `✅ ${mensagem}`;
            this.statusText.style.color = '#28a745'; // Verde success
        }
    }

    /**
     * Atualiza estado visual do scanner
     */
    atualizarScanner(estado) {
        this.scannerArea.className = `scanner-area ${estado}`;
        
        if (estado === 'scanning' || estado === 'capturing') {
            this.scannerArea.classList.add('scanning');
        } else {
            this.scannerArea.classList.remove('scanning');
        }
        
        // Estados visuais especiais
        if (estado === 'simulation') {
            this.scannerArea.style.borderColor = '#17a2b8'; // Azul para simulação
            this.scannerArea.style.backgroundColor = 'rgba(23, 162, 184, 0.1)';
        } else if (estado === 'critical-error') {
            this.scannerArea.style.borderColor = '#dc3545'; // Vermelho para erro crítico
            this.scannerArea.style.backgroundColor = 'rgba(220, 53, 69, 0.1)';
        } else if (estado === 'warning') {
            this.scannerArea.style.borderColor = '#ffc107'; // Amarelo para aviso
            this.scannerArea.style.backgroundColor = 'rgba(255, 193, 7, 0.1)';
        } else {
            // Reset para estado padrão
            this.scannerArea.style.borderColor = '';
            this.scannerArea.style.backgroundColor = '';
        }
    }

    /**
     * Atualiza instruções
     */
    atualizarInstrucoes(titulo, lista) {
        const h4 = this.instructionCard.querySelector('h4');
        const ul = this.instructionCard.querySelector('ul');
        
        h4.textContent = titulo;
        ul.innerHTML = '';
        
        lista.forEach(item => {
            const li = document.createElement('li');
            li.textContent = item;
            ul.appendChild(li);
        });
    }

    /**
     * Atualiza resultado de um dedo
     */
    atualizarResultado(dedo, status, qualidade = 0) {
        const card = document.getElementById(`resultadoDedo${dedo}`);
        const badge = card.querySelector('.status-badge');
        const fill = card.querySelector('.qualidade-fill');
        const valor = card.querySelector('.qualidade-valor');
        
        // Atualiza status
        badge.className = `status-badge ${status}`;
        badge.textContent = this.getStatusText(status);
        
        // Atualiza qualidade
        fill.style.width = `${qualidade}%`;
        valor.textContent = `${qualidade}%`;
        
        if (status === 'success') {
            card.classList.add('success');
        }
    }

    /**
     * Converte status para texto
     */
    getStatusText(status) {
        const statusMap = {
            'pending': 'Aguardando',
            'capturing': 'Capturando...',
            'success': 'Capturado',
            'success-simulation': '🌐 Simulado',
            'error': 'Erro'
        };
        return statusMap[status] || status;
    }

    /**
     * Reset dos resultados
     */
    resetarResultados() {
        this.atualizarResultado(1, 'pending', 0);
        this.atualizarResultado(2, 'pending', 0);
        
        document.getElementById('resultadoDedo1').classList.remove('success');
        document.getElementById('resultadoDedo2').classList.remove('success');
    }

    /**
     * Inicia captura biométrica com diagnóstico automático
     */
    async iniciarCaptura() {
        this.resetarEstados();
        this.abrir();
        
        try {
            // Etapa 1: Verificar conexão
            this.atualizarProgresso('conexao');
            this.atualizarStatus('Conectando ao ZKAgent...', 'info');
            
            const conexaoOk = await this.zkAgent.testarConexao();
            if (!conexaoOk) {
                throw new Error('ZKAgent não está respondendo. Verifique se o serviço está rodando na porta 5001.');
            }
            
            // Etapa 2: Verificar dispositivos
            this.atualizarProgresso('dispositivos');
            this.atualizarStatus('Verificando dispositivos biométricos...', 'info');
            
            const dispositivos = await this.zkAgent.verificarDispositivos();
            if (dispositivos === 0) {
                throw new Error('Nenhum dispositivo biométrico detectado. Verifique se o ZK4500 está conectado via USB.');
            }
            
            // Etapa 3: Preparar captura
            this.atualizarProgresso('preparacao');
            this.atualizarStatus(`${dispositivos} dispositivo(s) detectado(s). Preparando captura...`, 'success');
            
            await this.sleep(1000);
            
            // Etapa 4: Capturar biometria
            this.atualizarProgresso('captura');
            this.atualizarInstrucoes('Captura Biométrica', [
                'Coloque seu dedo no leitor ZK4500',
                'Mantenha o dedo imóvel até ouvir o bipe',
                'Aguarde a validação da qualidade'
            ]);
            
            // Capturar ambos os dedos
            await this.capturarDedo(1);
            await this.capturarDedo(2);
            
            // Etapa 5: Finalizar
            this.atualizarProgresso('sucesso');
            this.atualizarStatus('Biometria capturada com sucesso!', 'success');
            this.salvarBiometria();
            
        } catch (error) {
            console.error('Erro na captura biométrica:', error);
            
            // DIAGNÓSTICO AUTOMÁTICO quando houver erro
            console.log('🔍 Iniciando diagnóstico automático devido ao erro...');
            await this.executarDiagnostico();
            
            this.atualizarProgresso('erro');
            this.atualizarStatus(`Erro: ${error.message}`, 'error');
            
            // Mostrar botão de diagnóstico manual
            this.mostrarBotaoDiagnostico();
        }
    }

    /**
     * Captura um dedo específico
     */
    async capturarDedo(numeroDedo) {
        this.atualizarProgresso(numeroDedo + 1);
        this.atualizarResultado(numeroDedo, 'capturing');
        this.atualizarScanner('capturing');
        
        this.atualizarStatus(`Coloque o dedo ${numeroDedo} no leitor ZK4500...`, 'info');
        this.atualizarInstrucoes(`Captura - Dedo ${numeroDedo}`, [
            'Coloque o dedo centralmente no leitor',
            'Aplique pressão constante',
            'Mantenha o dedo imóvel',
            'Aguarde o sinal de confirmação'
        ]);
        
        try {
            const resultado = await this.zkAgent.capturarDigital();
            
            if (resultado.success) {
                this.templates[`dedo${numeroDedo}`] = resultado.template;
                this.qualidades[`dedo${numeroDedo}`] = resultado.quality;
                
                // 🌐 DETECÇÃO DO MODO SIMULAÇÃO 🌐
                if (resultado.simulationMode) {
                    this.atualizarResultado(numeroDedo, 'success-simulation', resultado.quality);
                    
                    // Mostra mensagem especial para simulação
                    if (resultado.identified && resultado.user) {
                        this.atualizarStatus(`🌐 MODO SIMULAÇÃO - ${resultado.user.name} identificado (${resultado.quality}%)`, 'simulation');
                    } else {
                        this.atualizarStatus(`🌐 MODO SIMULAÇÃO - Dedo ${numeroDedo} capturado (${resultado.quality}%)`, 'simulation');
                    }
                    
                    // Adiciona alerta visual de simulação (informativo, não crítico)
                    this.mostrarAlertaSimulacao(resultado, numeroDedo);
                    
                    await this.sleep(1500);
                } else {
                    // Captura real normal
                    this.atualizarResultado(numeroDedo, 'success', resultado.quality);
                    this.atualizarStatus(`Dedo ${numeroDedo} capturado com qualidade ${resultado.quality}%`, 'success');
                    
                    await this.sleep(1500);
                }
            } else {
                throw new Error(`Falha na captura do dedo ${numeroDedo}`);
            }
            
        } catch (error) {
            this.atualizarResultado(numeroDedo, 'error');
            
            // ⚠️ TRATAMENTO ESPECÍFICO PARA SIMULAÇÃO DETECTADA (outros simuladores) ⚠️
            if (error.message.startsWith('ZKAGENT_SIMULACAO:')) {
                const templateSimulado = error.message.replace('ZKAGENT_SIMULACAO:', '');
                
                // Alerta visual crítico no modal
                this.mostrarAlertaSimulacaoRejeitada(templateSimulado);
                
                // Mensagem específica para simulação
                const mensagemSimulacao = `🚨 SIMULAÇÃO DETECTADA - CAPTURA REJEITADA!

O ZKAgent está retornando dados SIMULADOS em vez de biometria real:
"${templateSimulado.substring(0, 80)}..."

❌ SISTEMA NÃO ACEITA DADOS SIMULADOS!

✅ SOLUÇÕES OBRIGATÓRIAS:
1. Reinstalar ZKFinger SDK mais recente
2. Verificar driver ZK4500 (Status: OK)
3. Posicionar dedo FISICAMENTE no sensor
4. Contatar suporte se problema persistir

⚠️ Captura biométrica DEVE ser 100% real para segurança!`;

                throw new Error(mensagemSimulacao);
            }
            
            // ⚠️ TRATAMENTO PARA TEMPLATE SUSPEITO ⚠️
            if (error.message.startsWith('ZKAGENT_SUSPEITO:')) {
                const templateSuspeito = error.message.replace('ZKAGENT_SUSPEITO:', '');
                
                // Alerta visual de template suspeito
                this.mostrarAlertaTemplateSuspeito(templateSuspeito);
                
                const mensagemSuspeito = `⚠️ TEMPLATE SUSPEITO DETECTADO!

O template capturado não parece ser biometria real:
"${templateSuspeito.substring(0, 60)}..."

Características suspeitas:
• Template muito pequeno (< 100 bytes)
• Contém caracteres de texto em vez de dados binários
• Formato inconsistente com biometria ZK4500

🔧 VERIFICAÇÕES NECESSÁRIAS:
1. Certifique-se que o dedo está no sensor
2. Limpe o sensor com álcool 70%
3. Verifique instalação do SDK ZKFinger
4. Teste outro dedo se necessário

❌ Sistema rejeitou template por segurança!`;

                throw new Error(mensagemSuspeito);
            }
            
            // Tratamento específico de outras mensagens de erro para o usuário
            let mensagemUsuario = error.message;
            
            if (error.message.includes('ERRO SDK ZK4500') && error.message.includes('código -8')) {
                // Erro -8 - mensagem simplificada para o usuário
                mensagemUsuario = `Falha na leitura do dedo ${numeroDedo}. 

Tente:
• Limpe o sensor com álcool 70%
• Posicione o dedo no centro do sensor
• Aplique pressão firme mas não excessiva
• Certifique-se que o dedo está seco

Se persistir, pode ser necessário reinstalar o driver ZK4500.`;
                
                this.atualizarInstrucoes('Problema na Captura', [
                    'Limpe o sensor ZK4500 com álcool 70%',
                    'Seque bem o dedo antes de posicionar',
                    'Posicione o dedo centralmente no sensor',
                    'Aplique pressão firme e constante',
                    'Aguarde alguns segundos para nova tentativa'
                ]);
                
            } else if (error.message.includes('HARDWARE ZK4500')) {
                // Problema de hardware
                mensagemUsuario = `Problema de conexão com o leitor biométrico.

Verifique:
• Cabo USB do ZK4500 conectado
• Leitor ligado e funcionando
• Gerenciador de Dispositivos sem erros

Tente desconectar e reconectar o cabo USB.`;
                
            } else if (error.message.includes('ZKAgent')) {
                // Problema de comunicação
                mensagemUsuario = `Problema de comunicação com o serviço biométrico.

Verificações:
• Serviço ZKAgent está rodando?
• Porta 5001 disponível?
• Firewall não está bloqueando?

Contate o suporte técnico se necessário.`;
            }
            
            throw new Error(mensagemUsuario);
        }
    }

    /**
     * Mostra alerta visual para MODO DE SIMULAÇÃO (desenvolvimento)
     */
    mostrarAlertaSimulacao(resultado, numeroDedo) {
        // Atualiza scanner para estado de simulação (diferente de erro)
        this.atualizarScanner('simulation');
        
        // Atualiza status com info de simulação
        if (resultado.identified && resultado.user) {
            this.atualizarStatus(`🌐 MODO SIMULAÇÃO - ${resultado.user.name} identificado`, 'simulation');
        } else {
            this.atualizarStatus(`🌐 MODO SIMULAÇÃO - Captura realizada`, 'simulation');
        }
        
        // Instrução específica para modo simulação
        this.atualizarInstrucoes('🌐 MODO DE SIMULAÇÃO ATIVO', [
            '💻 Sistema em modo desenvolvimento',
            '⚠️ Dados NÃO são biometria real',
            '✅ Para testes e desenvolvimento',
            '🔄 Use ZKAgent real para produção',
            '📊 Simulador funcionando corretamente'
        ]);
        
        // Adiciona elemento visual de simulação no modal
        this.adicionarAlertaVisualSimulacao(resultado);
    }

    /**
     * Mostra alerta visual crítico para simulação REJEITADA (outros simuladores)
     */
    mostrarAlertaSimulacaoRejeitada(templateSimulado) {
        // Atualiza scanner para estado de erro crítico
        this.atualizarScanner('critical-error');
        
        // Atualiza status com alerta de simulação
        this.atualizarStatus('🚨 SIMULAÇÃO DETECTADA - CAPTURA REJEITADA!', 'critical');
        
        // Instrução específica para simulação
        this.atualizarInstrucoes('🚨 DADOS SIMULADOS REJEITADOS', [
            '❌ Sistema detectou template simulado',
            '✅ Reinstale o ZKFinger SDK mais recente',
            '✅ Verifique driver ZK4500 (Status: OK)',
            '✅ Posicione dedo FISICAMENTE no sensor',
            '📞 Contate suporte se problema persistir'
        ]);
        
        // Adiciona elemento visual de alerta no modal
        this.adicionarAlertaVisualCritico('SIMULAÇÃO', templateSimulado);
    }

    /**
     * Mostra alerta visual para template suspeito
     */
    mostrarAlertaTemplateSuspeito(templateSuspeito) {
        // Atualiza scanner para estado de aviso
        this.atualizarScanner('warning');
        
        // Status de template suspeito
        this.atualizarStatus('⚠️ Template suspeito detectado', 'warning');
        
        // Instruções para template suspeito
        this.atualizarInstrucoes('⚠️ TEMPLATE SUSPEITO', [
            '🔍 Template não parece ser biometria real',
            '✅ Certifique-se que o dedo está no sensor',
            '✅ Limpe o sensor com álcool 70%',
            '✅ Verifique instalação do SDK ZKFinger',
            '🔄 Tente capturar novamente'
        ]);
        
        // Adiciona elemento visual de aviso
        this.adicionarAlertaVisualCritico('SUSPEITO', templateSuspeito);
    }

    /**
     * Adiciona alerta visual crítico no modal
     */
    adicionarAlertaVisualCritico(tipo, conteudo) {
        const modalBody = document.querySelector('#biometriaModal .modal-body');
        if (!modalBody) return;
        
        // Remove alerta anterior se existir
        const alertaExistente = modalBody.querySelector('.alerta-simulacao');
        if (alertaExistente) {
            alertaExistente.remove();
        }
        
        // Cria container do alerta
        const alertaDiv = document.createElement('div');
        alertaDiv.className = `alerta-simulacao alert ${tipo === 'SIMULAÇÃO' ? 'alert-danger' : 'alert-warning'}`;
        alertaDiv.style.marginTop = '15px';
        alertaDiv.style.border = '3px solid';
        alertaDiv.style.borderColor = tipo === 'SIMULAÇÃO' ? '#dc3545' : '#ffc107';
        
        const icone = tipo === 'SIMULAÇÃO' ? '🚨' : '⚠️';
        const titulo = tipo === 'SIMULAÇÃO' ? 'SIMULAÇÃO DETECTADA' : 'TEMPLATE SUSPEITO';
        
        alertaDiv.innerHTML = `
            <h5>${icone} ${titulo}</h5>
            <p><strong>Conteúdo detectado:</strong></p>
            <code style="background: #f8f9fa; padding: 10px; display: block; border-radius: 4px; max-height: 100px; overflow-y: auto;">
                ${conteudo.substring(0, 200)}${conteudo.length > 200 ? '...' : ''}
            </code>
            <hr>
            <p><strong>${icone} Sistema NÃO aceita dados ${tipo === 'SIMULAÇÃO' ? 'simulados' : 'suspeitos'} por segurança!</strong></p>
            <small class="text-muted">Para captura real: SDK ZKFinger + driver ZK4500 + posicionamento físico do dedo</small>
        `;
        
        // Insere no início do modal body
        modalBody.insertBefore(alertaDiv, modalBody.firstChild);
        
        // Faz scroll para o alerta
        alertaDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    /**
     * Salva a biometria no formulário
     */
    salvarBiometria() {
        console.log('🔐 SALVANDO BIOMETRIA - Dados capturados:');
        console.log('🔐 Templates:', this.templates);
        console.log('🔐 Qualidades:', this.qualidades);
        
        // Constrói objeto JSON com template e qualidade para cada dedo
        if (this.templates.dedo1) {
            const dadosDedo1 = {
                template: this.templates.dedo1,
                quality: this.qualidades.dedo1,
                timestamp: new Date().toISOString()
            };
            document.getElementById('digital_dedo1').value = JSON.stringify(dadosDedo1);
            console.log('🔐 Dedo 1 salvo:', dadosDedo1);
        } else {
            document.getElementById('digital_dedo1').value = '';
            console.log('🔐 Dedo 1: vazio');
        }
        
        if (this.templates.dedo2) {
            const dadosDedo2 = {
                template: this.templates.dedo2,
                quality: this.qualidades.dedo2,
                timestamp: new Date().toISOString()
            };
            document.getElementById('digital_dedo2').value = JSON.stringify(dadosDedo2);
            console.log('🔐 Dedo 2 salvo:', dadosDedo2);
        } else {
            document.getElementById('digital_dedo2').value = '';
            console.log('🔐 Dedo 2: vazio');
        }
        
        // Adiciona campos de qualidade (se existirem)
        const qualidade1Input = document.getElementById('qualidade_dedo1');
        const qualidade2Input = document.getElementById('qualidade_dedo2');
        
        if (qualidade1Input) qualidade1Input.value = this.qualidades.dedo1;
        if (qualidade2Input) qualidade2Input.value = this.qualidades.dedo2;
        
        this.fechar();
        
        // Feedback visual
        const btnBiometria = document.querySelector('button[onclick="abrirModalBiometria()"]');
        if (btnBiometria) {
            btnBiometria.textContent = '✅ Biometria Capturada';
            btnBiometria.style.background = '#28a745';
            btnBiometria.style.borderColor = '#28a745';
        }
        
        console.log('🔐 BIOMETRIA SALVA - Campos preenchidos no formulário');
        alert('Biometria salva com sucesso! Os dados biométricos serão incluídos no cadastro do funcionário.');
    }

    /**
     * Utilitário para delay
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Executa diagnóstico automático e mostra resultados
     */
    async executarDiagnostico() {
        try {
            const diagnostico = await this.zkAgent.diagnosticarSistema();
            
            // Salvar diagnóstico no sessionStorage para análise
            sessionStorage.setItem('diagnostico_biometria', JSON.stringify(diagnostico));
            
            console.log('📊 Diagnóstico salvo no sessionStorage');
            return diagnostico;
            
        } catch (error) {
            console.error('Erro no diagnóstico:', error);
            return null;
        }
    }

    /**
     * Mostra botão de diagnóstico manual no modal
     */
    mostrarBotaoDiagnostico() {
        const modalContent = document.querySelector('#biometriaModal .modal-body');
        if (!modalContent) return;
        
        // Remove botão existente se houver
        const botaoExistente = modalContent.querySelector('.btn-diagnostico');
        if (botaoExistente) {
            botaoExistente.remove();
        }
        
        // Cria novo botão
        const botaoDiagnostico = document.createElement('button');
        botaoDiagnostico.className = 'btn btn-warning btn-diagnostico mt-3';
        botaoDiagnostico.innerHTML = '🔍 Executar Diagnóstico Completo';
        botaoDiagnostico.onclick = () => this.executarDiagnosticoManual();
        
        modalContent.appendChild(botaoDiagnostico);
    }

    /**
     * Executa diagnóstico manual completo
     */
    async executarDiagnosticoManual() {
        const botao = document.querySelector('.btn-diagnostico');
        if (botao) {
            botao.innerHTML = '🔄 Executando diagnóstico...';
            botao.disabled = true;
        }
        
        try {
            this.atualizarStatus('Executando diagnóstico completo...', 'info');
            
            const diagnostico = await this.zkAgent.diagnosticarSistema();
            
            // Mostrar resultados no modal
            this.mostrarResultadosDiagnostico(diagnostico);
            
        } catch (error) {
            console.error('Erro no diagnóstico manual:', error);
            this.atualizarStatus(`Erro no diagnóstico: ${error.message}`, 'error');
        } finally {
            if (botao) {
                botao.innerHTML = '🔍 Executar Diagnóstico Completo';
                botao.disabled = false;
            }
        }
    }

    /**
     * Mostra resultados do diagnóstico no modal - FOCO LOCAL
     */
    mostrarResultadosDiagnostico(diagnostico) {
        const modalBody = document.querySelector('#biometriaModal .modal-body');
        if (!modalBody) return;
        
        // Remove resultado anterior se houver
        const resultadoExistente = modalBody.querySelector('.diagnostico-resultado');
        if (resultadoExistente) {
            resultadoExistente.remove();
        }
        
        // Cria container de resultados
        const containerResultado = document.createElement('div');
        containerResultado.className = 'diagnostico-resultado mt-3 p-3 border rounded bg-light';
        
        let statusZkAgent = '❌ Falhou';
        let statusDevices = '❌ Nenhum';
        let recomendacoes = [];
        
        // Análise do status ZKAgent LOCAL
        try {
            if (diagnostico.zkagent_status && !diagnostico.zkagent_status.includes('Erro')) {
                const zkStatus = JSON.parse(diagnostico.zkagent_status);
                if (zkStatus.status === 'ok') {
                    statusZkAgent = '✅ Funcionando';
                }
            } else {
                statusZkAgent = '❌ Não instalado/rodando';
                recomendacoes.push('🔧 Instalar ZKAgent: zkagent/ZKAgent-Distribuicao/install.bat');
                recomendacoes.push('🔄 Reiniciar serviço ZKAgent');
            }
        } catch (e) {
            statusZkAgent = '❌ Erro de comunicação';
            recomendacoes.push('🔧 Verificar se ZKAgent está rodando na porta 5001');
        }
        
        // Análise dos dispositivos LOCAL
        try {
            if (diagnostico.devices_count && !diagnostico.devices_count.includes('Erro')) {
                const devicesData = JSON.parse(diagnostico.devices_count);
                if (devicesData.devices > 0) {
                    statusDevices = `✅ ${devicesData.devices} ZK4500 detectado(s)`;
                } else {
                    statusDevices = '⚠️ ZK4500 não detectado';
                    recomendacoes.push('🔌 Verificar cabo USB do ZK4500');
                    recomendacoes.push('💻 Verificar driver no Gerenciador de Dispositivos');
                }
            } else {
                statusDevices = '❌ Erro ao verificar';
                recomendacoes.push('🔌 Conectar ZK4500 via USB');
            }
        } catch (e) {
            statusDevices = '❌ Falha na comunicação';
            recomendacoes.push('🔧 Problema na comunicação com ZKAgent');
        }
        
        // Análise específica do erro de captura
        if (diagnostico.capture_test) {
            if (diagnostico.capture_test.includes('Falha ao abrir o dispositivo')) {
                recomendacoes.unshift('🔌 SOLUÇÃO PRINCIPAL: Desconectar e reconectar cabo USB do ZK4500');
                recomendacoes.push('🔄 Aguardar 10 segundos e reconectar');
                recomendacoes.push('👂 Ouvir som do Windows detectando dispositivo');
            } else if (diagnostico.capture_test.includes('timeout')) {
                recomendacoes.push('⏱️ Timeout na captura - verificar se ZK4500 está respondendo');
            } else if (diagnostico.capture_test.includes('Erro')) {
                recomendacoes.push('🔧 Erro na captura - verificar logs do ZKAgent');
            }
        }
        
        // Se não há recomendações, tudo está OK
        if (recomendacoes.length === 0) {
            recomendacoes.push('✅ Sistema funcionando corretamente');
            recomendacoes.push('🧪 Tente a captura biométrica novamente');
        }
        
        containerResultado.innerHTML = `
            <h6>📊 Diagnóstico ZKAgent Local</h6>
            <div class="row mb-3">
                <div class="col-8">
                    <small class="d-block"><strong>🔧 ZKAgent:</strong> ${statusZkAgent}</small>
                    <small class="d-block"><strong>🔌 Hardware ZK4500:</strong> ${statusDevices}</small>
                    <small class="d-block"><strong>📡 Conexão:</strong> ${diagnostico.connection_method}</small>
                    <small class="d-block"><strong>🌐 URL:</strong> ${diagnostico.zkagent_url}</small>
                </div>
                <div class="col-4 text-end">
                    <small class="text-muted">${new Date(diagnostico.timestamp).toLocaleTimeString()}</small>
                </div>
            </div>
            <div class="border-top pt-2">
                <small><strong>💡 Próximos Passos:</strong></small>
                <ol class="small mb-0 mt-1">
                    ${recomendacoes.map(r => `<li>${r}</li>`).join('')}
                </ol>
            </div>
            <div class="border-top pt-2 mt-2">
                <small class="text-muted">
                    <strong>ℹ️ Lembre-se:</strong> O ZKAgent e ZK4500 devem estar no <u>mesmo PC</u> que está acessando este sistema.
                </small>
            </div>
        `;
        
        modalBody.appendChild(containerResultado);
    }

    /**
     * Adiciona alerta visual informativo para modo simulação
     */
    adicionarAlertaVisualSimulacao(resultado) {
        const modalBody = document.querySelector('#biometriaModal .modal-body');
        if (!modalBody) return;
        
        // Remove alerta anterior se existir
        const alertaExistente = modalBody.querySelector('.alerta-simulacao');
        if (alertaExistente) {
            alertaExistente.remove();
        }
        
        // Cria container do alerta (informativo, não crítico)
        const alertaDiv = document.createElement('div');
        alertaDiv.className = 'alerta-simulacao alert alert-info';
        alertaDiv.style.marginTop = '15px';
        alertaDiv.style.border = '2px solid #17a2b8';
        alertaDiv.style.backgroundColor = 'rgba(23, 162, 184, 0.1)';
        
        let conteudoInfo = '';
        if (resultado.identified && resultado.user) {
            conteudoInfo = `
                <p><strong>👤 Usuário identificado:</strong> ${resultado.user.name}</p>
                <p><strong>👔 Cargo:</strong> ${resultado.user.role || 'Não informado'}</p>
                <p><strong>📊 Qualidade:</strong> ${resultado.quality}%</p>
            `;
        } else {
            conteudoInfo = `
                <p><strong>📊 Qualidade da captura:</strong> ${resultado.quality}%</p>
                <p><strong>📱 Template gerado:</strong> ${resultado.template ? resultado.template.substring(0, 40) + '...' : 'Disponível'}</p>
            `;
        }
        
        alertaDiv.innerHTML = `
            <h5>🌐 MODO DE SIMULAÇÃO ATIVO</h5>
            <div class="alert alert-warning mb-2" style="border: 1px solid #ffc107; background-color: rgba(255, 193, 7, 0.1);">
                <strong>⚠️ AVISO:</strong> Estes NÃO são dados biométricos reais!
            </div>
            ${conteudoInfo}
            <hr>
            <div class="row">
                <div class="col-md-6">
                    <h6>💻 Para Desenvolvimento:</h6>
                    <ul class="mb-0" style="font-size: 0.9em;">
                        <li>✅ Testes de interface</li>
                        <li>✅ Validação de fluxos</li>
                        <li>✅ Demonstrações</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>🔄 Para Produção:</h6>
                    <ul class="mb-0" style="font-size: 0.9em;">
                        <li>🤖 Use ZKAgent real</li>
                        <li>🔌 Hardware ZK4500</li>
                        <li>👆 Biometria física</li>
                    </ul>
                </div>
            </div>
            <small class="text-muted mt-2 d-block">
                <strong>Como alternar:</strong> Execute <code>simulacao/usar-zkagent.bat</code> para usar hardware real
            </small>
        `;
        
        // Insere no início do modal body
        modalBody.insertBefore(alertaDiv, modalBody.firstChild);
        
        // Faz scroll para o alerta
        alertaDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

// Instância global do modal
let modalBiometria = null;

/**
 * Funções globais para compatibilidade
 */
function abrirModalBiometria() {
    if (!modalBiometria) {
        modalBiometria = new ModalBiometriaVisual();
    }
    modalBiometria.abrir();
}

function fecharModalBiometria() {
    if (modalBiometria) {
        modalBiometria.fechar();
    }
}

function iniciarCapturaBiometria() {
    if (modalBiometria) {
        modalBiometria.iniciarCaptura();
    }
}

function salvarBiometriaModal() {
    if (modalBiometria) {
        modalBiometria.salvarBiometria();
    }
}

// Inicialização quando o DOM carregar
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔐 Sistema de Biometria ZKAgent carregado - VERSÃO FALLBACK v2.0 - ' + new Date().toISOString());
    console.log('🔐 FALLBACK AUTOMÁTICO: Conexão direta → Proxy Flask (se CORS falhar)');
});