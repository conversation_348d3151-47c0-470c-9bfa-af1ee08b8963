#!/usr/bin/env python3
"""
Script de teste para jornadas dinâmicas
Sistema RLPONTO-WEB
Data: 05/07/2025
"""

import requests
import json

def testar_jornadas_dinamicas():
    """Testar funcionalidades de jornadas dinâmicas"""
    
    base_url = "http://************:5000"
    
    print("🧪 Iniciando testes das jornadas dinâmicas...")
    
    # 1. Testar página de jornadas
    print("\n📋 1. Testando página de jornadas...")
    try:
        response = requests.get(f"{base_url}/empresa-principal/jornadas-clientes")
        if response.status_code == 200:
            print("   ✅ Página de jornadas carregada com sucesso")
        else:
            print(f"   ❌ Erro ao carregar página: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erro de conexão: {e}")
    
    # 2. Testar API de jornadas disponíveis
    print("\n🔗 2. Testando API de jornadas disponíveis...")
    try:
        response = requests.get(f"{base_url}/empresa-principal/api/jornadas-disponiveis")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                jornadas = data.get('jornadas', [])
                print(f"   ✅ API funcionando - {len(jornadas)} jornadas encontradas")
                for jornada in jornadas[:3]:  # Mostrar apenas as 3 primeiras
                    print(f"      - {jornada.get('nome', 'N/A')}")
            else:
                print("   ❌ API retornou erro")
        else:
            print(f"   ❌ Erro na API: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erro de conexão: {e}")
    
    # 3. Testar criação de jornada (simulação)
    print("\n➕ 3. Testando criação de jornada...")
    test_data = {
        'empresa_cliente_id': '1',
        'nome_jornada': 'Teste Jornada Dinâmica',
        'tipo_jornada': 'Diurno',
        'categoria_funcionario': 'Teste',
        'seg_qui_entrada': '08:00',
        'seg_qui_saida': '17:00',
        'sexta_entrada': '08:00',
        'sexta_saida': '16:00',
        'intervalo_inicio': '12:00',
        'intervalo_fim': '13:00',
        'tolerancia_entrada_minutos': '15',
        'padrao': False,
        'descricao': 'Jornada criada para teste do sistema dinâmico'
    }
    
    try:
        response = requests.post(
            f"{base_url}/empresa-principal/jornadas/criar",
            data=test_data
        )
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ Criação de jornada funcionando")
            else:
                print(f"   ⚠️  Erro na criação: {data.get('message', 'N/A')}")
        else:
            print(f"   ❌ Erro HTTP: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erro de conexão: {e}")
    
    # 4. Testar validações de exclusão (simulação)
    print("\n🗑️ 4. Testando validações de exclusão...")
    try:
        # Tentar excluir jornada inexistente
        response = requests.post(
            f"{base_url}/empresa-principal/jornadas/excluir",
            json={'jornada_id': 99999}
        )
        if response.status_code == 200:
            data = response.json()
            if not data.get('success'):
                print("   ✅ Validação de jornada inexistente funcionando")
                print(f"      Mensagem: {data.get('message', 'N/A')}")
            else:
                print("   ⚠️  Validação não funcionou como esperado")
        else:
            print(f"   ❌ Erro HTTP: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erro de conexão: {e}")
    
    print("\n🎉 Testes concluídos!")
    print("\n📋 Resumo das funcionalidades implementadas:")
    print("   ✅ Endpoint de exclusão de jornadas com validações")
    print("   ✅ Interface melhorada com botão 'Criar Jornada' dinâmico")
    print("   ✅ Botões de exclusão com proteção para jornada padrão")
    print("   ✅ Validações de segurança implementadas")
    print("   ✅ CSS melhorado com animações")
    
    print("\n🔒 Validações de segurança implementadas:")
    print("   - Jornada padrão não pode ser excluída")
    print("   - Verificação de funcionários usando a jornada")
    print("   - Empresa deve ter pelo menos uma jornada ativa")
    print("   - Soft delete (marca como inativa)")
    
    print("\n🎨 Melhorias na interface:")
    print("   - Botão 'Criar Jornada' com animação pulse")
    print("   - Botões de exclusão apenas para jornadas não-padrão")
    print("   - Badge 'Protegida' para jornadas padrão")
    print("   - CSS melhorado com gradientes e animações")

if __name__ == "__main__":
    testar_jornadas_dinamicas()
