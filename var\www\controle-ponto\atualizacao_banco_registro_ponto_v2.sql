-- ========================================
-- ATUALIZAÇÃO BANCO DE DADOS - REGISTRO DE PONTO v2
-- Data: 08/01/2025  
-- Descrição: Adiciona funcionalidades de registro de ponto e configurações
-- ========================================

USE controle_ponto;

-- ========================================
-- 1. ATUALIZAR TABELA REGISTROS_PONTO
-- ========================================

-- Adicionar campos necessários para o sistema de registro
ALTER TABLE registros_ponto 
MODIFY COLUMN tipo_registro ENUM('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida', 'entrada', 'saida_antiga', 'intervalo_inicio', 'intervalo_fim') NOT NULL DEFAULT 'entrada_manha';

ALTER TABLE registros_ponto 
ADD COLUMN metodo_registro ENUM('biometrico', 'manual') NOT NULL DEFAULT 'biometrico' AFTER qualidade_biometria;

ALTER TABLE registros_ponto 
ADD COLUMN ip_origem VARCHAR(45) NULL COMMENT 'IP de origem do registro' AFTER observacoes;

ALTER TABLE registros_ponto 
ADD COLUMN criado_por INT NULL COMMENT 'ID do usuário que fez o registro manual' AFTER ip_origem;

ALTER TABLE registros_ponto 
ADD COLUMN criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER criado_por;

-- ========================================
-- 2. CRIAR TABELA EMPRESAS
-- ========================================

CREATE TABLE empresas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    razao_social VARCHAR(200) NOT NULL,
    nome_fantasia VARCHAR(200) NULL,
    cnpj VARCHAR(18) UNIQUE NOT NULL,
    inscricao_estadual VARCHAR(20) NULL,
    inscricao_municipal VARCHAR(20) NULL,
    endereco_rua VARCHAR(150) NULL,
    endereco_numero VARCHAR(10) NULL,
    endereco_complemento VARCHAR(50) NULL,
    endereco_bairro VARCHAR(50) NULL,
    endereco_cidade VARCHAR(50) NULL,
    endereco_estado VARCHAR(2) NULL,
    endereco_cep VARCHAR(10) NULL,
    telefone VARCHAR(15) NULL,
    email VARCHAR(100) NULL,
    website VARCHAR(100) NULL,
    ativa BOOLEAN DEFAULT TRUE,
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci 
COMMENT='Cadastro de empresas do sistema';

-- ========================================
-- 3. CRIAR TABELA HORARIOS_TRABALHO
-- ========================================

CREATE TABLE horarios_trabalho (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_id INT NOT NULL,
    nome_horario VARCHAR(100) NOT NULL COMMENT 'Ex: Administrativo, Produção, etc.',
    entrada_manha TIME NOT NULL DEFAULT '08:00:00',
    saida_almoco TIME NULL DEFAULT '12:00:00',
    entrada_tarde TIME NULL DEFAULT '13:00:00',
    saida TIME NOT NULL DEFAULT '17:00:00',
    tolerancia_minutos INT NOT NULL DEFAULT 10,
    ativo BOOLEAN DEFAULT TRUE,
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci 
COMMENT='Configurações de horários de trabalho por empresa';

-- ========================================
-- 4. ATUALIZAR TABELA FUNCIONARIOS
-- ========================================

-- Adicionar relacionamento com empresa e horário
ALTER TABLE funcionarios 
ADD COLUMN empresa_id INT NULL AFTER id;

ALTER TABLE funcionarios 
ADD COLUMN horario_trabalho_id INT NULL AFTER empresa_id;

-- Renomear campo setor
ALTER TABLE funcionarios 
CHANGE COLUMN setor_obra setor VARCHAR(50) NULL;

-- ========================================
-- 5. ATUALIZAR TABELA USUARIOS
-- ========================================

-- Adicionar campos para melhor controle de usuários
ALTER TABLE usuarios 
ADD COLUMN nome_completo VARCHAR(100) NULL AFTER usuario;

ALTER TABLE usuarios 
ADD COLUMN email VARCHAR(100) NULL AFTER nome_completo;

ALTER TABLE usuarios 
ADD COLUMN ativo BOOLEAN DEFAULT TRUE AFTER email;

ALTER TABLE usuarios 
ADD COLUMN ultimo_login TIMESTAMP NULL AFTER ativo;

ALTER TABLE usuarios 
ADD COLUMN data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER ultimo_login;

-- Atualizar tabela de permissões
ALTER TABLE permissoes 
MODIFY COLUMN nivel_acesso ENUM('admin', 'usuario', 'supervisor') DEFAULT 'usuario';

-- ========================================
-- 6. INSERIR DADOS PADRÃO
-- ========================================

-- Inserir empresa padrão
INSERT INTO empresas (id, razao_social, nome_fantasia, cnpj, ativa) 
VALUES (1, 'Empresa Padrão Ltda', 'Empresa Padrão', '00.000.000/0000-00', TRUE)
ON DUPLICATE KEY UPDATE razao_social = VALUES(razao_social);

-- Inserir horário padrão
INSERT INTO horarios_trabalho (id, empresa_id, nome_horario, entrada_manha, saida_almoco, entrada_tarde, saida, tolerancia_minutos) 
VALUES (1, 1, 'Horário Administrativo', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10)
ON DUPLICATE KEY UPDATE nome_horario = VALUES(nome_horario);

-- ========================================
-- 7. ADICIONAR CHAVES ESTRANGEIRAS
-- ========================================

-- FK para registros_ponto
ALTER TABLE registros_ponto 
ADD CONSTRAINT fk_registros_ponto_criado_por 
FOREIGN KEY (criado_por) REFERENCES usuarios(id) ON DELETE SET NULL;

-- FK para horarios_trabalho
ALTER TABLE horarios_trabalho 
ADD CONSTRAINT fk_horarios_trabalho_empresa 
FOREIGN KEY (empresa_id) REFERENCES empresas(id) ON DELETE CASCADE;

-- FK para funcionarios
ALTER TABLE funcionarios 
ADD CONSTRAINT fk_funcionarios_empresa 
FOREIGN KEY (empresa_id) REFERENCES empresas(id) ON DELETE SET NULL;

ALTER TABLE funcionarios 
ADD CONSTRAINT fk_funcionarios_horario 
FOREIGN KEY (horario_trabalho_id) REFERENCES horarios_trabalho(id) ON DELETE SET NULL;

-- ========================================
-- 8. ATUALIZAR DADOS EXISTENTES
-- ========================================

-- Atualizar funcionários existentes para usar empresa e horário padrão
UPDATE funcionarios 
SET empresa_id = 1, horario_trabalho_id = 1 
WHERE empresa_id IS NULL OR horario_trabalho_id IS NULL;

-- ========================================
-- 9. CRIAR ÍNDICES OTIMIZADOS
-- ========================================

-- Índices para tabelas novas
CREATE INDEX idx_empresas_cnpj ON empresas(cnpj);
CREATE INDEX idx_empresas_ativa ON empresas(ativa);
CREATE INDEX idx_horarios_empresa ON horarios_trabalho(empresa_id);
CREATE INDEX idx_horarios_ativo ON horarios_trabalho(ativo);

-- Índices para melhor performance nos relatórios
CREATE INDEX idx_registros_ponto_data_tipo ON registros_ponto(data_hora, tipo_registro);
CREATE INDEX idx_registros_ponto_metodo ON registros_ponto(metodo_registro);
CREATE INDEX idx_funcionarios_empresa ON funcionarios(empresa_id);
CREATE INDEX idx_funcionarios_horario ON funcionarios(horario_trabalho_id);

-- ========================================
-- SCRIPT CONCLUÍDO
-- ======================================== 