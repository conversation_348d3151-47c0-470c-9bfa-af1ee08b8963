#!/usr/bin/env python3
"""
Verificar backup da foto do Kalebe
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

import os
from utils.database import DatabaseManager

def verificar_backup_foto():
    """Verificar se há backup da foto do Kalebe"""
    print("🔍 VERIFICAÇÃO: BACKUP DA FOTO DO KALEBE")
    print("=" * 60)
    
    try:
        # 1. Verificar todos os diretórios possíveis
        print("📋 1. VERIFICANDO TODOS OS DIRETÓRIOS DE FOTOS:")
        
        diretorios_busca = [
            '/var/www/controle-ponto',
            '/var/www/controle-ponto/static',
            '/var/www/controle-ponto/static/uploads',
            '/var/www/controle-ponto/static/fotos',
            '/var/www/controle-ponto/uploads',
            '/var/www/controle-ponto/backup',
            '/var/www/controle-ponto/backup-build',
            '/tmp',
            '/home/<USER>'
        ]
        
        fotos_encontradas = []
        
        for diretorio in diretorios_busca:
            if os.path.exists(diretorio):
                print(f"   📁 Verificando {diretorio}:")
                try:
                    for root, dirs, files in os.walk(diretorio):
                        for arquivo in files:
                            # Buscar arquivos que podem ser foto do Kalebe
                            nome_lower = arquivo.lower()
                            if any(termo in nome_lower for termo in ['kalebe', '32', '0005', 'funcionario']):
                                if any(ext in nome_lower for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']):
                                    caminho_completo = os.path.join(root, arquivo)
                                    tamanho = os.path.getsize(caminho_completo)
                                    fotos_encontradas.append({
                                        'arquivo': arquivo,
                                        'caminho': caminho_completo,
                                        'tamanho': tamanho
                                    })
                                    print(f"      ✅ FOTO ENCONTRADA: {arquivo} ({tamanho} bytes)")
                except Exception as e:
                    print(f"      ❌ Erro ao verificar: {e}")
            else:
                print(f"   ❌ Diretório {diretorio} não existe")
        
        # 2. Verificar histórico de funcionários
        print(f"\n📋 2. VERIFICANDO HISTÓRICO DE FUNCIONÁRIOS:")
        db = DatabaseManager()
        
        # Verificar se há tabela de histórico
        tabelas = db.execute_query("SHOW TABLES")
        tabelas_nomes = [t[list(t.keys())[0]] for t in tabelas]
        
        tabelas_historico = [t for t in tabelas_nomes if 'historico' in t.lower() or 'backup' in t.lower()]
        
        if tabelas_historico:
            print(f"   ✅ Tabelas de histórico encontradas: {tabelas_historico}")
            
            for tabela in tabelas_historico:
                try:
                    # Verificar se tem dados do Kalebe
                    kalebe_historico = db.execute_query(f"""
                        SELECT * FROM {tabela} 
                        WHERE nome_completo LIKE %s OR funcionario_id = 32
                        LIMIT 5
                    """, ('%KALEBE%',))
                    
                    if kalebe_historico:
                        print(f"   ✅ Dados do Kalebe encontrados na tabela {tabela}:")
                        for registro in kalebe_historico:
                            if 'foto_3x4' in registro and registro['foto_3x4']:
                                print(f"      ✅ FOTO ENCONTRADA NO HISTÓRICO!")
                                return {
                                    'foto_encontrada': True,
                                    'fonte': f'tabela_{tabela}',
                                    'foto_data': registro['foto_3x4']
                                }
                except Exception as e:
                    print(f"   ❌ Erro ao verificar tabela {tabela}: {e}")
        else:
            print(f"   ❌ Nenhuma tabela de histórico encontrada")
        
        # 3. Verificar logs de aplicação
        print(f"\n📋 3. VERIFICANDO LOGS DE APLICAÇÃO:")
        
        logs_diretorios = [
            '/var/www/controle-ponto/logs',
            '/var/log/controle-ponto',
            '/tmp'
        ]
        
        for log_dir in logs_diretorios:
            if os.path.exists(log_dir):
                print(f"   📁 Verificando logs em {log_dir}:")
                try:
                    arquivos_log = [f for f in os.listdir(log_dir) if f.endswith('.log')]
                    for log_file in arquivos_log:
                        caminho_log = os.path.join(log_dir, log_file)
                        # Verificar se o log menciona o Kalebe
                        with open(caminho_log, 'r', encoding='utf-8', errors='ignore') as f:
                            conteudo = f.read()
                            if 'KALEBE' in conteudo or 'kalebe' in conteudo:
                                print(f"      ✅ Kalebe mencionado em {log_file}")
                except Exception as e:
                    print(f"      ❌ Erro ao verificar logs: {e}")
        
        # 4. Resultado final
        if fotos_encontradas:
            print(f"\n📊 RESULTADO: {len(fotos_encontradas)} fotos encontradas")
            return {
                'fotos_encontradas': True,
                'fotos': fotos_encontradas
            }
        else:
            print(f"\n📊 RESULTADO: Nenhuma foto do Kalebe encontrada")
            return {
                'fotos_encontradas': False,
                'recomendacao': 'solicitar_nova_foto'
            }
        
    except Exception as e:
        print(f"\n❌ ERRO NA VERIFICAÇÃO: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return {'erro': str(e)}

def corrigir_funcao_restauracao():
    """Corrigir a função de restauração para incluir foto_3x4"""
    print(f"\n🔧 CORREÇÃO: INCLUIR FOTO_3X4 NA RESTAURAÇÃO")
    print("=" * 60)
    
    print("📋 PROBLEMA IDENTIFICADO:")
    print("   ❌ A função de restauração não inclui o campo 'foto_3x4'")
    print("   ❌ Isso faz com que as fotos se percam durante a restauração")
    
    print("\n📋 SOLUÇÃO RECOMENDADA:")
    print("   ✅ Adicionar 'foto_3x4' na lista de campos da função restaurar_funcionario")
    print("   ✅ Adicionar 'digital_dedo1' e 'digital_dedo2' também")
    print("   ✅ Garantir que dados biométricos sejam preservados")
    
    print("\n📋 CAMPOS QUE DEVEM SER INCLUÍDOS:")
    campos_importantes = [
        'foto_3x4',
        'digital_dedo1', 
        'digital_dedo2',
        'biometria_qualidade_1',
        'biometria_qualidade_2',
        'biometria_data_cadastro'
    ]
    
    for campo in campos_importantes:
        print(f"   - {campo}")
    
    return True

if __name__ == "__main__":
    print("🎯 INVESTIGAÇÃO COMPLETA: FOTO DO KALEBE PERDIDA")
    print("=" * 70)
    
    # Verificar backup
    resultado = verificar_backup_foto()
    
    # Analisar correção necessária
    corrigir_funcao_restauracao()
    
    print(f"\n📊 CONCLUSÃO:")
    if resultado.get('fotos_encontradas'):
        print(f"✅ Fotos encontradas - podem ser restauradas")
    elif resultado.get('foto_encontrada'):
        print(f"✅ Foto encontrada no histórico - pode ser restaurada")
    else:
        print(f"❌ Foto perdida definitivamente")
        print(f"💡 RECOMENDAÇÃO: Solicitar nova foto do Kalebe")
        print(f"🔧 CORREÇÃO: Corrigir função de restauração para preservar fotos")
