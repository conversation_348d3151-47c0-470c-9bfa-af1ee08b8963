# 🕐 LÓGICA DE PASSOS - CONTROLE DE PONTO

**Sistema:** RLPONTO-WEB v1.0  
**Data:** 11/07/2025  
**Objetivo:** Documentar a lógica completa de funcionamento do controle de ponto

---

## 📋 **VISÃO GERAL DO FLUXO**

```mermaid
graph TD
    A[Funcionário Solicita Batida] --> B[Identificar Funcionário]
    B --> C[Obter Jornada de Trabalho]
    C --> D[Verificar Registros Existentes]
    D --> E[Determinar Tipo de Batida]
    E --> F[Validar Horário Permitido]
    F --> G[Validar Sequência]
    G --> H[Registrar no Banco]
    H --> I[Calcular Status Pontualidade]
    I --> J[Retornar <PERSON>sultado]
```

---

## 🔄 **PASSO 1: IDENTIFICAÇÃO DO FUNCIONÁRIO**

### **1.1 Entrada:**
- ID do funcionário
- Método de identificação (biometria, manual, cartão)

### **1.2 Validações:**
```python
# Verificar se funcionário existe e está ativo
funcionario = obter_funcionario(funcionario_id)
if not funcionario or funcionario['status_cadastro'] != 'Ativo':
    return erro("Funcionário não encontrado ou inativo")
```

### **1.3 Saída:**
- Dados básicos do funcionário
- Status de cadastro validado

---

## 📅 **PASSO 2: OBTER JORNADA DE TRABALHO**

### **2.1 Consulta Principal:**
```sql
SELECT 
    jt.seg_qui_entrada,
    jt.seg_qui_saida,
    jt.sexta_entrada,
    jt.sexta_saida,
    jt.intervalo_inicio,
    jt.intervalo_fim,
    jt.tolerancia_entrada_minutos
FROM funcionarios f
INNER JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
WHERE f.id = %s AND jt.ativa = 1
```

### **2.2 Mapeamento de Horários:**
```python
# Determinar horários baseado no dia da semana
dia_semana = datetime.now().weekday()  # 0=Segunda, 6=Domingo

if dia_semana < 4:  # Segunda a Quinta
    entrada_manha = jornada['seg_qui_entrada']
    saida_final = jornada['seg_qui_saida']
elif dia_semana == 4:  # Sexta
    entrada_manha = jornada['sexta_entrada']
    saida_final = jornada['sexta_saida']
else:  # Sábado/Domingo
    return erro("Jornada não configurada para fins de semana")

saida_almoco = jornada['intervalo_inicio']
entrada_tarde = jornada['intervalo_fim']
tolerancia = jornada['tolerancia_entrada_minutos']
```

### **2.3 Validação:**
```python
if not jornada:
    return erro("Funcionário não possui jornada configurada")
```

---

## 🔍 **PASSO 3: VERIFICAR REGISTROS EXISTENTES**

### **3.1 Consulta do Dia:**
```sql
SELECT 
    id, tipo_registro, data_hora, metodo_registro, status_pontualidade
FROM registros_ponto
WHERE funcionario_id = %s 
AND DATE(data_hora) = CURDATE()
ORDER BY data_hora ASC
```

### **3.2 Análise dos Registros:**
```python
registros_existentes = obter_batidas_do_dia(funcionario_id)
num_batidas = len(registros_existentes)
tipos_ja_registrados = [r['tipo_registro'] for r in registros_existentes]

# Verificar limite de batidas
if num_batidas >= 4:
    return erro("Limite de 4 batidas por dia atingido")
```

---

## ⚙️ **PASSO 4: DETERMINAR TIPO DE BATIDA (B1, B2, B3, B4)**

### **4.1 Sequência Padrão:**
```python
sequencia_padrao = {
    1: 'entrada_manha',    # B1 - Entrada
    2: 'saida_almoco',     # B2 - Início intervalo
    3: 'entrada_tarde',    # B3 - Retorno intervalo
    4: 'saida'             # B4 - Saída final
}
```

### **4.2 Classificação Inteligente por Período:**
```python
def determinar_periodo_atual(hora_atual, jornada):
    if hora_atual < jornada['entrada_manha']:
        return "antes_expediente"
    elif jornada['entrada_manha'] <= hora_atual <= jornada['saida_almoco']:
        return "manha"
    elif jornada['saida_almoco'] < hora_atual < jornada['entrada_tarde']:
        return "almoco"
    elif jornada['entrada_tarde'] <= hora_atual <= jornada['saida_final']:
        return "tarde"
    else:
        return "apos_expediente"
```

### **4.3 Lógica de Determinação:**
```python
def classificar_batida_inteligente(funcionario_id, num_batidas, jornada, hora_atual):
    periodo = determinar_periodo_atual(hora_atual, jornada)
    
    # Primeira batida (B1)
    if num_batidas == 0:
        if periodo in ["antes_expediente", "manha"]:
            return 'entrada_manha'
        elif periodo == "tarde":
            return 'entrada_tarde'
        else:
            return 'entrada_manha'  # Padrão
    
    # Segunda batida (B2)
    elif num_batidas == 1:
        if periodo == "manha":
            return 'saida_almoco'
        elif periodo in ["tarde", "apos_expediente"]:
            return 'saida'
        else:
            return 'saida_almoco'  # Padrão
    
    # Terceira batida (B3)
    elif num_batidas == 2:
        if periodo == "tarde":
            return 'entrada_tarde'
        elif periodo == "apos_expediente":
            return 'saida'
        else:
            return 'entrada_tarde'  # Padrão
    
    # Quarta batida (B4)
    else:
        return 'saida'
```

---

## ✅ **PASSO 5: VALIDAR HORÁRIO PERMITIDO**

### **5.1 Validação por Tipo de Registro:**
```python
def validar_horario_permitido(tipo_registro, hora_atual, jornada):
    tolerancia = jornada['tolerancia_entrada_minutos']
    
    if tipo_registro == 'entrada_manha':
        # Permitir entrada desde 1h antes até tolerância após horário
        hora_min = jornada['entrada_manha'] - timedelta(hours=1)
        hora_max = jornada['entrada_manha'] + timedelta(minutes=tolerancia)
        
        if not (hora_min <= hora_atual <= hora_max):
            return False, f"Entrada permitida entre {hora_min} e {hora_max}"
    
    elif tipo_registro == 'saida_almoco':
        # Permitir saída para almoço 30min antes/depois do horário
        hora_min = jornada['saida_almoco'] - timedelta(minutes=30)
        hora_max = jornada['saida_almoco'] + timedelta(minutes=30)
        
        if not (hora_min <= hora_atual <= hora_max):
            return False, f"Saída para almoço permitida entre {hora_min} e {hora_max}"
    
    # ... validações para entrada_tarde e saida
    
    return True, "Horário válido"
```

### **5.2 Cálculo de Status de Pontualidade:**
```python
def calcular_status_pontualidade(tipo_registro, hora_atual, jornada):
    if tipo_registro == 'entrada_manha':
        if hora_atual <= jornada['entrada_manha']:
            return "Pontual"
        elif hora_atual <= jornada['entrada_manha'] + timedelta(minutes=jornada['tolerancia']):
            return "Pontual"  # Dentro da tolerância
        else:
            return "Atrasado"
    
    # Lógica similar para outros tipos...
    return "Pontual"  # Padrão
```

---

## 🔄 **PASSO 6: VALIDAR SEQUÊNCIA**

### **6.1 Validação de Ordem Cronológica:**
```python
def validar_ordem_cronologica(registros_existentes, nova_batida_hora):
    if registros_existentes:
        ultima_batida = registros_existentes[-1]['data_hora']
        if nova_batida_hora <= ultima_batida:
            return False, "Nova batida deve ser posterior à última registrada"
    return True, "Ordem cronológica válida"
```

### **6.2 Validação de Sequência Lógica:**
```python
def validar_sequencia_logica(tipos_existentes, novo_tipo):
    sequencias_validas = {
        'entrada_manha': [],  # Primeira batida sempre válida
        'saida_almoco': ['entrada_manha'],
        'entrada_tarde': ['entrada_manha', 'saida_almoco'],
        'saida': ['entrada_manha', 'saida_almoco', 'entrada_tarde']
    }
    
    tipos_necessarios = sequencias_validas.get(novo_tipo, [])
    
    for tipo_necessario in tipos_necessarios:
        if tipo_necessario not in tipos_existentes:
            return False, f"Tipo {novo_tipo} requer {tipo_necessario} anterior"
    
    return True, "Sequência válida"
```

---

## 💾 **PASSO 7: REGISTRAR NO BANCO**

### **7.1 Inserção no Banco:**
```sql
INSERT INTO registros_ponto (
    funcionario_id, tipo_registro, data_hora, metodo_registro,
    status_pontualidade, observacoes, ip_origem, criado_por
) VALUES (
    %s, %s, NOW(), %s, %s, %s, %s, %s
)
```

### **7.2 Dados Registrados:**
```python
registro = {
    'funcionario_id': funcionario_id,
    'tipo_registro': tipo_determinado,
    'data_hora': datetime.now(),
    'metodo_registro': metodo_registro,  # 'biometrico', 'manual'
    'status_pontualidade': status_calculado,
    'observacoes': observacoes_usuario,
    'ip_origem': request.remote_addr,
    'criado_por': usuario_logado_id
}
```

---

## 📊 **PASSO 8: RETORNAR RESULTADO**

### **8.1 Resposta de Sucesso:**
```json
{
    "success": true,
    "message": "Ponto registrado com sucesso",
    "dados": {
        "tipo_registro": "entrada_manha",
        "horario": "08:15:30",
        "status_pontualidade": "Pontual",
        "sequencia_atual": "B1",
        "proxima_batida_esperada": "saida_almoco"
    }
}
```

### **8.2 Resposta de Erro:**
```json
{
    "success": false,
    "message": "Horário não permitido para entrada",
    "detalhes": {
        "horario_atual": "06:30:00",
        "horario_permitido": "07:00:00 - 09:15:00",
        "tipo_tentativa": "entrada_manha"
    }
}
```

---

## 🚨 **CASOS ESPECIAIS**

### **9.1 Funcionário Sem Jornada:**
```python
if not jornada:
    return {
        'success': False,
        'message': 'Funcionário não possui jornada configurada. Entre em contato com o administrador.'
    }
```

### **9.2 Batida Duplicada:**
```python
# Verificar se já existe batida do mesmo tipo no dia
if novo_tipo in tipos_ja_registrados:
    return {
        'success': False,
        'message': f'Já existe registro de {novo_tipo} hoje'
    }
```

### **9.3 Horário Muito Fora do Padrão:**
```python
# Bloquear registros muito fora do horário (ex: 02:00 da manhã)
if hora_atual.hour < 5 or hora_atual.hour > 23:
    return {
        'success': False,
        'message': 'Horário fora do período permitido para registro'
    }
```

---

## 🔧 **FLUXO DE CORREÇÃO DE PROBLEMAS**

### **10.1 Diagnóstico de Falhas:**
```python
def diagnosticar_problema_ponto(funcionario_id):
    # 1. Verificar se funcionário existe
    funcionario = obter_funcionario(funcionario_id)
    if not funcionario:
        return "ERRO: Funcionário não encontrado"

    # 2. Verificar se tem jornada
    jornada = obter_jornada_funcionario(funcionario_id)
    if not jornada:
        return "ERRO: Funcionário sem jornada configurada"

    # 3. Verificar registros do dia
    registros = obter_batidas_do_dia(funcionario_id)
    if len(registros) >= 4:
        return "ERRO: Limite de batidas atingido"

    # 4. Verificar sequência
    sequencia_valida = validar_sequencia_batidas(funcionario_id)
    if not sequencia_valida['valida']:
        return f"ERRO: Sequência inválida - {sequencia_valida['problemas']}"

    return "OK: Sistema funcionando corretamente"
```

### **10.2 Correção Automática:**
```python
def corrigir_sequencia_automatica(funcionario_id, data_referencia):
    registros = obter_batidas_do_dia(funcionario_id, data_referencia)

    # Reordenar por horário
    registros_ordenados = sorted(registros, key=lambda x: x['data_hora'])

    # Reclassificar tipos
    for i, registro in enumerate(registros_ordenados):
        tipo_correto = sequencia_padrao[i + 1]
        if registro['tipo_registro'] != tipo_correto:
            atualizar_tipo_registro(registro['id'], tipo_correto)
```

---

## 📋 **MATRIZ DE VALIDAÇÕES**

### **11.1 Tabela de Validações por Tipo:**

| Tipo Registro | Horário Mín | Horário Máx | Pré-requisitos | Status Pontualidade |
|---------------|-------------|-------------|----------------|-------------------|
| entrada_manha | 06:00 | entrada + tolerância | Nenhum | Pontual/Atrasado |
| saida_almoco | saida_almoco - 30min | saida_almoco + 30min | entrada_manha | Pontual |
| entrada_tarde | entrada_tarde - 30min | entrada_tarde + 30min | saida_almoco | Pontual |
| saida | entrada_tarde + 4h | 23:59 | entrada_tarde | Pontual/Antecipado |

### **11.2 Estados Possíveis do Sistema:**

| Batidas | Estado | Próxima Ação | Validações |
|---------|--------|--------------|------------|
| 0 | Início | entrada_manha | Horário permitido |
| 1 | Trabalhando | saida_almoco ou saida | Sequência lógica |
| 2 | Almoço | entrada_tarde | Duração mínima almoço |
| 3 | Trabalhando | saida | Jornada mínima |
| 4 | Completo | Nenhuma | Jornada fechada |

---

## 🎯 **REGRAS DE NEGÓCIO**

### **12.1 Tolerâncias:**
- **Entrada:** Configurável por jornada (padrão 15 min)
- **Almoço:** ±30 minutos do horário configurado
- **Saída:** Sem tolerância (pode sair a qualquer hora após horário mínimo)

### **12.2 Durações Mínimas:**
- **Almoço:** 30 minutos mínimo, 2 horas máximo
- **Jornada:** 4 horas mínimo, 12 horas máximo
- **Intervalo entre batidas:** 1 minuto mínimo

### **12.3 Cálculos:**
```python
# Horas trabalhadas = (B2-B1) + (B4-B3)
def calcular_horas_trabalhadas(registros):
    if len(registros) >= 2:
        periodo_manha = registros[1]['data_hora'] - registros[0]['data_hora']
    if len(registros) >= 4:
        periodo_tarde = registros[3]['data_hora'] - registros[2]['data_hora']
        return periodo_manha + periodo_tarde
    return periodo_manha
```

---

## 📝 **LOGS E AUDITORIA**

### **13.1 Eventos Registrados:**
```python
eventos_log = {
    'PONTO_REGISTRADO': 'Ponto registrado com sucesso',
    'PONTO_REJEITADO': 'Tentativa de registro rejeitada',
    'SEQUENCIA_CORRIGIDA': 'Sequência de batidas corrigida',
    'JORNADA_INCOMPLETA': 'Jornada finalizada incompleta',
    'ERRO_VALIDACAO': 'Erro na validação de horários'
}
```

### **13.2 Informações de Auditoria:**
- Data/hora da tentativa
- IP de origem
- Método de registro (biometria/manual)
- Usuário responsável (se manual)
- Resultado da validação
- Observações do usuário

---

**Status:** DOCUMENTAÇÃO COMPLETA E EXPANDIDA
**Versão:** 1.0
**Uso:** Referência técnica para desenvolvimento, manutenção e treinamento
