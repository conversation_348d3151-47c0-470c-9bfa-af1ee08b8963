#!/usr/bin/env python3
"""
Debug da foto do Kalebe perdida
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager

def debug_foto_kalebe():
    """Debug da foto do Kalebe"""
    print("🔍 DEBUG: FOTO DO KALEBE PERDIDA")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar se Kalebe está na tabela principal
        print("📋 1. VERIFICANDO KALEBE NA TABELA PRINCIPAL:")
        kalebe_principal = db.execute_query("""
            SELECT id, nome_completo, matricula_empresa, foto_3x4, status_cadastro, ativo
            FROM funcionarios
            WHERE nome_completo LIKE %s
        """, ('%KALEBE%',))
        
        if kalebe_principal:
            kalebe = kalebe_principal[0]
            print(f"   ✅ Kalebe encontrado na tabela principal:")
            print(f"      ID: {kalebe['id']}")
            print(f"      Nome: {kalebe['nome_completo']}")
            print(f"      Matrícula: {kalebe['matricula_empresa']}")
            print(f"      Status: {kalebe['status_cadastro']}, Ativo: {kalebe['ativo']}")
            print(f"      foto_3x4: {kalebe['foto_3x4']}")

        else:
            print("   ❌ Kalebe não encontrado na tabela principal")
        
        # 2. Verificar se Kalebe está na tabela de desligados
        print(f"\n📋 2. VERIFICANDO KALEBE NA TABELA DE DESLIGADOS:")
        kalebe_desligados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa, foto_3x4
            FROM funcionarios_desligados
            WHERE nome_completo LIKE %s
        """, ('%KALEBE%',))
        
        if kalebe_desligados:
            kalebe_desl = kalebe_desligados[0]
            print(f"   ✅ Kalebe encontrado na tabela de desligados:")
            print(f"      ID Original: {kalebe_desl['funcionario_id_original']}")
            print(f"      Nome: {kalebe_desl['nome_completo']}")
            print(f"      Matrícula: {kalebe_desl['matricula_empresa']}")
            print(f"      foto_3x4: {kalebe_desl['foto_3x4']}")

            # 3. Comparar dados de foto
            print(f"\n📋 3. COMPARAÇÃO DE DADOS DE FOTO:")
            if kalebe_principal and kalebe_desligados:
                kalebe_atual = kalebe_principal[0]
                print(f"   Tabela Principal:")
                print(f"      foto_3x4: {kalebe_atual['foto_3x4']}")
                print(f"   Tabela Desligados:")
                print(f"      foto_3x4: {kalebe_desl['foto_3x4']}")
                
                # Verificar se as fotos são diferentes
                if kalebe_atual['foto_3x4'] != kalebe_desl['foto_3x4']:
                    print(f"   ⚠️ PROBLEMA: foto_3x4 é diferente entre as tabelas!")
                    if kalebe_desl['foto_3x4'] and not kalebe_atual['foto_3x4']:
                        print(f"   ❌ FOTO PERDIDA: Desligados tem foto, Principal não tem")
                        return {
                            'problema': 'foto_perdida',
                            'id_principal': kalebe_atual['id'],
                            'foto_original': kalebe_desl['foto_3x4']
                        }
                else:
                    print(f"   ✅ Fotos são iguais entre as tabelas")
        else:
            print("   ❌ Kalebe não encontrado na tabela de desligados")
        
        # 4. Verificar arquivos de foto no sistema
        print(f"\n📋 4. VERIFICANDO ARQUIVOS DE FOTO NO SISTEMA:")
        import os
        
        # Diretórios onde as fotos podem estar
        diretorios_foto = [
            '/var/www/controle-ponto/static/uploads',
            '/var/www/controle-ponto/static/uploads/fotos',
            '/var/www/controle-ponto/static/fotos',
            '/var/www/controle-ponto/uploads'
        ]
        
        for diretorio in diretorios_foto:
            if os.path.exists(diretorio):
                print(f"   📁 Diretório {diretorio}:")
                try:
                    arquivos = os.listdir(diretorio)
                    fotos_kalebe = [f for f in arquivos if 'kalebe' in f.lower() or '32' in f or '0005' in f]
                    if fotos_kalebe:
                        print(f"      ✅ Fotos relacionadas ao Kalebe encontradas:")
                        for foto in fotos_kalebe:
                            print(f"         - {foto}")
                    else:
                        print(f"      ❌ Nenhuma foto do Kalebe encontrada")
                except Exception as e:
                    print(f"      ❌ Erro ao listar arquivos: {e}")
            else:
                print(f"   ❌ Diretório {diretorio} não existe")
        
        return {'problema': 'investigacao_completa'}
        
    except Exception as e:
        print(f"\n❌ ERRO NO DEBUG: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return {'problema': 'erro', 'erro': str(e)}

def corrigir_foto_kalebe(info_problema):
    """Corrigir a foto do Kalebe se necessário"""
    if info_problema.get('problema') == 'foto_perdida':
        print(f"\n🔧 CORRIGINDO FOTO DO KALEBE:")
        
        try:
            db = DatabaseManager()
            
            # Restaurar foto da tabela de desligados
            db.execute_query("""
                UPDATE funcionarios
                SET foto_3x4 = %s
                WHERE id = %s
            """, (
                info_problema['foto_original'],
                info_problema['id_principal']
            ))
            
            print(f"   ✅ Foto do Kalebe restaurada com sucesso!")
            
            # Verificar se foi corrigido
            kalebe_corrigido = db.execute_query("""
                SELECT id, nome_completo, foto_3x4
                FROM funcionarios
                WHERE id = %s
            """, (info_problema['id_principal'],))

            if kalebe_corrigido:
                kalebe = kalebe_corrigido[0]
                print(f"   ✅ Verificação:")
                print(f"      foto_3x4: {kalebe['foto_3x4']}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Erro ao corrigir foto: {e}")
            return False
    
    return False

if __name__ == "__main__":
    print("🎯 DEBUG COMPLETO: FOTO DO KALEBE PERDIDA")
    print("=" * 70)
    
    # Investigar problema
    resultado = debug_foto_kalebe()
    
    # Tentar corrigir se necessário
    if resultado.get('problema') == 'foto_perdida':
        print(f"\n🔧 PROBLEMA IDENTIFICADO: Foto perdida durante restauração")
        
        correcao_ok = corrigir_foto_kalebe(resultado)
        
        if correcao_ok:
            print(f"\n🎉 CORREÇÃO APLICADA!")
            print(f"✅ Foto do Kalebe restaurada")
        else:
            print(f"\n❌ FALHA NA CORREÇÃO!")
    else:
        print(f"\n📊 RESULTADO: {resultado.get('problema', 'Investigação concluída')}")
