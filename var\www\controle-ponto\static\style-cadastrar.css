/* =======================================================
   🎨 RLPONTO-WEB - ESTILOS PRINCIPAIS (MODERNIZADO)
   Data de Atualização: 09/01/2025
   Inspirado nas melhores práticas visuais do MCP @21st-dev/magic
   ======================================================= */

/* Estilos gerais modernizados */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9fafb;
    color: #1f2937;
    line-height: 1.6;
}

/* Container principal - compatibilidade com novo layout */
.container {
    display: flex;
    min-height: 100vh;
}

/* <PERSON><PERSON> da sidebar (para compatibilidade) */
.sidebar {
    display: none; /* Nova sidebar substituiu esta */
}

/* Conteúdo principal atualizado */
.main-content {
    flex: 1;
    padding: 0; /* Removido padding pois agora é controlado pelo novo layout */
    max-width: none; /* Removido para funcionar com novo layout */
    margin: 0; /* Removido para funcionar com novo layout */
}

/* Formulários modernizados */
h2 {
    color: #1f2937;
    margin-bottom: 24px;
    font-size: 28px;
    font-weight: 700;
    letter-spacing: -0.025em;
}

h3 {
    color: #4fbdba;
    margin-bottom: 16px;
    font-size: 20px;
    font-weight: 600;
}

/* Cards de formulário modernos */
form {
    background-color: #ffffff;
    padding: 32px;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    margin-bottom: 24px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Labels e inputs modernizados */
label {
    display: block;
    margin-bottom: 8px;
    color: #374151;
    font-weight: 500;
    font-size: 14px;
}

input[type="text"],
input[type="email"],
input[type="date"],
input[type="number"],
input[type="password"],
select,
textarea {
    width: 100%;
    padding: 12px 16px;
    margin-bottom: 20px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background-color: #ffffff;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #4fbdba;
    box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
}

.input-error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.checkbox-group {
    margin-bottom: 20px;
}

.checkbox-group label {
    display: inline-block;
    margin-right: 16px;
    font-weight: 400;
}

/* Botões modernizados */
.form-buttons {
    margin-top: 32px;
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

button[type="submit"] {
    background-color: #4fbdba;
    color: #ffffff;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

button[type="submit"]:hover {
    background-color: #3da8a6;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.voltar-btn {
    background-color: #6b7280;
    color: #ffffff;
}

.voltar-btn:hover {
    background-color: #4b5563;
    transform: translateY(-1px);
}

/* Mensagens de erro modernizadas */
.errors {
    margin-top: 24px;
    padding: 20px;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    color: #b91c1c;
}

.errors h3 {
    color: #b91c1c;
    margin-bottom: 12px;
    font-size: 16px;
}

.errors ul {
    margin: 0;
    padding-left: 20px;
}

.errors li {
    margin-bottom: 4px;
}

/* 📊 TABELAS MODERNIZADAS */
.table-responsive {
    overflow-x: auto;
    margin-bottom: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
    background: white;
    border-radius: 12px;
    overflow: hidden;
}

.data-table th,
.data-table td {
    padding: 16px 20px;
    text-align: left;
    border-bottom: 1px solid #f3f4f6;
}

.data-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table tbody tr {
    transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
    background: #f9fafb;
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* 🎯 AÇÕES E BOTÕES DE TABELA */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-view {
    background: #4fbdba;
    color: white;
}

.btn-view:hover {
    background: #3da8a6;
    color: white;
    transform: translateY(-1px);
}

.btn-edit {
    background: #f59e0b;
    color: white;
}

.btn-edit:hover {
    background: #d97706;
    transform: translateY(-1px);
}

.btn-delete {
    background: #ef4444;
    color: white;
}

.btn-delete:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* 📄 PAGINAÇÃO MODERNA */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
    flex-wrap: wrap;
}

.pagination a,
.pagination span {
    padding: 10px 14px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    text-decoration: none;
    color: #374151;
    background: white;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 14px;
}

.pagination a:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
    transform: translateY(-1px);
}

.pagination .current {
    background: #4fbdba;
    color: white;
    border-color: #4fbdba;
}

.pagination .disabled {
    color: #9ca3af;
    background: #f9fafb;
    cursor: not-allowed;
}

/* 🏷️ BADGES DE STATUS */
.badge {
    padding: 6px 12px;
    border-radius: 9999px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-success {
    background: #d1fae5;
    color: #065f46;
}

.badge-danger {
    background: #fee2e2;
    color: #991b1b;
}

.badge-warning {
    background: #fef3c7;
    color: #92400e;
}

.badge-secondary {
    background: #f3f4f6;
    color: #374151;
}

/* 🔍 FILTROS MODERNOS */
.filters {
    background: white;
    padding: 24px;
    border-radius: 12px;
    margin-bottom: 24px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.filters-row {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.filter-group input,
.filter-group select {
    width: 100%;
    padding: 10px 14px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 0;
}

.filter-group button {
    background: #4fbdba;
    color: white;
    border: none;
    padding: 10px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    margin-bottom: 0;
}

.filter-group button:hover {
    background: #3da8a6;
    transform: translateY(-1px);
}

/* 📱 RESPONSIVIDADE MELHORADA */
@media (max-width: 768px) {
    /* Ajustes para mobile compatíveis com nova sidebar */
    .main-content {
        padding: 0;
    }
    
    form {
        padding: 20px;
        margin-bottom: 16px;
        border-radius: 8px;
    }
    
    h2 {
        font-size: 24px;
    }
    
    .form-buttons {
        flex-direction: column;
    }
    
    button {
        width: 100%;
        justify-content: center;
    }
    
    .filters-row {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: 100%;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 8px;
    }
    
    .btn-sm {
        width: 100%;
        justify-content: center;
    }
    
    .pagination {
        font-size: 12px;
        gap: 4px;
    }
    
    .pagination a,
    .pagination span {
        padding: 8px 12px;
    }
    
    .table-responsive {
        border-radius: 8px;
    }
    
    .data-table th,
    .data-table td {
        padding: 12px 16px;
    }
}

@media (max-width: 480px) {
    form {
        padding: 16px;
    }
    
    h2 {
        font-size: 20px;
        margin-bottom: 16px;
    }
    
    .data-table th,
    .data-table td {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .filters {
        padding: 16px;
    }
}

/* =================================
   MODAL DE BIOMETRIA - MANTIDO PARA COMPATIBILIDADE
   ================================= */

/* Modal Container */
.modal-biometria {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-container {
    position: relative;
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Modal Header */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
    border-radius: 16px 16px 0 0;
}

.modal-header h2 {
    margin: 0;
    color: #1f2937;
    font-size: 24px;
    font-weight: 700;
}

.modal-close {
    width: 40px;
    height: 40px;
    border: none;
    background: #f3f4f6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #6b7280;
    font-size: 20px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #e5e7eb;
    color: #374151;
}

.modal-content {
    padding: 32px;
}

/* Continuação dos estilos de biometria existentes... */
.biometria-status {
    margin-bottom: 32px;
}

.status-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    margin-bottom: 24px;
}

.status-bar::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #e5e7eb;
    z-index: 1;
}

.status-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f3f4f6;
    border: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-label {
    font-size: 12px;
    color: #6b7280;
    text-align: center;
    font-weight: 500;
}

.status-step.active .step-number {
    background: #4fbdba;
    border-color: #4fbdba;
    color: white;
}

.status-step.completed .step-number {
    background: #10b981;
    border-color: #10b981;
    color: white;
}

.status-step.active .step-label {
    color: #4fbdba;
    font-weight: 600;
}

/* Área de captura */
.biometria-display {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    margin-bottom: 32px;
}

.biometria-scanner {
    text-align: center;
}

.scanner-frame {
    width: 200px;
    height: 200px;
    border: 3px solid #e5e7eb;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    background: #f9fafb;
    transition: all 0.3s ease;
}

.biometria-preview {
    text-align: center;
}

.preview-frame {
    width: 200px;
    height: 200px;
    border: 3px solid #e5e7eb;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    background: #f9fafb;
    overflow: hidden;
}

.preview-area {
    width: 100%;
    height: 100%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    font-size: 14px;
    border-radius: 12px;
}

.preview-info {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-top: 16px;
}

.qualidade-display, .dedo-display {
    background: #f9fafb;
    padding: 12px 16px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e5e7eb;
}

.qualidade-label, .dedo-label {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
}

.qualidade-valor, .dedo-valor {
    font-weight: 600;
    color: #1f2937;
}

.preview-label {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 12px;
    font-weight: 500;
}

/* Instruções */
.biometria-instructions {
    background: #f0f9ff;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #bae6fd;
}

.instruction-card h4 {
    color: #0369a1;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
}

.instruction-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.instruction-card li {
    padding: 8px 0;
    color: #0c4a6e;
    position: relative;
    padding-left: 24px;
    font-size: 14px;
}

.instruction-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #0369a1;
    font-weight: bold;
}

/* Resultados */
.capturas-resultado {
    margin-top: 32px;
}

.resultado-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.resultado-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.2s ease;
}

.resultado-card.success {
    border-color: #10b981;
    background: #f0fdf4;
}

.resultado-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.resultado-header h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.pending {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.success {
    background: #d1fae5;
    color: #065f46;
}

.status-badge.error {
    background: #fee2e2;
    color: #991b1b;
}

/* Medidor de qualidade */
.qualidade-meter {
    margin: 16px 0;
}

.qualidade-bar {
    height: 8px;
    background: #f3f4f6;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.qualidade-fill {
    height: 100%;
    background: linear-gradient(90deg, #ef4444, #f59e0b, #10b981);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.qualidade-valor {
    font-size: 12px;
    color: #6b7280;
    text-align: center;
    font-weight: 500;
}

/* Mensagens de status */
.status-message {
    margin: 20px 0;
}

.status-text {
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
}

.status-text.info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.status-text.success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #6ee7b7;
}

.status-text.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

/* Ações do modal */
.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 24px 32px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
    border-radius: 0 0 16px 16px;
}

/* Responsividade do modal */
@media (max-width: 768px) {
    .modal-container {
        width: 95%;
        max-height: 95vh;
    }
    
    .biometria-display {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    .resultado-grid {
        grid-template-columns: 1fr;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 16px;
    }
    
    .scanner-area {
        width: 150px;
        height: 150px;
    }
    
    .fingerprint-icon svg {
        width: 40px;
        height: 40px;
    }
}

/* 🎯 FOOTER DO SISTEMA */
.system-footer {
    margin-top: auto;
    padding: 24px 0;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
    font-size: 13px;
    color: #6b7280;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    text-align: center;
    line-height: 1.6;
}

.footer-line {
    margin: 4px 0;
}

.footer-line:first-child {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 14px;
}

.footer-line:last-child {
    font-size: 12px;
    color: var(--text-secondary);
}

@media (max-width: 768px) {
    .footer-content {
        padding: 0 16px;
    }
    
    .footer-line {
        font-size: 12px;
    }
    
    .footer-line:first-child {
        font-size: 13px;
    }
    
    .footer-line:last-child {
        font-size: 11px;
    }
}