#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para analisar todos os funcionários cadastrados no sistema RLPONTO-WEB
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def analisar_funcionarios():
    """Analisar todos os funcionários cadastrados no sistema"""
    try:
        print("=" * 80)
        print("🔍 ANÁLISE COMPLETA DE FUNCIONÁRIOS - RLPONTO-WEB")
        print("=" * 80)
        
        # Importar dependências
        from utils.database import DatabaseManager
        
        # Conectar ao banco
        db = DatabaseManager()
        print("✅ Conexão com banco de dados estabelecida")
        
        # 1. ESTATÍSTICAS GERAIS
        print("\n📊 ESTATÍSTICAS GERAIS:")
        print("-" * 50)
        
        # Total de funcionários
        total_funcionarios = db.execute_query("SELECT COUNT(*) as total FROM funcionarios")[0]['total']
        print(f"📈 Total de funcionários cadastrados: {total_funcionarios}")
        
        # Funcionários ativos
        ativos = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE ativo = 1")[0]['total']
        print(f"✅ Funcionários ativos: {ativos}")
        
        # Funcionários inativos
        inativos = total_funcionarios - ativos
        print(f"❌ Funcionários inativos: {inativos}")
        
        # Funcionários com foto
        com_foto = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE foto_3x4 IS NOT NULL")[0]['total']
        print(f"📸 Funcionários com foto: {com_foto}")
        
        # Funcionários com biometria
        com_biometria = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE digital_dedo1 IS NOT NULL")[0]['total']
        print(f"👆 Funcionários com biometria: {com_biometria}")
        
        # 2. DISTRIBUIÇÃO POR EMPRESA
        print("\n🏢 DISTRIBUIÇÃO POR EMPRESA:")
        print("-" * 50)
        
        empresas_query = """
        SELECT 
            COALESCE(e.nome_fantasia, 'Sem Empresa') as empresa,
            COALESCE(e.razao_social, 'N/A') as razao_social,
            COUNT(f.id) as total_funcionarios,
            SUM(CASE WHEN f.ativo = 1 THEN 1 ELSE 0 END) as ativos
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        GROUP BY f.empresa_id, e.nome_fantasia, e.razao_social
        ORDER BY total_funcionarios DESC
        """
        
        empresas = db.execute_query(empresas_query)
        for emp in empresas:
            print(f"  🏢 {emp['empresa']}")
            print(f"     Razão Social: {emp['razao_social']}")
            print(f"     Total: {emp['total_funcionarios']} | Ativos: {emp['ativos']}")
            print()
        
        # 3. DISTRIBUIÇÃO POR CARGO
        print("\n💼 DISTRIBUIÇÃO POR CARGO:")
        print("-" * 50)
        
        cargos_query = """
        SELECT 
            COALESCE(cargo, 'Não informado') as cargo,
            COUNT(*) as total,
            SUM(CASE WHEN ativo = 1 THEN 1 ELSE 0 END) as ativos
        FROM funcionarios
        GROUP BY cargo
        ORDER BY total DESC
        LIMIT 10
        """
        
        cargos = db.execute_query(cargos_query)
        for cargo in cargos:
            print(f"  💼 {cargo['cargo']}: {cargo['total']} total ({cargo['ativos']} ativos)")
        
        # 4. DISTRIBUIÇÃO POR SETOR
        print("\n🏭 DISTRIBUIÇÃO POR SETOR:")
        print("-" * 50)
        
        setores_query = """
        SELECT 
            COALESCE(
                CASE 
                    WHEN setor_obra IS NOT NULL AND setor_obra != '' THEN setor_obra
                    WHEN setor IS NOT NULL AND setor != '' THEN setor
                    ELSE 'Não informado'
                END, 'Não informado'
            ) as setor,
            COUNT(*) as total,
            SUM(CASE WHEN ativo = 1 THEN 1 ELSE 0 END) as ativos
        FROM funcionarios
        GROUP BY setor
        ORDER BY total DESC
        LIMIT 10
        """
        
        setores = db.execute_query(setores_query)
        for setor in setores:
            print(f"  🏭 {setor['setor']}: {setor['total']} total ({setor['ativos']} ativos)")
        
        # 5. LISTA DETALHADA DOS FUNCIONÁRIOS
        print("\n👥 LISTA DETALHADA DE FUNCIONÁRIOS:")
        print("-" * 80)
        
        funcionarios_query = """
        SELECT 
            f.id,
            f.nome_completo,
            f.cpf,
            f.matricula_empresa,
            f.cargo,
            COALESCE(
                CASE 
                    WHEN f.setor_obra IS NOT NULL AND f.setor_obra != '' THEN f.setor_obra
                    WHEN f.setor IS NOT NULL AND f.setor != '' THEN f.setor
                    ELSE 'Não informado'
                END, 'Não informado'
            ) as setor,
            f.ativo,
            f.data_admissao,
            f.telefone1,
            f.email,
            COALESCE(e.nome_fantasia, 'Sem Empresa') as empresa,
            CASE WHEN f.foto_3x4 IS NOT NULL THEN 'SIM' ELSE 'NÃO' END as tem_foto,
            CASE WHEN f.digital_dedo1 IS NOT NULL THEN 'SIM' ELSE 'NÃO' END as tem_biometria,
            COALESCE(ht.nome_horario, 'Sem horário') as horario_trabalho
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
        ORDER BY f.ativo DESC, f.nome_completo ASC
        """
        
        funcionarios = db.execute_query(funcionarios_query)
        
        for i, func in enumerate(funcionarios, 1):
            status = "✅ ATIVO" if func['ativo'] else "❌ INATIVO"
            print(f"\n{i:3d}. {func['nome_completo']} ({status})")
            print(f"     📋 ID: {func['id']} | CPF: {func['cpf']} | Matrícula: {func['matricula_empresa'] or 'N/A'}")
            print(f"     🏢 Empresa: {func['empresa']}")
            print(f"     💼 Cargo: {func['cargo'] or 'N/A'} | Setor: {func['setor']}")
            print(f"     📅 Admissão: {func['data_admissao'] or 'N/A'}")
            print(f"     📞 Telefone: {func['telefone1'] or 'N/A'} | Email: {func['email'] or 'N/A'}")
            print(f"     ⏰ Horário: {func['horario_trabalho']}")
            print(f"     📸 Foto: {func['tem_foto']} | 👆 Biometria: {func['tem_biometria']}")
        
        # 6. PROBLEMAS IDENTIFICADOS
        print("\n⚠️ PROBLEMAS IDENTIFICADOS:")
        print("-" * 50)
        
        # Funcionários sem empresa
        sem_empresa = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE empresa_id IS NULL")[0]['total']
        if sem_empresa > 0:
            print(f"❌ {sem_empresa} funcionários sem empresa definida")
        
        # Funcionários sem cargo
        sem_cargo = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE cargo IS NULL OR cargo = ''")[0]['total']
        if sem_cargo > 0:
            print(f"❌ {sem_cargo} funcionários sem cargo definido")
        
        # Funcionários sem horário
        sem_horario = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE horario_trabalho_id IS NULL")[0]['total']
        if sem_horario > 0:
            print(f"❌ {sem_horario} funcionários sem horário de trabalho")
        
        # Funcionários sem foto
        sem_foto = total_funcionarios - com_foto
        if sem_foto > 0:
            print(f"⚠️ {sem_foto} funcionários sem foto cadastrada")
        
        # Funcionários sem biometria
        sem_biometria = total_funcionarios - com_biometria
        if sem_biometria > 0:
            print(f"⚠️ {sem_biometria} funcionários sem biometria cadastrada")
        
        if sem_empresa == 0 and sem_cargo == 0 and sem_horario == 0:
            print("✅ Nenhum problema crítico identificado!")
        
        print("\n" + "=" * 80)
        print("✅ ANÁLISE CONCLUÍDA COM SUCESSO!")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a análise: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    print("Iniciando análise de funcionários...")
    analisar_funcionarios()
