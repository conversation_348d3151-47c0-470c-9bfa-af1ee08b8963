{% extends "base.html" %}

{% block title %}Detalhes do Funcionário - {{ funcionario.nome_completo }}{% endblock %}

{% block content %}
<div class="container mt-4">
    
    <!-- Breadcrumbs -->
    {% if breadcrumbs %}
    <nav aria-label="breadcrumb" style="margin-bottom: 20px;">
        <ol class="breadcrumb">
            {% for breadcrumb in breadcrumbs %}
                {% if loop.last %}
                    <li class="breadcrumb-item active" aria-current="page">{{ breadcrumb.label }}</li>
                {% else %}
                    <li class="breadcrumb-item"><a href="{{ breadcrumb.url }}">{{ breadcrumb.label }}</a></li>
                {% endif %}
            {% endfor %}
        </ol>
    </nav>
    {% endif %}

    <!-- Header -->
    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <div style="display: flex; align-items: center; gap: 20px;">
            <div style="width: 80px; height: 80px; border-radius: 50%; overflow: hidden; border: 3px solid #4fbdba;">
                <img src="/funcionarios/{{ funcionario.id }}/foto" 
                     alt="Foto de {{ funcionario.nome_completo }}" 
                     style="width: 100%; height: 100%; object-fit: cover;">
            </div>
            <div>
                <h2 style="margin: 0 0 10px 0; color: #495057;">{{ funcionario.nome_completo }}</h2>
                <p style="margin: 0; color: #6c757d; font-size: 16px;">
                    <strong>{{ funcionario.cargo }}</strong> - {{ funcionario.setor_obra }}
                </p>
                <p style="margin: 5px 0 0 0; color: #6c757d;">
                    Matrícula: <strong>{{ funcionario.matricula_empresa }}</strong> | 
                    Status: 
                    {% if funcionario.status_cadastro == 'Ativo' %}
                        <span style="color: #28a745; font-weight: bold;">✅ {{ funcionario.status_cadastro }}</span>
                    {% else %}
                        <span style="color: #dc3545; font-weight: bold;">❌ {{ funcionario.status_cadastro }}</span>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

<!-- Dados Pessoais -->
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
        👤 Dados Pessoais
    </h4>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <div>
            <strong>CPF:</strong><br>
            {{ funcionario.cpf | format_cpf }}
        </div>
        <div>
            <strong>RG:</strong><br>
            {{ funcionario.rg }}
        </div>
        <div>
            <strong>Data Nascimento:</strong><br>
            {{ funcionario.data_nascimento | format_date }}
        </div>
        <div>
            <strong>Sexo:</strong><br>
            {{ funcionario.sexo }}
        </div>
        <div>
            <strong>Estado Civil:</strong><br>
            {{ funcionario.estado_civil }}
        </div>
        <div>
            <strong>Nacionalidade:</strong><br>
            {{ funcionario.nacionalidade }}
        </div>
    </div>
</div>

<!-- Documentos Trabalhistas -->
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
        📄 Documentos Trabalhistas
    </h4>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <div>
            <strong>CTPS Número:</strong><br>
            {{ funcionario.ctps_numero }}
        </div>
        <div>
            <strong>CTPS Série/UF:</strong><br>
            {{ funcionario.ctps_serie_uf }}
        </div>
        <div>
            <strong>PIS/PASEP:</strong><br>
            {{ funcionario.pis_pasep }}
        </div>
    </div>
</div>

<!-- Endereço -->
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
        🏠 Endereço
    </h4>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <div style="grid-column: span 2;">
            <strong>Rua:</strong><br>
            {{ funcionario.endereco_rua }}
        </div>
        <div>
            <strong>Bairro:</strong><br>
            {{ funcionario.endereco_bairro }}
        </div>
        <div>
            <strong>Cidade:</strong><br>
            {{ funcionario.endereco_cidade }}
        </div>
        <div>
            <strong>CEP:</strong><br>
            {{ funcionario.endereco_cep }}
        </div>
        <div>
            <strong>Estado:</strong><br>
            {{ funcionario.endereco_estado }}
        </div>
    </div>
</div>

<!-- Contato -->
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
        📞 Contato
    </h4>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <div>
            <strong>Telefone Principal:</strong><br>
            {{ funcionario.telefone1 | format_telefone }}
        </div>
        {% if funcionario.telefone2 %}
        <div>
            <strong>Telefone Secundário:</strong><br>
            {{ funcionario.telefone2 | format_telefone }}
        </div>
        {% endif %}
        {% if funcionario.email %}
        <div>
            <strong>E-mail:</strong><br>
            <a href="mailto:{{ funcionario.email }}" style="color: #4fbdba;">{{ funcionario.email }}</a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Dados Profissionais -->
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
        💼 Dados Profissionais
    </h4>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <div>
            <strong>Data Admissão:</strong><br>
            {{ funcionario.data_admissao | format_date }}
        </div>
        <div>
            <strong>Tipo Contrato:</strong><br>
            {{ funcionario.tipo_contrato }}
        </div>
    </div>
</div>

<!-- Jornada de Trabalho -->
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
        ⏰ Jornada de Trabalho
    </h4>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
        <div>
            <h5 style="margin: 0 0 10px 0; color: #495057;">Segunda a Quinta</h5>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                <div>
                    <strong>Entrada:</strong><br>
                    {{ funcionario.jornada_seg_qui_entrada }}
                </div>
                <div>
                    <strong>Saída:</strong><br>
                    {{ funcionario.jornada_seg_qui_saida }}
                </div>
            </div>
        </div>
        
        <div>
            <h5 style="margin: 0 0 10px 0; color: #495057;">Sexta-feira</h5>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                <div>
                    <strong>Entrada:</strong><br>
                    {{ funcionario.jornada_sex_entrada }}
                </div>
                <div>
                    <strong>Saída:</strong><br>
                    {{ funcionario.jornada_sex_saida }}
                </div>
            </div>
        </div>
        
        <div>
            <h5 style="margin: 0 0 10px 0; color: #495057;">Intervalo</h5>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                <div>
                    <strong>Início:</strong><br>
                    {{ funcionario.jornada_intervalo_entrada }}
                </div>
                <div>
                    <strong>Fim:</strong><br>
                    {{ funcionario.jornada_intervalo_saida }}
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6;">
        <div>
            <strong>Nível Acesso:</strong><br>
            {{ funcionario.nivel_acesso }}
        </div>
        <div>
            <strong>Turno:</strong><br>
            {{ funcionario.turno }}
        </div>
        <div>
            <strong>Tolerância Ponto:</strong><br>
            {{ funcionario.tolerancia_ponto }} minutos
        </div>
        <div>
            <strong>Banco de Horas:</strong><br>
            {% if funcionario.banco_horas %}✅ Sim{% else %}❌ Não{% endif %}
        </div>
        <div>
            <strong>Hora Extra:</strong><br>
            {% if funcionario.hora_extra %}✅ Sim{% else %}❌ Não{% endif %}
        </div>
    </div>
</div>

<!-- 🦺 EPIs (Seção Corrigida) -->
{% if funcionario.epis and funcionario.epis|length > 0 %}
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
        <h4 style="margin: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
            🦺 EPIs (Equipamentos de Proteção Individual)
        </h4>
        <a href="/epis/funcionario/{{ funcionario.id }}" class="btn-sm" style="background: #4fbdba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">
            📋 Gerenciar EPIs
        </a>
    </div>
    
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background: #f8f9fa;">
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">EPI</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">CA</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">Data Entrega</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">Validade</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">Status</th>
                </tr>
            </thead>
            <tbody>
                {% for epi in funcionario.epis[:3] %}
                <tr>
                    <td style="padding: 10px; border: 1px solid #dee2e6;"><strong>{{ epi.epi_nome }}</strong></td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">{{ epi.epi_ca or '-' }}</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">
                        {% if epi.epi_data_entrega %}
                            {{ epi.epi_data_entrega | format_date }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">
                        {% if epi.epi_data_validade %}
                            {{ epi.epi_data_validade | format_date }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">
                        {% if epi.epi_data_validade %}
                            <span style="color: #28a745; font-weight: bold;">Válido</span>
                        {% else %}
                            <span style="color: #6c757d;">Sem validade</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        {% if funcionario.epis|length > 3 %}
            <p style="margin: 10px 0 0 0; color: #6c757d; font-size: 14px;">
                Mostrando 3 de {{ funcionario.epis|length }} EPIs. 
                <a href="/epis/funcionario/{{ funcionario.id }}" style="color: #4fbdba;">Ver todos</a>
            </p>
        {% endif %}
    </div>
</div>
{% else %}
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
        🦺 EPIs (Equipamentos de Proteção Individual)
    </h4>
    <div style="text-align: center; padding: 30px; color: #6c757d;">
        <div style="font-size: 48px; margin-bottom: 15px;">📦</div>
        <h5>Nenhum EPI cadastrado</h5>
        <p>Este funcionário ainda não possui EPIs registrados no sistema.</p>
        <a href="/epis/funcionario/{{ funcionario.id }}" class="btn-sm" style="background: #4fbdba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-top: 10px; display: inline-block;">
            ➕ Adicionar EPI
        </a>
    </div>
</div>
{% endif %}

<!-- Informações do Sistema -->
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
        ⚙️ Informações do Sistema
    </h4>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <div>
            <strong>Data Cadastro:</strong><br>
            {{ funcionario.data_cadastro | format_date }}
        </div>
        <div>
            <strong>Biometria Dedo 1:</strong><br>
            {% if funcionario.digital_dedo1 %}
                <span class="badge badge-success">Configurada</span>
            {% else %}
                <span class="badge badge-secondary">Não configurada</span>
            {% endif %}
        </div>
        <div>
            <strong>Biometria Dedo 2:</strong><br>
            {% if funcionario.digital_dedo2 %}
                <span class="badge badge-success">Configurada</span>
            {% else %}
                <span class="badge badge-secondary">Não configurada</span>
            {% endif %}
        </div>
    </div>
</div>

<!-- Ações rápidas -->
<div style="display: flex; gap: 10px; justify-content: center; margin-top: 30px;">
    <a href="/funcionarios" class="btn-sm" style="background: #6c757d; color: white; padding: 12px 24px;">
        ← Voltar à Lista
    </a>
    {% if current_user.is_admin %}
    <a href="/funcionarios/{{ funcionario.id }}/editar" class="btn-sm btn-edit" style="padding: 12px 24px;">
        ✏️ Editar Funcionário
    </a>
    {% endif %}
</div>

</div>
{% endblock %}

{% block extra_css %}
<style>
@media (max-width: 768px) {
    div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
    div[style*="display: flex"] {
        flex-direction: column !important;
        gap: 10px !important;
    }
}

/* Estilo para campos vazios */
.empty-field {
    color: #6c757d;
    font-style: italic;
}

/* Melhorias para badges */
.badge {
    white-space: nowrap;
}
</style>
{% endblock %}