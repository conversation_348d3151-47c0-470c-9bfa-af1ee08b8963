#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste da API de Relatórios - RLPONTO-WEB
Data: 06/06/2025
Objetivo: Testar a API de buscar registros diretamente
"""

import requests
import json
from datetime import datetime

def testar_api_relatorios():
    """Testa a API de relatórios diretamente."""
    
    print("=" * 80)
    print("🧪 TESTE DA API DE RELATÓRIOS - RLPONTO-WEB")
    print(f"⏰ Iniciado em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 80)
    
    # URLs para teste
    base_url = "http://10.19.208.31:5000"
    login_url = f"{base_url}/login"
    api_url = f"{base_url}/relatorios/api/buscar-registros"
    
    # Criar sessão para manter cookies
    session = requests.Session()
    
    try:
        # Passo 1: Fazer login
        print("🔐 PASSO 1: Fazendo login...")
        login_data = {
            'usuario': 'admin',
            'senha': '@Ric6109'
        }
        
        login_response = session.post(login_url, data=login_data, allow_redirects=False)
        print(f"Status do login: {login_response.status_code}")
        
        if login_response.status_code not in [200, 302]:
            print("❌ Falha no login")
            return False
        
        print("✅ Login realizado com sucesso")
        
        # Passo 2: Testar a API
        print("\n📋 PASSO 2: Testando API de buscar registros...")
        
        # Dados de teste
        filtros = {
            "data_inicio": "2025-06-01",
            "data_fim": "2025-06-06",
            "pagina": 1,
            "registros_por_pagina": 10
        }
        
        print(f"📤 Enviando filtros: {json.dumps(filtros, indent=2)}")
        
        # Fazer requisição
        headers = {
            'Content-Type': 'application/json'
        }
        
        api_response = session.post(api_url, json=filtros, headers=headers)
        
        print(f"📥 Status da resposta: {api_response.status_code}")
        
        # Analisar resposta
        if api_response.status_code == 200:
            try:
                response_data = api_response.json()
                print("✅ API respondeu com sucesso!")
                print(f"📊 Dados recebidos:")
                print(f"  - Success: {response_data.get('success')}")
                print(f"  - Total registros: {response_data.get('total_registros')}")
                print(f"  - Registros na página: {len(response_data.get('registros', []))}")
                print(f"  - Total páginas: {response_data.get('total_paginas')}")
                
                if 'debug_info' in response_data:
                    print(f"  - Error ID: {response_data['debug_info'].get('error_id')}")
                    print(f"  - Filtros aplicados: {response_data['debug_info'].get('filtros_aplicados')}")
                
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ Erro ao decodificar JSON: {str(e)}")
                print(f"📄 Resposta bruta: {api_response.text[:500]}...")
                return False
                
        else:
            print(f"❌ API retornou erro: {api_response.status_code}")
            print(f"📄 Resposta: {api_response.text[:500]}...")
            
            try:
                error_data = api_response.json()
                print(f"🆔 Error ID: {error_data.get('error_id', 'N/A')}")
                print(f"💬 Mensagem: {error_data.get('message', 'N/A')}")
                if 'debug_info' in error_data:
                    print(f"🔍 Debug Info: {error_data['debug_info']}")
            except:
                pass
            
            return False
    
    except requests.exceptions.ConnectionError:
        print("❌ Erro de conexão - Servidor não está rodando?")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {str(e)}")
        return False
    
    finally:
        session.close()

def main():
    """Função principal."""
    sucesso = testar_api_relatorios()
    
    print("\n" + "=" * 80)
    if sucesso:
        print("🎉 TESTE CONCLUÍDO COM SUCESSO!")
        print("✅ A API está funcionando corretamente")
    else:
        print("❌ TESTE FALHOU!")
        print("⚠️ Verifique os logs do servidor para mais detalhes")
    
    print(f"⏰ Teste concluído em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 80)

if __name__ == "__main__":
    main() 