
-- SCRIPT DE CORREÇÕES RLPONTO-WEB
-- Gerado em: 2025-07-17 17:32:48
-- Aplicar via php<PERSON>y<PERSON>dmin ou linha de comando

-- 1. Criar índices de performance
CREATE INDEX IF NOT EXISTS idx_registros_funcionario_data ON registros_ponto(funcionario_id, data_registro);
CREATE INDEX IF NOT EXISTS idx_funcionarios_ativo ON funcionarios(ativo);
CREATE INDEX IF NOT EXISTS idx_empresas_ativo ON empresas(ativo);
CREATE INDEX IF NOT EXISTS idx_horarios_empresa ON horarios_trabalho(empresa_id, ativo);

-- 2. <PERSON>par dados inconsistentes
DELETE FROM registros_ponto WHERE data_registro > CURDATE();

-- 3. Desativar funcionários órfãos
UPDATE funcionarios f
LEFT JOIN empresas e ON f.empresa_id = e.id
SET f.ativo = FALSE
WHERE e.id IS NULL AND f.ativo = TRUE;

-- 4. Verificar estrutura
SELECT 'Funcionários ativos:' as info, COUNT(*) as total FROM funcionarios WHERE ativo = TRUE;
SELECT 'Registros últimos 7 dias:' as info, COUNT(*) as total FROM registros_ponto WHERE data_registro >= DATE_SUB(CURDATE(), INTERVAL 7 DAY);
SELECT 'Empresas ativas:' as info, COUNT(*) as total FROM empresas WHERE ativo = TRUE;

-- 5. Testar consulta de horários
SELECT 
    f.id,
    f.nome,
    f.jornada_seg_qui_entrada,
    f.jornada_seg_qui_saida,
    f.jornada_intervalo_entrada,
    f.jornada_intervalo_saida
FROM funcionarios f 
WHERE f.ativo = TRUE 
LIMIT 5;
