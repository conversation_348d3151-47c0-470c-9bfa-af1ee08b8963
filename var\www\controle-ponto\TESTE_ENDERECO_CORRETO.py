#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TESTE COM ENDEREÇO CORRETO
==========================
"""

import requests

def teste_endereco_correto():
    """Testa com o endereço correto sem porta 5000"""
    
    print("🔍 TESTANDO COM ENDEREÇO CORRETO...")
    
    # URL CORRETA (sem porta 5000)
    base_url = "http://10.19.208.31"
    session = requests.Session()
    
    # Login
    login_data = {
        'usuario': 'admin',
        'senha': '@Ric6109'
    }
    
    try:
        print(f"🔐 Fazendo login em: {base_url}/login")
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code in [200, 302]:
            print("✅ Login OK")
            
            # Testar página de configurações
            print(f"🔍 Acessando: {base_url}/configuracoes/")
            config_response = session.get(f"{base_url}/configuracoes/")
            
            if config_response.status_code == 200:
                content = config_response.text
                
                print(f"✅ Página carregada (Tamanho: {len(content)})")
                
                # Verificações
                checks = [
                    ("🔥 BIOMETRIA ATIVA", "🔥 BIOMETRIA ATIVA" in content),
                    ("TOTALMENTE FUNCIONAL", "TOTALMENTE FUNCIONAL" in content),
                    ("biometric-highlight", "biometric-highlight" in content),
                    ("Sistema Online", "Sistema Online" in content),
                    ("Tab Geral", 'id="geral"' in content),
                    ("Tab Sistema", 'id="sistema"' in content),
                    ("Configurar Biometria", "Configurar Biometria" in content)
                ]
                
                print("\n📋 VERIFICAÇÕES COM ENDEREÇO CORRETO:")
                all_ok = True
                for check_name, result in checks:
                    status = "✅" if result else "❌"
                    print(f"{status} {check_name}")
                    if not result:
                        all_ok = False
                
                if all_ok:
                    print("\n🎉 TODAS AS VERIFICAÇÕES PASSARAM!")
                    print("✅ Configurações totalmente funcionais!")
                    print(f"🌐 ACESSE: {base_url}/configuracoes/")
                else:
                    print("\n⚠️ Algumas verificações falharam")
                    
            else:
                print(f"❌ Erro ao carregar página: {config_response.status_code}")
                # Tentar sem barra no final
                print(f"🔍 Tentando: {base_url}/configuracoes")
                config_response2 = session.get(f"{base_url}/configuracoes")
                if config_response2.status_code == 200:
                    print("✅ Funciona sem barra no final!")
                else:
                    print(f"❌ Também falhou: {config_response2.status_code}")
        else:
            print(f"❌ Erro no login: {login_response.status_code}")
            
    except Exception as e:
        print(f"❌ Erro de conexão: {e}")

if __name__ == "__main__":
    teste_endereco_correto() 