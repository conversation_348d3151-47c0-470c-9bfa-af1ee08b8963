#!/usr/bin/env python3
"""
Teste específico da função de desligamento
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import FuncionarioQueries, DatabaseManager

def testar_desligamento():
    """Testa a função de desligamento"""
    print("🧪 TESTE DE DESLIGAMENTO")
    print("=" * 50)
    
    try:
        # 1. Verificar se o funcionário TESTE 5 existe
        print("\n1. Verificando funcionário TESTE 5...")
        funcionario = DatabaseManager.execute_query(
            "SELECT * FROM funcionarios WHERE nome_completo LIKE %s",
            ('%TESTE 5%',)
        )
        
        if not funcionario:
            print("❌ Funcionário TESTE 5 não encontrado")
            return False
            
        funcionario = funcionario[0]
        print(f"✅ Funcionário encontrado: ID {funcionario['id']}, Matrícula {funcionario['matricula_empresa']}")
        
        # 2. Testar função de desligamento
        print("\n2. Testando função de desligamento...")
        
        success = FuncionarioQueries.desligar_funcionario(
            funcionario_id=funcionario['id'],
            motivo_desligamento='Demissao_sem_justa_causa',
            observacoes='Teste de desligamento via script',
            usuario_responsavel=1  # Admin
        )
        
        if success:
            print("✅ Desligamento executado com sucesso!")
            
            # Verificar se foi movido para funcionarios_desligados
            desligado = DatabaseManager.execute_query(
                "SELECT * FROM funcionarios_desligados WHERE funcionario_id_original = %s",
                (funcionario['id'],)
            )
            
            if desligado:
                print(f"✅ Funcionário movido para funcionarios_desligados")
                print(f"   - Data desligamento: {desligado[0]['data_desligamento']}")
                print(f"   - Motivo: {desligado[0]['motivo_desligamento']}")
            else:
                print("❌ Funcionário NÃO foi movido para funcionarios_desligados")
                
            # Verificar log
            log = DatabaseManager.execute_query(
                "SELECT * FROM log_desligamentos WHERE funcionario_id_original = %s",
                (funcionario['id'],)
            )
            
            if log:
                print(f"✅ Log de desligamento criado")
            else:
                print("❌ Log de desligamento NÃO foi criado")
                
        else:
            print("❌ Falha no desligamento")
            
        return success
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    testar_desligamento()
