#!/usr/bin/env python3
import paramiko

def corrigir_aba_empresas():
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🔧 CORRIGINDO ABA EMPRESAS")
        print("=" * 50)
        
        # 1. Fazer backup do arquivo atual
        print("1. Fazendo backup...")
        stdin, stdout, stderr = ssh.exec_command('''
        cd /var/www/controle-ponto/templates/configuracoes
        cp index.html index.html.backup_empresas_$(date +%Y%m%d_%H%M%S)
        ''')
        
        # 2. Modificar a aba empresas para carregar conteúdo diretamente
        print("2. Modificando aba empresas...")
        
        # Criar o novo conteúdo da aba empresas
        novo_conteudo_empresas = '''
            <!-- Tab Empresas -->
            <div class="tab-pane fade" id="empresas" role="tabpanel" aria-labelledby="empresas-tab">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-building"></i>
                        Gerenciamento de Empresas
                    </h4>
                    
                    <!-- Carregamento dinâmico de empresas -->
                    <div id="empresas-content">
                        <div class="text-center p-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                            <p>Carregando empresas...</p>
                        </div>
                    </div>
                </div>
            </div>
        '''
        
        # 3. Adicionar JavaScript para carregar empresas
        print("3. Adicionando JavaScript para carregar empresas...")
        
        js_carregar_empresas = '''
        
        // Função para carregar empresas na aba
        async function carregarEmpresas() {
            const container = document.getElementById('empresas-content');
            
            try {
                console.log('🔄 Carregando empresas...');
                
                // Mostrar loading
                container.innerHTML = `
                    <div class="text-center p-4">
                        <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                        <p>Carregando empresas...</p>
                    </div>
                `;
                
                // Fazer requisição para listar empresas
                const response = await fetch('/configuracoes/empresas');
                
                if (response.ok) {
                    const html = await response.text();
                    
                    // Extrair apenas o conteúdo da lista de empresas
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const empresasContainer = doc.querySelector('.empresas-container');
                    
                    if (empresasContainer) {
                        container.innerHTML = empresasContainer.innerHTML;
                    } else {
                        // Se não conseguir extrair, mostrar conteúdo básico
                        container.innerHTML = `
                            <div class="action-grid">
                                <div class="action-card">
                                    <div class="icon"><i class="fas fa-list"></i></div>
                                    <h5>Gerenciar Empresas</h5>
                                    <p>Visualizar e gerenciar empresas cadastradas</p>
                                    <a href="/configuracoes/empresas" class="btn-professional btn-success">
                                        <i class="fas fa-building"></i>Ver Empresas
                                    </a>
                                </div>
                                
                                <div class="action-card">
                                    <div class="icon"><i class="fas fa-plus-circle"></i></div>
                                    <h5>Nova Empresa</h5>
                                    <p>Cadastrar uma nova empresa no sistema</p>
                                    <a href="/configuracoes/empresas/nova" class="btn-professional btn-primary">
                                        <i class="fas fa-plus"></i>Cadastrar
                                    </a>
                                </div>
                            </div>
                        `;
                    }
                    
                    console.log('✅ Empresas carregadas com sucesso!');
                    
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                console.error('❌ Erro ao carregar empresas:', error);
                
                // Mostrar conteúdo de fallback
                container.innerHTML = `
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-list"></i></div>
                            <h5>Gerenciar Empresas</h5>
                            <p>Visualizar e gerenciar empresas cadastradas</p>
                            <a href="/configuracoes/empresas" class="btn-professional btn-success">
                                <i class="fas fa-building"></i>Ver Empresas
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-plus-circle"></i></div>
                            <h5>Nova Empresa</h5>
                            <p>Cadastrar uma nova empresa no sistema</p>
                            <a href="/configuracoes/empresas/nova" class="btn-professional btn-primary">
                                <i class="fas fa-plus"></i>Cadastrar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-exclamation-triangle"></i></div>
                            <h5>Erro ao Carregar</h5>
                            <p>Não foi possível carregar a lista de empresas</p>
                            <button onclick="carregarEmpresas()" class="btn-professional btn-warning">
                                <i class="fas fa-refresh"></i>Tentar Novamente
                            </button>
                        </div>
                    </div>
                `;
            }
        }
        '''
        
        # 4. Aplicar as correções no servidor
        stdin, stdout, stderr = ssh.exec_command(f'''
        cd /var/www/controle-ponto/templates/configuracoes
        
        # Substituir a aba empresas
        sed -i '/<!-- Tab Empresas -->/,/<!-- Tab Usuários -->/{{
            /<!-- Tab Empresas -->/r /dev/stdin
            /<!-- Tab Usuários -->/!d
        }}' index.html << 'EOF'
{novo_conteudo_empresas}
            <!-- Tab Usuários -->
EOF
        
        # Adicionar JavaScript antes do fechamento do script
        sed -i '/console.log.*Abas inicializadas/a\\
{js_carregar_empresas}' index.html
        
        echo "Correções aplicadas!"
        ''')
        
        result = stdout.read().decode('utf-8', errors='ignore')
        print(result)
        
        # 5. Modificar o JavaScript existente para chamar carregarEmpresas
        print("4. Modificando JavaScript para carregar empresas automaticamente...")
        
        stdin, stdout, stderr = ssh.exec_command('''
        cd /var/www/controle-ponto/templates/configuracoes
        
        # Adicionar chamada para carregar empresas quando a aba for clicada
        sed -i '/showTabContent(targetId);/a\\
                \\
                // Carregar conteúdo específico da aba\\
                if (targetId === "empresas") {\\
                    setTimeout(() => carregarEmpresas(), 100);\\
                }' index.html
        
        echo "JavaScript modificado!"
        ''')
        
        js_result = stdout.read().decode('utf-8', errors='ignore')
        print(js_result)
        
        # 6. Reiniciar serviço
        print("5. Reiniciando serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl restart controle-ponto')
        
        # 7. Verificar status
        stdin, stdout, stderr = ssh.exec_command('systemctl is-active controle-ponto')
        status = stdout.read().decode('utf-8', errors='ignore').strip()
        print(f"Status do serviço: {status}")
        
        ssh.close()
        
        print("\n" + "=" * 50)
        print("🎉 CORREÇÃO DA ABA EMPRESAS CONCLUÍDA!")
        print("=" * 50)
        print("✅ Backup criado")
        print("✅ Aba empresas modificada")
        print("✅ JavaScript adicionado")
        print("✅ Serviço reiniciado")
        print("\n🔗 TESTE AGORA:")
        print("Acesse: http://************:5000/configuracoes/")
        print("E clique na aba Empresas!")
        
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    corrigir_aba_empresas()
