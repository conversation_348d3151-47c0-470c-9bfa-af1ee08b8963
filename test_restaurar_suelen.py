#!/usr/bin/env python3
"""
Teste da restauração com Suelen
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager, FuncionarioQueries

def test_restaurar_suelen():
    """Testa a restauração da Suelen"""
    print("🔍 TESTE: RESTAURAÇÃO DA SUELEN")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar se Suelen está na tabela de desligados
        print("📋 1. VERIFICANDO SUELEN NA TABELA DE DESLIGADOS:")
        suelen_desligados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa, data_desligamento
            FROM funcionarios_desligados 
            WHERE nome_completo LIKE %s
        """, ('%SUELEN%',))
        
        if not suelen_desligados:
            print("   ❌ Suelen não encontrada na tabela de desligados")
            return False
        
        suelen = suelen_desligados[0]
        print(f"   ✅ Suelen encontrada:")
        print(f"      ID Original: {suelen['funcionario_id_original']}")
        print(f"      Matrícula: {suelen['matricula_empresa']}")
        print(f"      Data Desligamento: {suelen['data_desligamento']}")
        
        # 2. Verificar se existe na tabela principal
        print(f"\n📋 2. VERIFICANDO SUELEN NA TABELA PRINCIPAL:")
        suelen_principal = db.execute_query("""
            SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
            FROM funcionarios 
            WHERE matricula_empresa = %s
        """, (suelen['matricula_empresa'],))
        
        if suelen_principal:
            func = suelen_principal[0]
            status_ativo = "ATIVO" if func['ativo'] else "INATIVO"
            print(f"   ✅ Suelen encontrada na tabela principal:")
            print(f"      ID: {func['id']}, Status: {func['status_cadastro']}, Ativo: {status_ativo}")
        else:
            print("   ❌ Suelen não encontrada na tabela principal")
        
        # 3. Testar restauração
        print(f"\n📋 3. TESTANDO RESTAURAÇÃO:")
        resultado = FuncionarioQueries.restaurar_funcionario(suelen['funcionario_id_original'])
        
        print(f"   Success: {resultado['success']}")
        print(f"   Message: {resultado['message']}")
        
        if resultado['success']:
            print("   ✅ SUCESSO: Função de restauração funcionou!")
            
            # 4. Verificar resultado
            print(f"\n📋 4. VERIFICANDO RESULTADO:")
            
            # Verificar na tabela principal
            suelen_restaurada = db.execute_query("""
                SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
                FROM funcionarios 
                WHERE matricula_empresa = %s
            """, (suelen['matricula_empresa'],))
            
            if suelen_restaurada:
                func = suelen_restaurada[0]
                status_ativo = "ATIVO" if func['ativo'] else "INATIVO"
                print(f"   ✅ Suelen na tabela principal:")
                print(f"      ID: {func['id']}, Status: {func['status_cadastro']}, Ativo: {status_ativo}")
                
                if func['ativo'] and func['status_cadastro'] == 'Ativo':
                    print("   ✅ PERFEITO: Suelen está ativa!")
                else:
                    print("   ❌ PROBLEMA: Suelen não está ativa")
                    return False
            else:
                print("   ❌ Suelen não encontrada na tabela principal")
                return False
            
            # Verificar se foi removida da tabela de desligados
            suelen_ainda_desligada = db.execute_query("""
                SELECT COUNT(*) as total
                FROM funcionarios_desligados 
                WHERE funcionario_id_original = %s
            """, (suelen['funcionario_id_original'],))
            
            if suelen_ainda_desligada[0]['total'] == 0:
                print("   ✅ Suelen removida da tabela de desligados")
            else:
                print("   ⚠️ Suelen ainda está na tabela de desligados")
            
            return True
        else:
            print(f"   ❌ FALHA: {resultado['message']}")
            return False
        
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🎯 TESTE ESPECÍFICO: RESTAURAÇÃO DA SUELEN")
    print("=" * 60)
    
    sucesso = test_restaurar_suelen()
    
    if sucesso:
        print("\n🎉 SUCESSO TOTAL!")
        print("✅ Função de restaurar funcionário funcionando perfeitamente")
        print("✅ Suelen restaurada com sucesso")
    else:
        print("\n❌ FALHA!")
        print("❌ Função de restaurar ainda tem problemas")
