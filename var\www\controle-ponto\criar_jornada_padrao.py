#!/usr/bin/env python3
"""
Script para criar jornada padrão "Primeiro Turno" para a empresa principal
"""

import mysql.connector
import sys
from datetime import datetime

def main():
    try:
        # Conectar ao banco
        conn = mysql.connector.connect(
            host='localhost',
            user='cavalcrod',
            password='200381',
            database='controle_ponto'
        )
        cursor = conn.cursor(dictionary=True)
        
        print("🔍 Buscando empresa principal...")
        
        # Buscar empresa principal
        cursor.execute("SELECT id, razao_social FROM empresas WHERE empresa_principal = TRUE LIMIT 1")
        empresa = cursor.fetchone()
        
        if not empresa:
            print("❌ Empresa principal não encontrada!")
            return
            
        print(f"✅ Empresa principal encontrada: {empresa['razao_social']} (ID: {empresa['id']})")
        
        # Verificar se já existe jornada "Primeiro Turno"
        cursor.execute("""
            SELECT id, nome_jornada FROM jornadas_trabalho 
            WHERE empresa_id = %s AND nome_jornada = 'Primeiro Turno'
        """, (empresa['id'],))
        
        jornada_existente = cursor.fetchone()
        
        if jornada_existente:
            print(f"⚠️  Jornada 'Primeiro Turno' já existe (ID: {jornada_existente['id']})")
            return
            
        print("📝 Criando jornada padrão 'Primeiro Turno'...")
        
        # Criar jornada padrão
        sql_insert = """
        INSERT INTO jornadas_trabalho (
            empresa_id, nome_jornada, descricao, tipo_jornada, categoria_funcionario,
            seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
            intervalo_inicio, intervalo_fim, intervalo_duracao_minutos,
            intervalo_obrigatorio, tolerancia_entrada_minutos, 
            ativa, padrao, ordem_exibicao, cadastrado_por
        ) VALUES (
            %s, 'Primeiro Turno', 
            'Jornada padrão do primeiro turno - horário comercial',
            'Diurno', 'Geral',
            '09:00:00', '18:00:00', '09:00:00', '18:00:00',
            '13:00:00', '14:00:00', 60,
            TRUE, 5, TRUE, TRUE, 1, 'sistema'
        )
        """
        
        cursor.execute(sql_insert, (empresa['id'],))
        jornada_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ Jornada 'Primeiro Turno' criada com sucesso!")
        print(f"   📋 ID da jornada: {jornada_id}")
        print(f"   🕘 Horário: 09:00 às 18:00 (Seg-Sex)")
        print(f"   🍽️  Intervalo: 13:00 às 14:00 (1h)")
        print(f"   ⏰ Tolerância: 5 minutos")
        print(f"   ⭐ Jornada padrão: SIM")
        
        cursor.close()
        conn.close()
        
        print("\n🎯 Jornada padrão criada com sucesso!")
        
    except mysql.connector.Error as e:
        print(f"❌ Erro de banco de dados: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
