#!/usr/bin/env python3
import paramiko
import os

def check_empresas_status():
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("Conectado ao servidor com sucesso!")
        
        # Verificar logs do serviço
        print("=== VERIFICANDO LOGS DO SERVIÇO ===")
        stdin, stdout, stderr = ssh.exec_command('journalctl -u controle-ponto -n 20 --no-pager')
        logs = stdout.read().decode()
        print(logs)
        
        # Verificar se as rotas estão registradas
        print("\n=== VERIFICANDO ROTAS REGISTRADAS ===")
        stdin, stdout, stderr = ssh.exec_command('grep -n "configuracoes" /var/www/controle-ponto/app.py')
        routes = stdout.read().decode()
        print("Rotas de configurações encontradas:")
        print(routes)
        
        # Verificar se o blueprint está sendo importado
        print("\n=== VERIFICANDO IMPORTS ===")
        stdin, stdout, stderr = ssh.exec_command('grep -n "app_configuracoes" /var/www/controle-ponto/app.py')
        imports = stdout.read().decode()
        print("Imports encontrados:")
        print(imports)
        
        # Verificar se o arquivo app_configuracoes.py existe
        print("\n=== VERIFICANDO ARQUIVO APP_CONFIGURACOES.PY ===")
        stdin, stdout, stderr = ssh.exec_command('ls -la /var/www/controle-ponto/app_configuracoes.py')
        file_check = stdout.read().decode()
        print(file_check)
        
        # Verificar se há erros de sintaxe no arquivo
        print("\n=== VERIFICANDO SINTAXE DO ARQUIVO ===")
        stdin, stdout, stderr = ssh.exec_command('cd /var/www/controle-ponto && python3 -m py_compile app_configuracoes.py')
        syntax_check = stderr.read().decode()
        if syntax_check:
            print("Erros de sintaxe encontrados:")
            print(syntax_check)
        else:
            print("Nenhum erro de sintaxe encontrado")
        
        # Testar importação do blueprint
        print("\n=== TESTANDO IMPORTAÇÃO DO BLUEPRINT ===")
        test_import = '''
cd /var/www/controle-ponto
python3 -c "
try:
    from app_configuracoes import configuracoes_bp
    print('✅ Blueprint importado com sucesso')
    print(f'URL prefix: {configuracoes_bp.url_prefix}')
    print(f'Rotas registradas: {len(configuracoes_bp.deferred_functions)}')
except Exception as e:
    print(f'❌ Erro ao importar blueprint: {e}')
"
'''
        stdin, stdout, stderr = ssh.exec_command(test_import)
        import_result = stdout.read().decode()
        import_error = stderr.read().decode()
        print("Resultado do teste de importação:")
        print(import_result)
        if import_error:
            print("Erros:")
            print(import_error)
        
        # Verificar se a tabela empresas existe
        print("\n=== VERIFICANDO TABELA EMPRESAS ===")
        stdin, stdout, stderr = ssh.exec_command('mysql -u root -p@Ric6109 controle_ponto -e "SHOW TABLES LIKE \'%empresa%\';"')
        tables = stdout.read().decode()
        print("Tabelas relacionadas a empresas:")
        print(tables)
        
        ssh.close()
        print("\nVerificação concluída!")
        
    except Exception as e:
        print(f"Erro durante a verificação: {e}")

if __name__ == "__main__":
    check_empresas_status()
