{% extends "base.html" %}

{% block title %}{{ titulo }} - <PERSON><PERSON>{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.css">
<style>
    /* ========================================
       RLPONTO-WEB VISUAL IDENTITY - v1.0
       SEGUINDO ESPECIFICAÇÕES DO VISUAL.MD
       ======================================== */

    :root {
        /* Cor primária - Verde-azulado (igual sidebar) */
        --primary-color: #4fbdba;
        --primary-hover: #26a69a;
        --primary-light: #80cbc4;
        --primary-dark: #00695c;

        /* Backgrounds */
        --background-color: #f9fafb;        /* Background principal */
        --card-background: #ffffff;         /* Fundo de cards */
        --hover-bg: #f3f4f6;               /* Hover states */
        --sidebar-bg: #ffffff;             /* Sidebar background */

        /* Textos */
        --text-primary: #1f2937;           /* Texto principal (preto) */
        --text-secondary: #6b7280;         /* Texto secundário (cinza médio) */
        --text-muted: #9ca3af;             /* Texto desabilitado */
        --text-white: #ffffff;             /* Texto branco */

        /* Bordas e divisores */
        --border-color: #e5e7eb;           /* Bordas padrão */
        --border-light: #f3f4f6;           /* Bordas claras */
        --border-dark: #d1d5db;            /* Bordas escuras */

        /* Estados */
        --success-color: #10b981;          /* Verde sucesso */
        --success-bg: #dcfce7;             /* Background sucesso */
        --success-text: #166534;           /* Texto sucesso */

        --warning-color: #f59e0b;          /* Amarelo aviso */
        --warning-bg: #fef3c7;             /* Background aviso */
        --warning-text: #92400e;           /* Texto aviso */

        --danger-color: #ef4444;           /* Vermelho erro */
        --danger-bg: #fee2e2;              /* Background erro */
        --danger-text: #dc2626;            /* Texto erro */

        --info-color: #3b82f6;             /* Azul informação */
        --info-bg: #dbeafe;                /* Background info */
        --info-text: #1e40af;              /* Texto info */

        /* Tamanhos de fonte */
        --font-size-xs: 0.75rem;      /* 12px - Textos muito pequenos */
        --font-size-sm: 0.875rem;     /* 14px - Textos pequenos */
        --font-size-base: 1rem;       /* 16px - Texto base */
        --font-size-lg: 1.125rem;     /* 18px - Textos grandes */
        --font-size-xl: 1.25rem;      /* 20px - Subtítulos */
        --font-size-2xl: 1.5rem;      /* 24px - Títulos */
        --font-size-3xl: 1.875rem;    /* 30px - Títulos principais */

        /* Espaçamentos */
        --spacing-xs: 0.25rem;    /* 4px */
        --spacing-sm: 0.5rem;     /* 8px */
        --spacing-md: 1rem;       /* 16px */
        --spacing-lg: 1.5rem;     /* 24px */
        --spacing-xl: 2rem;       /* 32px */
        --spacing-2xl: 3rem;      /* 48px */

        /* Border radius */
        --radius-sm: 6px;
        --radius-md: 8px;
        --radius-lg: 12px;
    }

    /* ========================================
       GLOBAL STYLES - PADRÃO RLPONTO-WEB
       ======================================== */

    body {
        background-color: var(--background-color);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        color: var(--text-primary);
        line-height: 1.5;
    }

    .main-container {
        background-color: var(--background-color);
        min-height: 100vh;
        padding: 2rem;
    }

    /* ========================================
       HEADER PADRÃO - SEGUINDO VISUAL.MD
       ======================================== */

    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        border-radius: var(--radius-lg);
        padding: 2rem;
        margin-bottom: 2rem;
        color: var(--text-white);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 60%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        transform: rotate(15deg);
    }

    .page-header h1 {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        position: relative;
        z-index: 2;
    }

    .page-header p {
        font-size: var(--font-size-lg);
        margin: 0;
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }

    /* ========================================
       CARDS DE ESTATÍSTICAS - PADRÃO RLPONTO-WEB
       ======================================== */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }

    .stat-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border-color: var(--border-dark);
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: var(--spacing-md);
        font-size: 1.5rem;
    }

    .stat-icon.success { background: var(--success-bg); color: var(--success-color); }
    .stat-icon.warning { background: var(--warning-bg); color: var(--warning-color); }
    .stat-icon.danger { background: var(--danger-bg); color: var(--danger-color); }
    .stat-icon.primary { background: var(--info-bg); color: var(--primary-color); }

    .stat-number {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        line-height: 1;
    }

    .stat-label {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;
    }

    /* ========================================
       FILTROS - PADRÃO RLPONTO-WEB
       ======================================== */
    .filters-container {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
        align-items: end;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-label {
        font-weight: 500;
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        border: 1px solid var(--border-color);
        border-radius: var(--radius-sm);
        padding: 0.5rem 0.75rem;
        background: var(--card-background);
        color: var(--text-primary);
        font-size: var(--font-size-base);
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
        outline: none;
    }

    .btn-search {
        background: var(--primary-color);
        border: 1px solid var(--primary-color);
        color: var(--text-white);
        border-radius: var(--radius-md);
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        height: 44px;
        white-space: nowrap;
        min-width: fit-content;
    }

    .btn-search:hover {
        background: var(--primary-hover);
        border-color: var(--primary-hover);
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-export {
        background: var(--success-color);
        border: 1px solid var(--success-color);
        color: var(--text-white);
        border-radius: var(--radius-md);
        padding: 0.5rem 1rem;
        font-weight: 500;
        font-size: var(--font-size-sm);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        text-decoration: none;
        white-space: nowrap;
    }

    .btn-export:hover {
        background: #059669;
        border-color: #059669;
        color: var(--text-white);
        text-decoration: none;
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    /* ========================================
       NOVOS ESTILOS MODERNOS - FILTROS
       ======================================== */

    .filters-header {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
    }

    .filters-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-white);
        font-size: 1.25rem;
        box-shadow: 0 4px 6px -1px rgba(79, 189, 186, 0.3);
    }

    .filters-content {
        flex: 1;
    }

    .filters-title {
        font-size: var(--font-size-xl);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 0.25rem 0;
    }

    .filters-subtitle {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        margin: 0;
    }

    .modern-select,
    .modern-date {
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        padding: 0.75rem;
        background: var(--card-background);
        color: var(--text-primary);
        font-size: var(--font-size-base);
        transition: all 0.3s ease;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    .modern-select:focus,
    .modern-date:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
        outline: none;
        transform: translateY(-1px);
    }

    .modern-select:hover,
    .modern-date:hover {
        border-color: var(--border-dark);
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    }

    /* Período de Datas Melhorado */
    .date-range-group {
        grid-column: span 2;
    }

    .date-range-container {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-sm);
    }

    .date-input-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .date-label {
        font-size: var(--font-size-xs);
        color: var(--text-muted);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .date-separator {
        color: var(--primary-color);
        font-size: 1.1rem;
        margin-top: 1.5rem;
        opacity: 0.7;
    }

    .quick-date-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .quick-date-btn {
        background: var(--background-color);
        border: 1px solid var(--border-color);
        color: var(--text-secondary);
        border-radius: var(--radius-sm);
        padding: 0.25rem 0.75rem;
        font-size: var(--font-size-xs);
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .quick-date-btn:hover {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: var(--text-white);
        transform: translateY(-1px);
    }

    /* Botões de Ação */
    .actions-group {
        display: flex;
        flex-direction: column;
        justify-content: end;
    }

    .action-buttons {
        display: flex;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
    }

    .btn-clear {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        color: var(--text-secondary);
        border-radius: var(--radius-md);
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        height: 44px;
        white-space: nowrap;
        min-width: fit-content;
    }

    .btn-clear:hover {
        background: var(--hover-bg);
        border-color: var(--border-dark);
        color: var(--text-primary);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-print {
        background: var(--info-color);
        border: 1px solid var(--info-color);
        color: var(--text-white);
        border-radius: var(--radius-md);
        padding: 0.5rem 1rem;
        font-weight: 500;
        font-size: var(--font-size-sm);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        text-decoration: none;
        white-space: nowrap;
    }

    .btn-print:hover {
        background: #2563eb;
        border-color: #2563eb;
        color: var(--text-white);
        text-decoration: none;
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    /* ========================================
       TABELAS - PADRÃO RLPONTO-WEB
       ======================================== */
    .table-container {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        margin-bottom: var(--spacing-xl);
    }

    .table-header {
        background-color: var(--hover-bg);
        border-bottom: 1px solid var(--border-color);
        padding: var(--spacing-lg);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }

    .table-header-left {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }

    .table-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-white);
        font-size: 1.1rem;
        box-shadow: 0 2px 4px -1px rgba(79, 189, 186, 0.3);
    }

    .table-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .table-title {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .table-subtitle {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        margin: 0;
        font-weight: 500;
    }

    .table-actions {
        display: flex;
        gap: var(--spacing-sm);
        align-items: center;
        flex-wrap: wrap;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .modern-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin: 0;
    }

    .modern-table thead th {
        background-color: var(--hover-bg);
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        font-size: var(--font-size-xs);
        letter-spacing: 0.5px;
        padding: var(--spacing-md);
        color: var(--text-secondary);
    }

    .modern-table tbody td {
        border: none;
        padding: var(--spacing-md);
        vertical-align: middle;
        border-bottom: 1px solid var(--border-light);
        font-size: var(--font-size-sm);
        color: var(--text-primary);
    }

    .modern-table tbody tr:hover {
        background-color: var(--hover-bg);
    }

    .modern-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* ========================================
       BADGES E STATUS - PADRÃO RLPONTO-WEB
       ======================================== */
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: var(--font-size-xs);
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-presente {
        background: var(--success-bg);
        color: var(--success-text);
        border: 1px solid var(--success-color);
    }
    .status-atrasado {
        background: var(--warning-bg);
        color: var(--warning-text);
        border: 1px solid var(--warning-color);
    }
    .status-ausente {
        background: var(--danger-bg);
        color: var(--danger-text);
        border: 1px solid var(--danger-color);
    }
    .status-completo {
        background: var(--info-bg);
        color: var(--info-text);
        border: 1px solid var(--info-color);
    }

    /* ========================================
       RESPONSIVIDADE MODERNIZADA
       ======================================== */
    @media (max-width: 768px) {
        .main-container {
            padding: 1rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .filter-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }

        .date-range-group {
            grid-column: span 1;
        }

        .date-range-container {
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .date-separator {
            transform: rotate(90deg);
            margin: 0;
        }

        .quick-date-buttons {
            justify-content: center;
        }

        .action-buttons {
            flex-direction: column;
            width: 100%;
        }

        .btn-search,
        .btn-clear {
            width: 100%;
            justify-content: center;
        }

        .table-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .table-header-left {
            width: 100%;
        }

        .table-actions {
            width: 100%;
            justify-content: center;
        }

        .btn-export,
        .btn-print {
            flex: 1;
            min-width: 120px;
        }

        .modern-table {
            font-size: 0.8rem;
        }

        .modern-table thead th,
        .modern-table tbody td {
            padding: 0.5rem;
        }

        .filters-header {
            flex-direction: column;
            text-align: center;
            gap: var(--spacing-sm);
        }

        .filters-icon {
            align-self: center;
        }
    }

    @media (max-width: 480px) {
        .table-actions {
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .btn-export,
        .btn-print {
            width: 100%;
        }

        .quick-date-buttons {
            flex-direction: column;
        }

        .quick-date-btn {
            width: 100%;
            text-align: center;
        }
    }

    /* ========================================
       LOADING E ESTADOS
       ======================================== */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .loading-spinner {
        background: white;
        padding: 2rem;
        border-radius: var(--border-radius);
        text-align: center;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--text-secondary);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    /* Utilities */
    .d-flex { display: flex; }
    .align-items-center { align-items: center; }
    .gap-2 { gap: 0.5rem; }
    .me-2 { margin-right: 0.5rem; }
    .fw-medium { font-weight: 500; }
    .text-muted { color: var(--text-secondary); }
    .rounded-circle { border-radius: 50%; }
    .visually-hidden { 
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        white-space: nowrap !important;
        border: 0 !important;
    }
    .mt-2 { margin-top: 0.5rem; }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Header da Página -->
    <div class="page-header">
        <h1>
            <i class="fas fa-chart-line me-3"></i>
            Relatórios de Ponto
        </h1>
        <p>Visualize e analise os registros de ponto dos funcionários</p>
    </div>

    <!-- ========================================
         CARDS DE ESTATÍSTICAS
         ======================================== -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon success">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-number" id="totalPresentes">{{ estatisticas.presentes or 0 }}</div>
            <div class="stat-label">Funcionários Presentes</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-number" id="totalAtrasos">{{ estatisticas.atrasados or 0 }}</div>
            <div class="stat-label">Chegadas Atrasadas</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon danger">
                <i class="fas fa-user-times"></i>
            </div>
            <div class="stat-number" id="totalAusentes">{{ estatisticas.ausentes or 0 }}</div>
            <div class="stat-label">Funcionários Ausentes</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon primary">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div class="stat-number" id="totalRegistros">{{ registros|length or 0 }}</div>
            <div class="stat-label">Registros de Ponto</div>
        </div>
    </div>

    <!-- ========================================
         FILTROS DE BUSCA MODERNOS - SEGUINDO VISUAL.MD
         ======================================== -->
    <div class="filters-container">
        <div class="filters-header">
            <div class="filters-icon">
                <i class="fas fa-filter"></i>
            </div>
            <div class="filters-content">
                <h3 class="filters-title">Filtros de Pesquisa</h3>
                <p class="filters-subtitle">Configure os parâmetros para buscar registros específicos</p>
            </div>
        </div>

        <form id="filtrosForm" method="POST" action="{{ url_for('relatorios.pagina_relatorio_pontos') }}">
            <div class="filter-grid">
                <!-- Filtro de Funcionário -->
                <div class="form-group">
                    <label for="funcionario_id" class="form-label">
                        <i class="fas fa-user me-1"></i>
                        Funcionário
                    </label>
                    <select class="form-control modern-select" id="funcionario_id" name="funcionario_id">
                        <option value="">Todos os funcionários</option>
                        {% for f in funcionarios %}
                        <option value="{{ f.id }}" {% if request.form.funcionario_id == f.id|string %}selected{% endif %}>
                            {{ f.nome_completo }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Filtro de Setor -->
                <div class="form-group">
                    <label for="setor" class="form-label">
                        <i class="fas fa-building me-1"></i>
                        Setor
                    </label>
                    <select class="form-control modern-select" id="setor" name="setor">
                        <option value="">Todos os setores</option>
                        {% for setor in funcionarios|map(attribute='setor_funcionario')|unique|list %}
                        {% if setor %}
                        <option value="{{ setor }}" {% if request.form.setor == setor %}selected{% endif %}>
                            {{ setor }}
                        </option>
                        {% endif %}
                        {% endfor %}
                    </select>
                </div>

                <!-- Período de Datas - MELHORADO -->
                <div class="form-group date-range-group">
                    <label class="form-label">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Período
                    </label>
                    <div class="date-range-container">
                        <div class="date-input-wrapper">
                            <label for="data_inicio" class="date-label">De:</label>
                            <input type="date" class="form-control modern-date" id="data_inicio" name="data_inicio"
                                   value="{{ request.form.data_inicio or data_atual }}">
                        </div>
                        <div class="date-separator">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="date-input-wrapper">
                            <label for="data_fim" class="date-label">Até:</label>
                            <input type="date" class="form-control modern-date" id="data_fim" name="data_fim"
                                   value="{{ request.form.data_fim or data_atual }}">
                        </div>
                    </div>
                    <!-- Botões de período rápido -->
                    <div class="quick-date-buttons">
                        <button type="button" class="quick-date-btn" onclick="setQuickDate('hoje')">Hoje</button>
                        <button type="button" class="quick-date-btn" onclick="setQuickDate('semana')">Esta Semana</button>
                        <button type="button" class="quick-date-btn" onclick="setQuickDate('mes')">Este Mês</button>
                    </div>
                </div>

                <!-- Filtro de Tipo -->
                <div class="form-group">
                    <label for="tipo_registro" class="form-label">
                        <i class="fas fa-clock me-1"></i>
                        Tipo de Registro
                    </label>
                    <select class="form-control modern-select" id="tipo_registro" name="tipo_registro">
                        <option value="">Todos os tipos</option>
                        <option value="entrada" {% if request.form.tipo_registro == 'entrada' %}selected{% endif %}>Entrada</option>
                        <option value="saida" {% if request.form.tipo_registro == 'saida' %}selected{% endif %}>Saída</option>
                        <option value="entrada_almoco" {% if request.form.tipo_registro == 'entrada_almoco' %}selected{% endif %}>Entrada Almoço</option>
                        <option value="saida_almoco" {% if request.form.tipo_registro == 'saida_almoco' %}selected{% endif %}>Saída Almoço</option>
                    </select>
                </div>

                <!-- Botões de Ação -->
                <div class="form-group actions-group">
                    <label class="form-label" style="visibility: hidden;">Ações</label>
                    <div class="action-buttons">
                        <button type="submit" class="btn-search">
                            <i class="fas fa-search"></i>
                            Buscar Registros
                        </button>
                        <button type="button" class="btn-clear" onclick="limparFiltros()">
                            <i class="fas fa-eraser"></i>
                            Limpar
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- ========================================
         TABELA DE REGISTROS MODERNIZADA
         ======================================== -->
    <div class="table-container">
        <div class="table-header">
            <div class="table-header-left">
                <div class="table-icon">
                    <i class="fas fa-table"></i>
                </div>
                <div class="table-info">
                    <h3 class="table-title">Registros de Ponto</h3>
                    <p class="table-subtitle">
                        {% if registros %}
                            {{ registros|length }} registro(s) encontrado(s)
                        {% else %}
                            Nenhum registro encontrado
                        {% endif %}
                    </p>
                </div>
            </div>

            <div class="table-actions">
                {% if registros %}
                <button id="btnImprimirPonto" class="btn-print" onclick="imprimirPonto()">
                    <i class="fas fa-print"></i>
                    Imprimir Ponto
                </button>
                <button id="btnExportarCSV" class="btn-export" onclick="exportarCSVComFiltros()">
                    <i class="fas fa-download"></i>
                    Exportar CSV
                </button>
                {% endif %}
            </div>
        </div>
        
        {% if registros %}
        <div class="table-responsive">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>Data</th>
                        <th>Funcionário</th>
                        <th>Setor</th>
                        <th>Horário</th>
                        <th>Tipo</th>
                        <th>Status</th>
                        <th>Observações</th>
                    </tr>
                </thead>
                <tbody>
                    {% for registro in registros %}
                    <tr>
                        <td>{{ registro.data_formatada }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if registro.foto_funcionario %}
                                <img src="data:image/jpeg;base64,{{ registro.foto_funcionario }}" 
                                     class="rounded-circle me-2" width="32" height="32" alt="Foto">
                                {% endif %}
                                <div>
                                    <div class="fw-medium">{{ registro.nome_funcionario }}</div>
                                </div>
                            </div>
                        </td>
                        <td>{{ registro.setor_funcionario or '-' }}</td>
                        <td>
                            <span class="fw-medium">{{ registro.horario_formatado }}</span>
                        </td>
                        <td>
                            <span class="status-badge 
                                {% if registro.tipo_registro == 'entrada' %}status-presente
                                {% elif registro.tipo_registro == 'saida' %}status-completo
                                {% elif 'almoco' in registro.tipo_registro %}status-warning
                                {% else %}status-primary{% endif %}">
                                {{ registro.tipo_registro_formatado }}
                            </span>
                        </td>
                        <td>
                            {% if registro.status_pontualidade %}
                            <span class="status-badge 
                                {% if registro.status_pontualidade == 'Pontual' %}status-presente
                                {% elif registro.status_pontualidade == 'Atrasado' %}status-atrasado
                                {% else %}status-ausente{% endif %}">
                                {{ registro.status_pontualidade }}
                            </span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if registro.observacoes %}
                            <span class="text-muted">{{ registro.observacoes }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-search"></i>
            <h4>Nenhum registro encontrado</h4>
            <p>Nenhum registro de ponto foi encontrado com os filtros selecionados.</p>
            <p class="text-muted">Tente ajustar os filtros de busca para ver os resultados.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Carregando...</span>
        </div>
        <p class="mt-2">Processando registros...</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// ========================================
// CONTROLE DE PONTO - JAVASCRIPT MODERNO
// Seguindo a filosofia RLPONTO-WEB
// ========================================

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('filtrosForm');
    const loadingOverlay = document.getElementById('loadingOverlay');
    
    // Configurar data atual como padrão
    const hoje = new Date().toISOString().split('T')[0];
    const dataInicio = document.getElementById('data_inicio');
    const dataFim = document.getElementById('data_fim');
    
    // Não precisamos mais definir valores padrão, pois a página já carrega com os filtros
    // aplicados para o dia atual e os campos já vêm preenchidos do servidor
    
    // Loading nos formulários
    if (form) {
        form.addEventListener('submit', function() {
            loadingOverlay.style.display = 'flex';
        });
    }
    
    // Auto-submit quando alterar filtros (opcional)
    const autoSubmitFields = ['funcionario_id', 'setor', 'tipo_registro'];
    autoSubmitFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('change', function() {
                // Opcional: auto-submit após pequeno delay
                // setTimeout(() => form.submit(), 300);
            });
        }
    });
    
    // Validação de datas
    dataInicio.addEventListener('change', function() {
        if (dataFim.value && dataInicio.value > dataFim.value) {
            dataFim.value = dataInicio.value;
        }
    });
    
    dataFim.addEventListener('change', function() {
        if (dataInicio.value && dataFim.value < dataInicio.value) {
            dataInicio.value = dataFim.value;
        }
    });
    
    console.log('Sistema RLPONTO-WEB carregado com sucesso! Dados do dia atual exibidos automaticamente.');
});

// Exportação de dados com filtros corretos
function exportarCSVComFiltros() {
    const form = document.getElementById('filtrosForm');
    const formData = new FormData(form);
    
    // Construir query string a partir dos dados do formulário
    const params = new URLSearchParams();
    
    // Adicionar todos os filtros do formulário
    for (let [key, value] of formData.entries()) {
        if (value && value.trim() !== '') {
            params.append(key, value);
        }
    }
    
    // Construir URL de exportação
    const baseUrl = '{{ url_for("relatorios.pontos_csv") }}';
    const exportUrl = params.toString() ? `${baseUrl}?${params.toString()}` : baseUrl;
    
    console.log('🔍 Exportando CSV com filtros:', params.toString());
    
    // Abrir URL em nova janela/aba
    window.open(exportUrl, '_blank');
}

// ========================================
// NOVAS FUNCIONALIDADES MODERNIZADAS
// ========================================

// Função para definir períodos rápidos de data
function setQuickDate(periodo) {
    const dataInicio = document.getElementById('data_inicio');
    const dataFim = document.getElementById('data_fim');
    const hoje = new Date();

    let inicio, fim;

    switch(periodo) {
        case 'hoje':
            inicio = fim = hoje.toISOString().split('T')[0];
            break;

        case 'semana':
            // Início da semana (segunda-feira)
            const inicioSemana = new Date(hoje);
            inicioSemana.setDate(hoje.getDate() - hoje.getDay() + 1);

            // Fim da semana (domingo)
            const fimSemana = new Date(inicioSemana);
            fimSemana.setDate(inicioSemana.getDate() + 6);

            inicio = inicioSemana.toISOString().split('T')[0];
            fim = fimSemana.toISOString().split('T')[0];
            break;

        case 'mes':
            // Primeiro dia do mês
            const inicioMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);

            // Último dia do mês
            const fimMes = new Date(hoje.getFullYear(), hoje.getMonth() + 1, 0);

            inicio = inicioMes.toISOString().split('T')[0];
            fim = fimMes.toISOString().split('T')[0];
            break;
    }

    if (dataInicio && dataFim) {
        dataInicio.value = inicio;
        dataFim.value = fim;

        // Adicionar feedback visual
        const buttons = document.querySelectorAll('.quick-date-btn');
        buttons.forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');

        // Auto-submit após definir período (opcional)
        // setTimeout(() => document.getElementById('filtrosForm').submit(), 500);
    }
}

// Função para limpar todos os filtros
function limparFiltros() {
    const form = document.getElementById('filtrosForm');

    // Limpar todos os campos
    document.getElementById('funcionario_id').value = '';
    document.getElementById('setor').value = '';
    document.getElementById('tipo_registro').value = '';

    // Resetar datas para hoje
    const hoje = new Date().toISOString().split('T')[0];
    document.getElementById('data_inicio').value = hoje;
    document.getElementById('data_fim').value = hoje;

    // Remover classe active dos botões de período
    const buttons = document.querySelectorAll('.quick-date-btn');
    buttons.forEach(btn => btn.classList.remove('active'));

    // Feedback visual
    const btnLimpar = event.target;
    const originalText = btnLimpar.innerHTML;
    btnLimpar.innerHTML = '<i class="fas fa-check"></i> Limpo!';
    btnLimpar.disabled = true;

    setTimeout(() => {
        btnLimpar.innerHTML = originalText;
        btnLimpar.disabled = false;
    }, 1500);

    console.log('🧹 Filtros limpos - resetados para o dia atual');
}

// Função para imprimir registros de ponto
function imprimirPonto() {
    const btnImprimir = document.getElementById('btnImprimirPonto');
    const originalText = btnImprimir.innerHTML;

    // Feedback visual
    btnImprimir.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Preparando...';
    btnImprimir.disabled = true;

    // Obter dados do formulário atual
    const form = document.getElementById('filtrosForm');
    const formData = new FormData(form);

    // Construir query string
    const params = new URLSearchParams();
    for (let [key, value] of formData.entries()) {
        if (value && value.trim() !== '') {
            params.append(key, value);
        }
    }

    // Adicionar parâmetro para impressão
    params.append('print', 'true');

    // Construir URL de impressão
    const baseUrl = window.location.pathname;
    const printUrl = params.toString() ? `${baseUrl}?${params.toString()}` : `${baseUrl}?print=true`;

    console.log('🖨️ Abrindo página de impressão:', printUrl);

    // Abrir em nova janela otimizada para impressão
    const printWindow = window.open(printUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

    // Restaurar botão após delay
    setTimeout(() => {
        btnImprimir.innerHTML = originalText;
        btnImprimir.disabled = false;

        // Focar na janela de impressão se ainda estiver aberta
        if (printWindow && !printWindow.closed) {
            printWindow.focus();
        }
    }, 2000);
}

// Função para atualizar estatísticas em tempo real (se necessário)
function atualizarEstatisticas() {
    // Implementar se necessário chamadas AJAX para atualizar stats
    console.log('Atualizando estatísticas...');
}

// Adicionar estilos dinâmicos para botões ativos
document.addEventListener('DOMContentLoaded', function() {
    // Adicionar CSS para botão ativo
    const style = document.createElement('style');
    style.textContent = `
        .quick-date-btn.active {
            background: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: var(--text-white) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px -1px rgba(79, 189, 186, 0.3);
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %} 