#!/usr/bin/env python3
import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import FuncionarioQueries, DatabaseManager

print("=== DEBUG ERRO PERSISTENTE ===")

# 1. Verificar se há funcionários disponíveis
print("1. Verificando funcionários disponíveis...")
try:
    funcionarios = DatabaseManager.execute_query("SELECT id, nome_completo, matricula_empresa FROM funcionarios ORDER BY id")
    
    if funcionarios:
        print("✅ Funcionários encontrados:")
        for func in funcionarios:
            print(f"   ID {func['id']}: {func['nome_completo']} (Matrícula: {func['matricula_empresa']})")
    else:
        print("❌ Nenhum funcionário encontrado!")
        
except Exception as e:
    print(f"❌ Erro ao buscar funcionários: {e}")

# 2. Testar função de desligamento diretamente
print("\n2. Testando função de desligamento diretamente...")
try:
    # Usar o primeiro funcionário disponível
    funcionarios = DatabaseManager.execute_query("SELECT id FROM funcionarios ORDER BY id LIMIT 1")
    
    if funcionarios:
        funcionario_id = funcionarios[0]['id']
        print(f"Testando com funcionário ID {funcionario_id}")
        
        # Testar get_by_id
        funcionario = FuncionarioQueries.get_by_id(funcionario_id)
        if funcionario:
            print(f"✅ get_by_id funcionando: {funcionario['nome_completo']}")
            
            # Testar desligamento
            success = FuncionarioQueries.desligar_funcionario(
                funcionario_id=funcionario_id,
                motivo_desligamento='Teste_debug_erro_persistente',
                observacoes='Debug do erro que persiste na interface',
                usuario_responsavel=1
            )
            
            print(f"✅ Resultado desligamento: {success}")
            
            if success:
                print("✅ Função de desligamento está funcionando!")
                
                # Verificar se foi movido para desligados
                desligados = DatabaseManager.execute_query(
                    "SELECT COUNT(*) as total FROM funcionarios_desligados WHERE funcionario_id_original = %s",
                    (funcionario_id,)
                )
                print(f"✅ Funcionário movido para desligados: {desligados[0]['total']} registro(s)")
                
            else:
                print("❌ Função de desligamento falhou!")
                
        else:
            print(f"❌ get_by_id falhou para ID {funcionario_id}")
            
    else:
        print("❌ Nenhum funcionário disponível para teste")
        
except Exception as e:
    print(f"❌ Erro no teste: {e}")

# 3. Verificar se há algum problema com a rota
print("\n3. Verificando estrutura da aplicação...")
try:
    from app_funcionarios import funcionarios_bp
    print("✅ Blueprint funcionarios importado com sucesso")
    
    # Verificar se a rota está registrada
    print("✅ Blueprint parece estar funcionando")
    
except Exception as e:
    print(f"❌ Erro ao importar blueprint: {e}")

print("\n=== FIM DO DEBUG ===")
print("\nSe a função de desligamento está funcionando aqui,")
print("o problema está na interface web ou na autenticação.")
