#!/usr/bin/env python3
"""
Debug específico do INSERT de funcionário
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager

def debug_insert_funcionario():
    """Debug do INSERT de funcionário"""
    print("🔍 DEBUG: INSERT DE FUNCIONÁRIO")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar dados do Kalebe na tabela de desligados
        print("📋 1. DADOS DO KALEBE NA TABELA DE DESLIGADOS:")
        kalebe_dados = db.execute_query("""
            SELECT *
            FROM funcionarios_desligados 
            WHERE nome_completo LIKE %s
        """, ('%KALEBE%',))
        
        if not kalebe_dados:
            print("   ❌ Kalebe não encontrado")
            return False
        
        kalebe = kalebe_dados[0]
        print(f"   ✅ Kalebe encontrado:")
        print(f"      Nome: {kalebe['nome_completo']}")
        print(f"      CPF: {kalebe['cpf']}")
        print(f"      Matrícula: {kalebe['matricula_empresa']}")
        print(f"      Status: {kalebe['status_cadastro']}")
        
        # 2. Tentar INSERT manual simples
        print(f"\n📋 2. TESTANDO INSERT MANUAL SIMPLES:")
        
        try:
            # INSERT básico com apenas campos essenciais
            insert_result = db.execute_query("""
                INSERT INTO funcionarios (
                    nome_completo, cpf, matricula_empresa, status_cadastro, ativo, empresa_id
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                kalebe['nome_completo'],
                kalebe['cpf'], 
                'TEST001',  # Matrícula temporária para teste
                'Ativo',
                True,
                kalebe['empresa_id']
            ))
            
            print(f"   ✅ INSERT simples funcionou!")
            
            # Verificar se foi criado
            funcionario_teste = db.execute_query("""
                SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
                FROM funcionarios 
                WHERE matricula_empresa = 'TEST001'
            """)
            
            if funcionario_teste:
                func = funcionario_teste[0]
                print(f"   ✅ Funcionário criado:")
                print(f"      ID: {func['id']}")
                print(f"      Nome: {func['nome_completo']}")
                print(f"      Status: {func['status_cadastro']}")
                print(f"      Ativo: {func['ativo']}")
                
                # Limpar teste
                db.execute_query("DELETE FROM funcionarios WHERE matricula_empresa = 'TEST001'")
                print(f"   ✅ Registro de teste removido")
                
            else:
                print(f"   ❌ Funcionário não foi criado")
                return False
            
        except Exception as e:
            print(f"   ❌ Erro no INSERT simples: {e}")
            return False
        
        # 3. Testar INSERT com todos os campos (como na função original)
        print(f"\n📋 3. TESTANDO INSERT COMPLETO:")
        
        campos_funcionario = [
            'nome_completo', 'cpf', 'rg', 'data_nascimento', 'sexo', 'estado_civil', 'nacionalidade',
            'ctps_numero', 'ctps_serie_uf', 'pis_pasep', 'endereco_rua', 'endereco_bairro',
            'endereco_cidade', 'endereco_cep', 'endereco_estado', 'telefone1', 'telefone2', 'email',
            'cargo', 'setor', 'setor_obra', 'matricula_empresa', 'data_admissao', 'tipo_contrato',
            'nivel_acesso', 'turno', 'tolerancia_ponto', 'banco_horas', 'hora_extra', 'status_cadastro',
            'horas_trabalho_obrigatorias', 'empresa_id', 'jornada_trabalho_id', 'horario_trabalho_id',
            'digital_dedo1', 'digital_dedo2', 'foto_3x4'
        ]
        
        try:
            placeholders = ', '.join(['%s'] * len(campos_funcionario))
            campos_str = ', '.join(campos_funcionario)
            
            insert_query = f"""
            INSERT INTO funcionarios ({campos_str})
            VALUES ({placeholders})
            """
            
            print(f"   Query: {insert_query[:100]}...")
            
            # Preparar valores
            valores = []
            for campo in campos_funcionario:
                if campo == 'status_cadastro':
                    valores.append('Ativo')  # Forçar como Ativo
                elif campo == 'matricula_empresa':
                    valores.append('TEST002')  # Matrícula temporária
                else:
                    valores.append(kalebe[campo])
            
            print(f"   Valores preparados: {len(valores)} campos")
            
            # Executar INSERT
            result = db.execute_query(insert_query, valores)
            print(f"   ✅ INSERT completo executado!")
            
            # Verificar resultado
            funcionario_completo = db.execute_query("""
                SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
                FROM funcionarios 
                WHERE matricula_empresa = 'TEST002'
            """)
            
            if funcionario_completo:
                func = funcionario_completo[0]
                print(f"   ✅ Funcionário completo criado:")
                print(f"      ID: {func['id']}")
                print(f"      Nome: {func['nome_completo']}")
                print(f"      Status: {func['status_cadastro']}")
                print(f"      Ativo: {func['ativo']}")
                
                # Atualizar ativo = TRUE
                db.execute_query("UPDATE funcionarios SET ativo = TRUE WHERE id = %s", (func['id'],))
                print(f"   ✅ Campo ativo atualizado")
                
                # Verificar novamente
                funcionario_final = db.execute_query("""
                    SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
                    FROM funcionarios 
                    WHERE id = %s
                """, (func['id'],))
                
                if funcionario_final:
                    func_final = funcionario_final[0]
                    print(f"   ✅ Estado final:")
                    print(f"      Status: {func_final['status_cadastro']}")
                    print(f"      Ativo: {func_final['ativo']}")
                
                # Limpar teste
                db.execute_query("DELETE FROM funcionarios WHERE matricula_empresa = 'TEST002'")
                print(f"   ✅ Registro de teste removido")
                
                return True
            else:
                print(f"   ❌ Funcionário completo não foi criado")
                return False
            
        except Exception as e:
            print(f"   ❌ Erro no INSERT completo: {e}")
            import traceback
            print(f"   Traceback: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"\n❌ ERRO NO DEBUG: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🎯 DEBUG ESPECÍFICO: INSERT DE FUNCIONÁRIO")
    print("=" * 60)
    
    sucesso = debug_insert_funcionario()
    
    if sucesso:
        print("\n🎉 DEBUG CONCLUÍDO!")
        print("✅ INSERT de funcionário funcionando")
    else:
        print("\n❌ PROBLEMAS NO INSERT!")
        print("❌ Verificar estrutura da tabela e dados")
