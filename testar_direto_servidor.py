#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste Direto no Servidor
========================

Script para testar diretamente no servidor se a página está funcionando.

Data: 07/07/2025
"""

import requests
import time

# Configurações
BASE_URL = "http://************"
LOGIN_URL = f"{BASE_URL}/login"
ALOCACOES_URL = f"{BASE_URL}/empresa-principal/alocacoes"

def testar_servidor_funcionando():
    """Verifica se o servidor está funcionando"""
    print("🔍 VERIFICANDO SERVIDOR...")
    
    try:
        response = requests.get(BASE_URL, timeout=10)
        if response.status_code == 200:
            print("✅ Servidor respondendo")
            return True
        else:
            print(f"❌ Servidor com problema: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Servidor não acessível: {e}")
        return False

def testar_pagina_alocacoes_detalhado():
    """Teste detalhado da página de alocações"""
    print("\n🎯 TESTE DETALHADO DA PÁGINA DE ALOCAÇÕES")
    print("=" * 60)
    
    # Criar sessão
    session = requests.Session()
    
    try:
        # 1. Login
        print("1️⃣ Fazendo login...")
        login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
        response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
        
        if response.status_code not in [200, 302]:
            print(f"❌ Falha no login: {response.status_code}")
            return False
        
        print("✅ Login realizado")
        
        # 2. Acessar página de alocações
        print("2️⃣ Acessando página de alocações...")
        response = session.get(ALOCACOES_URL)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # Análise detalhada do conteúdo
            print("3️⃣ Analisando conteúdo da página...")
            
            # Verificar se é redirecionamento
            if "window.location" in content or "redirect" in content.lower():
                print("   ⚠️ Página contém redirecionamento")
                
                # Procurar por redirecionamentos
                if "login" in content.lower():
                    print("   🔄 Redirecionamento para login detectado")
                    return False
            
            # Verificar mensagens de erro
            if "erro ao carregar alocações" in content.lower():
                print("   ❌ ERRO ENCONTRADO: 'erro ao carregar alocações'")
                
                # Procurar por flash messages
                if 'class="alert' in content:
                    print("   📋 Mensagem de alerta encontrada na página")
                    
                    # Extrair mensagens de alerta
                    import re
                    alerts = re.findall(r'<div[^>]*class="[^"]*alert[^"]*"[^>]*>(.*?)</div>', content, re.DOTALL)
                    for alert in alerts:
                        clean_alert = re.sub(r'<[^>]+>', '', alert).strip()
                        if clean_alert:
                            print(f"      💬 Alerta: {clean_alert}")
                
                return False
            
            # Verificar se a página carregou corretamente
            elementos_esperados = [
                "alocação de funcionários",
                "nova alocação",
                "total",
                "ativas",
                "funcionários"
            ]
            
            elementos_encontrados = []
            for elemento in elementos_esperados:
                if elemento in content.lower():
                    elementos_encontrados.append(elemento)
            
            print(f"   📊 Elementos encontrados: {len(elementos_encontrados)}/{len(elementos_esperados)}")
            
            if len(elementos_encontrados) >= 3:
                print("   ✅ Página parece estar carregando corretamente")
                
                # Verificar se há dados de alocações
                if "teste 2 turno" in content.lower() or "ainexus" in content.lower():
                    print("   ✅ Dados de alocações encontrados na página")
                    return True
                else:
                    print("   ⚠️ Dados de alocações não encontrados")
                    
                    # Verificar se mostra "nenhuma alocação"
                    if "nenhuma alocação" in content.lower():
                        print("   📋 Página mostra 'nenhuma alocação encontrada'")
                    
                    return True  # Página funciona, só não tem dados
            else:
                print("   ❌ Página não carregou corretamente")
                return False
        
        elif response.status_code == 500:
            print("   ❌ Erro interno do servidor (500)")
            return False
        elif response.status_code == 404:
            print("   ❌ Página não encontrada (404)")
            return False
        else:
            print(f"   ❌ Erro inesperado: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        return False

def main():
    """Função principal"""
    print("🧪 TESTE DIRETO NO SERVIDOR")
    print("=" * 60)
    
    # Verificar se servidor está funcionando
    if not testar_servidor_funcionando():
        print("\n❌ SERVIDOR NÃO ESTÁ FUNCIONANDO")
        return
    
    # Aguardar um pouco
    time.sleep(2)
    
    # Testar página de alocações
    sucesso = testar_pagina_alocacoes_detalhado()
    
    if sucesso:
        print(f"\n🎉 PÁGINA DE ALOCAÇÕES FUNCIONANDO!")
        print("✅ Botão verde está operacional")
        print(f"🔗 URL: {ALOCACOES_URL}")
    else:
        print(f"\n❌ PÁGINA DE ALOCAÇÕES COM PROBLEMAS")
        print("🔧 Verificar logs ou configuração do servidor")

if __name__ == "__main__":
    main()
