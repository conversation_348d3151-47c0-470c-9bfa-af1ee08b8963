#!/usr/bin/env python3
"""
Verificar: A alteração da empresa foi realmente salva?
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def verificar_alteracao_empresa():
    """Verificar se alteração foi salva"""
    
    print("🔍 VERIFICAÇÃO: ALTERAÇÃO DA EMPRESA FOI SALVA?")
    print("=" * 60)
    
    db = DatabaseManager()
    
    # 1. Verificar jornada atual da AiNexus
    print("\n📋 PASSO 1: Verificando jornada atual da AiNexus...")
    
    jornada_atual = db.execute_query("""
        SELECT jt.id, jt.nome_jornada, jt.seg_qui_entrada, jt.seg_qui_saida,
               jt.sexta_entrada, jt.sexta_saida, jt.tolerancia_entrada_minutos,
               jt.padrao, jt.ativa, e.nome_fantasia
        FROM jornadas_trabalho jt
        INNER JOIN empresas e ON jt.empresa_id = e.id
        WHERE e.id = 11 AND jt.padrao = 1 AND jt.ativa = 1
    """, fetch_one=True)
    
    if jornada_atual:
        print(f"🏢 Empresa: {jornada_atual['nome_fantasia']}")
        print(f"📋 Jornada: {jornada_atual['nome_jornada']} (ID: {jornada_atual['id']})")
        print(f"   • Seg-Qui: {jornada_atual['seg_qui_entrada']} às {jornada_atual['seg_qui_saida']}")
        print(f"   • Sexta: {jornada_atual['sexta_entrada']} às {jornada_atual['sexta_saida']}")
        print(f"   • Tolerância: {jornada_atual['tolerancia_entrada_minutos']} min")
        
        # Verificar se é 07:00 ou 09:00
        entrada = str(jornada_atual['seg_qui_entrada'])
        if '07:00' in entrada:
            print(f"✅ ALTERAÇÃO SALVA: Horário está 07:00!")
        elif '09:00' in entrada:
            print(f"❌ ALTERAÇÃO NÃO SALVA: Horário ainda está 09:00!")
            print(f"💡 A alteração na interface não foi persistida no banco!")
        else:
            print(f"🤔 Horário diferente: {entrada}")
    
    # 2. Verificar funcionário Richardson
    print(f"\n📋 PASSO 2: Verificando funcionário Richardson...")
    
    funcionario = db.execute_query("""
        SELECT f.id, f.nome_completo, f.jornada_trabalho_id,
               jt.nome_jornada, jt.seg_qui_entrada, jt.seg_qui_saida,
               jt.tolerancia_entrada_minutos
        FROM funcionarios f
        INNER JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.id = 35
    """, fetch_one=True)
    
    if funcionario:
        print(f"👤 {funcionario['nome_completo']} (ID: {funcionario['id']})")
        print(f"   • Jornada: {funcionario['nome_jornada']} (ID: {funcionario['jornada_trabalho_id']})")
        print(f"   • Horário: {funcionario['seg_qui_entrada']} às {funcionario['seg_qui_saida']}")
        print(f"   • Tolerância: {funcionario['tolerancia_entrada_minutos']} min")
        
        # Comparar com jornada da empresa
        if jornada_atual and funcionario['jornada_trabalho_id'] == jornada_atual['id']:
            print(f"✅ Funcionário está usando a jornada padrão da empresa")
            
            if funcionario['seg_qui_entrada'] == jornada_atual['seg_qui_entrada']:
                print(f"✅ Horários estão sincronizados")
            else:
                print(f"❌ ERRO: Horários diferentes!")
                print(f"   Funcionário: {funcionario['seg_qui_entrada']}")
                print(f"   Empresa: {jornada_atual['seg_qui_entrada']}")
        else:
            print(f"❌ Funcionário NÃO está usando jornada padrão da empresa!")
    
    # 3. Verificar logs de mudança recentes
    print(f"\n📋 PASSO 3: Verificando logs de mudança recentes...")
    
    try:
        logs_recentes = db.execute_query("""
            SELECT * FROM log_mudancas_jornada 
            WHERE data_mudanca >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            ORDER BY data_mudanca DESC
            LIMIT 5
        """)
        
        if logs_recentes:
            print(f"📊 Logs recentes encontrados:")
            for log in logs_recentes:
                print(f"   • {log['data_mudanca']}: {log['tipo_mudanca']} - {log['motivo']}")
        else:
            print(f"📊 Nenhum log de mudança recente")
    
    except Exception as e:
        print(f"❌ Erro ao verificar logs: {e}")
    
    # 4. Testar alteração manual
    print(f"\n📋 PASSO 4: Testando alteração manual para 07:00...")
    
    if jornada_atual and '09:00' in str(jornada_atual['seg_qui_entrada']):
        print(f"🔧 Aplicando alteração manual de 09:00 para 07:00...")
        
        try:
            # Alterar jornada para 07:00
            db.execute_query("""
                UPDATE jornadas_trabalho 
                SET seg_qui_entrada = '07:00:00',
                    sexta_entrada = '07:00:00'
                WHERE id = %s
            """, (jornada_atual['id'],), fetch_all=False)
            
            print(f"✅ Jornada alterada para 07:00!")
            print(f"🔄 Trigger deveria ter atualizado funcionários automaticamente...")
            
            # Verificar se funcionário foi atualizado
            funcionario_apos = db.execute_query("""
                SELECT f.nome_completo, jt.seg_qui_entrada
                FROM funcionarios f
                INNER JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
                WHERE f.id = 35
            """, fetch_one=True)
            
            if funcionario_apos:
                print(f"👤 Richardson após alteração: {funcionario_apos['seg_qui_entrada']}")
                
                if '07:00' in str(funcionario_apos['seg_qui_entrada']):
                    print(f"✅ SUCESSO! Funcionário agora tem horário 07:00!")
                    print(f"✅ TRIGGER FUNCIONOU PERFEITAMENTE!")
                else:
                    print(f"❌ Funcionário ainda não foi atualizado")
        
        except Exception as e:
            print(f"❌ Erro ao alterar jornada: {e}")
    
    print(f"\n🎯 CONCLUSÃO:")
    print(f"Se a alteração manual funcionou, o problema é que")
    print(f"a interface de edição da empresa NÃO está salvando as alterações!")

if __name__ == "__main__":
    verificar_alteracao_empresa()
