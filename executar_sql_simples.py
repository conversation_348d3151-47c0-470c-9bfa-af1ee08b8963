#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simples para executar SQL de empresa principal
Usando as mesmas configurações do projeto RLPONTO-WEB
"""

import sys
import os

# Tentar importar pymysql
try:
    import pymysql
    print("✅ PyMySQL disponível")
except ImportError:
    print("❌ PyMySQL não encontrado")
    print("Tentando instalar...")
    os.system("pip install pymysql")
    try:
        import pymysql
        print("✅ PyMySQL instalado e importado")
    except ImportError:
        print("❌ Falha ao instalar PyMySQL")
        sys.exit(1)

def main():
    print("🚀 RLPONTO-WEB - Estrutura de Empresa Principal")
    print("=" * 50)
    
    # Configurações do banco (baseadas na documentação)
    config = {
        'host': '************',
        'user': 'cavalcrod',
        'password': '200381',
        'database': 'controle_ponto',
        'charset': 'utf8mb4'
    }
    
    # Verificar arquivo SQL
    arquivo_sql = "sql/empresa_principal_clientes.sql"
    if not os.path.exists(arquivo_sql):
        print(f"❌ Arquivo não encontrado: {arquivo_sql}")
        return False
    
    print(f"📝 Arquivo SQL encontrado: {arquivo_sql}")
    
    # Conectar ao banco
    try:
        print("🔌 Conectando ao banco de dados...")
        conexao = pymysql.connect(**config)
        print("✅ Conectado com sucesso!")
        
        cursor = conexao.cursor()
        
        # Ler arquivo SQL
        with open(arquivo_sql, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print(f"📊 Arquivo SQL carregado: {len(sql_content)} caracteres")
        
        # Executar comandos SQL mais importantes manualmente
        comandos_principais = [
            # Adicionar colunas na tabela empresas
            """
            ALTER TABLE empresas 
            ADD COLUMN IF NOT EXISTS empresa_principal BOOLEAN DEFAULT FALSE 
            COMMENT 'Define se é a empresa principal/proprietária do sistema'
            """,
            
            """
            ALTER TABLE empresas 
            ADD COLUMN IF NOT EXISTS empresa_matriz_id INT NULL 
            COMMENT 'ID da empresa matriz (para filiais)'
            """,
            
            """
            ALTER TABLE empresas 
            ADD COLUMN IF NOT EXISTS tipo_empresa ENUM('principal', 'cliente', 'filial', 'independente') 
            DEFAULT 'independente' COMMENT 'Tipo da empresa no sistema'
            """,
            
            # Criar tabela empresa_clientes
            """
            CREATE TABLE IF NOT EXISTS empresa_clientes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                empresa_principal_id INT NOT NULL COMMENT 'ID da empresa principal',
                empresa_cliente_id INT NOT NULL COMMENT 'ID da empresa que atua como cliente',
                nome_contrato VARCHAR(200) NULL COMMENT 'Nome do contrato/projeto',
                data_inicio DATE NOT NULL COMMENT 'Data de início do contrato',
                data_fim DATE NULL COMMENT 'Data prevista de fim do contrato',
                status_contrato ENUM('ativo', 'pausado', 'finalizado', 'cancelado') DEFAULT 'ativo',
                ativo BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (empresa_principal_id) REFERENCES empresas(id) ON DELETE CASCADE,
                FOREIGN KEY (empresa_cliente_id) REFERENCES empresas(id) ON DELETE CASCADE,
                UNIQUE KEY unique_cliente_principal (empresa_principal_id, empresa_cliente_id),
                INDEX idx_empresa_principal (empresa_principal_id),
                INDEX idx_empresa_cliente (empresa_cliente_id)
            ) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
            """,
            
            # Criar tabela funcionario_alocacoes
            """
            CREATE TABLE IF NOT EXISTS funcionario_alocacoes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                funcionario_id INT UNSIGNED NOT NULL COMMENT 'ID do funcionário alocado',
                empresa_cliente_id INT NOT NULL COMMENT 'ID da empresa cliente',
                contrato_id INT NULL COMMENT 'ID do contrato específico',
                jornada_trabalho_id INT NOT NULL COMMENT 'ID da jornada de trabalho herdada',
                data_inicio DATE NOT NULL COMMENT 'Data de início da alocação',
                data_fim DATE NULL COMMENT 'Data de fim da alocação',
                ativo BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
                FOREIGN KEY (empresa_cliente_id) REFERENCES empresas(id) ON DELETE CASCADE,
                FOREIGN KEY (contrato_id) REFERENCES empresa_clientes(id) ON DELETE SET NULL,
                INDEX idx_funcionario (funcionario_id),
                INDEX idx_empresa_cliente (empresa_cliente_id)
            ) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
            """
        ]
        
        # Executar comandos
        sucessos = 0
        erros = 0
        
        for i, comando in enumerate(comandos_principais):
            try:
                print(f"⚙️ Executando comando {i+1}/{len(comandos_principais)}...")
                cursor.execute(comando)
                sucessos += 1
                print(f"✅ Comando {i+1} executado com sucesso")
                
            except Exception as e:
                print(f"⚠️ Erro no comando {i+1}: {e}")
                erros += 1
                # Continuar com próximo comando
                continue
        
        # Commit das alterações
        conexao.commit()
        print(f"✅ Execução concluída: {sucessos} sucessos, {erros} erros")
        
        # Verificar estrutura criada
        print("🔍 Verificando estrutura criada...")
        
        # Verificar colunas empresas
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'controle_ponto' 
            AND TABLE_NAME = 'empresas' 
            AND COLUMN_NAME IN ('empresa_principal', 'empresa_matriz_id', 'tipo_empresa')
        """)
        colunas = cursor.fetchone()[0]
        
        # Verificar tabelas
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'controle_ponto' 
            AND TABLE_NAME IN ('empresa_clientes', 'funcionario_alocacoes')
        """)
        tabelas = cursor.fetchone()[0]
        
        print(f"📊 Resultado da verificação:")
        print(f"   - Colunas adicionadas em 'empresas': {colunas}/3")
        print(f"   - Tabelas criadas: {tabelas}/2")
        
        if colunas >= 2 and tabelas >= 2:
            print("✅ Estrutura de empresa principal criada com sucesso!")
            return True
        else:
            print("⚠️ Estrutura criada parcialmente")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False
        
    finally:
        if 'conexao' in locals():
            conexao.close()
            print("🔌 Conexão encerrada")

if __name__ == "__main__":
    sucesso = main()
    print("🎉 Concluído!" if sucesso else "❌ Falhou!")
    input("Pressione Enter para sair...")
