#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Serviço de Biometria ZK4500 para Windows
----------------------------------------

Este serviço cria um servidor WebSocket que se comunica com o leitor biométrico ZK4500
e fornece uma API para o frontend web capturar e validar impressões digitais.

Requisitos:
- Python 3.6+
- websockets
- pyzkfp2 (SDK Python para ZK4500)
- pywin32 (para integração com Windows)

Uso:
    python zk4500_bridge.py

O serviço escuta na porta 8765 por padrão.
"""

import asyncio
import json
import logging
import os
import sys
import time
import traceback
from datetime import datetime
from pathlib import Path
import platform
import base64
import ctypes
from ctypes import wintypes
import threading

# Configuração de logging
log_dir = Path("C:/Logs/controle-ponto")
log_dir.mkdir(parents=True, exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / "biometria.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("biometria-service")

# Verifica se está rodando no Windows
if platform.system() != "Windows":
    logger.error("Este serviço só funciona no Windows")
    sys.exit(1)

try:
    import websockets
except ImportError:
    logger.error("Módulo 'websockets' não encontrado. Instale com: pip install websockets")
    sys.exit(1)

# Constantes
WEBSOCKET_PORT = 8765
CAPTURE_TIMEOUT = 30  # segundos
MIN_QUALITY = 60  # qualidade mínima aceitável (0-100)
MATCH_THRESHOLD = 55  # limiar para considerar duas digitais como iguais (0-100)

# Carrega a DLL do ZK4500
try:
    # Tenta carregar a DLL do ZK4500
    zk_dll_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "zkfp.dll")
    if not os.path.exists(zk_dll_path):
        # Tenta encontrar no diretório padrão de instalação
        program_files = os.environ.get("ProgramFiles", "C:\\Program Files")
        zk_dll_path = os.path.join(program_files, "ZKTeco", "ZK4500", "zkfp.dll")
    
    if os.path.exists(zk_dll_path):
        zkfp = ctypes.WinDLL(zk_dll_path)
        HAS_ZKFP = True
        logger.info(f"DLL do ZK4500 carregada: {zk_dll_path}")
    else:
        logger.warning(f"DLL do ZK4500 não encontrada em: {zk_dll_path}")
        HAS_ZKFP = False
except Exception as e:
    logger.error(f"Erro ao carregar DLL do ZK4500: {str(e)}")
    HAS_ZKFP = False

# Definições de funções da DLL
if HAS_ZKFP:
    try:
        # Inicialização
        zkfp.ZKFP_Init.restype = ctypes.c_int
        zkfp.ZKFP_Init.argtypes = []
        
        # Terminar
        zkfp.ZKFP_Terminate.restype = ctypes.c_int
        zkfp.ZKFP_Terminate.argtypes = []
        
        # Obter número de dispositivos
        zkfp.ZKFP_GetDeviceCount.restype = ctypes.c_int
        zkfp.ZKFP_GetDeviceCount.argtypes = []
        
        # Abrir dispositivo
        zkfp.ZKFP_OpenDevice.restype = ctypes.c_void_p
        zkfp.ZKFP_OpenDevice.argtypes = [ctypes.c_int]
        
        # Fechar dispositivo
        zkfp.ZKFP_CloseDevice.restype = ctypes.c_int
        zkfp.ZKFP_CloseDevice.argtypes = [ctypes.c_void_p]
        
        # Capturar impressão digital
        zkfp.ZKFP_AcquireFingerprint.restype = ctypes.c_int
        zkfp.ZKFP_AcquireFingerprint.argtypes = [
            ctypes.c_void_p,  # handle
            ctypes.POINTER(ctypes.c_ubyte),  # template
            ctypes.POINTER(ctypes.c_int),  # template size
            ctypes.POINTER(ctypes.c_ubyte),  # image
            ctypes.POINTER(ctypes.c_int)  # image size
        ]
        
        # Verificar qualidade
        zkfp.ZKFP_GetQuality.restype = ctypes.c_int
        zkfp.ZKFP_GetQuality.argtypes = [
            ctypes.POINTER(ctypes.c_ubyte),  # template
            ctypes.c_int  # template size
        ]
        
        # Comparar templates
        zkfp.ZKFP_MatchFinger.restype = ctypes.c_int
        zkfp.ZKFP_MatchFinger.argtypes = [
            ctypes.POINTER(ctypes.c_ubyte),  # template1
            ctypes.c_int,  # template1 size
            ctypes.POINTER(ctypes.c_ubyte),  # template2
            ctypes.c_int,  # template2 size
            ctypes.POINTER(ctypes.c_int)  # score
        ]
        
        logger.info("Funções da DLL do ZK4500 definidas com sucesso")
    except Exception as e:
        logger.error(f"Erro ao definir funções da DLL: {str(e)}")
        HAS_ZKFP = False

class BiometriaDevice:
    """Classe para gerenciar o dispositivo de biometria ZK4500"""
    
    def __init__(self):
        self.initialized = False
        self.device_handle = None
        self.device_info = {}
        self.last_error = None
        self.template_size = 2048
        self.image_size = 150 * 200  # Tamanho típico da imagem
        
        # Inicializa o dispositivo se o SDK estiver disponível
        if HAS_ZKFP:
            self._initialize_device()
        else:
            self.device_info = {
                "name": "ZK4500 (Simulação)",
                "serial": "SIM12345678",
                "version": "1.0.0",
                "status": "simulation"
            }
    
    def _initialize_device(self):
        """Inicializa o dispositivo ZK4500"""
        try:
            # Inicializa a biblioteca
            ret = zkfp.ZKFP_Init()
            if ret != 0:  # 0 = ZKFP_ERR_OK
                self.last_error = f"Falha ao inicializar SDK: {ret}"
                logger.error(self.last_error)
                return False
            
            # Obtém o número de dispositivos conectados
            device_count = zkfp.ZKFP_GetDeviceCount()
            if device_count <= 0:
                self.last_error = "Nenhum dispositivo biométrico encontrado"
                logger.error(self.last_error)
                return False
            
            # Abre o primeiro dispositivo
            self.device_handle = zkfp.ZKFP_OpenDevice(0)
            if not self.device_handle:
                self.last_error = "Falha ao abrir dispositivo biométrico"
                logger.error(self.last_error)
                return False
            
            # Define informações básicas do dispositivo
            self.device_info = {
                "name": "ZK4500",
                "serial": "Unknown",
                "version": "1.0.0",
                "status": "ready"
            }
            
            self.initialized = True
            logger.info(f"Dispositivo biométrico inicializado: {self.device_info}")
            return True
            
        except Exception as e:
            self.last_error = f"Erro ao inicializar dispositivo: {str(e)}"
            logger.error(self.last_error)
            logger.error(traceback.format_exc())
            return False
    
    def capture_fingerprint(self):
        """Captura uma impressão digital do dispositivo"""
        if not HAS_ZKFP:
            # Modo de simulação
            time.sleep(2)  # Simula o tempo de captura
            quality = 85
            template = os.urandom(512)
            image_data = os.urandom(self.image_size)
            
            return {
                "success": True,
                "quality": quality,
                "template": base64.b64encode(template).decode('utf-8'),
                "image": base64.b64encode(image_data).decode('utf-8'),
                "timestamp": datetime.now().isoformat()
            }
        
        if not self.initialized or not self.device_handle:
            return {
                "success": False,
                "error": "Dispositivo não inicializado"
            }
        
        try:
            # Aloca buffers para template e imagem
            template_buffer = (ctypes.c_ubyte * self.template_size)()
            template_size = ctypes.c_int(self.template_size)
            image_buffer = (ctypes.c_ubyte * self.image_size)()
            image_size = ctypes.c_int(self.image_size)
            
            # Captura a impressão digital
            ret = zkfp.ZKFP_AcquireFingerprint(
                self.device_handle,
                template_buffer,
                ctypes.byref(template_size),
                image_buffer,
                ctypes.byref(image_size)
            )
            
            if ret != 0:  # 0 = ZKFP_ERR_OK
                return {
                    "success": False,
                    "error": f"Falha ao capturar impressão digital: {ret}"
                }
            
            # Converte os buffers para bytes
            template = bytes(template_buffer[:template_size.value])
            image = bytes(image_buffer[:image_size.value])
            
            # Verifica a qualidade
            quality = zkfp.ZKFP_GetQuality(template_buffer, template_size.value)
            
            return {
                "success": True,
                "quality": quality,
                "template": base64.b64encode(template).decode('utf-8'),
                "image": base64.b64encode(image).decode('utf-8'),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.last_error = f"Erro ao capturar impressão digital: {str(e)}"
            logger.error(self.last_error)
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": self.last_error
            }
    
    def check_quality(self, template_base64):
        """Verifica a qualidade de um template de impressão digital"""
        if not HAS_ZKFP:
            # Modo de simulação
            return 85
        
        if not self.initialized:
            return 0
        
        try:
            # Converte o template base64 para bytes
            template = base64.b64decode(template_base64)
            
            # Copia para um buffer
            template_buffer = (ctypes.c_ubyte * len(template))(*template)
            
            # Verifica a qualidade
            quality = zkfp.ZKFP_GetQuality(template_buffer, len(template))
            return quality
            
        except Exception as e:
            self.last_error = f"Erro ao verificar qualidade: {str(e)}"
            logger.error(self.last_error)
            return 0
    
    def match_fingerprints(self, template1_base64, template2_base64):
        """Compara duas impressões digitais e retorna o score de similaridade"""
        if not HAS_ZKFP:
            # Modo de simulação
            time.sleep(1)
            return 80 if template1_base64[:10] == template2_base64[:10] else 30
        
        if not self.initialized:
            return 0
        
        try:
            # Converte os templates base64 para bytes
            template1 = base64.b64decode(template1_base64)
            template2 = base64.b64decode(template2_base64)
            
            # Copia para buffers
            template1_buffer = (ctypes.c_ubyte * len(template1))(*template1)
            template2_buffer = (ctypes.c_ubyte * len(template2))(*template2)
            
            # Compara os templates
            score = ctypes.c_int(0)
            ret = zkfp.ZKFP_MatchFinger(
                template1_buffer, len(template1),
                template2_buffer, len(template2),
                ctypes.byref(score)
            )
            
            if ret != 0:  # 0 = ZKFP_ERR_OK
                return 0
                
            return score.value
            
        except Exception as e:
            self.last_error = f"Erro ao comparar impressões digitais: {str(e)}"
            logger.error(self.last_error)
            return 0
    
    def close(self):
        """Fecha o dispositivo e libera recursos"""
        if HAS_ZKFP and self.initialized and self.device_handle:
            try:
                zkfp.ZKFP_CloseDevice(self.device_handle)
                zkfp.ZKFP_Terminate()
                logger.info("Dispositivo biométrico fechado")
            except Exception as e:
                logger.error(f"Erro ao fechar dispositivo: {str(e)}")
        
        self.initialized = False
        self.device_handle = None

class BiometriaService:
    """Serviço WebSocket para comunicação com o frontend"""
    
    def __init__(self):
        self.device = BiometriaDevice()
        self.clients = set()
        self.server = None
    
    async def start_server(self):
        """Inicia o servidor WebSocket"""
        try:
            self.server = await websockets.serve(
                self.handle_client,
                "localhost",  # Escuta apenas em localhost por segurança
                WEBSOCKET_PORT
            )
            logger.info(f"Servidor WebSocket iniciado na porta {WEBSOCKET_PORT}")
            
            # Mantém o servidor rodando
            await self.server.wait_closed()
            
        except Exception as e:
            logger.error(f"Erro ao iniciar servidor WebSocket: {str(e)}")
            logger.error(traceback.format_exc())
    
    async def handle_client(self, websocket, path):
        """Gerencia a conexão com um cliente WebSocket"""
        client_id = id(websocket)
        logger.info(f"Novo cliente conectado: {client_id}")
        
        # Adiciona o cliente à lista
        self.clients.add(websocket)
        
        try:
            # Envia informações do dispositivo ao cliente
            await self.send_device_info(websocket)
            
            # Envia o status do leitor
            await self.send_reader_status(websocket)
            
            # Processa mensagens do cliente
            async for message in websocket:
                await self.process_message(websocket, message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Cliente desconectado: {client_id}")
        except Exception as e:
            logger.error(f"Erro ao processar mensagem do cliente {client_id}: {str(e)}")
            logger.error(traceback.format_exc())
        finally:
            # Remove o cliente da lista
            self.clients.remove(websocket)
    
    async def send_device_info(self, websocket):
        """Envia informações do dispositivo para o cliente"""
        await self.send_message(websocket, {
            "type": "DEVICE_INFO",
            "data": self.device.device_info
        })
    
    async def send_reader_status(self, websocket):
        """Envia o status do leitor para o cliente"""
        await self.send_message(websocket, {
            "type": "READER_STATUS",
            "data": {
                "connected": self.device.initialized
            }
        })
    
    async def process_message(self, websocket, message):
        """Processa uma mensagem recebida do cliente"""
        try:
            data = json.loads(message)
            command = data.get("command")
            params = data.get("params", {})
            
            logger.debug(f"Comando recebido: {command}, Params: {params}")
            
            if command == "GET_DEVICE_INFO":
                await self.send_device_info(websocket)
                
            elif command == "CAPTURE":
                dedo_index = params.get("dedoIndex", 1)
                await self.handle_capture(websocket, dedo_index)
                
            elif command == "CHECK_QUALITY":
                template = params.get("template")
                if template:
                    quality = self.device.check_quality(template)
                    await self.send_message(websocket, {
                        "type": "STATUS",
                        "data": {
                            "qualidade": quality,
                            "valido": quality >= MIN_QUALITY
                        }
                    })
                else:
                    await self.send_error(websocket, "Template não fornecido")
                    
            elif command == "MATCH":
                template1 = params.get("template1")
                template2 = params.get("template2")
                if template1 and template2:
                    score = self.device.match_fingerprints(template1, template2)
                    await self.send_message(websocket, {
                        "type": "STATUS",
                        "data": {
                            "score": score,
                            "match": score >= MATCH_THRESHOLD
                        }
                    })
                else:
                    await self.send_error(websocket, "Templates não fornecidos")
                    
            else:
                await self.send_error(websocket, f"Comando desconhecido: {command}")
                
        except json.JSONDecodeError:
            await self.send_error(websocket, "Formato de mensagem inválido")
        except Exception as e:
            logger.error(f"Erro ao processar mensagem: {str(e)}")
            logger.error(traceback.format_exc())
            await self.send_error(websocket, f"Erro interno: {str(e)}")
    
    async def handle_capture(self, websocket, dedo_index):
        """Gerencia o processo de captura de impressão digital"""
        try:
            # Notifica o cliente que a captura está iniciando
            await self.send_message(websocket, {
                "type": "STATUS",
                "data": {
                    "status": "capturing",
                    "message": f"Capturando impressão digital do dedo {dedo_index}..."
                }
            })
            
            # Captura a impressão digital
            result = self.device.capture_fingerprint()
            
            if result["success"]:
                # Verifica se a qualidade é aceitável
                if result["quality"] < MIN_QUALITY:
                    await self.send_message(websocket, {
                        "type": "CAPTURE_RESULT",
                        "data": {
                            "success": False,
                            "error": f"Qualidade da impressão digital muito baixa ({result['quality']}%). Por favor, tente novamente.",
                            "quality": result["quality"]
                        }
                    })
                else:
                    # Envia o resultado da captura
                    await self.send_message(websocket, {
                        "type": "CAPTURE_RESULT",
                        "data": {
                            "success": True,
                            "template": result["template"],
                            "image": result["image"],
                            "quality": result["quality"],
                            "timestamp": result["timestamp"],
                            "dedoIndex": dedo_index
                        }
                    })
            else:
                # Envia o erro
                await self.send_message(websocket, {
                    "type": "CAPTURE_RESULT",
                    "data": {
                        "success": False,
                        "error": result["error"]
                    }
                })
                
        except Exception as e:
            logger.error(f"Erro durante captura: {str(e)}")
            logger.error(traceback.format_exc())
            await self.send_error(websocket, f"Erro durante captura: {str(e)}")
    
    async def send_message(self, websocket, message):
        """Envia uma mensagem para um cliente"""
        try:
            await websocket.send(json.dumps(message))
        except Exception as e:
            logger.error(f"Erro ao enviar mensagem: {str(e)}")
    
    async def send_error(self, websocket, error_message):
        """Envia uma mensagem de erro para um cliente"""
        await self.send_message(websocket, {
            "type": "ERROR",
            "data": {
                "message": error_message
            }
        })
    
    def stop(self):
        """Para o servidor e libera recursos"""
        if self.server:
            self.server.close()
        
        if self.device:
            self.device.close()
        
        logger.info("Serviço de biometria encerrado")

# Função para criar um ícone na bandeja do sistema (opcional)
def create_tray_icon():
    try:
        import win32api
        import win32con
        import win32gui
        
        def on_exit(icon, item):
            win32gui.DestroyWindow(hwnd)
            os._exit(0)
        
        # Cria uma janela oculta
        wc = win32gui.WNDCLASS()
        wc.hInstance = win32api.GetModuleHandle(None)
        wc.lpszClassName = "ZK4500BridgeClass"
        wc.lpfnWndProc = {
            win32con.WM_DESTROY: lambda hwnd, msg, wparam, lparam: win32gui.PostQuitMessage(0)
        }
        
        win32gui.RegisterClass(wc)
        hwnd = win32gui.CreateWindow(
            wc.lpszClassName,
            "ZK4500 Bridge",
            0,
            0, 0, 0, 0,
            0, 0, wc.hInstance, None
        )
        
        # Cria o ícone na bandeja
        flags = win32gui.NIF_ICON | win32gui.NIF_MESSAGE | win32gui.NIF_TIP
        nid = (hwnd, 0, flags, win32con.WM_USER + 20, win32gui.LoadIcon(0, win32con.IDI_APPLICATION), "ZK4500 Bridge")
        win32gui.Shell_NotifyIcon(win32gui.NIM_ADD, nid)
        
        # Menu de contexto
        def create_menu():
            menu = win32gui.CreatePopupMenu()
            win32gui.AppendMenu(menu, win32con.MF_STRING, 1023, "Sair")
            return menu
        
        # Processa mensagens
        def wnd_proc(hwnd, msg, wparam, lparam):
            if msg == win32con.WM_USER + 20:
                if lparam == win32con.WM_RBUTTONUP:
                    menu = create_menu()
                    pos = win32gui.GetCursorPos()
                    win32gui.SetForegroundWindow(hwnd)
                    win32gui.TrackPopupMenu(menu, win32con.TPM_LEFTALIGN, pos[0], pos[1], 0, hwnd, None)
                    win32gui.PostMessage(hwnd, win32con.WM_NULL, 0, 0)
            elif msg == win32con.WM_COMMAND:
                if wparam == 1023:  # Sair
                    win32gui.DestroyWindow(hwnd)
            elif msg == win32con.WM_DESTROY:
                win32gui.Shell_NotifyIcon(win32gui.NIM_DELETE, (hwnd, 0))
                win32gui.PostQuitMessage(0)
            
            return win32gui.DefWindowProc(hwnd, msg, wparam, lparam)
        
        win32gui.PumpMessages()
    except ImportError:
        logger.warning("Módulo pywin32 não encontrado. Ícone na bandeja não será criado.")
    except Exception as e:
        logger.error(f"Erro ao criar ícone na bandeja: {str(e)}")

# Função principal
def main():
    logger.info("Iniciando serviço de biometria ZK4500")
    
    # Cria o serviço
    service = BiometriaService()
    
    # Inicia o servidor em uma thread separada
    server_thread = threading.Thread(
        target=lambda: asyncio.run(service.start_server()),
        daemon=True
    )
    server_thread.start()
    
    try:
        # Cria o ícone na bandeja (opcional)
        create_tray_icon()
        
        # Se não tiver ícone na bandeja, mantém o programa rodando
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Serviço interrompido pelo usuário")
    finally:
        service.stop()

if __name__ == "__main__":
    main()
