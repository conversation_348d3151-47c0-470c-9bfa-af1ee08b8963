#!/usr/bin/env python3
"""
Verificar todos os funcionários na tabela principal
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager

def verificar_todos_funcionarios():
    """Verificar todos os funcionários"""
    print("🔍 VERIFICAÇÃO: TODOS OS FUNCIONÁRIOS")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar TODOS os funcionários na tabela principal
        print("📋 1. TODOS OS FUNCIONÁRIOS NA TABELA PRINCIPAL:")
        todos_funcionarios = db.execute_query("""
            SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
            FROM funcionarios 
            ORDER BY id DESC
            LIMIT 10
        """)
        
        if todos_funcionarios:
            print(f"   ✅ {len(todos_funcionarios)} funcionários encontrados:")
            for func in todos_funcionarios:
                status_ativo = "ATIVO" if func['ativo'] else "INATIVO"
                print(f"      - ID: {func['id']}, Nome: {func['nome_completo']}")
                print(f"        Matrícula: {func['matricula_empresa']}, Status: {func['status_cadastro']}, Ativo: {status_ativo}")
        else:
            print("   ❌ Nenhum funcionário encontrado")
        
        # 2. Verificar funcionários com matrícula específica
        print(f"\n📋 2. FUNCIONÁRIOS COM MATRÍCULA 0005 (KALEBE):")
        kalebe_funcionarios = db.execute_query("""
            SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
            FROM funcionarios 
            WHERE matricula_empresa = '0005'
        """)
        
        if kalebe_funcionarios:
            print(f"   ✅ {len(kalebe_funcionarios)} funcionários com matrícula 0005:")
            for func in kalebe_funcionarios:
                status_ativo = "ATIVO" if func['ativo'] else "INATIVO"
                print(f"      - ID: {func['id']}, Nome: {func['nome_completo']}")
                print(f"        Status: {func['status_cadastro']}, Ativo: {status_ativo}")
        else:
            print("   ❌ Nenhum funcionário com matrícula 0005")
        
        # 3. Verificar funcionários ativos
        print(f"\n📋 3. FUNCIONÁRIOS ATIVOS:")
        funcionarios_ativos = db.execute_query("""
            SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
            FROM funcionarios 
            WHERE ativo = TRUE
            ORDER BY nome_completo
        """)
        
        if funcionarios_ativos:
            print(f"   ✅ {len(funcionarios_ativos)} funcionários ativos:")
            for func in funcionarios_ativos:
                print(f"      - {func['nome_completo']} (Matrícula: {func['matricula_empresa']})")
        else:
            print("   ❌ Nenhum funcionário ativo")
        
        # 4. Verificar último ID inserido
        print(f"\n📋 4. ÚLTIMO ID INSERIDO:")
        ultimo_id = db.execute_query("""
            SELECT MAX(id) as ultimo_id FROM funcionarios
        """)
        
        if ultimo_id and ultimo_id[0]['ultimo_id']:
            ultimo_id_valor = ultimo_id[0]['ultimo_id']
            print(f"   ✅ Último ID: {ultimo_id_valor}")
            
            # Verificar funcionário com último ID
            ultimo_funcionario = db.execute_query("""
                SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo, data_cadastro
                FROM funcionarios 
                WHERE id = %s
            """, (ultimo_id_valor,))
            
            if ultimo_funcionario:
                func = ultimo_funcionario[0]
                status_ativo = "ATIVO" if func['ativo'] else "INATIVO"
                print(f"   ✅ Último funcionário inserido:")
                print(f"      - ID: {func['id']}, Nome: {func['nome_completo']}")
                print(f"      - Matrícula: {func['matricula_empresa']}")
                print(f"      - Status: {func['status_cadastro']}, Ativo: {status_ativo}")
                print(f"      - Data cadastro: {func['data_cadastro']}")
        else:
            print("   ❌ Nenhum funcionário na tabela")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERRO NA VERIFICAÇÃO: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🎯 VERIFICAÇÃO COMPLETA: TODOS OS FUNCIONÁRIOS")
    print("=" * 70)
    
    sucesso = verificar_todos_funcionarios()
    
    if sucesso:
        print("\n✅ VERIFICAÇÃO CONCLUÍDA!")
    else:
        print("\n❌ ERRO NA VERIFICAÇÃO!")
