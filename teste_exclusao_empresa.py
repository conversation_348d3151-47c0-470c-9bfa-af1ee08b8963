#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de teste para verificar a correção da exclusão de empresas
Data: 03/07/2025
"""

import sys
import os
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('teste_exclusao')

# Adicionar diretório atual ao path para importar módulos do projeto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.database import get_db_connection
    logger.info("✅ Módulo de banco de dados importado com sucesso")
except Exception as e:
    logger.error(f"❌ Erro ao importar módulo de banco de dados: {str(e)}")
    sys.exit(1)

def testar_formato_retorno():
    """
    Testa o formato de retorno do cursor para verificar se é dicionário ou tupla
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Testar consulta COUNT
        cursor.execute("SELECT COUNT(*) as total FROM empresas WHERE ativa = TRUE")
        resultado = cursor.fetchone()
        
        print("\n=== TESTE DE FORMATO DE RETORNO ===")
        print(f"Resultado bruto: {resultado}")
        print(f"Tipo do resultado: {type(resultado)}")
        
        # Testar acesso como dicionário
        if isinstance(resultado, dict):
            print("✅ Resultado é um DICIONÁRIO")
            total = resultado.get('total', 0)
            print(f"Valor acessado via .get('total'): {total}")
        else:
            print("❌ Resultado NÃO é um dicionário")
            
        # Testar acesso como tupla
        try:
            valor_tupla = resultado[0] if resultado else 0
            print(f"Valor acessado via [0]: {valor_tupla}")
            print("✅ Acesso via índice funciona")
        except (TypeError, KeyError) as e:
            print(f"❌ Erro ao acessar como tupla: {str(e)}")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao testar formato de retorno: {str(e)}")
        return False

def testar_exclusao_empresa(empresa_id):
    """
    Testa a lógica de exclusão de empresa sem realmente excluir
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Verificar se a empresa existe
        cursor.execute('SELECT id, razao_social, ativa FROM empresas WHERE id = %s', (empresa_id,))
        empresa_existe = cursor.fetchone()
        
        print(f"\n=== TESTE DE CONSULTA EMPRESA {empresa_id} ===")
        
        if not empresa_existe:
            print(f"❌ Empresa ID {empresa_id} não encontrada")
            conn.close()
            return False
            
        print(f"✅ Empresa existe: {empresa_existe}")
        
        # Testar acesso aos dados da empresa
        if isinstance(empresa_existe, dict):
            razao_social = empresa_existe.get('razao_social', 'Desconhecida')
            ativa = empresa_existe.get('ativa', False)
            print(f"Detectado DICIONÁRIO - Razão Social: {razao_social}, Ativa: {ativa}")
        else:
            razao_social = empresa_existe[1] if len(empresa_existe) > 1 else 'Desconhecida'
            ativa = empresa_existe[2] if len(empresa_existe) > 2 else False
            print(f"Detectado TUPLA - Razão Social: {razao_social}, Ativa: {ativa}")
        
        # Verificar funcionários ativos
        cursor.execute("""
            SELECT COUNT(*) as total FROM funcionarios
            WHERE empresa_id = %s AND ativo = TRUE
        """, (empresa_id,))
        
        resultado_consulta = cursor.fetchone()
        print(f"Resultado bruto da consulta: {resultado_consulta}")
        
        # Testar a correção implementada
        if isinstance(resultado_consulta, dict):
            funcionarios_ativos = resultado_consulta.get('total', 0)
            print(f"Detectado DICIONÁRIO - Funcionários ativos: {funcionarios_ativos}")
        else:
            funcionarios_ativos = resultado_consulta[0] if resultado_consulta else 0
            print(f"Detectado TUPLA - Funcionários ativos: {funcionarios_ativos}")
        
        print(f"CONDIÇÃO: funcionarios_ativos > 0 é {'TRUE' if funcionarios_ativos > 0 else 'FALSE'}")
        print(f"RESULTADO: Empresa {'NÃO' if funcionarios_ativos > 0 else 'PODE'} ser excluída {'❌' if funcionarios_ativos > 0 else '✅'}")
        
        # Verificar relações dependentes
        cursor.execute("SELECT COUNT(*) as total FROM jornadas_trabalho WHERE empresa_id = %s", (empresa_id,))
        jornadas = cursor.fetchone()
        jornadas_count = jornadas.get('total', 0) if isinstance(jornadas, dict) else (jornadas[0] if jornadas else 0)
        
        cursor.execute("SELECT COUNT(*) as total FROM horarios_trabalho WHERE empresa_id = %s", (empresa_id,))
        horarios = cursor.fetchone()
        horarios_count = horarios.get('total', 0) if isinstance(horarios, dict) else (horarios[0] if horarios else 0)
        
        cursor.execute("SELECT COUNT(*) as total FROM clientes WHERE empresa_id = %s", (empresa_id,))
        clientes = cursor.fetchone()
        clientes_count = clientes.get('total', 0) if isinstance(clientes, dict) else (clientes[0] if clientes else 0)
        
        print("\n=== RELAÇÕES DEPENDENTES ===")
        print(f"Jornadas de trabalho: {jornadas_count}")
        print(f"Horários de trabalho: {horarios_count}")
        print(f"Clientes: {clientes_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao testar exclusão de empresa: {str(e)}")
        return False

if __name__ == "__main__":
    print("\n" + "="*50)
    print(" TESTE DE CORREÇÃO DE EXCLUSÃO DE EMPRESAS ")
    print("="*50)
    
    # Testar formato de retorno
    if not testar_formato_retorno():
        print("\n❌ Teste de formato de retorno falhou")
        sys.exit(1)
    
    # Testar com uma empresa específica
    empresa_id = 6  # Alterar para um ID válido
    if not testar_exclusao_empresa(empresa_id):
        print(f"\n❌ Teste de exclusão da empresa {empresa_id} falhou")
    
    print("\n" + "="*50)
    print(" TESTE CONCLUÍDO ")
    print("="*50) 