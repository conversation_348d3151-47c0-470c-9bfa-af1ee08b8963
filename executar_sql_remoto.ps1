# Script PowerShell para executar SQL no servidor remoto via SSH
# RLPONTO-WEB - Sistema de Controle de Ponto

Write-Host "RLPONTO-WEB - Execucao SQL Remota" -ForegroundColor Green
Write-Host "=================================="

# Configurações do servidor
$servidor = "************"
$usuario = "cavalcrod"
$senha = "200381"
$banco = "controle_ponto"
$arquivoSQL = "sql\empresa_principal_clientes.sql"

# Verificar se arquivo SQL existe
if (-not (Test-Path $arquivoSQL)) {
    Write-Host "Arquivo SQL nao encontrado: $arquivoSQL" -ForegroundColor Red
    exit 1
}

Write-Host "Arquivo SQL encontrado: $arquivoSQL" -ForegroundColor Green

# Verificar conectividade
Write-Host "Testando conectividade com servidor..." -ForegroundColor Yellow

try {
    $conexaoSSH = Test-NetConnection -ComputerName $servidor -Port 22 -WarningAction SilentlyContinue
    if ($conexaoSSH.TcpTestSucceeded) {
        Write-Host "SSH: Conectividade OK" -ForegroundColor Green
    } else {
        Write-Host "SSH: Falha na conectividade" -ForegroundColor Red
        exit 1
    }
    
    $conexaoMySQL = Test-NetConnection -ComputerName $servidor -Port 3306 -WarningAction SilentlyContinue
    if ($conexaoMySQL.TcpTestSucceeded) {
        Write-Host "MySQL: Conectividade OK" -ForegroundColor Green
    } else {
        Write-Host "MySQL: Falha na conectividade" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Erro ao testar conectividade: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Ler conteúdo do arquivo SQL
Write-Host "Lendo arquivo SQL..." -ForegroundColor Yellow
$sqlContent = Get-Content $arquivoSQL -Raw -Encoding UTF8

# Criar arquivo temporário local
$tempFile = "temp_sql_empresa_principal.sql"
$sqlContent | Out-File -FilePath $tempFile -Encoding UTF8

Write-Host "Arquivo temporario criado: $tempFile" -ForegroundColor Green

# Executar SQL via SSH
Write-Host "Executando SQL no servidor remoto..." -ForegroundColor Yellow

try {
    # Método 1: Usar plink (PuTTY) se disponível
    $plinkPath = Get-Command plink -ErrorAction SilentlyContinue
    if ($plinkPath) {
        Write-Host "Usando plink para conexao SSH..." -ForegroundColor Cyan
        
        # Comando para executar no servidor
        $comando = "mysql -u $usuario -p$senha $banco < /tmp/empresa_principal.sql"
        
        # Copiar arquivo para servidor e executar
        Get-Content $tempFile | & plink -ssh -batch -pw $senha $usuario@$servidor "cat > /tmp/empresa_principal.sql"
        & plink -ssh -batch -pw $senha $usuario@$servidor $comando
        
        Write-Host "SQL executado via plink" -ForegroundColor Green
    } else {
        Write-Host "plink nao encontrado, tentando ssh nativo..." -ForegroundColor Yellow
        
        # Método 2: Usar ssh nativo do Windows (se disponível)
        $sshPath = Get-Command ssh -ErrorAction SilentlyContinue
        if ($sshPath) {
            Write-Host "Usando ssh nativo..." -ForegroundColor Cyan
            
            # Copiar arquivo via scp
            & scp -o StrictHostKeyChecking=no $tempFile ${usuario}@${servidor}:/tmp/empresa_principal.sql
            
            # Executar SQL
            $comando = "mysql -u $usuario -p$senha $banco -e 'source /tmp/empresa_principal.sql'"
            & ssh -o StrictHostKeyChecking=no $usuario@$servidor $comando
            
            Write-Host "SQL executado via ssh nativo" -ForegroundColor Green
        } else {
            Write-Host "SSH nao disponivel, tentando metodo alternativo..." -ForegroundColor Yellow
            
            # Método 3: Executar MySQL diretamente (se cliente MySQL estiver disponível)
            $mysqlPath = Get-Command mysql -ErrorAction SilentlyContinue
            if ($mysqlPath) {
                Write-Host "Executando MySQL diretamente..." -ForegroundColor Cyan
                
                # Executar MySQL com redirecionamento
                & mysql -h $servidor -u $usuario -p$senha $banco -e "source $tempFile"
                
                Write-Host "SQL executado via MySQL direto" -ForegroundColor Green
            } else {
                Write-Host "Nenhum metodo de execucao disponivel" -ForegroundColor Red
                Write-Host "Instale: PuTTY (plink), OpenSSH ou MySQL Client" -ForegroundColor Yellow
                exit 1
            }
        }
    }
    
    # Verificar resultado
    Write-Host "Verificando estrutura criada..." -ForegroundColor Yellow
    
    $verificacaoSQL = @"
SELECT 'Verificacao da estrutura...' as status;
SELECT COUNT(*) as colunas_empresas FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$banco' AND TABLE_NAME = 'empresas' 
AND COLUMN_NAME IN ('empresa_principal', 'empresa_matriz_id', 'tipo_empresa');
SELECT COUNT(*) as tabelas_criadas FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = '$banco' 
AND TABLE_NAME IN ('empresa_clientes', 'funcionario_alocacoes', 'historico_alocacoes');
"@
    
    # Executar verificação
    if ($mysqlPath) {
        $verificacaoSQL | Out-File -FilePath "temp_verificacao.sql" -Encoding UTF8
        & mysql -h $servidor -u $usuario -p$senha $banco -e "source temp_verificacao.sql"
        Remove-Item "temp_verificacao.sql" -Force -ErrorAction SilentlyContinue
    }
    
    Write-Host "Estrutura de empresa principal processada!" -ForegroundColor Green
    
} catch {
    Write-Host "Erro na execucao: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Limpar arquivo temporário
    if (Test-Path $tempFile) {
        Remove-Item $tempFile -Force
        Write-Host "Arquivo temporario removido" -ForegroundColor Gray
    }
}

Write-Host "Processo concluido com sucesso!" -ForegroundColor Green
