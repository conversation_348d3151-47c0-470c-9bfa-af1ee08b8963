#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXECUTOR DA BATERIA COMPLETA DE TESTES
=====================================

Este script executa:
1. Criação de funcionários de teste
2. Análise completa do sistema
3. Geração de relatório detalhado

Autor: Sistema RLPONTO-WEB
Data: 17/07/2025
"""

import subprocess
import sys
import os
from datetime import datetime

def executar_comando(comando, descricao):
    """Executa um comando e retorna o resultado"""
    print(f"🔄 {descricao}...")
    try:
        result = subprocess.run(comando, shell=True, capture_output=True, text=True, cwd='/var/www/controle-ponto')
        
        if result.returncode == 0:
            print(f"✅ {descricao} - SUCESSO")
            if result.stdout:
                print("📄 Output:")
                print(result.stdout)
            return True
        else:
            print(f"❌ {descricao} - FALHOU")
            if result.stderr:
                print("🚨 Erro:")
                print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erro ao executar {descricao}: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 INICIANDO BATERIA COMPLETA DE TESTES DO RLPONTO-WEB")
    print("=" * 70)
    print(f"📅 Data/Hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # Etapa 1: Criar funcionários de teste
    print("\n📋 ETAPA 1: CRIAÇÃO DE FUNCIONÁRIOS DE TESTE")
    print("-" * 50)
    
    sucesso_criacao = executar_comando(
        "python3 criar_funcionarios_teste_completo.py",
        "Criando 30 funcionários de teste com cenários diversos"
    )
    
    if not sucesso_criacao:
        print("❌ Falha na criação dos funcionários de teste. Abortando...")
        return False
    
    # Etapa 2: Executar análise do sistema
    print("\n🔍 ETAPA 2: ANÁLISE COMPLETA DO SISTEMA")
    print("-" * 50)
    
    sucesso_analise = executar_comando(
        "python3 analisador_testes_sistema.py",
        "Executando bateria completa de testes e análises"
    )
    
    if not sucesso_analise:
        print("⚠️ Problemas na análise do sistema, mas continuando...")
    
    # Etapa 3: Verificar arquivos gerados
    print("\n📄 ETAPA 3: VERIFICAÇÃO DOS RELATÓRIOS")
    print("-" * 50)
    
    # Listar arquivos de relatório gerados
    try:
        arquivos_relatorio = []
        for arquivo in os.listdir('/var/www/controle-ponto'):
            if arquivo.startswith('relatorio_testes_') and (arquivo.endswith('.html') or arquivo.endswith('.json')):
                arquivos_relatorio.append(arquivo)
        
        if arquivos_relatorio:
            print("✅ Relatórios gerados:")
            for arquivo in sorted(arquivos_relatorio):
                tamanho = os.path.getsize(f'/var/www/controle-ponto/{arquivo}')
                print(f"   📄 {arquivo} ({tamanho} bytes)")
        else:
            print("⚠️ Nenhum relatório encontrado")
            
    except Exception as e:
        print(f"❌ Erro ao verificar relatórios: {e}")
    
    # Resumo final
    print("\n" + "=" * 70)
    print("🏁 BATERIA DE TESTES CONCLUÍDA")
    print("=" * 70)
    
    if sucesso_criacao and sucesso_analise:
        print("✅ STATUS: TODOS OS TESTES EXECUTADOS COM SUCESSO")
        print("📊 Verifique os relatórios gerados para análise detalhada")
        print("🌐 Acesse o sistema em: http://10.19.208.31/ponto-admin/")
        return True
    elif sucesso_criacao:
        print("🟡 STATUS: FUNCIONÁRIOS CRIADOS, MAS ANÁLISE COM PROBLEMAS")
        print("📊 Dados de teste disponíveis para análise manual")
        return True
    else:
        print("🔴 STATUS: FALHA NA EXECUÇÃO DOS TESTES")
        print("🔧 Verifique as configurações do sistema")
        return False

if __name__ == "__main__":
    main()
