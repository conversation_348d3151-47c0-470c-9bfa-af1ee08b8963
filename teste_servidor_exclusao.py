#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para testar a correção da exclusão de empresas no servidor de produção
Data: 03/07/2025
"""

import sys
import os
import requests
import json
import time
from datetime import datetime

# Configurações
SERVER_URL = "http://************"
LOGIN_URL = f"{SERVER_URL}/login"
EMPRESAS_URL = f"{SERVER_URL}/configuracoes/empresas"
ADMIN_USER = "admin"
ADMIN_PASS = "@Ric6109"

def login_admin():
    """
    Faz login como administrador e retorna a sessão
    """
    session = requests.Session()
    
    # Primeiro acesso para obter CSRF token
    response = session.get(LOGIN_URL)
    
    # Extrair CSRF token (implementação simplificada)
    csrf_token = None
    if 'csrf_token' in response.text:
        start = response.text.find('name="csrf_token" value="') + len('name="csrf_token" value="')
        end = response.text.find('"', start)
        if start > 0 and end > start:
            csrf_token = response.text[start:end]
    
    # Fazer login
    login_data = {
        "username": ADMIN_USER,
        "password": ADMIN_PASS
    }
    
    if csrf_token:
        login_data["csrf_token"] = csrf_token
    
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=True)
    
    if response.status_code == 200 and "Sair" in response.text:
        print("✅ Login realizado com sucesso")
        return session
    else:
        print("❌ Falha ao fazer login")
        return None

def criar_empresa_teste(session):
    """
    Cria uma empresa de teste sem funcionários
    """
    # Gerar nome único baseado em timestamp
    timestamp = int(time.time())
    empresa_nome = f"EMPRESA TESTE {timestamp}"
    cnpj = f"12.345.678/0001-{timestamp % 100:02d}"
    
    # Acessar página de nova empresa para obter CSRF token
    response = session.get(f"{EMPRESAS_URL}/nova")
    
    # Extrair CSRF token (implementação simplificada)
    csrf_token = None
    if 'csrf_token' in response.text:
        start = response.text.find('name="csrf_token" value="') + len('name="csrf_token" value="')
        end = response.text.find('"', start)
        if start > 0 and end > start:
            csrf_token = response.text[start:end]
    
    # Dados da empresa
    empresa_data = {
        "razao_social": empresa_nome,
        "nome_fantasia": f"TESTE {timestamp}",
        "cnpj": cnpj,
        "telefone": "11987654321",
        "email": f"teste{timestamp}@example.com",
        "ativa": "on"
    }
    
    if csrf_token:
        empresa_data["csrf_token"] = csrf_token
    
    # Criar empresa
    response = session.post(f"{EMPRESAS_URL}/salvar", data=empresa_data)
    
    if response.status_code == 200 and "sucesso" in response.text.lower():
        print(f"✅ Empresa de teste criada: {empresa_nome}")
        
        # Buscar ID da empresa criada
        response = session.get(EMPRESAS_URL)
        
        if empresa_nome in response.text:
            # Extrair ID da empresa (implementação simplificada)
            search_str = f'data-empresa-id="'
            start_pos = response.text.find(search_str, response.text.find(empresa_nome))
            
            if start_pos > -1:
                start_pos += len(search_str)
                end_pos = response.text.find('"', start_pos)
                empresa_id = response.text[start_pos:end_pos]
                print(f"✅ ID da empresa: {empresa_id}")
                return empresa_id
    
    print("❌ Falha ao criar empresa de teste")
    return None

def testar_exclusao_empresa(session, empresa_id):
    """
    Testa a exclusão da empresa
    """
    if not empresa_id:
        print("❌ ID da empresa não fornecido")
        return False
    
    # Configurar cabeçalhos para simular requisição AJAX
    headers = {
        "X-Requested-With": "XMLHttpRequest",
        "Content-Type": "application/json"
    }
    
    # Excluir empresa
    url = f"{EMPRESAS_URL}/{empresa_id}/excluir"
    response = session.post(url, headers=headers, json={"is_ajax": True})
    
    print(f"\n=== RESPOSTA DO SERVIDOR ===")
    print(f"Status: {response.status_code}")
    
    try:
        json_response = response.json()
        print(f"JSON: {json.dumps(json_response, indent=2)}")
        
        if response.status_code == 200 and json_response.get("success"):
            print("✅ Empresa excluída com sucesso")
            return True
        else:
            print(f"❌ Falha ao excluir empresa: {json_response.get('error', 'Erro desconhecido')}")
            return False
    except Exception as e:
        print(f"❌ Erro ao processar resposta: {str(e)}")
        print(f"Conteúdo: {response.text[:200]}...")
        return False

def verificar_exclusao(session, empresa_id):
    """
    Verifica se a empresa foi realmente excluída (soft delete)
    """
    response = session.get(EMPRESAS_URL)
    
    if f'data-empresa-id="{empresa_id}"' not in response.text:
        print("✅ Verificação: Empresa não aparece mais na listagem (soft delete funcionou)")
        return True
    else:
        print("❌ Verificação: Empresa ainda aparece na listagem (soft delete falhou)")
        return False

if __name__ == "__main__":
    print("\n" + "="*50)
    print(" TESTE DE EXCLUSÃO DE EMPRESAS NO SERVIDOR ")
    print("="*50)
    
    # Fazer login
    session = login_admin()
    if not session:
        sys.exit(1)
    
    # Criar empresa de teste
    empresa_id = criar_empresa_teste(session)
    if not empresa_id:
        sys.exit(1)
    
    # Testar exclusão
    print("\n" + "-"*50)
    print(" TESTANDO EXCLUSÃO ")
    print("-"*50)
    
    if testar_exclusao_empresa(session, empresa_id):
        verificar_exclusao(session, empresa_id)
    
    print("\n" + "="*50)
    print(" TESTE CONCLUÍDO ")
    print("="*50) 