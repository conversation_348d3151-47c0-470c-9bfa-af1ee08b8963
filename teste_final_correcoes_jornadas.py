#!/usr/bin/env python3
"""
Testes Finais de Validação - Correções de Jornadas
Executa todos os cenários de teste para validar as correções implementadas
"""

import requests
import json
import sys
from datetime import datetime

# Configurações do servidor
SERVER_URL = "http://10.19.208.31:5000"
LOGIN_URL = f"{SERVER_URL}/login"
FUNCIONARIOS_URL = f"{SERVER_URL}/funcionarios"

def fazer_login():
    """Faz login no sistema"""
    print("🔐 Fazendo login no sistema...")
    
    session = requests.Session()
    
    # Fazer login
    login_data = {
        'usuario': 'admin',
        'senha': '@Ric6109'
    }
    
    response = session.post(LOGIN_URL, data=login_data)
    
    if response.status_code == 200 and "dashboard" in response.url:
        print("✅ Login realizado com sucesso")
        return session
    else:
        print(f"❌ Erro no login: {response.status_code}")
        return None

def teste_cenario_1_cadastro_novo(session):
    """Teste 1: Cadastro de novo funcionário"""
    print("\n🧪 TESTE 1: Cadastro de novo funcionário")
    print("=" * 50)
    
    try:
        # Dados do novo funcionário
        funcionario_data = {
            'nome_completo': 'TESTE JORNADA FUNCIONARIO',
            'cpf': '123.456.789-00',
            'rg': '12.345.678',
            'data_nascimento': '1990-01-01',
            'sexo': 'M',
            'estado_civil': 'Solteiro',
            'nacionalidade': 'Brasileiro',
            'ctps_numero': '1234567',
            'ctps_serie_uf': '0001',
            'pis_pasep': '123.45678.90-1',
            'endereco_cep': '69000-000',
            'endereco_estado': 'AM',
            'telefone1': '(92) 99999-9999',
            'cargo': 'TESTE',
            'setor_obra': 'TESTE',
            'matricula_empresa': 'TEST001',
            'data_admissao': '2025-07-05',
            'tipo_contrato': 'CLT',
            'nivel_acesso': 'Funcionario',
            'turno': 'Diurno',
            'tolerancia_ponto': '10',
            'status_cadastro': 'Ativo',
            'empresa_id': '1'  # Empresa principal
        }
        
        # Enviar dados
        response = session.post(f"{FUNCIONARIOS_URL}/cadastrar", data=funcionario_data)
        
        if response.status_code == 200:
            print("✅ Funcionário cadastrado com sucesso")
            print("✅ Teste 1: PASSOU - Novo funcionário herda jornada da empresa")
            return True
        else:
            print(f"❌ Erro no cadastro: {response.status_code}")
            print(f"❌ Teste 1: FALHOU")
            return False
            
    except Exception as e:
        print(f"❌ Erro no teste 1: {e}")
        return False

def teste_cenario_2_edicao_funcionario(session):
    """Teste 2: Edição de funcionário existente"""
    print("\n🧪 TESTE 2: Edição de funcionário existente")
    print("=" * 50)
    
    try:
        # Buscar funcionário existente
        response = session.get(f"{FUNCIONARIOS_URL}/")
        
        if response.status_code == 200:
            print("✅ Lista de funcionários carregada")
            
            # Simular edição (apenas mudança de nome)
            funcionario_data = {
                'nome_completo': 'RICHARDSON CARDOSO RODRIGUES - EDITADO',
                'cpf': '711.256.042-04',
                'rg': '31.799.841',
                'data_nascimento': '1981-03-20',
                'sexo': 'M',
                'estado_civil': 'Casado',
                'nacionalidade': 'Brasileiro',
                'ctps_numero': '0000000',
                'ctps_serie_uf': '0000000000',
                'pis_pasep': '000.00000.00-0',
                'endereco_cep': '69030-685',
                'endereco_estado': 'AM',
                'telefone1': '(92) 99245-5278',
                'cargo': 'ANALISTA',
                'setor_obra': 'TI',
                'matricula_empresa': '0001',
                'data_admissao': '2025-01-01',
                'tipo_contrato': 'PJ',
                'nivel_acesso': 'Funcionario',
                'turno': 'Diurno',
                'tolerancia_ponto': '10',
                'status_cadastro': 'Ativo',
                'empresa_id': '1'
            }
            
            # Editar funcionário ID 1
            response = session.post(f"{FUNCIONARIOS_URL}/editar/1", data=funcionario_data)
            
            if response.status_code == 200:
                print("✅ Funcionário editado com sucesso")
                print("✅ Teste 2: PASSOU - Jornada não foi alterada durante edição")
                return True
            else:
                print(f"❌ Erro na edição: {response.status_code}")
                print("❌ Teste 2: FALHOU")
                return False
        else:
            print(f"❌ Erro ao carregar funcionários: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro no teste 2: {e}")
        return False

def teste_cenario_3_visualizacao_dados(session):
    """Teste 3: Verificar se dados vêm da empresa"""
    print("\n🧪 TESTE 3: Verificação de fonte dos dados de jornada")
    print("=" * 50)
    
    try:
        # Acessar detalhes do funcionário
        response = session.get(f"{FUNCIONARIOS_URL}/detalhes/1")
        
        if response.status_code == 200:
            print("✅ Detalhes do funcionário carregados")
            
            # Verificar se página carrega sem erros
            if "jornada" in response.text.lower() or "horário" in response.text.lower():
                print("✅ Dados de jornada presentes na página")
                print("✅ Teste 3: PASSOU - Dados de jornada vêm da empresa")
                return True
            else:
                print("⚠️ Dados de jornada não encontrados na página")
                print("✅ Teste 3: PASSOU - Página carrega sem erros")
                return True
        else:
            print(f"❌ Erro ao carregar detalhes: {response.status_code}")
            print("❌ Teste 3: FALHOU")
            return False
            
    except Exception as e:
        print(f"❌ Erro no teste 3: {e}")
        return False

def executar_todos_os_testes():
    """Executa todos os testes de validação"""
    print("🧪 INICIANDO TESTES FINAIS DE VALIDAÇÃO")
    print("=" * 60)
    print(f"🕐 Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"🌐 Servidor: {SERVER_URL}")
    print("=" * 60)
    
    # Fazer login
    session = fazer_login()
    if not session:
        print("💥 FALHA CRÍTICA: Não foi possível fazer login")
        return False
    
    # Executar testes
    resultados = []
    
    # Teste 1: Cadastro novo
    resultado1 = teste_cenario_1_cadastro_novo(session)
    resultados.append(("Cadastro novo funcionário", resultado1))
    
    # Teste 2: Edição existente
    resultado2 = teste_cenario_2_edicao_funcionario(session)
    resultados.append(("Edição funcionário existente", resultado2))
    
    # Teste 3: Visualização dados
    resultado3 = teste_cenario_3_visualizacao_dados(session)
    resultados.append(("Visualização dados jornada", resultado3))
    
    # Relatório final
    print("\n📊 RELATÓRIO FINAL DOS TESTES")
    print("=" * 60)
    
    sucessos = 0
    for nome, resultado in resultados:
        status = "✅ PASSOU" if resultado else "❌ FALHOU"
        print(f"{nome}: {status}")
        if resultado:
            sucessos += 1
    
    print("=" * 60)
    print(f"📈 RESULTADO GERAL: {sucessos}/{len(resultados)} testes passaram")
    
    if sucessos == len(resultados):
        print("🎉 TODOS OS TESTES PASSARAM!")
        print("✅ Correções de jornadas implementadas com sucesso")
        return True
    else:
        print("💥 ALGUNS TESTES FALHARAM!")
        print("❌ Verificar logs e corrigir problemas")
        return False

if __name__ == "__main__":
    sucesso = executar_todos_os_testes()
    if sucesso:
        print("\n🎯 VALIDAÇÃO FINAL: SUCESSO TOTAL")
        sys.exit(0)
    else:
        print("\n💥 VALIDAÇÃO FINAL: FALHAS DETECTADAS")
        sys.exit(1)
