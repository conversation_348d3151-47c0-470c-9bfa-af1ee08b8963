#===============================================
# ZKAgent Professional - Configuração de Produção
# Arquivo: zkagent-config.properties
# Data: Janeiro 2025
# Versão: 1.0 - Correções de Inatividade
#===============================================

# ========================================
# TIMEOUTS ROBUSTOS PARA AMBIENTE CORPORATIVO
# ========================================

# Timeouts de rede (em millisegundos)
network.connect.timeout=15000
network.read.timeout=20000
network.max.retries=3
network.retry.delay=2000

# Timeouts de captura biométrica
capture.timeout=30000
capture.max.retries=3
capture.retry.delay=1000

# ========================================
# AUTO-RECOVERY DE HARDWARE
# ========================================

# Habilitação e configuração de auto-recovery
hardware.auto.reconnect=true
hardware.retry.interval=60000
hardware.max.retries=5
hardware.recovery.on.startup=true

# Configurações de detecção de falhas
hardware.failure.detection=true
hardware.health.check.enabled=true
hardware.critical.error.codes=-8,-7

# ========================================
# HEALTH CHECK INTELIGENTE
# ========================================

# Intervalos de verificação (em millisegundos)
health.check.interval=300000
health.check.deep=true
health.check.auto.recovery=true

# Limites para consideração de sistema saudável
health.memory.threshold=80
health.success.rate.minimum=80
health.response.time.maximum=5000

# ========================================
# LOGGING DETALHADO
# ========================================

# Configurações de log
logging.level=INFO
logging.file.max.size=10MB
logging.files.keep=5
logging.cleanup.old=true
logging.performance.enabled=true

# Log de métricas e debugging
logging.capture.metrics=true
logging.recovery.details=true
logging.health.reports=true

# ========================================
# MONITORAMENTO AVANÇADO
# ========================================

# Intervalos de monitoramento (em millisegundos)
monitoring.device.interval=30000
monitoring.api.interval=120000
monitoring.memory.interval=600000
monitoring.performance.interval=600000

# Alertas e notificações
monitoring.notifications.enabled=true
monitoring.memory.alert.threshold=90
monitoring.failure.rate.alert=20

# ========================================
# CONFIGURAÇÕES DE SERVIDOR
# ========================================

# Servidor web interno
server.port=5001
server.threads.min=2
server.threads.max=10
server.timeout.connection=30000
server.timeout.idle=60000

# ========================================
# CONFIGURAÇÕES DE PRODUÇÃO
# ========================================

# Configurações para ambiente corporativo
production.mode=true
production.timeouts.extended=true
production.recovery.aggressive=true
production.monitoring.enhanced=true

# Configurações de estabilidade
stability.memory.management=true
stability.resource.cleanup=true
stability.shutdown.graceful=true

# ========================================
# CONFIGURAÇÕES DE SEGURANÇA
# ========================================

# Bloqueio de instância múltipla
security.single.instance=true
security.lock.timeout=30000

# Validação de integridade
security.sdk.validation=true
security.hardware.validation=true

# ========================================
# CONFIGURAÇÕES EXPERIMENTAIS
# ========================================

# Recursos em desenvolvimento (use com cuidado)
experimental.advanced.recovery=false
experimental.predictive.health=false
experimental.machine.learning.patterns=false

# ========================================
# CONFIGURAÇÕES DE COMPATIBILIDADE
# ========================================

# Modo de compatibilidade com sistemas antigos
compatibility.legacy.support=true
compatibility.usb.polling.enabled=true
compatibility.fallback.mode=true

# Configurações específicas do Windows
windows.service.mode=false
windows.startup.delay=5000
windows.hardware.refresh.rate=30000

# ========================================
# MÉTRICAS E TELEMETRIA
# ========================================

# Coleta de métricas para análise
metrics.collection.enabled=true
metrics.capture.success.rate=true
metrics.hardware.uptime=true
metrics.memory.usage=true
metrics.recovery.statistics=true

# Relatórios automáticos
reports.daily.summary=false
reports.performance.analysis=true
reports.failure.analysis=true

# ========================================
# BACKUP E RECUPERAÇÃO
# ========================================

# Configurações de backup automático
backup.config.enabled=true
backup.logs.retention.days=30
backup.metrics.retention.days=7

# Recuperação de desastres
disaster.recovery.enabled=true
disaster.recovery.auto.restart=true
disaster.recovery.max.attempts=3

#===============================================
# NOTAS IMPORTANTES:
#
# 1. Este arquivo resolve os problemas de inatividade
#    identificados na análise técnica.
#
# 2. Timeouts foram aumentados para ambientes corporativos
#    (15-20s vs 3-5s anteriores).
#
# 3. Auto-recovery está habilitado por padrão com
#    configurações conservadoras.
#
# 4. Health checks são executados a cada 5 minutos
#    com verificação completa do sistema.
#
# 5. Logging detalhado para troubleshooting.
#
# 6. Configurações de produção otimizadas para
#    operação 24/7 sem intervenção manual.
#=============================================== 