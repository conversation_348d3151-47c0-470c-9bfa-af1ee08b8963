-- =====================================================
-- TRIGGERS PARA HERANÇA DINÂMICA DE JORNADAS
-- Sistema de Controle de Ponto - RLPONTO-WEB
-- Data: 14/07/2025
-- =====================================================

-- Remover triggers existentes se houver
DROP TRIGGER IF EXISTS tr_atualizar_jornadas_funcionarios;
DROP TRIGGER IF EXISTS tr_historico_mudanca_jornada_funcionario;
DROP TRIGGER IF EXISTS tr_historico_alocacao_criada;
DROP TRIGGER IF EXISTS tr_historico_alocacao_finalizada;

-- =====================================================
-- 1. TRIGGER: Atualizar funcionários quando jornada da empresa muda
-- =====================================================

DELIMITER $$
CREATE TRIGGER tr_atualizar_jornadas_funcionarios
AFTER UPDATE ON jornadas_trabalho
FOR EACH ROW
BEGIN
    -- Se é jornada padrão e foi modificada
    IF NEW.padrao = 1 AND NEW.ativa = 1 AND (
        OLD.seg_qui_entrada != NEW.seg_qui_entrada OR
        OLD.seg_qui_saida != NEW.seg_qui_saida OR
        OLD.sexta_entrada != NEW.sexta_entrada OR
        OLD.sexta_saida != NEW.sexta_saida OR
        OLD.intervalo_inicio != NEW.intervalo_inicio OR
        OLD.intervalo_fim != NEW.intervalo_fim OR
        OLD.tolerancia_entrada_minutos != NEW.tolerancia_entrada_minutos
    ) THEN
        
        -- Atualizar funcionários da empresa que usam horário da empresa
        UPDATE funcionarios 
        SET jornada_trabalho_id = NEW.id,
            data_atualizacao_jornada = CURRENT_TIMESTAMP,
            jornada_alterada_por = NULL
        WHERE empresa_id = NEW.empresa_id 
        AND usa_horario_empresa = TRUE;
        
        -- Registrar no histórico para cada funcionário afetado
        INSERT INTO historico_funcionario (
            funcionario_id, tipo_evento, data_evento, data_referencia,
            detalhes, status_aprovacao
        )
        SELECT 
            f.id, 'EMPRESA_MUDOU_JORNADA', NOW(), CURDATE(),
            CONCAT('Empresa alterou jornada padrão. Nova jornada: ', NEW.nome_jornada, 
                   ' (', NEW.seg_qui_entrada, ' às ', NEW.seg_qui_saida, ')'),
            'NAO_APLICAVEL'
        FROM funcionarios f
        WHERE f.empresa_id = NEW.empresa_id 
        AND f.usa_horario_empresa = TRUE;
        
        -- Registrar no log de mudanças para cada funcionário afetado
        INSERT INTO log_mudancas_jornada (
            funcionario_id, jornada_anterior_id, jornada_nova_id, 
            tipo_mudanca, motivo, dados_jornada_nova
        )
        SELECT 
            f.id, OLD.id, NEW.id, 'EMPRESA_ALTEROU',
            CONCAT('Empresa alterou jornada padrão: ', OLD.nome_jornada, ' → ', NEW.nome_jornada),
            JSON_OBJECT(
                'nome_jornada', NEW.nome_jornada,
                'seg_qui_entrada', NEW.seg_qui_entrada,
                'seg_qui_saida', NEW.seg_qui_saida,
                'sexta_entrada', NEW.sexta_entrada,
                'sexta_saida', NEW.sexta_saida,
                'intervalo_inicio', NEW.intervalo_inicio,
                'intervalo_fim', NEW.intervalo_fim,
                'tolerancia_entrada_minutos', NEW.tolerancia_entrada_minutos
            )
        FROM funcionarios f
        WHERE f.empresa_id = NEW.empresa_id 
        AND f.usa_horario_empresa = TRUE;
        
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- 2. TRIGGER: Registrar mudanças manuais de jornada
-- =====================================================

DELIMITER $$
CREATE TRIGGER tr_historico_mudanca_jornada_funcionario
AFTER UPDATE ON funcionarios
FOR EACH ROW
BEGIN
    -- Se a jornada foi alterada
    IF OLD.jornada_trabalho_id != NEW.jornada_trabalho_id THEN
        
        -- Inserir no log de mudanças
        INSERT INTO log_mudancas_jornada (
            funcionario_id, jornada_anterior_id, jornada_nova_id, 
            tipo_mudanca, motivo, usuario_responsavel
        ) VALUES (
            NEW.id, OLD.jornada_trabalho_id, NEW.jornada_trabalho_id, 
            'ALTERACAO_MANUAL',
            'Jornada alterada manualmente',
            NEW.jornada_alterada_por
        );
        
        -- Inserir no histórico do funcionário
        INSERT INTO historico_funcionario (
            funcionario_id, tipo_evento, data_evento, data_referencia,
            detalhes, status_aprovacao
        ) VALUES (
            NEW.id, 'JORNADA_ALTERADA', NOW(), CURDATE(),
            CONCAT('Jornada alterada manualmente de ID ', COALESCE(OLD.jornada_trabalho_id, 'NULL'), 
                   ' para ID ', COALESCE(NEW.jornada_trabalho_id, 'NULL')),
            'NAO_APLICAVEL'
        );
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- 3. TRIGGER: Registrar criação de alocação
-- =====================================================

DELIMITER $$
CREATE TRIGGER tr_historico_alocacao_criada
AFTER INSERT ON funcionario_alocacoes
FOR EACH ROW
BEGIN
    -- Registrar no histórico do funcionário
    INSERT INTO historico_funcionario (
        funcionario_id, tipo_evento, data_evento, data_referencia,
        detalhes, status_aprovacao
    ) VALUES (
        NEW.funcionario_id, 'ALOCACAO_CRIADA', NOW(), CURDATE(),
        CONCAT('Funcionário alocado para cliente ID ', NEW.empresa_cliente_id, 
               ' com jornada ID ', NEW.jornada_trabalho_id),
        'NAO_APLICAVEL'
    );
    
    -- Inserir no log de mudanças de jornada
    INSERT INTO log_mudancas_jornada (
        funcionario_id, jornada_anterior_id, jornada_nova_id, 
        tipo_mudanca, motivo
    ) VALUES (
        NEW.funcionario_id, 
        (SELECT jornada_trabalho_id FROM funcionarios WHERE id = NEW.funcionario_id),
        NEW.jornada_trabalho_id, 
        'ALOCACAO_CLIENTE',
        CONCAT('Alocado para cliente - herdando jornada do cliente')
    );
END$$
DELIMITER ;

-- =====================================================
-- 4. TRIGGER: Registrar finalização de alocação
-- =====================================================

DELIMITER $$
CREATE TRIGGER tr_historico_alocacao_finalizada
AFTER UPDATE ON funcionario_alocacoes
FOR EACH ROW
BEGIN
    -- Se alocação foi desativada
    IF OLD.ativo = TRUE AND NEW.ativo = FALSE THEN
        -- Registrar no histórico do funcionário
        INSERT INTO historico_funcionario (
            funcionario_id, tipo_evento, data_evento, data_referencia,
            detalhes, status_aprovacao
        ) VALUES (
            NEW.funcionario_id, 'ALOCACAO_FINALIZADA', NOW(), CURDATE(),
            CONCAT('Alocação finalizada para cliente ID ', NEW.empresa_cliente_id),
            'NAO_APLICAVEL'
        );
    END IF;
END$$
DELIMITER ;

SELECT 'Triggers de herança dinâmica criados com sucesso!' as status;
