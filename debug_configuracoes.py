#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de Diagnóstico Avançado - Configurações Biométricas
==========================================================

Diagnóstica problemas profundos com templates Flask, cache, 
blueprints e outras possíveis causas de não atualização.

Desenvolvido por: <PERSON> - AiNexus Tecnologia
Data: Junho 2025
"""

import os
import sys
import json
import hashlib
import subprocess
from datetime import datetime
from pathlib import Path

def log_message(message, level="INFO"):
    """Log com timestamp e nível"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] [{level}] {message}")

def get_file_hash(filepath):
    """Calcula hash MD5 de um arquivo"""
    try:
        with open(filepath, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    except:
        return "N/A"

def check_template_content(filepath):
    """Verifica conteúdo específico do template"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            
        has_biometric_icon = 'fas fa-fingerprint' in content
        has_biometric_text = 'Biometria' in content
        has_biometric_link = '/configuracoes/biometria' in content
        
        return {
            'exists': True,
            'has_biometric_icon': has_biometric_icon,
            'has_biometric_text': has_biometric_text,
            'has_biometric_link': has_biometric_link,
            'all_biometric_elements': has_biometric_icon and has_biometric_text and has_biometric_link,
            'size': len(content),
            'lines': len(content.splitlines())
        }
    except Exception as e:
        return {
            'exists': False,
            'error': str(e)
        }

def check_flask_processes():
    """Verifica processos Flask rodando"""
    try:
        result = subprocess.run(['pgrep', '-f', 'python.*app.py'], 
                              capture_output=True, text=True)
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            return {'running': True, 'pids': pids, 'count': len(pids)}
        else:
            return {'running': False, 'pids': [], 'count': 0}
    except:
        return {'running': 'unknown', 'error': 'Comando pgrep não disponível'}

def check_flask_imports():
    """Verifica se os imports estão funcionando"""
    try:
        # Simular imports do app.py
        sys.path.insert(0, '/var/www/controle-ponto')
        
        import importlib.util
        
        # Verificar app_biometric_config
        biometric_spec = importlib.util.spec_from_file_location(
            "app_biometric_config", 
            "/var/www/controle-ponto/app_biometric_config.py"
        )
        
        if biometric_spec:
            return {'biometric_module': 'importable'}
        else:
            return {'biometric_module': 'not_found'}
            
    except Exception as e:
        return {'biometric_module': 'error', 'error': str(e)}

def diagnose_deep():
    """Diagnóstico profundo do problema"""
    log_message("🔍 INICIANDO DIAGNÓSTICO PROFUNDO...", "INFO")
    
    # Definir caminhos
    template_path = Path("/var/www/controle-ponto/templates/configuracoes/index.html")
    biometric_py_path = Path("/var/www/controle-ponto/app_biometric_config.py")
    app_py_path = Path("/var/www/controle-ponto/app.py")
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'diagnostics': {}
    }
    
    # 1. Verificar arquivos
    log_message("📁 Verificando arquivos...")
    
    template_check = check_template_content(template_path)
    results['diagnostics']['template'] = {
        'path': str(template_path),
        'hash': get_file_hash(template_path),
        'content_check': template_check
    }
    
    if template_check.get('all_biometric_elements'):
        log_message("✅ Template CONTÉM todos os elementos biométricos", "SUCCESS")
    else:
        log_message("❌ Template NÃO CONTÉM elementos biométricos completos", "ERROR")
        log_message(f"   - Ícone: {template_check.get('has_biometric_icon')}", "DEBUG")
        log_message(f"   - Texto: {template_check.get('has_biometric_text')}", "DEBUG")
        log_message(f"   - Link: {template_check.get('has_biometric_link')}", "DEBUG")
    
    # 2. Verificar módulo biométrico
    log_message("🔧 Verificando módulo biométrico...")
    results['diagnostics']['biometric_module'] = {
        'path': str(biometric_py_path),
        'exists': biometric_py_path.exists(),
        'hash': get_file_hash(biometric_py_path)
    }
    
    # 3. Verificar app.py
    log_message("⚙️ Verificando app.py...")
    app_content_check = None
    if app_py_path.exists():
        try:
            with open(app_py_path, 'r', encoding='utf-8') as f:
                app_content = f.read()
            
            has_biometric_import = 'app_biometric_config' in app_content
            has_biometric_register = 'biometric_config_bp' in app_content
            
            app_content_check = {
                'has_biometric_import': has_biometric_import,
                'has_biometric_register': has_biometric_register
            }
            
            if has_biometric_import and has_biometric_register:
                log_message("✅ app.py CONTÉM import e registro do blueprint biométrico", "SUCCESS")
            else:
                log_message("❌ app.py NÃO CONTÉM configuração biométrica completa", "ERROR")
                log_message(f"   - Import: {has_biometric_import}", "DEBUG")
                log_message(f"   - Register: {has_biometric_register}", "DEBUG")
                
        except Exception as e:
            app_content_check = {'error': str(e)}
    
    results['diagnostics']['app_py'] = {
        'path': str(app_py_path),
        'exists': app_py_path.exists(),
        'hash': get_file_hash(app_py_path),
        'content_check': app_content_check
    }
    
    # 4. Verificar processos Flask
    log_message("🔄 Verificando processos Flask...")
    flask_processes = check_flask_processes()
    results['diagnostics']['flask_processes'] = flask_processes
    
    if flask_processes.get('count', 0) > 1:
        log_message(f"⚠️ MÚLTIPLOS PROCESSOS FLASK DETECTADOS: {flask_processes['count']}", "WARNING")
        log_message("   Isso pode causar conflitos!", "WARNING")
    elif flask_processes.get('running'):
        log_message(f"✅ Um processo Flask rodando (PID: {flask_processes['pids'][0]})", "SUCCESS")
    else:
        log_message("❌ Nenhum processo Flask detectado", "ERROR")
    
    # 5. Verificar cache potencial
    log_message("💾 Verificando possíveis caches...")
    
    cache_dirs = [
        Path("/var/www/controle-ponto/__pycache__"),
        Path("/var/www/controle-ponto/.flask_cache"),
        Path("/tmp/flask_cache")
    ]
    
    cache_info = {}
    for cache_dir in cache_dirs:
        if cache_dir.exists():
            cache_info[str(cache_dir)] = {
                'exists': True,
                'files': len(list(cache_dir.rglob('*')))
            }
            log_message(f"⚠️ Cache encontrado: {cache_dir}", "WARNING")
        else:
            cache_info[str(cache_dir)] = {'exists': False}
    
    results['diagnostics']['cache'] = cache_info
    
    # 6. Verificar timestamps de arquivos
    log_message("⏰ Verificando timestamps...")
    
    timestamp_info = {}
    for file_path in [template_path, biometric_py_path, app_py_path]:
        if file_path.exists():
            stat = file_path.stat()
            timestamp_info[str(file_path)] = {
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'size': stat.st_size
            }
    
    results['diagnostics']['timestamps'] = timestamp_info
    
    # 7. Teste de renderização de template
    log_message("🎨 Testando renderização de template...")
    
    try:
        # Tentar simular uma renderização básica
        os.chdir('/var/www/controle-ponto')
        sys.path.insert(0, '/var/www/controle-ponto')
        
        from flask import Flask
        test_app = Flask(__name__)
        
        with test_app.app_context():
            from flask import render_template_string
            
            # Ler template real
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # Tentar renderizar um pedaço pequeno
            test_render = 'fas fa-fingerprint' in template_content
            
            results['diagnostics']['template_render_test'] = {
                'can_read_template': True,
                'biometric_content_present': test_render
            }
            
            if test_render:
                log_message("✅ Template pode ser lido e contém conteúdo biométrico", "SUCCESS")
            else:
                log_message("❌ Template legível mas SEM conteúdo biométrico", "ERROR")
                
    except Exception as e:
        results['diagnostics']['template_render_test'] = {
            'error': str(e)
        }
        log_message(f"❌ Erro no teste de renderização: {e}", "ERROR")
    
    # 8. Salvar resultado detalhado
    with open('/var/www/controle-ponto/diagnostic_report.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    log_message("📄 Relatório detalhado salvo em diagnostic_report.json", "INFO")
    
    # 9. Gerar recomendações
    log_message("💡 GERANDO RECOMENDAÇÕES...", "INFO")
    
    recommendations = []
    
    if not template_check.get('all_biometric_elements'):
        recommendations.append("🔧 CRÍTICO: Template não contém elementos biométricos - verificar sincronização")
    
    if flask_processes.get('count', 0) > 1:
        recommendations.append("⚠️ IMPORTANTE: Múltiplos processos Flask - matar todos e reiniciar")
    
    if any(cache_info[k]['exists'] for k in cache_info):
        recommendations.append("🧹 SUGESTÃO: Limpar caches Python encontrados")
    
    if not results['diagnostics']['biometric_module']['exists']:
        recommendations.append("📁 CRÍTICO: Módulo app_biometric_config.py não encontrado")
    
    if app_content_check and not (app_content_check.get('has_biometric_import') and app_content_check.get('has_biometric_register')):
        recommendations.append("⚙️ CRÍTICO: app.py não contém configuração biométrica completa")
    
    if not recommendations:
        recommendations.append("🎉 Todos os arquivos parecem corretos - problema pode ser de cache do navegador")
    
    log_message("📋 RECOMENDAÇÕES:", "INFO")
    for i, rec in enumerate(recommendations, 1):
        log_message(f"   {i}. {rec}", "RECOMMENDATION")
    
    return results

if __name__ == "__main__":
    try:
        results = diagnose_deep()
        print("\n" + "="*60)
        print("🎯 DIAGNÓSTICO CONCLUÍDO!")
        print("📄 Veja o arquivo diagnostic_report.json para detalhes completos")
        print("="*60)
    except Exception as e:
        log_message(f"❌ ERRO CRÍTICO no diagnóstico: {e}", "CRITICAL")
        import traceback
        traceback.print_exc() 