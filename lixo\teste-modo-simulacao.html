<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Modo Simulação Modal Biométrico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="var/www/controle-ponto/static/style-cadastrar.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 40px;
            min-height: 100vh;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .test-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .info-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌐 Teste - Modo Simulação Modal Biométrico</h1>
        
        <div class="info-card">
            <h3>🎯 Funcionalidades Implementadas</h3>
            <div class="row text-start">
                <div class="col-md-6">
                    <h5>✅ Detecção de Simulação:</h5>
                    <ul>
                        <li>🔍 Identifica device: "ZK4500-SIMULADO"</li>
                        <li>🌐 Modo desenvolvimento ativo</li>
                        <li>📊 Preserva dados simulados</li>
                        <li>⚠️ Alerta informativo (não crítico)</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>🎨 Elementos Visuais:</h5>
                    <ul>
                        <li>🌐 Status "MODO SIMULAÇÃO"</li>
                        <li>🎨 Scanner com animação especial</li>
                        <li>📋 Badge "🌐 Simulado"</li>
                        <li>💡 Instruções específicas</li>
                        <li>📱 Alerta visual informativo</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="info-card">
            <h3>🚨 Proteção Contra Outras Simulações</h3>
            <p>O sistema <strong>ainda rejeita</strong> simulações de outros softwares:</p>
            <ul class="text-start">
                <li>❌ Templates com "MODO_COMPATIVEL"</li>
                <li>❌ Templates com "ZKAgent_Professional"</li>
                <li>❌ Templates suspeitos (< 100 bytes)</li>
                <li>❌ Templates com texto simples</li>
            </ul>
            <p><strong>✅ Apenas nosso simulador específico é aceito em modo desenvolvimento!</strong></p>
        </div>

        <div class="info-card">
            <h3>🔧 Como Testar</h3>
            <ol class="text-start">
                <li><strong>1. Simulador Ativo:</strong> Execute <code>simulacao/usar-simulador.bat</code></li>
                <li><strong>2. Abrir Modal:</strong> Clique no botão abaixo</li>
                <li><strong>3. Iniciar Captura:</strong> O sistema detectará automaticamente</li>
                <li><strong>4. Observar:</strong> Mensagem "🌐 MODO SIMULAÇÃO" aparecerá</li>
                <li><strong>5. Validar:</strong> Dados são aceitos com aviso informativo</li>
            </ol>
        </div>

        <button class="test-btn" onclick="abrirModalBiometria()">
            🔐 Testar Modal Biométrico
        </button>

        <div class="info-card">
            <h3>📋 Log de Teste</h3>
            <div id="testLog" style="background: rgba(0,0,0,0.2); padding: 15px; border-radius: 8px; font-family: monospace; text-align: left; min-height: 100px; max-height: 300px; overflow-y: auto;">
                Aguardando teste...<br>
            </div>
        </div>
    </div>

    <!-- Modal de Biometria -->
    <div id="modalBiometria" class="modal-biometria" style="display:none;">
        <div class="modal-backdrop"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h2>🔐 Captura Biométrica - TESTE</h2>
                <button type="button" class="modal-close" onclick="fecharModalBiometria()">&times;</button>
            </div>
            
            <div class="modal-content">
                <!-- Status e Progresso -->
                <div class="biometria-status">
                    <div class="status-bar">
                        <div class="status-step active" id="step-1">
                            <div class="step-number">1</div>
                            <div class="step-label">Conexão</div>
                        </div>
                        <div class="status-step" id="step-2">
                            <div class="step-number">2</div>
                            <div class="step-label">Dedo 1</div>
                        </div>
                        <div class="status-step" id="step-3">
                            <div class="step-number">3</div>
                            <div class="step-label">Dedo 2</div>
                        </div>
                        <div class="status-step" id="step-4">
                            <div class="step-number">4</div>
                            <div class="step-label">Concluído</div>
                        </div>
                    </div>
                </div>
                
                <!-- Área de Visualização Biométrica -->
                <div class="biometria-display">
                    <div class="biometria-scanner">
                        <div class="scanner-frame">
                            <div class="scanner-area" id="scannerArea">
                                <div class="fingerprint-icon">
                                    <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
                                        <path d="M60 20C45.9 20 34.5 31.4 34.5 45.5C34.5 52.1 36.8 58.2 40.7 62.9L60 95L79.3 62.9C83.2 58.2 85.5 52.1 85.5 45.5C85.5 31.4 74.1 20 60 20Z" stroke="#007bff" stroke-width="2" fill="rgba(0,123,255,0.1)"/>
                                        <circle cx="60" cy="45" r="15" stroke="#007bff" stroke-width="2" fill="none"/>
                                        <path d="M45 45C45 36.7 51.7 30 60 30" stroke="#007bff" stroke-width="2" stroke-linecap="round"/>
                                        <path d="M50 45C50 39.5 54.5 35 60 35" stroke="#007bff" stroke-width="2" stroke-linecap="round"/>
                                        <path d="M55 45C55 42.2 57.2 40 60 40" stroke="#007bff" stroke-width="2" stroke-linecap="round"/>
                                    </svg>
                                </div>
                                <div class="scanner-pulse" id="scannerPulse"></div>
                            </div>
                            <div class="scanner-label">Simulador ZK4500 Ativo</div>
                        </div>
                    </div>
                    
                    <!-- Área de Instruções -->
                    <div class="biometria-instructions">
                        <div class="instruction-card" id="instructionCard">
                            <h4>Preparação</h4>
                            <ul>
                                <li>Simulador ativo na porta 5001</li>
                                <li>Dados de teste serão utilizados</li>
                                <li>Modo desenvolvimento detectado</li>
                                <li>Biometria NÃO é real</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Resultado das Capturas -->
                <div class="capturas-resultado" id="capturasResultado" style="display:none;">
                    <div class="resultado-grid">
                        <div class="resultado-card" id="resultadoDedo1">
                            <div class="resultado-header">
                                <h5>Dedo 1</h5>
                                <span class="status-badge pending">Aguardando</span>
                            </div>
                            <div class="qualidade-meter">
                                <div class="qualidade-bar">
                                    <div class="qualidade-fill" data-qualidade="0"></div>
                                </div>
                                <span class="qualidade-valor">0%</span>
                            </div>
                        </div>
                        
                        <div class="resultado-card" id="resultadoDedo2">
                            <div class="resultado-header">
                                <h5>Dedo 2</h5>
                                <span class="status-badge pending">Aguardando</span>
                            </div>
                            <div class="qualidade-meter">
                                <div class="qualidade-bar">
                                    <div class="qualidade-fill" data-qualidade="0"></div>
                                </div>
                                <span class="qualidade-valor">0%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Status Geral -->
                <div class="status-message">
                    <div id="statusBiometria" class="status-text">
                        Clique em "Iniciar Captura" para testar simulação
                    </div>
                </div>
            </div>
            
            <!-- Ações do Modal -->
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="fecharModalBiometria()">
                    Cancelar
                </button>
                <button type="button" class="btn btn-primary" id="btnIniciarCaptura" onclick="iniciarCapturaBiometria()">
                    Iniciar Captura
                </button>
                <button type="button" class="btn btn-success" id="btnSalvarBiometria" onclick="salvarBiometriaModal()" style="display:none;">
                    Salvar Biometria
                </button>
            </div>
        </div>
    </div>

    <!-- Hidden form fields para teste -->
    <input type="hidden" id="digital_dedo1" name="digital_dedo1">
    <input type="hidden" id="digital_dedo2" name="digital_dedo2">

    <!-- Scripts -->
    <script src="var/www/controle-ponto/static/js/biometria-zkagent.js"></script>
    <script>
        function logTest(message) {
            const log = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}<br>`;
            log.scrollTop = log.scrollHeight;
        }

        // Override da função salvarBiometria para log
        const originalModal = new ModalBiometriaVisual();
        const originalSalvar = originalModal.salvarBiometria;
        
        originalModal.salvarBiometria = function() {
            const templates = this.getTemplates();
            logTest(`✅ Biometria salva! Dedo1: ${templates.dedo1 ? 'OK' : 'NULL'}, Dedo2: ${templates.dedo2 ? 'OK' : 'NULL'}`);
            logTest(`📊 Qualidades: D1=${templates.qualidade1}%, D2=${templates.qualidade2}%`);
            
            // Chama função original
            originalSalvar.call(this);
        };

        // Log inicial
        document.addEventListener('DOMContentLoaded', function() {
            logTest('🌐 Sistema de teste carregado');
            logTest('🔧 Aguardando teste do modo simulação...');
        });

        // Override das funções de alerta para log
        const zkAgent = new ZKAgentBiometria();
        const originalCaptura = zkAgent.capturarDigital;
        
        zkAgent.capturarDigital = async function() {
            logTest('🔄 Iniciando captura biométrica...');
            try {
                const result = await originalCaptura.call(this);
                if (result.simulationMode) {
                    logTest('🌐 MODO SIMULAÇÃO DETECTADO!');
                    logTest(`📊 Qualidade: ${result.quality}%`);
                    if (result.user) {
                        logTest(`👤 Usuário identificado: ${result.user.name}`);
                    }
                } else {
                    logTest('✅ Captura real realizada');
                }
                return result;
            } catch (error) {
                logTest(`❌ Erro: ${error.message}`);
                throw error;
            }
        };
    </script>
</body>
</html> 