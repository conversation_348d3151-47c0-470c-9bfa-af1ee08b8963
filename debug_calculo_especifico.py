#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 DEBUG ESPECÍFICO DO CÁLCULO DE HORAS
======================================

Vamos calcular exatamente como o sistema está fazendo
para entender onde está o erro de 1 minuto.
"""

from datetime import datetime, time

def calcular_exato_como_sistema():
    """
    Calcula exatamente como o sistema está fazendo.
    """
    print("🔍 DEBUG ESPECÍFICO DO CÁLCULO")
    print("=" * 50)
    
    # Dados reais do Richardson
    entrada = time(8, 15, 0)      # 08:15:00
    saida_almoco = time(9, 20, 44) # 09:20:44
    
    print(f"📅 Dados reais:")
    print(f"   Entrada: {entrada}")
    print(f"   Saída Almoço: {saida_almoco}")
    
    # Converter para datetime
    hoje = datetime.now().date()
    dt_entrada = datetime.combine(hoje, entrada)
    dt_saida_almoco = datetime.combine(hoje, saida_almoco)
    
    print(f"\n🕐 Conversão para datetime:")
    print(f"   Entrada: {dt_entrada}")
    print(f"   Saída Almoço: {dt_saida_almoco}")
    
    # Calcular diferença
    diferenca = dt_saida_almoco - dt_entrada
    print(f"\n⏱️  Diferença:")
    print(f"   Timedelta: {diferenca}")
    print(f"   Total segundos: {diferenca.total_seconds()}")
    
    # Converter para horas
    horas_decimal = diferenca.total_seconds() / 3600
    print(f"\n🧮 Conversão para horas:")
    print(f"   Horas decimal: {horas_decimal}")
    print(f"   Horas decimal (6 casas): {horas_decimal:.6f}")
    
    # Arredondamento do sistema
    horas_arredondadas = round(horas_decimal, 2)
    print(f"   Horas arredondadas (round 2): {horas_arredondadas}")
    
    # Conversão para horas:minutos
    horas_inteiras = int(horas_arredondadas)
    minutos_decimal = (horas_arredondadas - horas_inteiras) * 60
    minutos_int = int(minutos_decimal)
    
    print(f"\n🕐 Conversão para horas:minutos:")
    print(f"   Horas inteiras: {horas_inteiras}")
    print(f"   Minutos decimal: {minutos_decimal}")
    print(f"   Minutos int: {minutos_int}")
    print(f"   Resultado: {horas_inteiras}h {minutos_int:02d}min")
    
    # Cálculo manual correto
    print(f"\n✅ Cálculo manual correto:")
    total_segundos = diferenca.total_seconds()
    horas_manuais = int(total_segundos // 3600)
    minutos_manuais = int((total_segundos % 3600) // 60)
    segundos_restantes = int(total_segundos % 60)
    
    print(f"   Total segundos: {total_segundos}")
    print(f"   Horas: {horas_manuais}")
    print(f"   Minutos: {minutos_manuais}")
    print(f"   Segundos: {segundos_restantes}")
    print(f"   Resultado correto: {horas_manuais}h {minutos_manuais:02d}min")
    
    # Comparação
    print(f"\n🔍 COMPARAÇÃO:")
    print(f"   Sistema mostra: {horas_inteiras}h {minutos_int:02d}min")
    print(f"   Deveria ser: {horas_manuais}h {minutos_manuais:02d}min")
    
    if horas_inteiras == horas_manuais and minutos_int == minutos_manuais:
        print(f"   ✅ CORRETO!")
    else:
        print(f"   ❌ ERRO DETECTADO!")
        print(f"   Diferença: {(horas_inteiras*60 + minutos_int) - (horas_manuais*60 + minutos_manuais)} minutos")

def testar_template_corrigido():
    """
    Testa a lógica do template corrigido.
    """
    print(f"\n🔧 TESTE DA LÓGICA DO TEMPLATE CORRIGIDO")
    print("=" * 50)
    
    # Simular dados como no template
    horas_str = "1.1h"  # Como vem do backend
    horas_num = float(horas_str.replace('h', ''))
    
    print(f"📊 Dados do template:")
    print(f"   String original: {horas_str}")
    print(f"   Número extraído: {horas_num}")
    
    # Lógica ANTIGA (com round)
    horas_inteiras_old = int(horas_num)
    minutos_decimal_old = (horas_num - horas_inteiras_old) * 60
    minutos_old = round(minutos_decimal_old)
    
    print(f"\n❌ Lógica ANTIGA (com round):")
    print(f"   Horas inteiras: {horas_inteiras_old}")
    print(f"   Minutos decimal: {minutos_decimal_old}")
    print(f"   Minutos round: {minutos_old}")
    print(f"   Resultado: {horas_inteiras_old}h {minutos_old:02d}min")
    
    # Lógica NOVA (sem round)
    horas_inteiras_new = int(horas_num)
    minutos_decimal_new = (horas_num - horas_inteiras_new) * 60
    minutos_new = int(minutos_decimal_new)
    
    print(f"\n✅ Lógica NOVA (sem round):")
    print(f"   Horas inteiras: {horas_inteiras_new}")
    print(f"   Minutos decimal: {minutos_decimal_new}")
    print(f"   Minutos int: {minutos_new}")
    print(f"   Resultado: {horas_inteiras_new}h {minutos_new:02d}min")

def main():
    calcular_exato_como_sistema()
    testar_template_corrigido()

if __name__ == "__main__":
    main()
