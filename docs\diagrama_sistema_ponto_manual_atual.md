# 📊 **DIAGRAMA LÓGICO - SISTEMA DE PONTO MANUAL ATUAL**

## 🎯 **FLUXO COMPLETO ATUALIZADO**

```mermaid
graph TD
    A[👤 Usuário acessa /registro-ponto/manual] --> B[📋 Seleciona funcionário]
    B --> C[🌐 Sistema chama GET /api/obter-horarios/{funcionario_id}]
    
    C --> D{🔍 Funcionário ativo?}
    D -->|❌ NÃO| E[❌ Retorna erro: Funcionário inativo]
    D -->|✅ SIM| F[📊 obter_horarios_funcionario()]
    
    F --> G{⚙️ Horários configurados?}
    G -->|❌ NÃO| H[❌ Retorna erro: Horário não configurado]
    G -->|✅ SIM| I[📝 obter_batidas_do_dia()]
    
    I --> J[🔢 Calcular número da próxima batida]
    J --> K[🎯 classificar_batida_inteligente()]
    
    K --> L[🗄️ Consultar tabela dia_dados]
    L --> M{🕐 Hora atual em algum período?}
    
    M -->|✅ SIM| N[🎯 Mapear período → tipo]
    M -->|❌ NÃO| O[🔄 Usar lógica de sequência B1-B6]
    
    N --> P{📋 Tipo já registrado hoje?}
    P -->|✅ SIM| O
    P -->|❌ NÃO| Q[✅ Retornar tipo do período]
    
    O --> R[📊 Determinar próximo na sequência]
    R --> Q
    
    Q --> S{🚫 Limite 6 batidas atingido?}
    S -->|✅ SIM| T[❌ Retorna erro: Limite atingido]
    S -->|❌ NÃO| U[📤 Retorna JSON com tipos disponíveis]
    
    U --> V[👤 Usuário seleciona tipo e observações]
    V --> W[📨 POST /api/registrar-manual]
    
    W --> X[🔍 Validar parâmetros obrigatórios]
    X --> Y{✅ Parâmetros válidos?}
    Y -->|❌ NÃO| Z[❌ Retorna erro de validação]
    Y -->|✅ SIM| AA[📊 obter_horarios_funcionario()]
    
    AA --> BB{⚙️ Funcionário tem horários?}
    BB -->|❌ NÃO| CC[❌ Retorna erro: Horário não configurado]
    BB -->|✅ SIM| DD[🔍 validar_tipo_registro_por_horario()]
    
    DD --> EE{⏰ Tipo permitido no horário?}
    EE -->|❌ NÃO| FF[❌ Retorna erro: Horário não permitido]
    EE -->|✅ SIM| GG[📊 Calcular status de pontualidade]
    
    GG --> HH[💾 registrar_ponto_no_banco()]
    HH --> II[✅ Retorna sucesso com dados do registro]
```

---

## 🔄 **FLUXO DETALHADO POR ETAPAS**

### **📱 FASE 1: INTERFACE E OBTENÇÃO DE TIPOS**

```
┌─────────────────────────────────────────────────────────────────┐
│ 1. Usuário acessa /registro-ponto/manual                       │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 2. Seleciona funcionário no dropdown                           │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 3. JavaScript chama: GET /api/obter-horarios/{funcionario_id}  │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 4. VALIDAÇÃO: Funcionário ativo?                               │
│    - Consulta: funcionarios.status_cadastro = 'Ativo'          │
│    - Se inativo: Retorna erro                                   │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 5. obter_horarios_funcionario(funcionario_id)                  │
│    - Consulta: funcionarios + empresas + horarios_trabalho     │
│    - Prioridade: alocação > empresa > funcionário > padrão     │
│    - Retorna: entrada_manha, saida_almoco, entrada_tarde,      │
│               saida, tolerancia_minutos                         │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 6. VALIDAÇÃO: Horários configurados?                           │
│    - Se não configurado: Retorna erro                          │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 7. obter_batidas_do_dia(funcionario_id, hoje)                  │
│    - Consulta: registros_ponto WHERE DATE(data_hora) = hoje    │
│    - Retorna: lista de registros existentes ordenados          │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 8. Calcular número da próxima batida                           │
│    - num_batidas = len(registros_existentes)                   │
│    - numero_batida = num_batidas + 1                           │
└─────────────────────────────────────────────────────────────────┘
```

### **🧠 FASE 2: CLASSIFICAÇÃO INTELIGENTE**

```
┌─────────────────────────────────────────────────────────────────┐
│ 9. classificar_batida_inteligente()                            │
│    - Parâmetros: funcionario_id, numero_batida, turno_info,    │
│                  hora_atual                                     │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 10. CONSULTAR TABELA dia_dados                                 │
│     - Query: SELECT turno, horario_inicio, horario_fim         │
│              FROM dia_dados WHERE ativo = TRUE                 │
│              ORDER BY ordem_prioridade                         │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 11. DETERMINAR PERÍODO ATUAL                                   │
│     - Para cada período: verificar se inicio <= hora < fim     │
│     - Períodos: Manha(06:00-11:00), Intervalo(11:00-14:00),   │
│                 Tarde(14:00-18:00), Fim_Diurno(18:00-21:00)   │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┴───────────────┐
                │                               │
                ▼                               ▼
┌─────────────────────────────────┐  ┌─────────────────────────────────┐
│ 12a. PERÍODO ENCONTRADO         │  │ 12b. PERÍODO NÃO ENCONTRADO     │
│      - Mapear período → tipo:   │  │      - Usar lógica de sequência │
│        Manha → entrada_manha    │  │      - B1: entrada_manha        │
│        Intervalo → saida_almoco │  │      - B2: saida_almoco         │
│        Tarde → entrada_tarde    │  │      - B3: entrada_tarde        │
│        Fim_Diurno → saida       │  │      - B4: saida                │
└─────────────────────────────────┘  │      - B5: inicio_extra         │
                │                    │      - B6: fim_extra            │
                ▼                    └─────────────────────────────────┘
┌─────────────────────────────────┐                  │
│ 13. VERIFICAR SE JÁ REGISTRADO  │                  │
│     - Se tipo_sugerido in       │                  │
│       tipos_registrados:        │                  │
│       usar fallback sequência   │                  │
└─────────────────────────────────┘                  │
                │                                    │
                └────────────────┬───────────────────┘
                                 ▼
┌─────────────────────────────────────────────────────────────────┐
│ 14. RETORNAR TIPO DETERMINADO                                  │
│     - PRIORIDADE 1: Tipo do período (se não registrado)        │
│     - PRIORIDADE 2: Próximo na sequência (fallback)            │
└─────────────────────────────────────────────────────────────────┘
```

### **📊 FASE 3: PREPARAÇÃO DA RESPOSTA**

```
┌─────────────────────────────────────────────────────────────────┐
│ 15. VALIDAR LIMITE DE BATIDAS                                  │
│     - Se num_batidas >= 6: Retorna erro "Limite atingido"      │
│     - Máximo: B1-B4 (regulares) + B5-B6 (horas extras)        │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 16. CRIAR MAPEAMENTO DE TIPOS                                  │
│     - entrada_manha: "Entrada Manhã (08:00)"                   │
│     - saida_almoco: "Saída Intervalo (12:00)"                  │
│     - entrada_tarde: "Retorno Intervalo (13:00)"               │
│     - saida: "Saída Final (17:00)"                             │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 17. DETERMINAR TIPOS DISPONÍVEIS                               │
│     - Se proximo_tipo válido e não registrado:                 │
│       tipos_liberados = [proximo_tipo]                         │
│     - Caso contrário: tipos_liberados = []                     │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 18. RETORNAR JSON                                              │
│     {                                                           │
│       "success": true,                                          │
│       "horarios": {...},                                       │
│       "tipos_disponiveis": [tipo_determinado],                 │
│       "todos_tipos": [...],                                    │
│       "registros_existentes": [...]                            │
│     }                                                           │
└─────────────────────────────────────────────────────────────────┘
```

### **💾 FASE 4: REGISTRO DO PONTO**

```
┌─────────────────────────────────────────────────────────────────┐
│ 19. Usuário seleciona tipo e preenche observações             │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 20. JavaScript envia: POST /api/registrar-manual               │
│     - FormData: funcionario_id, tipo_registro, observacoes     │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 21. VALIDAR PARÂMETROS OBRIGATÓRIOS                            │
│     - funcionario_id: deve ser int válido                      │
│     - tipo_registro: deve estar na lista permitida             │
│     - observacoes: opcional                                     │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 22. obter_horarios_funcionario(funcionario_id)                 │
│     - Revalidar se funcionário tem horários configurados       │
│     - Se não: Retorna erro                                      │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 23. validar_tipo_registro_por_horario()                        │
│     - Parâmetros: tipo_registro, horarios_funcionario,         │
│                   funcionario_id                               │
│     - Determina período atual e valida se tipo é permitido     │
└─────────────────────────────────────────────────────────────────┘
```

### **⏰ FASE 5: VALIDAÇÃO E CÁLCULO DE STATUS**

```
┌─────────────────────────────────────────────────────────────────┐
│ 24. DETERMINAR PERÍODO ATUAL                                   │
│     - hora_atual = datetime.now()                              │
│     - Comparar com horários do funcionário:                    │
│       * antes_expediente: < entrada_manha                      │
│       * manha: entrada_manha <= hora <= saida_almoco           │
│       * almoco: saida_almoco < hora < entrada_tarde            │
│       * tarde: entrada_tarde <= hora <= saida                  │
│       * apos_expediente: > saida                               │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 25. VALIDAR TIPO POR PERÍODO                                   │
│     - entrada_manha: permitida sempre, status baseado em       │
│       tolerância                                               │
│     - saida_almoco: deve ter entrada_manha, sempre pontual     │
│     - entrada_tarde: deve ter saida_almoco, mín. 1h intervalo  │
│     - saida: deve ter entrada_tarde, status baseado em         │
│       tolerância                                               │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 26. CALCULAR STATUS DE PONTUALIDADE                            │
│     - entrada_manha:                                            │
│       * hora <= entrada_manha: "Pontual"                       │
│       * hora <= entrada_manha + tolerancia: "Pontual"          │
│       * hora > entrada_manha + tolerancia: "Atrasado"          │
│     - saida_almoco: sempre "Pontual" (flexível)                │
│     - entrada_tarde: com compensação automática                │
│     - saida: similar à entrada_manha                           │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 27. RETORNAR VALIDAÇÃO                                         │
│     {                                                           │
│       'permitido': true/false,                                 │
│       'mensagem': 'Descrição do resultado',                    │
│       'horario_liberado': true/false,                          │
│       'status': 'Pontual'/'Atrasado'                          │
│     }                                                           │
└─────────────────────────────────────────────────────────────────┘
```

### **💾 FASE 6: PERSISTÊNCIA NO BANCO**

```
┌─────────────────────────────────────────────────────────────────┐
│ 28. VERIFICAR SE VALIDAÇÃO PASSOU                              │
│     - Se não permitido: Retorna erro com mensagem              │
│     - Se permitido: Continua para registro                     │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 29. OBTER HORA ATUAL REAL                                      │
│     - hora_atual = obter_hora_atual(fuso_horario_manaus=True)  │
│     - ⚠️ IMPORTANTE: Registra horário REAL, sem ajustes        │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 30. registrar_ponto_no_banco()                                 │
│     - Parâmetros: funcionario_id, tipo_registro,               │
│                   metodo_registro='manual', observacoes,       │
│                   status_pontualidade                          │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 31. VALIDAR DUPLICATAS                                         │
│     - Verificar se já existe registro do mesmo tipo hoje       │
│     - Se existe: Retorna erro                                   │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 32. INSERIR NO BANCO DE DADOS                                  │
│     INSERT INTO registros_ponto                                │
│     (funcionario_id, tipo_registro, data_hora,                 │
│      metodo_registro, observacoes, criado_por,                 │
│      status_pontualidade)                                      │
│     VALUES (?, ?, NOW(), 'manual', ?, ?, ?)                    │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 33. CRIAR JUSTIFICATIVAS (SE NECESSÁRIO)                       │
│     - Se observações contêm justificativa de atraso:           │
│       INSERT INTO justificativas_ponto                         │
│       (funcionario_id, data_registro, tipo_justificativa,      │
│        motivo, status_aprovacao='pendente')                    │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│ 34. RETORNAR RESULTADO FINAL                                   │
│     {                                                           │
│       "success": true,                                          │
│       "message": "Ponto registrado com sucesso",               │
│       "registro": {                                             │
│         "id": 123,                                              │
│         "tipo_registro": "entrada_manha",                       │
│         "data_hora": "2025-07-17 08:54:00",                    │
│         "status_pontualidade": "Pontual"                       │
│       }                                                         │
│     }                                                           │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🎯 **PONTOS CRÍTICOS DO SISTEMA ATUAL**

### **✅ CARACTERÍSTICAS PRINCIPAIS:**

1. **HORÁRIO REAL PRESERVADO:** Sistema registra horário exato informado, sem ajustes
2. **PRIORIDADE DO PERÍODO:** Tabela `dia_dados` tem prioridade sobre sequência
3. **VALIDAÇÃO RIGOROSA:** Múltiplas camadas de validação antes do registro
4. **STATUS CALCULADO:** Pontualidade calculada na validação, não no registro
5. **FLEXIBILIDADE:** Suporte a B1-B6 (4 básicas + 2 extras)

### **🔄 LÓGICA DE DECISÃO:**

```
PRIORIDADE 1: Período da tabela dia_dados
├── Se hora atual está em período definido
├── E tipo do período não foi registrado
└── Retorna tipo do período

PRIORIDADE 2: Sequência de registros (fallback)
├── Se período indefinido OU tipo já registrado
├── Determina próximo tipo na sequência B1-B6
└── Retorna próximo tipo não registrado
```

### **⚠️ DIFERENÇAS DO DIAGRAMA ANTIGO:**

1. **❌ NÃO FAZ:** Ajuste automático de horário
2. **❌ NÃO FAZ:** Cálculo de horas trabalhadas no registro
3. **❌ NÃO FAZ:** Modificação do horário informado
4. **✅ FAZ:** Preserva horário real para auditoria
5. **✅ FAZ:** Calcula status baseado em tolerância
6. **✅ FAZ:** Prioriza período sobre sequência

---

## 📊 **TABELAS E CONSULTAS PRINCIPAIS**

### **🗄️ TABELAS CONSULTADAS:**

1. **`dia_dados`** - Períodos do dia (PRINCIPAL para decisão)
2. **`funcionarios`** - Dados básicos e status
3. **`empresas`** - Empresa vinculada
4. **`horarios_trabalho`** - Horários configurados
5. **`registros_ponto`** - Registros existentes e novos

### **🔍 CONSULTAS CRÍTICAS:**

```sql
-- Determinar período atual
SELECT turno, horario_inicio, horario_fim
FROM dia_dados 
WHERE ativo = TRUE 
ORDER BY ordem_prioridade;

-- Obter horários do funcionário
SELECT entrada_manha, saida_almoco, entrada_tarde, saida, tolerancia_minutos
FROM funcionarios f
LEFT JOIN empresas e ON f.empresa_id = e.id
LEFT JOIN horarios_trabalho ht ON e.id = ht.empresa_id
WHERE f.id = ? AND f.status_cadastro = 'Ativo';

-- Registros existentes do dia
SELECT tipo_registro, data_hora, status_pontualidade
FROM registros_ponto 
WHERE funcionario_id = ? AND DATE(data_hora) = CURDATE()
ORDER BY data_hora ASC;
```

**Este diagrama reflete o sistema ATUAL em produção, com todas as correções implementadas em 17/07/2025.**
