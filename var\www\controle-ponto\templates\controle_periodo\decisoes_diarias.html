{% extends "base.html" %}

{% block title %}{{ titulo }} - RLPONTO-WEB{% endblock %}

{% block extra_css %}
<style>
    /* ========================================
       RLPONTO-WEB VISUAL IDENTITY - v1.1
       DECISÕES DIÁRIAS - DESIGN MODERNO
       ======================================== */

    /* Variáveis CSS do Sistema */
    :root {
        --primary-color: #4fbdba;
        --primary-hover: #26a69a;
        --background-color: #f9fafb;
        --card-background: #ffffff;
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --border-color: #e5e7eb;
        --success-color: #10b981;
        --success-bg: #dcfce7;
        --warning-color: #f59e0b;
        --warning-bg: #fef3c7;
        --danger-color: #ef4444;
        --danger-bg: #fee2e2;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
    }

    .main-container {
        background-color: var(--background-color);
        min-height: 100vh;
        padding: var(--spacing-xl);
    }

    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        border-radius: 12px;
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        color: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .funcionario-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        margin-bottom: var(--spacing-lg);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .funcionario-header {
        background: linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%);
        padding: var(--spacing-md);
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .funcionario-info h4 {
        margin: 0;
        color: var(--text-primary);
        font-weight: 600;
    }

    .funcionario-info p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .decisoes-count {
        background: var(--warning-color);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .decisoes-lista {
        padding: 0;
    }

    .decisao-item {
        padding: var(--spacing-md);
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: background-color 0.2s ease;
    }

    .decisao-item:hover {
        background-color: #f8fafc;
    }

    .decisao-item:last-child {
        border-bottom: none;
    }

    .decisao-detalhes {
        flex: 1;
    }

    .decisao-data {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }

    .decisao-tipo {
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .decisao-valor {
        font-weight: 600;
        margin-right: var(--spacing-md);
    }

    .decisao-valor.positivo {
        color: var(--success-color);
    }

    .decisao-valor.negativo {
        color: var(--danger-color);
    }

    .decisao-acoes {
        display: flex;
        gap: 0.5rem;
    }

    .btn-classificar {
        padding: 0.375rem 0.75rem;
        border: none;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-banco {
        background: var(--success-bg);
        color: var(--success-color);
        border: 1px solid var(--success-color);
    }

    .btn-banco:hover {
        background: var(--success-color);
        color: white;
    }

    .btn-pagamento {
        background: #dbeafe;
        color: #1e40af;
        border: 1px solid #3b82f6;
    }

    .btn-pagamento:hover {
        background: #3b82f6;
        color: white;
    }

    .btn-desconto {
        background: var(--danger-bg);
        color: var(--danger-color);
        border: 1px solid var(--danger-color);
    }

    .btn-desconto:hover {
        background: var(--danger-color);
        color: white;
    }

    .stats-resumo {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-xl);
    }

    .stat-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: var(--spacing-md);
        text-align: center;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-primary);
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
    }

    .empty-state {
        text-align: center;
        padding: var(--spacing-xl);
        color: var(--text-secondary);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: var(--spacing-md);
        opacity: 0.5;
    }

    /* Responsividade */
    @media (max-width: 767px) {
        .main-container {
            padding: var(--spacing-md);
        }
        
        .decisao-item {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-md);
        }
        
        .decisao-acoes {
            width: 100%;
            justify-content: space-between;
        }
        
        .stats-resumo {
            grid-template-columns: 1fr;
        }
    }

    /* Loading state */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .btn-classificar:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* Success feedback */
    .classificado {
        background-color: #f0f9ff !important;
        border-left: 4px solid var(--success-color);
    }

    .classificado .decisao-acoes {
        opacity: 0.5;
    }

    /* Modal para observações */
    .modal-observacoes {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: white;
        border-radius: 12px;
        padding: var(--spacing-xl);
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-color);
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--text-secondary);
    }

    .form-group {
        margin-bottom: var(--spacing-md);
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-primary);
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 0.875rem;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .btn-primary:hover {
        background: var(--primary-hover);
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        margin-left: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Header da Página -->
    <div class="page-header">
        <h1>
            <i class="fas fa-tasks me-3"></i>
            {{ titulo }}
        </h1>
        <p>Classifique as horas como banco, pagamento ou desconto</p>
    </div>

    <!-- Estatísticas Resumo -->
    <div class="stats-resumo">
        <div class="stat-card">
            <div class="stat-number">{{ total_funcionarios }}</div>
            <div class="stat-label">Funcionários</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ total_decisoes }}</div>
            <div class="stat-label">Decisões Pendentes</div>
        </div>
    </div>

    <!-- Lista de Funcionários com Decisões -->
    {% if funcionarios_decisoes %}
        {% for func_id, dados in funcionarios_decisoes.items() %}
        <div class="funcionario-card">
            <div class="funcionario-header">
                <div class="funcionario-info">
                    <h4>{{ dados.funcionario.nome }}</h4>
                    <p>{{ dados.funcionario.setor or 'Sem setor definido' }}</p>
                </div>
                <div class="decisoes-count">
                    {{ dados.decisoes|length }} pendência{{ 's' if dados.decisoes|length != 1 else '' }}
                </div>
            </div>
            <div class="decisoes-lista">
                {% for decisao in dados.decisoes %}
                <div class="decisao-item" data-funcionario="{{ decisao.funcionario_id }}" data-data="{{ decisao.data_referencia }}">
                    <div class="decisao-detalhes">
                        <div class="decisao-data">{{ decisao.data_referencia.strftime('%d/%m/%Y') }}</div>
                        <div class="decisao-tipo">
                            {% if decisao.tipo_pendencia == 'horas_extras' %}
                                <i class="fas fa-plus-circle text-success"></i> Horas Extras
                            {% elif decisao.tipo_pendencia == 'atraso' %}
                                <i class="fas fa-clock text-danger"></i> Atraso na Entrada
                            {% elif decisao.tipo_pendencia == 'excesso_almoco' %}
                                <i class="fas fa-utensils text-warning"></i> Excesso no Almoço
                            {% elif decisao.tipo_pendencia == 'saida_antecipada' %}
                                <i class="fas fa-sign-out-alt text-warning"></i> Saída Antecipada
                            {% endif %}
                        </div>
                    </div>
                    <div class="decisao-valor {{ 'positivo' if decisao.tipo_pendencia == 'horas_extras' else 'negativo' }}">
                        {% if decisao.tipo_pendencia == 'horas_extras' %}+{% else %}-{% endif %}
                        {{ (decisao.minutos_pendentes // 60) }}h {{ (decisao.minutos_pendentes % 60) }}min
                    </div>
                    <div class="decisao-acoes">
                        <button class="btn-classificar btn-banco" 
                                onclick="classificarHoras('{{ decisao.funcionario_id }}', '{{ decisao.data_referencia }}', 'banco', '{{ decisao.tipo_pendencia }}', this)">
                            Banco
                        </button>
                        <button class="btn-classificar btn-pagamento" 
                                onclick="classificarHoras('{{ decisao.funcionario_id }}', '{{ decisao.data_referencia }}', 'pagamento', '{{ decisao.tipo_pendencia }}', this)">
                            Pagamento
                        </button>
                        <button class="btn-classificar btn-desconto" 
                                onclick="classificarHoras('{{ decisao.funcionario_id }}', '{{ decisao.data_referencia }}', 'desconto', '{{ decisao.tipo_pendencia }}', this)">
                            Desconto
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="empty-state">
            <i class="fas fa-check-circle"></i>
            <h3>Nenhuma decisão pendente</h3>
            <p>Todas as horas do período atual já foram classificadas.</p>
            <a href="{{ url_for('controle_periodo.dashboard') }}" class="btn-primary">
                <i class="fas fa-arrow-left me-2"></i>
                Voltar ao Dashboard
            </a>
        </div>
    {% endif %}
</div>

<!-- Modal para Observações -->
<div class="modal-observacoes" id="modalObservacoes">
    <div class="modal-content">
        <div class="modal-header">
            <h4>Adicionar Observação</h4>
            <button class="modal-close" onclick="fecharModal()">&times;</button>
        </div>
        <form id="formObservacoes">
            <div class="form-group">
                <label class="form-label">Observações (opcional):</label>
                <textarea class="form-control" id="observacoes" rows="3" 
                          placeholder="Digite observações sobre esta classificação..."></textarea>
            </div>
            <div class="form-group">
                <button type="submit" class="btn-primary">Confirmar Classificação</button>
                <button type="button" class="btn-secondary" onclick="fecharModal()">Cancelar</button>
            </div>
        </form>
    </div>
</div>

<script>
// Variáveis globais para o modal
let classificacaoAtual = {};

function classificarHoras(funcionarioId, dataReferencia, tipoClassificacao, tipoPendencia, botao) {
    // Armazenar dados da classificação
    classificacaoAtual = {
        funcionario_id: funcionarioId,
        data_referencia: dataReferencia,
        tipo_classificacao: tipoClassificacao,
        tipo_pendencia: tipoPendencia,
        botao: botao
    };
    
    // Abrir modal para observações
    document.getElementById('modalObservacoes').style.display = 'flex';
    document.getElementById('observacoes').focus();
}

function fecharModal() {
    document.getElementById('modalObservacoes').style.display = 'none';
    document.getElementById('observacoes').value = '';
    classificacaoAtual = {};
}

// Submeter classificação
document.getElementById('formObservacoes').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const observacoes = document.getElementById('observacoes').value;
    const botao = classificacaoAtual.botao;
    
    // Desabilitar botão e mostrar loading
    botao.disabled = true;
    botao.textContent = 'Processando...';
    
    // Enviar classificação
    fetch('{{ url_for("controle_periodo.classificar_horas") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            funcionario_id: classificacaoAtual.funcionario_id,
            data_referencia: classificacaoAtual.data_referencia,
            tipo_classificacao: classificacaoAtual.tipo_classificacao,
            tipo_pendencia: classificacaoAtual.tipo_pendencia,
            observacoes: observacoes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Marcar item como classificado
            const item = botao.closest('.decisao-item');
            item.classList.add('classificado');
            
            // Desabilitar todos os botões do item
            const botoes = item.querySelectorAll('.btn-classificar');
            botoes.forEach(btn => {
                btn.disabled = true;
                btn.style.opacity = '0.5';
            });
            
            // Mostrar feedback de sucesso
            botao.textContent = `✓ ${classificacaoAtual.tipo_classificacao.toUpperCase()}`;
            botao.style.background = '#10b981';
            botao.style.color = 'white';
            
            // Fechar modal
            fecharModal();
            
            // Atualizar contador se necessário
            setTimeout(() => {
                location.reload();
            }, 1500);
            
        } else {
            alert('Erro: ' + data.message);
            botao.disabled = false;
            botao.textContent = classificacaoAtual.tipo_classificacao.toUpperCase();
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao processar classificação');
        botao.disabled = false;
        botao.textContent = classificacaoAtual.tipo_classificacao.toUpperCase();
    });
});

// Fechar modal com ESC
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        fecharModal();
    }
});

// Fechar modal clicando fora
document.getElementById('modalObservacoes').addEventListener('click', function(e) {
    if (e.target === this) {
        fecharModal();
    }
});
</script>
{% endblock %}
