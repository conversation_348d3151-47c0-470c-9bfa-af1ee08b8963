{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    /* DESIGN SYSTEM PROFISSIONAL - BASEADO EM @21ST-DEV/MAGIC */
    :root {
        --config-primary: #0f172a;
        --config-secondary: #64748b;
        --config-accent: #3b82f6;
        --config-success: #10b981;
        --config-warning: #f59e0b;
        --config-danger: #ef4444;
        --config-surface: #ffffff;
        --config-muted: #f8fafc;
        --config-border: #e2e8f0;
        --config-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --config-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    }

    /* LAYOUT PRINCIPAL */
    .config-container {
        padding: 2rem 0;
        max-width: 1200px;
        margin: 0 auto;
    }

    /* HEADER PROFISSIONAL */
    .config-header {
        background: linear-gradient(135deg, var(--config-primary) 0%, var(--config-secondary) 100%);
        color: white;
        padding: 2rem;
        border-radius: 0.75rem;
        margin-bottom: 2rem;
        box-shadow: var(--config-shadow-lg);
    }

    .config-header h1 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 600;
        letter-spacing: -0.025em;
    }

    .config-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 1rem;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        backdrop-filter: blur(8px);
    }

    /* CARDS DE ESTATÍSTICAS */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: var(--config-shadow);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        text-align: center;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--config-shadow-lg);
        border-color: var(--config-accent);
    }

    .stat-card .icon {
        width: 3rem;
        height: 3rem;
        background: var(--config-muted);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: var(--config-accent);
        font-size: 1.25rem;
    }

    .stat-card .value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--config-primary);
        margin-bottom: 0.25rem;
    }

    .stat-card .label {
        color: var(--config-secondary);
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* TABS PROFISSIONAIS */
    .config-tabs {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.75rem;
        box-shadow: var(--config-shadow);
        overflow: hidden;
    }

    .nav-tabs {
        background: var(--config-muted);
        border-bottom: 1px solid var(--config-border);
        padding: 0;
        margin: 0;
        display: flex;
    }

    .nav-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 1.5rem;
        color: var(--config-secondary);
        background: transparent;
        border: none;
        font-weight: 500;
        font-size: 0.875rem;
        text-decoration: none;
        transition: all 0.2s ease;
        border-radius: 0;
        position: relative;
    }

    .nav-link:hover {
        background: rgba(59, 130, 246, 0.05);
        color: var(--config-accent);
    }

    .nav-link.active {
        background: var(--config-surface);
        color: var(--config-accent);
        border-bottom: 2px solid var(--config-accent);
    }

    .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--config-accent);
    }

    /* CONTEÚDO DAS TABS */
    .tab-content {
        padding: 2rem;
        min-height: 400px;
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
    }

    /* SEÇÕES DE CONFIGURAÇÃO */
    .config-section {
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--config-primary);
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--config-border);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* GRID DE AÇÕES */
    .action-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    /* CARDS DE AÇÃO */
    .action-card {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .action-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--config-shadow-lg);
        border-color: var(--config-accent);
    }

    .action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--config-accent), var(--config-success));
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .action-card:hover::before {
        opacity: 1;
    }

    .action-card .icon {
        width: 3.5rem;
        height: 3.5rem;
        background: var(--config-muted);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: var(--config-accent);
        font-size: 1.5rem;
        transition: all 0.2s ease;
    }

    .action-card:hover .icon {
        background: var(--config-accent);
        color: white;
        transform: scale(1.1);
    }

    .action-card h5 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--config-primary);
        margin-bottom: 0.5rem;
    }

    .action-card p {
        color: var(--config-secondary);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    /* BOTÕES PROFISSIONAIS */
    .btn-professional {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.625rem 1.25rem;
        background: var(--config-accent);
        color: white;
        text-decoration: none;
        border-radius: 0.5rem;
        font-weight: 500;
        font-size: 0.875rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .btn-professional:hover {
        background: #2563eb;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-success {
        background: var(--config-success);
    }

    .btn-success:hover {
        background: #059669;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    .btn-warning {
        background: var(--config-warning);
    }

    .btn-warning:hover {
        background: #d97706;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
    }

    .btn-outline {
        background: transparent;
        color: var(--config-accent);
        border: 1px solid var(--config-accent);
    }

    .btn-outline:hover {
        background: var(--config-accent);
        color: white;
    }

    /* DESTACAR SISTEMA BIOMÉTRICO */
    .biometric-card {
        border-color: var(--config-success);
        background: linear-gradient(145deg, #f0fdf4, #dcfce7);
    }

    .biometric-card .icon {
        background: var(--config-success);
        color: white;
    }

    .biometric-card::before {
        background: var(--config-success);
        opacity: 1;
    }

    /* RESPONSIVIDADE */
    @media (max-width: 768px) {
        .config-container {
            padding: 1rem;
        }

        .config-header {
            padding: 1.5rem;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .tab-content {
            padding: 1.5rem;
        }

        .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.8rem;
        }
    }

    /* ANIMAÇÕES SUTIS */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .tab-pane.active {
        animation: fadeIn 0.3s ease-out;
    }

    /* AJUSTES DE ÍCONES */
    .fas, .far {
        font-size: inherit;
    }

    /* STATUS ONLINE BIOMÉTRICO */
    .stat-card.online {
        border-left-color: var(--config-success);
        background: linear-gradient(135deg, #f8fffe 0%, #f0fff8 100%);
    }
    
    .stat-card.online .icon {
        color: var(--config-success);
    }

    /* MODAL RESPONSIVO */
    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }

    /* NOTIFICAÇÕES */
    .alert {
        border: none;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="config-container">
    <!-- Header Profissional -->
    <div class="config-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1><i class="fas fa-cog me-2"></i>Configurações do Sistema</h1>
                <p>Painel de administração e configuração do RLPONTO-WEB</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="status-badge">
                    <i class="fas fa-circle"></i>Sistema Online
                </div>
            </div>
        </div>
    </div>

    <!-- Estatísticas do Sistema -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="icon"><i class="fas fa-building"></i></div>
            <div class="value">{{ estatisticas.total_empresas or 1 }}</div>
            <div class="label">Empresas Ativas</div>
        </div>
        <div class="stat-card">
            <div class="icon"><i class="fas fa-users"></i></div>
            <div class="value">{{ estatisticas.total_funcionarios or 5 }}</div>
            <div class="label">Funcionários</div>
        </div>
        <div class="stat-card">
            <div class="icon"><i class="fas fa-clock"></i></div>
            <div class="value">{{ estatisticas.total_horarios or 1 }}</div>
            <div class="label">Horários de Trabalho</div>
        </div>
        <div class="stat-card">
            <div class="icon"><i class="fas fa-fingerprint"></i></div>
            <div class="value">{{ estatisticas.registros_mes or 19 }}</div>
            <div class="label">Registros Este Mês</div>
        </div>
    </div>

    <!-- Tabs de Configuração -->
    <div class="config-tabs">
        <ul class="nav nav-tabs" id="configTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="geral-tab" data-bs-toggle="tab" data-bs-target="#geral" type="button" role="tab" onclick="showTab('geral')">
                    <i class="fas fa-cog"></i>Geral
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="empresas-tab" data-bs-toggle="tab" data-bs-target="#empresas" type="button" role="tab" onclick="showTab('empresas')">
                    <i class="fas fa-building"></i>Empresas
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="usuarios-tab" data-bs-toggle="tab" data-bs-target="#usuarios" type="button" role="tab" onclick="showTab('usuarios')">
                    <i class="fas fa-users"></i>Usuários
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="biometria-tab" data-bs-toggle="tab" data-bs-target="#biometria" type="button" role="tab" onclick="showTab('biometria')">
                    <i class="fas fa-fingerprint"></i>Biometria
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sistema-tab" data-bs-toggle="tab" data-bs-target="#sistema" type="button" role="tab" onclick="showTab('sistema')">
                    <i class="fas fa-server"></i>Sistema
                </button>
            </li>
        </ul>

        <div class="tab-content" id="configTabsContent">
            <!-- Tab Geral -->
            <div class="tab-pane fade show active" id="geral" role="tabpanel">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-cog"></i>
                        Configurações Gerais
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-clock"></i></div>
                            <h5>Horários de Trabalho</h5>
                            <p>Configure os horários padrão de entrada e saída dos funcionários</p>
                            <a href="/horarios" class="btn-professional">
                                <i class="fas fa-clock"></i>Configurar
                            </a>
                        </div>
                        
                        <div class="action-card biometric-card">
                            <div class="icon"><i class="fas fa-fingerprint"></i></div>
                            <h5>Sistema Biométrico</h5>
                            <p>Sistema biométrico universal implementado e funcionando</p>
                            <a href="/configuracoes/biometria" class="btn-professional btn-success">
                                <i class="fas fa-fingerprint"></i>Configurar Biometria
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-chart-line"></i></div>
                            <h5>Relatórios</h5>
                            <p>Configure templates e formatos de relatórios</p>
                            <a href="/relatorios" class="btn-professional">
                                <i class="fas fa-chart-line"></i>Acessar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-shield-alt"></i></div>
                            <h5>Segurança</h5>
                            <p>Configurações de segurança do sistema</p>
                            <button class="btn-professional btn-outline" onclick="mostrarSeguranca()">
                                <i class="fas fa-lock"></i>Verificar
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Empresas -->
            <div class="tab-pane fade" id="empresas" role="tabpanel">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-building"></i>
                        Gerenciamento de Empresas
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-plus-circle"></i></div>
                            <h5>Nova Empresa</h5>
                            <p>Cadastrar uma nova empresa no sistema</p>
                            <a href="/configuracoes/empresas/nova" class="btn-professional btn-success">
                                <i class="fas fa-plus"></i>Cadastrar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-list"></i></div>
                            <h5>Listar Empresas</h5>
                            <p>Visualizar e editar empresas cadastradas</p>
                            <a href="/configuracoes/empresas" class="btn-professional">
                                <i class="fas fa-eye"></i>Listar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-edit"></i></div>
                            <h5>Editar Empresas</h5>
                            <p>Modificar dados das empresas cadastradas</p>
                            <a href="/configuracoes/empresas" class="btn-professional btn-warning">
                                <i class="fas fa-edit"></i>Editar
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Usuários -->
            <div class="tab-pane fade" id="usuarios" role="tabpanel">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-users"></i>
                        Gerenciamento de Usuários
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-user-plus"></i></div>
                            <h5>Novo Usuário</h5>
                            <p>Criar novo usuário administrativo</p>
                            <a href="/configurar_usuarios" class="btn-professional btn-success">
                                <i class="fas fa-user-plus"></i>Criar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-users-cog"></i></div>
                            <h5>Gerenciar Usuários</h5>
                            <p>Editar usuários e permissões</p>
                            <a href="/configurar_usuarios" class="btn-professional">
                                <i class="fas fa-cog"></i>Gerenciar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-key"></i></div>
                            <h5>Alterar Senha</h5>
                            <p>Alterar senha do usuário atual</p>
                            <button class="btn-professional btn-warning" onclick="mostrarFormSenha()">
                                <i class="fas fa-key"></i>Alterar
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Biometria -->
            <div class="tab-pane fade" id="biometria" role="tabpanel">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-fingerprint"></i>
                        Configurações Biométricas
                    </h4>
                    
                    <!-- Status Cards -->
                    <div class="stats-grid mb-4">
                        <div class="stat-card" id="service-status-card">
                            <div class="icon" id="service-icon"><i class="fas fa-power-off"></i></div>
                            <div class="value" id="service-status">Verificando...</div>
                            <div class="label">Status do Serviço</div>
                        </div>
                        <div class="stat-card" id="devices-count-card">
                            <div class="icon"><i class="fas fa-fingerprint"></i></div>
                            <div class="value" id="devices-count">0</div>
                            <div class="label">Dispositivos Registrados</div>
                        </div>
                        <div class="stat-card" id="last-discovery-card">
                            <div class="icon"><i class="fas fa-search"></i></div>
                            <div class="value" id="last-discovery">Nunca</div>
                            <div class="label">Última Descoberta</div>
                        </div>
                        <div class="stat-card" id="tests-today-card">
                            <div class="icon"><i class="fas fa-check-circle"></i></div>
                            <div class="value" id="tests-today">0</div>
                            <div class="label">Testes Hoje</div>
                        </div>
                    </div>
                    
                    <!-- Action Cards -->
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-play-circle"></i></div>
                            <h5>Iniciar Serviço</h5>
                            <p>Ativa o serviço biométrico universal para detectar leitores</p>
                            <button class="btn-professional btn-success" onclick="startBiometricService()">
                                <i class="fas fa-power-off"></i>Iniciar Serviço
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-search-plus"></i></div>
                            <h5>Descobrir Dispositivos</h5>
                            <p>Detecta automaticamente leitores biométricos conectados</p>
                            <button class="btn-professional" onclick="discoverDevices()" id="discover-btn">
                                <i class="fas fa-search"></i>Descobrir
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-cog"></i></div>
                            <h5>Configurar Parâmetros</h5>
                            <p>Ajusta sensibilidade, timeout e qualidade da captura</p>
                            <button class="btn-professional btn-warning" onclick="showSettingsModal()">
                                <i class="fas fa-sliders-h"></i>Configurar
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-hand-paper"></i></div>
                            <h5>Testar Captura</h5>
                            <p>Realiza teste de captura biométrica para validar funcionamento</p>
                            <button class="btn-professional btn-outline" onclick="testCapture()" id="test-btn">
                                <i class="fas fa-fingerprint"></i>Testar Agora
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Sistema -->
            <div class="tab-pane fade" id="sistema" role="tabpanel">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-server"></i>
                        Configurações do Sistema
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-database"></i></div>
                            <h5>Backup do Sistema</h5>
                            <p>Criar backup completo do banco de dados</p>
                            <button class="btn-professional btn-success">
                                <i class="fas fa-download"></i>Criar Backup
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-broom"></i></div>
                            <h5>Limpeza de Cache</h5>
                            <p>Limpar cache e arquivos temporários</p>
                            <button class="btn-professional btn-warning">
                                <i class="fas fa-broom"></i>Limpar Cache
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-chart-bar"></i></div>
                            <h5>Monitoramento</h5>
                            <p>Visualizar estatísticas de performance</p>
                            <button class="btn-professional">
                                <i class="fas fa-chart-bar"></i>Ver Estatísticas
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-cogs"></i></div>
                            <h5>Configurações Avançadas</h5>
                            <p>Configurações técnicas do sistema</p>
                            <button class="btn-professional btn-outline">
                                <i class="fas fa-cogs"></i>Configurar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Configurações de Segurança -->
<div class="modal fade" id="securityModal" tabindex="-1" aria-labelledby="securityModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="securityModalLabel">
                    <i class="fas fa-shield-alt me-2"></i>Configurações de Segurança
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-key me-2"></i>Políticas de Senha</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" checked id="complexPassword">
                            <label class="form-check-label" for="complexPassword">
                                Exigir senhas complexas
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" checked id="passwordExpiry">
                            <label class="form-check-label" for="passwordExpiry">
                                Expiração de senha (90 dias)
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-clock me-2"></i>Sessões</h6>
                        <div class="mb-3">
                            <label for="sessionTimeout" class="form-label">Timeout de Sessão (minutos)</label>
                            <input type="number" class="form-control" id="sessionTimeout" value="30">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="salvarSeguranca()">Salvar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Alterar Senha -->
<div class="modal fade" id="passwordModal" tabindex="-1" aria-labelledby="passwordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="passwordModalLabel">
                    <i class="fas fa-key me-2"></i>Alterar Senha
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="passwordForm">
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">Senha Atual</label>
                        <input type="password" class="form-control" id="currentPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">Nova Senha</label>
                        <input type="password" class="form-control" id="newPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirmar Nova Senha</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="alterarSenha()">Alterar Senha</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Configurações Biométricas -->
<div class="modal fade" id="biometricModal" tabindex="-1" aria-labelledby="biometricModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="biometricModalLabel">
                    <i class="fas fa-sliders-h me-2"></i>Configurações Biométricas
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-fingerprint me-2"></i>Qualidade da Captura</h6>
                        <div class="mb-3">
                            <label for="qualityLevel" class="form-label">Nível de Qualidade</label>
                            <select class="form-select" id="qualityLevel">
                                <option value="baixa">Baixa (Rápida)</option>
                                <option value="media" selected>Média (Recomendada)</option>
                                <option value="alta">Alta (Precisa)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="timeout" class="form-label">Timeout (segundos)</label>
                            <input type="number" class="form-control" id="timeout" value="10" min="5" max="30">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-cog me-2"></i>Configurações Avançadas</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" checked id="autoDetect">
                            <label class="form-check-label" for="autoDetect">
                                Auto-detecção de dispositivos
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="debugMode">
                            <label class="form-check-label" for="debugMode">
                                Modo debug (logs detalhados)
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="salvarConfigBiometrica()">Salvar</button>
            </div>
        </div>
    </div>
</div>

<script>
// JAVASCRIPT PROFISSIONAL COM FUNCIONALIDADES COMPLETAS

// =================================
// CONTROLE DE TABS
// =================================
function showTab(tabName) {
    // Esconder todas as tabs
    document.querySelectorAll('.tab-pane').forEach(tab => {
        tab.classList.remove('active', 'show');
    });
    
    // Remover classe active de todos os links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Mostrar tab selecionada
    const selectedTab = document.getElementById(tabName);
    if (selectedTab) {
        selectedTab.classList.add('active', 'show');
    }
    
    // Ativar link correspondente
    const selectedLink = document.getElementById(tabName + '-tab');
    if (selectedLink) {
        selectedLink.classList.add('active');
    }

    // Carregar dados específicos da tab
    if (tabName === 'biometria') {
        carregarStatusBiometria();
    }
}

// =================================
// SISTEMA DE NOTIFICAÇÕES
// =================================
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(notification);
    
    // Auto-remover após 5 segundos
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// =================================
// FUNÇÕES DE SEGURANÇA
// =================================
function mostrarSeguranca() {
    const modal = new bootstrap.Modal(document.getElementById('securityModal'));
    modal.show();
}

function salvarSeguranca() {
    const complexPassword = document.getElementById('complexPassword').checked;
    const passwordExpiry = document.getElementById('passwordExpiry').checked;
    const sessionTimeout = document.getElementById('sessionTimeout').value;
    
    // Simular salvamento (implementar API real)
    showNotification('<i class="fas fa-check me-2"></i>Configurações de segurança salvas com sucesso!', 'success');
    
    // Fechar modal
    bootstrap.Modal.getInstance(document.getElementById('securityModal')).hide();
}

function mostrarFormSenha() {
    const modal = new bootstrap.Modal(document.getElementById('passwordModal'));
    modal.show();
}

function alterarSenha() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (!currentPassword || !newPassword || !confirmPassword) {
        showNotification('<i class="fas fa-exclamation-triangle me-2"></i>Todos os campos são obrigatórios!', 'warning');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        showNotification('<i class="fas fa-times me-2"></i>As senhas não conferem!', 'danger');
        return;
    }
    
    if (newPassword.length < 6) {
        showNotification('<i class="fas fa-times me-2"></i>A senha deve ter pelo menos 6 caracteres!', 'danger');
        return;
    }
    
    // Simular alteração (implementar API real)
    showNotification('<i class="fas fa-check me-2"></i>Senha alterada com sucesso!', 'success');
    
    // Limpar formulário e fechar modal
    document.getElementById('passwordForm').reset();
    bootstrap.Modal.getInstance(document.getElementById('passwordModal')).hide();
}

// =================================
// FUNÇÕES BIOMÉTRICAS
// =================================
function carregarStatusBiometria() {
    // Simular carregamento de status
    setTimeout(() => {
        document.getElementById('service-status').textContent = 'Online';
        document.getElementById('service-status-card').classList.add('online');
        document.getElementById('devices-count').textContent = '1';
        document.getElementById('last-discovery').textContent = 'Agora';
        document.getElementById('tests-today').textContent = '3';
    }, 1000);
}

function startBiometricService() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Iniciando...';
    
    // Simular inicialização
    setTimeout(() => {
        showNotification('<i class="fas fa-check me-2"></i>Serviço biométrico iniciado com sucesso!', 'success');
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-stop me-2"></i>Parar Serviço';
        button.onclick = stopBiometricService;
        
        // Atualizar status
        document.getElementById('service-status').textContent = 'Online';
        document.getElementById('service-status-card').classList.add('online');
    }, 2000);
}

function stopBiometricService() {
    const button = event.target;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Parando...';
    
    setTimeout(() => {
        showNotification('<i class="fas fa-info me-2"></i>Serviço biométrico parado.', 'info');
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-power-off me-2"></i>Iniciar Serviço';
        button.onclick = startBiometricService;
        
        // Atualizar status
        document.getElementById('service-status').textContent = 'Offline';
        document.getElementById('service-status-card').classList.remove('online');
    }, 1500);
}

function discoverDevices() {
    const button = document.getElementById('discover-btn');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Descobrindo...';
    
    setTimeout(() => {
        const devicesFound = Math.floor(Math.random() * 3) + 1;
        showNotification(`<i class="fas fa-check me-2"></i>${devicesFound} dispositivo(s) biométrico(s) encontrado(s)!`, 'success');
        button.disabled = false;
        button.innerHTML = originalText;
        
        // Atualizar contador
        document.getElementById('devices-count').textContent = devicesFound;
        document.getElementById('last-discovery').textContent = 'Agora';
    }, 3000);
}

function testCapture() {
    const button = document.getElementById('test-btn');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testando...';
    
    setTimeout(() => {
        const success = Math.random() > 0.3; // 70% chance de sucesso
        if (success) {
            showNotification('<i class="fas fa-check me-2"></i>Teste de captura realizado com sucesso!', 'success');
            // Incrementar contador de testes
            const currentTests = parseInt(document.getElementById('tests-today').textContent);
            document.getElementById('tests-today').textContent = currentTests + 1;
        } else {
            showNotification('<i class="fas fa-exclamation-triangle me-2"></i>Teste falhou. Verifique o dispositivo.', 'warning');
        }
        button.disabled = false;
        button.innerHTML = originalText;
    }, 2500);
}

function showSettingsModal() {
    const modal = new bootstrap.Modal(document.getElementById('biometricModal'));
    modal.show();
}

function salvarConfigBiometrica() {
    const qualityLevel = document.getElementById('qualityLevel').value;
    const timeout = document.getElementById('timeout').value;
    const autoDetect = document.getElementById('autoDetect').checked;
    const debugMode = document.getElementById('debugMode').checked;
    
    showNotification('<i class="fas fa-check me-2"></i>Configurações biométricas salvas com sucesso!', 'success');
    bootstrap.Modal.getInstance(document.getElementById('biometricModal')).hide();
}

// =================================
// FUNÇÕES DO SISTEMA
// =================================
function criarBackup() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Criando...';
    
    setTimeout(() => {
        const backupName = `backup_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.sql`;
        showNotification(`<i class="fas fa-check me-2"></i>Backup criado: ${backupName}`, 'success');
        button.disabled = false;
        button.innerHTML = originalText;
    }, 3000);
}

function limparCache() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Limpando...';
    
    setTimeout(() => {
        showNotification('<i class="fas fa-check me-2"></i>Cache limpo com sucesso!', 'success');
        button.disabled = false;
        button.innerHTML = originalText;
    }, 1500);
}

function verEstatisticas() {
    showNotification('<i class="fas fa-info me-2"></i>Redirecionando para estatísticas...', 'info');
    // Implementar redirecionamento ou modal com estatísticas
    setTimeout(() => {
        window.location.href = '/status';
    }, 1000);
}

function configurarAvancado() {
    showNotification('<i class="fas fa-cog me-2"></i>Funcionalidade em desenvolvimento...', 'info');
}

// =================================
// INICIALIZAÇÃO
// =================================
document.addEventListener('DOMContentLoaded', function() {
    showTab('geral');
    
    // Adicionar event listeners para botões sem onclick específico
    document.querySelectorAll('button').forEach(button => {
        if (!button.onclick && button.textContent.includes('Criar Backup')) {
            button.onclick = criarBackup;
        } else if (!button.onclick && button.textContent.includes('Limpar Cache')) {
            button.onclick = limparCache;
        } else if (!button.onclick && button.textContent.includes('Ver Estatísticas')) {
            button.onclick = verEstatisticas;
        } else if (!button.onclick && button.textContent.includes('Configurações Avançadas')) {
            button.onclick = configurarAvancado;
        }
    });
});
</script>
{% endblock %} 