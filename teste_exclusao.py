#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para testar a exclusão de uma empresa
Data: 03/07/2025
"""

import requests
import json
import sys

def main():
    try:
        # ID da empresa a ser excluída (ajuste conforme necessário)
        empresa_id = 7  # ID da empresa criada anteriormente
        
        # URL da API
        url = f"http://************/configuracoes/empresas/{empresa_id}/excluir"
        
        # Cabeçalhos
        headers = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # Dados
        data = {
            'is_ajax': True
        }
        
        # Fazer a requisição
        print(f"Enviando requisição para excluir a empresa ID {empresa_id}...")
        response = requests.post(url, headers=headers, data=json.dumps(data))
        
        # Verificar a resposta
        print(f"Status code: {response.status_code}")
        print(f"Resposta: {response.text}")
        
        if response.status_code == 200:
            print("✅ Exclusão bem-sucedida!")
        else:
            print("❌ Falha na exclusão!")
            
        # Verificar se a empresa foi realmente excluída (marcada como inativa)
        from utils.database import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, razao_social, ativa FROM empresas WHERE id = %s", (empresa_id,))
        empresa = cursor.fetchone()
        
        if empresa:
            ativa = empresa['ativa'] if isinstance(empresa, dict) else empresa[2]
            if not ativa:
                print(f"✅ Empresa ID {empresa_id} marcada como inativa no banco de dados!")
            else:
                print(f"❌ Empresa ID {empresa_id} ainda está ativa no banco de dados!")
        else:
            print(f"❌ Empresa ID {empresa_id} não encontrada no banco de dados!")
            
        conn.close()
        
    except Exception as e:
        print(f"Erro: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 