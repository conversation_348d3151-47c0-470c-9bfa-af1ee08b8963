#!/usr/bin/env python3
"""
Script de Teste: Sistema de Herança Dinâmica de Jornadas
Sistema de Controle de Ponto - RLPONTO-WEB
Data: 14/07/2025

Testa o sistema de herança dinâmica de jornadas implementado.
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
from sistema_heranca_jornadas import SistemaHerancaJornadas
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def testar_verificacao_consistencia():
    """Testa a verificação de consistência do sistema."""
    logger.info("🔍 TESTE 1: Verificação de Consistência")
    logger.info("-" * 50)
    
    try:
        relatorio = SistemaHerancaJornadas.verificar_consistencia_jornadas()
        
        if 'erro' in relatorio:
            logger.error(f"❌ Erro na verificação: {relatorio['erro']}")
            return False
        
        logger.info("📊 Relatório de Consistência:")
        logger.info(f"   • Funcionários sem jornada: {relatorio['funcionarios_sem_jornada']}")
        logger.info(f"   • Funcionários com jornada inativa: {relatorio['funcionarios_jornada_inativa']}")
        logger.info(f"   • Empresas sem jornada padrão: {relatorio['empresas_sem_jornada_padrao']}")
        logger.info(f"   • Alocações com jornada inconsistente: {relatorio['alocacoes_jornada_inconsistente']}")
        
        if relatorio['problemas']:
            logger.warning("⚠️ Problemas encontrados:")
            for problema in relatorio['problemas']:
                logger.warning(f"   • {problema}")
        else:
            logger.info("✅ Nenhum problema de consistência encontrado!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro no teste de consistência: {e}")
        return False

def testar_historico_funcionario():
    """Testa o histórico de jornadas de um funcionário."""
    logger.info("📋 TESTE 2: Histórico de Funcionário")
    logger.info("-" * 50)
    
    try:
        db = DatabaseManager()
        
        # Buscar um funcionário ativo
        funcionario = db.execute_query("""
            SELECT f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id,
                   e.nome_fantasia as empresa_nome,
                   jt.nome_jornada
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
            WHERE f.ativo = TRUE
            LIMIT 1
        """, fetch_one=True)
        
        if not funcionario:
            logger.warning("⚠️ Nenhum funcionário ativo encontrado")
            return False
        
        logger.info(f"👤 Funcionário: {funcionario['nome_completo']}")
        logger.info(f"🏢 Empresa: {funcionario['empresa_nome']}")
        logger.info(f"⏰ Jornada atual: {funcionario['nome_jornada']}")
        
        # Obter histórico
        historico = SistemaHerancaJornadas.obter_historico_jornadas_funcionario(
            funcionario['id'], 10
        )
        
        logger.info(f"📚 Histórico de jornadas ({len(historico)} registros):")
        
        if not historico:
            logger.info("   • Nenhum registro de mudança encontrado")
        else:
            for i, registro in enumerate(historico, 1):
                logger.info(f"   {i}. {registro['data_mudanca']} - {registro['tipo_mudanca']}")
                logger.info(f"      Motivo: {registro['motivo']}")
                if registro['jornada_anterior_nome']:
                    logger.info(f"      De: {registro['jornada_anterior_nome']} → Para: {registro['jornada_nova_nome']}")
                else:
                    logger.info(f"      Nova jornada: {registro['jornada_nova_nome']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro no teste de histórico: {e}")
        return False

def testar_aplicacao_jornada_empresa():
    """Testa a aplicação de jornada da empresa a um funcionário."""
    logger.info("🔄 TESTE 3: Aplicação de Jornada da Empresa")
    logger.info("-" * 50)
    
    try:
        db = DatabaseManager()
        
        # Buscar uma empresa com jornada padrão
        empresa = db.execute_query("""
            SELECT e.id, e.nome_fantasia, jt.id as jornada_id, jt.nome_jornada
            FROM empresas e
            LEFT JOIN jornadas_trabalho jt ON e.id = jt.empresa_id
            WHERE e.ativa = TRUE AND jt.padrao = TRUE AND jt.ativa = TRUE
            LIMIT 1
        """, fetch_one=True)
        
        if not empresa:
            logger.warning("⚠️ Nenhuma empresa com jornada padrão encontrada")
            return False
        
        # Buscar funcionário desta empresa
        funcionario = db.execute_query("""
            SELECT id, nome_completo, jornada_trabalho_id
            FROM funcionarios 
            WHERE empresa_id = %s AND ativo = TRUE
            LIMIT 1
        """, (empresa['id'],), fetch_one=True)
        
        if not funcionario:
            logger.warning(f"⚠️ Nenhum funcionário encontrado na empresa {empresa['nome_fantasia']}")
            return False
        
        logger.info(f"🏢 Empresa: {empresa['nome_fantasia']}")
        logger.info(f"⏰ Jornada padrão: {empresa['nome_jornada']}")
        logger.info(f"👤 Funcionário: {funcionario['nome_completo']}")
        logger.info(f"⏰ Jornada atual: ID {funcionario['jornada_trabalho_id']}")
        
        # Aplicar jornada da empresa
        sucesso = SistemaHerancaJornadas.aplicar_jornada_empresa_funcionario(
            funcionario['id'], empresa['id'], usuario_responsavel=1
        )
        
        if sucesso:
            logger.info("✅ Jornada da empresa aplicada com sucesso!")
            
            # Verificar se foi aplicada
            funcionario_atualizado = db.execute_query("""
                SELECT jornada_trabalho_id, data_atualizacao_jornada
                FROM funcionarios 
                WHERE id = %s
            """, (funcionario['id'],), fetch_one=True)
            
            logger.info(f"⏰ Nova jornada: ID {funcionario_atualizado['jornada_trabalho_id']}")
            logger.info(f"📅 Atualizado em: {funcionario_atualizado['data_atualizacao_jornada']}")
        else:
            logger.error("❌ Falha ao aplicar jornada da empresa")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro no teste de aplicação: {e}")
        return False

def testar_triggers_funcionando():
    """Verifica se os triggers estão funcionando."""
    logger.info("⚙️ TESTE 4: Verificação dos Triggers")
    logger.info("-" * 50)

    try:
        db = DatabaseManager()

        # Verificar se triggers existem (simplificado)
        triggers = db.execute_query("""
            SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE
            FROM INFORMATION_SCHEMA.TRIGGERS
            WHERE TRIGGER_SCHEMA = 'controle_ponto'
            AND TRIGGER_NAME IN (
                'tr_atualizar_jornadas_funcionarios',
                'tr_historico_mudanca_jornada_funcionario',
                'tr_historico_alocacao_criada',
                'tr_historico_alocacao_finalizada'
            )
        """)

        logger.info(f"🔧 Triggers encontrados ({len(triggers)}):")
        for trigger in triggers:
            logger.info(f"   • {trigger['TRIGGER_NAME']} - {trigger['EVENT_MANIPULATION']} on {trigger['EVENT_OBJECT_TABLE']}")

        # Verificar se tabela de log existe e tem dados
        log_count = db.execute_query("""
            SELECT COUNT(*) as total FROM log_mudancas_jornada
        """, fetch_one=True)

        logger.info(f"📊 Registros no log de mudanças: {log_count['total']}")

        # Verificar últimos registros (simplificado)
        if log_count['total'] > 0:
            ultimos_logs = db.execute_query("""
                SELECT tipo_mudanca, motivo, data_mudanca
                FROM log_mudancas_jornada
                ORDER BY data_mudanca DESC
                LIMIT 3
            """)

            logger.info("📋 Últimos registros de mudança:")
            for log in ultimos_logs:
                logger.info(f"   • {log['data_mudanca']} - {log['tipo_mudanca']}")
                logger.info(f"     Motivo: {log['motivo']}")

        return True

    except Exception as e:
        logger.error(f"❌ Erro na verificação de triggers: {e}")
        return False

def main():
    """Função principal de teste."""
    logger.info("🧪 INICIANDO TESTES DO SISTEMA DE HERANÇA DINÂMICA DE JORNADAS")
    logger.info("=" * 80)
    
    testes_passaram = 0
    total_testes = 4
    
    try:
        # Teste 1: Verificação de consistência
        if testar_verificacao_consistencia():
            testes_passaram += 1
            logger.info("✅ TESTE 1 PASSOU\n")
        else:
            logger.error("❌ TESTE 1 FALHOU\n")
        
        # Teste 2: Histórico de funcionário
        if testar_historico_funcionario():
            testes_passaram += 1
            logger.info("✅ TESTE 2 PASSOU\n")
        else:
            logger.error("❌ TESTE 2 FALHOU\n")
        
        # Teste 3: Aplicação de jornada
        if testar_aplicacao_jornada_empresa():
            testes_passaram += 1
            logger.info("✅ TESTE 3 PASSOU\n")
        else:
            logger.error("❌ TESTE 3 FALHOU\n")
        
        # Teste 4: Verificação de triggers
        if testar_triggers_funcionando():
            testes_passaram += 1
            logger.info("✅ TESTE 4 PASSOU\n")
        else:
            logger.error("❌ TESTE 4 FALHOU\n")
        
        # Resultado final
        logger.info("=" * 80)
        logger.info(f"🎯 RESULTADO FINAL: {testes_passaram}/{total_testes} testes passaram")
        
        if testes_passaram == total_testes:
            logger.info("🎉 TODOS OS TESTES PASSARAM! Sistema funcionando corretamente.")
            logger.info("")
            logger.info("✅ Sistema de Herança Dinâmica de Jornadas está OPERACIONAL")
            logger.info("✅ Triggers de atualização automática funcionando")
            logger.info("✅ Sistema de histórico completo implementado")
            logger.info("✅ Logs de mudanças registrando corretamente")
            return True
        else:
            logger.error(f"❌ {total_testes - testes_passaram} teste(s) falharam")
            return False
        
    except Exception as e:
        logger.error(f"❌ ERRO CRÍTICO NOS TESTES: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
