#!/usr/bin/env python3
"""
Investigar: Por que mudanças na jornada da empresa não propagam automaticamente?
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def investigar_trigger_jornada():
    """Investigar sistema de propagação automática"""
    
    print("🔍 INVESTIGAÇÃO: PROPAGAÇÃO AUTOMÁTICA DE JORNADAS")
    print("=" * 70)
    
    db = DatabaseManager()
    
    # 1. Verificar se existem triggers para jornadas_trabalho
    print("\n📋 PASSO 1: Verificando triggers na tabela jornadas_trabalho...")
    
    try:
        triggers = db.execute_query("""
            SELECT TRIGGER_NAME, EVENT_MANIPULATION, ACTION_TIMING
            FROM INFORMATION_SCHEMA.TRIGGERS 
            WHERE TRIGGER_SCHEMA = DATABASE()
            AND EVENT_OBJECT_TABLE = 'jornadas_trabalho'
        """)
        
        if triggers:
            print(f"✅ Encontrados {len(triggers)} triggers:")
            for trigger in triggers:
                print(f"   • {trigger['TRIGGER_NAME']} - {trigger['ACTION_TIMING']} {trigger['EVENT_MANIPULATION']}")
        else:
            print("❌ NENHUM trigger encontrado na tabela jornadas_trabalho!")
            print("💡 ESTE É O PROBLEMA! Não há trigger para propagar mudanças!")
    
    except Exception as e:
        print(f"❌ Erro ao verificar triggers: {e}")
    
    # 2. Verificar qual empresa foi alterada (exemplo)
    print("\n📋 PASSO 2: Identificando empresa que foi alterada...")
    
    # Buscar empresa AiNexus (que você alterou)
    empresa_teste = db.execute_query("""
        SELECT e.id, e.razao_social, e.nome_fantasia,
               jt.id as jornada_id, jt.nome_jornada,
               jt.seg_qui_entrada, jt.seg_qui_saida,
               jt.sexta_entrada, jt.sexta_saida,
               jt.tolerancia_entrada_minutos
        FROM empresas e
        INNER JOIN jornadas_trabalho jt ON e.id = jt.empresa_id
        WHERE jt.padrao = 1 AND jt.ativa = 1
        AND e.nome_fantasia LIKE '%AiNexus%'
    """, fetch_one=True)
    
    if empresa_teste:
        print(f"🏢 EMPRESA TESTE: {empresa_teste['nome_fantasia']} (ID: {empresa_teste['id']})")
        print(f"   • Jornada: {empresa_teste['nome_jornada']} (ID: {empresa_teste['jornada_id']})")
        print(f"   • Seg-Qui: {empresa_teste['seg_qui_entrada']} às {empresa_teste['seg_qui_saida']}")
        print(f"   • Tolerância: {empresa_teste['tolerancia_entrada_minutos']} min")
        
        empresa_id = empresa_teste['id']
        jornada_id = empresa_teste['jornada_id']
    else:
        print("❌ Empresa de teste não encontrada")
        return
    
    # 3. Verificar funcionários dessa empresa
    print(f"\n📋 PASSO 3: Verificando funcionários da empresa {empresa_teste['nome_fantasia']}...")
    
    funcionarios = db.execute_query("""
        SELECT f.id, f.nome_completo, f.jornada_trabalho_id, f.usa_horario_empresa,
               jt.nome_jornada as jornada_atual,
               jt.seg_qui_entrada as func_entrada,
               jt.seg_qui_saida as func_saida,
               jt.tolerancia_entrada_minutos as func_tolerancia
        FROM funcionarios f
        LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.empresa_id = %s AND f.ativo = 1
    """, (empresa_id,))
    
    if funcionarios:
        print(f"👥 Encontrados {len(funcionarios)} funcionários:")
        
        funcionarios_desatualizados = 0
        
        for func in funcionarios:
            print(f"\n   👤 {func['nome_completo']} (ID: {func['id']})")
            print(f"      • Jornada atual: {func['jornada_atual']} (ID: {func['jornada_trabalho_id']})")
            print(f"      • Horário: {func['func_entrada']} às {func['func_saida']}")
            print(f"      • Tolerância: {func['func_tolerancia']} min")
            print(f"      • Usa herança: {func['usa_horario_empresa']}")
            
            # Verificar se está desatualizado
            if (func['jornada_trabalho_id'] != jornada_id or
                func['func_entrada'] != empresa_teste['seg_qui_entrada'] or
                func['func_saida'] != empresa_teste['seg_qui_saida'] or
                func['func_tolerancia'] != empresa_teste['tolerancia_entrada_minutos']):
                
                print(f"      ❌ DESATUALIZADO!")
                funcionarios_desatualizados += 1
            else:
                print(f"      ✅ Atualizado")
        
        if funcionarios_desatualizados > 0:
            print(f"\n❌ PROBLEMA CONFIRMADO: {funcionarios_desatualizados} funcionários desatualizados!")
            print(f"💡 SOLUÇÃO: Criar trigger para atualização automática")
    
    # 4. Verificar funcionários alocados
    print(f"\n📋 PASSO 4: Verificando funcionários ALOCADOS para esta empresa...")
    
    alocados = db.execute_query("""
        SELECT f.id, f.nome_completo, f.empresa_id as empresa_principal,
               fa.empresa_cliente_id, fa.jornada_trabalho_id as jornada_alocacao,
               jt.nome_jornada as jornada_atual,
               jt.seg_qui_entrada, jt.seg_qui_saida
        FROM funcionario_alocacoes fa
        INNER JOIN funcionarios f ON fa.funcionario_id = f.id
        LEFT JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
        WHERE fa.empresa_cliente_id = %s AND fa.ativo = 1
    """, (empresa_id,))
    
    if alocados:
        print(f"👥 Encontrados {len(alocados)} funcionários alocados:")
        
        for func in alocados:
            print(f"\n   👤 {func['nome_completo']} (ID: {func['id']})")
            print(f"      • Empresa principal: {func['empresa_principal']}")
            print(f"      • Alocado para: {func['empresa_cliente_id']}")
            print(f"      • Jornada alocação: {func['jornada_atual']} (ID: {func['jornada_alocacao']})")
            
            if func['jornada_alocacao'] != jornada_id:
                print(f"      ❌ ALOCAÇÃO DESATUALIZADA!")
            else:
                print(f"      ✅ Alocação atualizada")
    else:
        print("📊 Nenhum funcionário alocado para esta empresa")
    
    print(f"\n🎯 DIAGNÓSTICO FINAL:")
    print(f"❌ PROBLEMA: Não há sistema automático de propagação de mudanças!")
    print(f"💡 SOLUÇÃO: Implementar trigger/sistema que:")
    print(f"   1. Detecte mudanças na jornada padrão da empresa")
    print(f"   2. Atualize TODOS os funcionários principais")
    print(f"   3. Atualize TODAS as alocações ativas")
    print(f"   4. Registre no histórico")

if __name__ == "__main__":
    investigar_trigger_jornada()
