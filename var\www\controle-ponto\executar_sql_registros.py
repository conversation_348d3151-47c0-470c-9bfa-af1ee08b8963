#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para executar SQL de criação das tabelas de registros de ponto
"""

import pymysql
import sys
import os
from utils.config import Config

def executar_sql_registros():
    """
    Executa o script SQL para criar tabelas de registros de ponto.
    """
    try:
        # Ler o arquivo SQL
        sql_file = 'sql_registros_ponto.sql'
        
        if not os.path.exists(sql_file):
            print(f"❌ Arquivo {sql_file} não encontrado!")
            return False
        
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # Conectar ao banco
        print(f"🔗 Conectando ao MySQL em {Config.DB_HOST}...")
        config = Config.get_database_url()
        
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        print("✅ Conexão estabelecida com sucesso!")
        
        # Executar comandos SQL separadamente
        commands = sql_content.split(';')
        
        executed = 0
        for i, command in enumerate(commands):
            command = command.strip()
            if command and not command.startswith('--'):
                try:
                    cursor.execute(command)
                    executed += 1
                    if executed % 5 == 0:
                        print(f"⏳ Executados {executed} comandos...")
                except Exception as e:
                    # Ignorar alguns erros comuns (tabelas já existem, etc)
                    if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                        print(f"⚠️ Comando {i+1}: {str(e)[:100]}... (ignorado)")
                    else:
                        print(f"❌ Erro no comando {i+1}: {e}")
        
        conn.commit()
        
        print(f"✅ Script executado com sucesso! {executed} comandos processados.")
        
        # Verificar se as tabelas foram criadas
        cursor.execute("SHOW TABLES LIKE 'registros_ponto'")
        if cursor.fetchone():
            print("✅ Tabela 'registros_ponto' criada com sucesso!")
        else:
            print("⚠️ Tabela 'registros_ponto' não encontrada.")
        
        # Verificar views
        cursor.execute("SHOW FULL TABLES WHERE Table_Type = 'VIEW'")
        views = cursor.fetchall()
        print(f"📊 Views criadas: {len(views)}")
        for view in views:
            print(f"  - {view[0]}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except pymysql.Error as e:
        print(f"❌ Erro MySQL: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False

def verificar_conexao():
    """
    Testa a conexão com o banco de dados.
    """
    try:
        print(f"🔍 Testando conexão com {Config.DB_HOST}...")
        config = Config.get_database_url()
        
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"✅ MySQL versão: {version[0]}")
        
        cursor.execute("SELECT DATABASE()")
        db = cursor.fetchone()
        print(f"📊 Banco de dados atual: {db[0] if db[0] else 'Nenhum'}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na conexão: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Executando script de criação das tabelas de registros de ponto...")
    print(f"📍 Servidor: {Config.DB_HOST}")
    print(f"👤 Usuário: {Config.DB_USER}")
    print(f"🗄️ Banco: {Config.DB_NAME}")
    print("-" * 60)
    
    # Verificar conexão primeiro
    if verificar_conexao():
        print("\n" + "=" * 60)
        print("📋 Executando script SQL...")
        
        if executar_sql_registros():
            print("\n🎉 Implementação concluída com sucesso!")
            print("\n📋 Próximos passos:")
            print("1. Iniciar o servidor Flask")
            print("2. Testar as funcionalidades no navegador")
            print("3. Verificar os logs para possíveis problemas")
        else:
            print("\n❌ Falha na execução do script.")
            sys.exit(1)
    else:
        print("\n❌ Não foi possível conectar ao banco de dados.")
        print("🔧 Verifique as configurações em utils/config.py")
        sys.exit(1) 