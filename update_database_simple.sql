-- RLPONTO-WEB v2.0 - Atualização Simplificada
-- Versão corrigida para compatibilidade MySQL
-- Data: 06/01/2025

USE controle_ponto;

-- Desativar verificação de foreign keys temporariamente
SET foreign_key_checks = 0;

-- 1. <PERSON>riar novas tabelas
CREATE TABLE IF NOT EXISTS logs_biometria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    similarity_score DECIMAL(5,4) DEFAULT 0.0000,
    device_info JSON,
    status ENUM('success', 'failed') DEFAULT 'failed',
    ip_address VARCHAR(45),
    user_agent TEXT
);

CREATE TABLE IF NOT EXISTS logs_seguranca (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo_evento VARCHAR(50) NOT NULL,
    funcionario_id INT NULL,
    detalhes JSON,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    nivel_risco ENUM('baixo', 'medio', 'alto') DEFAULT 'baixo'
);

CREATE TABLE IF NOT EXISTS configuracoes_sistema (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chave VARCHAR(100) UNIQUE NOT NULL,
    valor TEXT,
    descricao TEXT,
    tipo ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    categoria VARCHAR(50) DEFAULT 'geral',
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    atualizado_em DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. Adicionar colunas (ignorar erros se já existirem)
ALTER TABLE registros_ponto ADD COLUMN biometria_verificada BOOLEAN DEFAULT FALSE;
ALTER TABLE registros_ponto ADD COLUMN device_hash VARCHAR(64);
ALTER TABLE registros_ponto ADD COLUMN security_score INT DEFAULT 100;
ALTER TABLE funcionarios ADD COLUMN status ENUM('ativo', 'inativo', 'suspenso') DEFAULT 'ativo';

-- 3. Inserir configurações padrão
INSERT IGNORE INTO configuracoes_sistema (chave, valor, descricao, tipo, categoria) VALUES
('biometric_threshold', '0.7', 'Limite mínimo similaridade biométrica', 'string', 'biometria'),
('security_enabled', 'true', 'Habilitar verificações segurança', 'boolean', 'seguranca'),
('max_failed_attempts', '5', 'Máximo tentativas falhadas por hora', 'integer', 'seguranca'),
('attendance_tolerance_minutes', '15', 'Tolerância minutos pontualidade', 'integer', 'horarios'),
('morning_start', '07:00', 'Início período matutino', 'string', 'horarios'),
('morning_end', '09:30', 'Fim período matutino', 'string', 'horarios'),
('lunch_out_start', '11:30', 'Início saída almoço', 'string', 'horarios'),
('lunch_out_end', '13:30', 'Fim saída almoço', 'string', 'horarios'),
('evening_start', '17:00', 'Início período vespertino', 'string', 'horarios'),
('evening_end', '19:00', 'Fim período vespertino', 'string', 'horarios');

-- 4. Atualizar registros existentes
UPDATE registros_ponto rp 
JOIN funcionarios f ON rp.funcionario_id = f.id 
SET rp.biometria_verificada = TRUE, rp.security_score = 100 
WHERE f.template_biometrico IS NOT NULL 
AND f.template_biometrico != '';

-- 5. Log de conclusão
INSERT INTO logs_seguranca (tipo_evento, detalhes, nivel_risco) VALUES 
('database_update', '{"version": "2.0", "status": "completed"}', 'baixo');

-- Reativar verificação de foreign keys
SET foreign_key_checks = 1;

-- Verificação final
SELECT 'Atualização RLPONTO-WEB v2.0 concluída com sucesso!' as resultado;
SELECT COUNT(*) as novas_tabelas FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name IN ('logs_biometria', 'logs_seguranca', 'configuracoes_sistema'); 