-- =====================================================
-- CORREÇÃO COMPLETA: Todos os ENUMs incompatíveis
-- Data: 13/07/2025
-- =====================================================

-- 1. Corrigir turno
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN turno ENUM('Diurno','Noturno','Misto') NOT NULL;

-- 2. Corrigir tipo_contrato
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN tipo_contrato ENUM('CLT','PJ','Estagio','Temporario') NOT NULL;

-- 3. Corrigir status_cadastro
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN status_cadastro ENUM('Ativo','Inativo') DEFAULT 'Ativo';

-- 4. Verificação final de todas as correções
SELECT 'Verificando todas as correções...' as status;

SELECT 'nivel_acesso' as campo, COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios_desligados' 
AND COLUMN_NAME = 'nivel_acesso'

UNION ALL

SELECT 'turno' as campo, COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios_desligados' 
AND COLUMN_NAME = 'turno'

UNION ALL

SELECT 'tipo_contrato' as campo, COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios_desligados' 
AND COLUMN_NAME = 'tipo_contrato'

UNION ALL

SELECT 'status_cadastro' as campo, COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios_desligados' 
AND COLUMN_NAME = 'status_cadastro';

SELECT 'Todas as correções de ENUMs aplicadas com sucesso!' as resultado;
