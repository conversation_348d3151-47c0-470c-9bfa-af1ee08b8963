#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar problema com funcionários
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.database import get_db_connection
    print("✅ Módulo database importado com sucesso")
except Exception as e:
    print(f"❌ Erro ao importar database: {e}")
    sys.exit(1)

def verificar_funcionarios():
    """Verifica os funcionários e a estrutura da tabela."""
    
    try:
        print("=" * 60)
        print("🔍 VERIFICAÇÃO DE FUNCIONÁRIOS")
        print("=" * 60)
        
        print("\n📡 Tentando conectar ao banco...")
        conn = get_db_connection()
        cursor = conn.cursor()
        print("✅ Conexão estabelecida!")
        
        # 1. Verificar se a tabela existe
        print("\n1. 🔍 Verificando se a tabela funcionários existe:")
        cursor.execute("""
            SELECT COUNT(*) as total
            FROM information_schema.tables 
            WHERE table_schema = 'controle_ponto' 
            AND table_name = 'funcionarios'
        """)
        result = cursor.fetchone()
        existe = result['total'] > 0 if result else False
        
        if existe:
            print("   ✅ Tabela funcionários existe")
        else:
            print("   ❌ Tabela funcionários NÃO existe!")
            conn.close()
            return False
        
        # 2. Contar total de funcionários
        print("\n2. 📊 Total de funcionários:")
        cursor.execute("SELECT COUNT(*) as total FROM funcionarios")
        result = cursor.fetchone()
        total = result['total'] if result else 0
        print(f"   Total geral: {total}")
        
        if total == 0:
            print("   ⚠️ Não há funcionários cadastrados!")
            conn.close()
            return False
        
        # 3. Verificar estrutura da tabela (campos principais)
        print("\n3. 📋 Campos principais da tabela:")
        cursor.execute("SHOW COLUMNS FROM funcionarios")
        columns = cursor.fetchall()
        
        status_fields = []
        important_fields = ['id', 'nome_completo', 'ativo', 'status_cadastro', 'setor', 'setor_obra']
        
        for col in columns:
            campo = col['Field']
            if campo in important_fields:
                print(f"   📋 {campo}: {col['Type']}")
                if campo in ['ativo', 'status_cadastro']:
                    status_fields.append(campo)
        
        # 4. Verificar valores nos campos de status
        print("\n4. 🔍 Verificando valores de status:")
        for field in status_fields:
            try:
                if field == 'ativo':
                    cursor.execute("SELECT COUNT(*) as count FROM funcionarios WHERE ativo = 1")
                    result = cursor.fetchone()
                    count = result['count'] if result else 0
                    print(f"   Campo '{field}': {count} funcionários ativos")
                elif field == 'status_cadastro':
                    cursor.execute("SELECT COUNT(*) as count FROM funcionarios WHERE status_cadastro = 'Ativo'")
                    result = cursor.fetchone()
                    count = result['count'] if result else 0
                    print(f"   Campo '{field}': {count} funcionários ativos")
            except Exception as e:
                print(f"   ❌ Erro no campo {field}: {e}")
        
        # 5. Listar alguns funcionários para verificar dados
        print("\n5. 👥 Listagem de funcionários:")
        cursor.execute("SELECT id, nome_completo, status_cadastro, ativo FROM funcionarios LIMIT 5")
        funcionarios = cursor.fetchall()
        for f in funcionarios:
            status_cadastro = f.get('status_cadastro', 'N/A')
            ativo = f.get('ativo', 'N/A')
            print(f"   ID: {f['id']} - Nome: {f['nome_completo']} - Status: {status_cadastro} - Ativo: {ativo}")
        
        # 6. Testar query exata da página
        print("\n6. 🧪 Testando query EXATA da página de registro manual:")
        try:
            cursor.execute("""
                SELECT 
                    f.id,
                    f.nome_completo,
                    f.cpf,
                    f.matricula_empresa,
                    f.cargo,
                    f.setor,
                    f.foto_3x4,
                    e.nome_fantasia AS empresa,
                    ht.nome_horario
                FROM funcionarios f
                LEFT JOIN empresas e ON f.empresa_id = e.id
                LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
                WHERE f.ativo = TRUE
                ORDER BY f.nome_completo
            """)
            funcionarios = cursor.fetchall()
            print(f"   ✅ Query original: {len(funcionarios)} funcionários encontrados")
            
            if len(funcionarios) == 0:
                print("   🔍 PROBLEMA IDENTIFICADO: A query original não retorna funcionários!")
                print("   💡 Possível causa: Campo 'ativo' não existe ou tem valores diferentes")
                
                # Testar query corrigida
                print("\n7. 🔧 Testando query CORRIGIDA:")
                cursor.execute("""
                    SELECT 
                        f.id,
                        f.nome_completo,
                        f.cpf,
                        f.matricula_empresa,
                        f.cargo,
                        COALESCE(f.setor, f.setor_obra, 'N/A') as setor,
                        f.foto_3x4,
                        'Empresa Padrão' AS empresa,
                        'Horário Padrão' AS nome_horario
                    FROM funcionarios f
                    WHERE f.status_cadastro = 'Ativo'
                    ORDER BY f.nome_completo
                """)
                funcionarios_corrigidos = cursor.fetchall()
                print(f"   ✅ Query corrigida: {len(funcionarios_corrigidos)} funcionários encontrados")
                
                for f in funcionarios_corrigidos[:3]:
                    print(f"      - {f['nome_completo']} (ID: {f['id']})")
                    
                # Testar query mais simples ainda
                print("\n8. 🚀 Query MAIS SIMPLES (sem JOINs):")
                cursor.execute("""
                    SELECT 
                        f.id,
                        f.nome_completo,
                        f.cpf,
                        f.matricula_empresa,
                        f.cargo,
                        COALESCE(f.setor, f.setor_obra, 'N/A') as setor,
                        f.foto_3x4
                    FROM funcionarios f
                    WHERE f.status_cadastro = 'Ativo' OR f.ativo = 1
                    ORDER BY f.nome_completo
                """)
                funcionarios_simples = cursor.fetchall()
                print(f"   ✅ Query simples: {len(funcionarios_simples)} funcionários encontrados")
                
            else:
                for f in funcionarios[:3]:
                    print(f"      - {f['nome_completo']} (ID: {f['id']})")
                    
        except Exception as e:
            print(f"   ❌ Erro na query: {e}")
        
        conn.close()
        
        print(f"\n🎯 CONCLUSÃO:")
        if total > 0:
            print(f"   ✅ {total} funcionários existem no banco")
            print(f"   🔧 O problema está na query SQL da página")
            print(f"   💡 Solução: Corrigir a condição WHERE na query")
        else:
            print(f"   ❌ Não há funcionários no banco")
        
    except Exception as e:
        print(f"❌ Erro geral: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    verificar_funcionarios() 