<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ titulo or 'Erro' }} - <PERSON><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .error-container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            margin: 20px;
            text-align: center;
        }
        
        .error-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }
        
        .error-404 .error-icon { color: #6c757d; }
        .error-403 .error-icon { color: #dc3545; }
        .error-500 .error-icon { color: #fd7e14; }
        
        .error-title {
            color: #495057;
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .error-message {
            color: #6c757d;
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .error-code {
            background: #f8f9fa;
            color: #495057;
            padding: 8px 16px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin-bottom: 30px;
            display: inline-block;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-1px);
        }
        
        .error-details {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            text-align: left;
        }
        
        .error-details h4 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .error-details p {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 8px;
            font-family: 'Courier New', monospace;
        }
        
        @media (max-width: 480px) {
            .error-container {
                margin: 10px;
                padding: 30px 20px;
            }
            
            .error-title {
                font-size: 24px;
            }
            
            .error-message {
                font-size: 16px;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 200px;
            }
        }
        
        .system-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="error-container error-{{ codigo or '500' }}">
        <div class="error-icon">
            {% if codigo == 404 %}
                🔍
            {% elif codigo == 403 %}
                🚫
            {% elif codigo == 500 %}
                ⚠️
            {% else %}
                ❌
            {% endif %}
        </div>
        
        <h1 class="error-title">{{ titulo or 'Erro no Sistema' }}</h1>
        
        <p class="error-message">{{ mensagem or 'Ocorreu um erro inesperado.' }}</p>
        
        {% if codigo %}
        <div class="error-code">Código: {{ codigo }}</div>
        {% endif %}
        
        <div class="action-buttons">
            <a href="/" class="btn btn-primary">🏠 Voltar ao Início</a>
            <button onclick="history.back()" class="btn btn-secondary">⬅️ Página Anterior</button>
        </div>
        
        {% if error_id %}
        <div class="error-details">
            <h4>📋 Informações Técnicas</h4>
            <p><strong>ID do Erro:</strong> {{ error_id }}</p>
            <p><strong>Timestamp:</strong> {{ timestamp or 'N/A' }}</p>
            {% if url %}
            <p><strong>URL:</strong> {{ url }}</p>
            {% endif %}
        </div>
        {% endif %}
        
        <div class="system-info">
            Sistema de Controle de Ponto v1.0<br>
            Se o problema persistir, contate o administrador do sistema
        </div>
    </div>
    
    <script>
        // Auto-reload em modo debug para desenvolvimento
        {% if config and config.FLASK_DEBUG %}
        console.log('🔧 Modo debug ativo');
        {% endif %}
        
        // Log do erro no console para debugging
        {% if codigo %}
        console.error('Erro na aplicação - Código:', '{{ codigo }}');
        {% endif %}
        {% if titulo %}
        console.error('Título:', '{{ titulo }}');
        {% endif %}
        {% if mensagem %}
        console.error('Mensagem:', '{{ mensagem }}');
        {% endif %}
        console.error('URL:', window.location.href);
    </script>
</body>
</html> 