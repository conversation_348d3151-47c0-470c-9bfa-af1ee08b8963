#!/usr/bin/env python3
"""
Debug específico para encontrar o funcionário Richardson
Investigar por que ele não aparece na lista de alocação
"""

import sys
import os
sys.path.append('var/www/controle-ponto')

from utils.database import DatabaseManager
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_richardson():
    """Debug específico do funcionário Richardson"""
    print("🔍 DEBUG: FUNCIONÁRIO RICHARDSON")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Buscar Richardson na tabela funcionários
        print("\n1. 👤 BUSCAR RICHARDSON NA TABELA FUNCIONÁRIOS:")
        print("-" * 50)
        
        funcionarios_richardson = db.execute_query("""
            SELECT id, nome_completo, cpf, cargo, setor, empresa_id, 
                   status_cadastro, ativo, jornada_trabalho_id
            FROM funcionarios 
            WHERE nome_completo LIKE '%RICHARDSON%' OR nome_completo LIKE '%richardson%'
        """)
        
        if funcionarios_richardson:
            for func in funcionarios_richardson:
                print(f"✅ Encontrado: {func['nome_completo']} (ID: {func['id']})")
                print(f"   📊 Status: {func['status_cadastro']}")
                print(f"   🏢 Empresa ID: {func['empresa_id']}")
                print(f"   ⚡ Ativo: {func['ativo']}")
                print(f"   📅 Jornada ID: {func['jornada_trabalho_id']}")
                richardson_id = func['id']
                richardson_empresa_id = func['empresa_id']
        else:
            print("❌ Richardson não encontrado na tabela funcionários")
            return False
        
        # 2. Verificar empresa principal
        print(f"\n2. 🏢 VERIFICAR EMPRESA PRINCIPAL:")
        print("-" * 50)
        
        empresa_principal = db.execute_query("""
            SELECT id, razao_social, nome_fantasia, empresa_principal
            FROM empresas 
            WHERE empresa_principal = 1
        """)
        
        if empresa_principal:
            emp_principal = empresa_principal[0]
            print(f"✅ Empresa Principal: {emp_principal['razao_social']} (ID: {emp_principal['id']})")
            
            if richardson_empresa_id == emp_principal['id']:
                print("✅ Richardson pertence à empresa principal")
            else:
                print(f"❌ Richardson pertence à empresa {richardson_empresa_id}, não à principal {emp_principal['id']}")
        else:
            print("❌ Nenhuma empresa principal encontrada")
            return False
        
        # 3. Verificar alocações ativas do Richardson
        print(f"\n3. 🎯 VERIFICAR ALOCAÇÕES ATIVAS DO RICHARDSON:")
        print("-" * 50)
        
        alocacoes_richardson = db.execute_query("""
            SELECT fa.*, e.razao_social as cliente_nome
            FROM funcionario_alocacoes fa
            LEFT JOIN empresas e ON fa.empresa_cliente_id = e.id
            WHERE fa.funcionario_id = %s
            ORDER BY fa.created_at DESC
        """, (richardson_id,))
        
        if alocacoes_richardson:
            print(f"📋 {len(alocacoes_richardson)} alocações encontradas:")
            for aloc in alocacoes_richardson:
                status = "ATIVA" if aloc['ativo'] else "INATIVA"
                print(f"   - Cliente: {aloc['cliente_nome']} | Status: {status} | Data: {aloc['data_inicio']}")
                if aloc['ativo']:
                    print(f"     ⚠️ ALOCAÇÃO ATIVA ENCONTRADA! Isso explica por que não aparece na lista")
        else:
            print("✅ Nenhuma alocação encontrada - deveria aparecer na lista")
        
        # 4. Testar query da função get_funcionarios_disponiveis
        print(f"\n4. 🧪 TESTAR QUERY get_funcionarios_disponiveis:")
        print("-" * 50)
        
        funcionarios_disponiveis = db.execute_query("""
            SELECT f.id, f.nome_completo, f.cpf, f.cargo, f.setor, f.status_cadastro,
                   f.data_admissao, f.empresa_id,
                   CASE WHEN fa.id IS NOT NULL THEN TRUE ELSE FALSE END as ja_alocado,
                   fa.empresa_cliente_id as cliente_atual_id,
                   e.razao_social as cliente_atual_nome
            FROM funcionarios f
            LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id AND fa.ativo = TRUE
            LEFT JOIN empresas e ON fa.empresa_cliente_id = e.id
            WHERE f.empresa_id = %s AND f.status_cadastro = 'Ativo'
            ORDER BY f.nome_completo
        """, (emp_principal['id'],))
        
        print(f"📊 Funcionários retornados pela query: {len(funcionarios_disponiveis)}")
        
        richardson_encontrado = False
        for func in funcionarios_disponiveis:
            if 'RICHARDSON' in func['nome_completo'].upper():
                richardson_encontrado = True
                print(f"✅ Richardson encontrado na query:")
                print(f"   📛 Nome: {func['nome_completo']}")
                print(f"   🎯 Já alocado: {func['ja_alocado']}")
                print(f"   🏢 Cliente atual: {func['cliente_atual_nome'] or 'N/A'}")
                break
        
        if not richardson_encontrado:
            print("❌ Richardson NÃO encontrado na query get_funcionarios_disponiveis")
        
        # 5. Verificar se há filtro adicional no JavaScript
        print(f"\n5. 💡 POSSÍVEIS CAUSAS:")
        print("-" * 50)
        
        if alocacoes_richardson and any(aloc['ativo'] for aloc in alocacoes_richardson):
            print("🎯 CAUSA PROVÁVEL: Richardson tem alocação ativa")
            print("   - Funcionários já alocados podem estar sendo filtrados")
            print("   - Verificar se há lógica no frontend que remove funcionários alocados")
        elif not richardson_encontrado:
            print("🎯 CAUSA PROVÁVEL: Richardson não atende aos critérios da query")
            print("   - Verificar empresa_id, status_cadastro, etc.")
        else:
            print("🎯 CAUSA PROVÁVEL: Problema no frontend/JavaScript")
            print("   - Richardson está na query mas não aparece no modal")
        
        # 6. Listar todos os funcionários disponíveis
        print(f"\n6. 📋 TODOS OS FUNCIONÁRIOS DISPONÍVEIS:")
        print("-" * 50)
        
        for func in funcionarios_disponiveis:
            status_aloc = "ALOCADO" if func['ja_alocado'] else "LIVRE"
            print(f"   - {func['nome_completo']} | {func['cargo']} | {status_aloc}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    sucesso = debug_richardson()
    if sucesso:
        print("\n🎉 DEBUG CONCLUÍDO")
        sys.exit(0)
    else:
        print("\n💥 ERRO NO DEBUG")
        sys.exit(1)
