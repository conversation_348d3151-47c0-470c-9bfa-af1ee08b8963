#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TESTE DIRETO PARA VERIFICAR O ESTADO ATUAL
==========================================
"""

import requests
import os

def teste_final():
    """Teste direto para ver exatamente o que está acontecendo"""
    
    print("🔍 VERIFICANDO ARQUIVOS LOCAIS...")
    
    # Verificar se o arquivo foi realmente modificado
    config_file = "templates/configuracoes/index.html"
    
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✅ Arquivo existe: {config_file}")
        print(f"📏 Tamanho: {len(content)} caracteres")
        
        # Verificar conteúdo específico
        checks = [
            ("🔥 BIOMETRIA ATIVA", "🔥 BIOMETRIA ATIVA" in content),
            ("Configurar Biometria", "Configurar Biometria" in content),
            ("TOTALMENTE FUNCIONAL", "TOTALMENTE FUNCIONAL" in content),
            ("biometric-highlight", "biometric-highlight" in content),
            ("Sistema Online", "Sistema Online" in content)
        ]
        
        print("\n📋 VERIFICAÇÕES LOCAIS:")
        all_ok = True
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"{status} {check_name}")
            if not result:
                all_ok = False
        
        if all_ok:
            print("\n🎉 ARQUIVO LOCAL ESTÁ CORRETO!")
        else:
            print("\n⚠️ Arquivo local ainda tem problemas")
            print("\n📄 PRIMEIROS 500 CHARS DO ARQUIVO:")
            print("-" * 50)
            print(content[:500])
            print("-" * 50)
    else:
        print(f"❌ Arquivo não encontrado: {config_file}")
    
    print("\n🌐 TESTANDO SERVIDOR WEB...")
    
    # Testar servidor
    base_url = "http://10.19.208.31:5000"
    session = requests.Session()
    
    # Login
    login_data = {
        'usuario': 'admin',
        'senha': '@Ric6109'
    }
    
    try:
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code in [200, 302]:
            print("✅ Login OK")
            
            # Obter página
            config_response = session.get(f"{base_url}/configuracoes")
            
            if config_response.status_code == 200:
                server_content = config_response.text
                
                print(f"✅ Página carregada (Tamanho: {len(server_content)})")
                
                # Verificar se o servidor está retornando o novo conteúdo
                server_checks = [
                    ("🔥 BIOMETRIA ATIVA", "🔥 BIOMETRIA ATIVA" in server_content),
                    ("TOTALMENTE FUNCIONAL", "TOTALMENTE FUNCIONAL" in server_content),
                    ("biometric-highlight", "biometric-highlight" in server_content),
                    ("Sistema Online", "Sistema Online" in server_content)
                ]
                
                print("\n📋 VERIFICAÇÕES SERVIDOR:")
                server_ok = True
                for check_name, result in server_checks:
                    status = "✅" if result else "❌"
                    print(f"{status} {check_name}")
                    if not result:
                        server_ok = False
                
                if not server_ok:
                    print("\n⚠️ SERVIDOR AINDA RETORNA CONTEÚDO ANTIGO!")
                    print("🔄 Isso pode ser cache do Flask/servidor")
                    print("\n📄 PRIMEIROS 500 CHARS DO SERVIDOR:")
                    print("-" * 50)
                    print(server_content[:500])
                    print("-" * 50)
                else:
                    print("\n🎉 SERVIDOR RETORNA CONTEÚDO CORRETO!")
                    
            else:
                print(f"❌ Erro ao carregar página: {config_response.status_code}")
        else:
            print(f"❌ Erro no login: {login_response.status_code}")
            
    except Exception as e:
        print(f"❌ Erro de conexão: {e}")

if __name__ == "__main__":
    teste_final() 