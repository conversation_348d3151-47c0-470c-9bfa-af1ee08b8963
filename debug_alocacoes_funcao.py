#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'var', 'www', 'controle-ponto'))

from utils.database import DatabaseManager
import traceback

def debug_funcao_alocacoes():
    """Debug da função alocacoes() passo a passo"""
    print("🔍 DEBUG DA FUNÇÃO ALOCACOES()")
    print("=" * 50)
    
    try:
        # Simular o que a função alocacoes() faz
        
        # 1. Verificar empresa principal
        print("\n1. Verificando empresa principal...")
        try:
            db = DatabaseManager()
            sql_empresa = "SELECT * FROM empresas WHERE empresa_principal = TRUE LIMIT 1"
            empresa_principal = db.execute_query(sql_empresa)
            
            if empresa_principal:
                print(f"   ✅ Empresa principal encontrada: {empresa_principal[0]['razao_social']}")
                empresa_principal = empresa_principal[0]
            else:
                print("   ❌ Empresa principal não encontrada")
                return False
                
        except Exception as e:
            print(f"   ❌ Erro ao buscar empresa principal: {e}")
            return False
        
        # 2. Buscar todas as alocações
        print("\n2. Buscando todas as alocações...")
        try:
            sql_alocacoes = """
            SELECT fa.*,
                   f.nome_completo as nome, f.cargo, f.cpf, f.telefone1 as telefone,
                   e.razao_social, e.nome_fantasia, e.cnpj,
                   jt.nome_jornada as jornada_nome, NULL as carga_horaria,
                   ec.nome_contrato, ec.codigo_contrato
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            JOIN empresas e ON fa.empresa_cliente_id = e.id
            JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
            LEFT JOIN empresa_clientes ec ON fa.contrato_id = ec.id
            ORDER BY fa.created_at DESC
            """
            
            alocacoes = db.execute_query(sql_alocacoes)
            print(f"   ✅ Alocações encontradas: {len(alocacoes) if alocacoes else 0}")
            
        except Exception as e:
            print(f"   ❌ Erro ao buscar alocações: {e}")
            print(f"   📋 Traceback: {traceback.format_exc()}")
            return False
        
        # 3. Buscar clientes
        print("\n3. Buscando clientes da empresa principal...")
        try:
            sql_clientes = """
            SELECT ec.*, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email,
                   0 as funcionarios_alocados,
                   0 as jornadas_disponiveis
            FROM empresa_clientes ec
            INNER JOIN empresas e ON ec.empresa_cliente_id = e.id
            INNER JOIN empresas ep ON ec.empresa_principal_id = ep.id
            WHERE ep.empresa_principal = TRUE AND ec.ativo = TRUE
            ORDER BY ec.data_inicio DESC
            """
            
            clientes = db.execute_query(sql_clientes)
            print(f"   ✅ Clientes encontrados: {len(clientes) if clientes else 0}")
            
        except Exception as e:
            print(f"   ❌ Erro ao buscar clientes: {e}")
            print(f"   📋 Traceback: {traceback.format_exc()}")
            return False
        
        # 4. Calcular estatísticas
        print("\n4. Calculando estatísticas...")
        try:
            stats = {
                'total_alocacoes': len(alocacoes) if alocacoes else 0,
                'alocacoes_ativas': len([a for a in alocacoes if a['ativo']]) if alocacoes else 0,
                'funcionarios_unicos': len(set([a['funcionario_id'] for a in alocacoes])) if alocacoes else 0
            }
            print(f"   ✅ Estatísticas calculadas: {stats}")
            
        except Exception as e:
            print(f"   ❌ Erro ao calcular estatísticas: {e}")
            print(f"   📋 Traceback: {traceback.format_exc()}")
            return False
        
        # 5. Preparar contexto
        print("\n5. Preparando contexto...")
        try:
            context = {
                'titulo': 'Alocação de Funcionários',
                'empresa_principal': empresa_principal,
                'alocacoes': alocacoes,
                'clientes': clientes,
                'stats': stats
            }
            print(f"   ✅ Contexto preparado com sucesso")
            print(f"   📊 Dados do contexto:")
            print(f"      - Título: {context['titulo']}")
            print(f"      - Empresa principal: {context['empresa_principal']['razao_social']}")
            print(f"      - Alocações: {len(context['alocacoes'])}")
            print(f"      - Clientes: {len(context['clientes'])}")
            print(f"      - Stats: {context['stats']}")
            
        except Exception as e:
            print(f"   ❌ Erro ao preparar contexto: {e}")
            print(f"   📋 Traceback: {traceback.format_exc()}")
            return False
        
        print(f"\n✅ TODAS AS ETAPAS FUNCIONARAM CORRETAMENTE!")
        print(f"   O problema deve estar no template ou na renderização.")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    debug_funcao_alocacoes()
