-- =====================================================
-- RESTAURAÇÃO URGENTE - FUNCIONÁRIO EXCLUÍDO POR ERRO
-- Data: 13/07/2025
-- ERRO CRÍTICO: Funcionário real foi excluído durante teste
-- =====================================================

-- 1. Verificar funcionário na tabela de desligados
SELECT * FROM funcionarios_desligados 
WHERE nome_completo = 'SUELEN OLIVEIRA DOS SANTOS' 
ORDER BY data_desligamento DESC LIMIT 1;

-- 2. Restaurar funcionário para tabela ativa
INSERT INTO funcionarios (
    nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
    ctps_numero, ctps_serie_uf, pis_pasep,
    endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
    telefone1, telefone2, email, cargo, setor, setor_obra, matricula_empresa,
    data_admissao, tipo_contrato, nivel_acesso, turno, tolerancia_ponto,
    banco_horas, hora_extra, status_cadastro, horas_trabalho_obrigatorias,
    empresa_id, jornada_trabalho_id, horario_trabalho_id,
    digital_dedo1, digital_dedo2, foto_3x4
)
SELECT 
    nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
    ctps_numero, ctps_serie_uf, pis_pasep,
    endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
    telefone1, telefone2, email, cargo, setor, setor_obra, matricula_empresa,
    data_admissao, tipo_contrato, nivel_acesso, turno, tolerancia_ponto,
    banco_horas, hora_extra, 'Ativo', horas_trabalho_obrigatorias,
    empresa_id, jornada_trabalho_id, horario_trabalho_id,
    digital_dedo1, digital_dedo2, foto_3x4
FROM funcionarios_desligados 
WHERE nome_completo = 'SUELEN OLIVEIRA DOS SANTOS' 
AND data_desligamento >= '2025-07-13 18:00:00';

-- 3. Remover da tabela de desligados (foi erro)
DELETE FROM funcionarios_desligados 
WHERE nome_completo = 'SUELEN OLIVEIRA DOS SANTOS' 
AND data_desligamento >= '2025-07-13 18:00:00';

-- 4. Remover log de desligamento (foi erro)
DELETE FROM log_desligamentos 
WHERE nome_funcionario = 'SUELEN OLIVEIRA DOS SANTOS' 
AND data_desligamento >= '2025-07-13 18:00:00';

-- 5. Verificar restauração
SELECT id, nome_completo, matricula_empresa, status_cadastro 
FROM funcionarios 
WHERE nome_completo = 'SUELEN OLIVEIRA DOS SANTOS';

SELECT 'FUNCIONÁRIO RESTAURADO COM SUCESSO!' as resultado;
