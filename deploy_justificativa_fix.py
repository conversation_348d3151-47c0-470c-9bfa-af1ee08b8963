#!/usr/bin/env python3
"""
Deploy da correção de justificativas baseadas em documentos anexados
"""

import subprocess
import sys
import os
from datetime import datetime

def log(message, level="INFO"):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def run_command(command, description):
    """Executa comando e retorna resultado"""
    log(f"Executando: {description}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            log(f"✅ {description} - Sucesso")
            return True, result.stdout
        else:
            log(f"❌ {description} - Erro: {result.stderr}", "ERROR")
            return False, result.stderr
    except subprocess.TimeoutExpired:
        log(f"⏰ {description} - Timeout", "ERROR")
        return False, "Timeout"
    except Exception as e:
        log(f"❌ {description} - Exceção: {e}", "ERROR")
        return False, str(e)

def main():
    log("=== DEPLOY CORREÇÃO JUSTIFICATIVAS DOCUMENTOS ===")
    
    # Arquivo a ser enviado
    arquivo_local = "var/www/controle-ponto/app_ponto_admin.py"
    arquivo_remoto = "/var/www/controle-ponto/app_ponto_admin.py"
    servidor = "************"
    
    # Verificar se arquivo existe
    if not os.path.exists(arquivo_local):
        log(f"❌ Arquivo não encontrado: {arquivo_local}", "ERROR")
        return False
    
    log(f"✅ Arquivo encontrado: {arquivo_local}")
    
    # 1. Fazer backup no servidor
    backup_cmd = f'ssh root@{servidor} "cd /var/www/controle-ponto && cp app_ponto_admin.py app_ponto_admin_backup_justificativa_$(date +%Y%m%d_%H%M%S).py"'
    success, output = run_command(backup_cmd, "Criando backup no servidor")
    
    if not success:
        log("❌ Falha ao criar backup", "ERROR")
        return False
    
    # 2. Enviar arquivo corrigido
    scp_cmd = f'scp "{arquivo_local}" root@{servidor}:{arquivo_remoto}'
    success, output = run_command(scp_cmd, "Enviando arquivo corrigido")
    
    if not success:
        log("❌ Falha ao enviar arquivo", "ERROR")
        return False
    
    # 3. Reiniciar serviço
    restart_cmd = f'ssh root@{servidor} "systemctl restart rlponto-web && sleep 3 && systemctl status rlponto-web --no-pager"'
    success, output = run_command(restart_cmd, "Reiniciando serviço")
    
    if success:
        log("✅ Deploy concluído com sucesso!")
        log("🔍 Teste a funcionalidade acessando a página de detalhes do funcionário")
        return True
    else:
        log("❌ Falha ao reiniciar serviço", "ERROR")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
