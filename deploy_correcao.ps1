# Script de deploy para correção da exclusão de empresas
# Data: 03/07/2025
# Autor: <PERSON>

# Configurações
$SERVER_IP = "************"
$SERVER_USER = "root"
$TARGET_DIR = "/var/www/controle-ponto"
$BACKUP_DIR = "$TARGET_DIR/backup-build"
$TIMESTAMP = Get-Date -Format "yyyyMMdd_HHmmss"

# Arquivos a serem implantados (caminho local -> caminho remoto)
$FILES_TO_DEPLOY = @(
    @{local = "app_configuracoes.py"; remote = "app_configuracoes.py"},
    @{local = "static/js/configuracoes_error_handler.js"; remote = "static/js/configuracoes_error_handler.js"}
)

# Arquivos de template (caminho local -> caminho remoto)
$TEMPLATE_FILES = @(
    @{local = "var/www/controle-ponto/templates/configuracoes/empresas.html"; remote = "templates/configuracoes/empresas.html"}
)

# Função para exibir mensagens coloridas
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# Exibir informações iniciais
Write-ColorOutput Green "==================================================="
Write-ColorOutput Green "   DEPLOY DE CORREÇÃO - EXCLUSÃO DE EMPRESAS"
Write-ColorOutput Green "   Data: $(Get-Date -Format 'dd/MM/yyyy HH:mm:ss')"
Write-ColorOutput Green "==================================================="
Write-ColorOutput Yellow "Servidor: $SERVER_IP"
Write-ColorOutput Yellow "Diretório: $TARGET_DIR"
Write-ColorOutput Yellow "Backup: $BACKUP_DIR/$TIMESTAMP"
Write-ColorOutput Green "==================================================="

# Verificar se os arquivos existem localmente
Write-ColorOutput Cyan "`nVerificando arquivos locais..."
foreach ($file in $FILES_TO_DEPLOY) {
    if (Test-Path $file.local) {
        Write-ColorOutput Green "✓ $($file.local)"
    }
    else {
        Write-ColorOutput Red "✗ $($file.local) não encontrado!"
        exit 1
    }
}

foreach ($file in $TEMPLATE_FILES) {
    if (Test-Path $file.local) {
        Write-ColorOutput Green "✓ $($file.local)"
    }
    else {
        Write-ColorOutput Red "✗ $($file.local) não encontrado!"
        exit 1
    }
}

# Criar diretório de backup no servidor
Write-ColorOutput Cyan "`nCriando diretório de backup no servidor..."
$backupDirCommand = "mkdir -p $BACKUP_DIR/$TIMESTAMP"
ssh $SERVER_USER@$SERVER_IP $backupDirCommand
if ($LASTEXITCODE -ne 0) {
    Write-ColorOutput Red "Erro ao criar diretório de backup!"
    exit 1
}

# Fazer backup dos arquivos no servidor
Write-ColorOutput Cyan "`nCriando backup dos arquivos no servidor..."
foreach ($file in $FILES_TO_DEPLOY) {
    $backupCommand = "if [ -f $TARGET_DIR/$($file.remote) ]; then cp $TARGET_DIR/$($file.remote) $BACKUP_DIR/$TIMESTAMP/$(Split-Path -Leaf $file.remote); fi"
    ssh $SERVER_USER@$SERVER_IP $backupCommand
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput Green "✓ Backup de $($file.remote)"
    }
    else {
        Write-ColorOutput Yellow "! Não foi possível fazer backup de $($file.remote) (pode não existir)"
    }
}

foreach ($file in $TEMPLATE_FILES) {
    $backupCommand = "if [ -f $TARGET_DIR/$($file.remote) ]; then cp $TARGET_DIR/$($file.remote) $BACKUP_DIR/$TIMESTAMP/$(Split-Path -Leaf $file.remote); fi"
    ssh $SERVER_USER@$SERVER_IP $backupCommand
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput Green "✓ Backup de $($file.remote)"
    }
    else {
        Write-ColorOutput Yellow "! Não foi possível fazer backup de $($file.remote) (pode não existir)"
    }
}

# Copiar arquivos para o servidor
Write-ColorOutput Cyan "`nCopiando arquivos para o servidor..."
foreach ($file in $FILES_TO_DEPLOY) {
    $targetDir = Split-Path -Parent "$TARGET_DIR/$($file.remote)"
    $mkdirCommand = "mkdir -p $targetDir"
    ssh $SERVER_USER@$SERVER_IP $mkdirCommand

    scp $file.local "$SERVER_USER@$SERVER_IP`:$TARGET_DIR/$($file.remote)"
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput Green "✓ $($file.local) copiado para $($file.remote)"
    }
    else {
        Write-ColorOutput Red "✗ Erro ao copiar $($file.local)!"
        exit 1
    }
}

# Copiar templates para o servidor
Write-ColorOutput Cyan "`nCopiando templates para o servidor..."
foreach ($file in $TEMPLATE_FILES) {
    $targetDir = Split-Path -Parent "$TARGET_DIR/$($file.remote)"
    $mkdirCommand = "mkdir -p $targetDir"
    ssh $SERVER_USER@$SERVER_IP $mkdirCommand

    scp $file.local "$SERVER_USER@$SERVER_IP`:$TARGET_DIR/$($file.remote)"
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput Green "✓ $($file.local) copiado para $($file.remote)"
    }
    else {
        Write-ColorOutput Red "✗ Erro ao copiar $($file.local)!"
        exit 1
    }
}

# Ajustar permissões
Write-ColorOutput Cyan "`nAjustando permissões..."
$chownCommand = "chown -R www-data:www-data $TARGET_DIR"
ssh $SERVER_USER@$SERVER_IP $chownCommand
if ($LASTEXITCODE -eq 0) {
    Write-ColorOutput Green "✓ Permissões ajustadas"
}
else {
    Write-ColorOutput Red "✗ Erro ao ajustar permissões!"
}

# Reiniciar serviço
Write-ColorOutput Cyan "`nReiniciando serviço..."
$restartCommand = "systemctl restart controle-ponto"
ssh $SERVER_USER@$SERVER_IP $restartCommand
if ($LASTEXITCODE -eq 0) {
    Write-ColorOutput Green "✓ Serviço reiniciado"
}
else {
    Write-ColorOutput Red "✗ Erro ao reiniciar serviço!"
}

# Verificar status do serviço
Write-ColorOutput Cyan "`nVerificando status do serviço..."
$statusCommand = "systemctl status controle-ponto | grep 'Active:'"
$status = ssh $SERVER_USER@$SERVER_IP $statusCommand
if ($LASTEXITCODE -eq 0) {
    if ($status -match "active \(running\)") {
        Write-ColorOutput Green "✓ Serviço está ativo e em execução"
    }
    else {
        Write-ColorOutput Red "✗ Serviço não está em execução!"
    }
}
else {
    Write-ColorOutput Red "✗ Erro ao verificar status do serviço!"
}

# Exibir informações finais
Write-ColorOutput Green "`n==================================================="
Write-ColorOutput Green "   DEPLOY CONCLUÍDO"
Write-ColorOutput Green "   $(Get-Date -Format 'dd/MM/yyyy HH:mm:ss')"
Write-ColorOutput Green "==================================================="
Write-ColorOutput Yellow "Acesse o sistema em: http://$SERVER_IP"
Write-ColorOutput Yellow "Login: admin / @Ric6109"
Write-ColorOutput Green "==================================================="