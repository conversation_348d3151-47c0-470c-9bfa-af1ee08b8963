<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Relatórios - RLPONTO-WEB</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #218838;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .log-area {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        .filters {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Debug Relatórios - RLPONTO-WEB</h1>
        <p><strong>Objetivo:</strong> Testar e depurar o botão de filtros que está causando erro 500</p>
        
        <div class="filters">
            <h3>📋 Filtros de Teste</h3>
            <form id="filtrosForm">
                <div class="form-group">
                    <label for="data_inicio">Data Início:</label>
                    <input type="date" id="data_inicio" name="data_inicio" value="2025-06-01">
                </div>
                <div class="form-group">
                    <label for="data_fim">Data Fim:</label>
                    <input type="date" id="data_fim" name="data_fim" value="2025-06-06">
                </div>
                <div class="form-group">
                    <label for="funcionario_id">Funcionário:</label>
                    <select id="funcionario_id" name="funcionario_id">
                        <option value="">Todos os funcionários</option>
                        <option value="1">Funcionário 1</option>
                        <option value="2">Funcionário 2</option>
                        <option value="3">Funcionário 3</option>
                    </select>
                </div>
            </form>
        </div>
        
        <div>
            <button class="btn" onclick="testarAPI()">🔍 Testar API (Como o botão verde)</button>
            <button class="btn btn-danger" onclick="limparLogs()">🗑️ Limpar Logs</button>
        </div>
        
        <div class="log-area" id="logArea">
            <div>🐛 DEBUG - RLPONTO-WEB - Logs aparecerão aqui...</div>
            <div>⏰ Aguardando teste da API...</div>
        </div>
    </div>

    <script>
        function log(message, type = 'INFO') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString('pt-BR');
            const emoji = type === 'ERROR' ? '❌' : type === 'SUCCESS' ? '✅' : type === 'WARNING' ? '⚠️' : '📝';
            const color = type === 'ERROR' ? '#ff6b6b' : type === 'SUCCESS' ? '#51cf66' : type === 'WARNING' ? '#ffd43b' : '#00ff00';
            
            const logEntry = document.createElement('div');
            logEntry.style.color = color;
            logEntry.innerHTML = `[${timestamp}] ${emoji} ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        function limparLogs() {
            const logArea = document.getElementById('logArea');
            logArea.innerHTML = '<div>🗑️ Logs limpos. Aguardando novo teste...</div>';
        }

        async function testarAPI() {
            log('🎯 INICIANDO TESTE DA API DE RELATÓRIOS');
            log('📤 Coletando dados do formulário...');
            
            // Obter dados do formulário
            const form = document.getElementById('filtrosForm');
            const formData = new FormData(form);
            
            // Converter para objeto
            const filtros = {
                funcionario_id: formData.get('funcionario_id') ? parseInt(formData.get('funcionario_id')) : null,
                setor: null,
                tipo_registro: null,
                metodo_registro: null,
                data_inicio: formData.get('data_inicio') || null,
                data_fim: formData.get('data_fim') || null,
                pagina: 1,
                registros_por_pagina: 10
            };

            log(`📋 Filtros preparados: ${JSON.stringify(filtros, null, 2)}`);
            
            try {
                log('🔗 Enviando requisição para /relatorios/api/buscar-registros');
                log('⏳ Aguardando resposta do servidor...');
                
                const response = await fetch('/relatorios/api/buscar-registros', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(filtros)
                });

                log(`📥 Resposta recebida - Status: ${response.status}`);

                if (!response.ok) {
                    const errorData = await response.json();
                    log(`❌ ERRO ${response.status}: ${errorData.message}`, 'ERROR');
                    log(`🆔 Error ID: ${errorData.error_id}`, 'ERROR');
                    
                    if (errorData.debug_info) {
                        log(`🔍 Debug Info: ${JSON.stringify(errorData.debug_info, null, 2)}`, 'WARNING');
                    }
                    
                    if (errorData.details) {
                        log(`📄 Detalhes: ${errorData.details}`, 'ERROR');
                    }
                    
                    throw new Error(`${errorData.message} (ID: ${errorData.error_id})`);
                }

                const data = await response.json();
                
                if (!data.success) {
                    log(`❌ API retornou sucesso = false: ${data.message}`, 'ERROR');
                    throw new Error(data.message);
                }

                // Log de sucesso
                log('🎉 API FUNCIONOU PERFEITAMENTE!', 'SUCCESS');
                log(`📊 Registros encontrados: ${data.total_registros}`, 'SUCCESS');
                log(`📄 Registros na página: ${data.registros.length}`, 'SUCCESS');
                log(`📑 Total de páginas: ${data.total_paginas}`, 'SUCCESS');
                
                if (data.debug_info) {
                    log(`🔍 Error ID: ${data.debug_info.error_id}`, 'INFO');
                    log(`🔧 Filtros aplicados: ${data.debug_info.filtros_aplicados}`, 'INFO');
                }
                
                if (data.estatisticas) {
                    log(`📈 Estatísticas:`, 'INFO');
                    log(`   - Total registros: ${data.estatisticas.total_registros}`, 'INFO');
                    log(`   - Funcionários distintos: ${data.estatisticas.funcionarios_distintos}`, 'INFO');
                    log(`   - Registros biométricos: ${data.estatisticas.registros_biometricos}`, 'INFO');
                    log(`   - Registros manuais: ${data.estatisticas.registros_manuais}`, 'INFO');
                }
                
                // Mostrar alguns registros de exemplo
                if (data.registros && data.registros.length > 0) {
                    log('📝 Exemplo de registro:', 'INFO');
                    const exemplo = data.registros[0];
                    log(`   - Nome: ${exemplo.nome_completo}`, 'INFO');
                    log(`   - Data/Hora: ${exemplo.data_hora}`, 'INFO');
                    log(`   - Tipo: ${exemplo.tipo_descricao}`, 'INFO');
                    log(`   - Método: ${exemplo.metodo_descricao}`, 'INFO');
                }

            } catch (error) {
                log(`💥 ERRO CRÍTICO: ${error.message}`, 'ERROR');
                log('🚨 O botão verde NÃO está funcionando!', 'ERROR');
            }
        }

        // Log inicial
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Página de debug carregada');
            log('💡 Clique no botão verde para simular o erro 500');
            log('📋 Esta página replica exatamente o que o botão original faz');
        });
    </script>
</body>
</html> 