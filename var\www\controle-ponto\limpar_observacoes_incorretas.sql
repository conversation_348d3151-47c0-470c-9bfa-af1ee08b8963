-- Script para limpar observações incorretas na tabela registros_ponto
-- Sistema: RLPONTO-WEB v1.0
-- Data: 11/07/2025

USE controle_ponto;

-- 1. Verificar quantos registros têm observações = "2"
SELECT 
    'Registros com observações = "2"' as descricao,
    COUNT(*) as quantidade
FROM registros_ponto 
WHERE observacoes = '2';

-- 2. Verificar todos os valores únicos de observações
SELECT 
    observacoes,
    COUNT(*) as quantidade
FROM registros_ponto 
WHERE observacoes IS NOT NULL
GROUP BY observacoes
ORDER BY quantidade DESC;

-- 3. Backup dos registros antes da limpeza
CREATE TABLE IF NOT EXISTS backup_observacoes_20250711 AS
SELECT 
    id,
    funcionario_id,
    observacoes,
    data_hora,
    tipo_registro
FROM registros_ponto 
WHERE observacoes IS NOT NULL;

-- 4. <PERSON>par observações que são apenas "2" (valor incorreto)
UPDATE registros_ponto 
SET observacoes = NULL 
WHERE observacoes = '2';

-- 5. Verificar resultado da limpeza
SELECT 
    'Registros após limpeza' as descricao,
    COUNT(CASE WHEN observacoes IS NOT NULL THEN 1 END) as com_observacoes,
    COUNT(CASE WHEN observacoes IS NULL THEN 1 END) as sem_observacoes,
    COUNT(*) as total
FROM registros_ponto;

-- 6. Verificar se ainda há observações válidas
SELECT 
    observacoes,
    COUNT(*) as quantidade
FROM registros_ponto 
WHERE observacoes IS NOT NULL
GROUP BY observacoes
ORDER BY quantidade DESC;
