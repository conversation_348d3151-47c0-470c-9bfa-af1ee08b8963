#!/usr/bin/env python3
"""
Debug: Sistema de Herança de Jornadas
Investigar por que funcionários não estão herdando jornadas da empresa
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
from sistema_heranca_jornadas import SistemaHerancaJornadas
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def investigar_empresa_4():
    """Investigar especificamente a empresa MSV Engenharia/Renovar (ID 4)"""
    
    print("🔍 INVESTIGANDO EMPRESA MSV ENGENHARIA/RENOVAR (ID 4)")
    print("=" * 60)
    
    db = DatabaseManager()
    
    # 1. Verificar dados da empresa
    print("\n📋 1. DADOS DA EMPRESA:")
    empresa = db.execute_query("""
        SELECT id, nome_empresa, ativa FROM empresas WHERE id = 4
    """, fetch_one=True)
    
    if empresa:
        print(f"   • ID: {empresa['id']}")
        print(f"   • Nome: {empresa['nome_empresa']}")
        print(f"   • Ativa: {empresa['ativa']}")
    else:
        print("   ❌ Empresa não encontrada!")
        return
    
    # 2. Verificar jornada padrão da empresa
    print("\n📋 2. JORNADA PADRÃO DA EMPRESA:")
    jornada_empresa = db.execute_query("""
        SELECT id, nome_jornada, padrao, ativa,
               seg_qui_entrada, seg_qui_saida,
               sexta_entrada, sexta_saida,
               intervalo_inicio, intervalo_fim,
               tolerancia_entrada_minutos
        FROM jornadas_trabalho 
        WHERE empresa_id = 4 AND padrao = 1 AND ativa = 1
    """)
    
    if jornada_empresa:
        for jornada in jornada_empresa:
            print(f"   • ID: {jornada['id']}")
            print(f"   • Nome: {jornada['nome_jornada']}")
            print(f"   • Padrão: {jornada['padrao']}")
            print(f"   • Ativa: {jornada['ativa']}")
            print(f"   • Seg-Qui: {jornada['seg_qui_entrada']} às {jornada['seg_qui_saida']}")
            print(f"   • Sexta: {jornada['sexta_entrada']} às {jornada['sexta_saida']}")
            print(f"   • Intervalo: {jornada['intervalo_inicio']} às {jornada['intervalo_fim']}")
            print(f"   • Tolerância: {jornada['tolerancia_entrada_minutos']} min")
    else:
        print("   ❌ Nenhuma jornada padrão encontrada!")
    
    # 3. Verificar funcionários da empresa
    print("\n📋 3. FUNCIONÁRIOS DA EMPRESA:")
    funcionarios = db.execute_query("""
        SELECT f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id, f.usa_horario_empresa,
               jt.nome_jornada, jt.seg_qui_entrada, jt.seg_qui_saida, jt.sexta_entrada, jt.sexta_saida
        FROM funcionarios f
        LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.empresa_id = 4 AND f.ativo = 1
    """)
    
    if funcionarios:
        for func in funcionarios:
            print(f"\n   👤 {func['nome_completo']} (ID: {func['id']})")
            print(f"      • Empresa ID: {func['empresa_id']}")
            print(f"      • Jornada ID: {func['jornada_trabalho_id']}")
            print(f"      • Usa horário empresa: {func['usa_horario_empresa']}")
            if func['nome_jornada']:
                print(f"      • Jornada atual: {func['nome_jornada']}")
                print(f"      • Seg-Qui: {func['seg_qui_entrada']} às {func['seg_qui_saida']}")
                print(f"      • Sexta: {func['sexta_entrada']} às {func['sexta_saida']}")
            else:
                print("      ❌ SEM JORNADA DEFINIDA!")
    else:
        print("   ❌ Nenhum funcionário encontrado!")
    
    # 4. Testar aplicação manual da herança
    print("\n📋 4. TESTANDO APLICAÇÃO MANUAL DA HERANÇA:")
    if funcionarios and jornada_empresa:
        jornada_id = jornada_empresa[0]['id']
        for func in funcionarios:
            if func['jornada_trabalho_id'] != jornada_id:
                print(f"   🔄 Aplicando jornada {jornada_id} ao funcionário {func['id']}")
                try:
                    resultado = SistemaHerancaJornadas.aplicar_jornada_empresa_funcionario(
                        func['id'], 4, 'admin'
                    )
                    print(f"   ✅ Resultado: {resultado}")
                except Exception as e:
                    print(f"   ❌ Erro: {e}")
            else:
                print(f"   ✅ Funcionário {func['id']} já tem a jornada correta")

def verificar_triggers():
    """Verificar se os triggers estão funcionando"""
    
    print("\n🔧 VERIFICANDO TRIGGERS:")
    print("=" * 40)
    
    db = DatabaseManager()
    
    triggers = db.execute_query("""
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, ACTION_TIMING 
        FROM INFORMATION_SCHEMA.TRIGGERS 
        WHERE TRIGGER_SCHEMA = 'controle_ponto'
        AND TRIGGER_NAME LIKE '%jornada%'
    """)
    
    if triggers:
        for trigger in triggers:
            print(f"   ✅ {trigger['TRIGGER_NAME']} - {trigger['ACTION_TIMING']} {trigger['EVENT_MANIPULATION']}")
    else:
        print("   ❌ Nenhum trigger encontrado!")

def main():
    """Função principal"""
    try:
        investigar_empresa_4()
        verificar_triggers()
        
        print("\n" + "=" * 60)
        print("🎯 CONCLUSÃO:")
        print("Verifique os resultados acima para identificar o problema.")
        
    except Exception as e:
        print(f"❌ Erro durante investigação: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
