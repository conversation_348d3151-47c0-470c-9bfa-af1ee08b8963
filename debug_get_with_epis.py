#!/usr/bin/env python3
"""
Debug: Investigar função get_with_epis
Verificar exatamente quais dados estão sendo retornados
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import FuncionarioQueries, DatabaseManager

def debug_get_with_epis():
    """Debug da função get_with_epis"""
    
    print("🔍 DEBUG: FUNÇÃO get_with_epis")
    print("=" * 60)
    
    # Buscar funcionário da empresa MSV Engenharia (ID 4)
    db = DatabaseManager()
    funcionarios_empresa_4 = db.execute_query("""
        SELECT id, nome_completo FROM funcionarios 
        WHERE empresa_id = 4 AND ativo = 1 
        LIMIT 1
    """)
    
    if not funcionarios_empresa_4:
        print("❌ Nenhum funcionário da empresa 4 encontrado")
        return
    
    funcionario_id = funcionarios_empresa_4[0]['id']
    nome = funcionarios_empresa_4[0]['nome_completo']
    
    print(f"👤 Testando com: {nome} (ID: {funcionario_id})")
    
    # 1. Testar função get_with_epis
    print(f"\n📋 PASSO 1: Executando get_with_epis({funcionario_id})")
    funcionario = FuncionarioQueries.get_with_epis(funcionario_id)
    
    if not funcionario:
        print("❌ get_with_epis retornou None")
        return
    
    print("✅ get_with_epis retornou dados")
    
    # 2. Verificar campos de jornada retornados
    print(f"\n📋 PASSO 2: Verificando campos de jornada retornados")
    
    campos_jornada = [
        'jornada_trabalho_id',
        'jornada_nome_jornada', 
        'jornada_seg_qui_entrada',
        'jornada_seg_qui_saida',
        'jornada_sexta_entrada', 
        'jornada_sexta_saida',
        'jornada_intervalo_inicio',
        'jornada_intervalo_fim',
        'jornada_tolerancia_entrada_minutos'
    ]
    
    for campo in campos_jornada:
        valor = funcionario.get(campo)
        print(f"   {campo}: {valor}")
    
    # 3. Verificar se há campos de horário (legacy)
    print(f"\n📋 PASSO 3: Verificando campos de horário (legacy)")
    
    campos_horario = [
        'horario_trabalho_id',
        'entrada_manha',
        'saida_almoco', 
        'entrada_tarde',
        'saida'
    ]
    
    for campo in campos_horario:
        valor = funcionario.get(campo)
        if valor is not None:
            print(f"   {campo}: {valor}")
    
    # 4. Buscar dados diretamente da jornada
    print(f"\n📋 PASSO 4: Buscando dados diretamente da jornada")
    
    jornada_direta = db.execute_query("""
        SELECT 
            jt.id, jt.nome_jornada,
            jt.seg_qui_entrada, jt.seg_qui_saida,
            jt.sexta_entrada, jt.sexta_saida,
            jt.intervalo_inicio, jt.intervalo_fim,
            jt.tolerancia_entrada_minutos
        FROM funcionarios f
        INNER JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.id = %s
    """, (funcionario_id,), fetch_one=True)
    
    if jornada_direta:
        print("✅ Jornada encontrada diretamente:")
        for key, value in jornada_direta.items():
            print(f"   {key}: {value}")
    else:
        print("❌ Nenhuma jornada encontrada diretamente")
    
    # 5. Comparar com jornada da empresa
    print(f"\n📋 PASSO 5: Comparando com jornada padrão da empresa")
    
    jornada_empresa = db.execute_query("""
        SELECT 
            jt.id, jt.nome_jornada,
            jt.seg_qui_entrada, jt.seg_qui_saida,
            jt.sexta_entrada, jt.sexta_saida,
            jt.intervalo_inicio, jt.intervalo_fim,
            jt.tolerancia_entrada_minutos
        FROM jornadas_trabalho jt
        WHERE jt.empresa_id = 4 AND jt.padrao = 1 AND jt.ativa = 1
    """, fetch_one=True)
    
    if jornada_empresa:
        print("✅ Jornada padrão da empresa:")
        for key, value in jornada_empresa.items():
            print(f"   {key}: {value}")
        
        # Comparar
        if jornada_direta and jornada_empresa:
            print(f"\n🔍 COMPARAÇÃO:")
            if jornada_direta['id'] == jornada_empresa['id']:
                print("   ✅ Funcionário está usando a jornada padrão da empresa")
            else:
                print("   ❌ Funcionário NÃO está usando a jornada padrão da empresa")
                print(f"      Funcionário usa jornada ID: {jornada_direta['id']}")
                print(f"      Empresa tem jornada padrão ID: {jornada_empresa['id']}")
    else:
        print("❌ Empresa não tem jornada padrão")
    
    # 6. Verificar template - como os dados são exibidos
    print(f"\n📋 PASSO 6: Simulando exibição no template")
    
    # Simular como o template acessa os dados
    template_data = {
        'seg_qui_entrada': funcionario.get('jornada_seg_qui_entrada'),
        'seg_qui_saida': funcionario.get('jornada_seg_qui_saida'),
        'sexta_entrada': funcionario.get('jornada_sexta_entrada'),
        'sexta_saida': funcionario.get('jornada_sexta_saida'),
        'intervalo_inicio': funcionario.get('jornada_intervalo_inicio'),
        'intervalo_fim': funcionario.get('jornada_intervalo_fim')
    }
    
    print("📺 Dados que o template recebe:")
    for key, value in template_data.items():
        if value:
            # Converter timedelta para string como no template
            if hasattr(value, 'total_seconds'):
                horas = int(value.total_seconds() // 3600)
                minutos = int((value.total_seconds() % 3600) // 60)
                value_str = f"{horas:02d}:{minutos:02d}"
            else:
                value_str = str(value)
            print(f"   {key}: {value_str}")
        else:
            print(f"   {key}: N/A")

if __name__ == "__main__":
    debug_get_with_epis()
