#!/usr/bin/env python3
"""
Teste para investigar o bug do campo 'id' relacionado à jornada da empresa
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def testar_jornada_empresa():
    """Testar se a consulta de jornada está retornando campo 'id' problemático"""
    try:
        from utils.database import DatabaseManager
        import logging
        
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        print("🔍 TESTANDO CONSULTA DE JORNADA DA EMPRESA")
        print("=" * 60)
        
        # Buscar uma empresa para testar
        empresa = DatabaseManager.execute_query(
            "SELECT id, nome_fantasia FROM empresas LIMIT 1", 
            fetch_one=True
        )
        
        if not empresa:
            print("❌ Nenhuma empresa encontrada")
            return
            
        empresa_id = empresa['id']
        print(f"📊 Testando com empresa: {empresa['nome_fantasia']} (ID: {empresa_id})")
        
        # Primeira consulta (empresas_config) - pode não retornar 'id'
        print("\n1. CONSULTA EMPRESAS_CONFIG:")
        print("-" * 40)
        
        jornada1 = DatabaseManager.execute_query("""
            SELECT
                'Jornada Padrão da Empresa' as nome_horario,
                ec.jornada_segunda_entrada as entrada_manha,
                ec.jornada_segunda_saida_almoco as saida_almoco,
                ec.jornada_segunda_entrada_almoco as entrada_tarde,
                ec.jornada_segunda_saida as saida,
                ec.tolerancia_empresa_minutos as tolerancia_minutos,
                ec.jornada_sexta_entrada,
                ec.jornada_sexta_saida_almoco,
                ec.jornada_sexta_entrada_almoco,
                ec.jornada_sexta_saida,
                ec.intervalo_obrigatorio
            FROM empresas_config ec
            WHERE ec.empresa_id = %s
        """, (empresa_id,))
        
        if jornada1:
            print(f"✅ Resultado empresas_config encontrado:")
            for key, value in jornada1[0].items():
                print(f"   {key}: {value}")
            
            if 'id' in jornada1[0]:
                print(f"🚨 PROBLEMA: Campo 'id' encontrado na consulta empresas_config!")
        else:
            print("⚠️ Nenhum resultado em empresas_config")
        
        # Segunda consulta (horarios_trabalho) - ESTA RETORNA 'id'!
        print("\n2. CONSULTA HORARIOS_TRABALHO:")
        print("-" * 40)
        
        jornada2 = DatabaseManager.execute_query("""
            SELECT ht.id, ht.nome_horario, ht.entrada_manha, ht.saida_almoco,
                   ht.entrada_tarde, ht.saida, ht.tolerancia_minutos
            FROM horarios_trabalho ht
            WHERE ht.empresa_id = %s AND ht.ativo = 1
            ORDER BY ht.id ASC
            LIMIT 1
        """, (empresa_id,))
        
        if jornada2:
            print(f"✅ Resultado horarios_trabalho encontrado:")
            for key, value in jornada2[0].items():
                print(f"   {key}: {value}")
            
            if 'id' in jornada2[0]:
                print(f"🚨 PROBLEMA ENCONTRADO: Campo 'id' = {jornada2[0]['id']}")
                print(f"🎯 ESTA É A CAUSA DO BUG!")
                print(f"   A função get_jornada_padrao_empresa retorna jornada[0]")
                print(f"   que contém o campo 'id' da tabela horarios_trabalho")
                return True
        else:
            print("⚠️ Nenhum resultado em horarios_trabalho")
        
        # Testar a função real
        print("\n3. TESTANDO FUNÇÃO get_jornada_padrao_empresa:")
        print("-" * 50)
        
        sys.path.append('.')
        from app_funcionarios import get_jornada_padrao_empresa
        
        resultado = get_jornada_padrao_empresa(empresa_id)
        if resultado:
            print(f"📋 Resultado da função:")
            for key, value in resultado.items():
                print(f"   {key}: {value}")
                
            if 'id' in resultado:
                print(f"🚨 CONFIRMADO: A função retorna campo 'id' = {resultado['id']}")
                print(f"🔧 ESTE CAMPO ESTÁ SENDO ADICIONADO AOS DADOS DO FUNCIONÁRIO!")
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 INVESTIGAÇÃO DO BUG 'ID' - JORNADA DA EMPRESA")
    print("=" * 60)
    
    if testar_jornada_empresa():
        print("\n" + "=" * 60)
        print("🎯 CAUSA DO BUG IDENTIFICADA!")
        print("📋 A função get_jornada_padrao_empresa retorna o campo 'id'")
        print("🔧 Este campo está sendo misturado com os dados do funcionário")
        print("⚠️ SOLUÇÃO: Remover o campo 'id' do resultado da função")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❓ Causa do bug não identificada neste teste")
        print("=" * 60)
