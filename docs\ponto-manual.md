# 📋 **DOCUMENTAÇÃO COMPLETA - SISTEMA DE PONTO MANUAL**

## 📖 **ÍNDICE**

1. [<PERSON><PERSON><PERSON> Geral](#visão-geral)
2. [Fluxo Completo](#fluxo-completo)
3. [Tabelas Utilizadas](#tabelas-utilizadas)
4. [Funções Principais](#funções-principais)
5. [Lógica de Decisão](#lógica-de-decisão)
6. [Parâmetros de Decisão](#parâmetros-de-decisão)
7. [Validações e Regras](#validações-e-regras)
8. [Status de Pontualidade](#status-de-pontualidade)
9. [Tratamento de Erros](#tratamento-de-erros)
10. [APIs e Endpoints](#apis-e-endpoints)

---

## 🎯 **VISÃO GERAL**

O Sistema de Ponto Manual é responsável por:
- **Determinar automaticamente** qual tipo de registro oferecer ao funcionário
- **Validar** se o registro é permitido no horário atual
- **Calcular** status de pontualidade (Pontual/Atrasado)
- **Registrar** o ponto no banco de dados
- **Aplicar regras de negócio** baseadas em períodos e sequência

### **🔄 Princípios Fundamentais:**

1. **PRIORIDADE DO PERÍODO:** Tabela `dia_dados` tem prioridade sobre sequência
2. **LÓGICA DE FALLBACK:** Se período indefinido, usa sequência de registros
3. **VALIDAÇÃO RIGOROSA:** Múltiplas camadas de validação
4. **FLEXIBILIDADE:** Suporte a B1-B6 (4 batidas básicas + 2 horas extras)

---

## 🔄 **FLUXO COMPLETO**

### **📱 1. INTERFACE USUÁRIO**
```
Usuário acessa: /registro-ponto/manual
├── Seleciona funcionário
├── Sistema chama: GET /api/obter-horarios/{funcionario_id}
└── Exibe tipos disponíveis
```

### **⚙️ 2. PROCESSAMENTO BACKEND**
```
API /api/obter-horarios/{funcionario_id}
├── 1. obter_horarios_funcionario(funcionario_id)
├── 2. obter_batidas_do_dia(funcionario_id, hoje)
├── 3. classificar_batida_inteligente(funcionario_id, num_batidas+1, turno_info, hora_atual)
├── 4. Determinar tipos disponíveis
└── 5. Retornar JSON com tipos permitidos
```

### **💾 3. REGISTRO DO PONTO**
```
POST /api/registrar-manual
├── 1. Validar funcionário e horários
├── 2. validar_tipo_registro_por_horario(tipo, horarios, funcionario_id)
├── 3. Calcular status de pontualidade
├── 4. registrar_ponto_no_banco(...)
└── 5. Retornar resultado
```

---

## 🗄️ **TABELAS UTILIZADAS**

### **📊 1. TABELA `dia_dados` (PRINCIPAL)**
**Função:** Define períodos do dia para determinação automática de tipos

```sql
CREATE TABLE dia_dados (
    id INT PRIMARY KEY,
    turno ENUM('Manha', 'Intervalo', 'Tarde', 'Fim_Diurno', 'Noite_Inicio', 'Noite_Intervalo', 'Noite_Fim'),
    horario_inicio TIME,
    horario_fim TIME,
    descricao VARCHAR(255),
    ativo BOOLEAN,
    ordem_prioridade INT
);
```

**Dados Atuais:**
```
ID:  8 | Manha           | 06:00-11:00 | Período da manhã
ID:  9 | Intervalo       | 11:00-14:00 | Período de intervalo  
ID: 10 | Tarde           | 14:00-18:00 | Período da tarde
ID: 11 | Fim_Diurno      | 18:00-21:00 | Fim da jornada diurna
ID: 12 | Noite_Inicio    | 21:00-00:00 | Início período noturno
ID: 13 | Noite_Intervalo | 00:00-02:00 | Intervalo noturno
ID: 14 | Noite_Fim       | 02:00-05:59 | Fim período noturno
```

### **👤 2. TABELA `funcionarios`**
**Função:** Dados básicos do funcionário

**Campos Utilizados:**
- `id` - Identificador único
- `nome_completo` - Nome do funcionário
- `empresa_id` - Empresa vinculada
- `status_cadastro` - Status ativo/inativo

### **🏢 3. TABELA `empresas`**
**Função:** Dados da empresa do funcionário

**Campos Utilizados:**
- `id` - Identificador único
- `razao_social` - Nome da empresa

### **⏰ 4. TABELA `horarios_trabalho`**
**Função:** Horários de trabalho configurados

**Campos Utilizados:**
- `entrada_manha` - Horário de entrada (ex: 08:00)
- `saida_almoco` - Horário de saída para almoço (ex: 12:00)
- `entrada_tarde` - Horário de retorno do almoço (ex: 13:00)
- `saida` - Horário de saída final (ex: 17:00)
- `tolerancia_minutos` - Tolerância em minutos (ex: 10)
- `ativo` - Se o horário está ativo

### **📝 5. TABELA `registros_ponto`**
**Função:** Armazena todos os registros de ponto

**Campos Utilizados:**
- `funcionario_id` - ID do funcionário
- `tipo_registro` - Tipo (entrada_manha, saida_almoco, entrada_tarde, saida, inicio_extra, fim_extra)
- `data_hora` - Data e hora do registro
- `metodo_registro` - Método (manual, biometrico)
- `observacoes` - Observações do registro
- `status_pontualidade` - Status (Pontual, Atrasado)

---

## ⚙️ **FUNÇÕES PRINCIPAIS**

### **🎯 1. `classificar_batida_inteligente()`**
**Arquivo:** `app_registro_ponto.py` (linhas 397-579)

**Função:** Determina automaticamente qual tipo de registro oferecer

**Parâmetros:**
- `funcionario_id` (int) - ID do funcionário
- `numero_batida` (int) - Número da batida (1-6)
- `turno_info` (dict) - Informações do turno
- `hora_atual` (datetime) - Horário atual

**Lógica:**
```python
def classificar_batida_inteligente(funcionario_id, numero_batida, turno_info, hora_atual):
    # 1. Obter horários do funcionário
    horarios = obter_horarios_funcionario(funcionario_id)
    
    # 2. Consultar tabela dia_dados para determinar período
    cursor.execute("""
        SELECT turno, horario_inicio, horario_fim, descricao
        FROM dia_dados
        WHERE ativo = TRUE
        ORDER BY ordem_prioridade
    """)
    
    # 3. PRIORIDADE 1: Determinar tipo baseado no período atual
    for periodo in periodos:
        if inicio <= hora_atual_obj < fim:
            if turno == 'Manha':
                tipo_sugerido = "entrada_manha"
            elif turno == 'Intervalo':
                tipo_sugerido = "saida_almoco"
            elif turno == 'Tarde':
                tipo_sugerido = "entrada_tarde"
            elif turno == 'Fim_Diurno':
                tipo_sugerido = "saida"
    
    # 4. Verificar se tipo já foi registrado
    if tipo_sugerido not in tipos_registrados:
        return tipo_sugerido
    
    # 5. FALLBACK: Usar lógica de sequência
    return usar_logica_sequencia(tipos_registrados)
```

### **📋 2. `obter_horarios_funcionario()`**
**Arquivo:** `app_registro_ponto.py` (linhas 1033-1130)

**Função:** Obtém horários de trabalho do funcionário

**Consulta SQL:**
```sql
SELECT
    f.nome_completo,
    f.empresa_id,
    e.razao_social as empresa_nome,
    -- PRIORIDADE 1: Jornada da alocação ativa
    -- PRIORIDADE 2: Configuração da empresa
    -- PRIORIDADE 3: Jornada específica do funcionário
    COALESCE(
        ht_alocacao.entrada_manha,
        ec_config.jornada_segunda_entrada,
        ht_funcionario.entrada_manha,
        '08:00'
    ) as entrada_manha,
    -- ... outros horários
FROM funcionarios f
LEFT JOIN empresas e ON f.empresa_id = e.id
LEFT JOIN horarios_trabalho ht ON e.id = ht.empresa_id AND ht.ativo = 1
WHERE f.id = %s AND f.status_cadastro = 'Ativo'
```

### **✅ 3. `validar_tipo_registro_por_horario()`**
**Arquivo:** `app_registro_ponto.py` (linhas 2038-2400)

**Função:** Valida se o tipo de registro é permitido no horário atual

**Validações:**
1. **Período atual** - Determina em qual período do dia estamos
2. **Horário permitido** - Verifica se está dentro da janela permitida
3. **Status de pontualidade** - Calcula se é Pontual ou Atrasado
4. **Regras específicas** - Aplica regras por tipo de registro

### **💾 4. `registrar_ponto_no_banco()`**
**Arquivo:** `app_registro_ponto.py` (linhas 1177-1350)

**Função:** Registra o ponto no banco de dados

**Processo:**
1. Validar duplicatas
2. Inserir registro na tabela `registros_ponto`
3. Criar justificativas se necessário
4. Retornar resultado

---

## 🧠 **LÓGICA DE DECISÃO**

### **🎯 ALGORITMO PRINCIPAL**

```mermaid
graph TD
    A[Usuário solicita tipos] --> B[Obter horários funcionário]
    B --> C[Obter registros existentes do dia]
    C --> D[Determinar número da próxima batida]
    D --> E[Consultar tabela dia_dados]
    E --> F{Hora atual está em algum período?}
    F -->|SIM| G[Mapear período para tipo]
    F -->|NÃO| H[Usar lógica de sequência]
    G --> I{Tipo já foi registrado?}
    I -->|NÃO| J[Retornar tipo do período]
    I -->|SIM| H
    H --> K[Determinar próximo tipo na sequência]
    K --> L[Retornar tipo determinado]
```

### **📊 MAPEAMENTO PERÍODO → TIPO**

| Período | Horário | Tipo Sugerido |
|---------|---------|---------------|
| Manha | 06:00-11:00 | `entrada_manha` |
| Intervalo | 11:00-14:00 | `saida_almoco` |
| Tarde | 14:00-18:00 | `entrada_tarde` |
| Fim_Diurno | 18:00-21:00 | `saida` |
| Noite_Inicio | 21:00-00:00 | `entrada_manha` |
| Noite_Intervalo | 00:00-02:00 | `saida_almoco` |
| Noite_Fim | 02:00-05:59 | `entrada_tarde` |

### **🔄 LÓGICA DE SEQUÊNCIA (FALLBACK)**

```python
# Sequência padrão B1-B6
sequencia_padrao = {
    1: 'entrada_manha',    # B1 - Entrada
    2: 'saida_almoco',     # B2 - Saída almoço
    3: 'entrada_tarde',    # B3 - Retorno almoço
    4: 'saida',            # B4 - Saída final
    5: 'inicio_extra',     # B5 - Início hora extra
    6: 'fim_extra'         # B6 - Fim hora extra
}

# Lógica: Oferecer próximo tipo não registrado
tipos_registrados = ['entrada_manha', 'saida_almoco']
proximo_tipo = 'entrada_tarde'  # Próximo na sequência
```

---

## 📊 **PARÂMETROS DE DECISÃO**

### **🕐 1. PARÂMETROS TEMPORAIS**
- **`hora_atual`** - Horário atual do sistema
- **`data_atual`** - Data atual para consulta de registros
- **`periodo_atual`** - Período determinado pela tabela `dia_dados`

### **👤 2. PARÂMETROS DO FUNCIONÁRIO**
- **`funcionario_id`** - Identificador único
- **`horarios_funcionario`** - Horários de trabalho configurados
- **`empresa_id`** - Empresa vinculada
- **`tolerancia_minutos`** - Tolerância configurada

### **📝 3. PARÂMETROS DE REGISTROS**
- **`registros_existentes`** - Registros já feitos no dia
- **`tipos_registrados`** - Lista de tipos já registrados
- **`numero_batida`** - Número da próxima batida (1-6)

### **⚙️ 4. PARÂMETROS DE CONFIGURAÇÃO**
- **`turno_info`** - Informações do turno (Manhã/Tarde/Noite)
- **`metodo_registro`** - Método (manual/biométrico)
- **`observacoes`** - Observações do registro

---

## ✅ **VALIDAÇÕES E REGRAS**

### **🔒 1. VALIDAÇÕES OBRIGATÓRIAS**

#### **Funcionário Ativo:**
```python
if not funcionario or funcionario['status_cadastro'] != 'Ativo':
    return {'permitido': False, 'mensagem': 'Funcionário inativo'}
```

#### **Horários Configurados:**
```python
if not horarios_funcionario:
    return {'permitido': False, 'mensagem': 'Horário não configurado'}
```

#### **Limite de Batidas:**
```python
if num_batidas >= 6:
    return {'permitido': False, 'mensagem': 'Limite de 6 batidas atingido'}
```

### **⏰ 2. VALIDAÇÕES POR TIPO**

#### **Entrada Manhã:**
```python
if tipo_registro == 'entrada_manha':
    # Permitido desde 1h antes até tolerância após horário
    hora_min = entrada_manha - timedelta(hours=1)
    hora_max = entrada_manha + timedelta(minutes=tolerancia)
    
    if not (hora_min <= hora_atual <= hora_max):
        return {'permitido': False, 'mensagem': f'Entrada permitida entre {hora_min} e {hora_max}'}
```

#### **Saída Almoço:**
```python
if tipo_registro == 'saida_almoco':
    # Deve ter entrada_manha registrada
    if 'entrada_manha' not in tipos_registrados:
        return {'permitido': False, 'mensagem': 'Entrada manhã deve ser registrada primeiro'}
    
    # Flexível - sem restrição rígida de horário
    return {'permitido': True, 'mensagem': 'Saída para almoço permitida'}
```

#### **Entrada Tarde:**
```python
if tipo_registro == 'entrada_tarde':
    # Deve ter saida_almoco registrada
    if 'saida_almoco' not in tipos_registrados:
        return {'permitido': False, 'mensagem': 'Saída almoço deve ser registrada primeiro'}
    
    # Validar intervalo mínimo de 1 hora
    ultimo_registro = obter_ultimo_registro(funcionario_id)
    if ultimo_registro and ultimo_registro['tipo_registro'] == 'saida_almoco':
        tempo_intervalo = hora_atual - ultimo_registro['data_hora']
        if tempo_intervalo < timedelta(hours=1):
            return {'permitido': False, 'mensagem': 'Intervalo mínimo de 1 hora não respeitado'}
```

### **🎯 3. REGRAS DE NEGÓCIO**

#### **Sequência Obrigatória:**
- B1 (entrada_manha) → B2 (saida_almoco) → B3 (entrada_tarde) → B4 (saida)
- B5 e B6 são opcionais (horas extras)

#### **Tolerância Aplicada:**
- **Entrada manhã:** Tolerância após horário oficial
- **Saída final:** Tolerância após horário oficial  
- **Almoço:** Flexível (sem tolerância rígida)

#### **Períodos Noturnos:**
- Tratamento especial para turnos que atravessam meia-noite
- Lógica adaptada para horários 21:00-05:59

---

## 📊 **STATUS DE PONTUALIDADE**

### **🎯 CÁLCULO DO STATUS**

#### **Entrada Manhã:**
```python
def calcular_status_entrada_manha(hora_atual, entrada_manha, tolerancia):
    hora_limite = entrada_manha + timedelta(minutes=tolerancia)
    
    if hora_atual <= entrada_manha:
        return "Pontual"  # Entrada antecipada
    elif hora_atual <= hora_limite:
        return "Pontual"  # Dentro da tolerância
    else:
        return "Atrasado"  # Após tolerância
```

#### **Entrada Tarde:**
```python
def calcular_status_entrada_tarde(hora_atual, entrada_tarde, tolerancia):
    # Buscar horário de saída para almoço
    ultimo_registro = obter_ultimo_registro_tipo('saida_almoco')
    horario_retorno_oficial = entrada_tarde
    
    # Aplicar regra de compensação automática
    if ultimo_registro:
        saida_almoco_real = ultimo_registro['data_hora'].time()
        if saida_almoco_real > saida_almoco_oficial:
            # Compensar atraso na saída
            atraso_saida = saida_almoco_real - saida_almoco_oficial
            horario_retorno_oficial += atraso_saida
    
    hora_limite = horario_retorno_oficial + timedelta(minutes=tolerancia)
    
    if hora_atual <= horario_retorno_oficial:
        return "Pontual"
    elif hora_atual <= hora_limite:
        return "Pontual"  # Dentro da tolerância
    else:
        return "Atrasado"
```

#### **Saída Final:**
```python
def calcular_status_saida(hora_atual, saida_oficial, tolerancia):
    # Saída antecipada é permitida sem penalização
    if hora_atual < saida_oficial:
        return "Pontual"
    
    hora_limite = saida_oficial + timedelta(minutes=tolerancia)
    
    if hora_atual <= hora_limite:
        return "Pontual"
    else:
        return "Atrasado"  # Saída muito tardia
```

### **📋 REGRAS ESPECIAIS**

1. **Entrada antecipada:** Sempre considerada pontual
2. **Saída almoço:** Sempre pontual (flexível)
3. **Compensação automática:** Atraso na saída do almoço compensa retorno
4. **Tolerância aplicada:** Apenas para entrada manhã e saída final

---

## 🚨 **TRATAMENTO DE ERROS**

### **🔧 1. ERROS DE CONFIGURAÇÃO**

```python
# Funcionário sem horário configurado
if not horarios_funcionario:
    return {
        'success': False,
        'message': 'Funcionário não possui horário de trabalho configurado. Entre em contato com o administrador.'
    }

# Funcionário inativo
if funcionario['status_cadastro'] != 'Ativo':
    return {
        'success': False,
        'message': 'Funcionário inativo no sistema.'
    }
```

### **⚠️ 2. ERROS DE VALIDAÇÃO**

```python
# Tipo de registro não permitido
if not validacao['permitido']:
    return {
        'success': False,
        'message': validacao['mensagem']
    }

# Limite de batidas excedido
if num_batidas >= 6:
    return {
        'success': False,
        'message': 'Limite de 6 batidas por dia atingido (B1-B6). Verifique os registros existentes.'
    }
```

### **💥 3. ERROS DE SISTEMA**

```python
# Erro na classificação inteligente
try:
    tipo_registro = classificar_batida_inteligente(...)
except Exception as e:
    logger.error(f"Erro na classificação inteligente: {e}")
    # Fallback para método simples
    return classificar_batida_por_sequencia(numero_batida, turno_info)

# Erro no banco de dados
try:
    resultado = registrar_ponto_no_banco(...)
except Exception as e:
    logger.error(f"Erro ao registrar ponto: {e}")
    return {
        'success': False,
        'message': 'Erro interno do sistema. Tente novamente.'
    }
```

### **🔄 4. FALLBACKS**

1. **Período indefinido:** Usa lógica de sequência
2. **Erro na classificação:** Usa método simples
3. **Horários não encontrados:** Usa horários padrão
4. **Erro de conexão:** Retry automático

---

## 🌐 **APIs E ENDPOINTS**

### **📋 1. GET `/api/obter-horarios/{funcionario_id}`**

**Função:** Obter tipos de registro disponíveis para o funcionário

**Processo:**
1. Validar funcionário ativo
2. Obter horários configurados
3. Obter registros existentes do dia
4. Classificar próxima batida
5. Determinar tipos disponíveis
6. Retornar JSON

**Resposta:**
```json
{
    "success": true,
    "horarios": {
        "entrada_manha": "08:00",
        "saida_almoco": "12:00",
        "entrada_tarde": "13:00",
        "saida": "17:00",
        "tolerancia_minutos": 10,
        "nome_horario": "Horário Padrão"
    },
    "tipos_disponiveis": [
        {
            "value": "entrada_manha",
            "text": "Entrada Manhã (08:00)",
            "tolerancia": "Permitido até 08:10"
        }
    ],
    "todos_tipos": [...],
    "registros_existentes": [...]
}
```

### **💾 2. POST `/api/registrar-manual`**

**Função:** Registrar ponto manual

**Parâmetros:**
- `funcionario_id` (int) - ID do funcionário
- `tipo_registro` (string) - Tipo do registro
- `observacoes` (string, opcional) - Observações

**Processo:**
1. Validar parâmetros
2. Validar funcionário e horários
3. Validar tipo de registro por horário
4. Calcular status de pontualidade
5. Registrar no banco
6. Retornar resultado

**Resposta:**
```json
{
    "success": true,
    "message": "Ponto registrado com sucesso",
    "registro": {
        "id": 123,
        "tipo_registro": "entrada_manha",
        "data_hora": "2025-07-17 08:54:00",
        "status_pontualidade": "Pontual"
    }
}
```

---

## 📈 **LOGS E MONITORAMENTO**

### **🔍 LOGS DETALHADOS**

```python
# Início da classificação
logger.info(f"🎯 [CLASSIFICAÇÃO INTELIGENTE] INICIANDO - Funcionário: {funcionario_id}, Batida: #{numero_batida}, Hora: {hora_atual}")

# Período determinado
logger.info(f"[PERÍODO] ✅ USANDO TIPO BASEADO NO PERÍODO: {tipo_sugerido}")

# Fallback usado
logger.info(f"[FALLBACK] ✅ Oferecendo {tipo} ({motivo})")

# Resultado final
logger.info(f"[API HORÁRIOS] Funcionário: {funcionario_id}, Registros: {num_batidas}, Próximo tipo: {proximo_tipo}")
```

### **📊 MÉTRICAS IMPORTANTES**

- Taxa de uso de período vs sequência
- Tempo de resposta das APIs
- Erros de validação mais comuns
- Distribuição de status de pontualidade

---

## 🎯 **CONCLUSÃO**

O Sistema de Ponto Manual é uma solução robusta que combina:

1. **Inteligência temporal** - Usa tabela `dia_dados` para decisões baseadas em período
2. **Flexibilidade** - Fallback para lógica de sequência quando necessário
3. **Validação rigorosa** - Múltiplas camadas de validação
4. **Escalabilidade** - Suporte a diferentes turnos e configurações
5. **Confiabilidade** - Tratamento abrangente de erros

**O sistema prioriza SEMPRE o período atual sobre a sequência, garantindo que funcionários recebam o tipo de registro correto baseado no horário em que estão fazendo o ponto.**
