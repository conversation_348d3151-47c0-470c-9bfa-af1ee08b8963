#!/usr/bin/env python3
"""
Sistema de Herança Dinâmica de Jornadas
Sistema de Controle de Ponto - RLPONTO-WEB
Data: 14/07/2025

Implementa herança automática de jornadas com histórico completo.
"""

import logging
from datetime import datetime, date
from typing import Optional, Dict, List, Any
from utils.database import DatabaseManager

# Configurar logger
logger = logging.getLogger('controle-ponto.heranca-jornadas')

class SistemaHerancaJornadas:
    """
    Sistema completo de herança dinâmica de jornadas.
    
    Funcionalidades:
    - Herança automática no cadastro
    - Herança na alocação para clientes
    - Propagação automática de mudanças
    - Histórico completo de alterações
    """
    
    @staticmethod
    def registrar_mudanca_jornada(
        funcionario_id: int,
        jornada_anterior_id: Optional[int],
        jornada_nova_id: int,
        tipo_mudanca: str,
        motivo: str,
        usuario_responsavel: Optional[int] = None
    ) -> bool:
        """
        Registra mudança de jornada no log e histórico.
        
        Args:
            funcionario_id: ID do funcionário
            jornada_anterior_id: ID da jornada anterior (pode ser None)
            jornada_nova_id: ID da nova jornada
            tipo_mudanca: Tipo da mudança (CADASTRO_INICIAL, ALOCACAO_CLIENTE, etc.)
            motivo: Motivo da mudança
            usuario_responsavel: ID do usuário responsável
            
        Returns:
            bool: True se registrou com sucesso
        """
        try:
            # Buscar dados da jornada nova
            jornada_nova = DatabaseManager.execute_query("""
                SELECT nome_jornada, seg_qui_entrada, seg_qui_saida, 
                       sexta_entrada, sexta_saida, intervalo_inicio, intervalo_fim,
                       tolerancia_entrada_minutos
                FROM jornadas_trabalho 
                WHERE id = %s
            """, (jornada_nova_id,), fetch_one=True)
            
            if not jornada_nova:
                logger.error(f"Jornada nova {jornada_nova_id} não encontrada")
                return False
            
            # Buscar dados da jornada anterior (se existir)
            dados_jornada_anterior = None
            if jornada_anterior_id:
                jornada_anterior = DatabaseManager.execute_query("""
                    SELECT nome_jornada, seg_qui_entrada, seg_qui_saida, 
                           sexta_entrada, sexta_saida, intervalo_inicio, intervalo_fim,
                           tolerancia_entrada_minutos
                    FROM jornadas_trabalho 
                    WHERE id = %s
                """, (jornada_anterior_id,), fetch_one=True)
                
                if jornada_anterior:
                    dados_jornada_anterior = {
                        'nome_jornada': jornada_anterior['nome_jornada'],
                        'seg_qui_entrada': str(jornada_anterior['seg_qui_entrada']),
                        'seg_qui_saida': str(jornada_anterior['seg_qui_saida']),
                        'sexta_entrada': str(jornada_anterior['sexta_entrada']),
                        'sexta_saida': str(jornada_anterior['sexta_saida']),
                        'intervalo_inicio': str(jornada_anterior['intervalo_inicio']),
                        'intervalo_fim': str(jornada_anterior['intervalo_fim']),
                        'tolerancia_entrada_minutos': jornada_anterior['tolerancia_entrada_minutos']
                    }
            
            # Dados da jornada nova
            dados_jornada_nova = {
                'nome_jornada': jornada_nova['nome_jornada'],
                'seg_qui_entrada': str(jornada_nova['seg_qui_entrada']),
                'seg_qui_saida': str(jornada_nova['seg_qui_saida']),
                'sexta_entrada': str(jornada_nova['sexta_entrada']),
                'sexta_saida': str(jornada_nova['sexta_saida']),
                'intervalo_inicio': str(jornada_nova['intervalo_inicio']),
                'intervalo_fim': str(jornada_nova['intervalo_fim']),
                'tolerancia_entrada_minutos': jornada_nova['tolerancia_entrada_minutos']
            }
            
            # Inserir no log de mudanças
            sql_log = """
                INSERT INTO log_mudancas_jornada (
                    funcionario_id, jornada_anterior_id, jornada_nova_id, 
                    tipo_mudanca, motivo, dados_jornada_anterior, dados_jornada_nova,
                    usuario_responsavel
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            import json
            DatabaseManager.execute_query(sql_log, (
                funcionario_id, jornada_anterior_id, jornada_nova_id,
                tipo_mudanca, motivo, 
                json.dumps(dados_jornada_anterior) if dados_jornada_anterior else None,
                json.dumps(dados_jornada_nova),
                usuario_responsavel
            ), fetch_all=False)
            
            # Mapear tipo de mudança para tipo de evento do histórico
            tipo_evento_map = {
                'CADASTRO_INICIAL': 'JORNADA_HERDADA_EMPRESA',
                'ALOCACAO_CLIENTE': 'JORNADA_HERDADA_CLIENTE',
                'EMPRESA_ALTEROU': 'EMPRESA_MUDOU_JORNADA',
                'ALTERACAO_MANUAL': 'JORNADA_ALTERADA',
                'RETORNO_EMPRESA': 'JORNADA_HERDADA_EMPRESA'
            }
            
            tipo_evento = tipo_evento_map.get(tipo_mudanca, 'JORNADA_ALTERADA')
            
            # Criar detalhes para o histórico
            if jornada_anterior_id and dados_jornada_anterior:
                detalhes = f"""
Jornada alterada: {dados_jornada_anterior['nome_jornada']} → {dados_jornada_nova['nome_jornada']}
Horário anterior: {dados_jornada_anterior['seg_qui_entrada']} às {dados_jornada_anterior['seg_qui_saida']}
Novo horário: {dados_jornada_nova['seg_qui_entrada']} às {dados_jornada_nova['seg_qui_saida']}
Motivo: {motivo}
                """.strip()
            else:
                detalhes = f"""
Nova jornada: {dados_jornada_nova['nome_jornada']}
Horário: {dados_jornada_nova['seg_qui_entrada']} às {dados_jornada_nova['seg_qui_saida']}
Motivo: {motivo}
                """.strip()
            
            # Inserir no histórico do funcionário
            from sistema_aprovacoes import registrar_historico_funcionario
            registrar_historico_funcionario(
                funcionario_id=funcionario_id,
                tipo_evento=tipo_evento,
                data_evento=datetime.now(),
                data_referencia=date.today(),
                detalhes=detalhes,
                status_aprovacao='NAO_APLICAVEL'
            )
            
            logger.info(f"Mudança de jornada registrada: Funcionário {funcionario_id}, {tipo_mudanca}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao registrar mudança de jornada: {e}")
            return False
    
    @staticmethod
    def aplicar_jornada_empresa_funcionario(funcionario_id: int, empresa_id: int, usuario_responsavel: Optional[int] = None) -> bool:
        """
        Aplica jornada padrão da empresa ao funcionário.
        
        Args:
            funcionario_id: ID do funcionário
            empresa_id: ID da empresa
            usuario_responsavel: ID do usuário responsável
            
        Returns:
            bool: True se aplicou com sucesso
        """
        try:
            # Buscar jornada padrão da empresa
            jornada_empresa = DatabaseManager.execute_query("""
                SELECT id, nome_jornada FROM jornadas_trabalho
                WHERE empresa_id = %s AND padrao = 1 AND ativa = 1
                LIMIT 1
            """, (empresa_id,), fetch_one=True)
            
            if not jornada_empresa:
                logger.warning(f"Empresa {empresa_id} não possui jornada padrão")
                return False
            
            # Buscar jornada atual do funcionário
            funcionario = DatabaseManager.execute_query("""
                SELECT jornada_trabalho_id FROM funcionarios WHERE id = %s
            """, (funcionario_id,), fetch_one=True)
            
            if not funcionario:
                logger.error(f"Funcionário {funcionario_id} não encontrado")
                return False
            
            jornada_anterior_id = funcionario['jornada_trabalho_id']
            
            # Se já está na jornada correta, não fazer nada
            if jornada_anterior_id == jornada_empresa['id']:
                logger.info(f"Funcionário {funcionario_id} já está na jornada correta")
                return True
            
            # Atualizar funcionário
            DatabaseManager.execute_query("""
                UPDATE funcionarios 
                SET jornada_trabalho_id = %s,
                    usa_horario_empresa = TRUE,
                    data_atualizacao_jornada = CURRENT_TIMESTAMP,
                    jornada_alterada_por = %s
                WHERE id = %s
            """, (jornada_empresa['id'], usuario_responsavel, funcionario_id), fetch_all=False)
            
            # Registrar mudança
            SistemaHerancaJornadas.registrar_mudanca_jornada(
                funcionario_id=funcionario_id,
                jornada_anterior_id=jornada_anterior_id,
                jornada_nova_id=jornada_empresa['id'],
                tipo_mudanca='EMPRESA_ALTEROU',
                motivo=f"Aplicação da jornada padrão da empresa: {jornada_empresa['nome_jornada']}",
                usuario_responsavel=usuario_responsavel
            )
            
            logger.info(f"Jornada da empresa aplicada ao funcionário {funcionario_id}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao aplicar jornada da empresa: {e}")
            return False
    
    @staticmethod
    def sincronizar_funcionarios_empresa(empresa_id: int, usuario_responsavel: Optional[int] = None) -> int:
        """
        Sincroniza todos os funcionários da empresa com a jornada padrão.
        
        Args:
            empresa_id: ID da empresa
            usuario_responsavel: ID do usuário responsável
            
        Returns:
            int: Número de funcionários sincronizados
        """
        try:
            # Buscar funcionários que usam horário da empresa
            funcionarios = DatabaseManager.execute_query("""
                SELECT id, nome_completo, jornada_trabalho_id
                FROM funcionarios 
                WHERE empresa_id = %s 
                AND usa_horario_empresa = TRUE
                AND ativo = TRUE
            """, (empresa_id,))
            
            if not funcionarios:
                logger.info(f"Nenhum funcionário encontrado para sincronizar na empresa {empresa_id}")
                return 0
            
            sincronizados = 0
            for funcionario in funcionarios:
                if SistemaHerancaJornadas.aplicar_jornada_empresa_funcionario(
                    funcionario['id'], empresa_id, usuario_responsavel
                ):
                    sincronizados += 1
            
            logger.info(f"Sincronizados {sincronizados} funcionários da empresa {empresa_id}")
            return sincronizados
            
        except Exception as e:
            logger.error(f"Erro ao sincronizar funcionários da empresa: {e}")
            return 0
    
    @staticmethod
    def obter_historico_jornadas_funcionario(funcionario_id: int, limite: int = 50) -> List[Dict[str, Any]]:
        """
        Obtém histórico completo de mudanças de jornada do funcionário.
        
        Args:
            funcionario_id: ID do funcionário
            limite: Número máximo de registros
            
        Returns:
            List[Dict]: Lista com histórico de mudanças
        """
        try:
            historico = DatabaseManager.execute_query("""
                SELECT 
                    lmj.id,
                    lmj.tipo_mudanca,
                    lmj.motivo,
                    lmj.data_mudanca,
                    lmj.dados_jornada_anterior,
                    lmj.dados_jornada_nova,
                    ja.nome_jornada as jornada_anterior_nome,
                    jn.nome_jornada as jornada_nova_nome,
                    u.usuario as usuario_responsavel_nome
                FROM log_mudancas_jornada lmj
                LEFT JOIN jornadas_trabalho ja ON lmj.jornada_anterior_id = ja.id
                LEFT JOIN jornadas_trabalho jn ON lmj.jornada_nova_id = jn.id
                LEFT JOIN usuarios u ON lmj.usuario_responsavel = u.id
                WHERE lmj.funcionario_id = %s
                ORDER BY lmj.data_mudanca DESC
                LIMIT %s
            """, (funcionario_id, limite))
            
            return historico or []
            
        except Exception as e:
            logger.error(f"Erro ao obter histórico de jornadas: {e}")
            return []
    
    @staticmethod
    def verificar_consistencia_jornadas() -> Dict[str, Any]:
        """
        Verifica consistência do sistema de jornadas.
        
        Returns:
            Dict: Relatório de consistência
        """
        try:
            relatorio = {
                'funcionarios_sem_jornada': 0,
                'funcionarios_jornada_inativa': 0,
                'empresas_sem_jornada_padrao': 0,
                'alocacoes_jornada_inconsistente': 0,
                'problemas': []
            }
            
            # Funcionários sem jornada
            sem_jornada = DatabaseManager.execute_query("""
                SELECT COUNT(*) as total FROM funcionarios 
                WHERE jornada_trabalho_id IS NULL AND ativo = TRUE
            """, fetch_one=True)
            relatorio['funcionarios_sem_jornada'] = sem_jornada['total']
            
            # Funcionários com jornada inativa
            jornada_inativa = DatabaseManager.execute_query("""
                SELECT COUNT(*) as total FROM funcionarios f
                LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
                WHERE f.ativo = TRUE AND (jt.ativa = FALSE OR jt.id IS NULL)
            """, fetch_one=True)
            relatorio['funcionarios_jornada_inativa'] = jornada_inativa['total']
            
            # Empresas sem jornada padrão
            sem_padrao = DatabaseManager.execute_query("""
                SELECT COUNT(*) as total FROM empresas e
                WHERE e.ativa = TRUE 
                AND NOT EXISTS (
                    SELECT 1 FROM jornadas_trabalho jt 
                    WHERE jt.empresa_id = e.id AND jt.padrao = TRUE AND jt.ativa = TRUE
                )
            """, fetch_one=True)
            relatorio['empresas_sem_jornada_padrao'] = sem_padrao['total']
            
            # Alocações com jornada inconsistente
            alocacoes_inconsistentes = DatabaseManager.execute_query("""
                SELECT COUNT(*) as total FROM funcionario_alocacoes fa
                LEFT JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
                WHERE fa.ativo = TRUE AND (jt.ativa = FALSE OR jt.id IS NULL)
            """, fetch_one=True)
            relatorio['alocacoes_jornada_inconsistente'] = alocacoes_inconsistentes['total']
            
            # Identificar problemas
            if relatorio['funcionarios_sem_jornada'] > 0:
                relatorio['problemas'].append(f"{relatorio['funcionarios_sem_jornada']} funcionários sem jornada definida")
            
            if relatorio['funcionarios_jornada_inativa'] > 0:
                relatorio['problemas'].append(f"{relatorio['funcionarios_jornada_inativa']} funcionários com jornada inativa")
            
            if relatorio['empresas_sem_jornada_padrao'] > 0:
                relatorio['problemas'].append(f"{relatorio['empresas_sem_jornada_padrao']} empresas sem jornada padrão")
            
            if relatorio['alocacoes_jornada_inconsistente'] > 0:
                relatorio['problemas'].append(f"{relatorio['alocacoes_jornada_inconsistente']} alocações com jornada inconsistente")
            
            return relatorio
            
        except Exception as e:
            logger.error(f"Erro ao verificar consistência: {e}")
            return {'erro': str(e)}
