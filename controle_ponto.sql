﻿--
-- Script was generated by Devart dbForge Studio for MySQL, Version **********
-- Product home page: http://www.devart.com/dbforge/mysql/studio
-- Script date 03/07/2025 09:26:29
-- Server version: 8.0.42
--

--
-- Disable foreign keys
--
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;

--
-- Set SQL mode
--
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;

--
-- Set character set the client will use to send SQL statements to the server
--
SET NAMES 'utf8';

DROP DATABASE IF EXISTS controle_ponto;

CREATE DATABASE IF NOT EXISTS controle_ponto
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

--
-- Set default database
--
USE controle_ponto;

--
-- Create table `empresas`
--
CREATE TABLE IF NOT EXISTS empresas (
  id int NOT NULL AUTO_INCREMENT,
  razao_social varchar(200) NOT NULL,
  nome_fantasia varchar(200) DEFAULT NULL,
  cnpj varchar(18) NOT NULL,
  telefone varchar(15) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  ativa tinyint(1) DEFAULT 1,
  empresa_teste tinyint(1) DEFAULT 0,
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  logotipo longblob DEFAULT NULL,
  logotipo_mime_type varchar(100) DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `cnpj` on table `empresas`
--
ALTER TABLE empresas
ADD UNIQUE INDEX cnpj (cnpj);

--
-- Create table `jornadas_trabalho`
--
CREATE TABLE IF NOT EXISTS jornadas_trabalho (
  id int NOT NULL AUTO_INCREMENT,
  empresa_id int NOT NULL COMMENT 'ID da empresa',
  nome_jornada varchar(100) NOT NULL COMMENT 'Nome da jornada (ex: Diurno 01 - Profissionais)',
  descricao text DEFAULT NULL COMMENT 'DescriÃ§Ã£o detalhada da jornada',
  codigo_jornada varchar(20) DEFAULT NULL COMMENT 'CÃ³digo interno da jornada',
  tipo_jornada enum ('Diurno', 'Noturno', 'Misto', 'Especial') DEFAULT 'Diurno' COMMENT 'Tipo da jornada',
  categoria_funcionario varchar(100) DEFAULT NULL COMMENT 'Categoria de funcionÃ¡rio (ex: Profissionais, Serventes)',
  seg_qui_entrada time NOT NULL DEFAULT '08:00:00' COMMENT 'Entrada segunda a quinta',
  seg_qui_saida time NOT NULL DEFAULT '17:00:00' COMMENT 'SaÃ­da segunda a quinta',
  sexta_entrada time DEFAULT NULL COMMENT 'Entrada sexta-feira (se diferente)',
  sexta_saida time DEFAULT NULL COMMENT 'SaÃ­da sexta-feira (se diferente)',
  sabado_entrada time DEFAULT NULL COMMENT 'Entrada sÃ¡bado',
  sabado_saida time DEFAULT NULL COMMENT 'SaÃ­da sÃ¡bado',
  domingo_entrada time DEFAULT NULL COMMENT 'Entrada domingo',
  domingo_saida time DEFAULT NULL COMMENT 'SaÃ­da domingo',
  intervalo_obrigatorio tinyint(1) DEFAULT 1 COMMENT 'Se tem intervalo obrigatÃ³rio',
  intervalo_inicio time DEFAULT '12:00:00' COMMENT 'InÃ­cio do intervalo',
  intervalo_fim time DEFAULT '13:00:00' COMMENT 'Fim do intervalo',
  intervalo_duracao_minutos int DEFAULT 60 COMMENT 'DuraÃ§Ã£o do intervalo em minutos',
  intervalo_flexivel tinyint(1) DEFAULT 0 COMMENT 'Se o horÃ¡rio do intervalo Ã© flexÃ­vel',
  intervalo_inicio_permitido time DEFAULT '11:00:00' COMMENT 'InÃ­cio mais cedo permitido para intervalo',
  intervalo_fim_permitido time DEFAULT '14:00:00' COMMENT 'Fim mais tarde permitido para intervalo',
  tolerancia_entrada_minutos int DEFAULT 15 COMMENT 'TolerÃ¢ncia para entrada em minutos',
  tolerancia_saida_minutos int DEFAULT 10 COMMENT 'TolerÃ¢ncia para saÃ­da em minutos',
  tolerancia_intervalo_minutos int DEFAULT 10 COMMENT 'TolerÃ¢ncia para intervalo em minutos',
  permite_banco_horas tinyint(1) DEFAULT 1 COMMENT 'Se permite banco de horas nesta jornada',
  limite_banco_horas_diario_minutos int DEFAULT 120 COMMENT 'Limite diÃ¡rio de banco de horas em minutos',
  permite_hora_extra tinyint(1) DEFAULT 1 COMMENT 'Se permite hora extra',
  limite_hora_extra_diaria_minutos int DEFAULT 120 COMMENT 'Limite diÃ¡rio de hora extra em minutos',
  aplica_segunda tinyint(1) DEFAULT 1 COMMENT 'Se aplica na segunda-feira',
  aplica_terca tinyint(1) DEFAULT 1 COMMENT 'Se aplica na terÃ§a-feira',
  aplica_quarta tinyint(1) DEFAULT 1 COMMENT 'Se aplica na quarta-feira',
  aplica_quinta tinyint(1) DEFAULT 1 COMMENT 'Se aplica na quinta-feira',
  aplica_sexta tinyint(1) DEFAULT 1 COMMENT 'Se aplica na sexta-feira',
  aplica_sabado tinyint(1) DEFAULT 0 COMMENT 'Se aplica no sÃ¡bado',
  aplica_domingo tinyint(1) DEFAULT 0 COMMENT 'Se aplica no domingo',
  ativa tinyint(1) DEFAULT 1 COMMENT 'Se a jornada estÃ¡ ativa',
  padrao tinyint(1) DEFAULT 0 COMMENT 'Se Ã© a jornada padrÃ£o da empresa',
  ordem_exibicao int DEFAULT 0 COMMENT 'Ordem de exibiÃ§Ã£o na lista',
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de cadastro',
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Data da Ãºltima atualizaÃ§Ã£o',
  cadastrado_por varchar(100) DEFAULT NULL COMMENT 'UsuÃ¡rio que cadastrou',
  atualizado_por varchar(100) DEFAULT NULL COMMENT 'UsuÃ¡rio que atualizou',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
COMMENT = 'Jornadas de trabalho por empresa',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_ativa` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD INDEX idx_ativa (ativa);

--
-- Create index `idx_categoria` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD INDEX idx_categoria (categoria_funcionario);

--
-- Create index `idx_empresa` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD INDEX idx_empresa (empresa_id);

--
-- Create index `idx_padrao` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD INDEX idx_padrao (padrao);

--
-- Create index `idx_tipo_jornada` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD INDEX idx_tipo_jornada (tipo_jornada);

--
-- Create index `uk_empresa_padrao` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD UNIQUE INDEX uk_empresa_padrao (empresa_id, padrao);

--
-- Create foreign key
--
ALTER TABLE jornadas_trabalho
ADD CONSTRAINT jornadas_trabalho_ibfk_1 FOREIGN KEY (empresa_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create table `horarios_trabalho`
--
CREATE TABLE IF NOT EXISTS horarios_trabalho (
  id int NOT NULL AUTO_INCREMENT,
  empresa_id int NOT NULL,
  nome_horario varchar(100) NOT NULL,
  entrada_manha time NOT NULL DEFAULT '08:00:00',
  saida_almoco time DEFAULT '12:00:00',
  entrada_tarde time DEFAULT '13:00:00',
  saida time NOT NULL DEFAULT '17:00:00',
  tolerancia_minutos int NOT NULL DEFAULT 10,
  ativo tinyint(1) DEFAULT 1,
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE horarios_trabalho
ADD CONSTRAINT horarios_trabalho_ibfk_1 FOREIGN KEY (empresa_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create table `clientes`
--
CREATE TABLE IF NOT EXISTS clientes (
  id int NOT NULL AUTO_INCREMENT,
  empresa_id int NOT NULL COMMENT 'ID da empresa proprietÃ¡ria do cliente',
  nome_cliente varchar(200) NOT NULL COMMENT 'Nome/RazÃ£o social do cliente',
  nome_fantasia varchar(200) DEFAULT NULL COMMENT 'Nome fantasia do cliente',
  cnpj_cpf varchar(18) DEFAULT NULL COMMENT 'CNPJ/CPF do cliente',
  codigo_cliente varchar(50) DEFAULT NULL COMMENT 'CÃ³digo interno do cliente',
  tipo_cliente enum ('Pessoa FÃ­sica', 'Pessoa JurÃ­dica', 'Obra', 'Projeto') DEFAULT 'Pessoa JurÃ­dica' COMMENT 'Tipo do cliente',
  endereco_completo text DEFAULT NULL COMMENT 'EndereÃ§o completo do cliente',
  telefone varchar(15) DEFAULT NULL COMMENT 'Telefone do cliente',
  email varchar(100) DEFAULT NULL COMMENT 'Email do cliente',
  contato_responsavel varchar(100) DEFAULT NULL COMMENT 'Nome do responsÃ¡vel/contato',
  observacoes text DEFAULT NULL COMMENT 'ObservaÃ§Ãµes sobre o cliente',
  ativo tinyint(1) DEFAULT 1 COMMENT 'Se o cliente estÃ¡ ativo',
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de cadastro',
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Data da Ãºltima atualizaÃ§Ã£o',
  cadastrado_por varchar(100) DEFAULT NULL COMMENT 'UsuÃ¡rio que cadastrou',
  atualizado_por varchar(100) DEFAULT NULL COMMENT 'UsuÃ¡rio que atualizou',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AVG_ROW_LENGTH = 16384,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
COMMENT = 'Clientes das empresas para alocaÃ§Ã£o de funcionÃ¡rios',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_ativo` on table `clientes`
--
ALTER TABLE clientes
ADD INDEX idx_ativo (ativo);

--
-- Create index `idx_empresa_id` on table `clientes`
--
ALTER TABLE clientes
ADD INDEX idx_empresa_id (empresa_id);

--
-- Create index `idx_nome_cliente` on table `clientes`
--
ALTER TABLE clientes
ADD INDEX idx_nome_cliente (nome_cliente);

--
-- Create index `idx_tipo_cliente` on table `clientes`
--
ALTER TABLE clientes
ADD INDEX idx_tipo_cliente (tipo_cliente);

--
-- Create foreign key
--
ALTER TABLE clientes
ADD CONSTRAINT clientes_ibfk_1 FOREIGN KEY (empresa_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create table `funcionarios`
--
CREATE TABLE IF NOT EXISTS funcionarios (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  empresa_id int DEFAULT NULL,
  cliente_atual_id int DEFAULT NULL COMMENT 'ID do cliente atual do funcionÃ¡rio',
  horario_trabalho_id int DEFAULT NULL,
  nome_completo varchar(100) NOT NULL,
  cpf varchar(14) NOT NULL,
  rg varchar(20) NOT NULL,
  data_nascimento date NOT NULL,
  sexo enum ('M', 'F', 'Outro') NOT NULL,
  estado_civil enum ('Solteiro', 'Casado', 'Divorciado', 'Viuvo') NOT NULL,
  nacionalidade varchar(50) NOT NULL DEFAULT 'Brasileiro',
  ctps_numero varchar(20) NOT NULL,
  ctps_serie_uf varchar(20) NOT NULL,
  pis_pasep varchar(20) NOT NULL,
  endereco_rua varchar(100) DEFAULT NULL,
  endereco_bairro varchar(50) DEFAULT NULL,
  endereco_cidade varchar(50) DEFAULT NULL,
  endereco_cep varchar(10) NOT NULL,
  endereco_estado varchar(2) NOT NULL,
  telefone1 varchar(15) NOT NULL,
  telefone2 varchar(15) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  cargo varchar(50) NOT NULL,
  setor varchar(50) DEFAULT NULL,
  setor_obra varchar(50) NOT NULL,
  empresa varchar(100) DEFAULT 'Empresa Principal',
  matricula_empresa varchar(20) NOT NULL,
  data_admissao date NOT NULL,
  tipo_contrato enum ('CLT', 'PJ', 'Estagio', 'Temporario') NOT NULL,
  jornada_seg_qui_entrada time DEFAULT NULL,
  jornada_seg_qui_saida time DEFAULT NULL,
  jornada_sex_entrada time DEFAULT NULL,
  jornada_sex_saida time DEFAULT NULL,
  jornada_intervalo_entrada time DEFAULT NULL,
  jornada_intervalo_saida time DEFAULT NULL,
  digital_dedo1 longblob DEFAULT NULL COMMENT 'Template biométrico dedo 1',
  digital_dedo2 longblob DEFAULT NULL COMMENT 'Template biométrico dedo 2',
  foto_3x4 varchar(255) DEFAULT NULL COMMENT 'Caminho para foto 3x4',
  foto_url varchar(255) DEFAULT NULL COMMENT 'URL da foto para templates',
  nivel_acesso enum ('Funcionario', 'Supervisao', 'Gerencia') NOT NULL DEFAULT 'Funcionario',
  turno enum ('Diurno', 'Noturno', 'Misto') NOT NULL DEFAULT 'Diurno',
  tolerancia_ponto int UNSIGNED NOT NULL DEFAULT 5 COMMENT 'Tolerância em minutos',
  banco_horas tinyint(1) DEFAULT 0,
  hora_extra tinyint(1) DEFAULT 0,
  ativo tinyint(1) DEFAULT 1 COMMENT 'Compatibilidade com código existente',
  status_cadastro enum ('Ativo', 'Inativo') NOT NULL DEFAULT 'Ativo',
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  biometria_qualidade_1 tinyint UNSIGNED DEFAULT NULL COMMENT 'Qualidade da captura do dedo 1 (0-100)',
  biometria_qualidade_2 tinyint UNSIGNED DEFAULT NULL COMMENT 'Qualidade da captura do dedo 2 (0-100)',
  biometria_data_cadastro timestamp NULL DEFAULT NULL,
  horario_entrada_manha time GENERATED ALWAYS AS (`jornada_seg_qui_entrada`) VIRTUAL,
  horario_saida_almoco time GENERATED ALWAYS AS (`jornada_intervalo_entrada`) VIRTUAL,
  horario_entrada_tarde time GENERATED ALWAYS AS (`jornada_intervalo_saida`) VIRTUAL,
  horario_saida time GENERATED ALWAYS AS (`jornada_seg_qui_saida`) VIRTUAL,
  salario_base decimal(10, 2) DEFAULT NULL COMMENT 'Salário base do funcionário',
  tipo_pagamento enum ('Mensal', 'Quinzenal', 'Semanal', 'Diario', 'Por_Hora') DEFAULT 'Mensal' COMMENT 'Tipo/frequência de pagamento',
  valor_hora decimal(8, 2) DEFAULT NULL COMMENT 'Valor da hora de trabalho',
  valor_hora_extra decimal(8, 2) DEFAULT NULL COMMENT 'Valor da hora extra',
  percentual_hora_extra decimal(5, 2) DEFAULT 50.00 COMMENT 'Percentual adicional para hora extra',
  vale_transporte decimal(8, 2) DEFAULT NULL COMMENT 'Valor do vale transporte',
  vale_alimentacao decimal(8, 2) DEFAULT NULL COMMENT 'Valor do vale alimentação/refeição',
  outros_beneficios decimal(8, 2) DEFAULT NULL COMMENT 'Outros benefícios monetários',
  desconto_inss tinyint(1) DEFAULT 1 COMMENT 'Aplicar desconto de INSS',
  desconto_irrf tinyint(1) DEFAULT 1 COMMENT 'Aplicar desconto de IRRF',
  observacoes_pagamento text DEFAULT NULL COMMENT 'Observações sobre pagamento',
  data_ultima_alteracao_salario timestamp NULL DEFAULT NULL COMMENT 'Data da última alteração salarial',
  biometria_qualidade int DEFAULT 0,
  ultimo_login timestamp NULL DEFAULT NULL,
  tentativas_biometria int DEFAULT 0,
  status_biometria enum ('ativo', 'inativo', 'bloqueado') DEFAULT 'inativo',
  status enum ('ativo', 'inativo', 'suspenso') DEFAULT 'ativo',
  epi_obrigatorio_json json DEFAULT NULL,
  epi_treinamento_data date DEFAULT NULL,
  epi_responsavel varchar(100) DEFAULT NULL,
  epi_observacoes text DEFAULT NULL,
  epi_termo_assinado tinyint(1) DEFAULT 0,
  inicio_expediente time DEFAULT '07:00:00' COMMENT 'HorÃ¡rio programado de entrada',
  horario_saida_seg_qui time DEFAULT '17:00:00' COMMENT 'HorÃ¡rio de saÃ­da segunda a quinta',
  horario_saida_sexta time DEFAULT '16:30:00' COMMENT 'HorÃ¡rio de saÃ­da na sexta-feira',
  periodo_almoco_inicio time DEFAULT '11:00:00' COMMENT 'InÃ­cio da janela de almoÃ§o',
  periodo_almoco_fim time DEFAULT '14:00:00' COMMENT 'Fim da janela de almoÃ§o',
  duracao_minima_almoco int DEFAULT 60 COMMENT 'DuraÃ§Ã£o mÃ­nima do almoÃ§o em minutos',
  tolerancia_entrada int DEFAULT 15 COMMENT 'TolerÃ¢ncia para entrada em minutos',
  permite_banco_horas_positivo tinyint(1) DEFAULT 0 COMMENT 'Permite acumular horas extras como crÃ©dito',
  data_atualizacao_jornada timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  horas_trabalho_obrigatorias decimal(4, 2) DEFAULT 8.00,
  jornada_trabalho_id int DEFAULT NULL COMMENT 'ID da jornada especÃ­fica do funcionÃ¡rio',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 10,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Funcionários com dados biométricos e jornada de trabalho',
ROW_FORMAT = DYNAMIC;

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_cpf_format CHECK (regexp_like(`cpf`, _utf8mb4 '^[0-9]{3}\\.[0-9]{3}\\.[0-9]{3}-[0-9]{2}$'));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_email_format CHECK ((`email` IS NULL) OR regexp_like(`email`, _utf8mb4 '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_percentual_range CHECK ((`percentual_hora_extra` IS NULL) OR (`percentual_hora_extra` BETWEEN 0 AND 200));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_qualidade_range_1 CHECK ((`biometria_qualidade_1` IS NULL) OR (`biometria_qualidade_1` BETWEEN 0 AND 100));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_qualidade_range_2 CHECK ((`biometria_qualidade_2` IS NULL) OR (`biometria_qualidade_2` BETWEEN 0 AND 100));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_salario_positivo CHECK ((`salario_base` IS NULL) OR (`salario_base` >= 0));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_tolerancia_range CHECK (`tolerancia_ponto` BETWEEN 0 AND 60);

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_valor_hora_positivo CHECK ((`valor_hora` IS NULL) OR (`valor_hora` >= 0));

--
-- Create index `cpf` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD UNIQUE INDEX cpf (cpf);

--
-- Create index `idx_cliente_atual` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_cliente_atual (cliente_atual_id);

--
-- Create index `idx_cpf` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_cpf (cpf);

--
-- Create index `idx_data_admissao` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_data_admissao (data_admissao);

--
-- Create index `idx_funcionario_jornada` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_funcionario_jornada (jornada_trabalho_id);

--
-- Create index `idx_funcionarios_biometria_status` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_funcionarios_biometria_status (status_biometria);

--
-- Create index `idx_matricula` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_matricula (matricula_empresa);

--
-- Create index `idx_nome` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_nome (nome_completo);

--
-- Create index `idx_salario_base` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_salario_base (salario_base);

--
-- Create index `idx_setor` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_setor (setor_obra);

--
-- Create index `idx_status` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_status (status_cadastro);

--
-- Create index `idx_tipo_pagamento` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_tipo_pagamento (tipo_pagamento);

--
-- Create index `matricula_empresa` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD UNIQUE INDEX matricula_empresa (matricula_empresa);

--
-- Create foreign key
--
ALTER TABLE funcionarios
ADD CONSTRAINT fk_funcionario_jornada FOREIGN KEY (jornada_trabalho_id)
REFERENCES jornadas_trabalho (id) ON DELETE SET NULL;

--
-- Create foreign key
--
ALTER TABLE funcionarios
ADD CONSTRAINT funcionarios_cliente_atual_fk FOREIGN KEY (cliente_atual_id)
REFERENCES clientes (id) ON DELETE SET NULL;

--
-- Create view `vw_funcionarios_biometria`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_funcionarios_biometria
AS
SELECT
  `f`.`id` AS `id`,
  `f`.`nome_completo` AS `nome_completo`,
  `f`.`cpf` AS `cpf`,
  `f`.`setor_obra` AS `setor`,
  `f`.`cargo` AS `cargo`,
  `f`.`matricula_empresa` AS `matricula_empresa`,
  `f`.`empresa` AS `empresa`,
  `f`.`status_cadastro` AS `status_cadastro`,
  (CASE WHEN ((`f`.`digital_dedo1` IS NOT NULL) OR
      (`f`.`digital_dedo2` IS NOT NULL)) THEN 'Configurado' ELSE 'Não Configurado' END) AS `status_biometria`,
  `f`.`biometria_qualidade_1` AS `biometria_qualidade_1`,
  `f`.`biometria_qualidade_2` AS `biometria_qualidade_2`,
  `f`.`foto_url` AS `foto_url`,
  `f`.`data_cadastro` AS `data_cadastro`,
  `f`.`data_atualizacao` AS `data_atualizacao`
FROM `funcionarios` `f`
WHERE (`f`.`status_cadastro` = 'Ativo')
ORDER BY `f`.`nome_completo`;

--
-- Create table `funcionario_cliente_alocacao`
--
CREATE TABLE IF NOT EXISTS funcionario_cliente_alocacao (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL COMMENT 'ID do funcionÃ¡rio',
  cliente_id int NOT NULL COMMENT 'ID do cliente',
  jornada_trabalho_id int DEFAULT NULL COMMENT 'ID da jornada especÃ­fica para este cliente',
  data_inicio date NOT NULL COMMENT 'Data de inÃ­cio da alocaÃ§Ã£o',
  data_fim date DEFAULT NULL COMMENT 'Data de fim da alocaÃ§Ã£o (NULL = indefinido)',
  observacoes text DEFAULT NULL COMMENT 'ObservaÃ§Ãµes sobre a alocaÃ§Ã£o',
  ativo tinyint(1) DEFAULT 1 COMMENT 'Se a alocaÃ§Ã£o estÃ¡ ativa',
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de cadastro da alocaÃ§Ã£o',
  cadastrado_por varchar(100) DEFAULT NULL COMMENT 'UsuÃ¡rio que cadastrou',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
COMMENT = 'AlocaÃ§Ã£o de funcionÃ¡rios para clientes com jornadas especÃ­ficas',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_ativo` on table `funcionario_cliente_alocacao`
--
ALTER TABLE funcionario_cliente_alocacao
ADD INDEX idx_ativo (ativo);

--
-- Create index `idx_cliente_id` on table `funcionario_cliente_alocacao`
--
ALTER TABLE funcionario_cliente_alocacao
ADD INDEX idx_cliente_id (cliente_id);

--
-- Create index `idx_data_inicio` on table `funcionario_cliente_alocacao`
--
ALTER TABLE funcionario_cliente_alocacao
ADD INDEX idx_data_inicio (data_inicio);

--
-- Create index `idx_funcionario_id` on table `funcionario_cliente_alocacao`
--
ALTER TABLE funcionario_cliente_alocacao
ADD INDEX idx_funcionario_id (funcionario_id);

--
-- Create index `idx_jornada_trabalho_id` on table `funcionario_cliente_alocacao`
--
ALTER TABLE funcionario_cliente_alocacao
ADD INDEX idx_jornada_trabalho_id (jornada_trabalho_id);

--
-- Create foreign key
--
ALTER TABLE funcionario_cliente_alocacao
ADD CONSTRAINT funcionario_cliente_alocacao_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE funcionario_cliente_alocacao
ADD CONSTRAINT funcionario_cliente_alocacao_ibfk_2 FOREIGN KEY (cliente_id)
REFERENCES clientes (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE funcionario_cliente_alocacao
ADD CONSTRAINT funcionario_cliente_alocacao_ibfk_3 FOREIGN KEY (jornada_trabalho_id)
REFERENCES jornadas_trabalho (id) ON DELETE SET NULL;

--
-- Create table `epis`
--
CREATE TABLE IF NOT EXISTS epis (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL,
  epi_nome varchar(255) NOT NULL,
  epi_ca varchar(50) DEFAULT NULL,
  epi_data_entrega date DEFAULT NULL,
  epi_data_validade date DEFAULT NULL,
  epi_observacoes text DEFAULT NULL,
  status_epi enum ('entregue', 'vencido', 'devolvido') DEFAULT 'entregue',
  criado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  atualizado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Controle de EPIs dos funcionários',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_data_validade` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_data_validade (epi_data_validade);

--
-- Create index `idx_funcionario_epi` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_funcionario_epi (funcionario_id);

--
-- Create index `idx_status_epi` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_status_epi (status_epi);

--
-- Create foreign key
--
ALTER TABLE epis
ADD CONSTRAINT epis_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create table `banco_horas`
--
CREATE TABLE IF NOT EXISTS banco_horas (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL,
  data_referencia date NOT NULL,
  atraso_entrada_minutos int DEFAULT 0,
  excesso_almoco_minutos int DEFAULT 0,
  saida_antecipada_minutos int DEFAULT 0,
  horas_extras_minutos int DEFAULT 0,
  saldo_devedor_minutos int DEFAULT 0,
  saldo_credor_minutos int DEFAULT 0,
  saldo_liquido_minutos int DEFAULT 0,
  status_dia enum ('completo', 'incompleto', 'ausente_parcial', 'ausente_total', 'feriado', 'folga') DEFAULT 'incompleto',
  observacoes text DEFAULT NULL,
  justificativa text DEFAULT NULL,
  aprovado_por int DEFAULT NULL,
  data_aprovacao timestamp NULL DEFAULT NULL,
  criado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  atualizado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_data_referencia` on table `banco_horas`
--
ALTER TABLE banco_horas
ADD INDEX idx_data_referencia (data_referencia);

--
-- Create index `idx_funcionario` on table `banco_horas`
--
ALTER TABLE banco_horas
ADD INDEX idx_funcionario (funcionario_id);

--
-- Create index `idx_status` on table `banco_horas`
--
ALTER TABLE banco_horas
ADD INDEX idx_status (status_dia);

--
-- Create index `uk_funcionario_data` on table `banco_horas`
--
ALTER TABLE banco_horas
ADD UNIQUE INDEX uk_funcionario_data (funcionario_id, data_referencia);

--
-- Create foreign key
--
ALTER TABLE banco_horas
ADD CONSTRAINT banco_horas_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE;

--
-- Create table `usuarios`
--
CREATE TABLE IF NOT EXISTS usuarios (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  usuario varchar(50) NOT NULL,
  nome_completo varchar(100) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  ativo tinyint(1) DEFAULT 1,
  ultimo_login timestamp NULL DEFAULT NULL,
  senha varchar(255) NOT NULL,
  nivel_acesso enum ('usuario', 'admin') DEFAULT 'usuario',
  data_criacao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  data_ultimo_acesso timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 10,
AVG_ROW_LENGTH = 2730,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Usuários do sistema de controle de ponto',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_usuario` on table `usuarios`
--
ALTER TABLE usuarios
ADD INDEX idx_usuario (usuario);

--
-- Create index `usuario` on table `usuarios`
--
ALTER TABLE usuarios
ADD UNIQUE INDEX usuario (usuario);

--
-- Create table `registros_ponto`
--
CREATE TABLE IF NOT EXISTS registros_ponto (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL,
  tipo_registro enum ('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida') NOT NULL,
  data_hora timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  data_registro date GENERATED ALWAYS AS (CAST(`data_hora` AS date)) STORED COMMENT 'Data extraída para índices',
  metodo_registro enum ('biometrico', 'manual') NOT NULL,
  criado_por int UNSIGNED DEFAULT NULL COMMENT 'ID do usuário que fez registro manual',
  template_biometrico longblob DEFAULT NULL COMMENT 'Template biométrico usado',
  digital_capturada longblob DEFAULT NULL COMMENT 'Compatibilidade com código antigo',
  qualidade_biometria tinyint UNSIGNED DEFAULT NULL COMMENT 'Qualidade 0-100',
  observacoes text DEFAULT NULL,
  ip_origem varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  sincronizado tinyint(1) DEFAULT 0 COMMENT 'Compatibilidade',
  criado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  atualizado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  biometria_verificada tinyint(1) DEFAULT 0,
  device_hash varchar(64) DEFAULT NULL,
  security_score int DEFAULT 100,
  ip_address varchar(45) DEFAULT NULL,
  status_pontualidade enum ('Pontual', 'Atrasado') DEFAULT NULL COMMENT 'Status de pontualidade do registro',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Registros de ponto biométrico e manual - RLPONTO-WEB v1.2',
ROW_FORMAT = DYNAMIC;

--
-- Create check constraint
--
ALTER TABLE registros_ponto
ADD CONSTRAINT chk_qualidade_biometria CHECK ((`qualidade_biometria` IS NULL) OR (`qualidade_biometria` BETWEEN 0 AND 100));

--
-- Create index `idx_criado_por` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_criado_por (criado_por);

--
-- Create index `idx_data_hora` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_data_hora (data_hora);

--
-- Create index `idx_data_registro` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_data_registro (data_registro);

--
-- Create index `idx_funcionario_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_funcionario_data (funcionario_id, data_hora);

--
-- Create index `idx_funcionario_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_funcionario_tipo_data (funcionario_id, tipo_registro, data_hora);

--
-- Create index `idx_metodo` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_metodo (metodo_registro);

--
-- Create index `idx_registros_ponto_biometria` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_registros_ponto_biometria (biometria_verificada);

--
-- Create index `idx_registros_ponto_data_funcionario` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_registros_ponto_data_funcionario (data_hora, funcionario_id);

--
-- Create index `idx_registros_ponto_metodo` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_registros_ponto_metodo (metodo_registro);

--
-- Create index `idx_status_pontualidade` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_status_pontualidade (status_pontualidade);

--
-- Create index `idx_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_tipo_data (tipo_registro, data_hora);

--
-- Create index `uk_funcionario_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD UNIQUE INDEX uk_funcionario_tipo_data (funcionario_id, tipo_registro, data_registro);

--
-- Create foreign key
--
ALTER TABLE registros_ponto
ADD CONSTRAINT registros_ponto_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create foreign key
--
ALTER TABLE registros_ponto
ADD CONSTRAINT registros_ponto_ibfk_2 FOREIGN KEY (criado_por)
REFERENCES usuarios (id) ON DELETE SET NULL ON UPDATE CASCADE;

DELIMITER $$

--
-- Create function `VerificarIntegridadeBiometrica`
--
CREATE
DEFINER = 'cavalcrod'@'%'
FUNCTION IF NOT EXISTS VerificarIntegridadeBiometrica (p_funcionario_id int UNSIGNED)
RETURNS json
DETERMINISTIC
READS SQL DATA
BEGIN
  DECLARE resultado json;
  DECLARE tem_dedo1 boolean DEFAULT FALSE;
  DECLARE tem_dedo2 boolean DEFAULT FALSE;
  DECLARE total_registros int DEFAULT 0;
  DECLARE nome_funcionario varchar(100) DEFAULT '';

  -- Verificar se funcionário existe
  SELECT
    (digital_dedo1 IS NOT NULL) AS dedo1,
    (digital_dedo2 IS NOT NULL) AS dedo2,
    nome_completo INTO tem_dedo1, tem_dedo2, nome_funcionario
  FROM funcionarios
  WHERE id = p_funcionario_id
  AND status_cadastro = 'Ativo'
  LIMIT 1;

  -- Se funcionário não existe, retornar erro
  IF nome_funcionario = '' THEN
    SET resultado = JSON_OBJECT('erro', TRUE,
    'mensagem', 'Funcionário não encontrado ou inativo');
    RETURN resultado;
  END IF;

  SELECT
    COUNT(*) INTO total_registros
  FROM registros_ponto
  WHERE funcionario_id = p_funcionario_id;

  SET resultado = JSON_OBJECT('funcionario_id', p_funcionario_id,
  'nome_funcionario', nome_funcionario,
  'tem_biometria_dedo1', tem_dedo1,
  'tem_biometria_dedo2', tem_dedo2,
  'total_registros_ponto', total_registros,
  'status_biometrico', CASE WHEN tem_dedo1 OR
      tem_dedo2 THEN 'configurado' ELSE 'nao_configurado' END,
  'erro', FALSE);

  RETURN resultado;
END
$$

DELIMITER ;

--
-- Create view `vw_relatorio_pontos`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_relatorio_pontos
AS
SELECT
  COALESCE(`rp`.`id`, CONCAT('AUS_', `f`.`id`, '_', CURDATE())) AS `id`,
  `f`.`id` AS `funcionario_id`,
  `f`.`nome_completo` AS `nome_completo`,
  `f`.`matricula_empresa` AS `matricula_empresa`,
  `f`.`cpf` AS `cpf`,
  `rp`.`data_hora` AS `data_hora`,
  COALESCE(CAST(`rp`.`data_hora` AS date), CURDATE()) AS `data_registro`,
  COALESCE(CAST(`rp`.`data_hora` AS time), '00:00:00') AS `hora_registro`,
  COALESCE(`rp`.`tipo_registro`, 'ausente') AS `tipo_registro`,
  (CASE WHEN (`rp`.`tipo_registro` IS NULL) THEN 'Ausente' WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN 'Entrada ManhÃ£' WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN 'SaÃ­da AlmoÃ§o' WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN 'Entrada Tarde' WHEN (`rp`.`tipo_registro` = 'saida') THEN 'SaÃ­da' ELSE `rp`.`tipo_registro` END) AS `tipo_descricao`,
  COALESCE(`rp`.`metodo_registro`, 'nao_registrado') AS `metodo_registro`,
  (CASE WHEN (`rp`.`metodo_registro` IS NULL) THEN 'NÃ£o Registrado' WHEN (`rp`.`metodo_registro` = 'biometrico') THEN 'BiomÃ©trico' WHEN (`rp`.`metodo_registro` = 'manual') THEN 'Manual' ELSE `rp`.`metodo_registro` END) AS `metodo_descricao`,
  COALESCE(`f`.`setor`, `f`.`setor_obra`, 'NÃ£o informado') AS `setor`,
  `f`.`cargo` AS `cargo`,
  COALESCE(`f`.`empresa`, 'NÃ£o informado') AS `empresa`,
  `rp`.`qualidade_biometria` AS `qualidade_biometria`,
  (CASE WHEN (`rp`.`id` IS NULL) THEN 'FuncionÃ¡rio ausente - sem registros de ponto' ELSE `rp`.`observacoes` END) AS `observacoes`,
  `rp`.`ip_origem` AS `ip_origem`,
  COALESCE(`rp`.`criado_em`, `f`.`data_cadastro`) AS `criado_em`,
  `u`.`usuario` AS `criado_por_usuario`,
  (CASE WHEN (`rp`.`id` IS NULL) THEN 'Ausente' WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > '08:10:00')) THEN 'Atraso' WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > '13:10:00')) THEN 'Atraso' ELSE 'Pontual' END) AS `status_pontualidade`,
  (CASE WHEN (`rp`.`id` IS NULL) THEN 1 ELSE 0 END) AS `is_ausente`
FROM ((`funcionarios` `f`
  LEFT JOIN `registros_ponto` `rp`
    ON ((`f`.`id` = `rp`.`funcionario_id`)))
  LEFT JOIN `usuarios` `u`
    ON ((`rp`.`criado_por` = `u`.`id`)))
WHERE (`f`.`status_cadastro` = 'Ativo')
ORDER BY `f`.`nome_completo`, `rp`.`data_hora` DESC;

--
-- Create view `vw_horas_trabalhadas`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_horas_trabalhadas
AS
WITH `registros_diarios`
AS
(SELECT
    `f`.`id` AS `funcionario_id`,
    `f`.`nome_completo` AS `nome_completo`,
    `f`.`setor` AS `setor`,
    `f`.`cargo` AS `cargo`,
    CAST(`rp`.`data_hora` AS date) AS `data_registro`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN `rp`.`data_hora` END)) AS `entrada_manha`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN `rp`.`data_hora` END)) AS `saida_almoco`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN `rp`.`data_hora` END)) AS `entrada_tarde`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN `rp`.`data_hora` END)) AS `saida`
  FROM (`registros_ponto` `rp`
    JOIN `funcionarios` `f`
      ON ((`rp`.`funcionario_id` = `f`.`id`)))
  WHERE (`f`.`ativo` = 1)
  GROUP BY `f`.`id`,
           `f`.`nome_completo`,
           `f`.`setor`,
           `f`.`cargo`,
           CAST(`rp`.`data_hora` AS date))
SELECT
  `rd`.`funcionario_id` AS `funcionario_id`,
  `rd`.`nome_completo` AS `nome_completo`,
  `rd`.`setor` AS `setor`,
  `rd`.`cargo` AS `cargo`,
  `rd`.`data_registro` AS `data_registro`,
  `rd`.`entrada_manha` AS `entrada_manha`,
  `rd`.`saida_almoco` AS `saida_almoco`,
  `rd`.`entrada_tarde` AS `entrada_tarde`,
  `rd`.`saida` AS `saida`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NOT NULL) AND
      (`rd`.`saida_almoco` IS NOT NULL)) THEN TIMEDIFF(`rd`.`saida_almoco`, `rd`.`entrada_manha`) ELSE NULL END) AS `periodo_manha`,
  (CASE WHEN ((`rd`.`entrada_tarde` IS NOT NULL) AND
      (`rd`.`saida` IS NOT NULL)) THEN TIMEDIFF(`rd`.`saida`, `rd`.`entrada_tarde`) ELSE NULL END) AS `periodo_tarde`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NOT NULL) AND
      (`rd`.`saida_almoco` IS NOT NULL) AND
      (`rd`.`entrada_tarde` IS NOT NULL) AND
      (`rd`.`saida` IS NOT NULL)) THEN ADDTIME(TIMEDIFF(`rd`.`saida_almoco`, `rd`.`entrada_manha`), TIMEDIFF(`rd`.`saida`, `rd`.`entrada_tarde`)) ELSE NULL END) AS `total_horas_trabalhadas`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NULL) OR
      (`rd`.`saida_almoco` IS NULL) OR
      (`rd`.`entrada_tarde` IS NULL) OR
      (`rd`.`saida` IS NULL)) THEN 'Incompleto' ELSE 'Completo' END) AS `status_dia`
FROM `registros_diarios` `rd`;

--
-- Create view `vw_estatisticas_sistema`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_sistema
AS
SELECT
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (`funcionarios`.`status_cadastro` = 'Ativo')) AS `funcionarios_ativos`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (`funcionarios`.`status_cadastro` = 'Inativo')) AS `funcionarios_inativos`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (((`funcionarios`.`digital_dedo1` IS NOT NULL)
    OR (`funcionarios`.`digital_dedo2` IS NOT NULL))
    AND (`funcionarios`.`status_cadastro` = 'Ativo'))) AS `funcionarios_com_biometria`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE ((`funcionarios`.`digital_dedo1` IS NULL)
    AND (`funcionarios`.`digital_dedo2` IS NULL)
    AND (`funcionarios`.`status_cadastro` = 'Ativo'))) AS `funcionarios_sem_biometria`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE (CAST(`registros_ponto`.`data_hora` AS date) = CURDATE())) AS `registros_hoje`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE (YEARWEEK(`registros_ponto`.`data_hora`, 1) = YEARWEEK(NOW(), 1))) AS `registros_semana_atual`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE ((YEAR(`registros_ponto`.`data_hora`) = YEAR(NOW()))
    AND (MONTH(`registros_ponto`.`data_hora`) = MONTH(NOW())))) AS `registros_mes_atual`;

--
-- Create view `vw_estatisticas_pontos`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_pontos
AS
SELECT
  CAST(`rp`.`data_hora` AS date) AS `data_registro`,
  COUNT(0) AS `total_registros`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'biometrico') THEN 1 ELSE 0 END)) AS `registros_biometricos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'manual') THEN 1 ELSE 0 END)) AS `registros_manuais`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN 1 ELSE 0 END)) AS `entradas_manha`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN 1 ELSE 0 END)) AS `saidas_almoco`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN 1 ELSE 0 END)) AS `entradas_tarde`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN 1 ELSE 0 END)) AS `saidas`,
  SUM((CASE WHEN (((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > '08:10:00')) OR
      ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > '13:10:00'))) THEN 1 ELSE 0 END)) AS `atrasos`,
  COUNT(DISTINCT `rp`.`funcionario_id`) AS `funcionarios_registraram`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
WHERE (`f`.`ativo` = 1)
GROUP BY CAST(`rp`.`data_hora` AS date)
ORDER BY `data_registro` DESC;

--
-- Create view `vw_estatisticas_ponto_setor`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_ponto_setor
AS
SELECT
  `f`.`setor` AS `setor`,
  CAST(`rp`.`data_hora` AS date) AS `data_registro`,
  COUNT(0) AS `total_registros`,
  COUNT(DISTINCT `f`.`id`) AS `funcionarios_unicos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'biometrico') THEN 1 ELSE 0 END)) AS `registros_biometricos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'manual') THEN 1 ELSE 0 END)) AS `registros_manuais`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN 1 ELSE 0 END)) AS `entradas_manha`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN 1 ELSE 0 END)) AS `saidas_almoco`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN 1 ELSE 0 END)) AS `entradas_tarde`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN 1 ELSE 0 END)) AS `saidas`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > ADDTIME(`f`.`jornada_seg_qui_entrada`, SEC_TO_TIME((`f`.`tolerancia_ponto` * 60))))) THEN 1 ELSE 0 END)) AS `atrasos_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > ADDTIME(`f`.`jornada_intervalo_saida`, SEC_TO_TIME((`f`.`tolerancia_ponto` * 60))))) THEN 1 ELSE 0 END)) AS `atrasos_tarde`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
GROUP BY `f`.`setor`,
         CAST(`rp`.`data_hora` AS date);

--
-- Create view `vw_analise_pontualidade`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_analise_pontualidade
AS
SELECT
  `f`.`id` AS `funcionario_id`,
  `f`.`nome_completo` AS `nome_completo`,
  `f`.`setor` AS `setor`,
  `f`.`cargo` AS `cargo`,
  DATE_FORMAT(`rp`.`data_hora`, '%Y-%m') AS `mes_ano`,
  COUNT(DISTINCT CAST(`rp`.`data_hora` AS date)) AS `dias_trabalhados`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) <= '08:10:00')) THEN 1 ELSE 0 END)) AS `entradas_pontuais_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > '08:10:00')) THEN 1 ELSE 0 END)) AS `atrasos_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) <= '13:10:00')) THEN 1 ELSE 0 END)) AS `entradas_pontuais_tarde`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > '13:10:00')) THEN 1 ELSE 0 END)) AS `atrasos_tarde`,
  ROUND(((SUM((CASE WHEN ((`rp`.`tipo_registro` IN ('entrada_manha', 'entrada_tarde')) AND
      (((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) <= '08:10:00')) OR
      ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) <= '13:10:00')))) THEN 1 ELSE 0 END)) * 100.0) / NULLIF(SUM((CASE WHEN (`rp`.`tipo_registro` IN ('entrada_manha', 'entrada_tarde')) THEN 1 ELSE 0 END)), 0)), 2) AS `percentual_pontualidade`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
WHERE (`f`.`ativo` = 1)
GROUP BY `f`.`id`,
         `f`.`nome_completo`,
         `f`.`setor`,
         `f`.`cargo`,
         DATE_FORMAT(`rp`.`data_hora`, '%Y-%m');

--
-- Create table `permissoes`
--
CREATE TABLE IF NOT EXISTS permissoes (
  usuario_id int UNSIGNED NOT NULL,
  nivel_acesso enum ('admin', 'usuario') DEFAULT 'usuario',
  data_atribuicao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (usuario_id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Permissões e níveis de acesso dos usuários',
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE permissoes
ADD CONSTRAINT permissoes_ibfk_1 FOREIGN KEY (usuario_id)
REFERENCES usuarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create table `logs_sistema`
--
CREATE TABLE IF NOT EXISTS logs_sistema (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  usuario_id int UNSIGNED DEFAULT NULL,
  acao varchar(100) NOT NULL,
  tabela_afetada varchar(50) DEFAULT NULL,
  registro_id int UNSIGNED DEFAULT NULL,
  detalhes json DEFAULT NULL,
  ip_origem varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  data_hora timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 88,
AVG_ROW_LENGTH = 1489,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Logs de auditoria para segurança do sistema',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_acao_data` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_acao_data (acao, data_hora);

--
-- Create index `idx_data_hora` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_data_hora (data_hora);

--
-- Create index `idx_usuario_data` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_usuario_data (usuario_id, data_hora);

--
-- Create foreign key
--
ALTER TABLE logs_sistema
ADD CONSTRAINT logs_sistema_ibfk_1 FOREIGN KEY (usuario_id)
REFERENCES usuarios (id) ON DELETE SET NULL ON UPDATE CASCADE;

DELIMITER $$

--
-- Create procedure `LimparLogsAntigos`
--
CREATE
DEFINER = 'cavalcrod'@'%'
PROCEDURE IF NOT EXISTS LimparLogsAntigos (IN dias_para_manter int UNSIGNED)
BEGIN
  DECLARE registros_removidos int DEFAULT 0;

  -- Validar parâmetro
  IF dias_para_manter < 7 THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Mínimo de 7 dias deve ser mantido nos logs';
  END IF;

  DELETE
    FROM logs_sistema
  WHERE data_hora < DATE_SUB(NOW(), INTERVAL dias_para_manter DAY);

  SET registros_removidos = ROW_COUNT();

  SELECT
    registros_removidos AS registros_removidos,
    dias_para_manter AS dias_mantidos,
    NOW() AS data_limpeza;

  -- Log da limpeza
  INSERT INTO logs_sistema (acao, tabela_afetada, detalhes, data_hora)
    VALUES ('LIMPEZA_LOGS', 'logs_sistema', JSON_OBJECT('registros_removidos', registros_removidos, 'dias_mantidos', dias_para_manter), NOW());
END
$$

DELIMITER ;

--
-- Create table `tentativas_biometria`
--
CREATE TABLE IF NOT EXISTS tentativas_biometria (
  id int NOT NULL AUTO_INCREMENT,
  template_hash varchar(64) DEFAULT NULL,
  timestamp datetime DEFAULT CURRENT_TIMESTAMP,
  device_vendor_id varchar(10) DEFAULT NULL,
  device_product_id varchar(10) DEFAULT NULL,
  success tinyint(1) DEFAULT 0,
  funcionario_id int DEFAULT NULL,
  ip_address varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_funcionario` on table `tentativas_biometria`
--
ALTER TABLE tentativas_biometria
ADD INDEX idx_funcionario (funcionario_id);

--
-- Create index `idx_success` on table `tentativas_biometria`
--
ALTER TABLE tentativas_biometria
ADD INDEX idx_success (success);

--
-- Create index `idx_timestamp` on table `tentativas_biometria`
--
ALTER TABLE tentativas_biometria
ADD INDEX idx_timestamp (timestamp);

--
-- Create table `logs_seguranca`
--
CREATE TABLE IF NOT EXISTS logs_seguranca (
  id int NOT NULL AUTO_INCREMENT,
  tipo_evento varchar(50) NOT NULL,
  funcionario_id int DEFAULT NULL,
  detalhes json DEFAULT NULL,
  timestamp datetime DEFAULT CURRENT_TIMESTAMP,
  ip_address varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  nivel_risco enum ('baixo', 'medio', 'alto') DEFAULT 'baixo',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_funcionario_timestamp` on table `logs_seguranca`
--
ALTER TABLE logs_seguranca
ADD INDEX idx_funcionario_timestamp (funcionario_id, timestamp);

--
-- Create index `idx_nivel_risco` on table `logs_seguranca`
--
ALTER TABLE logs_seguranca
ADD INDEX idx_nivel_risco (nivel_risco);

--
-- Create index `idx_tipo_timestamp` on table `logs_seguranca`
--
ALTER TABLE logs_seguranca
ADD INDEX idx_tipo_timestamp (tipo_evento, timestamp);

--
-- Create table `logs_biometria`
--
CREATE TABLE IF NOT EXISTS logs_biometria (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int DEFAULT NULL,
  timestamp datetime DEFAULT CURRENT_TIMESTAMP,
  similarity_score decimal(5, 4) DEFAULT 0.0000,
  device_info json DEFAULT NULL,
  status enum ('success', 'failed') DEFAULT 'failed',
  ip_address varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_funcionario_timestamp` on table `logs_biometria`
--
ALTER TABLE logs_biometria
ADD INDEX idx_funcionario_timestamp (funcionario_id, timestamp);

--
-- Create index `idx_status_timestamp` on table `logs_biometria`
--
ALTER TABLE logs_biometria
ADD INDEX idx_status_timestamp (status, timestamp);

DELIMITER $$

--
-- Create procedure `sp_cleanup_old_logs`
--
CREATE
DEFINER = 'cavalcrod'@'%'
PROCEDURE IF NOT EXISTS sp_cleanup_old_logs ()
BEGIN
  DECLARE EXIT HANDLER FOR SQLEXCEPTION
  BEGIN
    ROLLBACK;
    RESIGNAL;
  END;

  START TRANSACTION;

    DELETE
      FROM logs_biometria
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY);
    DELETE
      FROM logs_seguranca
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 180 DAY)
      AND nivel_risco != 'alto';
    DELETE
      FROM tentativas_biometria
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY);

  COMMIT;
END
$$

DELIMITER ;

--
-- Create view `vw_estatisticas_biometria`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_biometria
AS
SELECT
  CAST(`lb`.`timestamp` AS date) AS `data`,
  COUNT(0) AS `total_tentativas`,
  SUM((CASE WHEN (`lb`.`status` = 'success') THEN 1 ELSE 0 END)) AS `sucessos`,
  SUM((CASE WHEN (`lb`.`status` = 'failed') THEN 1 ELSE 0 END)) AS `falhas`,
  ROUND(AVG(`lb`.`similarity_score`), 4) AS `score_medio`,
  COUNT(DISTINCT `lb`.`funcionario_id`) AS `funcionarios_unicos`
FROM `logs_biometria` `lb`
WHERE (`lb`.`timestamp` >= (CURDATE() - INTERVAL 30 DAY))
GROUP BY CAST(`lb`.`timestamp` AS date)
ORDER BY `data` DESC;

--
-- Create table `log_exclusao_empresas`
--
CREATE TABLE IF NOT EXISTS log_exclusao_empresas (
  id int NOT NULL AUTO_INCREMENT,
  empresa_id int NOT NULL,
  razao_social varchar(200) NOT NULL,
  cnpj varchar(18) NOT NULL,
  usuario_id int NOT NULL,
  data_exclusao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  motivo text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `dispositivos_biometricos`
--
CREATE TABLE IF NOT EXISTS dispositivos_biometricos (
  id int NOT NULL AUTO_INCREMENT,
  nome_dispositivo varchar(100) NOT NULL,
  fabricante varchar(50) NOT NULL,
  device_id varchar(255) NOT NULL,
  vendor_id varchar(10) DEFAULT NULL,
  product_id varchar(10) DEFAULT NULL,
  serial_number varchar(100) DEFAULT NULL,
  versao_driver varchar(50) DEFAULT NULL,
  porta_usb varchar(20) DEFAULT NULL,
  status_dispositivo enum ('ativo', 'inativo', 'erro', 'manutencao') NOT NULL DEFAULT 'ativo',
  data_registro timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  data_ultima_deteccao timestamp NULL DEFAULT NULL,
  data_desinstalacao timestamp NULL DEFAULT NULL,
  configuracao_json json DEFAULT NULL,
  observacoes text DEFAULT NULL,
  ativo tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `uk_device_id` on table `dispositivos_biometricos`
--
ALTER TABLE dispositivos_biometricos
ADD UNIQUE INDEX uk_device_id (device_id);

--
-- Create table `configuracoes_sistema`
--
CREATE TABLE IF NOT EXISTS configuracoes_sistema (
  id int NOT NULL AUTO_INCREMENT,
  chave varchar(100) NOT NULL,
  valor text DEFAULT NULL,
  descricao text DEFAULT NULL,
  tipo enum ('string', 'integer', 'boolean', 'time', 'json') DEFAULT 'string',
  categoria varchar(50) DEFAULT 'geral',
  criado_em datetime DEFAULT CURRENT_TIMESTAMP,
  atualizado_em datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  editavel tinyint(1) DEFAULT 1,
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 34,
AVG_ROW_LENGTH = 528,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `chave` on table `configuracoes_sistema`
--
ALTER TABLE configuracoes_sistema
ADD UNIQUE INDEX chave (chave);

--
-- Create index `idx_categoria` on table `configuracoes_sistema`
--
ALTER TABLE configuracoes_sistema
ADD INDEX idx_categoria (categoria);

--
-- Create index `idx_chave` on table `configuracoes_sistema`
--
ALTER TABLE configuracoes_sistema
ADD INDEX idx_chave (chave);

--
-- Create table `cad_empresas`
--
CREATE TABLE IF NOT EXISTS cad_empresas (
  id int NOT NULL AUTO_INCREMENT,
  nome_empresa varchar(255) DEFAULT NULL,
  logotipo longblob DEFAULT NULL,
  logotipo_mime_type varchar(100) DEFAULT NULL,
  regras_especificas text DEFAULT NULL,
  tolerancia_atraso int DEFAULT 10,
  tolerancia_saida_antecipada int DEFAULT 10,
  jornada_trabalho_padrao time DEFAULT '08:00:00',
  intervalo_almoco_inicio time DEFAULT '12:00:00',
  intervalo_almoco_fim time DEFAULT '13:00:00',
  data_criacao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  ativa tinyint(1) DEFAULT 1,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `backup_jornada_funcionarios`
--
CREATE TABLE IF NOT EXISTS backup_jornada_funcionarios (
  id int UNSIGNED NOT NULL DEFAULT 0,
  nome_completo varchar(100) NOT NULL,
  jornada_seg_qui_entrada time DEFAULT NULL,
  jornada_seg_qui_saida time DEFAULT NULL,
  jornada_sex_entrada time DEFAULT NULL,
  jornada_sex_saida time DEFAULT NULL,
  jornada_intervalo_entrada time DEFAULT NULL,
  jornada_intervalo_saida time DEFAULT NULL,
  tolerancia_ponto int UNSIGNED NOT NULL DEFAULT 5 COMMENT 'Tolerância em minutos',
  inicio_expediente time DEFAULT '07:00:00' COMMENT 'HorÃ¡rio programado de entrada',
  horario_saida_seg_qui time DEFAULT '17:00:00' COMMENT 'HorÃ¡rio de saÃ­da segunda a quinta',
  horario_saida_sexta time DEFAULT '16:30:00' COMMENT 'HorÃ¡rio de saÃ­da na sexta-feira',
  periodo_almoco_inicio time DEFAULT '11:00:00' COMMENT 'InÃ­cio da janela de almoÃ§o',
  periodo_almoco_fim time DEFAULT '14:00:00' COMMENT 'Fim da janela de almoÃ§o',
  duracao_minima_almoco int DEFAULT 60 COMMENT 'DuraÃ§Ã£o mÃ­nima do almoÃ§o em minutos',
  tolerancia_entrada int DEFAULT 15 COMMENT 'TolerÃ¢ncia para entrada em minutos',
  permite_banco_horas_positivo tinyint(1) DEFAULT 0 COMMENT 'Permite acumular horas extras como crÃ©dito',
  data_backup datetime NOT NULL
)
ENGINE = INNODB,
AVG_ROW_LENGTH = 5461,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

-- 
-- Dumping data for table usuarios
--
INSERT INTO usuarios VALUES
(2, 'teste', NULL, NULL, 1, NULL, 'pbkdf2:sha256:600000$OmXNQh5XtS7BNSri$e174ced16e67d04869383d125a0665498d51b7bc82b6bbc0a6e00d20cb5bc2c0', 'usuario', '2025-06-06 10:56:33', NULL),
(3, 'status', 'Usuário Status do Sistema', '<EMAIL>', 1, NULL, 'pbkdf2:sha256:600000$x5KZTJwcBg7xA6HP$b084242370e60ff03f0e061dcb5c9f6170383dc014dd06fcab8e9ac1bed748d1', 'usuario', '2025-06-08 19:20:11', NULL),
(5, 'cavalcrod', 'Quality Control Manager', '<EMAIL>', 1, NULL, 'pbkdf2:sha256:600000$G5lErTBgGugN2iXS$fdbebe2c4fd4f939c7d9161136d74713e4e89a22d6898dfd87142cf97a5a3a54', 'usuario', '2025-06-10 12:49:17', NULL),
(6, ' cavalcrod ', NULL, NULL, 1, NULL, ' @Ric6109 ', 'usuario', '2025-06-10 14:48:51', NULL),
(7, 'suelen', NULL, NULL, 1, NULL, 'pbkdf2:sha256:600000$uCl5CqhWae8CnC2d$f5833302654feaa517fd1da3bc54e056b34316ec9db8430d48060dc2895b6bac', 'usuario', '2025-06-16 18:18:47', NULL),
(9, 'admin', 'Admin Sistema', '<EMAIL>', 1, NULL, 'pbkdf2:sha256:600000$aopQv5YR3yfljlF0$2f17fe06bf7c03d7fb57cacf69183814f0daa09359c69f9b013ce9cd7cd00787', 'admin', '2025-06-25 21:56:52', NULL);

-- Table controle_ponto.empresas does not contain any data (it is empty)

-- Table controle_ponto.jornadas_trabalho does not contain any data (it is empty)

-- Table controle_ponto.clientes does not contain any data (it is empty)

-- 
-- Dumping data for table funcionarios
--
INSERT INTO funcionarios(id, empresa_id, cliente_atual_id, horario_trabalho_id, nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade, ctps_numero, ctps_serie_uf, pis_pasep, endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado, telefone1, telefone2, email, cargo, setor, setor_obra, empresa, matricula_empresa, data_admissao, tipo_contrato, jornada_seg_qui_entrada, jornada_seg_qui_saida, jornada_sex_entrada, jornada_sex_saida, jornada_intervalo_entrada, jornada_intervalo_saida, digital_dedo1, digital_dedo2, foto_3x4, foto_url, nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, ativo, status_cadastro, data_cadastro, data_atualizacao, biometria_qualidade_1, biometria_qualidade_2, biometria_data_cadastro, salario_base, tipo_pagamento, valor_hora, valor_hora_extra, percentual_hora_extra, vale_transporte, vale_alimentacao, outros_beneficios, desconto_inss, desconto_irrf, observacoes_pagamento, data_ultima_alteracao_salario, biometria_qualidade, ultimo_login, tentativas_biometria, status_biometria, status, epi_obrigatorio_json, epi_treinamento_data, epi_responsavel, epi_observacoes, epi_termo_assinado, inicio_expediente, horario_saida_seg_qui, horario_saida_sexta, periodo_almoco_inicio, periodo_almoco_fim, duracao_minima_almoco, tolerancia_entrada, permite_banco_horas_positivo, data_atualizacao_jornada, horas_trabalho_obrigatorias, jornada_trabalho_id) VALUES
(1, 1, NULL, 1, 'RICHARDSON CARDOSO RODRIGUES', '711.256.042-04', '31.799.841', '1981-03-20', 'M', 'Casado', 'Brasileiro', '0000000', '0000000000', '000.00000.00-0', 'RUA GERMÂNIO', 'VILA DA PRATA', 'MANAUS', '69030-685', 'AM', '(92) 99245-5278', NULL, '<EMAIL>', 'ANALISTA', 'Administrativo', 'TI', 'Empresa Principal', '0001', '2025-01-01', 'PJ', '08:00:00', '18:00:00', '08:00:00', '18:00:00', '13:00:00', '14:00:00', x'3038313632373843394342303635343632344641454343434136383330373339323635303037314231394539303746413042413532363741304339313146373331324534314639323232423932354432303738463135353530363036314538313043363332354142313633333139313231453538313735323146354230443734314441313144443131353034303935373043374430343042313230443234334631374538314238353045424430373838323546303145324430394441313430313139363931433132304137343034384232333135304137303034393730393434323537443234464430394631313934353045344630464139313345433035383831373342323446333136363832303645304343433232383531393341313833333131303930463645323543313038354130443334304142423134434230343835303831343046393931313932323534463234344131393732314537463037324531324235313644363038444631364330313446443138393932363645323233383144374630414445314238463145463031373539303442443044443930394541323036323230443131313842313938363041433730383238313242323138343930423830314532313143323132333038304530453043303831453338313038453145323331303135304646443231374431303239314230313046453430363544', x'3543383044374432433732363831363142314544443839424637333244393646313444393235314131324644304541443046313132364241313938363145313030424143313631363042443931304434313739453136373231333241304131423135343931324142323136363034334432343631304546383130353131323132304542453035374330344632313745373131303931433145314332423038333330394236304442373136364331324137304339393136323531423731323138433235314230453243304641373041353431334337313935463144314432314236323245353134363630433139313346323144453330443537303437333034393431324437303741443138424331463746304439313136333631443444303534343236384531354138303936433230363132373046314343423232463531423335304139343130363031344443314237353230383730383731314239313039313031364632304642413141444432304542303939363041363031384646314346313230303530434235303635373235304230373136304439383131343130394238304243393134314331434546313833463043454532344130323231323143343431453336323534383143434432323239303645453043443131334439323730443036324430414338313731443046453631433245313046423142343831394436', 'fotos_funcionarios/funcionario_1_20250617T074603.jpg', NULL, 'Funcionario', 'Diurno', 10, 1, 1, 1, 'Ativo', '2025-06-05 16:27:38', '2025-06-17 07:46:03', NULL, NULL, NULL, NULL, 'Mensal', NULL, NULL, 50.00, NULL, NULL, NULL, 1, 1, NULL, NULL, 0, NULL, 0, 'inativo', 'ativo', NULL, NULL, '', '', 0, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-06-25 13:03:47', 8.00, NULL),
(8, NULL, NULL, NULL, 'SUELEN OLIVEIRA DOS SANTOS', '702.965.122-73', '32.320.213', '2000-03-05', 'F', 'Solteiro', 'Brasileira', '9244846', '0050', '201.86947.50-4', 'RUA MARIA SEGADILHA, N 93', 'LÍRIO DO VALE', 'MANAUS', '69038-220', 'AM', '(92) 99332-4049', NULL, NULL, 'ANALISTA ADMINISTRATIVO', NULL, 'ADMINISTRATIVO', 'Empresa Principal', '0004', '2025-04-01', 'CLT', '07:30:00', '17:30:00', '07:30:00', '16:30:00', '12:00:00', '13:00:00', NULL, NULL, 'fotos_funcionarios/funcionario_8_20250617T095959.png', NULL, 'Supervisao', 'Diurno', 10, 1, 1, 1, 'Ativo', '2025-06-16 18:36:19', '2025-06-17 09:59:59', NULL, NULL, NULL, NULL, 'Mensal', NULL, NULL, 50.00, NULL, NULL, NULL, 1, 1, NULL, NULL, 0, NULL, 0, 'inativo', 'ativo', NULL, NULL, NULL, NULL, 0, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-06-25 13:03:47', 8.00, NULL),
(9, NULL, NULL, NULL, 'TESTE 2 TURNO', '744.589.652-14', '12.354.785', '1980-01-01', 'F', 'Solteiro', 'Brasileira', '1234567', '0000000000', '000.00000.00-0', 'RUA 17', 'SANTO AGOSTINHO', 'MANAUS', '69036-850', 'AM', '(92) 99248-7456', NULL, NULL, 'SOLDADOR', NULL, 'OBRA NORTE', 'Empresa Principal', '0005', '2025-01-01', 'CLT', '07:00:00', '17:00:00', '07:00:00', '16:00:00', '12:00:00', '13:00:00', NULL, NULL, 'fotos_funcionarios/funcionario_9_20250618T091757.png', NULL, 'Funcionario', 'Noturno', 10, 1, 1, 1, 'Ativo', '2025-06-18 09:17:57', '2025-06-18 09:18:15', NULL, NULL, NULL, NULL, 'Mensal', NULL, NULL, 50.00, NULL, NULL, NULL, 1, 1, NULL, NULL, 0, NULL, 0, 'inativo', 'ativo', NULL, NULL, NULL, NULL, 0, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-06-25 13:03:47', 8.00, NULL);

-- Table controle_ponto.tentativas_biometria does not contain any data (it is empty)

-- Table controle_ponto.registros_ponto does not contain any data (it is empty)

-- 
-- Dumping data for table permissoes
--
INSERT INTO permissoes(usuario_id, nivel_acesso, data_atribuicao) VALUES
(2, 'usuario', '2025-06-06 10:56:33'),
(3, 'usuario', '2025-06-08 19:20:33'),
(6, 'admin', '2025-06-10 14:49:26'),
(7, 'usuario', '2025-06-16 18:18:47'),
(9, 'admin', '2025-06-25 22:21:37');

-- Table controle_ponto.log_exclusao_empresas does not contain any data (it is empty)

-- 
-- Dumping data for table logs_sistema
--
INSERT INTO logs_sistema(id, usuario_id, acao, tabela_afetada, registro_id, detalhes, ip_origem, user_agent, data_hora) VALUES
(1, NULL, 'INICIALIZACAO_BANCO', 'sistema', NULL, '{"status": "sucesso", "versao": "1.2", "data_criacao": "2025-06-05 20:06:48.000000"}', NULL, NULL, '2025-06-05 16:06:48'),
(2, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 1, '{"data_hora": "2025-06-05 22:29:09.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:29:09'),
(3, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 1, '{"data_hora": "2025-06-05 08:05:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(4, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 2, '{"data_hora": "2025-06-05 17:10:00.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(5, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 3, '{"data_hora": "2025-06-05 08:05:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 2, "metodo_registro": "manual", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(6, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 4, '{"data_hora": "2025-06-05 17:10:00.000000", "tipo_registro": "saida", "funcionario_id": 2, "metodo_registro": "biometrico", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(7, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 5, '{"data_hora": "2025-06-04 07:58:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(8, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 6, '{"data_hora": "2025-06-04 07:58:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 2, "metodo_registro": "biometrico", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(9, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 7, '{"data_hora": "2025-06-06 01:20:04.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:20:04'),
(10, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 8, '{"data_hora": "2025-06-06 01:22:30.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:22:30'),
(11, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 9, '{"data_hora": "2025-06-06 01:23:22.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:23:22'),
(12, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 10, '{"data_hora": "2025-06-06 09:02:37.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 09:02:37'),
(13, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 11, '{"data_hora": "2025-06-06 14:13:20.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 3, "metodo_registro": "manual", "nome_funcionario": "Maria Oliveira Costa", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 14:13:20'),
(14, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 12, '{"data_hora": "2025-06-07 19:40:10.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-07 19:40:10'),
(15, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 13, '{"data_hora": "2025-06-08 18:00:48.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-08 18:00:48'),
(16, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 14, '{"data_hora": "2025-06-09 00:04:24.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": 85}', NULL, NULL, '2025-06-09 00:04:24'),
(17, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 15, '{"data_hora": "2025-06-09 00:04:47.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": 85}', NULL, NULL, '2025-06-09 00:04:47'),
(18, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 16, '{"data_hora": "2025-06-09 00:07:26.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": 85}', NULL, NULL, '2025-06-09 00:07:26'),
(19, NULL, 'CRIACAO_TABELA_CONFIGURACOES', 'configuracoes_sistema', NULL, '{"operacao": "correcao_critica", "responsavel": "Claude AI Assistant", "tabela_criada": "configuracoes_sistema", "documento_referencia": "Correção de Falhas do Sistema.markdown", "configuracoes_inseridas": 15}', NULL, NULL, '2025-06-09 16:29:24'),
(20, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T22:54:43.035102", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "c1595090815e454e", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 22:54:43'),
(21, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:11:56.757323", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "bccdffaffc286b31", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:11:57'),
(22, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:16:03.609032", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "b21d38e74d319904", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:16:04'),
(23, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:27:36.807910", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "0eb7dfb22da9dc81", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:27:37'),
(24, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:54:05.768789", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "eaa985a0b05e6d8c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:54:06'),
(25, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:55:03.681970", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/configuracoes/", "ip_origem": "***********", "session_id": "eaa985a0b05e6d8c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:55:04'),
(26, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:56:38.642329", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "35897cc7bc0b505a", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:56:39'),
(27, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:56:45.825609", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/funcionarios/", "ip_origem": "***********", "session_id": "35897cc7bc0b505a", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:56:46'),
(28, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:57:34.806182", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "35897cc7bc0b505a", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:57:35'),
(29, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T00:14:01.872003", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "d98a5642d8c60b0c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 00:14:02'),
(30, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T00:18:35.866717", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/funcionarios/", "ip_origem": "***********", "session_id": "d98a5642d8c60b0c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 00:18:36'),
(31, NULL, '[LOGIN:INFO] Tentativa de login: status', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T00:18:48.950638", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "d98a5642d8c60b0c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "status"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 00:18:49'),
(32, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T08:52:37.501327", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "8f96b1ea1d8f70f0", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 08:52:38'),
(33, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T08:53:41.277687", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/funcionarios/", "ip_origem": "************", "session_id": "8f96b1ea1d8f70f0", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 08:53:41'),
(34, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:00:25.913665", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "1cde18f92f10a7e0", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:00:26'),
(35, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:11:18.258902", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:11:18'),
(36, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:27:44.012921", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:27:44'),
(37, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:27:44.946649", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:27:45'),
(38, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:30:18.282472", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:30:18'),
(39, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:30:19.110784", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:30:19'),
(40, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:32:40.344872", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:32:40'),
(41, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:36:42.178963", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:36:42'),
(42, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:37:29.274996", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:37:29'),
(43, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:38:52.184760", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:38:52'),
(44, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:39:49.266399", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:39:49'),
(45, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:40:51.249143", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:40:51'),
(46, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:41:16.921365", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:41:17'),
(47, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:48:30.584236", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:48:31'),
(48, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:54:06.616742", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "595c40ab92027e1c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:54:07'),
(49, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:55:02.441801", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "595c40ab92027e1c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:55:02'),
(50, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:03:08.824593", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "7e2cb865c3a31b14", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:03:09'),
(51, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:06:51.146595", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "7e2cb865c3a31b14", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:06:51'),
(52, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:08:49.517407", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "de4a1d3357ef8065", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:08:50'),
(53, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:09:00.389321", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": "admin", "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "de4a1d3357ef8065", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:09:00'),
(54, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:11:23.115108", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "ec15c5627341d603", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:11:23'),
(55, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:11:45.553938", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "ec15c5627341d603", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:11:46'),
(56, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:18:50.672069", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "b803533a43808d88", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:18:51'),
(57, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:20:36.909630", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "60df3a52ab109440", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:20:37'),
(58, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:32:09.372911", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "9aa91e335bae0f50", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:32:09'),
(59, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 17, '{"data_hora": "2025-06-10 12:33:43.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-10 12:33:43'),
(60, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 18, '{"data_hora": "2025-06-10 14:43:21.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-10 14:43:21'),
(61, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 19, '{"data_hora": "2025-06-10 19:06:17.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-10 19:06:17'),
(62, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 20, '{"data_hora": "2025-06-11 11:16:19.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-11 11:16:19'),
(63, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 21, '{"data_hora": "2025-06-11 16:03:56.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-11 16:03:56'),
(64, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 22, '{"data_hora": "2025-06-13 11:34:04.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-13 11:34:04'),
(65, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 23, '{"data_hora": "2025-06-13 11:35:48.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-13 11:35:48'),
(66, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 24, '{"data_hora": "2025-06-13 12:36:36.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-13 12:36:36'),
(67, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 25, '{"data_hora": "2025-06-16 09:21:59.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 09:21:59'),
(68, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 26, '{"data_hora": "2025-06-16 10:17:33.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 10:17:33'),
(69, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 27, '{"data_hora": "2025-06-16 14:17:39.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 6, "metodo_registro": "manual", "nome_funcionario": "FUNCIONARIO TESTE", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 14:17:39'),
(70, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 28, '{"data_hora": "2025-06-16 14:18:55.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 14:18:55'),
(71, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 29, '{"data_hora": "2025-06-16 14:20:28.000000", "tipo_registro": "saida_almoco", "funcionario_id": 6, "metodo_registro": "manual", "nome_funcionario": "FUNCIONARIO TESTE", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 14:20:28'),
(72, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 30, '{"data_hora": "2025-06-16 14:20:43.000000", "tipo_registro": "entrada_manha", "funcionario_id": 6, "metodo_registro": "manual", "nome_funcionario": "FUNCIONARIO TESTE", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 14:20:43'),
(73, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 34, '{"data_hora": "2025-06-16 15:46:46.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 15:46:46'),
(74, NULL, 'CORRECAO', NULL, NULL, '{"autor": "sistema", "arquivo": "app_registro_ponto.py", "mensagem": "Foi corrigida a consulta SQL em app_registro_ponto.py para priorizar o campo setor_obra em vez de setor.", "data_correcao": "2025-06-16 21:07:46"}', NULL, NULL, '2025-06-16 21:07:46'),
(75, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 40, '{"data_hora": "2025-06-17 07:52:53.000000", "tipo_registro": "entrada_manha", "funcionario_id": 8, "metodo_registro": "manual", "nome_funcionario": "SUELEN OLIVEIRA DOS SANTOS", "qualidade_biometria": null}', NULL, NULL, '2025-06-17 07:52:53'),
(76, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 42, '{"data_hora": "2025-06-17 08:04:15.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-17 08:04:15'),
(77, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 43, '{"data_hora": "2025-06-17 13:00:54.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-17 13:00:54'),
(78, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 45, '{"data_hora": "2025-06-17 14:05:24.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-17 14:05:24'),
(79, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 47, '{"data_hora": "2025-06-17 18:06:19.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-17 18:06:19'),
(80, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 48, '{"data_hora": "2025-06-18 08:12:38.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-18 08:12:38'),
(81, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 49, '{"data_hora": "2025-06-18 08:28:44.000000", "tipo_registro": "entrada_manha", "funcionario_id": 8, "metodo_registro": "manual", "nome_funcionario": "SUELEN OLIVEIRA DOS SANTOS", "qualidade_biometria": null}', NULL, NULL, '2025-06-18 08:28:44'),
(82, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 50, '{"data_hora": "2025-06-18 13:02:16.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-18 13:02:16'),
(83, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 51, '{"data_hora": "2025-06-18 14:19:53.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-18 14:19:53'),
(84, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 53, '{"data_hora": "2025-06-18 19:07:35.000000", "tipo_registro": "saida", "funcionario_id": 8, "metodo_registro": "manual", "nome_funcionario": "SUELEN OLIVEIRA DOS SANTOS", "qualidade_biometria": null}', NULL, NULL, '2025-06-18 19:07:35'),
(85, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 54, '{"data_hora": "2025-06-19 07:42:27.000000", "tipo_registro": "entrada_manha", "funcionario_id": 8, "metodo_registro": "manual", "nome_funcionario": "SUELEN OLIVEIRA DOS SANTOS", "qualidade_biometria": null}', NULL, NULL, '2025-06-19 07:42:27'),
(86, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 55, '{"data_hora": "2025-06-19 08:56:42.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-19 08:56:42'),
(87, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 56, '{"data_hora": "2025-06-19 13:15:13.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-19 13:15:13');

-- 
-- Dumping data for table logs_seguranca
--
INSERT INTO logs_seguranca(id, tipo_evento, funcionario_id, detalhes, timestamp, ip_address, user_agent, nivel_risco) VALUES
(1, 'database_update', NULL, '{"version": "2.0", "features": ["WebUSB API direct hardware access", "ZK4500 biometric device support", "Real-time template comparison", "Automatic attendance type detection", "Enhanced security audit logs"], "description": "Sistema biométrico WebUSB integrado"}', '2025-06-09 01:40:29', NULL, NULL, 'baixo');

-- Table controle_ponto.logs_biometria does not contain any data (it is empty)

-- Table controle_ponto.horarios_trabalho does not contain any data (it is empty)

-- Table controle_ponto.funcionario_cliente_alocacao does not contain any data (it is empty)

-- Table controle_ponto.epis does not contain any data (it is empty)

-- 
-- Dumping data for table dispositivos_biometricos
--
INSERT INTO dispositivos_biometricos(id, nome_dispositivo, fabricante, device_id, vendor_id, product_id, serial_number, versao_driver, porta_usb, status_dispositivo, data_registro, data_ultima_deteccao, data_desinstalacao, configuracao_json, observacoes, ativo) VALUES
(1, 'Dispositivo Teste RLPONTO', 'Genérico', 'test_device_001', NULL, NULL, NULL, NULL, NULL, 'ativo', '2025-06-11 11:39:21', NULL, NULL, NULL, 'Dispositivo de teste criado automaticamente durante instalação', 1);

-- 
-- Dumping data for table configuracoes_sistema
--
INSERT INTO configuracoes_sistema(id, chave, valor, descricao, tipo, categoria, criado_em, atualizado_em, editavel, data_atualizacao) VALUES
(1, 'biometric_threshold', '0.7', 'Limite mínimo de similaridade biométrica', 'string', 'biometria', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(2, 'security_enabled', 'true', 'Habilitar verificações de segurança', 'boolean', 'seguranca', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(3, 'max_failed_attempts', '5', 'Máximo de tentativas falhadas por hora', 'integer', 'seguranca', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(4, 'device_whitelist', '["1b55:4500"]', 'Lista de dispositivos autorizados', 'json', 'hardware', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(5, 'attendance_tolerance_minutes', '15', 'Tolerância em minutos para pontualidade', 'integer', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(6, 'morning_start', '07:00', 'Início do período matutino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(7, 'morning_end', '09:30', 'Fim do período matutino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(8, 'lunch_out_start', '11:30', 'Início da saída para almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(9, 'lunch_out_end', '13:30', 'Fim da saída para almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(10, 'lunch_return_start', '13:30', 'Início da volta do almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(11, 'lunch_return_end', '15:00', 'Fim da volta do almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(12, 'evening_start', '17:00', 'Início do período vespertino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(13, 'evening_end', '19:00', 'Fim do período vespertino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(14, 'tema_sistema', 'claro', 'Tema do sistema', 'string', 'interface', '2025-06-09 11:03:09', '2025-06-09 23:58:37', 1, '2025-06-09 23:58:37'),
(15, 'mostrar_fotos_funcionarios', 'true', 'Mostrar fotos', 'boolean', 'interface', '2025-06-09 11:03:09', '2025-06-09 23:58:36', 1, '2025-06-09 23:58:36'),
(16, 'registros_por_pagina', '50', 'Registros por página', 'integer', 'interface', '2025-06-09 11:03:09', '2025-06-09 23:58:36', 1, '2025-06-09 23:58:36'),
(17, 'versao_sistema', '1.0', 'Versão', 'string', 'tecnico', '2025-06-09 11:03:09', '2025-06-09 11:03:09', 0, '2025-06-09 23:25:05'),
(18, 'formato_data', 'dd/mm/yyyy', 'Formato de data', 'string', 'interface', '2025-06-09 12:43:07', '2025-06-09 23:58:36', 1, '2025-06-09 23:58:36'),
(20, 'fuso_horario', 'America/Manaus', 'Fuso horário padrão do sistema', 'string', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(21, 'timeout_sessao', '86400', 'Tempo limite de sessão em segundos', 'integer', 'seguranca', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(22, 'max_tentativas_login', '5', 'Máximo de tentativas de login', 'integer', 'seguranca', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(23, 'backup_automatico', 'true', 'Habilitar backup automático', 'boolean', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(24, 'horario_backup', '02:00:00', 'Horário para execução do backup automático', 'time', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(25, 'tolerancia_ponto_minutos', '15', 'Tolerância em minutos para registro de ponto', 'integer', 'ponto', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(26, 'email_notificacoes', 'true', 'Habilitar notificações por email', 'boolean', 'notificacoes', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(27, 'smtp_servidor', 'localhost', 'Servidor SMTP para envio de emails', 'string', 'email', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(28, 'smtp_porta', '587', 'Porta do servidor SMTP', 'integer', 'email', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(30, 'modo_debug', 'false', 'Modo de depuração ativo', 'boolean', 'desenvolvimento', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(31, 'cache_habilitado', 'true', 'Cache de consultas habilitado', 'boolean', 'performance', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(32, 'log_nivel', 'INFO', 'Nível de log do sistema', 'string', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(33, 'biometria_qualidade_minima', '60', 'Qualidade mínima exigida para biometria', 'integer', 'biometria', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05');

-- Table controle_ponto.cad_empresas does not contain any data (it is empty)

-- Table controle_ponto.banco_horas does not contain any data (it is empty)

-- 
-- Dumping data for table backup_jornada_funcionarios
--
INSERT INTO backup_jornada_funcionarios(id, nome_completo, jornada_seg_qui_entrada, jornada_seg_qui_saida, jornada_sex_entrada, jornada_sex_saida, jornada_intervalo_entrada, jornada_intervalo_saida, tolerancia_ponto, inicio_expediente, horario_saida_seg_qui, horario_saida_sexta, periodo_almoco_inicio, periodo_almoco_fim, duracao_minima_almoco, tolerancia_entrada, permite_banco_horas_positivo, data_backup) VALUES
(1, 'RICHARDSON CARDOSO RODRIGUES', '08:00:00', '18:00:00', '08:00:00', '18:00:00', '13:00:00', '14:00:00', 10, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-06-25 14:32:03'),
(8, 'SUELEN OLIVEIRA DOS SANTOS', '07:30:00', '17:30:00', '07:30:00', '16:30:00', '12:00:00', '13:00:00', 10, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-06-25 14:32:03'),
(9, 'TESTE 2 TURNO', '07:00:00', '17:00:00', '07:00:00', '16:00:00', '12:00:00', '13:00:00', 10, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-06-25 14:32:03');

--
-- Set default database
--
USE controle_ponto;

DELIMITER $$

--
-- Create trigger `tr_registros_ponto_audit_insert`
--
CREATE
DEFINER = 'cavalcrod'@'%'
TRIGGER IF NOT EXISTS tr_registros_ponto_audit_insert
AFTER INSERT
ON registros_ponto
FOR EACH ROW
BEGIN
  DECLARE nome_funcionario varchar(100);

  -- Buscar nome do funcionário
  SELECT
    nome_completo INTO nome_funcionario
  FROM funcionarios
  WHERE id = NEW.funcionario_id
  LIMIT 1;

  INSERT INTO logs_sistema (acao, tabela_afetada, registro_id, usuario_id, detalhes, data_hora)
    VALUES ('INSERT_REGISTRO_PONTO', 'registros_ponto', NEW.id, NEW.criado_por, JSON_OBJECT('funcionario_id', NEW.funcionario_id, 'nome_funcionario', COALESCE(nome_funcionario, 'N/A'), 'tipo_registro', NEW.tipo_registro, 'metodo_registro', NEW.metodo_registro, 'data_hora', NEW.data_hora, 'qualidade_biometria', NEW.qualidade_biometria), NOW());
END
$$

--
-- Create trigger `tr_funcionarios_audit_update`
--
CREATE
DEFINER = 'cavalcrod'@'%'
TRIGGER IF NOT EXISTS tr_funcionarios_audit_update
AFTER UPDATE
ON funcionarios
FOR EACH ROW
BEGIN
  -- Só registra se houve mudança significativa
  IF (OLD.nome_completo != NEW.nome_completo
    OR OLD.status_cadastro != NEW.status_cadastro
    OR (OLD.digital_dedo1 IS NULL) != (NEW.digital_dedo1 IS NULL)
    OR (OLD.digital_dedo2 IS NULL) != (NEW.digital_dedo2 IS NULL)) THEN

    INSERT INTO logs_sistema (acao, tabela_afetada, registro_id, detalhes, data_hora)
      VALUES ('UPDATE_FUNCIONARIO', 'funcionarios', NEW.id, JSON_OBJECT('funcionario_id', NEW.id, 'nome', NEW.nome_completo, 'mudancas', JSON_OBJECT('nome_alterado', OLD.nome_completo != NEW.nome_completo, 'status_alterado', OLD.status_cadastro != NEW.status_cadastro, 'biometria_dedo1_alterado', (OLD.digital_dedo1 IS NULL) != (NEW.digital_dedo1 IS NULL), 'biometria_dedo2_alterado', (OLD.digital_dedo2 IS NULL) != (NEW.digital_dedo2 IS NULL))), NOW());
  END IF;
END
$$

DELIMITER ;

--
-- Restore previous SQL mode
--
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;

--
-- Enable foreign keys
--
/*!40014 SET FOREIGN_KEY_CHECKS = @OLD_FOREIGN_KEY_CHECKS */;