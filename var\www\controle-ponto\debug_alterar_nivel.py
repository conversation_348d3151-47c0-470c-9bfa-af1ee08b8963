#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug da Funcionalidade de Alteração de Nível - RLPONTO-WEB
===========================================================

Script para debugar e testar a funcionalidade de alteração de nível
via interface web, simulando exatamente as requisições.

Data: 07/07/2025
"""

import requests
import json
from datetime import datetime

# Configurações
BASE_URL = "http://localhost"
LOGIN_URL = f"{BASE_URL}/login"
ALTERAR_NIVEL_URL = f"{BASE_URL}/alterar_nivel"
CONFIGURAR_USUARIOS_URL = f"{BASE_URL}/configurar_usuarios"

def testar_login_e_alteracao():
    """Testa login e alteração de nível"""
    print("🔍 DEBUG: TESTE DE ALTERAÇÃO DE NÍVEL VIA WEB")
    print("=" * 60)
    
    # Criar sessão
    session = requests.Session()
    
    try:
        # 1. Fazer login
        print("1️⃣ Fazendo login...")
        login_data = {
            'usuario': 'admin',
            'senha': '@Ric6109'
        }
        
        response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
        print(f"   Status do login: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        
        if response.status_code not in [200, 302]:
            print(f"❌ Falha no login: {response.status_code}")
            print(f"   Resposta: {response.text[:200]}")
            return False
        
        # 2. Acessar página de configuração
        print("\n2️⃣ Acessando página de configuração...")
        response = session.get(CONFIGURAR_USUARIOS_URL)
        print(f"   Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Falha ao acessar configuração: {response.status_code}")
            return False
        
        # 3. Testar alteração de nível
        print("\n3️⃣ Testando alteração de nível...")
        
        # Usar um usuário de teste (ID 2 = teste)
        test_data = {
            'id': '2',
            'nivel': 'admin'
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        print(f"   Enviando dados: {test_data}")
        response = session.post(ALTERAR_NIVEL_URL, data=test_data, headers=headers)
        print(f"   Status: {response.status_code}")
        print(f"   Headers da resposta: {dict(response.headers)}")
        
        try:
            result = response.json()
            print(f"   Resposta JSON: {result}")
            
            if result.get('success'):
                print("✅ Alteração bem-sucedida!")
                
                # Reverter para usuário
                print("\n4️⃣ Revertendo para usuário...")
                revert_data = {
                    'id': '2',
                    'nivel': 'usuario'
                }
                
                response = session.post(ALTERAR_NIVEL_URL, data=revert_data, headers=headers)
                result = response.json()
                print(f"   Resposta da reversão: {result}")
                
                if result.get('success'):
                    print("✅ Reversão bem-sucedida!")
                    return True
                else:
                    print(f"❌ Falha na reversão: {result.get('message')}")
                    return False
            else:
                print(f"❌ Falha na alteração: {result.get('message')}")
                return False
                
        except json.JSONDecodeError:
            print(f"❌ Resposta não é JSON válido: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        return False

def verificar_banco_dados():
    """Verifica estado do banco de dados"""
    print("\n🔍 VERIFICANDO BANCO DE DADOS")
    print("=" * 40)
    
    import pymysql
    
    try:
        conn = pymysql.connect(
            host='localhost',
            user='cavalcrod',
            password='200381',
            database='controle_ponto',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        with conn.cursor() as cursor:
            # Verificar usuários
            cursor.execute("""
                SELECT u.id, u.usuario, u.nivel_acesso as nivel_usuarios, 
                       p.nivel_acesso as nivel_permissoes
                FROM usuarios u 
                LEFT JOIN permissoes p ON u.id = p.usuario_id 
                ORDER BY u.id
            """)
            
            usuarios = cursor.fetchall()
            print("\n📋 Estado atual dos usuários:")
            for user in usuarios:
                nivel_perm = user['nivel_permissoes'] or 'NULL'
                status = "✅" if user['nivel_permissoes'] else "❌"
                print(f"   ID: {user['id']} | {user['usuario']} | "
                      f"Usuários: {user['nivel_usuarios']} | "
                      f"Permissões: {nivel_perm} {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar banco: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Função principal"""
    print("🚀 DEBUG DE ALTERAÇÃO DE NÍVEL - RLPONTO-WEB")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    # Verificar banco primeiro
    if not verificar_banco_dados():
        print("❌ Falha na verificação do banco")
        return
    
    # Testar funcionalidade web
    if testar_login_e_alteracao():
        print("\n🎉 TESTE CONCLUÍDO COM SUCESSO!")
        print("✅ Funcionalidade de alteração de nível está funcionando")
    else:
        print("\n❌ TESTE FALHOU!")
        print("❌ Problema identificado na funcionalidade web")

if __name__ == "__main__":
    main()
