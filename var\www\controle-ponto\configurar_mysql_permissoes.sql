-- Script para configurar permissões MySQL - Controle de Ponto
-- Execute este script no servidor MySQL (************)

-- Criar usu<PERSON>rio 'controle_user' com permissão para conectar do IP ***********
CREATE USER IF NOT EXISTS 'controle_user'@'***********' IDENTIFIED BY 'controle123';

-- <PERSON><PERSON><PERSON> usu<PERSON><PERSON> 'controle_user' com permissão para conectar de qualquer IP da rede local
CREATE USER IF NOT EXISTS 'controle_user'@'10.19.208.%' IDENTIFIED BY 'controle123';

-- Criar usu<PERSON>rio 'controle_user' com permissão para conectar de localhost (para scripts locais)
CREATE USER IF NOT EXISTS 'controle_user'@'localhost' IDENTIFIED BY 'controle123';

-- Conceder todas as permissões no database 'controle_ponto' para o IP específico
GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'***********';

-- Conceder todas as permissões no database 'controle_ponto' para a rede local
GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'10.19.208.%';

-- Conceder todas as permissões no database 'controle_ponto' para localhost
GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'localhost';

-- Atualizar privilégios
FLUSH PRIVILEGES;

-- Mostrar usuários criados
SELECT User, Host FROM mysql.user WHERE User = 'controle_user';

-- Mostrar privilégios concedidos
SHOW GRANTS FOR 'controle_user'@'***********';
SHOW GRANTS FOR 'controle_user'@'10.19.208.%';
SHOW GRANTS FOR 'controle_user'@'localhost'; 