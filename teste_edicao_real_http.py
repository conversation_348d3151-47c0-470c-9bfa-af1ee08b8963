#!/usr/bin/env python3
"""
Script para testar edição real via HTTP, simulando exatamente o que acontece no navegador.
"""

import requests
import sys
import os
from urllib.parse import urljoin

def testar_edicao_funcionario_http():
    """
    Testa edição de funcionário via HTTP real.
    """
    print("🌐 TESTE DE EDIÇÃO VIA HTTP REAL")
    print("=" * 60)
    
    base_url = "http://************:5000"
    funcionario_id = 1
    
    # Criar sessão para manter cookies
    session = requests.Session()
    
    try:
        # 1. Fazer login primeiro
        print("🔐 Fazendo login...")
        login_data = {
            'usuario': 'admin',
            'senha': '@Ric6109'
        }
        
        login_response = session.post(
            urljoin(base_url, '/login'),
            data=login_data,
            allow_redirects=True
        )
        
        if login_response.status_code != 200:
            print(f"❌ Erro no login: {login_response.status_code}")
            return False
        
        print("✅ Login realizado com sucesso")
        
        # 2. Buscar página de edição (GET)
        print(f"\n📄 Buscando página de edição do funcionário {funcionario_id}...")
        edit_url = urljoin(base_url, f'/funcionarios/{funcionario_id}/editar')
        
        get_response = session.get(edit_url)
        
        if get_response.status_code != 200:
            print(f"❌ Erro ao buscar página de edição: {get_response.status_code}")
            print(f"Resposta: {get_response.text[:500]}")
            return False
        
        print("✅ Página de edição carregada com sucesso")
        
        # 3. Extrair dados do formulário da página HTML
        print("\n📋 Extraindo dados do formulário...")
        html_content = get_response.text
        
        # Buscar campos do formulário (simulação básica)
        # Em um teste real, você usaria BeautifulSoup para extrair os campos
        form_data = {
            'nome_completo': 'RICHARDSON CARDOSO RODRIGUES',
            'cpf': '711.256.042-04',
            'rg': '31.799.841',
            'data_nascimento': '1981-03-20',
            'sexo': 'M',
            'estado_civil': 'Casado',
            'nacionalidade': 'Brasileiro',
            'ctps_numero': '0000000',
            'ctps_serie_uf': '0000000000',
            'pis_pasep': '000.00000.00-0',
            'endereco_cep': '69000-000',
            'endereco_estado': 'AM',
            'endereco_rua': 'Rua Teste',
            'endereco_bairro': 'Centro',
            'endereco_cidade': 'Manaus',
            'telefone1': '(92) 99999-9999',
            'telefone2': '',
            'email': '<EMAIL>',
            'empresa_id': '11',  # AiNexus Tecnologia
            'cargo': 'Analista de Sistemas',
            'setor_obra': 'TI',
            'matricula_empresa': '2025001',
            'data_admissao': '2025-01-01',
            'tipo_contrato': 'CLT',
            'nivel_acesso': 'Funcionario',
            'turno': 'Diurno',
            'tolerancia_ponto': '10',
            'status_cadastro': 'Ativo',
            'salario_base': '5000.00',
            'horas_trabalho_obrigatorias': '8.0'
        }
        
        print(f"📊 Dados do formulário preparados: {len(form_data)} campos")
        
        # 4. Enviar dados via POST (simulando "Salvar" sem alterações)
        print(f"\n💾 Enviando dados via POST (simulando clique em 'Salvar')...")
        
        post_response = session.post(
            edit_url,
            data=form_data,
            allow_redirects=False  # Não seguir redirects para capturar resposta
        )
        
        print(f"📊 Status da resposta: {post_response.status_code}")
        print(f"📊 Headers: {dict(post_response.headers)}")
        
        # Verificar se houve erro
        if post_response.status_code == 302:
            # Redirect - sucesso
            location = post_response.headers.get('Location', '')
            print(f"✅ Redirecionamento para: {location}")
            
            if 'funcionarios' in location and 'detalhes' in location:
                print("✅ SUCESSO: Edição realizada sem erros!")
                return True
            else:
                print("⚠️ Redirecionamento inesperado")
                return False
                
        elif post_response.status_code == 200:
            # Página retornada - pode ter erro
            response_text = post_response.text
            
            if 'Erro ao processar formulário' in response_text:
                print("❌ ERRO ENCONTRADO na resposta!")
                
                # Extrair mensagem de erro
                if "'id'" in response_text:
                    print("🎯 ERRO 'id' CONFIRMADO na resposta HTTP!")
                
                # Salvar resposta para análise
                with open('/tmp/erro_response.html', 'w', encoding='utf-8') as f:
                    f.write(response_text)
                print("💾 Resposta salva em /tmp/erro_response.html")
                
                return False
            else:
                print("⚠️ Resposta 200 sem erro aparente")
                return True
        else:
            print(f"❌ Status inesperado: {post_response.status_code}")
            print(f"Resposta: {post_response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ Erro durante teste HTTP: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def verificar_logs_servidor():
    """
    Verifica os logs do servidor para erros relacionados.
    """
    print("\n📋 VERIFICANDO LOGS DO SERVIDOR")
    print("=" * 50)
    
    try:
        import subprocess
        
        # Buscar logs recentes relacionados a erro
        result = subprocess.run([
            'ssh', 'root@************',
            'tail -20 /var/www/controle-ponto/app.log | grep -E "(ERROR|ERRO|id|funcionario)"'
        ], capture_output=True, text=True, timeout=10)
        
        if result.stdout:
            print("📋 Logs encontrados:")
            print(result.stdout)
        else:
            print("✅ Nenhum erro recente nos logs")
            
    except Exception as e:
        print(f"❌ Erro ao verificar logs: {e}")

if __name__ == "__main__":
    print("🧪 TESTE DE EDIÇÃO REAL VIA HTTP")
    print("=" * 60)
    
    sucesso = testar_edicao_funcionario_http()
    
    print("\n" + "=" * 60)
    print("📊 RESULTADO DO TESTE")
    print("=" * 60)
    
    if sucesso:
        print("✅ TESTE PASSOU!")
        print("🎯 Edição funcionou corretamente via HTTP")
        print("✅ Correção do erro 'id' está funcionando")
    else:
        print("❌ TESTE FALHOU!")
        print("🎯 Erro 'id' ainda está ocorrendo")
        print("🔍 Verificar logs e resposta salva")
    
    verificar_logs_servidor()
    
    print("\n📋 PRÓXIMOS PASSOS:")
    if sucesso:
        print("✅ Problema resolvido - teste no navegador")
    else:
        print("🔧 Investigar resposta salva em /tmp/erro_response.html")
        print("📋 Verificar logs do servidor")
        print("🔍 Pode ser necessária correção adicional")
