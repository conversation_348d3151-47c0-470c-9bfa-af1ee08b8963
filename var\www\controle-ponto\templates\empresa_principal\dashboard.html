{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    /* Seguindo o padrão de cores da sidebar - tons claros */
    :root {
        --primary-color: #4fbdba;
        --background-color: #f9fafb;
        --card-background: #ffffff;
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --text-muted: #9ca3af;
        --border-color: #e5e7eb;
        --hover-bg: #f3f4f6;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --info-color: #3b82f6;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    /* Container principal com background padrão */
    .dashboard-container {
        background-color: var(--background-color);
        min-height: 100vh;
        padding: 2rem;
    }

    /* Stats Cards modernos seguindo o padrão */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .modern-stats-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: var(--shadow-sm);
    }
    
    .modern-stats-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: #d1d5db;
    }
    
    .stats-icon {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        font-size: 1.25rem;
    }
    
    .stats-icon.success {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
    }
    
    .stats-icon.info {
        background: linear-gradient(135deg, var(--info-color), #1d4ed8);
        color: white;
    }
    
    .stats-icon.warning {
        background: linear-gradient(135deg, var(--warning-color), #d97706);
        color: white;
    }
    
    .stats-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
    }
    
    .stats-main {
        flex: 1;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        line-height: 1;
        margin-bottom: 0.25rem;
    }
    
    .stats-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
    }
    
    .stats-change {
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        white-space: nowrap;
    }
    
    .stats-change.positive {
        background: #dcfce7;
        color: #166534;
    }
    
    .stats-change.neutral {
        background: var(--hover-bg);
        color: var(--text-secondary);
    }

    /* Header seguindo padrão do sistema */
    .modern-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #26a69a 100%);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        color: white;
        box-shadow: var(--shadow-md);
    }
    
    .modern-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 60%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        transform: rotate(15deg);
    }
    
    .header-content {
        position: relative;
        z-index: 2;
    }
    
    .header-badge {
        background: rgba(255,255,255,0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.3);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.875rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Action Buttons seguindo padrão claro */
    .action-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .modern-action-btn {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        text-decoration: none;
        color: var(--text-primary);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        box-shadow: var(--shadow-sm);
    }
    
    .modern-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: #d1d5db;
        text-decoration: none;
        color: var(--text-primary);
    }
    
    .modern-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(79, 189, 186, 0.1), transparent);
        transition: left 0.5s;
    }
    
    .modern-action-btn:hover::before {
        left: 100%;
    }
    
    .action-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        font-size: 1.5rem;
        position: relative;
        z-index: 2;
    }
    
    .action-icon.primary {
        background: linear-gradient(135deg, var(--info-color), #1d4ed8);
        color: white;
    }
    
    .action-icon.success {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
    }
    
    .action-icon.warning {
        background: linear-gradient(135deg, var(--warning-color), #d97706);
        color: white;
    }
    
    .action-title {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.25rem;
        position: relative;
        z-index: 2;
        color: var(--text-primary);
    }
    
    .action-subtitle {
        font-size: 0.875rem;
        color: var(--text-secondary);
        position: relative;
        z-index: 2;
    }

    /* Cards seguindo padrão do sistema */
    .modern-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
    }
    
    .modern-card:hover {
        box-shadow: var(--shadow-md);
    }
    
    .modern-card-header {
        background: var(--hover-bg);
        border-bottom: 1px solid var(--border-color);
        padding: 1.25rem 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .modern-card-title {
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0;
    }
    
    .modern-card-body {
        padding: 1.5rem;
        background: var(--card-background);
    }

    /* Jornadas com design claro */
    .jornada-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        margin-bottom: 1rem;
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
    }

    .jornada-card:hover {
        box-shadow: var(--shadow-md);
    }

    .jornada-header {
        background: var(--hover-bg);
        border-bottom: 1px solid var(--border-color);
        padding: 1rem 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .jornada-title {
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .jornada-body {
        padding: 1.5rem;
    }

    /* Badges e status */
    .badge-primary {
        background: var(--primary-color);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-left: 0.5rem;
    }

    .status-badge {
        background: #dcfce7;
        color: #166534;
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.875rem;
        font-weight: 600;
    }

    /* Alerts seguindo padrão */
    .alert-warning {
        background: #fef3c7;
        border: 1px solid #fbbf24;
        color: #92400e;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }

    .alert-info {
        background: #dbeafe;
        border: 1px solid #60a5fa;
        color: #1e40af;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
        text-align: center;
    }

    .alert-danger {
        background: #fee2e2;
        border: 1px solid #f87171;
        color: #dc2626;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }

    /* Inputs e forms */
    .form-control {
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 0.5rem 0.75rem;
        background: var(--card-background);
        color: var(--text-primary);
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
        outline: none;
    }

    .input-group-text {
        background: var(--hover-bg);
        border: 1px solid var(--border-color);
        color: var(--text-secondary);
    }

    .form-label {
        font-weight: 500;
        color: var(--text-secondary);
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    /* Botões */
    .btn-primary {
        background: var(--primary-color);
        border: 1px solid var(--primary-color);
        color: white;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: #26a69a;
        border-color: #26a69a;
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    .btn-outline-primary,
    .btn-outline-danger {
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    /* Loading spinner */
    .spinner-border {
        color: var(--primary-color);
    }

    /* Responsividade */
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 1rem;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        .action-grid {
            grid-template-columns: 1fr;
        }
        
        .modern-header {
            padding: 1.5rem;
        }
        
        .stats-number {
            font-size: 1.75rem;
        }
    }

    /* Animações suaves */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in {
        animation: fadeInUp 0.6s ease both;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header seguindo padrão do sistema -->
    <div class="modern-header">
        <div class="header-content">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2" style="font-weight: 700; font-size: 2.25rem;">
                        <i class="fas fa-crown me-2"></i>{{ empresa_principal.razao_social }}
                    </h1>
                    {% if empresa_principal.nome_fantasia %}
                        <h4 class="mb-3" style="font-weight: 400; opacity: 0.9;">{{ empresa_principal.nome_fantasia }}</h4>
                    {% endif %}
                    <div class="d-flex flex-wrap gap-3" style="font-size: 0.95rem; opacity: 0.9;">
                        <span><i class="fas fa-id-card me-1"></i> {{ empresa_principal.cnpj }}</span>
                        {% if empresa_principal.telefone %}
                            <span><i class="fas fa-phone me-1"></i> {{ empresa_principal.telefone }}</span>
                        {% endif %}
                        {% if empresa_principal.email %}
                            <span><i class="fas fa-envelope me-1"></i> {{ empresa_principal.email }}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="header-badge">
                        <i class="fas fa-star"></i>
                        EMPRESA PRINCIPAL
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Grid seguindo padrão claro -->
    <div class="stats-grid">
        <div class="modern-stats-card fade-in">
            <div class="stats-icon success">
                <i class="fas fa-building"></i>
            </div>
            <div class="stats-content">
                <div class="stats-main">
                    <div class="stats-number">{{ stats.total_empresas or 0 }}</div>
                    <div class="stats-label">Total de Empresas</div>
                </div>
                <div class="stats-change positive">+100%</div>
            </div>
        </div>
        
        <div class="modern-stats-card fade-in" style="animation-delay: 0.1s;">
            <div class="stats-icon info">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-content">
                <div class="stats-main">
                    <div class="stats-number">{{ stats.empresas_ativas or 0 }}</div>
                    <div class="stats-label">Empresas Ativas</div>
                </div>
                <div class="stats-change positive">+{{ ((stats.empresas_ativas or 0) / (stats.total_empresas or 1) * 100)|round(1) }}%</div>
            </div>
        </div>
        
        <div class="modern-stats-card fade-in" style="animation-delay: 0.2s;">
            <div class="stats-icon warning">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-content">
                <div class="stats-main">
                    <div class="stats-number">{{ stats.total_funcionarios or 0 }}</div>
                    <div class="stats-label">Total de Funcionários</div>
                </div>
                <div class="stats-change neutral">Estável</div>
            </div>
        </div>
    </div>

    <!-- Action Buttons seguindo padrão -->
    <div class="modern-card mb-4 fade-in" style="animation-delay: 0.3s;">
        <div class="modern-card-header">
            <h5 class="modern-card-title">
                <i class="fas fa-bolt"></i>
                Ações Rápidas
            </h5>
        </div>
        <div class="modern-card-body">
            <div class="action-grid">
                <a href="{{ url_for('empresa_principal.clientes') }}" class="modern-action-btn">
                    <div class="action-icon primary">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="action-title">Gerenciar Clientes</div>
                    <div class="action-subtitle">Visualizar e gerenciar empresas clientes</div>
                </a>
                
                <a href="{{ url_for('empresa_principal.funcionarios') }}" class="modern-action-btn">
                    <div class="action-icon success">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="action-title">Funcionários</div>
                    <div class="action-subtitle">Controle de funcionários e dados pessoais</div>
                </a>
                
                <a href="{{ url_for('empresa_principal.relatorios') }}" class="modern-action-btn">
                    <div class="action-icon warning">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="action-title">Relatórios</div>
                    <div class="action-subtitle">Análises e relatórios detalhados</div>
                </a>
            </div>
        </div>
    </div>

    <!-- Informações da Empresa Principal -->
    <div class="row">
        <div class="col-md-12">
            <div class="modern-card fade-in" style="animation-delay: 0.4s;">
                <div class="modern-card-header">
                    <h5 class="modern-card-title">
                        <i class="fas fa-crown"></i>
                        Informações da Empresa Principal
                    </h5>
                </div>
                <div class="modern-card-body">
                    {% if empresa_principal %}
                        <div class="row">
                            <div class="col-md-6">
                                <h6 style="font-weight: 600; color: var(--text-primary);"><strong>{{ empresa_principal.razao_social }}</strong></h6>
                                {% if empresa_principal.nome_fantasia %}
                                    <p style="color: var(--text-secondary); margin-bottom: 1rem;">{{ empresa_principal.nome_fantasia }}</p>
                                {% endif %}
                                <p style="margin-bottom: 0.5rem; color: var(--text-primary);"><strong>CNPJ:</strong> {{ empresa_principal.cnpj }}</p>
                            </div>
                            <div class="col-md-6">
                                {% if empresa_principal.telefone %}
                                    <p style="margin-bottom: 0.5rem; color: var(--text-primary);"><strong>Telefone:</strong> {{ empresa_principal.telefone }}</p>
                                {% endif %}
                                {% if empresa_principal.email %}
                                    <p style="margin-bottom: 0.5rem; color: var(--text-primary);"><strong>E-mail:</strong> {{ empresa_principal.email }}</p>
                                {% endif %}
                                <p style="color: var(--text-primary);"><strong>Status:</strong>
                                    <span class="status-badge">
                                        Empresa Principal
                                    </span>
                                </p>
                            </div>
                        </div>


                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-crown text-muted mb-3" style="font-size: 3rem; color: var(--text-muted);"></i>
                            <h6 style="color: var(--text-secondary); margin-bottom: 1rem;">Empresa principal não configurada</h6>
                            <a href="/empresas/" class="btn btn-primary">
                                <i class="fas fa-cog me-1"></i> Configurar Empresa Principal
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Animações suaves seguindo padrão do sistema
document.addEventListener('DOMContentLoaded', function() {
    // Dashboard carregado
    console.log('Dashboard empresa principal carregado');
});


</script>
{% endblock %}
