-- RLPONTO-WEB v2.0 - Atualização de Banco de Dados (VERSÃO CORRIGIDA)
-- Sistema Biométrico Integrado com WebUSB e Auditoria Completa
-- Data: 06/01/2025

USE controle_ponto;

-- Desabilitar verificação de foreign key temporariamente
SET foreign_key_checks = 0;

-- Procedimento para adicionar coluna se não existir
DELIMITER $$
DROP PROCEDURE IF EXISTS AddColumnIfNotExists$$
CREATE PROCEDURE AddColumnIfNotExists(
    IN table_name VARCHAR(128),
    IN column_name VARCHAR(128),
    IN column_definition TEXT
)
BEGIN
    DECLARE column_exists INT DEFAULT 0;
    
    SELECT COUNT(*)
    INTO column_exists
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = table_name
    AND COLUMN_NAME = column_name;
    
    IF column_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE ', table_name, ' ADD COLUMN ', column_name, ' ', column_definition);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;

-- Tabela de logs de tentativas biométricas
CREATE TABLE IF NOT EXISTS logs_biometria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    similarity_score DECIMAL(5,4) DEFAULT 0.0000,
    device_info JSON,
    status ENUM('success', 'failed') DEFAULT 'failed',
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_funcionario_timestamp (funcionario_id, timestamp),
    INDEX idx_status_timestamp (status, timestamp),
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de logs de segurança geral
CREATE TABLE IF NOT EXISTS logs_seguranca (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo_evento VARCHAR(50) NOT NULL,
    funcionario_id INT NULL,
    detalhes JSON,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    nivel_risco ENUM('baixo', 'medio', 'alto') DEFAULT 'baixo',
    INDEX idx_tipo_timestamp (tipo_evento, timestamp),
    INDEX idx_funcionario_timestamp (funcionario_id, timestamp),
    INDEX idx_nivel_risco (nivel_risco),
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de tentativas de acesso biométrico
CREATE TABLE IF NOT EXISTS tentativas_biometria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_hash VARCHAR(64),
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    device_vendor_id VARCHAR(10),
    device_product_id VARCHAR(10),
    success BOOLEAN DEFAULT FALSE,
    funcionario_id INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_timestamp (timestamp),
    INDEX idx_success (success),
    INDEX idx_funcionario (funcionario_id),
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de configurações do sistema
CREATE TABLE IF NOT EXISTS configuracoes_sistema (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chave VARCHAR(100) UNIQUE NOT NULL,
    valor TEXT,
    descricao TEXT,
    tipo ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    categoria VARCHAR(50) DEFAULT 'geral',
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    atualizado_em DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_categoria (categoria),
    INDEX idx_chave (chave)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Adicionar campos de auditoria à tabela registros_ponto
CALL AddColumnIfNotExists('registros_ponto', 'biometria_verificada', 'BOOLEAN DEFAULT FALSE COMMENT "Indica se o registro foi feito com biometria verificada"');
CALL AddColumnIfNotExists('registros_ponto', 'device_hash', 'VARCHAR(64) COMMENT "Hash do dispositivo usado"');
CALL AddColumnIfNotExists('registros_ponto', 'user_agent', 'TEXT COMMENT "User agent do navegador"');
CALL AddColumnIfNotExists('registros_ponto', 'security_score', 'INT DEFAULT 100 COMMENT "Pontuação de segurança do registro"');
CALL AddColumnIfNotExists('registros_ponto', 'ip_address', 'VARCHAR(45) COMMENT "Endereço IP do cliente"');

-- Adicionar campo de status aos funcionários se não existir
CALL AddColumnIfNotExists('funcionarios', 'status', 'ENUM("ativo", "inativo", "suspenso") DEFAULT "ativo" COMMENT "Status do funcionário"');

-- Criar índices para os novos campos
SET @sql = 'CREATE INDEX IF NOT EXISTS idx_biometria_verificada ON registros_ponto(biometria_verificada)';
SET sql_mode = 'ALLOW_INVALID_DATES';
-- Verificar se índice não existe antes de criar
SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                     WHERE table_schema = DATABASE() 
                     AND table_name = 'registros_ponto' 
                     AND index_name = 'idx_biometria_verificada');

-- Criar índices se não existirem
SET @sql1 = IF(@index_exists = 0, 'CREATE INDEX idx_biometria_verificada ON registros_ponto(biometria_verificada)', 'SELECT "Índice já existe"');
PREPARE stmt1 FROM @sql1;
EXECUTE stmt1;
DEALLOCATE PREPARE stmt1;

SET @index_exists2 = (SELECT COUNT(*) FROM information_schema.statistics 
                      WHERE table_schema = DATABASE() 
                      AND table_name = 'registros_ponto' 
                      AND index_name = 'idx_security_score');

SET @sql2 = IF(@index_exists2 = 0, 'CREATE INDEX idx_security_score ON registros_ponto(security_score)', 'SELECT "Índice já existe"');
PREPARE stmt2 FROM @sql2;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

SET @index_exists3 = (SELECT COUNT(*) FROM information_schema.statistics 
                      WHERE table_schema = DATABASE() 
                      AND table_name = 'funcionarios' 
                      AND index_name = 'idx_status');

SET @sql3 = IF(@index_exists3 = 0, 'CREATE INDEX idx_status ON funcionarios(status)', 'SELECT "Índice já existe"');
PREPARE stmt3 FROM @sql3;
EXECUTE stmt3;
DEALLOCATE PREPARE stmt3;

-- Inserir configurações padrão
INSERT IGNORE INTO configuracoes_sistema (chave, valor, descricao, tipo, categoria) VALUES
('biometric_threshold', '0.7', 'Limite mínimo de similaridade biométrica', 'string', 'biometria'),
('security_enabled', 'true', 'Habilitar verificações de segurança', 'boolean', 'seguranca'),
('max_failed_attempts', '5', 'Máximo de tentativas falhadas por hora', 'integer', 'seguranca'),
('device_whitelist', '["1b55:4500"]', 'Lista de dispositivos autorizados (vendor:product)', 'json', 'hardware'),
('attendance_tolerance_minutes', '15', 'Tolerância em minutos para pontualidade', 'integer', 'horarios'),
('morning_start', '07:00', 'Início do período matutino', 'string', 'horarios'),
('morning_end', '09:30', 'Fim do período matutino', 'string', 'horarios'),
('lunch_out_start', '11:30', 'Início da saída para almoço', 'string', 'horarios'),
('lunch_out_end', '13:30', 'Fim da saída para almoço', 'string', 'horarios'),
('lunch_return_start', '13:30', 'Início da volta do almoço', 'string', 'horarios'),
('lunch_return_end', '15:00', 'Fim da volta do almoço', 'string', 'horarios'),
('evening_start', '17:00', 'Início do período vespertino', 'string', 'horarios'),
('evening_end', '19:00', 'Fim do período vespertino', 'string', 'horarios');

-- View para relatórios de registros completos
CREATE OR REPLACE VIEW vw_registros_completos AS
SELECT 
    rp.id,
    rp.funcionario_id,
    f.nome as funcionario_nome,
    f.cpf as funcionario_cpf,
    rp.data_hora,
    rp.tipo,
    rp.status,
    COALESCE(rp.biometria_verificada, FALSE) as biometria_verificada,
    COALESCE(rp.security_score, 100) as security_score,
    CASE 
        WHEN rp.tipo = 'entrada' THEN 'Entrada'
        WHEN rp.tipo = 'saida_almoco' THEN 'Saída Almoço'
        WHEN rp.tipo = 'entrada_almoco' THEN 'Volta Almoço'
        WHEN rp.tipo = 'saida' THEN 'Saída'
        ELSE rp.tipo
    END as tipo_descricao,
    CASE 
        WHEN rp.status = 'pontual' THEN 'Pontual'
        WHEN rp.status = 'atrasado' THEN 'Atrasado'
        WHEN rp.status = 'adiantado' THEN 'Adiantado'
        ELSE rp.status
    END as status_descricao,
    DATE(rp.data_hora) as data_registro,
    TIME(rp.data_hora) as hora_registro
FROM registros_ponto rp
JOIN funcionarios f ON rp.funcionario_id = f.id
WHERE COALESCE(f.status, 'ativo') = 'ativo'
ORDER BY rp.data_hora DESC;

-- View para estatísticas de biometria
CREATE OR REPLACE VIEW vw_estatisticas_biometria AS
SELECT 
    DATE(lb.timestamp) as data,
    COUNT(*) as total_tentativas,
    SUM(CASE WHEN lb.status = 'success' THEN 1 ELSE 0 END) as sucessos,
    SUM(CASE WHEN lb.status = 'failed' THEN 1 ELSE 0 END) as falhas,
    ROUND(AVG(lb.similarity_score), 4) as score_medio,
    COUNT(DISTINCT lb.funcionario_id) as funcionarios_unicos
FROM logs_biometria lb
GROUP BY DATE(lb.timestamp)
ORDER BY data DESC;

-- Atualizar registros existentes para marcar como verificados se têm dados biométricos
UPDATE registros_ponto rp
JOIN funcionarios f ON rp.funcionario_id = f.id
SET rp.biometria_verificada = TRUE,
    rp.security_score = 100
WHERE f.template_biometrico IS NOT NULL 
AND f.template_biometrico != ''
AND COALESCE(rp.biometria_verificada, FALSE) = FALSE;

-- Triggers serão criados apenas se não existirem
DELIMITER $$

-- Verificar se trigger já existe antes de criar
DROP TRIGGER IF EXISTS tr_registros_ponto_security_log$$
CREATE TRIGGER tr_registros_ponto_security_log
AFTER INSERT ON registros_ponto
FOR EACH ROW
BEGIN
    INSERT INTO logs_seguranca (
        tipo_evento,
        funcionario_id,
        detalhes,
        timestamp,
        nivel_risco
    ) VALUES (
        'registro_ponto',
        NEW.funcionario_id,
        JSON_OBJECT(
            'registro_id', NEW.id,
            'tipo', NEW.tipo,
            'status', NEW.status,
            'biometria_verificada', COALESCE(NEW.biometria_verificada, FALSE),
            'security_score', COALESCE(NEW.security_score, 100)
        ),
        NEW.data_hora,
        CASE 
            WHEN COALESCE(NEW.security_score, 100) < 50 THEN 'alto'
            WHEN COALESCE(NEW.security_score, 100) < 80 THEN 'medio'
            ELSE 'baixo'
        END
    );
END$$

-- Trigger para detectar tentativas suspeitas
DROP TRIGGER IF EXISTS tr_biometria_failed_security$$
CREATE TRIGGER tr_biometria_failed_security
AFTER INSERT ON logs_biometria
FOR EACH ROW
BEGIN
    DECLARE failed_count INT DEFAULT 0;
    
    IF NEW.status = 'failed' THEN
        -- Contar falhas na última hora
        SELECT COUNT(*) INTO failed_count
        FROM logs_biometria 
        WHERE status = 'failed' 
        AND timestamp >= DATE_SUB(NEW.timestamp, INTERVAL 1 HOUR);
        
        -- Se mais de 5 falhas na última hora, criar log de segurança
        IF failed_count > 5 THEN
            INSERT INTO logs_seguranca (
                tipo_evento,
                funcionario_id,
                detalhes,
                timestamp,
                nivel_risco
            ) VALUES (
                'tentativas_excessivas',
                NEW.funcionario_id,
                JSON_OBJECT(
                    'failed_count', failed_count,
                    'time_window', '1 hour',
                    'last_similarity', NEW.similarity_score
                ),
                NEW.timestamp,
                'alto'
            );
        END IF;
    END IF;
END$$

DELIMITER ;

-- Stored procedure para limpeza de logs antigos
DELIMITER $$

DROP PROCEDURE IF EXISTS sp_cleanup_old_logs$$
CREATE PROCEDURE sp_cleanup_old_logs()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Remover logs de biometria com mais de 90 dias
    DELETE FROM logs_biometria 
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- Remover logs de segurança com mais de 180 dias (exceto eventos críticos)
    DELETE FROM logs_seguranca 
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 180 DAY)
    AND nivel_risco != 'alto';
    
    -- Remover tentativas de biometria com mais de 30 dias
    DELETE FROM tentativas_biometria 
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    COMMIT;
END$$

DELIMITER ;

-- Reabilitar verificação de foreign key
SET foreign_key_checks = 1;

-- Log de conclusão
INSERT IGNORE INTO logs_seguranca (
    tipo_evento,
    funcionario_id,
    detalhes,
    timestamp,
    nivel_risco
) VALUES (
    'database_update',
    NULL,
    JSON_OBJECT(
        'version', '2.0',
        'description', 'Sistema biométrico integrado com WebUSB',
        'status', 'completed',
        'timestamp', NOW()
    ),
    NOW(),
    'baixo'
);

-- Limpar procedimento temporário
DROP PROCEDURE IF EXISTS AddColumnIfNotExists;

-- Verificar se tudo foi criado corretamente
SELECT 'Atualização concluída com sucesso!' as status;
SELECT COUNT(*) as 'Novas Tabelas Criadas' FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name IN ('logs_biometria', 'logs_seguranca', 'tentativas_biometria', 'configuracoes_sistema');

COMMIT; 