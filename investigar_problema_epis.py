#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para investigar problema dos EPIs não aparecendo
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def investigar_epis():
    """Investigar problema dos EPIs"""
    print("🔍 INVESTIGANDO PROBLEMA DOS EPIs")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar EPIs no banco de dados
        print("\n1. Verificando EPIs no banco de dados...")
        
        # Verificar tabela funcionario_epis
        sql_epis_funcionario = """
        SELECT fe.id, fe.funcionario_id, fe.epi_id, fe.data_entrega, fe.ativo,
               e.nome as epi_nome, e.ca as epi_ca, e.descricao as epi_descricao
        FROM funcionario_epis fe
        JOIN epis e ON fe.epi_id = e.id
        WHERE fe.funcionario_id = 1
        ORDER BY fe.data_entrega DESC, fe.id DESC
        """
        
        epis_funcionario = db.execute_query(sql_epis_funcionario)
        
        print(f"📊 EPIs do Richardson na tabela funcionario_epis: {len(epis_funcionario)}")
        if epis_funcionario:
            for epi in epis_funcionario:
                status = "ATIVO" if epi['ativo'] else "INATIVO"
                print(f"   - EPI ID {epi['id']}: {epi['epi_nome']} (CA: {epi['epi_ca']}) - {status}")
                print(f"     Data entrega: {epi['data_entrega']}")
        else:
            print(f"   ❌ Nenhum EPI encontrado para Richardson")
        
        # 2. Verificar função get_with_epis
        print(f"\n2. Verificando função get_with_epis...")
        from utils.database import FuncionarioQueries
        
        funcionario_com_epis = FuncionarioQueries.get_with_epis(1)
        
        if funcionario_com_epis:
            print(f"📋 Dados retornados por get_with_epis:")
            print(f"   Nome: {funcionario_com_epis.get('nome_completo')}")
            print(f"   EPIs: {funcionario_com_epis.get('epis', 'CAMPO NÃO ENCONTRADO')}")
            
            # Verificar se há campo epis
            if 'epis' in funcionario_com_epis:
                epis_data = funcionario_com_epis['epis']
                if epis_data:
                    print(f"   📊 EPIs encontrados: {len(epis_data) if isinstance(epis_data, list) else 'Não é lista'}")
                    if isinstance(epis_data, list):
                        for i, epi in enumerate(epis_data):
                            print(f"      {i+1}. {epi}")
                else:
                    print(f"   ❌ Campo 'epis' está vazio")
            else:
                print(f"   ❌ Campo 'epis' não existe no retorno")
        
        # 3. Verificar template
        print(f"\n3. Verificando como o template processa EPIs...")
        
        # Simular dados que o template recebe
        template_data = {
            'funcionario': funcionario_com_epis,
            'epis': funcionario_com_epis.get('epis', []) if funcionario_com_epis else []
        }
        
        print(f"📋 Dados que o template recebe:")
        print(f"   funcionario.epis: {template_data['funcionario'].get('epis', 'NÃO ENCONTRADO') if template_data['funcionario'] else 'FUNCIONÁRIO NULO'}")
        print(f"   epis (variável separada): {template_data['epis']}")
        
        # 4. Verificar estrutura da query get_with_epis
        print(f"\n4. Testando query direta para EPIs...")
        
        sql_epis_direto = """
        SELECT 
            f.id as funcionario_id,
            f.nome_completo,
            GROUP_CONCAT(
                CONCAT(e.nome, ' (CA: ', e.ca, ')')
                SEPARATOR ', '
            ) as epis_lista,
            COUNT(fe.id) as total_epis
        FROM funcionarios f
        LEFT JOIN funcionario_epis fe ON f.id = fe.funcionario_id AND fe.ativo = TRUE
        LEFT JOIN epis e ON fe.epi_id = e.id
        WHERE f.id = 1
        GROUP BY f.id, f.nome_completo
        """
        
        resultado_direto = db.execute_query(sql_epis_direto, fetch_one=True)
        
        if resultado_direto:
            print(f"📋 Query direta:")
            print(f"   Funcionário: {resultado_direto['nome_completo']}")
            print(f"   Total EPIs: {resultado_direto['total_epis']}")
            print(f"   Lista EPIs: {resultado_direto['epis_lista']}")
        
        # 5. Verificar se há EPIs cadastrados no sistema
        print(f"\n5. Verificando EPIs cadastrados no sistema...")
        
        sql_todos_epis = """
        SELECT id, nome, ca, descricao, ativo
        FROM epis
        ORDER BY nome
        """
        
        todos_epis = db.execute_query(sql_todos_epis)
        
        print(f"📊 EPIs cadastrados no sistema: {len(todos_epis)}")
        for epi in todos_epis[:5]:  # Mostrar apenas os primeiros 5
            status = "ATIVO" if epi['ativo'] else "INATIVO"
            print(f"   - ID {epi['id']}: {epi['nome']} (CA: {epi['ca']}) - {status}")
        
        if len(todos_epis) > 5:
            print(f"   ... e mais {len(todos_epis) - 5} EPIs")
        
        # 6. Testar adição manual de EPI
        print(f"\n6. Testando adição manual de EPI para Richardson...")
        
        if todos_epis:
            primeiro_epi = todos_epis[0]
            print(f"   📋 Tentando adicionar EPI: {primeiro_epi['nome']} (ID: {primeiro_epi['id']})")
            
            # Verificar se já existe
            sql_verificar = """
            SELECT id FROM funcionario_epis 
            WHERE funcionario_id = 1 AND epi_id = %s AND ativo = TRUE
            """
            
            existe = db.execute_query(sql_verificar, (primeiro_epi['id'],), fetch_one=True)
            
            if not existe:
                sql_inserir = """
                INSERT INTO funcionario_epis (funcionario_id, epi_id, data_entrega, ativo)
                VALUES (1, %s, CURRENT_DATE, TRUE)
                """
                
                result = db.execute_query(sql_inserir, (primeiro_epi['id'],), fetch_all=False)
                
                if result is not None:
                    print(f"   ✅ EPI adicionado com sucesso!")
                    
                    # Verificar se aparece agora
                    funcionario_atualizado = FuncionarioQueries.get_with_epis(1)
                    if funcionario_atualizado and funcionario_atualizado.get('epis'):
                        print(f"   ✅ EPI aparece na função get_with_epis!")
                        print(f"   📋 EPIs: {funcionario_atualizado['epis']}")
                    else:
                        print(f"   ❌ EPI ainda não aparece na função get_with_epis")
                else:
                    print(f"   ❌ Erro ao adicionar EPI")
            else:
                print(f"   ⚠️ EPI já existe para Richardson")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a investigação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    investigar_epis()
