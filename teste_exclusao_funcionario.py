#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar a funcionalidade de exclusão de funcionários
"""

import sys
import os
import traceback
from datetime import datetime

# Adicionar o diretório do projeto ao path
sys.path.insert(0, '/var/www/controle-ponto')

try:
    from utils.database import DatabaseManager
    from app_funcionarios import _criar_funcionario
    print("✅ Módulos importados com sucesso")
except ImportError as e:
    print(f"❌ Erro ao importar: {e}")
    sys.exit(1)

def criar_funcionario_teste():
    """Cria um funcionário de teste para exclusão"""
    timestamp = datetime.now().strftime("%H%M%S")
    dados = {
        'nome_completo': 'FUNCIONÁRIO TESTE EXCLUSÃO',
        'cpf': f'999.888.777-{timestamp[-2:]}',
        'rg': '99.888.777-8',
        'data_nascimento': '1990-01-01',
        'sexo': 'M',
        'estado_civil': 'Solteiro',
        'nacionalidade': 'Brasileira',
        'ctps_numero': '9999999',
        'ctps_serie_uf': '999/MG',
        'pis_pasep': '999.88777.88-9',
        'endereco_rua': 'RUA TESTE EXCLUSÃO, 999',
        'endereco_bairro': 'CENTRO',
        'endereco_cidade': 'BELO HORIZONTE',
        'endereco_cep': '30000-000',
        'endereco_estado': 'MG',
        'telefone1': '(31) 99999-9999',
        'telefone2': '(31) 3333-4444',
        'email': '<EMAIL>',
        'cargo': 'ANALISTA DE TESTE',
        'setor_obra': 'DESENVOLVIMENTO',
        'matricula_empresa': f'EXCL{timestamp}',
        'data_admissao': '2025-01-01',
        'tipo_contrato': 'CLT',
        'nivel_acesso': 'Funcionario',
        'turno': 'Diurno',
        'tolerancia_ponto': 5,
        'banco_horas': True,
        'hora_extra': True,
        'status_cadastro': 'Ativo',
        'horas_semanais_obrigatorias': 44.0,
        'empresa_id': 11,
        'jornada_trabalho_id': 1,
        'digital_dedo1': None,
        'digital_dedo2': None,
        'foto_3x4': None,
        'epis': []
    }
    
    try:
        funcionario_id = _criar_funcionario(dados)
        if funcionario_id:
            print(f"✅ Funcionário de teste criado com ID: {funcionario_id}")
            return funcionario_id
        else:
            print("❌ Falha ao criar funcionário de teste")
            return None
    except Exception as e:
        print(f"❌ Erro ao criar funcionário de teste: {e}")
        return None

def testar_exclusao_funcionario(funcionario_id):
    """Testa a exclusão/desligamento de funcionário"""
    try:
        # Importar a função de desligamento
        from utils.database import FuncionarioQueries

        print(f"\n🔍 Testando exclusão do funcionário ID: {funcionario_id}")

        # Verificar se funcionário existe antes da exclusão
        funcionario_antes = FuncionarioQueries.get_by_id(funcionario_id)
        if not funcionario_antes:
            print("❌ Funcionário não encontrado antes da exclusão")
            return False

        print(f"📊 Funcionário encontrado: {funcionario_antes['nome_completo']}")

        # 🛡️ PROTEÇÃO: Só excluir funcionários com "TESTE" no nome
        nome = funcionario_antes['nome_completo'].upper()
        if 'TESTE' not in nome:
            print(f"🛡️ PROTEÇÃO ATIVADA: Funcionário '{funcionario_antes['nome_completo']}' não contém 'TESTE' no nome")
            print("❌ Exclusão cancelada por segurança - apenas funcionários de teste podem ser excluídos")
            return False

        print("✅ Funcionário de teste identificado - prosseguindo com exclusão")

        # Executar exclusão (que na verdade é desligamento)
        sucesso = FuncionarioQueries.delete_funcionario(funcionario_id)
        
        if sucesso:
            print("✅ Exclusão/desligamento executado com sucesso")
            
            # Verificar se funcionário foi movido para tabela de desligados
            funcionario_depois = FuncionarioQueries.get_by_id(funcionario_id)
            if not funcionario_depois:
                print("✅ Funcionário removido da tabela principal")
                
                # Verificar se está na tabela de desligados
                desligado = DatabaseManager.execute_query(
                    "SELECT * FROM funcionarios_desligados WHERE funcionario_id_original = %s",
                    (funcionario_id,),
                    fetch_one=True
                )
                
                if desligado:
                    print("✅ Funcionário encontrado na tabela de desligados")
                    print(f"   Motivo: {desligado.get('motivo_desligamento', 'N/A')}")
                    print(f"   Data: {desligado.get('data_desligamento', 'N/A')}")
                    return True
                else:
                    print("⚠️ Funcionário não encontrado na tabela de desligados")
                    return False
            else:
                print("⚠️ Funcionário ainda existe na tabela principal")
                return False
        else:
            print("❌ Falha na exclusão/desligamento")
            return False
            
    except Exception as e:
        print(f"❌ Erro durante teste de exclusão: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Função principal"""
    print("🧪 TESTE DE EXCLUSÃO DE FUNCIONÁRIOS")
    print("=" * 60)
    print(f"⏰ Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Teste de conexão
    try:
        result = DatabaseManager.execute_query("SELECT 1 as test", fetch_one=True)
        if result and result.get('test') == 1:
            print("✅ Conexão com banco funcionando")
        else:
            print("❌ Conexão com banco falhou")
            return
    except Exception as e:
        print(f"❌ ERRO de conexão: {e}")
        return
    
    # Criar funcionário de teste
    funcionario_id = criar_funcionario_teste()
    if not funcionario_id:
        print("❌ Não foi possível criar funcionário de teste")
        return
    
    # Testar exclusão
    if testar_exclusao_funcionario(funcionario_id):
        print("\n🎉 TESTE DE EXCLUSÃO PASSOU!")
        print("✅ A funcionalidade de exclusão está funcionando corretamente")
        print("✅ Funcionário foi desligado e movido para tabela de histórico")
    else:
        print("\n❌ TESTE DE EXCLUSÃO FALHOU!")
        print("❌ Há um problema com a funcionalidade de exclusão")

if __name__ == "__main__":
    main()
