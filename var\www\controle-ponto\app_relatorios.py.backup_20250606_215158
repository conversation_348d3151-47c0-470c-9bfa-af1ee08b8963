# ========================================
# BLUEPRINT RELATÓRIOS DE PONTO - RLPONTO-WEB
# Data: 08/01/2025
# Descrição: Sistema completo de relatórios e análises
# ========================================

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, make_response
import csv
import io
from datetime import datetime, timedelta, date
from utils.auth import require_login, require_admin
from utils.database import get_db_connection
from pymysql.cursors import DictCursor
from utils.helpers import (
    mascarar_dados_relatorio, RegistroPontoValidator, 
    gerar_dados_grafico_pontos, calcular_horas_trabalhadas,
    formatar_tipo_registro_descricao
)
import logging

# Configurar logger
logger = logging.getLogger(__name__)

# Criar Blueprint
relatorios_bp = Blueprint('relatorios', __name__, url_prefix='/relatorios')

# ========================================
# ROTAS PRINCIPAIS
# ========================================

@relatorios_bp.route('/pontos')
@require_login
def pagina_relatorio_pontos():
    """
    Página principal de relatórios de ponto.
    Exibe formulário de filtros e tabela de resultados.
    """
    try:
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        
        # Obter lista de funcionários para filtro
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        cursor.execute("""
            SELECT id, nome_completo, setor, cargo 
            FROM funcionarios 
            WHERE ativo = 1 
            ORDER BY nome_completo
        """)
        
        funcionarios_raw = cursor.fetchall()
        conn.close()
        
        # Preparar contexto da página
        context = {
            'titulo': 'Relatórios de Ponto',
            'funcionarios': [
                {
                    'id': f['id'],
                    'nome_completo': f['nome_completo'],
                    'setor': f['setor'] or 'Não informado',
                    'cargo': f['cargo'] or 'Não informado'
                }
                for f in funcionarios_raw
            ],
            'tipos_registro': [
                {'value': '', 'text': 'Todos os tipos'},
                {'value': 'entrada_manha', 'text': 'Entrada Manhã'},
                {'value': 'saida_almoco', 'text': 'Saída Almoço'},
                {'value': 'entrada_tarde', 'text': 'Entrada Tarde'},
                {'value': 'saida', 'text': 'Saída'}
            ],
            'metodos_registro': [
                {'value': '', 'text': 'Todos os métodos'},
                {'value': 'biometrico', 'text': 'Biométrico'},
                {'value': 'manual', 'text': 'Manual'}
            ],
            'data_hoje': date.today().strftime('%Y-%m-%d'),
            'data_inicio_mes': date.today().replace(day=1).strftime('%Y-%m-%d'),
            'nivel_acesso': nivel_acesso
        }
        
        return render_template('relatorios/pontos.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar página de relatórios: {str(e)}")
        return redirect(url_for('main.index'))

@relatorios_bp.route('/estatisticas')
@require_login
def pagina_estatisticas():
    """
    Página de estatísticas e dashboards de ponto.
    Exibe gráficos e métricas resumidas.
    """
    try:
        # Obter estatísticas dos últimos 30 dias
        data_inicio = (date.today() - timedelta(days=30)).strftime('%Y-%m-%d')
        data_fim = date.today().strftime('%Y-%m-%d')
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Estatísticas gerais
        cursor.execute("""
            SELECT 
                COUNT(*) as total_registros,
                COUNT(DISTINCT funcionario_id) as funcionarios_ativos,
                SUM(CASE WHEN metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
                SUM(CASE WHEN metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais
            FROM registros_ponto rp
            INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            WHERE DATE(rp.data_hora) BETWEEN %s AND %s
            AND f.ativo = 1
        """, (data_inicio, data_fim))
        
        stats_gerais = cursor.fetchone()
        
        # Dados para gráficos (últimos 7 dias)
        cursor.execute("""
            SELECT * FROM vw_estatisticas_pontos 
            WHERE data_registro BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND CURDATE()
            ORDER BY data_registro DESC
        """)
        
        dados_graficos = cursor.fetchall()
        conn.close()
        
        # Processar dados para gráficos
        labels_dias = []
        dados_registros_diarios = []
        dados_pontualidade = []
        
        for row in dados_graficos:
            labels_dias.append(row[0].strftime('%d/%m'))
            dados_registros_diarios.append(row[1])  # total_registros
            dados_pontualidade.append(row[8])  # atrasos
        
        # Reverter para ordem cronológica
        labels_dias.reverse()
        dados_registros_diarios.reverse()
        dados_pontualidade.reverse()
        
        context = {
            'titulo': 'Estatísticas de Ponto',
            'stats': {
                'total_registros': stats_gerais["total_registros"] or 0,
                'funcionarios_ativos': stats_gerais["funcionarios_ativos"] or 0,
                'registros_biometricos': stats_gerais["registros_biometricos"] or 0,
                'registros_manuais': stats_gerais["registros_manuais"] or 0,
                'percentual_biometrico': round((stats_gerais["registros_biometricos"] or 0) / max(stats_gerais["total_registros"] or 1, 1) * 100, 1)
            },
            'graficos': {
                'labels_dias': labels_dias,
                'dados_registros_diarios': dados_registros_diarios,
                'dados_pontualidade': dados_pontualidade
            },
            'periodo': f"{data_inicio} a {data_fim}"
        }
        
        return render_template('relatorios/estatisticas.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar estatísticas: {str(e)}")
        return redirect(url_for('main.index'))

# ========================================
# APIs PARA RELATÓRIOS
# ========================================

@relatorios_bp.route('/api/buscar-registros', methods=['POST'])
@require_login
def api_buscar_registros():
    """
    API para buscar registros de ponto com filtros.
    Retorna dados paginados e formatados.
    """
    try:
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        
        # Obter e validar dados da requisição
        if not request.is_json:
            logger.error("[BUSCAR REGISTROS] Requisição não contém JSON")
            return jsonify({
                'success': False,
                'message': 'Requisição deve ser JSON'
            }), 400
            
        filtros = request.get_json()
        logger.info(f"[BUSCAR REGISTROS] Filtros recebidos: {filtros}")
        
        # Validar filtros
        validator = RegistroPontoValidator()
        if not validator.validar_filtros_relatorio(filtros):
            erros = validator.get_errors()
            logger.warning(f"[BUSCAR REGISTROS] Filtros inválidos: {erros}")
            return jsonify({
                'success': False,
                'message': 'Filtros inválidos',
                'errors': erros
            }), 400
        
        # Preparar query base
        query_base = """
            SELECT 
                rp.id,
                rp.funcionario_id,
                f.nome_completo,
                f.matricula_empresa,
                f.cpf,
                f.setor,
                f.cargo,
                f.empresa,
                rp.data_hora,
                DATE(rp.data_hora) as data_registro,
                TIME(rp.data_hora) as hora_registro,
                rp.tipo_registro,
                rp.metodo_registro,
                rp.qualidade_biometria,
                rp.observacoes,
                rp.ip_origem,
                rp.criado_em,
                u.usuario as criado_por_usuario,
                CASE 
                    WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atraso'
                    WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atraso'
                    ELSE 'Pontual'
                END AS status_pontualidade
            FROM registros_ponto rp
            INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            LEFT JOIN usuarios u ON rp.criado_por = u.id
            WHERE f.ativo = 1
        """
        
        # Construir condições WHERE baseadas nos filtros
        condicoes = []
        parametros = []
        
        if filtros.get('data_inicio'):
            condicoes.append("DATE(rp.data_hora) >= %s")
            parametros.append(filtros['data_inicio'])
            logger.debug(f"[BUSCAR REGISTROS] Filtro data_inicio: {filtros['data_inicio']}")
        
        if filtros.get('data_fim'):
            condicoes.append("DATE(rp.data_hora) <= %s")
            parametros.append(filtros['data_fim'])
            logger.debug(f"[BUSCAR REGISTROS] Filtro data_fim: {filtros['data_fim']}")
        
        if filtros.get('funcionario_id'):
            condicoes.append("rp.funcionario_id = %s")
            parametros.append(int(filtros['funcionario_id']))
            logger.debug(f"[BUSCAR REGISTROS] Filtro funcionario_id: {filtros['funcionario_id']}")
        
        if filtros.get('setor'):
            condicoes.append("f.setor = %s")
            parametros.append(filtros['setor'])
            logger.debug(f"[BUSCAR REGISTROS] Filtro setor: {filtros['setor']}")
        
        if filtros.get('tipo_registro'):
            condicoes.append("rp.tipo_registro = %s")
            parametros.append(filtros['tipo_registro'])
            logger.debug(f"[BUSCAR REGISTROS] Filtro tipo_registro: {filtros['tipo_registro']}")
        
        if filtros.get('metodo_registro'):
            condicoes.append("rp.metodo_registro = %s")
            parametros.append(filtros['metodo_registro'])
            logger.debug(f"[BUSCAR REGISTROS] Filtro metodo_registro: {filtros['metodo_registro']}")
        
        # Adicionar condições à query
        if condicoes:
            query_base += " AND " + " AND ".join(condicoes)
        
        # Paginação
        page = int(filtros.get('pagina', 1))
        per_page = min(int(filtros.get('registros_por_pagina', 50)), 100)  # Máximo 100 por página
        offset = (page - 1) * per_page
        
        # Query para contar total
        query_count = f"SELECT COUNT(*) as total FROM ({query_base}) as subquery"
        
        # Query final com paginação
        query_final = f"{query_base} ORDER BY rp.data_hora DESC LIMIT %s OFFSET %s"
        parametros_final = parametros + [per_page, offset]
        
        # Executar queries
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        try:
            # Buscar total de registros
            cursor.execute(query_count, parametros)
            total_registros = cursor.fetchone()['total']
            total_pages = (total_registros + per_page - 1) // per_page
            
            # Buscar registros paginados
            cursor.execute(query_final, parametros_final)
            registros = cursor.fetchall()
            
            # Processar registros para adicionar campos esperados e converter datetime
            registros_processados = []
            for registro in registros:
                # Converter datetime para string
                reg_processado = dict(registro)
                
                # Converter campos datetime para string
                if reg_processado.get('data_hora'):
                    reg_processado['data_hora'] = reg_processado['data_hora'].isoformat()
                if reg_processado.get('criado_em'):
                    reg_processado['criado_em'] = reg_processado['criado_em'].isoformat()
                if reg_processado.get('data_registro'):
                    reg_processado['data_registro'] = reg_processado['data_registro'].isoformat()
                if reg_processado.get('hora_registro'):
                    reg_processado['hora_registro'] = str(reg_processado['hora_registro'])
                
                # Adicionar campos descritivos
                reg_processado['tipo_descricao'] = formatar_tipo_registro_descricao(reg_processado.get('tipo_registro', ''))
                reg_processado['metodo_descricao'] = 'Biométrico' if reg_processado.get('metodo_registro') == 'biometrico' else 'Manual'
                
                registros_processados.append(reg_processado)
            
            # Mascarar dados sensíveis
            registros_mascarados = [mascarar_dados_relatorio(r) for r in registros_processados]
            
            # Buscar estatísticas
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_registros,
                    COUNT(DISTINCT funcionario_id) as funcionarios_distintos,
                    SUM(CASE WHEN metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
                    SUM(CASE WHEN metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais
                FROM registros_ponto
                WHERE DATE(data_hora) BETWEEN %s AND %s
            """, (filtros.get('data_inicio'), filtros.get('data_fim')))
            
            estatisticas = cursor.fetchone()
            
            # Montar resposta
            response_data = {
                'success': True,
                'registros': registros_mascarados,
                'total_registros': total_registros,
                'total_paginas': total_pages,
                'pagina_atual': page,
                'estatisticas': estatisticas
            }
            
            logger.info(f"[BUSCAR REGISTROS] Sucesso - Total: {total_registros}, Página: {page}/{total_pages}")
            return jsonify(response_data)
            
        except Exception as e:
            import traceback
            # Gerar ID único para o erro
            error_id = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Log detalhado do erro
            logger.error(f"[BUSCAR REGISTROS] Erro ID: {error_id}")
            logger.error(f"[BUSCAR REGISTROS] Tipo de erro: {type(e).__name__}")
            logger.error(f"[BUSCAR REGISTROS] Mensagem: {str(e)}")
            logger.error(f"[BUSCAR REGISTROS] Traceback:\n{traceback.format_exc()}")
            logger.error(f"[BUSCAR REGISTROS] Filtros que causaram erro: {filtros}")
            
            return jsonify({
                'success': False,
                'message': f'Erro interno do sistema\nOcorreu um erro inesperado. ID: {error_id}\n\nCódigo: 500'
            }), 500
            
        finally:
            conn.close()
        
    except Exception as e:
        logger.error(f"[BUSCAR REGISTROS] Erro geral: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao buscar registros: {str(e)}'
        }), 500

@relatorios_bp.route('/api/exportar-csv', methods=['POST'])
@require_login
def api_exportar_csv():
    """
    API para exportar registros em formato CSV.
    Aplica mesmos filtros da consulta principal.
    """
    try:
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        
        # Obter filtros da requisição
        filtros = request.get_json() or {}
        
        # Validar filtros
        validator = RegistroPontoValidator()
        if not validator.validar_filtros_relatorio(filtros):
            return jsonify({
                'success': False,
                'message': 'Filtros inválidos',
                'errors': validator.get_errors()
            }), 400
        
        # Limitar exportação (performance)
        limite_exportacao = 5000
        
        # Usar view otimizada
        query_base = "SELECT * FROM vw_relatorio_pontos WHERE 1=1"
        parametros = []
        
        # Aplicar filtros
        if filtros.get('data_inicio'):
            query_base += " AND data_registro >= %s"
            parametros.append(filtros['data_inicio'])
        
        if filtros.get('data_fim'):
            query_base += " AND data_registro <= %s"
            parametros.append(filtros['data_fim'])
        
        if filtros.get('funcionario_id'):
            query_base += " AND funcionario_id = %s"
            parametros.append(int(filtros['funcionario_id']))
        
        if filtros.get('tipo_registro'):
            query_base += " AND tipo_registro = %s"
            parametros.append(filtros['tipo_registro'])
        
        if filtros.get('metodo_registro'):
            query_base += " AND metodo_registro = %s"
            parametros.append(filtros['metodo_registro'])
        
        query_final = f"{query_base} ORDER BY data_hora DESC LIMIT {limite_exportacao}"
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        cursor.execute(query_final, parametros)
        registros = cursor.fetchall()
        conn.close()
        
        # Criar arquivo CSV em memória
        output = io.StringIO()
        writer = csv.writer(output, delimiter=';')
        
        # Cabeçalhos
        cabecalhos = [
            'ID', 'Nome Completo', 'CPF', 'Matrícula', 'Setor', 'Cargo',
            'Data', 'Hora', 'Tipo Registro', 'Método', 'Qualidade Biometria',
            'Status Pontualidade', 'Observações', 'Criado Por', 'IP Origem'
        ]
        writer.writerow(cabecalhos)
        
        # Dados
        for reg in registros:
            # Aplicar mascaramento baseado no nível de acesso
            cpf_exibir = reg[4] if nivel_acesso == 'admin' else reg[5]  # cpf ou cpf_exibicao
            
            linha = [
                reg[0],  # id
                reg[2],  # nome_completo
                cpf_exibir,  # cpf (mascarado ou não)
                reg[3] or '',  # matricula_empresa
                reg[16] or 'Não informado',  # setor
                reg[17] or 'Não informado',  # cargo
                reg[7].strftime('%d/%m/%Y') if reg[7] else '',  # data_registro
                reg[8].strftime('%H:%M') if reg[8] else '',  # hora_registro
                reg[11],  # tipo_descricao
                reg[13],  # metodo_descricao
                reg[18] if reg[18] is not None else '',  # qualidade_biometria
                reg[24],  # status_pontualidade
                reg[19] or '',  # observacoes
                reg[21] or '',  # criado_por_usuario
                reg[20] or ''  # ip_origem
            ]
            writer.writerow(linha)
        
        # Preparar resposta
        csv_content = output.getvalue()
        output.close()
        
        # Nome do arquivo com timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'relatorio_pontos_{timestamp}.csv'
        
        response = make_response(csv_content)
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename={filename}'
        
        # Log da exportação
        logger.info(f"Exportação CSV realizada - Usuário: {session.get('user_id')} - Registros: {len(registros)}")
        
        return response
        
    except Exception as e:
        logger.error(f"Erro ao exportar CSV: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao exportar CSV: {str(e)}'
        }), 500

@relatorios_bp.route('/api/resumo-funcionario/<int:funcionario_id>')
@require_login
def api_resumo_funcionario(funcionario_id):
    """
    API para obter resumo de registros de um funcionário específico.
    Usado em modais de detalhes.
    """
    try:
        periodo_dias = int(request.args.get('periodo', 30))  # Últimos 30 dias por padrão
        data_inicio = (date.today() - timedelta(days=periodo_dias)).strftime('%Y-%m-%d')
        data_fim = date.today().strftime('%Y-%m-%d')
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Informações do funcionário
        cursor.execute("""
            SELECT nome_completo, cpf, setor, cargo, matricula_empresa, empresa, foto_url
            FROM funcionarios 
            WHERE id = %s AND ativo = 1
        """, (funcionario_id,))
        
        funcionario_info = cursor.fetchone()
        if not funcionario_info:
            conn.close()
            return jsonify({
                'success': False,
                'message': 'Funcionário não encontrado'
            }), 404
        
        # Registros do período
        cursor.execute("""
            SELECT 
                DATE(data_hora) as data_trabalho,
                tipo_registro,
                TIME(data_hora) as hora_registro,
                metodo_registro,
                CASE 
                    WHEN tipo_registro = 'entrada_manha' AND TIME(data_hora) > '08:10:00' THEN 'Atraso'
                    WHEN tipo_registro = 'entrada_tarde' AND TIME(data_hora) > '13:10:00' THEN 'Atraso'
                    ELSE 'Pontual'
                END AS status_pontualidade
            FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND DATE(data_hora) BETWEEN %s AND %s
            ORDER BY data_hora DESC
        """, (funcionario_id, data_inicio, data_fim))
        
        registros = cursor.fetchall()
        
        # Estatísticas do período
        cursor.execute("""
            SELECT 
                COUNT(*) as total_registros,
                COUNT(DISTINCT DATE(data_hora)) as dias_trabalhados,
                SUM(CASE WHEN metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
                SUM(CASE WHEN metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais,
                SUM(CASE 
                    WHEN (tipo_registro = 'entrada_manha' AND TIME(data_hora) > '08:10:00') OR
                         (tipo_registro = 'entrada_tarde' AND TIME(data_hora) > '13:10:00')
                    THEN 1 ELSE 0 
                END) as total_atrasos
            FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND DATE(data_hora) BETWEEN %s AND %s
        """, (funcionario_id, data_inicio, data_fim))
        
        estatisticas = cursor.fetchone()
        conn.close()
        
        # Processar registros por dia
        registros_por_dia = {}
        for reg in registros:
            data_str = reg[0].strftime('%Y-%m-%d')
            if data_str not in registros_por_dia:
                registros_por_dia[data_str] = []
            
            registros_por_dia[data_str].append({
                'tipo_registro': reg[1],
                'tipo_descricao': formatar_tipo_registro_descricao(reg[1]),
                'hora_registro': reg[2].strftime('%H:%M') if reg[2] else '',
                'metodo_registro': reg[3],
                'status_pontualidade': reg[4]
            })
        
        # Calcular horas trabalhadas por dia
        horas_por_dia = []
        for data_str, regs in registros_por_dia.items():
            horas_info = calcular_horas_trabalhadas(regs)
            horas_por_dia.append({
                'data': data_str,
                'data_formatada': datetime.strptime(data_str, '%Y-%m-%d').strftime('%d/%m/%Y'),
                'registros': regs,
                'horas_trabalhadas': horas_info['horas_trabalhadas'],
                'completo': horas_info['completo'],
                'observacao': horas_info['observacao']
            })
        
        # Ordenar por data decrescente
        horas_por_dia.sort(key=lambda x: x['data'], reverse=True)
        
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        
        return jsonify({
            'success': True,
            'funcionario': {
                'nome_completo': funcionario_info[0],
                'cpf': funcionario_info[1] if nivel_acesso == 'admin' else '***.***.***-**',
                'setor': funcionario_info[2] or 'Não informado',
                'cargo': funcionario_info[3] or 'Não informado',
                'matricula_empresa': funcionario_info[4] or '',
                'empresa': funcionario_info[5] or 'Não informado',
                'foto_url': funcionario_info[6] or '/static/images/funcionario_sem_foto.svg'
            },
            'estatisticas': {
                'total_registros': estatisticas[0] or 0,
                'dias_trabalhados': estatisticas[1] or 0,
                'registros_biometricos': estatisticas[2] or 0,
                'registros_manuais': estatisticas[3] or 0,
                'total_atrasos': estatisticas[4] or 0,
                'percentual_biometrico': round((estatisticas[2] or 0) / max(estatisticas[0] or 1, 1) * 100, 1)
            },
            'registros_por_dia': horas_por_dia,
            'periodo': f"{data_inicio} a {data_fim}"
        })
        
    except Exception as e:
        logger.error(f"Erro ao buscar resumo do funcionário {funcionario_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao buscar resumo: {str(e)}'
        }), 500

@relatorios_bp.route('/api/dados-graficos')
@require_login
def api_dados_graficos():
    """
    API para buscar dados dos gráficos de horas trabalhadas e pontualidade.
    """
    try:
        # Obter parâmetros da requisição
        funcionario_id = request.args.get('funcionario_id')
        setor = request.args.get('setor')
        data_inicial = request.args.get('data_inicial', (date.today() - timedelta(days=30)).strftime('%Y-%m-%d'))
        data_final = request.args.get('data_final', date.today().strftime('%Y-%m-%d'))

        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        # Construir condições da query
        conditions = ["DATE(rp.data_hora) BETWEEN %s AND %s"]
        params = [data_inicial, data_final]

        if funcionario_id:
            conditions.append("rp.funcionario_id = %s")
            params.append(funcionario_id)
        
        if setor:
            conditions.append("f.setor = %s")
            params.append(setor)

        where_clause = " AND ".join(conditions)

        # Buscar dados de horas trabalhadas
        cursor.execute(f"""
            SELECT 
                ht.data_registro,
                ht.total_horas_trabalhadas,
                COUNT(DISTINCT ht.funcionario_id) as funcionarios
            FROM vw_horas_trabalhadas ht
            INNER JOIN funcionarios f ON ht.funcionario_id = f.id
            WHERE DATE(ht.data_registro) BETWEEN %s AND %s
            {' AND f.setor = %s' if setor else ''}
            {' AND ht.funcionario_id = %s' if funcionario_id else ''}
            GROUP BY ht.data_registro
            ORDER BY ht.data_registro
        """, params)

        dados_horas = cursor.fetchall()

        # Buscar dados de pontualidade
        cursor.execute(f"""
            SELECT 
                ap.mes_ano,
                ROUND(AVG(ap.percentual_pontualidade), 2) as taxa_pontualidade,
                SUM(ap.atrasos_manha + ap.atrasos_tarde) as total_atrasos,
                COUNT(DISTINCT ap.funcionario_id) as funcionarios
            FROM vw_analise_pontualidade ap
            INNER JOIN funcionarios f ON ap.funcionario_id = f.id
            WHERE DATE_FORMAT(STR_TO_DATE(ap.mes_ano, '%Y-%m'), '%Y-%m-%d') BETWEEN %s AND %s
            {' AND f.setor = %s' if setor else ''}
            {' AND ap.funcionario_id = %s' if funcionario_id else ''}
            GROUP BY ap.mes_ano
            ORDER BY ap.mes_ano
        """, params)

        dados_pontualidade = cursor.fetchall()

        # Calcular resumo
        cursor.execute(f"""
            SELECT 
                SEC_TO_TIME(SUM(TIME_TO_SEC(ht.total_horas_trabalhadas))) as total_horas_mes,
                SEC_TO_TIME(AVG(TIME_TO_SEC(ht.total_horas_trabalhadas))) as media_horas_dia,
                COUNT(DISTINCT ht.data_registro) as dias_trabalhados,
                ROUND(AVG(ap.percentual_pontualidade), 2) as taxa_pontualidade,
                SUM(ap.atrasos_manha + ap.atrasos_tarde) as total_atrasos
            FROM vw_horas_trabalhadas ht
            LEFT JOIN vw_analise_pontualidade ap ON ht.funcionario_id = ap.funcionario_id
            INNER JOIN funcionarios f ON ht.funcionario_id = f.id
            WHERE {where_clause}
        """, params)

        resumo = cursor.fetchone()
        conn.close()

        # Processar dados para resposta
        dados_processados = {
            'horas': {
                'labels': [d['data_registro'].strftime('%d/%m/%Y') for d in dados_horas],
                'valores': [str(d['total_horas_trabalhadas']) for d in dados_horas]
            },
            'pontualidade': {
                'labels': [d['mes_ano'] for d in dados_pontualidade],
                'valores': [float(d['taxa_pontualidade']) for d in dados_pontualidade]
            },
            'resumo': {
                'total_horas_mes': str(resumo['total_horas_mes']) if resumo['total_horas_mes'] else '00:00:00',
                'media_horas_dia': str(resumo['media_horas_dia']) if resumo['media_horas_dia'] else '00:00:00',
                'dias_trabalhados': resumo['dias_trabalhados'] or 0,
                'taxa_pontualidade': float(resumo['taxa_pontualidade'] or 0),
                'total_atrasos': resumo['total_atrasos'] or 0
            }
        }

        return jsonify({
            'success': True,
            'dados': dados_processados
        })

    except Exception as e:
        logger.error(f"Erro ao buscar dados dos gráficos: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao buscar dados dos gráficos: {str(e)}'
        }), 500

# ========================================
# TRATAMENTO DE ERROS
# ========================================

@relatorios_bp.errorhandler(404)
def handle_404(error):
    """Tratamento de erro 404 para o blueprint."""
    return jsonify({
        'success': False,
        'message': 'Endpoint não encontrado'
    }), 404

@relatorios_bp.errorhandler(500)
def handle_500(error):
    """Tratamento de erro 500 para o blueprint."""
    logger.error(f"Erro interno no blueprint relatorios: {str(error)}")
    return jsonify({
        'success': False,
        'message': 'Erro interno do servidor'
    }), 500

# ========================================
# FIM DO BLUEPRINT RELATÓRIOS
# ========================================