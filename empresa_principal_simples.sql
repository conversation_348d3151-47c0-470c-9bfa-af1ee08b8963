-- ================================================================
-- IMPLEMENTAÇÃO SIMPLIFICADA: EMPRESA PRINCIPAL E GESTÃO DE CLIENTES
-- Sistema: RLPONTO-WEB
-- Data: 03/07/2025
-- ================================================================

USE controle_ponto;

-- ================================================================
-- 1. MODIFICAÇÕES NA TABELA EMPRESAS
-- ================================================================

-- Adicionar campo empresa_principal (verificar se já existe)
SELECT 'Adicionando campo empresa_principal...' as status;
ALTER TABLE empresas 
ADD COLUMN empresa_principal BOOLEAN DEFAULT FALSE 
COMMENT 'Define se é a empresa principal/proprietária do sistema';

-- Adicionar campo empresa_matriz_id (verificar se já existe)
SELECT 'Adicionando campo empresa_matriz_id...' as status;
ALTER TABLE empresas 
ADD COLUMN empresa_matriz_id INT NULL 
COMMENT 'ID da empresa matriz (para filiais)';

-- Adicionar campo tipo_empresa (verificar se já existe)
SELECT 'Adicionando campo tipo_empresa...' as status;
ALTER TABLE empresas 
ADD COLUMN tipo_empresa ENUM('principal', 'cliente', 'filial', 'independente') 
DEFAULT 'independente' 
COMMENT 'Tipo da empresa no sistema';

-- Adicionar índices
SELECT 'Adicionando índices...' as status;
ALTER TABLE empresas ADD INDEX idx_empresa_principal (empresa_principal);
ALTER TABLE empresas ADD INDEX idx_empresa_matriz (empresa_matriz_id);
ALTER TABLE empresas ADD INDEX idx_tipo_empresa (tipo_empresa);

-- Adicionar foreign key para empresa matriz
ALTER TABLE empresas 
ADD CONSTRAINT fk_empresa_matriz 
FOREIGN KEY (empresa_matriz_id) REFERENCES empresas(id) ON DELETE SET NULL;

-- ================================================================
-- 2. TABELA EMPRESA_CLIENTES (Relacionamento Principal-Cliente)
-- ================================================================

SELECT 'Criando tabela empresa_clientes...' as status;
CREATE TABLE IF NOT EXISTS empresa_clientes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_principal_id INT NOT NULL COMMENT 'ID da empresa principal',
    empresa_cliente_id INT NOT NULL COMMENT 'ID da empresa que atua como cliente',
    nome_contrato VARCHAR(200) NULL COMMENT 'Nome do contrato/projeto',
    codigo_contrato VARCHAR(50) NULL COMMENT 'Código único do contrato',
    descricao_projeto TEXT NULL COMMENT 'Descrição detalhada do projeto',
    data_inicio DATE NOT NULL COMMENT 'Data de início do contrato',
    data_fim DATE NULL COMMENT 'Data prevista de fim do contrato',
    valor_contrato DECIMAL(15,2) NULL COMMENT 'Valor total do contrato',
    status_contrato ENUM('ativo', 'pausado', 'finalizado', 'cancelado') DEFAULT 'ativo',
    ativo BOOLEAN DEFAULT TRUE,
    observacoes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL COMMENT 'ID do usuário que criou',
    
    -- Índices e constraints
    FOREIGN KEY (empresa_principal_id) REFERENCES empresas(id) ON DELETE CASCADE,
    FOREIGN KEY (empresa_cliente_id) REFERENCES empresas(id) ON DELETE CASCADE,
    UNIQUE KEY unique_cliente_principal (empresa_principal_id, empresa_cliente_id),
    INDEX idx_empresa_principal (empresa_principal_id),
    INDEX idx_empresa_cliente (empresa_cliente_id),
    INDEX idx_status_contrato (status_contrato),
    INDEX idx_data_inicio (data_inicio),
    INDEX idx_ativo (ativo)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ================================================================
-- 3. TABELA FUNCIONARIO_ALOCACOES (Alocação de Funcionários)
-- ================================================================

SELECT 'Criando tabela funcionario_alocacoes...' as status;
CREATE TABLE IF NOT EXISTS funcionario_alocacoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT UNSIGNED NOT NULL COMMENT 'ID do funcionário alocado',
    empresa_cliente_id INT NOT NULL COMMENT 'ID da empresa cliente',
    contrato_id INT NULL COMMENT 'ID do contrato específico (referência a empresa_clientes)',
    jornada_trabalho_id INT NOT NULL COMMENT 'ID da jornada de trabalho herdada',
    cargo_no_cliente VARCHAR(100) NULL COMMENT 'Cargo específico no cliente',
    data_inicio DATE NOT NULL COMMENT 'Data de início da alocação',
    data_fim DATE NULL COMMENT 'Data de fim da alocação',
    percentual_alocacao DECIMAL(5,2) DEFAULT 100.00 COMMENT 'Percentual de tempo alocado (0-100%)',
    valor_hora DECIMAL(10,2) NULL COMMENT 'Valor da hora para este cliente',
    ativo BOOLEAN DEFAULT TRUE,
    observacoes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL COMMENT 'ID do usuário que criou',
    
    -- Índices e constraints
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
    FOREIGN KEY (empresa_cliente_id) REFERENCES empresas(id) ON DELETE CASCADE,
    FOREIGN KEY (contrato_id) REFERENCES empresa_clientes(id) ON DELETE SET NULL,
    FOREIGN KEY (jornada_trabalho_id) REFERENCES jornadas_trabalho(id) ON DELETE RESTRICT,
    INDEX idx_funcionario (funcionario_id),
    INDEX idx_empresa_cliente (empresa_cliente_id),
    INDEX idx_contrato (contrato_id),
    INDEX idx_jornada (jornada_trabalho_id),
    INDEX idx_data_inicio (data_inicio),
    INDEX idx_ativo (ativo),
    INDEX idx_periodo (data_inicio, data_fim)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ================================================================
-- 4. CONFIGURAÇÕES INICIAIS
-- ================================================================

SELECT 'Inserindo configurações do sistema...' as status;
INSERT IGNORE INTO configuracoes_sistema (chave, valor, descricao, tipo, categoria, editavel) VALUES
('empresa_principal_id', '0', 'ID da empresa principal do sistema', 'integer', 'empresa', 1),
('permitir_multiplas_alocacoes', 'true', 'Permitir funcionário em múltiplos clientes simultaneamente', 'boolean', 'funcionarios', 1),
('heranca_jornada_automatica', 'true', 'Herança automática de jornada ao alocar funcionário', 'boolean', 'funcionarios', 1);

-- ================================================================
-- 5. VERIFICAÇÃO FINAL
-- ================================================================

SELECT 'Verificando estrutura criada...' as status;

SELECT COUNT(*) as colunas_empresas 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'empresas' 
AND COLUMN_NAME IN ('empresa_principal', 'empresa_matriz_id', 'tipo_empresa');

SELECT COUNT(*) as tabelas_criadas 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME IN ('empresa_clientes', 'funcionario_alocacoes');

SELECT 'Estrutura de Empresa Principal criada com sucesso!' as resultado;
