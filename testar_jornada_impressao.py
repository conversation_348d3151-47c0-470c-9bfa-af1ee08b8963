#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
sys.path.append('.')

print('🔍 DEBUG: Testando dados de jornada para impressão')

try:
    from utils.database import FuncionarioQueries
    
    funcionario_id = 11
    print(f'👤 Testando funcionário ID: {funcionario_id}')
    
    funcionario = FuncionarioQueries.get_with_epis(funcionario_id)
    
    if funcionario:
        print(f'✅ Funcionário encontrado: {funcionario.get("nome_completo")}')
        print(f'🏢 Empresa: {funcionario.get("empresa_nome")}')
        
        print('\n=== DADOS DE JORNADA ===')
        print(f'nome_jornada: {funcionario.get("nome_jornada")}')
        print(f'jornada_seg_qui_entrada: {funcionario.get("jornada_seg_qui_entrada")}')
        print(f'jornada_seg_qui_saida: {funcionario.get("jornada_seg_qui_saida")}')
        print(f'jornada_sex_entrada: {funcionario.get("jornada_sex_entrada")}')
        print(f'jornada_sex_saida: {funcionario.get("jornada_sex_saida")}')
        print(f'jornada_intervalo_entrada: {funcionario.get("jornada_intervalo_entrada")}')
        print(f'jornada_intervalo_saida: {funcionario.get("jornada_intervalo_saida")}')
        print(f'tolerancia_entrada_minutos: {funcionario.get("tolerancia_entrada_minutos")}')
        
        print('\n=== DADOS DE HORÁRIO (FALLBACK) ===')
        print(f'nome_horario: {funcionario.get("nome_horario")}')
        print(f'entrada_manha: {funcionario.get("entrada_manha")}')
        print(f'saida: {funcionario.get("saida")}')
        print(f'saida_almoco: {funcionario.get("saida_almoco")}')
        print(f'entrada_tarde: {funcionario.get("entrada_tarde")}')
        
        print('\n=== OUTROS CAMPOS RELEVANTES ===')
        print(f'jornada_trabalho_id: {funcionario.get("jornada_trabalho_id")}')
        print(f'horario_trabalho_id: {funcionario.get("horario_trabalho_id")}')
        print(f'alocacao_id: {funcionario.get("alocacao_id")}')
        print(f'empresa_cliente_id: {funcionario.get("empresa_cliente_id")}')
        
        # Verificar se há algum dado de jornada
        tem_jornada = any([
            funcionario.get("nome_jornada"),
            funcionario.get("jornada_seg_qui_entrada"),
            funcionario.get("nome_horario"),
            funcionario.get("entrada_manha")
        ])
        
        print(f'\n📊 Tem dados de jornada: {tem_jornada}')
        
    else:
        print(f'❌ Funcionário {funcionario_id} não encontrado')

except Exception as e:
    print(f'❌ Erro: {e}')
    import traceback
    traceback.print_exc()

# Verificar jornadas da empresa
print('\n=== VERIFICANDO JORNADAS DA EMPRESA ===')
try:
    from utils.database import DatabaseManager
    empresa_id = 2  # Msv Engenharia
    jornadas = DatabaseManager.execute_query('SELECT * FROM jornadas_trabalho WHERE empresa_id = %s', (empresa_id,))
    print(f'Jornadas encontradas para empresa {empresa_id}: {len(jornadas)}')
    for j in jornadas:
        print(f'  - {j["nome_jornada"]} (ID: {j["id"]}, Padrão: {j["padrao"]}, Ativa: {j["ativa"]})')

    print('\n=== VERIFICANDO FUNCIONÁRIOS COM JORNADA ===')
    funcionarios = DatabaseManager.execute_query('SELECT id, nome_completo, jornada_trabalho_id FROM funcionarios WHERE jornada_trabalho_id IS NOT NULL LIMIT 5')
    print(f'Funcionários com jornada: {len(funcionarios)}')
    for f in funcionarios:
        print(f'  - {f["nome_completo"]} (ID: {f["id"]}, Jornada ID: {f["jornada_trabalho_id"]})')

    # Testar um funcionário que tem jornada
    if funcionarios:
        func_teste = funcionarios[0]
        print(f'\n=== TESTANDO FUNCIONÁRIO COM JORNADA: {func_teste["nome_completo"]} ===')
        funcionario_com_jornada = FuncionarioQueries.get_with_epis(func_teste["id"])
        if funcionario_com_jornada:
            print(f'nome_jornada: {funcionario_com_jornada.get("nome_jornada")}')
            print(f'jornada_seg_qui_entrada: {funcionario_com_jornada.get("jornada_seg_qui_entrada")}')
            print(f'jornada_seg_qui_saida: {funcionario_com_jornada.get("jornada_seg_qui_saida")}')

except Exception as e2:
    print(f'❌ Erro na verificação: {e2}')
