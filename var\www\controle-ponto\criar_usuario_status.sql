-- =====================================
-- SCRIPT PARA CRIAR USUÁRIO "STATUS"
-- RLPONTO-WEB v1.0
-- Data: 08/06/2025
-- =====================================

USE controle_ponto;

-- Inserir usuário "status" na tabela usuarios
INSERT INTO usuarios (usuario, senha, nome_completo, email, ativo, nivel_acesso, data_criacao) 
VALUES ('status', '12345678', 'Usuário Status do Sistema', '<EMAIL>', 1, 'usuario', NOW())
ON DUPLICATE KEY UPDATE 
    senha = '12345678',
    nome_completo = 'Usuário Status do Sistema',
    email = '<EMAIL>',
    ativo = 1,
    nivel_acesso = 'usuario';

-- Obter o ID do usuário "status"
SET @status_user_id = (SELECT id FROM usuarios WHERE usuario = 'status');

-- Inserir permissões específicas para o usuário status na tabela permissoes
INSERT INTO permissoes (usuario_id, nivel_acesso, data_criacao) 
VALUES (@status_user_id, 'usuario', NOW())
ON DUPLICATE KEY UPDATE 
    nivel_acesso = 'usuario';

-- Verificar se o usuário foi criado com sucesso
SELECT 
    u.id,
    u.usuario,
    u.nome_completo,
    u.email,
    u.ativo,
    u.nivel_acesso,
    u.data_criacao,
    p.nivel_acesso as permissao_nivel
FROM usuarios u
LEFT JOIN permissoes p ON u.id = p.usuario_id
WHERE u.usuario = 'status';

-- Mostrar estatísticas finais
SELECT 
    'Usuario status criado com sucesso!' as status_criacao,
    (SELECT COUNT(*) FROM usuarios) as total_usuarios,
    (SELECT COUNT(*) FROM permissoes) as total_permissoes; 