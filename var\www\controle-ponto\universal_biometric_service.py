#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Serviço Biométrico Universal - RLPONTO-WEB v2.0
===============================================

Sistema universal de biometria que suporta qualquer leitor biométrico
com drivers Windows instalados, removendo dependência de hardware específico.

Características:
✓ Auto-detecção de dispositivos via Windows Biometric Framework
✓ Suporte a múltiplos fabricantes (SecuGen, Suprema, Nitgen, etc.)
✓ Sistema de registro permanente de dispositivos
✓ API REST padronizada
✓ Fallback inteligente para simulação
✓ Zero referências a marcas específicas

Autor: Richardson Rodrigues - AiNexus Tecnologia  
Sistema: RLPONTO-WEB v1.0
Data: Junho 2025
"""

import json
import logging
import os
import time
import traceback
import subprocess
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path
import platform
import hashlib
import uuid

from flask import Flask, request, jsonify
from flask_cors import CORS

# Configuração de logging
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / "universal_biometric.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("universal-biometric")

# Constantes globais
SERVICE_PORT = 5001
CONFIG_FILE = "biometric_devices.json"

class UniversalBiometricFramework:
    """Framework universal para detecção e uso de leitores biométricos"""
    
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        self.devices = []
        self.registered_devices = self._load_device_registry()
        logger.info("Universal Biometric Framework iniciado")
        
    def _load_device_registry(self) -> Dict[str, Dict]:
        """Carrega registro de dispositivos salvos"""
        if Path(CONFIG_FILE).exists():
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Erro ao carregar registro: {e}")
        return {}
    
    def _save_device_registry(self):
        """Salva registro de dispositivos"""
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.registered_devices, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Erro ao salvar registro: {e}")
    
    def discover_devices(self) -> List[Dict[str, Any]]:
        """Descobre todos os leitores biométricos disponíveis"""
        devices = []
        
        # Método 1: Windows Biometric Framework
        if self.is_windows:
            devices.extend(self._detect_via_wbf())
        
        # Método 2: Detecção USB genérica
        devices.extend(self._detect_via_usb())
        
        # Dispositivos reais apenas - nenhum simulador
        
        self.devices = devices
        logger.info(f"Descobertos {len(devices)} dispositivos")
        return devices
    
    def _detect_via_wbf(self) -> List[Dict[str, Any]]:
        """Detecção via Windows Biometric Framework"""
        devices = []
        try:
            # Verifica WBF via PowerShell
            ps_script = '''
            Get-WmiObject -Class Win32_PnPEntity | Where-Object {
                ($_.Name -match "biometric|fingerprint|sensor") -and ($_.Status -eq "OK")
            } | Select-Object Name, DeviceID, Manufacturer
            '''
            
            result = subprocess.run(['powershell', '-Command', ps_script],
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                for line in result.stdout.split('\n')[3:]:
                    if line.strip() and not line.startswith('-'):
                        device = self._parse_device_line(line)
                        if device:
                            devices.append(device)
                            
        except Exception as e:
            logger.warning(f"Detecção WBF falhou: {e}")
        
        return devices
    
    def _detect_via_usb(self) -> List[Dict[str, Any]]:
        """Detecção via USB para dispositivos conhecidos"""
        devices = []
        
        # Fabricantes conhecidos de leitores biométricos
        known_vendors = {
            '1234': 'SecuGen', '16d1': 'Suprema', '08ff': 'AuthenTec',
            '0483': 'Integrated Biometrics', '27c6': 'Goodix',
            '06cb': 'Synaptics', '138a': 'Validity Sensors'
        }
        
        try:
            if self.is_windows:
                ps_script = '''
                Get-WmiObject -Class Win32_USBHub | Where-Object {
                    $_.DeviceID -match "VID_" -and $_.Status -eq "OK"
                } | Select-Object DeviceID, Name
                '''
                
                result = subprocess.run(['powershell', '-Command', ps_script],
                                      capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    import re
                    for line in result.stdout.split('\n'):
                        if 'VID_' in line:
                            vid_match = re.search(r'VID_([0-9A-F]{4})', line.upper())
                            if vid_match:
                                vid = vid_match.group(1).lower()
                                if vid in known_vendors:
                                    device = self._create_usb_device(line, known_vendors[vid])
                                    devices.append(device)
                                    
        except Exception as e:
            logger.warning(f"Detecção USB falhou: {e}")
        
        return devices
    
    def _parse_device_line(self, line: str) -> Dict[str, Any]:
        """Parseia linha de dispositivo WBF"""
        try:
            parts = line.strip().split()
            if len(parts) < 2:
                return None
                
            name = ' '.join(parts[:-1])
            device_id = hashlib.md5(line.encode()).hexdigest()[:16]
            
            # Detecta fabricante
            manufacturer = "Unknown"
            for vendor in ['SecuGen', 'Suprema', 'Nitgen', 'Integrated', 'Goodix']:
                if vendor.lower() in name.lower():
                    manufacturer = vendor
                    break
            
            return {
                'id': device_id,
                'name': name,
                'manufacturer': manufacturer,
                'type': 'fingerprint',
                'status': 'ready',
                'driver': 'wbf'
            }
        except Exception:
            return None
    
    def _create_usb_device(self, device_line: str, manufacturer: str) -> Dict[str, Any]:
        """Cria objeto de dispositivo USB"""
        device_id = hashlib.md5((device_line + manufacturer).encode()).hexdigest()[:16]
        
        return {
            'id': device_id,
            'name': f'{manufacturer} Biometric Reader',
            'manufacturer': manufacturer,
            'type': 'fingerprint',
            'status': 'detected',
            'driver': 'usb'
        }
    
# Função de simulador removida - apenas dispositivos reais
    
    def register_device(self, device_id: str, device_info: Dict) -> bool:
        """Registra dispositivo para uso permanente"""
        try:
            self.registered_devices[device_id] = {
                'device_info': device_info,
                'registered_at': datetime.now().isoformat(),
                'usage_count': 0
            }
            self._save_device_registry()
            logger.info(f"Dispositivo registrado: {device_info.get('name')}")
            return True
        except Exception as e:
            logger.error(f"Erro ao registrar: {e}")
            return False
    
    def capture_fingerprint(self, device_id: str = None) -> Dict[str, Any]:
        """Captura impressão digital do dispositivo especificado"""
        target_device = None
        
        if device_id:
            target_device = next((d for d in self.devices if d['id'] == device_id), None)
        else:
            target_device = self.devices[0] if self.devices else None
        
        if not target_device:
            return {'success': False, 'error': 'Nenhum dispositivo disponível'}
        
        logger.info(f"Capturando com: {target_device['name']}")
        
        # CAPTURA REAL - Integrará com drivers de hardware
        # Por enquanto retorna erro até implementação completa dos drivers
        return {
            'success': False,
            'error': 'Captura real ainda não implementada - requer integração com drivers específicos'
        }

class UniversalBiometricService:
    """Serviço principal HTTP para biometria universal"""
    
    def __init__(self):
        self.framework = UniversalBiometricFramework()
        self.app = Flask(__name__)
        CORS(self.app)
        self._setup_routes()
        
    def _setup_routes(self):
        """Configura rotas da API"""
        
        @self.app.route('/test', methods=['GET'])
        def test():
            return jsonify({
                'status': 'ok',
                'service': 'universal-biometric',
                'version': '2.0.0'
            })
        
        @self.app.route('/devices/discover', methods=['GET', 'POST'])
        def discover_devices():
            try:
                devices = self.framework.discover_devices()
                return jsonify({
                    'success': True,
                    'devices': devices,
                    'count': len(devices)
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.route('/devices/register', methods=['POST'])
        def register_device():
            try:
                data = request.get_json()
                device_id = data.get('device_id')
                device_info = data.get('device_info')
                
                if not device_id or not device_info:
                    return jsonify({
                        'success': False,
                        'error': 'device_id e device_info obrigatórios'
                    }), 400
                
                success = self.framework.register_device(device_id, device_info)
                return jsonify({'success': success})
                
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.route('/capture', methods=['POST'])
        def capture():
            try:
                data = request.get_json() or {}
                device_id = data.get('device_id')
                
                result = self.framework.capture_fingerprint(device_id)
                return jsonify(result)
                
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @self.app.route('/status', methods=['GET'])
        def status():
            return jsonify({
                'service': 'Universal Biometric Service',
                'version': '2.0.0',
                'devices_count': len(self.framework.devices),
                'registered_count': len(self.framework.registered_devices)
            })
    
    def run(self, host='localhost', port=SERVICE_PORT):
        """Inicia o serviço HTTP"""
        logger.info(f"Iniciando servico em {host}:{port}")
        
        # Descoberta inicial
        self.framework.discover_devices()
        
        self.app.run(host=host, port=port, debug=False, threaded=True)

def main():
    """Função principal"""
    print("=" * 60)
    print("RLPONTO-WEB - Servico Biometrico Universal v2.0")
    print("Sistema generico para qualquer leitor biometrico")
    print("Zero dependencia de hardware especifico")
    print("=" * 60)
    
    try:
        service = UniversalBiometricService()
        service.run()
    except KeyboardInterrupt:
        logger.info("Servico interrompido")
    except Exception as e:
        logger.error(f"Erro critico: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
