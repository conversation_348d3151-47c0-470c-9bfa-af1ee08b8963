* {
    box-sizing: border-box;
}
body {
    margin: 0;
    background: linear-gradient(180deg, #0b0c10, #1f2833);
    font-family: 'Segoe UI', Arial, sans-serif;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    padding: 0;
    color: #fff;
}
.container {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 20px auto;
    background-color: #1f2833;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,255,255,0.2);
}
.sidebar {
    width: 250px;
    background-color: #0b0c10;
    padding: 20px;
    border-right: 1px solid #66fcf1;
}
.tab-btn {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    background-color: #45a29e;
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
}
.tab-btn:hover {
    background-color: #3b8c88;
}
.tab-btn.active {
    background-color: #66fcf1;
    color: #0b0c10;
}
.tab-btn.error {
    background-color: #ffc107; /* Amar<PERSON> para campos obrigatórios não preenchidos */
    color: #0b0c10;
}
.main-content {
    flex: 1;
    padding: 30px;
}
h2 {
    text-align: center;
    color: #66fcf1;
    font-size: 24px;
    margin-bottom: 20px;
}
h3 {
    color: #66fcf1;
    font-size: 18px;
    margin-top: 20px;
}
label {
    display: block;
    margin-top: 15px;
    font-weight: bold;
}
input, select, textarea {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    border: none;
    border-radius: 5px;
    background-color: #c5c6c7;
    color: #0b0c10;
}
input.input-error {
    border: 2px solid #ff4d4d;
}
textarea {
    resize: vertical;
}
.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
}
.checkbox-group label {
    display: inline;
    margin: 0;
    font-weight: normal;
}
.checkbox-group input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin: 0;
}
button {
    margin-top: 20px;
    width: 100%;
    padding: 12px;
    background-color: #45a29e;
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    font-size: 16px;
}
button:hover {
    background-color: #3b8c88;
}
.voltar-btn {
    background-color: #ff4d4d; /* Vermelho */
}
.voltar-btn:hover {
    background-color: #e04343; /* Vermelho mais escuro */
}
.form-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}
.form-buttons button {
    width: 50%;
}
.tab-content {
    display: none;
}
.tab-content.active {
    display: block;
}
.errors {
    margin-top: 20px;
    color: #ff4d4d;
}
.errors ul {
    padding-left: 20px;
}
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        margin: 10px;
    }
    .sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #66fcf1;
    }
    .main-content {
        padding: 20px;
    }
    .form-buttons {
        flex-direction: column;
    }
    .form-buttons button {
        width: 100%;
    }
    .checkbox-group {
        flex-wrap: wrap;
    }
}
@media (max-width: 480px) {
    h2 {
        font-size: 20px;
    }
    h3 {
        font-size: 16px;
    }
    button {
        font-size: 14px;
    }
}