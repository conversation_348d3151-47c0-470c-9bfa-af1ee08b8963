# ========================================
# BLUEPRINT HORÁRIOS DE TRABALHO - RLPONTO-WEB
# Data: 08/01/2025
# Descrição: Sistema de gerenciamento de horários de trabalho
# ========================================

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash
from datetime import datetime, time
from utils.auth import require_login, require_admin
from utils.database import get_db_connection
import logging
import re

# Configurar logger
logger = logging.getLogger(__name__)

# Criar Blueprint
horarios_trabalho_bp = Blueprint('horarios_trabalho', __name__, url_prefix='/horarios-trabalho')

def validar_horario(horario_str):
    """Valida formato HH:MM"""
    if not horario_str:
        return True
    pattern = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
    return bool(re.match(pattern, horario_str))

@horarios_trabalho_bp.route('/')
@require_admin
def index():
    """Lista horários de trabalho"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT 
                h.id, h.nome_horario, h.entrada_manha, h.saida_almoco,
                h.entrada_tarde, h.saida, h.tolerancia_minutos, h.ativo,
                e.razao_social, COUNT(f.id) as funcionarios_vinculados
            FROM horarios_trabalho h
            INNER JOIN empresas e ON h.empresa_id = e.id
            LEFT JOIN funcionarios f ON h.id = f.horario_trabalho_id AND f.ativo = TRUE
            GROUP BY h.id, h.nome_horario, h.entrada_manha, h.saida_almoco,
                     h.entrada_tarde, h.saida, h.tolerancia_minutos, h.ativo, e.razao_social
            ORDER BY e.razao_social, h.nome_horario
        """)
        
        horarios = cursor.fetchall()
        conn.close()
        
        return render_template('horarios_trabalho/index.html', horarios=horarios)
        
    except Exception as e:
        logger.error(f"Erro ao listar horários: {str(e)}")
        flash('Erro ao carregar horários', 'error')
        return redirect(url_for('configuracoes.index')) 