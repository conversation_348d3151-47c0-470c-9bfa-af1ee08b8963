#!/usr/bin/env python3
import paramiko

def debug_login_issue():
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🔍 DEBUG - PROBLEMA DE LOGIN")
        print("=" * 50)
        
        # 1. Verificar se o hash da senha está correto
        print("1. Verificando hash da senha...")
        stdin, stdout, stderr = ssh.exec_command('''
        python3 -c "
import hashlib
senha = '@Ric6109'
hash_sha256 = hashlib.sha256(senha.encode()).hexdigest()
print(f'Senha: {senha}')
print(f'Hash SHA256: {hash_sha256}')
"
        ''')
        
        hash_result = stdout.read().decode()
        print(hash_result)
        
        # 2. Verificar o que está no banco
        print("2. Verificando dados no banco...")
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u root -p@Ric6109 controle_ponto -e "
        SELECT usuario, senha, nivel_acesso, ativo 
        FROM usuarios 
        WHERE usuario = 'admin';"
        ''')
        
        db_result = stdout.read().decode()
        print("Dados no banco:")
        print(db_result)
        
        # 3. Verificar função de verificação de senha
        print("3. Verificando função de verificação de senha...")
        stdin, stdout, stderr = ssh.exec_command('grep -A 30 "def is_password_hash" /var/www/controle-ponto/app.py')
        hash_function = stdout.read().decode()
        print("Função is_password_hash:")
        print(hash_function)
        
        # 4. Verificar como a senha está sendo verificada
        print("4. Verificando verificação de senha no login...")
        stdin, stdout, stderr = ssh.exec_command('grep -A 15 -B 5 "senha_valida.*=" /var/www/controle-ponto/app.py')
        password_check = stdout.read().decode()
        print("Verificação de senha:")
        print(password_check)
        
        # 5. Testar login diretamente no servidor
        print("5. Testando login diretamente no servidor...")
        test_login = '''
        cd /var/www/controle-ponto
        python3 -c "
import hashlib
import pymysql

# Conectar ao banco
conn = pymysql.connect(
    host='localhost',
    user='root',
    password='@Ric6109',
    database='controle_ponto',
    cursorclass=pymysql.cursors.DictCursor
)

try:
    with conn.cursor() as cursor:
        cursor.execute('SELECT * FROM usuarios WHERE usuario = %s', ('admin',))
        user = cursor.fetchone()
        
        if user:
            print(f'Usuário encontrado: {user[\"usuario\"]}')
            print(f'Senha no banco: {user[\"senha\"]}')
            print(f'Nível: {user[\"nivel_acesso\"]}')
            print(f'Ativo: {user[\"ativo\"]}')
            
            # Testar senha
            senha_digitada = '@Ric6109'
            senha_hash = hashlib.sha256(senha_digitada.encode()).hexdigest()
            
            print(f'Senha digitada: {senha_digitada}')
            print(f'Hash calculado: {senha_hash}')
            print(f'Senhas coincidem: {user[\"senha\"] == senha_hash}')
            
        else:
            print('Usuário não encontrado!')
            
finally:
    conn.close()
"
        '''
        
        stdin, stdout, stderr = ssh.exec_command(test_login)
        test_result = stdout.read().decode()
        test_error = stderr.read().decode()
        
        print("Resultado do teste:")
        print(test_result)
        if test_error:
            print("Erros:")
            print(test_error)
        
        # 6. Verificar logs de erro do Flask
        print("6. Verificando logs de erro do Flask...")
        stdin, stdout, stderr = ssh.exec_command('journalctl -u controle-ponto -n 10 --no-pager | grep -i error')
        error_logs = stdout.read().decode()
        print("Logs de erro:")
        print(error_logs if error_logs else "Nenhum erro encontrado")
        
        # 7. Atualizar senha para texto plano temporariamente
        print("7. Atualizando senha para texto plano (teste)...")
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u root -p@Ric6109 controle_ponto -e "
        UPDATE usuarios 
        SET senha = '@Ric6109' 
        WHERE usuario = 'admin';"
        ''')
        
        update_result = stdout.read().decode()
        update_error = stderr.read().decode()
        
        if "Warning" in update_error and "ERROR" not in update_error:
            print("✅ Senha atualizada para texto plano")
        else:
            print(f"Resultado: {update_result}")
            if update_error:
                print(f"Erro: {update_error}")
        
        # 8. Verificar novamente
        print("8. Verificando senha atualizada...")
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u root -p@Ric6109 controle_ponto -e "
        SELECT usuario, senha, nivel_acesso 
        FROM usuarios 
        WHERE usuario = 'admin';"
        ''')
        
        final_check = stdout.read().decode()
        print("Dados finais:")
        print(final_check)
        
        ssh.close()
        
        print("\n" + "=" * 50)
        print("🎯 RESUMO DO DEBUG")
        print("=" * 50)
        print("✅ Senha atualizada para texto plano")
        print("✅ Usuário admin existe no banco")
        print("🔄 Teste o login novamente com:")
        print("   Usuário: admin")
        print("   Senha: @Ric6109")
        
    except Exception as e:
        print(f"❌ Erro durante o debug: {e}")

if __name__ == "__main__":
    debug_login_issue()
