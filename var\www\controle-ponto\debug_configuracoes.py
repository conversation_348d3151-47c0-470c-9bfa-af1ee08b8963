#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DEBUG - Por que o conteúdo das abas não aparece?
===============================================
"""

import requests
import re

def debug_configuracoes():
    """Debug detalhado da página de configurações"""
    
    print("🔍 DEBUG: Por que o conteúdo das abas não aparece?")
    
    base_url = "http://10.19.208.31"
    session = requests.Session()
    
    # Login
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code in [200, 302]:
        print("✅ Login OK")
        
        # Obter página
        config_response = session.get(f"{base_url}/configuracoes/")
        
        if config_response.status_code == 200:
            content = config_response.text
            
            print(f"✅ Página carregada (Tamanho: {len(content)})")
            
            # Debug específico
            print("\n🔍 VERIFICAÇÕES DE DEBUG:")
            
            # 1. Verificar se há erros JavaScript
            js_errors = [
                "SyntaxError", "ReferenceError", "TypeError", "Error:", "Uncaught"
            ]
            
            for error in js_errors:
                if error in content:
                    print(f"⚠️ Possível erro JS: {error} encontrado")
            
            # 2. Verificar se Bootstrap está carregando
            bootstrap_checks = [
                ("Bootstrap CSS", "bootstrap" in content and "css" in content),
                ("Bootstrap JS", "bootstrap" in content and "js" in content),
                ("Font Awesome", "font-awesome" in content or "fontawesome" in content),
                ("Tab Content", "tab-content" in content),
                ("Tab Pane", "tab-pane" in content)
            ]
            
            print("\n📦 VERIFICAÇÕES DE RECURSOS:")
            for check_name, result in bootstrap_checks:
                status = "✅" if result else "❌"
                print(f"{status} {check_name}")
            
            # 3. Verificar estrutura das abas
            tab_structure = [
                ("Div tab-content", '<div class="tab-content"' in content),
                ("Tab pane geral", 'id="geral"' in content),
                ("Action grid", 'action-grid' in content),
                ("Action card", 'action-card' in content),
                ("Biometric highlight", 'biometric-highlight' in content)
            ]
            
            print("\n🏗️ VERIFICAÇÕES DE ESTRUTURA:")
            for check_name, result in tab_structure:
                status = "✅" if result else "❌"
                print(f"{status} {check_name}")
            
            # 4. Procurar por CSS que pode estar ocultando conteúdo
            hidden_css = [
                "display: none", "display:none", "visibility: hidden", 
                "opacity: 0", "height: 0", "overflow: hidden"
            ]
            
            print("\n🎨 VERIFICAÇÕES DE CSS:")
            for css in hidden_css:
                if css in content:
                    print(f"⚠️ CSS suspeito encontrado: {css}")
            
            # 5. Extrair conteúdo da aba geral
            print("\n📄 PROCURANDO CONTEÚDO DA ABA GERAL:")
            
            # Regex para encontrar o conteúdo da aba geral
            geral_pattern = r'<div[^>]*id="geral"[^>]*>(.*?)</div>(?=\s*<div[^>]*class="tab-pane")'
            geral_match = re.search(geral_pattern, content, re.DOTALL)
            
            if geral_match:
                geral_content = geral_match.group(1)
                print(f"✅ Conteúdo da aba geral encontrado ({len(geral_content)} chars)")
                
                # Verificar se tem action-cards
                if "action-card" in geral_content:
                    print("✅ Action cards encontrados na aba geral")
                    
                    # Contar cards
                    card_count = geral_content.count("action-card")
                    print(f"📊 Número de cards: {card_count}")
                    
                    # Verificar biometria
                    if "BIOMETRIA ATIVA" in geral_content:
                        print("✅ Card de biometria encontrado!")
                    else:
                        print("❌ Card de biometria NÃO encontrado")
                else:
                    print("❌ Action cards NÃO encontrados na aba geral")
                    print(f"📄 Conteúdo atual: {geral_content[:200]}...")
            else:
                print("❌ Conteúdo da aba geral NÃO encontrado")
            
            # 6. Verificar se há JavaScript de inicialização
            print("\n⚡ VERIFICAÇÕES DE JAVASCRIPT:")
            js_checks = [
                ("jQuery", "$" in content or "jquery" in content.lower()),
                ("Bootstrap JS", "bootstrap.bundle" in content),
                ("Tab initialization", "tab" in content and "show" in content),
                ("Console logs", "console.log" in content)
            ]
            
            for check_name, result in js_checks:
                status = "✅" if result else "❌"
                print(f"{status} {check_name}")
                
        else:
            print(f"❌ Erro ao carregar página: {config_response.status_code}")
    else:
        print(f"❌ Erro no login: {login_response.status_code}")

if __name__ == "__main__":
    debug_configuracoes()