# Script de deploy para o servidor
$server = "************"
$user = "root"
$password = "@Ric6109"

# Criar arquivo empresa.py no servidor
$empresaContent = @"
from database import get_db_connection
import logging

logger = logging.getLogger(__name__)

class Empresa:
    def __init__(self, id=None, nome_empresa=None, logotipo=None, logotipo_mime_type=None, 
                 regras_especificas=None, tolerancia_atraso=10, tolerancia_saida_antecipada=10,
                 jornada_trabalho_padrao='08:00:00', intervalo_almoco_inicio='12:00:00',
                 intervalo_almoco_fim='13:00:00', data_criacao=None, data_atualizacao=None, ativa=True):
        self.id = id
        self.nome_empresa = nome_empresa
        self.logotipo = logotipo
        self.logotipo_mime_type = logotipo_mime_type
        self.regras_especificas = regras_especificas
        self.tolerancia_atraso = tolerancia_atraso
        self.tolerancia_saida_antecipada = tolerancia_saida_antecipada
        self.jornada_trabalho_padrao = jornada_trabalho_padrao
        self.intervalo_almoco_inicio = intervalo_almoco_inicio
        self.intervalo_almoco_fim = intervalo_almoco_fim
        self.data_criacao = data_criacao
        self.data_atualizacao = data_atualizacao
        self.ativa = ativa

    @classmethod
    def criar(cls, nome_empresa, logotipo=None, logotipo_mime_type=None, regras_especificas=None,
              tolerancia_atraso=10, tolerancia_saida_antecipada=10, jornada_trabalho_padrao='08:00:00',
              intervalo_almoco_inicio='12:00:00', intervalo_almoco_fim='13:00:00'):
        """Cria uma nova empresa no banco de dados"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = '''
                INSERT INTO cad_empresas 
                (nome_empresa, logotipo, logotipo_mime_type, regras_especificas, 
                 tolerancia_atraso, tolerancia_saida_antecipada, jornada_trabalho_padrao,
                 intervalo_almoco_inicio, intervalo_almoco_fim)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            '''
            
            cursor.execute(query, (
                nome_empresa, logotipo, logotipo_mime_type, regras_especificas,
                tolerancia_atraso, tolerancia_saida_antecipada, jornada_trabalho_padrao,
                intervalo_almoco_inicio, intervalo_almoco_fim
            ))
            
            empresa_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"Empresa '{nome_empresa}' criada com ID {empresa_id}")
            return cls.buscar_por_id(empresa_id)
            
        except Exception as e:
            logger.error(f"Erro ao criar empresa: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()

    @classmethod
    def buscar_por_id(cls, empresa_id):
        """Busca uma empresa pelo ID"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            cursor.execute("SELECT * FROM cad_empresas WHERE id = %s", (empresa_id,))
            resultado = cursor.fetchone()
            
            if resultado:
                return cls(**resultado)
            return None
            
        except Exception as e:
            logger.error(f"Erro ao buscar empresa por ID {empresa_id}: {e}")
            raise
        finally:
            if conn:
                conn.close()

    @classmethod
    def listar_todas(cls, ativas_apenas=True):
        """Lista todas as empresas"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            if ativas_apenas:
                cursor.execute("SELECT * FROM cad_empresas WHERE ativa = 1 ORDER BY nome_empresa")
            else:
                cursor.execute("SELECT * FROM cad_empresas ORDER BY nome_empresa")
            
            resultados = cursor.fetchall()
            return [cls(**resultado) for resultado in resultados]
            
        except Exception as e:
            logger.error(f"Erro ao listar empresas: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def atualizar(self):
        """Atualiza os dados da empresa no banco"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = '''
                UPDATE cad_empresas SET
                nome_empresa = %s, logotipo = %s, logotipo_mime_type = %s,
                regras_especificas = %s, tolerancia_atraso = %s, tolerancia_saida_antecipada = %s,
                jornada_trabalho_padrao = %s, intervalo_almoco_inicio = %s, intervalo_almoco_fim = %s,
                ativa = %s
                WHERE id = %s
            '''
            
            cursor.execute(query, (
                self.nome_empresa, self.logotipo, self.logotipo_mime_type,
                self.regras_especificas, self.tolerancia_atraso, self.tolerancia_saida_antecipada,
                self.jornada_trabalho_padrao, self.intervalo_almoco_inicio, self.intervalo_almoco_fim,
                self.ativa, self.id
            ))
            
            conn.commit()
            logger.info(f"Empresa ID {self.id} atualizada com sucesso")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar empresa ID {self.id}: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()

    def excluir(self):
        """Exclui logicamente a empresa (marca como inativa)"""
        self.ativa = False
        self.atualizar()
        logger.info(f"Empresa ID {self.id} marcada como inativa")

    def to_dict(self):
        """Converte a empresa para dicionário"""
        return {
            'id': self.id,
            'nome_empresa': self.nome_empresa,
            'logotipo': self.logotipo,
            'logotipo_mime_type': self.logotipo_mime_type,
            'regras_especificas': self.regras_especificas,
            'tolerancia_atraso': self.tolerancia_atraso,
            'tolerancia_saida_antecipada': self.tolerancia_saida_antecipada,
            'jornada_trabalho_padrao': str(self.jornada_trabalho_padrao) if self.jornada_trabalho_padrao else None,
            'intervalo_almoco_inicio': str(self.intervalo_almoco_inicio) if self.intervalo_almoco_inicio else None,
            'intervalo_almoco_fim': str(self.intervalo_almoco_fim) if self.intervalo_almoco_fim else None,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'data_atualizacao': self.data_atualizacao.isoformat() if self.data_atualizacao else None,
            'ativa': self.ativa
        }
"@

# Criar arquivo routes_empresa.py no servidor
$routesContent = @"
from flask import Blueprint, request, jsonify, render_template, redirect, url_for, flash
from models.empresa import Empresa
import logging
import base64

logger = logging.getLogger(__name__)
empresa_bp = Blueprint('empresa', __name__)

@empresa_bp.route('/empresas')
def listar_empresas():
    """Lista todas as empresas"""
    try:
        empresas = Empresa.listar_todas()
        return render_template('empresas/listar.html', empresas=empresas)
    except Exception as e:
        logger.error(f"Erro ao listar empresas: {e}")
        flash('Erro ao carregar lista de empresas', 'error')
        return redirect(url_for('admin.dashboard'))

@empresa_bp.route('/empresas/nova')
def nova_empresa():
    """Formulário para criar nova empresa"""
    return render_template('empresas/form.html', empresa=None)

@empresa_bp.route('/empresas/criar', methods=['POST'])
def criar_empresa():
    """Cria uma nova empresa"""
    try:
        nome_empresa = request.form.get('nome_empresa')
        regras_especificas = request.form.get('regras_especificas')
        tolerancia_atraso = int(request.form.get('tolerancia_atraso', 10))
        tolerancia_saida_antecipada = int(request.form.get('tolerancia_saida_antecipada', 10))
        jornada_trabalho_padrao = request.form.get('jornada_trabalho_padrao', '08:00:00')
        intervalo_almoco_inicio = request.form.get('intervalo_almoco_inicio', '12:00:00')
        intervalo_almoco_fim = request.form.get('intervalo_almoco_fim', '13:00:00')
        
        # Processar logotipo se enviado
        logotipo = None
        logotipo_mime_type = None
        if 'logotipo' in request.files:
            arquivo = request.files['logotipo']
            if arquivo and arquivo.filename:
                logotipo = arquivo.read()
                logotipo_mime_type = arquivo.content_type
        
        empresa = Empresa.criar(
            nome_empresa=nome_empresa,
            logotipo=logotipo,
            logotipo_mime_type=logotipo_mime_type,
            regras_especificas=regras_especificas,
            tolerancia_atraso=tolerancia_atraso,
            tolerancia_saida_antecipada=tolerancia_saida_antecipada,
            jornada_trabalho_padrao=jornada_trabalho_padrao,
            intervalo_almoco_inicio=intervalo_almoco_inicio,
            intervalo_almoco_fim=intervalo_almoco_fim
        )
        
        flash(f'Empresa "{nome_empresa}" criada com sucesso!', 'success')
        return redirect(url_for('empresa.listar_empresas'))
        
    except Exception as e:
        logger.error(f"Erro ao criar empresa: {e}")
        flash('Erro ao criar empresa', 'error')
        return redirect(url_for('empresa.nova_empresa'))

@empresa_bp.route('/empresas/<int:empresa_id>')
def ver_empresa(empresa_id):
    """Visualiza uma empresa específica"""
    try:
        empresa = Empresa.buscar_por_id(empresa_id)
        if not empresa:
            flash('Empresa não encontrada', 'error')
            return redirect(url_for('empresa.listar_empresas'))
        
        return render_template('empresas/detalhes.html', empresa=empresa)
        
    except Exception as e:
        logger.error(f"Erro ao buscar empresa {empresa_id}: {e}")
        flash('Erro ao carregar empresa', 'error')
        return redirect(url_for('empresa.listar_empresas'))

@empresa_bp.route('/empresas/<int:empresa_id>/editar')
def editar_empresa(empresa_id):
    """Formulário para editar empresa"""
    try:
        empresa = Empresa.buscar_por_id(empresa_id)
        if not empresa:
            flash('Empresa não encontrada', 'error')
            return redirect(url_for('empresa.listar_empresas'))
        
        return render_template('empresas/form.html', empresa=empresa)
        
    except Exception as e:
        logger.error(f"Erro ao buscar empresa {empresa_id}: {e}")
        flash('Erro ao carregar empresa', 'error')
        return redirect(url_for('empresa.listar_empresas'))

@empresa_bp.route('/empresas/<int:empresa_id>/atualizar', methods=['POST'])
def atualizar_empresa(empresa_id):
    """Atualiza uma empresa"""
    try:
        empresa = Empresa.buscar_por_id(empresa_id)
        if not empresa:
            flash('Empresa não encontrada', 'error')
            return redirect(url_for('empresa.listar_empresas'))
        
        # Atualizar dados
        empresa.nome_empresa = request.form.get('nome_empresa')
        empresa.regras_especificas = request.form.get('regras_especificas')
        empresa.tolerancia_atraso = int(request.form.get('tolerancia_atraso', 10))
        empresa.tolerancia_saida_antecipada = int(request.form.get('tolerancia_saida_antecipada', 10))
        empresa.jornada_trabalho_padrao = request.form.get('jornada_trabalho_padrao', '08:00:00')
        empresa.intervalo_almoco_inicio = request.form.get('intervalo_almoco_inicio', '12:00:00')
        empresa.intervalo_almoco_fim = request.form.get('intervalo_almoco_fim', '13:00:00')
        
        # Processar novo logotipo se enviado
        if 'logotipo' in request.files:
            arquivo = request.files['logotipo']
            if arquivo and arquivo.filename:
                empresa.logotipo = arquivo.read()
                empresa.logotipo_mime_type = arquivo.content_type
        
        empresa.atualizar()
        
        flash(f'Empresa "{empresa.nome_empresa}" atualizada com sucesso!', 'success')
        return redirect(url_for('empresa.ver_empresa', empresa_id=empresa_id))
        
    except Exception as e:
        logger.error(f"Erro ao atualizar empresa {empresa_id}: {e}")
        flash('Erro ao atualizar empresa', 'error')
        return redirect(url_for('empresa.editar_empresa', empresa_id=empresa_id))

@empresa_bp.route('/empresas/<int:empresa_id>/excluir', methods=['POST'])
def excluir_empresa(empresa_id):
    """Exclui (desativa) uma empresa"""
    try:
        empresa = Empresa.buscar_por_id(empresa_id)
        if not empresa:
            flash('Empresa não encontrada', 'error')
            return redirect(url_for('empresa.listar_empresas'))
        
        empresa.excluir()
        flash(f'Empresa "{empresa.nome_empresa}" desativada com sucesso!', 'success')
        return redirect(url_for('empresa.listar_empresas'))
        
    except Exception as e:
        logger.error(f"Erro ao excluir empresa {empresa_id}: {e}")
        flash('Erro ao excluir empresa', 'error')
        return redirect(url_for('empresa.listar_empresas'))

@empresa_bp.route('/api/empresas')
def api_listar_empresas():
    """API para listar empresas"""
    try:
        empresas = Empresa.listar_todas()
        return jsonify([empresa.to_dict() for empresa in empresas])
    except Exception as e:
        logger.error(f"Erro na API de empresas: {e}")
        return jsonify({'error': 'Erro interno do servidor'}), 500

@empresa_bp.route('/empresas/<int:empresa_id>/logotipo')
def logotipo_empresa(empresa_id):
    """Serve o logotipo da empresa"""
    try:
        empresa = Empresa.buscar_por_id(empresa_id)
        if not empresa or not empresa.logotipo:
            return '', 404
        
        from flask import Response
        return Response(
            empresa.logotipo,
            mimetype=empresa.logotipo_mime_type or 'image/jpeg'
        )
        
    except Exception as e:
        logger.error(f"Erro ao servir logotipo da empresa {empresa_id}: {e}")
        return '', 500
"@

Write-Host "Conectando ao servidor para criar os arquivos..."

# Usar plink para executar comandos no servidor
$commands = @(
    "cd /opt/controle-ponto",
    "cat > models/empresa.py << 'EOF'`n$empresaContent`nEOF",
    "cat > routes/empresa.py << 'EOF'`n$routesContent`nEOF",
    "systemctl restart controle-ponto",
    "systemctl status controle-ponto"
)

foreach ($cmd in $commands) {
    Write-Host "Executando: $cmd"
    $result = plink -ssh $user@$server -pw $password $cmd
    Write-Host $result
}

Write-Host "Deploy concluído!"
