# 🚀 GUIA DE INICIALIZAÇÃO - RLPONTO-WEB

Este guia descreve como inicializar o sistema RLPONTO-WEB corretamente.

---

## ✅ **PRÉ-REQUISITOS VERIFICADOS**

### **1. Dependências Python**
- ✅ Flask==2.3.3
- ✅ pymysql==1.1.0
- ✅ werkzeug==2.3.7
- ✅ requests==2.31.0
- ✅ websockets==11.0.3

### **2. ZKAgent Funcionando**
- ✅ Serviço rodando na porta 5001
- ✅ API respondendo: `{"status":"ok"}`
- ✅ Dispositivos detectados: `{"devices":0}` (sem hardware físico)

---

## 🔧 **ETAPAS DE INICIALIZAÇÃO**

### **ETAPA 1: Verificar ZKAgent**
```bash
# Teste rápido
curl http://localhost:5001/test
# Resposta esperada: {"status":"ok"}

# Verificar dispositivos
curl http://localhost:5001/list-devices
# Resposta esperada: {"devices":X} onde X é o número de leitores
```

### **ETAPA 2: Inici<PERSON><PERSON>**
```bash
cd /var/www/controle-ponto
python3 app.py
```

**Saída esperada:**
```
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://[IP_DA_MÁQUINA]:5000
```

### **ETAPA 3: Testar Comunicação**
```bash
# Em outro terminal
python3 teste_zkagent.py
```

**Resultado esperado:**
```
🔐 TESTE DE COMUNICAÇÃO ZKAGENT
🔄 Testando conexão direta com ZKAgent...
✅ Teste básico: 200 - {"status":"ok"}
✅ Dispositivos: 200 - {"devices":0}

🔄 Testando proxy Flask...
✅ Proxy teste: 200 - {"status":"ok"}
✅ Proxy dispositivos: 200 - {"devices":0}

🎉 TODOS OS TESTES PASSARAM!
```

---

## 🌐 **ACESSOS DO SISTEMA**

### **URLs Principais**
- **Portal Principal**: `http://localhost:5000`
- **Login**: `http://localhost:5000/login`
- **Cadastro Funcionários**: `http://localhost:5000/funcionarios/cadastrar`

### **APIs Biométricas**
- **ZKAgent Direto**: `http://localhost:5001/`
- **Proxy Flask**: `http://localhost:5000/api/zkagent/`
- **Status ZKAgent**: `http://localhost:5000/api/zkagent-status`

### **Credenciais Padrão**
- **Usuário**: `admin`
- **Senha**: `admin` (troca obrigatória no primeiro login)

---

## 🔐 **TESTE COMPLETO DO MODAL BIOMÉTRICO**

### **1. Acesso ao Sistema**
1. Navegue para: `http://localhost:5000`
2. Faça login com credenciais admin
3. Acesse: **Funcionários** → **Cadastrar Novo**

### **2. Teste da Biometria**
1. Preencha dados básicos do funcionário
2. Clique em **"Capturar Biometria"**
3. No modal, clique em **"Iniciar Captura"**

### **3. Monitoramento de Logs**
**Console do Navegador (F12):**
```
🔐 ZKAgent URL detectada: http://localhost:5001
🔄 Tentando conexão direta ZKAgent: http://localhost:5001/test
✅ Conexão direta ZKAgent funcionando
🔄 Testando conexão direta com ZKAgent...
✅ Teste básico: 200 - {"status":"ok"}
```

**Terminal Flask:**
```
INFO - Proxy ZKAgent: GET http://localhost:5001/test
INFO - Proxy ZKAgent: GET http://localhost:5001/list-devices
```

---

## ⚠️ **TROUBLESHOOTING**

### **Problema: ZKAgent não responde**
```bash
# Verificar se está rodando
netstat -an | findstr 5001

# Se não estiver rodando, inicializar
cd zkagent/ZKAgent-Distribuicao
install-simples.bat
```

### **Problema: CORS no navegador**
```
❌ Conexão direta falhou, tentando proxy Flask
✅ Proxy Flask funcionando
```
**Solução**: Normal! O sistema usa fallback automático.

### **Problema: Dependências faltando**
```bash
pip3 install -r requirements.txt
```

### **Problema: Banco de dados**
```bash
# Verificar MySQL
systemctl status mysql

# Importar estrutura
mysql -u root -p < controle_ponto.sql
```

---

## 📊 **STATUS ATUAL DO SISTEMA**

### **✅ COMPONENTES FUNCIONAIS**
- [x] **ZKAgent**: ✅ Rodando na porta 5001
- [x] **Flask**: ✅ Dependências instaladas
- [x] **Proxy CORS**: ✅ Implementado
- [x] **Modal Biométrico**: ✅ Interface profissional
- [x] **Fallback Automático**: ✅ Direto → Proxy
- [x] **Error Handling**: ✅ Logs detalhados

### **🔄 PRÓXIMA ETAPA**
1. **Inicializar Flask**: `python3 app.py`
2. **Testar modal biométrico** no navegador
3. **Verificar logs** de comunicação
4. **Confirmar funcionamento** completo

---

## 🎯 **COMANDOS ESSENCIAIS**

```bash
# Navegar para o diretório
cd /var/www/controle-ponto

# Testar ZKAgent
python3 teste_zkagent.py

# Inicializar Flask
python3 app.py

# Em caso de problemas de dependência
pip3 install -r requirements.txt

# Verificar logs
tail -f /var/log/controle-ponto/app.log
```

---

**🎉 SISTEMA PRONTO PARA USO!**

O RLPONTO-WEB está configurado e pronto para funcionar. Execute `python3 app.py` e teste o modal biométrico! 