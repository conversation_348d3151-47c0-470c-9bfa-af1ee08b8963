#!/usr/bin/env python3
import paramiko
import json

def diagnostico_completo_empresas():
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🔍 DIAGNÓSTICO COMPLETO - ABA EMPRESAS")
        print("=" * 60)
        
        # 1. Verificar se a tabela empresas existe
        print("\n1. VERIFICANDO ESTRUTURA DO BANCO DE DADOS")
        print("-" * 40)
        
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u cavalcrod -p200381 controle_ponto -e "
        SHOW TABLES LIKE 'empresas';
        DESCRIBE empresas;
        SELECT COUNT(*) as total_empresas FROM empresas;
        SELECT * FROM empresas LIMIT 3;
        "
        ''')
        
        db_result = stdout.read().decode('utf-8', errors='ignore')
        db_error = stderr.read().decode('utf-8', errors='ignore')
        
        print("Resultado do banco:")
        print(db_result)
        if db_error:
            print("Erros do banco:")
            print(db_error)
        
        # 2. Verificar rotas do Flask
        print("\n2. VERIFICANDO ROTAS DO FLASK")
        print("-" * 40)
        
        stdin, stdout, stderr = ssh.exec_command('''
        cd /var/www/controle-ponto
        python3 -c "
from app import app
from flask import url_for
with app.app_context():
    try:
        print('Rota configuracoes.listar_empresas:', url_for('configuracoes.listar_empresas'))
        print('Rota configuracoes.nova_empresa:', url_for('configuracoes.nova_empresa'))
        print('Rota empresas.index:', url_for('empresas.index'))
    except Exception as e:
        print('Erro nas rotas:', e)
"
        ''')
        
        routes_result = stdout.read().decode('utf-8', errors='ignore')
        routes_error = stderr.read().decode('utf-8', errors='ignore')
        
        print("Rotas disponíveis:")
        print(routes_result)
        if routes_error:
            print("Erros nas rotas:")
            print(routes_error)
        
        # 3. Verificar se os blueprints estão registrados
        print("\n3. VERIFICANDO BLUEPRINTS REGISTRADOS")
        print("-" * 40)
        
        stdin, stdout, stderr = ssh.exec_command('''
        cd /var/www/controle-ponto
        grep -n "register_blueprint" app.py
        grep -n "configuracoes_bp" app.py
        grep -n "empresas_bp" app.py
        ''')
        
        blueprints_result = stdout.read().decode('utf-8', errors='ignore')
        print("Blueprints registrados:")
        print(blueprints_result)
        
        # 4. Testar acesso direto às rotas
        print("\n4. TESTANDO ACESSO ÀS ROTAS")
        print("-" * 40)
        
        # Testar rota de listar empresas
        stdin, stdout, stderr = ssh.exec_command('''
        curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/configuracoes/empresas
        ''')
        
        status_empresas = stdout.read().decode('utf-8', errors='ignore').strip()
        print(f"Status /configuracoes/empresas: {status_empresas}")

        # Testar rota de configurações
        stdin, stdout, stderr = ssh.exec_command('''
        curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/configuracoes/
        ''')

        status_config = stdout.read().decode('utf-8', errors='ignore').strip()
        print(f"Status /configuracoes/: {status_config}")
        
        # 5. Verificar logs do Flask
        print("\n5. VERIFICANDO LOGS DO FLASK")
        print("-" * 40)
        
        stdin, stdout, stderr = ssh.exec_command('''
        tail -20 /var/log/controle-ponto/app.log | grep -i "empresa\|erro\|exception"
        ''')
        
        logs_result = stdout.read().decode('utf-8', errors='ignore')
        print("Logs recentes relacionados a empresas:")
        print(logs_result if logs_result else "Nenhum log encontrado")

        # 6. Verificar se o template existe
        print("\n6. VERIFICANDO TEMPLATES")
        print("-" * 40)

        stdin, stdout, stderr = ssh.exec_command('''
        ls -la /var/www/controle-ponto/templates/configuracoes/ | grep empresa
        ls -la /var/www/controle-ponto/templates/empresas/ 2>/dev/null || echo "Pasta empresas não existe"
        ''')

        templates_result = stdout.read().decode('utf-8', errors='ignore')
        print("Templates relacionados a empresas:")
        print(templates_result)
        
        # 7. Verificar JavaScript da aba
        print("\n7. VERIFICANDO JAVASCRIPT DAS ABAS")
        print("-" * 40)
        
        stdin, stdout, stderr = ssh.exec_command('''
        grep -n -A 5 -B 5 "empresas" /var/www/controle-ponto/templates/configuracoes/index.html | grep -E "tab-pane|empresas|display|visibility"
        ''')
        
        js_result = stdout.read().decode('utf-8', errors='ignore')
        print("JavaScript relacionado à aba empresas:")
        print(js_result)

        # 8. Verificar se há CSS que pode estar escondendo a aba
        print("\n8. VERIFICANDO CSS QUE PODE ESCONDER A ABA")
        print("-" * 40)

        stdin, stdout, stderr = ssh.exec_command('''
        grep -n -E "display.*none|visibility.*hidden|opacity.*0" /var/www/controle-ponto/templates/configuracoes/index.html | head -10
        ''')

        css_result = stdout.read().decode('utf-8', errors='ignore')
        print("CSS que pode estar escondendo elementos:")
        print(css_result)
        
        ssh.close()
        
        print("\n" + "=" * 60)
        print("🎯 DIAGNÓSTICO CONCLUÍDO!")
        print("=" * 60)
        
        # Análise dos resultados
        print("\n📋 ANÁLISE DOS RESULTADOS:")
        
        if "empresas" in db_result:
            print("✅ Tabela empresas existe no banco")
        else:
            print("❌ Tabela empresas NÃO existe no banco")
        
        if status_empresas == "200":
            print("✅ Rota /configuracoes/empresas responde corretamente")
        else:
            print(f"❌ Rota /configuracoes/empresas retorna status: {status_empresas}")
        
        if status_config == "200":
            print("✅ Rota /configuracoes/ responde corretamente")
        else:
            print(f"❌ Rota /configuracoes/ retorna status: {status_config}")
        
        if "empresas.html" in templates_result:
            print("✅ Template empresas.html existe")
        else:
            print("❌ Template empresas.html NÃO existe")
        
    except Exception as e:
        print(f"❌ Erro durante diagnóstico: {e}")

if __name__ == "__main__":
    diagnostico_completo_empresas()
