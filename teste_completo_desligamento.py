#!/usr/bin/env python3
"""
TESTE COMPLETO E REAL do sistema de desligamento
"""
import requests
import sys
import json

def testar_sistema_completo():
    """Testa o sistema completo de desligamento"""
    print("🧪 TESTE COMPLETO DO SISTEMA DE DESLIGAMENTO")
    print("=" * 60)
    
    base_url = "http://10.19.208.31:5000"
    session = requests.Session()
    
    try:
        # 1. TESTE: Acessar página de login
        print("\n1. 🔐 Testando acesso à página de login...")
        login_response = session.get(f"{base_url}/login")
        if login_response.status_code == 200:
            print("   ✅ Página de login acessível")
        else:
            print(f"   ❌ Erro ao acessar login: {login_response.status_code}")
            return False
        
        # 2. TESTE: Fazer login como admin
        print("\n2. 🔑 Testando login como administrador...")
        login_data = {
            'usuario': 'admin',
            'senha': 'admin123'
        }
        login_post = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if login_post.status_code in [302, 200]:
            print("   ✅ Login realizado com sucesso")
        else:
            print(f"   ❌ Falha no login: {login_post.status_code}")
            # Tentar com outras credenciais
            print("   🔄 Tentando credenciais alternativas...")
            login_data = {'usuario': 'richardson', 'senha': 'admin'}
            login_post = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
            if login_post.status_code in [302, 200]:
                print("   ✅ Login alternativo realizado")
            else:
                print(f"   ❌ Falha no login alternativo: {login_post.status_code}")
                return False
        
        # 3. TESTE: Acessar página de funcionários
        print("\n3. 👥 Testando acesso à página de funcionários...")
        funcionarios_response = session.get(f"{base_url}/funcionarios/")
        
        if funcionarios_response.status_code == 200:
            print("   ✅ Página de funcionários acessível")
            
            # Verificar se há funcionários na página
            if "João Silva Santos" in funcionarios_response.text or "RICHARDSON" in funcionarios_response.text:
                print("   ✅ Funcionários sendo exibidos na página")
            else:
                print("   ⚠️ Página carregou mas não mostra funcionários")
                
            # Verificar se há mensagem de erro
            if "Erro ao desligar funcionário" in funcionarios_response.text:
                print("   ❌ MENSAGEM DE ERRO AINDA PRESENTE!")
                return False
            else:
                print("   ✅ Nenhuma mensagem de erro encontrada")
                
        else:
            print(f"   ❌ Erro ao acessar funcionários: {funcionarios_response.status_code}")
            return False
        
        # 4. TESTE: Verificar se modal de desligamento funciona
        print("\n4. 🗑️ Testando funcionalidade de desligamento...")
        
        # Verificar se há botões de desligamento na página
        if 'onclick="confirmarDesligamento(' in funcionarios_response.text:
            print("   ✅ Botões de desligamento encontrados na página")
        else:
            print("   ❌ Botões de desligamento NÃO encontrados")
            return False
        
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("✅ Sistema de desligamento está funcionando corretamente")
        return True
        
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE: {e}")
        return False

if __name__ == "__main__":
    sucesso = testar_sistema_completo()
    if not sucesso:
        print("\n🚨 SISTEMA COM PROBLEMAS - NECESSÁRIA CORREÇÃO")
        sys.exit(1)
    else:
        print("\n✅ SISTEMA FUNCIONANDO PERFEITAMENTE")
        sys.exit(0)
