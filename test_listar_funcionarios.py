#!/usr/bin/env python3
import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import FuncionarioQueries

# Testar listagem de funcionários
print("🧪 TESTE DE LISTAGEM DE FUNCIONÁRIOS")
print("=" * 50)

try:
    result = FuncionarioQueries.get_all(page=1, per_page=10)
    
    print(f"✅ Resultado obtido:")
    print(f"   - Total de funcionários: {len(result.get('data', []))}")
    print(f"   - Paginação: {result.get('pagination', {})}")
    
    if result.get('data'):
        print(f"\n📋 Funcionários encontrados:")
        for i, func in enumerate(result['data'], 1):
            print(f"   {i}. {func.get('nome_completo')} (ID: {func.get('id')}, Matrícula: {func.get('matricula_empresa')})")
    else:
        print("❌ Nenhum funcionário encontrado")
        
except Exception as e:
    print(f"❌ Erro no teste: {e}")
    import traceback
    traceback.print_exc()
