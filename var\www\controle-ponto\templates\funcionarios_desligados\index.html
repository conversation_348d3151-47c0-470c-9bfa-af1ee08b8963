{% extends "base.html" %}

{% block title %}Funcionários Desligados - Controle de Ponto{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/relatorios.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='style-cadastrar.css') }}">
<style>
    .desligados-header {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 2rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
    }

    .desligados-header h1 {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
    }

    .desligados-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #dc3545;
    }

    .stat-card h3 {
        margin: 0 0 0.5rem 0;
        color: #495057;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stat-card .number {
        font-size: 2rem;
        font-weight: 700;
        color: #dc3545;
        margin: 0;
    }

    .filters-section {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        border: 1px solid #e9ecef;
    }

    .filters-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
        cursor: pointer;
        user-select: none;
    }

    .filters-header h3 {
        margin: 0;
        color: #495057;
        font-size: 1.1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: color 0.3s ease;
    }

    .filters-header:hover h3 {
        color: #dc3545;
    }

    .filters-toggle {
        color: #6c757d;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .filters-content {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .filters-content.collapsed {
        max-height: 0;
        opacity: 0;
        margin-bottom: 0;
    }

    .filters-content.expanded {
        max-height: 500px;
        opacity: 1;
    }

    .filters-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        align-items: flex-end;
    }

    .filter-group {
        flex: 1;
        min-width: 200px;
        position: relative;
    }

    .filter-group.search-group {
        flex: 2;
        min-width: 300px;
    }

    .filter-group.actions-group {
        flex: 0 0 auto;
        display: flex;
        gap: 0.75rem;
        align-items: flex-end;
        margin-left: auto;
    }

    .filter-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: #495057;
        font-weight: 500;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-group .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: #fafbfc;
        width: 100%;
    }

    .filter-group .form-control:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.1);
        background: white;
        outline: none;
    }

    .search-input-wrapper {
        position: relative;
    }

    .search-input-wrapper .form-control {
        padding-left: 2.5rem;
    }

    .search-input-wrapper .search-icon {
        position: absolute;
        left: 0.875rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        pointer-events: none;
    }

    .btn-filter {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border: none;
        padding: 0.875rem 1.75rem;
        border-radius: 10px;
        font-weight: 600;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        white-space: nowrap;
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
    }

    .btn-filter:hover {
        background: linear-gradient(135deg, #c82333, #a71e2a);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(220, 53, 69, 0.3);
    }

    .btn-clear {
        background: transparent;
        color: #6c757d;
        border: 2px solid #e9ecef;
        padding: 0.875rem 1.5rem;
        border-radius: 10px;
        font-weight: 500;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        white-space: nowrap;
    }

    .btn-clear:hover {
        background: #f8f9fa;
        border-color: #dc3545;
        color: #dc3545;
        transform: translateY(-1px);
    }

    @media (max-width: 992px) {
        .filters-grid {
            flex-direction: column;
            align-items: stretch;
        }

        .filter-group.actions-group {
            flex-direction: row;
            justify-content: center;
            margin-left: 0;
            margin-top: 0.5rem;
        }

        .filter-group.search-group {
            min-width: auto;
        }
    }

    @media (max-width: 576px) {
        .filter-group.actions-group {
            flex-direction: column;
        }

        .btn-filter, .btn-clear {
            width: 100%;
            justify-content: center;
        }
    }

    .table-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        border: 1px solid #e9ecef;
    }

    .table-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        padding: 1.5rem;
        border-bottom: 2px solid #dee2e6;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .table-header h2 {
        margin: 0;
        color: #495057;
        font-size: 1.3rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .table-info {
        color: #6c757d;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .desligados-table {
        width: 100%;
        border-collapse: collapse;
    }

    .desligados-table th {
        background: #f8f9fa;
        padding: 1rem;
        text-align: left;
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .desligados-table td {
        padding: 1rem;
        border-bottom: 1px solid #f1f3f4;
        vertical-align: middle;
    }

    .desligados-table tr:hover {
        background-color: #f8f9fa;
    }

    .badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .badge-demissao { background: #ffeaa7; color: #d63031; }
    .badge-pedido { background: #74b9ff; color: #0984e3; }
    .badge-termino { background: #fd79a8; color: #e84393; }
    .badge-aposentadoria { background: #00b894; color: #00a085; }
    .badge-outros { background: #ddd; color: #636e72; }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .btn-action {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.3rem;
    }

    .btn-restore {
        background: #28a745;
        color: white;
    }

    .btn-restore:hover {
        background: #218838;
        transform: translateY(-1px);
    }

    .btn-details {
        background: #007bff;
        color: white;
    }

    .btn-details:hover {
        background: #0056b3;
        transform: translateY(-1px);
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="desligados-header">
        <h1><i class="fas fa-user-times"></i> Funcionários Desligados</h1>
        <p>Gestão e controle de funcionários desligados com histórico completo</p>
    </div>

    <!-- Estatísticas -->
    <div class="stats-cards">
        <div class="stat-card">
            <h3>Total Desligados</h3>
            <p class="number">{{ total_desligados or 0 }}</p>
        </div>
        <div class="stat-card">
            <h3>Este Mês</h3>
            <p class="number">{{ desligados_mes or 0 }}</p>
        </div>
        <div class="stat-card">
            <h3>Demissões</h3>
            <p class="number">{{ total_demissoes or 0 }}</p>
        </div>
        <div class="stat-card">
            <h3>Pedidos Demissão</h3>
            <p class="number">{{ total_pedidos or 0 }}</p>
        </div>
    </div>

    <!-- Filtros -->
    <div class="filters-section">
        <div class="filters-header" onclick="toggleFilters()">
            <h3>
                <i class="fas fa-filter"></i>
                Filtros de Pesquisa
            </h3>
            <i class="fas fa-chevron-up filters-toggle" id="filtersToggle"></i>
        </div>

        <div class="filters-content expanded" id="filtersContent">
            <form method="GET" id="filtrosForm">
            <div class="filters-grid">
                <div class="filter-group">
                    <label for="motivo">
                        <i class="fas fa-clipboard-list"></i>
                        Motivo do Desligamento
                    </label>
                    <select name="motivo" id="motivo" class="form-control">
                        <option value="">Todos os motivos</option>
                        <option value="Demissao_sem_justa_causa" {{ 'selected' if filtros.motivo == 'Demissao_sem_justa_causa' else '' }}>Demissão sem justa causa</option>
                        <option value="Demissao_com_justa_causa" {{ 'selected' if filtros.motivo == 'Demissao_com_justa_causa' else '' }}>Demissão com justa causa</option>
                        <option value="Pedido_demissao" {{ 'selected' if filtros.motivo == 'Pedido_demissao' else '' }}>Pedido de demissão</option>
                        <option value="Termino_contrato" {{ 'selected' if filtros.motivo == 'Termino_contrato' else '' }}>Término de contrato</option>
                        <option value="Aposentadoria" {{ 'selected' if filtros.motivo == 'Aposentadoria' else '' }}>Aposentadoria</option>
                        <option value="Outros" {{ 'selected' if filtros.motivo == 'Outros' else '' }}>Outros</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="periodo">
                        <i class="fas fa-calendar-alt"></i>
                        Período
                    </label>
                    <select name="periodo" id="periodo" class="form-control">
                        <option value="">Todos os períodos</option>
                        <option value="30" {{ 'selected' if filtros.periodo == '30' else '' }}>Últimos 30 dias</option>
                        <option value="90" {{ 'selected' if filtros.periodo == '90' else '' }}>Últimos 90 dias</option>
                        <option value="365" {{ 'selected' if filtros.periodo == '365' else '' }}>Último ano</option>
                    </select>
                </div>

                <div class="filter-group search-group">
                    <label for="busca">
                        <i class="fas fa-search"></i>
                        Buscar Funcionário
                    </label>
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" name="busca" id="busca" class="form-control" placeholder="Digite o nome ou matrícula..." value="{{ filtros.busca or '' }}">
                    </div>
                </div>

                <div class="filter-group actions-group">
                    <button type="submit" class="btn-filter">
                        <i class="fas fa-filter"></i>
                        Filtrar
                    </button>
                    <button type="button" class="btn-clear" onclick="limparFiltros()">
                        <i class="fas fa-times"></i>
                        Limpar
                    </button>
                </div>
            </div>
        </form>
        </div>
    </div>

    <!-- Tabela -->
    <div class="table-container">
        <div class="table-header">
            <h2>
                <i class="fas fa-table"></i>
                Funcionários Desligados
            </h2>
            <div class="table-info">
                <i class="fas fa-info-circle"></i>
                {{ funcionarios_desligados|length if funcionarios_desligados else 0 }} registro{{ 's' if (funcionarios_desligados|length != 1) else '' }} encontrado{{ 's' if (funcionarios_desligados|length != 1) else '' }}
            </div>
        </div>
        
        {% if funcionarios_desligados %}
        <table class="desligados-table">
            <thead>
                <tr>
                    <th>Funcionário</th>
                    <th>Matrícula</th>
                    <th>Cargo</th>
                    <th>Data Desligamento</th>
                    <th>Motivo</th>
                    <th>Responsável</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                {% for funcionario in funcionarios_desligados %}
                <tr>
                    <td>
                        <strong>{{ funcionario.nome_completo }}</strong><br>
                        <small class="text-muted">{{ funcionario.cpf }}</small>
                    </td>
                    <td>{{ funcionario.matricula_empresa }}</td>
                    <td>{{ funcionario.cargo }}</td>
                    <td>{{ funcionario.data_desligamento.strftime('%d/%m/%Y') if funcionario.data_desligamento else '-' }}</td>
                    <td>
                        {% set motivo_class = {
                            'Demissao_sem_justa_causa': 'badge-demissao',
                            'Demissao_com_justa_causa': 'badge-demissao', 
                            'Pedido_demissao': 'badge-pedido',
                            'Termino_contrato': 'badge-termino',
                            'Aposentadoria': 'badge-aposentadoria'
                        }.get(funcionario.motivo_desligamento, 'badge-outros') %}
                        <span class="badge {{ motivo_class }}">
                            {{ funcionario.motivo_desligamento.replace('_', ' ').title() }}
                        </span>
                    </td>
                    <td>{{ funcionario.usuario_responsavel or 'Sistema' }}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-action btn-restore" onclick="restaurarFuncionario({{ funcionario.funcionario_id_original }}, '{{ funcionario.nome_completo }}')">
                                <i class="fas fa-undo"></i> Restaurar
                            </button>
                            <a href="/funcionarios-desligados/{{ funcionario.funcionario_id_original }}/detalhes" class="btn-action btn-details">
                                <i class="fas fa-eye"></i> Detalhes
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-users"></i>
            <h3>Nenhum funcionário desligado encontrado</h3>
            <p>Não há funcionários desligados que correspondam aos filtros selecionados.</p>
        </div>
        {% endif %}
    </div>
</div>

<script>
function restaurarFuncionario(funcionarioId, nome) {
    if (confirm(`Tem certeza que deseja restaurar o funcionário ${nome}?\n\nEsta ação irá:\n• Reativar o funcionário\n• Restaurar todos os dados históricos\n• Remover da lista de desligados`)) {
        fetch(`/funcionarios/restaurar/${funcionarioId}`, {
            method: 'POST',
            credentials: 'same-origin'
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                alert('Erro ao restaurar funcionário. Tente novamente.');
            }
        })
        .catch(error => {
            alert('Erro na requisição: ' + error.message);
        });
    }
}

// Função para toggle dos filtros
function toggleFilters() {
    const content = document.getElementById('filtersContent');
    const toggle = document.getElementById('filtersToggle');

    if (content.classList.contains('expanded')) {
        content.classList.remove('expanded');
        content.classList.add('collapsed');
        toggle.classList.remove('fa-chevron-up');
        toggle.classList.add('fa-chevron-down');
    } else {
        content.classList.remove('collapsed');
        content.classList.add('expanded');
        toggle.classList.remove('fa-chevron-down');
        toggle.classList.add('fa-chevron-up');
    }
}

// Função para limpar filtros
function limparFiltros() {
    document.getElementById('motivo').value = '';
    document.getElementById('periodo').value = '';
    document.getElementById('busca').value = '';
    document.getElementById('filtrosForm').submit();
}

// Auto-submit do formulário quando filtros mudarem
document.getElementById('motivo').addEventListener('change', function() {
    document.getElementById('filtrosForm').submit();
});

document.getElementById('periodo').addEventListener('change', function() {
    document.getElementById('filtrosForm').submit();
});

// Melhorar experiência do campo de busca
document.getElementById('busca').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        document.getElementById('filtrosForm').submit();
    }
});

// Adicionar feedback visual aos filtros ativos
document.addEventListener('DOMContentLoaded', function() {
    const filtros = ['motivo', 'periodo', 'busca'];
    let filtrosAtivos = 0;

    filtros.forEach(function(filtroId) {
        const elemento = document.getElementById(filtroId);
        if (elemento && elemento.value) {
            filtrosAtivos++;
            elemento.style.borderColor = '#dc3545';
            elemento.style.backgroundColor = '#fff5f5';
        }
    });

    // Mostrar indicador de filtros ativos
    if (filtrosAtivos > 0) {
        const header = document.querySelector('.filters-header h3');
        if (header) {
            header.innerHTML += ` <span style="background: #dc3545; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem; margin-left: 0.5rem;">${filtrosAtivos} ativo${filtrosAtivos > 1 ? 's' : ''}</span>`;
        }
    }
});
</script>
{% endblock %}
