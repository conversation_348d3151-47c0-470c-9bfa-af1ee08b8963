#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para verificar a aplicação da correção
Data: 03/07/2025
"""

from utils.database import get_db_connection
import requests
import json
import sys

def main():
    try:
        # Verificar a empresa ID 7 (criada anteriormente)
        empresa_id = 7
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, razao_social, ativa FROM empresas WHERE id = %s", (empresa_id,))
        empresa = cursor.fetchone()
        
        if empresa:
            ativa = empresa['ativa'] if isinstance(empresa, dict) else empresa[2]
            print(f"Empresa ID {empresa_id} - Ativa: {ativa}")
        else:
            print(f"Empresa ID {empresa_id} não encontrada!")
            
        # Verificar se a função de exclusão está funcionando
        print("\nTestando a exclusão de uma empresa através da API...")
        
        # Login
        session = requests.Session()
        login_url = "http://************/login"
        login_data = {"usuario": "admin", "senha": "@Ric6109"}
        session.post(login_url, data=login_data)
        
        # Excluir empresa
        url = f"http://************/configuracoes/empresas/{empresa_id}/excluir"
        headers = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
        data = {'is_ajax': True}
        
        response = session.post(url, headers=headers, data=json.dumps(data))
        print(f"Status code: {response.status_code}")
        print(f"Resposta: {response.text}")
        
        # Verificar se a empresa foi marcada como inativa
        cursor.execute("SELECT id, razao_social, ativa FROM empresas WHERE id = %s", (empresa_id,))
        empresa_depois = cursor.fetchone()
        
        if empresa_depois:
            ativa_depois = empresa_depois['ativa'] if isinstance(empresa_depois, dict) else empresa_depois[2]
            print(f"Empresa depois da exclusão - ID: {empresa_id}, Ativa: {ativa_depois}")
            
            if not ativa_depois:
                print(f"✅ Empresa ID {empresa_id} foi marcada como inativa com sucesso!")
            else:
                print(f"❌ Empresa ID {empresa_id} ainda está ativa no banco de dados!")
        else:
            print(f"❌ Empresa ID {empresa_id} não encontrada após a exclusão!")
            
        conn.close()
        
    except Exception as e:
        print(f"Erro: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 