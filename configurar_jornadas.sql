-- =====================================================
-- CONFIGURAÇÃO DE JORNADAS DINÂMICAS
-- Sistema de Controle de Ponto - RLPONTO-WEB
-- Data: 05/07/2025
-- =====================================================

-- 1. VERIFICAR JORNADAS EXISTENTES
SELECT '=== JORNADAS EXISTENTES ===' as info;
SELECT 
    j.id, j.empresa_id, j.nome_jornada, j.padrao, j.ativa,
    e.razao_social
FROM jornadas_trabalho j
JOIN empresas e ON j.empresa_id = e.id
WHERE j.ativa = TRUE
ORDER BY j.empresa_id, j.padrao DESC;

-- 2. RENOMEAR JORNADAS PADRÃO PARA "PRIMEIRO TURNO"
SELECT '=== RENOMEANDO JORNADAS PADRÃO ===' as info;
UPDATE jornadas_trabalho 
SET nome_jornada = 'Primeiro Turno'
WHERE padrao = TRUE AND nome_jornada != 'Primeiro Turno';

-- Verificar renomeação
SELECT 'Jornadas após renomeação:' as info;
SELECT 
    j.id, j.empresa_id, j.nome_jornada, j.padrao,
    e.razao_social
FROM jornadas_trabalho j
JOIN empresas e ON j.empresa_id = e.id
WHERE j.padrao = TRUE AND j.ativa = TRUE;

-- 3. CRIAR "SEGUNDO TURNO" PARA EMPRESAS QUE NÃO TÊM
SELECT '=== CRIANDO SEGUNDO TURNO ===' as info;

-- Buscar empresas que têm jornada padrão mas não têm segundo turno
INSERT INTO jornadas_trabalho (
    empresa_id, nome_jornada, descricao, tipo_jornada, categoria_funcionario,
    seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
    sabado_entrada, sabado_saida, intervalo_inicio, intervalo_fim,
    intervalo_duracao_minutos, tolerancia_entrada_minutos, 
    ativa, padrao, ordem_exibicao, cadastrado_por
)
SELECT DISTINCT
    j.empresa_id,
    'Segundo Turno' as nome_jornada,
    'Jornada do segundo turno - horário vespertino/noturno' as descricao,
    'Diurno' as tipo_jornada,
    'Geral' as categoria_funcionario,
    '14:00:00' as seg_qui_entrada,    -- 14:00 às 22:00
    '22:00:00' as seg_qui_saida,
    '14:00:00' as sexta_entrada,      -- 14:00 às 21:00 na sexta
    '21:00:00' as sexta_saida,
    NULL as sabado_entrada,           -- Sem trabalho no sábado
    NULL as sabado_saida,
    '18:00:00' as intervalo_inicio,   -- Intervalo 18:00-19:00
    '19:00:00' as intervalo_fim,
    60 as intervalo_duracao_minutos,
    15 as tolerancia_entrada_minutos,
    TRUE as ativa,
    FALSE as padrao,                  -- NÃO é padrão
    2 as ordem_exibicao,
    'sistema' as cadastrado_por
FROM jornadas_trabalho j
WHERE j.padrao = TRUE 
  AND j.ativa = TRUE
  AND j.empresa_id NOT IN (
      SELECT DISTINCT empresa_id 
      FROM jornadas_trabalho 
      WHERE nome_jornada = 'Segundo Turno' AND ativa = TRUE
  );

-- 4. CRIAR JORNADA PADRÃO PARA EMPRESAS SEM JORNADAS
SELECT '=== CRIANDO JORNADA PADRÃO PARA EMPRESAS SEM JORNADAS ===' as info;

INSERT INTO jornadas_trabalho (
    empresa_id, nome_jornada, descricao, tipo_jornada, categoria_funcionario,
    seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
    intervalo_inicio, intervalo_fim, intervalo_duracao_minutos,
    tolerancia_entrada_minutos, ativa, padrao, ordem_exibicao, cadastrado_por
)
SELECT 
    e.id as empresa_id,
    'Primeiro Turno' as nome_jornada,
    'Jornada padrão da empresa - horário comercial' as descricao,
    'Diurno' as tipo_jornada,
    'Geral' as categoria_funcionario,
    '08:00:00' as seg_qui_entrada,
    '17:00:00' as seg_qui_saida,
    '08:00:00' as sexta_entrada,
    '16:00:00' as sexta_saida,
    '12:00:00' as intervalo_inicio,
    '13:00:00' as intervalo_fim,
    60 as intervalo_duracao_minutos,
    15 as tolerancia_entrada_minutos,
    TRUE as ativa,
    TRUE as padrao,
    1 as ordem_exibicao,
    'sistema' as cadastrado_por
FROM empresas e
WHERE e.ativa = TRUE
  AND e.id NOT IN (
      SELECT DISTINCT empresa_id 
      FROM jornadas_trabalho 
      WHERE ativa = TRUE
  );

-- 5. VERIFICAR RESULTADO FINAL
SELECT '=== RESULTADO FINAL ===' as info;
SELECT 
    e.id as empresa_id,
    e.razao_social,
    e.nome_fantasia,
    COUNT(j.id) as total_jornadas,
    SUM(CASE WHEN j.padrao = TRUE THEN 1 ELSE 0 END) as jornadas_padrao,
    GROUP_CONCAT(j.nome_jornada ORDER BY j.padrao DESC, j.nome_jornada SEPARATOR ', ') as jornadas
FROM empresas e
LEFT JOIN jornadas_trabalho j ON e.id = j.empresa_id AND j.ativa = TRUE
WHERE e.ativa = TRUE
GROUP BY e.id, e.razao_social, e.nome_fantasia
ORDER BY e.razao_social;

-- 6. LISTAR TODAS AS JORNADAS POR EMPRESA
SELECT '=== JORNADAS POR EMPRESA ===' as info;
SELECT 
    e.razao_social as empresa,
    j.nome_jornada,
    j.tipo_jornada,
    CONCAT(j.seg_qui_entrada, ' - ', j.seg_qui_saida) as horario_seg_qui,
    CASE WHEN j.padrao THEN 'SIM' ELSE 'NÃO' END as padrao,
    CASE WHEN j.ativa THEN 'ATIVA' ELSE 'INATIVA' END as status
FROM empresas e
LEFT JOIN jornadas_trabalho j ON e.id = j.empresa_id
WHERE e.ativa = TRUE AND (j.ativa = TRUE OR j.id IS NULL)
ORDER BY e.razao_social, j.padrao DESC, j.nome_jornada;

SELECT '=== CONFIGURAÇÃO CONCLUÍDA ===' as info;
