#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para adicionar campos de biometria à tabela funcionários
e configurar permissões de acesso para o IP atual.
"""

import pymysql
import logging
import socket

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_current_ip():
    """Obtém o IP atual da máquina"""
    try:
        # Conecta a um endereço externo para descobrir o IP local usado
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "localhost"

def configurar_permissoes_mysql(cursor, ip_atual):
    """Configura permissões do usuário para o IP atual"""
    try:
        # Criar usuários com permissões para o IP atual
        usuarios_sql = [
            f"CREATE USER IF NOT EXISTS 'controle_user'@'{ip_atual}' IDENTIFIED BY 'controle123';",
            f"CREATE USER IF NOT EXISTS 'controle_user'@'10.19.208.%' IDENTIFIED BY 'controle123';",
            f"CREATE USER IF NOT EXISTS 'controle_user'@'localhost' IDENTIFIED BY 'controle123';"
        ]
        
        for sql in usuarios_sql:
            try:
                cursor.execute(sql)
                logger.info(f"✅ Usuário criado: {sql}")
            except Exception as e:
                if "already exists" in str(e).lower():
                    logger.info(f"ℹ️ Usuário já existe: {sql}")
                else:
                    logger.warning(f"⚠️ Erro ao criar usuário: {e}")
        
        # Conceder permissões
        permissoes_sql = [
            f"GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'{ip_atual}';",
            f"GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'10.19.208.%';",
            f"GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'localhost';"
        ]
        
        for sql in permissoes_sql:
            try:
                cursor.execute(sql)
                logger.info(f"✅ Permissão concedida: {sql}")
            except Exception as e:
                logger.warning(f"⚠️ Erro ao conceder permissão: {e}")
        
        # Atualizar privilégios
        cursor.execute("FLUSH PRIVILEGES;")
        logger.info("✅ Privilégios atualizados")
        
        return True
    except Exception as e:
        logger.error(f"❌ Erro na configuração de permissões: {e}")
        return False

def adicionar_campos_biometria(cursor):
    """Adiciona campos de biometria à tabela funcionários"""
    campos_sql = [
        "ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS biometria_template_1 LONGTEXT COMMENT 'Template biométrico do primeiro dedo';",
        "ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS biometria_template_2 LONGTEXT COMMENT 'Template biométrico do segundo dedo';",
        "ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS biometria_qualidade_1 INT DEFAULT 0 COMMENT 'Qualidade da captura do primeiro dedo';",
        "ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS biometria_qualidade_2 INT DEFAULT 0 COMMENT 'Qualidade da captura do segundo dedo';",
        "ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS biometria_data_cadastro TIMESTAMP NULL COMMENT 'Data de cadastro da biometria';"
    ]
    
    campos_adicionados = 0
    for sql in campos_sql:
        try:
            cursor.execute(sql)
            campo_nome = sql.split("ADD COLUMN IF NOT EXISTS ")[1].split(" ")[0]
            logger.info(f"✅ Campo adicionado: {campo_nome}")
            campos_adicionados += 1
        except Exception as e:
            if "Duplicate column name" in str(e):
                campo_nome = sql.split("ADD COLUMN IF NOT EXISTS ")[1].split(" ")[0]
                logger.info(f"ℹ️ Campo já existe: {campo_nome}")
            else:
                logger.error(f"❌ Erro ao adicionar campo: {e}")
    
    return campos_adicionados

def main():
    """Função principal"""
    print("=" * 60)
    print("🔧 CONFIGURAÇÃO BIOMETRIA + PERMISSÕES MYSQL")
    print("=" * 60)
    
    # Obter IP atual
    ip_atual = get_current_ip()
    logger.info(f"🌐 IP atual detectado: {ip_atual}")
    
    # Configurações de conexão para tentar
    configs = [
        {
            'host': '************',
            'user': 'root',
            'password': '',
            'database': 'controle_ponto',
            'charset': 'utf8mb4',
            'name': 'root_sem_senha'
        },
        {
            'host': '************',
            'user': 'root',
            'password': 'root',
            'database': 'controle_ponto',
            'charset': 'utf8mb4',
            'name': 'root_com_senha'
        },
        {
            'host': '************',
            'user': 'controle_user',
            'password': 'controle123',
            'database': 'controle_ponto',
            'charset': 'utf8mb4',
            'name': 'controle_user'
        }
    ]
    
    conexao_sucesso = False
    
    for config in configs:
        try:
            logger.info(f"🔌 Tentando conectar com {config['name']}...")
            
            config_copy = config.copy()
            nome_config = config_copy.pop('name')
            
            connection = pymysql.connect(**config_copy)
            cursor = connection.cursor()
            
            logger.info(f"✅ Conectado com sucesso usando {nome_config}")
            
            # Configurar permissões (apenas se for root)
            if config['user'] == 'root':
                logger.info("🔐 Configurando permissões de usuário...")
                if configurar_permissoes_mysql(cursor, ip_atual):
                    logger.info("✅ Permissões configuradas com sucesso")
                else:
                    logger.warning("⚠️ Algumas permissões podem não ter sido configuradas")
            
            # Adicionar campos de biometria
            logger.info("🔬 Adicionando campos de biometria...")
            campos_adicionados = adicionar_campos_biometria(cursor)
            
            # Confirmar alterações
            connection.commit()
            
            # Verificar estrutura da tabela
            cursor.execute("DESCRIBE funcionarios;")
            colunas = cursor.fetchall()
            colunas_biometria = [col for col in colunas if 'biometria' in col[0]]
            
            logger.info(f"📋 Colunas de biometria encontradas: {len(colunas_biometria)}")
            for col in colunas_biometria:
                logger.info(f"   - {col[0]} ({col[1]})")
            
            connection.close()
            conexao_sucesso = True
            break
            
        except Exception as e:
            logger.error(f"❌ Falha com {config['name']}: {e}")
            continue
    
    if conexao_sucesso:
        print("\n" + "=" * 60)
        print("🎉 CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!")
        print("=" * 60)
        print(f"✅ IP configurado: {ip_atual}")
        print("✅ Campos de biometria adicionados")
        print("✅ Permissões atualizadas")
        print("\n🔧 Próximos passos:")
        print("1. Teste a aplicação web")
        print("2. Verifique se a captura biométrica funciona")
        print("3. Cadastre um funcionário com biometria")
    else:
        print("\n" + "=" * 60)
        print("💥 CONFIGURAÇÃO FALHOU!")
        print("=" * 60)
        print("❌ Não foi possível conectar com nenhuma configuração")
        print(f"🌐 IP atual: {ip_atual}")
        print("\n🔧 Soluções:")
        print("1. Verifique se o MySQL está rodando no servidor ************")
        print("2. Configure manualmente as permissões do usuário")
        print("3. Verifique conectividade de rede")

if __name__ == "__main__":
    main()