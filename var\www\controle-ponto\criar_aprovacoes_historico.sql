-- ========================================
-- CRIAR APROVAÇÕES E HISTÓRICO - JOÃO SILVA
-- Data: 11/07/2025
-- ========================================

USE controle_ponto;

-- Obter ID do funcionário
SET @funcionario_id = (SELECT id FROM funcionarios WHERE cpf = '123.456.789-00');

-- ========================================
-- 1. CRIAR APROVAÇÃO HORA EXTRA 08/07/2025
-- ========================================

INSERT INTO aprovacoes_horas_extras (
    funcionario_id, data_referencia, inicio_extra, fim_extra,
    duracao_minutos, motivo_hora_extra, porcentagem_extra,
    status_aprovacao
) VALUES (
    @funcionario_id, '2025-07-08', '2025-07-08 17:45:00', '2025-07-08 19:30:00',
    105, 'Deploy urgente do sistema - correção crítica', 50.00,
    'PENDENTE'
);

SET @aprovacao_id = LAST_INSERT_ID();

-- Atualizar registros de ponto com aprovacao_id
UPDATE registros_ponto 
SET aprovacao_id = @aprovacao_id 
WHERE funcionario_id = @funcionario_id 
AND DATE(data_hora) = '2025-07-08' 
AND tipo_registro IN ('inicio_extra', 'fim_extra');

-- ========================================
-- 2. CRIAR HISTÓRICO DE EVENTOS
-- ========================================

-- Evento: Hora extra solicitada
INSERT INTO historico_funcionario (
    funcionario_id, tipo_evento, data_evento, data_referencia,
    detalhes, valor_minutos, status_aprovacao
) VALUES (
    @funcionario_id, 'HORA_EXTRA_SOLICITADA', '2025-07-08 19:30:00', '2025-07-08',
    'Solicitação de hora extra - Deploy urgente do sistema (105 minutos)', 105, 'PENDENTE'
);

-- Evento: Atraso registrado (terça-feira)
INSERT INTO historico_funcionario (
    funcionario_id, tipo_evento, data_evento, data_referencia,
    detalhes, valor_minutos, status_aprovacao
) VALUES (
    @funcionario_id, 'ATRASO_REGISTRADO', '2025-07-08 08:45:00', '2025-07-08',
    'Atraso de 45 minutos - Trânsito intenso', -45, 'NAO_APLICAVEL'
);

-- Evento: Banco de horas debitado (quarta - almoço longo)
INSERT INTO historico_funcionario (
    funcionario_id, tipo_evento, data_evento, data_referencia,
    detalhes, valor_minutos, status_aprovacao
) VALUES (
    @funcionario_id, 'BANCO_HORAS_DEBITADO', '2025-07-10 14:30:00', '2025-07-10',
    'Almoço estendido para consulta médica (90 minutos extras)', -90, 'NAO_APLICAVEL'
);

-- ========================================
-- 3. SIMULAR APROVAÇÃO RH (APROVADA)
-- ========================================

-- Aprovar a hora extra
UPDATE aprovacoes_horas_extras 
SET status_aprovacao = 'APROVADO',
    aprovado_por = 1,
    data_aprovacao = '2025-07-09 09:00:00',
    observacoes_aprovacao = 'Aprovado - Deploy crítico justificado. Parabéns pela dedicação.'
WHERE id = @aprovacao_id;

-- Registrar aprovação no histórico
INSERT INTO historico_funcionario (
    funcionario_id, tipo_evento, data_evento, data_referencia,
    detalhes, valor_minutos, status_aprovacao, aprovado_por
) VALUES (
    @funcionario_id, 'HORA_EXTRA_APROVADA', '2025-07-09 09:00:00', '2025-07-08',
    'Hora extra aprovada pelo RH - 105 minutos creditados no banco de horas', 105, 'APROVADO', 1
);

-- ========================================
-- 4. CONFIGURAR PORCENTAGEM SÁBADO
-- ========================================

INSERT INTO configuracoes_hora_extra (
    funcionario_id, data_referencia, tipo_dia, porcentagem_extra, aplicado_por,
    observacoes
) VALUES (
    @funcionario_id, '2025-07-12', 'SABADO', 100.00, 1,
    'Plantão de manutenção - 100% hora extra conforme acordo coletivo'
);

-- ========================================
-- 5. CRIAR NOTIFICAÇÃO RH
-- ========================================

INSERT INTO notificacoes_rh (
    tipo_notificacao, funcionario_id, aprovacao_id, titulo, mensagem
) VALUES (
    'HORA_EXTRA_PENDENTE', @funcionario_id, @aprovacao_id,
    'Nova solicitação de hora extra - João Silva Santos',
    'Funcionário João Silva Santos solicitou aprovação para 105 minutos de hora extra em 08/07/2025. Motivo: Deploy urgente do sistema.'
);

-- Marcar notificação como lida (RH já aprovou)
UPDATE notificacoes_rh 
SET lida = TRUE, data_leitura = '2025-07-09 09:00:00'
WHERE funcionario_id = @funcionario_id;

-- ========================================
-- 6. VERIFICAÇÕES E RELATÓRIOS
-- ========================================

SELECT 'Aprovações e histórico criados com sucesso!' as status;

-- Relatório de aprovações
SELECT 
    'APROVAÇÕES' as tipo,
    a.id,
    a.data_referencia,
    a.duracao_minutos,
    a.status_aprovacao,
    a.motivo_hora_extra,
    a.observacoes_aprovacao
FROM aprovacoes_horas_extras a
WHERE a.funcionario_id = @funcionario_id;

-- Relatório de histórico
SELECT 
    'HISTÓRICO' as tipo,
    h.data_evento,
    h.tipo_evento,
    h.detalhes,
    h.valor_minutos,
    h.status_aprovacao
FROM historico_funcionario h
WHERE h.funcionario_id = @funcionario_id
ORDER BY h.data_evento;

-- Relatório de configurações
SELECT 
    'CONFIGURAÇÕES' as tipo,
    c.data_referencia,
    c.tipo_dia,
    c.porcentagem_extra,
    c.observacoes
FROM configuracoes_hora_extra c
WHERE c.funcionario_id = @funcionario_id;

-- Relatório de notificações
SELECT 
    'NOTIFICAÇÕES' as tipo,
    n.titulo,
    n.mensagem,
    n.lida,
    n.data_criacao
FROM notificacoes_rh n
WHERE n.funcionario_id = @funcionario_id;
