#!/usr/bin/env python3
import mysql.connector
import sys

def conectar_db():
    return mysql.connector.connect(
        host='************',
        user='cavalcrod',
        password='200381',
        database='controle_ponto'
    )

def implementar_jornadas():
    try:
        print("🚀 Conectando ao banco...")
        conn = conectar_db()
        cursor = conn.cursor(dictionary=True)
        
        # 1. Verificar jornadas existentes
        print("📋 Verificando jornadas existentes...")
        cursor.execute("SELECT id, empresa_id, nome_jornada, padrao FROM jornadas_trabalho ORDER BY empresa_id")
        jornadas = cursor.fetchall()
        print(f"   ✅ Encontradas {len(jornadas)} jornadas")
        
        # 2. Renomear para Primeiro Turno
        print("🔄 Renomeando jornadas padrão para 'Primeiro Turno'...")
        cursor.execute("UPDATE jornadas_trabalho SET nome_jornada = 'Primeiro Turno' WHERE padrao = TRUE")
        conn.commit()
        print(f"   ✅ {cursor.rowcount} jornadas renomeadas")
        
        # 3. Buscar empresas com jornada padrão
        print("🏢 Buscando empresas para criar Segundo Turno...")
        cursor.execute("SELECT DISTINCT empresa_id FROM jornadas_trabalho WHERE padrao = TRUE")
        empresas = cursor.fetchall()
        print(f"   ✅ Encontradas {len(empresas)} empresas")
        
        # 4. Criar Segundo Turno para cada empresa
        print("➕ Criando Segundo Turno...")
        for empresa in empresas:
            emp_id = empresa['empresa_id']
            
            # Verificar se já existe
            cursor.execute("SELECT id FROM jornadas_trabalho WHERE empresa_id = %s AND nome_jornada = 'Segundo Turno'", (emp_id,))
            existe = cursor.fetchone()
            
            if not existe:
                sql = """
                INSERT INTO jornadas_trabalho (
                    empresa_id, nome_jornada, descricao, tipo_jornada, categoria_funcionario,
                    seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
                    intervalo_inicio, intervalo_fim, intervalo_duracao_minutos,
                    tolerancia_entrada_minutos, ativa, padrao, ordem_exibicao, cadastrado_por
                ) VALUES (
                    %s, 'Segundo Turno', 'Jornada do segundo turno - horário vespertino/noturno',
                    'Diurno', 'Geral', '14:00:00', '22:00:00', '14:00:00', '21:00:00',
                    '18:00:00', '19:00:00', 60, 15, TRUE, FALSE, 2, 'sistema'
                )
                """
                cursor.execute(sql, (emp_id,))
                conn.commit()
                print(f"   ✅ Criado Segundo Turno para empresa {emp_id}")
            else:
                print(f"   ⚠️  Empresa {emp_id} já tem Segundo Turno")
        
        # 5. Verificar resultado final
        print("📊 Verificando resultado final...")
        cursor.execute("SELECT empresa_id, nome_jornada, padrao FROM jornadas_trabalho ORDER BY empresa_id, padrao DESC")
        resultado = cursor.fetchall()
        
        empresas_count = {}
        for jornada in resultado:
            emp_id = jornada['empresa_id']
            if emp_id not in empresas_count:
                empresas_count[emp_id] = []
            empresas_count[emp_id].append(jornada['nome_jornada'])
        
        for emp_id, jornadas_nomes in empresas_count.items():
            print(f"   🏢 Empresa {emp_id}: {', '.join(jornadas_nomes)}")
        
        cursor.close()
        conn.close()
        print("🎉 Implementação concluída com sucesso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    implementar_jornadas()
