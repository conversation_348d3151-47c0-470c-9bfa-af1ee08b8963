@echo off
:: =====================================================================
:: ANALISADOR BRIDGE ZK4500 - RLPONTO-WEB v1.0
:: Diagnóstica status, conectividade e detecta problemas do bridge
:: Desenvolvido por: <PERSON> Rodrigues - AiNexus Tecnologia
:: =====================================================================

setlocal enabledelayedexpansion

:: Definir variáveis
set BRIDGE_DIR=C:\RLPonto-Bridge

:: Verificar se existe sistema de logging
if exist "%BRIDGE_DIR%\bridge_logger.bat" (
    call "%BRIDGE_DIR%\bridge_logger.bat" :START_SECTION "ANALISE_BRIDGE_ZK4500"
    set LOGGING_ENABLED=1
) else (
    set LOGGING_ENABLED=0
)

echo.
echo ========================================================
echo   ANALISADOR BRIDGE ZK4500 - RLPONTO-WEB v1.0
echo   AiNexus Tecnologia - Sistema Biometrico Empresarial
echo ========================================================
echo.

:: Definir variáveis
set BRIDGE_NAME=RLPonto-BridgeZK4500
set BRIDGE_DIR=C:\RLPonto-Bridge
set BRIDGE_URL=http://localhost:8080
set PYTHON_EXE=python.exe

echo [DIAGNOSTICO INICIADO EM] %date% %time%
echo ========================================================

echo.
echo [1/10] Verificando instalacao do Python...
%PYTHON_EXE% --version >nul 2>&1
if %errorLevel% == 0 (
    for /f "tokens=*" %%i in ('%PYTHON_EXE% --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo [OK] %PYTHON_VERSION% encontrado
) else (
    echo [ERRO] Python nao encontrado no PATH
    echo        Instale Python 3.8+ e adicione ao PATH do sistema
)

echo.
echo [2/10] Verificando diretorio de instalacao...
if exist "%BRIDGE_DIR%" (
    echo [OK] Diretorio existe: %BRIDGE_DIR%
    dir "%BRIDGE_DIR%" | findstr /i "biometric_bridge_service.py" >nul 2>&1
    if %errorLevel% == 0 (
        echo [OK] Arquivo principal encontrado: biometric_bridge_service.py
    ) else (
        echo [ERRO] Arquivo principal nao encontrado: biometric_bridge_service.py
    )
    
    if exist "%BRIDGE_DIR%\INSTALACAO_INFO.txt" (
        echo [OK] Arquivo de informacoes encontrado
        echo [INFO] Detalhes da instalacao:
        type "%BRIDGE_DIR%\INSTALACAO_INFO.txt" | findstr /v "^$"
    ) else (
        echo [AVISO] Arquivo de informacoes nao encontrado
    )
) else (
    echo [ERRO] Diretorio nao existe: %BRIDGE_DIR%
    echo        Execute o instalador.bat primeiro
)

echo.
echo [3/10] Verificando servico do Windows...
sc query "%BRIDGE_NAME%" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Servico registrado no Windows
    for /f "tokens=*" %%i in ('sc query "%BRIDGE_NAME%" ^| findstr "STATE"') do (
        set SERVICE_STATE=%%i
        echo [INFO] %SERVICE_STATE%
    )
    
    sc qc "%BRIDGE_NAME%" | findstr "START_TYPE" >nul 2>&1
    if %errorLevel% == 0 (
        for /f "tokens=*" %%i in ('sc qc "%BRIDGE_NAME%" ^| findstr "START_TYPE"') do (
            set START_TYPE=%%i
            echo [INFO] %START_TYPE%
        )
    )
) else (
    echo [ERRO] Servico nao registrado no Windows
    echo        Execute o instalador.bat para registrar o servico
)

echo.
echo [4/10] Verificando processos Python em execucao...
tasklist | findstr /i python.exe >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Processos Python encontrados:
    tasklist | findstr /i python.exe
) else (
    echo [INFO] Nenhum processo Python em execucao
)

echo.
echo [5/10] Verificando porta 8080...
netstat -an | findstr :8080 >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Porta 8080 em uso:
    netstat -an | findstr :8080
) else (
    echo [AVISO] Porta 8080 nao esta em uso
    echo         O bridge pode nao estar rodando
)

echo.
echo [6/10] Verificando dependencias Python...
if exist "%BRIDGE_DIR%" (
    cd /d "%BRIDGE_DIR%"
    echo [INFO] Verificando Flask...
    %PYTHON_EXE% -c "import flask; print('Flask version:', flask.__version__)" 2>nul
    if %errorLevel% == 0 (
        echo [OK] Flask instalado e funcionando
    ) else (
        echo [ERRO] Flask nao encontrado ou com problemas
    )
    
    echo [INFO] Verificando Flask-CORS...
    %PYTHON_EXE% -c "import flask_cors; print('Flask-CORS instalado')" 2>nul
    if %errorLevel% == 0 (
        echo [OK] Flask-CORS instalado e funcionando
    ) else (
        echo [ERRO] Flask-CORS nao encontrado ou com problemas
    )
) else (
    echo [AVISO] Diretorio de instalacao nao existe - pulando verificacao
)

echo.
echo [7/10] Testando conectividade HTTP...
echo [INFO] Testando: %BRIDGE_URL%/api/bridge-status
curl -s --connect-timeout 5 "%BRIDGE_URL%/api/bridge-status" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Bridge respondendo via HTTP
    echo [INFO] Resposta do bridge:
    curl -s "%BRIDGE_URL%/api/bridge-status" 2>nul
) else (
    echo [ERRO] Bridge nao responde via HTTP
    echo        Verifique se o servico esta rodando
)

echo.
echo [8/10] Testando deteccao de dispositivos...
echo [INFO] Testando: %BRIDGE_URL%/api/detect-biometric-devices
curl -s --connect-timeout 10 "%BRIDGE_URL%/api/detect-biometric-devices" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Endpoint de deteccao respondendo
    echo [INFO] Dispositivos detectados:
    curl -s "%BRIDGE_URL%/api/detect-biometric-devices" 2>nul | findstr /i "dispositivos_encontrados\|nome\|status" 2>nul
) else (
    echo [ERRO] Endpoint de deteccao nao responde
    echo        Verifique se o ZK4500 esta conectado
)

echo.
echo [9/10] Verificando hardware ZK4500...
echo [INFO] Executando deteccao via WMIC...
wmic path Win32_PnPEntity where "DeviceID like '%%VID_1B55%%' OR Name like '%%ZK%%'" get Name,DeviceID,Status /format:csv 2>nul | findstr /v "Node" | findstr /v "^$" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Hardware ZK4500 detectado:
    wmic path Win32_PnPEntity where "DeviceID like '%%VID_1B55%%' OR Name like '%%ZK%%'" get Name,DeviceID,Status /format:csv 2>nul | findstr /v "Node" | findstr /v "^$"
) else (
    echo [AVISO] Hardware ZK4500 nao detectado via WMIC
    echo         Verifique se o dispositivo esta conectado via USB
)

echo.
echo [10/10] Verificando logs do sistema...
echo [INFO] Verificando eventos relacionados ao bridge...
for /f "tokens=*" %%i in ('wevtutil qe Application /c:5 /rd:true /f:text /q:"*[System[Provider[@Name='Service Control Manager']]]" 2^>nul ^| findstr /i "RLPonto"') do (
    echo [LOG] %%i
)

echo.
echo ========================================================
echo   DIAGNOSTICO CONCLUIDO
echo ========================================================
echo.

:: Gerar relatório de resumo
echo [RESUMO DO DIAGNOSTICO]
echo ========================================================

%PYTHON_EXE% --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Python instalado
) else (
    echo [ERRO] Python NAO instalado
)

if exist "%BRIDGE_DIR%" (
    echo [OK] Diretorio de instalacao existe
) else (
    echo [ERRO] Diretorio de instalacao NAO existe
)

sc query "%BRIDGE_NAME%" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Servico registrado
) else (
    echo [ERRO] Servico NAO registrado
)

netstat -an | findstr :8080 >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Porta 8080 em uso
) else (
    echo [ERRO] Porta 8080 NAO em uso
)

curl -s --connect-timeout 3 "%BRIDGE_URL%/api/bridge-status" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Bridge respondendo
) else (
    echo [ERRO] Bridge NAO respondendo
)

wmic path Win32_PnPEntity where "DeviceID like '%%VID_1B55%%'" get DeviceID /format:csv 2>nul | findstr "VID_1B55" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] ZK4500 detectado
) else (
    echo [ERRO] ZK4500 NAO detectado
)

echo.
echo ========================================================
echo [SOLUCOES RECOMENDADAS]
echo ========================================================
echo.
echo Se encontrou problemas:
echo   1. ERRO Python: Instale Python 3.8+ do python.org
echo   2. ERRO Diretorio: Execute instalador.bat como Administrador
echo   3. ERRO Servico: Execute instalador.bat como Administrador
echo   4. ERRO Porta 8080: Execute "sc start %BRIDGE_NAME%"
echo   5. ERRO Bridge: Verifique dependencias e reinicie servico
echo   6. ERRO ZK4500: Verifique conexao USB e drivers
echo.
echo Para logs detalhados: Visualizador de Eventos ^> Logs do Windows ^> Sistema
echo Para reinstalar: Execute desinstalar.bat e depois instalador.bat
echo.

:: Finalizar logging se disponível
if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :END_SECTION "ANALISE_BRIDGE_ZK4500" "SUCCESS"

echo Pressione qualquer tecla para finalizar...
pause >nul