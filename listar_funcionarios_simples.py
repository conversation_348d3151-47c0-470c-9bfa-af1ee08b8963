#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simples para listar todos os funcionários cadastrados no sistema RLPONTO-WEB
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def listar_funcionarios():
    """Listar todos os funcionários cadastrados no sistema"""
    try:
        print("=" * 100)
        print("👥 LISTA COMPLETA DE FUNCIONÁRIOS - RLPONTO-WEB")
        print("=" * 100)
        
        # Importar dependências
        from utils.database import DatabaseManager
        
        # Conectar ao banco
        db = DatabaseManager()
        print("✅ Conexão com banco de dados estabelecida\n")
        
        # 1. ESTATÍSTICAS BÁSICAS
        print("📊 ESTATÍSTICAS BÁSICAS:")
        print("-" * 50)
        
        # Total de funcionários
        total_funcionarios = db.execute_query("SELECT COUNT(*) as total FROM funcionarios")[0]['total']
        print(f"📈 Total de funcionários cadastrados: {total_funcionarios}")
        
        # Funcionários ativos
        ativos = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE ativo = 1")[0]['total']
        print(f"✅ Funcionários ativos: {ativos}")
        
        # Funcionários inativos
        inativos = total_funcionarios - ativos
        print(f"❌ Funcionários inativos: {inativos}")
        
        # Funcionários com foto
        com_foto = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE foto_3x4 IS NOT NULL AND foto_3x4 != ''")[0]['total']
        print(f"📸 Funcionários com foto: {com_foto}")
        
        # Funcionários com biometria
        com_biometria = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE digital_dedo1 IS NOT NULL")[0]['total']
        print(f"👆 Funcionários com biometria: {com_biometria}")
        
        print("\n" + "=" * 100)
        print("👥 LISTA DETALHADA DE TODOS OS FUNCIONÁRIOS:")
        print("=" * 100)
        
        # Query para buscar todos os funcionários
        funcionarios_query = """
        SELECT 
            f.id,
            f.nome_completo,
            f.cpf,
            f.matricula_empresa,
            f.cargo,
            f.setor,
            f.setor_obra,
            f.ativo,
            f.status_cadastro,
            f.data_admissao,
            f.telefone1,
            f.email,
            f.empresa_id,
            COALESCE(e.nome_fantasia, 'Sem Empresa') as empresa_nome,
            CASE WHEN f.foto_3x4 IS NOT NULL AND f.foto_3x4 != '' THEN 'SIM' ELSE 'NÃO' END as tem_foto,
            CASE WHEN f.digital_dedo1 IS NOT NULL THEN 'SIM' ELSE 'NÃO' END as tem_biometria,
            COALESCE(ht.nome_horario, 'Sem horário') as horario_trabalho,
            f.salario_base,
            f.valor_hora,
            f.data_cadastro
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
        ORDER BY f.ativo DESC, f.nome_completo ASC
        """
        
        funcionarios = db.execute_query(funcionarios_query)
        
        if not funcionarios:
            print("❌ Nenhum funcionário encontrado no sistema!")
            return False
        
        for i, func in enumerate(funcionarios, 1):
            status = "✅ ATIVO" if func['ativo'] else "❌ INATIVO"
            setor_final = func['setor_obra'] if func['setor_obra'] else (func['setor'] if func['setor'] else 'Não informado')
            
            print(f"\n{i:3d}. {func['nome_completo']} ({status})")
            print(f"     ┌─ 📋 Dados Básicos:")
            print(f"     │  ID: {func['id']} | CPF: {func['cpf']} | Matrícula: {func['matricula_empresa'] or 'N/A'}")
            print(f"     │  Status: {func['status_cadastro']} | Cadastro: {func['data_cadastro']}")
            print(f"     ├─ 🏢 Empresa:")
            print(f"     │  {func['empresa_nome']} (ID: {func['empresa_id'] or 'N/A'})")
            print(f"     ├─ 💼 Trabalho:")
            print(f"     │  Cargo: {func['cargo'] or 'N/A'} | Setor: {setor_final}")
            print(f"     │  Admissão: {func['data_admissao'] or 'N/A'}")
            print(f"     │  Horário: {func['horario_trabalho']}")
            print(f"     ├─ 💰 Remuneração:")
            print(f"     │  Salário Base: R$ {func['salario_base'] or 0:.2f}")
            print(f"     │  Valor/Hora: R$ {func['valor_hora'] or 0:.2f}")
            print(f"     ├─ 📞 Contato:")
            print(f"     │  Telefone: {func['telefone1'] or 'N/A'}")
            print(f"     │  Email: {func['email'] or 'N/A'}")
            print(f"     └─ 🔐 Sistema:")
            print(f"        Foto: {func['tem_foto']} | Biometria: {func['tem_biometria']}")
        
        # 2. RESUMO POR EMPRESA
        print("\n" + "=" * 100)
        print("🏢 RESUMO POR EMPRESA:")
        print("=" * 100)
        
        empresas_query = """
        SELECT 
            COALESCE(e.nome_fantasia, 'Sem Empresa') as empresa,
            COALESCE(e.razao_social, 'N/A') as razao_social,
            COUNT(f.id) as total_funcionarios,
            SUM(CASE WHEN f.ativo = 1 THEN 1 ELSE 0 END) as ativos
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        GROUP BY e.id, e.nome_fantasia, e.razao_social
        ORDER BY total_funcionarios DESC
        """
        
        empresas = db.execute_query(empresas_query)
        for emp in empresas:
            print(f"\n🏢 {emp['empresa']}")
            print(f"   Razão Social: {emp['razao_social']}")
            print(f"   Total: {emp['total_funcionarios']} funcionários | Ativos: {emp['ativos']}")
        
        # 3. RESUMO POR CARGO
        print("\n" + "=" * 100)
        print("💼 RESUMO POR CARGO:")
        print("=" * 100)
        
        cargos_query = """
        SELECT 
            COALESCE(cargo, 'Não informado') as cargo,
            COUNT(*) as total,
            SUM(CASE WHEN ativo = 1 THEN 1 ELSE 0 END) as ativos
        FROM funcionarios
        GROUP BY cargo
        ORDER BY total DESC
        """
        
        cargos = db.execute_query(cargos_query)
        for cargo in cargos:
            print(f"💼 {cargo['cargo']}: {cargo['total']} total ({cargo['ativos']} ativos)")
        
        # 4. PROBLEMAS IDENTIFICADOS
        print("\n" + "=" * 100)
        print("⚠️ PROBLEMAS IDENTIFICADOS:")
        print("=" * 100)
        
        problemas = []
        
        # Funcionários sem empresa
        sem_empresa = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE empresa_id IS NULL")[0]['total']
        if sem_empresa > 0:
            problemas.append(f"❌ {sem_empresa} funcionários sem empresa definida")
        
        # Funcionários sem cargo
        sem_cargo = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE cargo IS NULL OR cargo = ''")[0]['total']
        if sem_cargo > 0:
            problemas.append(f"❌ {sem_cargo} funcionários sem cargo definido")
        
        # Funcionários sem horário
        sem_horario = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE horario_trabalho_id IS NULL")[0]['total']
        if sem_horario > 0:
            problemas.append(f"❌ {sem_horario} funcionários sem horário de trabalho")
        
        # Funcionários sem foto
        sem_foto = total_funcionarios - com_foto
        if sem_foto > 0:
            problemas.append(f"⚠️ {sem_foto} funcionários sem foto cadastrada")

        # Funcionários sem biometria
        sem_biometria = total_funcionarios - com_biometria
        if sem_biometria > 0:
            problemas.append(f"⚠️ {sem_biometria} funcionários sem biometria cadastrada")
        
        if problemas:
            for problema in problemas:
                print(problema)
        else:
            print("✅ Nenhum problema crítico identificado!")
        
        print("\n" + "=" * 100)
        print("✅ ANÁLISE CONCLUÍDA COM SUCESSO!")
        print(f"📊 Total analisado: {total_funcionarios} funcionários")
        print("=" * 100)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a análise: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    print("Iniciando listagem de funcionários...")
    listar_funcionarios()
