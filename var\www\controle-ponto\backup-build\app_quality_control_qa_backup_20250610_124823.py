#!/usr/bin/env python3
"""
MÓDULO DE CONTROLE DE QUALIDADE - RLPONTO-WEB v1.0

Este módulo implementa a estratégia anti-regressão como padrão integral do sistema.
Fornece monitoramento contínuo, acompanhamento profissional e prevenção de falhas.

Autor: <PERSON> - AiNexus Tecnologia
Data: 2025-01-09
© Copyright: 2025 AiNexus Tecnologia. Todos os direitos reservados.
"""

from flask import Blueprint, render_template, request, session, redirect, url_for, jsonify, flash
from werkzeug.security import check_password_hash
from datetime import datetime, timedelta
import subprocess
import sys
import os
import shutil
from pathlib import Path
import json
from functools import wraps

# Configuração do Blueprint
quality_control_bp = Blueprint('quality_control', __name__)

# Credenciais para usuário QA (conforme solicitado)
QA_USER = "cavalcrod"
QA_PASSWORD = "@Ric6109"

def qa_login_required(f):
    """Decorator para proteger rotas do Quality Control"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'qa_user' not in session or session['qa_user'] != QA_USER:
            return redirect(url_for('quality_control.qa_login'))
        return f(*args, **kwargs)
    return decorated_function

@quality_control_bp.route('/qa/login', methods=['GET', 'POST'])
def qa_login():
    """Página de login específica para Quality Control"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        print(f"[DEBUG QA] Tentativa de login - Usuário: {username}")
        print(f"[DEBUG QA] Senha recebida: {password}")
        print(f"[DEBUG QA] Usuário esperado: {QA_USER}")
        print(f"[DEBUG QA] Senha esperada: {QA_PASSWORD}")
        print(f"[DEBUG QA] Usuário correto: {username == QA_USER}")
        print(f"[DEBUG QA] Senha correta: {password == QA_PASSWORD}")
        
        if username == QA_USER and password == QA_PASSWORD:
            session['qa_user'] = username
            session['qa_login_time'] = datetime.now().isoformat()
            flash('Login realizado com sucesso!', 'success')
            return redirect(url_for('quality_control.dashboard'))
        else:
            flash('Usuário ou senha incorretos', 'error')
    
    return render_template('quality_control/login.html')

@quality_control_bp.route('/qa/logout')
def qa_logout():
    """Logout do Quality Control"""
    session.pop('qa_user', None)
    session.pop('qa_login_time', None)
    flash('Logout realizado com sucesso!', 'info')
    return redirect(url_for('quality_control.qa_login'))

@quality_control_bp.route('/qa/dashboard')
@qa_login_required
def dashboard():
    """Dashboard principal do Quality Control"""
    # Executar verificação automática
    system_status = run_system_check()
    
    # Obter histórico de verificações
    check_history = get_check_history()
    
    # Estatísticas do sistema
    stats = calculate_system_stats()
    
    return render_template('quality_control/dashboard.html', 
                         system_status=system_status,
                         check_history=check_history,
                         stats=stats,
                         last_update=datetime.now())

@quality_control_bp.route('/qa/api/run-check', methods=['POST'])
@qa_login_required
def api_run_check():
    """API para executar verificação manual"""
    try:
        result = run_system_check()
        save_check_to_history(result)
        return jsonify({
            'status': 'success',
            'data': result,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@quality_control_bp.route('/qa/api/backup', methods=['POST'])
@qa_login_required
def api_create_backup():
    """API para criar backup manual"""
    try:
        backup_files = create_system_backup()
        return jsonify({
            'status': 'success',
            'backup_files': backup_files,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@quality_control_bp.route('/qa/api/stats')
@qa_login_required
def api_get_stats():
    """API para obter estatísticas em tempo real"""
    try:
        stats = calculate_system_stats()
        return jsonify({
            'status': 'success',
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

def run_system_check():
    """Executa verificação completa do sistema"""
    results = {
        'timestamp': datetime.now().isoformat(),
        'overall_status': 'checking',
        'checks': [],
        'issues_found': 0,
        'backup_created': False
    }
    
    try:
        # 1. Criar backup automático
        backup_files = create_system_backup()
        results['backup_created'] = True
        results['backup_files'] = backup_files
        
        # 2. Testar carregamento de módulos
        module_check = test_module_loading()
        results['checks'].append(module_check)
        if not module_check['success']:
            results['issues_found'] += len(module_check['failures'])
        
        # 3. Testar blueprints
        blueprint_check = test_blueprint_registration()
        results['checks'].append(blueprint_check)
        if not blueprint_check['success']:
            results['issues_found'] += len(blueprint_check['missing'])
        
        # 4. Testar banco de dados
        db_check = test_database_connectivity()
        results['checks'].append(db_check)
        if not db_check['success']:
            results['issues_found'] += 1
        
        # 5. Testar importações cruzadas
        import_check = test_cross_module_imports()
        results['checks'].append(import_check)
        if not import_check['success']:
            results['issues_found'] += len(import_check['failures'])
        
        # Determinar status geral
        results['overall_status'] = 'success' if results['issues_found'] == 0 else 'warning'
        
    except Exception as e:
        results['overall_status'] = 'error'
        results['error'] = str(e)
        results['issues_found'] += 1
    
    return results

def test_module_loading():
    """Testa carregamento de todos os módulos críticos"""
    modules_to_test = [
        ("app", "Aplicação principal"),
        ("app_funcionarios", "Módulo de funcionários"),
        ("app_configuracoes", "Módulo de configurações"),
        ("app_relatorios", "Módulo de relatórios"),
        ("app_status", "Módulo de status"),
        ("utils.database", "Utilitários de banco")
    ]
    
    failures = []
    successes = []
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            successes.append({
                'module': module_name,
                'description': description,
                'status': 'success'
            })
        except Exception as e:
            failures.append({
                'module': module_name,
                'description': description,
                'error': str(e),
                'status': 'failed'
            })
    
    return {
        'name': 'Carregamento de Módulos',
        'type': 'module_loading',
        'success': len(failures) == 0,
        'successes': successes,
        'failures': failures,
        'summary': f"{len(successes)}/{len(modules_to_test)} módulos carregaram com sucesso"
    }

def test_blueprint_registration():
    """Testa registro de blueprints"""
    try:
        import app
        blueprint_names = [bp.name for bp in app.app.blueprints.values()]
        required_blueprints = [
            'funcionarios', 'configuracoes', 'relatorios', 
            'status', 'quality_control', 'epis', 'registro_ponto'
        ]
        
        registered = []
        missing = []
        
        for bp_name in required_blueprints:
            if bp_name in blueprint_names:
                registered.append(bp_name)
            else:
                missing.append(bp_name)
        
        return {
            'name': 'Registro de Blueprints',
            'type': 'blueprint_registration',
            'success': len(missing) == 0,
            'registered': registered,
            'missing': missing,
            'all_blueprints': blueprint_names,
            'summary': f"{len(registered)}/{len(required_blueprints)} blueprints registrados"
        }
        
    except Exception as e:
        return {
            'name': 'Registro de Blueprints',
            'type': 'blueprint_registration',
            'success': False,
            'error': str(e),
            'summary': 'Erro ao verificar blueprints'
        }

def test_database_connectivity():
    """Testa conectividade com banco de dados"""
    try:
        from utils.database import get_db_connection
        
        conn = get_db_connection()
        if conn and hasattr(conn, 'open') and conn.open:
            conn.close()
            return {
                'name': 'Conectividade do Banco',
                'type': 'database_connectivity',
                'success': True,
                'summary': 'Conexão com banco estabelecida com sucesso'
            }
        else:
            return {
                'name': 'Conectividade do Banco',
                'type': 'database_connectivity',
                'success': False,
                'error': 'Falha na conexão',
                'summary': 'Não foi possível estabelecer conexão'
            }
            
    except Exception as e:
        return {
            'name': 'Conectividade do Banco',
            'type': 'database_connectivity',
            'success': False,
            'error': str(e),
            'summary': 'Erro ao testar conexão'
        }

def test_cross_module_imports():
    """Testa importações entre módulos"""
    test_cases = [
        ("app_funcionarios", "utils.database"),
        ("app_configuracoes", "utils.database"),
        ("app_relatorios", "utils.database"),
        ("app", "app_funcionarios"),
        ("app", "app_configuracoes")
    ]
    
    successes = []
    failures = []
    
    for module1, module2 in test_cases:
        try:
            # Limpar imports anteriores
            if module1 in sys.modules:
                del sys.modules[module1]
            if module2 in sys.modules:
                del sys.modules[module2]
            
            # Testar import sequencial
            __import__(module1)
            __import__(module2)
            
            successes.append({
                'modules': f"{module1} → {module2}",
                'status': 'success'
            })
            
        except Exception as e:
            failures.append({
                'modules': f"{module1} → {module2}",
                'error': str(e),
                'status': 'failed'
            })
    
    return {
        'name': 'Importações Cross-Module',
        'type': 'cross_module_imports',
        'success': len(failures) == 0,
        'successes': successes,
        'failures': failures,
        'summary': f"{len(successes)}/{len(test_cases)} importações funcionando"
    }

def create_system_backup():
    """Cria backup dos arquivos críticos do sistema"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = Path("backup-build")
    backup_dir.mkdir(exist_ok=True)
    
    # Arquivos críticos para backup
    critical_files = [
        "app.py",
        "app_funcionarios.py", 
        "app_configuracoes.py",
        "app_relatorios.py",
        "app_status.py",
        "app_quality_control.py",
        "utils/database.py"
    ]
    
    backed_up = []
    
    for file_path in critical_files:
        if Path(file_path).exists():
            backup_name = f"{Path(file_path).stem}_qa_backup_{timestamp}{Path(file_path).suffix}"
            backup_path = backup_dir / backup_name
            shutil.copy2(file_path, backup_path)
            backed_up.append({
                'original': file_path,
                'backup': backup_name,
                'size': backup_path.stat().st_size
            })
    
    # Salvar log do backup
    backup_log = {
        'timestamp': timestamp,
        'files': backed_up,
        'created_by': 'quality_control_system',
        'type': 'automatic_qa_backup'
    }
    
    log_file = backup_dir / f"qa_backup_log_{timestamp}.json"
    with open(log_file, 'w', encoding='utf-8') as f:
        json.dump(backup_log, f, indent=2, ensure_ascii=False)
    
    return backed_up

def get_check_history():
    """Obtém histórico das últimas verificações"""
    history_file = Path("logs/qa_check_history.json")
    
    if history_file.exists():
        try:
            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
                # Retornar últimas 10 verificações
                return sorted(history, key=lambda x: x['timestamp'], reverse=True)[:10]
        except:
            return []
    
    return []

def save_check_to_history(check_result):
    """Salva resultado da verificação no histórico"""
    history_file = Path("logs/qa_check_history.json")
    history_file.parent.mkdir(exist_ok=True)
    
    # Carregar histórico existente
    history = []
    if history_file.exists():
        try:
            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
        except:
            history = []
    
    # Adicionar nova verificação
    history.append(check_result)
    
    # Manter apenas últimas 50 verificações
    history = history[-50:]
    
    # Salvar histórico atualizado
    with open(history_file, 'w', encoding='utf-8') as f:
        json.dump(history, f, indent=2, ensure_ascii=False)

def calculate_system_stats():
    """Calcula estatísticas do sistema"""
    history = get_check_history()
    
    stats = {
        'total_checks': len(history),
        'success_rate': 0,
        'avg_issues': 0,
        'last_check': None,
        'uptime_percentage': 0,
        'trend': 'stable'
    }
    
    if history:
        # Taxa de sucesso
        successful_checks = len([h for h in history if h.get('overall_status') == 'success'])
        stats['success_rate'] = (successful_checks / len(history)) * 100
        
        # Média de problemas
        total_issues = sum([h.get('issues_found', 0) for h in history])
        stats['avg_issues'] = total_issues / len(history)
        
        # Última verificação
        stats['last_check'] = history[0]['timestamp'] if history else None
        
        # Uptime (baseado em verificações bem-sucedidas)
        recent_checks = history[:10]  # Últimas 10 verificações
        recent_success = len([h for h in recent_checks if h.get('overall_status') == 'success'])
        stats['uptime_percentage'] = (recent_success / len(recent_checks)) * 100 if recent_checks else 0
        
        # Tendência (comparar últimas 5 com 5 anteriores)
        if len(history) >= 10:
            recent_avg = sum([h.get('issues_found', 0) for h in history[:5]]) / 5
            previous_avg = sum([h.get('issues_found', 0) for h in history[5:10]]) / 5
            
            if recent_avg < previous_avg:
                stats['trend'] = 'improving'
            elif recent_avg > previous_avg:
                stats['trend'] = 'degrading'
            else:
                stats['trend'] = 'stable'
    
    return stats

# Inicialização automática
def init_quality_control():
    """Inicializa o sistema de controle de qualidade"""
    # Criar diretórios necessários
    Path("logs").mkdir(exist_ok=True)
    Path("backup-build").mkdir(exist_ok=True)
    
    # Executar verificação inicial
    try:
        initial_check = run_system_check()
        save_check_to_history(initial_check)
        print("✅ Sistema de Quality Control inicializado com sucesso!")
        return True
    except Exception as e:
        print(f"❌ Erro ao inicializar Quality Control: {e}")
        return False

# Executar inicialização quando o módulo for importado
if __name__ != "__main__":
    init_quality_control()
