#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste Final da Página de Alocações
==================================

Script para testar se a correção da consulta SQL foi aplicada.

Data: 07/07/2025
"""

import requests
import time

# Configurações
BASE_URL = "http://************"
LOGIN_URL = f"{BASE_URL}/login"
ALOCACOES_URL = f"{BASE_URL}/empresa-principal/alocacoes"

def testar_alocacoes_final():
    """Teste final da página de alocações"""
    print("🧪 TESTE FINAL DA PÁGINA DE ALOCAÇÕES")
    print("=" * 60)
    
    # Aguardar um pouco para o serviço reiniciar
    print("⏳ Aguardando serviço reiniciar...")
    time.sleep(3)
    
    # Criar sessão e fazer login
    session = requests.Session()
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    
    try:
        response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
        
        if response.status_code not in [200, 302]:
            print(f"❌ Falha no login: {response.status_code}")
            return False
        
        print("✅ Login realizado com sucesso")
        
        # Testar página de alocações
        print(f"\n🎯 Testando página de alocações...")
        response = session.get(ALOCACOES_URL)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text.lower()
            
            # Verificar se ainda há erro
            if "erro ao carregar" in content:
                print(f"   ❌ AINDA HÁ ERRO: 'erro ao carregar alocações'")
                print(f"   🔧 Consulta SQL ainda precisa de correção")
                return False
            else:
                print(f"   ✅ PÁGINA CARREGOU SEM ERROS!")
                
                # Verificar elementos esperados
                elementos_esperados = [
                    "alocação de funcionários",
                    "nova alocação",
                    "total de alocações",
                    "funcionários",
                    "clientes"
                ]
                
                elementos_encontrados = []
                for elemento in elementos_esperados:
                    if elemento in content:
                        elementos_encontrados.append(elemento)
                
                print(f"   📋 Elementos encontrados: {len(elementos_encontrados)}/{len(elementos_esperados)}")
                
                if len(elementos_encontrados) >= 3:
                    print(f"   🎉 PÁGINA DE ALOCAÇÕES FUNCIONANDO CORRETAMENTE!")
                    return True
                else:
                    print(f"   ⚠️ Página carregou mas pode estar incompleta")
                    return False
        
        elif response.status_code == 500:
            print(f"   ❌ Erro interno do servidor (500)")
            print(f"   🔧 Ainda há problema na consulta SQL")
            return False
        else:
            print(f"   ❌ Erro inesperado: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        return False

if __name__ == "__main__":
    sucesso = testar_alocacoes_final()
    
    if sucesso:
        print(f"\n🎉 BOTÃO VERDE DE ALOCAÇÕES FUNCIONANDO!")
        print("✅ Correção aplicada com sucesso")
        print("✅ Página carrega sem erros")
        print("✅ Elementos esperados presentes")
        print(f"\n🔗 URL: {ALOCACOES_URL}")
    else:
        print(f"\n❌ BOTÃO AINDA COM PROBLEMAS")
        print("🔧 Pode precisar de correção adicional na consulta SQL")
        print("💡 Verificar logs do servidor para detalhes")
