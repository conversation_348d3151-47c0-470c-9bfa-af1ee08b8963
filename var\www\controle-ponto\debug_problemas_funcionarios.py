#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 SCRIPT DE DIAGNÓSTICO - PROBLEMAS FUNCIONÁRIOS
Reproduz e diagnostica os problemas reportados:
1. Jornada sendo sobrescrita durante edição
2. EPIs não sendo salvos no cadastro/edição

Autor: Sistema RLPONTO-WEB
Data: Julho 2025
"""

import sys
import os
sys.path.append('.')

from utils.database import DatabaseManager, FuncionarioQueries
from app_funcionarios import _extrair_dados_epis, _processar_epis_funcionario, get_jornada_padrao_empresa
import logging

# Configurar logging para debug
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def testar_estrutura_banco():
    """Testa se a estrutura do banco está correta"""
    print("🔍 TESTE 1: ESTRUTURA DO BANCO")
    print("=" * 50)
    
    try:
        # Verificar tabela funcionarios
        funcionarios = DatabaseManager.execute_query("DESCRIBE funcionarios")
        print("✅ Tabela funcionarios existe")
        
        # Verificar se tem campos de jornada
        campos_jornada = [f for f in funcionarios if 'jornada' in f['Field'].lower() or 'horario' in f['Field'].lower()]
        print(f"📋 Campos relacionados à jornada: {len(campos_jornada)}")
        for campo in campos_jornada:
            print(f"   - {campo['Field']}: {campo['Type']}")
        
        # Verificar tabela EPIs
        try:
            epis = DatabaseManager.execute_query("DESCRIBE epis")
            print("✅ Tabela epis existe")
            print(f"📋 Campos da tabela EPIs: {len(epis)}")
            for campo in epis:
                print(f"   - {campo['Field']}: {campo['Type']}")
        except Exception as e:
            print(f"❌ Erro na tabela EPIs: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar estrutura: {e}")
        return False

def testar_funcionario_existente():
    """Testa um funcionário existente para verificar jornada e EPIs"""
    print("\n🔍 TESTE 2: FUNCIONÁRIO EXISTENTE")
    print("=" * 50)
    
    try:
        # Buscar primeiro funcionário
        funcionarios = DatabaseManager.execute_query("SELECT * FROM funcionarios LIMIT 1")
        if not funcionarios:
            print("❌ Nenhum funcionário encontrado no banco")
            return False
            
        funcionario = funcionarios[0]
        funcionario_id = funcionario['id']
        
        print(f"👤 Funcionário: {funcionario.get('nome_completo', 'N/A')} (ID: {funcionario_id})")
        print(f"🏢 Empresa ID: {funcionario.get('empresa_id', 'N/A')}")
        print(f"⏰ Horário Trabalho ID: {funcionario.get('horario_trabalho_id', 'N/A')}")
        
        # Verificar jornada da empresa
        empresa_id = funcionario.get('empresa_id')
        if empresa_id:
            jornada_empresa = get_jornada_padrao_empresa(empresa_id)
            if jornada_empresa:
                print(f"✅ Jornada da empresa encontrada: {jornada_empresa.get('nome_horario', 'N/A')}")
            else:
                print("⚠️ Nenhuma jornada padrão encontrada para a empresa")
        
        # Verificar EPIs usando get_with_epis
        funcionario_completo = FuncionarioQueries.get_with_epis(funcionario_id)
        if funcionario_completo and 'epis' in funcionario_completo:
            epis = funcionario_completo['epis']
            print(f"🦺 EPIs encontrados: {len(epis)}")
            for i, epi in enumerate(epis):
                print(f"   EPI {i+1}: {epi.get('epi_nome', 'N/A')} (CA: {epi.get('epi_ca', 'N/A')})")
        else:
            print("⚠️ Nenhum EPI encontrado ou erro ao buscar")
            
        return funcionario_id
        
    except Exception as e:
        print(f"❌ Erro ao testar funcionário: {e}")
        return False

def simular_edicao_funcionario(funcionario_id):
    """Simula edição de funcionário para testar problemas"""
    print(f"\n🔍 TESTE 3: SIMULAÇÃO DE EDIÇÃO - FUNCIONÁRIO {funcionario_id}")
    print("=" * 50)
    
    try:
        # Buscar dados atuais
        funcionario_atual = FuncionarioQueries.get_by_id(funcionario_id)
        if not funcionario_atual:
            print("❌ Funcionário não encontrado")
            return False
            
        print(f"📋 Dados atuais:")
        print(f"   - Horário Trabalho ID: {funcionario_atual.get('horario_trabalho_id', 'N/A')}")
        print(f"   - Empresa ID: {funcionario_atual.get('empresa_id', 'N/A')}")
        
        # Simular dados de formulário com EPIs
        form_data_simulado = {
            'nome_completo': funcionario_atual.get('nome_completo', ''),
            'empresa_id': funcionario_atual.get('empresa_id', ''),
            'horario_trabalho_id': funcionario_atual.get('horario_trabalho_id', ''),
            # Simular EPIs no formato do formulário
            'epis[0][epi_nome]': 'Capacete de Segurança TESTE',
            'epis[0][epi_ca]': '12345',
            'epis[0][epi_data_entrega]': '2025-07-04',
            'epis[0][epi_data_validade]': '2026-07-04',
            'epis[0][epi_observacoes]': 'EPI de teste para diagnóstico',
            'epis[1][epi_nome]': 'Luvas de Proteção TESTE',
            'epis[1][epi_ca]': '67890',
            'epis[1][epi_data_entrega]': '2025-07-04',
        }
        
        print(f"\n📝 Simulando formulário com {len([k for k in form_data_simulado.keys() if k.startswith('epis')])} campos de EPI")
        
        # Testar extração de EPIs
        epis_extraidos = _extrair_dados_epis(form_data_simulado)
        print(f"🦺 EPIs extraídos: {len(epis_extraidos)}")
        for i, epi in enumerate(epis_extraidos):
            print(f"   EPI {i+1}: {epi}")
            
        return True
        
    except Exception as e:
        print(f"❌ Erro na simulação: {e}")
        return False

def testar_correcoes_implementadas():
    """Testa se as correções implementadas estão funcionando"""
    print("\n🔍 TESTE 4: VERIFICAÇÃO DAS CORREÇÕES")
    print("=" * 50)

    try:
        # Testar extração de EPIs
        form_data_teste = {
            'epis[0][epi_nome]': 'Capacete TESTE',
            'epis[0][epi_ca]': '12345',
            'epis[0][id]': '999',  # ID fictício para teste
            'epis[1][epi_nome]': 'Luvas TESTE',
            'epis[1][epi_ca]': '67890',
        }

        print("📝 Testando extração de EPIs do formulário...")
        epis_extraidos = _extrair_dados_epis(form_data_teste)
        print(f"✅ EPIs extraídos: {len(epis_extraidos)}")

        for i, epi in enumerate(epis_extraidos):
            print(f"   EPI {i+1}: {epi}")

        # Verificar se IDs foram preservados
        epi_com_id = [epi for epi in epis_extraidos if epi.get('id')]
        print(f"📋 EPIs com ID preservado: {len(epi_com_id)}")

        return True

    except Exception as e:
        print(f"❌ Erro ao testar correções: {e}")
        return False

def testar_richardson_especifico():
    """Teste específico para o funcionário Richardson"""
    print("\n🎯 TESTE ESPECÍFICO: RICHARDSON CARDOSO RODRIGUES")
    print("=" * 60)

    try:
        # Buscar Richardson especificamente
        richardson = DatabaseManager.execute_query(
            "SELECT * FROM funcionarios WHERE nome_completo LIKE '%RICHARDSON%' LIMIT 1"
        )

        if not richardson:
            print("❌ Richardson não encontrado")
            return False

        funcionario = richardson[0]
        funcionario_id = funcionario['id']

        print(f"👤 Funcionário: {funcionario['nome_completo']} (ID: {funcionario_id})")
        print(f"🏢 Empresa ID: {funcionario['empresa_id']}")
        print(f"⏰ Horário Trabalho ID ATUAL: {funcionario['horario_trabalho_id']}")
        print(f"📋 Jornada Seg-Qui Entrada: {funcionario.get('jornada_seg_qui_entrada', 'N/A')}")
        print(f"📋 Jornada Seg-Qui Saída: {funcionario.get('jornada_seg_qui_saida', 'N/A')}")
        print(f"📋 Jornada Sex Entrada: {funcionario.get('jornada_sex_entrada', 'N/A')}")
        print(f"📋 Jornada Sex Saída: {funcionario.get('jornada_sex_saida', 'N/A')}")

        # Verificar jornada da empresa AiNexus
        jornada_empresa = get_jornada_padrao_empresa(funcionario['empresa_id'])
        if jornada_empresa:
            print(f"\n🏢 JORNADA DA EMPRESA AINEXUS:")
            print(f"   Nome: {jornada_empresa.get('nome_horario', 'N/A')}")
            print(f"   ID: {jornada_empresa.get('horario_trabalho_id', 'N/A')}")
            print(f"   Entrada Seg-Qui: {jornada_empresa.get('entrada_manha', 'N/A')}")
            print(f"   Saída Seg-Qui: {jornada_empresa.get('saida', 'N/A')}")

        print(f"\n🔍 ANÁLISE:")
        if funcionario['horario_trabalho_id'] == jornada_empresa.get('horario_trabalho_id'):
            print("⚠️ Richardson está usando a jornada padrão da empresa")
            print("   Isso pode indicar que a jornada foi sobrescrita!")
        else:
            print("✅ Richardson tem jornada específica diferente da empresa")
            print("   Isso indica que a jornada personalizada foi preservada")

        return funcionario_id

    except Exception as e:
        print(f"❌ Erro ao testar Richardson: {e}")
        return False

def main():
    """Função principal de diagnóstico"""
    print("🚨 DIAGNÓSTICO DE PROBLEMAS - FUNCIONÁRIOS")
    print("=" * 60)
    print("Problemas reportados:")
    print("1. Jornada sendo sobrescrita durante edição")
    print("2. EPIs não sendo salvos no cadastro/edição")
    print("=" * 60)
    print("🔧 CORREÇÕES IMPLEMENTADAS:")
    print("✅ Lógica de preservação de jornada melhorada")
    print("✅ Preservação de campos individuais de jornada")
    print("✅ Logs de debug adicionados para EPIs")
    print("✅ Tratamento de erros melhorado")
    print("=" * 60)

    # Teste 1: Estrutura do banco
    if not testar_estrutura_banco():
        print("❌ Falha na estrutura do banco - abortando testes")
        return

    # Teste 2: Funcionário existente
    funcionario_id = testar_funcionario_existente()
    if not funcionario_id:
        print("❌ Falha ao encontrar funcionário - abortando testes")
        return

    # Teste 3: Simulação de edição
    if not simular_edicao_funcionario(funcionario_id):
        print("❌ Falha na simulação de edição")
        return

    # Teste 4: Verificar correções
    if not testar_correcoes_implementadas():
        print("❌ Falha ao testar correções")
        return

    # Teste 5: Richardson específico
    if not testar_richardson_especifico():
        print("❌ Falha ao testar Richardson")
        return

    print("\n✅ DIAGNÓSTICO CONCLUÍDO")
    print("=" * 60)
    print("📋 RESUMO:")
    print("• Estrutura do banco verificada")
    print("• Funcionário de teste identificado")
    print("• Simulação de edição executada")
    print("• Correções testadas")
    print("\n🔍 PRÓXIMOS PASSOS:")
    print("1. Testar edição real de funcionário no sistema")
    print("2. Verificar logs em tempo real durante edição")
    print("3. Confirmar que jornada não é sobrescrita")
    print("4. Confirmar que EPIs são salvos corretamente")

if __name__ == "__main__":
    main()
