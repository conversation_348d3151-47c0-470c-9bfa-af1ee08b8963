/**
 * Script de funcionalidades do formulário de cadastro de funcionários
 * Formatação, validação e auto-preenchimento de campos
 * 
 * Autor: Sistema RLPONTO-WEB
 * Data: Junho 2025
 */

// 🛡️ VARIÁVEIS GLOBAIS DE SEGURANÇA BIOMÉTRICA
let biometriaExistente = false;
let modoEdicaoBiometriaAtivo = false;
let usuarioAdmin = false;

// 🔐 FUNÇÕES DE PROTEÇÃO BIOMÉTRICA
function confirmarEdicaoBiometria() {
    const confirmacao = confirm(
        "⚠️ ATENÇÃO: EDIÇÃO DE BIOMETRIA EXISTENTE\n\n" +
        "Você está prestes a SUBSTITUIR a biometria atual do funcionário.\n" +
        "Esta ação é IRREVERSÍVEL e pode comprometer o controle de acesso.\n\n" +
        "🔐 Dados atuais serão PERDIDOS permanentemente!\n" +
        "🚨 Funcionário não conseguirá bater ponto até nova captura!\n\n" +
        "Tem CERTEZA que deseja continuar?"
    );
    
    if (confirmacao) {
        const segundaConfirmacao = confirm(
            "🚨 ÚLTIMA CONFIRMAÇÃO\n\n" +
            "Você confirmou que deseja SUBSTITUIR a biometria existente.\n" +
            "Esta é sua última chance de cancelar!\n\n" +
            "Clique OK para CONTINUAR ou Cancelar para ABORTAR."
        );
        
        if (segundaConfirmacao) {
            console.log('🔐 ADMIN autorizou edição de biometria existente');
            modoEdicaoBiometriaAtivo = true;
            abrirModalBiometria();
        }
    }
}

function mostrarAlertaPermissao() {
    alert(
        "🚫 ACESSO NEGADO\n\n" +
        "Somente ADMINISTRADORES podem editar biometria existente.\n\n" +
        "Esta é uma medida de SEGURANÇA para proteger dados críticos:\n" +
        "• Biometria é usada para controle de acesso\n" +
        "• Perda destes dados compromete o sistema\n" +
        "• Alterações requerem privilégios elevados\n\n" +
        "Contate um administrador se necessário."
    );
}

// 🔍 VERIFICAÇÃO DE BIOMETRIA EXISTENTE
function verificarBiometriaExistente() {
    const dedo1 = document.getElementById('digital_dedo1')?.value || '';
    const dedo2 = document.getElementById('digital_dedo2')?.value || '';
    
    biometriaExistente = (dedo1.trim() !== '' || dedo2.trim() !== '');
    
    console.log('🔐 Verificação biometria:', {
        dedo1_presente: dedo1.trim() !== '',
        dedo2_presente: dedo2.trim() !== '',
        biometriaExistente: biometriaExistente
    });
    
    return biometriaExistente;
}

// 🛡️ INTERCEPTADOR MELHORADO - SEM OVERRIDE AUTOMÁTICO
let abrirModalBiometriaOriginal = null;

// Função para interceptar apenas quando necessário
function interceptarModalBiometria() {
    // Só intercepta se ainda não foi feito
    if (abrirModalBiometriaOriginal === null) {
        abrirModalBiometriaOriginal = window.abrirModalBiometria;
        
        window.abrirModalBiometria = function() {
            console.log('🔐 abrirModalBiometria interceptada por segurança');
            
            // Verifica se há biometria existente
            verificarBiometriaExistente();
            
            // Se há biometria existente e não foi autorizada edição, bloqueia
            if (biometriaExistente && !modoEdicaoBiometriaAtivo) {
                console.log('🚫 Tentativa de edição de biometria existente bloqueada');
                mostrarAlertaPermissao();
                return false;
            }
            
            // Se chegou aqui, pode abrir o modal
            console.log('✅ Abertura do modal autorizada');
            
            // Chama função original se existir
            if (typeof abrirModalBiometriaOriginal === 'function') {
                return abrirModalBiometriaOriginal();
            } else {
                // Fallback mais seguro
                console.log('⚠️ Função original ainda não carregada, aguardando...');
                
                // Aguarda 100ms e tenta novamente
                setTimeout(() => {
                    if (window.abrirModalBiometria !== arguments.callee) {
                        window.abrirModalBiometria();
                    }
                }, 100);
                
                return false;
            }
        };
    }
}

// 🔐 VALIDAÇÃO CRÍTICA ANTES DO ENVIO
function validarBiometriaAntesEnvio() {
    const form = document.querySelector('form');
    if (!form) return true;
    
    const dedo1Original = document.getElementById('digital_dedo1')?.dataset.original || '';
    const dedo2Original = document.getElementById('digital_dedo2')?.dataset.original || '';
    const dedo1Atual = document.getElementById('digital_dedo1')?.value || '';
    const dedo2Atual = document.getElementById('digital_dedo2')?.value || '';
    
    // Se havia biometria e agora está vazia, alertar
    if ((dedo1Original !== '' || dedo2Original !== '') && (dedo1Atual === '' && dedo2Atual === '')) {
        const confirmacao = confirm(
            "⚠️ PERDA DE DADOS BIOMÉTRICOS DETECTADA!\n\n" +
            "Os dados de biometria serão REMOVIDOS do funcionário.\n" +
            "Isso significa que ele NÃO PODERÁ bater ponto!\n\n" +
            "Deseja realmente continuar SEM biometria?"
        );
        
        if (!confirmacao) {
            return false;
        }
    }
    
    return true;
}

// Função para atualizar nacionalidade baseada no sexo
function atualizarNacionalidade() {
    const sexo = document.getElementById('sexo').value;
    const nacionalidadeInput = document.getElementById('nacionalidade');
    if (!nacionalidadeInput.value || nacionalidadeInput.value === 'Brasileira' || nacionalidadeInput.value === 'Brasileiro') {
        let novoValor = '';
        switch(sexo) {
            case 'M': novoValor = 'Brasileiro'; break;
            case 'F': novoValor = 'Brasileira'; break;
            case 'Outro': novoValor = 'Brasileira'; break;
            default: novoValor = '';
        }
        if (novoValor) {
            nacionalidadeInput.value = novoValor;
            nacionalidadeInput.classList.add('auto-filled');
            setTimeout(() => { nacionalidadeInput.classList.remove('auto-filled'); }, 2000);
        }
    }
}

// Centraliza caixa alta para todos os campos de texto exceto e-mail
function aplicarCaixaAltaTodosCampos() {
    const campos = document.querySelectorAll('input[type="text"], input[type="tel"]');
    campos.forEach(function(input) {
        if (input.name !== 'email') {
            input.addEventListener('input', function(e) {
                e.target.value = e.target.value.toUpperCase();
            });
            input.addEventListener('paste', function(e) {
                setTimeout(() => { e.target.value = e.target.value.toUpperCase(); }, 0);
            });
        }
    });
}

// Formatação de telefones
function formatTelefone(input) {
    input.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length <= 10) {
            value = value.replace(/(\d{2})(\d)/, '($1) $2');
            value = value.replace(/(\d{4})(\d)/, '$1-$2');
        } else {
            value = value.replace(/(\d{2})(\d)/, '($1) $2');
            value = value.replace(/(\d{5})(\d)/, '$1-$2');
        }
        e.target.value = value.toUpperCase();
    });
    input.addEventListener('paste', function(e) {
        setTimeout(() => { input.value = input.value.toUpperCase(); }, 0);
    });
}

// 💰 FUNÇÕES DE CÁLCULO DE PAGAMENTO
function calcularValorHora() {
    const salarioBase = parseFloat(document.getElementById('salario_base').value) || 0;
    const valorHoraInput = document.getElementById('valor_hora');
    
    if (salarioBase > 0) {
        // Cálculo padrão CLT: 220 horas mensais (44h/semana × 5 semanas)
        const valorHora = salarioBase / 220;
        valorHoraInput.value = valorHora.toFixed(2);
        
        // Auto-calcular hora extra também
        calcularValorHoraExtra();
        
        // Feedback visual
        valorHoraInput.style.background = '#e8f5e8';
        setTimeout(() => { valorHoraInput.style.background = '#f8f9fa'; }, 1000);
        
        console.log(`💰 Valor hora calculado: R$ ${valorHora.toFixed(2)} (salário: R$ ${salarioBase})`);
    } else {
        valorHoraInput.value = '';
    }
}

function calcularValorHoraExtra() {
    const valorHora = parseFloat(document.getElementById('valor_hora').value) || 0;
    const percentualExtra = parseFloat(document.getElementById('percentual_hora_extra').value) || 50;
    const valorHoraExtraInput = document.getElementById('valor_hora_extra');
    
    if (valorHora > 0 && percentualExtra >= 0) {
        // Cálculo: valor hora + percentual adicional
        const valorHoraExtra = valorHora * (1 + percentualExtra / 100);
        valorHoraExtraInput.value = valorHoraExtra.toFixed(2);
        
        // Feedback visual
        valorHoraExtraInput.style.background = '#e8f5e8';
        setTimeout(() => { valorHoraExtraInput.style.background = '#f8f9fa'; }, 1000);
        
        console.log(`💰 Valor hora extra calculado: R$ ${valorHoraExtra.toFixed(2)} (+${percentualExtra}%)`);
    } else {
        valorHoraExtraInput.value = '';
    }
}

function formatarMoeda(input) {
    input.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value) {
            value = (parseFloat(value) / 100).toFixed(2);
            e.target.value = value;
        }
    });
}

function configurarCalculosPagamento() {
    // Configurar eventos para cálculo automático
    const salarioInput = document.getElementById('salario_base');
    const percentualExtraInput = document.getElementById('percentual_hora_extra');
    
    if (salarioInput) {
        salarioInput.addEventListener('input', calcularValorHora);
        salarioInput.addEventListener('change', calcularValorHora);
    }
    
    if (percentualExtraInput) {
        percentualExtraInput.addEventListener('input', calcularValorHoraExtra);
        percentualExtraInput.addEventListener('change', calcularValorHoraExtra);
    }
    
    // Calcular valores iniciais se já houver dados
    if (salarioInput && salarioInput.value) {
        calcularValorHora();
    }
}

// Configuração de formatação e validação de campos
function configurarFormatacaoCampos() {
    // Formatação de CPF
    const cpfInput = document.getElementById('cpf');
    if (cpfInput) {
        cpfInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
            e.target.value = value.toUpperCase();
        });
    }

    // Formatação de RG
    const rgInput = document.getElementById('rg');
    if (rgInput) {
        rgInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\w]/g, '');
            if (value.length <= 9 && /^\d+$/.test(value)) {
                value = value.replace(/(\d{2})(\d)/, '$1.$2');
                value = value.replace(/(\d{3})(\d)/, '$1.$2');
                value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
            }
            e.target.value = value.toUpperCase();
        });
    }

    // Formatação de CTPS
    const ctpsInput = document.getElementById('ctps_numero');
    if (ctpsInput) {
        ctpsInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 7) value = value.substring(0, 7);
            e.target.value = value.toUpperCase();
        });
    }

    // Formatação de CTPS Série/UF
    const ctpsSerieInput = document.getElementById('ctps_serie_uf');
    if (ctpsSerieInput) {
        ctpsSerieInput.addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            value = value.replace(/[^0-9A-Z\-\/]/g, '');
            e.target.value = value.toUpperCase();
        });
    }

    // Formatação de PIS/PASEP
    const pisInput = document.getElementById('pis_pasep');
    if (pisInput) {
        pisInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{5})(\d)/, '$1.$2');
            value = value.replace(/(\d{2})(\d{1,2})$/, '$1-$2');
            e.target.value = value.toUpperCase();
        });
    }

    // Formatação de CEP
    const cepInput = document.getElementById('endereco_cep');
    if (cepInput) {
        cepInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = value.replace(/(\d{5})(\d)/, '$1-$2');
            e.target.value = value.toUpperCase();
        });
    }

    // Formatação de telefones
    const telefone1 = document.getElementById('telefone1');
    const telefone2 = document.getElementById('telefone2');
    if (telefone1) formatTelefone(telefone1);
    if (telefone2) formatTelefone(telefone2);
}

// Busca automática de CEP
function configurarBuscaCEP() {
    const cepInput = document.getElementById('endereco_cep');
    if (cepInput) {
        cepInput.addEventListener('blur', function(e) {
            const cep = e.target.value.replace(/\D/g, '');
            if (cep.length === 8) {
                e.target.style.borderColor = '#ffc107';
                e.target.style.backgroundColor = '#fff8e1';
                fetch(`https://viacep.com.br/ws/${cep}/json/`)
                    .then(response => response.json())
                    .then(data => {
                        if (!data.erro) {
                            const campos = [
                                {elemento: 'endereco_rua', valor: data.logradouro},
                                {elemento: 'endereco_bairro', valor: data.bairro},
                                {elemento: 'endereco_cidade', valor: data.localidade},
                                {elemento: 'endereco_estado', valor: data.uf}
                            ];
                            campos.forEach(campo => {
                                if (campo.valor) {
                                    const input = document.getElementById(campo.elemento);
                                    if (input) {
                                        input.value = campo.valor.toUpperCase();
                                        input.classList.add('auto-filled');
                                        setTimeout(() => { input.classList.remove('auto-filled'); }, 3000);
                                    }
                                }
                            });
                            e.target.style.borderColor = '#28a745';
                            e.target.style.backgroundColor = '#e8f5e8';
                            setTimeout(() => {
                                e.target.style.borderColor = '#ced4da';
                                e.target.style.backgroundColor = '#f8f9fa';
                            }, 2000);
                        } else {
                            e.target.style.borderColor = '#dc3545';
                            e.target.style.backgroundColor = '#ffeaea';
                            setTimeout(() => {
                                e.target.style.borderColor = '#ced4da';
                                e.target.style.backgroundColor = '#f8f9fa';
                            }, 2000);
                        }
                    })
                    .catch(error => {
                        console.error('Erro ao buscar CEP:', error);
                        e.target.style.borderColor = '#ffc107';
                        e.target.style.backgroundColor = '#fff8e1';
                        setTimeout(() => {
                            e.target.style.borderColor = '#ced4da';
                            e.target.style.backgroundColor = '#f8f9fa';
                        }, 2000);
                    });
            }
        });
    }
}

// Auto-preenchimento de campos com valores padrão
function configurarValoresPadrao() {
    // Auto-preenchimento de matrícula
    const matriculaInput = document.getElementById('matricula_empresa');
    if (matriculaInput && !matriculaInput.value) {
        const proximaMatricula = document.querySelector('[data-proxima-matricula]')?.dataset.proximaMatricula;
        if (proximaMatricula) {
            matriculaInput.value = proximaMatricula;
            matriculaInput.classList.add('auto-filled');
            setTimeout(() => { matriculaInput.classList.remove('auto-filled'); }, 2000);
        }
    }
}

// Preview da foto
function configurarPreviewFoto() {
    const fotoInput = document.getElementById('foto_3x4');
    const preview = document.getElementById('preview_foto');
    
    if (fotoInput && preview) {
        fotoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.border = '2px solid #28a745';
                    setTimeout(() => {
                        preview.style.border = '1px solid #dee2e6';
                    }, 2000);
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

// 🛡️ INICIALIZAÇÃO SEGURA DO SISTEMA BIOMÉTRICO
function inicializarProtecoesBiometria() {
    console.log('🔐 Inicializando proteções biométricas...');
    
    // Detectar se é modo edição e verificar biometria existente
    const modoEdicao = document.querySelector('input[name="funcionario_id"]') !== null || 
                      document.querySelector('[data-funcionario-id]') !== null ||
                      window.location.pathname.includes('/editar');
    
    if (modoEdicao) {
        console.log('🔐 Modo edição detectado');
        verificarBiometriaExistente();
        
        // Armazenar valores originais para comparação
        const dedo1Input = document.getElementById('digital_dedo1');
        const dedo2Input = document.getElementById('digital_dedo2');
        if (dedo1Input) dedo1Input.dataset.original = dedo1Input.value;
        if (dedo2Input) dedo2Input.dataset.original = dedo2Input.value;
        
        // ✅ SÓ INTERCEPTA EM MODO EDIÇÃO COM BIOMETRIA EXISTENTE
        if (biometriaExistente) {
            console.log('🔐 Interceptando modal para proteção de biometria existente');
            
            // Aguarda o script principal carregar antes de interceptar
            setTimeout(() => {
                interceptarModalBiometria();
            }, 500);
        }
    }
    
    // Detectar se usuário é admin
    usuarioAdmin = document.querySelector('.admin-controls') !== null ||
                   document.querySelector('[data-is-admin="true"]') !== null;
    
    console.log('🔐 Proteções configuradas:', {
        modoEdicao: modoEdicao,
        biometriaExistente: biometriaExistente,
        usuarioAdmin: usuarioAdmin,
        interceptacaoAtiva: modoEdicao && biometriaExistente
    });
}

// 🔐 INTERCEPTAR SUBMISSÃO DO FORMULÁRIO
function configurarValidacaoFormulario() {
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('🔐 Validando formulário antes do envio...');
            
            if (!validarBiometriaAntesEnvio()) {
                e.preventDefault();
                console.log('🚫 Envio do formulário bloqueado por validação biométrica');
                return false;
            }
            
            console.log('✅ Validação biométrica aprovada');
        });
    }
}

// 🚀 INICIALIZAÇÃO PRINCIPAL
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔐 Sistema de proteção biométrica carregado');
    
    // Inicializar todas as funcionalidades
    aplicarCaixaAltaTodosCampos();
    configurarFormatacaoCampos();
    configurarBuscaCEP();
    configurarValoresPadrao();
    configurarPreviewFoto();
    configurarCalculosPagamento();
    
    // 🛡️ PROTEÇÕES BIOMÉTRICAS CRÍTICAS
    inicializarProtecoesBiometria();
    configurarValidacaoFormulario();
    
    // Auto-update nacionalidade quando sexo mudar
    const sexoSelect = document.getElementById('sexo');
    if (sexoSelect) {
        sexoSelect.addEventListener('change', atualizarNacionalidade);
    }
    
    console.log('✅ Sistema inicializado com proteções biométricas ativas');
}); 