-- ========================================
-- RLPONTO-WEB v2.0 - ATUALIZAÇÃO COMPLETA
-- Data: 11/07/2025
-- Descrição: Implementa B5/B6, aprovações RH, histórico, porcentagem
-- ========================================

USE controle_ponto;

-- ========================================
-- 1. ATUALIZAR TABELA registros_ponto
-- ========================================

-- Adicionar novos tipos B5 e B6
ALTER TABLE registros_ponto 
MODIFY COLUMN tipo_registro ENUM(
    'entrada_manha', 
    'saida_almoco', 
    'entrada_tarde', 
    'saida',
    'inicio_extra',  -- B5
    'fim_extra'      -- B6
) NOT NULL DEFAULT 'entrada_manha';

-- Adicionar campos para aprovação
ALTER TABLE registros_ponto 
ADD COLUMN requer_aprovacao BOOLEAN DEFAULT FALSE,
ADD COLUMN aprovacao_id INT NULL;

-- ========================================
-- 2. CRIAR TABELA aprovacoes_horas_extras
-- ========================================

CREATE TABLE IF NOT EXISTS aprovacoes_horas_extras (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT UNSIGNED NOT NULL,
    data_referencia DATE NOT NULL,
    inicio_extra DATETIME NOT NULL,
    fim_extra DATETIME NOT NULL,
    duracao_minutos INT NOT NULL,
    motivo_hora_extra TEXT,
    porcentagem_extra DECIMAL(5,2) DEFAULT 50.00,
    status_aprovacao ENUM('PENDENTE', 'APROVADO', 'REJEITADO') DEFAULT 'PENDENTE',
    aprovado_por INT NULL,
    data_aprovacao DATETIME NULL,
    observacoes_aprovacao TEXT NULL,
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    atualizado_em DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
    FOREIGN KEY (aprovado_por) REFERENCES usuarios(id),
    INDEX idx_funcionario_data (funcionario_id, data_referencia),
    INDEX idx_status (status_aprovacao)
);

-- ========================================
-- 3. CRIAR TABELA historico_funcionario
-- ========================================

CREATE TABLE IF NOT EXISTS historico_funcionario (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT UNSIGNED NOT NULL,
    tipo_evento ENUM(
        'HORA_EXTRA_SOLICITADA',
        'HORA_EXTRA_APROVADA', 
        'HORA_EXTRA_REJEITADA',
        'BANCO_HORAS_CREDITADO',
        'BANCO_HORAS_DEBITADO',
        'AUSENCIA_REGISTRADA',
        'ATRASO_REGISTRADO'
    ) NOT NULL,
    data_evento DATETIME NOT NULL,
    data_referencia DATE NOT NULL,
    detalhes TEXT NOT NULL,
    valor_minutos INT DEFAULT 0,
    status_aprovacao ENUM('PENDENTE', 'APROVADO', 'REJEITADO', 'NAO_APLICAVEL') DEFAULT 'NAO_APLICAVEL',
    aprovado_por INT NULL,
    observacoes TEXT NULL,
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
    FOREIGN KEY (aprovado_por) REFERENCES usuarios(id),
    INDEX idx_funcionario_data (funcionario_id, data_referencia),
    INDEX idx_tipo_evento (tipo_evento)
);

-- ========================================
-- 4. CRIAR TABELA configuracoes_hora_extra
-- ========================================

CREATE TABLE IF NOT EXISTS configuracoes_hora_extra (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT UNSIGNED NOT NULL,
    data_referencia DATE NOT NULL,
    tipo_dia ENUM('SABADO', 'DOMINGO', 'FERIADO', 'NORMAL') NOT NULL,
    porcentagem_extra DECIMAL(5,2) NOT NULL DEFAULT 50.00,
    aplicado_por INT NOT NULL,
    observacoes TEXT,
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
    FOREIGN KEY (aplicado_por) REFERENCES usuarios(id),
    UNIQUE KEY unique_funcionario_data (funcionario_id, data_referencia)
);

-- ========================================
-- 5. CRIAR TABELA feriados
-- ========================================

CREATE TABLE IF NOT EXISTS feriados (
    id INT AUTO_INCREMENT PRIMARY KEY,
    data_feriado DATE NOT NULL,
    nome_feriado VARCHAR(100) NOT NULL,
    tipo ENUM('NACIONAL', 'ESTADUAL', 'MUNICIPAL') DEFAULT 'NACIONAL',
    ativo BOOLEAN DEFAULT TRUE,
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_data (data_feriado)
);

-- ========================================
-- 6. CRIAR TABELA notificacoes_rh
-- ========================================

CREATE TABLE IF NOT EXISTS notificacoes_rh (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo_notificacao ENUM('HORA_EXTRA_PENDENTE', 'BANCO_HORAS_NEGATIVO', 'AUSENCIA_INJUSTIFICADA') NOT NULL,
    funcionario_id INT UNSIGNED NOT NULL,
    aprovacao_id INT NULL,
    titulo VARCHAR(200) NOT NULL,
    mensagem TEXT NOT NULL,
    lida BOOLEAN DEFAULT FALSE,
    data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
    data_leitura DATETIME NULL,
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
    FOREIGN KEY (aprovacao_id) REFERENCES aprovacoes_horas_extras(id),
    INDEX idx_nao_lidas (lida, data_criacao)
);

-- ========================================
-- 7. ADICIONAR FOREIGN KEYS
-- ========================================

-- Adicionar FK para aprovacao_id em registros_ponto
ALTER TABLE registros_ponto 
ADD FOREIGN KEY (aprovacao_id) REFERENCES aprovacoes_horas_extras(id);

-- ========================================
-- 8. INSERIR FERIADOS NACIONAIS 2025
-- ========================================

INSERT IGNORE INTO feriados (data_feriado, nome_feriado, tipo) VALUES
('2025-01-01', 'Confraternização Universal', 'NACIONAL'),
('2025-04-18', 'Sexta-feira Santa', 'NACIONAL'),
('2025-04-21', 'Tiradentes', 'NACIONAL'),
('2025-05-01', 'Dia do Trabalhador', 'NACIONAL'),
('2025-09-07', 'Independência do Brasil', 'NACIONAL'),
('2025-10-12', 'Nossa Senhora Aparecida', 'NACIONAL'),
('2025-11-02', 'Finados', 'NACIONAL'),
('2025-11-15', 'Proclamação da República', 'NACIONAL'),
('2025-12-25', 'Natal', 'NACIONAL');

-- ========================================
-- 9. ATUALIZAR HELPERS E VALIDAÇÕES
-- ========================================

-- Criar view para relatórios
CREATE OR REPLACE VIEW vw_relatorio_diario AS
SELECT 
    f.id as funcionario_id,
    f.nome_completo,
    DATE(rp.data_hora) as data_referencia,
    GROUP_CONCAT(
        CONCAT(TIME(rp.data_hora), ':', rp.tipo_registro) 
        ORDER BY rp.data_hora SEPARATOR ' | '
    ) as batidas_dia,
    COUNT(CASE WHEN rp.tipo_registro IN ('inicio_extra', 'fim_extra') THEN 1 END) as tem_hora_extra,
    COUNT(CASE WHEN rp.requer_aprovacao = TRUE THEN 1 END) as requer_aprovacao
FROM funcionarios f
LEFT JOIN registros_ponto rp ON f.id = rp.funcionario_id
WHERE f.status_cadastro = 'Ativo'
GROUP BY f.id, DATE(rp.data_hora);

-- ========================================
-- 10. VERIFICAÇÕES FINAIS
-- ========================================

SELECT 'Verificando estrutura atualizada...' as status;

-- Verificar se as tabelas foram criadas
SELECT 
    TABLE_NAME,
    TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME IN (
    'aprovacoes_horas_extras',
    'historico_funcionario', 
    'configuracoes_hora_extra',
    'feriados',
    'notificacoes_rh'
);

-- Verificar novos tipos de registro
SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'registros_ponto' 
AND COLUMN_NAME = 'tipo_registro';

SELECT 'Atualização v2.0 concluída com sucesso!' as status;
