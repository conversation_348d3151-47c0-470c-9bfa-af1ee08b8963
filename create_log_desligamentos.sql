CREATE TABLE IF NOT EXISTS log_desligamentos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id_original INT UNSIGNED NOT NULL,
    nome_funcionario VARCHAR(100) NOT NULL,
    matricula VARCHAR(10) NOT NULL,
    motivo_desligamento ENUM('Demissao_sem_justa_causa','Demissao_com_justa_causa','Pedido_demissao','Termino_contrato','Aposentadoria','Falecimento','Abandono_emprego','Acordo_mutuo','Outros') NOT NULL,
    data_desligamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usuario_responsavel INT UNSIGNED,
    observacoes TEXT,
    INDEX idx_funcionario_original (funcionario_id_original),
    INDEX idx_data_desligamento (data_desligamento)
);
