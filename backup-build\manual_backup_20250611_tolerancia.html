{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    .manual-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .pesquisa-section {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .pesquisa-input {
        position: relative;
        max-width: 100%;
    }
    
    .pesquisa-input input {
        padding-left: 45px;
        border-radius: 25px;
        border: 2px solid #e9ecef;
        font-size: 1.1rem;
        height: 50px;
        transition: all 0.3s ease;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }
    
    .pesquisa-input input:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40,167,69,0.25);
        outline: none;
    }
    
    .pesquisa-input i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 5;
    }
    
    .funcionarios-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    
    .funcionario-card {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        position: relative;
    }
    
    .funcionario-card:hover {
        border-color: #28a745;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40,167,69,0.2);
    }
    
    .funcionario-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .funcionario-foto {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        object-fit: cover;
        margin-right: 15px;
        border: 3px solid #e9ecef;
        background: linear-gradient(45deg, #6c757d, #adb5bd);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        flex-shrink: 0;
    }
    
    .funcionario-foto img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 5px;
    }
    
    .funcionario-info h5 {
        margin: 0 0 5px 0;
        color: #495057;
        font-size: 1.1rem;
    }
    
    .funcionario-info .subtitle {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 2px 0;
    }
    
    .funcionario-detalhes {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        font-size: 0.85rem;
        margin-bottom: 15px;
    }
    
    .detalhe-item {
        display: flex;
        align-items: center;
        color: #6c757d;
    }
    
    .detalhe-item i {
        width: 16px;
        margin-right: 8px;
        color: #28a745;
    }

    .funcionario-actions {
        display: flex;
        justify-content: center;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e9ecef;
    }

    .btn-registrar-funcionario {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        border-radius: 25px;
        padding: 12px 25px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .btn-registrar-funcionario:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(40,167,69,0.3);
        color: white;
    }
    
    .no-results {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    
    .no-results i {
        font-size: 3rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }
    
    /* ✅ MODAL FORÇADO A FICAR OCULTO INICIALMENTE */
    #registroModal {
        display: none !important;
    }
    
    #registroModal.show {
        display: block !important;
    }
    
    /* Modal */
    .modal-dialog {
        max-width: 600px;
        margin: 30px auto;
    }
    
    .modal-content {
        border-radius: 15px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .modal-header {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border-radius: 15px 15px 0 0;
        border-bottom: none;
    }
    
    .modal-body {
        padding: 30px;
        max-height: 60vh;
        overflow-y: auto;
    }
    
    .funcionario-modal-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
    }
    
    .funcionario-modal-foto {
        width: 80px;
        height: 80px;
        border-radius: 10px;
        object-fit: cover;
        margin-right: 20px;
        border: 3px solid #28a745;
        background: linear-gradient(45deg, #6c757d, #adb5bd);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        flex-shrink: 0;
    }
    
    .funcionario-modal-foto img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 7px;
    }
    
    /* Tipo de Registro Redesenhado com visual moderno */
    .tipos-registro-modal {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin-top: 10px;
    }
    
    .tipo-radio {
        position: relative;
    }
    
    .tipo-radio input[type="radio"] {
        display: none;
    }
    
    .tipo-radio label {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 18px 15px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
        height: 100%;
        box-shadow: 0 2px 6px rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
    }
    
    .tipo-radio label::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #28a745, #20c997);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .tipo-radio label:hover {
        border-color: #28a745;
        background: #f9fdf8;
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(40,167,69,0.15);
    }

    .tipo-radio label:hover::before {
        opacity: 1;
    }
    
    .tipo-radio input[type="radio"]:checked + label {
        border-color: #28a745;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(40,167,69,0.25);
    }
    
    .tipo-radio input[type="radio"]:checked + label::before {
        opacity: 0;
    }
    
    .tipo-radio i {
        font-size: 1.8rem;
        margin-bottom: 8px;
        display: block;
    }

    .tipo-radio .tipo-hora {
        font-weight: 700;
        margin-top: 5px;
        font-size: 0.95rem;
    }

    .tipo-radio .tipo-texto {
        font-size: 0.85rem;
        opacity: 0.9;
    }
    
    /* Estilo para as informações de horário e tolerância */
    .horario-info {
        display: flex;
        flex-direction: column;
        gap: 3px;
        margin-top: 5px;
    }
    
    .horario-info .badge {
        font-size: 0.75rem;
        padding: 4px 8px;
        border-radius: 12px;
        display: inline-block;
    }

    /* Efeito pulsante para tipos disponíveis */
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(40,167,69,0.4); }
        70% { box-shadow: 0 0 0 8px rgba(40,167,69,0); }
        100% { box-shadow: 0 0 0 0 rgba(40,167,69,0); }
    }
    
    /* Tipos indisponíveis */
    .tipo-radio.indisponivel label {
        opacity: 0.7;
        cursor: not-allowed;
        border-color: #dee2e6;
        background: #f8f9fa;
        box-shadow: none;
    }
    
    .tipo-radio.indisponivel label:hover {
        transform: none;
        box-shadow: none;
        border-color: #dee2e6;
    }

    .tipo-radio.indisponivel label::before {
        opacity: 0;
    }
    
    .tipo-radio .status-indicador {
        position: absolute;
        top: -6px;
        right: -6px;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        color: white;
        z-index: 1;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .tipo-radio .status-disponivel {
        background: #28a745;
        animation: pulse 2s infinite;
    }
    
    .tipo-radio .status-indisponivel {
        background: #6c757d;
        animation: none;
    }

    .hora-atual-indicador {
        display: flex;
        align-items: center;
        gap: 10px;
        background: linear-gradient(to right, #f9fcf7, #e9f7ef);
        border-radius: 30px;
        padding: 10px 15px;
        margin-bottom: 15px;
        border: 1px solid #d1e7dd;
        box-shadow: 0 3px 8px rgba(0,0,0,0.05);
    }
    
    .hora-atual-indicador i {
        color: #28a745;
        font-size: 1.1rem;
    }

    .hora-atual-indicador strong {
        color: #198754;
        font-size: 1.1rem;
        font-weight: 700;
    }
    
    /* Melhorias no botão de confirmar */
    .btn-registrar {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(40,167,69,0.2);
        position: relative;
        overflow: hidden;
    }
    
    .btn-registrar::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: all 0.6s ease;
    }
    
    .btn-registrar:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(40,167,69,0.3);
        color: white;
    }
    
    .btn-registrar:hover::before {
        left: 100%;
    }

    /* Header Info no final */
    .footer-info {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-top: 30px;
        box-shadow: 0 4px 15px rgba(40,167,69,0.3);
    }
    
    .footer-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .info-item {
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 15px;
        text-align: center;
    }
    
    .info-item i {
        font-size: 1.2rem;
        margin-bottom: 5px;
        display: block;
    }
    
    /* Loading overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }
    
    .loading-content {
        background: white;
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        max-width: 300px;
    }
    
    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e9ecef;
        border-top: 4px solid #28a745;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .resultado-badge {
        display: inline-block;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
        margin-top: 15px;
    }
    
    .resultado-sucesso {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .resultado-erro {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    /* Ajuste para dispositivos móveis */
    @media (max-width: 768px) {
        .funcionario-detalhes {
            grid-template-columns: 1fr;
        }
        
        .funcionarios-grid {
            grid-template-columns: 1fr;
        }
        
        .footer-info-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .modal-dialog {
            margin: 10px;
            max-width: calc(100% - 20px);
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .tipos-registro-modal {
            grid-template-columns: 1fr;
        }
    }

    /* Destaque para setor do funcionário */
    .detalhe-item.setor-item {
        font-weight: 500;
        color: #495057;
    }
    
    .detalhe-item.setor-item i {
        color: #0d6efd;  /* Azul para destacar o setor */
    }
</style>
{% endblock %}

{% block content %}
<div class="manual-container">
    <h2><i class="fas fa-edit"></i> {{ titulo }}</h2>

    <!-- Pesquisa -->
    <div class="pesquisa-section">
        <h5><i class="fas fa-search"></i> Buscar Funcionário</h5>
        <div class="pesquisa-input">
            <i class="fas fa-search"></i>
            <input type="text" id="pesquisa-funcionario" class="form-control" 
                   placeholder="Digite nome, CPF, matrícula ou setor...">
        </div>
    </div>

    <!-- Grid de Funcionários -->
    <div class="funcionarios-grid" id="funcionarios-grid">
        {% if funcionarios %}
            {% for funcionario in funcionarios %}
            <div class="funcionario-card" 
                 data-funcionario-id="{{ funcionario.id }}"
                 data-search="{{ funcionario.nome_completo|lower }} {{ funcionario.cpf_exibicao }} {{ funcionario.matricula_empresa|lower }} {{ funcionario.setor|lower }} {{ funcionario.cargo|lower }}">
                
                <div class="funcionario-header">
                    <div class="funcionario-foto">
                        {% if funcionario.foto_url %}
                            <img src="{{ funcionario.foto_url }}" alt="Foto de {{ funcionario.nome_completo }}" 
                                 onload="this.style.display='block'" 
                                 onerror="this.style.display='none'; this.parentElement.innerHTML='<i class=\'fas fa-user\'></i>';">
                        {% else %}
                            <i class="fas fa-user"></i>
                        {% endif %}
                    </div>
                    <div class="funcionario-info">
                        <h5>{{ funcionario.nome_completo }}</h5>
                        <div class="subtitle">{{ funcionario.cargo }}</div>
                        <div class="subtitle">Matrícula: {{ funcionario.matricula_empresa or 'N/A' }}</div>
                    </div>
                </div>
                
                <div class="funcionario-detalhes">
                    <div class="detalhe-item">
                        <i class="fas fa-id-card"></i>
                        <span>{{ funcionario.cpf_exibicao }}</span>
                    </div>
                    <div class="detalhe-item setor-item">
                        <i class="fas fa-building"></i>
                        <span id="setor-{{ funcionario.id }}">{{ funcionario.setor }}</span>
                    </div>
                    <div class="detalhe-item">
                        <i class="fas fa-industry"></i>
                        <span>{{ funcionario.empresa }}</span>
                    </div>
                    <div class="detalhe-item">
                        <i class="fas fa-clock"></i>
                        <span>{{ funcionario.horario_trabalho }}</span>
                    </div>
                </div>

                <div class="funcionario-actions">
                    <button class="btn-registrar-funcionario" 
                            data-funcionario-id="{{ funcionario.id }}">
                        <i class="fas fa-plus"></i> Registrar Ponto
                    </button>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="no-results">
                    <i class="fas fa-users"></i>
                    <h5>Nenhum funcionário encontrado</h5>
                    <p>Não há funcionários cadastrados ou ativos no sistema</p>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Mensagem quando não há resultados na pesquisa -->
    <div class="no-results" id="no-results" style="display: none;">
        <i class="fas fa-search"></i>
        <h5>Nenhum funcionário encontrado</h5>
        <p>Tente ajustar os termos da pesquisa</p>
    </div>
    
    <!-- Informações do rodapé -->
    <div class="footer-info">
        <div class="footer-info-grid">
            <div class="info-item">
                <i class="fas fa-users"></i>
                <div>{{ total_funcionarios }}</div>
                <small>Funcionários</small>
            </div>
            <div class="info-item">
                <i class="fas fa-calendar"></i>
                <div>{{ data_atual }}</div>
                <small>Data Atual</small>
            </div>
            <div class="info-item">
                <i class="fas fa-clock"></i>
                <div id="hora-atual">{{ hora_atual }}</div>
                <small>Hora Atual</small>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Registro - FORÇADO A FICAR OCULTO -->
<div class="modal fade" id="registroModal" tabindex="-1" aria-hidden="true" style="display: none !important;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> Registrar Ponto Manual
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            
            <div class="modal-body">
                <!-- Dados do Funcionário -->
                <div class="funcionario-modal-header" id="funcionario-modal-info">
                    <!-- Será preenchido via JavaScript -->
                </div>
                
                <!-- Dados do funcionário (setor) -->
                <div class="alert alert-info mb-3" id="funcionario-setor-info" style="display:none;">
                    <i class="fas fa-building"></i> 
                    <strong>Setor:</strong> <span id="modal-setor"></span>
                </div>
                
                <!-- Formulário de Registro -->
                <form id="form-registro-manual">
                    <input type="hidden" id="funcionario-id" name="funcionario_id">
                    
                    <!-- Horários Configurados - Removido para evitar redundância -->
                    
                    <!-- Tipos de Registro -->
                    <div class="mb-3">
                        <div class="hora-atual-indicador">
                            <i class="fas fa-clock"></i>
                            <div>Horário atual: <strong id="hora-atual-modal">{{ hora_atual }}</strong></div>
                        </div>
                        
                        <label class="form-label"><strong>Tipo de Registro:</strong></label>
                        <div class="tipos-registro-modal" id="tipos-registro">
                            <!-- Será preenchido via JavaScript -->
                        </div>
                    </div>
                    
                    <!-- Observações -->
                    <div class="mb-3">
                        <label for="observacoes" class="form-label">
                            <i class="fas fa-comment"></i> Observações
                        </label>
                        <textarea class="form-control" id="observacoes" name="observacoes" 
                                  rows="3" placeholder="Observações sobre o registro (opcional)..."></textarea>
                    </div>
                    
                    <!-- Resultado -->
                    <div id="resultado-registro"></div>
                </form>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Cancelar
                </button>
                <button type="button" class="btn btn-registrar" id="btn-confirmar-registro">
                    <i class="fas fa-save"></i> Registrar Ponto
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loading-overlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <div>Processando registro...</div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // ✅ GARANTIR QUE MODAL ESTEJA OCULTO AO CARREGAR PÁGINA
    const modalElement = document.getElementById('registroModal');
    if (modalElement) {
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
        modalElement.setAttribute('aria-hidden', 'true');
    }
    
    // Elementos da interface
    const pesquisaInput = document.getElementById('pesquisa-funcionario');
    const funcionariosGrid = document.getElementById('funcionarios-grid');
    const noResults = document.getElementById('no-results');
    const registroModal = new bootstrap.Modal(modalElement);
    const formRegistro = document.getElementById('form-registro-manual');
    const btnConfirmar = document.getElementById('btn-confirmar-registro');
    const loadingOverlay = document.getElementById('loading-overlay');
    
    let funcionarioSelecionado = null;
    let todosTipos = []; // Lista completa de tipos de registro
    let registrosExistentes = []; // ✅ NOVA VARIÁVEL: Armazena os registros já feitos hoje
    
    // Atualizar hora atual
    setInterval(function() {
        const agora = new Date();
        const horaFormatada = agora.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
        });
        const horaElement = document.getElementById('hora-atual');
        if (horaElement) {
            horaElement.textContent = horaFormatada;
        }
    }, 1000);
    
    // Pesquisa de funcionários
    if (pesquisaInput) {
        pesquisaInput.addEventListener('input', function() {
            const termo = this.value.toLowerCase().trim();
            const cards = funcionariosGrid.querySelectorAll('.funcionario-card');
            let resultados = 0;
            
            cards.forEach(card => {
                const searchData = card.dataset.search;
                const matches = searchData.includes(termo);
                
                card.style.display = matches ? 'block' : 'none';
                if (matches) resultados++;
            });
            
            // Mostrar/ocultar mensagem de "não encontrado"
            if (noResults) {
                noResults.style.display = resultados === 0 ? 'block' : 'none';
                funcionariosGrid.style.display = resultados === 0 ? 'none' : 'grid';
            }
        });
    }
    
    // Event listener para botões de registrar
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-registrar-funcionario') || 
            e.target.closest('.btn-registrar-funcionario')) {
            const btn = e.target.classList.contains('btn-registrar-funcionario') ? 
                        e.target : e.target.closest('.btn-registrar-funcionario');
            const funcionarioId = btn.getAttribute('data-funcionario-id');
            if (funcionarioId) {
                abrirModalRegistro(funcionarioId);
            }
        }
    });
    
    // Função para abrir modal
    function abrirModalRegistro(funcionarioId) {
        funcionarioSelecionado = funcionarioId;
        
        // ✅ FORÇAR VISIBILIDADE DO MODAL ANTES DE ABRIR
        modalElement.style.display = 'block';
        
        // Buscar dados do funcionário e horários
        Promise.all([
            buscarDadosFuncionario(funcionarioId),
            buscarHorariosFuncionario(funcionarioId)
        ]).then(([funcionario, horarios]) => {
            preencherModalFuncionario(funcionario);
            // ✅ Armazenar os registros já feitos hoje
            registrosExistentes = horarios.registros_existentes || [];
            todosTipos = horarios.todos_tipos || []; // Armazenar todos os tipos
            preencherTiposRegistro(horarios.tipos_disponiveis || [], horarios.todos_tipos || [], horarios.hora_atual);
            registroModal.show();
            
            // ✅ CORREÇÃO: Habilitar botão de confirmação inicialmente
            if (btnConfirmar) {
                btnConfirmar.disabled = false;
                btnConfirmar.innerHTML = '<i class="fas fa-save"></i> Registrar Ponto';
            }
        }).catch(error => {
            console.error('Erro ao carregar dados:', error);
            alert('Erro ao carregar dados do funcionário: ' + error.message);
        });
    }
    
    // Confirmar registro
    if (btnConfirmar) {
        btnConfirmar.addEventListener('click', function() {
            confirmarRegistro();
        });
    }
    
    function buscarDadosFuncionario(funcionarioId) {
        const card = document.querySelector(`[data-funcionario-id="${funcionarioId}"]`);
        if (card) {
            const fotoElement = card.querySelector('.funcionario-foto img');
            const foto = fotoElement ? fotoElement.src : '';
            const nome = card.querySelector('.funcionario-info h5')?.textContent || 'Nome não informado';
            const cargo = card.querySelector('.funcionario-info .subtitle')?.textContent || 'Cargo não informado';
            
            // Obter o setor diretamente pelo ID específico
            const setorElement = document.getElementById(`setor-${funcionarioId}`);
            const setor = setorElement ? setorElement.textContent.trim() : '';
            
            // Obter outros detalhes
            const detalhes = Array.from(card.querySelectorAll('.detalhe-item:not(.setor-item) span')).map(s => s.textContent);
            
            return Promise.resolve({
                id: funcionarioId,
                nome_completo: nome,
                cargo: cargo,
                cpf_exibicao: detalhes[0] || '',  // CPF já mascarado do template
                setor: setor,
                empresa: detalhes[1] || '',
                foto_url: foto
            });
        }
        return Promise.reject(new Error('Funcionário não encontrado'));
    }
    
    function buscarHorariosFuncionario(funcionarioId) {
        return fetch(`/registro-ponto/api/obter-horarios/${funcionarioId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // ✅ MELHORIA: Armazenar dados completos incluindo ausências em variável global
                    window.horariosFuncionario = data;
                    return data;
                } else {
                    throw new Error(data.message || 'Erro ao obter horários');
                }
            })
            .catch(error => {
                console.warn('Erro ao buscar horários, usando padrão:', error);
                const horaAtual = new Date().toTimeString().substring(0, 5);
                
                // ✅ Em caso de erro, definir um objeto padrão
                const dadosPadrao = {
                    success: true,
                    hora_atual: horaAtual,
                    horarios: {
                        entrada_manha: '08:00',
                        saida_almoco: '12:00',
                        entrada_tarde: '13:00',
                        saida: '17:00',
                        tolerancia_minutos: 10,
                        nome_horario: 'Padrão'
                    },
                    todos_tipos: [
                        { value: 'entrada_manha', text: 'Entrada Manhã (08:00)' },
                        { value: 'saida_almoco', text: 'Saída Almoço (12:00)' },
                        { value: 'entrada_tarde', text: 'Entrada Tarde (13:00)' },
                        { value: 'saida', text: 'Saída (17:00)' }
                    ],
                    tipos_disponiveis: [], // Vazio por padrão em caso de erro
                    ausencias: [], // Array vazio para ausências em caso de erro
                    registros_existentes: [] // ✅ NOVA PROPRIEDADE: Array vazio para registros existentes
                };
                
                // ✅ Armazenar dados padrão na variável global
                window.horariosFuncionario = dadosPadrao;
                return dadosPadrao;
            });
    }
    
    function preencherModalFuncionario(funcionario) {
        const modalInfo = document.getElementById('funcionario-modal-info');
        if (modalInfo) {
            let fotoHtml = '';
            if (funcionario.foto_url && funcionario.foto_url !== '/static/images/funcionario_sem_foto.svg') {
                fotoHtml = `
                    <div class="funcionario-modal-foto">
                        <img src="${funcionario.foto_url}" alt="Foto" 
                             onload="this.style.display='block'" 
                             onerror="this.style.display='none'; this.parentElement.innerHTML='<i class=\'fas fa-user\'></i>';">
                    </div>`;
            } else {
                fotoHtml = `
                    <div class="funcionario-modal-foto">
                        <i class="fas fa-user"></i>
                    </div>`;
            }
            
            // Simplificando o cabeçalho para mostrar apenas o nome do funcionário
            modalInfo.innerHTML = `
                ${fotoHtml}
                <div>
                    <h5 class="mb-1">${funcionario.nome_completo}</h5>
                </div>
            `;
            
            // Exibir setor do funcionário
            const setorInfo = document.getElementById('funcionario-setor-info');
            const modalSetor = document.getElementById('modal-setor');
            if (setorInfo && modalSetor && funcionario.setor) {
                modalSetor.textContent = funcionario.setor;
                setorInfo.style.display = 'block';
            } else if (setorInfo) {
                setorInfo.style.display = 'none';
            }
        }
        
        const funcionarioIdInput = document.getElementById('funcionario-id');
        if (funcionarioIdInput) {
            funcionarioIdInput.value = funcionario.id;
        }
    }
    
    function preencherTiposRegistro(tiposDisponiveis, todosTipos, horaAtual) {
        const tiposContainer = document.getElementById('tipos-registro');
        let html = '';
        
        // ✅ NOVA SEÇÃO: Mostrar registros já feitos hoje
        if (registrosExistentes && registrosExistentes.length > 0) {
            html += `
            <div class="alert alert-info mt-3 mb-3" role="alert" style="grid-column: span 2;">
                <i class="fas fa-info-circle"></i> 
                <strong>Registros já realizados hoje:</strong>
            </div>`;
            
            registrosExistentes.forEach(registro => {
                const tipoFormatado = registro.tipo_registro.replace('_', ' ').replace(/\b\w/g, c => c.toUpperCase());
                const horaRegistro = registro.hora || '00:00';
                const statusClass = registro.status_pontualidade === 'Pontual' ? 'success' : 'warning';
                const statusIconClass = registro.status_pontualidade === 'Pontual' ? 'check-circle' : 'exclamation-circle';
                
                html += `
                <div class="alert alert-${statusClass} mb-2" role="alert" style="grid-column: span 2;">
                    <i class="fas fa-${statusIconClass}"></i> 
                    ${tipoFormatado} (${horaRegistro}) - <strong>${registro.status_pontualidade || 'Registrado'}</strong>
                </div>`;
                
                // ✅ CORREÇÃO: Desabilitar tipos que já foram registrados
                // Remover da lista de tipos disponíveis
                tiposDisponiveis = tiposDisponiveis.filter(t => t.value !== registro.tipo_registro);
            });
        }
        
        if (tiposDisponiveis.length === 0) {
            html += `
                <div class="alert alert-warning mb-3" role="alert" style="grid-column: span 2;">
                    <i class="fas fa-clock"></i> 
                    <strong>Nenhum tipo de registro está disponível no momento.</strong><br>
                </div>`;
                
            // ✅ MELHORIA: Adicionar informações sobre o próximo horário disponível
            if (todosTipos.length > 0) {
                // Verificar qual seria o próximo horário mais provável com base na hora atual
                const horaAtualObj = new Date();
                const horarioAtualHoras = horaAtualObj.getHours();
                const horarioAtualMinutos = horaAtualObj.getMinutes();
                
                let proximoHorario = null;
                let proximoTexto = '';
                
                todosTipos.forEach(tipo => {
                    // Extrair horário do texto
                    const match = tipo.text.match(/\((\d{2}):(\d{2})\)$/);
                    if (match) {
                        const horas = parseInt(match[1], 10);
                        const minutos = parseInt(match[2], 10);
                        
                        // Se o horário for maior que o atual, pode ser o próximo
                        if (horas > horarioAtualHoras || (horas === horarioAtualHoras && minutos > horarioAtualMinutos)) {
                            if (!proximoHorario || 
                                horas < proximoHorario.horas || 
                                (horas === proximoHorario.horas && minutos < proximoHorario.minutos)) {
                                proximoHorario = { horas, minutos };
                                proximoTexto = tipo.text;
                            }
                        }
                    }
                });
                
                if (proximoTexto) {
                    html += `
                    <div class="alert alert-info mb-3" role="alert" style="grid-column: span 2;">
                        <i class="fas fa-info-circle"></i> 
                        Próximo horário disponível: <strong>${proximoTexto}</strong>
                    </div>`;
                }
            }
            
            // ✅ CORREÇÃO: Desabilitar botão de confirmação quando não há tipos disponíveis
            if (btnConfirmar) {
                btnConfirmar.disabled = true;
                btnConfirmar.innerHTML = '<i class="fas fa-ban"></i> Sem Registros Disponíveis';
            }
        } else {
            tiposDisponiveis.forEach(tipo => {
                // Extrair horário do formato "Tipo (HH:MM)"
                let horarioOriginal = '';
                let horarioComTolerancia = '';
                
                const match = tipo.text.match(/\((\d{2}:\d{2})\)$/);
                if (match && match[1]) {
                    horarioOriginal = match[1];
                    
                    // Calcular horário com tolerância (apenas para visualização)
                    const tolerancia = window.horariosFuncionario?.horarios?.tolerancia_minutos || 10;
                    
                    // Determinar se adiciona ou subtrai tolerância com base no tipo de registro
                    if (tipo.value === 'entrada_manha' || tipo.value === 'entrada_tarde') {
                        // Para entradas, adicionar tolerância (pode chegar até X min depois)
                        horarioComTolerancia = `até ${horarioOriginal.split(':')[0]}:${(parseInt(horarioOriginal.split(':')[1]) + tolerancia).toString().padStart(2, '0')}`;
                    } else {
                        // Para saídas, subtrair tolerância (pode sair até X min antes)
                        let minutos = parseInt(horarioOriginal.split(':')[1]) - tolerancia;
                        let horas = parseInt(horarioOriginal.split(':')[0]);
                        
                        if (minutos < 0) {
                            minutos += 60;
                            horas -= 1;
                        }
                        
                        horarioComTolerancia = `a partir de ${horas.toString().padStart(2, '0')}:${minutos.toString().padStart(2, '0')}`;
                    }
                }
                
                // Texto base sem horário
                const tipoBase = tipo.text.replace(/\s*\(\d{2}:\d{2}\)$/, '');
                
                html += `
                <div class="form-check tipo-registro-opcao">
                    <input class="form-check-input" type="radio" name="tipo_registro" 
                           id="tipo_${tipo.value}" value="${tipo.value}" required>
                    <label class="form-check-label" for="tipo_${tipo.value}">
                        <i class="fas fa-clock"></i> ${tipoBase} 
                        <div class="horario-info">
                            <span class="badge bg-primary">${horarioOriginal}</span>
                            <span class="badge bg-success">Tolerância: ${horarioComTolerancia}</span>
                        </div>
                    </label>
                </div>`;
            });
        }
        
        // ✅ MELHORIA: Exibir registros ausentes
        const ausencias = window.horariosFuncionario?.ausencias || [];
        if (ausencias && ausencias.length > 0) {
            html += `
            <div class="alert alert-danger mt-3 mb-2" role="alert" style="grid-column: span 2;">
                <i class="fas fa-exclamation-triangle"></i> 
                <strong>Registros não realizados:</strong>
                </div>`;
            
            ausencias.forEach(ausencia => {
                const tipoFormatado = ausencia.tipo.replace('_', ' ').replace(/\b\w/g, c => c.toUpperCase());
                html += `
                <div class="alert alert-danger mb-2" role="alert" style="grid-column: span 2;">
                    <i class="fas fa-times-circle"></i> 
                    ${tipoFormatado} (${ausencia.horario_previsto}) - <strong>${ausencia.status}</strong>
                </div>`;
            });
        }
        
        // Verificar registros indisponíveis mas que ainda serão possíveis
        const tiposDisponiveis_values = tiposDisponiveis.map(t => t.value);
        const tiposIndisponiveis = todosTipos.filter(t => !tiposDisponiveis_values.includes(t.value));
        
        // Filtrar tipos já registrados da lista de indisponíveis
        const tiposRegistrados = registrosExistentes.map(r => r.tipo_registro);
        const tiposIndisponiveisNaoRegistrados = tiposIndisponiveis.filter(t => !tiposRegistrados.includes(t.value));
        
        if (tiposIndisponiveisNaoRegistrados.length > 0 && tiposDisponiveis.length > 0) {
            html += `
            <div class="alert alert-secondary mt-3" role="alert" style="grid-column: span 2;">
                <i class="fas fa-info-circle"></i> 
                <strong>Outros horários:</strong>
            </div>`;
            
            tiposIndisponiveisNaoRegistrados.forEach(tipo => {
                html += `
                <div class="form-check tipo-registro-opcao disabled">
                    <input class="form-check-input" type="radio" name="tipo_registro_disabled" 
                           id="tipo_disabled_${tipo.value}" value="${tipo.value}" disabled>
                    <label class="form-check-label text-muted" for="tipo_disabled_${tipo.value}">
                        <i class="fas fa-clock"></i> ${tipo.text}
                        <span class="badge bg-secondary">INDISPONÍVEL</span>
                    </label>
                </div>`;
            });
        }
        
        // Mostrar tipos já registrados como desabilitados
        if (tiposRegistrados.length > 0) {
            const tiposJaRegistrados = todosTipos.filter(t => tiposRegistrados.includes(t.value));
            
            if (tiposJaRegistrados.length > 0 && tiposDisponiveis.length > 0) {
                html += `
                <div class="alert alert-success mt-3" role="alert" style="grid-column: span 2;">
                    <i class="fas fa-check-circle"></i> 
                    <strong>Registros já realizados hoje:</strong>
                </div>`;
                
                tiposJaRegistrados.forEach(tipo => {
                    // Encontrar o registro correspondente
                    const registro = registrosExistentes.find(r => r.tipo_registro === tipo.value);
                    const horaRegistrada = registro ? registro.hora : '?';
                    const statusRegistro = registro && registro.status_pontualidade ? registro.status_pontualidade : 'Registrado';
                    const statusClass = statusRegistro === 'Pontual' ? 'success' : 'warning';
                    
                    html += `
                    <div class="form-check tipo-registro-opcao disabled">
                        <input class="form-check-input" type="radio" name="tipo_registro_registrado" 
                               id="tipo_registrado_${tipo.value}" value="${tipo.value}" disabled>
                        <label class="form-check-label" for="tipo_registrado_${tipo.value}">
                            <i class="fas fa-check"></i> ${tipo.text}
                            <span class="badge bg-${statusClass}">${statusRegistro} às ${horaRegistrada}</span>
                        </label>
                    </div>`;
                });
            }
        }
        
        tiposContainer.innerHTML = html;
    }
    
    function confirmarRegistro() {
        // Verificar se há funcionário selecionado
        if (!funcionarioSelecionado) {
            alert('Erro: nenhum funcionário selecionado');
            return;
        }
        
        // Obter tipo de registro selecionado
        const tipoRegistroElement = document.querySelector('input[name="tipo_registro"]:checked');
        if (!tipoRegistroElement) {
            alert('Selecione um tipo de registro');
            return;
        }
        
        const tipoRegistro = tipoRegistroElement.value;
        
        // ✅ MELHORIA: Verificar se este tipo já foi registrado hoje
        if (registrosExistentes && registrosExistentes.length > 0) {
            const jaRegistrado = registrosExistentes.find(r => r.tipo_registro === tipoRegistro);
            if (jaRegistrado) {
                alert(`Este tipo de registro já foi feito hoje às ${jaRegistrado.hora || '?'}`);
                return;
            }
        }
        
        // ✅ MELHORIA: Validar a ordem lógica dos registros
        const sequenciaCorreta = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida'];
        if (window.horariosFuncionario && window.horariosFuncionario.ausencias) {
            const ausencias = window.horariosFuncionario.ausencias;
            
            // Se há ausências anteriores na sequência lógica, alertar o usuário
            const indiceAtual = sequenciaCorreta.indexOf(tipoRegistro);
            if (indiceAtual > 0) {
                // Verificar se algum tipo anterior está ausente
                for (let i = 0; i < indiceAtual; i++) {
                    const tipoAnterior = sequenciaCorreta[i];
                    const ausente = ausencias.find(a => a.tipo === tipoAnterior);
                    
                    if (ausente) {
                        const tipoAnteriorFormatado = tipoAnterior.replace('_', ' ').replace(/\b\w/g, c => c.toUpperCase());
                        const confirmar = confirm(`ATENÇÃO: O registro de ${tipoAnteriorFormatado} (${ausente.horario_previsto}) não foi feito e está marcado como AUSENTE.\n\nDeseja registrar ${tipoRegistro} mesmo assim?`);
                        
                        if (!confirmar) {
                            return; // Cancelar registro
                        }
                        break; // Se o usuário confirmou, não exibir mais alertas
                    }
                }
            }
        }
        
        // Obter observações
        const observacoes = document.getElementById('observacoes').value.trim();
        
        // ✅ CORREÇÃO: Desabilitar o botão durante o envio
        btnConfirmar.disabled = true;
        btnConfirmar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';
        
        // Mostrar overlay de loading
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }
        
        // ✅ CORREÇÃO: Usar FormData em vez de JSON
        const formData = new FormData();
        formData.append('funcionario_id', funcionarioSelecionado);
        formData.append('tipo_registro', tipoRegistro);
        formData.append('observacoes', observacoes);
        
        // Enviar requisição para API
        fetch('/registro-ponto/api/registrar-manual', {
            method: 'POST',
            body: formData // ✅ CORREÇÃO: Enviar FormData em vez de JSON
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // ✅ CORREÇÃO: Manter botão desabilitado após sucesso
                btnConfirmar.disabled = true;
                btnConfirmar.innerHTML = '<i class="fas fa-check-circle"></i> Registrado com Sucesso';
                
                // Adicionar o novo registro à lista de registros existentes
                registrosExistentes.push({
                    tipo_registro: tipoRegistro,
                    hora: new Date().toTimeString().substring(0, 5),
                    status_pontualidade: data.status_pontualidade || 'Registrado'
                });
                
                // Atualizar os tipos de registro para refletir o novo registro
                const tiposDisponiveis = window.horariosFuncionario.tipos_disponiveis.filter(t => t.value !== tipoRegistro);
                window.horariosFuncionario.tipos_disponiveis = tiposDisponiveis;
                
                mostrarResultado('sucesso', data.message, data);
                
                // Atualizar os tipos para mostrar o que acabou de ser registrado como já feito
                preencherTiposRegistro(tiposDisponiveis, window.horariosFuncionario.todos_tipos, window.horariosFuncionario.hora_atual);
                
                // Fechar modal após 3 segundos
                setTimeout(() => {
                    registroModal.hide();
                    limparModal();
                    // Recarregar a página para atualizar os registros
                    location.reload();
                }, 2000);
            } else {
                // ✅ CORREÇÃO: Verificar se houve erro por registro duplicado
                if (data.registro_existente) {
                    // Se o registro já existe, desabilitar o botão
                    btnConfirmar.disabled = true;
                    btnConfirmar.innerHTML = '<i class="fas fa-ban"></i> Já Registrado';
                    
                    // Adicionar o registro existente à lista
                    if (!registrosExistentes.some(r => r.tipo_registro === tipoRegistro)) {
                        registrosExistentes.push({
                            tipo_registro: tipoRegistro,
                            hora: data.registro_existente.hora || '?',
                            status_pontualidade: 'Registrado'
                        });
                    }
                    
                    // Atualizar interface
                    const tiposDisponiveis = window.horariosFuncionario.tipos_disponiveis.filter(t => t.value !== tipoRegistro);
                    window.horariosFuncionario.tipos_disponiveis = tiposDisponiveis;
                    preencherTiposRegistro(tiposDisponiveis, window.horariosFuncionario.todos_tipos, window.horariosFuncionario.hora_atual);
                } else {
                    // Outro tipo de erro - reativar o botão
                    btnConfirmar.disabled = false;
                    btnConfirmar.innerHTML = '<i class="fas fa-save"></i> Registrar Ponto';
                }
                
                mostrarResultado('erro', data.message);
            }
        })
        .catch(error => {
            mostrarResultado('erro', 'Erro de comunicação: ' + error.message);
            console.error('Erro ao registrar ponto:', error);
            
            // Reativar botão em caso de erro de comunicação
            btnConfirmar.disabled = false;
            btnConfirmar.innerHTML = '<i class="fas fa-save"></i> Registrar Ponto';
        })
        .finally(() => {
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        });
    }
    
    function mostrarResultado(tipo, mensagem, dados = null) {
        const resultadoDiv = document.getElementById('resultado-registro');
        if (!resultadoDiv) return;
        
        const classe = tipo === 'sucesso' ? 'alert alert-success' : 'alert alert-danger';
        const icone = tipo === 'sucesso' ? 'fas fa-check-circle' : 'fas fa-times-circle';
        
        let conteudo = `
            <div class="${classe} mt-3" role="alert">
                <strong><i class="${icone}"></i> ${tipo === 'sucesso' ? 'Sucesso!' : 'Erro!'}</strong><br>
                ${mensagem}
        `;
        
        if (dados && tipo === 'sucesso') {
            conteudo += `
                <br><small>Registrado em: ${dados.data_hora || 'agora'}</small>
            `;
            
            // ✅ MELHORIA: Mostrar status de pontualidade
            if (dados.status_pontualidade) {
                const statusClass = dados.status_pontualidade === 'Pontual' ? 'success' : 'warning';
                conteudo += `
                <br><div class="mt-2">
                    <span class="badge bg-${statusClass}">${dados.status_pontualidade}</span>
                </div>
                `;
            }
        }
        
        conteudo += `</div>`;
        resultadoDiv.innerHTML = conteudo;
    }
    
    function limparModal() {
        // Limpar formulário
        formRegistro.reset();
        document.getElementById('resultado-registro').innerHTML = '';
        
        // Limpar seleções de rádio
        const radios = document.querySelectorAll('input[name="tipo_registro"]');
        radios.forEach(radio => radio.checked = false);
        
        // ✅ GARANTIR QUE MODAL SEJA OCULTO APÓS FECHAR
        modalElement.style.display = 'none';
        
        // ✅ CORREÇÃO: Resetar botão de confirmação
        if (btnConfirmar) {
            btnConfirmar.disabled = false;
            btnConfirmar.innerHTML = '<i class="fas fa-save"></i> Registrar Ponto';
        }
        
        // Limpar registros existentes
        registrosExistentes = [];
    }
    
    // Limpar modal quando fechar
    modalElement.addEventListener('hidden.bs.modal', function() {
        limparModal();
    });
});
</script>
{% endblock %} 