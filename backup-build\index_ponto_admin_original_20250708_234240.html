{% extends "base.html" %}

{% block title %}Administração de Ponto - RLPONTO-WEB{% endblock %}

{% block page_title %}Administração de Ponto{% endblock %}

{% set breadcrumbs = [
    {'text': 'In<PERSON>cio', 'url': '/'},
    {'text': 'Administração de Ponto'}
] %}

{% block content %}

<!-- CSS customizado para layout moderno -->
<style>
:root {
    --primary-color: #4fbdba;
    --primary-hover: #26a69a;
    --primary-light: #80cbc4;
    --background-color: #f9fafb;
    --card-background: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
}

.modern-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 8px 32px rgba(79, 189, 186, 0.15);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 60%;
    height: 200%;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: rotate(15deg);
}

.page-header-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin-top: 0.5rem;
}

.back-button {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.back-button:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--accent-color);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: var(--accent-color);
}

.stat-card.blue::before { --accent-color: #3b82f6; }
.stat-card.green::before { --accent-color: #10b981; }
.stat-card.yellow::before { --accent-color: #f59e0b; }
.stat-card.teal::before { --accent-color: #06b6d4; }

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.blue { background: linear-gradient(135deg, #3b82f6, #1e40af); }
.stat-icon.green { background: linear-gradient(135deg, #10b981, #047857); }
.stat-icon.yellow { background: linear-gradient(135deg, #f59e0b, #d97706); }
.stat-icon.teal { background: linear-gradient(135deg, #06b6d4, #0891b2); }

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filters-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
}

.filters-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.filter-input, .filter-select {
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 0.75rem;
    background: var(--card-background);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.filter-input:focus, .filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    outline: none;
}

.filters-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.btn-filter {
    background: var(--primary-color);
    border: 1px solid var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-filter:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(79, 189, 186, 0.3);
}

.btn-clear {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-clear:hover {
    background: #f3f4f6;
    color: var(--text-primary);
}

.employees-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
}

.employees-header {
    background: #f8fafc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.employees-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.employee-count {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.employee-card {
    padding: 1.5rem;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
    cursor: pointer;
}

.employee-card:hover {
    background: #f8fafc;
    border-left: 4px solid var(--primary-color);
    padding-left: calc(1.5rem - 4px);
}

.employee-card:last-child {
    border-bottom: none;
}

.employee-main {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.employee-avatar {
    width: 56px;
    height: 56px;
    border-radius: 14px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    box-shadow: 0 4px 8px rgba(79, 189, 186, 0.3);
    flex-shrink: 0;
}

.employee-info {
    flex: 1;
    min-width: 0;
}

.employee-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.employee-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
}

.employee-meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.employee-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-number.blue { color: #3b82f6; }
.stat-number.green { color: #10b981; }
.stat-number.yellow { color: #f59e0b; }
.stat-number.teal { color: #06b6d4; }

.stat-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.employee-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.action-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: none;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.action-btn.primary {
    background: var(--primary-color);
    color: white;
}

.action-btn.primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: #f3f4f6;
    color: var(--text-secondary);
}

.action-btn.secondary:hover {
    background: #e5e7eb;
    color: var(--text-primary);
}

.empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .page-header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .filters-grid {
        grid-template-columns: 1fr;
    }
    
    .employee-main {
        flex-direction: column;
        text-align: center;
    }
    
    .employee-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .employee-actions {
        justify-content: center;
    }
}
</style>

<!-- Container Principal -->
<div class="modern-container">
    <!-- Cabeçalho da Página -->
    <div class="page-header">
        <div class="page-header-content">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-clock" style="margin-right: 0.5rem;"></i>
                    Administração de Ponto
                </h1>
                <p class="page-subtitle">Gerencie registros de ponto e acompanhe a presença dos funcionários</p>
            </div>
            <a href="/" class="back-button">
                <i class="fas fa-arrow-left"></i>
                Voltar ao Dashboard
            </a>
        </div>
    </div>

    <!-- Cards de Estatísticas -->
    <div class="stats-grid">
        <div class="stat-card blue">
            <div class="stat-header">
                <div class="stat-icon blue">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="stat-value">{{ stats.funcionarios_com_ponto or 0 }}</div>
            <div class="stat-label">Funcionários com Ponto</div>
        </div>

        <div class="stat-card green">
            <div class="stat-header">
                <div class="stat-icon green">
                    <i class="fas fa-clipboard-check"></i>
                </div>
            </div>
            <div class="stat-value">{{ stats.total_registros or 0 }}</div>
            <div class="stat-label">Registros no Mês</div>
        </div>

        <div class="stat-card yellow">
            <div class="stat-header">
                <div class="stat-icon yellow">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
            <div class="stat-value">{{ stats.faltas or 0 }}</div>
            <div class="stat-label">Faltas no Mês</div>
        </div>

        <div class="stat-card teal">
            <div class="stat-header">
                <div class="stat-icon teal">
                    <i class="fas fa-comment-dots"></i>
                </div>
            </div>
            <div class="stat-value">{{ stats.registros_justificados or 0 }}</div>
            <div class="stat-label">Registros Justificados</div>
        </div>
    </div>

    <!-- Seção de Filtros -->
    <div class="filters-section">
        <h3 class="filters-title">
            <i class="fas fa-filter"></i>
            Filtros de Busca
        </h3>
        
        <form method="GET" action="/ponto-admin/">
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label" for="search">
                        <i class="fas fa-search"></i> Nome do Funcionário
                    </label>
                    <input type="text" id="search" name="search" class="filter-input" 
                           placeholder="Digite o nome para buscar..." 
                           value="{{ request.args.get('search', '') }}">
                </div>

                <div class="filter-group">
                    <label class="filter-label" for="empresa">
                        <i class="fas fa-building"></i> Empresa
                    </label>
                    <select id="empresa" name="empresa" class="filter-select">
                        <option value="">Todas as empresas</option>
                        <!-- Empresas serão carregadas dinamicamente -->
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label" for="status">
                        <i class="fas fa-toggle-on"></i> Status
                    </label>
                    <select id="status" name="status" class="filter-select">
                        <option value="">Todos os status</option>
                        <option value="Ativo" {{ 'selected' if request.args.get('status') == 'Ativo' }}>Ativo</option>
                        <option value="Inativo" {{ 'selected' if request.args.get('status') == 'Inativo' }}>Inativo</option>
                    </select>
                </div>
            </div>

            <div class="filters-actions">
                <button type="submit" class="btn-filter">
                    <i class="fas fa-search"></i>
                    Aplicar Filtros
                </button>
                <button type="button" class="btn-clear" onclick="limparFiltros()">
                    <i class="fas fa-times"></i>
                    Limpar Filtros
                </button>
            </div>
        </form>
    </div>

    <!-- Seção de Funcionários -->
    <div class="employees-section">
        <div class="employees-header">
            <h3 class="employees-title">
                <i class="fas fa-list"></i>
                Lista de Funcionários
                <span class="employee-count">{{ funcionarios|length }}</span>
            </h3>
        </div>

        {% if funcionarios %}
            {% for funcionario in funcionarios %}
            <div class="employee-card" onclick="verDetalhes({{ funcionario.id }})">
                <div class="employee-main">
                    <div class="employee-avatar">
                        {{ funcionario.nome_completo[:2].upper() }}
                    </div>
                    
                    <div class="employee-info">
                        <h4 class="employee-name">{{ funcionario.nome_completo }}</h4>
                        
                        <div class="employee-meta">
                            <div class="employee-meta-item">
                                <i class="fas fa-building"></i>
                                {{ funcionario.empresa_nome or 'N/A' }}
                            </div>
                            <div class="employee-meta-item">
                                <i class="fas fa-user-tag"></i>
                                {{ funcionario.cargo or 'N/A' }}
                            </div>
                            <div class="employee-meta-item">
                                <i class="fas fa-layer-group"></i>
                                {{ funcionario.setor or 'N/A' }}
                            </div>
                            {% if funcionario.ultimo_ponto %}
                            <div class="employee-meta-item">
                                <i class="fas fa-calendar"></i>
                                Último ponto: {{ funcionario.ultimo_ponto.strftime('%d/%m/%Y') }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="employee-stats">
                            <div class="stat-item">
                                <span class="stat-number blue">{{ funcionario.registros_mes or 0 }}</span>
                                <span class="stat-text">Registros</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number {{ 'green' if not funcionario.faltas_mes or funcionario.faltas_mes == 0 else 'yellow' }}">
                                    {{ funcionario.faltas_mes or 0 }}
                                </span>
                                <span class="stat-text">Faltas</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number teal">{{ funcionario.justificativas_mes or 0 }}</span>
                                <span class="stat-text">Justificativas</span>
                            </div>
                        </div>

                        <div class="employee-actions" onclick="event.stopPropagation();">
                            <button class="action-btn primary" onclick="verDetalhes({{ funcionario.id }})" title="Ver detalhes">
                                <i class="fas fa-eye"></i>
                                Detalhes
                            </button>
                            <button class="action-btn secondary" onclick="gerarRelatorio({{ funcionario.id }})" title="Gerar relatório">
                                <i class="fas fa-file-pdf"></i>
                                Relatório
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-users-slash"></i>
                </div>
                <h3>Nenhum funcionário encontrado</h3>
                <p>Nenhum funcionário corresponde aos filtros aplicados.</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
function verDetalhes(funcionarioId) {
    window.location.href = `/ponto-admin/funcionario/${funcionarioId}`;
}

function gerarRelatorio(funcionarioId) {
    window.open(`/ponto-admin/relatorio-funcionario/${funcionarioId}`, '_blank');
}

function limparFiltros() {
    document.getElementById('search').value = '';
    document.getElementById('empresa').value = '';
    document.getElementById('status').value = '';
    // Opcional: submeter automaticamente após limpar
    // document.querySelector('form').submit();
}

// Adicionar animações de entrada
document.addEventListener('DOMContentLoaded', function() {
    // Animar cards de estatísticas
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.style.animation = 'fadeInUp 0.6s ease forwards';
    });

    // Animar cards de funcionários
    const employeeCards = document.querySelectorAll('.employee-card');
    employeeCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.05}s`;
        card.style.animation = 'fadeInUp 0.4s ease forwards';
    });
});

// Animações CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .stat-card, .employee-card {
        opacity: 0;
    }
`;
document.head.appendChild(style);
</script>

{% endblock %}
