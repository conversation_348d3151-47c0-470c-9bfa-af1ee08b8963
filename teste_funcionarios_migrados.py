#!/usr/bin/env python3
"""
Teste para verificar se a migração de horas semanais funcionou corretamente
"""
import sys
import os
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager, FuncionarioQueries

def testar_funcionarios():
    print("🧪 TESTE DE FUNCIONÁRIOS APÓS MIGRAÇÃO")
    print("=" * 60)
    
    try:
        # Teste 1: Verificar estrutura da tabela
        print("\n1. 📋 VERIFICANDO ESTRUTURA DA TABELA")
        db = DatabaseManager()
        
        # Verificar se campo novo existe
        estrutura = db.execute_query("DESCRIBE funcionarios")
        campos = [campo['Field'] for campo in estrutura]
        
        if 'horas_semanais_obrigatorias' in campos:
            print("   ✅ Campo 'horas_semanais_obrigatorias' existe")
        else:
            print("   ❌ Campo 'horas_semanais_obrigatorias' NÃO existe")
            
        if 'horas_trabalho_obrigatorias_old' in campos:
            print("   ✅ Campo 'horas_trabalho_obrigatorias_old' existe (backup)")
        else:
            print("   ⚠️ Campo de backup não encontrado")
        
        # Teste 2: Consulta direta simples
        print("\n2. 🔍 CONSULTA DIRETA SIMPLES")
        funcionarios = db.execute_query("""
            SELECT id, nome_completo, horas_semanais_obrigatorias, ativo
            FROM funcionarios 
            WHERE ativo = 1
            LIMIT 5
        """)
        
        if funcionarios:
            print(f"   ✅ {len(funcionarios)} funcionários encontrados:")
            for func in funcionarios:
                print(f"      - {func['nome_completo']}: {func['horas_semanais_obrigatorias']}h semanais")
        else:
            print("   ❌ Nenhum funcionário encontrado")
        
        # Teste 3: Usar FuncionarioQueries.get_all()
        print("\n3. 🔧 TESTANDO FuncionarioQueries.get_all()")
        try:
            result = FuncionarioQueries.get_all(page=1, per_page=5)
            
            if result and result.get('data'):
                print(f"   ✅ {len(result['data'])} funcionários retornados pela query")
                for func in result['data']:
                    horas = func.get('horas_semanais_obrigatorias', 'N/A')
                    print(f"      - {func['nome_completo']}: {horas}h semanais")
            else:
                print("   ❌ FuncionarioQueries.get_all() retornou vazio")
                
        except Exception as e:
            print(f"   ❌ ERRO em FuncionarioQueries.get_all(): {e}")
        
        # Teste 4: Verificar backup
        print("\n4. 💾 VERIFICANDO BACKUP")
        try:
            backup = db.execute_query("SELECT COUNT(*) as total FROM backup_horas_diarias_20250715")
            if backup and backup[0]['total'] > 0:
                print(f"   ✅ Backup com {backup[0]['total']} registros preservados")
            else:
                print("   ⚠️ Backup não encontrado ou vazio")
        except Exception as e:
            print(f"   ⚠️ Erro ao verificar backup: {e}")
            
        print("\n" + "=" * 60)
        print("✅ TESTE CONCLUÍDO")
        
    except Exception as e:
        print(f"❌ ERRO GERAL NO TESTE: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    testar_funcionarios()
