#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para debugar a função _criar_funcionario com versão simplificada
"""

import sys
import os
import traceback
from datetime import datetime

# Adicionar o diretório do projeto ao path
sys.path.insert(0, '/var/www/controle-ponto')

# Importar apenas o necessário
try:
    from utils.database import DatabaseManager
    print("✅ DatabaseManager importado")
    
except ImportError as e:
    print(f"❌ Erro ao importar: {e}")
    sys.exit(1)

def criar_funcionario_simples(data):
    """
    Versão simplificada da função _criar_funcionario para debug
    """
    try:
        print(f"🔄 Iniciando criação simplificada")
        
        # SQL básico sem complicações
        sql = """
        INSERT INTO funcionarios (
            nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
            ctps_numero, ctps_serie_uf, pis_pasep,
            endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
            telefone1, telefone2, email,
            cargo, setor_obra, matricula_empresa, data_admissao, tipo_contrato,
            nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, status_cadastro,
            horas_semanais_obrigatorias, empresa_id, jornada_trabalho_id,
            digital_dedo1, digital_dedo2, foto_3x4
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s
        )
        """

        # Parâmetros simples sem processamento complexo
        params = (
            data['nome_completo'], data['cpf'], data['rg'], data['data_nascimento'],
            data['sexo'], data['estado_civil'], data['nacionalidade'],
            data['ctps_numero'], data['ctps_serie_uf'], data['pis_pasep'],
            data['endereco_rua'], data['endereco_bairro'], data['endereco_cidade'], 
            data['endereco_cep'], data['endereco_estado'],
            data['telefone1'], data['telefone2'], data['email'],
            data['cargo'], data['setor_obra'], data['matricula_empresa'],
            data['data_admissao'], data['tipo_contrato'],
            data['nivel_acesso'], data['turno'], data['tolerancia_ponto'],
            data['banco_horas'], data['hora_extra'], data['status_cadastro'],
            data['horas_semanais_obrigatorias'], data['empresa_id'], data['jornada_trabalho_id'],
            data['digital_dedo1'], data['digital_dedo2'], data['foto_3x4']
        )

        print(f"📊 Executando SQL com {len(params)} parâmetros")
        
        # Executar inserção
        funcionario_id = DatabaseManager.execute_query(sql, params, fetch_all=False)
        
        print(f"✅ Funcionário criado com ID: {funcionario_id}")
        return funcionario_id

    except Exception as e:
        print(f"❌ ERRO na criação: {e}")
        print(f"   Tipo: {type(e).__name__}")
        print(f"   Traceback: {traceback.format_exc()}")
        raise e

def criar_dados_teste():
    """Cria dados de teste válidos"""
    timestamp = datetime.now().strftime("%H%M%S")
    return {
        'nome_completo': 'JOÃO TESTE SIMPLES',
        'cpf': f'333.444.555-{timestamp[-2:]}',
        'rg': '12.345.678-9',
        'data_nascimento': '1990-01-01',
        'sexo': 'M',
        'estado_civil': 'Solteiro',
        'nacionalidade': 'Brasileira',
        'ctps_numero': '1234567',
        'ctps_serie_uf': '001/MG',
        'pis_pasep': '123.45678.90-1',
        'endereco_rua': 'RUA TESTE, 123',
        'endereco_bairro': 'CENTRO',
        'endereco_cidade': 'BELO HORIZONTE',
        'endereco_cep': '30000-000',
        'endereco_estado': 'MG',
        'telefone1': '(31) 99999-9999',
        'telefone2': '(31) 3333-4444',
        'email': '<EMAIL>',
        'cargo': 'ANALISTA DE TESTE',
        'setor_obra': 'DESENVOLVIMENTO',
        'matricula_empresa': f'SIMP{timestamp}',
        'data_admissao': '2025-01-01',
        'tipo_contrato': 'CLT',
        'nivel_acesso': 'Funcionario',
        'turno': 'Diurno',
        'tolerancia_ponto': 5,
        'banco_horas': True,
        'hora_extra': True,
        'status_cadastro': 'Ativo',
        'horas_semanais_obrigatorias': 44.0,
        'empresa_id': 11,
        'jornada_trabalho_id': 1,
        'digital_dedo1': None,
        'digital_dedo2': None,
        'foto_3x4': None
    }

def main():
    """Função principal"""
    print("🔧 DEBUG FUNÇÃO SIMPLIFICADA")
    print("=" * 60)
    print(f"⏰ Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Teste de conexão
    try:
        result = DatabaseManager.execute_query("SELECT 1 as test", fetch_one=True)
        if result and result.get('test') == 1:
            print("✅ Conexão com banco funcionando")
        else:
            print("❌ Conexão com banco falhou")
            return
    except Exception as e:
        print(f"❌ ERRO de conexão: {e}")
        return
    
    # Teste da função simplificada
    try:
        dados = criar_dados_teste()
        print(f"📊 Dados criados: {len(dados)} campos")
        
        funcionario_id = criar_funcionario_simples(dados)
        
        if funcionario_id:
            print(f"✅ Funcionário criado com ID: {funcionario_id}")
            
            # Limpar teste
            try:
                DatabaseManager.execute_query(
                    "DELETE FROM funcionarios WHERE id = %s", 
                    (funcionario_id,), 
                    fetch_all=False
                )
                print(f"🧹 Funcionário de teste removido (ID: {funcionario_id})")
            except Exception as cleanup_error:
                print(f"⚠️ Erro ao limpar teste: {cleanup_error}")
            
            print("\n🎉 FUNÇÃO SIMPLIFICADA PASSOU!")
            print("✅ O problema está em alguma função específica da versão original")
        else:
            print("❌ Função simplificada não retornou ID")
            
    except Exception as e:
        print(f"❌ ERRO na função simplificada: {e}")
        print(f"   Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
