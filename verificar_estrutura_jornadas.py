#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar estrutura da tabela jornadas_trabalho
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def verificar_estrutura_jornadas():
    """Verificar estrutura da tabela jornadas_trabalho"""
    print("🔍 VERIFICANDO ESTRUTURA DA TABELA JORNADAS_TRABALHO")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar estrutura da tabela
        print("\n1. Verificando estrutura da tabela jornadas_trabalho...")
        sql_estrutura = "DESCRIBE jornadas_trabalho"
        
        estrutura = db.execute_query(sql_estrutura)
        
        print(f"📊 Campos da tabela jornadas_trabalho:")
        for campo in estrutura:
            print(f"   - {campo['Field']}: {campo['Type']} ({campo['Null']}, {campo['Key']}, {campo['Default']})")
        
        # 2. Verificar TODAS as jornadas da AiNexus (sem campo turno)
        print("\n2. Verificando TODAS as jornadas da AiNexus...")
        sql_todas_jornadas_ainexus = """
        SELECT 
            jt.id, jt.nome_jornada, jt.tipo_jornada,
            jt.seg_qui_entrada, jt.seg_qui_saida,
            jt.sexta_entrada, jt.sexta_saida,
            jt.intervalo_inicio, jt.intervalo_fim,
            jt.tolerancia_entrada_minutos,
            jt.padrao, jt.ativa,
            e.razao_social as empresa_nome,
            e.id as empresa_id
        FROM jornadas_trabalho jt
        JOIN empresas e ON jt.empresa_id = e.id
        WHERE e.razao_social LIKE '%%AiNexus%%'
        ORDER BY jt.padrao DESC, jt.id
        """
        
        jornadas_ainexus = db.execute_query(sql_todas_jornadas_ainexus)
        
        print(f"📊 TODAS as jornadas da AiNexus: {len(jornadas_ainexus)}")
        for jornada in jornadas_ainexus:
            padrao = "PADRÃO" if jornada['padrao'] else "NORMAL"
            ativa = "ATIVA" if jornada['ativa'] else "INATIVA"
            print(f"   - ID {jornada['id']}: {jornada['nome_jornada']} ({padrao}, {ativa})")
            print(f"     Empresa ID: {jornada['empresa_id']}")
            print(f"     Tipo: {jornada['tipo_jornada']}")
            print(f"     Seg-Qui: {jornada['seg_qui_entrada']} às {jornada['seg_qui_saida']}")
            print(f"     Sexta: {jornada['sexta_entrada']} às {jornada['sexta_saida']}")
            print(f"     Intervalo: {jornada['intervalo_inicio']} às {jornada['intervalo_fim']}")
            print(f"     Tolerância: {jornada['tolerancia_entrada_minutos']} minutos")
            print()
        
        # 3. Verificar funcionários da AiNexus
        print("\n3. Verificando funcionários da AiNexus...")
        sql_funcionarios_ainexus = """
        SELECT 
            f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id,
            e.razao_social as empresa_nome
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        WHERE e.razao_social LIKE '%%AiNexus%%' AND f.status_cadastro = 'Ativo'
        ORDER BY f.id
        """
        
        funcionarios_ainexus = db.execute_query(sql_funcionarios_ainexus)
        
        print(f"📊 Funcionários da AiNexus: {len(funcionarios_ainexus)}")
        for func in funcionarios_ainexus:
            print(f"   - ID {func['id']}: {func['nome_completo']}")
            print(f"     Empresa ID: {func['empresa_id']}")
            print(f"     Jornada Trabalho ID: {func['jornada_trabalho_id']}")
        
        # 4. Testar get_with_epis para cada funcionário
        print("\n4. Testando get_with_epis para cada funcionário...")
        from utils.database import FuncionarioQueries
        
        for func in funcionarios_ainexus:
            funcionario_id = func['id']
            nome = func['nome_completo']
            
            print(f"\n   📋 Funcionário {funcionario_id}: {nome}")
            
            funcionario_completo = FuncionarioQueries.get_with_epis(funcionario_id)
            
            if funcionario_completo:
                print(f"      Jornada: {funcionario_completo.get('nome_jornada')}")
                print(f"      Tipo: {funcionario_completo.get('tipo_jornada')}")
                print(f"      Seg-Qui: {funcionario_completo.get('jornada_seg_qui_entrada')} às {funcionario_completo.get('jornada_seg_qui_saida')}")
                print(f"      Sexta: {funcionario_completo.get('jornada_sex_entrada')} às {funcionario_completo.get('jornada_sex_saida')}")
                print(f"      Intervalo: {funcionario_completo.get('jornada_intervalo_entrada')} às {funcionario_completo.get('jornada_intervalo_saida')}")
                print(f"      Tolerância: {funcionario_completo.get('tolerancia_entrada_minutos')} minutos")
                print(f"      Alocação ID: {funcionario_completo.get('alocacao_id')}")
                print(f"      Cliente: {funcionario_completo.get('cliente_nome')}")
                
                # Verificar se há dados de horário legacy
                if funcionario_completo.get('nome_horario'):
                    print(f"      ⚠️ HORÁRIO LEGACY: {funcionario_completo.get('nome_horario')}")
                    print(f"         Entrada: {funcionario_completo.get('entrada_manha')}")
                    print(f"         Saída: {funcionario_completo.get('saida')}")
                    print(f"         Tolerância Legacy: {funcionario_completo.get('tolerancia_minutos')} minutos")
            else:
                print(f"      ❌ Erro ao carregar dados")
        
        # 5. Verificar se há jornada ID 1 de outra empresa
        print("\n5. Verificando jornada ID 1...")
        sql_jornada_id_1 = """
        SELECT 
            jt.id, jt.nome_jornada, jt.tipo_jornada,
            jt.tolerancia_entrada_minutos, jt.padrao, jt.ativa,
            e.razao_social as empresa_nome, e.id as empresa_id
        FROM jornadas_trabalho jt
        JOIN empresas e ON jt.empresa_id = e.id
        WHERE jt.id = 1
        """
        
        jornada_id_1 = db.execute_query(sql_jornada_id_1)
        
        if jornada_id_1:
            jornada = jornada_id_1[0]
            print(f"📋 Jornada ID 1:")
            print(f"   Nome: {jornada['nome_jornada']}")
            print(f"   Empresa: {jornada['empresa_nome']} (ID {jornada['empresa_id']})")
            print(f"   Tipo: {jornada['tipo_jornada']}")
            print(f"   Tolerância: {jornada['tolerancia_entrada_minutos']} minutos")
            print(f"   Padrão: {jornada['padrao']}")
            print(f"   Ativa: {jornada['ativa']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a investigação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    verificar_estrutura_jornadas()
