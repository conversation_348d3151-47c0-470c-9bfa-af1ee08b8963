#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para Corrigir a Tabela de Configurações
Sistema: RLPONTO-WEB v1.0
Data: 09/06/2025
Autor: <PERSON> / AiNexus Tecnologia

Corrige a estrutura da tabela configuracoes_sistema e adiciona configurações em falta
"""

import pymysql
import sys
import os
from datetime import datetime

# Configurações do banco
DB_CONFIG = {
    'host': 'localhost',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

def conectar_banco():
    """Conecta ao banco de dados"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Erro ao conectar ao banco: {e}")
        return None

def atualizar_estrutura_tabela(conn):
    """Atualiza a estrutura da tabela para incluir campos necessários"""
    cursor = conn.cursor()
    
    try:
        print("🔧 Atualizando estrutura da tabela...")
        
        # Adicionar coluna editavel
        cursor.execute("""
            ALTER TABLE configuracoes_sistema 
            ADD COLUMN editavel BOOLEAN DEFAULT TRUE
        """)
        
        # Atualizar enum do tipo
        cursor.execute("""
            ALTER TABLE configuracoes_sistema 
            MODIFY COLUMN tipo ENUM('string', 'integer', 'boolean', 'time', 'json') DEFAULT 'string'
        """)
        
        conn.commit()
        print("✅ Estrutura atualizada!")
        return True
        
    except Exception as e:
        print(f"ℹ️ Estrutura já correta ou erro: {e}")
        return True

def inserir_configuracoes_faltantes(conn):
    """Insere configurações que estão em falta"""
    cursor = conn.cursor()
    
    configuracoes = [
        ('tolerancia_ponto_padrao', '10', 'integer', 'sistema', 'Tolerância padrão de ponto em minutos', True),
        ('tema_sistema', 'claro', 'string', 'interface', 'Tema do sistema', True),
        ('registros_por_pagina', '50', 'integer', 'interface', 'Registros por página', True),
        ('mostrar_fotos_funcionarios', 'true', 'boolean', 'interface', 'Mostrar fotos dos funcionários', True),
        ('formato_data', 'dd/mm/yyyy', 'string', 'interface', 'Formato de data', True),
        ('versao_sistema', '1.0', 'string', 'tecnico', 'Versão do sistema', False),
        ('empresa_desenvolvedora', 'AiNexus Tecnologia', 'string', 'tecnico', 'Empresa desenvolvedora', False),
        ('desenvolvedor', 'Richardson Rodrigues', 'string', 'tecnico', 'Desenvolvedor', False)
    ]
    
    inseridas = 0
    
    for chave, valor, tipo, categoria, descricao, editavel in configuracoes:
        try:
            # Verificar se já existe
            cursor.execute("SELECT id FROM configuracoes_sistema WHERE chave = %s", (chave,))
            if not cursor.fetchone():
                # Inserir nova configuração
                cursor.execute("""
                    INSERT INTO configuracoes_sistema (chave, valor, tipo, categoria, descricao, editavel)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (chave, valor, tipo, categoria, descricao, editavel))
                inseridas += 1
                
        except Exception as e:
            print(f"⚠️ Erro ao inserir {chave}: {e}")
    
    conn.commit()
    print(f"✅ {inseridas} configurações inseridas")
    return True

def verificar_resultado(conn):
    """Verifica o resultado final"""
    cursor = conn.cursor()
    
    # Total de configurações
    cursor.execute("SELECT COUNT(*) FROM configuracoes_sistema")
    total = cursor.fetchone()[0]
    
    # Por categoria
    cursor.execute("SELECT categoria, COUNT(*) FROM configuracoes_sistema GROUP BY categoria")
    por_categoria = cursor.fetchall()
    
    print(f"\n📊 RESULTADO FINAL:")
    print(f"Total de configurações: {total}")
    print(f"Distribuição por categoria:")
    for categoria, count in por_categoria:
        print(f"  - {categoria}: {count} configurações")

def main():
    """Função principal"""
    print("🔧 CORREÇÃO DA TABELA DE CONFIGURAÇÕES")
    print("=" * 50)
    
    # Conectar ao banco
    conn = conectar_banco()
    if not conn:
        sys.exit(1)
    
    try:
        # Atualizar estrutura
        if not atualizar_estrutura_tabela(conn):
            sys.exit(1)
        
        # Inserir configurações
        if not inserir_configuracoes_faltantes(conn):
            sys.exit(1)
        
        # Verificar resultado
        verificar_resultado(conn)
        
        print("\n✅ CORREÇÃO CONCLUÍDA COM SUCESSO!")
        print("O sistema de configurações está agora funcionando.")
        
    finally:
        conn.close()

if __name__ == "__main__":
    main() 