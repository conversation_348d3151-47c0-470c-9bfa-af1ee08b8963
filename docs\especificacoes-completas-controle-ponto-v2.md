# 🚀 ESPECIFICAÇÕES COMPLETAS - CONTROLE DE PONTO V2.0

**Sistema:** RLPONTO-WEB v2.0  
**Data:** 11/07/2025  
**Status:** NOVAS FUNCIONALIDADES E CORREÇÕES

---

## 🆕 **NOVAS FUNCIONALIDADES IMPLEMENTADAS**

### **1. BATIDAS B5 e B6 - HORAS EXTRAS**

#### **B5 - INÍCIO DE HORA EXTRA:**
```python
def validar_b5(funcionario_id, horario_batida):
    # Pré-requisitos obrigatórios
    batidas_dia = obter_batidas_do_dia(funcionario_id)
    tipos_registrados = [b['tipo_registro'] for b in batidas_dia]
    
    # VALIDAÇÃO CRÍTICA: B1, B2, B3, B4 devem estar completos
    requisitos = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']
    
    for req in requisitos:
        if req not in tipos_registrados:
            return {
                'permitido': False,
                'mensagem': f'B5 não permitido: falta {req}. Complete a jornada primeiro.'
            }
    
    # Verificar tolerância de saída
    ultima_saida = obter_ultima_batida(funcionario_id, 'saida')
    jornada = obter_jornada_funcionario(funcionario_id)
    tolerancia_saida = jornada['saida'] + timedelta(minutes=10)
    
    if horario_batida <= tolerancia_saida:
        return {
            'permitido': False,
            'mensagem': f'B5 só permitido após {tolerancia_saida}. Ainda dentro da tolerância de saída.'
        }
    
    return {
        'permitido': True,
        'tipo_registro': 'inicio_extra',
        'status': 'HORA_EXTRA_INICIADA',
        'requer_aprovacao': True  # ⚠️ IMPORTANTE: Sempre requer aprovação RH
    }
```

#### **B6 - FIM DE HORA EXTRA:**
```python
def validar_b6(funcionario_id, horario_batida):
    # Verificar se B5 foi iniciado
    batidas_dia = obter_batidas_do_dia(funcionario_id)
    tipos_registrados = [b['tipo_registro'] for b in batidas_dia]
    
    if 'inicio_extra' not in tipos_registrados:
        return {
            'permitido': False,
            'mensagem': 'B6 inválido: B5 (início de hora extra) não foi registrado.'
        }
    
    # Calcular duração da hora extra
    inicio_extra = obter_ultima_batida(funcionario_id, 'inicio_extra')
    duracao_extra = horario_batida - inicio_extra['data_hora']
    
    return {
        'permitido': True,
        'tipo_registro': 'fim_extra',
        'duracao_extra_minutos': duracao_extra.total_seconds() / 60,
        'status': 'HORA_EXTRA_FINALIZADA',
        'requer_aprovacao': True  # ⚠️ IMPORTANTE: Sempre requer aprovação RH
    }
```

---

## 📋 **SISTEMA DE APROVAÇÃO DE HORAS EXTRAS**

### **TABELA: `aprovacoes_horas_extras`**
```sql
CREATE TABLE aprovacoes_horas_extras (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT NOT NULL,
    data_referencia DATE NOT NULL,
    inicio_extra DATETIME NOT NULL,
    fim_extra DATETIME NOT NULL,
    duracao_minutos INT NOT NULL,
    motivo_hora_extra TEXT,
    porcentagem_extra DECIMAL(5,2) DEFAULT 50.00,
    status_aprovacao ENUM('PENDENTE', 'APROVADO', 'REJEITADO') DEFAULT 'PENDENTE',
    aprovado_por INT NULL,
    data_aprovacao DATETIME NULL,
    observacoes_aprovacao TEXT NULL,
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    atualizado_em DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
    FOREIGN KEY (aprovado_por) REFERENCES usuarios(id),
    INDEX idx_funcionario_data (funcionario_id, data_referencia),
    INDEX idx_status (status_aprovacao)
);
```

### **FLUXO DE APROVAÇÃO:**
```python
def processar_hora_extra_completa(funcionario_id, inicio_extra, fim_extra):
    # 1. Calcular duração
    duracao = fim_extra - inicio_extra
    
    # 2. Criar registro para aprovação
    aprovacao_id = criar_solicitacao_aprovacao({
        'funcionario_id': funcionario_id,
        'data_referencia': inicio_extra.date(),
        'inicio_extra': inicio_extra,
        'fim_extra': fim_extra,
        'duracao_minutos': duracao.total_seconds() / 60,
        'status_aprovacao': 'PENDENTE',
        'motivo_hora_extra': obter_motivo_usuario(),
        'porcentagem_extra': obter_porcentagem_configurada()
    })
    
    # 3. Registrar no histórico do funcionário
    registrar_historico_funcionario({
        'funcionario_id': funcionario_id,
        'tipo_evento': 'HORA_EXTRA_SOLICITADA',
        'data_evento': datetime.now(),
        'detalhes': f'Solicitação #{aprovacao_id} - {duracao.total_seconds()/60}min',
        'status': 'PENDENTE_APROVACAO',
        'requer_aprovacao_rh': True
    })
    
    # 4. Notificar RH
    notificar_rh_nova_solicitacao(aprovacao_id)
    
    return aprovacao_id
```

---

## 📊 **HISTÓRICO DO FUNCIONÁRIO - HORAS EXTRAS**

### **TABELA: `historico_funcionario`**
```sql
CREATE TABLE historico_funcionario (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT NOT NULL,
    tipo_evento ENUM(
        'HORA_EXTRA_SOLICITADA',
        'HORA_EXTRA_APROVADA', 
        'HORA_EXTRA_REJEITADA',
        'BANCO_HORAS_CREDITADO',
        'BANCO_HORAS_DEBITADO',
        'AUSENCIA_REGISTRADA',
        'ATRASO_REGISTRADO'
    ) NOT NULL,
    data_evento DATETIME NOT NULL,
    data_referencia DATE NOT NULL,
    detalhes TEXT NOT NULL,
    valor_minutos INT DEFAULT 0,
    status_aprovacao ENUM('PENDENTE', 'APROVADO', 'REJEITADO', 'NAO_APLICAVEL') DEFAULT 'NAO_APLICAVEL',
    aprovado_por INT NULL,
    observacoes TEXT NULL,
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
    FOREIGN KEY (aprovado_por) REFERENCES usuarios(id),
    INDEX idx_funcionario_data (funcionario_id, data_referencia),
    INDEX idx_tipo_evento (tipo_evento)
);
```

### **REGISTROS AUTOMÁTICOS NO HISTÓRICO:**
```python
def registrar_eventos_historico():
    # Quando B5 é registrado
    registrar_historico({
        'tipo_evento': 'HORA_EXTRA_SOLICITADA',
        'detalhes': f'Início de hora extra às {inicio_extra} - Aguardando aprovação RH',
        'status_aprovacao': 'PENDENTE'
    })
    
    # Quando B6 é registrado
    registrar_historico({
        'tipo_evento': 'HORA_EXTRA_SOLICITADA',
        'detalhes': f'Fim de hora extra às {fim_extra} - Duração: {duracao}min - Aguardando aprovação RH',
        'valor_minutos': duracao_minutos,
        'status_aprovacao': 'PENDENTE'
    })
    
    # Quando RH aprova
    registrar_historico({
        'tipo_evento': 'HORA_EXTRA_APROVADA',
        'detalhes': f'Hora extra aprovada por {nome_aprovador} - {duracao}min creditados',
        'valor_minutos': duracao_minutos,
        'status_aprovacao': 'APROVADO',
        'aprovado_por': aprovador_id
    })
```

---

## 🗓️ **EXPEDIENTE FINS DE SEMANA E FERIADOS**

### **REGRAS ESPECIAIS:**
```python
def validar_expediente_especial(data_referencia, funcionario_id):
    dia_semana = data_referencia.weekday()  # 0=Segunda, 6=Domingo
    eh_feriado = verificar_feriado(data_referencia)
    
    # Sábado (5) ou Domingo (6) ou Feriado
    if dia_semana >= 5 or eh_feriado:
        batidas_dia = obter_batidas_do_dia(funcionario_id, data_referencia)
        
        if not batidas_dia:
            # SEM PONTO: Não conta ausência
            return {
                'conta_ausencia': False,
                'tipo_dia': 'OPCIONAL',
                'mensagem': 'Dia opcional - ausência não computada'
            }
        else:
            # COM PONTO: Aplica todas as regras normais
            return {
                'conta_ausencia': True,
                'tipo_dia': 'EXPEDIENTE_ESPECIAL',
                'aplica_regras_normais': True,
                'porcentagem_extra_disponivel': True,
                'mensagem': 'Expediente iniciado - regras normais aplicadas'
            }
    
    return {
        'conta_ausencia': True,
        'tipo_dia': 'NORMAL',
        'aplica_regras_normais': True
    }
```

---

## 💰 **SISTEMA DE PORCENTAGEM DE HORA EXTRA**

### **TABELA: `configuracoes_hora_extra`**
```sql
CREATE TABLE configuracoes_hora_extra (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT NOT NULL,
    data_referencia DATE NOT NULL,
    tipo_dia ENUM('SABADO', 'DOMINGO', 'FERIADO', 'NORMAL') NOT NULL,
    porcentagem_extra DECIMAL(5,2) NOT NULL DEFAULT 50.00,
    aplicado_por INT NOT NULL,
    observacoes TEXT,
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
    FOREIGN KEY (aplicado_por) REFERENCES usuarios(id),
    UNIQUE KEY unique_funcionario_data (funcionario_id, data_referencia)
);
```

### **MODAL DE PONTO MANUAL - PORCENTAGEM:**
```javascript
// Interface para dias especiais
function mostrarModalPorcentagem(data_referencia, funcionario_id) {
    const dia_semana = data_referencia.getDay();
    const eh_feriado = verificarFeriado(data_referencia);
    
    if (dia_semana === 0 || dia_semana === 6 || eh_feriado) {
        const modal = `
            <div class="modal-porcentagem">
                <h3>⚠️ Expediente Especial</h3>
                <p>Para ${obterTipoDia(data_referencia)}, especifique se for hora extra por porcentagem:</p>
                
                <label>
                    <input type="checkbox" id="aplicar_porcentagem"> 
                    Aplicar porcentagem de hora extra
                </label>
                
                <div id="campo_porcentagem" style="display:none;">
                    <label>Porcentagem (%):</label>
                    <input type="number" id="porcentagem_valor" min="0" max="200" step="0.01" value="50.00">
                </div>
                
                <button onclick="confirmarPonto()">Confirmar Ponto</button>
            </div>
        `;
        
        // Mostrar apenas uma vez por dia
        if (!jaConfiguradoHoje(funcionario_id, data_referencia)) {
            mostrarModal(modal);
        }
    }
}
```

---

## 📈 **RELATÓRIOS APRIMORADOS**

### **RELATÓRIO DIÁRIO DO FUNCIONÁRIO:**
```python
def gerar_relatorio_diario(funcionario_id, data_referencia):
    relatorio = {
        'funcionario': obter_dados_funcionario(funcionario_id),
        'data': data_referencia,
        'batidas': obter_batidas_do_dia(funcionario_id, data_referencia),
        'banco_horas': calcular_banco_horas_dia(funcionario_id, data_referencia),
        'horas_extras': obter_horas_extras_dia(funcionario_id, data_referencia),
        'status_aprovacoes': obter_status_aprovacoes(funcionario_id, data_referencia)
    }
    
    # ⚠️ EXPLÍCITO: Banco de horas negativo
    if relatorio['banco_horas'] < 0:
        relatorio['alertas'] = [
            f"⚠️ BANCO DE HORAS NEGATIVO: {abs(relatorio['banco_horas'])}min",
            "Tempo em débito que deve ser compensado"
        ]
    
    # ⚠️ EXPLÍCITO: Horas extras pendentes
    if relatorio['horas_extras']:
        for extra in relatorio['horas_extras']:
            if extra['status_aprovacao'] == 'PENDENTE':
                relatorio['alertas'].append(
                    f"⏳ HORA EXTRA PENDENTE: {extra['duracao_minutos']}min - Aguardando aprovação RH"
                )
            elif extra['status_aprovacao'] == 'APROVADO':
                relatorio['alertas'].append(
                    f"✅ HORA EXTRA APROVADA: {extra['duracao_minutos']}min - {extra['porcentagem_extra']}%"
                )
    
    return relatorio
```

### **HISTÓRICO COMPLETO:**
```python
def obter_historico_completo_funcionario(funcionario_id, periodo_inicio, periodo_fim):
    return {
        'periodo': f"{periodo_inicio} a {periodo_fim}",
        'eventos': obter_eventos_historico(funcionario_id, periodo_inicio, periodo_fim),
        'resumo_aprovacoes': {
            'pendentes': contar_aprovacoes_pendentes(funcionario_id),
            'aprovadas': contar_aprovacoes_aprovadas(funcionario_id, periodo_inicio, periodo_fim),
            'rejeitadas': contar_aprovacoes_rejeitadas(funcionario_id, periodo_inicio, periodo_fim)
        },
        'banco_horas_acumulado': calcular_banco_horas_periodo(funcionario_id, periodo_inicio, periodo_fim)
    }
```

---

## 🔄 **FLUXO COMPLETO B5/B6 COM APROVAÇÃO**

### **SEQUÊNCIA COMPLETA:**
```
1. Funcionário completa B1, B2, B3, B4 ✅
2. Funcionário registra B5 (após tolerância) ✅
3. Sistema cria solicitação de aprovação 📋
4. Sistema registra no histórico 📝
5. Funcionário registra B6 ✅
6. Sistema calcula duração total ⏱️
7. Sistema atualiza solicitação com duração final 📊
8. RH recebe notificação para aprovação 📧
9. RH aprova/rejeita com observações ✅/❌
10. Sistema atualiza histórico com decisão 📝
11. Relatórios mostram status atual 📈
```

---

## 🎯 **DEFINIÇÃO DE BANCO DE HORAS**

### **CONCEITO:**
> **Banco de Horas = Tudo que sobra do cálculo de 8 horas diárias obrigatórias**

### **CÁLCULO DIÁRIO:**
```python
def calcular_banco_horas_diario(funcionario_id, data_referencia):
    batidas = obter_batidas_do_dia(funcionario_id, data_referencia)
    jornada = obter_jornada_funcionario(funcionario_id)

    # Horas trabalhadas = (B2-B1) + (B4-B3)
    if len(batidas) >= 4:
        periodo_manha = batidas[1]['data_hora'] - batidas[0]['data_hora']
        periodo_tarde = batidas[3]['data_hora'] - batidas[2]['data_hora']
        total_trabalhado = periodo_manha + periodo_tarde
    else:
        total_trabalhado = calcular_parcial(batidas)

    # Jornada obrigatória (8 horas = 480 minutos)
    jornada_obrigatoria = timedelta(hours=8)

    # Banco de horas = Trabalhado - Obrigatório
    banco_horas = total_trabalhado - jornada_obrigatoria

    return {
        'total_trabalhado_minutos': total_trabalhado.total_seconds() / 60,
        'jornada_obrigatoria_minutos': 480,
        'banco_horas_minutos': banco_horas.total_seconds() / 60,
        'status': 'POSITIVO' if banco_horas.total_seconds() >= 0 else 'NEGATIVO'
    }
```

---

## 📋 **ESTRUTURA COMPLETA DO BANCO DE DADOS**

### **ALTERAÇÕES NA TABELA `registros_ponto`:**
```sql
ALTER TABLE registros_ponto
ADD COLUMN tipo_registro ENUM(
    'entrada_manha',
    'saida_almoco',
    'entrada_tarde',
    'saida',
    'inicio_extra',  -- B5
    'fim_extra'      -- B6
) NOT NULL DEFAULT 'entrada_manha';

ALTER TABLE registros_ponto
ADD COLUMN requer_aprovacao BOOLEAN DEFAULT FALSE;

ALTER TABLE registros_ponto
ADD COLUMN aprovacao_id INT NULL;

ALTER TABLE registros_ponto
ADD FOREIGN KEY (aprovacao_id) REFERENCES aprovacoes_horas_extras(id);
```

### **TABELA DE FERIADOS:**
```sql
CREATE TABLE feriados (
    id INT AUTO_INCREMENT PRIMARY KEY,
    data_feriado DATE NOT NULL,
    nome_feriado VARCHAR(100) NOT NULL,
    tipo ENUM('NACIONAL', 'ESTADUAL', 'MUNICIPAL') DEFAULT 'NACIONAL',
    ativo BOOLEAN DEFAULT TRUE,
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,

    UNIQUE KEY unique_data (data_feriado)
);
```

---

## 🔔 **SISTEMA DE NOTIFICAÇÕES**

### **TABELA: `notificacoes_rh`**
```sql
CREATE TABLE notificacoes_rh (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo_notificacao ENUM('HORA_EXTRA_PENDENTE', 'BANCO_HORAS_NEGATIVO', 'AUSENCIA_INJUSTIFICADA') NOT NULL,
    funcionario_id INT NOT NULL,
    aprovacao_id INT NULL,
    titulo VARCHAR(200) NOT NULL,
    mensagem TEXT NOT NULL,
    lida BOOLEAN DEFAULT FALSE,
    data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
    data_leitura DATETIME NULL,

    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
    FOREIGN KEY (aprovacao_id) REFERENCES aprovacoes_horas_extras(id),
    INDEX idx_nao_lidas (lida, data_criacao)
);
```

### **NOTIFICAÇÕES AUTOMÁTICAS:**
```python
def criar_notificacoes_automaticas():
    # Quando B5/B6 são registrados
    def notificar_hora_extra_pendente(aprovacao_id):
        funcionario = obter_funcionario_por_aprovacao(aprovacao_id)
        criar_notificacao({
            'tipo_notificacao': 'HORA_EXTRA_PENDENTE',
            'funcionario_id': funcionario['id'],
            'aprovacao_id': aprovacao_id,
            'titulo': f'Nova solicitação de hora extra - {funcionario["nome_completo"]}',
            'mensagem': f'Funcionário {funcionario["nome_completo"]} solicitou aprovação para hora extra. Clique para revisar.'
        })

    # Quando banco de horas fica muito negativo
    def notificar_banco_negativo(funcionario_id, saldo_minutos):
        if saldo_minutos <= -480:  # Mais de 8 horas negativas
            criar_notificacao({
                'tipo_notificacao': 'BANCO_HORAS_NEGATIVO',
                'funcionario_id': funcionario_id,
                'titulo': f'Banco de horas crítico - {obter_nome_funcionario(funcionario_id)}',
                'mensagem': f'Funcionário com {abs(saldo_minutos)}min negativos no banco de horas. Ação necessária.'
            })
```

---

## 📊 **DASHBOARD RH - APROVAÇÕES**

### **INTERFACE DE APROVAÇÃO:**
```javascript
// Tela de aprovações pendentes
function carregarAprovacoesPendentes() {
    return `
        <div class="dashboard-aprovacoes">
            <h2>🕐 Aprovações de Horas Extras Pendentes</h2>

            <div class="filtros">
                <select id="filtro_funcionario">
                    <option value="">Todos os funcionários</option>
                    <!-- Carregar funcionários -->
                </select>

                <input type="date" id="filtro_data_inicio" placeholder="Data início">
                <input type="date" id="filtro_data_fim" placeholder="Data fim">

                <button onclick="filtrarAprovacoes()">Filtrar</button>
            </div>

            <div class="lista-aprovacoes">
                <!-- Para cada aprovação pendente -->
                <div class="card-aprovacao" data-id="{aprovacao_id}">
                    <div class="header-aprovacao">
                        <h3>{nome_funcionario}</h3>
                        <span class="badge-pendente">PENDENTE</span>
                    </div>

                    <div class="detalhes-aprovacao">
                        <p><strong>Data:</strong> {data_referencia}</p>
                        <p><strong>Período:</strong> {inicio_extra} às {fim_extra}</p>
                        <p><strong>Duração:</strong> {duracao_formatada}</p>
                        <p><strong>Motivo:</strong> {motivo_hora_extra}</p>
                        <p><strong>Porcentagem:</strong> {porcentagem_extra}%</p>
                    </div>

                    <div class="acoes-aprovacao">
                        <button class="btn-aprovar" onclick="aprovarHoraExtra({aprovacao_id})">
                            ✅ Aprovar
                        </button>
                        <button class="btn-rejeitar" onclick="rejeitarHoraExtra({aprovacao_id})">
                            ❌ Rejeitar
                        </button>
                        <button class="btn-detalhes" onclick="verDetalhes({aprovacao_id})">
                            📋 Ver Histórico
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}
```

---

## 🎯 **VALIDAÇÕES CRÍTICAS**

### **SEQUÊNCIA OBRIGATÓRIA B1-B6:**
```python
def validar_sequencia_completa(funcionario_id, novo_tipo):
    batidas_dia = obter_batidas_do_dia(funcionario_id)
    tipos_existentes = [b['tipo_registro'] for b in batidas_dia]

    # Matriz de validação
    sequencias_validas = {
        'entrada_manha': [],  # B1 sempre válido
        'saida_almoco': ['entrada_manha'],  # B2 requer B1
        'entrada_tarde': ['entrada_manha', 'saida_almoco'],  # B3 requer B1,B2
        'saida': ['entrada_manha', 'saida_almoco', 'entrada_tarde'],  # B4 requer B1,B2,B3
        'inicio_extra': ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida'],  # B5 requer B1,B2,B3,B4
        'fim_extra': ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida', 'inicio_extra']  # B6 requer todos
    }

    requisitos = sequencias_validas.get(novo_tipo, [])

    for requisito in requisitos:
        if requisito not in tipos_existentes:
            return {
                'valido': False,
                'erro': f'Tipo {novo_tipo} requer {requisito} anterior',
                'tipos_faltantes': [r for r in requisitos if r not in tipos_existentes]
            }

    return {'valido': True}
```

---

## 📈 **MÉTRICAS E RELATÓRIOS**

### **RELATÓRIO MENSAL DE HORAS EXTRAS:**
```python
def gerar_relatorio_mensal_horas_extras(mes, ano):
    return {
        'periodo': f"{mes}/{ano}",
        'total_solicitacoes': contar_solicitacoes_periodo(mes, ano),
        'aprovadas': contar_aprovadas_periodo(mes, ano),
        'rejeitadas': contar_rejeitadas_periodo(mes, ano),
        'pendentes': contar_pendentes_periodo(mes, ano),
        'total_horas_aprovadas': somar_horas_aprovadas_periodo(mes, ano),
        'funcionarios_com_extras': listar_funcionarios_com_extras(mes, ano),
        'custo_estimado': calcular_custo_horas_extras(mes, ano)
    }
```

---

**Status:** ESPECIFICAÇÕES COMPLETAS V2.0
**Novas Funcionalidades:** B5/B6, Aprovação RH, Histórico, Porcentagem, Fins de Semana
**Inclui:** Banco de Dados, Notificações, Dashboard, Validações, Relatórios
**Pronto para:** Implementação e Deploy
