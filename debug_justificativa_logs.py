#!/usr/bin/env python3
"""
Script para debug dos logs de justificativas em tempo real
"""

import subprocess
import sys
import time
from datetime import datetime

def log(message, level="INFO"):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def main():
    log("=== DEBUG LOGS JUSTIFICATIVAS ===")
    log("Conectando ao servidor para monitorar logs...")
    
    try:
        # Comando para monitorar logs do Flask
        cmd = [
            "ssh", "root@************",
            "tail -f /var/log/nginx/access.log /var/log/nginx/error.log 2>/dev/null || " +
            "journalctl -f -u nginx 2>/dev/null || " +
            "ps aux | grep python | grep app.py"
        ]
        
        log("Executando comando de monitoramento...")
        log("Pressione Ctrl+C para parar")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Ler logs em tempo real
        while True:
            output = process.stdout.readline()
            if output:
                print(f"[LOG] {output.strip()}")
            
            # Verificar se processo ainda está rodando
            if process.poll() is not None:
                break
                
    except KeyboardInterrupt:
        log("Monitoramento interrompido pelo usuário")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        log(f"Erro: {e}", "ERROR")

if __name__ == "__main__":
    main()
