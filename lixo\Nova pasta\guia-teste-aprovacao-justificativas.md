# 🧪 GUIA DE TESTE - SISTEMA DE APROVAÇÃO DE JUSTIFICATIVAS

## 📋 **VISÃO GERAL**

Este guia detalha como testar o sistema de aprovação/reprovação de justificativas com impacto direto no banco de horas e folha de pagamento.

## 🎯 **CENÁRIOS DE TESTE**

### **1. SAÍDA ANTECIPADA - APROVAÇÃO**

#### **Dados do Teste:**
- **Funcionário:** <PERSON> (ID: 1)
- **Data:** 10/07/2025
- **Saída Real:** 12:13
- **Saída Esperada:** 18:00
- **Antecipação:** 347 minutos (5h 47min)

#### **Passos:**
1. <PERSON><PERSON><PERSON>: `/ponto-admin/funcionario/1`
2. Navegar para data: `10/07/2025`
3. Verificar badge: `SAÍDA ANTECIPADA`
4. Verificar justificativa: `"teste"`
5. <PERSON><PERSON>r em `Aprovar Justificativa`
6. Adici<PERSON>r observação: `"Emergência familiar aprovada"`
7. Confirmar aprova<PERSON>

#### **Resultado <PERSON>:**
```sql
-- Banco de Horas ANTES:
saida_antecipada_minutos = 347
saldo_devedor_minutos = 347
saldo_liquido_minutos = -347

-- Banco de Horas DEPOIS:
saida_antecipada_minutos = 0
saldo_devedor_minutos = 0
saldo_liquido_minutos = 0
observacoes = "ABONO: Saída antecipada justificada e aprovada"
```

### **2. SAÍDA ANTECIPADA - REPROVAÇÃO**

#### **Passos:**
1. Mesmo cenário anterior
2. Clicar em `Reprovar Justificativa`
3. Adicionar observação: `"Motivo não justifica saída antecipada"`
4. Confirmar reprovação

#### **Resultado Esperado:**
```sql
-- Banco de Horas:
saida_antecipada_minutos = 347
saldo_devedor_minutos = 347
saldo_liquido_minutos = -347
observacoes = "DESCONTO: Saída antecipada reprovada"
```

### **3. ATRASO - APROVAÇÃO**

#### **Dados do Teste:**
- **Funcionário:** Richardson Cardoso Rodrigues (ID: 1)
- **Data:** 04/07/2025
- **Entrada Real:** 08:52
- **Entrada Esperada:** 08:00
- **Atraso:** 52 minutos

#### **Resultado Esperado:**
```sql
-- Banco de Horas DEPOIS:
atraso_entrada_minutos = 0
saldo_devedor_minutos -= 52
saldo_liquido_minutos += 52
observacoes = "ABONO: Atraso justificado e aprovado"
```

## 🔍 **VERIFICAÇÕES NO BANCO DE DADOS**

### **1. Verificar Justificativas:**
```sql
SELECT * FROM justificativas_ponto 
WHERE funcionario_id = 1 AND data_registro = '2025-07-10';
```

### **2. Verificar Banco de Horas:**
```sql
SELECT * FROM banco_horas 
WHERE funcionario_id = 1 AND data_referencia = '2025-07-10';
```

### **3. Verificar Registros de Ponto:**
```sql
SELECT tipo_registro, TIME(data_hora) as hora, observacoes, status_pontualidade
FROM registros_ponto 
WHERE funcionario_id = 1 AND DATE(data_hora) = '2025-07-10'
ORDER BY data_hora;
```

## 📊 **IMPACTO NO PAGAMENTO**

### **Aprovação:**
- ✅ **Período conta como trabalhado**
- ✅ **Sem desconto na folha**
- ✅ **Saldo neutro/positivo no banco de horas**

### **Reprovação:**
- ❌ **Período não conta como trabalhado**
- ❌ **Desconto proporcional aplicado**
- ❌ **Saldo negativo no banco de horas**

## 🚨 **PONTOS CRÍTICOS PARA TESTAR**

### **1. Migração Automática:**
- Sistema deve migrar justificativas das `observacoes` para tabela `justificativas_ponto`
- Verificar se justificativa aparece corretamente no modal

### **2. Cálculos Precisos:**
- Minutos de antecipação/atraso devem ser calculados corretamente
- Saldos do banco de horas devem ser atualizados matematicamente

### **3. Logs e Auditoria:**
- Todas as ações devem gerar logs no sistema
- Observações devem ser registradas no banco de horas

### **4. Interface do Usuário:**
- Badge de status deve atualizar após aprovação/reprovação
- Justificativa deve permanecer visível
- Feedback visual deve confirmar ação realizada

## 🔧 **COMANDOS ÚTEIS PARA TESTE**

### **Criar Justificativa Manualmente:**
```sql
INSERT INTO justificativas_ponto 
(funcionario_id, data_registro, tipo_justificativa, motivo, status_aprovacao, criado_por)
VALUES (1, '2025-07-10', 'saida_antecipada', 'teste', 'pendente', 1);
```

### **Verificar Status Atual:**
```sql
SELECT 
    f.nome_completo,
    j.data_registro,
    j.tipo_justificativa,
    j.motivo,
    j.status_aprovacao,
    bh.saida_antecipada_minutos,
    bh.saldo_liquido_minutos
FROM justificativas_ponto j
JOIN funcionarios f ON j.funcionario_id = f.id
LEFT JOIN banco_horas bh ON j.funcionario_id = bh.funcionario_id 
    AND j.data_registro = bh.data_referencia
WHERE j.funcionario_id = 1;
```

## ✅ **CHECKLIST DE VALIDAÇÃO**

- [ ] Justificativa carrega corretamente no modal
- [ ] Botões de aprovação/reprovação funcionam
- [ ] Cálculo de minutos está correto
- [ ] Banco de horas é atualizado automaticamente
- [ ] Observações são registradas
- [ ] Logs são gerados
- [ ] Interface reflete mudanças
- [ ] Migração automática funciona
- [ ] Saldos são recalculados corretamente
- [ ] Status de aprovação é persistido

## 🎯 **RESULTADO ESPERADO FINAL**

O sistema deve permitir que gestores aprovem ou reprovem justificativas com impacto direto e automático no banco de horas, garantindo que:

1. **Aprovações** abonem faltas e considerem períodos como trabalhados
2. **Reprovações** apliquem descontos proporcionais
3. **Cálculos** sejam precisos e automáticos
4. **Auditoria** seja completa e rastreável
5. **Interface** seja intuitiva e responsiva

Este sistema é fundamental para a folha de pagamento e deve funcionar com 100% de precisão.
