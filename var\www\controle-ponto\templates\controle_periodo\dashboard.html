{% extends "base.html" %}

{% block title %}{{ titulo }} - RLPONTO-WEB{% endblock %}

{% block extra_css %}
<style>
    /* ========================================
       RLPONTO-WEB VISUAL IDENTITY - v1.1
       SEGUINDO ESPECIFICAÇÕES DO VISUAL.MD
       CONTROLE DE PERÍODO - DESIGN MODERNO
       ======================================== */

    /* Variáveis CSS do Sistema */
    :root {
        /* Cores principais */
        --primary-color: #4fbdba;
        --primary-hover: #26a69a;
        --primary-light: #80cbc4;
        --primary-dark: #00695c;
        
        /* Backgrounds */
        --background-color: #f9fafb;
        --card-background: #ffffff;
        --hover-bg: #f3f4f6;
        
        /* Textos */
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --text-muted: #9ca3af;
        --text-white: #ffffff;
        
        /* Bordas */
        --border-color: #e5e7eb;
        --border-light: #f3f4f6;
        --border-dark: #d1d5db;
        
        /* Estados */
        --success-color: #10b981;
        --success-bg: #dcfce7;
        --warning-color: #f59e0b;
        --warning-bg: #fef3c7;
        --danger-color: #ef4444;
        --danger-bg: #fee2e2;
        --info-color: #3b82f6;
        --info-bg: #dbeafe;
        
        /* Espaçamentos */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
    }

    /* Container Principal */
    .main-container {
        background-color: var(--background-color);
        min-height: 100vh;
        padding: var(--spacing-xl);
    }

    /* Header da Página */
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        border-radius: 12px;
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        color: var(--text-white);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 60%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        transform: rotate(15deg);
    }

    .page-header h1 {
        font-size: 1.875rem;
        font-weight: 700;
        margin-bottom: var(--spacing-sm);
        position: relative;
        z-index: 1;
    }

    .page-header p {
        font-size: 1.125rem;
        opacity: 0.9;
        position: relative;
        z-index: 1;
        margin: 0;
    }

    /* Grid de Estatísticas */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }

    /* Cards de Estatísticas */
    .stat-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: var(--spacing-lg);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border-color: var(--border-dark);
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: var(--text-white);
    }

    .stat-icon.success { background: var(--success-color); }
    .stat-icon.warning { background: var(--warning-color); }
    .stat-icon.danger { background: var(--danger-color); }
    .stat-icon.info { background: var(--info-color); }
    .stat-icon.primary { background: var(--primary-color); }

    .stat-content {
        flex: 1;
    }

    .stat-number {
        font-size: 1.875rem;
        font-weight: 700;
        color: var(--text-primary);
        line-height: 1;
        margin-bottom: var(--spacing-xs);
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
    }

    /* Cards Padrão */
    .standard-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: var(--spacing-lg);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        margin-bottom: var(--spacing-lg);
    }

    .standard-card h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-sm);
        border-bottom: 2px solid var(--primary-color);
    }

    /* Barra de Progresso */
    .progress-container {
        margin: var(--spacing-md) 0;
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: var(--border-light);
        border-radius: 4px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .progress-text {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: var(--spacing-xs);
        text-align: center;
    }

    /* Botões */
    .btn-primary {
        background: var(--primary-color);
        border: 1px solid var(--primary-color);
        color: var(--text-white);
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .btn-primary:hover {
        background: var(--primary-hover);
        border-color: var(--primary-hover);
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        color: var(--text-white);
        text-decoration: none;
    }

    .btn-secondary {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        color: var(--text-primary);
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .btn-secondary:hover {
        background: var(--hover-bg);
        border-color: var(--border-dark);
        color: var(--text-primary);
        text-decoration: none;
    }

    /* Grid de Ações */
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    /* Responsividade */
    @media (max-width: 767px) {
        .main-container {
            padding: var(--spacing-md);
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .page-header {
            padding: var(--spacing-lg);
        }
        
        .page-header h1 {
            font-size: 1.5rem;
        }
        
        .actions-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Lista de Decisões Pendentes */
    .decisoes-lista {
        max-height: 400px;
        overflow-y: auto;
    }

    .decisao-item {
        padding: var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .decisao-item:last-child {
        border-bottom: none;
    }

    .decisao-info h5 {
        margin: 0 0 var(--spacing-xs) 0;
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .decisao-info p {
        margin: 0;
        font-size: 0.75rem;
        color: var(--text-secondary);
    }

    .decisao-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .decisao-badge.horas-extras {
        background: var(--success-bg);
        color: var(--success-color);
    }

    .decisao-badge.atraso {
        background: var(--danger-bg);
        color: var(--danger-color);
    }

    .decisao-badge.excesso-almoco {
        background: var(--warning-bg);
        color: var(--warning-color);
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Header da Página -->
    <div class="page-header">
        <h1>
            <i class="fas fa-calendar-check me-3"></i>
            {{ titulo }}
        </h1>
        <p>
            Período: {{ periodo.inicio.strftime('%d/%m/%Y') }} a {{ periodo.fim.strftime('%d/%m/%Y') }}
            {% if periodo.dias_restantes > 0 %}
                • {{ periodo.dias_restantes }} dias restantes
            {% else %}
                • Período encerrado
            {% endif %}
        </p>
    </div>

    <!-- Grid de Estatísticas -->
    <div class="stats-grid">
        <!-- Funcionários Ativos -->
        <div class="stat-card">
            <div class="stat-icon primary">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ estatisticas.funcionarios_ativos }}</div>
                <div class="stat-label">Funcionários Ativos</div>
            </div>
        </div>

        <!-- Decisões Pendentes -->
        <div class="stat-card">
            <div class="stat-icon warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ estatisticas.decisoes_pendentes }}</div>
                <div class="stat-label">Decisões Pendentes</div>
            </div>
        </div>

        <!-- Horas Extras -->
        <div class="stat-card">
            <div class="stat-icon success">
                <i class="fas fa-plus-circle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ estatisticas.horas_extras_formatadas }}</div>
                <div class="stat-label">Horas Extras</div>
            </div>
        </div>

        <!-- Atrasos -->
        <div class="stat-card">
            <div class="stat-icon danger">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ estatisticas.atrasos_formatados }}</div>
                <div class="stat-label">Total de Atrasos</div>
            </div>
        </div>
    </div>

    <!-- Progresso do Período -->
    <div class="standard-card">
        <h3>
            <i class="fas fa-chart-line me-2"></i>
            Progresso do Período
        </h3>
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" style="width: {{ periodo.percentual_concluido }}%"></div>
            </div>
            <div class="progress-text">
                {{ periodo.percentual_concluido }}% concluído
                ({{ periodo.dias_decorridos }} de {{ periodo.dias_totais }} dias)
            </div>
        </div>

        {% if periodo.dias_restantes > 0 %}
        <p class="text-muted mt-2">
            <i class="fas fa-info-circle me-1"></i>
            Próximo fechamento em {{ periodo.dias_restantes }} dias ({{ periodo.proximo_fechamento.strftime('%d/%m/%Y') }})
        </p>
        {% else %}
        <p class="text-success mt-2">
            <i class="fas fa-check-circle me-1"></i>
            Período encerrado - Pronto para fechamento
        </p>
        {% endif %}
    </div>

    <!-- Decisões Pendentes -->
    {% if decisoes_pendentes %}
    <div class="standard-card">
        <h3>
            <i class="fas fa-tasks me-2"></i>
            Decisões Pendentes Recentes
        </h3>
        <div class="decisoes-lista">
            {% for decisao in decisoes_pendentes %}
            <div class="decisao-item">
                <div class="decisao-info">
                    <h5>{{ decisao.nome_completo }}</h5>
                    <p>{{ decisao.data_referencia.strftime('%d/%m/%Y') }} - {{ decisao.setor or 'Sem setor' }}</p>
                </div>
                <div>
                    <span class="decisao-badge {{ decisao.tipo_pendencia }}">
                        {% if decisao.tipo_pendencia == 'horas_extras' %}
                            +{{ (decisao.minutos_pendentes // 60) }}h {{ (decisao.minutos_pendentes % 60) }}min
                        {% elif decisao.tipo_pendencia == 'atraso' %}
                            -{{ (decisao.minutos_pendentes // 60) }}h {{ (decisao.minutos_pendentes % 60) }}min
                        {% elif decisao.tipo_pendencia == 'excesso_almoco' %}
                            Almoço +{{ (decisao.minutos_pendentes // 60) }}h {{ (decisao.minutos_pendentes % 60) }}min
                        {% elif decisao.tipo_pendencia == 'saida_antecipada' %}
                            Saída -{{ (decisao.minutos_pendentes // 60) }}h {{ (decisao.minutos_pendentes % 60) }}min
                        {% endif %}
                    </span>
                </div>
            </div>
            {% endfor %}
        </div>

        {% if estatisticas.decisoes_pendentes > 10 %}
        <div class="text-center mt-3">
            <small class="text-muted">
                Mostrando 10 de {{ estatisticas.decisoes_pendentes }} decisões pendentes
            </small>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Ações Rápidas -->
    <div class="standard-card">
        <h3>
            <i class="fas fa-bolt me-2"></i>
            Ações Rápidas
        </h3>
        <div class="actions-grid">
            <a href="{{ url_for('controle_periodo.decisoes_diarias') }}" class="btn-primary">
                <i class="fas fa-tasks"></i>
                Classificar Horas
                {% if estatisticas.decisoes_pendentes > 0 %}
                <span class="badge bg-warning text-dark ms-1">{{ estatisticas.decisoes_pendentes }}</span>
                {% endif %}
            </a>

            <a href="{{ url_for('controle_periodo.fechamento_periodo') }}" class="btn-secondary">
                <i class="fas fa-lock"></i>
                Fechamento do Período
            </a>

            <a href="{{ url_for('relatorios.pagina_relatorio_pontos') }}" class="btn-secondary">
                <i class="fas fa-chart-bar"></i>
                Relatórios
            </a>

            <a href="{{ url_for('empresa_principal.funcionarios') }}" class="btn-secondary">
                <i class="fas fa-users"></i>
                Funcionários
            </a>
        </div>
    </div>
</div>

<script>
// JavaScript para funcionalidades dinâmicas
document.addEventListener('DOMContentLoaded', function() {
    // Atualizar estatísticas a cada 5 minutos
    setInterval(function() {
        // Implementar atualização automática se necessário
        console.log('Verificando atualizações...');
    }, 300000); // 5 minutos

    // Animação da barra de progresso
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
        const targetWidth = progressFill.style.width;
        progressFill.style.width = '0%';
        setTimeout(() => {
            progressFill.style.width = targetWidth;
        }, 500);
    }
});
</script>
{% endblock %}
