#!/usr/bin/env python3
"""
Script para testar se a correção do erro 'id' funcionou.
"""

import sys
import os
import traceback

# Adicionar o diretório do projeto ao path
sys.path.append('/var/www/controle-ponto')

try:
    from app_funcionarios import _extrair_dados_formulario, _validar_dados_funcionario, REQUIRED_FIELDS
    from utils.helpers import FormValidator
    from flask import Flask, request
    
    print("✅ Imports realizados com sucesso")
    
    # Criar uma aplicação Flask de teste
    app = Flask(__name__)
    
    def testar_conversao_erros():
        """
        Testa se a conversão de erros está funcionando.
        """
        print("\n🧪 TESTANDO CONVERSÃO DE ERROS")
        print("=" * 50)
        
        # Criar um validator com erros simulados
        validator = FormValidator()
        validator.add_error('id', 'Campo id não deveria estar aqui')
        validator.add_error('nome_completo', 'Nome é obrigatório')
        validator.add_error('cpf', 'CPF inválido')
        
        print("📋 Erros no validator (dicionário):")
        errors_dict = validator.get_errors()
        for field, field_errors in errors_dict.items():
            print(f"  {field}: {field_errors}")
        
        # Testar conversão manual
        errors_list = []
        for field, field_errors in errors_dict.items():
            for error_msg in field_errors:
                field_name = field.replace('_', ' ').title()
                errors_list.append(f"{field_name}: {error_msg}")
        
        print("\n📋 Erros convertidos (lista):")
        for i, error in enumerate(errors_list, 1):
            print(f"  {i}. {error}")
        
        return errors_list
    
    def simular_edicao_com_erro():
        """
        Simula uma edição que causaria o erro 'id'.
        """
        print("\n🔍 SIMULANDO EDIÇÃO COM ERRO 'id'")
        print("=" * 50)
        
        # Dados que causariam erro de validação
        form_data = {
            'nome_completo': '',  # ❌ Vazio - causará erro
            'cpf': '123',         # ❌ Inválido - causará erro
            'empresa_id': '999',  # ❌ Empresa inexistente - causará erro
            'epis[0][id]': '123', # ❌ Campo que estava causando problema
            'epis[0][epi_nome]': 'Capacete'
        }
        
        with app.test_request_context('/', method='POST', data=form_data):
            try:
                print("📋 Extraindo dados do formulário...")
                dados_extraidos = _extrair_dados_formulario()
                
                print("🔍 Validando dados (esperando erros)...")
                validator = FormValidator()
                _validar_dados_funcionario(dados_extraidos, validator)
                
                if validator.has_errors():
                    print("✅ Erros encontrados (como esperado)")
                    
                    # Testar conversão de erros
                    errors_dict = validator.get_errors()
                    print(f"\n📊 Dicionário de erros: {len(errors_dict)} campos com erro")
                    
                    # Aplicar conversão como no código corrigido
                    errors_list = []
                    for field, field_errors in errors_dict.items():
                        for error_msg in field_errors:
                            field_name = field.replace('_', ' ').title()
                            errors_list.append(f"{field_name}: {error_msg}")
                    
                    print(f"📊 Lista de erros: {len(errors_list)} mensagens")
                    
                    print("\n📋 Mensagens de erro formatadas:")
                    for i, error in enumerate(errors_list, 1):
                        print(f"  {i}. {error}")
                    
                    # Verificar se há erro relacionado a 'id'
                    erros_id = [e for e in errors_list if 'id' in e.lower()]
                    if erros_id:
                        print(f"\n⚠️ Erros relacionados a 'id': {len(erros_id)}")
                        for erro in erros_id:
                            print(f"  - {erro}")
                    else:
                        print("\n✅ Nenhum erro relacionado a 'id' encontrado")
                    
                    return True
                else:
                    print("❌ Nenhum erro encontrado (inesperado)")
                    return False
                    
            except Exception as e:
                print(f"❌ ERRO durante teste: {e}")
                print(f"Traceback: {traceback.format_exc()}")
                return False
    
    def verificar_arquivo_corrigido():
        """
        Verifica se o arquivo foi corrigido corretamente.
        """
        print("\n🔍 VERIFICANDO ARQUIVO CORRIGIDO")
        print("=" * 50)
        
        arquivo = '/var/www/controle-ponto/app_funcionarios.py'
        with open(arquivo, 'r', encoding='utf-8') as f:
            conteudo = f.read()
        
        # Verificar se a correção foi aplicada
        if 'errors_list = []' in conteudo:
            print("✅ Conversão de erros encontrada no código")
        else:
            print("❌ Conversão de erros NÃO encontrada")
        
        if 'errors_list.append' in conteudo:
            print("✅ Lógica de conversão implementada")
        else:
            print("❌ Lógica de conversão NÃO implementada")
        
        if "'errors': errors_list" in conteudo:
            print("✅ Lista de erros sendo passada ao template")
        else:
            print("❌ Lista de erros NÃO sendo passada ao template")
    
    if __name__ == "__main__":
        print("🧪 TESTE DA CORREÇÃO DO ERRO 'id'")
        print("=" * 60)
        
        verificar_arquivo_corrigido()
        testar_conversao_erros()
        
        sucesso = simular_edicao_com_erro()
        
        print("\n" + "=" * 60)
        print("📊 RESULTADO DO TESTE")
        print("=" * 60)
        
        if sucesso:
            print("✅ TESTE PASSOU!")
            print("🎯 A correção está funcionando corretamente")
            print("📋 Erros são convertidos de dicionário para lista")
            print("🔧 Template pode iterar sobre os erros sem problemas")
        else:
            print("❌ TESTE FALHOU!")
            print("🔍 Verificar logs para mais detalhes")
        
        print("\n📋 PRÓXIMOS PASSOS:")
        print("1. Testar no navegador: http://10.19.208.31:5000")
        print("2. Ir para funcionários → editar qualquer funcionário")
        print("3. Clicar em 'Salvar' sem alterar nada")
        print("4. Verificar se o erro 'id' foi resolvido")

except ImportError as e:
    print(f"❌ Erro de import: {e}")
except Exception as e:
    print(f"❌ Erro inesperado: {e}")
    print(f"Traceback: {traceback.format_exc()}")
