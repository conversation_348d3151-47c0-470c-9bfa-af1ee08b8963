#!/usr/bin/env python3
"""
MÓDULO DE CONTROLE DE QUALIDADE - RLPONTO-WEB v1.0

Este módulo implementa a estratégia anti-regressão como padrão integral do sistema.
Fornece monitoramento contínuo, acompanhamento profissional e prevenção de falhas.

Autor: <PERSON> - AiNexus Tecnologia
Data: 2025-01-09
© Copyright: 2025 AiNexus Tecnologia. Todos os direitos reservados.
"""

from flask import Blueprint, render_template, request, session, redirect, url_for, jsonify, flash
from datetime import datetime, timedelta
import subprocess
import sys
import os
import shutil
from pathlib import Path
import json
from functools import wraps

# Configuração do Blueprint
quality_control_bp = Blueprint('quality_control', __name__)

def qa_login_required(f):
    """Decorator para proteger rotas do Quality Control - apenas usuário cavalcrod"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'usuario' not in session or session['usuario'] != 'cavalcrod':
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@quality_control_bp.route('/qa/logout')
def qa_logout():
    """Logout do Quality Control"""
    session.clear()
    flash('Logout realizado com sucesso!', 'info')
    return redirect(url_for('login'))

@quality_control_bp.route('/qa/dashboard')
@qa_login_required
def dashboard():
    """Dashboard principal do Quality Control"""
    # Executar verificação automática
    system_status = run_system_check()
    
    # Obter histórico de verificações
    check_history = get_check_history()
    
    # Estatísticas do sistema
    stats = calculate_system_stats()
    
    return render_template('quality_control/dashboard.html', 
                         system_status=system_status,
                         check_history=check_history,
                         stats=stats,
                         last_update=datetime.now())

@quality_control_bp.route('/qa/api/run-check', methods=['POST'])
@qa_login_required
def api_run_check():
    """API para executar verificação manual"""
    try:
        result = run_system_check()
        save_check_to_history(result)
        return jsonify({
            'status': 'success',
            'data': result,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@quality_control_bp.route('/qa/api/backup', methods=['POST'])
@qa_login_required
def api_create_backup():
    """API para criar backup manual"""
    try:
        backup_files = create_system_backup()
        return jsonify({
            'status': 'success',
            'backup_files': backup_files,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@quality_control_bp.route('/qa/api/stats')
@qa_login_required
def api_get_stats():
    """API para obter estatísticas em tempo real"""
    try:
        stats = calculate_system_stats()
        return jsonify({
            'status': 'success',
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

def run_system_check():
    """Executa verificação completa do sistema com detalhes técnicos"""
    results = {
        'timestamp': datetime.now().isoformat(),
        'overall_status': 'checking',
        'checks': [],
        'issues_found': 0,
        'backup_created': False,
        'detailed_issues': [],
        'system_resources': {},
        'file_system_info': {}
    }
    
    try:
        # 1. Criar backup automático com localização
        backup_info = create_system_backup()
        results['backup_created'] = True
        results['backup_info'] = backup_info
        
        # 2. Verificar recursos do sistema
        system_resources = check_system_resources()
        results['system_resources'] = system_resources
        
        # 3. Verificar sistema de arquivos
        filesystem_info = check_filesystem_status()
        results['file_system_info'] = filesystem_info
        
        # 4. Testar carregamento de módulos com detalhes
        module_check = test_module_loading_detailed()
        results['checks'].append(module_check)
        if not module_check['success']:
            results['issues_found'] += len(module_check['failures'])
            results['detailed_issues'].extend(module_check['failures'])
        
        # 5. Testar blueprints com detalhes
        blueprint_check = test_blueprint_registration_detailed()
        results['checks'].append(blueprint_check)
        if not blueprint_check['success']:
            results['issues_found'] += len(blueprint_check['missing'])
            results['detailed_issues'].extend([f"Blueprint não registrado: {bp}" for bp in blueprint_check['missing']])
        
        # 6. Testar banco de dados com métricas
        db_check = test_database_connectivity_detailed()
        results['checks'].append(db_check)
        if not db_check['success']:
            results['issues_found'] += 1
            results['detailed_issues'].append(f"Problema no banco: {db_check.get('error', 'Conexão falhou')}")
        
        # 7. Testar importações cruzadas com mapeamento
        import_check = test_cross_module_imports_detailed()
        results['checks'].append(import_check)
        if not import_check['success']:
            results['issues_found'] += len(import_check['failures'])
            results['detailed_issues'].extend(import_check['failures'])
        
        # 8. Verificar logs do sistema
        log_check = analyze_system_logs()
        results['checks'].append(log_check)
        if log_check.get('warnings', 0) > 0:
            results['detailed_issues'].extend(log_check.get('warning_messages', []))
        
        # 9. Verificar portas e serviços
        port_check = check_port_status()
        results['checks'].append(port_check)
        if not port_check['success']:
            results['issues_found'] += len(port_check['failures'])
            results['detailed_issues'].extend(port_check['failures'])
        
        # Determinar status geral
        results['overall_status'] = 'success' if results['issues_found'] == 0 else 'warning'
        
    except Exception as e:
        results['overall_status'] = 'error'
        results['error'] = str(e)
        results['issues_found'] += 1
        results['detailed_issues'].append(f"Erro geral no sistema: {str(e)}")
    
    return results

def test_module_loading():
    """Testa carregamento de todos os módulos críticos"""
    modules_to_test = [
        ("app", "Aplicação principal"),
        ("app_funcionarios", "Módulo de funcionários"),
        ("app_configuracoes", "Módulo de configurações"),
        ("app_relatorios", "Módulo de relatórios"),
        ("app_status", "Módulo de status"),
        ("utils.database", "Utilitários de banco")
    ]
    
    failures = []
    successes = []
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            successes.append({
                'module': module_name,
                'description': description,
                'status': 'success'
            })
        except Exception as e:
            failures.append({
                'module': module_name,
                'description': description,
                'error': str(e),
                'status': 'failed'
            })
    
    return {
        'name': 'Carregamento de Módulos',
        'type': 'module_loading',
        'success': len(failures) == 0,
        'successes': successes,
        'failures': failures,
        'summary': f"{len(successes)}/{len(modules_to_test)} módulos carregaram com sucesso"
    }

def test_blueprint_registration():
    """Testa registro de blueprints"""
    try:
        import app
        blueprint_names = [bp.name for bp in app.app.blueprints.values()]
        required_blueprints = [
            'funcionarios', 'configuracoes', 'relatorios', 
            'status', 'quality_control', 'epis', 'registro_ponto'
        ]
        
        registered = []
        missing = []
        
        for bp_name in required_blueprints:
            if bp_name in blueprint_names:
                registered.append(bp_name)
            else:
                missing.append(bp_name)
        
        return {
            'name': 'Registro de Blueprints',
            'type': 'blueprint_registration',
            'success': len(missing) == 0,
            'registered': registered,
            'missing': missing,
            'all_blueprints': blueprint_names,
            'summary': f"{len(registered)}/{len(required_blueprints)} blueprints registrados"
        }
        
    except Exception as e:
        return {
            'name': 'Registro de Blueprints',
            'type': 'blueprint_registration',
            'success': False,
            'error': str(e),
            'summary': 'Erro ao verificar blueprints'
        }

def test_database_connectivity():
    """Testa conectividade com banco de dados"""
    try:
        from utils.database import get_db_connection
        
        conn = get_db_connection()
        if conn and hasattr(conn, 'open') and conn.open:
            conn.close()
            return {
                'name': 'Conectividade do Banco',
                'type': 'database_connectivity',
                'success': True,
                'summary': 'Conexão com banco estabelecida com sucesso'
            }
        else:
            return {
                'name': 'Conectividade do Banco',
                'type': 'database_connectivity',
                'success': False,
                'error': 'Falha na conexão',
                'summary': 'Não foi possível estabelecer conexão'
            }
            
    except Exception as e:
        return {
            'name': 'Conectividade do Banco',
            'type': 'database_connectivity',
            'success': False,
            'error': str(e),
            'summary': 'Erro ao testar conexão'
        }

def test_cross_module_imports():
    """Testa importações entre módulos"""
    test_cases = [
        ("app_funcionarios", "utils.database"),
        ("app_configuracoes", "utils.database"),
        ("app_relatorios", "utils.database"),
        ("app", "app_funcionarios"),
        ("app", "app_configuracoes")
    ]
    
    successes = []
    failures = []
    
    for module1, module2 in test_cases:
        try:
            # Limpar imports anteriores
            if module1 in sys.modules:
                del sys.modules[module1]
            if module2 in sys.modules:
                del sys.modules[module2]
            
            # Testar import sequencial
            __import__(module1)
            __import__(module2)
            
            successes.append({
                'modules': f"{module1} → {module2}",
                'status': 'success'
            })
            
        except Exception as e:
            failures.append({
                'modules': f"{module1} → {module2}",
                'error': str(e),
                'status': 'failed'
            })
    
    return {
        'name': 'Importações Cross-Module',
        'type': 'cross_module_imports',
        'success': len(failures) == 0,
        'successes': successes,
        'failures': failures,
        'summary': f"{len(successes)}/{len(test_cases)} importações funcionando"
    }

def create_system_backup():
    """Cria backup dos arquivos críticos do sistema"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = Path("backup-build")
    backup_dir.mkdir(exist_ok=True)
    
    # Arquivos críticos para backup
    critical_files = [
        "app.py",
        "app_funcionarios.py", 
        "app_configuracoes.py",
        "app_relatorios.py",
        "app_status.py",
        "app_quality_control.py",
        "utils/database.py"
    ]
    
    backed_up = []
    
    for file_path in critical_files:
        if Path(file_path).exists():
            backup_name = f"{Path(file_path).stem}_qa_backup_{timestamp}{Path(file_path).suffix}"
            backup_path = backup_dir / backup_name
            shutil.copy2(file_path, backup_path)
            backed_up.append({
                'original': file_path,
                'backup': backup_name,
                'size': backup_path.stat().st_size
            })
    
    # Salvar log do backup
    backup_log = {
        'timestamp': timestamp,
        'files': backed_up,
        'created_by': 'quality_control_system',
        'type': 'automatic_qa_backup'
    }
    
    log_file = backup_dir / f"qa_backup_log_{timestamp}.json"
    with open(log_file, 'w', encoding='utf-8') as f:
        json.dump(backup_log, f, indent=2, ensure_ascii=False)
    
    return backed_up

def get_check_history():
    """Obtém histórico das últimas verificações"""
    history_file = Path("logs/qa_check_history.json")
    
    if history_file.exists():
        try:
            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
                # Retornar últimas 10 verificações
                return sorted(history, key=lambda x: x['timestamp'], reverse=True)[:10]
        except:
            return []
    
    return []

def save_check_to_history(check_result):
    """Salva resultado da verificação no histórico"""
    history_file = Path("logs/qa_check_history.json")
    history_file.parent.mkdir(exist_ok=True)
    
    # Carregar histórico existente
    history = []
    if history_file.exists():
        try:
            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
        except:
            history = []
    
    # Adicionar nova verificação
    history.append(check_result)
    
    # Manter apenas últimas 50 verificações
    history = history[-50:]
    
    # Salvar histórico atualizado
    with open(history_file, 'w', encoding='utf-8') as f:
        json.dump(history, f, indent=2, ensure_ascii=False)

def calculate_system_stats():
    """Calcula estatísticas do sistema"""
    history = get_check_history()
    
    stats = {
        'total_checks': len(history),
        'success_rate': 0,
        'avg_issues': 0,
        'last_check': None,
        'uptime_percentage': 0,
        'trend': 'stable'
    }
    
    if history:
        # Taxa de sucesso
        successful_checks = len([h for h in history if h.get('overall_status') == 'success'])
        stats['success_rate'] = (successful_checks / len(history)) * 100
        
        # Média de problemas
        total_issues = sum([h.get('issues_found', 0) for h in history])
        stats['avg_issues'] = total_issues / len(history)
        
        # Última verificação
        stats['last_check'] = history[0]['timestamp'] if history else None
        
        # Uptime (baseado em verificações bem-sucedidas)
        recent_checks = history[:10]  # Últimas 10 verificações
        recent_success = len([h for h in recent_checks if h.get('overall_status') == 'success'])
        stats['uptime_percentage'] = (recent_success / len(recent_checks)) * 100 if recent_checks else 0
        
        # Tendência (comparar últimas 5 com 5 anteriores)
        if len(history) >= 10:
            recent_avg = sum([h.get('issues_found', 0) for h in history[:5]]) / 5
            previous_avg = sum([h.get('issues_found', 0) for h in history[5:10]]) / 5
            
            if recent_avg < previous_avg:
                stats['trend'] = 'improving'
            elif recent_avg > previous_avg:
                stats['trend'] = 'degrading'
            else:
                stats['trend'] = 'stable'
    
    return stats

# Inicialização automática
def init_quality_control():
    """Inicializa o sistema de controle de qualidade"""
    # Criar diretórios necessários
    Path("logs").mkdir(exist_ok=True)
    Path("backup-build").mkdir(exist_ok=True)
    
    # Executar verificação inicial
    try:
        initial_check = run_system_check()
        save_check_to_history(initial_check)
        print("✅ Sistema de Quality Control inicializado com sucesso!")
        return True
    except Exception as e:
        print(f"❌ Erro ao inicializar Quality Control: {e}")
        return False

# ==============================================
# FUNÇÕES DETALHADAS DE VERIFICAÇÃO TÉCNICA
# ==============================================

def check_system_resources():
    """Verifica recursos do sistema em tempo real"""
    try:
        import psutil
        
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('.')
        
        return {
            'cpu_usage': round(cpu_percent, 2),
            'memory_total_gb': round(memory.total / (1024**3), 2),
            'memory_used_gb': round(memory.used / (1024**3), 2),
            'memory_percent': round(memory.percent, 2),
            'disk_total_gb': round(disk.total / (1024**3), 2),
            'disk_used_gb': round(disk.used / (1024**3), 2),
            'disk_percent': round((disk.used / disk.total) * 100, 2),
            'disk_free_gb': round(disk.free / (1024**3), 2),
            'status': 'healthy' if cpu_percent < 80 and memory.percent < 80 else 'warning'
        }
    except ImportError:
        return {
            'error': 'psutil não instalado - instale com: pip install psutil',
            'status': 'error'
        }
    except Exception as e:
        return {'error': str(e), 'status': 'error'}

def check_filesystem_status():
    """Verifica status detalhado do sistema de arquivos"""
    try:
        current_dir = os.getcwd()
        backup_dir = os.path.join(current_dir, 'backup-build')
        logs_dir = os.path.join(current_dir, 'logs')
        
        # Contar arquivos por tipo
        file_counts = {}
        all_files = [f for f in os.listdir('.') if os.path.isfile(f)]
        
        for file in all_files:
            ext = os.path.splitext(file)[1] or 'sem_extensao'
            file_counts[ext] = file_counts.get(ext, 0) + 1
        
        # Verificar backup directory
        backup_files = []
        if os.path.exists(backup_dir):
            backup_files = [f for f in os.listdir(backup_dir) if os.path.isfile(os.path.join(backup_dir, f))]
        
        return {
            'current_directory': current_dir,
            'backup_directory': backup_dir,
            'backup_exists': os.path.exists(backup_dir),
            'backup_file_count': len(backup_files),
            'backup_files': backup_files[-10:],  # Últimos 10 backups
            'logs_directory': logs_dir,
            'logs_exists': os.path.exists(logs_dir),
            'writable': os.access(current_dir, os.W_OK),
            'file_counts': file_counts,
            'total_files': len(all_files),
            'permissions': oct(os.stat('.').st_mode)[-3:]
        }
    except Exception as e:
        return {'error': str(e), 'status': 'error'}

def test_module_loading_detailed():
    """Testa carregamento de módulos com análise completa"""
    modules_to_test = [
        ("app", "Aplicação principal Flask"),
        ("app_funcionarios", "CRUD de funcionários e biometria"),
        ("app_configuracoes", "Configurações do sistema"),
        ("app_relatorios", "Sistema de relatórios"),
        ("app_status", "Monitoramento de status"),
        ("app_quality_control", "Controle de qualidade"),
        ("utils.database", "Conexões e utilitários de banco"),
        ("utils.auth", "Sistema de autenticação")
    ]
    
    failures = []
    successes = []
    module_analysis = {}
    
    for module_name, description in modules_to_test:
        try:
            module = __import__(module_name)
            
            # Análise detalhada do módulo
            file_path = getattr(module, '__file__', None)
            analysis = {
                'file_path': file_path,
                'exists': os.path.exists(file_path) if file_path else False,
                'size_bytes': os.path.getsize(file_path) if file_path and os.path.exists(file_path) else 0,
                'functions': [attr for attr in dir(module) if callable(getattr(module, attr)) and not attr.startswith('_')],
                'classes': [attr for attr in dir(module) if isinstance(getattr(module, attr), type) and not attr.startswith('_')],
                'doc': (getattr(module, '__doc__', '') or 'Sem documentação')[:200],
                'imports': list(getattr(module, '__dict__', {}).keys())[:20]  # Limitar a 20 primeiros imports
            }
            
            analysis['function_count'] = len(analysis['functions'])
            analysis['class_count'] = len(analysis['classes'])
            analysis['size_kb'] = round(analysis['size_bytes'] / 1024, 2)
            
            successes.append({
                'module': module_name,
                'description': description,
                'status': 'success',
                'analysis': analysis
            })
            module_analysis[module_name] = analysis
            
        except Exception as e:
            error_detail = f"{module_name}: {str(e)}"
            failures.append(error_detail)
    
    return {
        'name': 'Carregamento de Módulos',
        'success': len(failures) == 0,
        'successes': successes,
        'failures': failures,
        'module_analysis': module_analysis,
        'total_tested': len(modules_to_test),
        'passed': len(successes),
        'failed': len(failures),
        'critical_missing': [f for f in failures if any(critical in f for critical in ['app.py', 'database', 'auth'])],
        'summary': f"{len(successes)}/{len(modules_to_test)} módulos carregaram - {len(failures)} falhas"
    }

def test_blueprint_registration_detailed():
    """Testa registro de blueprints com detalhes"""
    try:
        from flask import current_app
        
        expected_blueprints = [
            'funcionarios',
            'configuracoes', 
            'relatorios',
            'status',
            'quality_control'
        ]
        
        registered = list(current_app.blueprints.keys())
        missing = [bp for bp in expected_blueprints if bp not in registered]
        unexpected = [bp for bp in registered if bp not in expected_blueprints]
        
        blueprint_details = {}
        for bp_name, bp in current_app.blueprints.items():
            blueprint_details[bp_name] = {
                'url_prefix': bp.url_prefix,
                'routes': [rule.rule for rule in current_app.url_map.iter_rules() if rule.endpoint.startswith(bp_name)],
                'static_folder': bp.static_folder,
                'template_folder': bp.template_folder
            }
        
        return {
            'name': 'Registro de Blueprints',
            'success': len(missing) == 0,
            'registered': registered,
            'missing': missing,
            'unexpected': unexpected,
            'blueprint_details': blueprint_details,
            'total_expected': len(expected_blueprints),
            'total_registered': len(registered),
            'summary': f"{len(registered)} blueprints registrados - {len(missing)} ausentes"
        }
        
    except Exception as e:
        return {
            'name': 'Registro de Blueprints',
            'success': False,
            'error': str(e),
            'missing': [],
            'summary': f'Erro ao verificar blueprints: {str(e)}'
        }

def test_database_connectivity_detailed():
    """Testa conectividade do banco com métricas detalhadas"""
    try:
        import pymysql
        from datetime import datetime
        
        start_time = datetime.now()
        
        # Teste de conexão
        conn = pymysql.connect(
            host='************',
            user='cavalcrod',
            password='200381',
            database='controle_ponto',
            charset='utf8mb4'
        )
        
        connect_time = (datetime.now() - start_time).total_seconds()
        
        with conn.cursor() as cursor:
            # Verificar tabelas
            cursor.execute("SHOW TABLES")
            tables = [row[0] for row in cursor.fetchall()]
            
            # Verificar usuários
            cursor.execute("SELECT COUNT(*) as total FROM usuarios")
            user_count = cursor.fetchone()[0]
            
            # Verificar funcionários
            cursor.execute("SELECT COUNT(*) as total FROM funcionarios")
            employee_count = cursor.fetchone()[0]
            
            # Verificar status do servidor
            cursor.execute("SHOW STATUS LIKE 'Uptime'")
            uptime = cursor.fetchone()[1]
            
            # Verificar versão
            cursor.execute("SELECT VERSION()")
            db_version = cursor.fetchone()[0]
            
            # Verificar conexões ativas
            cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
            active_connections = cursor.fetchone()[1]
        
        conn.close()
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        
        return {
            'name': 'Conectividade do Banco',
            'success': True,
            'connection_time_ms': round(connect_time * 1000, 2),
            'total_time_ms': round(total_time * 1000, 2),
            'server_info': {
                'host': '************',
                'database': 'controle_ponto',
                'version': db_version,
                'uptime_seconds': uptime,
                'active_connections': active_connections
            },
            'tables': {
                'total_tables': len(tables),
                'table_names': tables
            },
            'data_counts': {
                'usuarios': user_count,
                'funcionarios': employee_count
            },
            'performance': {
                'connection_status': 'fast' if connect_time < 1 else 'slow',
                'health': 'healthy'
            },
            'summary': f'Conectado em {round(connect_time * 1000, 2)}ms - {len(tables)} tabelas encontradas'
        }
        
    except Exception as e:
        return {
            'name': 'Conectividade do Banco',
            'success': False,
            'error': str(e),
            'summary': f'Falha na conexão: {str(e)}'
        }

def test_cross_module_imports_detailed():
    """Testa importações com mapeamento de dependências"""
    import_map = {
        "app → app_funcionarios": ("app", "app_funcionarios"),
        "app → app_configuracoes": ("app", "app_configuracoes"), 
        "app → app_relatorios": ("app", "app_relatorios"),
        "app → app_status": ("app", "app_status"),
        "app → app_quality_control": ("app", "app_quality_control"),
        "funcionarios → utils.database": ("app_funcionarios", "utils.database"),
        "configuracoes → utils.database": ("app_configuracoes", "utils.database"),
        "relatorios → utils.database": ("app_relatorios", "utils.database")
    }
    
    results = {}
    failures = []
    successes = []
    
    for description, (module1, module2) in import_map.items():
        try:
            # Backup de módulos existentes
            backup_modules = {}
            for mod in [module1, module2]:
                if mod in sys.modules:
                    backup_modules[mod] = sys.modules[mod]
                    del sys.modules[mod]
            
            # Testar importação
            start_time = datetime.now()
            __import__(module1)
            __import__(module2)
            import_time = (datetime.now() - start_time).total_seconds()
            
            results[description] = {
                'status': 'success',
                'import_time_ms': round(import_time * 1000, 2),
                'modules': [module1, module2]
            }
            successes.append(description)
            
            # Restaurar módulos
            for mod, backup in backup_modules.items():
                sys.modules[mod] = backup
                
        except Exception as e:
            error_detail = f"{description}: {str(e)}"
            results[description] = {
                'status': 'failed',
                'error': str(e),
                'modules': [module1, module2]
            }
            failures.append(error_detail)
    
    return {
        'name': 'Importações Cross-Module',
        'success': len(failures) == 0,
        'results': results,
        'successes': successes,
        'failures': failures,
        'dependency_map': import_map,
        'total_tested': len(import_map),
        'summary': f"{len(successes)}/{len(import_map)} importações funcionando"
    }

def analyze_system_logs():
    """Analisa logs do sistema para identificar problemas"""
    try:
        logs_dir = Path("logs")
        log_analysis = {
            'files_found': [],
            'errors': [],
            'warnings': [],
            'total_lines': 0,
            'last_entries': []
        }
        
        if logs_dir.exists():
            for log_file in logs_dir.glob("*.log"):
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        log_analysis['files_found'].append({
                            'file': log_file.name,
                            'lines': len(lines),
                            'size_kb': round(log_file.stat().st_size / 1024, 2)
                        })
                        log_analysis['total_lines'] += len(lines)
                        
                        # Analisar últimas 50 linhas para erros
                        recent_lines = lines[-50:] if len(lines) > 50 else lines
                        for line in recent_lines:
                            line_lower = line.lower()
                            if any(error in line_lower for error in ['error', 'exception', 'traceback', 'failed']):
                                log_analysis['errors'].append({
                                    'file': log_file.name,
                                    'message': line.strip()[:200]
                                })
                            elif any(warn in line_lower for warn in ['warning', 'warn', 'deprecated']):
                                log_analysis['warnings'].append({
                                    'file': log_file.name,
                                    'message': line.strip()[:200]
                                })
                        
                        # Últimas 5 entradas de cada arquivo
                        log_analysis['last_entries'].extend([
                            {'file': log_file.name, 'line': line.strip()[:150]} 
                            for line in lines[-5:]
                        ])
                
                except Exception as e:
                    log_analysis['errors'].append({
                        'file': log_file.name,
                        'message': f'Erro ao ler arquivo: {str(e)}'
                    })
        
        return {
            'name': 'Análise de Logs',
            'success': len(log_analysis['errors']) == 0,
            'analysis': log_analysis,
            'error_count': len(log_analysis['errors']),
            'warning_count': len(log_analysis['warnings']),
            'files_analyzed': len(log_analysis['files_found']),
            'warning_messages': [w['message'] for w in log_analysis['warnings']],
            'summary': f"{len(log_analysis['files_found'])} arquivos - {len(log_analysis['errors'])} erros - {len(log_analysis['warnings'])} warnings"
        }
        
    except Exception as e:
        return {
            'name': 'Análise de Logs',
            'success': False,
            'error': str(e),
            'summary': f'Erro na análise: {str(e)}'
        }

def check_port_status():
    """Verifica status das portas essenciais"""
    import socket
    
    ports_to_check = [
        (5000, "Flask Application"),
        (3306, "MySQL Database"),
        (8080, "ZKAgent Service"),
        (22, "SSH"),
        (80, "HTTP"),
        (443, "HTTPS")
    ]
    
    results = {}
    failures = []
    successes = []
    
    for port, description in ports_to_check:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            
            # Testar localhost
            result_local = sock.connect_ex(('localhost', port))
            
            # Testar IP do servidor de banco
            sock2 = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock2.settimeout(2)
            result_remote = sock2.connect_ex(('************', port))
            
            sock.close()
            sock2.close()
            
            port_info = {
                'port': port,
                'description': description,
                'localhost_open': result_local == 0,
                'remote_open': result_remote == 0,
                'status': 'open' if (result_local == 0 or result_remote == 0) else 'closed'
            }
            
            results[f"{port}_{description}"] = port_info
            
            if port_info['status'] == 'open':
                successes.append(f"{port} ({description})")
            else:
                failures.append(f"{port} ({description}) - Porta fechada")
                
        except Exception as e:
            error_detail = f"{port} ({description}): {str(e)}"
            failures.append(error_detail)
            results[f"{port}_{description}"] = {
                'port': port,
                'description': description,
                'error': str(e),
                'status': 'error'
            }
    
    return {
        'name': 'Status de Portas',
        'success': len(failures) == 0,
        'results': results,
        'successes': successes,
        'failures': failures,
        'total_checked': len(ports_to_check),
        'summary': f"{len(successes)}/{len(ports_to_check)} portas abertas"
    }

def create_system_backup():
    """Cria backup com informações detalhadas de localização"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = Path("backup-build")
    backup_dir.mkdir(exist_ok=True)
    
    # Arquivos críticos para backup
    critical_files = [
        "app.py",
        "app_funcionarios.py", 
        "app_configuracoes.py",
        "app_relatorios.py",
        "app_status.py",
        "app_quality_control.py",
        "utils/database.py",
        "utils/auth.py"
    ]
    
    backed_up = []
    backup_summary = {
        'timestamp': timestamp,
        'backup_directory': str(backup_dir.absolute()),
        'created_by': 'quality_control_system',
        'backup_type': 'automatic_qa_backup',
        'files': []
    }
    
    for file_path in critical_files:
        source_path = Path(file_path)
        if source_path.exists():
            backup_name = f"{source_path.stem}_qa_backup_{timestamp}{source_path.suffix}"
            backup_full_path = backup_dir / backup_name
            
            try:
                shutil.copy2(source_path, backup_full_path)
                
                file_info = {
                    'original_path': str(source_path.absolute()),
                    'backup_name': backup_name,
                    'backup_full_path': str(backup_full_path.absolute()),
                    'size_bytes': backup_full_path.stat().st_size,
                    'size_kb': round(backup_full_path.stat().st_size / 1024, 2),
                    'original_modified': source_path.stat().st_mtime,
                    'backup_created': backup_full_path.stat().st_mtime
                }
                
                backed_up.append(file_info)
                backup_summary['files'].append(file_info)
                
            except Exception as e:
                backup_summary['files'].append({
                    'original_path': str(source_path),
                    'error': str(e),
                    'status': 'failed'
                })
    
    # Calcular estatísticas do backup
    total_size = sum([f.get('size_bytes', 0) for f in backup_summary['files'] if 'size_bytes' in f])
    backup_summary['total_files'] = len([f for f in backup_summary['files'] if 'backup_name' in f])
    backup_summary['total_size_bytes'] = total_size
    backup_summary['total_size_mb'] = round(total_size / (1024 * 1024), 2)
    
    # Salvar log detalhado do backup
    log_file = backup_dir / f"qa_backup_log_{timestamp}.json"
    with open(log_file, 'w', encoding='utf-8') as f:
        json.dump(backup_summary, f, indent=2, ensure_ascii=False)
    
    return backup_summary

# ==============================================
# NOVAS APIs PARA FUNCIONALIDADES AVANÇADAS
# ==============================================

@quality_control_bp.route('/qa/api/detailed-check', methods=['POST'])
@qa_login_required
def api_detailed_check():
    """API para verificação detalhada com análise completa"""
    try:
        check_type = request.json.get('type', 'full') if request.is_json else 'full'
        
        if check_type == 'modules':
            result = test_module_loading_detailed()
        elif check_type == 'database':
            result = test_database_connectivity_detailed()
        elif check_type == 'imports':
            result = test_cross_module_imports_detailed()
        elif check_type == 'logs':
            result = analyze_system_logs()
        elif check_type == 'ports':
            result = check_port_status()
        elif check_type == 'resources':
            result = check_system_resources()
        elif check_type == 'filesystem':
            result = check_filesystem_status()
        else:
            result = run_system_check()
        
        return jsonify({
            'status': 'success',
            'data': result,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@quality_control_bp.route('/qa/api/backup-list', methods=['GET'])
@qa_login_required
def api_backup_list():
    """API para listar backups disponíveis"""
    try:
        backup_dir = Path("backup-build")
        backups = []
        
        if backup_dir.exists():
            for file in backup_dir.glob("*.json"):
                if "backup_log" in file.name:
                    try:
                        with open(file, 'r', encoding='utf-8') as f:
                            backup_info = json.load(f)
                            backups.append({
                                'log_file': file.name,
                                'timestamp': backup_info.get('timestamp'),
                                'total_files': backup_info.get('total_files', 0),
                                'size_mb': backup_info.get('total_size_mb', 0),
                                'type': backup_info.get('backup_type', 'unknown')
                            })
                    except:
                        continue
        
        # Ordenar por timestamp (mais recente primeiro)
        backups.sort(key=lambda x: x['timestamp'], reverse=True)
        
        return jsonify({
            'status': 'success',
            'backups': backups,
            'backup_directory': str(backup_dir.absolute()),
            'total_backups': len(backups)
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@quality_control_bp.route('/qa/api/system-info', methods=['GET'])
@qa_login_required
def api_system_info():
    """API para informações completas do sistema"""
    try:
        info = {
            'resources': check_system_resources(),
            'filesystem': check_filesystem_status(),
            'python_version': sys.version,
            'working_directory': os.getcwd(),
            'environment_variables': dict([(k, v) for k, v in os.environ.items() if not any(secret in k.lower() for secret in ['password', 'key', 'secret'])]),
            'installed_modules': list(sys.modules.keys())[:50]  # Primeiros 50 módulos
        }
        
        return jsonify({
            'status': 'success',
            'system_info': info,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error', 
            'message': str(e)
        }), 500
