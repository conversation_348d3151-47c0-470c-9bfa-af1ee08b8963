<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Pontos - Impressão</title>
    <style>
        /* Reset e configurações básicas */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 12pt;
            line-height: 1.4;
            color: #333;
            background: white;
        }

        /* Configurações de impressão */
        @media print {
            body {
                font-size: 10pt;
                line-height: 1.3;
            }
            
            .no-print {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            table {
                page-break-inside: avoid;
            }
            
            tr {
                page-break-inside: avoid;
            }
        }

        /* Cabeçalho */
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #6f42c1;
        }

        .header h1 {
            color: #6f42c1;
            font-size: 24pt;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .header .subtitle {
            color: #666;
            font-size: 14pt;
            margin-bottom: 5px;
        }

        .header .info {
            color: #888;
            font-size: 11pt;
        }

        /* Informações dos filtros */
        .filters-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #6f42c1;
        }

        .filters-info h3 {
            color: #6f42c1;
            margin-bottom: 10px;
            font-size: 14pt;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .filter-item {
            font-size: 11pt;
        }

        .filter-label {
            font-weight: bold;
            color: #555;
        }

        .filter-value {
            color: #333;
        }

        /* Estatísticas */
        .statistics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .stat-card {
            text-align: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .stat-number {
            font-size: 24pt;
            font-weight: bold;
            color: #6f42c1;
            display: block;
        }

        .stat-label {
            font-size: 11pt;
            color: #666;
            margin-top: 5px;
        }

        /* Tabela */
        .table-container {
            margin-bottom: 30px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10pt;
        }

        th, td {
            padding: 8px 6px;
            text-align: left;
            border: 1px solid #ddd;
            vertical-align: top;
        }

        th {
            background: #6f42c1;
            color: white;
            font-weight: bold;
            font-size: 9pt;
            text-transform: uppercase;
        }

        tr:nth-child(even) {
            background: #f8f9fa;
        }

        tr:hover {
            background: #e9ecef;
        }

        /* Status de pontualidade */
        .status-pontual {
            color: #28a745;
            font-weight: bold;
        }

        .status-atraso {
            color: #dc3545;
            font-weight: bold;
        }

        .status-ausente {
            color: #6c757d;
            font-style: italic;
        }

        /* Rodapé */
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10pt;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }

        /* Botões de ação (não imprimem) */
        .action-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .btn {
            padding: 10px 20px;
            margin-left: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12pt;
            text-decoration: none;
            display: inline-block;
        }

        .btn-print {
            background: #6f42c1;
            color: white;
        }

        .btn-back {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        /* Responsivo */
        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .statistics {
                grid-template-columns: repeat(2, 1fr);
            }
            
            table {
                font-size: 9pt;
            }
            
            th, td {
                padding: 6px 4px;
            }
        }
    </style>
</head>
<body>
    <!-- Botões de ação (não imprimem) -->
    <div class="action-buttons no-print">
        <button onclick="window.print()" class="btn btn-print">
            🖨️ Imprimir
        </button>
        <a href="{{ url_for('relatorios.pagina_relatorio_pontos') }}" class="btn btn-back">
            ← Voltar
        </a>
    </div>

    <!-- Cabeçalho -->
    <div class="header">
        <h1>📊 Relatório de Pontos</h1>
        <div class="subtitle">RLPONTO-WEB • Sistema de Controle de Ponto</div>
        <div class="info">Gerado em {{ data_geracao if data_geracao else 'Data não disponível' }}</div>
    </div>

    <!-- Informações dos filtros aplicados -->
    {% if filtros_aplicados %}
    <div class="filters-info">
        <h3>🔍 Filtros Aplicados</h3>
        <div class="filters-grid">
            {% if filtros_aplicados.funcionario_id %}
            <div class="filter-item">
                <span class="filter-label">Funcionário:</span>
                <span class="filter-value">ID {{ filtros_aplicados.funcionario_id }}</span>
            </div>
            {% endif %}
            
            {% if filtros_aplicados.setor %}
            <div class="filter-item">
                <span class="filter-label">Setor:</span>
                <span class="filter-value">{{ filtros_aplicados.setor }}</span>
            </div>
            {% endif %}
            
            {% if filtros_aplicados.data_inicio %}
            <div class="filter-item">
                <span class="filter-label">Data Início:</span>
                <span class="filter-value">{{ filtros_aplicados.data_inicio }}</span>
            </div>
            {% endif %}
            
            {% if filtros_aplicados.data_fim %}
            <div class="filter-item">
                <span class="filter-label">Data Fim:</span>
                <span class="filter-value">{{ filtros_aplicados.data_fim }}</span>
            </div>
            {% endif %}
            
            {% if filtros_aplicados.tipo_registro %}
            <div class="filter-item">
                <span class="filter-label">Tipo:</span>
                <span class="filter-value">{{ filtros_aplicados.tipo_registro }}</span>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Estatísticas -->
    {% if estatisticas %}
    <div class="statistics">
        <div class="stat-card">
            <span class="stat-number">{{ estatisticas.presentes or 0 }}</span>
            <div class="stat-label">Presentes</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ estatisticas.atrasados or 0 }}</span>
            <div class="stat-label">Atrasados</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ estatisticas.ausentes or 0 }}</span>
            <div class="stat-label">Ausentes</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ estatisticas.total or 0 }}</span>
            <div class="stat-label">Total</div>
        </div>
    </div>
    {% endif %}

    <!-- Tabela de registros -->
    <div class="table-container">
        {% if registros %}
        <table>
            <thead>
                <tr>
                    <th>Funcionário</th>
                    <th>Setor</th>
                    <th>Data</th>
                    <th>Horário</th>
                    <th>Tipo</th>
                    <th>Status</th>
                    <th>Observações</th>
                </tr>
            </thead>
            <tbody>
                {% for registro in registros %}
                <tr>
                    <td>{{ registro.nome_funcionario or 'Nome não informado' }}</td>
                    <td>{{ registro.setor_funcionario or 'Não informado' }}</td>
                    <td>{{ registro.data_formatada or 'Data não informada' }}</td>
                    <td>{{ registro.horario_formatado or 'Horário não informado' }}</td>
                    <td>{{ registro.tipo_registro_formatado or registro.tipo_registro or 'Não informado' }}</td>
                    <td>
                        {% if registro.status_pontualidade == 'Pontual' %}
                            <span class="status-pontual">{{ registro.status_pontualidade }}</span>
                        {% elif registro.status_pontualidade in ['Atraso', 'Atrasado'] %}
                            <span class="status-atraso">{{ registro.status_pontualidade }}</span>
                        {% elif registro.status_pontualidade == 'Ausente' %}
                            <span class="status-ausente">{{ registro.status_pontualidade }}</span>
                        {% else %}
                            {{ registro.status_pontualidade or 'Não calculado' }}
                        {% endif %}
                    </td>
                    <td>{{ registro.observacoes or '-' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div style="text-align: center; padding: 40px; color: #666;">
            <h3>📋 Nenhum registro encontrado</h3>
            <p>Não há registros para os filtros aplicados.</p>
        </div>
        {% endif %}
    </div>

    <!-- Rodapé -->
    <div class="footer">
        <p>Sistema de Controle de Ponto - RLPONTO-WEB</p>
        <p>Relatório gerado automaticamente em {{ data_geracao if data_geracao else 'Data não disponível' }}</p>
        {% if registros %}
        <p>Total de {{ registros|length }} registro(s) exibido(s)</p>
        {% endif %}
    </div>

    <script>
        // Auto-foco na janela para facilitar impressão
        window.focus();
        
        // Opcional: Auto-imprimir após carregamento (descomente se desejar)
        // window.addEventListener('load', function() {
        //     setTimeout(() => window.print(), 1000);
        // });
    </script>
</body>
</html>
