{% extends "base.html" %}

{% block title %}Status do Sistema - RLPONTO-WEB{% endblock %}

{% block extra_css %}
<!-- Chart.js para gráficos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    /* 📊 STATUS DASHBOARD - Design Moderno baseado em MCP inspirations */
    
    /* Ocultar sidebar para usuário status */
    .modern-sidebar,
    .sidebar {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }
    
    :root {
        --status-primary: #4fbdba;
        --status-success: #10b981;
        --status-warning: #f59e0b;
        --status-danger: #ef4444;
        --status-info: #3b82f6;
        --status-card-bg: #ffffff;
        --status-bg: #f8fafc;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    }

    .status-dashboard {
        padding: 24px;
        background: var(--status-bg);
        min-height: 100vh;
    }

    .status-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32px;
        padding: 24px;
        background: var(--status-card-bg);
        border-radius: 16px;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .status-title {
        margin: 0;
        font-size: 28px;
        font-weight: 700;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 6px 16px;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-badge.desenvolvimento {
        background: #dbeafe;
        color: #1e40af;
        border: 1px solid #93c5fd;
    }
    
    .status-badge.producao {
        background: #dcfce7;
        color: #166534;
        border: 1px solid #bbf7d0;
    }

    .status-actions {
        display: flex;
        gap: 12px;
    }

    .btn-status {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        border: 1px solid var(--border-color);
        border-radius: 12px;
        background: var(--status-card-bg);
        color: var(--text-primary);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .btn-status:hover {
        background: var(--status-primary);
        color: white;
        border-color: var(--status-primary);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-status.primary {
        background: var(--status-primary);
        color: white;
        border-color: var(--status-primary);
    }

    .btn-status.danger {
        background: var(--status-danger);
        color: white;
        border-color: var(--status-danger);
    }

    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;
        margin-bottom: 32px;
    }

    .status-card {
        background: var(--status-card-bg);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
    }

    .status-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .card-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .card-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
    }

    .card-icon.primary { background: linear-gradient(135deg, var(--status-primary), #26a69a); }
    .card-icon.success { background: linear-gradient(135deg, var(--status-success), #059669); }
    .card-icon.info { background: linear-gradient(135deg, var(--status-info), #2563eb); }
    .card-icon.warning { background: linear-gradient(135deg, var(--status-warning), #d97706); }

    .progress-section {
        margin-bottom: 20px;
    }

    .progress-bar {
        width: 100%;
        height: 12px;
        background: #f1f5f9;
        border-radius: 6px;
        overflow: hidden;
        margin-bottom: 8px;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--status-primary), #26a69a);
        border-radius: 6px;
        transition: width 0.3s ease;
        position: relative;
    }

    .progress-fill::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .progress-text {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        color: var(--text-secondary);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-top: 20px;
    }

    .stat-item {
        text-align: center;
        padding: 16px;
        background: #f8fafc;
        border-radius: 12px;
        border: 1px solid var(--border-color);
    }

    .stat-number {
        font-size: 24px;
        font-weight: 700;
        color: var(--status-primary);
        margin-bottom: 4px;
    }

    .stat-label {
        font-size: 12px;
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .timeline-section {
        background: var(--status-card-bg);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-md);
        margin-bottom: 24px;
    }

    .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
    }

    .timeline-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .timeline-item {
        display: flex;
        gap: 16px;
        padding: 16px 0;
        border-bottom: 1px solid var(--border-color);
    }

    .timeline-item:last-child {
        border-bottom: none;
    }

    .timeline-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: white;
        flex-shrink: 0;
    }

    .timeline-content {
        flex: 1;
    }

    .timeline-title {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 4px;
    }

    .timeline-meta {
        font-size: 12px;
        color: var(--text-secondary);
        display: flex;
        gap: 16px;
    }

    .activities-section {
        background: var(--status-card-bg);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-md);
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 0;
        border-bottom: 1px solid var(--border-color);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    .activity-dot.info { background: var(--status-info); }
    .activity-dot.success { background: var(--status-success); }
    .activity-dot.warning { background: var(--status-warning); }
    .activity-dot.danger { background: var(--status-danger); }

    .activity-content {
        flex: 1;
        font-size: 14px;
        color: var(--text-primary);
    }

    .activity-time {
        font-size: 12px;
        color: var(--text-secondary);
    }

    /* Chart Container */
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }

    /* Responsivo */
    @media (max-width: 768px) {
        .status-dashboard {
            padding: 16px;
        }
        
        .status-header {
            flex-direction: column;
            gap: 16px;
            text-align: center;
        }
        
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Animações suaves */
    .status-card, .timeline-item, .activity-item {
        animation: fadeInUp 0.6s ease forwards;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="status-dashboard">
    <!-- Header -->
    <div class="status-header">
        <div>
            <h1 class="status-title">
                <i class="fas fa-chart-line"></i>
                Status do Sistema
            </h1>
            <div style="margin-top: 8px;">
                <span class="status-badge {% if project_data.status == 'DESENVOLVIMENTO' %}desenvolvimento{% else %}producao{% endif %}">
                    <i class="fas fa-{% if project_data.status == 'DESENVOLVIMENTO' %}code{% else %}check-circle{% endif %}"></i>
                    {{ project_data.status }}
                </span>
            </div>
        </div>
        <div class="status-actions">
            <button class="btn-status primary" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt"></i>
                Atualizar
            </button>
            <a href="{{ url_for('status.logout') }}" class="btn-status danger">
                <i class="fas fa-sign-out-alt"></i>
                Sair
            </a>
        </div>
    </div>

    <!-- Cards de Estatísticas -->
    <div class="dashboard-grid">
        <!-- Card Progresso do Projeto -->
        <div class="status-card">
            <div class="card-header">
                <h3 class="card-title">Progresso do Projeto</h3>
                <div class="card-icon primary">
                    <i class="fas fa-rocket"></i>
                </div>
            </div>
            <div class="progress-section">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ project_data.progresso }}%"></div>
                </div>
                <div class="progress-text">
                    <span>{{ project_data.marcos_concluidos }}/{{ project_data.marcos_total }} marcos</span>
                    <span>{{ project_data.progresso }}%</span>
                </div>
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ project_data.marcos_concluidos }}</div>
                    <div class="stat-label">Concluídos</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ project_data.marcos_total - project_data.marcos_concluidos }}</div>
                    <div class="stat-label">Restantes</div>
                </div>
            </div>
        </div>

        <!-- Card Estatísticas do Projeto -->
        <div class="status-card">
            <div class="card-header">
                <h3 class="card-title">Arquivos do Projeto</h3>
                <div class="card-icon success">
                    <i class="fas fa-code"></i>
                </div>
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ project_stats.arquivos_python }}</div>
                    <div class="stat-label">Python</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ project_stats.arquivos_template }}</div>
                    <div class="stat-label">Templates</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ project_stats.linhas_codigo }}</div>
                    <div class="stat-label">Linhas</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ "%.1f"|format(project_stats.tamanho_total / 1024 / 1024) }} MB</div>
                    <div class="stat-label">Tamanho</div>
                </div>
            </div>
        </div>

        <!-- Card Banco de Dados -->
        <div class="status-card">
            <div class="card-header">
                <h3 class="card-title">Banco de Dados</h3>
                <div class="card-icon info">
                    <i class="fas fa-database"></i>
                </div>
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ db_stats.funcionarios or 0 }}</div>
                    <div class="stat-label">Funcionários</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ db_stats.registros_ponto or 0 }}</div>
                    <div class="stat-label">Registros</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ db_stats.usuarios or 0 }}</div>
                    <div class="stat-label">Usuários</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ db_stats.tamanho_mb or 0 }} MB</div>
                    <div class="stat-label">Tamanho</div>
                </div>
            </div>
        </div>

        <!-- Card Informações do Projeto -->
        <div class="status-card">
            <div class="card-header">
                <h3 class="card-title">Informações</h3>
                <div class="card-icon warning">
                    <i class="fas fa-info-circle"></i>
                </div>
            </div>
            <div style="space-y: 12px;">
                <p style="margin: 8px 0; font-size: 14px; color: var(--text-secondary);">
                    <strong>Projeto:</strong> {{ project_data.nome }}
                </p>
                <p style="margin: 8px 0; font-size: 14px; color: var(--text-secondary);">
                    <strong>Empresa:</strong> {{ project_data.empresa }}
                </p>
                <p style="margin: 8px 0; font-size: 14px; color: var(--text-secondary);">
                    <strong>Desenvolvedor:</strong> {{ project_data.desenvolvedor }}
                </p>
                <p style="margin: 8px 0; font-size: 14px; color: var(--text-secondary);">
                    <strong>Início:</strong> {{ project_data.inicio }}
                </p>
                <p style="margin: 8px 0; font-size: 14px; color: var(--text-secondary);">
                    <strong>Término Previsto:</strong> {{ project_data.termino_previsto }}
                </p>
                <p style="margin: 8px 0; font-size: 14px; color: var(--text-secondary);">
                    <strong>Dias Restantes:</strong> {{ project_data.dias_restantes }} dias
                </p>
                <p style="margin: 8px 0; font-size: 14px; color: var(--text-secondary);">
                    <strong>Última Modificação:</strong> {{ project_data.ultima_atualizacao.strftime('%d/%m/%Y %H:%M') }}
                </p>
            </div>
        </div>
        
        <!-- Card Lógica de Status -->
        <div class="status-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-robot"></i>
                    Lógica de Status Automático
                </h3>
                <div class="card-icon info">
                    <i class="fas fa-cogs"></i>
                </div>
            </div>
            <div style="margin-top: 16px;">
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <div style="display: flex; align-items: center; gap: 12px; padding: 8px; border-radius: 8px; background: {% if project_data.progresso <= 10 %}#f0f9ff{% else %}#f9fafb{% endif %};">
                        <span style="font-size: 20px;">📋</span>
                        <div>
                            <strong style="color: var(--text-primary);">PLANEJAMENTO</strong>
                            <div style="font-size: 12px; color: var(--text-secondary);">0-10% dos marcos</div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px; padding: 8px; border-radius: 8px; background: {% if project_data.progresso >= 11 and project_data.progresso < 80 %}#f0f9ff{% else %}#f9fafb{% endif %};">
                        <span style="font-size: 20px;">💻</span>
                        <div>
                            <strong style="color: var(--text-primary);">DESENVOLVIMENTO</strong>
                            <div style="font-size: 12px; color: var(--text-secondary);">11-79% dos marcos</div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px; padding: 8px; border-radius: 8px; background: {% if project_data.progresso >= 80 and project_data.progresso < 95 %}#f0f9ff{% else %}#f9fafb{% endif %};">
                        <span style="font-size: 20px;">🧪</span>
                        <div>
                            <strong style="color: var(--text-primary);">HOMOLOGAÇÃO</strong>
                            <div style="font-size: 12px; color: var(--text-secondary);">80-94% dos marcos</div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px; padding: 8px; border-radius: 8px; background: {% if project_data.progresso >= 95 %}#f0f9ff{% else %}#f9fafb{% endif %};">
                        <span style="font-size: 20px;">🚀</span>
                        <div>
                            <strong style="color: var(--text-primary);">PRODUÇÃO</strong>
                            <div style="font-size: 12px; color: var(--text-secondary);">≥95% dos marcos</div>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 12px; text-align: center; font-size: 12px; color: var(--text-secondary); background: #f0f9ff; padding: 8px; border-radius: 6px;">
                    <i class="fas fa-info-circle"></i> Status detectado automaticamente baseado em arquivos existentes
                </div>
            </div>
        </div>
        
        <!-- Card Gráfico de Progresso -->
        <div class="status-card" style="grid-column: span 2;">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-area"></i>
                    Progresso do Projeto vs Cronograma
                    {% if project_data.auto_tracking %}
                    <span style="font-size: 12px; color: var(--status-success); margin-left: 8px;">
                        <i class="fas fa-robot"></i> Auto-Tracking
                    </span>
                    {% endif %}
                </h3>
                <div class="card-icon info">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="progressChart"></canvas>
            </div>
            <div class="stats-grid" style="margin-top: 20px;">
                <div class="stat-item">
                    <div class="stat-number">{{ project_data.dias_decorridos }}</div>
                    <div class="stat-label">Dias Decorridos</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ project_data.dias_restantes }}</div>
                    <div class="stat-label">Dias Restantes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ project_data.progresso_tempo }}%</div>
                    <div class="stat-label">Tempo Decorrido</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ project_data.progresso }}%</div>
                    <div class="stat-label">Marcos Concluídos</div>
                </div>
            </div>
        </div>
        
        <!-- Card Marcos Automáticos -->
        {% if project_data.milestone_details %}
        <div class="status-card" style="grid-column: span 2;">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-tasks"></i>
                    Marcos do Projeto (Auto-Detectados)
                </h3>
                <div class="card-icon success">
                    <i class="fas fa-robot"></i>
                </div>
            </div>
            <div class="milestones-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px; margin-top: 16px;">
                {% for milestone_id, milestone in project_data.milestone_details.items() %}
                <div class="milestone-item" style="padding: 16px; background: {% if milestone.completed %}#f0f9ff{% else %}#fafafa{% endif %}; border-radius: 12px; border-left: 4px solid {% if milestone.completed %}var(--status-success){% else %}var(--text-secondary){% endif %};">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <h4 style="margin: 0; font-size: 14px; font-weight: 600; color: var(--text-primary);">
                            {{ milestone.name }}
                        </h4>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            {% if milestone.completed %}
                                <i class="fas fa-check-circle" style="color: var(--status-success);"></i>
                                <span style="font-size: 12px; color: var(--status-success); font-weight: 500;">Concluído</span>
                            {% else %}
                                <i class="fas fa-clock" style="color: var(--text-secondary);"></i>
                                <span style="font-size: 12px; color: var(--text-secondary);">Pendente</span>
                            {% endif %}
                        </div>
                    </div>
                    {% if milestone.completion_date and milestone.completed %}
                    <p style="margin: 0; font-size: 12px; color: var(--text-secondary);">
                        <i class="fas fa-calendar"></i> 
                        Concluído em: {{ milestone.completion_date.strftime('%d/%m/%Y %H:%M') }}
                    </p>
                    {% endif %}
                    <div style="margin-top: 8px;">
                        <span style="font-size: 11px; color: var(--text-secondary); background: #e5e7eb; padding: 4px 8px; border-radius: 6px;">
                            Peso: {{ milestone.weight }}
                        </span>
                    </div>
                </div>
                {% endfor %}
            </div>
            {# 🔒 CORREÇÃO DE SEGURANÇA: Botões administrativos removidos para usuário "status" #}
            {# ⚠️ FUTURO: Implementar sistema de roles admin quando necessário #}
        </div>
        {% endif %}
    </div>

    <!-- Timeline de Arquivos -->
    <div class="timeline-section">
        <div class="timeline-header">
            <h3 class="card-title">
                <i class="fas fa-clock"></i>
                Arquivos Trabalhados Recentemente
            </h3>
        </div>
        <div class="timeline-list">
            {% for arquivo in timeline[:15] %}
            <div class="timeline-item">
                <div class="timeline-icon 
                    {% if arquivo.tipo == 'Python' %}primary
                    {% elif arquivo.tipo == 'Template' %}success
                    {% elif arquivo.tipo == 'Estilo' %}info
                    {% elif arquivo.tipo == 'JavaScript' %}warning
                    {% else %}primary{% endif %}">
                    {% if arquivo.tipo == 'Python' %}
                        <i class="fab fa-python"></i>
                    {% elif arquivo.tipo == 'Template' %}
                        <i class="fas fa-code"></i>
                    {% elif arquivo.tipo == 'Estilo' %}
                        <i class="fab fa-css3-alt"></i>
                    {% elif arquivo.tipo == 'JavaScript' %}
                        <i class="fab fa-js"></i>
                    {% else %}
                        <i class="fas fa-file"></i>
                    {% endif %}
                </div>
                <div class="timeline-content">
                    <div class="timeline-title">{{ arquivo.nome }}</div>
                    <div class="timeline-meta">
                        <span><i class="fas fa-folder"></i> {{ arquivo.arquivo }}</span>
                        <span><i class="fas fa-clock"></i> {{ arquivo.modificado.strftime('%d/%m/%Y %H:%M') }}</span>
                        <span><i class="fas fa-weight-hanging"></i> {{ "%.1f"|format(arquivo.tamanho / 1024) }} KB</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Atividades Recentes -->
    <div class="activities-section">
        <div class="timeline-header">
            <h3 class="card-title">
                <i class="fas fa-list"></i>
                Atividades Recentes
            </h3>
        </div>
        {% for activity in activities %}
        <div class="activity-item">
            <div class="activity-dot 
                {% if activity.level == 'ERROR' %}danger
                {% elif activity.level == 'WARNING' %}warning
                {% elif activity.level == 'INFO' %}info
                {% else %}success{% endif %}"></div>
            <div class="activity-content">{{ activity.message }}</div>
            <div class="activity-time">{{ activity.timestamp }}</div>
        </div>
        {% endfor %}
    </div>
</div>

<script>
// Função para atualizar dados via AJAX
function refreshDashboard() {
    const btn = document.querySelector('.btn-status.primary');
    const icon = btn.querySelector('i');
    
    // Animação de loading
    icon.className = 'fas fa-spinner fa-spin';
    btn.disabled = true;
    
    fetch('{{ url_for("status.refresh_data") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Atualizar dados na interface
            location.reload(); // Por simplicidade, recarregar a página
        } else {
            alert('Erro ao atualizar dados: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro de conexão');
    })
    .finally(() => {
        // Restaurar botão
        icon.className = 'fas fa-sync-alt';
        btn.disabled = false;
    });
}

// Auto-refresh a cada 5 minutos
setInterval(refreshDashboard, 300000);

// Adicionar timestamp de última atualização REAL do projeto
document.addEventListener('DOMContentLoaded', function() {
    // Usar a data de modificação do último arquivo modificado no projeto
    const lastUpdate = new Date('{{ project_data.ultima_atualizacao.isoformat() }}').toLocaleString('pt-BR');
    const header = document.querySelector('.status-header div:first-child');
    const timestamp = document.createElement('small');
    timestamp.style.color = 'var(--text-secondary)';
    timestamp.style.display = 'block';
    timestamp.style.marginTop = '8px';
    timestamp.innerHTML = `<i class="fas fa-clock"></i> Última modificação no projeto: ${lastUpdate}`;
    header.appendChild(timestamp);
    
    // Inicializar gráfico de progresso
    initProgressChart();
});

// Função para verificar marcos automaticamente
function forceCheckMilestones() {
    const btn = event.target;
    const icon = btn.querySelector('i');
    
    // Animação de loading
    icon.className = 'fas fa-spinner fa-spin';
    btn.disabled = true;
    
    fetch('{{ url_for("status.force_milestone_check") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Atualizar página para mostrar novos marcos
            location.reload();
        } else {
            alert('Erro ao verificar marcos: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro de conexão');
    })
    .finally(() => {
        icon.className = 'fas fa-sync';
        btn.disabled = false;
    });
}

// 🛠️ CORREÇÃO CRÍTICA: Função para corrigir timestamps duplicados
function fixTimestamps() {
    if (!confirm('⚠️ CORREÇÃO CRÍTICA\n\nEsta ação irá corrigir os timestamps duplicados dos marcos.\nTodos os marcos terão datas únicas baseadas nos arquivos reais.\n\nContinuar?')) {
        return;
    }
    
    const btn = event.target;
    const icon = btn.querySelector('i');
    
    // Animação de loading
    icon.className = 'fas fa-spinner fa-spin';
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Corrigindo...';
    
    fetch('{{ url_for("status.fix_timestamps") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`✅ CORREÇÃO CONCLUÍDA!\n\n${data.message}\n\nA página será recarregada para mostrar os timestamps corrigidos.`);
            location.reload();
        } else {
            alert('❌ Erro na correção: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('❌ Erro de conexão na correção');
    })
    .finally(() => {
        icon.className = 'fas fa-tools';
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-tools"></i> Corrigir Timestamps';
    });
}

// Gráfico de Progresso vs Cronograma
function initProgressChart() {
    const ctx = document.getElementById('progressChart').getContext('2d');
    
    // Dados do projeto
    const totalDias = {{ project_data.total_dias }};
    const diasDecorridos = {{ project_data.dias_decorridos }};
    const progressoAtual = {{ project_data.progresso }};
    
    // Gerar labels dos dias (semanas)
    const labels = [];
    const progressoPlanejado = [];
    const progressoRealizado = [];
    
    // Gerar dados semanais
    for (let semana = 0; semana <= Math.ceil(totalDias / 7); semana++) {
        const dia = semana * 7;
        if (dia <= totalDias) {
            labels.push(`Semana ${semana + 1}`);
            
            // Progresso planejado linear
            const progressoPlanejadoValue = Math.min(100, (dia / totalDias) * 100);
            progressoPlanejado.push(progressoPlanejadoValue);
            
            // Progresso realizado
            let progressoRealizadoValue = 0;
            if (dia <= diasDecorridos) {
                progressoRealizadoValue = progressoAtual;
            } else if (semana === 0) {
                progressoRealizadoValue = 0;
            } else {
                progressoRealizadoValue = null; // Futuro
            }
            progressoRealizado.push(progressoRealizadoValue);
        }
    }
    
    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Progresso Planejado',
                    data: progressoPlanejado,
                    borderColor: '#6b7280',
                    backgroundColor: 'rgba(107, 114, 128, 0.1)',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    tension: 0.4,
                    fill: false
                },
                {
                    label: 'Progresso Realizado',
                    data: progressoRealizado,
                    borderColor: '#4fbdba',
                    backgroundColor: 'rgba(79, 189, 186, 0.1)',
                    borderWidth: 3,
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#4fbdba',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            family: 'Inter',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    titleColor: '#1f2937',
                    bodyColor: '#6b7280',
                    borderColor: '#e5e7eb',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y.toFixed(1) + '%';
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Cronograma (21/05/2025 - 05/07/2025)',
                        color: '#6b7280'
                    },
                    grid: {
                        color: '#f3f4f6'
                    },
                    ticks: {
                        color: '#6b7280'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Progresso (%)',
                        color: '#6b7280'
                    },
                    min: 0,
                    max: 100,
                    grid: {
                        color: '#f3f4f6'
                    },
                    ticks: {
                        color: '#6b7280',
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
}
</script>
{% endblock %} 