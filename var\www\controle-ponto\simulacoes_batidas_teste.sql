-- ========================================
-- SIMULAÇÕES REALISTAS DE BATIDAS - JOÃO SILVA
-- Data: 11/07/2025
-- Cenários: Erros humanos, esquecimentos, horas extras, fins de semana
-- ========================================

USE controle_ponto;

-- Obter ID do funcionário
SET @funcionario_id = (SELECT id FROM funcionarios WHERE cpf = '123.456.789-00');

-- ========================================
-- SEGUNDA-FEIRA 07/07/2025 - DIA NORMAL
-- ========================================

-- B1: Entrada pontual
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_manha', '2025-07-07 08:00:00', 'biometrico', 'Pontual', 'Entrada pontual');

-- B2: Saída para almoço
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida_almoco', '2025-07-07 12:00:00', 'biometrico', 'Pontual', 'Saída para almoço');

-- B3: Retorno do almoço
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_tarde', '2025-07-07 13:00:00', 'biometrico', 'Pontual', 'Retorno do almoço');

-- B4: Saída normal
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida', '2025-07-07 17:00:00', 'biometrico', 'Pontual', 'Saída normal');

-- ========================================
-- TERÇA-FEIRA 08/07/2025 - ATRASO E HORA EXTRA
-- ========================================

-- B1: Entrada atrasada (trânsito)
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_manha', '2025-07-08 08:45:00', 'biometrico', 'Atrasado', 'Atraso por trânsito');

-- B2: Saída para almoço
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida_almoco', '2025-07-08 12:15:00', 'biometrico', 'Pontual', 'Saída para almoço');

-- B3: Retorno do almoço
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_tarde', '2025-07-08 13:15:00', 'biometrico', 'Pontual', 'Retorno do almoço');

-- B4: Saída normal
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida', '2025-07-08 17:30:00', 'biometrico', 'Pontual', 'Saída com compensação');

-- B5: Início hora extra (projeto urgente)
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes, requer_aprovacao)
VALUES (@funcionario_id, 'inicio_extra', '2025-07-08 17:45:00', 'manual', 'Pontual', 'Projeto urgente - deploy', TRUE);

-- B6: Fim hora extra
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes, requer_aprovacao)
VALUES (@funcionario_id, 'fim_extra', '2025-07-08 19:30:00', 'manual', 'Pontual', 'Deploy concluído', TRUE);

-- ========================================
-- QUARTA-FEIRA 09/07/2025 - ESQUECEU DE BATER
-- ========================================

-- B1: Entrada normal
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_manha', '2025-07-09 08:10:00', 'biometrico', 'Pontual', 'Entrada normal');

-- ESQUECEU B2 (saída almoço) - só lembrou na volta

-- B3: Retorno do almoço (registrado manualmente)
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_tarde', '2025-07-09 14:00:00', 'manual', 'Pontual', 'Esqueci de bater na saída do almoço');

-- B4: Saída normal
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida', '2025-07-09 17:05:00', 'biometrico', 'Pontual', 'Saída normal');

-- Registro manual da saída do almoço (feito depois)
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida_almoco', '2025-07-09 12:30:00', 'manual', 'Pontual', 'Registro manual - esqueci de bater');

-- ========================================
-- QUINTA-FEIRA 10/07/2025 - DIA NORMAL
-- ========================================

-- B1: Entrada pontual
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_manha', '2025-07-10 07:55:00', 'biometrico', 'Pontual', 'Entrada antecipada');

-- B2: Saída para almoço
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida_almoco', '2025-07-10 12:00:00', 'biometrico', 'Pontual', 'Saída para almoço');

-- B3: Retorno do almoço (almoço longo - dentista)
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_tarde', '2025-07-10 14:30:00', 'biometrico', 'Pontual', 'Consulta dentista');

-- B4: Saída compensada
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida', '2025-07-10 18:30:00', 'biometrico', 'Pontual', 'Compensação consulta');

-- ========================================
-- SEXTA-FEIRA 11/07/2025 - SEXTA REDUZIDA
-- ========================================

-- B1: Entrada normal
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_manha', '2025-07-11 08:00:00', 'biometrico', 'Pontual', 'Sexta-feira');

-- B2: Saída para almoço
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida_almoco', '2025-07-11 12:00:00', 'biometrico', 'Pontual', 'Almoço sexta');

-- B3: Retorno do almoço
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_tarde', '2025-07-11 13:00:00', 'biometrico', 'Pontual', 'Retorno almoço');

-- B4: Saída sexta (16:30)
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida', '2025-07-11 16:30:00', 'biometrico', 'Pontual', 'Saída sexta reduzida');

-- ========================================
-- SÁBADO 12/07/2025 - PLANTÃO OPCIONAL
-- ========================================

-- B1: Entrada plantão
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_manha', '2025-07-12 09:00:00', 'manual', 'Pontual', 'Plantão sábado - manutenção');

-- B2: Saída para almoço
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida_almoco', '2025-07-12 12:00:00', 'manual', 'Pontual', 'Pausa almoço');

-- B3: Retorno
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_tarde', '2025-07-12 13:00:00', 'manual', 'Pontual', 'Retorno plantão');

-- B4: Saída plantão
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida', '2025-07-12 15:00:00', 'manual', 'Pontual', 'Fim plantão');

-- ========================================
-- DOMINGO 13/07/2025 - SEM EXPEDIENTE
-- ========================================
-- (Nenhuma batida - dia de descanso)

-- ========================================
-- SEGUNDA-FEIRA 14/07/2025 - PROBLEMA BIOMETRIA
-- ========================================

-- B1: Entrada (biometria falhou, registro manual)
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_manha', '2025-07-14 08:05:00', 'manual', 'Pontual', 'Biometria não funcionou');

-- B2: Saída almoço (biometria ok)
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida_almoco', '2025-07-14 12:00:00', 'biometrico', 'Pontual', 'Saída almoço');

-- B3: Retorno (esqueceu de bater - registrou 2h depois)
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'entrada_tarde', '2025-07-14 13:00:00', 'manual', 'Pontual', 'Registro tardio - esqueci');

-- B4: Saída normal
INSERT INTO registros_ponto (funcionario_id, tipo_registro, data_hora, metodo_registro, status_pontualidade, observacoes)
VALUES (@funcionario_id, 'saida', '2025-07-14 17:00:00', 'biometrico', 'Pontual', 'Saída normal');

SELECT 'Simulações de batidas criadas com sucesso!' as status;

-- Verificar registros criados
SELECT 
    DATE(data_hora) as data,
    TIME(data_hora) as hora,
    tipo_registro,
    metodo_registro,
    status_pontualidade,
    observacoes
FROM registros_ponto 
WHERE funcionario_id = @funcionario_id 
ORDER BY data_hora;
