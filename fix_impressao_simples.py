#!/usr/bin/env python3
"""
Script simples para corrigir a impressão de ponto via SSH
"""

import subprocess
import sys

def run_ssh_command(command):
    """Executa comando SSH"""
    full_command = f'ssh user@************ "{command}"'
    try:
        result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=60)
        return result.stdout, result.stderr, result.returncode
    except subprocess.TimeoutExpired:
        return "", "Timeout", 1

def main():
    print("🔧 Corrigindo página de impressão de ponto...")
    
    # 1. Criar template relatorio_funcionario.html
    print("\n📄 Criando template...")
    
    template_command = '''
    cd /var/www/controle-ponto/templates/ponto_admin
    
    # Verificar se já existe
    if [ -f "relatorio_funcionario.html" ]; then
        echo "Template já existe, fazendo backup..."
        cp relatorio_funcionario.html relatorio_funcionario.html.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # Criar template básico
    cat > relatorio_funcionario.html << 'TEMPLATE_EOF'
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Ponto - {{ funcionario.nome_completo }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 15px; margin-bottom: 20px; }
        .funcionario-info { background: #f8f9fa; padding: 15px; margin-bottom: 20px; }
        .registros-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .registros-table th, .registros-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        .registros-table th { background: #f8f9fa; }
        .action-buttons { position: fixed; top: 20px; right: 20px; }
        .btn { padding: 10px 15px; margin: 5px; text-decoration: none; border-radius: 5px; }
        .btn-print { background: #28a745; color: white; }
        .btn-back { background: #6c757d; color: white; }
        @media print { .action-buttons { display: none; } }
    </style>
</head>
<body>
    <div class="action-buttons">
        <button onclick="window.print()" class="btn btn-print">🖨️ Imprimir</button>
        <a href="{{ url_for('ponto_admin.detalhes_funcionario', funcionario_id=funcionario.id) }}" class="btn btn-back">← Voltar</a>
    </div>

    <div class="header">
        <h1>RELATÓRIO DE REGISTROS DE PONTO</h1>
        <p>{{ funcionario.empresa_nome or 'EMPRESA' }}</p>
        <p>Gerado em: {{ data_atual.strftime('%d/%m/%Y %H:%M') if data_atual else '' }}</p>
    </div>

    <div class="funcionario-info">
        <h2>{{ funcionario.nome_completo }}</h2>
        <p><strong>Matrícula:</strong> {{ funcionario.matricula or 'N/A' }}</p>
        <p><strong>Cargo:</strong> {{ funcionario.cargo or 'N/A' }}</p>
        <p><strong>CPF:</strong> {{ funcionario.cpf or 'N/A' }}</p>
    </div>

    {% if data_inicio or data_fim %}
    <div style="text-align: center; background: #e3f2fd; padding: 10px; margin-bottom: 20px;">
        Período: {{ data_inicio or 'Início' }} até {{ data_fim or data_atual.strftime('%d/%m/%Y') if data_atual else '' }}
    </div>
    {% endif %}

    <h3>Registros de Ponto ({{ registros|length }} registros)</h3>
    
    {% if registros %}
    <table class="registros-table">
        <thead>
            <tr>
                <th>Data</th>
                <th>Entrada</th>
                <th>Saída Almoço</th>
                <th>Retorno Almoço</th>
                <th>Saída</th>
                <th>Horas Trabalhadas</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            {% for registro in registros %}
            <tr>
                <td>{{ registro.data_registro.strftime('%d/%m/%Y') if registro.data_registro else 'N/A' }}</td>
                <td>{{ registro.entrada.strftime('%H:%M') if registro.entrada else '-' }}</td>
                <td>{{ registro.saida_almoco.strftime('%H:%M') if registro.saida_almoco else '-' }}</td>
                <td>{{ registro.retorno_almoco.strftime('%H:%M') if registro.retorno_almoco else '-' }}</td>
                <td>{{ registro.saida.strftime('%H:%M') if registro.saida else '-' }}</td>
                <td>{{ registro.horas_trabalhadas or '-' }}</td>
                <td>{{ registro.status_texto or 'Incompleto' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p style="text-align: center; color: #666;">Nenhum registro encontrado para o período selecionado.</p>
    {% endif %}

    <div style="margin-top: 30px; text-align: center; font-size: 10pt; color: #666;">
        <p>Sistema de Controle de Ponto - RLPONTO-WEB</p>
    </div>
</body>
</html>
TEMPLATE_EOF
    
    echo "✅ Template criado com sucesso"
    '''
    
    stdout, stderr, returncode = run_ssh_command(template_command)
    print(stdout)
    if stderr:
        print(f"Erro: {stderr}")
    
    # 2. Adicionar rota no app_ponto_admin.py
    print("\n🔧 Adicionando rota de impressão...")
    
    route_command = '''
    cd /var/www/controle-ponto
    
    # Fazer backup
    cp app_ponto_admin.py app_ponto_admin.py.backup.$(date +%Y%m%d_%H%M%S)
    
    # Verificar se a rota já existe
    if grep -q "/funcionario/<int:funcionario_id>/imprimir" app_ponto_admin.py; then
        echo "✅ Rota de impressão já existe"
    else
        # Adicionar a rota após a linha que contém "# ROTAS PARA SISTEMA DE JUSTIFICATIVAS"
        sed -i '/# ROTAS PARA SISTEMA DE JUSTIFICATIVAS/i\\
\\
@ponto_admin_bp.route('\''/funcionario/<int:funcionario_id>/imprimir'\'')\
@require_login\
def imprimir_ponto_funcionario(funcionario_id):\
    """Página de impressão de ponto de um funcionário"""\
    try:\
        from datetime import datetime\
        \
        funcionario = get_funcionario_detalhes(funcionario_id)\
        if not funcionario:\
            flash('\''Funcionário não encontrado'\'', '\''error'\'')\
            return redirect(url_for('\''ponto_admin.index'\''))\
\
        # Parâmetros de filtro\
        data_inicio = request.args.get('\''data_inicio'\'')\
        data_fim = request.args.get('\''data_fim'\'')\
\
        registros = get_registros_ponto_funcionario(funcionario_id, data_inicio, data_fim)\
\
        return render_template('\''ponto_admin/relatorio_funcionario.html'\'',\
                             funcionario=funcionario,\
                             registros=registros,\
                             data_inicio=data_inicio,\
                             data_fim=data_fim,\
                             data_atual=datetime.now())\
    except Exception as e:\
        logger.error(f"Erro ao gerar página de impressão do funcionário {funcionario_id}: {e}")\
        flash('\''Erro ao gerar página de impressão'\'', '\''error'\'')\
        return redirect(url_for('\''ponto_admin.index'\''))\
' app_ponto_admin.py
        
        echo "✅ Rota de impressão adicionada"
    fi
    
    # Atualizar a função relatorio_funcionario para incluir data_atual
    if ! grep -q "data_atual=datetime.now()" app_ponto_admin.py; then
        # Adicionar import datetime na função relatorio_funcionario
        sed -i '/def relatorio_funcionario(funcionario_id):/,/try:/ {
            /try:/ a\\
        from datetime import datetime
        }' app_ponto_admin.py
        
        # Adicionar data_atual no render_template da função relatorio_funcionario
        sed -i '/return render_template.*relatorio_funcionario.html/,/data_fim=data_fim)/ {
            s/data_fim=data_fim)/data_fim=data_fim,\
                             data_atual=datetime.now())/
        }' app_ponto_admin.py
        
        echo "✅ Função relatorio_funcionario atualizada"
    else
        echo "✅ Função relatorio_funcionario já possui data_atual"
    fi
    '''
    
    stdout, stderr, returncode = run_ssh_command(route_command)
    print(stdout)
    if stderr:
        print(f"Erro: {stderr}")
    
    # 3. Reiniciar serviço
    print("\n🔄 Reiniciando serviço...")
    
    restart_command = '''
    sudo systemctl restart controle-ponto
    sleep 3
    sudo systemctl status controle-ponto --no-pager -l | head -10
    '''
    
    stdout, stderr, returncode = run_ssh_command(restart_command)
    print(stdout)
    
    # 4. Testar
    print("\n✅ Testando funcionalidade...")
    
    test_command = '''
    curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/ponto-admin/
    '''
    
    stdout, stderr, returncode = run_ssh_command(test_command)
    status_code = stdout.strip()
    
    if status_code == '200':
        print("✅ Serviço funcionando corretamente!")
        print("\n🎉 CORREÇÃO CONCLUÍDA!")
        print("📝 A página de impressão agora está disponível em:")
        print("   http://************/ponto-admin/funcionario/<id>/imprimir")
    else:
        print(f"⚠️ Status HTTP: {status_code}")

if __name__ == "__main__":
    main()
