#!/usr/bin/env python3
import paramiko
import hashlib

def fix_admin_user():
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🔧 CORRIGINDO USUÁRIO ADMIN")
        print("=" * 50)
        
        # 1. Verificar estrutura atual
        print("1. Verificando estrutura da tabela usuarios...")
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u root -p@Ric6109 controle_ponto -e "DESCRIBE usuarios;"
        ''')
        
        structure = stdout.read().decode()
        print("Estrutura atual:")
        print(structure)
        
        # 2. Criar usuário admin com os campos corretos
        print("\n2. Criando usuário admin...")
        
        # Gerar hash da senha usando SHA256 (como o sistema usa)
        senha_hash = hashlib.sha256('@Ric6109'.encode()).hexdigest()
        
        create_admin_sql = f'''
        mysql -u root -p@Ric6109 controle_ponto -e "
        INSERT INTO usuarios (
            usuario, 
            nome_completo, 
            email, 
            senha, 
            nivel_acesso, 
            ativo
        ) VALUES (
            'admin', 
            'Administrador do Sistema', 
            '<EMAIL>', 
            '{senha_hash}', 
            'admin', 
            1
        ) ON DUPLICATE KEY UPDATE 
        senha = '{senha_hash}',
        nivel_acesso = 'admin',
        ativo = 1;"
        '''
        
        stdin, stdout, stderr = ssh.exec_command(create_admin_sql)
        create_result = stdout.read().decode()
        create_error = stderr.read().decode()
        
        if "Warning" in create_error and "ERROR" not in create_error:
            print("✅ Usuário admin criado com sucesso (warning de senha é normal)")
        elif create_error:
            print(f"Erro: {create_error}")
        else:
            print("✅ Usuário admin criado com sucesso")
        
        # 3. Verificar se foi criado
        print("\n3. Verificando usuário criado...")
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u root -p@Ric6109 controle_ponto -e "
        SELECT id, usuario, nome_completo, email, nivel_acesso, ativo 
        FROM usuarios 
        WHERE usuario = 'admin';"
        ''')
        
        verification = stdout.read().decode()
        print("Usuário admin:")
        print(verification)
        
        # 4. Testar hash da senha
        print("\n4. Testando hash da senha...")
        stdin, stdout, stderr = ssh.exec_command(f'''
        mysql -u root -p@Ric6109 controle_ponto -e "
        SELECT 
            usuario, 
            senha = '{senha_hash}' as senha_correta,
            nivel_acesso
        FROM usuarios 
        WHERE usuario = 'admin';"
        ''')
        
        hash_test = stdout.read().decode()
        print("Teste de hash:")
        print(hash_test)
        
        # 5. Verificar função de login no código
        print("\n5. Verificando função de login...")
        stdin, stdout, stderr = ssh.exec_command('grep -A 20 "cursor.execute.*usuarios.*WHERE.*usuario" /var/www/controle-ponto/app.py')
        login_code = stdout.read().decode()
        print("Código de login:")
        print(login_code)
        
        # 6. Reiniciar serviço
        print("\n6. Reiniciando serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl restart controle-ponto')
        
        # 7. Verificar status
        stdin, stdout, stderr = ssh.exec_command('systemctl is-active controle-ponto')
        status = stdout.read().decode().strip()
        print(f"Status do serviço: {status}")
        
        ssh.close()
        
        print("\n" + "=" * 50)
        print("🎉 CORREÇÃO CONCLUÍDA!")
        print("=" * 50)
        print("✅ Usuário admin criado")
        print("✅ Senha configurada corretamente")
        print("✅ Nível de acesso: admin")
        print("✅ Serviço reiniciado")
        print("\n🔑 CREDENCIAIS:")
        print("Usuário: admin")
        print("Senha: @Ric6109")
        print("\n🌐 TESTE AGORA:")
        print("1. Acesse: http://************:5000/login")
        print("2. Use as credenciais acima")
        print("3. Vá para Configurações")
        print("4. Teste a aba Empresas")
        
    except Exception as e:
        print(f"❌ Erro durante a correção: {e}")

if __name__ == "__main__":
    fix_admin_user()
