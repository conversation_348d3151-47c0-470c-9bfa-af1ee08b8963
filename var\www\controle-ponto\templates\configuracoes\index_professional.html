{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    /* DESIGN SYSTEM PROFISSIONAL - BASEADO EM @21ST-DEV/MAGIC */
    :root {
        --config-primary: #0f172a;
        --config-secondary: #64748b;
        --config-accent: #3b82f6;
        --config-success: #10b981;
        --config-warning: #f59e0b;
        --config-danger: #ef4444;
        --config-surface: #ffffff;
        --config-muted: #f8fafc;
        --config-border: #e2e8f0;
        --config-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --config-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    }

    /* LAYOUT PRINCIPAL */
    .config-container {
        padding: 2rem 0;
        max-width: 1200px;
        margin: 0 auto;
    }

    /* HEADER PROFISSIONAL */
    .config-header {
        background: linear-gradient(135deg, var(--config-primary) 0%, var(--config-secondary) 100%);
        color: white;
        padding: 2rem;
        border-radius: 0.75rem;
        margin-bottom: 2rem;
        box-shadow: var(--config-shadow-lg);
    }

    .config-header h1 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 600;
        letter-spacing: -0.025em;
    }

    .config-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 1rem;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        backdrop-filter: blur(8px);
    }

    /* CARDS DE ESTATÍSTICAS */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: var(--config-shadow);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        text-align: center;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--config-shadow-lg);
        border-color: var(--config-accent);
    }

    .stat-card .icon {
        width: 3rem;
        height: 3rem;
        background: var(--config-muted);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: var(--config-accent);
        font-size: 1.25rem;
    }

    .stat-card .value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--config-primary);
        margin-bottom: 0.25rem;
    }

    .stat-card .label {
        color: var(--config-secondary);
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* TABS PROFISSIONAIS */
    .config-tabs {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.75rem;
        box-shadow: var(--config-shadow);
        overflow: hidden;
    }

    .nav-tabs {
        background: var(--config-muted);
        border-bottom: 1px solid var(--config-border);
        padding: 0;
        margin: 0;
        display: flex;
    }

    .nav-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 1.5rem;
        color: var(--config-secondary);
        background: transparent;
        border: none;
        font-weight: 500;
        font-size: 0.875rem;
        text-decoration: none;
        transition: all 0.2s ease;
        border-radius: 0;
        position: relative;
    }

    .nav-link:hover {
        background: rgba(59, 130, 246, 0.05);
        color: var(--config-accent);
    }

    .nav-link.active {
        background: var(--config-surface);
        color: var(--config-accent);
        border-bottom: 2px solid var(--config-accent);
    }

    .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--config-accent);
    }

    /* CONTEÚDO DAS TABS */
    .tab-content {
        padding: 2rem;
        min-height: 400px;
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
    }

    /* SEÇÕES DE CONFIGURAÇÃO */
    .config-section {
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--config-primary);
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--config-border);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* GRID DE AÇÕES */
    .action-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    /* CARDS DE AÇÃO */
    .action-card {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .action-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--config-shadow-lg);
        border-color: var(--config-accent);
    }

    .action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--config-accent), var(--config-success));
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .action-card:hover::before {
        opacity: 1;
    }

    .action-card .icon {
        width: 3.5rem;
        height: 3.5rem;
        background: var(--config-muted);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: var(--config-accent);
        font-size: 1.5rem;
        transition: all 0.2s ease;
    }

    .action-card:hover .icon {
        background: var(--config-accent);
        color: white;
        transform: scale(1.1);
    }

    .action-card h5 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--config-primary);
        margin-bottom: 0.5rem;
    }

    .action-card p {
        color: var(--config-secondary);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    /* BOTÕES PROFISSIONAIS */
    .btn-professional {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.625rem 1.25rem;
        background: var(--config-accent);
        color: white;
        text-decoration: none;
        border-radius: 0.5rem;
        font-weight: 500;
        font-size: 0.875rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .btn-professional:hover {
        background: #2563eb;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-success {
        background: var(--config-success);
    }

    .btn-success:hover {
        background: #059669;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    .btn-warning {
        background: var(--config-warning);
    }

    .btn-warning:hover {
        background: #d97706;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
    }

    .btn-outline {
        background: transparent;
        color: var(--config-accent);
        border: 1px solid var(--config-accent);
    }

    .btn-outline:hover {
        background: var(--config-accent);
        color: white;
    }

    /* DESTACAR SISTEMA BIOMÉTRICO */
    .biometric-card {
        border-color: var(--config-success);
        background: linear-gradient(145deg, #f0fdf4, #dcfce7);
    }

    .biometric-card .icon {
        background: var(--config-success);
        color: white;
    }

    .biometric-card::before {
        background: var(--config-success);
        opacity: 1;
    }

    /* RESPONSIVIDADE */
    @media (max-width: 768px) {
        .config-container {
            padding: 1rem;
        }

        .config-header {
            padding: 1.5rem;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .tab-content {
            padding: 1.5rem;
        }

        .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.8rem;
        }
    }

    /* ANIMAÇÕES SUTIS */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .tab-pane.active {
        animation: fadeIn 0.3s ease-out;
    }

    /* AJUSTES DE ÍCONES */
    .fas, .far {
        font-size: inherit;
    }
</style>
{% endblock %}

{% block content %}
<div class="config-container">
    <!-- Header Profissional -->
    <div class="config-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1><i class="fas fa-cog me-2"></i>Configurações do Sistema</h1>
                <p>Painel de administração e configuração do RLPONTO-WEB</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="status-badge">
                    <i class="fas fa-circle"></i>Sistema Online
                </div>
            </div>
        </div>
    </div>

    <!-- Estatísticas do Sistema -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="icon"><i class="fas fa-building"></i></div>
            <div class="value">{{ estatisticas.total_empresas or 1 }}</div>
            <div class="label">Empresas Ativas</div>
        </div>
        <div class="stat-card">
            <div class="icon"><i class="fas fa-users"></i></div>
            <div class="value">{{ estatisticas.total_funcionarios or 5 }}</div>
            <div class="label">Funcionários</div>
        </div>
        <div class="stat-card">
            <div class="icon"><i class="fas fa-clock"></i></div>
            <div class="value">{{ estatisticas.total_horarios or 1 }}</div>
            <div class="label">Horários de Trabalho</div>
        </div>
        <div class="stat-card">
            <div class="icon"><i class="fas fa-fingerprint"></i></div>
            <div class="value">{{ estatisticas.registros_mes or 19 }}</div>
            <div class="label">Registros Este Mês</div>
        </div>
    </div>

    <!-- Tabs de Configuração -->
    <div class="config-tabs">
        <ul class="nav nav-tabs" id="configTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="geral-tab" data-bs-toggle="tab" data-bs-target="#geral" type="button" role="tab" onclick="showTab('geral')">
                    <i class="fas fa-cog"></i>Geral
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="empresas-tab" data-bs-toggle="tab" data-bs-target="#empresas" type="button" role="tab" onclick="showTab('empresas')">
                    <i class="fas fa-building"></i>Empresas
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="usuarios-tab" data-bs-toggle="tab" data-bs-target="#usuarios" type="button" role="tab" onclick="showTab('usuarios')">
                    <i class="fas fa-users"></i>Usuários
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="biometria-tab" data-bs-toggle="tab" data-bs-target="#biometria" type="button" role="tab" onclick="showTab('biometria')">
                    <i class="fas fa-fingerprint"></i>Biometria
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sistema-tab" data-bs-toggle="tab" data-bs-target="#sistema" type="button" role="tab" onclick="showTab('sistema')">
                    <i class="fas fa-server"></i>Sistema
                </button>
            </li>
        </ul>

        <div class="tab-content" id="configTabsContent">
            <!-- Tab Geral -->
            <div class="tab-pane fade show active" id="geral" role="tabpanel">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-cog"></i>
                        Configurações Gerais
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-clock"></i></div>
                            <h5>Horários de Trabalho</h5>
                            <p>Configure os horários padrão de entrada e saída dos funcionários</p>
                            <a href="/horarios" class="btn-professional">
                                <i class="fas fa-clock"></i>Configurar
                            </a>
                        </div>
                        
                        <div class="action-card biometric-card">
                            <div class="icon"><i class="fas fa-fingerprint"></i></div>
                            <h5>Sistema Biométrico</h5>
                            <p>Sistema biométrico universal implementado e funcionando</p>
                            <a href="/configuracoes/biometria" class="btn-professional btn-success">
                                <i class="fas fa-fingerprint"></i>Configurar Biometria
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-chart-line"></i></div>
                            <h5>Relatórios</h5>
                            <p>Configure templates e formatos de relatórios</p>
                            <a href="/relatorios" class="btn-professional">
                                <i class="fas fa-chart-line"></i>Acessar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-shield-alt"></i></div>
                            <h5>Segurança</h5>
                            <p>Configurações de segurança do sistema</p>
                            <button class="btn-professional btn-outline" onclick="mostrarSeguranca()">
                                <i class="fas fa-lock"></i>Verificar
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Empresas -->
            <div class="tab-pane fade" id="empresas" role="tabpanel">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-building"></i>
                        Gerenciamento de Empresas
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-plus-circle"></i></div>
                            <h5>Nova Empresa</h5>
                            <p>Cadastrar uma nova empresa no sistema</p>
                            <a href="/configuracoes/empresas/nova" class="btn-professional btn-success">
                                <i class="fas fa-plus"></i>Cadastrar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-list"></i></div>
                            <h5>Listar Empresas</h5>
                            <p>Visualizar e editar empresas cadastradas</p>
                            <a href="/configuracoes/empresas" class="btn-professional">
                                <i class="fas fa-eye"></i>Listar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-edit"></i></div>
                            <h5>Editar Empresas</h5>
                            <p>Modificar dados das empresas cadastradas</p>
                            <a href="/configuracoes/empresas" class="btn-professional btn-warning">
                                <i class="fas fa-edit"></i>Editar
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Usuários -->
            <div class="tab-pane fade" id="usuarios" role="tabpanel">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-users"></i>
                        Gerenciamento de Usuários
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-user-plus"></i></div>
                            <h5>Novo Usuário</h5>
                            <p>Criar novo usuário administrativo</p>
                            <a href="/configurar_usuarios" class="btn-professional btn-success">
                                <i class="fas fa-user-plus"></i>Criar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-users-cog"></i></div>
                            <h5>Gerenciar Usuários</h5>
                            <p>Editar usuários e permissões</p>
                            <a href="/configurar_usuarios" class="btn-professional">
                                <i class="fas fa-cog"></i>Gerenciar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-key"></i></div>
                            <h5>Alterar Senha</h5>
                            <p>Alterar senha do usuário atual</p>
                            <button class="btn-professional btn-warning" onclick="mostrarFormSenha()">
                                <i class="fas fa-key"></i>Alterar
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Biometria -->
            <div class="tab-pane fade" id="biometria" role="tabpanel">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-fingerprint"></i>
                        Configurações Biométricas
                    </h4>
                    
                    <!-- Status Cards -->
                    <div class="stats-grid mb-4">
                        <div class="stat-card" id="service-status-card">
                            <div class="icon" id="service-icon"><i class="fas fa-power-off"></i></div>
                            <div class="value" id="service-status">Verificando...</div>
                            <div class="label">Status do Serviço</div>
                        </div>
                        <div class="stat-card" id="devices-count-card">
                            <div class="icon"><i class="fas fa-fingerprint"></i></div>
                            <div class="value" id="devices-count">0</div>
                            <div class="label">Dispositivos Registrados</div>
                        </div>
                        <div class="stat-card" id="last-discovery-card">
                            <div class="icon"><i class="fas fa-search"></i></div>
                            <div class="value" id="last-discovery">Nunca</div>
                            <div class="label">Última Descoberta</div>
                        </div>
                        <div class="stat-card" id="tests-today-card">
                            <div class="icon"><i class="fas fa-check-circle"></i></div>
                            <div class="value" id="tests-today">0</div>
                            <div class="label">Testes Hoje</div>
                        </div>
                    </div>
                    
                    <!-- Action Cards -->
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-play-circle"></i></div>
                            <h5>Iniciar Serviço</h5>
                            <p>Ativa o serviço biométrico universal para detectar leitores</p>
                            <button class="btn-professional btn-success" onclick="startBiometricService()">
                                <i class="fas fa-power-off"></i>Iniciar Serviço
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-search-plus"></i></div>
                            <h5>Descobrir Dispositivos</h5>
                            <p>Detecta automaticamente leitores biométricos conectados</p>
                            <button class="btn-professional" onclick="discoverDevices()" id="discover-btn">
                                <i class="fas fa-search"></i>Descobrir
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-cog"></i></div>
                            <h5>Configurar Parâmetros</h5>
                            <p>Ajusta sensibilidade, timeout e qualidade da captura</p>
                            <button class="btn-professional btn-warning" onclick="showSettingsModal()">
                                <i class="fas fa-sliders-h"></i>Configurar
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-hand-paper"></i></div>
                            <h5>Testar Captura</h5>
                            <p>Realiza teste de captura biométrica para validar funcionamento</p>
                            <button class="btn-professional btn-outline" onclick="testCapture()" id="test-btn">
                                <i class="fas fa-fingerprint"></i>Testar Agora
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Sistema -->
            <div class="tab-pane fade" id="sistema" role="tabpanel">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-server"></i>
                        Configurações do Sistema
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-database"></i></div>
                            <h5>Backup do Sistema</h5>
                            <p>Criar backup completo do banco de dados</p>
                            <button class="btn-professional btn-success">
                                <i class="fas fa-download"></i>Criar Backup
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-broom"></i></div>
                            <h5>Limpeza de Cache</h5>
                            <p>Limpar cache e arquivos temporários</p>
                            <button class="btn-professional btn-warning">
                                <i class="fas fa-broom"></i>Limpar Cache
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-chart-bar"></i></div>
                            <h5>Monitoramento</h5>
                            <p>Visualizar estatísticas de performance</p>
                            <button class="btn-professional">
                                <i class="fas fa-chart-bar"></i>Ver Estatísticas
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-cogs"></i></div>
                            <h5>Configurações Avançadas</h5>
                            <p>Configurações técnicas do sistema</p>
                            <button class="btn-professional btn-outline">
                                <i class="fas fa-cogs"></i>Configurar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// JAVASCRIPT PROFISSIONAL - SEM HACKS
function showTab(tabName) {
    // Esconder todas as tabs
    document.querySelectorAll('.tab-pane').forEach(tab => {
        tab.classList.remove('active', 'show');
    });
    
    // Remover classe active de todos os links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Mostrar tab selecionada
    const selectedTab = document.getElementById(tabName);
    if (selectedTab) {
        selectedTab.classList.add('active', 'show');
    }
    
    // Ativar link correspondente
    const selectedLink = document.getElementById(tabName + '-tab');
    if (selectedLink) {
        selectedLink.classList.add('active');
    }
}

// Funções específicas da biometria
function startBiometricService() {
    console.log('Iniciando serviço biométrico...');
}

function discoverDevices() {
    console.log('Descobrindo dispositivos...');
}

function testCapture() {
    console.log('Testando captura biométrica...');
}

function showSettingsModal() {
    console.log('Mostrando configurações...');
}

function mostrarSeguranca() {
    console.log('Verificando segurança...');
}

function mostrarFormSenha() {
    console.log('Mostrando formulário de senha...');
}

// Inicializar tab ativa
document.addEventListener('DOMContentLoaded', function() {
    showTab('geral');
});
</script>
{% endblock %} 