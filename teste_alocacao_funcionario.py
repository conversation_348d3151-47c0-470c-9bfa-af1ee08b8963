#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar a alocação de funcionários
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def testar_alocacao_funcionario():
    """Testar a funcionalidade de alocação de funcionários"""
    print("🔍 TESTANDO ALOCAÇÃO DE FUNCIONÁRIOS")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar funcionários disponíveis
        print("\n1. Verificando funcionários disponíveis...")
        sql_funcionarios = """
        SELECT f.id, f.nome_completo, f.cpf, f.cargo, f.empresa_id,
               e.razao_social as empresa_nome
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        WHERE f.status_cadastro = 'Ativo'
        ORDER BY f.nome_completo
        LIMIT 5
        """
        
        funcionarios = db.execute_query(sql_funcionarios)
        
        print(f"📊 Funcionários encontrados: {len(funcionarios)}")
        for func in funcionarios:
            print(f"   - ID {func['id']}: {func['nome_completo']} ({func['empresa_nome']}) - {func['cargo']}")
        
        # 2. Verificar empresas clientes disponíveis
        print("\n2. Verificando empresas clientes...")
        sql_clientes = """
        SELECT e.id, e.razao_social, e.nome_fantasia
        FROM empresas e
        WHERE e.ativa = TRUE AND e.empresa_principal = FALSE
        ORDER BY e.razao_social
        LIMIT 5
        """
        
        clientes = db.execute_query(sql_clientes)
        
        print(f"📊 Empresas clientes encontradas: {len(clientes)}")
        for cliente in clientes:
            print(f"   - ID {cliente['id']}: {cliente['razao_social']}")
        
        # 3. Verificar jornadas disponíveis
        print("\n3. Verificando jornadas disponíveis...")
        sql_jornadas = """
        SELECT j.id, j.nome_jornada, j.empresa_id,
               e.razao_social as empresa_nome
        FROM jornadas_trabalho j
        JOIN empresas e ON j.empresa_id = e.id
        WHERE j.ativa = TRUE
        ORDER BY e.razao_social, j.nome_jornada
        LIMIT 5
        """
        
        jornadas = db.execute_query(sql_jornadas)
        
        print(f"📊 Jornadas encontradas: {len(jornadas)}")
        for jornada in jornadas:
            print(f"   - ID {jornada['id']}: {jornada['nome_jornada']} ({jornada['empresa_nome']})")
        
        # 4. Verificar estrutura da tabela funcionario_alocacoes
        print("\n4. Verificando estrutura da tabela funcionario_alocacoes...")
        sql_estrutura = "DESCRIBE funcionario_alocacoes"
        try:
            estrutura = db.execute_query(sql_estrutura)
            print("📋 Campos da tabela funcionario_alocacoes:")
            for campo in estrutura:
                print(f"   - {campo['Field']}: {campo['Type']}")
        except Exception as e:
            print(f"⚠️ Tabela funcionario_alocacoes não existe ou erro: {e}")
        
        # 5. Verificar alocações existentes
        print("\n5. Verificando alocações existentes...")
        sql_alocacoes = """
        SELECT fa.id, fa.funcionario_id, fa.empresa_cliente_id, fa.ativo,
               f.nome_completo as funcionario_nome,
               e.razao_social as cliente_nome,
               fa.data_inicio, fa.data_fim
        FROM funcionario_alocacoes fa
        JOIN funcionarios f ON fa.funcionario_id = f.id
        JOIN empresas e ON fa.empresa_cliente_id = e.id
        ORDER BY fa.id DESC
        LIMIT 10
        """
        
        try:
            alocacoes = db.execute_query(sql_alocacoes)
            print(f"📊 Alocações encontradas: {len(alocacoes)}")
            for alocacao in alocacoes:
                status = "ATIVA" if alocacao['ativo'] else "INATIVA"
                print(f"   - ID {alocacao['id']}: {alocacao['funcionario_nome']} → {alocacao['cliente_nome']} ({status})")
        except Exception as e:
            print(f"⚠️ Erro ao buscar alocações: {e}")
        
        # 6. Testar query de inserção (simulação)
        print("\n6. Testando query de inserção (simulação)...")
        sql_insert = """
        INSERT INTO funcionario_alocacoes
        (funcionario_id, empresa_cliente_id, jornada_trabalho_id, cargo_no_cliente,
         data_inicio, data_fim, percentual_alocacao, valor_hora, observacoes, ativo)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, 1)
        """
        
        print("📝 Query de inserção preparada:")
        print(f"   SQL: {sql_insert}")
        print("   ✅ Query válida para inserção")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    testar_alocacao_funcionario()
