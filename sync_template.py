#!/usr/bin/env python3
"""
Script para sincronizar o template modernizado com o servidor
"""

import paramiko
import os

def sync_template():
    """Sincroniza o template modernizado com o servidor"""
    
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    print("🚀 SINCRONIZAÇÃO DO TEMPLATE MODERNIZADO")
    print("=" * 60)
    
    # Ler o arquivo local modernizado
    local_file = 'var/www/controle-ponto/templates/funcionarios/index.html'
    
    if not os.path.exists(local_file):
        print(f"❌ Arquivo local não encontrado: {local_file}")
        return False
    
    print(f"📄 Lendo arquivo local: {local_file}")
    
    with open(local_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"✅ Arquivo lido: {len(content)} caracteres")
    print(f"🔍 Primeiras linhas:")
    print(content[:200] + "...")
    
    try:
        # Conectar via SSH
        print("\n🔗 Conectando ao servidor...")
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password, timeout=30)
        
        print("✅ Conectado com sucesso!")
        
        # Fazer backup do arquivo original
        print("\n💾 Fazendo backup do arquivo original...")
        backup_cmd = "cp /var/www/controle-ponto/templates/funcionarios/index.html /var/www/controle-ponto/templates/funcionarios/index.html.backup.$(date +%Y%m%d_%H%M%S)"
        stdin, stdout, stderr = ssh.exec_command(backup_cmd)
        
        backup_result = stdout.read().decode()
        backup_error = stderr.read().decode()
        
        if backup_error:
            print(f"⚠️ Aviso no backup: {backup_error}")
        else:
            print("✅ Backup criado com sucesso!")
        
        # Criar o arquivo no servidor usando SFTP
        print("\n📤 Enviando arquivo modernizado...")
        
        sftp = ssh.open_sftp()
        
        # Escrever o conteúdo no arquivo remoto
        remote_file = '/var/www/controle-ponto/templates/funcionarios/index.html'
        
        with sftp.open(remote_file, 'w') as remote_f:
            remote_f.write(content)
        
        sftp.close()
        
        print("✅ Arquivo enviado com sucesso!")
        
        # Verificar se o arquivo foi atualizado
        print("\n🔍 Verificando arquivo no servidor...")
        stdin, stdout, stderr = ssh.exec_command("head -10 /var/www/controle-ponto/templates/funcionarios/index.html")
        
        server_content = stdout.read().decode()
        print("📄 Primeiras linhas no servidor:")
        print(server_content)
        
        # Verificar se contém o novo conteúdo
        if "Gestão de Funcionários" in server_content:
            print("✅ Arquivo atualizado corretamente!")
        else:
            print("❌ Arquivo não foi atualizado corretamente!")
            return False
        
        # Ajustar permissões
        print("\n🔧 Ajustando permissões...")
        stdin, stdout, stderr = ssh.exec_command("chown www-data:www-data /var/www/controle-ponto/templates/funcionarios/index.html")
        stdin, stdout, stderr = ssh.exec_command("chmod 644 /var/www/controle-ponto/templates/funcionarios/index.html")
        
        # Limpar cache Python
        print("\n🧹 Limpando cache Python...")
        stdin, stdout, stderr = ssh.exec_command("find /var/www/controle-ponto -name '*.pyc' -delete")
        stdin, stdout, stderr = ssh.exec_command("find /var/www/controle-ponto -name '__pycache__' -type d -exec rm -rf {} + 2>/dev/null || true")
        
        # Reiniciar Flask
        print("\n🔄 Reiniciando Flask...")
        stdin, stdout, stderr = ssh.exec_command("pkill -f 'python.*app.py' || true")
        stdin, stdout, stderr = ssh.exec_command("sleep 3")
        stdin, stdout, stderr = ssh.exec_command("cd /var/www/controle-ponto && nohup python3 app.py > flask.log 2>&1 &")
        stdin, stdout, stderr = ssh.exec_command("sleep 5")
        
        # Verificar se Flask iniciou
        print("\n✅ Verificando Flask...")
        stdin, stdout, stderr = ssh.exec_command("ps aux | grep python | grep app.py | grep -v grep")
        flask_status = stdout.read().decode()
        
        if flask_status:
            print("✅ Flask reiniciado com sucesso!")
            print(f"📋 Processo: {flask_status.strip()}")
        else:
            print("❌ Flask não iniciou!")
        
        ssh.close()
        
        print("\n" + "="*60)
        print("🎉 SINCRONIZAÇÃO CONCLUÍDA!")
        print("="*60)
        print("🌐 Acesse: http://************/funcionarios/")
        print("🎨 A página agora deve mostrar o design modernizado!")
        print("💡 Se ainda não aparecer, limpe o cache do navegador (Ctrl+F5)")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a sincronização: {e}")
        return False

if __name__ == "__main__":
    sync_template()
