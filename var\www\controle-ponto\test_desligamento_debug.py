#!/usr/bin/env python3
"""
Script de debug para testar a funcionalidade de desligamento de funcionários
"""
import sys
import os
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import FuncionarioQueries, DatabaseManager
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    print("🔍 TESTE DE DEBUG - DESLIGAMENTO DE FUNCIONÁRIOS")
    print("=" * 60)
    
    try:
        # 1. Verificar se existe funcionário para teste
        print("\n1. Verificando funcionários disponíveis...")
        funcionarios = DatabaseManager.execute_query(
            "SELECT id, nome_completo, matricula_empresa FROM funcionarios WHERE status_cadastro = 'Ativo' LIMIT 5"
        )
        
        if not funcionarios:
            print("❌ Nenhum funcionário ativo encontrado")
            return False
            
        print(f"✅ Encontrados {len(funcionarios)} funcionários ativos:")
        for func in funcionarios:
            print(f"   - ID: {func['id']}, Nome: {func['nome_completo']}, Matrícula: {func['matricula_empresa']}")
        
        # 2. Testar com o primeiro funcionário
        funcionario_teste = funcionarios[0]
        funcionario_id = funcionario_teste['id']
        
        print(f"\n2. Testando desligamento do funcionário ID {funcionario_id} ({funcionario_teste['nome_completo']})...")
        
        # 3. Verificar se funcionário existe antes do desligamento
        funcionario_antes = FuncionarioQueries.get_by_id(funcionario_id)
        if not funcionario_antes:
            print(f"❌ Funcionário {funcionario_id} não encontrado")
            return False
            
        print(f"✅ Funcionário encontrado: {funcionario_antes['nome_completo']}")
        
        # 4. Executar desligamento
        print(f"\n3. Executando desligamento...")
        resultado = FuncionarioQueries.desligar_funcionario(
            funcionario_id=funcionario_id,
            motivo_desligamento='Demissao_sem_justa_causa',
            observacoes='Teste de debug via script direto',
            usuario_responsavel=1
        )
        
        print(f"📊 Resultado do desligamento: {resultado}")
        
        # 5. Verificar se funcionário foi movido para tabela de desligados
        if resultado:
            print(f"\n4. Verificando se funcionário foi movido para tabela de desligados...")
            desligado = DatabaseManager.execute_query(
                "SELECT * FROM funcionarios_desligados WHERE funcionario_id_original = %s",
                (funcionario_id,)
            )
            
            if desligado:
                print(f"✅ Funcionário encontrado na tabela de desligados")
                print(f"   - Nome: {desligado[0]['nome_completo']}")
                print(f"   - Matrícula: {desligado[0]['matricula_empresa']}")
                print(f"   - Motivo: {desligado[0]['motivo_desligamento']}")
                print(f"   - Data: {desligado[0]['data_desligamento']}")
            else:
                print(f"❌ Funcionário NÃO encontrado na tabela de desligados")
                
            # Verificar se foi removido da tabela ativa
            funcionario_ativo = FuncionarioQueries.get_by_id(funcionario_id)
            if funcionario_ativo:
                print(f"❌ ERRO: Funcionário ainda existe na tabela ativa!")
            else:
                print(f"✅ Funcionário removido da tabela ativa")
                
        return resultado
        
    except Exception as e:
        print(f"🚨 ERRO: {e}")
        import traceback
        print(f"🚨 TRACEBACK: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    resultado = main()
    print(f"\n🎯 RESULTADO FINAL: {'SUCESSO' if resultado else 'FALHA'}")
    sys.exit(0 if resultado else 1)
