#!/usr/bin/env python3
"""
Script para verificar o conteúdo do template biométrico
"""

import paramiko

def check_biometrico():
    """Verifica o conteúdo do template biométrico no servidor"""
    
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    print("🔍 VERIFICAÇÃO DO TEMPLATE BIOMÉTRICO")
    print("=" * 60)
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password, timeout=30)
        
        print("✅ Conectado ao servidor")
        
        # Verificar se contém modern-header
        print("\n🔍 Procurando por 'modern-header'...")
        stdin, stdout, stderr = ssh.exec_command("grep -n 'modern-header' /var/www/controle-ponto/templates/registro_ponto/biometrico.html")
        
        result = stdout.read().decode()
        if result:
            print("✅ Encontrado 'modern-header':")
            print(result)
        else:
            print("❌ 'modern-header' não encontrado")
        
        # Verificar se contém variáveis CSS
        print("\n🔍 Procurando por variáveis CSS...")
        stdin, stdout, stderr = ssh.exec_command("grep -n 'primary-color' /var/www/controle-ponto/templates/registro_ponto/biometrico.html")
        
        result = stdout.read().decode()
        if result:
            print("✅ Encontrado variáveis CSS:")
            print(result[:200] + "...")
        else:
            print("❌ Variáveis CSS não encontradas")
        
        # Verificar tamanho do arquivo
        print("\n📏 Verificando tamanho do arquivo...")
        stdin, stdout, stderr = ssh.exec_command("wc -c /var/www/controle-ponto/templates/registro_ponto/biometrico.html")
        
        result = stdout.read().decode()
        print(f"📄 Tamanho: {result.strip()}")
        
        # Verificar últimas linhas modificadas
        print("\n📅 Verificando data de modificação...")
        stdin, stdout, stderr = ssh.exec_command("ls -la /var/www/controle-ponto/templates/registro_ponto/biometrico.html")
        
        result = stdout.read().decode()
        print(f"📅 Info: {result.strip()}")
        
        # Mostrar primeiras 30 linhas
        print("\n📄 Primeiras 30 linhas do arquivo:")
        stdin, stdout, stderr = ssh.exec_command("head -30 /var/www/controle-ponto/templates/registro_ponto/biometrico.html")
        
        result = stdout.read().decode()
        print(result)
        
        ssh.close()
        
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    check_biometrico()
