#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Serviço de Biometria ZK4500 para Controle de Ponto
--------------------------------------------------

Este serviço cria um servidor WebSocket que se comunica com o leitor biométrico ZK4500
e fornece uma API para o frontend web capturar e validar impressões digitais.

Requisitos:
- Python 3.6+
- websockets
- pyzkfp2 (SDK Python para ZK4500)
- libusb-1.0

Uso:
    python3 biometria_service.py

O serviço escuta na porta 8765 por padrão.
"""

import asyncio
import json
import logging
import os
import sys
import time
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
import platform

import websockets

# Configuração de logging
log_dir = Path("C:/Logs/controle-ponto" if platform.system() == "Windows" else "/var/log/controle-ponto")
log_dir.mkdir(parents=True, exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / "biometria.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("biometria-service")

# Tenta importar o SDK do ZK4500
try:
    import pyzkfp2 as zkfp
    HAS_ZKFP = True
except ImportError:
    logger.warning("SDK pyzkfp2 não encontrado. Usando modo de simulação.")
    HAS_ZKFP = False

# Constantes
WEBSOCKET_PORT = 8765
CAPTURE_TIMEOUT = 30  # segundos
MIN_QUALITY = 60  # qualidade mínima aceitável (0-100)
MATCH_THRESHOLD = 55  # limiar para considerar duas digitais como iguais (0-100)

class BiometriaDevice:
    """Classe para gerenciar o dispositivo de biometria ZK4500"""
    
    def __init__(self):
        self.initialized = False
        self.device_handle = None
        self.device_info = {}
        self.last_error = None
        
        # Inicializa o dispositivo se o SDK estiver disponível
        if HAS_ZKFP:
            self._initialize_device()
        else:
            self.device_info = {
                "name": "ZK4500 (Simulação)",
                "serial": "SIM12345678",
                "version": "1.0.0",
                "status": "simulation"
            }
    
    def _initialize_device(self):
        """Inicializa o dispositivo ZK4500"""
        try:
            # Inicializa a biblioteca
            ret = zkfp.ZKFP_Init()
            if ret != zkfp.ZKFP_ERR_OK:
                self.last_error = f"Falha ao inicializar SDK: {ret}"
                logger.error(self.last_error)
                return False
            
            # Obtém o número de dispositivos conectados
            device_count = zkfp.ZKFP_GetDeviceCount()
            if device_count <= 0:
                self.last_error = "Nenhum dispositivo biométrico encontrado"
                logger.error(self.last_error)
                return False
            
            # Abre o primeiro dispositivo
            self.device_handle = zkfp.ZKFP_OpenDevice(0)
            if self.device_handle is None:
                self.last_error = "Falha ao abrir dispositivo biométrico"
                logger.error(self.last_error)
                return False
            
            # Obtém informações do dispositivo
            device_info = zkfp.ZKFP_GetDeviceInfo(self.device_handle)
            if device_info:
                self.device_info = {
                    "name": device_info.get("name", "ZK4500"),
                    "serial": device_info.get("serial", "Unknown"),
                    "version": device_info.get("version", "1.0.0"),
                    "status": "ready"
                }
            else:
                self.device_info = {
                    "name": "ZK4500",
                    "serial": "Unknown",
                    "version": "Unknown",
                    "status": "ready"
                }
            
            self.initialized = True
            logger.info(f"Dispositivo biométrico inicializado: {self.device_info}")
            return True
            
        except Exception as e:
            self.last_error = f"Erro ao inicializar dispositivo: {str(e)}"
            logger.error(self.last_error)
            logger.error(traceback.format_exc())
            return False
    
    def capture_fingerprint(self) -> Dict[str, Any]:
        """Captura uma impressão digital do dispositivo"""
        if not HAS_ZKFP:
            # Modo de simulação
            time.sleep(2)  # Simula o tempo de captura
            quality = 85
            template = os.urandom(512).hex()  # Template simulado
            image_data = os.urandom(150*200).hex()  # Imagem simulada
            
            return {
                "success": True,
                "quality": quality,
                "template": template,
                "image": image_data,
                "timestamp": datetime.now().isoformat()
            }
        
        if not self.initialized or self.device_handle is None:
            return {
                "success": False,
                "error": "Dispositivo não inicializado"
            }
        
        try:
            # Captura a impressão digital
            ret, template, image = zkfp.ZKFP_AcquireFingerprint(self.device_handle)
            
            if ret != zkfp.ZKFP_ERR_OK:
                return {
                    "success": False,
                    "error": f"Falha ao capturar impressão digital: {ret}"
                }
            
            # Verifica a qualidade
            quality = zkfp.ZKFP_GetQuality(template)
            
            return {
                "success": True,
                "quality": quality,
                "template": template.hex(),
                "image": image.hex() if image else None,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.last_error = f"Erro ao capturar impressão digital: {str(e)}"
            logger.error(self.last_error)
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": self.last_error
            }
    
    def check_quality(self, template_hex: str) -> int:
        """Verifica a qualidade de um template de impressão digital"""
        if not HAS_ZKFP:
            # Modo de simulação
            return 85
        
        if not self.initialized:
            return 0
        
        try:
            # Converte o template hex para bytes
            template = bytes.fromhex(template_hex)
            
            # Verifica a qualidade
            quality = zkfp.ZKFP_GetQuality(template)
            return quality
            
        except Exception as e:
            self.last_error = f"Erro ao verificar qualidade: {str(e)}"
            logger.error(self.last_error)
            return 0
    
    def match_fingerprints(self, template1_hex: str, template2_hex: str) -> int:
        """Compara duas impressões digitais e retorna o score de similaridade"""
        if not HAS_ZKFP:
            # Modo de simulação
            time.sleep(1)
            return 80 if template1_hex[:10] == template2_hex[:10] else 30
        
        if not self.initialized:
            return 0
        
        try:
            # Converte os templates hex para bytes
            template1 = bytes.fromhex(template1_hex)
            template2 = bytes.fromhex(template2_hex)
            
            # Compara os templates
            score = zkfp.ZKFP_MatchFingerprint(template1, template2)
            return score
            
        except Exception as e:
            self.last_error = f"Erro ao comparar impressões digitais: {str(e)}"
            logger.error(self.last_error)
            return 0
    
    def close(self):
        """Fecha o dispositivo e libera recursos"""
        if HAS_ZKFP and self.initialized and self.device_handle is not None:
            try:
                zkfp.ZKFP_CloseDevice(self.device_handle)
                zkfp.ZKFP_Terminate()
                logger.info("Dispositivo biométrico fechado")
            except Exception as e:
                logger.error(f"Erro ao fechar dispositivo: {str(e)}")
        
        self.initialized = False
        self.device_handle = None

class BiometriaService:
    """Serviço WebSocket para comunicação com o frontend"""
    
    def __init__(self):
        self.device = BiometriaDevice()
        self.clients = set()
        self.server = None
    
    async def start_server(self):
        """Inicia o servidor WebSocket"""
        try:
            self.server = await websockets.serve(
                self.handle_client,
                "0.0.0.0",  # Escuta em todas as interfaces
                WEBSOCKET_PORT
            )
            logger.info(f"Servidor WebSocket iniciado na porta {WEBSOCKET_PORT}")
            
            # Mantém o servidor rodando
            await self.server.wait_closed()
            
        except Exception as e:
            logger.error(f"Erro ao iniciar servidor WebSocket: {str(e)}")
            logger.error(traceback.format_exc())
    
    async def handle_client(self, websocket, path):
        """Gerencia a conexão com um cliente WebSocket"""
        client_id = id(websocket)
        logger.info(f"Novo cliente conectado: {client_id}")
        
        # Adiciona o cliente à lista
        self.clients.add(websocket)
        
        try:
            # Envia informações do dispositivo ao cliente
            await self.send_device_info(websocket)
            
            # Envia o status do leitor
            await self.send_reader_status(websocket)
            
            # Processa mensagens do cliente
            async for message in websocket:
                await self.process_message(websocket, message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Cliente desconectado: {client_id}")
        except Exception as e:
            logger.error(f"Erro ao processar mensagem do cliente {client_id}: {str(e)}")
            logger.error(traceback.format_exc())
        finally:
            # Remove o cliente da lista
            self.clients.remove(websocket)
    
    async def send_device_info(self, websocket):
        """Envia informações do dispositivo para o cliente"""
        await self.send_message(websocket, {
            "type": "DEVICE_INFO",
            "data": self.device.device_info
        })
    
    async def send_reader_status(self, websocket):
        """Envia o status do leitor para o cliente"""
        await self.send_message(websocket, {
            "type": "READER_STATUS",
            "data": {
                "connected": self.device.initialized
            }
        })
    
    async def process_message(self, websocket, message):
        """Processa uma mensagem recebida do cliente"""
        try:
            data = json.loads(message)
            command = data.get("command")
            params = data.get("params", {})
            
            logger.debug(f"Comando recebido: {command}, Params: {params}")
            
            if command == "GET_DEVICE_INFO":
                await self.send_device_info(websocket)
                
            elif command == "CAPTURE":
                dedo_index = params.get("dedoIndex", 1)
                await self.handle_capture(websocket, dedo_index)
                
            elif command == "CHECK_QUALITY":
                template = params.get("template")
                if template:
                    quality = self.device.check_quality(template)
                    await self.send_message(websocket, {
                        "type": "STATUS",
                        "data": {
                            "qualidade": quality,
                            "valido": quality >= MIN_QUALITY
                        }
                    })
                else:
                    await self.send_error(websocket, "Template não fornecido")
                    
            elif command == "MATCH":
                template1 = params.get("template1")
                template2 = params.get("template2")
                if template1 and template2:
                    score = self.device.match_fingerprints(template1, template2)
                    await self.send_message(websocket, {
                        "type": "STATUS",
                        "data": {
                            "score": score,
                            "match": score >= MATCH_THRESHOLD
                        }
                    })
                else:
                    await self.send_error(websocket, "Templates não fornecidos")
                    
            else:
                await self.send_error(websocket, f"Comando desconhecido: {command}")
                
        except json.JSONDecodeError:
            await self.send_error(websocket, "Formato de mensagem inválido")
        except Exception as e:
            logger.error(f"Erro ao processar mensagem: {str(e)}")
            logger.error(traceback.format_exc())
            await self.send_error(websocket, f"Erro interno: {str(e)}")
    
    async def handle_capture(self, websocket, dedo_index):
        """Gerencia o processo de captura de impressão digital"""
        try:
            # Notifica o cliente que a captura está iniciando
            await self.send_message(websocket, {
                "type": "STATUS",
                "data": {
                    "status": "capturing",
                    "message": f"Capturando impressão digital do dedo {dedo_index}..."
                }
            })
            
            # Captura a impressão digital
            result = self.device.capture_fingerprint()
            
            if result["success"]:
                # Verifica se a qualidade é aceitável
                if result["quality"] < MIN_QUALITY:
                    await self.send_message(websocket, {
                        "type": "CAPTURE_RESULT",
                        "data": {
                            "success": False,
                            "error": f"Qualidade da impressão digital muito baixa ({result['quality']}%). Por favor, tente novamente.",
                            "quality": result["quality"]
                        }
                    })
                else:
                    # Envia o resultado da captura
                    await self.send_message(websocket, {
                        "type": "CAPTURE_RESULT",
                        "data": {
                            "success": True,
                            "template": result["template"],
                            "image": result["image"],
                            "quality": result["quality"],
                            "timestamp": result["timestamp"],
                            "dedoIndex": dedo_index
                        }
                    })
            else:
                # Envia o erro
                await self.send_message(websocket, {
                    "type": "CAPTURE_RESULT",
                    "data": {
                        "success": False,
                        "error": result["error"]
                    }
                })
                
        except Exception as e:
            logger.error(f"Erro durante captura: {str(e)}")
            logger.error(traceback.format_exc())
            await self.send_error(websocket, f"Erro durante captura: {str(e)}")
    
    async def send_message(self, websocket, message):
        """Envia uma mensagem para um cliente"""
        try:
            await websocket.send(json.dumps(message))
        except Exception as e:
            logger.error(f"Erro ao enviar mensagem: {str(e)}")
    
    async def send_error(self, websocket, error_message):
        """Envia uma mensagem de erro para um cliente"""
        await self.send_message(websocket, {
            "type": "ERROR",
            "data": {
                "message": error_message
            }
        })
    
    def stop(self):
        """Para o servidor e libera recursos"""
        if self.server:
            self.server.close()
        
        self.device.close()
        logger.info("Serviço de biometria encerrado")

async def main():
    """Função principal"""
    # Inicia o serviço
    service = BiometriaService()
    
    try:
        # Configura tratamento de sinais para encerramento gracioso
        loop = asyncio.get_running_loop()
        for signal_name in ('SIGINT', 'SIGTERM'):
            try:
                loop.add_signal_handler(
                    getattr(signal, signal_name),
                    lambda: asyncio.create_task(shutdown(service))
                )
            except (NotImplementedError, AttributeError):
                # Windows não suporta add_signal_handler
                pass
        
        logger.info("Iniciando serviço de biometria...")
        await service.start_server()
        
    except KeyboardInterrupt:
        logger.info("Interrompido pelo usuário")
    except Exception as e:
        logger.error(f"Erro no serviço de biometria: {str(e)}")
        logger.error(traceback.format_exc())
    finally:
        service.stop()

async def shutdown(service):
    """Encerra o serviço graciosamente"""
    logger.info("Encerrando serviço...")
    service.stop()
    # Força a saída após um timeout
    await asyncio.sleep(2)
    sys.exit(0)

if __name__ == "__main__":
    try:
        import signal
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Serviço interrompido pelo usuário")
