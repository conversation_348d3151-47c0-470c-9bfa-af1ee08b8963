#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 TESTE DA JORNADA DO FUNCIONÁRIO
==================================

Verifica se o funcionário tem jornada configurada corretamente
e como está sendo calculado as horas esperadas.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'var', 'www', 'controle-ponto'))

from utils.database import DatabaseManager
from datetime import datetime, time
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger('teste_jornada')

def buscar_funcionario_empresa_principal():
    """
    Busca o funcionário da Empresa Principal que aparece na imagem
    """
    try:
        db = DatabaseManager()
        
        # Buscar todos os funcionários para ver quais existem
        query = """
        SELECT
            f.id,
            f.nome_completo,
            f.empresa_id,
            e.razao_social as empresa_nome,
            f.jornada_trabalho_id,
            f.horario_trabalho_id,
            f.horas_semanais_obrigatorias
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        ORDER BY f.id
        LIMIT 10
        """
        
        resultado = db.execute_query(query)
        
        if resultado:
            print("👥 FUNCIONÁRIOS ENCONTRADOS:")
            print("-" * 50)
            for func in resultado:
                print(f"   ID: {func['id']}")
                print(f"   Nome: {func['nome_completo']}")
                print(f"   Empresa: {func['empresa_nome']}")
                print(f"   Jornada ID: {func['jornada_trabalho_id']}")
                print(f"   Horário ID: {func['horario_trabalho_id']}")
                print(f"   Horas Semanais: {func['horas_semanais_obrigatorias']}")
                print("-" * 30)

            # Procurar funcionário da Empresa Principal ou SEDE
            for func in resultado:
                if func['empresa_nome'] and ('Empresa Principal' in func['empresa_nome'] or 'SEDE' in func['empresa_nome']):
                    print(f"✅ Encontrado funcionário da Empresa Principal: {func['nome_completo']}")
                    return func['id']

            # Se não encontrar, usar o primeiro
            print(f"⚠️ Usando primeiro funcionário: {resultado[0]['nome_completo']}")
            return resultado[0]['id']
        else:
            print("❌ Nenhum funcionário encontrado")
            return None
            
    except Exception as e:
        logger.error(f"Erro ao buscar funcionário: {e}")
        return None

def verificar_jornada_funcionario(funcionario_id):
    """
    Verifica a jornada configurada do funcionário
    """
    try:
        db = DatabaseManager()
        
        print(f"\n🔍 VERIFICANDO JORNADA DO FUNCIONÁRIO {funcionario_id}")
        print("=" * 60)
        
        # Query completa para verificar todas as fontes de jornada
        query = """
        SELECT
            f.nome_completo,
            f.empresa_id,
            e.razao_social as empresa_nome,
            
            -- Jornada do funcionário
            f.jornada_trabalho_id,
            jt.nome_jornada,
            jt.seg_qui_entrada as jt_entrada,
            jt.seg_qui_saida as jt_saida,
            jt.intervalo_inicio as jt_almoco_inicio,
            jt.intervalo_fim as jt_almoco_fim,
            
            -- Horário específico do funcionário
            f.horario_trabalho_id,
            ht_funcionario.entrada_manha as ht_func_entrada,
            ht_funcionario.saida as ht_func_saida,
            
            -- Configuração da empresa
            ec_config.jornada_segunda_entrada as ec_entrada,
            ec_config.jornada_segunda_saida as ec_saida,
            ec_config.intervalo_obrigatorio as ec_intervalo,
            
            -- Resultado final (prioridade: empresa > funcionário > jornada)
            COALESCE(
                TIME(ec_config.jornada_segunda_entrada),
                ht_funcionario.entrada_manha,
                jt.seg_qui_entrada
            ) as entrada_final,
            COALESCE(
                TIME(ec_config.jornada_segunda_saida),
                ht_funcionario.saida,
                jt.seg_qui_saida
            ) as saida_final,
            COALESCE(
                ec_config.intervalo_obrigatorio,
                1.0
            ) as intervalo_final
            
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        LEFT JOIN empresas_config ec_config ON f.empresa_id = ec_config.empresa_id
        LEFT JOIN horarios_trabalho ht_funcionario ON f.horario_trabalho_id = ht_funcionario.id
        WHERE f.id = %s
        """
        
        resultado = db.execute_query(query, (funcionario_id,))
        
        if not resultado:
            print(f"❌ Funcionário {funcionario_id} não encontrado")
            return None
        
        dados = resultado[0]
        
        print(f"👤 FUNCIONÁRIO: {dados['nome_completo']}")
        print(f"🏢 EMPRESA: {dados['empresa_nome']}")
        print()
        
        print("📋 CONFIGURAÇÕES ENCONTRADAS:")
        print("-" * 40)
        
        # Jornada de trabalho
        if dados['jornada_trabalho_id']:
            print(f"✅ JORNADA DE TRABALHO:")
            print(f"   ID: {dados['jornada_trabalho_id']}")
            print(f"   Nome: {dados['nome_jornada']}")
            print(f"   Entrada: {dados['jt_entrada']}")
            print(f"   Saída: {dados['jt_saida']}")
            print(f"   Almoço: {dados['jt_almoco_inicio']} - {dados['jt_almoco_fim']}")
        else:
            print("❌ JORNADA DE TRABALHO: Não configurada")
        
        print()
        
        # Horário específico
        if dados['horario_trabalho_id']:
            print(f"✅ HORÁRIO ESPECÍFICO:")
            print(f"   ID: {dados['horario_trabalho_id']}")
            print(f"   Entrada: {dados['ht_func_entrada']}")
            print(f"   Saída: {dados['ht_func_saida']}")
        else:
            print("❌ HORÁRIO ESPECÍFICO: Não configurado")
        
        print()
        
        # Configuração da empresa
        if dados['ec_entrada']:
            print(f"✅ CONFIGURAÇÃO DA EMPRESA:")
            print(f"   Entrada: {dados['ec_entrada']}")
            print(f"   Saída: {dados['ec_saida']}")
            print(f"   Intervalo: {dados['ec_intervalo']}h")
        else:
            print("❌ CONFIGURAÇÃO DA EMPRESA: Não configurada")
        
        print()
        
        print()
        print("🎯 RESULTADO FINAL (PRIORIDADE):")
        print("-" * 40)
        print(f"   Entrada: {dados['entrada_final']}")
        print(f"   Saída: {dados['saida_final']}")
        print(f"   Intervalo: {dados['intervalo_final']}h")
        
        # Calcular horas esperadas
        if dados['entrada_final'] and dados['saida_final']:
            # Converter timedelta para time se necessário
            entrada_final = dados['entrada_final']
            saida_final = dados['saida_final']

            if hasattr(entrada_final, 'total_seconds'):
                # É timedelta, converter para time
                entrada_final = (datetime.min + entrada_final).time()
            if hasattr(saida_final, 'total_seconds'):
                # É timedelta, converter para time
                saida_final = (datetime.min + saida_final).time()

            entrada_dt = datetime.combine(datetime.today(), entrada_final)
            saida_dt = datetime.combine(datetime.today(), saida_final)
            
            # Se saída é no dia seguinte
            if saida_dt <= entrada_dt:
                from datetime import timedelta
                saida_dt += timedelta(days=1)
            
            total_jornada_horas = (saida_dt - entrada_dt).total_seconds() / 3600
            intervalo_horas = float(dados['intervalo_final'] or 1.0)
            horas_esperadas = total_jornada_horas - intervalo_horas
            
            print()
            print("🧮 CÁLCULO DAS HORAS ESPERADAS:")
            print("-" * 40)
            print(f"   Total da jornada: {total_jornada_horas:.2f}h")
            print(f"   Intervalo: {intervalo_horas}h")
            print(f"   ✅ HORAS ESPERADAS: {horas_esperadas:.2f}h")
            
            return horas_esperadas
        else:
            print()
            print("❌ ERRO: Não foi possível calcular horas esperadas")
            print("❌ Entrada ou saída não configuradas!")
            return None
            
    except Exception as e:
        logger.error(f"Erro ao verificar jornada: {e}")
        return None

def main():
    print("🔍 TESTE DA JORNADA DO FUNCIONÁRIO")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    # Buscar funcionário da Empresa Principal
    funcionario_id = buscar_funcionario_empresa_principal()
    
    if funcionario_id:
        # Verificar jornada
        horas_esperadas = verificar_jornada_funcionario(funcionario_id)
        
        print(f"\n" + "=" * 60)
        print(f"📊 RESULTADO FINAL")
        print(f"=" * 60)
        
        if horas_esperadas:
            print(f"✅ JORNADA CONFIGURADA CORRETAMENTE")
            print(f"✅ Funcionário deve trabalhar {horas_esperadas:.2f}h por dia")
            print(f"✅ Sistema calculará atrasos baseado neste valor")
        else:
            print(f"❌ JORNADA NÃO CONFIGURADA")
            print(f"❌ Sistema não pode calcular horas esperadas")
            print(f"❌ Necessário configurar jornada do funcionário")
    else:
        print(f"❌ Funcionário não encontrado")

if __name__ == "__main__":
    main()
