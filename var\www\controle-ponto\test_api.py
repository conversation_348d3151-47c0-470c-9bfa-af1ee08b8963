#!/usr/bin/env python3
"""
Script para testar a API de jornadas com sessão válida
"""

import requests
import json

def test_api():
    base_url = "http://************"
    
    # Criar sessão
    session = requests.Session()
    
    print("🔐 Fazendo login...")
    
    # Fazer login
    login_data = {
        'usuario': 'admin',
        'senha': '@Ric6109'
    }
    
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code == 200:
        print("✅ Login realizado com sucesso")
        
        # Testar API de jornadas
        print("🔍 Testando API de jornadas...")
        
        api_response = session.get(f"{base_url}/empresa-principal/api/jornadas")
        
        print(f"Status: {api_response.status_code}")
        print(f"Headers: {dict(api_response.headers)}")
        print(f"Content: {api_response.text[:500]}")
        
        if api_response.status_code == 200:
            try:
                data = api_response.json()
                print(f"✅ API funcionando: {data}")
            except:
                print("❌ Resposta não é JSON válido")
        else:
            print(f"❌ API retornou erro: {api_response.status_code}")
            
    else:
        print(f"❌ Erro no login: {login_response.status_code}")
        print(f"Resposta: {login_response.text[:200]}")

if __name__ == "__main__":
    test_api()
