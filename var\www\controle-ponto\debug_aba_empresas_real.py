#!/usr/bin/env python3
import paramiko

def debug_aba_empresas_real():
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🔍 DEBUG REAL - ABA EMPRESAS NÃO FUNCIONA")
        print("=" * 50)
        
        # 1. Verificar se a aba empresas existe no HTML
        print("1. Verificando se aba empresas existe no template...")
        stdin, stdout, stderr = ssh.exec_command('grep -n "empresas-tab\|id=\\"empresas\\"" /var/www/controle-ponto/templates/configuracoes/index.html')
        aba_check = stdout.read().decode()
        print("Aba empresas no template:")
        print(aba_check)
        
        # 2. Verificar o conteúdo da aba empresas
        print("\n2. Verificando conteúdo da aba empresas...")
        stdin, stdout, stderr = ssh.exec_command('grep -A 20 "Tab Empresas" /var/www/controle-ponto/templates/configuracoes/index.html')
        content_check = stdout.read().decode()
        print("Conteúdo da aba:")
        print(content_check)
        
        # 3. Verificar se há JavaScript funcionando
        print("\n3. Verificando JavaScript das abas...")
        stdin, stdout, stderr = ssh.exec_command('tail -30 /var/www/controle-ponto/templates/configuracoes/index.html')
        js_check = stdout.read().decode()
        print("JavaScript no final do arquivo:")
        print(js_check)
        
        # 4. Verificar se Bootstrap está carregado
        print("\n4. Verificando Bootstrap no template base...")
        stdin, stdout, stderr = ssh.exec_command('grep -n bootstrap /var/www/controle-ponto/templates/base.html')
        bootstrap_check = stdout.read().decode()
        print("Bootstrap no base.html:")
        print(bootstrap_check)
        
        # 5. Criar um template de teste simples
        print("\n5. Criando template de teste simples...")
        
        test_template = '''<!DOCTYPE html>
<html>
<head>
    <title>Teste Abas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Teste de Abas</h2>
        
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home" type="button">Home</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="empresas-tab" data-bs-toggle="tab" data-bs-target="#empresas" type="button">Empresas</button>
            </li>
        </ul>
        
        <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active" id="home" role="tabpanel">
                <h3>Aba Home</h3>
                <p>Esta é a aba home funcionando.</p>
            </div>
            <div class="tab-pane fade" id="empresas" role="tabpanel">
                <h3>Aba Empresas</h3>
                <p>Esta é a aba empresas funcionando!</p>
                <a href="/configuracoes/empresas" class="btn btn-primary">Gerenciar Empresas</a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>'''
        
        stdin, stdout, stderr = ssh.exec_command(f'cat > /var/www/controle-ponto/templates/teste_abas.html << "EOF"\n{test_template}\nEOF')
        
        # 6. Criar rota de teste
        print("6. Criando rota de teste...")
        
        test_route = '''
# ROTA DE TESTE - ADICIONAR NO FINAL DO app.py
@app.route('/teste-abas')
def teste_abas():
    return render_template('teste_abas.html')
'''
        
        stdin, stdout, stderr = ssh.exec_command(f'echo "{test_route}" >> /var/www/controle-ponto/app.py')
        
        # 7. Reiniciar serviço
        print("7. Reiniciando serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl restart controle-ponto')
        
        # 8. Verificar se o template de configurações tem problema
        print("8. Verificando estrutura HTML do template de configurações...")
        stdin, stdout, stderr = ssh.exec_command('grep -n "tab-content\|tab-pane" /var/www/controle-ponto/templates/configuracoes/index.html | head -10')
        structure_check = stdout.read().decode()
        print("Estrutura das abas:")
        print(structure_check)
        
        ssh.close()
        
        print("\n" + "=" * 50)
        print("🎯 RESULTADO DO DEBUG")
        print("=" * 50)
        print("✅ Template de teste criado")
        print("✅ Rota de teste adicionada")
        print("🔗 TESTE AGORA:")
        print("1. Acesse: http://************:5000/teste-abas")
        print("2. Veja se as abas funcionam")
        print("3. Se funcionarem, o problema está no template de configurações")
        
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    debug_aba_empresas_real()
