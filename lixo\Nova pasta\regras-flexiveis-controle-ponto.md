# 🕐 REGRAS FLEXÍVEIS - CONTROLE DE PONTO

**Sistema:** RLPONTO-WEB v1.0  
**Data:** 11/07/2025  
**Princ<PERSON>pio:** FLEXIBILIDADE TOTAL - O sistema NUNCA impede batidas

---

## 🎯 **PRINCÍPIO FUNDAMENTAL**

### **REGRA OURO:**
> **O funcionário SEMPRE pode bater o ponto, independente do horário.**  
> **O sistema é responsável por VALIDAR e CLASSIFICAR a batida após o registro.**

```
FUNCIONÁRIO BATE → SISTEMA REGISTRA → SISTEMA VALIDA → SISTEMA CLASSIFICA
```

---

## 📋 **ESTRUTURA DE PERÍODOS**

### **1. PRIMEIRO TURNO (DIURNO)**
```
🌅 PERÍODO MANHÃ:     05:00 - 11:00
🍽️ PERÍODO INTERVALO: 11:00 - 14:00  
🌆 PERÍODO TARDE:     14:00 - 18:00
```

### **2. TURN<PERSON> NOTURNO**
```
🌙 PERÍODO NOITE:     21:00 - 00:00
🛌 PERÍODO INTERVALO: 00:00 - 02:00
🌅 PERÍODO MADRUGADA: 02:00 - 05:59
```

---

## ⚙️ **LÓGICA DE VALIDAÇÃO POR PERÍODO**

### **EXEMPLO PRÁTICO - JORNADA 07:00-17:00:**

#### **Cenário 1: Entrada às 08:00**
```python
# Dados da batida
horario_batida = "08:00"
jornada_inicio = "07:00"
periodo_atual = "MANHÃ"  # 05:00-11:00

# Validação do sistema
resultado = {
    'dentro_da_jornada': True,      # 08:00 está dentro do período manhã
    'status_pontualidade': 'ATRASO',
    'tempo_atraso': 60,             # 1 hora de atraso
    'periodo_classificado': 'MANHÃ',
    'banco_horas': -60              # Tempo negativo no banco - DEVE aparecer explicitamente no relatório diário
}
```

#### **Cenário 2: Entrada às 10:00**
```python
# Dados da batida
horario_batida = "10:00"
jornada_inicio = "07:00"
periodo_atual = "MANHÃ"  # 05:00-11:00

# Validação do sistema
resultado = {
    'dentro_da_jornada': True,      # 10:00 ainda está no período manhã
    'status_pontualidade': 'ATRASO',
    'tempo_atraso': 180,            # 3 horas de atraso
    'periodo_classificado': 'MANHÃ',
    'banco_horas': -180             # Tempo negativo no banco
}
```

---

## 🍽️ **REGRAS DO INTERVALO**

### **PRINCÍPIO:**
> **O funcionário NÃO tem horário fixo de intervalo.**
> **O sistema define automaticamente o período de intervalo disponível.**

### **PRIMEIRO TURNO - INTERVALO:**
```python
# Configuração automática do sistema
intervalo_config = {
    'periodo_disponivel': '11:00 - 14:00',    # 3 horas disponíveis
    'duracao_obrigatoria': 60,                # 1 hora obrigatória
    'flexibilidade': 'TOTAL',                 # Pode iniciar a qualquer momento
    'validacao': 'POS_REGISTRO'               # Valida após registrar
}

# Exemplo: Funcionário sai às 12:30 e volta às 13:30
saida_almoco = "12:30"
entrada_tarde = "13:30"

# Validação do sistema
if entrada_tarde - saida_almoco >= 60:  # 1 hora mínima
    status = "INTERVALO_VÁLIDO"
else:
    status = "INTERVALO_INSUFICIENTE"
    # Aplicar tolerância de 10min após 1 hora obrigatória
    if minutos_intervalo < 60:
        banco_horas -= (60 - minutos_intervalo)  # Desconta tempo não cumprido
    elif minutos_intervalo > 70:  # Mais de 1h10min
        banco_horas -= (minutos_intervalo - 70)  # Multa por excesso
```

### **TURNO NOTURNO - INTERVALO:**
```python
# Configuração automática do sistema
intervalo_noturno = {
    'periodo_disponivel': '00:00 - 02:00',    # 2 horas disponíveis
    'duracao_obrigatoria': 60,                # 1 hora obrigatória
    'flexibilidade': 'TOTAL',                 # Pode iniciar a qualquer momento
    'validacao': 'POS_REGISTRO'               # Valida após registrar
}
```

---

## 🌙 **REGRAS DO TURNO NOTURNO**

### **ESTRUTURA:**
```
🌙 INÍCIO:     21:00
🛌 INTERVALO:  00:00 - 02:00 (1h obrigatória)
🌅 FIM:        05:59
```

### **VALIDAÇÃO NOTURNA:**
```python
def validar_turno_noturno(horario_batida, jornada_noturna):
    # Exemplo: Jornada 21:00 - 05:59
    inicio_jornada = "21:00"
    fim_jornada = "05:59"
    
    # Determinar período
    if "21:00" <= horario_batida <= "23:59":
        periodo = "NOITE_INICIO"
    elif "00:00" <= horario_batida <= "02:00":
        periodo = "INTERVALO_NOTURNO"
    elif "02:00" <= horario_batida <= "05:59":
        periodo = "MADRUGADA"
    
    # Validar pontualidade
    if horario_batida > inicio_jornada and periodo == "NOITE_INICIO":
        status = "ATRASO"
        atraso_minutos = calcular_diferenca(inicio_jornada, horario_batida)
    else:
        status = "PONTUAL"
    
    return {
        'periodo': periodo,
        'status': status,
        'banco_horas': -atraso_minutos if status == "ATRASO" else 0
    }
```

---

## 📊 **MATRIZ DE CLASSIFICAÇÃO**

### **TABELA DE PERÍODOS E VALIDAÇÕES:**

| Horário | Período | Jornada 07:00-17:00 | Jornada 21:00-05:59 | Ação Sistema |
|---------|---------|---------------------|---------------------|--------------|
| 05:00-11:00 | MANHÃ | ✅ Válido | ❌ Fora jornada | Registra + Classifica |
| 11:00-14:00 | INTERVALO | 🍽️ Intervalo disponível | ❌ Fora jornada | Registra + Valida duração |
| 14:00-18:00 | TARDE | ✅ Válido | ❌ Fora jornada | Registra + Classifica + Tolerância |
| 21:00-00:00 | NOITE | ❌ Fora jornada | ✅ Válido | Registra + Classifica |
| 00:00-02:00 | INTERVALO NOTURNO | ❌ Fora jornada | 🍽️ Intervalo disponível | Registra + Valida duração + Tolerância 10min |
| 02:00-05:59 | MADRUGADA | ❌ Fora jornada | ✅ Válido | Registra + Classifica + Tolerância |

---

## 🔄 **FLUXO DE PROCESSAMENTO**

### **PASSO 1: REGISTRO SEMPRE ACEITO**
```python
def registrar_ponto_flexivel(funcionario_id, horario_batida):
    # SEMPRE registra, independente do horário
    registro_id = inserir_registro_ponto(funcionario_id, horario_batida)
    
    # Depois processa validações
    validacao = processar_validacoes(funcionario_id, horario_batida)
    
    return {
        'registro_id': registro_id,
        'status': 'REGISTRADO',
        'validacao': validacao
    }
```

### **PASSO 2: CLASSIFICAÇÃO POR PERÍODO**
```python
def classificar_por_periodo(horario_batida):
    # Determinar período atual
    periodo = determinar_periodo_atual(horario_batida)
    
    # Buscar jornada do funcionário
    jornada = obter_jornada_funcionario(funcionario_id)
    
    # Verificar se está dentro da jornada
    dentro_jornada = verificar_periodo_jornada(periodo, jornada)
    
    return {
        'periodo': periodo,
        'dentro_jornada': dentro_jornada,
        'tipo_jornada': jornada['tipo']  # DIURNO ou NOTURNO
    }
```

### **PASSO 3: CÁLCULO DE BANCO DE HORAS**
```python
def calcular_banco_horas(funcionario_id, horario_batida, jornada):
    if jornada['tipo'] == 'DIURNO':
        return calcular_banco_diurno(horario_batida, jornada)
    else:
        return calcular_banco_noturno(horario_batida, jornada)

def calcular_banco_diurno(horario_batida, jornada):
    inicio_previsto = jornada['inicio']
    
    if horario_batida > inicio_previsto:
        atraso = calcular_diferenca_minutos(inicio_previsto, horario_batida)
        return -atraso  # Tempo negativo
    else:
        antecipacao = calcular_diferenca_minutos(horario_batida, inicio_previsto)
        return antecipacao  # Tempo positivo
```

---

## 🎯 **EXEMPLOS PRÁTICOS**

### **EXEMPLO 1: FUNCIONÁRIO DIURNO**
```
Jornada: 07:00 - 17:00
Intervalo disponível: 11:00 - 14:00 (1h obrigatória)

Batidas do dia:
- 08:30 → MANHÃ, ATRASO 90min, Banco: -90min
- 12:00 → INTERVALO, INÍCIO ALMOÇO
- 13:15 → INTERVALO, FIM ALMOÇO (75min = OK)
- 17:30 → TARDE, HORA EXTRA 30min, Banco: +30min

Resultado final: -90min + 30min = -60min no banco
```

### **EXEMPLO 2: FUNCIONÁRIO NOTURNO**
```
Jornada: 21:00 - 05:59
Intervalo disponível: 00:00 - 02:00 (1h obrigatória)

Batidas do dia:
- 21:30 → NOITE, ATRASO 30min, Banco: -30min
- 00:30 → INTERVALO, INÍCIO ALMOÇO
- 01:30 → INTERVALO, FIM ALMOÇO (60min = OK)
- 06:00 → MADRUGADA, PONTUAL, Banco: 0min

Resultado final: -30min no banco
```

---

## 📋 **REGRAS DE IMPLEMENTAÇÃO**

### **1. NUNCA BLOQUEAR:**
```python
# ❌ ERRADO
if horario_fora_jornada:
    return erro("Horário não permitido")

# ✅ CORRETO
registro = registrar_ponto(funcionario_id, horario_batida)
validacao = validar_pos_registro(registro)
return sucesso_com_alertas(registro, validacao)
```

### **2. SEMPRE CLASSIFICAR:**
```python
def processar_batida(funcionario_id, horario_batida):
    # 1. Registrar sempre
    registro = criar_registro(funcionario_id, horario_batida)
    
    # 2. Determinar período
    periodo = determinar_periodo(horario_batida)
    
    # 3. Validar contra jornada
    validacao = validar_jornada(funcionario_id, periodo, horario_batida)
    
    # 4. Calcular impacto no banco
    banco_horas = calcular_impacto_banco(validacao)
    
    # 5. Retornar resultado completo
    return {
        'registro': registro,
        'periodo': periodo,
        'validacao': validacao,
        'banco_horas': banco_horas
    }
```

### **3. FEEDBACK CLARO:**
```python
# Mensagens para o usuário
mensagens = {
    'PONTUAL': "Ponto registrado no horário correto",
    'ATRASO': f"Ponto registrado com {atraso}min de atraso",
    'ANTECIPADO': f"Ponto registrado {antecipacao}min antes do horário",
    'FORA_JORNADA': "Ponto registrado fora do período de trabalho",
    'INTERVALO_OK': "Intervalo de duração adequada",
    'INTERVALO_CURTO': f"Intervalo insuficiente: {duracao}min (mínimo 60min)"
}
```

---

**Status:** REGRAS DEFINIDAS E DOCUMENTADAS  
**Princípio:** FLEXIBILIDADE TOTAL COM VALIDAÇÃO INTELIGENTE  
**Implementação:** REGISTRO SEMPRE + CLASSIFICAÇÃO POSTERIOR


OBS: banco de horas é tudo aquilo que sobra do calculo de 8 horas diarias que o funcionario deve cumprir, 

Implante mais 2 batidas, B5 e B6, que são para ser usadas em horas extras e tambem em banco de horas.
como vai funcionar: se aplica o b5 no caso de horas extras ex: o funcionario encerrou a jornada as 17:00 e ele tem uma tolerancia de 10 min, ou seja, 
ele tem até as 17:10 para bater, se ele bater as 17:05 ele encerra a jornada, podendo iniciar ai já uma hora extra ou banco de horas, ai entra o b5, se ele bater
apos finalizar, o b5 indica um inicio de hora extra. a validação do b5 se da se todos os outros pontos foram batidos de forma correta b1, b2, b3 e b4. b5 
só será permitido se cumprir esses pre-requisitos e NÃO é permitido iniciar o b5 se faltar algum desses pontos batidos. o b6 encerra o b5, a validação do b6 se da se o 
b5 foi iniciado, se não o b6 é invalodo de qualquer forma, pois o b6 vai fechar a quantodade de tempo que o funcionario cumpriu e esses dados serão salvos
no banco de horas extras ou banco de horas.

pode acontecer de no sabado e domingo haver expediente, o sistema deve se comportar da seguinte forma, o sistema deve saber em qual dia da semana esta e depois 
tomar a seguinte decisão: no sabado não conta ausencia se não HOUVER ponto batido, mas se houver o inicio de uma jornada, a regra começa a valer para todos os
outros horarios normais. essa regra tambem vale para feriados. 

crie um campo para adicionar a porcentagem de hora extra, pois em sua maioria, no sabado, domingo e feriado é pago hora extra por porcentagem. cria no banco de 
dados isso, no modal onde bate o ponto manual crie algo como uma informação: "para sabado, domingo ou feriados, especifique se for hora extra por porcentagem"
e crie um campo para quem for bater o ponto preencher com esta porcentagem. esse pedido deve aparecer somente uma vez e quando confirmar o ponto salvar essa porcentageme no 
relatorio aparecer que nesse dia foi aplicado a porcentagem de hora extra.
