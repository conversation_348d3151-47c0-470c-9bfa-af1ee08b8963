#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para aplicar views necessárias para relatórios
Data: 06/06/2025
Descrição: Aplica as views SQL necessárias para o funcionamento correto dos relatórios
"""

import pymysql
import logging
import sys
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configurações do banco
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def verificar_e_criar_views():
    """Verifica e cria as views necessárias para relatórios."""
    conn = None
    try:
        logger.info("Conectando ao banco de dados...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 1. Criar VIEW vw_relatorio_pontos (corrigida)
        logger.info("Criando/atualizando view vw_relatorio_pontos...")
        cursor.execute("DROP VIEW IF EXISTS vw_relatorio_pontos")
        cursor.execute("""
            CREATE VIEW vw_relatorio_pontos AS
            SELECT 
                rp.id,
                rp.funcionario_id,
                f.nome_completo,
                f.matricula_empresa,
                f.cpf,
                CONCAT(
                    SUBSTRING(f.cpf, 1, 3), '.***.***-',
                    SUBSTRING(f.cpf, -2)
                ) as cpf_exibicao,
                rp.data_hora,
                DATE(rp.data_hora) as data_registro,
                TIME(rp.data_hora) as hora_registro,
                rp.tipo_registro,
                CASE rp.tipo_registro
                    WHEN 'entrada_manha' THEN 'Entrada Manhã'
                    WHEN 'saida_almoco' THEN 'Saída Almoço'
                    WHEN 'entrada_tarde' THEN 'Entrada Tarde'
                    WHEN 'saida' THEN 'Saída'
                END AS tipo_descricao,
                rp.metodo_registro,
                CASE 
                    WHEN rp.metodo_registro = 'biometrico' THEN 'Biométrico'
                    ELSE 'Manual'
                END AS metodo_descricao,
                COALESCE(f.setor_obra, f.setor, 'Não informado') as setor,
                f.cargo,
                f.empresa,
                rp.qualidade_biometria,
                rp.observacoes,
                rp.ip_origem,
                rp.criado_em,
                u.usuario as criado_por_usuario,
                CASE 
                    WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atraso'
                    WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atraso'
                    ELSE 'Pontual'
                END AS status_pontualidade
            FROM registros_ponto rp
            INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            LEFT JOIN usuarios u ON rp.criado_por = u.id
            WHERE f.ativo = 1
            ORDER BY rp.data_hora DESC
        """)
        logger.info("✓ View vw_relatorio_pontos criada com sucesso!")
        
        # 2. Criar VIEW vw_estatisticas_pontos (corrigida)
        logger.info("Criando/atualizando view vw_estatisticas_pontos...")
        cursor.execute("DROP VIEW IF EXISTS vw_estatisticas_pontos")
        cursor.execute("""
            CREATE VIEW vw_estatisticas_pontos AS
            SELECT 
                DATE(rp.data_hora) as data_registro,
                COUNT(*) as total_registros,
                SUM(CASE WHEN rp.metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
                SUM(CASE WHEN rp.metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais,
                SUM(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN 1 ELSE 0 END) as entradas_manha,
                SUM(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN 1 ELSE 0 END) as saidas_almoco,
                SUM(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN 1 ELSE 0 END) as entradas_tarde,
                SUM(CASE WHEN rp.tipo_registro = 'saida' THEN 1 ELSE 0 END) as saidas,
                SUM(CASE 
                    WHEN (rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00') OR
                         (rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00')
                    THEN 1 ELSE 0 
                END) as atrasos,
                COUNT(DISTINCT rp.funcionario_id) as funcionarios_registraram
            FROM registros_ponto rp
            INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            WHERE f.ativo = 1
            GROUP BY DATE(rp.data_hora)
            ORDER BY data_registro DESC
        """)
        logger.info("✓ View vw_estatisticas_pontos criada com sucesso!")
        
        # 3. Criar VIEW vw_horas_trabalhadas
        logger.info("Criando/atualizando view vw_horas_trabalhadas...")
        cursor.execute("DROP VIEW IF EXISTS vw_horas_trabalhadas")
        cursor.execute("""
            CREATE VIEW vw_horas_trabalhadas AS
            WITH registros_diarios AS (
                SELECT 
                    f.id as funcionario_id,
                    f.nome_completo,
                    f.setor,
                    f.cargo,
                    DATE(rp.data_hora) as data_registro,
                    MAX(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN rp.data_hora END) as entrada_manha,
                    MAX(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN rp.data_hora END) as saida_almoco,
                    MAX(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN rp.data_hora END) as entrada_tarde,
                    MAX(CASE WHEN rp.tipo_registro = 'saida' THEN rp.data_hora END) as saida
                FROM 
                    registros_ponto rp
                    INNER JOIN funcionarios f ON rp.funcionario_id = f.id
                WHERE f.ativo = 1
                GROUP BY 
                    f.id,
                    f.nome_completo,
                    f.setor,
                    f.cargo,
                    DATE(rp.data_hora)
            )
            SELECT 
                rd.*,
                CASE
                    WHEN rd.entrada_manha IS NOT NULL AND rd.saida_almoco IS NOT NULL
                    THEN TIMEDIFF(rd.saida_almoco, rd.entrada_manha) 
                    ELSE NULL
                END as periodo_manha,
                CASE
                    WHEN rd.entrada_tarde IS NOT NULL AND rd.saida IS NOT NULL
                    THEN TIMEDIFF(rd.saida, rd.entrada_tarde)
                    ELSE NULL
                END as periodo_tarde,
                CASE
                    WHEN rd.entrada_manha IS NOT NULL AND rd.saida_almoco IS NOT NULL
                         AND rd.entrada_tarde IS NOT NULL AND rd.saida IS NOT NULL
                    THEN ADDTIME(
                        TIMEDIFF(rd.saida_almoco, rd.entrada_manha),
                        TIMEDIFF(rd.saida, rd.entrada_tarde)
                    )
                    ELSE NULL
                END as total_horas_trabalhadas,
                CASE 
                    WHEN rd.entrada_manha IS NULL OR rd.saida_almoco IS NULL 
                    OR rd.entrada_tarde IS NULL OR rd.saida IS NULL 
                    THEN 'Incompleto'
                    ELSE 'Completo'
                END as status_dia
            FROM 
                registros_diarios rd
        """)
        logger.info("✓ View vw_horas_trabalhadas criada com sucesso!")
        
        # 4. Criar VIEW vw_analise_pontualidade
        logger.info("Criando/atualizando view vw_analise_pontualidade...")
        cursor.execute("DROP VIEW IF EXISTS vw_analise_pontualidade")
        cursor.execute("""
            CREATE VIEW vw_analise_pontualidade AS
            SELECT 
                f.id as funcionario_id,
                f.nome_completo,
                f.setor,
                f.cargo,
                DATE_FORMAT(rp.data_hora, '%Y-%m') as mes_ano,
                COUNT(DISTINCT DATE(rp.data_hora)) as dias_trabalhados,
                SUM(CASE 
                    WHEN rp.tipo_registro = 'entrada_manha' 
                    AND TIME(rp.data_hora) <= '08:10:00'
                    THEN 1 
                    ELSE 0 
                END) as entradas_pontuais_manha,
                SUM(CASE 
                    WHEN rp.tipo_registro = 'entrada_manha' 
                    AND TIME(rp.data_hora) > '08:10:00'
                    THEN 1 
                    ELSE 0 
                END) as atrasos_manha,
                SUM(CASE 
                    WHEN rp.tipo_registro = 'entrada_tarde' 
                    AND TIME(rp.data_hora) <= '13:10:00'
                    THEN 1 
                    ELSE 0 
                END) as entradas_pontuais_tarde,
                SUM(CASE 
                    WHEN rp.tipo_registro = 'entrada_tarde' 
                    AND TIME(rp.data_hora) > '13:10:00'
                    THEN 1 
                    ELSE 0 
                END) as atrasos_tarde,
                ROUND(
                    (SUM(CASE 
                        WHEN rp.tipo_registro IN ('entrada_manha', 'entrada_tarde')
                        AND ((rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) <= '08:10:00')
                          OR (rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) <= '13:10:00'))
                        THEN 1 
                        ELSE 0 
                    END) * 100.0) / 
                    NULLIF(SUM(CASE WHEN rp.tipo_registro IN ('entrada_manha', 'entrada_tarde') THEN 1 ELSE 0 END), 0),
                    2
                ) as percentual_pontualidade
            FROM 
                registros_ponto rp
                INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            WHERE f.ativo = 1
            GROUP BY 
                f.id,
                f.nome_completo,
                f.setor,
                f.cargo,
                DATE_FORMAT(rp.data_hora, '%Y-%m')
        """)
        logger.info("✓ View vw_analise_pontualidade criada com sucesso!")
        
        # Commitar mudanças
        conn.commit()
        
        # Verificar se as views foram criadas
        logger.info("\nVerificando views criadas...")
        cursor.execute("""
            SELECT 
                table_name as view_name
            FROM information_schema.views 
            WHERE table_schema = 'controle_ponto' 
            AND table_name IN (
                'vw_relatorio_pontos', 
                'vw_estatisticas_pontos', 
                'vw_horas_trabalhadas', 
                'vw_analise_pontualidade'
            )
            ORDER BY table_name
        """)
        
        views_criadas = cursor.fetchall()
        logger.info(f"\nViews criadas com sucesso: {len(views_criadas)}")
        for view in views_criadas:
            logger.info(f"  ✓ {view['view_name']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Erro ao criar views: {str(e)}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if conn:
            conn.close()
            logger.info("\nConexão fechada.")

def main():
    """Função principal."""
    logger.info("=" * 60)
    logger.info("APLICANDO VIEWS DE RELATÓRIOS - RLPONTO-WEB")
    logger.info(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    logger.info("=" * 60)
    
    if verificar_e_criar_views():
        logger.info("\n✅ SUCESSO: Todas as views foram criadas/atualizadas!")
        logger.info("\nPróximos passos:")
        logger.info("1. Teste novamente o sistema de relatórios")
        logger.info("2. Verifique os logs se houver erros")
        return 0
    else:
        logger.error("\n❌ ERRO: Falha ao criar algumas views!")
        logger.error("Verifique os logs acima para mais detalhes.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 