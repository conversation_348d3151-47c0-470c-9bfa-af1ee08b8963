-- =====================================================
-- CORREÇÃO: Compatibilidade de ENUMs entre tabelas
-- Data: 13/07/2025
-- Problema: Incompatibilidade entre ENUMs das tabelas funcionarios e funcionarios_desligados
-- =====================================================

-- 1. Verificar estruturas atuais
SELECT 'Verificando estruturas atuais...' as status;

SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios' 
AND COLUMN_NAME = 'nivel_acesso';

SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios_desligados' 
AND COLUMN_NAME = 'nivel_acesso';

-- 2. Corrigir ENUM da tabela funcionarios_desligados para ser compatível
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN nivel_acesso ENUM('Funcionario','Supervisao','Gerencia') DEFAULT 'Funcionario';

-- 3. Verificar outras incompatibilidades de ENUM
-- Verificar turno
SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios' 
AND COLUMN_NAME = 'turno';

SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios_desligados' 
AND COLUMN_NAME = 'turno';

-- Corrigir turno se necessário
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN turno ENUM('Manha','Tarde','Noite','Integral') NOT NULL;

-- 4. Verificar tipo_contrato
SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios' 
AND COLUMN_NAME = 'tipo_contrato';

SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios_desligados' 
AND COLUMN_NAME = 'tipo_contrato';

-- 5. Verificar status_cadastro
SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios' 
AND COLUMN_NAME = 'status_cadastro';

SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios_desligados' 
AND COLUMN_NAME = 'status_cadastro';

-- 6. Verificação final
SELECT 'Verificando correções aplicadas...' as status;

SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'funcionarios_desligados' 
AND COLUMN_NAME = 'nivel_acesso';

SELECT 'Correção de ENUMs concluída!' as resultado;
