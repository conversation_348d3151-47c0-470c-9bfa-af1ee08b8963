#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para Executar SQL de Remoção de Empresas (AUTOMÁTICO) - RLPONTO-WEB
-------------------------------------------------------------------------
Executa o arquivo SQL que remove todas as empresas e dados relacionados sem pedir confirmação.
"""

import pymysql
import sys
import os
from utils.config import Config

def executar_sql_arquivo(arquivo):
    """
    Executa o arquivo SQL
    """
    try:
        # Verificar se o arquivo existe
        if not os.path.exists(arquivo):
            print(f"❌ Arquivo {arquivo} não encontrado!")
            return False
            
        # Ler conteúdo do arquivo
        with open(arquivo, 'r', encoding='utf-8') as f:
            sql_content = f.read()
            
        # Conectar ao banco
        config = Config.get_database_url()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        print(f"🔍 Executando SQL do arquivo: {arquivo}")
        
        # Dividir por ponto e vírgula
        statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        # Executar cada statement
        for i, stmt in enumerate(statements):
            if stmt.strip():
                try:
                    cursor.execute(stmt)
                    print(f"✅ Comando {i+1}/{len(statements)} executado com sucesso")
                except Exception as e:
                    print(f"⚠️ Erro no comando {i+1}: {e}")
        
        # Verificar resultados
        try:
            cursor.execute("""
            SELECT 'Empresas restantes: ' AS mensagem, COUNT(*) AS total FROM empresas
            """)
            result = cursor.fetchone()
            print(f"\n✅ {result[0]} {result[1]}")
            
            if result[1] == 0:
                print("🎉 Todas as empresas foram removidas com sucesso!")
            else:
                print("⚠️ Ainda existem empresas no banco de dados!")
        except Exception as e:
            print(f"⚠️ Erro ao verificar resultados: {e}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("\n🎯 Processo concluído!")
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚫 REMOÇÃO AUTOMÁTICA DE TODAS AS EMPRESAS - RLPONTO-WEB")
    print("=" * 60)
    print("\n⚠️ ATENÇÃO: Esta operação removerá TODAS as empresas e dados relacionados!")
    print("⚠️ Esta ação NÃO PODE ser desfeita!")
    print("⚠️ EXECUTANDO EM MODO AUTOMÁTICO (sem confirmação)...\n")
    
    arquivo_sql = os.path.join(os.path.dirname(__file__), 'remover_todas_empresas.sql')
    executar_sql_arquivo(arquivo_sql) 