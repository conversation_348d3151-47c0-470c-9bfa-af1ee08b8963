# Script de Verificação dos Servidores MCP
# RLPONTO-WEB Project

Write-Host "=== Verificação dos Servidores MCP ===" -ForegroundColor Green
Write-Host ""

# Função para testar comando
function Test-Command {
    param(
        [string]$Command,
        [string]$Name
    )
    
    try {
        $result = Invoke-Expression "$Command 2>$null"
        if ($LASTEXITCODE -eq 0 -or $result) {
            Write-Host "   ✓ $Name funcionando" -ForegroundColor Green
            return $true
        } else {
            Write-Host "   ✗ $Name não funcionando" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "   ✗ $Name não encontrado" -ForegroundColor Red
        return $false
    }
}

# Verificar pré-requisitos
Write-Host "1. Verificando pré-requisitos..." -ForegroundColor Yellow
$nodeOk = Test-Command "node --version" "Node.js"
$npmOk = Test-Command "npm --version" "npm"
$npxOk = Test-Command "npx --version" "npx"

if (-not ($nodeOk -and $npmOk -and $npxOk)) {
    Write-Host ""
    Write-Host "✗ Pré-requisitos não atendidos!" -ForegroundColor Red
    Write-Host "Execute o script de instalação primeiro: scripts/instalar-mcp.ps1" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "2. Verificando arquivo de configuração..." -ForegroundColor Yellow
$configPath = ".vscode\settings.json"
if (Test-Path $configPath) {
    Write-Host "   ✓ Arquivo de configuração encontrado" -ForegroundColor Green
    
    # Verificar conteúdo do arquivo
    try {
        $config = Get-Content $configPath -Raw | ConvertFrom-Json
        if ($config.mcpServers) {
            Write-Host "   ✓ Configuração MCP válida" -ForegroundColor Green
            
            # Listar servidores configurados
            $servers = $config.mcpServers | Get-Member -MemberType NoteProperty | Select-Object -ExpandProperty Name
            Write-Host "   Servidores configurados: $($servers -join ', ')" -ForegroundColor Cyan
        } else {
            Write-Host "   ✗ Configuração MCP não encontrada no arquivo" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ✗ Erro ao ler arquivo de configuração" -ForegroundColor Red
    }
} else {
    Write-Host "   ✗ Arquivo de configuração não encontrado" -ForegroundColor Red
}

Write-Host ""
Write-Host "3. Testando servidores MCP..." -ForegroundColor Yellow

# Testar GitHub MCP
Write-Host "   Testando GitHub MCP..." -ForegroundColor Cyan
try {
    $result = npx @smithery/cli@latest --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✓ GitHub MCP (Smithery CLI) disponível" -ForegroundColor Green
    } else {
        Write-Host "   ⚠ GitHub MCP pode precisar ser baixado na primeira execução" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ✗ Erro ao testar GitHub MCP" -ForegroundColor Red
}

# Testar Browser Tools MCP
Write-Host "   Testando Browser Tools MCP..." -ForegroundColor Cyan
try {
    $result = npx @agentdeskai/browser-tools-mcp@1.2.0 --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✓ Browser Tools MCP disponível" -ForegroundColor Green
    } else {
        Write-Host "   ⚠ Browser Tools MCP pode precisar ser baixado na primeira execução" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ✗ Erro ao testar Browser Tools MCP" -ForegroundColor Red
}

# Testar Context7 MCP
Write-Host "   Testando Context7 MCP..." -ForegroundColor Cyan
try {
    $result = npx @upstash/context7-mcp --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✓ Context7 MCP disponível" -ForegroundColor Green
    } else {
        Write-Host "   ⚠ Context7 MCP pode precisar ser baixado na primeira execução" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ✗ Erro ao testar Context7 MCP" -ForegroundColor Red
}

# Testar 21st-dev Magic MCP
Write-Host "   Testando 21st-dev Magic MCP..." -ForegroundColor Cyan
try {
    $result = npx @21st-dev/magic@latest --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✓ 21st-dev Magic MCP disponível" -ForegroundColor Green
    } else {
        Write-Host "   ⚠ 21st-dev Magic MCP pode precisar ser baixado na primeira execução" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ✗ Erro ao testar 21st-dev Magic MCP" -ForegroundColor Red
}

Write-Host ""
Write-Host "4. Verificando cache npm..." -ForegroundColor Yellow
try {
    $cacheInfo = npm cache verify 2>$null
    Write-Host "   ✓ Cache npm verificado" -ForegroundColor Green
} catch {
    Write-Host "   ⚠ Problemas com cache npm" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "5. Verificando conectividade..." -ForegroundColor Yellow
try {
    $response = Test-NetConnection -ComputerName "registry.npmjs.org" -Port 443 -InformationLevel Quiet
    if ($response) {
        Write-Host "   ✓ Conectividade com npm registry OK" -ForegroundColor Green
    } else {
        Write-Host "   ✗ Problemas de conectividade com npm registry" -ForegroundColor Red
    }
} catch {
    Write-Host "   ⚠ Não foi possível testar conectividade" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Resumo da Verificação ===" -ForegroundColor Green
Write-Host ""

# Verificar se VS Code está rodando
$vscodeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
if ($vscodeProcesses) {
    Write-Host "⚠ VS Code está em execução. Para aplicar as configurações MCP:" -ForegroundColor Yellow
    Write-Host "   1. Feche completamente o VS Code"
    Write-Host "   2. Reabra o VS Code"
    Write-Host "   3. Abra o projeto RLPONTO-WEB"
} else {
    Write-Host "✓ VS Code não está em execução - pronto para aplicar configurações" -ForegroundColor Green
}

Write-Host ""
Write-Host "Como usar os servidores MCP no VS Code:" -ForegroundColor Cyan
Write-Host "1. Abra o VS Code"
Write-Host "2. Pressione Ctrl+Shift+P"
Write-Host "3. Digite 'MCP' para ver comandos disponíveis"
Write-Host "4. Ou procure por extensões MCP na marketplace"
Write-Host ""

# Verificar se há atualizações disponíveis
Write-Host "6. Verificando atualizações..." -ForegroundColor Yellow
try {
    $outdated = npm outdated -g --json 2>$null | ConvertFrom-Json
    if ($outdated) {
        Write-Host "   ⚠ Pacotes com atualizações disponíveis encontrados" -ForegroundColor Yellow
        Write-Host "   Execute 'npm update -g' para atualizar" -ForegroundColor Cyan
    } else {
        Write-Host "   ✓ Todos os pacotes estão atualizados" -ForegroundColor Green
    }
} catch {
    Write-Host "   ⚠ Não foi possível verificar atualizações" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Verificação concluída!" -ForegroundColor Green
Write-Host "Para mais informações, consulte: docs/guia-instalacao-mcp.md" -ForegroundColor Cyan
Write-Host ""
Read-Host "Pressione Enter para finalizar"
