# 📊 ANÁLISE COMPLETA DO BANCO DE DADOS RLPONTO-WEB

**Data:** 11/07/2025  
**Versão:** 1.0  
**Sistema:** RLPONTO-WEB - Controle de Ponto Biométrico  

## 🗄️ ESTRUTURA ATUAL DO BANCO

### 📋 TABELAS IDENTIFICADAS (Total: 30)

#### 🔐 **AUTENTICAÇÃO E SEGURANÇA**
- `usuarios` - Usuários do sistema
- `permissoes` - Níveis de acesso dos usuários
- `logs_seguranca` - Logs de segurança e tentativas de acesso
- `logs_sistema` - Logs gerais do sistema
- `configuracoes_sistema` - Configurações globais do sistema

#### 🏢 **EMPRESAS E CLIENTES**
- `empresas` - Cadastro de empresas
- `cad_empresas` - Cadastro adicional de empresas
- `empresas_config` - Configurações específicas por empresa
- `clientes` - Cadastro de clientes
- `empresa_clientes` - Relacionamento empresa-cliente

#### 👥 **FUNCIONÁRIOS E ALOCAÇÕES**
- `funcionarios` - Cadastro de funcionários
- `funcionario_alocacoes` - Alocações de funcionários
- `funcionario_cliente_alocacao` - Alocação funcionário-cliente
- `historico_alocacoes` - Histórico de mudanças de alocação
- `backup_jornada_funcionarios` - Backup de jornadas

#### ⏰ **CONTROLE DE PONTO**
- `registros_ponto` - Registros de batidas de ponto
- `horarios_trabalho` - Configurações de horários por empresa
- `jornadas_trabalho` - Jornadas de trabalho específicas
- `historico_alteracoes_ponto` - Histórico de alterações manuais
- `banco_horas` - Controle de banco de horas

#### 📝 **JUSTIFICATIVAS E ALERTAS**
- `justificativas_ponto` - Justificativas para irregularidades
- `alertas_ponto` - Alertas automáticos do sistema
- `historico_inferencias` - Histórico de inferências automáticas

#### 🛡️ **EQUIPAMENTOS E BIOMETRIA**
- `epis` - Equipamentos de Proteção Individual
- `dispositivos_biometricos` - Dispositivos biométricos cadastrados
- `logs_biometria` - Logs de tentativas biométricas
- `tentativas_biometria` - Tentativas de autenticação biométrica

#### 📊 **DADOS E LOGS**
- `dia_dados` - Dados consolidados por dia
- `log_exclusao_empresas` - Log de exclusões de empresas

### 🔍 **VIEWS IDENTIFICADAS (Total: 13)**
- `v_alertas_pendentes` - Alertas pendentes
- `v_estatisticas_alertas` - Estatísticas de alertas
- `v_turnos_ativos` - Turnos ativos
- `vw_analise_pontualidade` - Análise de pontualidade
- `vw_clientes_detalhados` - Detalhes de clientes
- `vw_empresa_principal` - Dados da empresa principal
- `vw_estatisticas_biometria` - Estatísticas biométricas
- `vw_estatisticas_ponto_setor` - Estatísticas por setor
- `vw_estatisticas_pontos` - Estatísticas gerais de ponto
- `vw_estatisticas_sistema` - Estatísticas do sistema
- `vw_funcionarios_alocados` - Funcionários e suas alocações
- `vw_funcionarios_biometria` - Funcionários com biometria
- `vw_horas_trabalhadas` - Horas trabalhadas consolidadas
- `vw_relatorio_pontos` - Relatórios de pontos

## 🎯 ESTRATÉGIA DE LIMPEZA

### ✅ **PRESERVAR (NÃO REMOVER)**
1. **Usuários do Sistema:**
   - `usuarios` - Manter todos os usuários
   - `permissoes` - Manter todas as permissões
   
2. **Configurações Essenciais:**
   - `configuracoes_sistema` - Manter configurações globais
   
3. **Logs Recentes:**
   - `logs_sistema` - Manter últimos 7 dias
   - `logs_seguranca` - Manter últimos 7 dias
   - `logs_biometria` - Manter últimos 7 dias

### ❌ **REMOVER COMPLETAMENTE**

#### **Dados Operacionais:**
- `empresas` - Todas as empresas
- `funcionarios` - Todos os funcionários
- `registros_ponto` - Todos os registros de ponto
- `clientes` - Todos os clientes
- `epis` - Todos os EPIs

#### **Relacionamentos:**
- `empresa_clientes` - Todos os relacionamentos
- `funcionario_alocacoes` - Todas as alocações
- `funcionario_cliente_alocacao` - Todas as alocações específicas

#### **Configurações Específicas:**
- `horarios_trabalho` - Todos os horários
- `jornadas_trabalho` - Todas as jornadas
- `empresas_config` - Todas as configurações

#### **Justificativas e Alertas:**
- `justificativas_ponto` - Todas as justificativas
- `alertas_ponto` - Todos os alertas
- `historico_alocacoes` - Todo o histórico
- `historico_alteracoes_ponto` - Todo o histórico
- `historico_inferencias` - Todo o histórico

#### **Dados Auxiliares:**
- `banco_horas` - Todos os registros
- `backup_jornada_funcionarios` - Todos os backups
- `cad_empresas` - Cadastros auxiliares
- `dia_dados` - Dados consolidados
- `dispositivos_biometricos` - Dispositivos
- `log_exclusao_empresas` - Logs de exclusão
- `tentativas_biometria` - Tentativas antigas

#### **Views:**
- Todas as 13 views serão removidas (serão recriadas automaticamente)

## 🔧 SCRIPTS CRIADOS

### 1. **zerar_banco_completo.sql**
- Script SQL direto para limpeza
- Execução manual via MySQL
- Backup automático de usuários
- Verificações de segurança

### 2. **executar_zerar_banco.py**
- Script Python interativo
- Confirmação dupla obrigatória
- Backup automático
- Verificação passo a passo
- Log detalhado da operação

## ⚠️ PRECAUÇÕES DE SEGURANÇA

### 🛡️ **Backups Automáticos**
- `usuarios_backup_zerar` - Backup da tabela usuarios
- `permissoes_backup_zerar` - Backup da tabela permissoes

### 🔒 **Confirmações Obrigatórias**
1. Usuário deve digitar "CONFIRMO"
2. Usuário deve digitar "SIM TENHO CERTEZA"
3. Verificação de conexão com banco
4. Backup obrigatório antes da operação

### 📊 **Verificações Finais**
- Contagem de usuários preservados
- Contagem de dados removidos
- Log da operação no sistema
- Relatório detalhado de execução

## 🚀 COMO EXECUTAR

### **Opção 1: Script Python (Recomendado)**
```bash
cd /var/www/controle-ponto
python3 executar_zerar_banco.py
```

### **Opção 2: Script SQL Direto**
```bash
mysql -u cavalcrod -p200381 controle_ponto < zerar_banco_completo.sql
```

## 📈 RESULTADO ESPERADO

### ✅ **Após a Execução:**
- ✅ Sistema de login funcionando normalmente
- ✅ Usuários e permissões preservados
- ✅ Configurações do sistema mantidas
- ✅ Banco limpo e pronto para novos dados
- ✅ Auto_increment resetado para 1
- ✅ Estrutura das tabelas intacta

### 🔄 **Próximos Passos:**
1. Cadastrar nova empresa principal
2. Configurar horários de trabalho
3. Cadastrar funcionários
4. Configurar dispositivos biométricos
5. Iniciar operação normal

## 📝 OBSERVAÇÕES IMPORTANTES

- **Irreversível:** A operação não pode ser desfeita
- **Backup:** Faça backup completo antes de executar
- **Teste:** Execute primeiro em ambiente de teste
- **Usuários:** Todos os usuários serão preservados
- **Configurações:** Configurações globais serão mantidas
- **Views:** Serão recriadas automaticamente pelo sistema
