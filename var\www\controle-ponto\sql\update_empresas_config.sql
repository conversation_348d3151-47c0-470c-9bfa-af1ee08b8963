-- =====================================================
-- ATUALIZAÇÃO: CONFIGURAÇÕES DE EMPRESAS
-- =====================================================
-- Adiciona suporte para jornada padrão e configurações avançadas
-- Data: 04/07/2025
-- Autor: AiNexus Tecnologia

-- 1. Criar tabela de configurações de empresas
CREATE TABLE IF NOT EXISTS empresas_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_id INT NOT NULL,
    jornada_padrao_id INT NULL COMMENT 'Jornada padrão que funcionários herdam automaticamente',
    tolerancia_atraso INT DEFAULT 10 COMMENT 'Tolerância para atraso em minutos',
    tolerancia_saida_antecipada INT DEFAULT 10 COMMENT 'Tolerância para saída antecipada em minutos',
    permite_banco_horas BOOLEAN DEFAULT TRUE COMMENT 'Se permite acúmulo de banco de horas',
    limite_banco_horas_mensal DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Limite de horas extras por mês',
    configuracoes_extras JSON NULL COMMENT 'Configurações específicas em JSON',
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (empresa_id) REFERENCES empresas(id) ON DELETE CASCADE,
    FOREIGN KEY (jornada_padrao_id) REFERENCES horarios_trabalho(id) ON DELETE SET NULL,
    UNIQUE KEY unique_empresa_config (empresa_id)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci 
COMMENT='Configurações específicas por empresa';

-- 2. Inserir configurações padrão para empresas existentes
INSERT INTO empresas_config (empresa_id, jornada_padrao_id)
SELECT 
    e.id,
    (SELECT h.id FROM horarios_trabalho h WHERE h.empresa_id = e.id AND h.ativo = 1 LIMIT 1)
FROM empresas e
WHERE e.ativa = 1 
AND NOT EXISTS (SELECT 1 FROM empresas_config ec WHERE ec.empresa_id = e.id)
ON DUPLICATE KEY UPDATE 
    data_atualizacao = CURRENT_TIMESTAMP;

-- 3. Criar índices para performance (ignorar se já existem)
CREATE INDEX idx_empresas_config_empresa ON empresas_config(empresa_id);
CREATE INDEX idx_empresas_config_jornada ON empresas_config(jornada_padrao_id);

-- 4. Verificar estrutura criada
SELECT 
    'Tabela empresas_config criada com sucesso!' as status,
    COUNT(*) as total_configuracoes
FROM empresas_config;

-- 5. Mostrar configurações atuais
SELECT 
    e.razao_social,
    e.nome_fantasia,
    h.nome_horario as jornada_padrao,
    ec.tolerancia_atraso,
    ec.permite_banco_horas
FROM empresas e
LEFT JOIN empresas_config ec ON e.id = ec.empresa_id
LEFT JOIN horarios_trabalho h ON ec.jornada_padrao_id = h.id
WHERE e.ativa = 1
ORDER BY e.razao_social;
