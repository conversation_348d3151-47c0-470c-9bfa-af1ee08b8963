#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de Correção - Erro 500 Sistema de Relatórios
Data: 06/06/2025
Baseado na documentação oficial do Flask e Chart.js

PROBLEMAS IDENTIFICADOS:
1. Serialização de datetime não tratada corretamente
2. Função mascarar_dados_relatorio chamada incorretamente
3. Falta de validação de dados antes da serialização
4. Tratamento de erro inadequado

SOLUÇÕES IMPLEMENTADAS:
- Converter datetime para string antes da serialização
- Corrigir chamada da função mascarar_dados_relatorio
- Adicionar validação robusta de dados
- Implementar tratamento de erro baseado na documentação Flask
"""

import os
import sys
import shutil
from datetime import datetime

def fazer_backup_arquivo(caminho_arquivo):
    """Cria backup do arquivo original"""
    if os.path.exists(caminho_arquivo):
        backup_path = f"{caminho_arquivo}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(caminho_arquivo, backup_path)
        print(f"✅ Backup criado: {backup_path}")
        return backup_path
    return None

def aplicar_correcao_app_relatorios():
    """Aplica correções no arquivo app_relatorios.py"""
    
    arquivo_original = "var/www/controle-ponto/app_relatorios.py"
    
    # Fazer backup
    backup_path = fazer_backup_arquivo(arquivo_original)
    
    print("🔧 Aplicando correções no app_relatorios.py...")
    
    # Ler arquivo original
    with open(arquivo_original, 'r', encoding='utf-8') as f:
        conteudo = f.read()
    
    # CORREÇÃO 1: Importar json do Flask para serialização correta
    correcao_import = """from flask import Blueprint, render_template, request, jsonify, session, make_response, current_app, json
from datetime import datetime, date, timedelta
import logging
import io
import csv
from pymysql.cursors import DictCursor
from utils.database import get_db_connection
from utils.auth import require_login
from utils.helpers import (
    mascarar_dados_relatorio, 
    RegistroPontoValidator,
    gerar_dados_grafico_pontos,
    obter_ip_usuario
)"""
    
    # Substituir imports antigos
    conteudo = conteudo.replace(
        "from flask import Blueprint, render_template, request, jsonify, session, make_response, current_app",
        correcao_import.split('\n')[0]
    )
    
    # CORREÇÃO 2: Função para converter datetime para string
    funcao_converter_datetime = '''
def converter_datetime_para_json(obj):
    """
    Converte objetos datetime para string em formato ISO.
    Baseado na documentação oficial do Flask para serialização JSON.
    """
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    if hasattr(obj, 'isoformat'):
        return obj.isoformat()
    return obj

def processar_registro_para_json(registro):
    """
    Processa um registro de ponto para ser seguro para serialização JSON.
    Aplica conversão de datetime e mascaramento de dados.
    """
    if not isinstance(registro, dict):
        return registro
    
    # Criar cópia para evitar modificar o original
    reg_processado = {}
    
    for chave, valor in registro.items():
        # Converter datetime para string
        if isinstance(valor, (datetime, date)):
            reg_processado[chave] = valor.isoformat()
        elif hasattr(valor, 'isoformat'):
            reg_processado[chave] = valor.isoformat()
        else:
            reg_processado[chave] = valor
    
    # Adicionar campos de descrição se não existirem
    if 'tipo_registro' in reg_processado and 'tipo_descricao' not in reg_processado:
        descricoes_tipo = {
            'entrada_manha': 'Entrada Manhã',
            'saida_almoco': 'Saída Almoço',
            'entrada_tarde': 'Entrada Tarde',
            'saida': 'Saída'
        }
        reg_processado['tipo_descricao'] = descricoes_tipo.get(
            reg_processado['tipo_registro'], 
            reg_processado['tipo_registro'].replace('_', ' ').title()
        )
    
    if 'metodo_registro' in reg_processado and 'metodo_descricao' not in reg_processado:
        descricoes_metodo = {
            'biometrico': 'Biométrico',
            'manual': 'Manual'
        }
        reg_processado['metodo_descricao'] = descricoes_metodo.get(
            reg_processado['metodo_registro'],
            reg_processado['metodo_registro'].title()
        )
    
    return reg_processado

'''
    
    # Inserir função após os imports
    posicao_insercao = conteudo.find("# Configurar logging")
    if posicao_insercao == -1:
        posicao_insercao = conteudo.find("logger = logging.getLogger(__name__)")
    
    if posicao_insercao != -1:
        conteudo = conteudo[:posicao_insercao] + funcao_converter_datetime + "\n\n" + conteudo[posicao_insercao:]
    
    # CORREÇÃO 3: Substituir o bloco problemático de processamento de registros
    bloco_antigo = """            # Processar registros para adicionar campos esperados e converter datetime
            registros_processados = []
            for registro in registros:
                # Converter datetime para string
                reg_processado = dict(registro)
                
                # Converter campos datetime para string
                if reg_processado.get('data_hora'):
                    reg_processado['data_hora'] = reg_processado['data_hora'].isoformat()
                if reg_processado.get('criado_em'):
                    reg_processado['criado_em'] = reg_processado['criado_em'].isoformat()
                
                # Adicionar campos de descrição
                if 'tipo_registro' in reg_processado:
                    descricoes_tipo = {
                        'entrada_manha': 'Entrada Manhã',
                        'saida_almoco': 'Saída Almoço', 
                        'entrada_tarde': 'Entrada Tarde',
                        'saida': 'Saída'
                    }
                    reg_processado['tipo_descricao'] = descricoes_tipo.get(
                        reg_processado['tipo_registro'], 
                        reg_processado['tipo_registro'].replace('_', ' ').title()
                    )
                
                if 'metodo_registro' in reg_processado:
                    descricoes_metodo = {
                        'biometrico': 'Biométrico',
                        'manual': 'Manual'
                    }
                    reg_processado['metodo_descricao'] = descricoes_metodo.get(
                        reg_processado['metodo_registro'],
                        reg_processado['metodo_registro'].title()
                    )
                
                registros_processados.append(reg_processado)"""
    
    bloco_novo = """            # Processar registros com segurança para JSON
            registros_processados = []
            for registro in registros:
                try:
                    reg_processado = processar_registro_para_json(dict(registro))
                    registros_processados.append(reg_processado)
                except Exception as e:
                    logger.warning(f"Erro ao processar registro {registro.get('id', 'unknown')}: {str(e)}")
                    continue"""
    
    conteudo = conteudo.replace(bloco_antigo, bloco_novo)
    
    # CORREÇÃO 4: Corrigir chamada da função mascarar_dados_relatorio
    conteudo = conteudo.replace(
        "registros_mascarados = [mascarar_dados_relatorio(r) for r in registros_processados]",
        f"registros_mascarados = [mascarar_dados_relatorio(r, session.get('nivel_acesso', 'usuario')) for r in registros_processados]"
    )
    
    # CORREÇÃO 5: Melhorar tratamento de erro baseado na documentação Flask
    bloco_erro_antigo = """        except Exception as e:
            import traceback
            # Gerar ID único para o erro
            error_id = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Log detalhado do erro
            logger.error(f"[BUSCAR REGISTROS] Erro ID: {error_id}")
            logger.error(f"[BUSCAR REGISTROS] Tipo de erro: {type(e).__name__}")
            logger.error(f"[BUSCAR REGISTROS] Mensagem: {str(e)}")
            logger.error(f"[BUSCAR REGISTROS] Traceback:\\n{traceback.format_exc()}")
            logger.error(f"[BUSCAR REGISTROS] Filtros que causaram erro: {filtros}")
            
            return jsonify({
                'success': False,
                'message': f'Erro interno do sistema\\nOcorreu um erro inesperado. ID: {error_id}\\n\\nCódigo: 500'
            }), 500"""
    
    bloco_erro_novo = """        except Exception as e:
            import traceback
            # Gerar ID único para o erro
            error_id = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Log detalhado do erro
            logger.error(f"[BUSCAR REGISTROS] Erro ID: {error_id}")
            logger.error(f"[BUSCAR REGISTROS] Tipo de erro: {type(e).__name__}")
            logger.error(f"[BUSCAR REGISTROS] Mensagem: {str(e)}")
            logger.error(f"[BUSCAR REGISTROS] Traceback:\\n{traceback.format_exc()}")
            logger.error(f"[BUSCAR REGISTROS] Filtros que causaram erro: {filtros}")
            
            # Resposta JSON estruturada baseada na documentação Flask
            error_response = {
                'success': False,
                'error_id': error_id,
                'message': 'Erro interno do sistema',
                'details': f'Ocorreu um erro inesperado. ID: {error_id}',
                'code': 500
            }
            
            return current_app.response_class(
                response=json.dumps(error_response),
                status=500,
                mimetype='application/json'
            )"""
    
    conteudo = conteudo.replace(bloco_erro_antigo, bloco_erro_novo)
    
    # Salvar arquivo corrigido
    with open(arquivo_original, 'w', encoding='utf-8') as f:
        f.write(conteudo)
    
    print("✅ Correções aplicadas no app_relatorios.py")
    return True

def main():
    """Função principal do script"""
    print("="*80)
    print("SCRIPT DE CORREÇÃO - ERRO 500 SISTEMA DE RELATÓRIOS")
    print("Baseado na documentação oficial do Flask e Chart.js")
    print("="*80)
    
    try:
        # Verificar se estamos no diretório correto
        if not os.path.exists("var/www/controle-ponto/app_relatorios.py"):
            print("❌ Erro: Arquivo app_relatorios.py não encontrado!")
            print("Execute este script na raiz do projeto RLPONTO-WEB")
            return False
        
        # Aplicar correções
        sucesso = aplicar_correcao_app_relatorios()
        
        if sucesso:
            print("\n" + "="*80)
            print("✅ CORREÇÕES APLICADAS COM SUCESSO!")
            print("="*80)
            print("\n📋 PRÓXIMOS PASSOS:")
            print("1. Envie o arquivo corrigido para o servidor:")
            print("   scp var/www/controle-ponto/app_relatorios.py root@************:/var/www/controle-ponto/")
            print("\n2. Reinicie o servidor web no servidor")
            print("\n3. Teste o sistema de relatórios novamente")
            print("\n4. Se funcionar, os logs de depuração podem ser removidos")
            return True
        else:
            print("❌ Falha ao aplicar correções")
            return False
            
    except Exception as e:
        print(f"❌ Erro durante execução: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 