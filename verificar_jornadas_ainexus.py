#!/usr/bin/env python3
"""
Script para verificar jornadas da AiNexus
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def verificar_jornadas_ainexus():
    """Verificar jornadas da empresa AiNexus"""
    print("🔍 VERIFICANDO JORNADAS DA AINEXUS")
    print("=" * 50)
    
    try:
        # Verificar empresa
        empresa = DatabaseManager.execute_query("SELECT * FROM empresas WHERE id = 11")
        if empresa:
            print(f"🏢 Empresa: {empresa[0]['razao_social']}")
        else:
            print("❌ Empresa ID 11 não encontrada")
            return
            
        # Verificar jornadas da empresa
        jornadas = DatabaseManager.execute_query("SELECT * FROM jornadas_trabalho WHERE empresa_id = 11")
        
        if jornadas:
            print(f"\n📋 JORNADAS ENCONTRADAS: {len(jornadas)}")
            for i, jornada in enumerate(jornadas, 1):
                print(f"\n   Jornada {i}:")
                print(f"   - ID: {jornada['id']}")
                print(f"   - Nome: {jornada['nome_jornada']}")
                print(f"   - Padrão: {'SIM' if jornada['padrao'] else 'NÃO'}")
                print(f"   - Ativa: {'SIM' if jornada['ativa'] else 'NÃO'}")
                print(f"   - Seg-Qui Entrada: {jornada['seg_qui_entrada']}")
                print(f"   - Seg-Qui Saída: {jornada['seg_qui_saida']}")
                print(f"   - Sexta Entrada: {jornada.get('sexta_entrada', 'N/A')}")
                print(f"   - Sexta Saída: {jornada.get('sexta_saida', 'N/A')}")
        else:
            print("\n❌ PROBLEMA: Empresa AiNexus não tem jornadas cadastradas!")
            print("   Isso explica por que o Richardson está com jornada padrão do sistema.")
            
            # Sugerir criação de jornada
            print("\n💡 SOLUÇÃO: Criar jornada padrão para AiNexus")
            resposta = input("Deseja criar uma jornada padrão para AiNexus? (s/n): ")
            
            if resposta.lower() == 's':
                criar_jornada_ainexus()
                
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()

def criar_jornada_ainexus():
    """Criar jornada padrão para AiNexus"""
    print("\n🔧 CRIANDO JORNADA PADRÃO PARA AINEXUS")
    print("=" * 50)
    
    try:
        # Dados da jornada padrão
        sql = """
        INSERT INTO jornadas_trabalho (
            empresa_id, nome_jornada, seg_qui_entrada, seg_qui_saida,
            sexta_entrada, sexta_saida, intervalo_inicio, intervalo_fim,
            tolerancia_entrada_minutos, padrao, ativa
        ) VALUES (
            11, 'Jornada Padrão AiNexus', '08:00:00', '17:00:00',
            '08:00:00', '17:00:00', '12:00:00', '13:00:00',
            15, 1, 1
        )
        """
        
        DatabaseManager.execute_query(sql, fetch_all=False)
        print("✅ Jornada padrão criada com sucesso!")
        
        # Verificar se foi criada
        jornadas = DatabaseManager.execute_query("SELECT * FROM jornadas_trabalho WHERE empresa_id = 11 AND padrao = 1")
        if jornadas:
            jornada = jornadas[0]
            print(f"📋 Jornada criada:")
            print(f"   - ID: {jornada['id']}")
            print(f"   - Nome: {jornada['nome_jornada']}")
            print(f"   - Horário: {jornada['seg_qui_entrada']} às {jornada['seg_qui_saida']}")

            # Agora atualizar o Richardson para usar esta jornada
            print(f"\n🔧 ATUALIZANDO RICHARDSON PARA USAR A NOVA JORNADA")
            sql_update = """
            UPDATE funcionarios SET
                horario_trabalho_id = %s,
                jornada_seg_qui_entrada = %s,
                jornada_seg_qui_saida = %s,
                jornada_sex_entrada = %s,
                jornada_sex_saida = %s,
                jornada_intervalo_entrada = %s,
                jornada_intervalo_saida = %s
            WHERE id = 1
            """

            params = (
                jornada['id'],
                jornada['seg_qui_entrada'],
                jornada['seg_qui_saida'],
                jornada['sexta_entrada'],
                jornada['sexta_saida'],
                jornada['intervalo_inicio'],
                jornada['intervalo_fim']
            )
            
            DatabaseManager.execute_query(sql_update, params, fetch_all=False)
            print("✅ Richardson atualizado com a nova jornada!")
            
    except Exception as e:
        print(f"❌ Erro ao criar jornada: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verificar_jornadas_ainexus()
