
# Tratamento de Batidas de Ponto com Falhas Humanas

Este documento descreve a lógica de implantação para um sistema de controle de ponto que trata automaticamente falhas comuns, como batidas ausentes, invertidas ou fora de ordem. O sistema aplica inferência e alertas para garantir a integridade da jornada de trabalho, sem depender exclusivamente da rigidez dos horários programados.

---

## 1. Entrada Ausente (primeira batida não registrada)

**Cenário:** O funcionário esquece de bater a entrada (batida 1), e realiza a primeira batida apenas no momento do intervalo.

**Ação do Sistema:**
- Marcar a batida como "entrada ausente"
- Não iniciar jornada automaticamente
- Gerar pendência para ajuste manual
- Exigir justificativa e possível autorização do RH
- Jornada não considerada para cálculos até correção

---

## 2. <PERSON><PERSON><PERSON> de Intervalo Ausente (segunda batida não registrada)

**Cenário:** O funcionário bate a entrada normalmente, mas esquece de registrar o início do intervalo.

**Ação do Sistema:**
- Ignorar batida 3 como retorno do intervalo
- Marcar retorno como "batida inválida: início de intervalo ausente"
- Emitir alerta de jornada incompleta
- Possibilidade de correção manual
- Validar jornada mínima para avaliar possível legalidade

---

## 3. Intervalo Não Registrado (bate apenas entrada e saída)

**Cenário:** Funcionário registra apenas a entrada e a saída (batida 1 e batida 4), sem qualquer registro de intervalo.

**Ação do Sistema:**
- Verificar se a jornada ultrapassa 6 horas
- Se sim, exigir registro de intervalo conforme legislação (ex: CLT)
- Marcar jornada como "intervalo não registrado"
- Gerar alerta e bloqueio de fechamento automático
- Permitir ajuste manual com justificativa

---

## 4. Retorno de Intervalo Ausente (batida 3 ausente)

**Cenário:** O funcionário bate a entrada (B1), o início do intervalo (B2), esquece o retorno (B3) e registra a saída (B4).

**Ação do Sistema:**
- Se batida 4 ocorrer pelo menos 60 minutos após batida 2, considerar B4 como retorno (batida 3)
- Marcar ausência da batida final (batida 4 real)
- Gerar alerta: "Fim de expediente ausente"
- Permitir fechamento com advertência
- Possibilidade de correção posterior

---

## 5. Início de Intervalo Ausente, Retorno Registrado

**Cenário:** O funcionário esquece de bater o início do intervalo (B2), mas bate o retorno (B3) e a saída (B4).

**Ação do Sistema:**
- Verificar se houve jornada longa suficiente para justificar um intervalo
- Marcar B3 como "possível retorno com início de intervalo ausente"
- Emitir pendência para validação
- Permitir correção pelo RH ou usuário com justificativa

---

## 6. Batidas Extras (mais de 4 no dia)

**Cenário:** O funcionário realiza 5 ou mais batidas no mesmo dia.

**Ação do Sistema:**
- Utilizar as 4 primeiras batidas válidas cronologicamente
- Marcar batidas extras como "não utilizadas"
- Emitir alerta de revisão
- Permitir ajuste manual, se necessário

---

## 7. Batidas Fora de Ordem (ex: retorno antes do início do intervalo)

**Cenário:** As batidas estão invertidas em relação ao esperado.

**Ação do Sistema:**
- Reordenar batidas com base no horário
- Reclassificar os tipos conforme a sequência lógica
- Se a sequência for inválida mesmo após reordenação, gerar erro crítico
- Bloquear fechamento automático da jornada

---

## 8. Jornada com Apenas 1 Batida

**Cenário:** Funcionário registra apenas a entrada (B1).

**Ação do Sistema:**
- Marcar como jornada incompleta
- Emitir alerta e gerar pendência
- Impedir qualquer tipo de fechamento ou cálculo automático
