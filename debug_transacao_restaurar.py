#!/usr/bin/env python3
"""
Debug específico da transação de restauração
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager
import pymysql

def debug_transacao_restaurar():
    """Debug da transação de restauração"""
    print("🔍 DEBUG: TRANSAÇÃO DE RESTAURAÇÃO")
    print("=" * 60)
    
    try:
        # 1. Verificar funcionários desligados
        print("📋 1. VERIFICANDO FUNCIONÁRIOS DESLIGADOS:")
        db = DatabaseManager()
        funcionarios_desligados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa
            FROM funcionarios_desligados 
            ORDER BY data_desligamento DESC
            LIMIT 1
        """)
        
        if not funcionarios_desligados:
            print("   ❌ Nenhum funcionário desligado encontrado")
            return False
        
        funcionario = funcionarios_desligados[0]
        funcionario_id = funcionario['funcionario_id_original']
        print(f"   ✅ Funcionário para teste: {funcionario['nome_completo']} (ID: {funcionario_id})")
        
        # 2. Fazer restauração manual com controle total da transação
        print(f"\n📋 2. RESTAURAÇÃO MANUAL COM TRANSAÇÃO CONTROLADA:")
        
        # Conectar diretamente ao banco com credenciais corretas
        connection = pymysql.connect(
            host='************',
            user='cavalcrod',
            password='200381',
            database='controle_ponto',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor,
            autocommit=False  # IMPORTANTE: Controle manual de transação
        )
        
        try:
            with connection.cursor() as cursor:
                print("   🔄 Iniciando transação manual...")
                
                # Buscar dados do funcionário desligado
                cursor.execute("SELECT * FROM funcionarios_desligados WHERE funcionario_id_original = %s", (funcionario_id,))
                funcionario_desligado = cursor.fetchone()
                
                if not funcionario_desligado:
                    print("   ❌ Funcionário não encontrado na tabela de desligados")
                    return False
                
                print(f"   ✅ Dados do funcionário carregados")
                
                # Verificar se já existe na tabela principal
                cursor.execute("SELECT id, ativo FROM funcionarios WHERE matricula_empresa = %s", (funcionario_desligado['matricula_empresa'],))
                funcionario_existente = cursor.fetchone()
                
                if funcionario_existente:
                    print(f"   ⚠️ Funcionário já existe na tabela principal (ID: {funcionario_existente['id']}, Ativo: {funcionario_existente['ativo']})")
                    
                    # Apenas reativar
                    cursor.execute("UPDATE funcionarios SET status_cadastro = 'Ativo', ativo = TRUE WHERE id = %s", (funcionario_existente['id'],))
                    print(f"   ✅ Funcionário reativado")
                    novo_id = funcionario_existente['id']
                else:
                    print(f"   🔄 Criando novo funcionário...")
                    
                    # Inserir novo funcionário com campos essenciais
                    insert_query = """
                    INSERT INTO funcionarios (
                        nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, 
                        nacionalidade, pis_pasep, endereco_cep, endereco_estado, 
                        telefone1, cargo, setor_obra, matricula_empresa, data_admissao, 
                        tipo_contrato, nivel_acesso, turno, tolerancia_ponto, 
                        status_cadastro, ativo, empresa_id
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                    """
                    
                    valores = [
                        funcionario_desligado['nome_completo'],
                        funcionario_desligado['cpf'],
                        funcionario_desligado['rg'] or 'N/A',
                        funcionario_desligado['data_nascimento'],
                        funcionario_desligado['sexo'],
                        funcionario_desligado['estado_civil'],
                        funcionario_desligado['nacionalidade'] or 'Brasileiro',
                        funcionario_desligado['pis_pasep'] or '00000000000',
                        funcionario_desligado['endereco_cep'] or '00000-000',
                        funcionario_desligado['endereco_estado'] or 'SP',
                        funcionario_desligado['telefone1'] or '(00) 00000-0000',
                        funcionario_desligado['cargo'],
                        funcionario_desligado['setor_obra'] or 'N/A',
                        funcionario_desligado['matricula_empresa'],
                        funcionario_desligado['data_admissao'],
                        funcionario_desligado['tipo_contrato'] or 'CLT',
                        funcionario_desligado['nivel_acesso'] or 'Funcionario',
                        funcionario_desligado['turno'] or 'Diurno',
                        funcionario_desligado['tolerancia_ponto'] or 5,
                        'Ativo',  # status_cadastro
                        True,     # ativo
                        funcionario_desligado['empresa_id']
                    ]
                    
                    cursor.execute(insert_query, valores)
                    novo_id = cursor.lastrowid
                    print(f"   ✅ Novo funcionário criado com ID: {novo_id}")
                
                # Verificar se foi inserido/atualizado
                cursor.execute("SELECT id, nome_completo, status_cadastro, ativo FROM funcionarios WHERE id = %s", (novo_id,))
                verificacao = cursor.fetchone()
                
                if verificacao:
                    print(f"   ✅ Verificação: {verificacao['nome_completo']} - Status: {verificacao['status_cadastro']}, Ativo: {verificacao['ativo']}")
                else:
                    print(f"   ❌ ERRO: Funcionário não encontrado após operação!")
                    connection.rollback()
                    return False
                
                # Remover da tabela de desligados
                cursor.execute("DELETE FROM funcionarios_desligados WHERE funcionario_id_original = %s", (funcionario_id,))
                print(f"   ✅ Funcionário removido da tabela de desligados")
                
                # COMMIT MANUAL
                print(f"   🔄 Fazendo COMMIT da transação...")
                connection.commit()
                print(f"   ✅ COMMIT realizado com sucesso!")
                
                # Verificação final fora da transação
                cursor.execute("SELECT id, nome_completo, status_cadastro, ativo FROM funcionarios WHERE id = %s", (novo_id,))
                verificacao_final = cursor.fetchone()
                
                if verificacao_final:
                    print(f"   ✅ VERIFICAÇÃO FINAL: {verificacao_final['nome_completo']} - Status: {verificacao_final['status_cadastro']}, Ativo: {verificacao_final['ativo']}")
                    return True
                else:
                    print(f"   ❌ ERRO: Funcionário não encontrado após COMMIT!")
                    return False
                
        except Exception as e:
            print(f"   ❌ ERRO na transação: {e}")
            connection.rollback()
            print(f"   🔄 ROLLBACK realizado")
            import traceback
            print(f"   Traceback: {traceback.format_exc()}")
            return False
        finally:
            connection.close()
        
    except Exception as e:
        print(f"\n❌ ERRO NO DEBUG: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🎯 DEBUG COMPLETO: TRANSAÇÃO DE RESTAURAÇÃO")
    print("=" * 70)
    
    sucesso = debug_transacao_restaurar()
    
    if sucesso:
        print("\n🎉 SUCESSO!")
        print("✅ Transação de restauração funcionando")
        print("✅ Funcionário restaurado com sucesso")
    else:
        print("\n❌ FALHA!")
        print("❌ Problema na transação de restauração")
