# ========================================
# BLUEPRINT REGISTRO DE PONTO - RLPONTO-WEB
# Data: 08/01/2025
# Descrição: Sistema de registro de ponto biométrico e manual
# ========================================

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from datetime import datetime, date, time, timedelta
from utils.auth import require_login
from utils.database import get_db_connection
from utils.helpers import mascarar_cpf, validar_cpf_formato
from pymysql.cursors import DictCursor
import logging
import ipaddress
import ntplib
import time as systime

# Configurar logger
logger = logging.getLogger(__name__)

# Criar Blueprint - Aceitando tanto hífen quanto underline para compatibilidade
registro_ponto_bp = Blueprint('registro_ponto', __name__, url_prefix='/registro-ponto')

# Criar um segundo blueprint com underscore para compatibilidade
registro_ponto_underscore_bp = Blueprint('registro_ponto_underscore', __name__, url_prefix='/registro_ponto')

# ========================================
# FUNÇÕES AUXILIARES
# ========================================

def normalizar_caminho_foto(foto_3x4):
    """
    Normaliza o caminho da foto para garantir que sempre comece com /static/
    
    Args:
        foto_3x4 (str): Caminho da foto do banco de dados
        
    Returns:
        str or None: Caminho normalizado ou None se não houver foto válida
    """
    if not foto_3x4:
        return None
    
    # Ignorar o placeholder padrão
    if foto_3x4 == '/static/images/funcionario_sem_foto.svg':
        return None
    
    # Se já começa com /static/, retornar como está
    if foto_3x4.startswith('/static/'):
        return foto_3x4
    
    # Se não começa com /static/, adicionar o prefixo
    if foto_3x4.startswith('static/'):
        return '/' + foto_3x4
    else:
        return '/static/' + foto_3x4

def validar_duplicata_registro(funcionario_id, tipo_registro, data_atual):
    """
    Valida se já existe um registro do mesmo tipo no dia atual.
    
    Args:
        funcionario_id (int): ID do funcionário
        tipo_registro (str): Tipo do registro (entrada_manha, saida_almoco, etc.)
        data_atual (date): Data atual para verificação
        
    Returns:
        dict: {'existe': bool, 'mensagem': str, 'registro_existente': dict ou None}
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # ✅ BUSCAR REGISTRO EXISTENTE COM MAIS DETALHES
        cursor.execute("""
            SELECT 
                rp.id,
                rp.data_hora,
                rp.metodo_registro,
                rp.observacoes,
                u.usuario as criado_por_nome
            FROM registros_ponto rp
            LEFT JOIN usuarios u ON rp.criado_por = u.id
            WHERE rp.funcionario_id = %s 
            AND rp.tipo_registro = %s 
            AND DATE(rp.data_hora) = %s
            ORDER BY rp.data_hora DESC
            LIMIT 1
        """, (funcionario_id, tipo_registro, data_atual))
        
        registro = cursor.fetchone()
        conn.close()
        
        if registro:
            # ✅ RETORNAR INFORMAÇÕES DETALHADAS DO REGISTRO EXISTENTE
            data_hora_existente = registro['data_hora'].strftime('%d/%m/%Y %H:%M')
            metodo = 'Biométrico' if registro['metodo_registro'] == 'biometrico' else 'Manual'
            criado_por = registro['criado_por_nome'] if registro['criado_por_nome'] else 'Sistema'
            
            tipo_descricao = {
                'entrada_manha': 'Entrada Manhã',
                'saida_almoco': 'Saída Almoço', 
                'entrada_tarde': 'Entrada Tarde',
                'saida': 'Saída'
            }.get(tipo_registro, tipo_registro)
            
            mensagem = f"Já existe um registro de '{tipo_descricao}' para hoje ({data_hora_existente}) - Método: {metodo}"
            if registro['metodo_registro'] == 'manual':
                mensagem += f" - Registrado por: {criado_por}"
                
            return {
                'existe': True,
                'mensagem': mensagem,
                'registro_existente': {
                    'id': registro['id'],
                    'data_hora': data_hora_existente,
                    'metodo': metodo,
                    'observacoes': registro['observacoes'],
                    'criado_por': criado_por
                }
            }
        
        return {
            'existe': False,
            'mensagem': 'Nenhum registro duplicado encontrado',
            'registro_existente': None
        }
        
    except Exception as e:
        logger.error(f"Erro ao validar duplicata: {str(e)}")
        return {
            'existe': False,
            'mensagem': f'Erro na validação: {str(e)}',
            'registro_existente': None
        }

def obter_horarios_funcionario(funcionario_id):
    """
    Obtém os horários de trabalho configurados para um funcionário.
    
    Args:
        funcionario_id (int): ID do funcionário
        
    Returns:
        dict: Horários configurados ou horários padrão
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # ✅ BUSCAR HORÁRIOS REAIS DO FUNCIONÁRIO
        cursor.execute("""
            SELECT 
                nome_completo,
                jornada_seg_qui_entrada,
                jornada_seg_qui_saida,
                jornada_sex_entrada,
                jornada_sex_saida,
                jornada_intervalo_entrada,
                jornada_intervalo_saida,
                tolerancia_ponto
            FROM funcionarios 
            WHERE id = %s AND ativo = TRUE
        """, (funcionario_id,))
        
        resultado = cursor.fetchone()
        conn.close()
        
        if not resultado:
            raise Exception(f"Funcionário {funcionario_id} não encontrado ou inativo")
        
        # ✅ CONVERTER TIMEDELTA PARA STRING FORMATADA
        def converter_tempo(tempo_obj):
            if tempo_obj is None:
                return None
            
            # Se for timedelta, converter para horas:minutos
            if hasattr(tempo_obj, 'total_seconds'):
                total_seconds = int(tempo_obj.total_seconds())
                horas = total_seconds // 3600
                minutos = (total_seconds % 3600) // 60
                return f"{horas:02d}:{minutos:02d}"
            
            # Se já for string no formato time, converter
            if hasattr(tempo_obj, 'strftime'):
                return tempo_obj.strftime('%H:%M')
            
            # Se já for string, retornar como está
            if isinstance(tempo_obj, str):
                return tempo_obj
            
            return str(tempo_obj)
        
        # ✅ MONTAR HORÁRIOS REAIS BASEADOS NO CADASTRO
        horarios_reais = {
            'entrada_manha': converter_tempo(resultado['jornada_seg_qui_entrada']),
            'saida_almoco': converter_tempo(resultado['jornada_intervalo_entrada']),  # Início do intervalo = saída almoço
            'entrada_tarde': converter_tempo(resultado['jornada_intervalo_saida']),   # Fim do intervalo = entrada tarde
            'saida': converter_tempo(resultado['jornada_seg_qui_saida']),
            'tolerancia_minutos': resultado['tolerancia_ponto'] or 10,
            'nome_horario': 'Horário Administrativo'
        }
        
        logger.info(f"[HORARIOS] Horários obtidos para funcionário {funcionario_id}: {horarios_reais}")
        return horarios_reais
            
    except Exception as e:
        logger.error(f"Erro ao obter horários do funcionário {funcionario_id}: {str(e)}")
        # ✅ RETORNAR HORÁRIOS PADRÃO EM CASO DE ERRO
        return {
            'entrada_manha': '08:00',
            'saida_almoco': '12:00',
            'entrada_tarde': '13:00',
            'saida': '17:00',
            'tolerancia_minutos': 10,
            'nome_horario': 'Padrão (Erro)'
        }

def registrar_ponto_no_banco(funcionario_id, tipo_registro, metodo_registro, observacoes=None, qualidade_biometria=None):
    """
    Registra um ponto no banco de dados.
    
    Args:
        funcionario_id (int): ID do funcionário
        tipo_registro (str): Tipo do registro (entrada_manha, saida_almoco, etc.)
        metodo_registro (str): Método usado (biometrico, manual)
        observacoes (str, opcional): Observações do registro
        qualidade_biometria (int, opcional): Qualidade da biometria (0-100)
        
    Returns:
        dict: Resultado da operação
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Obter IP do usuário
        ip_origem = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
        if ',' in ip_origem:
            ip_origem = ip_origem.split(',')[0].strip()
        
        # ID do usuário logado (para registros manuais)
        criado_por = session.get('user_id') if metodo_registro == 'manual' else None
        
        # Inserir registro
        cursor.execute("""
            INSERT INTO registros_ponto 
            (funcionario_id, tipo_registro, metodo_registro, qualidade_biometria, observacoes, ip_origem, criado_por, data_hora)
            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
        """, (funcionario_id, tipo_registro, metodo_registro, qualidade_biometria, observacoes, ip_origem, criado_por))
        
        registro_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        logger.info(f"Ponto registrado - ID: {registro_id}, Funcionário: {funcionario_id}, Tipo: {tipo_registro}, Método: {metodo_registro}")
        
        return {
            'success': True,
            'registro_id': registro_id,
            'message': 'Ponto registrado com sucesso!'
        }
        
    except Exception as e:
        logger.error(f"Erro ao registrar ponto: {str(e)}")
        return {
            'success': False,
            'message': f'Erro ao registrar ponto: {str(e)}'
        }

def verificar_e_criar_tabela_registros():
    """
    Verifica se a tabela registros_ponto existe e cria se necessário.
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Verificar se a tabela existe
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables 
            WHERE table_schema = 'controle_ponto' 
            AND table_name = 'registros_ponto'
        """)
        
        result = cursor.fetchone()
        existe = result['COUNT(*)'] > 0
        
        if not existe:
            logger.warning("[CORREÇÃO] Tabela registros_ponto não existe. Criando...")
            
            # Criar a tabela
            cursor.execute("""
                CREATE TABLE registros_ponto (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    funcionario_id INT NOT NULL,
                    tipo_registro ENUM('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida') NOT NULL,
                    metodo_registro ENUM('biometrico', 'manual') NOT NULL,
                    data_hora DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    qualidade_biometria INT NULL,
                    observacoes TEXT NULL,
                    ip_origem VARCHAR(45) NULL,
                    criado_por INT NULL,
                    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_funcionario_data (funcionario_id, data_hora),
                    INDEX idx_tipo_registro (tipo_registro),
                    INDEX idx_metodo_registro (metodo_registro),
                    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
                    FOREIGN KEY (criado_por) REFERENCES usuarios(id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            conn.commit()
            logger.info("[CORREÇÃO] Tabela registros_ponto criada com sucesso!")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"[CORREÇÃO] Erro ao verificar/criar tabela registros_ponto: {str(e)}")
        return False

def obter_hora_atual(fuso_horario_manaus=True):
    """
    Obtém a hora atual do sistema, com preferência por NTP quando disponível.
    Se solicitado, ajusta para o fuso horário de Manaus/AM (GMT-4).

    Returns:
        datetime: Data e hora atual, ajustada para fuso de Manaus se solicitado
    """
    hora_atual = None
    
    # Tentar obter hora via NTP
    try:
        client = ntplib.NTPClient()
        response = client.request('pool.ntp.org', timeout=1)
        hora_atual = datetime.fromtimestamp(response.tx_time)
        logger.info("Hora obtida via NTP")
    except Exception as e:
        logger.warning(f"Erro ao obter hora via NTP: {str(e)}. Usando hora local.")
        hora_atual = datetime.now()
    
    # Ajustar para fuso de Manaus (GMT-4) se solicitado
    if fuso_horario_manaus:
        # A implementação ideal usaria pytz ou datetime.astimezone()
        # Esta é uma aproximação simplificada para o fuso de Manaus (-4h)
        # Em produção com Python 3.9+, use zoneinfo ou pytz
        
        # Verificamos se a hora já está no fuso correto
        # Se a diferença for maior que 4 horas, ajustamos
        # Essa lógica assume que o servidor está em GMT
        hora_utc = datetime.utcnow()
        diferenca = (hora_atual - hora_utc).total_seconds() / 3600
        
        # Se não estiver próximo do fuso de Manaus (-4h)
        if abs(diferenca + 4) > 1:
            logger.info(f"Ajustando fuso: diferença atual é {diferenca}h do UTC")
            # Ajustar para GMT-4 (Manaus)
            hora_atual = hora_utc.replace(tzinfo=None) - datetime.timedelta(hours=4)
    
    return hora_atual

def validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario):
    """
    Valida se o tipo de registro solicitado é permitido no horário atual.
    
    Args:
        tipo_registro (str): Tipo de registro (entrada_manha, saida_almoco, etc.)
        horarios_funcionario (dict): Horários configurados do funcionário
    
    Returns:
        dict: {'permitido': bool, 'mensagem': str, 'horario_liberado': bool}
    """
    # Obter hora atual no fuso de Manaus
    hora_atual = obter_hora_atual(fuso_horario_manaus=True)
    hora_atual_str = hora_atual.strftime('%H:%M')
    
    # Converter horários de string para objetos time para comparação
    def string_para_time(hora_str):
        if not hora_str:
            return None
        try:
            horas, minutos = map(int, hora_str.split(':'))
            return time(horas, minutos)
        except (ValueError, AttributeError):
            return None
    
    entrada_manha = string_para_time(horarios_funcionario['entrada_manha'])
    saida_almoco = string_para_time(horarios_funcionario['saida_almoco'])
    entrada_tarde = string_para_time(horarios_funcionario['entrada_tarde'])
    saida = string_para_time(horarios_funcionario['saida'])
    
    # Tolerância em minutos
    tolerancia = horarios_funcionario['tolerancia_minutos']
    
    # Converter hora atual para objeto time
    hora_atual_obj = hora_atual.time()
    
    # Determinar o período do dia em que estamos
    # Isso nos ajuda a decidir quais tipos de registro são apropriados
    periodo_atual = None
    
    if entrada_manha and saida_almoco:
        # Período da manhã (entre entrada_manha e saida_almoco)
        if entrada_manha <= hora_atual_obj <= saida_almoco:
            periodo_atual = "manha"
    
    if saida_almoco and entrada_tarde:
        # Período de almoço (entre saida_almoco e entrada_tarde)
        if saida_almoco <= hora_atual_obj <= entrada_tarde:
            periodo_atual = "almoco"
    
    if entrada_tarde and saida:
        # Período da tarde (entre entrada_tarde e saida)
        if entrada_tarde <= hora_atual_obj <= saida:
            periodo_atual = "tarde"
    
    # Se estamos depois do horário de saída
    if saida and hora_atual_obj > saida:
        periodo_atual = "apos_expediente"
    
    # Se estamos antes do horário de entrada
    if entrada_manha and hora_atual_obj < entrada_manha:
        periodo_atual = "antes_expediente"
    
    logger.info(f"[VALIDAÇÃO] Hora atual: {hora_atual_str}, Período determinado: {periodo_atual}")
    
    # Validar cada tipo de registro com base no período atual
    if tipo_registro == 'entrada_manha':
        # Entrada manhã é permitida a partir do horário de entrada menos tolerância
        if entrada_manha is None:
            return {'permitido': False, 'mensagem': 'Horário de entrada não configurado', 'horario_liberado': False}
        
        hora_min = datetime.combine(date.today(), entrada_manha)
        hora_min = (hora_min - datetime.timedelta(minutes=tolerancia)).time()
        
        # Entrada manhã é mais apropriada no início do dia
        if periodo_atual in ["antes_expediente", "manha"]:
            # Se for antes do horário mínimo
            if hora_atual_obj < hora_min:
                return {
                    'permitido': False, 
                    'mensagem': f"Entrada ainda não liberada. Disponível a partir de {hora_min.strftime('%H:%M')}", 
                    'horario_liberado': False
                }
            return {'permitido': True, 'mensagem': 'Entrada registrada com sucesso', 'horario_liberado': True}
        else:
            # Ainda permitimos, mas não é o período ideal
            return {'permitido': True, 'mensagem': 'Entrada registrada com sucesso (fora do período ideal)', 'horario_liberado': True}
    
    elif tipo_registro == 'saida_almoco':
        # Saída almoço é permitida a partir do horário configurado
        if saida_almoco is None:
            return {'permitido': False, 'mensagem': 'Horário de saída para almoço não configurado', 'horario_liberado': False}
        
        hora_min = datetime.combine(date.today(), saida_almoco)
        hora_min = (hora_min - datetime.timedelta(minutes=tolerancia)).time()
        
        # Saída almoço é mais apropriada no final da manhã
        # Mas também deve ser permitida durante o período de almoço
        if periodo_atual in ["manha", "almoco"]:
            return {'permitido': True, 'mensagem': 'Saída para almoço registrada com sucesso', 'horario_liberado': True}
        else:
            # Ainda permitimos, mas não é o período ideal
            return {'permitido': True, 'mensagem': 'Saída para almoço registrada com sucesso (fora do período ideal)', 'horario_liberado': True}
    
    elif tipo_registro == 'entrada_tarde':
        # Entrada tarde (retorno almoço) é permitida a partir do horário configurado
        if entrada_tarde is None:
            return {'permitido': False, 'mensagem': 'Horário de retorno de almoço não configurado', 'horario_liberado': False}
        
        hora_min = datetime.combine(date.today(), entrada_tarde)
        hora_min = (hora_min - datetime.timedelta(minutes=tolerancia)).time()
        
        # Entrada tarde é mais apropriada durante o período de almoço ou início da tarde
        if periodo_atual in ["almoco", "tarde"]:
            return {'permitido': True, 'mensagem': 'Retorno de almoço registrado com sucesso', 'horario_liberado': True}
        else:
            # Ainda permitimos, mas não é o período ideal
            return {'permitido': True, 'mensagem': 'Retorno de almoço registrado com sucesso (fora do período ideal)', 'horario_liberado': True}
        
    elif tipo_registro == 'saida':
        # Saída é permitida a partir do horário configurado
        if saida is None:
            return {'permitido': False, 'mensagem': 'Horário de saída não configurado', 'horario_liberado': False}
        
        hora_min = datetime.combine(date.today(), saida)
        hora_min = (hora_min - datetime.timedelta(minutes=tolerancia)).time()
        
        # Saída é mais apropriada no final da tarde ou após o expediente
        if periodo_atual in ["tarde", "apos_expediente"]:
            return {'permitido': True, 'mensagem': 'Saída registrada com sucesso', 'horario_liberado': True}
        else:
            # Ainda permitimos, mas não é o período ideal
            return {'permitido': True, 'mensagem': 'Saída registrada com sucesso (fora do período ideal)', 'horario_liberado': True}
    
    else:
        return {'permitido': False, 'mensagem': f'Tipo de registro não reconhecido: {tipo_registro}', 'horario_liberado': False}

# ========================================
# ROTAS PRINCIPAIS
# ========================================

@registro_ponto_bp.route('/biometrico')
@require_login
def pagina_registro_biometrico():
    """
    Página de registro de ponto biométrico.
    Interface para captura biométrica via ZK4500.
    """
    try:
        context = {
            'titulo': 'Registro de Ponto Biométrico',
            'usuario_logado': session.get('usuario', 'Usuário'),
            'data_atual': date.today().strftime('%d/%m/%Y'),
            'hora_atual': datetime.now().strftime('%H:%M')
        }
        
        return render_template('registro_ponto/biometrico.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar página biométrica: {str(e)}")
        # Corrigir redirecionamento para função correta
        return redirect(url_for('funcionarios.index'))

# Duplicar para o blueprint com underscore
@registro_ponto_underscore_bp.route('/biometrico')
@require_login
def pagina_registro_biometrico_underscore():
    """Versão duplicada da rota biometrico com underscore"""
    return pagina_registro_biometrico()

@registro_ponto_bp.route('/manual')
@require_login
def pagina_registro_manual():
    """
    Página de registro de ponto manual.
    Lista funcionários para seleção e registro manual.
    """
    try:
        logger.info(f"[DEBUG] Iniciando pagina_registro_manual para usuário: {session.get('usuario', 'desconhecido')}")
        
        # Verificar e criar tabela se necessário
        if not verificar_e_criar_tabela_registros():
            logger.error("[DEBUG] Falha ao verificar/criar tabela registros_ponto")
            
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        logger.info(f"[DEBUG] Nível de acesso: {nivel_acesso}")
        
        # Obter lista de funcionários ativos
        logger.info("[DEBUG] Iniciando conexão com banco de dados")
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        logger.info("[DEBUG] Conexão com banco estabelecida")
        
        logger.info("[DEBUG] Executando query para buscar funcionários")
        cursor.execute("""
            SELECT 
                f.id,
                f.nome_completo,
                f.cpf,
                f.matricula_empresa,
                f.cargo,
                COALESCE(f.setor, 'Administrativo') as setor,
                f.foto_3x4,
                COALESCE(e.nome_fantasia, 'Empresa Padrão') AS empresa,
                COALESCE(ht.nome_horario, 'Horário Padrão') as nome_horario
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
            WHERE f.ativo = TRUE
            ORDER BY f.nome_completo
        """)
        
        funcionarios_raw = cursor.fetchall()
        logger.info(f"[DEBUG] Query executada com sucesso. {len(funcionarios_raw)} funcionários encontrados")
        conn.close()
        logger.info("[DEBUG] Conexão com banco fechada")
        
        # Processar lista de funcionários
        logger.info("[DEBUG] Iniciando processamento da lista de funcionários")
        funcionarios = []
        for i, f in enumerate(funcionarios_raw):
            try:
                # Validar se o CPF existe antes de tentar mascarar
                cpf_original = f['cpf'] if f['cpf'] else ''
                cpf_exibicao = ''
                
                if cpf_original:
                    try:
                        from utils.helpers import mascarar_cpf
                        cpf_exibicao = mascarar_cpf(cpf_original) if nivel_acesso != 'admin' else cpf_original
                    except Exception as e:
                        logger.warning(f"[DEBUG] Erro ao mascarar CPF para funcionário {f['nome_completo']}: {str(e)}")
                        cpf_exibicao = '***.***.***-**'
                else:
                    cpf_exibicao = 'CPF não informado'
                
                funcionario = {
                    'id': f['id'],
                    'nome_completo': f['nome_completo'] or 'Nome não informado',
                    'cpf': cpf_original,
                    'cpf_exibicao': cpf_exibicao,
                    'matricula_empresa': f['matricula_empresa'] or 'Sem matrícula',
                    'cargo': f['cargo'] or 'Não informado',
                    'setor': f['setor'],
                    'foto_url': normalizar_caminho_foto(f['foto_3x4']),
                    'empresa': f['empresa'],
                    'horario_trabalho': f['nome_horario']
                }
                funcionarios.append(funcionario)
                if i < 3:  # Log apenas os primeiros 3 para não sobrecarregar
                    logger.info(f"[DEBUG] Funcionário {i+1} processado: {funcionario['nome_completo']}")
            except Exception as e:
                logger.error(f"[DEBUG] Erro ao processar funcionário {i+1}: {str(e)}")
                continue
                
        logger.info(f"[DEBUG] Lista de funcionários processada. Total: {len(funcionarios)}")
        
        logger.info("[DEBUG] Montando contexto do template")
        context = {
            'titulo': 'Registro de Ponto Manual',
            'funcionarios': funcionarios,
            'total_funcionarios': len(funcionarios),
            'data_atual': date.today().strftime('%d/%m/%Y'),
            'hora_atual': datetime.now().strftime('%H:%M'),
            'nivel_acesso': nivel_acesso
        }
        logger.info(f"[DEBUG] Contexto montado com {len(context)} variáveis")
        
        logger.info("[DEBUG] Tentando renderizar template registro_ponto/manual.html")
        resultado = render_template('registro_ponto/manual.html', **context)
        logger.info("[DEBUG] Template renderizado com sucesso")
        
        return resultado
        
    except Exception as e:
        error_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        logger.error(f"[ERROR] {error_id} - Erro ao carregar página manual: {str(e)}")
        logger.error(f"[ERROR] {error_id} - Traceback: ", exc_info=True)
        
        # Retornar página de erro personalizada em vez de redirect
        return render_template('erro.html', 
                             codigo=500,
                             titulo="Erro interno do sistema",
                             mensagem=f"Ocorreu um erro inesperado. ID: {error_id}",
                             error_id=error_id), 500

# Duplicar para o blueprint com underscore
@registro_ponto_underscore_bp.route('/manual')
@require_login
def pagina_registro_manual_underscore():
    """Versão duplicada da rota manual com underscore"""
    return pagina_registro_manual()

# ========================================
# APIs DE REGISTRO
# ========================================

@registro_ponto_bp.route('/api/registrar-biometrico', methods=['POST'])
@require_login
def api_registrar_biometrico():
    """
    API para processar registro de ponto biométrico.
    Recebe dados da captura biométrica e registra no banco.
    """
    try:
        dados = request.get_json()
        
        # Validar dados obrigatórios
        funcionario_id = dados.get('funcionario_id')
        tipo_registro = dados.get('tipo_registro')
        qualidade_biometria = dados.get('qualidade_biometria')
        
        if not all([funcionario_id, tipo_registro]):
            return jsonify({
                'success': False,
                'message': 'Dados obrigatórios não informados'
            }), 400
        
        # Validar se funcionário existe e está ativo
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        cursor.execute("""
            SELECT id, nome_completo, ativo 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,))
        
        funcionario = cursor.fetchone()
        conn.close()
        
        if not funcionario:
            return jsonify({
                'success': False,
                'message': 'Funcionário não encontrado'
            }), 404
        
        if not funcionario['ativo']:  # ativo
            return jsonify({
                'success': False,
                'message': 'Funcionário inativo'
            }), 400
        
        # Validar duplicata
        data_atual = date.today()
        duplicata = validar_duplicata_registro(funcionario_id, tipo_registro, data_atual)
        
        if duplicata['existe']:
            return jsonify({
                'success': False,
                'message': duplicata['mensagem'],
                'registro_existente': duplicata['registro_existente']
            }), 400
        
        # Registrar ponto
        resultado = registrar_ponto_no_banco(
            funcionario_id=funcionario_id,
            tipo_registro=tipo_registro,
            metodo_registro='biometrico',
            qualidade_biometria=qualidade_biometria
        )
        
        if resultado['success']:
            return jsonify({
                'success': True,
                'message': resultado['message'],
                'registro_id': resultado['registro_id'],
                'funcionario_nome': funcionario['nome_completo'],
                'data_hora': datetime.now().strftime('%d/%m/%Y %H:%M')
            })
        else:
            return jsonify({
                'success': False,
                'message': resultado['message']
            }), 500
        
    except Exception as e:
        logger.error(f"Erro no registro biométrico: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

# Duplicar API para o blueprint com underscore
@registro_ponto_underscore_bp.route('/api/registrar-biometrico', methods=['POST'])
@require_login
def api_registrar_biometrico_underscore():
    """Versão duplicada da API com underscore"""
    return api_registrar_biometrico()

@registro_ponto_bp.route('/api/registrar-manual', methods=['POST'])
@require_login
def api_registrar_manual():
    """
    API para processar registro de ponto manual.
    Valida dados, horários e registra ponto manual no banco.
    """
    try:
        dados = request.get_json()
        
        # Validar dados obrigatórios
        funcionario_id = dados.get('funcionario_id')
        tipo_registro = dados.get('tipo_registro')
        observacoes = dados.get('observacoes', '')
        
        if not all([funcionario_id, tipo_registro]):
            return jsonify({
                'success': False,
                'message': 'Dados obrigatórios não informados'
            }), 400
        
        # Validar se funcionário existe e está ativo
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        cursor.execute("""
            SELECT id, nome_completo, ativo 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,))
        
        funcionario = cursor.fetchone()
        conn.close()
        
        if not funcionario:
            return jsonify({
                'success': False,
                'message': 'Funcionário não encontrado'
            }), 404
        
        if not funcionario['ativo']:  # ativo
            return jsonify({
                'success': False,
                'message': 'Funcionário inativo'
            }), 400
        
        # Validar duplicata
        data_atual = date.today()
        duplicata = validar_duplicata_registro(funcionario_id, tipo_registro, data_atual)
        
        if duplicata['existe']:
            return jsonify({
                'success': False,
                'message': duplicata['mensagem'],
                'registro_existente': duplicata['registro_existente']
            }), 400
        
        # ✅ NOVA VALIDAÇÃO - Verificar se tipo de registro é permitido no horário atual
        horarios_funcionario = obter_horarios_funcionario(funcionario_id)
        validacao_horario = validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario)
        
        if not validacao_horario['permitido']:
            return jsonify({
                'success': False,
                'message': validacao_horario['mensagem']
            }), 400
            
        # Hora atual para mensagem
        hora_atual = obter_hora_atual().strftime('%H:%M')
        
        # Registrar ponto manual
        resultado = registrar_ponto_no_banco(
            funcionario_id=funcionario_id,
            tipo_registro=tipo_registro,
            metodo_registro='manual',
            observacoes=f"{observacoes if observacoes else ''} [Horário atual: {hora_atual}]"
        )
        
        if resultado['success']:
            return jsonify({
                'success': True,
                'message': resultado['message'],
                'registro_id': resultado['registro_id'],
                'funcionario_nome': funcionario['nome_completo'],
                'data_hora': datetime.now().strftime('%d/%m/%Y %H:%M')
            })
        else:
            return jsonify({
                'success': False,
                'message': resultado['message']
            }), 500
        
    except Exception as e:
        logger.error(f"Erro no registro manual: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

# Duplicar API para o blueprint com underscore
@registro_ponto_underscore_bp.route('/api/registrar-manual', methods=['POST'])
@require_login
def api_registrar_manual_underscore():
    """Versão duplicada da API com underscore"""
    return api_registrar_manual()

@registro_ponto_bp.route('/api/obter-horarios/<int:funcionario_id>')
@require_login
def api_obter_horarios(funcionario_id):
    """
    API para obter horários de trabalho de um funcionário.
    Usado no modal de registro manual.
    Filtra tipos de registro disponíveis com base no horário atual.
    """
    try:
        horarios = obter_horarios_funcionario(funcionario_id)
        
        # Converter times para strings
        horarios_formatados = {
            'entrada_manha': horarios['entrada_manha'],
            'saida_almoco': horarios['saida_almoco'],
            'entrada_tarde': horarios['entrada_tarde'],
            'saida': horarios['saida'],
            'tolerancia_minutos': horarios['tolerancia_minutos'],
            'nome_horario': horarios['nome_horario']
        }
        
        # Gerar opções de tipos de registro baseadas nos horários
        todos_tipos = []
        tipos_liberados = []
        
        # Hora atual para mostrar no frontend
        hora_atual = obter_hora_atual().strftime('%H:%M')
        
        if horarios['entrada_manha']:
            tipo = {
                'value': 'entrada_manha',
                'text': f'Entrada Manhã ({horarios_formatados["entrada_manha"]})'
            }
            todos_tipos.append(tipo)
            
            # ✅ VALIDAR SE ESTE TIPO ESTÁ LIBERADO NO HORÁRIO ATUAL
            validacao = validar_tipo_registro_por_horario('entrada_manha', horarios)
            if validacao['horario_liberado']:
                tipos_liberados.append(tipo)
        
        if horarios['saida_almoco']:
            tipo = {
                'value': 'saida_almoco',
                'text': f'Saída Almoço ({horarios_formatados["saida_almoco"]})'
            }
            todos_tipos.append(tipo)
            
            # ✅ VALIDAR SE ESTE TIPO ESTÁ LIBERADO NO HORÁRIO ATUAL
            validacao = validar_tipo_registro_por_horario('saida_almoco', horarios)
            if validacao['horario_liberado']:
                tipos_liberados.append(tipo)
        
        if horarios['entrada_tarde']:
            tipo = {
                'value': 'entrada_tarde',
                'text': f'Retorno Almoço ({horarios_formatados["entrada_tarde"]})'
            }
            todos_tipos.append(tipo)
            
            # ✅ VALIDAR SE ESTE TIPO ESTÁ LIBERADO NO HORÁRIO ATUAL
            validacao = validar_tipo_registro_por_horario('entrada_tarde', horarios)
            if validacao['horario_liberado']:
                tipos_liberados.append(tipo)
        
        if horarios['saida']:
            tipo = {
                'value': 'saida',
                'text': f'Fim Expediente ({horarios_formatados["saida"]})'
            }
            todos_tipos.append(tipo)
            
            # ✅ VALIDAR SE ESTE TIPO ESTÁ LIBERADO NO HORÁRIO ATUAL
            validacao = validar_tipo_registro_por_horario('saida', horarios)
            if validacao['horario_liberado']:
                tipos_liberados.append(tipo)
        
        return jsonify({
            'success': True,
            'horarios': horarios_formatados,
            'tipos_disponiveis': tipos_liberados,
            'todos_tipos': todos_tipos,
            'hora_atual': hora_atual
        })
        
    except Exception as e:
        logger.error(f"Erro ao obter horários do funcionário {funcionario_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao obter horários: {str(e)}'
        }), 500

# Duplicar API para o blueprint com underscore
@registro_ponto_underscore_bp.route('/api/obter-horarios/<int:funcionario_id>')
@require_login
def api_obter_horarios_underscore(funcionario_id):
    """Versão duplicada da API com underscore"""
    return api_obter_horarios(funcionario_id)

# ========================================
# TRATAMENTO DE ERROS
# ========================================

@registro_ponto_bp.errorhandler(404)
def handle_404(error):
    """Tratamento de erro 404 para o blueprint."""
    return jsonify({
        'success': False,
        'message': 'Endpoint não encontrado'
    }), 404

@registro_ponto_underscore_bp.errorhandler(404)
def handle_404_underscore(error):
    """Tratamento de erro 404 para o blueprint com underscore."""
    return jsonify({
        'success': False,
        'message': 'Endpoint não encontrado'
    }), 404

@registro_ponto_bp.errorhandler(500)
def handle_500(error):
    """Tratamento de erro 500 para o blueprint."""
    logger.error(f"Erro interno no blueprint registro_ponto: {str(error)}")
    return jsonify({
        'success': False,
        'message': 'Erro interno do servidor'
    }), 500

@registro_ponto_underscore_bp.errorhandler(500)
def handle_500_underscore(error):
    """Tratamento de erro 500 para o blueprint com underscore."""
    logger.error(f"Erro interno no blueprint registro_ponto_underscore: {str(error)}")
    return jsonify({
        'success': False,
        'message': 'Erro interno do servidor'
    }), 500

# ========================================
# FIM DO BLUEPRINT REGISTRO DE PONTO
# ======================================== 