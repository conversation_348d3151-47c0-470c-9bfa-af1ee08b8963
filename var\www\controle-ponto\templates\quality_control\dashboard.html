<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quality Control Dashboard | RLPONTO-WEB v1.0</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: #0a0e27; 
            color: #fff; 
            font-family: 'JetBrains Mono', 'Courier New', monospace;
            font-size: 14px;
        }
        
        .navbar { 
            background: linear-gradient(135deg, #1a1d3e 0%, #2d3561 100%); 
            border-bottom: 2px solid #00ff88;
        }
        
        .card { 
            background: rgba(26, 29, 62, 0.9); 
            border: 1px solid #404578; 
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        .card-header { 
            background: rgba(45, 53, 97, 0.8); 
            border-bottom: 1px solid #00ff88;
        }
        
        .btn-primary { 
            background: linear-gradient(135deg, #00ff88 0%, #00cc70 100%); 
            border: none; 
            color: #000;
            font-weight: 600;
        }
        
        .btn-danger { 
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%); 
            border: none;
        }
        
        .btn-warning { 
            background: linear-gradient(135deg, #ffa502 0%, #ff8c00 100%); 
            border: none;
            color: #000;
        }
        
        .btn-info { 
            background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%); 
            border: none;
        }
        
        .badge.bg-success { background: #00ff88 !important; color: #000; }
        .badge.bg-danger { background: #ff4757 !important; }
        .badge.bg-warning { background: #ffa502 !important; color: #000; }
        .badge.bg-info { background: #3742fa !important; }
        
        .terminal {
            background: #0c0c0c;
            border: 1px solid #00ff88;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            color: #00ff88;
        }
        
        .terminal pre {
            margin: 0;
            color: #00ff88;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(26, 29, 62, 0.9) 0%, rgba(45, 53, 97, 0.7) 100%);
            border: 1px solid #404578;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, #00ff88, transparent);
            animation: loading 2s infinite;
        }
        
        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #00ff88;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }
        
        .metric-label {
            color: #a8b3d9;
            font-size: 0.9rem;
            margin-top: 8px;
        }
        
        .problem-item {
            background: rgba(220, 53, 69, 0.1);
            border-left: 3px solid #dc3545;
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 4px;
        }
        
        .check-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #404578;
            border-radius: 8px;
            margin-bottom: 10px;
            background: rgba(26, 29, 62, 0.5);
        }
        
        .check-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 18px;
        }
        
        .check-success { background: rgba(0, 255, 136, 0.2); color: #00ff88; }
        .check-danger { background: rgba(255, 71, 87, 0.2); color: #ff4757; }
        .check-warning { background: rgba(255, 165, 2, 0.2); color: #ffa502; }
        
        .backup-file {
            background: rgba(40, 167, 69, 0.1);
            border-left: 3px solid #28a745;
            padding: 8px;
            margin-bottom: 3px;
            border-radius: 4px;
        }
        
        .modal-content {
            background: #1a1d3e;
            border: 1px solid #404578;
        }
        
        .modal-header {
            border-bottom: 1px solid #404578;
        }
        
        .table-dark {
            background: rgba(26, 29, 62, 0.9);
        }
        
        .table-dark td, .table-dark th {
            border-color: #404578;
            font-size: 13px;
        }
        
        .progress {
            background: rgba(26, 29, 62, 0.9);
            border: 1px solid #404578;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #00ff88, #00cc70);
        }
        
        .tab-content {
            background: rgba(26, 29, 62, 0.5);
            border: 1px solid #404578;
            border-top: none;
            border-radius: 0 0 12px 12px;
            padding: 20px;
        }
        
        .nav-tabs {
            border-bottom: 1px solid #404578;
        }
        
        .nav-tabs .nav-link {
            background: rgba(26, 29, 62, 0.7);
            border: 1px solid #404578;
            color: #a8b3d9;
            margin-right: 5px;
            border-radius: 12px 12px 0 0;
        }
        
        .nav-tabs .nav-link.active {
            background: rgba(0, 255, 136, 0.2);
            border-color: #00ff88;
            color: #00ff88;
        }

        .backup-restore-item {
            background: rgba(13, 110, 253, 0.1);
            border: 1px solid rgba(13, 110, 253, 0.3) !important;
            transition: all 0.3s ease;
        }

        .backup-restore-item:hover {
            background: rgba(13, 110, 253, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-terminal me-2"></i>
                <strong>QA Control Terminal</strong>
                <span class="badge bg-success ms-2">v1.0</span>
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user-shield me-2"></i>
                    <strong>cavalcrod@rlponto-qa</strong>
                </span>
                <button class="btn btn-outline-danger btn-sm" onclick="logout()">
                    <i class="fas fa-power-off me-1"></i>
                    EXIT
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Toolbar de Ações -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body p-3">
                        <div class="d-flex flex-wrap gap-2">
                            <button class="btn btn-primary" onclick="runFullCheck()">
                                <i class="fas fa-play me-1"></i> FULL SCAN
                            </button>
                            <button class="btn btn-warning" onclick="createSystemBackup()">
                                <i class="fas fa-database me-1"></i> BACKUP NOW
                            </button>
                            <button class="btn btn-danger" onclick="showRestoreOptions()">
                                <i class="fas fa-undo me-1"></i> RESTORE BACKUP
                            </button>
                            <button class="btn btn-info" onclick="showSystemInfo()">
                                <i class="fas fa-microchip me-1"></i> SYS INFO
                            </button>
                            <button class="btn btn-danger" onclick="showLogs()">
                                <i class="fas fa-file-alt me-1"></i> LOGS
                            </button>
                            <button class="btn btn-success" onclick="location.reload()">
                                <i class="fas fa-sync-alt me-1"></i> REFRESH
                            </button>
                            <button class="btn btn-secondary ms-auto" onclick="showBackupsList()">
                                <i class="fas fa-history me-1"></i> BACKUP HISTORY
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Métricas do Sistema -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="metric-value">{{ "%.1f"|format(stats.success_rate) }}%</div>
                <div class="metric-label">
                    <i class="fas fa-check-circle me-1"></i>
                    SUCCESS RATE
                </div>
            </div>
            
            <div class="stat-card">
                <div class="metric-value">{{ "%.1f"|format(stats.uptime_percentage) }}%</div>
                <div class="metric-label">
                    <i class="fas fa-server me-1"></i>
                    SYSTEM UPTIME
                </div>
            </div>
            
            <div class="stat-card">
                <div class="metric-value">{{ system_status.issues_found }}</div>
                <div class="metric-label">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    ACTIVE ISSUES
                </div>
            </div>
            
            <div class="stat-card">
                <div class="metric-value">{{ stats.trend|upper }}</div>
                <div class="metric-label">
                    <i class="fas fa-chart-line me-1"></i>
                    TREND STATUS
                </div>
            </div>
        </div>

        <!-- Sistema Anti-Regressão Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <i class="fas fa-shield-alt me-2"></i>
                        <h5 class="mb-0">SISTEMA ANTI-REGRESSÃO</h5>
                        <span class="badge bg-{{ 'success' if system_status.overall_status == 'success' else 'danger' if system_status.overall_status == 'error' else 'warning' }} ms-auto">
                            {{ system_status.overall_status|upper }}
                        </span>
                    </div>
                    <div class="card-body">
                        {% if system_status.backup_info %}
                        <div class="alert alert-success mb-3">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>BACKUP AUTOMÁTICO CRIADO:</strong> 
                            {{ system_status.backup_info.total_files }} arquivos salvos ({{ system_status.backup_info.total_size_mb }} MB)
                            <br>
                            <small><strong>Local:</strong> {{ system_status.backup_info.backup_directory }}</small>
                        </div>
                        {% endif %}

                        <!-- Recursos do Sistema -->
                        {% if system_status.system_resources %}
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <h6><i class="fas fa-microchip me-2"></i>CPU</h6>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar" role="progressbar" style="width: {{ system_status.system_resources.cpu_usage }}%"></div>
                                </div>
                                <small>{{ system_status.system_resources.cpu_usage }}% utilização</small>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-memory me-2"></i>MEMÓRIA</h6>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar" role="progressbar" style="width: {{ system_status.system_resources.memory_percent }}%"></div>
                                </div>
                                <small>{{ system_status.system_resources.memory_used_gb }}/{{ system_status.system_resources.memory_total_gb }} GB</small>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-hdd me-2"></i>DISCO</h6>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar" role="progressbar" style="width: {{ system_status.system_resources.disk_percent }}%"></div>
                                </div>
                                <small>{{ system_status.system_resources.disk_used_gb }}/{{ system_status.system_resources.disk_total_gb }} GB</small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Abas de Análise Técnica -->
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-tabs" id="analysisTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="problems-tab" data-bs-toggle="tab" data-bs-target="#problems" type="button">
                            <i class="fas fa-bug me-1"></i> PROBLEMAS ({{ system_status.issues_found }})
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="modules-tab" data-bs-toggle="tab" data-bs-target="#modules" type="button">
                            <i class="fas fa-cubes me-1"></i> MÓDULOS
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="database-tab" data-bs-toggle="tab" data-bs-target="#database" type="button">
                            <i class="fas fa-database me-1"></i> DATABASE
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="imports-tab" data-bs-toggle="tab" data-bs-target="#imports" type="button">
                            <i class="fas fa-project-diagram me-1"></i> IMPORTS
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="backups-tab" data-bs-toggle="tab" data-bs-target="#backups" type="button">
                            <i class="fas fa-archive me-1"></i> BACKUPS
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content" id="analysisTabContent">
                    <!-- Aba Problemas -->
                    <div class="tab-pane fade show active" id="problems" role="tabpanel">
                        {% if system_status.detailed_issues %}
                            <h6 class="mb-3"><i class="fas fa-exclamation-triangle me-2"></i>PROBLEMAS IDENTIFICADOS:</h6>
                            {% for issue in system_status.detailed_issues %}
                            <div class="problem-item">
                                <i class="fas fa-bug me-2"></i>
                                <code>{{ issue }}</code>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                                <h4 class="text-success">SISTEMA LIMPO</h4>
                                <p class="text-muted">Nenhum problema detectado no sistema</p>
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Aba Módulos -->
                    <div class="tab-pane fade" id="modules" role="tabpanel">
                        <div class="row">
                            {% for check in system_status.checks %}
                                {% if check.name == 'Carregamento de Módulos' %}
                                    <div class="col-12">
                                        <h6 class="mb-3">
                                            <i class="fas fa-cubes me-2"></i>
                                            ANÁLISE DE MÓDULOS ({{ check.passed }}/{{ check.total_tested }})
                                        </h6>
                                        {% if check.successes %}
                                            {% for success in check.successes %}
                                            <div class="check-item">
                                                <div class="check-icon check-success">
                                                    <i class="fas fa-check"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <strong>{{ success.module }}</strong>
                                                    {% if success.analysis %}
                                                        <span class="badge bg-info ms-2">{{ success.analysis.size_kb }} KB</span>
                                                        <span class="badge bg-secondary ms-1">{{ success.analysis.function_count }} funções</span>
                                                    {% endif %}
                                                    <br>
                                                    <small class="text-muted">{{ success.description }}</small>
                                                    {% if success.analysis and success.analysis.file_path %}
                                                    <br>
                                                    <small><code>{{ success.analysis.file_path }}</code></small>
                                                    {% endif %}
                                                </div>
                                                <button class="btn btn-sm btn-outline-primary" onclick="showModuleDetails('{{ success.module }}')">
                                                    <i class="fas fa-info"></i>
                                                </button>
                                            </div>
                                            {% endfor %}
                                        {% endif %}
                                        {% if check.failures %}
                                            <h6 class="text-danger mt-3">FALHAS:</h6>
                                            {% for failure in check.failures %}
                                            <div class="problem-item">
                                                <i class="fas fa-times me-2"></i>
                                                <code>{{ failure }}</code>
                                            </div>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Aba Database -->
                    <div class="tab-pane fade" id="database" role="tabpanel">
                        {% for check in system_status.checks %}
                            {% if check.name == 'Conectividade do Banco' %}
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-server me-2"></i>SERVIDOR</h6>
                                        <div class="terminal">
                                            <pre>Host: {{ check.server_info.host if check.server_info else 'N/A' }}
Database: {{ check.server_info.database if check.server_info else 'N/A' }}
Version: {{ check.server_info.version if check.server_info else 'N/A' }}
Uptime: {{ check.server_info.uptime_seconds if check.server_info else 'N/A' }}s
Conexões Ativas: {{ check.server_info.active_connections if check.server_info else 'N/A' }}</pre>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-table me-2"></i>ESTRUTURA</h6>
                                        <div class="terminal">
                                            <pre>Total de Tabelas: {{ check.tables.total_tables if check.tables else 'N/A' }}
Usuários: {{ check.data_counts.usuarios if check.data_counts else 'N/A' }}
Funcionários: {{ check.data_counts.funcionarios if check.data_counts else 'N/A' }}

Performance:
- Conexão: {{ check.connection_time_ms if check.connection_time_ms else 'N/A' }}ms
- Status: {{ check.performance.health|upper if check.performance else 'N/A' }}</pre>
                                        </div>
                                    </div>
                                </div>
                                {% if check.tables and check.tables.table_names %}
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6><i class="fas fa-list me-2"></i>TABELAS ENCONTRADAS</h6>
                                        <div class="row">
                                            {% for table in check.tables.table_names %}
                                            <div class="col-md-3 mb-2">
                                                <span class="badge bg-secondary w-100">{{ table }}</span>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                    </div>
                    
                    <!-- Aba Imports -->
                    <div class="tab-pane fade" id="imports" role="tabpanel">
                        {% for check in system_status.checks %}
                            {% if check.name == 'Importações Cross-Module' %}
                                <h6 class="mb-3">
                                    <i class="fas fa-project-diagram me-2"></i>
                                    MAPEAMENTO DE DEPENDÊNCIAS ({{ check.successes|length if check.successes else 0 }}/{{ check.total_tested }})
                                </h6>
                                {% if check.results %}
                                    {% for description, result in check.results.items() %}
                                    <div class="check-item">
                                        <div class="check-icon check-{{ 'success' if result.status == 'success' else 'danger' }}">
                                            <i class="fas fa-{{ 'check' if result.status == 'success' else 'times' }}"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <strong>{{ description }}</strong>
                                            {% if result.status == 'success' %}
                                                <span class="badge bg-success ms-2">{{ result.import_time_ms }}ms</span>
                                            {% else %}
                                                <br><small class="text-danger">{{ result.error }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                    </div>
                    
                    <!-- Aba Backups -->
                    <div class="tab-pane fade" id="backups" role="tabpanel">
                        {% if system_status.backup_info %}
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-info-circle me-2"></i>ÚLTIMO BACKUP</h6>
                                <div class="terminal">
                                    <pre>Timestamp: {{ system_status.backup_info.timestamp }}
Diretório: {{ system_status.backup_info.backup_directory }}
Arquivos: {{ system_status.backup_info.total_files }}
Tamanho Total: {{ system_status.backup_info.total_size_mb }} MB
Tipo: {{ system_status.backup_info.backup_type }}</pre>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-file-archive me-2"></i>ARQUIVOS SALVOS</h6>
                                <div style="max-height: 200px; overflow-y: auto;">
                                    {% for file in system_status.backup_info.files %}
                                        {% if file.backup_name %}
                                        <div class="backup-file">
                                            <i class="fas fa-file me-1"></i>
                                            <strong>{{ file.backup_name }}</strong>
                                            <br>
                                            <small>{{ file.size_kb }} KB - {{ file.original_path }}</small>
                                        </div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button class="btn btn-warning me-2" onclick="showBackupsList()">
                                    <i class="fas fa-history me-1"></i> VER HISTÓRICO COMPLETO
                                </button>
                                <button class="btn btn-info" onclick="openBackupDirectory()">
                                    <i class="fas fa-folder-open me-1"></i> ABRIR DIRETÓRIO
                                </button>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="mb-2">
                            <i class="fas fa-terminal me-2 text-success"></i>
                            RLPONTO-WEB v1.0 - Quality Control Terminal
                        </h6>
                        <small class="text-muted">
                            Sistema Anti-Regressão Ativo | Última verificação: {{ last_update.strftime('%d/%m/%Y %H:%M:%S') }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Detalhes -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailsModalTitle">Detalhes Técnicos</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="detailsModalContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Funções principais
        async function runFullCheck() {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> SCANNING...';
            
            try {
                const response = await fetch('/qa/api/run-check', { method: 'POST' });
                const result = await response.json();
                
                if (result.status === 'success') {
                    location.reload();
                } else {
                    showAlert('Erro na verificação: ' + result.message, 'danger');
                }
            } catch (error) {
                showAlert('Erro de conexão: ' + error.message, 'danger');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-play me-1"></i> FULL SCAN';
            }
        }

        async function createSystemBackup() {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> CREATING...';
            
            try {
                const response = await fetch('/qa/api/backup', { method: 'POST' });
                const result = await response.json();
                
                if (result.status === 'success') {
                    showAlert(`Backup criado com sucesso!`, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('Erro no backup: ' + result.message, 'danger');
                }
            } catch (error) {
                showAlert('Erro de conexão: ' + error.message, 'danger');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-database me-1"></i> BACKUP NOW';
            }
        }

        async function showRestoreOptions() {
            try {
                const response = await fetch('/qa/api/restore-options');
                const result = await response.json();
                
                if (result.status === 'success') {
                    const options = result.restore_options;
                    let content = `
                        <h6><i class="fas fa-info-circle me-2"></i>OPÇÕES DE RESTAURAÇÃO</h6>
                        <div class="terminal mb-3">
                            <pre>Diretório: ${options.backup_directory}
Backups de Arquivos: ${result.total_file_backups}
Backups de Banco: ${result.total_db_backups}</pre>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-file-archive me-2"></i>RESTAURAR ARQUIVOS</h6>
                    `;
                    
                    if (options.file_backups.length > 0) {
                        options.file_backups.forEach(backup => {
                            content += `
                                <div class="backup-restore-item mb-2 p-3 border rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>${backup.timestamp}</strong>
                                            <br>
                                            <small class="text-muted">
                                                ${backup.total_files} arquivos | ${backup.size_mb} MB | ${backup.type}
                                            </small>
                                        </div>
                                        <button class="btn btn-sm btn-warning" onclick="restoreFiles('${backup.log_file}')">
                                            <i class="fas fa-undo me-1"></i> RESTAURAR
                                        </button>
                                    </div>
                                </div>
                            `;
                        });
                    } else {
                        content += '<p class="text-muted">Nenhum backup de arquivos disponível</p>';
                    }
                    
                    content += `
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-database me-2"></i>RESTAURAR BANCO DE DADOS</h6>
                    `;
                    
                    if (options.database_backups.length > 0) {
                        options.database_backups.forEach(backup => {
                            content += `
                                <div class="backup-restore-item mb-2 p-3 border rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>${backup.filename}</strong>
                                            <br>
                                            <small class="text-muted">
                                                ${backup.size_mb} MB | ${new Date(backup.created).toLocaleString()}
                                            </small>
                                        </div>
                                        <button class="btn btn-sm btn-danger" onclick="restoreDatabase('${backup.filename}')">
                                            <i class="fas fa-database me-1"></i> RESTAURAR
                                        </button>
                                    </div>
                                </div>
                            `;
                        });
                    } else {
                        content += '<p class="text-muted">Nenhum backup de banco disponível</p>';
                    }
                    
                    content += `
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>ATENÇÃO:</strong> A restauração criará backup automático dos arquivos/banco atuais antes de proceder.
                        </div>
                    `;
                    
                    showModal('Opções de Restauração', content);
                } else {
                    showAlert('Erro ao obter opções de restauração: ' + result.message, 'danger');
                }
            } catch (error) {
                showAlert('Erro de conexão: ' + error.message, 'danger');
            }
        }

        async function restoreFiles(logFile) {
            if (!confirm('Tem certeza que deseja restaurar os arquivos deste backup? Um backup dos arquivos atuais será criado automaticamente.')) {
                return;
            }
            
            try {
                const response = await fetch('/qa/api/restore-files', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ backup_log_file: logFile })
                });
                const result = await response.json();
                
                if (result.status === 'success') {
                    showAlert(`Restauração concluída! ${result.restored_files.length} arquivos restaurados.`, 'success');
                    setTimeout(() => location.reload(), 3000);
                } else {
                    showAlert('Erro na restauração: ' + result.message, 'danger');
                }
            } catch (error) {
                showAlert('Erro de conexão: ' + error.message, 'danger');
            }
        }

        async function restoreDatabase(sqlFile) {
            if (!confirm('ATENÇÃO: Você está prestes a restaurar o banco de dados. Isso substituirá TODOS os dados atuais! Um backup será criado automaticamente. Continuar?')) {
                return;
            }
            
            try {
                showAlert('Iniciando restauração do banco de dados... Isso pode levar alguns minutos.', 'info');
                
                const response = await fetch('/qa/api/restore-database', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sql_backup_file: sqlFile })
                });
                const result = await response.json();
                
                if (result.status === 'success') {
                    showAlert('Banco de dados restaurado com sucesso!', 'success');
                    setTimeout(() => location.reload(), 3000);
                } else {
                    showAlert('Erro na restauração do banco: ' + result.message, 'danger');
                }
            } catch (error) {
                showAlert('Erro de conexão: ' + error.message, 'danger');
            }
        }

        async function showSystemInfo() {
            try {
                const response = await fetch('/qa/api/system-info');
                const result = await response.json();
                
                if (result.status === 'success') {
                    const info = result.system_info;
                    let content = `
                        <h6><i class="fas fa-server me-2"></i>RECURSOS DO SISTEMA</h6>
                        <div class="terminal mb-3">
                            <pre>CPU: ${info.resources.cpu_usage}% | RAM: ${info.resources.memory_used_gb}/${info.resources.memory_total_gb} GB
Disco: ${info.resources.disk_used_gb}/${info.resources.disk_total_gb} GB (${info.resources.disk_percent}%)
Python: ${info.python_version.split(' ')[0]}
Diretório: ${info.working_directory}</pre>
                        </div>
                        
                        <h6><i class="fas fa-folder me-2"></i>SISTEMA DE ARQUIVOS</h6>
                        <div class="terminal mb-3">
                            <pre>Backup Dir: ${info.filesystem.backup_directory}
Backup Exists: ${info.filesystem.backup_exists}
Total Files: ${info.filesystem.total_files}
Permissions: ${info.filesystem.permissions}</pre>
                        </div>
                        
                        <h6><i class="fas fa-cubes me-2"></i>MÓDULOS CARREGADOS (Top 20)</h6>
                        <div class="row">
                    `;
                    
                    info.installed_modules.slice(0, 20).forEach(module => {
                        content += `<div class="col-md-6"><span class="badge bg-secondary mb-1 w-100">${module}</span></div>`;
                    });
                    
                    content += '</div>';
                    
                    showModal('Informações do Sistema', content);
                } else {
                    showAlert('Erro ao obter informações: ' + result.message, 'danger');
                }
            } catch (error) {
                showAlert('Erro de conexão: ' + error.message, 'danger');
            }
        }

        async function showBackupsList() {
            try {
                const response = await fetch('/qa/api/backup-list');
                const result = await response.json();
                
                if (result.status === 'success') {
                    let content = `
                        <h6><i class="fas fa-info-circle me-2"></i>INFORMAÇÕES</h6>
                        <div class="terminal mb-3">
                            <pre>Diretório: ${result.backup_directory}
Total de Backups: ${result.total_backups}</pre>
                        </div>
                        
                        <h6><i class="fas fa-history me-2"></i>HISTÓRICO DE BACKUPS</h6>
                        <div class="table-responsive">
                            <table class="table table-dark table-sm">
                                <thead>
                                    <tr>
                                        <th>Timestamp</th>
                                        <th>Arquivos</th>
                                        <th>Tamanho</th>
                                        <th>Tipo</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;
                    
                    result.backups.forEach(backup => {
                        content += `
                            <tr>
                                <td><code>${backup.timestamp}</code></td>
                                <td><span class="badge bg-info">${backup.total_files}</span></td>
                                <td><span class="badge bg-success">${backup.size_mb} MB</span></td>
                                <td><span class="badge bg-secondary">${backup.type}</span></td>
                            </tr>
                        `;
                    });
                    
                    content += `
                                </tbody>
                            </table>
                        </div>
                    `;
                    
                    showModal('Histórico de Backups', content);
                } else {
                    showAlert('Erro ao listar backups: ' + result.message, 'danger');
                }
            } catch (error) {
                showAlert('Erro de conexão: ' + error.message, 'danger');
            }
        }

        async function showLogs() {
            try {
                const response = await fetch('/qa/api/detailed-check', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: 'logs' })
                });
                const result = await response.json();
                
                if (result.status === 'success') {
                    const logs = result.data;
                    let content = `
                        <h6><i class="fas fa-chart-bar me-2"></i>ESTATÍSTICAS</h6>
                        <div class="terminal mb-3">
                            <pre>Arquivos Analisados: ${logs.files_analyzed}
Total de Linhas: ${logs.analysis.total_lines}
Erros Encontrados: ${logs.error_count}
Warnings: ${logs.warning_count}</pre>
                        </div>
                    `;
                    
                    if (logs.analysis.files_found.length > 0) {
                        content += `
                            <h6><i class="fas fa-file-alt me-2"></i>ARQUIVOS DE LOG</h6>
                            <div class="table-responsive mb-3">
                                <table class="table table-dark table-sm">
                                    <thead>
                                        <tr><th>Arquivo</th><th>Linhas</th><th>Tamanho</th></tr>
                                    </thead>
                                    <tbody>
                        `;
                        
                        logs.analysis.files_found.forEach(file => {
                            content += `
                                <tr>
                                    <td><code>${file.file}</code></td>
                                    <td><span class="badge bg-info">${file.lines}</span></td>
                                    <td><span class="badge bg-success">${file.size_kb} KB</span></td>
                                </tr>
                            `;
                        });
                        
                        content += '</tbody></table></div>';
                    }
                    
                    if (logs.analysis.errors.length > 0) {
                        content += '<h6 class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>ERROS</h6>';
                        logs.analysis.errors.forEach(error => {
                            content += `<div class="problem-item"><i class="fas fa-bug me-2"></i><code>${error.message}</code></div>`;
                        });
                    }
                    
                    showModal('Análise de Logs', content);
                } else {
                    showAlert('Erro ao analisar logs: ' + result.message, 'danger');
                }
            } catch (error) {
                showAlert('Erro de conexão: ' + error.message, 'danger');
            }
        }

        function showModuleDetails(moduleName) {
            showAlert(`Análise detalhada do módulo ${moduleName} em desenvolvimento`, 'info');
        }

        function openBackupDirectory() {
            showAlert('Para acessar os backups, navegue até: ./backup-build/', 'info');
        }

        function logout() {
            if (confirm('Deseja realmente sair do Quality Control Terminal?')) {
                window.location.href = '/qa/logout';
            }
        }

        // Funções auxiliares
        function showModal(title, content) {
            document.getElementById('detailsModalTitle').textContent = title;
            document.getElementById('detailsModalContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('detailsModal')).show();
        }

        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 end-0 m-3`;
            alertDiv.style.zIndex = '9999';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // Auto-refresh a cada 2 minutos
        setInterval(() => {
            console.log('Auto-refreshing system status...');
            location.reload();
        }, 120000);
        
        // Atualizar timestamp a cada segundo
        setInterval(() => {
            const now = new Date();
            document.title = `QA Terminal [${now.toLocaleTimeString()}] - RLPONTO-WEB`;
        }, 1000);
    </script>
</body>
</html>