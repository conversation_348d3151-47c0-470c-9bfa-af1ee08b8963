# Script PowerShell para executar SQL de empresa principal
# RLPONTO-WEB - Sistema de Controle de Ponto

Write-Host "RLPONTO-WEB - Criacao da Estrutura de Empresa Principal" -ForegroundColor Green
Write-Host "============================================================"

# Verificar se arquivo SQL existe
$arquivoSQL = "sql\empresa_principal_clientes.sql"
if (-not (Test-Path $arquivoSQL)) {
    Write-Host "Arquivo SQL nao encontrado: $arquivoSQL" -ForegroundColor Red
    exit 1
}

Write-Host "Arquivo SQL encontrado: $arquivoSQL" -ForegroundColor Green

# Executar MySQL
Write-Host "Executando script SQL no banco de dados..." -ForegroundColor Yellow

# Verificar se mysql está disponível
try {
    $mysqlVersion = mysql --version
    Write-Host "MySQL encontrado: $mysqlVersion" -ForegroundColor Green
} catch {
    Write-Host "MySQL nao encontrado no PATH" -ForegroundColor Red
    exit 1
}

# Executar o arquivo SQL
try {
    Write-Host "Executando comandos SQL..." -ForegroundColor Yellow

    # Usar cmd para executar o mysql com redirecionamento
    $command = "mysql -u cavalcrod -p controle_ponto < `"$arquivoSQL`""
    cmd /c $command

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Script executado com sucesso!" -ForegroundColor Green
    } else {
        Write-Host "Erro na execucao. Codigo: $LASTEXITCODE" -ForegroundColor Red
    }

} catch {
    Write-Host "Erro na execucao: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "Processo concluido!" -ForegroundColor Green
