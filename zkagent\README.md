# 🌉 ZKAgent Professional - Ponte Biométrica

**Versão:** 1.0-CORRIGIDO  
**Função:** Ponte entre site/sistema web e hardware biométrico ZK4500  
**Arquitetura:** Site ↔ ZKAgent ↔ Hardware ZK4500

---

## 🏗️ **ARQUITETURA CORRETA**

```
[SEU SITE/SISTEMA] ←→ HTTP ←→ [ZKAGENT:5001] ←→ USB ←→ [HARDWARE ZK4500]
```

**O ZKAgent é uma PONTE que:**
1. Recebe requisições HTTP do seu site
2. Comunica com hardware ZK4500 via SDK
3. Retorna template biométrico para o site
4. O site salva no banco via seu próprio backend

**❌ O ZKAgent NÃO:**
- Conecta a backends externos
- Armazena dados biométricos
- Substitui seu backend
- Processa regras de negócio

---

## 🚀 **COMO USAR NO SEU SITE**

### **Integração JavaScript**
```javascript
// Verificar se ZKAgent está online
async function verificarZKAgent() {
    try {
        const response = await fetch('http://localhost:5001/ping');
        const data = await response.json();
        return data.status === 'ok';
    } catch (error) {
        console.error('ZKAgent offline:', error);
        return false;
    }
}

// Capturar biometria
async function capturarBiometria() {
    try {
        const response = await fetch('http://localhost:5001/capture', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });
        
        const data = await response.json();
        
        if (data.template) {
            // Sucesso - enviar template para seu backend
            await salvarBiometriaNoBackend(data.template);
            return { success: true, template: data.template };
        } else {
            return { success: false, error: data.error };
        }
    } catch (error) {
        return { success: false, error: 'Erro de conexão' };
    }
}

// Salvar no seu backend
async function salvarBiometriaNoBackend(template) {
    await fetch('/api/funcionarios/biometria', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            funcionario_id: getFuncionarioId(),
            template_biometrico: template,
            data_captura: new Date().toISOString()
        })
    });
}
```

---

## 📡 **API ENDPOINTS**

### **GET /ping**
Verificar se ZKAgent está online
```json
{
    "status": "ok",
    "version": "1.0-CORRIGIDO",
    "timestamp": "2025-01-06T10:30:00"
}
```

### **POST /capture** ⭐ **PRINCIPAL**
Capturar template biométrico
```json
// Sucesso
{
    "template": "base64_encoded_template...",
    "size": 2048,
    "quality": 85,
    "real": true,
    "timestamp": "2025-01-06T10:30:00"
}

// Erro
{
    "error": "Nenhum dispositivo conectado",
    "timestamp": "2025-01-06T10:30:00"
}
```

### **GET /status**
Status completo do sistema
```json
{
    "version": "1.0-CORRIGIDO",
    "status": "Conectado",
    "devices": 1,
    "sdkMode": "REAL",
    "port": 5001,
    "timestamp": "2025-01-06T10:30:00"
}
```

---

## 🎯 **RESUMO PARA O DESENVOLVEDOR**

**O ZKAgent é um microsserviço que:**
1. Roda na porta 5001
2. Expõe API REST para captura biométrica
3. Seu site faz requisições HTTP para capturar
4. Você salva o resultado no seu banco de dados

**É isso! Simples e direto! 🚀**
