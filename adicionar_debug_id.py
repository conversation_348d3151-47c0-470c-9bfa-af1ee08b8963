#!/usr/bin/env python3
"""
Script para adicionar logs de debug específicos para capturar o erro 'id'.
"""

import os
import shutil
from datetime import datetime

def fazer_backup():
    """
    Faz backup do arquivo original.
    """
    arquivo_original = '/var/www/controle-ponto/app_funcionarios.py'
    backup_nome = f'/var/www/controle-ponto/app_funcionarios_backup_debug_{datetime.now().strftime("%Y%m%d_%H%M%S")}.py'
    
    shutil.copy2(arquivo_original, backup_nome)
    print(f"✅ Backup criado: {backup_nome}")
    return backup_nome

def adicionar_debug_logs():
    """
    Adiciona logs de debug específicos para capturar o erro 'id'.
    """
    arquivo = '/var/www/controle-ponto/app_funcionarios.py'
    
    # Ler o arquivo atual
    with open(arquivo, 'r', encoding='utf-8') as f:
        conteudo = f.read()
    
    # Adicionar debug na função _validar_dados_funcionario
    debug_validacao = '''
    # 🔍 DEBUG ESPECÍFICO PARA ERRO 'id'
    logger.error(f"🔍 [DEBUG ID] Iniciando validação de funcionário")
    logger.error(f"🔍 [DEBUG ID] Dados recebidos: {list(data.keys())}")
    logger.error(f"🔍 [DEBUG ID] Verificando se 'id' está nos dados: {'id' in data}")
    if 'id' in data:
        logger.error(f"🔍 [DEBUG ID] PROBLEMA ENCONTRADO! Campo 'id' = '{data['id']}' (tipo: {type(data['id'])})")
    
    # 🔍 DEBUG: Verificar campos EPIs
    epis_data = data.get('epis', [])
    logger.error(f"🔍 [DEBUG ID] EPIs encontrados: {len(epis_data)}")
    for i, epi in enumerate(epis_data):
        logger.error(f"🔍 [DEBUG ID] EPI {i}: {epi}")
        if 'id' in epi:
            logger.error(f"🔍 [DEBUG ID] EPI {i} tem campo 'id': {epi['id']}")
    '''
    
    # Encontrar onde inserir o debug
    posicao_debug = conteudo.find('# Campos obrigatórios\n    for field in REQUIRED_FIELDS:')
    
    if posicao_debug != -1:
        # Inserir debug antes da validação dos campos obrigatórios
        conteudo_novo = (
            conteudo[:posicao_debug] + 
            debug_validacao + 
            '\n    ' +
            conteudo[posicao_debug:]
        )
        
        # Adicionar debug na função validate_required
        debug_validate = '''
        # 🔍 DEBUG ESPECÍFICO PARA validate_required
        if field_name == 'id':
            logger.error(f"🔍 [DEBUG ID] TENTATIVA DE VALIDAR CAMPO 'id'!")
            logger.error(f"🔍 [DEBUG ID] Valor: '{value}' (tipo: {type(value)})")
            logger.error(f"🔍 [DEBUG ID] Stack trace:")
            import traceback
            logger.error(f"🔍 [DEBUG ID] {traceback.format_stack()}")
        '''
        
        # Encontrar a função validate_required e adicionar debug
        pos_validate = conteudo_novo.find('def validate_required(self, value, field_name, display_name=None):')
        if pos_validate != -1:
            # Encontrar o início do corpo da função
            pos_corpo = conteudo_novo.find('"""Valida campo obrigatório."""', pos_validate)
            if pos_corpo != -1:
                pos_fim_docstring = conteudo_novo.find('"""', pos_corpo + 3) + 3
                conteudo_novo = (
                    conteudo_novo[:pos_fim_docstring] +
                    debug_validate +
                    conteudo_novo[pos_fim_docstring:]
                )
        
        # Salvar arquivo modificado
        with open(arquivo, 'w', encoding='utf-8') as f:
            f.write(conteudo_novo)
        
        print("✅ Debug logs adicionados com sucesso!")
        print("🔍 Logs adicionados em:")
        print("  - Função _validar_dados_funcionario")
        print("  - Função validate_required (se encontrada)")
        
        return True
    else:
        print("❌ Não foi possível encontrar o local para inserir debug")
        return False

def mostrar_instrucoes():
    """
    Mostra instruções para reproduzir o erro.
    """
    print("\n" + "=" * 60)
    print("📋 INSTRUÇÕES PARA REPRODUZIR O ERRO")
    print("=" * 60)
    print("""
1. Os logs de debug foram adicionados ao código
2. Agora reproduza o erro:
   - Acesse http://10.19.208.31:5000
   - Vá para a lista de funcionários
   - Clique em "Editar" em qualquer funcionário
   - NÃO altere nada no formulário
   - Clique em "Salvar Alterações"

3. Monitore os logs:
   - tail -f /var/www/controle-ponto/app.log
   - Procure por linhas com "[DEBUG ID]"

4. Os logs irão mostrar:
   - Se o campo 'id' está nos dados
   - De onde ele está vindo
   - Quando a validação tenta validá-lo

5. Após capturar o erro, execute:
   - python3 remover_debug_id.py (para limpar os logs)
    """)

if __name__ == "__main__":
    print("🔍 ADICIONANDO DEBUG PARA CAPTURAR ERRO 'id'")
    print("=" * 60)
    
    # Fazer backup
    backup_file = fazer_backup()
    
    # Adicionar debug
    if adicionar_debug_logs():
        mostrar_instrucoes()
        
        print(f"\n💾 Backup salvo em: {backup_file}")
        print("🔄 Reinicie o servidor Flask para aplicar as mudanças")
    else:
        print("❌ Falha ao adicionar debug logs")
        print("🔄 Restaurando backup...")
        shutil.copy2(backup_file, '/var/www/controle-ponto/app_funcionarios.py')
        print("✅ Backup restaurado")
