* {
    box-sizing: border-box;
}
body {
    margin: 0;
    background: linear-gradient(180deg, #0b0c10, #1f2833);
    font-family: 'Segoe UI', Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    color: #fff;
}
.container {
    background-color: #1f2833;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,255,255,0.2);
    width: 100%;
    max-width: 600px;
}
h2 {
    text-align: center;
    color: #66fcf1;
    font-size: 24px;
}
h3 {
    color: #66fcf1;
    font-size: 18px;
    margin-top: 20px;
}
label {
    display: block;
    margin-top: 15px;
    font-weight: bold;
}
input, select, textarea {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    border: none;
    border-radius: 5px;
    background-color: #c5c6c7;
    color: #0b0c10;
}
button {
    margin-top: 20px;
    width: 100%;
    padding: 12px;
    background-color: #45a29e;
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    font-size: 16px;
}
button:hover {
    background-color: #3b8c88;
}
.erro {
    margin-top: 15px;
    color: #ff4d4d;
    text-align: center;
}
.mensagem {
    margin-top: 15px;
    color: #66fcf1;
    text-align: center;
}
.nav {
    margin-bottom: 20px;
}
a {
    color: #66fcf1;
    text-decoration: none;
    margin-right: 10px;
}
a:hover {
    color: #45a29e;
}
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}
th, td {
    padding: 10px;
    text-align: left;
    border: 1px solid #66fcf1;
    color: #fff;
}
th {
    background-color: #45a29e;
}
td form {
    display: flex;
    gap: 10px;
}
td form select {
    width: 100px;
}
td form button {
    width: auto;
    padding: 5px 10px;
    margin-top: 0;
}
.action-buttons {
    display: flex;
    gap: 10px;
}
.action-buttons button {
    width: auto;
    padding: 5px 10px;
    margin-top: 0;
}
.popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #1f2833;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,255,255,0.2);
    z-index: 1000;
    width: 300px;
}
.popup-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.popup-content h3 {
    margin: 0;
    text-align: center;
}
.popup-content input {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 5px;
    background-color: #c5c6c7;
    color: #0b0c10;
}
.popup-content button {
    width: 100%;
    padding: 10px;
    background-color: #45a29e;
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
}
.popup-content button:hover {
    background-color: #3b8c88;
}
.popup-content .close {
    background-color: #ff4d4d;
    padding: 8px; /* Tamanho reduzido */
    font-size: 14px; /* Tamanho da fonte reduzido */
}
.popup-content .close:hover {
    background-color: #e04343;
}
.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
}
@media (max-width: 480px) {
    .container {
        padding: 20px;
    }
    h2 {
        font-size: 20px;
    }
    button {
        padding: 10px;
        font-size: 14px;
    }
}
