-- ========================================
-- EXPANDIR TIPOS DE EVENTOS PARA JUSTIFICATIVAS
-- ========================================
-- Data: 16/07/2025
-- Objetivo: Adicionar novos tipos de eventos para justificativas aprovadas
-- Responsável: IA Assistant

-- ========================================
-- 1. VERIFICAR ESTRUTURA ATUAL
-- ========================================

-- Verificar tipos de eventos atuais
SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'historico_funcionario' 
AND COLUMN_NAME = 'tipo_evento'
AND TABLE_SCHEMA = DATABASE();

-- ========================================
-- 2. EXPANDIR ENUM COM NOVOS TIPOS
-- ========================================

ALTER TABLE historico_funcionario 
MODIFY COLUMN tipo_evento ENUM(
    -- Tipos existentes
    'HORA_EXTRA_SOLICITADA',
    'HORA_EXTRA_APROVADA', 
    'HORA_EXTRA_REJEITADA',
    'BANCO_HORAS_CREDITADO',
    'BANCO_HORAS_DEBITADO',
    'AUSENCIA_REGISTRADA',
    'ATRASO_REGISTRADO',
    
    -- Novos tipos para justificativas por período
    'ATRASO_JUSTIFICADO_APROVADO',
    'ATRASO_RETORNO_JUSTIFICADO_APROVADO',
    'SAIDA_ANTECIPADA_JUSTIFICADA_APROVADA',
    'HORARIO_ALMOCO_JUSTIFICADO_APROVADO',
    
    -- Novos tipos para justificativas por motivo
    'ATESTADO_MEDICO_APROVADO',
    'FALTA_JUSTIFICADA_APROVADA',
    'AUSENCIA_JUSTIFICADA_APROVADA',
    'EMERGENCIA_JUSTIFICADA_APROVADA',
    'COMPROMISSO_MEDICO_APROVADO',
    
    -- Tipo genérico
    'JUSTIFICATIVA_APROVADA',
    
    -- Tipos para herança de jornadas (se existirem)
    'JORNADA_ALTERADA',
    'JORNADA_HERDADA_EMPRESA',
    'JORNADA_HERDADA_CLIENTE',
    'ALOCACAO_CRIADA',
    'ALOCACAO_FINALIZADA',
    'EMPRESA_MUDOU_JORNADA'
) NOT NULL;

-- ========================================
-- 3. VERIFICAR ALTERAÇÃO
-- ========================================

-- Verificar se os novos tipos foram adicionados
SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'historico_funcionario' 
AND COLUMN_NAME = 'tipo_evento'
AND TABLE_SCHEMA = DATABASE();

-- ========================================
-- 4. CRIAR ÍNDICES PARA PERFORMANCE
-- ========================================

-- Índice para consultas por tipo de evento
CREATE INDEX IF NOT EXISTS idx_historico_tipo_evento 
ON historico_funcionario(tipo_evento);

-- Índice composto para consultas por funcionário e tipo
CREATE INDEX IF NOT EXISTS idx_historico_funcionario_tipo 
ON historico_funcionario(funcionario_id, tipo_evento);

-- Índice para consultas por data de referência
CREATE INDEX IF NOT EXISTS idx_historico_data_referencia 
ON historico_funcionario(data_referencia);

-- ========================================
-- 5. COMENTÁRIOS PARA DOCUMENTAÇÃO
-- ========================================

ALTER TABLE historico_funcionario 
COMMENT = 'Histórico de eventos dos funcionários incluindo justificativas aprovadas por período específico da jornada';

-- ========================================
-- 6. TESTE DE INSERÇÃO
-- ========================================

-- Teste para verificar se os novos tipos funcionam
-- (Este INSERT será removido após o teste)
/*
INSERT INTO historico_funcionario (
    funcionario_id, tipo_evento, data_evento, data_referencia,
    detalhes, status_aprovacao
) VALUES (
    1, 'ATRASO_JUSTIFICADO_APROVADO', NOW(), CURDATE(),
    'Teste de inserção com novo tipo de evento', 'APROVADO'
);

-- Verificar se foi inserido
SELECT * FROM historico_funcionario 
WHERE tipo_evento = 'ATRASO_JUSTIFICADO_APROVADO' 
ORDER BY id DESC LIMIT 1;

-- Remover teste
DELETE FROM historico_funcionario 
WHERE tipo_evento = 'ATRASO_JUSTIFICADO_APROVADO' 
AND detalhes = 'Teste de inserção com novo tipo de evento';
*/

-- ========================================
-- 7. LOG DE EXECUÇÃO
-- ========================================

SELECT 
    'EXPANSÃO DE TIPOS DE EVENTOS CONCLUÍDA' as status,
    NOW() as data_execucao,
    DATABASE() as banco_dados,
    USER() as usuario_execucao;
