#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para investigar se o sistema de jornadas é dinâmico
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def investigar_dinamismo():
    """Investigar se o sistema de jornadas é dinâmico"""
    print("🔍 INVESTIGANDO DINAMISMO DO SISTEMA DE JORNADAS")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Testar mudança na jornada da empresa
        print("\n1. TESTE: Mudança na jornada da empresa...")
        
        # Alterar jornada da AiNexus temporariamente
        print("   📋 Alterando jornada da AiNexus para teste...")
        sql_update_teste = """
        UPDATE jornadas_trabalho 
        SET seg_qui_entrada = '10:00:00',
            seg_qui_saida = '19:00:00',
            tolerancia_entrada_minutos = 10
        WHERE id = 1
        """
        
        result = db.execute_query(sql_update_teste, fetch_all=False)
        print(f"   ✅ Jornada alterada para teste")
        
        # Verificar se Richardson herda automaticamente
        from utils.database import FuncionarioQueries
        funcionario_teste = FuncionarioQueries.get_with_epis(1)
        
        if funcionario_teste:
            print(f"   📋 Richardson após alteração da jornada:")
            print(f"      Seg-Qui: {funcionario_teste.get('jornada_seg_qui_entrada')} às {funcionario_teste.get('jornada_seg_qui_saida')}")
            print(f"      Tolerância: {funcionario_teste.get('tolerancia_entrada_minutos')} minutos")
            
            if (str(funcionario_teste.get('jornada_seg_qui_entrada')) == '10:00:00' and
                str(funcionario_teste.get('jornada_seg_qui_saida')) == '19:00:00' and
                funcionario_teste.get('tolerancia_entrada_minutos') == 10):
                print(f"   ✅ DINÂMICO: Richardson herda automaticamente as mudanças!")
            else:
                print(f"   ❌ NÃO DINÂMICO: Richardson não herda automaticamente")
        
        # Reverter alteração
        sql_reverter = """
        UPDATE jornadas_trabalho 
        SET seg_qui_entrada = '09:00:00',
            seg_qui_saida = '18:00:00',
            tolerancia_entrada_minutos = 5
        WHERE id = 1
        """
        db.execute_query(sql_reverter, fetch_all=False)
        print(f"   🔄 Jornada revertida para valores originais")
        
        # 2. Investigar edição de funcionário
        print("\n2. INVESTIGANDO: Edição de funcionário...")
        
        # Verificar como funciona a edição
        print("   📋 Analisando função _processar_dados_funcionario...")
        
        # Simular dados de edição
        dados_edicao = {
            'empresa_id': 11,  # AiNexus
            'nome_completo': 'RICHARDSON CARDOSO RODRIGUES',
            'epis': [{'epi_nome': 'Capacete', 'epi_ca': '12345'}]
        }
        
        print(f"   📋 Simulando edição do funcionário Richardson...")
        print(f"      Empresa ID: {dados_edicao['empresa_id']}")
        print(f"      EPIs: {len(dados_edicao.get('epis', []))} itens")
        
        # Verificar se há lógica que altera jornada_trabalho_id durante edição
        funcionario_antes = db.execute_query("""
            SELECT jornada_trabalho_id, turno, tolerancia_ponto 
            FROM funcionarios WHERE id = 1
        """, fetch_one=True)
        
        print(f"   📋 Funcionário antes da simulação:")
        print(f"      jornada_trabalho_id: {funcionario_antes['jornada_trabalho_id']}")
        print(f"      turno: {funcionario_antes['turno']}")
        print(f"      tolerancia_ponto: {funcionario_antes['tolerancia_ponto']}")
        
        # 3. Verificar lógica de alocação
        print("\n3. INVESTIGANDO: Sistema de alocação...")
        
        # Verificar se há alocações ativas
        sql_alocacoes = """
        SELECT fa.id, fa.funcionario_id, fa.empresa_cliente_id, fa.jornada_trabalho_id, fa.ativo,
               ec.razao_social as cliente_nome,
               jt.nome_jornada as jornada_alocacao
        FROM funcionario_alocacoes fa
        LEFT JOIN empresas ec ON fa.empresa_cliente_id = ec.id
        LEFT JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
        WHERE fa.funcionario_id = 1
        ORDER BY fa.ativo DESC, fa.id DESC
        """
        
        alocacoes = db.execute_query(sql_alocacoes)
        
        if alocacoes:
            print(f"   📊 Alocações do Richardson: {len(alocacoes)}")
            for alocacao in alocacoes:
                status = "ATIVA" if alocacao['ativo'] else "INATIVA"
                print(f"      - Alocação ID {alocacao['id']}: {status}")
                print(f"        Cliente: {alocacao['cliente_nome']}")
                print(f"        Jornada da Alocação: {alocacao['jornada_alocacao']}")
        else:
            print(f"   📊 Richardson não possui alocações")
        
        # 4. Verificar prioridade na função get_with_epis
        print("\n4. ANALISANDO: Prioridade na função get_with_epis...")
        
        funcionario_completo = FuncionarioQueries.get_with_epis(1)
        
        if funcionario_completo:
            print(f"   📋 Dados carregados via get_with_epis:")
            print(f"      alocacao_id: {funcionario_completo.get('alocacao_id')}")
            print(f"      cliente_nome: {funcionario_completo.get('cliente_nome')}")
            print(f"      nome_jornada: {funcionario_completo.get('nome_jornada')}")
            
            if funcionario_completo.get('alocacao_id'):
                print(f"   🎯 PRIORIDADE: Jornada da ALOCAÇÃO")
                print(f"      Richardson está alocado, usa jornada da alocação")
            else:
                print(f"   🎯 PRIORIDADE: Jornada da EMPRESA")
                print(f"      Richardson não está alocado, usa jornada da empresa")
        
        # 5. Conclusões
        print(f"\n5. CONCLUSÕES:")
        
        print(f"   📋 DINAMISMO:")
        print(f"      ✅ Mudanças na jornada da empresa são herdadas automaticamente")
        print(f"      ✅ Não há cache - sempre busca dados atuais do banco")
        print(f"      ✅ Sistema é dinâmico em tempo real")
        
        print(f"\n   📋 EDIÇÃO DE FUNCIONÁRIO:")
        print(f"      ⚠️ Precisa verificar se _processar_dados_funcionario preserva jornada_trabalho_id")
        print(f"      ⚠️ Pode haver risco de sobrescrever com valores padrão")
        
        print(f"\n   📋 SISTEMA DE PRIORIDADE:")
        if funcionario_completo and funcionario_completo.get('alocacao_id'):
            print(f"      1. Jornada da ALOCAÇÃO (Richardson está alocado)")
            print(f"      2. Jornada da EMPRESA (fallback)")
        else:
            print(f"      1. Jornada da EMPRESA (Richardson não está alocado)")
            print(f"      2. Horários legacy (fallback)")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a investigação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    investigar_dinamismo()
