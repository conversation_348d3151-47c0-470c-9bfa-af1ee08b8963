-- ========================================
-- VERIFICAR SIMULAÇÕES - RELATÓRIO FINAL
-- Data: 11/07/2025
-- ========================================

USE controle_ponto;

-- Obter ID do funcionário
SET @funcionario_id = (SELECT id FROM funcionarios WHERE cpf = '123.456.789-00');

SELECT 'RESUMO FUNCIONÁRIO' as tipo;
SELECT 
    f.nome_completo,
    f.matricula_empresa,
    f.cargo,
    COUNT(rp.id) as total_batidas,
    COUNT(CASE WHEN rp.tipo_registro IN ('inicio_extra', 'fim_extra') THEN 1 END) as batidas_extra
FROM funcionarios f
LEFT JOIN registros_ponto rp ON f.id = rp.funcionario_id
WHERE f.cpf = '123.456.789-00'
GROUP BY f.id;

SELECT 'BATIDAS POR DIA' as tipo;
SELECT 
    DATE(rp.data_hora) as data,
    COUNT(*) as total_batidas,
    GROUP_CONCAT(CONCAT(TIME(rp.data_hora), ':', rp.tipo_registro) ORDER BY rp.data_hora SEPARATOR ' | ') as sequencia
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
WHERE f.cpf = '123.456.789-00'
GROUP BY DATE(rp.data_hora)
ORDER BY DATE(rp.data_hora);

SELECT 'APROVAÇÕES' as tipo;
SELECT 
    a.id,
    a.data_referencia,
    a.duracao_minutos,
    a.status_aprovacao,
    a.motivo_hora_extra
FROM aprovacoes_horas_extras a
WHERE a.funcionario_id = @funcionario_id;

SELECT 'HISTÓRICO' as tipo;
SELECT 
    h.data_evento,
    h.tipo_evento,
    h.detalhes,
    h.valor_minutos,
    h.status_aprovacao
FROM historico_funcionario h
WHERE h.funcionario_id = @funcionario_id
ORDER BY h.data_evento;

SELECT 'VERIFICAÇÃO COMPLETA FINALIZADA' as status;
