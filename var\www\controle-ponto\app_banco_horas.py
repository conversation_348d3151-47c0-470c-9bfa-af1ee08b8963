#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Sistema de Relatórios de Banco de Horas
Sistema: RLPONTO-WEB v1.0
Data: 25/06/2025
Desenvolvido por: AiNexus Tecnologia - Richardson Rodrigues

Relatórios e dashboards para o sistema de banco de horas personalizado
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from datetime import datetime, date, timedelta
from utils.auth import require_login, require_admin
from utils.database import get_db_connection
from pymysql.cursors import DictCursor
import logging
import calendar

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Criar blueprint
banco_horas_bp = Blueprint('banco_horas', __name__, url_prefix='/banco-horas')

@banco_horas_bp.route('/')
@require_login
def index():
    """Dashboard principal do banco de horas"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Obter dados do usuário logado
        usuario_id = session.get('user_id')
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        
        # Se for usuário comum, mostrar apenas seus dados
        if nivel_acesso == 'usuario':
            # Buscar funcionário associado ao usuário
            cursor.execute("""
                SELECT id, nome_completo 
                FROM funcionarios 
                WHERE usuario_id = %s AND status_cadastro = 'Ativo'
            """, (usuario_id,))
            funcionario = cursor.fetchone()
            
            if not funcionario:
                return render_template('banco_horas/sem_acesso.html')
            
            funcionario_id = funcionario['id']
            dados = obter_resumo_funcionario(cursor, funcionario_id)
            
            return render_template('banco_horas/funcionario.html', 
                                 funcionario=funcionario, 
                                 dados=dados)
        
        # Para administradores, mostrar dashboard geral
        else:
            dados_gerais = obter_dashboard_geral(cursor)
            return render_template('banco_horas/dashboard.html', dados=dados_gerais)
            
    except Exception as e:
        logger.error(f"Erro no dashboard de banco de horas: {e}")
        return render_template('erro.html', 
                             mensagem="Erro ao carregar dashboard de banco de horas")
    finally:
        if 'conn' in locals():
            conn.close()

@banco_horas_bp.route('/relatorio-mensal')
@require_admin
def relatorio_mensal():
    """Relatório mensal de banco de horas"""
    try:
        # Obter parâmetros
        ano = request.args.get('ano', datetime.now().year, type=int)
        mes = request.args.get('mes', datetime.now().month, type=int)
        funcionario_id = request.args.get('funcionario_id', type=int)
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Obter dados do relatório
        dados = obter_relatorio_mensal(cursor, ano, mes, funcionario_id)
        
        # Obter lista de funcionários para filtro
        cursor.execute("""
            SELECT id, nome_completo 
            FROM funcionarios 
            WHERE status_cadastro = 'Ativo'
            ORDER BY nome_completo
        """)
        funcionarios = list(cursor.fetchall())
        
        return render_template('banco_horas/relatorio_mensal.html',
                             dados=dados,
                             funcionarios=funcionarios,
                             ano=ano,
                             mes=mes,
                             funcionario_id=funcionario_id,
                             nome_mes=calendar.month_name[mes])
        
    except Exception as e:
        logger.error(f"Erro no relatório mensal: {e}")
        return render_template('erro.html', 
                             mensagem="Erro ao gerar relatório mensal")
    finally:
        if 'conn' in locals():
            conn.close()

@banco_horas_bp.route('/justificativas')
@require_admin
def justificativas():
    """Gerenciar justificativas pendentes"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Obter justificativas pendentes
        cursor.execute("""
            SELECT 
                j.id,
                j.funcionario_id,
                f.nome_completo,
                j.data_referencia,
                j.tipo_justificativa,
                j.motivo,
                j.minutos_justificados,
                j.status_aprovacao,
                j.criado_em,
                u.nome as solicitado_por_nome
            FROM justificativas_ponto j
            INNER JOIN funcionarios f ON j.funcionario_id = f.id
            LEFT JOIN usuarios u ON j.solicitado_por = u.id
            WHERE j.status_aprovacao = 'pendente'
            ORDER BY j.criado_em DESC
        """)
        
        justificativas_pendentes = list(cursor.fetchall())
        
        return render_template('banco_horas/justificativas.html',
                             justificativas=justificativas_pendentes)
        
    except Exception as e:
        logger.error(f"Erro ao carregar justificativas: {e}")
        return render_template('erro.html', 
                             mensagem="Erro ao carregar justificativas")
    finally:
        if 'conn' in locals():
            conn.close()

@banco_horas_bp.route('/api/aprovar-justificativa', methods=['POST'])
@require_admin
def aprovar_justificativa():
    """API para aprovar/rejeitar justificativas com impacto no banco de horas"""
    try:
        data = request.get_json()
        justificativa_id = data.get('justificativa_id')
        acao = data.get('acao')  # 'aprovar' ou 'rejeitar'
        observacoes = data.get('observacoes', '')

        if not justificativa_id or acao not in ['aprovar', 'rejeitar']:
            return jsonify({'success': False, 'message': 'Dados inválidos'}), 400

        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        # ✅ NOVO: Buscar dados da justificativa antes de atualizar
        cursor.execute("""
            SELECT
                j.funcionario_id,
                j.data_registro,
                j.tipo_justificativa,
                j.motivo,
                f.nome_completo,
                f.seg_qui_entrada,
                f.seg_qui_saida,
                f.intervalo_inicio,
                f.intervalo_fim
            FROM justificativas_ponto j
            INNER JOIN funcionarios f ON j.funcionario_id = f.id
            WHERE j.id = %s
        """, (justificativa_id,))

        justificativa_info = cursor.fetchone()
        if not justificativa_info:
            return jsonify({'success': False, 'message': 'Justificativa não encontrada'}), 404

        # Atualizar justificativa
        status = 'aprovado' if acao == 'aprovar' else 'rejeitado'
        cursor.execute("""
            UPDATE justificativas_ponto
            SET status_aprovacao = %s,
                aprovado_por = %s,
                data_aprovacao = NOW(),
                observacoes_aprovacao = %s
            WHERE id = %s
        """, (status, session.get('user_id'), observacoes, justificativa_id))

        # ✅ NOVO: Processar impacto no banco de horas
        resultado_banco = processar_impacto_banco_horas(
            justificativa_info, acao, cursor
        )

        conn.commit()

        return jsonify({
            'success': True,
            'message': f'Justificativa {status} com sucesso',
            'impacto_banco_horas': resultado_banco
        })

    except Exception as e:
        logger.error(f"Erro ao processar justificativa: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'}), 500
    finally:
        if 'conn' in locals():
            conn.close()

def obter_resumo_funcionario(cursor, funcionario_id):
    """Obtém resumo do banco de horas de um funcionário"""
    try:
        # Saldo atual do mês
        hoje = date.today()
        primeiro_dia = date(hoje.year, hoje.month, 1)
        
        cursor.execute("""
            SELECT 
                SUM(saldo_liquido_minutos) as saldo_mensal,
                SUM(atraso_entrada_minutos) as total_atrasos,
                SUM(excesso_almoco_minutos) as total_excesso_almoco,
                SUM(saida_antecipada_minutos) as total_saidas_antecipadas,
                SUM(horas_extras_minutos) as total_horas_extras,
                COUNT(*) as dias_trabalhados,
                SUM(CASE WHEN status_dia = 'completo' THEN 1 ELSE 0 END) as dias_completos
            FROM banco_horas
            WHERE funcionario_id = %s 
            AND data_referencia >= %s 
            AND data_referencia <= %s
        """, (funcionario_id, primeiro_dia, hoje))
        
        resumo_mensal = cursor.fetchone()
        
        # Últimos registros
        cursor.execute("""
            SELECT 
                data_referencia,
                saldo_liquido_minutos,
                status_dia,
                observacoes
            FROM banco_horas
            WHERE funcionario_id = %s
            ORDER BY data_referencia DESC
            LIMIT 10
        """, (funcionario_id,))
        
        ultimos_registros = list(cursor.fetchall())
        
        return {
            'resumo_mensal': resumo_mensal,
            'ultimos_registros': ultimos_registros
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter resumo do funcionário: {e}")
        return {}

def obter_dashboard_geral(cursor):
    """Obtém dados para dashboard geral de administradores"""
    try:
        hoje = date.today()
        primeiro_dia = date(hoje.year, hoje.month, 1)
        
        # Estatísticas gerais
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT funcionario_id) as total_funcionarios,
                SUM(CASE WHEN saldo_liquido_minutos > 0 THEN 1 ELSE 0 END) as funcionarios_credito,
                SUM(CASE WHEN saldo_liquido_minutos < 0 THEN 1 ELSE 0 END) as funcionarios_devedor,
                SUM(saldo_liquido_minutos) as saldo_total_empresa
            FROM banco_horas
            WHERE data_referencia >= %s AND data_referencia <= %s
        """, (primeiro_dia, hoje))
        
        estatisticas = cursor.fetchone()
        
        # Top funcionários com mais atrasos
        cursor.execute("""
            SELECT 
                f.nome_completo,
                SUM(bh.atraso_entrada_minutos) as total_atrasos
            FROM banco_horas bh
            INNER JOIN funcionarios f ON bh.funcionario_id = f.id
            WHERE bh.data_referencia >= %s AND bh.data_referencia <= %s
            AND bh.atraso_entrada_minutos > 0
            GROUP BY bh.funcionario_id, f.nome_completo
            ORDER BY total_atrasos DESC
            LIMIT 5
        """, (primeiro_dia, hoje))
        
        top_atrasos = list(cursor.fetchall())
        
        # Justificativas pendentes
        cursor.execute("""
            SELECT COUNT(*) as total_pendentes
            FROM justificativas_ponto
            WHERE status_aprovacao = 'pendente'
        """)
        
        justificativas_pendentes = cursor.fetchone()['total_pendentes']
        
        return {
            'estatisticas': estatisticas,
            'top_atrasos': top_atrasos,
            'justificativas_pendentes': justificativas_pendentes
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter dashboard geral: {e}")
        return {}

def obter_relatorio_mensal(cursor, ano, mes, funcionario_id=None):
    """Obtém dados para relatório mensal"""
    try:
        primeiro_dia = date(ano, mes, 1)
        ultimo_dia = date(ano, mes, calendar.monthrange(ano, mes)[1])
        
        # Construir query base
        where_clause = "WHERE bh.data_referencia >= %s AND bh.data_referencia <= %s"
        params = [primeiro_dia, ultimo_dia]
        
        if funcionario_id:
            where_clause += " AND bh.funcionario_id = %s"
            params.append(funcionario_id)
        
        cursor.execute(f"""
            SELECT 
                f.nome_completo,
                bh.data_referencia,
                bh.atraso_entrada_minutos,
                bh.excesso_almoco_minutos,
                bh.saida_antecipada_minutos,
                bh.horas_extras_minutos,
                bh.saldo_liquido_minutos,
                bh.status_dia,
                bh.observacoes
            FROM banco_horas bh
            INNER JOIN funcionarios f ON bh.funcionario_id = f.id
            {where_clause}
            ORDER BY f.nome_completo, bh.data_referencia
        """, params)
        
        registros = list(cursor.fetchall())
        
        return registros
        
    except Exception as e:
        logger.error(f"Erro ao obter relatório mensal: {e}")
        return []

def processar_impacto_banco_horas(justificativa_info, acao, cursor):
    """
    Processa o impacto da aprovação/reprovação no banco de horas

    Args:
        justificativa_info: Dados da justificativa
        acao: 'aprovar' ou 'rejeitar'
        cursor: Cursor do banco de dados

    Returns:
        dict: Resultado do processamento
    """
    try:
        funcionario_id = justificativa_info['funcionario_id']
        data_registro = justificativa_info['data_registro']
        tipo_justificativa = justificativa_info['tipo_justificativa']

        logger.info(f"🔄 Processando impacto no banco de horas: {acao} para {tipo_justificativa}")

        # Buscar registros de ponto do dia
        cursor.execute("""
            SELECT tipo_registro, TIME(data_hora) as hora_registro
            FROM registros_ponto
            WHERE funcionario_id = %s AND DATE(data_hora) = %s
            ORDER BY data_hora ASC
        """, (funcionario_id, data_registro))

        registros = list(cursor.fetchall())
        registros_dict = {reg['tipo_registro']: reg['hora_registro'] for reg in registros}

        # Calcular impacto baseado no tipo de justificativa
        if tipo_justificativa == 'saida_antecipada':
            return processar_saida_antecipada(
                justificativa_info, registros_dict, acao, cursor
            )
        elif tipo_justificativa == 'atraso':
            return processar_atraso(
                justificativa_info, registros_dict, acao, cursor
            )
        else:
            return {'status': 'sem_impacto', 'message': 'Tipo de justificativa não requer processamento de banco de horas'}

    except Exception as e:
        logger.error(f"Erro ao processar impacto no banco de horas: {e}")
        return {'status': 'erro', 'message': str(e)}

def processar_saida_antecipada(justificativa_info, registros_dict, acao, cursor):
    """
    Processa aprovação/reprovação de saída antecipada

    APROVADO: Abona a falta, considera período como trabalhado
    REPROVADO: Aplica desconto do período não trabalhado
    """
    try:
        funcionario_id = justificativa_info['funcionario_id']
        data_registro = justificativa_info['data_registro']
        nome_funcionario = justificativa_info['nome_completo']

        # Horários da jornada do funcionário
        horario_saida_esperado = justificativa_info['seg_qui_saida']  # Ex: 18:00

        # Horário real de saída (do registro de ponto)
        horario_saida_real = registros_dict.get('saida')

        if not horario_saida_real or not horario_saida_esperado:
            return {'status': 'erro', 'message': 'Horários de saída não encontrados'}

        # Calcular minutos de saída antecipada
        from datetime import datetime, time

        # Converter para datetime para cálculo
        saida_esperada = datetime.combine(data_registro, horario_saida_esperado)
        saida_real = datetime.combine(data_registro, horario_saida_real)

        minutos_antecipacao = int((saida_esperada - saida_real).total_seconds() / 60)

        logger.info(f"📊 Saída antecipada: {minutos_antecipacao} minutos ({horario_saida_real} vs {horario_saida_esperado})")

        if acao == 'aprovar':
            # ✅ APROVADO: Abona a falta - considera como trabalhado
            resultado = abonar_saida_antecipada(
                funcionario_id, data_registro, minutos_antecipacao, cursor
            )
            logger.info(f"✅ APROVADO: Saída antecipada abonada para {nome_funcionario}")

        else:
            # ❌ REPROVADO: Aplica desconto do período não trabalhado
            resultado = aplicar_desconto_saida_antecipada(
                funcionario_id, data_registro, minutos_antecipacao, cursor
            )
            logger.info(f"❌ REPROVADO: Desconto aplicado para {nome_funcionario}")

        return resultado

    except Exception as e:
        logger.error(f"Erro ao processar saída antecipada: {e}")
        return {'status': 'erro', 'message': str(e)}

def abonar_saida_antecipada(funcionario_id, data_registro, minutos_antecipacao, cursor):
    """
    Abona saída antecipada - considera período como trabalhado
    Remove desconto do banco de horas
    """
    try:
        # Verificar se já existe registro no banco de horas
        cursor.execute("""
            SELECT id, saida_antecipada_minutos, saldo_devedor_minutos, saldo_liquido_minutos
            FROM banco_horas
            WHERE funcionario_id = %s AND data_referencia = %s
        """, (funcionario_id, data_registro))

        registro_banco = cursor.fetchone()

        if registro_banco:
            # ✅ ABONO: Zerar saída antecipada e recalcular saldos
            novo_saldo_devedor = registro_banco['saldo_devedor_minutos'] - minutos_antecipacao
            novo_saldo_liquido = registro_banco['saldo_liquido_minutos'] + minutos_antecipacao

            cursor.execute("""
                UPDATE banco_horas
                SET saida_antecipada_minutos = 0,
                    saldo_devedor_minutos = %s,
                    saldo_liquido_minutos = %s,
                    observacoes = CONCAT(COALESCE(observacoes, ''), '; ABONO: Saída antecipada justificada e aprovada'),
                    atualizado_em = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (max(0, novo_saldo_devedor), novo_saldo_liquido, registro_banco['id']))

            return {
                'status': 'abonado',
                'message': f'Saída antecipada de {minutos_antecipacao} minutos foi abonada',
                'minutos_abonados': minutos_antecipacao,
                'novo_saldo_liquido': novo_saldo_liquido
            }
        else:
            # Criar registro com abono
            cursor.execute("""
                INSERT INTO banco_horas
                (funcionario_id, data_referencia, saida_antecipada_minutos, saldo_devedor_minutos,
                 saldo_credor_minutos, saldo_liquido_minutos, status_dia, observacoes)
                VALUES (%s, %s, 0, 0, 0, 0, 'completo', 'ABONO: Saída antecipada justificada e aprovada')
            """, (funcionario_id, data_registro))

            return {
                'status': 'abonado',
                'message': f'Saída antecipada de {minutos_antecipacao} minutos foi abonada',
                'minutos_abonados': minutos_antecipacao,
                'novo_saldo_liquido': 0
            }

    except Exception as e:
        logger.error(f"Erro ao abonar saída antecipada: {e}")
        return {'status': 'erro', 'message': str(e)}

def aplicar_desconto_saida_antecipada(funcionario_id, data_registro, minutos_antecipacao, cursor):
    """
    Aplica desconto por saída antecipada reprovada
    Registra como ausência do período não trabalhado
    """
    try:
        # Verificar se já existe registro no banco de horas
        cursor.execute("""
            SELECT id, saida_antecipada_minutos, saldo_devedor_minutos, saldo_liquido_minutos
            FROM banco_horas
            WHERE funcionario_id = %s AND data_referencia = %s
        """, (funcionario_id, data_registro))

        registro_banco = cursor.fetchone()

        if registro_banco:
            # ❌ DESCONTO: Aplicar saída antecipada e recalcular saldos
            novo_saldo_devedor = registro_banco['saldo_devedor_minutos'] + minutos_antecipacao
            novo_saldo_liquido = registro_banco['saldo_liquido_minutos'] - minutos_antecipacao

            cursor.execute("""
                UPDATE banco_horas
                SET saida_antecipada_minutos = %s,
                    saldo_devedor_minutos = %s,
                    saldo_liquido_minutos = %s,
                    observacoes = CONCAT(COALESCE(observacoes, ''), '; DESCONTO: Saída antecipada reprovada'),
                    atualizado_em = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (minutos_antecipacao, novo_saldo_devedor, novo_saldo_liquido, registro_banco['id']))

            return {
                'status': 'descontado',
                'message': f'Desconto de {minutos_antecipacao} minutos aplicado por saída antecipada reprovada',
                'minutos_descontados': minutos_antecipacao,
                'novo_saldo_liquido': novo_saldo_liquido
            }
        else:
            # Criar registro com desconto
            cursor.execute("""
                INSERT INTO banco_horas
                (funcionario_id, data_referencia, saida_antecipada_minutos, saldo_devedor_minutos,
                 saldo_credor_minutos, saldo_liquido_minutos, status_dia, observacoes)
                VALUES (%s, %s, %s, %s, 0, %s, 'incompleto', 'DESCONTO: Saída antecipada reprovada')
            """, (funcionario_id, data_registro, minutos_antecipacao, minutos_antecipacao, -minutos_antecipacao))

            return {
                'status': 'descontado',
                'message': f'Desconto de {minutos_antecipacao} minutos aplicado por saída antecipada reprovada',
                'minutos_descontados': minutos_antecipacao,
                'novo_saldo_liquido': -minutos_antecipacao
            }

    except Exception as e:
        logger.error(f"Erro ao aplicar desconto de saída antecipada: {e}")
        return {'status': 'erro', 'message': str(e)}

def processar_atraso(justificativa_info, registros_dict, acao, cursor):
    """
    Processa aprovação/reprovação de atraso

    APROVADO: Abona o atraso, considera como pontual
    REPROVADO: Mantém desconto do atraso
    """
    try:
        funcionario_id = justificativa_info['funcionario_id']
        data_registro = justificativa_info['data_registro']
        nome_funcionario = justificativa_info['nome_completo']

        # Horários da jornada do funcionário
        horario_entrada_esperado = justificativa_info['seg_qui_entrada']  # Ex: 08:00

        # Horário real de entrada (do registro de ponto)
        horario_entrada_real = registros_dict.get('entrada_manha')

        if not horario_entrada_real or not horario_entrada_esperado:
            return {'status': 'erro', 'message': 'Horários de entrada não encontrados'}

        # Calcular minutos de atraso
        from datetime import datetime, time

        # Converter para datetime para cálculo
        entrada_esperada = datetime.combine(data_registro, horario_entrada_esperado)
        entrada_real = datetime.combine(data_registro, horario_entrada_real)

        minutos_atraso = int((entrada_real - entrada_esperada).total_seconds() / 60)

        logger.info(f"📊 Atraso: {minutos_atraso} minutos ({horario_entrada_real} vs {horario_entrada_esperado})")

        if acao == 'aprovar':
            # ✅ APROVADO: Abona o atraso
            resultado = abonar_atraso(
                funcionario_id, data_registro, minutos_atraso, cursor
            )
            logger.info(f"✅ APROVADO: Atraso abonado para {nome_funcionario}")

        else:
            # ❌ REPROVADO: Mantém desconto do atraso
            resultado = aplicar_desconto_atraso(
                funcionario_id, data_registro, minutos_atraso, cursor
            )
            logger.info(f"❌ REPROVADO: Desconto de atraso mantido para {nome_funcionario}")

        return resultado

    except Exception as e:
        logger.error(f"Erro ao processar atraso: {e}")
        return {'status': 'erro', 'message': str(e)}

def abonar_atraso(funcionario_id, data_registro, minutos_atraso, cursor):
    """Abona atraso - remove desconto do banco de horas"""
    try:
        cursor.execute("""
            SELECT id, atraso_entrada_minutos, saldo_devedor_minutos, saldo_liquido_minutos
            FROM banco_horas
            WHERE funcionario_id = %s AND data_referencia = %s
        """, (funcionario_id, data_registro))

        registro_banco = cursor.fetchone()

        if registro_banco:
            novo_saldo_devedor = registro_banco['saldo_devedor_minutos'] - minutos_atraso
            novo_saldo_liquido = registro_banco['saldo_liquido_minutos'] + minutos_atraso

            cursor.execute("""
                UPDATE banco_horas
                SET atraso_entrada_minutos = 0,
                    saldo_devedor_minutos = %s,
                    saldo_liquido_minutos = %s,
                    observacoes = CONCAT(COALESCE(observacoes, ''), '; ABONO: Atraso justificado e aprovado'),
                    atualizado_em = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (max(0, novo_saldo_devedor), novo_saldo_liquido, registro_banco['id']))

            return {
                'status': 'abonado',
                'message': f'Atraso de {minutos_atraso} minutos foi abonado',
                'minutos_abonados': minutos_atraso,
                'novo_saldo_liquido': novo_saldo_liquido
            }

        return {'status': 'sem_registro', 'message': 'Registro no banco de horas não encontrado'}

    except Exception as e:
        logger.error(f"Erro ao abonar atraso: {e}")
        return {'status': 'erro', 'message': str(e)}

def aplicar_desconto_atraso(funcionario_id, data_registro, minutos_atraso, cursor):
    """Aplica/mantém desconto por atraso reprovado"""
    try:
        cursor.execute("""
            SELECT id, atraso_entrada_minutos, saldo_devedor_minutos, saldo_liquido_minutos
            FROM banco_horas
            WHERE funcionario_id = %s AND data_referencia = %s
        """, (funcionario_id, data_registro))

        registro_banco = cursor.fetchone()

        if registro_banco:
            cursor.execute("""
                UPDATE banco_horas
                SET atraso_entrada_minutos = %s,
                    observacoes = CONCAT(COALESCE(observacoes, ''), '; DESCONTO: Atraso reprovado'),
                    atualizado_em = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (minutos_atraso, registro_banco['id']))

            return {
                'status': 'descontado',
                'message': f'Desconto de {minutos_atraso} minutos mantido por atraso reprovado',
                'minutos_descontados': minutos_atraso,
                'novo_saldo_liquido': registro_banco['saldo_liquido_minutos']
            }

        return {'status': 'sem_registro', 'message': 'Registro no banco de horas não encontrado'}

    except Exception as e:
        logger.error(f"Erro ao aplicar desconto de atraso: {e}")
        return {'status': 'erro', 'message': str(e)}

def processar_impacto_banco_horas_direto(funcionario_id, data_registro, tipo_justificativa, status_aprovacao):
    """
    Função direta para processar impacto no banco de horas
    Usada quando a aprovação/reprovação é feita via formulário
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        # Buscar dados do funcionário
        cursor.execute("""
            SELECT
                f.nome_completo,
                f.seg_qui_entrada,
                f.seg_qui_saida,
                f.intervalo_inicio,
                f.intervalo_fim
            FROM funcionarios f
            WHERE f.id = %s
        """, (funcionario_id,))

        funcionario_info = cursor.fetchone()
        if not funcionario_info:
            return {'status': 'erro', 'message': 'Funcionário não encontrado'}

        # Simular estrutura de justificativa_info
        justificativa_info = {
            'funcionario_id': funcionario_id,
            'data_registro': data_registro,
            'tipo_justificativa': tipo_justificativa,
            'nome_completo': funcionario_info['nome_completo'],
            'seg_qui_entrada': funcionario_info['seg_qui_entrada'],
            'seg_qui_saida': funcionario_info['seg_qui_saida'],
            'intervalo_inicio': funcionario_info['intervalo_inicio'],
            'intervalo_fim': funcionario_info['intervalo_fim']
        }

        # Determinar ação baseada no status
        acao = 'aprovar' if status_aprovacao == 'aprovada' else 'rejeitar'

        # Processar impacto
        resultado = processar_impacto_banco_horas(justificativa_info, acao, cursor)

        conn.commit()

        logger.info(f"✅ Impacto processado com sucesso: {resultado}")
        return resultado

    except Exception as e:
        logger.error(f"Erro ao processar impacto direto no banco de horas: {e}")
        return {'status': 'erro', 'message': str(e)}
    finally:
        if 'conn' in locals():
            conn.close()
