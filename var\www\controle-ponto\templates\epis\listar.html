{% extends "base.html" %}

{% block title %}EPIs de {{ funcionario.nome_completo }} - Controle de Ponto{% endblock %}

{% block content %}
<!-- <PERSON><PERSON><PERSON><PERSON><PERSON> da página -->
<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
    <div>
        <h2 style="margin-bottom: 5px; color: #495057;">Equipamentos de Proteção Individual (EPIs)</h2>
        <p style="color: #6c757d; margin: 0;">Funcionário: <strong>{{ funcionario.nome_completo }}</strong></p>
    </div>
    <div style="display: flex; gap: 10px;">
        {% if current_user.is_admin %}
        <a href="{{ url_for('epis.adicionar_epi', funcionario_id=funcionario_id) }}" 
           class="btn btn-success" 
           style="padding: 10px 20px; border-radius: 8px;">
            <i class="fas fa-plus"></i> Adicionar EPI
        </a>
        {% endif %}
        <a href="{{ url_for('funcionarios.detalhes', funcionario_id=funcionario_id) }}" 
           class="btn btn-secondary" 
           style="padding: 10px 20px; border-radius: 8px;">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>
</div>

<!-- Breadcrumb -->
{% if breadcrumbs %}
<nav aria-label="breadcrumb" style="margin-bottom: 20px;">
    <ol class="breadcrumb">
        {% for crumb in breadcrumbs %}
            {% if loop.last %}
                <li class="breadcrumb-item active" aria-current="page">{{ crumb.name }}</li>
            {% else %}
                <li class="breadcrumb-item">
                    <a href="{{ crumb.url }}">{{ crumb.name }}</a>
                </li>
            {% endif %}
        {% endfor %}
    </ol>
</nav>
{% endif %}

<!-- Grid de EPIs ou mensagem vazia -->
{% if epis %}
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
        {% for epi in epis %}
            <div class="col">
                <div class="card h-100" style="border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); transition: all 0.3s ease;">
                    <!-- Cabeçalho do Card com Status -->
                    <div class="card-header" style="background: linear-gradient(135deg, #4fbdba, #26a69a); color: white; border-radius: 12px 12px 0 0; position: relative;">
                        <div style="display: flex; justify-content: space-between; align-items: start;">
                            <div>
                                <h5 class="card-title" style="margin-bottom: 8px; font-weight: 600;">
                                    {{ epi.epi_nome }}
                                </h5>
                                {% if epi.epi_ca %}
                                    <small style="opacity: 0.9;">
                                        <i class="fas fa-certificate"></i> CA: {{ epi.epi_ca }}
                                    </small>
                                {% endif %}
                            </div>
                            <!-- Badge de Status -->
                            {% set status_info = {
                                'entregue': {'class': 'bg-success', 'icon': 'fas fa-check-circle', 'label': 'Entregue'},
                                'vencido': {'class': 'bg-danger', 'icon': 'fas fa-exclamation-triangle', 'label': 'Vencido'},
                                'devolvido': {'class': 'bg-warning text-dark', 'icon': 'fas fa-undo', 'label': 'Devolvido'}
                            } %}
                            {% set current_status = epi.status_epi or 'entregue' %}
                            <span class="badge {{ status_info[current_status]['class'] }}" 
                                  style="font-size: 12px; padding: 6px 10px; border-radius: 20px;">
                                <i class="{{ status_info[current_status]['icon'] }}"></i>
                                {{ status_info[current_status]['label'] }}
                            </span>
                        </div>
                    </div>

                    <!-- Corpo do Card -->
                    <div class="card-body" style="padding: 20px;">
                        <!-- Informações de Data -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div>
                                <small class="text-muted" style="display: block; margin-bottom: 4px;">
                                    <i class="fas fa-calendar-check"></i> Data de Entrega
                                </small>
                                <strong style="color: #495057;">
                                    {% if epi.epi_data_entrega %}
                                        {{ format_date(epi.epi_data_entrega) }}
                                    {% else %}
                                        <span class="text-muted">Não informado</span>
                                    {% endif %}
                                </strong>
                            </div>
                            <div>
                                <small class="text-muted" style="display: block; margin-bottom: 4px;">
                                    <i class="fas fa-calendar-times"></i> Data de Validade
                                </small>
                                {% if epi.epi_data_validade %}
                                    {% if epi.status_validade == 'vencido' %}
                                        <strong style="color: #dc3545;" title="EPI vencido">
                                            <i class="fas fa-exclamation-triangle"></i> {{ format_date(epi.epi_data_validade) }}
                                        </strong>
                                    {% elif epi.status_validade == 'alerta' %}
                                        <strong style="color: #fd7e14;" title="EPI vence em breve">
                                            <i class="fas fa-clock"></i> {{ format_date(epi.epi_data_validade) }}
                                        </strong>
                                    {% else %}
                                        <strong style="color: #28a745;" title="EPI dentro da validade">
                                            {{ format_date(epi.epi_data_validade) }}
                                        </strong>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">Não informado</span>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Observações -->
                        {% if epi.epi_observacoes %}
                            <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #4fbdba;">
                                <small class="text-muted" style="display: block; margin-bottom: 4px;">
                                    <i class="fas fa-comment-alt"></i> Observações
                                </small>
                                <p style="margin: 0; color: #495057; font-size: 14px; line-height: 1.4;">
                                    {{ epi.epi_observacoes }}
                                </p>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Footer do Card com Ações -->
                    {% if current_user.is_admin %}
                    <div class="card-footer" style="background: #f8f9fa; border-radius: 0 0 12px 12px; padding: 15px 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i>
                                {% if epi.epi_data_entrega %}
                                    Cadastrado em {{ format_date(epi.epi_data_entrega) }}
                                {% else %}
                                    Cadastrado recentemente
                                {% endif %}
                            </small>
                            <button type="button" 
                                    class="btn btn-outline-danger btn-sm"
                                    style="border-radius: 20px; padding: 5px 12px;"
                                    onclick="confirmarExclusaoEPI({{ epi.id }}, '{{ epi.epi_nome }}', {{ funcionario_id }})"
                                    title="Excluir EPI">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <!-- Estado Vazio -->
    <div style="text-align: center; padding: 60px 20px; background: white; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <div style="font-size: 4rem; color: #e9ecef; margin-bottom: 20px;">
            <i class="fas fa-hard-hat"></i>
        </div>
        <h3 style="color: #495057; margin-bottom: 10px; font-weight: 600;">Nenhum EPI cadastrado</h3>
        <p style="color: #6c757d; margin-bottom: 25px; max-width: 400px; margin-left: auto; margin-right: auto;">
            Este funcionário ainda não possui equipamentos de proteção individual cadastrados.
        </p>
        {% if current_user.is_admin %}
            <a href="{{ url_for('epis.adicionar_epi', funcionario_id=funcionario_id) }}" 
               class="btn btn-primary" 
               style="padding: 12px 30px; border-radius: 25px; font-weight: 600;">
                <i class="fas fa-plus"></i> Cadastrar Primeiro EPI
            </a>
        {% endif %}
    </div>
{% endif %}

<!-- Scripts JavaScript -->
<script>
    function confirmarExclusaoEPI(epiId, epiNome, funcionarioId) {
        if (confirm(`Tem certeza que deseja excluir o EPI "${epiNome}"?\n\nEsta ação não pode ser desfeita.`)) {
            // Criar formulário dinâmico para envio via POST
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/epis/${epiId}/excluir`;
            
            // Token CSRF se necessário
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrf_token';
                csrfInput.value = csrfToken.getAttribute('content');
                form.appendChild(csrfInput);
            }
            
            document.body.appendChild(form);
            form.submit();
        }
    }

    // Efeito hover nos cards
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
            });
        });
    });
</script>

<!-- CSS personalizado -->
<style>
    .card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
    
    .card:hover {
        cursor: pointer;
    }
    
    .btn {
        transition: all 0.2s ease;
    }
    
    .btn:hover {
        transform: translateY(-1px);
    }
    
    /* Responsividade para dispositivos móveis */
    @media (max-width: 768px) {
        .row.row-cols-1.row-cols-md-2.row-cols-lg-3 {
            --bs-gutter-x: 1rem;
        }
        
        .card-body {
            padding: 15px !important;
        }
        
        .card-header {
            padding: 15px !important;
        }
    }
    
    /* Animação de fade-in para os cards */
    .card {
        animation: fadeInUp 0.6s ease forwards;
        opacity: 0;
        transform: translateY(20px);
    }
    
    .card:nth-child(1) { animation-delay: 0.1s; }
    .card:nth-child(2) { animation-delay: 0.2s; }
    .card:nth-child(3) { animation-delay: 0.3s; }
    .card:nth-child(4) { animation-delay: 0.4s; }
    .card:nth-child(5) { animation-delay: 0.5s; }
    .card:nth-child(6) { animation-delay: 0.6s; }
    
    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}
