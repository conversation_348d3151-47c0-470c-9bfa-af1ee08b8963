# Deploy Real - Correção JavaScript RLPONTO-WEB
# Data: 03/07/2025

$SERVER = "************"
$USER = "root"
$REMOTE_PATH = "/var/www/controle-ponto/templates/configuracoes/"
$LOCAL_FILE = "var\www\controle-ponto\templates\configuracoes\empresa_form.html"

Write-Host "=== DEPLOY REAL - CORREÇÃO JAVASCRIPT ===" -ForegroundColor Green
Write-Host "Servidor: $SERVER" -ForegroundColor Cyan
Write-Host "Arquivo: $LOCAL_FILE" -ForegroundColor Cyan
Write-Host ""

# Verificar se o arquivo existe
if (-not (Test-Path $LOCAL_FILE)) {
    Write-Host "❌ ERRO: Arquivo não encontrado: $LOCAL_FILE" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Arquivo encontrado: $LOCAL_FILE" -ForegroundColor Green
Write-Host ""

# Mostrar o conteúdo das principais correções
Write-Host "🔧 CORREÇÕES QUE SERÃO APLICADAS:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Proteção contra elementos nulos:" -ForegroundColor White
Write-Host "   if (!feedback) { console.error(...); return; }" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Correção de ID inconsistente:" -ForegroundColor White
Write-Host "   mostrarFeedback('razao_social', ...) // era 'razao-social'" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. ID do feedback corrigido:" -ForegroundColor White
Write-Host "   id='razao_social-feedback' // era 'razao-social-feedback'" -ForegroundColor Cyan
Write-Host ""

Write-Host "📋 COMANDOS PARA EXECUTAR MANUALMENTE:" -ForegroundColor Magenta
Write-Host ""
Write-Host "1. Conectar ao servidor:" -ForegroundColor White
Write-Host "   ssh root@$SERVER" -ForegroundColor Cyan
Write-Host "   Senha: @Ric6109" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Fazer backup:" -ForegroundColor White
Write-Host "   cd $REMOTE_PATH" -ForegroundColor Cyan
Write-Host "   cp empresa_form.html empresa_form_backup_`$(date +%Y%m%d_%H%M%S).html" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. Copiar arquivo corrigido (use WinSCP, FileZilla ou SCP):" -ForegroundColor White
Write-Host "   De: $(Resolve-Path $LOCAL_FILE)" -ForegroundColor Cyan
Write-Host "   Para: root@${SERVER}:${REMOTE_PATH}empresa_form.html" -ForegroundColor Cyan
Write-Host ""
Write-Host "4. Ajustar permissões:" -ForegroundColor White
Write-Host "   chown www-data:www-data empresa_form.html" -ForegroundColor Cyan
Write-Host "   chmod 644 empresa_form.html" -ForegroundColor Cyan
Write-Host ""
Write-Host "5. Reiniciar serviço:" -ForegroundColor White
Write-Host "   systemctl restart rlponto-web" -ForegroundColor Cyan
Write-Host ""
Write-Host "6. Testar:" -ForegroundColor White
Write-Host "   curl -I http://localhost:5000" -ForegroundColor Cyan
Write-Host ""

Write-Host "🎯 TESTE FINAL APÓS DEPLOY:" -ForegroundColor Green
Write-Host "1. Acesse: http://$SERVER:5000" -ForegroundColor White
Write-Host "2. Login: admin / @Ric6109" -ForegroundColor White
Write-Host "3. Vá para: Configurações > Empresas" -ForegroundColor White
Write-Host "4. Edite uma empresa e modifique o CNPJ" -ForegroundColor White
Write-Host "5. Clique em 'Salvar Empresa'" -ForegroundColor White
Write-Host "6. Verifique se NÃO há mais erro JavaScript!" -ForegroundColor White
Write-Host ""

# Tentar usar SCP se disponível
Write-Host "🚀 TENTANDO DEPLOY AUTOMÁTICO..." -ForegroundColor Blue

try {
    # Verificar se SCP está disponível
    $scpTest = Get-Command scp -ErrorAction SilentlyContinue
    if ($scpTest) {
        Write-Host "✅ SCP encontrado, tentando deploy..." -ForegroundColor Green
        
        # Executar SCP
        $scpCommand = "scp `"$LOCAL_FILE`" root@${SERVER}:${REMOTE_PATH}empresa_form.html"
        Write-Host "Executando: $scpCommand" -ForegroundColor Cyan
        
        # Nota: SCP vai pedir senha interativamente
        Invoke-Expression $scpCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Arquivo transferido com sucesso!" -ForegroundColor Green
            
            # Executar comandos finais no servidor
            $sshCommands = @"
cd $REMOTE_PATH && 
chown www-data:www-data empresa_form.html 2>/dev/null || echo 'Permissões mantidas' && 
chmod 644 empresa_form.html && 
systemctl restart rlponto-web 2>/dev/null || echo 'Reinício manual necessário' && 
echo 'Deploy finalizado!'
"@
            
            ssh root@$SERVER $sshCommands
            
            Write-Host ""
            Write-Host "🎉 DEPLOY CONCLUÍDO!" -ForegroundColor Green
            Write-Host "🧪 Teste agora o formulário de empresas!" -ForegroundColor Yellow
        } else {
            Write-Host "❌ Erro na transferência SCP" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️ SCP não encontrado, deploy manual necessário" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Erro no deploy automático: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "📋 Use as instruções manuais acima" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📊 STATUS: Deploy preparado e instruções fornecidas" -ForegroundColor Blue
Write-Host "🔧 Execute os comandos manuais se o automático falhou" -ForegroundColor Blue
