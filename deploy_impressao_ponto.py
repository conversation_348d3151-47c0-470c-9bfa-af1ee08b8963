#!/usr/bin/env python3
"""
Script para fazer deploy da correção da página de impressão de ponto
"""

import paramiko
import os
import sys
from datetime import datetime

def deploy_impressao_ponto():
    """Deploy da correção da página de impressão de ponto"""
    
    # Configurações do servidor
    hostname = '************'
    username = 'user'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        print("🔗 Conectando ao servidor...")
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        # 1. Fazer backup do arquivo atual
        print("\n📦 Fazendo backup do arquivo atual...")
        backup_command = f'''
        cd /var/www/controle-ponto
        
        # Backup do app_ponto_admin.py
        cp app_ponto_admin.py backup-build/app_ponto_admin_backup_impressao_$(date +%Y%m%d_%H%M%S).py
        
        echo "✅ Backup realizado com sucesso"
        '''
        
        stdin, stdout, stderr = ssh.exec_command(backup_command)
        backup_result = stdout.read().decode()
        print(backup_result)
        
        # 2. Criar o template relatorio_funcionario.html
        print("\n📄 Criando template relatorio_funcionario.html...")
        
        # Ler o conteúdo do template local
        with open('var/www/controle-ponto/templates/ponto_admin/relatorio_funcionario.html', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Escapar aspas para o comando
        template_content_escaped = template_content.replace("'", "'\"'\"'")
        
        create_template_command = f'''
        cd /var/www/controle-ponto/templates/ponto_admin
        
        # Criar o template
        cat > relatorio_funcionario.html << 'EOF'
{template_content}
EOF
        
        echo "✅ Template relatorio_funcionario.html criado"
        '''
        
        stdin, stdout, stderr = ssh.exec_command(create_template_command)
        template_result = stdout.read().decode()
        print(template_result)
        
        # 3. Copiar o arquivo app_ponto_admin.py atualizado
        print("\n🔧 Copiando app_ponto_admin.py atualizado...")

        # Ler o conteúdo do arquivo local
        with open('var/www/controle-ponto/app_ponto_admin.py', 'r', encoding='utf-8') as f:
            app_content = f.read()

        # Escapar aspas para o comando
        app_content_escaped = app_content.replace("'", "'\"'\"'")

        update_app_command = f'''
        cd /var/www/controle-ponto

        # Criar o arquivo atualizado
        cat > app_ponto_admin.py << 'EOF'
{app_content}
EOF

        echo "✅ app_ponto_admin.py atualizado"
        '''

        stdin, stdout, stderr = ssh.exec_command(update_app_command)
        app_result = stdout.read().decode()
        print(app_result)
        
        # 4. Reiniciar o serviço
        print("\n🔄 Reiniciando serviço...")
        restart_command = '''
        sudo systemctl restart controle-ponto
        sleep 3
        sudo systemctl status controle-ponto --no-pager -l
        '''
        
        stdin, stdout, stderr = ssh.exec_command(restart_command)
        restart_result = stdout.read().decode()
        print(restart_result)
        
        # 5. Verificar se o serviço está funcionando
        print("\n✅ Verificando status do serviço...")
        status_command = 'curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/ponto-admin/'
        
        stdin, stdout, stderr = ssh.exec_command(status_command)
        status_code = stdout.read().decode().strip()
        
        if status_code == '200':
            print("✅ Serviço funcionando corretamente!")
        else:
            print(f"⚠️ Status HTTP: {status_code}")
        
        ssh.close()
        
        print(f"\n🎉 DEPLOY CONCLUÍDO COM SUCESSO!")
        print(f"📅 Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        print(f"🔗 Teste a funcionalidade em: http://************/ponto-admin/")
        print(f"📝 A página de impressão agora está disponível na URL: /ponto-admin/funcionario/<id>/imprimir")
        
    except Exception as e:
        print(f"❌ Erro durante o deploy: {e}")
        return False
    
    return True

if __name__ == "__main__":
    deploy_impressao_ponto()
