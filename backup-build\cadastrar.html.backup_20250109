{% extends "base.html" %}

{% block title %}
    {% if modo_edicao %}Editar Funcionário{% else %}Cadastrar Funcionário{% endif %} - <PERSON>e <PERSON>o
{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .form-header {
        border-bottom: 2px solid #4fbdba;
        padding-bottom: 15px;
        margin-bottom: 25px;
    }
    
    .form-header h2 {
        color: #495057;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
    }
    
    .form-section {
        margin-bottom: 30px;
    }
    
    .section-title {
        color: #495057;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .form-grid-2 {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
    
    .form-grid-3 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 5px;
        font-size: 14px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px 12px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        background: #f8f9fa;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #4fbdba;
        background: white;
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    }
    
    .form-group.required label::after {
        content: " *";
        color: #dc3545;
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .checkbox-group input[type="checkbox"] {
        width: auto;
        margin: 0;
    }
    
    .error-list {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
    }
    
    .error-list h4 {
        margin: 0 0 10px 0;
        font-size: 16px;
    }
    
    .error-list ul {
        margin: 0;
        padding-left: 20px;
    }
    
    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        padding-top: 20px;
        border-top: 1px solid #dee2e6;
        margin-top: 30px;
    }
    
    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-primary {
        background: #4fbdba;
        color: white;
    }
    
    .btn-primary:hover {
        background: #3da8a6;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 189, 186, 0.3);
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #545b62;
        color: white;
    }
    
    .btn-success {
        background: #28a745;
        color: white;
    }
    
    .btn-success:hover {
        background: #218838;
    }
    
    .biometria-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }
    
    .biometria-button {
        background: #4fbdba;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        margin-top: 10px;
        transition: all 0.2s ease;
    }
    
    .biometria-button:hover {
        background: #3da8a6;
    }
    
    .biometria-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-left: 8px;
        background-color: #dc3545;
    }
    
    .biometria-indicator.captured {
        background-color: #28a745;
    }

    /* ✅ CORREÇÃO #5: Estilos para área de erro do modal */
    .modal-error-section {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        padding: 16px;
        margin-top: 20px;
        animation: slideDown 0.3s ease-out;
    }
    
    .error-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 12px;
    }
    
    .error-icon {
        font-size: 24px;
        color: #dc3545;
    }
    
    .error-title {
        color: #721c24;
        font-size: 16px;
        font-weight: 600;
        margin: 0;
    }
    
    .error-content {
        color: #721c24;
    }
    
    .error-message {
        font-size: 14px;
        margin-bottom: 12px;
        padding: 8px 12px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        border-left: 4px solid #dc3545;
    }
    
    .error-details {
        background: rgba(255, 255, 255, 0.5);
        padding: 12px;
        border-radius: 4px;
        margin: 10px 0;
        font-size: 12px;
    }
    
    .error-details h5 {
        margin: 0 0 8px 0;
        font-size: 13px;
        color: #495057;
    }
    
    .error-code,
    .error-timestamp,
    .error-suggestion {
        margin: 4px 0;
        padding: 4px 8px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 3px;
        font-family: 'Courier New', monospace;
    }
    
    .error-actions {
        display: flex;
        gap: 8px;
        margin-top: 12px;
        flex-wrap: wrap;
    }
    
    .btn-sm {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .btn-outline-info {
        background: transparent;
        color: #17a2b8;
        border: 1px solid #17a2b8;
    }
    
    .btn-outline-info:hover {
        background: #17a2b8;
        color: white;
    }
    
    .btn-warning {
        background: #ffc107;
        color: #212529;
    }
    
    .btn-warning:hover {
        background: #e0a800;
    }
    
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Indicadores visuais para campos auto-completados */
    .auto-filled {
        background-color: #e7f3ff !important;
        border-color: #0066cc !important;
        transition: all 0.3s ease;
    }
    
    @media (max-width: 768px) {
        .form-grid,
        .form-grid-2,
        .form-grid-3 {
            grid-template-columns: 1fr;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .form-container {
            padding: 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
    <div class="form-header">
        <h2>
            {% if modo_edicao %}
                ✏️ Editar Funcionário #{{ funcionario_id }}
            {% else %}
                👤 Cadastrar Novo Funcionário
            {% endif %}
        </h2>
    </div>
    
    {% if errors %}
    <div class="error-list">
        <h4>❌ Corrija os seguintes erros:</h4>
        <ul>
            {% for error in errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}
    
    <form method="POST" enctype="multipart/form-data">
        <!-- Dados Pessoais -->
        <div class="form-section">
            <h3 class="section-title">
                👤 Dados Pessoais
            </h3>
            
            <div class="form-grid">
                <div class="form-group required">
                    <label for="nome_completo">Nome Completo</label>
                    <input type="text" id="nome_completo" name="nome_completo" value="{{ data.nome_completo or '' }}" 
                           placeholder="Ex: João da Silva Santos" required oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group required">
                    <label for="cpf">CPF</label>
                    <input type="text" id="cpf" name="cpf" value="{{ data.cpf or '' }}" required maxlength="14" 
                           placeholder="000.000.000-00" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group required">
                    <label for="rg">RG</label>
                    <input type="text" id="rg" name="rg" value="{{ data.rg or '' }}" required maxlength="20"
                           placeholder="Ex: 12.345.678-9 ou MG-12.345.678" oninput="toUpperCase(this);">
                </div>
            </div>
            
            <div class="form-grid-3">
                <div class="form-group required">
                    <label for="data_nascimento">Data de Nascimento</label>
                    <input type="date" id="data_nascimento" name="data_nascimento" value="{{ data.data_nascimento or '' }}" required>
                </div>
                
                <div class="form-group">
                    <label for="sexo">Sexo</label>
                    <select id="sexo" name="sexo" onchange="atualizarNacionalidade()">
                        <option value="">Selecione...</option>
                        <option value="M" {% if data.sexo == 'M' %}selected{% endif %}>Masculino</option>
                        <option value="F" {% if data.sexo == 'F' %}selected{% endif %}>Feminino</option>
                        <option value="Outro" {% if data.sexo == 'Outro' %}selected{% endif %}>Outro</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="estado_civil">Estado Civil</label>
                    <select id="estado_civil" name="estado_civil">
                        <option value="">Selecione...</option>
                        <option value="Solteiro" {% if data.estado_civil == 'Solteiro' %}selected{% endif %}>Solteiro(a)</option>
                        <option value="Casado" {% if data.estado_civil == 'Casado' %}selected{% endif %}>Casado(a)</option>
                        <option value="Divorciado" {% if data.estado_civil == 'Divorciado' %}selected{% endif %}>Divorciado(a)</option>
                        <option value="Viuvo" {% if data.estado_civil == 'Viuvo' %}selected{% endif %}>Viúvo(a)</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group required">
                <label for="nacionalidade">Nacionalidade</label>
                <input type="text" id="nacionalidade" name="nacionalidade" value="{{ data.nacionalidade or 'Brasileira' }}" 
                       placeholder="Ex: Brasileira, Americana, Italiana..." required oninput="toUpperCase(this);">
            </div>
        </div>
        
        <!-- Documentos Trabalhistas -->
        <div class="form-section">
            <h3 class="section-title">
                📋 Documentos Trabalhistas
            </h3>
            
            <div class="form-grid-3">
                <div class="form-group required">
                    <label for="ctps_numero">CTPS Número</label>
                    <input type="text" id="ctps_numero" name="ctps_numero" value="{{ data.ctps_numero or '' }}" 
                           placeholder="Ex: 1234567" maxlength="7" required oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group required">
                    <label for="ctps_serie_uf">CTPS Série/UF</label>
                    <input type="text" id="ctps_serie_uf" name="ctps_serie_uf" value="{{ data.ctps_serie_uf or '' }}" 
                           placeholder="Ex: 001/MG ou 123-MG" maxlength="10" required oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group required">
                    <label for="pis_pasep">PIS/PASEP</label>
                    <input type="text" id="pis_pasep" name="pis_pasep" value="{{ data.pis_pasep or '' }}" 
                           placeholder="000.00000.00-0" maxlength="14" required oninput="toUpperCase(this);">
                </div>
            </div>
        </div>
        
        <!-- Endereço -->
        <div class="form-section">
            <h3 class="section-title">
                🏠 Endereço
            </h3>
            
            <div class="form-grid-2">
                <div class="form-group required">
                    <label for="endereco_cep">CEP</label>
                    <input type="text" id="endereco_cep" name="endereco_cep" value="{{ data.endereco_cep or '' }}" 
                           required maxlength="9" placeholder="00000-000" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group required">
                    <label for="endereco_estado">Estado</label>
                    <select id="endereco_estado" name="endereco_estado" required>
                        <option value="">Selecione...</option>
                        {% for estado in ['AC','AL','AP','AM','BA','CE','DF','ES','GO','MA','MT','MS','MG','PA','PB','PR','PE','PI','RJ','RN','RS','RO','RR','SC','SP','SE','TO'] %}
                        <option value="{{ estado }}" {% if data.endereco_estado == estado %}selected{% endif %}>{{ estado }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="endereco_rua">Rua/Logradouro</label>
                    <input type="text" id="endereco_rua" name="endereco_rua" value="{{ data.endereco_rua or '' }}"
                           placeholder="Ex: Rua das Flores, 123, Apt 45" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group">
                    <label for="endereco_bairro">Bairro</label>
                    <input type="text" id="endereco_bairro" name="endereco_bairro" value="{{ data.endereco_bairro or '' }}"
                           placeholder="Ex: Centro, Vila Nova, Jardim América" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group">
                    <label for="endereco_cidade">Cidade</label>
                    <input type="text" id="endereco_cidade" name="endereco_cidade" value="{{ data.endereco_cidade or '' }}"
                           placeholder="Ex: São Paulo, Belo Horizonte, Rio de Janeiro" oninput="toUpperCase(this);">
                </div>
            </div>
        </div>
        
        <!-- Contato -->
        <div class="form-section">
            <h3 class="section-title">
                📞 Contato
            </h3>
            
            <div class="form-grid-3">
                <div class="form-group required">
                    <label for="telefone1">Telefone Principal</label>
                    <input type="tel" id="telefone1" name="telefone1" value="{{ data.telefone1 or '' }}" required 
                           placeholder="(11) 99999-9999" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group">
                    <label for="telefone2">Telefone Secundário</label>
                    <input type="tel" id="telefone2" name="telefone2" value="{{ data.telefone2 or '' }}" 
                           placeholder="(11) 3333-4444 (Opcional)" oninput="toUpperCase(this);">
                </div>
                
                <div class="form-group">
                    <label for="email">E-mail</label>
                    <input type="email" id="email" name="email" value="{{ data.email or '' }}" 
                           placeholder="<EMAIL>" oninput="toLowerCase(this);">
                </div>
            </div>
        </div>
        
        <!-- Dados Profissionais -->
        <div class="form-section">
            <h3 class="section-title">
                💼 Dados Profissionais
            </h3>
            
            <div class="form-grid">
                <div class="form-group required">
                    <label for="cargo">Cargo</label>
                    <input type="text" id="cargo" name="cargo" value="{{ data.cargo or '' }}" required
                           placeholder="Ex: Analista, Operador, Técnico, Auxiliar"
                           list="cargos_sugeridos" oninput="toUpperCase(this);">
                    <datalist id="cargos_sugeridos">
                        <option value="Analista Administrativo">
                        <option value="Auxiliar Administrativo">
                        <option value="Técnico em Segurança">
                        <option value="Operador de Máquinas">
                        <option value="Servente de Obras">
                        <option value="Pedreiro">
                        <option value="Eletricista">
                        <option value="Soldador">
                        <option value="Motorista">
                        <option value="Vigilante">
                    </datalist>
                </div>
                
                <div class="form-group required">
                    <label for="setor_obra">Setor/Obra</label>
                    <input type="text" id="setor_obra" name="setor_obra" value="{{ data.setor_obra or '' }}" required
                           placeholder="Ex: Administrativo, Produção, Obra Centro"
                           list="setores_sugeridos" oninput="toUpperCase(this);">
                    <datalist id="setores_sugeridos">
                        <option value="Administrativo">
                        <option value="Financeiro">
                        <option value="Recursos Humanos">
                        <option value="Produção">
                        <option value="Manutenção">
                        <option value="Segurança">
                        <option value="Obra Centro">
                        <option value="Obra Norte">
                        <option value="Obra Sul">
                    </datalist>
                </div>
                
                <div class="form-group required">
                    <label for="matricula_empresa">Matrícula</label>
                    <input type="text" id="matricula_empresa" name="matricula_empresa" 
                           value="{{ data.matricula_empresa or proxima_matricula or '' }}" required
                           placeholder="Ex: 2025001 (Gerado automaticamente)" oninput="toUpperCase(this);">
                </div>
            </div>
            
            <div class="form-grid-3">
                <div class="form-group required">
                    <label for="data_admissao">Data Admissão</label>
                    <input type="date" id="data_admissao" name="data_admissao" value="{{ data.data_admissao or '' }}" required>
                </div>
                
                <div class="form-group required">
                    <label for="tipo_contrato">Tipo Contrato</label>
                    <select id="tipo_contrato" name="tipo_contrato" required>
                        <option value="">Selecione...</option>
                        <option value="CLT" {% if data.tipo_contrato == 'CLT' %}selected{% endif %}>CLT (Consolidação das Leis do Trabalho)</option>
                        <option value="PJ" {% if data.tipo_contrato == 'PJ' %}selected{% endif %}>PJ (Pessoa Jurídica)</option>
                        <option value="Estagio" {% if data.tipo_contrato == 'Estagio' %}selected{% endif %}>Estágio</option>
                        <option value="Temporario" {% if data.tipo_contrato == 'Temporario' %}selected{% endif %}>Temporário</option>
                    </select>
                </div>
                
                <div class="form-group required">
                    <label for="status_cadastro">Status</label>
                    <select id="status_cadastro" name="status_cadastro" required>
                        <option value="Ativo" {% if data.status_cadastro == 'Ativo' or not data.status_cadastro %}selected{% endif %}>Ativo</option>
                        <option value="Inativo" {% if data.status_cadastro == 'Inativo' %}selected{% endif %}>Inativo</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Jornada de Trabalho -->
        <div class="form-section">
            <h3 class="section-title">
                ⏰ Jornada de Trabalho
            </h3>
            
            <div class="form-grid-2">
                <div>
                    <h4 style="margin-bottom: 15px; color: #6c757d;">Segunda a Quinta</h4>
                    <div class="form-grid-2">
                        <div class="form-group required">
                            <label for="jornada_seg_qui_entrada">Entrada</label>
                            <input type="time" id="jornada_seg_qui_entrada" name="jornada_seg_qui_entrada" 
                                   value="{{ data.jornada_seg_qui_entrada or '07:00' }}" required>
                        </div>
                        <div class="form-group required">
                            <label for="jornada_seg_qui_saida">Saída</label>
                            <input type="time" id="jornada_seg_qui_saida" name="jornada_seg_qui_saida" 
                                   value="{{ data.jornada_seg_qui_saida or '17:00' }}" required>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 style="margin-bottom: 15px; color: #6c757d;">Sexta-feira</h4>
                    <div class="form-grid-2">
                        <div class="form-group required">
                            <label for="jornada_sex_entrada">Entrada</label>
                            <input type="time" id="jornada_sex_entrada" name="jornada_sex_entrada" 
                                   value="{{ data.jornada_sex_entrada or '07:00' }}" required>
                        </div>
                        <div class="form-group required">
                            <label for="jornada_sex_saida">Saída</label>
                            <input type="time" id="jornada_sex_saida" name="jornada_sex_saida" 
                                   value="{{ data.jornada_sex_saida or '16:00' }}" required>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-grid-2" style="margin-top: 20px;">
                <div>
                    <h4 style="margin-bottom: 15px; color: #6c757d;">Intervalo</h4>
                    <div class="form-grid-2">
                        <div class="form-group required">
                            <label for="jornada_intervalo_entrada">Início</label>
                            <input type="time" id="jornada_intervalo_entrada" name="jornada_intervalo_entrada" 
                                   value="{{ data.jornada_intervalo_entrada or '12:00' }}" required>
                        </div>
                        <div class="form-group required">
                            <label for="jornada_intervalo_saida">Fim</label>
                            <input type="time" id="jornada_intervalo_saida" name="jornada_intervalo_saida" 
                                   value="{{ data.jornada_intervalo_saida or '13:00' }}" required>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 style="margin-bottom: 15px; color: #6c757d;">Configurações</h4>
                    <div class="form-grid">
                        <div class="form-group required">
                            <label for="nivel_acesso">Nível Acesso</label>
                            <select id="nivel_acesso" name="nivel_acesso" required>
                                <option value="">Selecione...</option>
                                <option value="Funcionario" {% if data.nivel_acesso == 'Funcionario' %}selected{% endif %}>Funcionário</option>
                                <option value="Supervisao" {% if data.nivel_acesso == 'Supervisao' %}selected{% endif %}>Supervisão</option>
                                <option value="Gerencia" {% if data.nivel_acesso == 'Gerencia' %}selected{% endif %}>Gerência</option>
                            </select>
                        </div>
                        
                        <div class="form-group required">
                            <label for="turno">Turno</label>
                            <select id="turno" name="turno" required>
                                <option value="">Selecione...</option>
                                <option value="Diurno" {% if data.turno == 'Diurno' %}selected{% endif %}>Diurno (06:00 - 18:00)</option>
                                <option value="Noturno" {% if data.turno == 'Noturno' %}selected{% endif %}>Noturno (18:00 - 06:00)</option>
                                <option value="Misto" {% if data.turno == 'Misto' %}selected{% endif %}>Misto (Variável)</option>
                            </select>
                        </div>
                        
                        <div class="form-group required">
                            <label for="tolerancia_ponto">Tolerância (min)</label>
                            <input type="number" id="tolerancia_ponto" name="tolerancia_ponto" 
                                   value="{{ data.tolerancia_ponto or '5' }}" required min="0" max="60"
                                   placeholder="5 (minutos de tolerância)">
                        </div>
                    </div>
                    
                    <div class="form-grid-2" style="margin-top: 15px;">
                        <div class="checkbox-group">
                            <input type="checkbox" id="banco_horas" name="banco_horas" {% if data.banco_horas %}checked{% endif %}>
                            <label for="banco_horas">Banco de Horas</label>
                        </div>
                        
                        <div class="checkbox-group">
                            <input type="checkbox" id="hora_extra" name="hora_extra" {% if data.hora_extra %}checked{% endif %}>
                            <label for="hora_extra">Hora Extra</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Remuneração e Pagamento -->
        <div class="form-section">
            <h3 class="section-title">
                💰 Remuneração e Pagamento
            </h3>
            
            <div class="form-grid">
                <div class="form-group required">
                    <label for="salario_base">Salário Base (R$)</label>
                    <input type="number" id="salario_base" name="salario_base" 
                           value="{{ data.salario_base or '' }}" 
                           step="0.01" min="0" max="999999.99"
                           placeholder="1400.00" onchange="calcularValorHora()">
                </div>
                
                <div class="form-group">
                    <label for="tipo_pagamento">Tipo de Pagamento</label>
                    <select id="tipo_pagamento" name="tipo_pagamento">
                        <option value="Mensal" {% if data.tipo_pagamento == 'Mensal' %}selected{% endif %}>Mensal</option>
                        <option value="Quinzenal" {% if data.tipo_pagamento == 'Quinzenal' %}selected{% endif %}>Quinzenal</option>
                        <option value="Semanal" {% if data.tipo_pagamento == 'Semanal' %}selected{% endif %}>Semanal</option>
                        <option value="Diario" {% if data.tipo_pagamento == 'Diario' %}selected{% endif %}>Diário</option>
                        <option value="Por_Hora" {% if data.tipo_pagamento == 'Por_Hora' %}selected{% endif %}>Por Hora</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="valor_hora">Valor da Hora (R$)</label>
                    <input type="number" id="valor_hora" name="valor_hora" 
                           value="{{ data.valor_hora or '' }}" 
                           step="0.01" min="0" max="9999.99"
                           placeholder="6.36" readonly style="background: #f8f9fa;">
                    <small style="color: #6c757d; font-size: 12px;">Calculado automaticamente: Salário ÷ 220h/mês</small>
                </div>
            </div>
            
            <div class="form-grid-3">
                <div class="form-group">
                    <label for="percentual_hora_extra">% Hora Extra</label>
                    <input type="number" id="percentual_hora_extra" name="percentual_hora_extra" 
                           value="{{ data.percentual_hora_extra or '50.00' }}" 
                           step="0.01" min="0" max="200"
                           placeholder="50.00" onchange="calcularValorHoraExtra()">
                </div>
                
                <div class="form-group">
                    <label for="valor_hora_extra">Valor Hora Extra (R$)</label>
                    <input type="number" id="valor_hora_extra" name="valor_hora_extra" 
                           value="{{ data.valor_hora_extra or '' }}" 
                           step="0.01" min="0" max="9999.99"
                           placeholder="9.54" readonly style="background: #f8f9fa;">
                    <small style="color: #6c757d; font-size: 12px;">Calculado: Valor hora + percentual</small>
                </div>
                
                <div class="form-group">
                    <label for="vale_transporte">Vale Transporte (R$)</label>
                    <input type="number" id="vale_transporte" name="vale_transporte" 
                           value="{{ data.vale_transporte or '' }}" 
                           step="0.01" min="0" max="999.99"
                           placeholder="150.00">
                </div>
            </div>
            
            <div class="form-grid-3">
                <div class="form-group">
                    <label for="vale_alimentacao">Vale Alimentação (R$)</label>
                    <input type="number" id="vale_alimentacao" name="vale_alimentacao" 
                           value="{{ data.vale_alimentacao or '' }}" 
                           step="0.01" min="0" max="999.99"
                           placeholder="400.00">
                </div>
                
                <div class="form-group">
                    <label for="outros_beneficios">Outros Benefícios (R$)</label>
                    <input type="number" id="outros_beneficios" name="outros_beneficios" 
                           value="{{ data.outros_beneficios or '' }}" 
                           step="0.01" min="0" max="9999.99"
                           placeholder="0.00">
                </div>
                
                <div class="form-group" style="display: flex; flex-direction: column; gap: 10px;">
                    <label>Descontos</label>
                    <div class="checkbox-group">
                        <input type="checkbox" id="desconto_inss" name="desconto_inss" 
                               {% if data.desconto_inss is none or data.desconto_inss %}checked{% endif %}>
                        <label for="desconto_inss">INSS</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="desconto_irrf" name="desconto_irrf" 
                               {% if data.desconto_irrf is none or data.desconto_irrf %}checked{% endif %}>
                        <label for="desconto_irrf">IRRF</label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="observacoes_pagamento">Observações sobre Pagamento</label>
                <textarea id="observacoes_pagamento" name="observacoes_pagamento" 
                          rows="3" style="resize: vertical;"
                          placeholder="Informações adicionais sobre pagamento, benefícios específicos, comissões, etc.">{{ data.observacoes_pagamento or '' }}</textarea>
            </div>
        </div>
        
        <!-- Biometria e Foto -->
        <div class="form-section">
            <h3 class="section-title">
                🔐 Biometria e Foto
            </h3>
            
            <!-- Foto 3x4 -->
            <div class="form-group" style="margin-bottom: 30px;">
                <label for="foto_3x4">Foto 3x4 (Opcional)</label>
                <div style="display: flex; align-items: center; gap: 20px; margin-top: 10px;">
                    <div>
                        <input type="file" id="foto_3x4" name="foto_3x4" accept="image/*" style="margin-bottom: 10px;">
                        <div style="font-size: 12px; color: #6c757d;">
                            ✅ Formatos aceitos: JPG, JPEG, PNG, GIF. Tamanho máximo: 5MB
                            <br><small>Sistema verifica o tipo real do arquivo por segurança</small>
                        </div>
                    </div>
                    <div>
                        <img id="preview_foto" src="{% if data.foto_3x4 %}/static/{{ data.foto_3x4 }}{% else %}/static/images/funcionario_sem_foto.svg{% endif %}" 
                             alt="Preview da foto" 
                             style="width: 120px; height: 160px; border: 1px solid #dee2e6; border-radius: 6px; object-fit: cover;">
                    </div>
                </div>
            </div>
            
            <!-- Novo botão de biometria -->
            <div class="biometria-section">
                <div id="biometriaStatusSection">
                    {% if modo_edicao and (data.digital_dedo1 or data.digital_dedo2) %}
                    <!-- ✅ BIOMETRIA EXISTENTE - Status e proteção -->
                    <div class="biometria-status-existente" style="background: #e8f5e8; border: 2px solid #28a745; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <span style="font-size: 24px;">🔐</span>
                            <div>
                                <h4 style="margin: 0; color: #155724;">Biometria Registrada</h4>
                                <p style="margin: 0; color: #155724; font-size: 14px;">
                                    Este funcionário possui biometria cadastrada no sistema
                                </p>
                            </div>
                        </div>
                        
                        <div class="biometria-detalhes" style="background: white; padding: 10px; border-radius: 6px; margin: 10px 0;">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                {% if data.digital_dedo1 %}
                                <div class="dedo-status">
                                    <strong>🔐 Dedo 1:</strong> 
                                    <span style="color: #28a745; font-weight: bold;">✅ Registrado</span>
                                </div>
                                {% else %}
                                <div class="dedo-status">
                                    <strong>🔐 Dedo 1:</strong> 
                                    <span style="color: #6c757d;">❌ Não registrado</span>
                                </div>
                                {% endif %}
                                
                                {% if data.digital_dedo2 %}
                                <div class="dedo-status">
                                    <strong>🔐 Dedo 2:</strong> 
                                    <span style="color: #28a745; font-weight: bold;">✅ Registrado</span>
                                </div>
                                {% else %}
                                <div class="dedo-status">
                                    <strong>🔐 Dedo 2:</strong> 
                                    <span style="color: #6c757d;">❌ Não registrado</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if current_user.is_admin %}
                        <!-- ✅ ADMIN: Pode editar biometria -->
                        <div class="admin-controls" style="background: #fff3cd; padding: 10px; border-radius: 6px; border: 1px solid #ffeaa7;">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                <span style="font-size: 18px;">⚠️</span>
                                <strong style="color: #856404;">Atenção - Você está editando biometria existente</strong>
                            </div>
                            <p style="margin: 0; color: #856404; font-size: 13px;">
                                Como administrador, você pode substituir a biometria atual. Esta ação é irreversível!
                            </p>
                        </div>
                        
                        <button type="button" class="btn btn-warning" onclick="confirmarEdicaoBiometria()" style="margin-top: 10px;">
                            ⚠️ Substituir Biometria (Admin)
                        </button>
                        {% else %}
                        <!-- ❌ NÃO ADMIN: Bloqueado para edição -->
                        <div class="user-blocked" style="background: #f8d7da; padding: 10px; border-radius: 6px; border: 1px solid #f5c6cb;">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                <span style="font-size: 18px;">🚫</span>
                                <strong style="color: #721c24;">Acesso Restrito</strong>
                            </div>
                            <p style="margin: 0; color: #721c24; font-size: 13px;">
                                Somente administradores podem editar biometria existente. Esta é uma medida de segurança.
                            </p>
                        </div>
                        
                        <button type="button" class="btn btn-secondary" onclick="mostrarAlertaPermissao()" disabled style="margin-top: 10px;">
                            🚫 Edição Bloqueada
                        </button>
                        {% endif %}
                    </div>
                    {% else %}
                    <!-- ❌ SEM BIOMETRIA - Captura normal -->
                    <div class="biometria-status-vazia" style="background: #fff3cd; border: 2px solid #ffc107; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <span style="font-size: 24px;">⚠️</span>
                            <div>
                                <h4 style="margin: 0; color: #856404;">Biometria Não Configurada</h4>
                                <p style="margin: 0; color: #856404; font-size: 14px;">
                                    Este funcionário ainda não possui biometria registrada para controle de acesso.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-primary" onclick="abrirModalBiometria()">
                        🔐 Capturar Biometria
                    </button>
                    {% endif %}
                </div>
                
                <!-- Campos hidden preservados -->
                <input type="hidden" id="digital_dedo1" name="digital_dedo1" value="{{ data.digital_dedo1 or '' }}">
                <input type="hidden" id="digital_dedo2" name="digital_dedo2" value="{{ data.digital_dedo2 or '' }}">
            </div>
        </div>
        
        <!-- Modal de Biometria Melhorado -->
        <div id="modalBiometria" class="modal-biometria" style="display:none;">
            <div class="modal-backdrop"></div>
            <div class="modal-container">
                <div class="modal-header">
                    <h2>🔐 Captura Biométrica</h2>
                    <button type="button" class="modal-close" onclick="fecharModalBiometria()">&times;</button>
                </div>
                
                <div class="modal-content">
                    <!-- Status e Progresso -->
                    <div class="biometria-status">
                        <div class="status-bar">
                            <div class="status-step active" id="step-1">
                                <div class="step-number">1</div>
                                <div class="step-label">Conexão</div>
                            </div>
                            <div class="status-step" id="step-2">
                                <div class="step-number">2</div>
                                <div class="step-label">Dedo 1</div>
                            </div>
                            <div class="status-step" id="step-3">
                                <div class="step-number">3</div>
                                <div class="step-label">Dedo 2</div>
                            </div>
                            <div class="status-step" id="step-4">
                                <div class="step-number">4</div>
                                <div class="step-label">Concluído</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Área de Visualização Biométrica -->
                    <div class="biometria-display">
                        <div class="biometria-scanner">
                            <div class="scanner-frame">
                                <div class="scanner-area" id="scannerArea">
                                    <div class="fingerprint-icon">
                                        <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
                                            <path d="M60 20C45.9 20 34.5 31.4 34.5 45.5C34.5 52.1 36.8 58.2 40.7 62.9L60 95L79.3 62.9C83.2 58.2 85.5 52.1 85.5 45.5C85.5 31.4 74.1 20 60 20Z" stroke="#007bff" stroke-width="2" fill="rgba(0,123,255,0.1)"/>
                                            <circle cx="60" cy="45" r="15" stroke="#007bff" stroke-width="2" fill="none"/>
                                            <path d="M45 45C45 36.7 51.7 30 60 30" stroke="#007bff" stroke-width="2" stroke-linecap="round"/>
                                            <path d="M50 45C50 39.5 54.5 35 60 35" stroke="#007bff" stroke-width="2" stroke-linecap="round"/>
                                            <path d="M55 45C55 42.2 57.2 40 60 40" stroke="#007bff" stroke-width="2" stroke-linecap="round"/>
                                        </svg>
                                    </div>
                                    <div class="scanner-pulse" id="scannerPulse"></div>
                                </div>
                                <div class="scanner-label">Coloque o dedo no leitor ZK4500</div>
                            </div>
                        </div>
                        
                        <!-- Área de Instruções -->
                        <div class="biometria-instructions">
                            <div class="instruction-card" id="instructionCard">
                                <h4>Preparação</h4>
                                <ul>
                                    <li>Conecte o leitor ZK4500 via USB</li>
                                    <li>Certifique-se que o dedo está limpo e seco</li>
                                    <li>Posicione o dedo centralmente no leitor</li>
                                    <li>Mantenha pressão constante até o sinal</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Resultado das Capturas -->
                    <div class="capturas-resultado" id="capturasResultado" style="display:none;">
                        <div class="resultado-grid">
                            <div class="resultado-card" id="resultadoDedo1">
                                <div class="resultado-header">
                                    <h5>Dedo 1</h5>
                                    <span class="status-badge pending">Aguardando</span>
                                </div>
                                <div class="qualidade-meter">
                                    <div class="qualidade-bar">
                                        <div class="qualidade-fill" data-qualidade="0"></div>
                                    </div>
                                    <span class="qualidade-valor">0%</span>
                                </div>
                            </div>
                            
                            <div class="resultado-card" id="resultadoDedo2">
                                <div class="resultado-header">
                                    <h5>Dedo 2</h5>
                                    <span class="status-badge pending">Aguardando</span>
                                </div>
                                <div class="qualidade-meter">
                                    <div class="qualidade-bar">
                                        <div class="qualidade-fill" data-qualidade="0"></div>
                                    </div>
                                    <span class="qualidade-valor">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status Geral -->
                    <div class="status-message">
                        <div id="statusBiometria" class="status-text">
                            Clique em "Iniciar Captura" para começar
                        </div>
                    </div>
                    
                    <!-- ✅ CORREÇÃO #5: Área específica para exibição de erros detalhados -->
                    <div class="modal-error-section" id="modalErrorSection" style="display: none;">
                        <div class="error-header">
                            <div class="error-icon">⚠️</div>
                            <h4 class="error-title">Erro na Captura Biométrica</h4>
                        </div>
                        
                        <div class="error-content">
                            <div class="error-message" id="errorMessage">
                                <!-- Mensagem de erro será inserida aqui -->
                            </div>
                            
                            <div class="error-details" id="errorDetails" style="display: none;">
                                <h5>📋 Detalhes Técnicos:</h5>
                                <div class="error-code" id="errorCode"></div>
                                <div class="error-timestamp" id="errorTimestamp"></div>
                                <div class="error-suggestion" id="errorSuggestion"></div>
                            </div>
                            
                            <div class="error-actions">
                                <button type="button" class="btn btn-sm btn-outline-info" onclick="mostrarDetalhesErro()">
                                    🔍 Ver Detalhes Técnicos
                                </button>
                                <button type="button" class="btn btn-sm btn-warning" onclick="tentarNovamente()">
                                    🔄 Tentar Novamente
                                </button>
                                <button type="button" class="btn btn-sm btn-secondary" onclick="limparErros()">
                                    ❌ Limpar Erro
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Ações do Modal -->
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="fecharModalBiometria()">
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="btnIniciarCaptura" onclick="iniciarCapturaBiometria()">
                        Iniciar Captura
                    </button>
                    <button type="button" class="btn btn-success" id="btnSalvarBiometria" onclick="salvarBiometriaModal()" style="display:none;">
                        Salvar Biometria
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Ações -->
        <div class="form-actions">
            <a href="/funcionarios" class="btn btn-secondary">
                ← Cancelar
            </a>
            <button type="submit" class="btn btn-primary">
                {% if modo_edicao %}
                    💾 Salvar Alterações
                {% else %}
                    ✅ Cadastrar Funcionário
                {% endif %}
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<!-- Script de funcionalidades do formulário -->
<script src="{{ url_for('static', filename='js/cadastro-funcionarios.js') }}"></script>
<!-- Script de biometria ZKAgent -->
<script src="{{ url_for('static', filename='js/biometria-zkagent.js') }}"></script>
{% endblock %} 