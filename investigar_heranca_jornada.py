#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para investigar a herança de jornadas de funcionários
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def investigar_heranca_jornada():
    """Investigar por que funcionários não herdam jornadas corretamente"""
    print("🔍 INVESTIGANDO HERANÇA DE JORNADAS")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar jornada da empresa AiNexus
        print("\n1. Verificando jornada da empresa AiNexus...")
        sql_jornada_ainexus = """
        SELECT 
            j.id, j.nome_jornada, j.tipo_jornada,
            j.seg_qui_entrada, j.seg_qui_saida,
            j.sexta_entrada, j.sexta_saida,
            j.intervalo_inicio, j.intervalo_fim,
            j.tolerancia_entrada_minutos,
            j.padrao, j.ativa,
            e.razao_social as empresa_nome
        FROM jornadas_trabalho j
        JOIN empresas e ON j.empresa_id = e.id
        WHERE e.razao_social LIKE '%%AiNexus%%' AND j.ativa = TRUE
        ORDER BY j.padrao DESC
        """
        
        jornadas_ainexus = db.execute_query(sql_jornada_ainexus)
        
        print(f"📊 Jornadas da AiNexus encontradas: {len(jornadas_ainexus)}")
        for jornada in jornadas_ainexus:
            padrao = "PADRÃO" if jornada['padrao'] else "NORMAL"
            print(f"   - ID {jornada['id']}: {jornada['nome_jornada']} ({padrao})")
            print(f"     Seg-Qui: {jornada['seg_qui_entrada']} às {jornada['seg_qui_saida']}")
            print(f"     Sexta: {jornada['sexta_entrada']} às {jornada['sexta_saida']}")
            print(f"     Intervalo: {jornada['intervalo_inicio']} às {jornada['intervalo_fim']}")
            print(f"     Tolerância: {jornada['tolerancia_entrada_minutos']} minutos")
        
        # 2. Verificar funcionários da AiNexus
        print("\n2. Verificando funcionários da AiNexus...")
        sql_funcionarios_ainexus = """
        SELECT 
            f.id, f.nome_completo, f.empresa_id,
            f.horario_trabalho_id,
            f.jornada_seg_qui_entrada, f.jornada_seg_qui_saida,
            f.jornada_sex_entrada, f.jornada_sex_saida,
            f.jornada_intervalo_entrada, f.jornada_intervalo_saida,
            e.razao_social as empresa_nome
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        WHERE e.razao_social LIKE '%%AiNexus%%' AND f.status_cadastro = 'Ativo'
        ORDER BY f.nome_completo
        """
        
        funcionarios_ainexus = db.execute_query(sql_funcionarios_ainexus)
        
        print(f"📊 Funcionários da AiNexus encontrados: {len(funcionarios_ainexus)}")
        for func in funcionarios_ainexus:
            print(f"   - ID {func['id']}: {func['nome_completo']}")
            print(f"     Horário Trabalho ID: {func['horario_trabalho_id']}")
            print(f"     Jornada Seg-Qui: {func['jornada_seg_qui_entrada']} às {func['jornada_seg_qui_saida']}")
            print(f"     Jornada Sexta: {func['jornada_sex_entrada']} às {func['jornada_sex_saida']}")
            print(f"     Intervalo: {func['jornada_intervalo_entrada']} às {func['jornada_intervalo_saida']}")
        
        # 3. Verificar se existe tabela horarios_trabalho (legacy)
        print("\n3. Verificando tabela horarios_trabalho (legacy)...")
        try:
            sql_horarios_trabalho = """
            SELECT * FROM horarios_trabalho 
            WHERE id IN (SELECT DISTINCT horario_trabalho_id FROM funcionarios WHERE horario_trabalho_id IS NOT NULL)
            """
            horarios_trabalho = db.execute_query(sql_horarios_trabalho)
            
            print(f"📊 Horários de trabalho encontrados: {len(horarios_trabalho)}")
            for horario in horarios_trabalho:
                print(f"   - ID {horario['id']}: {horario}")
        except Exception as e:
            print(f"⚠️ Tabela horarios_trabalho não existe ou erro: {e}")
        
        # 4. Verificar função get_with_epis para funcionários
        print("\n4. Verificando como funcionários são carregados...")
        sql_funcionario_especifico = """
        SELECT 
            f.*,
            e.razao_social as empresa_nome,
            jt.nome_jornada, jt.seg_qui_entrada as jornada_empresa_seg_qui_entrada,
            jt.seg_qui_saida as jornada_empresa_seg_qui_saida,
            jt.sexta_entrada as jornada_empresa_sexta_entrada,
            jt.sexta_saida as jornada_empresa_sexta_saida,
            jt.intervalo_inicio as jornada_empresa_intervalo_inicio,
            jt.intervalo_fim as jornada_empresa_intervalo_fim
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN jornadas_trabalho jt ON e.id = jt.empresa_id AND jt.padrao = TRUE AND jt.ativa = TRUE
        WHERE f.id = 1
        """
        
        funcionario_detalhado = db.execute_query(sql_funcionario_especifico)
        
        if funcionario_detalhado:
            func = funcionario_detalhado[0]
            print(f"📋 Detalhes do funcionário ID 1:")
            print(f"   Nome: {func['nome_completo']}")
            print(f"   Empresa: {func['empresa_nome']}")
            print(f"   Jornada da Empresa: {func['nome_jornada']}")
            print(f"   Horários da Empresa:")
            print(f"     Seg-Qui: {func['jornada_empresa_seg_qui_entrada']} às {func['jornada_empresa_seg_qui_saida']}")
            print(f"     Sexta: {func['jornada_empresa_sexta_entrada']} às {func['jornada_empresa_sexta_saida']}")
            print(f"     Intervalo: {func['jornada_empresa_intervalo_inicio']} às {func['jornada_empresa_intervalo_fim']}")
            print(f"   Horários do Funcionário:")
            print(f"     Seg-Qui: {func['jornada_seg_qui_entrada']} às {func['jornada_seg_qui_saida']}")
            print(f"     Sexta: {func['jornada_sex_entrada']} às {func['jornada_sex_saida']}")
            print(f"     Intervalo: {func['jornada_intervalo_entrada']} às {func['jornada_intervalo_saida']}")
        
        # 5. Verificar alocações do funcionário
        print("\n5. Verificando alocações do funcionário...")
        sql_alocacoes = """
        SELECT 
            fa.id, fa.funcionario_id, fa.empresa_cliente_id, fa.jornada_trabalho_id,
            f.nome_completo as funcionario_nome,
            ec.razao_social as cliente_nome,
            jt.nome_jornada as jornada_alocacao_nome,
            jt.seg_qui_entrada as jornada_alocacao_seg_qui_entrada,
            jt.seg_qui_saida as jornada_alocacao_seg_qui_saida
        FROM funcionario_alocacoes fa
        JOIN funcionarios f ON fa.funcionario_id = f.id
        JOIN empresas ec ON fa.empresa_cliente_id = ec.id
        LEFT JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
        WHERE fa.funcionario_id = 1 AND fa.ativo = TRUE
        """
        
        alocacoes = db.execute_query(sql_alocacoes)
        
        print(f"📊 Alocações do funcionário ID 1: {len(alocacoes)}")
        for alocacao in alocacoes:
            print(f"   - Alocação ID {alocacao['id']}: {alocacao['funcionario_nome']} → {alocacao['cliente_nome']}")
            print(f"     Jornada da Alocação: {alocacao['jornada_alocacao_nome']}")
            print(f"     Horário: {alocacao['jornada_alocacao_seg_qui_entrada']} às {alocacao['jornada_alocacao_seg_qui_saida']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a investigação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    investigar_heranca_jornada()
