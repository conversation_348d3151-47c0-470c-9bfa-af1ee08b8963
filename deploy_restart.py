#!/usr/bin/env python3
"""
Deploy e Restart do servidor RLPONTO-WEB
Modernização da página de funcionários aplicada
"""

import paramiko
import time
import sys

def restart_server():
    """Reinicia o servidor usando SSH"""
    
    # Configurações
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    print("🚀 DEPLOY E RESTART - RLPONTO-WEB")
    print("🎨 Aplicando modernização da página /funcionarios/")
    print("=" * 60)
    
    try:
        # Conectar via SSH
        print("🔗 Conectando ao servidor...")
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password, timeout=30)
        
        print("✅ Conectado com sucesso!")
        
        # Lista de comandos para executar
        commands = [
            # Verificar processos atuais
            ("Verificando processos Flask", "ps aux | grep python | grep app.py | grep -v grep || echo 'Nenhum processo encontrado'"),
            
            # Parar processos existentes
            ("Parando processos Flask", "pkill -f 'python.*app.py' || true"),
            
            # Aguardar
            ("Aguardando parada", "sleep 3"),
            
            # Verificar se parou
            ("Verificando parada", "pgrep -f 'python.*app.py' || echo 'Processos parados'"),
            
            # Navegar para diretório
            ("Navegando para diretório", "cd /var/www/controle-ponto && pwd"),
            
            # Verificar se arquivo existe
            ("Verificando app.py", "ls -la /var/www/controle-ponto/app.py"),
            
            # Iniciar servidor
            ("Iniciando servidor Flask", "cd /var/www/controle-ponto && nohup python3 app.py > flask.log 2>&1 &"),
            
            # Aguardar inicialização
            ("Aguardando inicialização", "sleep 5"),
            
            # Verificar se iniciou
            ("Verificando inicialização", "ps aux | grep python | grep app.py | grep -v grep || echo 'Servidor não iniciou'"),
            
            # Verificar logs
            ("Verificando logs", "tail -n 5 /var/www/controle-ponto/flask.log 2>/dev/null || echo 'Log não disponível'"),
            
            # Testar conexão local
            ("Testando conexão", "curl -s -o /dev/null -w '%{http_code}' http://localhost:5000/ || echo 'Erro na conexão'")
        ]
        
        # Executar comandos
        for description, command in commands:
            print(f"\n📋 {description}...")
            print(f"💻 Comando: {command}")
            
            try:
                stdin, stdout, stderr = ssh.exec_command(command, timeout=30)
                
                # Ler saída
                output = stdout.read().decode().strip()
                error = stderr.read().decode().strip()
                
                if output:
                    print(f"✅ Saída: {output}")
                if error:
                    print(f"⚠️ Erro: {error}")
                    
            except Exception as e:
                print(f"❌ Erro ao executar: {e}")
        
        # Verificação final
        print("\n" + "="*60)
        print("🔍 VERIFICAÇÃO FINAL")
        print("="*60)
        
        # Verificar se o servidor está rodando
        stdin, stdout, stderr = ssh.exec_command("ps aux | grep python | grep app.py | grep -v grep")
        processes = stdout.read().decode().strip()
        
        if processes:
            print("✅ Servidor Flask está rodando!")
            print(f"📋 Processos: {processes}")
        else:
            print("❌ Servidor Flask não está rodando!")
        
        # Verificar porta
        stdin, stdout, stderr = ssh.exec_command("netstat -tlnp | grep :5000 || echo 'Porta 5000 não está em uso'")
        port_check = stdout.read().decode().strip()
        print(f"🔌 Porta 5000: {port_check}")
        
        ssh.close()
        
        print("\n🎉 RESTART CONCLUÍDO!")
        print(f"🌐 Acesse: http://{hostname}/funcionarios/")
        print("🎨 A página foi modernizada com o novo design!")
        
        return True
        
    except paramiko.AuthenticationException:
        print("❌ Erro de autenticação SSH")
        return False
    except paramiko.SSHException as e:
        print(f"❌ Erro SSH: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False

def main():
    """Função principal"""
    
    print("🎯 Objetivo: Aplicar modernização da página /funcionarios/")
    print("📅 Data: 14/07/2025")
    print("📄 Baseado em: docs/layout-rlponto.md")
    print()
    
    # Executar restart
    success = restart_server()
    
    if success:
        print("\n✅ DEPLOY REALIZADO COM SUCESSO!")
        print("\n📋 Mudanças aplicadas:")
        print("• Header moderno com gradiente")
        print("• Seção de relatórios com botões coloridos")
        print("• Grid de cards responsivo")
        print("• Filtros modernizados")
        print("• Animações de entrada")
        print("• Modal moderno de exclusão")
        print("• Design baseado em layout-rlponto.md")
        
        print("\n🧪 Próximos passos:")
        print("1. Acesse http://************/funcionarios/")
        print("2. Teste todas as funcionalidades")
        print("3. Verifique responsividade em mobile")
        print("4. Confirme que animações estão funcionando")
        
    else:
        print("\n❌ FALHA NO DEPLOY")
        print("🔧 Verifique:")
        print("• Conexão com o servidor")
        print("• Permissões de arquivo")
        print("• Logs do servidor")
        
    print(f"\n📊 Status final: {'SUCESSO' if success else 'FALHA'}")

if __name__ == "__main__":
    main()
