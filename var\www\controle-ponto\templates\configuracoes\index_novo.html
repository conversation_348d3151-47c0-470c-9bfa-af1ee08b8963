{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    /* ========================================
     * SISTEMA DE CONFIGURAÇÕES MODERNO
     * Baseado em @21st-dev/magic inspirations
     * ======================================== */
    
    :root {
        --config-primary: #3b82f6;
        --config-primary-dark: #2563eb;
        --config-secondary: #64748b;
        --config-surface: #ffffff;
        --config-muted: #f8fafc;
        --config-border: #e2e8f0;
        --config-accent: #10b981;
        --config-warning: #f59e0b;
        --config-danger: #ef4444;
        --config-text: #1e293b;
        --config-text-muted: #64748b;
        --config-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
        --config-shadow-lg: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    }

    .config-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }

    /* HEADER PROFISSIONAL */
    .config-header {
        background: linear-gradient(135deg, var(--config-primary) 0%, var(--config-primary-dark) 100%);
        color: white;
        padding: 2rem;
        border-radius: 0.75rem;
        margin-bottom: 2rem;
        box-shadow: var(--config-shadow-lg);
    }

    .config-header h1 {
        margin: 0 0 0.5rem 0;
        font-size: 1.875rem;
        font-weight: 600;
        letter-spacing: -0.025em;
    }

    .config-header p {
        margin: 0;
        opacity: 0.9;
        font-size: 1rem;
    }

    /* SISTEMA DE ABAS MODERNO */
    .config-tabs-container {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.75rem;
        box-shadow: var(--config-shadow);
        overflow: hidden;
    }

    .config-tabs-nav {
        background: var(--config-muted);
        border-bottom: 1px solid var(--config-border);
        padding: 0;
        margin: 0;
        display: flex;
        overflow-x: auto;
    }

    .config-tab-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 1.5rem;
        background: transparent;
        border: none;
        color: var(--config-text-muted);
        font-weight: 500;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
        position: relative;
        border-bottom: 2px solid transparent;
    }

    .config-tab-button:hover {
        background: rgba(59, 130, 246, 0.05);
        color: var(--config-primary);
    }

    .config-tab-button.active {
        background: var(--config-surface);
        color: var(--config-primary);
        border-bottom-color: var(--config-primary);
    }

    .config-tab-button i {
        font-size: 1rem;
    }

    /* CONTEÚDO DAS ABAS */
    .config-tab-content {
        padding: 2rem;
        min-height: 500px;
        background: var(--config-surface);
    }

    .config-tab-pane {
        display: none;
        animation: fadeIn 0.3s ease-in-out;
    }

    .config-tab-pane.active {
        display: block;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* SEÇÕES DE CONFIGURAÇÃO */
    .config-section {
        margin-bottom: 2rem;
    }

    .config-section h3 {
        margin: 0 0 1rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--config-text);
    }

    .config-section p {
        margin: 0 0 1.5rem 0;
        color: var(--config-text-muted);
        line-height: 1.6;
    }

    /* CARDS DE AÇÃO */
    .action-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .action-card {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.5rem;
        padding: 1.5rem;
        transition: all 0.2s ease;
        cursor: pointer;
        text-decoration: none;
        color: inherit;
        box-shadow: var(--config-shadow);
    }

    .action-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--config-shadow-lg);
        text-decoration: none;
        color: inherit;
        border-color: var(--config-primary);
    }

    .action-card-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, var(--config-primary), var(--config-primary-dark));
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .action-card-icon i {
        font-size: 1.5rem;
        color: white;
    }

    .action-card h4 {
        margin: 0 0 0.5rem 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--config-text);
    }

    .action-card p {
        margin: 0;
        font-size: 0.875rem;
        color: var(--config-text-muted);
        line-height: 1.5;
    }

    /* EMPRESAS TAB SPECIFIC */
    .empresas-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .empresas-title {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--config-text);
    }

    .btn-nova-empresa {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: linear-gradient(135deg, var(--config-accent), #059669);
        color: white;
        border: none;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.2s ease;
        box-shadow: var(--config-shadow);
    }

    .btn-nova-empresa:hover {
        transform: translateY(-1px);
        box-shadow: var(--config-shadow-lg);
        color: white;
        text-decoration: none;
    }

    /* LISTA DE EMPRESAS */
    .empresas-list {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: var(--config-shadow);
    }

    .empresa-item {
        padding: 1.5rem;
        border-bottom: 1px solid var(--config-border);
        transition: background 0.2s ease;
    }

    .empresa-item:last-child {
        border-bottom: none;
    }

    .empresa-item:hover {
        background: var(--config-muted);
    }

    .empresa-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
        margin-bottom: 1rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .empresa-info h5 {
        margin: 0 0 0.25rem 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--config-text);
    }

    .empresa-info .empresa-cnpj {
        color: var(--config-text-muted);
        font-size: 0.875rem;
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    }

    .empresa-status {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-ativa {
        background: #dcfce7;
        color: #166534;
    }

    .status-inativa {
        background: #fef2f2;
        color: #991b1b;
    }

    .empresa-detalhes {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }

    .detalhe-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--config-text-muted);
        font-size: 0.875rem;
    }

    .detalhe-item i {
        color: var(--config-primary);
    }

    .empresa-acoes {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .btn-acao {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        border: none;
        cursor: pointer;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-editar {
        background: #fbbf24;
        color: #1f2937;
    }

    .btn-editar:hover {
        background: #f59e0b;
        color: #1f2937;
    }

    .btn-excluir {
        background: #ef4444;
        color: white;
    }

    .btn-excluir:hover {
        background: #dc2626;
    }

    /* EMPTY STATE */
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: var(--config-text-muted);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: var(--config-border);
    }

    .empty-state h4 {
        margin: 0 0 0.5rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--config-text);
    }

    .empty-state p {
        margin: 0;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    /* RESPONSIVO */
    @media (max-width: 768px) {
        .config-container {
            padding: 1rem 0.5rem;
        }
        
        .config-header {
            padding: 1.5rem;
        }
        
        .config-tab-content {
            padding: 1.5rem;
        }
        
        .action-grid {
            grid-template-columns: 1fr;
        }
        
        .empresas-header {
            flex-direction: column;
            align-items: stretch;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="config-container">
    <!-- HEADER -->
    <div class="config-header">
        <h1><i class="fas fa-cogs"></i> Configurações do Sistema</h1>
        <p>Gerencie todas as configurações e parâmetros do sistema RLPONTO-WEB</p>
    </div>

    <!-- SISTEMA DE ABAS -->
    <div class="config-tabs-container">
        <!-- NAVEGAÇÃO DAS ABAS -->
        <nav class="config-tabs-nav">
            <button class="config-tab-button active" data-tab="geral">
                <i class="fas fa-sliders-h"></i>
                Configurações Gerais
            </button>
            <button class="config-tab-button" data-tab="empresas">
                <i class="fas fa-building"></i>
                Empresas
            </button>
            <button class="config-tab-button" data-tab="biometria">
                <i class="fas fa-fingerprint"></i>
                Dispositivos Biométricos
            </button>
            <button class="config-tab-button" data-tab="horarios">
                <i class="fas fa-clock"></i>
                Horários de Trabalho
            </button>
            <button class="config-tab-button" data-tab="usuarios">
                <i class="fas fa-users"></i>
                Usuários
            </button>
            <button class="config-tab-button" data-tab="sistema">
                <i class="fas fa-server"></i>
                Sistema
            </button>
        </nav>

        <!-- CONTEÚDO DAS ABAS -->
        <div class="config-tab-content">
            <!-- ABA GERAL -->
            <div class="config-tab-pane active" id="tab-geral">
                <div class="config-section">
                    <h3>Configurações Gerais</h3>
                    <p>Configure os parâmetros principais do sistema de controle de ponto.</p>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <h4>Informações da Empresa</h4>
                            <p>Configure dados básicos como razão social, CNPJ e informações de contato.</p>
                        </div>
                        
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h4>Políticas de Segurança</h4>
                            <p>Defina regras de autenticação, senhas e controles de acesso.</p>
                        </div>
                        
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <h4>Notificações</h4>
                            <p>Configure alertas automáticos e notificações do sistema.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ABA EMPRESAS -->
            <div class="config-tab-pane" id="tab-empresas">
                <div class="empresas-header">
                    <h2 class="empresas-title">Gerenciar Empresas</h2>
                    <a href="{{ url_for('configuracoes.nova_empresa') }}" class="btn-nova-empresa">
                        <i class="fas fa-plus"></i>
                        Nova Empresa
                    </a>
                </div>

                <!-- LISTA DE EMPRESAS -->
                <div id="empresas-container">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--config-primary);"></i>
                        <p style="margin-top: 1rem; color: var(--config-text-muted);">Carregando empresas...</p>
                    </div>
                </div>
            </div>

            <!-- ABA BIOMETRIA -->
            <div class="config-tab-pane" id="tab-biometria">
                <div class="config-section">
                    <h3>Dispositivos Biométricos</h3>
                    <p>Configure e gerencie os dispositivos biométricos conectados ao sistema.</p>
                    
                    <div class="action-grid">
                        <div class="action-card" onclick="detectarDispositivos()">
                            <div class="action-card-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h4>Detectar Dispositivos</h4>
                            <p>Buscar automaticamente por dispositivos biométricos conectados.</p>
                        </div>
                        
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <h4>Configurar Dispositivos</h4>
                            <p>Ajustar parâmetros de qualidade, timeout e outras configurações.</p>
                        </div>
                        
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-vial"></i>
                            </div>
                            <h4>Testar Captura</h4>
                            <p>Realizar testes de captura biométrica para validar funcionamento.</p>
                        </div>
                    </div>

                    <!-- STATUS DOS DISPOSITIVOS -->
                    <div id="dispositivos-status" style="margin-top: 2rem;">
                        <!-- Será preenchido via JavaScript -->
                    </div>
                </div>
            </div>

            <!-- ABA HORÁRIOS -->
            <div class="config-tab-pane" id="tab-horarios">
                <div class="config-section">
                    <h3>Horários de Trabalho</h3>
                    <p>Configure os horários de trabalho padrão e personalizados para diferentes setores.</p>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            <h4>Novo Horário</h4>
                            <p>Criar um novo modelo de horário de trabalho.</p>
                        </div>
                        
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            <h4>Gerenciar Horários</h4>
                            <p>Visualizar e editar horários existentes.</p>
                        </div>
                        
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <h4>Feriados e Exceções</h4>
                            <p>Configurar feriados e horários especiais.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ABA USUÁRIOS -->
            <div class="config-tab-pane" id="tab-usuarios">
                <div class="config-section">
                    <h3>Gerenciamento de Usuários</h3>
                    <p>Configure usuários do sistema, permissões e níveis de acesso.</p>
                    
                    <div class="action-grid">
                        <a href="{{ url_for('configurar_usuarios') }}" class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <h4>Adicionar Usuário</h4>
                            <p>Criar novo usuário administrador do sistema.</p>
                        </a>
                        
                        <a href="{{ url_for('configurar_usuarios') }}" class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-users-cog"></i>
                            </div>
                            <h4>Gerenciar Usuários</h4>
                            <p>Visualizar, editar e excluir usuários existentes.</p>
                        </a>
                        
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <h4>Políticas de Senha</h4>
                            <p>Configurar regras de complexidade e expiração de senhas.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ABA SISTEMA -->
            <div class="config-tab-pane" id="tab-sistema">
                <div class="config-section">
                    <h3>Configurações do Sistema</h3>
                    <p>Configurações avançadas de banco de dados, logs e manutenção.</p>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <h4>Backup do Banco</h4>
                            <p>Realizar backup manual dos dados do sistema.</p>
                        </div>
                        
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <h4>Logs do Sistema</h4>
                            <p>Visualizar e gerenciar logs de atividades e erros.</p>
                        </div>
                        
                        <div class="action-card">
                            <div class="action-card-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <h4>Informações do Sistema</h4>
                            <p>Versão, status e informações técnicas do sistema.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// ========================================
// SISTEMA DE ABAS FUNCIONAL
// ========================================
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar sistema de abas
    initTabs();
    
    // Carregar empresas se a aba empresas estiver ativa
    const activeTab = document.querySelector('.config-tab-button.active');
    if (activeTab && activeTab.dataset.tab === 'empresas') {
        carregarEmpresas();
    }
});

function initTabs() {
    const tabButtons = document.querySelectorAll('.config-tab-button');
    const tabPanes = document.querySelectorAll('.config-tab-pane');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Remover classe active de todos os botões e panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // Adicionar classe active ao botão clicado
            this.classList.add('active');
            
            // Mostrar o pane correspondente
            const targetPane = document.getElementById(`tab-${targetTab}`);
            if (targetPane) {
                targetPane.classList.add('active');
                
                // Carregar conteúdo específico da aba
                if (targetTab === 'empresas') {
                    carregarEmpresas();
                } else if (targetTab === 'biometria') {
                    carregarStatusDispositivos();
                }
            }
        });
    });
}

async function carregarEmpresas() {
    const container = document.getElementById('empresas-container');
    
    try {
        const response = await fetch('{{ url_for("configuracoes.listar_empresas") }}');
        
        if (!response.ok) {
            throw new Error('Erro ao carregar empresas');
        }
        
        const html = await response.text();
        
        // Extrair apenas o conteúdo da lista de empresas
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const empresasList = doc.querySelector('.empresas-list');
        
        if (empresasList) {
            container.innerHTML = empresasList.outerHTML;
        } else {
            // Se não há empresas, mostrar estado vazio
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-building"></i>
                    <h4>Nenhuma empresa cadastrada</h4>
                    <p>Comece adicionando sua primeira empresa ao sistema.</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Erro ao carregar empresas:', error);
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle" style="color: var(--config-danger);"></i>
                <h4>Erro ao carregar empresas</h4>
                <p>Não foi possível carregar a lista de empresas. Tente novamente.</p>
            </div>
        `;
    }
}

async function carregarStatusDispositivos() {
    const container = document.getElementById('dispositivos-status');
    
    container.innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin" style="font-size: 1.5rem; color: var(--config-primary);"></i>
            <p style="margin-top: 0.5rem; color: var(--config-text-muted);">Verificando dispositivos...</p>
        </div>
    `;
    
    // Implementar verificação de dispositivos
    setTimeout(() => {
        container.innerHTML = `
            <div class="action-card" style="max-width: none;">
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <div class="action-card-icon" style="margin: 0;">
                        <i class="fas fa-fingerprint"></i>
                    </div>
                    <div>
                        <h4 style="margin: 0 0 0.25rem 0;">Status: Aguardando Detecção</h4>
                        <p style="margin: 0;">Clique em "Detectar Dispositivos" para buscar leitores conectados.</p>
                    </div>
                </div>
            </div>
        `;
    }, 1000);
}

function detectarDispositivos() {
    const container = document.getElementById('dispositivos-status');
    
    container.innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin" style="font-size: 1.5rem; color: var(--config-primary);"></i>
            <p style="margin-top: 0.5rem; color: var(--config-text-muted);">Detectando dispositivos...</p>
        </div>
    `;
    
    // Chamar API de detecção
    fetch('/configuracoes/api/detectar-dispositivos')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.dispositivos && data.dispositivos.length > 0) {
                let html = '';
                data.dispositivos.forEach(dispositivo => {
                    html += `
                        <div class="action-card" style="max-width: none; border-color: var(--config-accent);">
                            <div style="display: flex; align-items: center; gap: 1rem;">
                                <div class="action-card-icon" style="margin: 0; background: linear-gradient(135deg, var(--config-accent), #059669);">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div style="flex: 1;">
                                    <h4 style="margin: 0 0 0.25rem 0; color: var(--config-accent);">${dispositivo.name}</h4>
                                    <p style="margin: 0; font-size: 0.875rem;">Status: ${dispositivo.status} | ID: ${dispositivo.instance_id}</p>
                                </div>
                                <button onclick="testarDispositivo('${dispositivo.instance_id}', '${dispositivo.name}')" 
                                        style="background: var(--config-primary); color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.375rem; cursor: pointer;">
                                    Testar
                                </button>
                            </div>
                        </div>
                    `;
                });
                container.innerHTML = html;
            } else {
                container.innerHTML = `
                    <div class="action-card" style="max-width: none; border-color: var(--config-warning);">
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <div class="action-card-icon" style="margin: 0; background: linear-gradient(135deg, var(--config-warning), #d97706);">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div>
                                <h4 style="margin: 0 0 0.25rem 0; color: var(--config-warning);">Nenhum dispositivo encontrado</h4>
                                <p style="margin: 0;">Verifique se o dispositivo biométrico está conectado e os drivers instalados.</p>
                            </div>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Erro na detecção:', error);
            container.innerHTML = `
                <div class="action-card" style="max-width: none; border-color: var(--config-danger);">
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <div class="action-card-icon" style="margin: 0; background: linear-gradient(135deg, var(--config-danger), #dc2626);">
                            <i class="fas fa-times"></i>
                        </div>
                        <div>
                            <h4 style="margin: 0 0 0.25rem 0; color: var(--config-danger);">Erro na detecção</h4>
                            <p style="margin: 0;">Falha ao comunicar com a API de detecção de dispositivos.</p>
                        </div>
                    </div>
                </div>
            `;
        });
}

function testarDispositivo(instanceId, deviceName) {
    // Implementar teste de dispositivo
    alert(`Testando dispositivo: ${deviceName}\nID: ${instanceId}`);
}
</script>
{% endblock %} 