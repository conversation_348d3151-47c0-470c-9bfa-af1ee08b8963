#!/usr/bin/env python3
"""
Debug: O que acontece quando você salva a empresa?
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def debug_salvamento_empresa():
    """Debug do salvamento da empresa"""
    
    print("🔍 DEBUG: SALVAMENTO DA EMPRESA")
    print("=" * 50)
    
    db = DatabaseManager()
    
    # 1. Estado ANTES da alteração
    print("\n📋 ESTADO ANTES:")
    
    # Verificar jornada atual
    jornada_antes = db.execute_query("""
        SELECT id, nome_jornada, seg_qui_entrada, seg_qui_saida, tolerancia_entrada_minutos
        FROM jornadas_trabalho 
        WHERE empresa_id = 11 AND padrao = 1 AND ativa = 1
    """, fetch_one=True)
    
    if jornada_antes:
        print(f"   📋 Jornada: {jornada_antes['nome_jornada']} (ID: {jornada_antes['id']})")
        print(f"   ⏰ Horário: {jornada_antes['seg_qui_entrada']} às {jornada_antes['seg_qui_saida']}")
        print(f"   🕐 Tolerância: {jornada_antes['tolerancia_entrada_minutos']} min")
    
    # Verificar empresas_config
    config_antes = db.execute_query("""
        SELECT jornada_segunda_entrada, jornada_segunda_saida, tolerancia_empresa_minutos
        FROM empresas_config 
        WHERE empresa_id = 11
    """, fetch_one=True)
    
    if config_antes:
        print(f"   📊 Config: {config_antes['jornada_segunda_entrada']} às {config_antes['jornada_segunda_saida']}")
        print(f"   📊 Tolerância config: {config_antes['tolerancia_empresa_minutos']} min")
    else:
        print("   ❌ Nenhuma config encontrada")
    
    # 2. SIMULAR o salvamento da empresa (como se você tivesse clicado salvar)
    print(f"\n🔧 SIMULANDO SALVAMENTO: Alterando 09:00 para 07:00...")
    
    try:
        # Simular exatamente o que o código faz
        print("   📝 Atualizando empresas_config...")
        db.execute_query("""
            INSERT INTO empresas_config (
                empresa_id,
                jornada_segunda_entrada, jornada_segunda_saida_almoco,
                jornada_segunda_entrada_almoco, jornada_segunda_saida,
                jornada_sexta_entrada, jornada_sexta_saida_almoco,
                jornada_sexta_entrada_almoco, jornada_sexta_saida,
                intervalo_obrigatorio, tolerancia_empresa_minutos
            )
            VALUES (11, '07:00', '12:00', '13:00', '18:00', '07:00', '12:00', '13:00', '18:00', 1, 5)
            ON DUPLICATE KEY UPDATE
                jornada_segunda_entrada = VALUES(jornada_segunda_entrada),
                jornada_segunda_saida_almoco = VALUES(jornada_segunda_saida_almoco),
                jornada_segunda_entrada_almoco = VALUES(jornada_segunda_entrada_almoco),
                jornada_segunda_saida = VALUES(jornada_segunda_saida),
                jornada_sexta_entrada = VALUES(jornada_sexta_entrada),
                jornada_sexta_saida_almoco = VALUES(jornada_sexta_saida_almoco),
                jornada_sexta_entrada_almoco = VALUES(jornada_sexta_entrada_almoco),
                jornada_sexta_saida = VALUES(jornada_sexta_saida),
                intervalo_obrigatorio = VALUES(intervalo_obrigatorio),
                tolerancia_empresa_minutos = VALUES(tolerancia_empresa_minutos),
                data_atualizacao = CURRENT_TIMESTAMP
        """, fetch_all=False)
        
        print("   ✅ empresas_config atualizada")
        
        print("   📝 Atualizando jornadas_trabalho...")
        resultado = db.execute_query("""
            UPDATE jornadas_trabalho 
            SET seg_qui_entrada = '07:00:00',
                seg_qui_saida = '18:00:00',
                sexta_entrada = '07:00:00',
                sexta_saida = '18:00:00',
                intervalo_inicio = '12:00:00',
                intervalo_fim = '13:00:00',
                tolerancia_entrada_minutos = 5
            WHERE empresa_id = 11 AND padrao = 1 AND ativa = 1
        """, fetch_all=False)
        
        print(f"   ✅ jornadas_trabalho atualizada (linhas afetadas: {resultado})")
        
        if resultado == 0:
            print("   ❌ PROBLEMA: Nenhuma linha foi atualizada!")
            print("   💡 Isso significa que a condição WHERE não encontrou registros")
        
    except Exception as e:
        print(f"   ❌ ERRO durante simulação: {e}")
        return
    
    # 3. Estado DEPOIS da alteração
    print(f"\n📋 ESTADO DEPOIS:")
    
    # Verificar jornada após alteração
    jornada_depois = db.execute_query("""
        SELECT id, nome_jornada, seg_qui_entrada, seg_qui_saida, tolerancia_entrada_minutos
        FROM jornadas_trabalho 
        WHERE empresa_id = 11 AND padrao = 1 AND ativa = 1
    """, fetch_one=True)
    
    if jornada_depois:
        print(f"   📋 Jornada: {jornada_depois['nome_jornada']} (ID: {jornada_depois['id']})")
        print(f"   ⏰ Horário: {jornada_depois['seg_qui_entrada']} às {jornada_depois['seg_qui_saida']}")
        print(f"   🕐 Tolerância: {jornada_depois['tolerancia_entrada_minutos']} min")
        
        # Comparar
        if str(jornada_depois['seg_qui_entrada']) == '7:00:00':
            print("   ✅ SUCESSO: Jornada foi alterada para 07:00!")
        else:
            print(f"   ❌ FALHA: Jornada ainda está {jornada_depois['seg_qui_entrada']}")
    
    # 4. Verificar funcionário Richardson
    print(f"\n👤 VERIFICANDO FUNCIONÁRIO RICHARDSON:")
    
    funcionario = db.execute_query("""
        SELECT f.id, f.nome_completo, f.jornada_trabalho_id,
               jt.seg_qui_entrada, jt.tolerancia_entrada_minutos
        FROM funcionarios f
        INNER JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.id = 35
    """, fetch_one=True)
    
    if funcionario:
        print(f"   👤 {funcionario['nome_completo']}")
        print(f"   ⏰ Horário: {funcionario['seg_qui_entrada']}")
        print(f"   🕐 Tolerância: {funcionario['tolerancia_entrada_minutos']} min")
        
        if str(funcionario['seg_qui_entrada']) == '7:00:00':
            print("   ✅ FUNCIONÁRIO ATUALIZADO!")
        else:
            print("   ❌ Funcionário ainda não foi atualizado")
    
    # 5. Verificar logs do trigger
    print(f"\n📊 VERIFICANDO LOGS DO TRIGGER:")
    
    logs_novos = db.execute_query("""
        SELECT * FROM log_mudancas_jornada 
        WHERE data_mudanca >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ORDER BY data_mudanca DESC
        LIMIT 3
    """)
    
    if logs_novos:
        print(f"   📊 {len(logs_novos)} logs recentes:")
        for log in logs_novos:
            print(f"      • {log['data_mudanca']}: {log['tipo_mudanca']}")
    else:
        print("   📊 Nenhum log novo - trigger pode não ter disparado")
    
    print(f"\n🎯 DIAGNÓSTICO:")
    if jornada_depois and str(jornada_depois['seg_qui_entrada']) == '7:00:00':
        print("✅ CORREÇÃO FUNCIONOU! O sistema agora atualiza corretamente!")
        print("💡 Teste novamente na interface web")
    else:
        print("❌ Ainda há problema - investigar mais")

if __name__ == "__main__":
    debug_salvamento_empresa()
