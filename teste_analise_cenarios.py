#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 ANÁLISE DOS CENÁRIOS DE CÁLCULO DE HORAS
===========================================

Analisa os cenários específicos apresentados para verificar
se os cálculos estão corretos com as novas regras robustas.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'var', 'www', 'controle-ponto'))

from datetime import datetime, time
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger('analise_cenarios')

def analisar_cenario_manual(nome, data, entrada, saida_almoco, retorno_almo<PERSON>, saida, horas_negativas_sistema):
    """
    Analisa um cenário específico manualmente
    """
    print(f"\n📊 ANÁLISE DO CENÁRIO: {nome}")
    print("=" * 60)
    print(f"📅 Data: {data}")
    print(f"🕐 B1 (Entrada): {entrada}")
    print(f"🕐 B2 (Saída Almoço): {saida_almoco}")
    print(f"🕐 B3 (Retorno Almoço): {retorno_almoco}")
    print(f"🕐 B4 (Saída): {saida}")
    print(f"🔴 Horas Negativas (Sistema): {horas_negativas_sistema}")
    
    # Calcular manualmente seguindo as regras
    total_horas_trabalhadas = 0.0
    detalhes_calculo = []
    
    # PERÍODO MANHÃ: B1 e B2
    if entrada and saida_almoco:
        entrada_dt = datetime.combine(datetime.today(), entrada)
        saida_almoco_dt = datetime.combine(datetime.today(), saida_almoco)
        
        if saida_almoco_dt > entrada_dt:
            horas_manha = (saida_almoco_dt - entrada_dt).total_seconds() / 3600
            total_horas_trabalhadas += horas_manha
            detalhes_calculo.append(f"Manhã (B1-B2): {horas_manha:.2f}h")
            print(f"   ✅ Período manhã: {entrada} - {saida_almoco} = {horas_manha:.2f}h")
    
    # PERÍODO TARDE: B3 e B4
    if retorno_almoco and saida:
        retorno_dt = datetime.combine(datetime.today(), retorno_almoco)
        saida_dt = datetime.combine(datetime.today(), saida)
        
        if saida_dt > retorno_dt:
            horas_tarde = (saida_dt - retorno_dt).total_seconds() / 3600
            total_horas_trabalhadas += horas_tarde
            detalhes_calculo.append(f"Tarde (B3-B4): {horas_tarde:.2f}h")
            print(f"   ✅ Período tarde: {retorno_almoco} - {saida} = {horas_tarde:.2f}h")
    
    print(f"\n📋 CÁLCULO MANUAL:")
    print(f"   Detalhes: {' + '.join(detalhes_calculo) if detalhes_calculo else 'Nenhum período completo'}")
    print(f"   Total trabalhado: {total_horas_trabalhadas:.2f}h")
    
    # Jornada esperada (assumindo 9h como padrão: 08:00-18:00 - 1h almoço)
    jornada_esperada = 9.0
    horas_negativas_calculadas = max(0, jornada_esperada - total_horas_trabalhadas)
    
    print(f"\n🎯 ANÁLISE DE HORAS NEGATIVAS:")
    print(f"   Jornada esperada: {jornada_esperada:.2f}h")
    print(f"   Horas trabalhadas: {total_horas_trabalhadas:.2f}h")
    print(f"   Deficit calculado: {horas_negativas_calculadas:.2f}h")
    
    # Converter para formato HH:MM
    horas_int = int(horas_negativas_calculadas)
    minutos_int = int((horas_negativas_calculadas - horas_int) * 60)
    formato_hhmm = f"{horas_int:02d}:{minutos_int:02d}"
    
    print(f"   Formato HH:MM: -{formato_hhmm}")
    
    # Comparar com o sistema
    print(f"\n🔍 COMPARAÇÃO:")
    print(f"   Sistema mostra: {horas_negativas_sistema}")
    print(f"   Cálculo manual: -{formato_hhmm}")
    
    if horas_negativas_sistema == f"-{formato_hhmm}":
        print(f"   ✅ CORRETO - Sistema e cálculo manual coincidem")
    else:
        print(f"   ❌ DIVERGÊNCIA - Sistema e cálculo manual diferentes")
        print(f"   🔧 Possível problema no cálculo do sistema")
    
    return {
        'total_trabalhado': total_horas_trabalhadas,
        'deficit_calculado': horas_negativas_calculadas,
        'formato_hhmm': f"-{formato_hhmm}",
        'sistema_correto': horas_negativas_sistema == f"-{formato_hhmm}"
    }

def main():
    print("🔍 ANÁLISE DOS CENÁRIOS DE CÁLCULO DE HORAS")
    print("=" * 80)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 80)
    
    # Definir cenários baseados na imagem
    cenarios = [
        {
            'nome': 'Quinta-feira 17/07/2025',
            'data': '17/07/2025',
            'entrada': time(23, 16),
            'saida_almoco': time(23, 16),
            'retorno_almoco': None,
            'saida': None,
            'horas_negativas_sistema': '-9:00'
        },
        {
            'nome': 'Terça-feira 15/07/2025',
            'data': '15/07/2025',
            'entrada': time(10, 5),
            'saida_almoco': time(12, 42),
            'retorno_almoco': time(17, 45),
            'saida': time(17, 45),
            'horas_negativas_sistema': '-6:22'
        },
        {
            'nome': 'Segunda-feira 14/07/2025',
            'data': '14/07/2025',
            'entrada': time(7, 51),
            'saida_almoco': time(12, 30),
            'retorno_almoco': None,
            'saida': None,
            'horas_negativas_sistema': '-4:21'
        }
    ]
    
    resultados = []
    
    # Analisar cada cenário
    for cenario in cenarios:
        resultado = analisar_cenario_manual(
            cenario['nome'],
            cenario['data'],
            cenario['entrada'],
            cenario['saida_almoco'],
            cenario['retorno_almoco'],
            cenario['saida'],
            cenario['horas_negativas_sistema']
        )
        resultados.append({**cenario, **resultado})
    
    # Resumo final
    print(f"\n" + "=" * 80)
    print(f"📊 RESUMO FINAL DA ANÁLISE")
    print(f"=" * 80)
    
    corretos = sum(1 for r in resultados if r['sistema_correto'])
    total = len(resultados)
    
    print(f"✅ Cenários corretos: {corretos}/{total}")
    print(f"❌ Cenários com divergência: {total - corretos}/{total}")
    
    if corretos == total:
        print(f"\n🎉 TODOS OS CÁLCULOS ESTÃO CORRETOS!")
        print(f"✅ O sistema está calculando as horas negativas corretamente")
    else:
        print(f"\n⚠️ ENCONTRADAS DIVERGÊNCIAS!")
        print(f"🔧 Cenários que precisam de correção:")
        
        for resultado in resultados:
            if not resultado['sistema_correto']:
                print(f"   • {resultado['nome']}: Sistema={resultado['horas_negativas_sistema']}, Correto={resultado['formato_hhmm']}")
    
    print(f"\n💡 EXPLICAÇÃO DO CENÁRIO TERÇA-FEIRA 15/07/2025:")
    print(f"=" * 60)
    print(f"📋 Registros:")
    print(f"   B1 (Entrada): 10:05")
    print(f"   B2 (Saída Almoço): 12:42")
    print(f"   B3 (Retorno Almoço): 17:45")
    print(f"   B4 (Saída): 17:45")
    print(f"")
    print(f"🧮 Cálculo correto:")
    print(f"   Período manhã (B1-B2): 10:05 - 12:42 = 2h 37min = 2.62h")
    print(f"   Período tarde (B3-B4): 17:45 - 17:45 = 0h 00min = 0.00h")
    print(f"   Total trabalhado: 2.62h + 0.00h = 2.62h")
    print(f"   Jornada esperada: 9.00h")
    print(f"   Deficit: 9.00h - 2.62h = 6.38h")
    print(f"   Formato: -6:23 (6h 23min)")
    print(f"")
    print(f"🎯 O sistema mostra -6:22, que está muito próximo do cálculo manual (-6:23)")
    print(f"   A diferença de 1 minuto pode ser devido a arredondamentos.")

if __name__ == "__main__":
    main()
