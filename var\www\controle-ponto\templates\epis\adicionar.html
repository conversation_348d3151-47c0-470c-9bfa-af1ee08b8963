{% extends "base.html" %}

{% block title %}➕ Adicionar EPI - {{ funcionario.nome_completo }}{% endblock %}

{% block extra_css %}
<style>
.form-epi {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    padding: 40px;
    margin: 40px auto;
    max-width: 600px;
    position: relative;
    overflow: hidden;
}

.form-epi::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(135deg, #4fbdba, #40a8a5);
}

.form-floating > .form-control {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px 16px 8px 16px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fafbfc;
}

.form-floating > .form-control:focus {
    border-color: #4fbdba;
    box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    background: white;
}

.btn-submit-epi {
    background: linear-gradient(135deg, #4fbdba, #40a8a5);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    width: 100%;
    margin-top: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(79, 189, 186, 0.3);
}

.btn-submit-epi:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 189, 186, 0.4);
    color: white;
}

.funcionario-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 32px;
    border-radius: 20px;
    margin-bottom: 32px;
    text-align: center;
}

.required-indicator {
    color: #ef4444;
    font-weight: bold;
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ url_for('funcionarios.index') }}" style="color: #4fbdba;">👥 Funcionários</a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ url_for('funcionarios.detalhes', funcionario_id=funcionario_id) }}" style="color: #4fbdba;">
                    {{ funcionario.nome_completo }}
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ url_for('epis.listar_epis', funcionario_id=funcionario_id) }}" style="color: #4fbdba;">
                    📦 EPIs
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">➕ Adicionar</li>
        </ol>
    </nav>

    <!-- Header do Funcionário -->
    <div class="funcionario-header">
        <h2 class="h4 mb-2">👤 {{ funcionario.nome_completo }}</h2>
        <p class="mb-0 opacity-90">➕ Adicionar Novo EPI</p>
    </div>

    <!-- Formulário -->
    <div class="form-epi">
        <div class="text-center mb-4">
            <h3 class="h4 mb-2" style="color: #1e293b;">📦 Novo EPI</h3>
            <p class="text-muted">Preencha as informações do equipamento</p>
        </div>

        <form method="POST">
            <!-- Nome do EPI -->
            <div class="form-floating mb-3">
                <input type="text" 
                       class="form-control" 
                       id="epi_nome" 
                       name="epi_nome" 
                       placeholder="Nome do EPI"
                       required>
                <label for="epi_nome">
                    🦺 Nome do EPI <span class="required-indicator">*</span>
                </label>
            </div>

            <!-- C.A. -->
            <div class="form-floating mb-3">
                <input type="text" 
                       class="form-control" 
                       id="epi_ca" 
                       name="epi_ca" 
                       placeholder="Certificado de Aprovação">
                <label for="epi_ca">🏷️ Certificado de Aprovação (C.A.)</label>
            </div>

            <!-- Datas -->
            <div class="row">
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        <input type="date" 
                               class="form-control" 
                               id="epi_data_entrega" 
                               name="epi_data_entrega">
                        <label for="epi_data_entrega">📅 Data de Entrega</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        <input type="date" 
                               class="form-control" 
                               id="epi_data_validade" 
                               name="epi_data_validade">
                        <label for="epi_data_validade">⏰ Data de Validade</label>
                    </div>
                </div>
            </div>

            <!-- Observações -->
            <div class="form-floating mb-3">
                <textarea class="form-control" 
                          id="epi_observacoes" 
                          name="epi_observacoes" 
                          placeholder="Observações"
                          style="height: 120px;"></textarea>
                <label for="epi_observacoes">💭 Observações</label>
            </div>

            <!-- Botões -->
            <div class="d-flex gap-3">
                <a href="{{ url_for('epis.listar_epis', funcionario_id=funcionario_id) }}" 
                   class="btn btn-outline-secondary flex-fill">
                    ↩️ Cancelar
                </a>
                <button type="submit" class="btn-submit-epi flex-fill">
                    ✅ Adicionar EPI
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-definir data de hoje
document.addEventListener('DOMContentLoaded', function() {
    const hoje = new Date().toISOString().split('T')[0];
    document.getElementById('epi_data_entrega').value = hoje;
});
</script>
{% endblock %}
