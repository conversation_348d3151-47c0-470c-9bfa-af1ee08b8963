<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RLPONTO-WEB | Sistema Biométrico</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom Styles -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .fingerprint-scanner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            position: relative;
            overflow: hidden;
        }
        
        .pulse-ring {
            border: 3px solid rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                width: 0;
                height: 0;
                opacity: 1;
            }
            100% {
                width: 200px;
                height: 200px;
                opacity: 0;
            }
        }
        
        .scan-line {
            position: absolute;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00ff88, transparent);
            animation: scan 2s ease-in-out infinite;
        }
        
        @keyframes scan {
            0%, 100% { top: 20%; opacity: 0; }
            50% { top: 80%; opacity: 1; }
        }
        
        .status-indicator {
            transition: all 0.3s ease;
        }
        
        .usb-connected {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .usb-disconnected {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
    
    <!-- Main Container -->
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 mb-4 shadow-lg">
                    <i data-lucide="fingerprint" class="w-8 h-8 text-white"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-900 mb-2">Sistema Biométrico</h1>
                <p class="text-gray-600 text-sm">Registre seu ponto com segurança</p>
            </div>
            
            <!-- Status Card -->
            <div class="bg-white rounded-3xl shadow-xl p-8 mb-6 border border-gray-100">
                
                <!-- USB Status -->
                <div class="flex items-center justify-between mb-6 p-4 rounded-2xl bg-gray-50">
                    <div class="flex items-center gap-3">
                        <div id="usbStatus" class="w-3 h-3 rounded-full usb-disconnected"></div>
                        <span class="text-sm font-medium text-gray-700" id="usbStatusText">Leitor Desconectado</span>
                    </div>
                    <button id="connectReaderBtn" class="px-4 py-2 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 transition-colors">
                        <i data-lucide="usb" class="w-4 h-4 inline mr-1"></i>
                        Conectar
                    </button>
                </div>
                
                <!-- Fingerprint Scanner -->
                <div class="flex flex-col items-center mb-6">
                    <div class="relative">
                        <div class="fingerprint-scanner w-32 h-32 flex items-center justify-center mb-4 shadow-2xl">
                            <i data-lucide="fingerprint" class="w-16 h-16 text-white z-10 relative"></i>
                            <div class="pulse-ring" id="pulseRing"></div>
                            <div class="scan-line" id="scanLine" style="display: none;"></div>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <h3 class="text-lg font-semibold text-gray-900 mb-1" id="scannerTitle">Aguardando Leitura</h3>
                        <p class="text-sm text-gray-500" id="scannerDescription">Posicione seu dedo no leitor biométrico</p>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="space-y-3">
                    <button id="startScanBtn" disabled class="w-full py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i data-lucide="scan" class="w-5 h-5 inline mr-2"></i>
                        Iniciar Captura Biométrica
                    </button>
                    
                    <button id="emergencyBtn" class="w-full py-3 bg-gradient-to-r from-amber-500 to-orange-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200">
                        <i data-lucide="alert-triangle" class="w-5 h-5 inline mr-2"></i>
                        Registro de Emergência
                    </button>
                </div>
                
                <!-- Registration Type (Auto-Selected) -->
                <div class="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-100">
                    <div class="flex items-center gap-3">
                        <i data-lucide="clock" class="w-5 h-5 text-blue-600"></i>
                        <div>
                            <p class="text-sm font-medium text-blue-900">Tipo de Registro</p>
                            <p class="text-xs text-blue-600" id="registrationType">Será detectado automaticamente</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="grid grid-cols-2 gap-3">
                <button onclick="location.href='/funcionarios'" class="flex items-center justify-center gap-2 py-3 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 border border-gray-100">
                    <i data-lucide="users" class="w-4 h-4 text-gray-600"></i>
                    <span class="text-sm font-medium text-gray-700">Funcionários</span>
                </button>
                
                <button onclick="location.href='/relatorios'" class="flex items-center justify-center gap-2 py-3 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 border border-gray-100">
                    <i data-lucide="bar-chart-3" class="w-4 h-4 text-gray-600"></i>
                    <span class="text-sm font-medium text-gray-700">Relatórios</span>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-3xl p-8 m-4 max-w-sm w-full shadow-2xl">
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="check" class="w-8 h-8 text-green-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Ponto Registrado!</h3>
                <p class="text-gray-600 mb-6" id="successMessage">Seu ponto foi registrado com sucesso.</p>
                <button onclick="closeSuccessModal()" class="w-full py-3 bg-green-600 text-white font-semibold rounded-xl hover:bg-green-700 transition-colors">
                    Continuar
                </button>
            </div>
        </div>
    </div>
    
    <!-- Error Modal -->
    <div id="errorModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-3xl p-8 m-4 max-w-sm w-full shadow-2xl">
            <div class="text-center">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="x" class="w-8 h-8 text-red-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Erro no Registro</h3>
                <p class="text-gray-600 mb-6" id="errorMessage">Ocorreu um erro. Tente novamente.</p>
                <button onclick="closeErrorModal()" class="w-full py-3 bg-red-600 text-white font-semibold rounded-xl hover:bg-red-700 transition-colors">
                    Tentar Novamente
                </button>
            </div>
        </div>
    </div>

    <script src="/static/js/biometria-producao.js"></script>
    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Initialize biometric system
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof BiometricSystem !== 'undefined') {
                BiometricSystem.init();
            }
        });
        
        // USB Detection Mock (will be replaced by real implementation)
        function detectUSBDevices() {
            // This will be implemented with real USB detection
            console.log('Detectando dispositivos USB...');
            
            // Simulate USB detection for now
            setTimeout(() => {
                updateUSBStatus(true);
            }, 2000);
        }
        
        function updateUSBStatus(connected) {
            const statusIndicator = document.getElementById('usbStatus');
            const statusText = document.getElementById('usbStatusText');
            const startBtn = document.getElementById('startScanBtn');
            
            if (connected) {
                statusIndicator.className = 'w-3 h-3 rounded-full usb-connected';
                statusText.textContent = 'Leitor ZK4500 Conectado';
                startBtn.disabled = false;
            } else {
                statusIndicator.className = 'w-3 h-3 rounded-full usb-disconnected';
                statusText.textContent = 'Leitor Desconectado';
                startBtn.disabled = true;
            }
        }
        
        // Connect Reader Button
        document.getElementById('connectReaderBtn').addEventListener('click', function() {
            this.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 inline mr-1 animate-spin"></i>Conectando...';
            lucide.createIcons();
            
            // Simulate connection process
            setTimeout(() => {
                detectUSBDevices();
                this.innerHTML = '<i data-lucide="usb" class="w-4 h-4 inline mr-1"></i>Conectar';
                lucide.createIcons();
            }, 2000);
        });
        
        // Auto-detect registration type based on time
        function determineRegistrationType() {
            const now = new Date();
            const hour = now.getHours();
            const minute = now.getMinutes();
            const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            
            let registrationType = '';
            let status = '';
            
            // Morning entry (7:00-9:30)
            if (hour >= 7 && (hour < 9 || (hour === 9 && minute <= 30))) {
                registrationType = 'Entrada Matinal';
                status = hour > 8 ? ' (Atraso)' : ' (No Horário)';
            }
            // Lunch out (11:30-13:30)
            else if ((hour === 11 && minute >= 30) || hour === 12 || (hour === 13 && minute <= 30)) {
                registrationType = 'Saída para Almoço';
                status = ' (No Horário)';
            }
            // Lunch return (13:30-15:00)
            else if ((hour === 13 && minute >= 30) || hour === 14 || (hour === 15 && minute === 0)) {
                registrationType = 'Retorno do Almoço';
                status = hour >= 14 ? ' (Atraso)' : ' (No Horário)';
            }
            // Evening exit (17:00-19:00)
            else if (hour >= 17 && hour < 19) {
                registrationType = 'Saída';
                status = ' (No Horário)';
            }
            // Outside normal hours
            else {
                registrationType = 'Registro Fora do Horário';
                status = ' (Verificar com RH)';
            }
            
            document.getElementById('registrationType').textContent = registrationType + status + ` - ${timeStr}`;
            return registrationType;
        }
        
        // Update registration type every minute
        determineRegistrationType();
        setInterval(determineRegistrationType, 60000);
        
        // Modal functions
        function showSuccessModal(message) {
            document.getElementById('successMessage').textContent = message;
            document.getElementById('successModal').classList.remove('hidden');
            document.getElementById('successModal').classList.add('flex');
        }
        
        function closeSuccessModal() {
            document.getElementById('successModal').classList.add('hidden');
            document.getElementById('successModal').classList.remove('flex');
        }
        
        function showErrorModal(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorModal').classList.remove('hidden');
            document.getElementById('errorModal').classList.add('flex');
        }
        
        function closeErrorModal() {
            document.getElementById('errorModal').classList.add('hidden');
            document.getElementById('errorModal').classList.remove('flex');
        }
        
        // Emergency registration
        document.getElementById('emergencyBtn').addEventListener('click', function() {
            const confirmed = confirm('Deseja registrar um ponto de emergência? Esta ação será registrada no sistema.');
            if (confirmed) {
                // Implement emergency registration logic
                showSuccessModal('Registro de emergência efetuado. Procure o RH para regularização.');
            }
        });
        
        // Start scan animation
        function startScanAnimation() {
            document.getElementById('scanLine').style.display = 'block';
            document.getElementById('scannerTitle').textContent = 'Capturando...';
            document.getElementById('scannerDescription').textContent = 'Mantenha o dedo no leitor';
        }
        
        function stopScanAnimation() {
            document.getElementById('scanLine').style.display = 'none';
            document.getElementById('scannerTitle').textContent = 'Aguardando Leitura';
            document.getElementById('scannerDescription').textContent = 'Posicione seu dedo no leitor biométrico';
        }
    </script>
</body>
</html>