#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'var', 'www', 'controle-ponto'))

from flask import Flask, render_template
from utils.database import DatabaseManager
import traceback

def test_template_alocacoes():
    """Testar se o template alocacoes.html pode ser renderizado"""
    print("🔍 TESTANDO RENDERIZAÇÃO DO TEMPLATE ALOCACOES")
    print("=" * 50)
    
    try:
        # Criar app Flask temporário
        app = Flask(__name__, 
                   template_folder='var/www/controle-ponto/templates',
                   static_folder='var/www/controle-ponto/static')
        app.secret_key = 'test_key'
        
        with app.app_context():
            # Buscar dados reais
            db = DatabaseManager()
            
            # Empresa principal
            sql_empresa = "SELECT * FROM empresas WHERE empresa_principal = TRUE LIMIT 1"
            empresa_principal = db.execute_query(sql_empresa)[0]
            
            # Alocações
            sql_alocacoes = """
            SELECT fa.*,
                   f.nome_completo as nome, f.cargo, f.cpf, f.telefone1 as telefone,
                   e.razao_social, e.nome_fantasia, e.cnpj,
                   jt.nome_jornada as jornada_nome, NULL as carga_horaria,
                   ec.nome_contrato, ec.codigo_contrato
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            JOIN empresas e ON fa.empresa_cliente_id = e.id
            JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
            LEFT JOIN empresa_clientes ec ON fa.contrato_id = ec.id
            ORDER BY fa.created_at DESC
            """
            alocacoes = db.execute_query(sql_alocacoes)
            
            # Clientes
            sql_clientes = """
            SELECT ec.*, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email,
                   0 as funcionarios_alocados,
                   0 as jornadas_disponiveis
            FROM empresa_clientes ec
            INNER JOIN empresas e ON ec.empresa_cliente_id = e.id
            INNER JOIN empresas ep ON ec.empresa_principal_id = ep.id
            WHERE ep.empresa_principal = TRUE AND ec.ativo = TRUE
            ORDER BY ec.data_inicio DESC
            """
            clientes = db.execute_query(sql_clientes)
            
            # Stats
            stats = {
                'total_alocacoes': len(alocacoes) if alocacoes else 0,
                'alocacoes_ativas': len([a for a in alocacoes if a['ativo']]) if alocacoes else 0,
                'funcionarios_unicos': len(set([a['funcionario_id'] for a in alocacoes])) if alocacoes else 0
            }
            
            # Contexto
            context = {
                'titulo': 'Alocação de Funcionários',
                'empresa_principal': empresa_principal,
                'alocacoes': alocacoes,
                'clientes': clientes,
                'stats': stats
            }
            
            print(f"📊 Dados preparados:")
            print(f"   - Empresa principal: {empresa_principal['razao_social']}")
            print(f"   - Alocações: {len(alocacoes)}")
            print(f"   - Clientes: {len(clientes)}")
            print(f"   - Stats: {stats}")
            
            # Tentar renderizar o template
            print(f"\n🎨 Tentando renderizar template...")
            try:
                html = render_template('empresa_principal/alocacoes.html', **context)
                print(f"   ✅ Template renderizado com sucesso!")
                print(f"   📏 Tamanho do HTML: {len(html)} caracteres")
                
                # Verificar se há conteúdo esperado
                if 'Alocação de Funcionários' in html:
                    print(f"   ✅ Título encontrado no HTML")
                else:
                    print(f"   ❌ Título não encontrado no HTML")
                
                if empresa_principal['razao_social'] in html:
                    print(f"   ✅ Nome da empresa encontrado no HTML")
                else:
                    print(f"   ❌ Nome da empresa não encontrado no HTML")
                
                return True
                
            except Exception as e:
                print(f"   ❌ Erro ao renderizar template: {e}")
                print(f"   📋 Traceback: {traceback.format_exc()}")
                return False
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_template_alocacoes()
