-- Investigação do registro do TESTE 5 em 11/07/2025
-- Sistema: RLPONTO-WEB v1.0

-- 1. Verificar todos os registros do TESTE 5 no dia 11/07/2025
SELECT 
    rp.id,
    rp.funcionario_id,
    rp.data_hora,
    rp.tipo_registro,
    rp.metodo_registro,
    rp.status_pontualidade,
    rp.observacoes,
    f.nome_completo
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
WHERE f.nome_completo LIKE '%TESTE 5%'
AND DATE(rp.data_hora) = '2025-07-11'
ORDER BY rp.data_hora;

-- 2. Verificar horário de trabalho do TESTE 5
SELECT 
    f.nome_completo,
    f.horario_trabalho_id,
    ht.nome_horario,
    ht.entrada_manha,
    ht.said<PERSON>_<PERSON><PERSON><PERSON>,
    ht.entrada_tarde,
    ht.saida,
    ht.tolerancia_minutos
FROM funcionarios f
LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
WHERE f.nome_completo LIKE '%TESTE 5%';

-- 3. Verificar todos os registros do TESTE 5 nos últimos dias
SELECT 
    DATE(rp.data_hora) as data_registro,
    TIME(rp.data_hora) as hora_registro,
    rp.tipo_registro,
    rp.metodo_registro,
    rp.status_pontualidade
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
WHERE f.nome_completo LIKE '%TESTE 5%'
AND rp.data_hora >= '2025-07-09'
ORDER BY rp.data_hora;

-- 4. Verificar se há configurações especiais para este funcionário
SELECT 
    f.nome_completo,
    f.status_cadastro,
    f.setor,
    f.cargo,
    e.razao_social as empresa
FROM funcionarios f
LEFT JOIN empresas e ON f.empresa_id = e.id
WHERE f.nome_completo LIKE '%TESTE 5%';
