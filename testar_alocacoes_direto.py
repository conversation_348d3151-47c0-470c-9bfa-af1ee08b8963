#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste Direto da Página de Alocações
===================================

Script para testar diretamente a página de alocações.

Data: 07/07/2025
"""

import requests

# Configurações
BASE_URL = "http://************"
LOGIN_URL = f"{BASE_URL}/login"

def testar_rotas_empresa_principal():
    """Testa diferentes rotas da empresa principal"""
    print("🧪 TESTE DIRETO DAS ROTAS DE EMPRESA PRINCIPAL")
    print("=" * 60)
    
    # Criar sessão e fazer login
    session = requests.Session()
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    
    if response.status_code not in [200, 302]:
        print(f"❌ Falha no login: {response.status_code}")
        return
    
    print("✅ Login realizado com sucesso")
    
    # Testar diferentes URLs
    urls_para_testar = [
        "/empresa_principal",
        "/empresa_principal/",
        "/empresa_principal/index",
        "/empresa_principal/dashboard", 
        "/empresa_principal/alocacoes",
        "/empresa_principal/clientes"
    ]
    
    print(f"\n📋 TESTANDO ROTAS:")
    print("-" * 40)
    
    for url in urls_para_testar:
        try:
            full_url = BASE_URL + url
            response = session.get(full_url)
            
            status_icon = "✅" if response.status_code == 200 else "❌"
            print(f"   {status_icon} {url}: {response.status_code}")
            
            if response.status_code == 200:
                # Verificar se é a página correta
                content = response.text.lower()
                if "alocação" in content or "empresa principal" in content:
                    print(f"      📄 Conteúdo relevante encontrado")
                else:
                    print(f"      ⚠️ Conteúdo pode não ser o esperado")
            
        except Exception as e:
            print(f"   ❌ {url}: Erro - {e}")

if __name__ == "__main__":
    testar_rotas_empresa_principal()
