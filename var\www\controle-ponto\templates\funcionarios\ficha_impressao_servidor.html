<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ funcionario.nome_completo }}</title>
    <style>
        /* ===== RESET E BASE ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 10pt;
            line-height: 1.2;
            color: #333;
            background: #fff;
            padding: 15px;
        }

        /* ===== CABEÇALHO DA EMPRESA ===== */
        .empresa-header {
            text-align: center;
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .empresa-nome {
            font-size: 14pt;
            font-weight: 600;
            color: #333;
            margin-bottom: 3px;
        }

        .empresa-cnpj {
            font-size: 9pt;
            color: #666;
            margin-bottom: 3px;
        }



        .data-geracao {
            font-size: 8pt;
            color: #666;
            margin-top: 3px;
        }

        /* ===== SEÇÃO DO FUNCIONÁRIO ===== */
        .funcionario-section {
            margin-bottom: 15px;
        }

        .funcionario-header {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 12px;
            border-radius: 3px;
        }

        .funcionario-nome {
            font-size: 12pt;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .funcionario-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 9pt;
        }

        .funcionario-info .info-item {
            display: flex;
            justify-content: space-between;
        }

        .info-label {
            font-weight: 600;
            color: #555;
        }

        .info-value {
            color: #333;
        }

        /* ===== GRID DE INFORMAÇÕES ===== */
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 12px;
        }

        .info-section {
            border: 1px solid #ddd;
            border-radius: 3px;
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 6px 10px;
            border-bottom: 1px solid #ddd;
            font-weight: 600;
            font-size: 9pt;
            color: #333;
        }

        .section-content {
            padding: 8px;
        }

        .field-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 8pt;
        }

        .field-row:last-child {
            margin-bottom: 0;
        }

        .field-label {
            font-weight: 600;
            color: #555;
            width: 45%;
        }

        .field-value {
            color: #333;
            width: 53%;
            text-align: right;
        }

        /* ===== SEÇÃO DE EPIs ===== */
        .epis-section {
            margin-top: 12px;
        }

        .epis-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 7pt;
            margin-top: 5px;
        }

        .epis-table th,
        .epis-table td {
            border: 1px solid #ddd;
            padding: 3px 5px;
            text-align: left;
        }

        .epis-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .epis-table td {
            color: #333;
        }

        /* ===== RODAPÉ ===== */
        .footer {
            margin-top: 15px;
            padding-top: 8px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 7pt;
            color: #666;
        }

        /* ===== ESTILOS DE IMPRESSÃO ===== */
        @media print {
            @page {
                size: A4;
                margin: 0.5cm;
            }

            body {
                padding: 0;
                margin: 0;
                font-size: 8pt !important;
                line-height: 1.1 !important;
            }

            .empresa-header {
                padding-bottom: 5px;
                margin-bottom: 8px;
                border-bottom: 1px solid #333;
            }

            .empresa-nome {
                font-size: 12pt !important;
                margin-bottom: 2px;
            }

            .empresa-cnpj {
                font-size: 8pt !important;
                margin-bottom: 2px;
            }



            .data-geracao {
                font-size: 7pt !important;
                margin-top: 2px;
            }

            .funcionario-section {
                margin-bottom: 8px;
            }

            .funcionario-header {
                padding: 6px;
                margin-bottom: 8px;
            }

            .funcionario-nome {
                font-size: 10pt !important;
                margin-bottom: 3px;
            }

            .funcionario-info {
                gap: 5px;
                font-size: 7pt !important;
            }

            .info-grid {
                gap: 8px;
                margin-bottom: 8px;
            }

            .section-header {
                padding: 4px 6px;
                font-size: 8pt !important;
            }

            .section-content {
                padding: 5px;
            }

            .field-row {
                margin-bottom: 2px;
                font-size: 7pt !important;
            }

            .epis-section {
                margin-top: 8px;
            }

            .epis-table {
                font-size: 6pt !important;
                margin-top: 3px;
            }

            .epis-table th,
            .epis-table td {
                padding: 2px 3px;
            }

            .footer {
                margin-top: 10px;
                padding-top: 5px;
                font-size: 6pt !important;
            }

            /* Ocultar elementos desnecessários na impressão */
            .no-print {
                display: none !important;
            }

            /* Forçar quebra de página se necessário */
            .page-break {
                page-break-before: always;
            }
        }

        /* ===== BOTÕES DE AÇÃO (apenas tela) ===== */
        .action-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .btn-action {
            padding: 8px 15px;
            border: none;
            border-radius: 6px;
            font-size: 10pt;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-print {
            background: #4fbdba;
            color: white;
        }

        .btn-print:hover {
            background: #26a69a;
            transform: translateY(-1px);
        }

        .btn-back {
            background: #6c757d;
            color: white;
        }

        .btn-back:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        @media print {
            .action-buttons {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Botões de Ação (apenas na tela) -->
    <div class="action-buttons no-print">
        <button onclick="window.print()" class="btn-action btn-print">
            🖨️ Imprimir
        </button>
        <a href="{{ url_for('funcionarios.detalhes', funcionario_id=funcionario.id) }}" class="btn-action btn-back">
            ← Voltar
        </a>
    </div>

    <!-- Cabeçalho da Empresa -->
    <div class="empresa-header">
        <div class="empresa-nome">
            {{ empresa.nome_fantasia or empresa.razao_social or 'EMPRESA' }}
        </div>
        {% if empresa.cnpj %}
        <div class="empresa-cnpj">
            CNPJ: {{ empresa.cnpj }}
        </div>
        {% endif %}
        <div class="data-geracao">
            Gerado em: {{ data_geracao }}
        </div>
    </div>

    <!-- Informações Principais do Funcionário -->
    <div class="funcionario-section">
        <div class="funcionario-header">
            <div class="funcionario-nome">{{ funcionario.nome_completo }}</div>
            <div class="funcionario-info">
                <div class="info-item">
                    <span class="info-label">Matrícula:</span>
                    <span class="info-value">{{ funcionario.matricula_empresa }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Status:</span>
                    <span class="info-value">{{ funcionario.status_cadastro }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Cargo:</span>
                    <span class="info-value">{{ funcionario.cargo }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Setor:</span>
                    <span class="info-value">{{ funcionario.setor_obra }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Grid de Informações Detalhadas -->
    <div class="info-grid">
        <!-- Dados Pessoais -->
        <div class="info-section">
            <div class="section-header">Dados Pessoais</div>
            <div class="section-content">
                <div class="field-row">
                    <span class="field-label">CPF:</span>
                    <span class="field-value">{{ funcionario.cpf | format_cpf }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">RG:</span>
                    <span class="field-value">{{ funcionario.rg or 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Data Nascimento:</span>
                    <span class="field-value">{{ funcionario.data_nascimento | format_date }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Sexo:</span>
                    <span class="field-value">{{ funcionario.sexo }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Estado Civil:</span>
                    <span class="field-value">{{ funcionario.estado_civil }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Nacionalidade:</span>
                    <span class="field-value">{{ funcionario.nacionalidade }}</span>
                </div>
            </div>
        </div>

        <!-- Documentos -->
        <div class="info-section">
            <div class="section-header">Documentos</div>
            <div class="section-content">
                <div class="field-row">
                    <span class="field-label">CTPS Número:</span>
                    <span class="field-value">{{ funcionario.ctps_numero or 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">CTPS Série/UF:</span>
                    <span class="field-value">{{ funcionario.ctps_serie_uf or 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">PIS/PASEP:</span>
                    <span class="field-value">{{ funcionario.pis_pasep or 'Não informado' }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Mais informações em grid -->
    <div class="info-grid">
        <!-- Contato -->
        <div class="info-section">
            <div class="section-header">Contato</div>
            <div class="section-content">
                <div class="field-row">
                    <span class="field-label">Telefone 1:</span>
                    <span class="field-value">{{ funcionario.telefone1 | format_telefone }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Telefone 2:</span>
                    <span class="field-value">{{ funcionario.telefone2 | format_telefone or 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Email:</span>
                    <span class="field-value">{{ funcionario.email or 'Não informado' }}</span>
                </div>
            </div>
        </div>

        <!-- Dados Profissionais -->
        <div class="info-section">
            <div class="section-header">Dados Profissionais</div>
            <div class="section-content">
                <div class="field-row">
                    <span class="field-label">Data Admissão:</span>
                    <span class="field-value">{{ funcionario.data_admissao | format_date }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Tipo Contrato:</span>
                    <span class="field-value">{{ funcionario.tipo_contrato }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Nível Acesso:</span>
                    <span class="field-value">{{ funcionario.nivel_acesso }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Tolerância:</span>
                    <span class="field-value">{{ funcionario.tolerancia_ponto }} min</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Endereço e Dados Adicionais -->
    <div class="info-grid">
        <!-- Endereço -->
        <div class="info-section">
            <div class="section-header">Endereço</div>
            <div class="section-content">
                <div class="field-row">
                    <span class="field-label">Rua:</span>
                    <span class="field-value">{{ funcionario.endereco_rua or 'N/I' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Bairro:</span>
                    <span class="field-value">{{ funcionario.endereco_bairro or 'N/I' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Cidade:</span>
                    <span class="field-value">{{ funcionario.endereco_cidade or 'N/I' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">CEP:</span>
                    <span class="field-value">{{ funcionario.endereco_cep or 'N/I' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Estado:</span>
                    <span class="field-value">{{ funcionario.endereco_estado or 'N/I' }}</span>
                </div>
            </div>
        </div>

        <!-- Observações (espaço reservado) -->
        <div class="info-section">
            <div class="section-header">Observações</div>
            <div class="section-content">
                <div style="height: 60px; border: 1px dashed #ddd; padding: 5px; font-size: 7pt; color: #999;">
                    Espaço para anotações manuais
                </div>
            </div>
        </div>
    </div>

    <!-- EPIs (se houver) -->
    {% if funcionario.epis and funcionario.epis|length > 0 %}
    <div class="epis-section">
        <div class="section-header">Equipamentos de Proteção Individual (EPIs)</div>
        <table class="epis-table">
            <thead>
                <tr>
                    <th>Nome do EPI</th>
                    <th>CA</th>
                    <th>Data Entrega</th>
                    <th>Data Vencimento</th>
                </tr>
            </thead>
            <tbody>
                {% for epi in funcionario.epis %}
                <tr>
                    <td>{{ epi.epi_nome or 'Não informado' }}</td>
                    <td>{{ epi.epi_ca or 'N/A' }}</td>
                    <td>{{ epi.epi_data_entrega | format_date if epi.epi_data_entrega else 'N/A' }}</td>
                    <td>{{ epi.epi_data_vencimento | format_date if epi.epi_data_vencimento else 'N/A' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}

    <!-- Rodapé -->
    <div class="footer">
        <p>Sistema de Controle de Ponto - RLPONTO-WEB v1.0</p>
        <p>© 2025 AiNexus Tecnologia. Todos os direitos reservados.</p>
    </div>
</body>
</html>
