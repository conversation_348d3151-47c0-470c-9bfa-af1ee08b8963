<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Correção JavaScript Empresas</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .validation-feedback {
            margin-top: 5px;
            font-size: 14px;
            display: none;
        }
        .validation-feedback.valid {
            color: #28a745;
        }
        .validation-feedback.invalid {
            color: #dc3545;
        }
        .is-valid {
            border-color: #28a745;
        }
        .is-invalid {
            border-color: #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-button {
            background: #28a745;
        }
        .test-button:hover {
            background: #1e7e34;
        }
        .status {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste - Correção JavaScript Empresas</h1>
        <p><strong>Data:</strong> 03/07/2025 | <strong>Sistema:</strong> RLPONTO-WEB</p>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <form id="empresaForm">
            <div class="form-group">
                <label for="razao_social">Razão Social:</label>
                <input type="text" id="razao_social" name="razao_social" placeholder="Digite a razão social">
                <div id="razao_social-feedback" class="validation-feedback"></div>
            </div>
            
            <div class="form-group">
                <label for="cnpj">CNPJ:</label>
                <input type="text" id="cnpj" name="cnpj" placeholder="00.000.000/0000-00">
                <div id="cnpj-feedback" class="validation-feedback"></div>
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" placeholder="<EMAIL>">
                <div id="email-feedback" class="validation-feedback"></div>
            </div>
            
            <button type="button" onclick="testarValidacao()" class="test-button">🧪 Testar Validação</button>
            <button type="button" onclick="testarElementosNulos()">🔍 Testar Elementos Nulos</button>
            <button type="button" onclick="limparTudo()">🧹 Limpar Tudo</button>
        </form>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        // Funções corrigidas do RLPONTO-WEB
        function mostrarFeedback(campo, mensagem, tipo) {
            const feedback = document.getElementById(campo + '-feedback');
            const input = document.getElementById(campo);
            
            // Verificar se os elementos existem antes de manipular
            if (!feedback) {
                console.error(`Elemento de feedback não encontrado: ${campo}-feedback`);
                log(`❌ ERRO: Feedback não encontrado para ${campo}`);
                return;
            }
            
            if (!input) {
                console.error(`Elemento de input não encontrado: ${campo}`);
                log(`❌ ERRO: Input não encontrado para ${campo}`);
                return;
            }
            
            feedback.textContent = mensagem;
            feedback.className = `validation-feedback ${tipo}`;
            feedback.style.display = 'block';
            
            input.classList.remove('is-valid', 'is-invalid');
            input.classList.add(tipo === 'valid' ? 'is-valid' : 'is-invalid');
            
            log(`✅ Feedback aplicado: ${campo} - ${mensagem} (${tipo})`);
        }
        
        function limparFeedback(campo) {
            const feedback = document.getElementById(campo + '-feedback');
            const input = document.getElementById(campo);
            
            // Verificar se os elementos existem antes de manipular
            if (feedback) {
                feedback.style.display = 'none';
                log(`🧹 Feedback limpo: ${campo}`);
            } else {
                log(`⚠️ Feedback não encontrado para limpar: ${campo}`);
            }
            
            if (input) {
                input.classList.remove('is-valid', 'is-invalid');
                log(`🧹 Classes removidas: ${campo}`);
            } else {
                log(`⚠️ Input não encontrado para limpar: ${campo}`);
            }
        }
        
        function validarFormulario() {
            let valido = true;
            log('🔍 Iniciando validação do formulário...');
            
            // Validar razão social
            const razaoSocial = document.getElementById('razao_social').value.trim();
            if (!razaoSocial) {
                mostrarFeedback('razao_social', 'Razão social é obrigatória', 'invalid');
                valido = false;
            } else if (razaoSocial.length < 3) {
                mostrarFeedback('razao_social', 'Razão social deve ter pelo menos 3 caracteres', 'invalid');
                valido = false;
            } else {
                mostrarFeedback('razao_social', 'Razão social válida', 'valid');
                limparFeedback('razao_social');
            }
            
            // Validar CNPJ
            const cnpj = document.getElementById('cnpj').value.trim();
            if (!cnpj) {
                mostrarFeedback('cnpj', 'CNPJ é obrigatório', 'invalid');
                valido = false;
            } else if (cnpj.length < 14) {
                mostrarFeedback('cnpj', 'CNPJ incompleto', 'invalid');
                valido = false;
            } else {
                mostrarFeedback('cnpj', 'CNPJ válido', 'valid');
            }
            
            // Validar email
            const email = document.getElementById('email').value.trim();
            if (email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (emailRegex.test(email)) {
                    mostrarFeedback('email', 'Email válido', 'valid');
                } else {
                    mostrarFeedback('email', 'Email inválido', 'invalid');
                    valido = false;
                }
            }
            
            log(`📊 Resultado da validação: ${valido ? 'VÁLIDO' : 'INVÁLIDO'}`);
            return valido;
        }
        
        // Funções de teste
        function testarValidacao() {
            log('🧪 === TESTE DE VALIDAÇÃO ===');
            showStatus('Executando teste de validação...', 'success');
            
            try {
                const resultado = validarFormulario();
                showStatus(`Teste concluído! Formulário ${resultado ? 'válido' : 'inválido'}`, 'success');
            } catch (error) {
                log(`❌ ERRO no teste: ${error.message}`);
                showStatus(`Erro no teste: ${error.message}`, 'error');
            }
        }
        
        function testarElementosNulos() {
            log('🔍 === TESTE DE ELEMENTOS NULOS ===');
            showStatus('Testando elementos que não existem...', 'success');
            
            // Testar com elemento que não existe
            mostrarFeedback('campo_inexistente', 'Teste de elemento nulo', 'invalid');
            limparFeedback('campo_inexistente');
            
            showStatus('Teste de elementos nulos concluído', 'success');
        }
        
        function limparTudo() {
            log('🧹 === LIMPANDO TUDO ===');
            
            // Limpar campos
            document.getElementById('razao_social').value = '';
            document.getElementById('cnpj').value = '';
            document.getElementById('email').value = '';
            
            // Limpar feedbacks
            limparFeedback('razao_social');
            limparFeedback('cnpj');
            limparFeedback('email');
            
            showStatus('Formulário limpo', 'success');
        }
        
        function log(mensagem) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${mensagem}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function showStatus(mensagem, tipo) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = mensagem;
            statusDiv.className = `status ${tipo}`;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }
        
        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Sistema de teste carregado');
            log('📝 Instruções:');
            log('   1. Preencha os campos e clique em "Testar Validação"');
            log('   2. Teste com campos vazios para ver validações');
            log('   3. Use "Testar Elementos Nulos" para verificar proteções');
            showStatus('Sistema de teste pronto!', 'success');
        });
    </script>
</body>
</html>
