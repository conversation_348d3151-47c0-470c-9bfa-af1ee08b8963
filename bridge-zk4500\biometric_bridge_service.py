#!/usr/bin/env python3
"""
RLPONTO-WEB - Serviço Bridge Biométrico Local
Permite que o servidor remoto detecte dispositivos biométricos na máquina local Windows

Sistema: RLPONTO-WEB v1.0
Desenvolvido por: <PERSON>ues - AiNexus Tecnologia
Data: 12/06/2025

ARQUITETURA:
Windows Local (ZK4500) ←→ Bridge Service (porta 8080) ←→ Servidor Linux (************)
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import subprocess
import json
import re
import logging
import platform
from typing import Dict, List, Optional
import time

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# ✅ CONFIGURAÇÃO ROBUSTA DO CORS PARA BRIDGE SERVICE
# Configuração específica para permitir comunicação entre servidor remoto e bridge local
CORS(app, 
    resources={
        r"/api/*": {
            "origins": [
                "http://************",           # Servidor de produção
                "http://************:5000",     # Servidor Flask direto
                "http://localhost:3000",         # Frontend desenvolvimento
                "http://127.0.0.1:3000",        # Frontend desenvolvimento
                "http://localhost:5000",         # Flask local
                "http://127.0.0.1:5000"         # Flask local
            ],
            "methods": ["GET", "HEAD", "POST", "OPTIONS", "PUT", "PATCH", "DELETE"],
            "allow_headers": [
                "Content-Type", 
                "Authorization", 
                "X-Requested-With",
                "X-Bridge-Token",
                "X-Device-Request",
                "Accept",
                "Origin"
            ],
            "expose_headers": ["Content-Range", "X-Content-Range", "X-Bridge-Response"],
            "supports_credentials": False,  # Bridge não precisa de credenciais
            "max_age": 1800  # 30 minutos de cache para preflight
        }
    },
    vary_header=True,
    send_wildcard=False
)

logger.info("✅ CORS configurado no Bridge Service")
logger.info("🔗 Origens permitidas: Servidor produção (************) e desenvolvimento local")
logger.info("🛡️ Headers específicos para comunicação bridge configurados")

class LocalBiometricDetector:
    """
    Detector local de dispositivos biométricos no Windows
    """
    
    def __init__(self):
        self.known_vendors = {
            '1B55': 'ZKTeco Inc.',
            '138A': 'Validity Sensors',
            '27C6': 'Goodix Technology',
            '04F3': 'Elan Microelectronics'
        }
    
    def detect_windows_devices(self) -> List[Dict]:
        """
        Detecta dispositivos biométricos no Windows usando WMIC
        """
        devices = []
        start_time = time.time()
        
        logger.info(f"🚀 [BRIDGE] Iniciando detecção local Windows...")
        
        try:
            # COMANDO WMIC QUE FUNCIONA NO WINDOWS
            cmd = [
                'wmic', 'path', 'Win32_PnPEntity', 
                'where', "DeviceID like '%VID_1B55%' OR Name like '%ZK%' OR Name like '%fingerprint%' OR Name like '%biometric%'",
                'get', 'Name,DeviceID,Status,Manufacturer', '/format:csv'
            ]
            
            logger.info(f"🔍 [BRIDGE] Executando: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0 and result.stdout.strip():
                logger.info(f"✅ [BRIDGE] WMIC executado com sucesso")
                logger.info(f"📋 [BRIDGE] Output WMIC:\n{result.stdout}")
                
                lines = result.stdout.strip().split('\n')
                
                if len(lines) > 1:  # Primeira linha é header
                    for line in lines[1:]:
                        if line.strip():
                            parts = line.split(',')
                            if len(parts) >= 4:
                                device_id = parts[1].strip() if len(parts) > 1 else ''
                                manufacturer = parts[2].strip() if len(parts) > 2 else ''
                                name = parts[3].strip() if len(parts) > 3 else 'Unknown Device'
                                status = parts[4].strip() if len(parts) > 4 else 'Unknown'
                                
                                logger.info(f"🔍 [BRIDGE] Analisando: {name} | {device_id}")
                                
                                # Verificar se é dispositivo biométrico válido
                                if device_id and ('VID_1B55' in device_id.upper() or 
                                                'ZK' in name.upper() or 
                                                'fingerprint' in name.lower() or 
                                                'biometric' in name.lower()):
                                    
                                    # Extrair VID e PID
                                    vid_match = re.search(r'VID_([0-9A-F]{4})', device_id, re.IGNORECASE)
                                    pid_match = re.search(r'PID_([0-9A-F]{4})', device_id, re.IGNORECASE)
                                    
                                    vid = vid_match.group(1).upper() if vid_match else ''
                                    pid = pid_match.group(1).upper() if pid_match else ''
                                    
                                    logger.info(f"🎯 [BRIDGE] VID: {vid}, PID: {pid}")
                                    
                                    # Identificar ZK4500 especificamente
                                    if vid == '1B55' and pid == '0840':
                                        device_info = {
                                            'friendly_name': 'ZK4500 Fingerprint Reader',
                                            'instance_id': device_id,
                                            'status': status if status else 'OK',
                                            'class': 'Biometric',
                                            'manufacturer': 'ZKTeco Inc.',
                                            'device_type': 'fingerprint',
                                            'vendor_id': vid,
                                            'product_id': pid,
                                            'supported': True,
                                            'detection_method': 'WMIC_BRIDGE_LOCAL',
                                            'bridge_timestamp': time.time(),
                                            'local_machine': platform.node()
                                        }
                                        devices.append(device_info)
                                        logger.info(f"✅ [BRIDGE] ZK4500 DETECTADO: {device_id}")
                                    else:
                                        # Outros dispositivos biométricos
                                        device_info = {
                                            'friendly_name': name,
                                            'instance_id': device_id,
                                            'status': status if status else 'OK',
                                            'class': 'Biometric',
                                            'manufacturer': manufacturer if manufacturer else 'Unknown',
                                            'device_type': 'biometric',
                                            'vendor_id': vid,
                                            'product_id': pid,
                                            'supported': vid in self.known_vendors,
                                            'detection_method': 'WMIC_BRIDGE_LOCAL',
                                            'bridge_timestamp': time.time(),
                                            'local_machine': platform.node()
                                        }
                                        devices.append(device_info)
                                        logger.info(f"✅ [BRIDGE] Dispositivo detectado: {name}")
                
                execution_time = (time.time() - start_time) * 1000
                logger.info(f"🎯 [BRIDGE] TOTAL DETECTADO: {len(devices)} dispositivos em {execution_time:.2f}ms")
                return devices
            else:
                logger.warning(f"❌ [BRIDGE] WMIC não retornou dispositivos")
                logger.warning(f"❌ [BRIDGE] stderr: {result.stderr}")
                return []
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ [BRIDGE] Timeout na detecção via WMIC")
            return []
        except Exception as e:
            logger.error(f"❌ [BRIDGE] Erro na detecção: {str(e)}")
            return []

# Instanciar detector
detector = LocalBiometricDetector()

@app.route('/api/detect-biometric-devices', methods=['GET'])
def api_detect_devices():
    """
    API para detecção de dispositivos biométricos locais
    Usado pelo servidor remoto para obter dispositivos da máquina Windows
    """
    try:
        logger.info(f"🌐 [BRIDGE API] Requisição de detecção recebida")
        
        # Detectar dispositivos localmente
        devices = detector.detect_windows_devices()
        
        response_data = {
            'success': True,
            'dispositivos_encontrados': len(devices),
            'dispositivos': devices,
            'bridge_info': {
                'local_machine': platform.node(),
                'sistema_operacional': platform.system(),
                'versao_sistema': platform.version(),
                'timestamp': time.time(),
                'metodo_deteccao': 'WMIC_Windows_Bridge',
                'bridge_version': '1.0'
            },
            'message': f'{len(devices)} dispositivos biométricos detectados localmente' if devices else 'Nenhum dispositivo biométrico detectado na máquina local'
        }
        
        logger.info(f"✅ [BRIDGE API] Retornando {len(devices)} dispositivos")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"❌ [BRIDGE API] Erro: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'dispositivos_encontrados': 0,
            'dispositivos': [],
            'message': 'Erro interno do bridge biométrico'
        }), 500

@app.route('/api/bridge-status', methods=['GET'])
def api_bridge_status():
    """
    Status do serviço bridge
    """
    return jsonify({
        'status': 'online',
        'bridge_version': '1.0',
        'machine': platform.node(),
        'sistema': platform.system(),
        'timestamp': time.time(),
        'message': 'Serviço Bridge Biométrico operacional'
    })

@app.route('/api/test-device/<device_id>', methods=['POST'])
def api_test_device(device_id):
    """
    Testar comunicação com dispositivo específico
    """
    logger.info(f"🧪 [BRIDGE] Teste solicitado para dispositivo: {device_id}")
    
    # Simular teste de comunicação
    return jsonify({
        'success': True,
        'device_id': device_id,
        'test_result': 'Comunicação OK',
        'message': f'Dispositivo {device_id} respondeu corretamente'
    })

@app.route('/api/test-device-body', methods=['POST'])
def api_test_device_body():
    """
    Testar comunicação com dispositivo específico recebendo o ID no corpo da requisição
    Esta rota é compatível com o novo endpoint do servidor
    """
    try:
        data = request.json
        if not data or 'instance_id' not in data:
            logger.error("❌ [BRIDGE] Erro: ID do dispositivo não fornecido no corpo")
            return jsonify({
                'success': False,
                'error': 'ID do dispositivo não fornecido',
                'message': 'É necessário fornecer o ID do dispositivo no corpo da requisição'
            }), 400
        
        device_id = data['instance_id']
        logger.info(f"🧪 [BRIDGE] Teste solicitado para dispositivo (via corpo): {device_id}")
        
        # Verificar se o dispositivo existe
        devices = detector.detect_windows_devices()
        device_found = next((d for d in devices if d['instance_id'] == device_id), None)
        
        if device_found:
            logger.info(f"✅ [BRIDGE] Dispositivo encontrado: {device_found['friendly_name']}")
            return jsonify({
                'success': True,
                'device_id': device_id,
                'device_name': device_found['friendly_name'],
                'status': device_found['status'],
                'conectado': device_found['status'] == 'OK',
                'test_result': 'Comunicação OK' if device_found['status'] == 'OK' else 'Dispositivo não responde',
                'message': f'Dispositivo {device_found["friendly_name"]} testado com sucesso',
                'timestamp': time.time()
            })
        else:
            logger.warning(f"⚠️ [BRIDGE] Dispositivo não encontrado: {device_id}")
            return jsonify({
                'success': False,
                'device_id': device_id,
                'conectado': False,
                'error': 'Dispositivo não encontrado',
                'message': 'O dispositivo não está conectado ou não foi reconhecido pelo sistema'
            }), 404
            
    except Exception as e:
        logger.error(f"❌ [BRIDGE] Erro no teste do dispositivo: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'Erro interno no teste do dispositivo'
        }), 500

if __name__ == '__main__':
    print("🚀 RLPONTO-WEB - Serviço Bridge Biométrico Local")
    print("=" * 60)
    print(f"🖥️  Sistema: {platform.system()} {platform.version()}")
    print(f"💻 Máquina: {platform.node()}")
    print(f"🌐 URL: http://localhost:8080")
    print("=" * 60)
    print("✅ Serviço pronto para detectar dispositivos biométricos locais")
    print("🔗 Servidor remoto pode acessar via: http://[SEU_IP]:8080")
    print("=" * 60)
    
    # Iniciar serviço bridge na porta 8080
    app.run(
        host='0.0.0.0',  # Aceitar conexões de qualquer IP
        port=8080,
        debug=False,
        threaded=True
    ) 