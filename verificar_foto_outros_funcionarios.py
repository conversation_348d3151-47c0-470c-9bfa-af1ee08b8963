#!/usr/bin/env python3
"""
Verificar fotos de outros funcionários desligados
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager

def verificar_fotos_funcionarios():
    """Verificar fotos de funcionários desligados"""
    print("🔍 VERIFICAÇÃO: FOTOS DE FUNCIONÁRIOS DESLIGADOS")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar todos os funcionários desligados
        print("📋 1. VERIFICANDO FUNCIONÁRIOS DESLIGADOS:")
        funcionarios_desligados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa, foto_3x4
            FROM funcionarios_desligados 
            ORDER BY data_desligamento DESC
        """)
        
        if funcionarios_desligados:
            print(f"   ✅ {len(funcionarios_desligados)} funcionários desligados encontrados:")
            
            funcionarios_com_foto = []
            funcionarios_sem_foto = []
            
            for func in funcionarios_desligados:
                if func['foto_3x4']:
                    funcionarios_com_foto.append(func)
                    print(f"      ✅ {func['nome_completo']} (ID: {func['funcionario_id_original']}) - TEM FOTO")
                else:
                    funcionarios_sem_foto.append(func)
                    print(f"      ❌ {func['nome_completo']} (ID: {func['funcionario_id_original']}) - SEM FOTO")
            
            print(f"\n   📊 RESUMO:")
            print(f"      Com foto: {len(funcionarios_com_foto)}")
            print(f"      Sem foto: {len(funcionarios_sem_foto)}")
            
            # 2. Verificar funcionários ativos
            print(f"\n📋 2. VERIFICANDO FUNCIONÁRIOS ATIVOS:")
            funcionarios_ativos = db.execute_query("""
                SELECT id, nome_completo, matricula_empresa, foto_3x4
                FROM funcionarios 
                WHERE ativo = TRUE
                ORDER BY nome_completo
            """)
            
            if funcionarios_ativos:
                print(f"   ✅ {len(funcionarios_ativos)} funcionários ativos encontrados:")
                
                ativos_com_foto = []
                ativos_sem_foto = []
                
                for func in funcionarios_ativos:
                    if func['foto_3x4']:
                        ativos_com_foto.append(func)
                        print(f"      ✅ {func['nome_completo']} (ID: {func['id']}) - TEM FOTO")
                    else:
                        ativos_sem_foto.append(func)
                        print(f"      ❌ {func['nome_completo']} (ID: {func['id']}) - SEM FOTO")
                
                print(f"\n   📊 RESUMO ATIVOS:")
                print(f"      Com foto: {len(ativos_com_foto)}")
                print(f"      Sem foto: {len(ativos_sem_foto)}")
            
            # 3. Verificar se há funcionário com foto que pode ser restaurado para testar
            if funcionarios_com_foto:
                print(f"\n📋 3. TESTANDO RESTAURAÇÃO COM FUNCIONÁRIO QUE TEM FOTO:")
                
                funcionario_teste = funcionarios_com_foto[0]
                print(f"   Funcionário para teste: {funcionario_teste['nome_completo']}")
                print(f"   ID Original: {funcionario_teste['funcionario_id_original']}")
                print(f"   Tem foto: {'SIM' if funcionario_teste['foto_3x4'] else 'NÃO'}")
                
                # Testar restauração
                from utils.database import FuncionarioQueries
                
                resultado = FuncionarioQueries.restaurar_funcionario(funcionario_teste['funcionario_id_original'])
                
                print(f"\n   📊 RESULTADO DA RESTAURAÇÃO:")
                print(f"   Success: {resultado['success']}")
                print(f"   Message: {resultado['message']}")
                
                if resultado['success']:
                    # Verificar se a foto foi preservada
                    funcionario_restaurado = db.execute_query("""
                        SELECT id, nome_completo, foto_3x4
                        FROM funcionarios 
                        WHERE matricula_empresa = %s AND ativo = TRUE
                    """, (funcionario_teste['matricula_empresa'],))
                    
                    if funcionario_restaurado:
                        func_rest = funcionario_restaurado[0]
                        print(f"   ✅ Funcionário restaurado:")
                        print(f"      ID: {func_rest['id']}")
                        print(f"      Nome: {func_rest['nome_completo']}")
                        print(f"      Foto preservada: {'SIM' if func_rest['foto_3x4'] else 'NÃO'}")
                        
                        if func_rest['foto_3x4']:
                            print(f"   🎉 SUCESSO: Foto foi preservada durante a restauração!")
                            return True
                        else:
                            print(f"   ❌ PROBLEMA: Foto se perdeu durante a restauração!")
                            return False
                    else:
                        print(f"   ❌ Funcionário não encontrado após restauração")
                        return False
                else:
                    print(f"   ❌ Falha na restauração: {resultado['message']}")
                    return False
            else:
                print(f"\n📋 3. NENHUM FUNCIONÁRIO DESLIGADO TEM FOTO PARA TESTAR")
                return None
        else:
            print("   ❌ Nenhum funcionário desligado encontrado")
            return None
        
    except Exception as e:
        print(f"\n❌ ERRO NA VERIFICAÇÃO: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🎯 VERIFICAÇÃO COMPLETA: FOTOS DE FUNCIONÁRIOS")
    print("=" * 70)
    
    resultado = verificar_fotos_funcionarios()
    
    if resultado is True:
        print(f"\n🎉 SUCESSO!")
        print(f"✅ Função de restauração preserva fotos corretamente")
        print(f"✅ O problema do Kalebe foi específico (foto já estava perdida)")
    elif resultado is False:
        print(f"\n❌ PROBLEMA CONFIRMADO!")
        print(f"❌ Função de restauração não preserva fotos")
        print(f"❌ Necessário corrigir a função")
    else:
        print(f"\n⚠️ TESTE INCONCLUSIVO!")
        print(f"⚠️ Não há funcionários com foto para testar")
        print(f"💡 RECOMENDAÇÃO: Solicitar nova foto do Kalebe")
