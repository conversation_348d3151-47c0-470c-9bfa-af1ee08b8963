#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para verificar os valores de setor e setor_obra para o funcionário RICHARDSON
"""

import os
import sys
import logging
import pymysql
from datetime import datetime

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Dados de conexão com o banco de dados de produção
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

def verificar_campos_funcionario(nome_funcionario):
    """
    Verifica os valores de setor e setor_obra para um funcionário específico
    """
    try:
        # Conexão com o banco de dados
        logger.info(f"✓ Conectando ao servidor de produção {DB_CONFIG['host']}...")
        conn = pymysql.connect(**DB_CONFIG)
        
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # Consultar dados do funcionário
        cursor.execute("""
            SELECT 
                id, nome_completo, 
                setor, setor_obra,
                cargo, empresa
            FROM funcionarios 
            WHERE nome_completo LIKE %s
            AND ativo = TRUE
        """, (f"%{nome_funcionario}%",))
        
        funcionarios = cursor.fetchall()
        
        logger.info("\n==== Análise dos campos setor e setor_obra ====")
        logger.info("ID | Nome | Cargo | setor | setor_obra | empresa")
        
        for func in funcionarios:
            setor = func['setor'] or 'NULL'
            setor_obra = func['setor_obra'] or 'NULL'
            cargo = func['cargo'] or 'NULL'
            empresa = func['empresa'] or 'NULL'
            
            logger.info(f"{func['id']} | {func['nome_completo']} | {cargo} | {setor} | {setor_obra} | {empresa}")
        
        # Verificar também se existem registros na tabela registros_ponto para este funcionário
        if funcionarios:
            func_id = funcionarios[0]['id']
            cursor.execute("""
                SELECT 
                    rp.id, 
                    DATE_FORMAT(rp.data_hora, '%%d/%%m/%%Y') as data, 
                    TIME(rp.data_hora) as hora,
                    rp.tipo_registro,
                    f.nome_completo,
                    f.setor,
                    f.setor_obra
                FROM registros_ponto rp
                JOIN funcionarios f ON rp.funcionario_id = f.id
                WHERE rp.funcionario_id = %s
                ORDER BY rp.data_hora DESC
                LIMIT 5
            """, (func_id,))
            
            registros = cursor.fetchall()
            
            logger.info("\n==== Últimos 5 registros de ponto ====")
            logger.info("ID | Data | Hora | Tipo | Nome | setor | setor_obra")
            
            for reg in registros:
                setor = reg['setor'] or 'NULL'
                setor_obra = reg['setor_obra'] or 'NULL'
                
                logger.info(f"{reg['id']} | {reg['data']} | {reg['hora']} | {reg['tipo_registro']} | {reg['nome_completo']} | {setor} | {setor_obra}")
        
        # Verificar a view vw_relatorio_pontos
        if funcionarios:
            func_id = funcionarios[0]['id']
            cursor.execute("""
                SELECT 
                    id, 
                    DATE_FORMAT(data_hora, '%%d/%%m/%%Y') as data, 
                    hora_registro as hora,
                    tipo_registro,
                    nome_completo,
                    setor
                FROM vw_relatorio_pontos
                WHERE funcionario_id = %s
                ORDER BY data_hora DESC
                LIMIT 5
            """, (func_id,))
            
            registros_view = cursor.fetchall()
            
            logger.info("\n==== Últimos 5 registros na view vw_relatorio_pontos ====")
            logger.info("ID | Data | Hora | Tipo | Nome | setor (da view)")
            
            for reg in registros_view:
                setor_view = reg['setor'] or 'NULL'
                
                logger.info(f"{reg['id']} | {reg['data']} | {reg['hora']} | {reg['tipo_registro']} | {reg['nome_completo']} | {setor_view}")
        
        # Fechar a conexão
        cursor.close()
        conn.close()
        
        logger.info("✓ Verificação concluída com sucesso")
        return True
    except Exception as e:
        logger.error(f"❌ Erro ao verificar funcionário: {str(e)}")
        logger.error("Detalhes:", exc_info=True)
        return False

if __name__ == "__main__":
    logger.info("=== INÍCIO DA VERIFICAÇÃO DE CAMPOS DO FUNCIONÁRIO ===")
    logger.info(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    
    # Verificar os campos do funcionário RICHARDSON
    verificar_campos_funcionario("RICHARDSON")
    
    logger.info("=== FIM DA VERIFICAÇÃO ===") 