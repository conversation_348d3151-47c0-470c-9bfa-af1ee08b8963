#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug Detalhado - Sistema de Relatórios RLPONTO-WEB
Data: 06/06/2025
Objetivo: Identificar e resolver o erro 500 no botão de filtros
"""

import json
import traceback
from datetime import datetime, timedelta
from utils.database import get_db_connection
from utils.helpers import RegistroPontoValidator
import pymysql.cursors

def log_debug(message, level="INFO"):
    """Log com timestamp para debug."""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] [{level}] {message}")

def testar_conexao_banco():
    """Testa a conexão com o banco de dados."""
    log_debug("🔌 TESTE 1: Testando conexão com banco de dados...")
    try:
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        conn.close()
        log_debug("✅ Conexão com banco: OK", "SUCCESS")
        return True
    except Exception as e:
        log_debug(f"❌ Erro na conexão: {str(e)}", "ERROR")
        log_debug(f"❌ Traceback: {traceback.format_exc()}", "ERROR")
        return False

def testar_validador():
    """Testa a classe RegistroPontoValidator."""
    log_debug("🔧 TESTE 2: Testando classe RegistroPontoValidator...")
    try:
        validator = RegistroPontoValidator()
        
        # Teste com filtros válidos
        filtros_validos = {
            'data_inicio': '2025-06-01',
            'data_fim': '2025-06-06',
            'funcionario_id': 1,
            'pagina': 1,
            'registros_por_pagina': 50
        }
        
        resultado = validator.validar_filtros_relatorio(filtros_validos)
        log_debug(f"✅ Validador funcional - Filtros válidos: {resultado}", "SUCCESS")
        
        # Teste com filtros inválidos
        filtros_invalidos = {
            'data_inicio': '2025-06-06',
            'data_fim': '2025-06-01',  # Data final menor que inicial
            'funcionario_id': 'abc',   # ID inválido
        }
        
        validator2 = RegistroPontoValidator()
        resultado2 = validator2.validar_filtros_relatorio(filtros_invalidos)
        erros = validator2.get_errors()
        log_debug(f"✅ Validador capturou erros: {not resultado2}, Erros: {erros}", "SUCCESS")
        
        return True
    except Exception as e:
        log_debug(f"❌ Erro no validador: {str(e)}", "ERROR")
        log_debug(f"❌ Traceback: {traceback.format_exc()}", "ERROR")
        return False

def testar_query_buscar_registros():
    """Testa a query principal de busca de registros."""
    log_debug("📊 TESTE 3: Testando query de busca de registros...")
    try:
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # Query simplificada baseada na função original
        query = """
            SELECT 
                rp.id,
                rp.funcionario_id,
                f.nome_completo,
                f.setor,
                rp.data_hora,
                rp.tipo_registro,
                rp.metodo_registro,
                CASE 
                    WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atraso'
                    WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atraso'
                    ELSE 'Pontual'
                END AS status_pontualidade
            FROM registros_ponto rp
            INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            WHERE f.ativo = 1
            AND DATE(rp.data_hora) >= %s
            AND DATE(rp.data_hora) <= %s
            ORDER BY rp.data_hora DESC 
            LIMIT 10
        """
        
        # Usar datas do último mês
        data_fim = datetime.now().date()
        data_inicio = data_fim - timedelta(days=30)
        
        log_debug(f"📅 Período de teste: {data_inicio} até {data_fim}")
        
        cursor.execute(query, (data_inicio, data_fim))
        registros = cursor.fetchall()
        
        log_debug(f"✅ Query executada com sucesso - {len(registros)} registros encontrados", "SUCCESS")
        
        # Mostrar amostra dos dados
        if registros:
            primeiro_registro = registros[0]
            log_debug(f"📝 Exemplo de registro: {dict(primeiro_registro)}", "INFO")
        
        conn.close()
        return True, len(registros)
        
    except Exception as e:
        log_debug(f"❌ Erro na query: {str(e)}", "ERROR")
        log_debug(f"❌ Traceback: {traceback.format_exc()}", "ERROR")
        return False, 0

def testar_query_estatisticas():
    """Testa a query de estatísticas."""
    log_debug("📈 TESTE 4: Testando query de estatísticas...")
    try:
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # Query de estatísticas
        query = """
            SELECT 
                COUNT(*) as total_registros,
                COUNT(DISTINCT funcionario_id) as funcionarios_distintos,
                SUM(CASE WHEN metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
                SUM(CASE WHEN metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais
            FROM registros_ponto
            WHERE DATE(data_hora) BETWEEN %s AND %s
        """
        
        data_fim = datetime.now().date()
        data_inicio = data_fim - timedelta(days=30)
        
        cursor.execute(query, (data_inicio, data_fim))
        estatisticas = cursor.fetchone()
        
        log_debug(f"✅ Estatísticas calculadas: {dict(estatisticas)}", "SUCCESS")
        
        conn.close()
        return True
        
    except Exception as e:
        log_debug(f"❌ Erro nas estatísticas: {str(e)}", "ERROR")
        log_debug(f"❌ Traceback: {traceback.format_exc()}", "ERROR")
        return False

def simular_requisicao_completa():
    """Simula uma requisição completa como a do frontend."""
    log_debug("🎯 TESTE 5: Simulando requisição completa...")
    try:
        # Dados simulados do frontend
        filtros = {
            'funcionario_id': None,
            'setor': None,
            'tipo_registro': None,
            'metodo_registro': None,
            'data_inicio': '2025-06-01',
            'data_fim': '2025-06-06',
            'pagina': 1,
            'registros_por_pagina': 50
        }
        
        log_debug(f"📤 Filtros simulados: {filtros}")
        
        # Passo 1: Validar filtros
        log_debug("🔍 Passo 1: Validando filtros...")
        validator = RegistroPontoValidator()
        if not validator.validar_filtros_relatorio(filtros):
            erros = validator.get_errors()
            log_debug(f"❌ Filtros inválidos: {erros}", "ERROR")
            return False
        log_debug("✅ Filtros válidos")
        
        # Passo 2: Construir query
        log_debug("🔨 Passo 2: Construindo query...")
        query_base = """
            SELECT 
                rp.id,
                rp.funcionario_id,
                f.nome_completo,
                f.matricula_empresa,
                f.cpf,
                f.setor,
                f.cargo,
                f.empresa,
                rp.data_hora,
                DATE(rp.data_hora) as data_registro,
                TIME(rp.data_hora) as hora_registro,
                rp.tipo_registro,
                rp.metodo_registro,
                rp.qualidade_biometria,
                rp.observacoes,
                rp.ip_origem,
                rp.criado_em,
                u.usuario as criado_por_usuario,
                CASE 
                    WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atraso'
                    WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atraso'
                    ELSE 'Pontual'
                END AS status_pontualidade
            FROM registros_ponto rp
            INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            LEFT JOIN usuarios u ON rp.criado_por = u.id
            WHERE f.ativo = 1
        """
        
        # Adicionar filtros
        condicoes = []
        parametros = []
        
        if filtros.get('data_inicio'):
            condicoes.append("DATE(rp.data_hora) >= %s")
            parametros.append(filtros['data_inicio'])
        
        if filtros.get('data_fim'):
            condicoes.append("DATE(rp.data_hora) <= %s")
            parametros.append(filtros['data_fim'])
        
        if condicoes:
            query_base += " AND " + " AND ".join(condicoes)
        
        log_debug(f"📝 Query construída com {len(condicoes)} condições")
        
        # Passo 3: Executar query de contagem
        log_debug("🔢 Passo 3: Contando registros...")
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        query_count = f"SELECT COUNT(*) as total FROM ({query_base}) as subquery"
        cursor.execute(query_count, parametros)
        total_registros = cursor.fetchone()['total']
        log_debug(f"✅ Total de registros: {total_registros}")
        
        # Passo 4: Executar query principal
        log_debug("📋 Passo 4: Buscando registros...")
        page = int(filtros.get('pagina', 1))
        per_page = min(int(filtros.get('registros_por_pagina', 50)), 100)
        offset = (page - 1) * per_page
        
        query_final = f"{query_base} ORDER BY rp.data_hora DESC LIMIT %s OFFSET %s"
        parametros_final = parametros + [per_page, offset]
        
        cursor.execute(query_final, parametros_final)
        registros = cursor.fetchall()
        log_debug(f"✅ Registros buscados: {len(registros)}")
        
        # Passo 5: Buscar estatísticas
        log_debug("📊 Passo 5: Buscando estatísticas...")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_registros,
                COUNT(DISTINCT funcionario_id) as funcionarios_distintos,
                SUM(CASE WHEN metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
                SUM(CASE WHEN metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais
            FROM registros_ponto
            WHERE DATE(data_hora) BETWEEN %s AND %s
        """, (filtros.get('data_inicio'), filtros.get('data_fim')))
        
        estatisticas = cursor.fetchone()
        log_debug(f"✅ Estatísticas: {dict(estatisticas)}")
        
        # Passo 6: Montar resposta
        log_debug("📦 Passo 6: Montando resposta...")
        total_pages = (total_registros + per_page - 1) // per_page
        
        response_data = {
            'success': True,
            'registros': [dict(r) for r in registros],
            'total_registros': total_registros,
            'total_paginas': total_pages,
            'pagina_atual': page,
            'estatisticas': dict(estatisticas)
        }
        
        log_debug(f"✅ Resposta montada - {len(response_data['registros'])} registros", "SUCCESS")
        
        conn.close()
        return True
        
    except Exception as e:
        log_debug(f"❌ Erro na simulação completa: {str(e)}", "ERROR")
        log_debug(f"❌ Traceback: {traceback.format_exc()}", "ERROR")
        return False

def verificar_imports():
    """Verifica se todos os imports necessários estão funcionando."""
    log_debug("📚 TESTE 6: Verificando imports...")
    try:
        # Testar imports específicos
        from app_relatorios import relatorios_bp
        log_debug("✅ Import app_relatorios: OK")
        
        from utils.helpers import mascarar_dados_relatorio, processar_registro_para_json
        log_debug("✅ Import utils.helpers: OK")
        
        from utils.database import get_db_connection
        log_debug("✅ Import utils.database: OK")
        
        import pymysql.cursors
        log_debug("✅ Import pymysql.cursors: OK")
        
        return True
        
    except Exception as e:
        log_debug(f"❌ Erro nos imports: {str(e)}", "ERROR")
        log_debug(f"❌ Traceback: {traceback.format_exc()}", "ERROR")
        return False

def main():
    """Função principal do debug."""
    print("=" * 80)
    print("🐛 DEBUG DETALHADO - SISTEMA DE RELATÓRIOS RLPONTO-WEB")
    print(f"⏰ Iniciado em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 80)
    
    # Executar todos os testes
    testes = [
        ("Conexão com Banco", testar_conexao_banco),
        ("Validador de Filtros", testar_validador),
        ("Query de Busca", lambda: testar_query_buscar_registros()[0]),
        ("Query de Estatísticas", testar_query_estatisticas),
        ("Requisição Completa", simular_requisicao_completa),
        ("Verificação de Imports", verificar_imports)
    ]
    
    resultados = []
    
    for nome, teste in testes:
        log_debug(f"\n{'='*50}")
        log_debug(f"🧪 Executando: {nome}")
        log_debug(f"{'='*50}")
        
        try:
            resultado = teste()
            resultados.append((nome, resultado))
            
            if resultado:
                log_debug(f"✅ {nome}: PASSOU", "SUCCESS")
            else:
                log_debug(f"❌ {nome}: FALHOU", "ERROR")
                
        except Exception as e:
            log_debug(f"💥 {nome}: ERRO CRÍTICO - {str(e)}", "CRITICAL")
            resultados.append((nome, False))
    
    # Resumo final
    print("\n" + "=" * 80)
    print("📋 RESUMO DOS TESTES")
    print("=" * 80)
    
    passou = 0
    total = len(resultados)
    
    for nome, resultado in resultados:
        status = "✅ PASSOU" if resultado else "❌ FALHOU"
        print(f"{nome:.<40} {status}")
        if resultado:
            passou += 1
    
    print(f"\n🎯 RESULTADO FINAL: {passou}/{total} testes passaram ({passou/total*100:.1f}%)")
    
    if passou == total:
        print("🎉 Todos os testes passaram! O problema pode estar na integração Flask.")
        print("💡 Recomendação: Verificar logs do servidor Flask e configurações de rota.")
    else:
        print("⚠️ Alguns testes falharam. Veja os detalhes acima para identificar o problema.")
    
    print(f"\n⏰ Debug concluído em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 80)

if __name__ == "__main__":
    main() 