#!/usr/bin/env python3
# Verificação das configurações biométricas

import os
from pathlib import Path

def check_biometric_config():
    print("🔍 Verificando configurações biométricas...")
    
    # Verificar template
    template_file = Path("templates/configuracoes/index.html")
    if template_file.exists():
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'fas fa-fingerprint' in content and 'Biometria' in content:
                print("✅ Template de configurações: OK (botão biometria presente)")
            else:
                print("❌ Template de configurações: ERRO (botão biometria ausente)")
    else:
        print("❌ Template de configurações não encontrado")
    
    # Verificar blueprint biométrico
    biometric_file = Path("app_biometric_config.py")
    if biometric_file.exists():
        print("✅ Blueprint biométrico: OK")
    else:
        print("❌ Blueprint biométrico: ERRO (arquivo não encontrado)")
    
    # Verificar template biométrico
    biometric_template = Path("templates/configuracoes/biometria.html")
    if biometric_template.exists():
        print("✅ Template biométrico: OK")
    else:
        print("❌ Template biométrico: ERRO (arquivo não encontrado)")
    
    print("\n🏁 Verificação concluída!")

if __name__ == "__main__":
    check_biometric_config()
