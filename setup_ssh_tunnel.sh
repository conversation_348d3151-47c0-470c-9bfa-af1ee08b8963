#!/bin/bash
# Script para configurar túnel SSH - Bridge via WireGuard VPN
# RLPONTO-WEB Bridge ZK4500

echo "🔧 CONFIGURANDO TÚNEL SSH PARA BRIDGE BIOMÉTRICO"
echo "=============================================="

# Configurações
VPN_IP="***********"      # IP da máquina Windows na VPN
BRIDGE_PORT="8080"        # Porta do bridge
LOCAL_PORT="8080"         # Porta local no servidor
SSH_USER="richardson"     # Usuário SSH (se disponível)

echo "📋 Configurações:"
echo "   VPN IP: $VPN_IP"
echo "   Bridge Port: $BRIDGE_PORT" 
echo "   Local Port: $LOCAL_PORT"
echo ""

# Verificar se bridge está acessível via VPN
echo "🔍 Testando conectividade com bridge..."
if timeout 5 bash -c "</dev/tcp/$VPN_IP/$BRIDGE_PORT"; then
    echo "✅ Bridge acessível via VPN!"
    
    # Criar túnel SSH reverso (se SSH server disponível)
    echo "🔗 Tentando criar túnel SSH..."
    # Nota: Requer SSH server na máquina Windows
    # ssh -N -L $LOCAL_PORT:localhost:$BRIDGE_PORT $SSH_USER@$VPN_IP &
    
else
    echo "❌ Bridge não acessível diretamente via VPN"
fi

echo ""
echo "📋 OPÇÕES DISPONÍVEIS:"
echo "1. Configurar SSH server na máquina Windows"
echo "2. Usar ngrok para expor bridge publicamente"
echo "3. Configurar roteamento no gateway WireGuard"
echo "4. Manter configuração manual no sistema (atual)"

echo ""
echo "✅ SISTEMA ATUAL: Usando configuração manual ZK4500"
echo "   Status: Sistema funcionando com fallback manual" 