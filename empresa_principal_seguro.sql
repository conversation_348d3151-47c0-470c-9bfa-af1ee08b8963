-- ================================================================
-- IMPLEMENTAÇÃO SEGURA: EMPRESA PRINCIPAL E GESTÃO DE CLIENTES
-- Sistema: RLPONTO-WEB
-- Data: 03/07/2025
-- Versão: Segura com verificações
-- ================================================================

USE controle_ponto;

-- ================================================================
-- 1. VERIFICAR E ADICIONAR COLUNAS NA TABELA EMPRESAS
-- ================================================================

SELECT 'Verificando estrutura da tabela empresas...' as status;

-- Adicionar empresa_principal se não existir
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = 'controle_ponto' 
                   AND TABLE_NAME = 'empresas' 
                   AND COLUMN_NAME = 'empresa_principal');

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE empresas ADD COLUMN empresa_principal BOOLEAN DEFAULT FALSE COMMENT "Define se é a empresa principal/proprietária do sistema"',
    'SELECT "Coluna empresa_principal já existe" as info');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Adicionar empresa_matriz_id se não existir
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = 'controle_ponto' 
                   AND TABLE_NAME = 'empresas' 
                   AND COLUMN_NAME = 'empresa_matriz_id');

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE empresas ADD COLUMN empresa_matriz_id INT NULL COMMENT "ID da empresa matriz (para filiais)"',
    'SELECT "Coluna empresa_matriz_id já existe" as info');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Adicionar tipo_empresa se não existir
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = 'controle_ponto' 
                   AND TABLE_NAME = 'empresas' 
                   AND COLUMN_NAME = 'tipo_empresa');

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE empresas ADD COLUMN tipo_empresa ENUM("principal", "cliente", "filial", "independente") DEFAULT "independente" COMMENT "Tipo da empresa no sistema"',
    'SELECT "Coluna tipo_empresa já existe" as info');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ================================================================
-- 2. ADICIONAR ÍNDICES SE NÃO EXISTIREM
-- ================================================================

SELECT 'Verificando índices...' as status;

-- Índice empresa_principal
SET @idx_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
                   WHERE TABLE_SCHEMA = 'controle_ponto' 
                   AND TABLE_NAME = 'empresas' 
                   AND INDEX_NAME = 'idx_empresa_principal');

SET @sql = IF(@idx_exists = 0, 
    'ALTER TABLE empresas ADD INDEX idx_empresa_principal (empresa_principal)',
    'SELECT "Índice idx_empresa_principal já existe" as info');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Índice empresa_matriz
SET @idx_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
                   WHERE TABLE_SCHEMA = 'controle_ponto' 
                   AND TABLE_NAME = 'empresas' 
                   AND INDEX_NAME = 'idx_empresa_matriz');

SET @sql = IF(@idx_exists = 0, 
    'ALTER TABLE empresas ADD INDEX idx_empresa_matriz (empresa_matriz_id)',
    'SELECT "Índice idx_empresa_matriz já existe" as info');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ================================================================
-- 3. CRIAR TABELA EMPRESA_CLIENTES SE NÃO EXISTIR
-- ================================================================

SELECT 'Criando tabela empresa_clientes...' as status;

CREATE TABLE IF NOT EXISTS empresa_clientes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_principal_id INT NOT NULL COMMENT 'ID da empresa principal',
    empresa_cliente_id INT NOT NULL COMMENT 'ID da empresa que atua como cliente',
    nome_contrato VARCHAR(200) NULL COMMENT 'Nome do contrato/projeto',
    codigo_contrato VARCHAR(50) NULL COMMENT 'Código único do contrato',
    data_inicio DATE NOT NULL COMMENT 'Data de início do contrato',
    data_fim DATE NULL COMMENT 'Data prevista de fim do contrato',
    valor_contrato DECIMAL(15,2) NULL COMMENT 'Valor total do contrato',
    status_contrato ENUM('ativo', 'pausado', 'finalizado', 'cancelado') DEFAULT 'ativo',
    ativo BOOLEAN DEFAULT TRUE,
    observacoes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_cliente_principal (empresa_principal_id, empresa_cliente_id),
    INDEX idx_empresa_principal (empresa_principal_id),
    INDEX idx_empresa_cliente (empresa_cliente_id),
    INDEX idx_status_contrato (status_contrato),
    INDEX idx_ativo (ativo)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ================================================================
-- 4. CRIAR TABELA FUNCIONARIO_ALOCACOES SE NÃO EXISTIR
-- ================================================================

SELECT 'Criando tabela funcionario_alocacoes...' as status;

CREATE TABLE IF NOT EXISTS funcionario_alocacoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT UNSIGNED NOT NULL COMMENT 'ID do funcionário alocado',
    empresa_cliente_id INT NOT NULL COMMENT 'ID da empresa cliente',
    contrato_id INT NULL COMMENT 'ID do contrato específico',
    jornada_trabalho_id INT NOT NULL COMMENT 'ID da jornada de trabalho herdada',
    cargo_no_cliente VARCHAR(100) NULL COMMENT 'Cargo específico no cliente',
    data_inicio DATE NOT NULL COMMENT 'Data de início da alocação',
    data_fim DATE NULL COMMENT 'Data de fim da alocação',
    percentual_alocacao DECIMAL(5,2) DEFAULT 100.00 COMMENT 'Percentual de tempo alocado',
    ativo BOOLEAN DEFAULT TRUE,
    observacoes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_funcionario (funcionario_id),
    INDEX idx_empresa_cliente (empresa_cliente_id),
    INDEX idx_contrato (contrato_id),
    INDEX idx_jornada (jornada_trabalho_id),
    INDEX idx_ativo (ativo)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ================================================================
-- 5. ADICIONAR FOREIGN KEYS SE NÃO EXISTIREM
-- ================================================================

SELECT 'Verificando foreign keys...' as status;

-- FK empresa_clientes -> empresas (principal)
SET @fk_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                  WHERE TABLE_SCHEMA = 'controle_ponto' 
                  AND TABLE_NAME = 'empresa_clientes' 
                  AND CONSTRAINT_NAME = 'fk_empresa_principal');

SET @sql = IF(@fk_exists = 0, 
    'ALTER TABLE empresa_clientes ADD CONSTRAINT fk_empresa_principal FOREIGN KEY (empresa_principal_id) REFERENCES empresas(id) ON DELETE CASCADE',
    'SELECT "FK fk_empresa_principal já existe" as info');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- FK empresa_clientes -> empresas (cliente)
SET @fk_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                  WHERE TABLE_SCHEMA = 'controle_ponto' 
                  AND TABLE_NAME = 'empresa_clientes' 
                  AND CONSTRAINT_NAME = 'fk_empresa_cliente');

SET @sql = IF(@fk_exists = 0, 
    'ALTER TABLE empresa_clientes ADD CONSTRAINT fk_empresa_cliente FOREIGN KEY (empresa_cliente_id) REFERENCES empresas(id) ON DELETE CASCADE',
    'SELECT "FK fk_empresa_cliente já existe" as info');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ================================================================
-- 6. VERIFICAÇÃO FINAL
-- ================================================================

SELECT 'Verificando estrutura final...' as status;

SELECT COUNT(*) as colunas_empresas 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME = 'empresas' 
AND COLUMN_NAME IN ('empresa_principal', 'empresa_matriz_id', 'tipo_empresa');

SELECT COUNT(*) as tabelas_criadas 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'controle_ponto' 
AND TABLE_NAME IN ('empresa_clientes', 'funcionario_alocacoes');

SELECT 'Estrutura de Empresa Principal configurada com sucesso!' as resultado;
