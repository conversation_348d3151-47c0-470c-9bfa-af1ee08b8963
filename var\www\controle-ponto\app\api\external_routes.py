"""
Rotas para Gerenciamento de APIs Externas e MCP Servers
Interface web para configurar e monitorar conexões externas
"""
from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from ..services.external_api_service import external_api_service, mcp_service
from ..config.api_config import api_manager, APIConfig, MCPServerConfig
import asyncio
from datetime import datetime

external_bp = Blueprint('external', __name__, url_prefix='/external')

@external_bp.route('/')
def dashboard():
    """Dashboard principal de APIs externas e MCP servers"""
    apis = api_manager.list_apis(enabled_only=False)
    mcp_servers = api_manager.list_mcp_servers(enabled_only=False)
    active_connections = mcp_service.list_active_connections()
    
    return render_template('external/dashboard.html', 
                         apis=apis, 
                         mcp_servers=mcp_servers,
                         active_connections=active_connections,
                         datetime=datetime)

@external_bp.route('/apis')
def list_apis():
    """Lista todas as APIs configuradas"""
    apis = api_manager.list_apis(enabled_only=False)
    return render_template('external/apis.html', apis=apis)

@external_bp.route('/apis/add', methods=['GET', 'POST'])
def add_api():
    """Adiciona nova API externa"""
    if request.method == 'POST':
        try:
            name = request.form.get('name', '').strip()
            base_url = request.form.get('base_url', '').strip()
            api_key = request.form.get('api_key', '').strip()
            timeout = int(request.form.get('timeout', 30))
            
            # Headers customizados (JSON)
            headers_json = request.form.get('headers', '{}').strip()
            if headers_json:
                import json
                headers = json.loads(headers_json)
            else:
                headers = {"Content-Type": "application/json"}
            
            if not all([name, base_url, api_key]):
                flash('Nome, URL base e chave API são obrigatórios', 'error')
                return render_template('external/add_api.html')
            
            success = api_manager.add_api(name, base_url, api_key, headers, timeout)
            
            if success:
                flash(f'API {name} adicionada com sucesso!', 'success')
                return redirect(url_for('external.list_apis'))
            else:
                flash('Erro ao adicionar API', 'error')
                
        except Exception as e:
            flash(f'Erro: {str(e)}', 'error')
    
    return render_template('external/add_api.html')

@external_bp.route('/apis/<name>/toggle', methods=['POST'])
def toggle_api(name):
    """Alterna status de uma API"""
    success = api_manager.toggle_api(name)
    if success:
        api = api_manager.get_api(name)
        status = "ativada" if api.enabled else "desativada"
        flash(f'API {name} {status}', 'success')
    else:
        flash(f'Erro ao alterar status da API {name}', 'error')
    
    return redirect(url_for('external.list_apis'))

@external_bp.route('/apis/<name>/remove', methods=['POST'])
def remove_api(name):
    """Remove uma API"""
    success = api_manager.remove_api(name)
    if success:
        flash(f'API {name} removida', 'success')
    else:
        flash(f'Erro ao remover API {name}', 'error')
    
    return redirect(url_for('external.list_apis'))

@external_bp.route('/apis/<name>/test', methods=['POST'])
def test_api(name):
    """Testa conectividade com uma API"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            external_api_service.call_api(name, 'health', 'GET')
        )
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erro no teste: {str(e)}'
        })

@external_bp.route('/mcp-servers')
def list_mcp_servers():
    """Lista todos os MCP Servers configurados"""
    mcp_servers = api_manager.list_mcp_servers(enabled_only=False)
    active_connections = mcp_service.list_active_connections()
    
    return render_template('external/mcp_servers.html', 
                         mcp_servers=mcp_servers,
                         active_connections=active_connections)

@external_bp.route('/mcp-servers/add', methods=['GET', 'POST'])
def add_mcp_server():
    """Adiciona novo MCP Server"""
    if request.method == 'POST':
        try:
            name = request.form.get('name', '').strip()
            server_url = request.form.get('server_url', '').strip()
            auth_token = request.form.get('auth_token', '').strip()
            transport = request.form.get('transport', 'http')
            
            # Capabilities (lista separada por vírgulas)
            capabilities_str = request.form.get('capabilities', '').strip()
            capabilities = [cap.strip() for cap in capabilities_str.split(',') if cap.strip()]
            
            if not capabilities:
                capabilities = ["tools", "resources", "prompts"]
            
            if not all([name, server_url, auth_token]):
                flash('Nome, URL do servidor e token são obrigatórios', 'error')
                return render_template('external/add_mcp_server.html')
            
            success = api_manager.add_mcp_server(name, server_url, auth_token, capabilities, transport)
            
            if success:
                flash(f'MCP Server {name} adicionado com sucesso!', 'success')
                return redirect(url_for('external.list_mcp_servers'))
            else:
                flash('Erro ao adicionar MCP Server', 'error')
                
        except Exception as e:
            flash(f'Erro: {str(e)}', 'error')
    
    return render_template('external/add_mcp_server.html')

@external_bp.route('/mcp-servers/<name>/toggle', methods=['POST'])
def toggle_mcp_server(name):
    """Alterna status de um MCP Server"""
    success = api_manager.toggle_mcp_server(name)
    if success:
        mcp = api_manager.get_mcp_server(name)
        status = "ativado" if mcp.enabled else "desativado"
        flash(f'MCP Server {name} {status}', 'success')
    else:
        flash(f'Erro ao alterar status do MCP Server {name}', 'error')
    
    return redirect(url_for('external.list_mcp_servers'))

@external_bp.route('/mcp-servers/<name>/remove', methods=['POST'])
def remove_mcp_server(name):
    """Remove um MCP Server"""
    success = api_manager.remove_mcp_server(name)
    if success:
        flash(f'MCP Server {name} removido', 'success')
    else:
        flash(f'Erro ao remover MCP Server {name}', 'error')
    
    return redirect(url_for('external.list_mcp_servers'))

@external_bp.route('/mcp-servers/<name>/connect', methods=['POST'])
def connect_mcp_server(name):
    """Conecta a um MCP Server"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            mcp_service.connect_mcp_server(name)
        )
        
        if result['success']:
            flash(f'Conectado ao MCP Server {name}', 'success')
        else:
            flash(f'Erro ao conectar: {result["error"]}', 'error')
        
        return redirect(url_for('external.list_mcp_servers'))
        
    except Exception as e:
        flash(f'Erro na conexão: {str(e)}', 'error')
        return redirect(url_for('external.list_mcp_servers'))

@external_bp.route('/mcp-servers/<name>/test-tool', methods=['POST'])
def test_mcp_tool(name):
    """Testa uma ferramenta de um MCP Server"""
    try:
        tool_name = request.json.get('tool_name', 'ping')
        arguments = request.json.get('arguments', {})
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            mcp_service.call_mcp_tool(name, tool_name, arguments)
        )
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erro no teste: {str(e)}'
        })

@external_bp.route('/api/call', methods=['POST'])
def api_call():
    """Endpoint para chamadas de API via AJAX"""
    try:
        data = request.json
        api_name = data.get('api_name')
        endpoint = data.get('endpoint')
        method = data.get('method', 'GET')
        payload = data.get('data', {})
        params = data.get('params', {})
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            external_api_service.call_api(api_name, endpoint, method, payload, params)
        )
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erro na chamada: {str(e)}'
        })

@external_bp.route('/api/openai/chat', methods=['POST'])
def openai_chat():
    """Endpoint específico para chat com OpenAI"""
    try:
        data = request.json
        prompt = data.get('prompt', '')
        model = data.get('model', 'gpt-3.5-turbo')
        
        if not prompt:
            return jsonify({
                'success': False,
                'error': 'Prompt é obrigatório'
            })
        
        result = external_api_service.call_openai(prompt, model)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erro OpenAI: {str(e)}'
        })

@external_bp.route('/status')
def status():
    """Status geral de todas as conexões"""
    apis = api_manager.list_apis()
    mcp_servers = api_manager.list_mcp_servers()
    active_connections = mcp_service.list_active_connections()
    
    status_data = {
        'apis': {
            'total': len(api_manager.list_apis(enabled_only=False)),
            'enabled': len(apis),
            'list': list(apis.keys())
        },
        'mcp_servers': {
            'total': len(api_manager.list_mcp_servers(enabled_only=False)),
            'enabled': len(mcp_servers),
            'connected': len(active_connections),
            'list': list(mcp_servers.keys())
        },
        'timestamp': datetime.now().isoformat()
    }
    
    return jsonify(status_data) 