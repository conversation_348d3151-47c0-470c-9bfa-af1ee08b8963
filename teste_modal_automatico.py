#!/usr/bin/env python3
import sys
sys.path.insert(0, '/var/www/controle-ponto')

import requests
from bs4 import BeautifulSoup

print("=== TESTE AUTOMÁTICO DO MODAL ===")

base_url = "http://10.19.208.31:5000"
session = requests.Session()

try:
    # 1. Simular exatamente o que o modal faz
    print("1. Simulando POST do modal...")
    
    # Dados exatos que o modal envia
    form_data = {
        'motivo_desligamento': 'Demissao_sem_justa_causa',  # Valor do select
        'observacoes': 'Teste automático do modal'
    }
    
    # Usar funcionário ID 8 (SUELEN)
    funcionario_id = 8
    
    print(f"   Enviando POST para: /funcionarios/{funcionario_id}/apagar")
    print(f"   Dados: {form_data}")
    
    # Fazer POST exatamente como o modal
    response = session.post(
        f"{base_url}/funcionarios/{funcionario_id}/apagar",
        data=form_data,
        allow_redirects=False
    )
    
    print(f"   Status: {response.status_code}")
    print(f"   Headers: {dict(response.headers)}")
    
    if response.status_code == 302:
        location = response.headers.get('Location', '')
        print(f"   Redirecionado para: {location}")
        
        # Seguir redirecionamento para ver a mensagem
        final_response = session.get(f"{base_url}{location}")
        
        if "desligado com sucesso" in final_response.text:
            print("   ✅ SUCESSO: Funcionário desligado!")
        elif "Erro ao desligar funcionário" in final_response.text:
            print("   ❌ ERRO: Mensagem de erro encontrada!")
            print("   🔍 ESTE É O PROBLEMA!")
        else:
            print("   ⚠️ Resposta inesperada")
            
    elif response.status_code == 200:
        print("   ⚠️ Status 200 - não redirecionou")
        if "Erro ao desligar funcionário" in response.text:
            print("   ❌ ERRO: Mensagem de erro na resposta!")
    else:
        print(f"   ❌ Status inesperado: {response.status_code}")
        print(f"   Conteúdo: {response.text[:500]}")

except Exception as e:
    print(f"❌ ERRO: {e}")
    import traceback
    print(traceback.format_exc())

print("\n=== VERIFICANDO LOGS DO SERVIDOR ===")

# Aguardar um pouco para os logs serem escritos
import time
time.sleep(2)

print("Logs capturados - verifique o arquivo app.log no servidor")
print("\n=== FIM DO TESTE ===")
