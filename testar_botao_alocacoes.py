#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste do Botão de Alocações - RLPONTO-WEB
=========================================

Script para testar se o botão verde de alocações está funcionando
após as correções aplicadas.

Data: 07/07/2025
"""

import requests
from datetime import datetime

# Configurações
BASE_URL = "http://************"
LOGIN_URL = f"{BASE_URL}/login"
DASHBOARD_URL = f"{BASE_URL}/empresa_principal"
ALOCACOES_URL = f"{BASE_URL}/empresa_principal/alocacoes"

def testar_botao_alocacoes():
    """Testa o botão de alocações"""
    print("🧪 TESTE DO BOTÃO VERDE DE ALOCAÇÕES")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    # Criar sessão
    session = requests.Session()
    
    try:
        # 1. Fazer login
        print("\n1️⃣ Fazendo login como admin...")
        login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
        response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
        
        if response.status_code not in [200, 302]:
            print(f"❌ Falha no login: {response.status_code}")
            return False
        
        print("✅ Login realizado com sucesso")
        
        # 2. Acessar dashboard da empresa principal
        print("\n2️⃣ Acessando dashboard da empresa principal...")
        response = session.get(DASHBOARD_URL)
        
        if response.status_code != 200:
            print(f"❌ Falha ao acessar dashboard: {response.status_code}")
            return False
        
        print("✅ Dashboard acessado com sucesso")
        
        # Verificar se o botão de alocações está presente
        if "alocações" in response.text.lower() or "alocacoes" in response.text.lower():
            print("✅ Botão de alocações encontrado no dashboard")
        else:
            print("⚠️ Botão de alocações não encontrado no dashboard")
        
        # 3. Testar acesso direto à página de alocações
        print("\n3️⃣ Testando acesso à página de alocações...")
        response = session.get(ALOCACOES_URL)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Página de alocações carregada com sucesso!")
            
            # Verificar conteúdo da página
            content = response.text.lower()
            
            elementos_esperados = [
                "alocação de funcionários",
                "nova alocação", 
                "funcionários",
                "clientes",
                "total",
                "ativas"
            ]
            
            elementos_encontrados = []
            elementos_faltantes = []
            
            for elemento in elementos_esperados:
                if elemento in content:
                    elementos_encontrados.append(elemento)
                else:
                    elementos_faltantes.append(elemento)
            
            print(f"\n   📋 Elementos encontrados: {len(elementos_encontrados)}/{len(elementos_esperados)}")
            for elemento in elementos_encontrados:
                print(f"      ✅ {elemento}")
            
            if elementos_faltantes:
                print(f"\n   ⚠️ Elementos faltantes:")
                for elemento in elementos_faltantes:
                    print(f"      ❌ {elemento}")
            
            # Verificar se há erro na página
            if "erro" in content:
                print(f"\n   ⚠️ Palavra 'erro' encontrada na página")
                if "erro ao carregar" in content:
                    print(f"      ❌ Erro específico: 'erro ao carregar'")
                    return False
            else:
                print(f"\n   ✅ Nenhum erro detectado na página")
            
            return True
            
        elif response.status_code == 500:
            print("❌ Erro interno do servidor (500)")
            print("   Possível problema na consulta SQL ou configuração")
            return False
        elif response.status_code == 404:
            print("❌ Página não encontrada (404)")
            print("   Rota pode não estar registrada corretamente")
            return False
        else:
            print(f"❌ Erro inesperado: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        return False

def verificar_servidor():
    """Verifica se o servidor está respondendo"""
    print("\n🔍 VERIFICANDO SERVIDOR:")
    print("-" * 30)
    
    try:
        response = requests.get(BASE_URL, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Servidor respondendo normalmente")
            return True
        else:
            print(f"   ❌ Servidor com problema: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro de conectividade: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TESTE DO BOTÃO DE ALOCAÇÕES - RLPONTO-WEB")
    print("=" * 60)
    
    # Verificar servidor primeiro
    if not verificar_servidor():
        print("\n❌ TESTE ABORTADO: Servidor não está respondendo")
        exit(1)
    
    # Testar botão de alocações
    sucesso = testar_botao_alocacoes()
    
    if sucesso:
        print(f"\n🎉 TESTE CONCLUÍDO COM SUCESSO!")
        print("✅ Botão verde de alocações está funcionando")
        print("✅ Página carrega sem erros")
        print("✅ Elementos esperados estão presentes")
    else:
        print(f"\n❌ TESTE FALHOU!")
        print("❌ Botão de alocações ainda apresenta problemas")
        print("💡 Verifique logs do servidor para mais detalhes")
