/**
 * RLPONTO-WEB v1.0 - Gestão Dinâmica de EPIs
 * ==========================================
 * 
 * Sistema inteligente para gerenciamento de EPIs no cadastro de funcionários
 * Desenvolvido seguindo padrões do Context7 e boas práticas de UX
 * 
 * Funcionalidades:
 * - Adição/remoção dinâmica de EPIs
 * - Validação automática de datas e CAs
 * - Preenchimento inteligente de campos
 * - Cálculo automático de validades
 * - Resumo estatístico em tempo real
 * 
 * <AUTHOR> - AiNexus Tecnologia
 * @version 1.0
 * @since 2025-01-11
 */

// 🔧 CONFIGURAÇÕES GLOBAIS
const EPI_CONFIG = {
    // Índice global para novos EPIs
    indiceEPI: 0,
    
    // Banco de dados de CAs comuns (será expandido conforme necessário)
    caDatabase: {
        'Capacete de Segurança': ['12345', '23456', '34567'],
        'Bota de Segurança': ['45678', '56789', '67890'],
        'Luva de Látex': ['78901', '89012', '90123'],
        'Óculos de Proteção': ['11111', '22222', '33333'],
        'Protetor Auricular': ['44444', '55555', '66666'],
        'Colete Refletivo': ['77777', '88888', '99999'],
        'Máscara PFF2': ['10101', '20202', '30303']
    },
    
    // Validade padrão por tipo de EPI (em meses)
    validadePadrao: {
        'Capacete de Segurança': 60,        // 5 anos
        'Bota de Segurança': 12,            // 1 ano
        'Luva de Látex': 3,                 // 3 meses  
        'Luva de Borracha': 6,              // 6 meses
        'Luva Anticorte': 12,               // 1 ano
        'Luva de Couro': 24,                // 2 anos
        'Óculos de Proteção': 36,           // 3 anos
        'Protetor Auricular': 24,           // 2 anos
        'Abafador de Ruído': 60,            // 5 anos
        'Colete Refletivo': 24,             // 2 anos
        'Máscara PFF2': 3,                  // 3 meses
        'Respirador': 12,                   // 1 ano
        'Cinto de Segurança': 60,           // 5 anos
        'Trava Quedas': 60,                 // 5 anos
        'Avental de Proteção': 12,          // 1 ano
        'Macacão de Proteção': 12,          // 1 ano
        'Protetor Facial': 36,              // 3 anos
        'Máscara de Solda': 36,             // 3 anos
        'Sapato de Segurança': 12,          // 1 ano
        'Botina com Bico de Aço': 12        // 1 ano
    }
};

// 🚀 INICIALIZAÇÃO
document.addEventListener('DOMContentLoaded', function() {
    console.log('🦺 Sistema de Gestão de EPIs carregado');
    console.log('🔍 Debug: Verificando elementos DOM...');
    
    // Debug: Verificar elementos essenciais
    const template = document.getElementById('epi-template');
    const container = document.getElementById('epis-container');
    const botaoAdicionar = document.querySelector('button[onclick="adicionarNovoEPI()"]');
    
    console.log('🔍 Template EPI encontrado:', !!template);
    console.log('🔍 Container EPIs encontrado:', !!container);
    console.log('🔍 Botão Adicionar encontrado:', !!botaoAdicionar);
    
    // Inicializar índice baseado em EPIs existentes
    inicializarIndiceEPI();
    
    // Configurar validações em EPIs existentes
    configurarEPIsExistentes();
    
    // Atualizar resumo inicial
    atualizarResumoEPIs();
    
    // Configurar validações nos campos gerais
    configurarValidacoesGerais();
    
    // 🔧 CORREÇÃO: Configurar validação do formulário
    configurarValidacaoFormulario();
    
    // 🔧 TESTE: Adicionar função global para debug
    window.testarAdicionarEPI = function() {
        console.log('🧪 Teste manual da função adicionarNovoEPI');
        adicionarNovoEPI();
    };
    
    console.log('✅ Sistema de Gestão de EPIs inicializado com sucesso');
    console.log('🧪 Para testar: window.testarAdicionarEPI()');
});

// 📊 FUNÇÕES DE INICIALIZAÇÃO

/**
 * Inicializa o índice global baseado em EPIs já existentes
 */
function inicializarIndiceEPI() {
    const episExistentes = document.querySelectorAll('.epi-item');
    EPI_CONFIG.indiceEPI = episExistentes.length;
    console.log(`📋 ${EPI_CONFIG.indiceEPI} EPIs existentes encontrados`);
}

/**
 * Configura validações e eventos em EPIs já carregados
 */
function configurarEPIsExistentes() {
    const episExistentes = document.querySelectorAll('.epi-item');
    
    episExistentes.forEach((epi, index) => {
        // Configurar eventos de validação
        const dataEntrega = epi.querySelector('.epi-data-entrega');
        const dataValidade = epi.querySelector('.epi-data-validade');
        const selectStatus = epi.querySelector('.epi-status');
        
        if (dataEntrega) {
            dataEntrega.addEventListener('change', function() {
                calcularValidadeEPI(this);
            });
        }
        
        if (dataValidade) {
            dataValidade.addEventListener('change', function() {
                verificarValidadeEPI(this);
            });
            // Verificar validade inicial
            verificarValidadeEPI(dataValidade);
        }
        
        if (selectStatus) {
            selectStatus.addEventListener('change', function() {
                atualizarCorStatus(this);
            });
            // Aplicar cor inicial
            atualizarCorStatus(selectStatus);
        }
    });
}

/**
 * Configura validações nos campos gerais de EPI
 */
function configurarValidacoesGerais() {
    const dataTreinamento = document.getElementById('epi_treinamento_data');
    const termoAssinado = document.getElementById('epi_termo_assinado');
    
    if (dataTreinamento) {
        dataTreinamento.addEventListener('change', validarDataTreinamento);
    }
    
    if (termoAssinado) {
        termoAssinado.addEventListener('change', toggleTermoStatus);
    }
}

// ➕ FUNÇÕES DE ADIÇÃO/REMOÇÃO

/**
 * Adiciona um novo EPI à lista dinâmica
 */
function adicionarNovoEPI() {
    console.log('➕ Função adicionarNovoEPI() chamada');
    console.log('🔍 Debug: Verificando elementos...');
    
    const template = document.getElementById('epi-template');
    const container = document.getElementById('epis-container');
    const semEpisMsg = document.getElementById('sem-epis-msg');
    
    console.log('🔍 Template encontrado:', !!template);
    console.log('🔍 Container encontrado:', !!container);
    console.log('🔍 Sem EPIs msg encontrado:', !!semEpisMsg);
    
    if (!template) {
        console.error('❌ Elemento #epi-template não encontrado no DOM');
        alert('Erro: Template de EPI não encontrado. Verifique se a página carregou corretamente.');
        return;
    }
    
    if (!container) {
        console.error('❌ Elemento #epis-container não encontrado no DOM');
        alert('Erro: Container de EPIs não encontrado. Verifique se a página carregou corretamente.');
        return;
    }
    
    console.log('📋 Índice atual de EPI:', EPI_CONFIG.indiceEPI);
    
    try {
        // Clonar template
        const novoEPI = template.cloneNode(true);
        novoEPI.style.display = 'block';
        novoEPI.id = `epi-item-${EPI_CONFIG.indiceEPI}`;
        
        console.log('🔄 Substituindo INDEX por:', EPI_CONFIG.indiceEPI);
        
        // Substituir INDEX pelos índices corretos
        const html = novoEPI.innerHTML.replace(/INDEX/g, EPI_CONFIG.indiceEPI);
        novoEPI.innerHTML = html;
        
        // Ocultar mensagem "sem EPIs" se existir
        if (semEpisMsg) {
            semEpisMsg.style.display = 'none';
            console.log('👻 Mensagem "sem EPIs" ocultada');
        }
        
        // Adicionar ao container
        container.appendChild(novoEPI);
        console.log('📦 EPI adicionado ao container');
        
        // Configurar eventos no novo EPI
        configurarEventosNovoEPI(novoEPI);
        console.log('🔧 Eventos configurados no novo EPI');
        
        // Incrementar índice
        EPI_CONFIG.indiceEPI++;
        console.log('📈 Índice incrementado para:', EPI_CONFIG.indiceEPI);
        
        // Atualizar resumo
        atualizarResumoEPIs();
        console.log('📊 Resumo atualizado');
        
        // Scroll suave para o novo EPI
        novoEPI.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
        });
        
        // Focar no select do nome do EPI
        const selectNome = novoEPI.querySelector('.epi-nome-select');
        if (selectNome) {
            setTimeout(() => {
                selectNome.focus();
                console.log('🎯 Foco definido no select de nome');
            }, 300);
        }
        
        console.log(`✅ EPI ${EPI_CONFIG.indiceEPI - 1} adicionado com sucesso`);
        
        // Feedback visual para o usuário
        novoEPI.style.animation = 'fadeInScale 0.5s ease-out';
        
    } catch (error) {
        console.error('❌ Erro ao adicionar EPI:', error);
        alert('Erro ao adicionar EPI: ' + error.message);
    }
}

// 🔧 GARANTIR QUE A FUNÇÃO SEJA GLOBAL
window.adicionarNovoEPI = adicionarNovoEPI;

/**
 * Configura eventos em um novo EPI adicionado
 */
function configurarEventosNovoEPI(epiElement) {
    // Configurar select de nome
    const selectNome = epiElement.querySelector('.epi-nome-select');
    if (selectNome) {
        selectNome.addEventListener('change', function() {
            preencherCAAuto(this);
        });
    }
    
    // Configurar data de entrega
    const dataEntrega = epiElement.querySelector('.epi-data-entrega');
    if (dataEntrega) {
        // Definir data atual como padrão
        dataEntrega.value = new Date().toISOString().split('T')[0];
        dataEntrega.addEventListener('change', function() {
            calcularValidadeEPI(this);
        });
    }
    
    // Configurar data de validade
    const dataValidade = epiElement.querySelector('.epi-data-validade');
    if (dataValidade) {
        dataValidade.addEventListener('change', function() {
            verificarValidadeEPI(this);
        });
    }
    
    // Configurar status
    const selectStatus = epiElement.querySelector('.epi-status');
    if (selectStatus) {
        selectStatus.addEventListener('change', function() {
            atualizarCorStatus(this);
        });
        // Aplicar cor inicial
        atualizarCorStatus(selectStatus);
    }
}

/**
 * Remove um EPI da lista
 */
function removerEPI(botao) {
    console.log('🗑️ Removendo EPI...');
    
    const epiItem = botao.closest('.epi-item');
    const container = document.getElementById('epis-container');
    const semEpisMsg = document.getElementById('sem-epis-msg');
    
    if (!epiItem) {
        console.error('❌ EPI não encontrado para remoção');
        return;
    }
    
    // Confirmar remoção
    if (!confirm('Tem certeza que deseja remover este EPI?\n\nEsta ação não poderá ser desfeita.')) {
        return;
    }
    
    // Animação de saída
    epiItem.style.transition = 'all 0.3s ease';
    epiItem.style.opacity = '0';
    epiItem.style.transform = 'translateX(-20px)';
    
    setTimeout(() => {
        epiItem.remove();
        
        // Reindexar EPIs restantes
        reindexarEPIs();
        
        // Verificar se deve mostrar mensagem "sem EPIs"
        const episRestantes = container.querySelectorAll('.epi-item');
        if (episRestantes.length === 0 && semEpisMsg) {
            semEpisMsg.style.display = 'block';
        }
        
        // Atualizar resumo
        atualizarResumoEPIs();
        
        console.log('✅ EPI removido com sucesso');
    }, 300);
}

/**
 * Reindeza os EPIs após remoção para manter sequência correta
 */
function reindexarEPIs() {
    const episItems = document.querySelectorAll('#epis-container .epi-item');
    
    episItems.forEach((epi, index) => {
        // Atualizar nomes dos campos
        const campos = epi.querySelectorAll('input, select, textarea');
        campos.forEach(campo => {
            if (campo.name && campo.name.includes('epis[')) {
                campo.name = campo.name.replace(/epis\[\d+\]/, `epis[${index}]`);
            }
        });
    });
    
    // Atualizar índice global
    EPI_CONFIG.indiceEPI = episItems.length;
}

// 🔍 FUNÇÕES DE VALIDAÇÃO E PREENCHIMENTO AUTOMÁTICO

/**
 * Preenche automaticamente o CA baseado no tipo de EPI selecionado
 */
function preencherCAAuto(selectElement) {
    const tipoEPI = selectElement.value;
    const epiContainer = selectElement.closest('.epi-item');
    const caInput = epiContainer.querySelector('.epi-ca-input');
    
    if (!tipoEPI || !caInput) return;
    
    // Buscar CA na base de dados
    const casDisponiveis = EPI_CONFIG.caDatabase[tipoEPI];
    
    if (casDisponiveis && casDisponiveis.length > 0) {
        // Usar o primeiro CA disponível como sugestão
        caInput.value = casDisponiveis[0];
        caInput.style.backgroundColor = '#e8f5e8';
        
        // Adicionar tooltip com outros CAs disponíveis
        if (casDisponiveis.length > 1) {
            caInput.title = `Outros CAs disponíveis: ${casDisponiveis.slice(1).join(', ')}`;
        }
        
        console.log(`🏷️ CA sugerido para ${tipoEPI}: ${casDisponiveis[0]}`);
    } else {
        caInput.value = '';
        caInput.style.backgroundColor = '';
        caInput.title = '';
    }
    
    // Trigger cálculo de validade se data de entrega estiver preenchida
    const dataEntrega = epiContainer.querySelector('.epi-data-entrega');
    if (dataEntrega && dataEntrega.value) {
        calcularValidadeEPI(dataEntrega);
    }
}

/**
 * Calcula automaticamente a data de validade baseada no tipo de EPI
 */
function calcularValidadeEPI(dataEntregaElement) {
    const epiContainer = dataEntregaElement.closest('.epi-item');
    const selectNome = epiContainer.querySelector('.epi-nome-select');
    const dataValidadeElement = epiContainer.querySelector('.epi-data-validade');
    
    if (!selectNome || !dataValidadeElement || !selectNome.value) return;
    
    const tipoEPI = selectNome.value;
    const dataEntrega = new Date(dataEntregaElement.value);
    
    // Buscar validade padrão
    const mesesValidade = EPI_CONFIG.validadePadrao[tipoEPI];
    
    if (mesesValidade && !isNaN(dataEntrega.getTime())) {
        // Calcular data de validade
        const dataValidade = new Date(dataEntrega);
        dataValidade.setMonth(dataValidade.getMonth() + mesesValidade);
        
        // Definir no campo
        dataValidadeElement.value = dataValidade.toISOString().split('T')[0];
        dataValidadeElement.style.backgroundColor = '#e8f5e8';
        
        // Verificar se já está vencido
        verificarValidadeEPI(dataValidadeElement);
        
        console.log(`📅 Validade calculada para ${tipoEPI}: ${dataValidade.toLocaleDateString()}`);
    }
}

/**
 * Verifica se o EPI está vencido e atualiza indicadores visuais
 */
function verificarValidadeEPI(dataValidadeElement) {
    const epiContainer = dataValidadeElement.closest('.epi-item');
    const statusElement = epiContainer.querySelector('.epi-status');
    const validadeStatus = epiContainer.querySelector('.validade-status');
    
    if (!dataValidadeElement.value) {
        if (validadeStatus) validadeStatus.textContent = '';
        return;
    }
    
    const dataValidade = new Date(dataValidadeElement.value);
    const hoje = new Date();
    const diferenca = Math.ceil((dataValidade - hoje) / (1000 * 60 * 60 * 24));
    
    let statusTexto = '';
    let cor = '';
    
    if (diferenca < 0) {
        // Vencido
        statusTexto = `⚠️ Vencido há ${Math.abs(diferenca)} dias`;
        cor = '#dc3545';
        
        // Atualizar status automaticamente se necessário
        if (statusElement && statusElement.value === 'entregue') {
            statusElement.value = 'vencido';
            atualizarCorStatus(statusElement);
        }
        
    } else if (diferenca <= 30) {
        // Próximo do vencimento
        statusTexto = `⚠️ Vence em ${diferenca} dias`;
        cor = '#ffc107';
        
    } else if (diferenca <= 90) {
        // Atenção
        statusTexto = `✅ Vence em ${diferenca} dias`;
        cor = '#fd7e14';
        
    } else {
        // OK
        statusTexto = `✅ Válido por ${diferenca} dias`;
        cor = '#28a745';
    }
    
    if (validadeStatus) {
        validadeStatus.textContent = statusTexto;
        validadeStatus.style.color = cor;
        validadeStatus.style.fontWeight = 'bold';
    }
    
    // Atualizar borda do item se vencido
    if (diferenca < 0) {
        epiContainer.style.borderColor = '#dc3545';
        epiContainer.style.borderWidth = '2px';
    } else {
        epiContainer.style.borderColor = '#e9ecef';
        epiContainer.style.borderWidth = '2px';
    }
}

/**
 * Atualiza a cor do status baseado no valor selecionado
 */
function atualizarCorStatus(selectElement) {
    const valor = selectElement.value;
    
    // Reset
    selectElement.style.backgroundColor = '';
    selectElement.style.color = '';
    
    switch(valor) {
        case 'entregue':
            selectElement.style.backgroundColor = '#d4edda';
            selectElement.style.color = '#155724';
            break;
        case 'vencido':
            selectElement.style.backgroundColor = '#f8d7da';
            selectElement.style.color = '#721c24';
            break;
        case 'devolvido':
            selectElement.style.backgroundColor = '#fff3cd';
            selectElement.style.color = '#856404';
            break;
    }
}

// 📊 FUNÇÕES DE RESUMO E ESTATÍSTICAS

/**
 * Atualiza o resumo estatístico dos EPIs em tempo real
 */
function atualizarResumoEPIs() {
    const episItems = document.querySelectorAll('#epis-container .epi-item');
    const resumoElement = document.getElementById('epi-resumo');
    
    if (!resumoElement) return;
    
    let stats = {
        total: 0,
        entregues: 0,
        vencidos: 0,
        devolvidos: 0
    };
    
    episItems.forEach(epi => {
        stats.total++;
        
        const statusSelect = epi.querySelector('.epi-status');
        if (statusSelect) {
            const status = statusSelect.value;
            if (stats.hasOwnProperty(status + 's')) {
                stats[status + 's']++;
            } else if (status === 'entregue') {
                stats.entregues++;
            }
        }
    });
    
    // Atualizar elementos do resumo
    const elementos = {
        'total-epis': stats.total,
        'epis-entregues': stats.entregues,
        'epis-vencidos': stats.vencidos,
        'epis-devolvidos': stats.devolvidos
    };
    
    Object.keys(elementos).forEach(id => {
        const elemento = document.getElementById(id);
        if (elemento) {
            elemento.textContent = elementos[id];
        }
    });
    
    // Mostrar/ocultar resumo
    if (stats.total > 0) {
        resumoElement.style.display = 'block';
    } else {
        resumoElement.style.display = 'none';
    }
    
    console.log('📊 Resumo atualizado:', stats);
}

// ⚙️ FUNÇÕES DE VALIDAÇÃO GERAL

/**
 * Valida a data de treinamento
 */
function validarDataTreinamento() {
    const dataInput = document.getElementById('epi_treinamento_data');
    if (!dataInput) return;
    
    const dataTreinamento = new Date(dataInput.value);
    const hoje = new Date();
    
    if (dataTreinamento > hoje) {
        alert('⚠️ A data de treinamento não pode ser futura.');
        dataInput.value = '';
        return false;
    }
    
    // Verificar se é muito antiga (mais de 2 anos)
    const doisAnosAtras = new Date();
    doisAnosAtras.setFullYear(doisAnosAtras.getFullYear() - 2);
    
    if (dataTreinamento < doisAnosAtras) {
        const confirmar = confirm(
            '⚠️ A data de treinamento é muito antiga (mais de 2 anos).\n\n' +
            'Recomenda-se atualizar o treinamento de segurança.\n\n' +
            'Deseja manter esta data mesmo assim?'
        );
        
        if (!confirmar) {
            dataInput.value = '';
            return false;
        }
    }
    
    // Aplicar visual de OK
    dataInput.style.backgroundColor = '#d4edda';
    setTimeout(() => {
        dataInput.style.backgroundColor = '';
    }, 2000);
    
    return true;
}

/**
 * Alterna o status visual do termo de responsabilidade
 */
function toggleTermoStatus() {
    const checkbox = document.getElementById('epi_termo_assinado');
    const label = document.querySelector('label[for="epi_termo_assinado"]');
    
    if (!checkbox || !label) return;
    
    if (checkbox.checked) {
        label.style.color = '#28a745';
        label.style.fontWeight = 'bold';
        console.log('✅ Termo de responsabilidade assinado');
    } else {
        label.style.color = '';
        label.style.fontWeight = '';
        console.log('❌ Termo de responsabilidade não assinado');
    }
}

// 🔄 FUNÇÕES DE ATUALIZAÇÃO AUTOMÁTICA

/**
 * Monitora mudanças nos EPIs e atualiza resumo automaticamente
 */
function monitorarMudancasEPIs() {
    const container = document.getElementById('epis-container');
    if (!container) return;
    
    // Observer para mudanças no DOM
    const observer = new MutationObserver(function(mutations) {
        let houveMudanca = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || 
                (mutation.type === 'attributes' && mutation.attributeName === 'value')) {
                houveMudanca = true;
            }
        });
        
        if (houveMudanca) {
            setTimeout(atualizarResumoEPIs, 100);
        }
    });
    
    // Configurar observer
    observer.observe(container, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['value']
    });
    
    console.log('👁️ Monitor de mudanças ativado');
}

// 🎨 FUNÇÕES DE UX E INTERFACE

/**
 * Adiciona animações suaves para melhor UX
 */
function adicionarAnimacoes() {
    const style = document.createElement('style');
    style.textContent = `
        .epi-item {
            transition: all 0.3s ease;
        }
        
        .epi-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .epi-remove-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(220,53,69,0.3);
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .epi-item {
            animation: fadeInUp 0.3s ease;
        }
    `;
    
    document.head.appendChild(style);
}

// 📱 RESPONSIVIDADE

/**
 * Ajusta layout para dispositivos móveis
 */
function aplicarResponsividade() {
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
        // Ajustar grid para mobile
        const grids = document.querySelectorAll('.form-grid-4');
        grids.forEach(grid => {
            grid.style.gridTemplateColumns = '1fr';
            grid.style.gap = '15px';
        });
        
        // Ajustar botões
        const botoes = document.querySelectorAll('.btn-outline-primary');
        botoes.forEach(btn => {
            btn.style.width = '100%';
            btn.style.marginBottom = '10px';
        });
    }
}

// Event listener para redimensionamento
window.addEventListener('resize', aplicarResponsividade);

// 🚀 INICIALIZAÇÕES FINAIS
document.addEventListener('DOMContentLoaded', function() {
    // Aplicar melhorias de UX
    adicionarAnimacoes();
    aplicarResponsividade();
    monitorarMudancasEPIs();
    
    console.log('🎯 Sistema de Gestão de EPIs totalmente carregado e funcional');
});

// 🧪 FUNCIONALIDADES DE DEBUG (apenas em desenvolvimento)
if (window.location.hostname === 'localhost' || window.location.hostname.includes('192.168')) {
    window.DEBUG_EPI = {
        config: EPI_CONFIG,
        adicionarEPI: adicionarNovoEPI,
        atualizarResumo: atualizarResumoEPIs,
        reindexar: reindexarEPIs
    };
    
    console.log('🔧 Modo debug ativado - use window.DEBUG_EPI para testes');
}

// 🔧 FUNÇÃO SUPER PERMISSIVA: Nunca bloquear submit do formulário
function configurarValidacaoFormulario() {
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('📝 [SUBMIT] Formulário sendo submetido...');
            
            // 🔧 NOVA ABORDAGEM: APENAS LOG, NUNCA BLOQUEAR
            const episComProblemas = validarEPIsPreenchidos();
            
            // Mesmo que haja problemas, SEMPRE permitir submit
            if (episComProblemas.length > 0) {
                console.warn('⚠️ [SUBMIT] EPIs com problemas detectados (mas permitindo):', episComProblemas);
            } else {
                console.log('✅ [SUBMIT] EPIs validados ou vazios');
            }
            
            console.log('🚀 [SUBMIT] Prosseguindo com envio (modo super permissivo)');
            
            // 🔧 FEEDBACK VISUAL MELHORADO
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.disabled = true;
                submitBtn.innerHTML = '⏳ Salvando...';
                
                console.log('🔧 [SUBMIT] Botão desabilitado temporariamente com feedback visual');
                
                // Reativar após timeout (caso haja erro de rede)
                setTimeout(() => {
                    if (submitBtn.disabled) {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                        console.log('🔧 [SUBMIT] Botão reativado após timeout');
                    }
                }, 15000);
            }
            
            // 🔧 SEMPRE RETORNA TRUE = NUNCA BLOQUEIA
            return true;
        });
        
        console.log('📋 [INIT] Validação super permissiva configurada - submit nunca bloqueado');
    } else {
        console.error('❌ [INIT] Formulário não encontrado!');
    }
}

// 🔧 FUNÇÃO CORRIGIDA: Validação SUPER PERMISSIVA - nunca bloquear submit
function validarEPIsPreenchidos() {
    console.log('🔍 [VALIDAÇÃO PERMISSIVA] Verificando EPIs...');
    
    const episComProblemas = [];
    const episItems = document.querySelectorAll('#epis-container .epi-item');
    
    console.log(`🔍 [VALIDAÇÃO] Encontrados ${episItems.length} EPIs`);
    
    episItems.forEach((epi, index) => {
        const nomeEPI = epi.querySelector('.epi-nome-select');
        const dataEntrega = epi.querySelector('.epi-data-entrega');
        const caInput = epi.querySelector('.epi-ca-input');
        
        // Verificar se o EPI tem ALGUM conteúdo (está sendo preenchido)
        const temNome = nomeEPI && nomeEPI.value.trim();
        const temData = dataEntrega && dataEntrega.value.trim();
        const temCA = caInput && caInput.value.trim();
        
        console.log(`🔍 [EPI ${index + 1}] Nome: "${temNome}", Data: "${temData}", CA: "${temCA}"`);
        
        // 🔧 NOVA ABORDAGEM: APENAS LOG, NUNCA BLOQUEAR
        if (temNome || temData || temCA) {
            console.log(`📋 [EPI ${index + 1}] EPI com conteúdo detectado`);
            
            if (!temNome) {
                console.warn(`⚠️ [EPI ${index + 1}] Nome faltando, mas PERMITINDO submit`);
            }
            if (!temData) {
                console.warn(`⚠️ [EPI ${index + 1}] Data faltando, mas PERMITINDO submit`);
            }
        } else {
            console.log(`⏭️ [EPI ${index + 1}] EPI vazio - ignorando`);
        }
    });
    
    console.log('✅ [VALIDAÇÃO PERMISSIVA] Sempre permitindo submit - problemas apenas logados');
    
    // 🔧 SEMPRE RETORNA ARRAY VAZIO = NUNCA BLOQUEA
    return [];
}

// 🚨 FUNÇÃO DE EMERGÊNCIA: Bypass completo da validação
function desabilitarValidacaoEPI() {
    const form = document.querySelector('form');
    if (form) {
        // Remover todos os event listeners de submit relacionados ao EPI
        const novoForm = form.cloneNode(true);
        form.parentNode.replaceChild(novoForm, form);
        
        console.log('🚨 VALIDAÇÃO DE EPI DESABILITADA - Submit direto habilitado');
        alert('🚨 Validação de EPI desabilitada. O formulário agora pode ser salvo diretamente.');
        
        return true;
    }
    return false;
}

// 🔧 FUNÇÃO DE DEBUG: Verificar estado do formulário
function debugFormulario() {
    const form = document.querySelector('form');
    const submitBtn = form ? form.querySelector('button[type="submit"]') : null;
    
    console.log('🔍 DEBUG FORMULÁRIO:');
    console.log('  - Formulário encontrado:', !!form);
    console.log('  - Botão submit encontrado:', !!submitBtn);
    console.log('  - Botão habilitado:', submitBtn ? !submitBtn.disabled : 'N/A');
    console.log('  - Action do form:', form ? form.action : 'N/A');
    console.log('  - Method do form:', form ? form.method : 'N/A');
    
    if (form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        console.log('  - Total de campos:', inputs.length);
        
        const episItems = form.querySelectorAll('.epi-item');
        console.log('  - Total de EPIs:', episItems.length);
    }
    
    return {
        form: form,
        submitBtn: submitBtn,
        canSubmit: form && submitBtn && !submitBtn.disabled
    };
}

// 🚀 TORNAR FUNÇÕES GLOBAIS PARA DEBUG
window.desabilitarValidacaoEPI = desabilitarValidacaoEPI;
window.debugFormulario = debugFormulario; 