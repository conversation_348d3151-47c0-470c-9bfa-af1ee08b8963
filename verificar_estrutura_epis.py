#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar estrutura de EPIs no banco
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def verificar_estrutura_epis():
    """Verificar estrutura de EPIs no banco"""
    print("🔍 VERIFICANDO ESTRUTURA DE EPIs NO BANCO")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Listar todas as tabelas
        print("\n1. Listando todas as tabelas do banco...")
        sql_tabelas = "SHOW TABLES"
        tabelas = db.execute_query(sql_tabelas)
        
        print(f"📊 Tabelas encontradas: {len(tabelas)}")
        tabelas_relacionadas_epi = []
        for tabela in tabelas:
            nome_tabela = list(tabela.values())[0]
            print(f"   - {nome_tabela}")
            if 'epi' in nome_tabela.lower():
                tabelas_relacionadas_epi.append(nome_tabela)
        
        print(f"\n📋 Tabelas relacionadas a EPI: {tabelas_relacionadas_epi}")
        
        # 2. Verificar estrutura da tabela funcionarios
        print(f"\n2. Verificando estrutura da tabela funcionarios...")
        sql_desc_funcionarios = "DESCRIBE funcionarios"
        campos_funcionarios = db.execute_query(sql_desc_funcionarios)
        
        campos_epi_funcionarios = []
        for campo in campos_funcionarios:
            if 'epi' in campo['Field'].lower():
                campos_epi_funcionarios.append(campo)
                print(f"   📋 Campo EPI: {campo['Field']} ({campo['Type']})")
        
        if not campos_epi_funcionarios:
            print(f"   ❌ Nenhum campo relacionado a EPI na tabela funcionarios")
        
        # 3. Verificar se existe tabela epis
        if 'epis' in [t[list(t.keys())[0]] for t in tabelas]:
            print(f"\n3. Verificando tabela epis...")
            sql_desc_epis = "DESCRIBE epis"
            campos_epis = db.execute_query(sql_desc_epis)
            
            print(f"📋 Estrutura da tabela epis:")
            for campo in campos_epis:
                print(f"   - {campo['Field']}: {campo['Type']} ({campo['Null']}, {campo['Key']})")
            
            # Verificar EPIs cadastrados
            sql_count_epis = "SELECT COUNT(*) as total FROM epis"
            total_epis = db.execute_query(sql_count_epis, fetch_one=True)
            print(f"📊 Total de EPIs cadastrados: {total_epis['total']}")
        
        # 4. Verificar função get_with_epis
        print(f"\n4. Analisando função get_with_epis...")
        from utils.database import FuncionarioQueries
        
        funcionario = FuncionarioQueries.get_with_epis(1)
        
        if funcionario:
            print(f"📋 Campos retornados por get_with_epis:")
            for key, value in funcionario.items():
                if 'epi' in key.lower():
                    print(f"   - {key}: {value}")
            
            # Verificar se há campo epis
            if 'epis' in funcionario:
                print(f"   📋 Campo 'epis': {funcionario['epis']}")
            else:
                print(f"   ❌ Campo 'epis' não encontrado")
        
        # 5. Verificar como EPIs são salvos
        print(f"\n5. Investigando como EPIs são salvos...")
        
        # Verificar se há campo JSON ou TEXT para EPIs na tabela funcionarios
        sql_funcionario_richardson = """
        SELECT * FROM funcionarios WHERE id = 1
        """
        
        richardson = db.execute_query(sql_funcionario_richardson, fetch_one=True)
        
        if richardson:
            print(f"📋 Dados do Richardson:")
            for key, value in richardson.items():
                if 'epi' in key.lower() or (isinstance(value, str) and value and ('epi' in value.lower() or '[' in value)):
                    print(f"   - {key}: {value}")
        
        # 6. Verificar se EPIs estão em JSON
        print(f"\n6. Procurando por dados JSON que possam conter EPIs...")
        
        if richardson:
            for key, value in richardson.items():
                if isinstance(value, str) and value and (value.startswith('[') or value.startswith('{')):
                    try:
                        parsed = json.loads(value)
                        print(f"   📋 Campo JSON '{key}': {parsed}")
                    except:
                        pass
        
        # 7. Verificar logs de adição de EPIs
        print(f"\n7. Verificando se há tabela de logs...")
        
        tabelas_log = [t for t in [tab[list(tab.keys())[0]] for tab in tabelas] if 'log' in t.lower()]
        if tabelas_log:
            print(f"📋 Tabelas de log encontradas: {tabelas_log}")
        else:
            print(f"❌ Nenhuma tabela de log encontrada")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a verificação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    verificar_estrutura_epis()
