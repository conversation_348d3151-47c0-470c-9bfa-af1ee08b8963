import ctypes
import logging
import os

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Nome da DLL (ajuste com base no que você encontrou)
dll_name = "ZKFPcap.dll"
dll_path = os.path.join("C:\\Windows\\System32\\ZKFSensors", dll_name)
if not os.path.exists(dll_path):
    dll_path = os.path.join("C:\\Windows\\System32", dll_name)
    if not os.path.exists(dll_path):
        logger.error(f"DLL {dll_name} não encontrada. Verifique o diretório.")
        exit(1)

# Carregar a DLL
try:
    zkfp = ctypes.WinDLL(dll_path)
except OSError as e:
    logger.error(f"Falha ao carregar a DLL {dll_name}: {str(e)}")
    exit(1)

# Funções baseadas na interface do ZKFinger SDK
def initialize_device():
    logger.info("Inicializando o dispositivo ZK4500...")
    zkfp.Init.restype = ctypes.c_int
    result = zkfp.Init()
    if result == 0:
        logger.info("SDK inicializado com sucesso.")
        zkfp.OpenDevice.restype = ctypes.c_long
        handle = zkfp.OpenDevice(0)  # 0 para o primeiro dispositivo
        if handle > 0:
            logger.info("Dispositivo ZK4500 aberto com sucesso. Handle: %d", handle)
            return handle
        else:
            logger.error("Falha ao abrir o dispositivo ZK4500. Código de erro: %d", handle)
            return None
    else:
        logger.error("Falha ao inicializar o SDK. Código de erro: %d", result)
        return None

def capture_fingerprint(handle):
    logger.info("Capturando impressão digital...")
    template = (ctypes.c_ubyte * 2048)()
    template_len = ctypes.c_int(2048)
    img_buffer = (ctypes.c_ubyte * (400 * 400))()  # Tamanho aproximado, ajuste com GetParameters
    zkfp.AcquireFingerprint.argtypes = [ctypes.c_long, ctypes.POINTER(ctypes.c_ubyte), ctypes.POINTER(ctypes.c_ubyte), ctypes.POINTER(ctypes.c_int)]
    zkfp.AcquireFingerprint.restype = ctypes.c_int
    result = zkfp.AcquireFingerprint(handle, img_buffer, template, ctypes.byref(template_len))
    if result == 0:
        logger.info("Impressão digital capturada com sucesso.")
        return bytes(template[:template_len.value])
    else:
        logger.error(f"Falha ao capturar a impressão digital. Código de erro: {result}")
        return None

def main():
    device = initialize_device()
    if device:
        biometria = capture_fingerprint(device)
        if biometria:
            logger.info("Biometria capturada com sucesso. Pressione Enter para continuar.")
            with open("biometria.bin", "wb") as f:
                f.write(biometria)
            logger.info("Biometria salva em biometria.bin.")
        zkfp.CloseDevice(device)
        zkfp.Terminate()
    else:
        logger.error("Não foi possível continuar devido a falha na inicialização.")

if __name__ == "__main__":
    main()
