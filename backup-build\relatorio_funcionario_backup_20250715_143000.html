<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Ponto - {{ funcionario.nome_completo }}</title>
    <style>
        /* ===== RESET E BASE ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 10pt;
            line-height: 1.3;
            color: #333;
            background: #fff;
            padding: 20px;
        }

        /* ===== CABEÇALHO DA EMPRESA ===== */
        .empresa-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .empresa-nome {
            font-size: 16pt;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .empresa-cnpj {
            font-size: 10pt;
            color: #666;
            margin-bottom: 5px;
        }

        .relatorio-titulo {
            font-size: 14pt;
            font-weight: 600;
            color: #2c3e50;
            margin-top: 10px;
        }

        /* ===== INFORMAÇÕES DO FUNCIONÁRIO ===== */
        .funcionario-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .funcionario-nome {
            font-size: 14pt;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .funcionario-detalhes {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 9pt;
        }

        .detalhe-item {
            display: flex;
            justify-content: space-between;
        }

        .detalhe-label {
            font-weight: 600;
            color: #495057;
        }

        .detalhe-valor {
            color: #6c757d;
        }

        /* ===== PERÍODO DO RELATÓRIO ===== */
        .periodo-info {
            text-align: center;
            background: #e3f2fd;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 11pt;
            font-weight: 500;
            color: #1976d2;
        }

        /* ===== TABELA DE REGISTROS ===== */
        .registros-container {
            margin-bottom: 20px;
        }

        .registros-titulo {
            font-size: 12pt;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }

        .registros-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 8pt;
            margin-bottom: 15px;
        }

        .registros-table th,
        .registros-table td {
            border: 1px solid #dee2e6;
            padding: 6px 8px;
            text-align: center;
        }

        .registros-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .registros-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        .registros-table tbody tr:hover {
            background: #e3f2fd;
        }

        /* ===== CORES PARA STATUS ===== */
        .status-completo { color: #28a745; font-weight: 600; }
        .status-incompleto { color: #dc3545; font-weight: 600; }
        .status-falta { color: #6c757d; font-weight: 600; }
        .status-folga { color: #17a2b8; font-weight: 600; }
        .status-feriado { color: #fd7e14; font-weight: 600; }

        /* ===== RESUMO ESTATÍSTICAS ===== */
        .resumo-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-valor {
            font-size: 14pt;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 3px;
        }

        .stat-label {
            font-size: 8pt;
            color: #6c757d;
            text-transform: uppercase;
            font-weight: 500;
        }

        /* ===== BOTÕES DE AÇÃO (APENAS NA TELA) ===== */
        .action-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .btn-action {
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            font-size: 10pt;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-print {
            background: #28a745;
            color: white;
        }

        .btn-print:hover {
            background: #218838;
        }

        .btn-back {
            background: #6c757d;
            color: white;
        }

        .btn-back:hover {
            background: #5a6268;
        }

        /* ===== RODAPÉ ===== */
        .relatorio-footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            font-size: 8pt;
            color: #6c757d;
        }

        /* ===== ESTILOS PARA IMPRESSÃO ===== */
        @media print {
            .action-buttons {
                display: none !important;
            }
            
            body {
                padding: 0;
                font-size: 9pt;
            }
            
            .empresa-header {
                page-break-inside: avoid;
            }
            
            .funcionario-info {
                page-break-inside: avoid;
            }
            
            .registros-table {
                page-break-inside: auto;
            }
            
            .registros-table tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            
            .resumo-stats {
                page-break-inside: avoid;
            }
        }

        /* ===== RESPONSIVIDADE ===== */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .funcionario-detalhes {
                grid-template-columns: 1fr;
            }
            
            .resumo-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .action-buttons {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 20px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Botões de Ação (apenas na tela) -->
    <div class="action-buttons">
        <button onclick="window.print()" class="btn-action btn-print">
            🖨️ Imprimir
        </button>
        <a href="{{ url_for('ponto_admin.detalhes_funcionario', funcionario_id=funcionario.id) }}" class="btn-action btn-back">
            ← Voltar
        </a>
    </div>

    <!-- Cabeçalho da Empresa -->
    <div class="empresa-header">
        <div class="empresa-nome">
            {{ funcionario.empresa_nome or 'EMPRESA' }}
        </div>
        <div class="relatorio-titulo">
            RELATÓRIO DE REGISTROS DE PONTO
        </div>
        <div class="data-geracao">
            Gerado em: {{ data_atual.strftime('%d/%m/%Y %H:%M') if data_atual else '' }}
        </div>
    </div>

    <!-- Informações do Funcionário -->
    <div class="funcionario-info">
        <div class="funcionario-nome">
            {{ funcionario.nome_completo }}
        </div>
        <div class="funcionario-detalhes">
            <div class="detalhe-item">
                <span class="detalhe-label">Matrícula:</span>
                <span class="detalhe-valor">{{ funcionario.matricula or 'N/A' }}</span>
            </div>
            <div class="detalhe-item">
                <span class="detalhe-label">Cargo:</span>
                <span class="detalhe-valor">{{ funcionario.cargo or 'N/A' }}</span>
            </div>
            <div class="detalhe-item">
                <span class="detalhe-label">CPF:</span>
                <span class="detalhe-valor">{{ funcionario.cpf or 'N/A' }}</span>
            </div>
            <div class="detalhe-item">
                <span class="detalhe-label">Empresa:</span>
                <span class="detalhe-valor">{{ funcionario.empresa_nome or 'N/A' }}</span>
            </div>
        </div>
    </div>

    <!-- Período do Relatório -->
    {% if data_inicio or data_fim %}
    <div class="periodo-info">
        Período: 
        {% if data_inicio %}{{ data_inicio }}{% else %}Início{% endif %}
        até 
        {% if data_fim %}{{ data_fim }}{% else %}{{ data_atual.strftime('%d/%m/%Y') if data_atual else '' }}{% endif %}
    </div>
    {% endif %}

    <!-- Registros de Ponto -->
    <div class="registros-container">
        <div class="registros-titulo">
            Registros de Ponto ({{ registros|length }} registros)
        </div>
        
        {% if registros %}
        <table class="registros-table">
            <thead>
                <tr>
                    <th>Data</th>
                    <th>Dia Semana</th>
                    <th>Entrada</th>
                    <th>Saída Almoço</th>
                    <th>Retorno Almoço</th>
                    <th>Saída</th>
                    <th>Horas Trabalhadas</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for registro in registros %}
                <tr>
                    <td>{{ registro.data_registro.strftime('%d/%m/%Y') if registro.data_registro else 'N/A' }}</td>
                    <td>{{ registro.dia_semana or 'N/A' }}</td>
                    <td>{{ registro.entrada.strftime('%H:%M') if registro.entrada else '-' }}</td>
                    <td>{{ registro.saida_almoco.strftime('%H:%M') if registro.saida_almoco else '-' }}</td>
                    <td>{{ registro.retorno_almoco.strftime('%H:%M') if registro.retorno_almoco else '-' }}</td>
                    <td>{{ registro.saida.strftime('%H:%M') if registro.saida else '-' }}</td>
                    <td>{{ registro.horas_trabalhadas or '-' }}</td>
                    <td class="status-{{ registro.status_classe or 'incompleto' }}">
                        {{ registro.status_texto or 'Incompleto' }}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div style="text-align: center; padding: 20px; color: #6c757d;">
            Nenhum registro encontrado para o período selecionado.
        </div>
        {% endif %}
    </div>

    <!-- Resumo Estatísticas -->
    <div class="resumo-stats">
        <div class="stat-card">
            <div class="stat-valor">{{ registros|length }}</div>
            <div class="stat-label">Total de Registros</div>
        </div>
        <div class="stat-card">
            <div class="stat-valor">{{ registros|selectattr('status_classe', 'equalto', 'completo')|list|length }}</div>
            <div class="stat-label">Dias Completos</div>
        </div>
        <div class="stat-card">
            <div class="stat-valor">{{ registros|selectattr('status_classe', 'equalto', 'incompleto')|list|length }}</div>
            <div class="stat-label">Dias Incompletos</div>
        </div>
        <div class="stat-card">
            <div class="stat-valor">{{ registros|selectattr('status_classe', 'equalto', 'falta')|list|length }}</div>
            <div class="stat-label">Faltas</div>
        </div>
    </div>

    <!-- Rodapé -->
    <div class="relatorio-footer">
        <p>Sistema de Controle de Ponto - RLPONTO-WEB v1.0</p>
        <p>Relatório gerado automaticamente em {{ data_atual.strftime('%d/%m/%Y %H:%M:%S') if data_atual else '' }}</p>
    </div>
</body>
</html>
