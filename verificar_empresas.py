#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para Verificar Empresas - RLPONTO-WEB
-------------------------------------------
Verifica se as empresas foram removidas.
"""

from utils.database import DatabaseManager

def verificar_empresas():
    """
    Verifica se as empresas foram removidas
    """
    print("=" * 60)
    print("🔍 VERIFICAÇÃO DE EMPRESAS - RLPONTO-WEB")
    print("=" * 60)
    
    try:
        # Verificar empresas
        query = "SELECT COUNT(*) as total FROM empresas"
        result = DatabaseManager.execute_query(query, fetch=True)
        total = result[0]["total"] if result else 0
        
        print(f"Total de empresas: {total}")
        
        if total == 0:
            print("\n✅ TODAS AS EMPRESAS FORAM REMOVIDAS COM SUCESSO!")
            print("🔄 O sistema está pronto para começar do zero")
        else:
            print(f"⚠️ Ainda existem {total} empresas no banco")
            
            # Listar empresas se houver
            if total > 0:
                empresas = DatabaseManager.execute_query("SELECT id, razao_social, cnpj, empresa_teste FROM empresas", fetch=True)
                print("\nEmpresas existentes:")
                for empresa in empresas:
                    print(f"ID: {empresa['id']} | Nome: {empresa['razao_social']} | CNPJ: {empresa['cnpj']} | Teste: {empresa['empresa_teste']}")
        
        print("\n✅ Verificação finalizada!")
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    verificar_empresas() 