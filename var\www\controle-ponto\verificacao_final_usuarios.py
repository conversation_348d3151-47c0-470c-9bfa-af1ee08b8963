#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Verificação Final do Sistema de Usuários - RLPONTO-WEB
======================================================

Script para verificar o estado final do sistema de usuários
após as correções aplicadas.

Data: 07/07/2025
"""

import pymysql
from datetime import datetime

# Configurações do banco
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def verificar_estado_final():
    """Verifica o estado final dos usuários"""
    print("🔍 VERIFICAÇÃO FINAL DO SISTEMA DE USUÁRIOS")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        with conn.cursor() as cursor:
            # Verificar estado atual
            cursor.execute("""
                SELECT 
                    u.id, 
                    u.usuario, 
                    u.nivel_acesso as nivel_usuarios, 
                    p.nivel_acesso as nivel_permissoes,
                    CASE 
                        WHEN p.nivel_acesso IS NULL THEN '❌ SEM PERMISSÃO'
                        WHEN u.nivel_acesso = p.nivel_acesso THEN '✅ CONSISTENTE'
                        ELSE '⚠️ INCONSISTENTE'
                    END as status,
                    u.ativo
                FROM usuarios u 
                LEFT JOIN permissoes p ON u.id = p.usuario_id 
                ORDER BY u.id
            """)
            
            usuarios = cursor.fetchall()
            
            print("\n📋 ESTADO ATUAL DOS USUÁRIOS:")
            print("-" * 80)
            print(f"{'ID':<3} | {'USUÁRIO':<12} | {'NÍVEL USR':<10} | {'NÍVEL PERM':<11} | {'STATUS':<15} | {'ATIVO'}")
            print("-" * 80)
            
            inconsistencias = 0
            for user in usuarios:
                nivel_perm = user['nivel_permissoes'] or 'NULL'
                status_icon = user['status']
                if '❌' in status_icon or '⚠️' in status_icon:
                    inconsistencias += 1
                
                print(f"{user['id']:<3} | {user['usuario']:<12} | {user['nivel_usuarios']:<10} | "
                      f"{nivel_perm:<11} | {status_icon:<15} | {user['ativo']}")
            
            print("-" * 80)
            
            # Resumo
            total_usuarios = len(usuarios)
            usuarios_consistentes = total_usuarios - inconsistencias
            
            print(f"\n📊 RESUMO:")
            print(f"   Total de usuários: {total_usuarios}")
            print(f"   Usuários consistentes: {usuarios_consistentes}")
            print(f"   Inconsistências: {inconsistencias}")
            
            if inconsistencias == 0:
                print("\n🎉 SISTEMA 100% CONSISTENTE!")
                print("✅ Todos os usuários têm permissões adequadas")
                print("✅ Não há inconsistências entre tabelas")
                print("✅ Sistema de alteração de níveis funcionando")
            else:
                print(f"\n⚠️ ENCONTRADAS {inconsistencias} INCONSISTÊNCIAS")
                print("❌ Sistema precisa de correção adicional")
            
            # Verificar usuários especiais
            print(f"\n👥 USUÁRIOS ESPECIAIS:")
            especiais = ['admin', 'cavalcrod', 'status']
            for esp in especiais:
                cursor.execute("""
                    SELECT u.usuario, u.nivel_acesso, p.nivel_acesso as perm_nivel
                    FROM usuarios u 
                    LEFT JOIN permissoes p ON u.id = p.usuario_id 
                    WHERE u.usuario = %s
                """, (esp,))
                
                user_esp = cursor.fetchone()
                if user_esp:
                    perm_status = "✅" if user_esp['perm_nivel'] else "❌"
                    print(f"   {esp}: {user_esp['nivel_acesso']} / {user_esp['perm_nivel']} {perm_status}")
                else:
                    print(f"   {esp}: ❌ NÃO ENCONTRADO")
            
    except Exception as e:
        print(f"❌ Erro na verificação: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    verificar_estado_final()
