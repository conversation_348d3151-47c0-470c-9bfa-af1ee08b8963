#!/usr/bin/env python3
import paramiko
import os

def fix_empresas_links():
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("Conectado ao servidor com sucesso!")
        
        # Corrigir os links no template de configurações
        print("Corrigindo links da aba Empresas...")
        
        # Comando para corrigir os links
        fix_command = '''
        cd /var/www/controle-ponto/templates/configuracoes
        
        # Fazer backup do arquivo original
        cp index.html index.html.backup_$(date +%Y%m%d_%H%M%S)
        
        # Corrigir os links
        sed -i 's|href="/empresas/cadastrar"|href="{{ url_for('"'"'configuracoes.nova_empresa'"'"') }}"|g' index.html
        sed -i 's|href="/empresas"|href="{{ url_for('"'"'configuracoes.listar_empresas'"'"') }}"|g' index.html
        
        echo "Links corrigidos com sucesso!"
        '''
        
        stdin, stdout, stderr = ssh.exec_command(fix_command)
        
        # Verificar resultado
        output = stdout.read().decode()
        error = stderr.read().decode()
        
        if error:
            print(f"Erro: {error}")
        else:
            print("Correção aplicada com sucesso!")
            print(output)
        
        # Verificar se as correções foram aplicadas
        print("Verificando as correções...")
        stdin, stdout, stderr = ssh.exec_command('grep -n "configuracoes.nova_empresa\|configuracoes.listar_empresas" /var/www/controle-ponto/templates/configuracoes/index.html')
        verification = stdout.read().decode()
        print("Linhas corrigidas:")
        print(verification)
        
        # Reiniciar o serviço
        print("Reiniciando serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl restart controle-ponto')
        
        # Verificar status
        print("Verificando status do serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl status controle-ponto --no-pager')
        status = stdout.read().decode()
        print(status)
        
        ssh.close()
        print("Correção concluída com sucesso!")
        
    except Exception as e:
        print(f"Erro durante a correção: {e}")

if __name__ == "__main__":
    fix_empresas_links()
