#!/bin/bash

# =====================================================
# COMANDOS PARA ATUALIZAR SERVIDOR - CONFIGURAÇÕES BIOMÉTRICAS
# =====================================================

echo "🚀 Iniciando atualização do servidor..."

# 1. Fazer backup
echo "📦 Fazendo backup dos arquivos atuais..."
cd /var/www/controle-ponto
mkdir -p backup-pre-biometric-$(date +%Y%m%d_%H%M%S)
cp templates/configuracoes/index.html backup-pre-biometric-$(date +%Y%m%d_%H%M%S)/
cp app_biometric_config.py backup-pre-biometric-$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || echo "app_biometric_config.py não existe ainda"

# 2. Parar serviço Flask
echo "🛑 Parando serviço Flask..."
pkill -f 'python.*app.py' || echo "Nenhum processo Flask encontrado"

# 3. Aguardar parada completa
echo "⏱️ Aguardando parada completa..."
sleep 3

# 4. Verificar se parou
echo "🔍 Verificando processos Flask..."
if pgrep -f 'python.*app.py'; then
    echo "❌ Ainda há processos Flask rodando!"
    pkill -9 -f 'python.*app.py'
    sleep 2
else
    echo "✅ Processos Flask parados"
fi

# 5. Criar arquivo de verificação
echo "📝 Criando arquivo de verificação..."
cat > check_biometric.py << 'EOF'
#!/usr/bin/env python3
import os
from pathlib import Path

def check():
    print("🔍 Verificando configurações biométricas...")
    
    # Verificar template principal
    template = Path("templates/configuracoes/index.html")
    if template.exists():
        with open(template, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'fas fa-fingerprint' in content and 'Biometria' in content:
                print("✅ Template principal: OK (botão biometria presente)")
                return True
            else:
                print("❌ Template principal: ERRO (botão biometria ausente)")
                return False
    else:
        print("❌ Template principal não encontrado")
        return False

if __name__ == "__main__":
    if check():
        print("🎉 Configuração biométrica detectada!")
    else:
        print("💥 Problema na configuração!")
EOF

chmod +x check_biometric.py

# 6. Executar verificação inicial
echo "🔍 Verificação inicial..."
python3 check_biometric.py

echo ""
echo "=============================================="
echo "🎯 PRÓXIMOS PASSOS MANUAIS:"
echo "=============================================="
echo "1. Copie os arquivos atualizados do Windows para o servidor"
echo "2. Execute: python3 check_biometric.py"
echo "3. Se OK, execute: python3 app.py &"
echo "4. Teste acessando a página de configurações"
echo "==============================================" 