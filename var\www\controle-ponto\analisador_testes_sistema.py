#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALISADOR DE TESTES DO SISTEMA RLPONTO-WEB
==========================================

Este script executa uma bateria completa de testes no sistema e gera
um relatório detalhado com falhas, erros de lógica e possíveis bugs.

Autor: Sistema RLPONTO-WEB
Data: 17/07/2025
"""

import mysql.connector
import json
from datetime import datetime, timedelta
from decimal import Decimal
import re

# Configuração do banco de dados
DB_CONFIG = {
    'host': 'localhost',
    'user': 'controle_ponto_user',
    'password': 'senha_segura_2024',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

class AnalisadorTestes:
    def __init__(self):
        self.conn = None
        self.relatorio = {
            'data_execucao': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'testes_executados': [],
            'falhas_encontradas': [],
            'erros_logica': [],
            'bugs_potenciais': [],
            'estatisticas': {},
            'recomendacoes': []
        }
    
    def conectar_banco(self):
        """Conecta ao banco de dados"""
        try:
            self.conn = mysql.connector.connect(**DB_CONFIG)
            return True
        except Exception as e:
            self.adicionar_falha("CONEXAO_BANCO", f"Erro ao conectar ao banco: {e}")
            return False
    
    def adicionar_falha(self, categoria, descricao, severidade="ALTA"):
        """Adiciona uma falha ao relatório"""
        self.relatorio['falhas_encontradas'].append({
            'categoria': categoria,
            'descricao': descricao,
            'severidade': severidade,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })
    
    def adicionar_erro_logica(self, funcionalidade, problema, impacto):
        """Adiciona um erro de lógica ao relatório"""
        self.relatorio['erros_logica'].append({
            'funcionalidade': funcionalidade,
            'problema': problema,
            'impacto': impacto,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })
    
    def adicionar_bug_potencial(self, area, descricao, evidencia):
        """Adiciona um bug potencial ao relatório"""
        self.relatorio['bugs_potenciais'].append({
            'area': area,
            'descricao': descricao,
            'evidencia': evidencia,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })
    
    def teste_1_integridade_funcionarios(self):
        """Teste 1: Verificar integridade dos dados dos funcionários"""
        print("🧪 Executando Teste 1: Integridade dos Funcionários")
        
        cursor = self.conn.cursor()
        
        # Verificar funcionários duplicados
        cursor.execute("""
            SELECT cpf, COUNT(*) as total 
            FROM funcionarios 
            WHERE status = 'ativo' 
            GROUP BY cpf 
            HAVING COUNT(*) > 1
        """)
        duplicados = cursor.fetchall()
        
        if duplicados:
            self.adicionar_falha("FUNCIONARIOS_DUPLICADOS", 
                               f"Encontrados {len(duplicados)} CPFs duplicados", "ALTA")
        
        # Verificar funcionários sem jornada
        cursor.execute("""
            SELECT f.id, f.nome 
            FROM funcionarios f 
            LEFT JOIN horarios_trabalho ht ON f.id = ht.funcionario_id 
            WHERE f.status = 'ativo' AND ht.funcionario_id IS NULL
        """)
        sem_jornada = cursor.fetchall()
        
        if sem_jornada:
            self.adicionar_falha("FUNCIONARIOS_SEM_JORNADA", 
                               f"{len(sem_jornada)} funcionários ativos sem jornada definida", "ALTA")
        
        # Verificar funcionários com empresas inativas
        cursor.execute("""
            SELECT f.id, f.nome, e.nome_empresa 
            FROM funcionarios f 
            JOIN empresas e ON f.empresa_id = e.id 
            WHERE f.status = 'ativo' AND e.ativa = 0
        """)
        empresa_inativa = cursor.fetchall()
        
        if empresa_inativa:
            self.adicionar_falha("FUNCIONARIOS_EMPRESA_INATIVA", 
                               f"{len(empresa_inativa)} funcionários ativos em empresas inativas", "MEDIA")
        
        cursor.close()
        self.relatorio['testes_executados'].append("Integridade dos Funcionários")
    
    def teste_2_calculo_horas(self):
        """Teste 2: Verificar cálculos de horas trabalhadas"""
        print("🧪 Executando Teste 2: Cálculo de Horas")
        
        cursor = self.conn.cursor()
        
        # Buscar registros com problemas de cálculo
        cursor.execute("""
            SELECT id, funcionario_id, data, entrada, saida_almoco, retorno_almoco, saida, 
                   horas_trabalhadas, horas_extras, descontos
            FROM registro_ponto 
            WHERE data >= '2025-07-01' 
            AND (entrada IS NOT NULL OR saida IS NOT NULL)
            LIMIT 100
        """)
        registros = cursor.fetchall()
        
        problemas_calculo = 0
        for registro in registros:
            id_reg, func_id, data, entrada, saida_almoco, retorno_almoco, saida, horas_trab, horas_extras, descontos = registro
            
            # Verificar se há entrada sem saída
            if entrada and not saida:
                self.adicionar_erro_logica("CALCULO_HORAS", 
                                         f"Registro {id_reg}: Entrada sem saída correspondente", 
                                         "Horas não calculadas corretamente")
                problemas_calculo += 1
            
            # Verificar saída antes da entrada
            if entrada and saida:
                try:
                    if saida < entrada:
                        self.adicionar_bug_potencial("VALIDACAO_HORARIOS", 
                                                    f"Registro {id_reg}: Saída antes da entrada", 
                                                    f"Entrada: {entrada}, Saída: {saida}")
                        problemas_calculo += 1
                except:
                    pass
            
            # Verificar intervalos inconsistentes
            if saida_almoco and retorno_almoco:
                try:
                    if retorno_almoco < saida_almoco:
                        self.adicionar_bug_potencial("VALIDACAO_INTERVALOS", 
                                                    f"Registro {id_reg}: Retorno antes da saída para almoço", 
                                                    f"Saída almoço: {saida_almoco}, Retorno: {retorno_almoco}")
                        problemas_calculo += 1
                except:
                    pass
        
        if problemas_calculo > 0:
            self.adicionar_falha("PROBLEMAS_CALCULO", 
                               f"Encontrados {problemas_calculo} problemas de cálculo de horas", "ALTA")
        
        cursor.close()
        self.relatorio['testes_executados'].append("Cálculo de Horas")
    
    def teste_3_horas_extras_b5_b6(self):
        """Teste 3: Verificar sistema de horas extras B5/B6"""
        print("🧪 Executando Teste 3: Horas Extras B5/B6")
        
        cursor = self.conn.cursor()
        
        # Verificar registros com B5/B6
        cursor.execute("""
            SELECT id, funcionario_id, data, saida, b5, b6
            FROM registro_ponto 
            WHERE (b5 IS NOT NULL OR b6 IS NOT NULL)
            AND data >= '2025-07-01'
        """)
        registros_b5_b6 = cursor.fetchall()
        
        problemas_b5_b6 = 0
        for registro in registros_b5_b6:
            id_reg, func_id, data, saida, b5, b6 = registro
            
            # Verificar B6 sem saída normal
            if b6 and not saida:
                self.adicionar_erro_logica("HORAS_EXTRAS", 
                                         f"Registro {id_reg}: B6 sem saída normal definida", 
                                         "Cálculo de horas extras incorreto")
                problemas_b5_b6 += 1
            
            # Verificar B5 após B6
            if b5 and b6:
                try:
                    if b5 > b6:
                        self.adicionar_bug_potencial("SEQUENCIA_B5_B6", 
                                                    f"Registro {id_reg}: B5 após B6", 
                                                    f"B5: {b5}, B6: {b6}")
                        problemas_b5_b6 += 1
                except:
                    pass
        
        if problemas_b5_b6 > 0:
            self.adicionar_falha("PROBLEMAS_B5_B6", 
                               f"Encontrados {problemas_b5_b6} problemas no sistema B5/B6", "MEDIA")
        
        cursor.close()
        self.relatorio['testes_executados'].append("Horas Extras B5/B6")
    
    def teste_4_justificativas(self):
        """Teste 4: Verificar sistema de justificativas"""
        print("🧪 Executando Teste 4: Sistema de Justificativas")
        
        cursor = self.conn.cursor()
        
        # Verificar justificativas órfãs
        cursor.execute("""
            SELECT j.id, j.funcionario_id, j.data
            FROM justificativas_ponto j
            LEFT JOIN registro_ponto r ON j.funcionario_id = r.funcionario_id AND j.data = r.data
            WHERE r.id IS NULL
        """)
        justificativas_orfas = cursor.fetchall()
        
        if justificativas_orfas:
            self.adicionar_erro_logica("JUSTIFICATIVAS", 
                                     f"{len(justificativas_orfas)} justificativas sem registro de ponto correspondente", 
                                     "Inconsistência entre justificativas e registros")
        
        # Verificar justificativas pendentes há muito tempo
        cursor.execute("""
            SELECT COUNT(*) as total
            FROM justificativas_ponto 
            WHERE status = 'pendente' 
            AND data_criacao < DATE_SUB(NOW(), INTERVAL 30 DAY)
        """)
        pendentes_antigas = cursor.fetchone()[0]
        
        if pendentes_antigas > 0:
            self.adicionar_falha("JUSTIFICATIVAS_PENDENTES", 
                               f"{pendentes_antigas} justificativas pendentes há mais de 30 dias", "MEDIA")
        
        cursor.close()
        self.relatorio['testes_executados'].append("Sistema de Justificativas")
    
    def teste_5_banco_horas(self):
        """Teste 5: Verificar sistema de banco de horas"""
        print("🧪 Executando Teste 5: Banco de Horas")
        
        cursor = self.conn.cursor()
        
        # Verificar se existe tabela de banco de horas
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'controle_ponto' 
            AND table_name = 'banco_horas'
        """)
        
        if cursor.fetchone()[0] == 0:
            self.adicionar_falha("BANCO_HORAS_INEXISTENTE", 
                               "Tabela banco_horas não encontrada no banco de dados", "ALTA")
        else:
            # Verificar registros inconsistentes no banco de horas
            cursor.execute("""
                SELECT funcionario_id, SUM(horas) as total_horas
                FROM banco_horas 
                GROUP BY funcionario_id
                HAVING SUM(horas) < 0
            """)
            saldos_negativos = cursor.fetchall()
            
            if saldos_negativos:
                self.adicionar_erro_logica("BANCO_HORAS", 
                                         f"{len(saldos_negativos)} funcionários com saldo negativo no banco de horas", 
                                         "Possível erro no cálculo do banco de horas")
        
        cursor.close()
        self.relatorio['testes_executados'].append("Banco de Horas")

    def teste_6_performance_consultas(self):
        """Teste 6: Verificar performance das consultas"""
        print("🧪 Executando Teste 6: Performance das Consultas")

        cursor = self.conn.cursor()

        # Teste de consulta pesada
        import time
        start_time = time.time()

        cursor.execute("""
            SELECT f.nome, COUNT(r.id) as total_registros
            FROM funcionarios f
            LEFT JOIN registro_ponto r ON f.id = r.funcionario_id
            WHERE f.status = 'ativo'
            GROUP BY f.id, f.nome
            ORDER BY total_registros DESC
        """)
        resultados = cursor.fetchall()

        end_time = time.time()
        tempo_consulta = end_time - start_time

        if tempo_consulta > 5.0:  # Mais de 5 segundos
            self.adicionar_falha("PERFORMANCE_LENTA",
                               f"Consulta de relatório levou {tempo_consulta:.2f} segundos", "MEDIA")

        cursor.close()
        self.relatorio['testes_executados'].append("Performance das Consultas")

    def teste_7_integridade_empresas(self):
        """Teste 7: Verificar integridade das empresas"""
        print("🧪 Executando Teste 7: Integridade das Empresas")

        cursor = self.conn.cursor()

        # Verificar empresas sem funcionários ativos
        cursor.execute("""
            SELECT e.id, e.nome_empresa
            FROM empresas e
            LEFT JOIN funcionarios f ON e.id = f.empresa_id AND f.status = 'ativo'
            WHERE e.ativa = 1 AND f.id IS NULL
        """)
        empresas_sem_funcionarios = cursor.fetchall()

        if empresas_sem_funcionarios:
            self.adicionar_erro_logica("EMPRESAS_VAZIAS",
                                     f"{len(empresas_sem_funcionarios)} empresas ativas sem funcionários",
                                     "Possível inconsistência nos dados")

        # Verificar configurações de jornada inconsistentes
        cursor.execute("""
            SELECT empresa_id, COUNT(DISTINCT CONCAT(segunda_entrada, segunda_saida)) as jornadas_diferentes
            FROM horarios_trabalho
            GROUP BY empresa_id
            HAVING COUNT(DISTINCT CONCAT(segunda_entrada, segunda_saida)) > 3
        """)
        jornadas_inconsistentes = cursor.fetchall()

        if jornadas_inconsistentes:
            self.adicionar_bug_potencial("JORNADAS_INCONSISTENTES",
                                        f"{len(jornadas_inconsistentes)} empresas com muitas jornadas diferentes",
                                        "Possível falta de padronização")

        cursor.close()
        self.relatorio['testes_executados'].append("Integridade das Empresas")

    def teste_8_validacao_dados(self):
        """Teste 8: Verificar validação de dados"""
        print("🧪 Executando Teste 8: Validação de Dados")

        cursor = self.conn.cursor()

        # Verificar CPFs inválidos
        cursor.execute("""
            SELECT id, nome, cpf
            FROM funcionarios
            WHERE cpf NOT REGEXP '^[0-9]{3}\\.[0-9]{3}\\.[0-9]{3}-[0-9]{2}$'
            AND status = 'ativo'
        """)
        cpfs_invalidos = cursor.fetchall()

        if cpfs_invalidos:
            self.adicionar_falha("CPFS_INVALIDOS",
                               f"{len(cpfs_invalidos)} funcionários com CPF em formato inválido", "MEDIA")

        # Verificar emails inválidos
        cursor.execute("""
            SELECT id, nome, email
            FROM funcionarios
            WHERE email IS NOT NULL
            AND email != ''
            AND email NOT REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'
            AND status = 'ativo'
        """)
        emails_invalidos = cursor.fetchall()

        if emails_invalidos:
            self.adicionar_falha("EMAILS_INVALIDOS",
                               f"{len(emails_invalidos)} funcionários com email em formato inválido", "BAIXA")

        # Verificar datas futuras
        cursor.execute("""
            SELECT id, funcionario_id, data
            FROM registro_ponto
            WHERE data > CURDATE()
        """)
        datas_futuras = cursor.fetchall()

        if datas_futuras:
            self.adicionar_bug_potencial("DATAS_FUTURAS",
                                        f"{len(datas_futuras)} registros com datas futuras",
                                        "Possível erro na validação de datas")

        cursor.close()
        self.relatorio['testes_executados'].append("Validação de Dados")

    def gerar_estatisticas(self):
        """Gera estatísticas gerais do sistema"""
        print("📊 Gerando Estatísticas do Sistema")

        cursor = self.conn.cursor()

        # Estatísticas gerais
        cursor.execute("SELECT COUNT(*) FROM funcionarios WHERE status = 'ativo'")
        total_funcionarios = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM empresas WHERE ativa = 1")
        total_empresas = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM registro_ponto WHERE data >= '2025-07-01'")
        total_registros_mes = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM justificativas_ponto WHERE status = 'pendente'")
        justificativas_pendentes = cursor.fetchone()[0]

        self.relatorio['estatisticas'] = {
            'funcionarios_ativos': total_funcionarios,
            'empresas_ativas': total_empresas,
            'registros_mes_atual': total_registros_mes,
            'justificativas_pendentes': justificativas_pendentes,
            'total_falhas': len(self.relatorio['falhas_encontradas']),
            'total_erros_logica': len(self.relatorio['erros_logica']),
            'total_bugs_potenciais': len(self.relatorio['bugs_potenciais'])
        }

        cursor.close()

    def gerar_recomendacoes(self):
        """Gera recomendações baseadas nos problemas encontrados"""
        print("💡 Gerando Recomendações")

        recomendacoes = []

        # Recomendações baseadas nas falhas encontradas
        if any(f['categoria'] == 'FUNCIONARIOS_DUPLICADOS' for f in self.relatorio['falhas_encontradas']):
            recomendacoes.append({
                'prioridade': 'ALTA',
                'area': 'Cadastro de Funcionários',
                'recomendacao': 'Implementar validação única de CPF no cadastro',
                'acao': 'Criar constraint UNIQUE na coluna CPF da tabela funcionarios'
            })

        if any(f['categoria'] == 'PROBLEMAS_CALCULO' for f in self.relatorio['falhas_encontradas']):
            recomendacoes.append({
                'prioridade': 'ALTA',
                'area': 'Cálculo de Horas',
                'recomendacao': 'Revisar algoritmo de cálculo de horas trabalhadas',
                'acao': 'Implementar validações de consistência nos horários'
            })

        if any(f['categoria'] == 'BANCO_HORAS_INEXISTENTE' for f in self.relatorio['falhas_encontradas']):
            recomendacoes.append({
                'prioridade': 'ALTA',
                'area': 'Banco de Horas',
                'recomendacao': 'Implementar sistema completo de banco de horas',
                'acao': 'Criar tabela banco_horas e lógica de acumulação'
            })

        if len(self.relatorio['bugs_potenciais']) > 5:
            recomendacoes.append({
                'prioridade': 'MEDIA',
                'area': 'Qualidade do Sistema',
                'recomendacao': 'Implementar testes automatizados',
                'acao': 'Criar suite de testes unitários e de integração'
            })

        self.relatorio['recomendacoes'] = recomendacoes

    def executar_todos_testes(self):
        """Executa todos os testes disponíveis"""
        print("🚀 INICIANDO BATERIA COMPLETA DE TESTES")
        print("=" * 60)

        if not self.conectar_banco():
            return False

        try:
            # Executar todos os testes
            self.teste_1_integridade_funcionarios()
            self.teste_2_calculo_horas()
            self.teste_3_horas_extras_b5_b6()
            self.teste_4_justificativas()
            self.teste_5_banco_horas()
            self.teste_6_performance_consultas()
            self.teste_7_integridade_empresas()
            self.teste_8_validacao_dados()

            # Gerar estatísticas e recomendações
            self.gerar_estatisticas()
            self.gerar_recomendacoes()

            return True

        except Exception as e:
            self.adicionar_falha("ERRO_EXECUCAO", f"Erro durante execução dos testes: {e}", "CRITICA")
            return False
        finally:
            if self.conn:
                self.conn.close()

    def gerar_relatorio_html(self):
        """Gera relatório em formato HTML"""
        html = f"""
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Relatório de Testes - RLPONTO-WEB</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }}
                .section {{ margin-bottom: 30px; }}
                .section h2 {{ color: #007bff; border-left: 4px solid #007bff; padding-left: 10px; }}
                .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }}
                .stat-card {{ background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; border-left: 4px solid #28a745; }}
                .falha {{ background: #fff5f5; border-left: 4px solid #dc3545; padding: 10px; margin: 10px 0; border-radius: 4px; }}
                .erro {{ background: #fff8e1; border-left: 4px solid #ff9800; padding: 10px; margin: 10px 0; border-radius: 4px; }}
                .bug {{ background: #f3e5f5; border-left: 4px solid #9c27b0; padding: 10px; margin: 10px 0; border-radius: 4px; }}
                .recomendacao {{ background: #e8f5e8; border-left: 4px solid #4caf50; padding: 10px; margin: 10px 0; border-radius: 4px; }}
                .severidade-alta {{ border-left-color: #dc3545 !important; }}
                .severidade-media {{ border-left-color: #ff9800 !important; }}
                .severidade-baixa {{ border-left-color: #28a745 !important; }}
                .timestamp {{ font-size: 0.8em; color: #666; }}
                .resumo {{ background: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🧪 RELATÓRIO DE TESTES - RLPONTO-WEB</h1>
                    <p>Bateria Completa de Testes do Sistema</p>
                    <p><strong>Data de Execução:</strong> {self.relatorio['data_execucao']}</p>
                </div>
        """

        # Adicionar estatísticas
        stats = self.relatorio['estatisticas']
        html += f"""
                <div class="section">
                    <h2>📊 Estatísticas Gerais</h2>
                    <div class="stats">
                        <div class="stat-card">
                            <h3>{stats.get('funcionarios_ativos', 0)}</h3>
                            <p>Funcionários Ativos</p>
                        </div>
                        <div class="stat-card">
                            <h3>{stats.get('empresas_ativas', 0)}</h3>
                            <p>Empresas Ativas</p>
                        </div>
                        <div class="stat-card">
                            <h3>{stats.get('registros_mes_atual', 0)}</h3>
                            <p>Registros Este Mês</p>
                        </div>
                        <div class="stat-card">
                            <h3>{stats.get('total_falhas', 0)}</h3>
                            <p>Falhas Encontradas</p>
                        </div>
                    </div>
                </div>
        """

        # Adicionar resumo
        total_problemas = stats.get('total_falhas', 0) + stats.get('total_erros_logica', 0) + stats.get('total_bugs_potenciais', 0)
        status_sistema = "🔴 CRÍTICO" if total_problemas > 10 else "🟡 ATENÇÃO" if total_problemas > 5 else "🟢 ESTÁVEL"

        html += f"""
                <div class="resumo">
                    <h2>📋 Resumo Executivo</h2>
                    <p><strong>Status do Sistema:</strong> {status_sistema}</p>
                    <p><strong>Total de Problemas:</strong> {total_problemas}</p>
                    <p><strong>Testes Executados:</strong> {len(self.relatorio['testes_executados'])}</p>
                    <p><strong>Recomendações:</strong> {len(self.relatorio['recomendacoes'])}</p>
                </div>
        """

        # Adicionar falhas
        if self.relatorio['falhas_encontradas']:
            html += '<div class="section"><h2>❌ Falhas Encontradas</h2>'
            for falha in self.relatorio['falhas_encontradas']:
                html += f'''
                    <div class="falha severidade-{falha['severidade'].lower()}">
                        <strong>{falha['categoria']}</strong> - {falha['severidade']}
                        <p>{falha['descricao']}</p>
                        <span class="timestamp">{falha['timestamp']}</span>
                    </div>
                '''
            html += '</div>'

        # Adicionar erros de lógica
        if self.relatorio['erros_logica']:
            html += '<div class="section"><h2>⚠️ Erros de Lógica</h2>'
            for erro in self.relatorio['erros_logica']:
                html += f'''
                    <div class="erro">
                        <strong>{erro['funcionalidade']}</strong>
                        <p><strong>Problema:</strong> {erro['problema']}</p>
                        <p><strong>Impacto:</strong> {erro['impacto']}</p>
                        <span class="timestamp">{erro['timestamp']}</span>
                    </div>
                '''
            html += '</div>'

        # Adicionar bugs potenciais
        if self.relatorio['bugs_potenciais']:
            html += '<div class="section"><h2>🐛 Bugs Potenciais</h2>'
            for bug in self.relatorio['bugs_potenciais']:
                html += f'''
                    <div class="bug">
                        <strong>{bug['area']}</strong>
                        <p><strong>Descrição:</strong> {bug['descricao']}</p>
                        <p><strong>Evidência:</strong> {bug['evidencia']}</p>
                        <span class="timestamp">{bug['timestamp']}</span>
                    </div>
                '''
            html += '</div>'

        # Adicionar recomendações
        if self.relatorio['recomendacoes']:
            html += '<div class="section"><h2>💡 Recomendações</h2>'
            for rec in self.relatorio['recomendacoes']:
                html += f'''
                    <div class="recomendacao">
                        <strong>{rec['area']}</strong> - Prioridade: {rec['prioridade']}
                        <p><strong>Recomendação:</strong> {rec['recomendacao']}</p>
                        <p><strong>Ação:</strong> {rec['acao']}</p>
                    </div>
                '''
            html += '</div>'

        html += """
            </div>
        </body>
        </html>
        """

        return html

    def salvar_relatorio(self, formato='html'):
        """Salva o relatório em arquivo"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if formato == 'html':
            filename = f'relatorio_testes_{timestamp}.html'
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.gerar_relatorio_html())
        elif formato == 'json':
            filename = f'relatorio_testes_{timestamp}.json'
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.relatorio, f, indent=2, ensure_ascii=False, default=str)

        return filename

def main():
    """Função principal"""
    analisador = AnalisadorTestes()

    print("🔍 INICIANDO ANÁLISE COMPLETA DO SISTEMA RLPONTO-WEB")
    print("=" * 60)

    if analisador.executar_todos_testes():
        # Salvar relatórios
        arquivo_html = analisador.salvar_relatorio('html')
        arquivo_json = analisador.salvar_relatorio('json')

        print("\n" + "=" * 60)
        print("✅ ANÁLISE CONCLUÍDA COM SUCESSO!")
        print(f"📄 Relatório HTML: {arquivo_html}")
        print(f"📄 Relatório JSON: {arquivo_json}")

        # Mostrar resumo
        stats = analisador.relatorio['estatisticas']
        total_problemas = stats.get('total_falhas', 0) + stats.get('total_erros_logica', 0) + stats.get('total_bugs_potenciais', 0)

        print(f"\n📊 RESUMO:")
        print(f"   • Falhas Críticas: {stats.get('total_falhas', 0)}")
        print(f"   • Erros de Lógica: {stats.get('total_erros_logica', 0)}")
        print(f"   • Bugs Potenciais: {stats.get('total_bugs_potenciais', 0)}")
        print(f"   • Total de Problemas: {total_problemas}")
        print(f"   • Recomendações: {len(analisador.relatorio['recomendacoes'])}")

        if total_problemas > 10:
            print("\n🔴 STATUS: SISTEMA CRÍTICO - Ação imediata necessária!")
        elif total_problemas > 5:
            print("\n🟡 STATUS: SISTEMA REQUER ATENÇÃO - Correções recomendadas")
        else:
            print("\n🟢 STATUS: SISTEMA ESTÁVEL - Monitoramento contínuo")

    else:
        print("❌ Falha na execução dos testes!")

if __name__ == "__main__":
    main()
