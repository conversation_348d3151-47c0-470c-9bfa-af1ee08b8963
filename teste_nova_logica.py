#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste da Nova Lógica de Ponto
Data: 09/07/2025
Descrição: Script para testar as novas funcionalidades implementadas
"""

import requests
import json
from datetime import datetime, date

# Configurações
BASE_URL = "http://************"
FUNCIONARIO_ID_TESTE = 1  # ID de funcionário para teste

def testar_determinacao_turno():
    """Testa a determinação automática de turno"""
    print("=" * 50)
    print("TESTE: Determinação de Turno")
    print("=" * 50)
    
    # Simular diferentes horários
    horarios_teste = [
        "07:30:00",  # Manhã
        "15:45:00",  # Tarde
        "23:15:00",  # Noite
        "02:30:00"   # Noite (madrugada)
    ]
    
    for horario in horarios_teste:
        try:
            # Fazer requisição para endpoint de teste (se existir)
            response = requests.get(f"{BASE_URL}/api/teste/turno/{horario}")
            if response.status_code == 200:
                resultado = response.json()
                print(f"Horário: {horario} → Turno: {resultado.get('turno', 'Erro')}")
            else:
                print(f"Horário: {horario} → Erro na requisição: {response.status_code}")
        except Exception as e:
            print(f"Horário: {horario} → Erro: {e}")

def testar_registro_inteligente():
    """Testa o registro inteligente de ponto"""
    print("\n" + "=" * 50)
    print("TESTE: Registro Inteligente")
    print("=" * 50)
    
    try:
        # Dados para teste
        dados_teste = {
            'funcionario_id': FUNCIONARIO_ID_TESTE,
            'metodo_registro': 'manual',
            'observacoes': 'Teste da nova lógica'
        }
        
        # Fazer requisição para registro
        response = requests.post(f"{BASE_URL}/api/registro/inteligente", json=dados_teste)
        
        if response.status_code == 200:
            resultado = response.json()
            print("✅ Registro realizado com sucesso!")
            print(f"Tipo determinado: {resultado.get('tipo_determinado', 'N/A')}")
            print(f"Turno: {resultado.get('turno_determinado', {}).get('turno', 'N/A')}")
            print(f"Número da batida: {resultado.get('numero_batida', 'N/A')}")
            print(f"Alertas processados: {resultado.get('alertas_processados', 0)}")
        else:
            print(f"❌ Erro no registro: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Erro na requisição: {e}")

def testar_consulta_alertas():
    """Testa a consulta de alertas"""
    print("\n" + "=" * 50)
    print("TESTE: Consulta de Alertas")
    print("=" * 50)
    
    try:
        # Consultar alertas pendentes
        response = requests.get(f"{BASE_URL}/api/alertas/pendentes")
        
        if response.status_code == 200:
            alertas = response.json()
            print(f"Total de alertas pendentes: {len(alertas)}")
            
            for alerta in alertas[:5]:  # Mostrar apenas os 5 primeiros
                print(f"- {alerta.get('tipo_alerta', 'N/A')}: {alerta.get('descricao', 'N/A')}")
        else:
            print(f"❌ Erro na consulta: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erro na requisição: {e}")

def testar_inferencia_batidas():
    """Testa a inferência de batidas"""
    print("\n" + "=" * 50)
    print("TESTE: Inferência de Batidas")
    print("=" * 50)
    
    try:
        # Dados para teste
        dados_teste = {
            'funcionario_id': FUNCIONARIO_ID_TESTE,
            'data_referencia': date.today().isoformat()
        }
        
        response = requests.post(f"{BASE_URL}/api/inferencia/batidas", json=dados_teste)
        
        if response.status_code == 200:
            resultado = response.json()
            print("✅ Inferência realizada com sucesso!")
            print(f"Status: {resultado.get('status', 'N/A')}")
            print(f"Turno determinado: {resultado.get('turno', {}).get('turno', 'N/A')}")
            print(f"Batidas originais: {len(resultado.get('batidas_originais', []))}")
            print(f"Batidas inferidas: {len(resultado.get('batidas_inferidas', []))}")
            print(f"Alertas: {len(resultado.get('alertas', []))}")
            
            for alerta in resultado.get('alertas', []):
                print(f"  - {alerta}")
                
        else:
            print(f"❌ Erro na inferência: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Erro na requisição: {e}")

def verificar_tabelas_criadas():
    """Verifica se as tabelas foram criadas corretamente"""
    print("\n" + "=" * 50)
    print("TESTE: Verificação de Tabelas")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/api/sistema/status")
        
        if response.status_code == 200:
            status = response.json()
            print("✅ Sistema respondendo!")
            print(f"Versão: {status.get('versao', 'N/A')}")
            print(f"Banco conectado: {status.get('banco_conectado', False)}")
        else:
            print(f"❌ Sistema não respondeu: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erro na verificação: {e}")

def main():
    """Função principal de teste"""
    print("🚀 INICIANDO TESTES DA NOVA LÓGICA DE PONTO")
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"Servidor: {BASE_URL}")
    print(f"Funcionário de teste: {FUNCIONARIO_ID_TESTE}")
    
    # Executar testes
    verificar_tabelas_criadas()
    testar_determinacao_turno()
    testar_inferencia_batidas()
    testar_consulta_alertas()
    testar_registro_inteligente()
    
    print("\n" + "=" * 50)
    print("🏁 TESTES CONCLUÍDOS")
    print("=" * 50)
    print("\nVerifique os resultados acima para identificar possíveis problemas.")
    print("Se houver erros, verifique:")
    print("1. Se o servidor está rodando")
    print("2. Se as tabelas foram criadas")
    print("3. Se as rotas da API foram implementadas")
    print("4. Se há erros nos logs do servidor")

if __name__ == "__main__":
    main()
