#!/usr/bin/env python3
"""
Script para verificar o estado atual do funcionário Richardson
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def verificar_richardson():
    """Verificar estado atual do <PERSON>"""
    print("🎯 VERIFICANDO RICHARDSON CARDOSO RODRIGUES")
    print("=" * 50)
    
    try:
        # Buscar Richardson
        sql = "SELECT * FROM funcionarios WHERE nome_completo LIKE %s"
        funcionarios = DatabaseManager.execute_query(sql, ('%RICHARDSON%',))
        
        if not funcionarios:
            print("❌ Richardson não encontrado")
            return
            
        funcionario = funcionarios[0]
        
        print(f"👤 Nome: {funcionario['nome_completo']}")
        print(f"🆔 ID: {funcionario['id']}")
        print(f"🏢 Empresa ID: {funcionario['empresa_id']}")
        print(f"⏰ Horário Trabalho ID: {funcionario['horario_trabalho_id']}")
        print(f"📅 Seg-Qui Entrada: {funcionario.get('jornada_seg_qui_entrada', 'N/A')}")
        print(f"📅 Seg-Qui Saída: {funcionario.get('jornada_seg_qui_saida', 'N/A')}")
        print(f"📅 Sex Entrada: {funcionario.get('jornada_sex_entrada', 'N/A')}")
        print(f"📅 Sex Saída: {funcionario.get('jornada_sex_saida', 'N/A')}")
        print(f"🍽️ Intervalo Entrada: {funcionario.get('jornada_intervalo_entrada', 'N/A')}")
        print(f"🍽️ Intervalo Saída: {funcionario.get('jornada_intervalo_saida', 'N/A')}")
        
        # Verificar jornada da empresa
        print(f"\n🏢 JORNADA DA EMPRESA {funcionario['empresa_id']}:")
        sql_empresa = """
        SELECT jt.*, e.razao_social 
        FROM jornadas_trabalho jt 
        JOIN empresas e ON jt.empresa_id = e.id 
        WHERE jt.empresa_id = %s AND jt.padrao = 1
        """
        jornadas_empresa = DatabaseManager.execute_query(sql_empresa, (funcionario['empresa_id'],))
        
        if jornadas_empresa:
            jornada = jornadas_empresa[0]
            print(f"   Empresa: {jornada['razao_social']}")
            print(f"   Jornada: {jornada['nome_jornada']}")
            print(f"   ID: {jornada['id']}")
            print(f"   Seg-Qui Entrada: {jornada['seg_qui_entrada']}")
            print(f"   Seg-Qui Saída: {jornada['seg_qui_saida']}")
            print(f"   Sexta Entrada: {jornada.get('sexta_entrada', 'N/A')}")
            print(f"   Sexta Saída: {jornada.get('sexta_saida', 'N/A')}")
            
            # Comparar
            print(f"\n🔍 ANÁLISE:")
            if funcionario['horario_trabalho_id'] == jornada['id']:
                print("⚠️ PROBLEMA: Richardson está usando a jornada padrão da empresa!")
                print("   Isso indica que a jornada personalizada foi sobrescrita.")
            else:
                print("✅ OK: Richardson tem jornada específica diferente da empresa.")
        else:
            print("   ❌ Empresa não tem jornada padrão definida")
            
        # Verificar EPIs
        print(f"\n🦺 EPIs DO RICHARDSON:")
        sql_epis = "SELECT * FROM epis WHERE funcionario_id = %s"
        epis = DatabaseManager.execute_query(sql_epis, (funcionario['id'],))
        
        if epis:
            print(f"   📋 Total de EPIs: {len(epis)}")
            for i, epi in enumerate(epis, 1):
                print(f"   EPI {i}: {epi['epi_nome']} (CA: {epi['epi_ca']})")
        else:
            print("   ❌ Nenhum EPI cadastrado")
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verificar_richardson()
