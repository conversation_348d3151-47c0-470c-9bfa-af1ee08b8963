import pymysql

# Configuração de conexão
config = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

# Conectar ao banco
conn = pymysql.connect(**config)
cursor = conn.cursor(pymysql.cursors.DictCursor)

# Consultar o funcionário RICHARDSON
cursor.execute("""
    SELECT 
        id, 
        nome_completo,
        setor,
        setor_obra,
        cargo
    FROM funcionarios
    WHERE nome_completo LIKE '%RICHARDSON%'
    AND ativo = TRUE
""")

print("\n=== DADOS DO FUNCIONÁRIO ===")
funcionario = cursor.fetchone()
print(f"ID: {funcionario['id']}")
print(f"Nome: {funcionario['nome_completo']}")
print(f"Cargo: {funcionario['cargo']}")
print(f"Campo setor: {funcionario['setor'] or 'NULL'}")
print(f"Campo setor_obra: {funcionario['setor_obra'] or 'NULL'}")

# Verificar a view vw_relatorio_pontos
cursor.execute("""
    SELECT 
        id,
        nome_completo,
        setor
    FROM vw_relatorio_pontos
    WHERE nome_completo LIKE '%RICHARDSON%'
    LIMIT 1
""")

print("\n=== DADOS DA VIEW ===")
view_data = cursor.fetchone()
print(f"ID: {view_data['id']}")
print(f"Nome: {view_data['nome_completo']}")
print(f"Setor na View: {view_data['setor']}")

print("\n=== DIAGNOSTICADO ===")
if funcionario['setor_obra'] and view_data['setor'] != funcionario['setor_obra']:
    print("PROBLEMA: O setor_obra não está sendo priorizado corretamente na view!")
    print(f"setor_obra ({funcionario['setor_obra']}) deveria aparecer no lugar de setor ({view_data['setor']})")
else:
    print("Sem problemas detectados")

conn.close() 