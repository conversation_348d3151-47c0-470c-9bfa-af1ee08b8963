#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
from pymysql.cursors import DictCursor

# Conectar ao banco
conn = pymysql.connect(
    host='************',
    user='cavalcrod', 
    password='200381',
    db='controle_ponto'
)

cursor = conn.cursor(DictCursor)

# Buscar horários do Richardson
cursor.execute("""
    SELECT 
        id, nome_completo, 
        jornada_seg_qui_entrada, jornada_seg_qui_saida,
        jornada_sex_entrada, jornada_sex_saida,
        jornada_intervalo_entrada, jornada_intervalo_saida
    FROM funcionarios 
    WHERE nome_completo LIKE '%RICHARDSON%' 
    LIMIT 1
""")

result = cursor.fetchone()
print("=== HORÁRIOS CADASTRADOS NO BANCO ===")
print(f"ID: {result['id']}")
print(f"Nome: {result['nome_completo']}")
print(f"Seg-Qui Entrada: {result['jornada_seg_qui_entrada']}")
print(f"Seg-Qui <PERSON>: {result['jornada_seg_qui_saida']}")
print(f"Sex Entrada: {result['jornada_sex_entrada']}")
print(f"Sex Saída: {result['jornada_sex_saida']}")
print(f"Intervalo Entrada: {result['jornada_intervalo_entrada']}")
print(f"Intervalo Saída: {result['jornada_intervalo_saida']}")

conn.close()

print("\n=== VERIFICAÇÃO API HORÁRIOS ===")
import sys
sys.path.insert(0, '.')
from app_registro_ponto import obter_horarios_funcionario

try:
    horarios = obter_horarios_funcionario(result['id'])
    print(f"API retorna: {horarios}")
except Exception as e:
    print(f"Erro na API: {e}") 