# -*- coding: utf-8 -*-
"""
RLPONTO-WEB - Módulo de Controle de Período de Apuração
Sistema de controle de período 21-20, decisões diárias e fechamento automático

Data de Criação: 11/07/2025
Desenvolvido por: AiNexus Tecnologia
Autor: <PERSON> - Full Stack Developer
Sistema: RLPONTO-WEB v1.0
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from datetime import datetime, date, timedelta
from utils.auth import require_login, require_admin
from utils.database import get_db_connection
from pymysql.cursors import DictCursor
import logging
from functools import wraps

# Configurar logger
logger = logging.getLogger(__name__)

# Decorator personalizado para controle de período (permite admin)
def require_admin_controle(f):
    """
    Decorator para exigir nível de acesso admin para controle de período.
    Permite acesso a usuários com nível 'admin'
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'usuario' not in session:
            if request.is_json:
                return jsonify({'error': 'Autenticação necessária'}), 401
            return redirect(url_for('login'))

        nivel_acesso = session.get('nivel_acesso', 'usuario')

        # Permitir acesso para admin
        if nivel_acesso != 'admin':
            if request.is_json:
                return jsonify({'error': 'Acesso negado. Privilégios de administrador necessários para Controle de Período'}), 403
            return redirect(url_for('index'))

        return f(*args, **kwargs)
    return decorated_function

# Criar Blueprint
controle_periodo_bp = Blueprint('controle_periodo', __name__, url_prefix='/controle-periodo')

# ===== FUNÇÕES AUXILIARES =====

def calcular_periodo_apuracao(data_referencia=None):
    """
    Calcula o período de apuração (21 ao 20 do mês seguinte)
    
    Args:
        data_referencia (date, optional): Data de referência. Defaults to hoje.
    
    Returns:
        dict: Informações do período atual
    """
    if data_referencia is None:
        data_referencia = date.today()
    
    # Se estamos antes do dia 21, o período é do mês anterior
    if data_referencia.day < 21:
        # Período: 21 do mês anterior ao 20 do mês atual
        inicio_periodo = date(data_referencia.year, data_referencia.month - 1, 21)
        if data_referencia.month == 1:  # Janeiro
            inicio_periodo = date(data_referencia.year - 1, 12, 21)
        fim_periodo = date(data_referencia.year, data_referencia.month, 20)
    else:
        # Período: 21 do mês atual ao 20 do próximo mês
        inicio_periodo = date(data_referencia.year, data_referencia.month, 21)
        if data_referencia.month == 12:  # Dezembro
            fim_periodo = date(data_referencia.year + 1, 1, 20)
        else:
            fim_periodo = date(data_referencia.year, data_referencia.month + 1, 20)
    
    # Calcular dias restantes
    dias_restantes = (fim_periodo - data_referencia).days
    dias_totais = (fim_periodo - inicio_periodo).days + 1
    dias_decorridos = dias_totais - dias_restantes
    
    return {
        'inicio': inicio_periodo,
        'fim': fim_periodo,
        'dias_restantes': max(0, dias_restantes),
        'dias_totais': dias_totais,
        'dias_decorridos': dias_decorridos,
        'percentual_concluido': round((dias_decorridos / dias_totais) * 100, 1),
        'periodo_ativo': dias_restantes >= 0,
        'proximo_fechamento': fim_periodo
    }

def obter_decisoes_pendentes():
    """
    Obtém lista de decisões pendentes de classificação
    
    Returns:
        list: Lista de funcionários com horas para classificar
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        periodo = calcular_periodo_apuracao()
        
        cursor.execute("""
            SELECT 
                f.id as funcionario_id,
                f.nome_completo,
                f.setor,
                bh.data_referencia,
                bh.horas_extras_minutos,
                bh.atraso_entrada_minutos,
                bh.excesso_almoco_minutos,
                bh.saida_antecipada_minutos,
                bh.saldo_liquido_minutos,
                bh.status_dia,
                bh.observacoes,
                CASE 
                    WHEN bh.horas_extras_minutos > 0 THEN 'horas_extras'
                    WHEN bh.atraso_entrada_minutos > 0 THEN 'atraso'
                    WHEN bh.excesso_almoco_minutos > 0 THEN 'excesso_almoco'
                    WHEN bh.saida_antecipada_minutos > 0 THEN 'saida_antecipada'
                    ELSE 'normal'
                END as tipo_pendencia,
                CASE 
                    WHEN bh.horas_extras_minutos > 0 THEN bh.horas_extras_minutos
                    WHEN bh.atraso_entrada_minutos > 0 THEN bh.atraso_entrada_minutos
                    WHEN bh.excesso_almoco_minutos > 0 THEN bh.excesso_almoco_minutos
                    WHEN bh.saida_antecipada_minutos > 0 THEN bh.saida_antecipada_minutos
                    ELSE 0
                END as minutos_pendentes
            FROM banco_horas bh
            INNER JOIN funcionarios f ON bh.funcionario_id = f.id
            WHERE bh.data_referencia BETWEEN %s AND %s
            AND f.ativo = 1
            AND (
                bh.horas_extras_minutos > 0 
                OR bh.atraso_entrada_minutos > 0 
                OR bh.excesso_almoco_minutos > 0 
                OR bh.saida_antecipada_minutos > 0
            )
            AND bh.observacoes NOT LIKE '%CLASSIFICADO:%'
            ORDER BY f.nome_completo, bh.data_referencia DESC
        """, (periodo['inicio'], periodo['fim']))
        
        decisoes = list(cursor.fetchall())
        
        cursor.close()
        conn.close()
        
        return decisoes
        
    except Exception as e:
        logger.error(f"Erro ao obter decisões pendentes: {e}")
        return []

def obter_estatisticas_periodo():
    """
    Obtém estatísticas do período atual
    
    Returns:
        dict: Estatísticas do período
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        periodo = calcular_periodo_apuracao()
        
        # Estatísticas gerais
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT bh.funcionario_id) as funcionarios_ativos,
                COUNT(*) as dias_registrados,
                SUM(bh.horas_extras_minutos) as total_horas_extras,
                SUM(bh.atraso_entrada_minutos) as total_atrasos,
                SUM(CASE WHEN bh.status_dia = 'completo' THEN 1 ELSE 0 END) as dias_completos,
                SUM(CASE WHEN bh.saldo_liquido_minutos > 0 THEN bh.saldo_liquido_minutos ELSE 0 END) as saldo_positivo_total,
                SUM(CASE WHEN bh.saldo_liquido_minutos < 0 THEN ABS(bh.saldo_liquido_minutos) ELSE 0 END) as saldo_negativo_total
            FROM banco_horas bh
            INNER JOIN funcionarios f ON bh.funcionario_id = f.id
            WHERE bh.data_referencia BETWEEN %s AND %s
            AND f.ativo = 1
        """, (periodo['inicio'], periodo['fim']))
        
        stats = cursor.fetchone()
        
        # Decisões pendentes
        decisoes_pendentes = len(obter_decisoes_pendentes())
        
        cursor.close()
        conn.close()
        
        return {
            'funcionarios_ativos': stats['funcionarios_ativos'] or 0,
            'dias_registrados': stats['dias_registrados'] or 0,
            'total_horas_extras': stats['total_horas_extras'] or 0,
            'total_atrasos': stats['total_atrasos'] or 0,
            'dias_completos': stats['dias_completos'] or 0,
            'saldo_positivo_total': stats['saldo_positivo_total'] or 0,
            'saldo_negativo_total': stats['saldo_negativo_total'] or 0,
            'decisoes_pendentes': decisoes_pendentes,
            'horas_extras_formatadas': f"{(stats['total_horas_extras'] or 0) // 60}h {(stats['total_horas_extras'] or 0) % 60}min",
            'atrasos_formatados': f"{(stats['total_atrasos'] or 0) // 60}h {(stats['total_atrasos'] or 0) % 60}min"
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter estatísticas: {e}")
        return {
            'funcionarios_ativos': 0,
            'dias_registrados': 0,
            'total_horas_extras': 0,
            'total_atrasos': 0,
            'dias_completos': 0,
            'saldo_positivo_total': 0,
            'saldo_negativo_total': 0,
            'decisoes_pendentes': 0,
            'horas_extras_formatadas': '0h 0min',
            'atrasos_formatados': '0h 0min'
        }

# ===== ROTAS =====

@controle_periodo_bp.route('/test')
def test_simple():
    """Rota de teste sem autenticação"""
    return "<h1>✅ TESTE OK - Controle de Período Funcionando!</h1>"

@controle_periodo_bp.route('/debug')
@require_admin_controle
def debug_dashboard():
    """Rota de debug simplificada"""
    try:
        from flask import session
        usuario = session.get('usuario', 'Não logado')
        nivel = session.get('nivel_acesso', 'Sem nível')

        return f"""
        <h1>DEBUG - Controle de Período</h1>
        <p>Usuário: {usuario}</p>
        <p>Nível: {nivel}</p>
        <p>Sessão: {dict(session)}</p>
        <p>Status: ✅ Funcionando!</p>
        <a href="/controle-periodo/">Ir para Dashboard</a>
        """
    except Exception as e:
        return f"Erro no debug: {e}"

@controle_periodo_bp.route('/')
@require_admin_controle
def dashboard():
    """Dashboard principal do controle de período"""
    try:
        logger.info("📊 CARREGANDO DASHBOARD DE CONTROLE DE PERÍODO")

        # Obter dados do período atual
        periodo = calcular_periodo_apuracao()

        # Obter estatísticas
        estatisticas = obter_estatisticas_periodo()

        # Obter decisões pendentes (primeiras 10 para o dashboard) com tratamento de erro
        try:
            decisoes_pendentes = obter_decisoes_pendentes()[:10]
        except Exception as e:
            logger.error(f"Erro ao obter decisões pendentes: {e}")
            decisoes_pendentes = []

        # Preparar contexto
        context = {
            'titulo': 'Controle de Período de Apuração',
            'periodo': periodo,
            'estatisticas': estatisticas,
            'decisoes_pendentes': decisoes_pendentes,
            'data_atual': date.today(),
            'nivel_acesso': session.get('nivel_acesso', 'usuario')
        }

        logger.info("✅ DASHBOARD CARREGADO COM SUCESSO")
        return render_template('controle_periodo/dashboard.html', **context)
        
    except Exception as e:
        logger.error(f"Erro no dashboard de controle de período: {e}")
        import traceback
        traceback.print_exc()

        # Retornar resposta HTML simples em caso de erro
        return f"""
        <h1>❌ Erro no Controle de Período</h1>
        <p><strong>Erro:</strong> {str(e)}</p>
        <p><strong>Tipo:</strong> {type(e).__name__}</p>
        <p><strong>ID:</strong> {datetime.now().strftime('%Y%m%d_%H%M%S')}</p>
        <hr>
        <a href="/logout">🚪 Logout</a> |
        <a href="/">🏠 Início</a> |
        <a href="/controle-periodo/debug">🔧 Debug</a>
        """, 500

@controle_periodo_bp.route('/decisoes')
@require_admin_controle
def decisoes_diarias():
    """Página de decisões diárias de classificação"""
    try:
        logger.info("📋 CARREGANDO DECISÕES DIÁRIAS")

        # Obter todas as decisões pendentes
        decisoes_pendentes = obter_decisoes_pendentes()

        # Agrupar por funcionário
        funcionarios_decisoes = {}
        for decisao in decisoes_pendentes:
            func_id = decisao['funcionario_id']
            if func_id not in funcionarios_decisoes:
                funcionarios_decisoes[func_id] = {
                    'funcionario': {
                        'id': decisao['funcionario_id'],
                        'nome': decisao['nome_completo'],
                        'setor': decisao['setor']
                    },
                    'decisoes': []
                }
            funcionarios_decisoes[func_id]['decisoes'].append(decisao)

        context = {
            'titulo': 'Decisões Diárias de Classificação',
            'funcionarios_decisoes': funcionarios_decisoes,
            'total_decisoes': len(decisoes_pendentes),
            'total_funcionarios': len(funcionarios_decisoes)
        }

        return render_template('controle_periodo/decisoes_diarias.html', **context)

    except Exception as e:
        logger.error(f"Erro nas decisões diárias: {e}")
        return redirect(url_for('controle_periodo.dashboard'))

@controle_periodo_bp.route('/classificar', methods=['POST'])
@require_admin_controle
def classificar_horas():
    """Classifica horas como banco, pagamento ou desconto"""
    try:
        data = request.get_json()
        funcionario_id = data.get('funcionario_id')
        data_referencia = data.get('data_referencia')
        tipo_classificacao = data.get('tipo_classificacao')  # 'banco', 'pagamento', 'desconto'
        tipo_pendencia = data.get('tipo_pendencia')  # 'horas_extras', 'atraso', etc.
        observacoes = data.get('observacoes', '')

        if not all([funcionario_id, data_referencia, tipo_classificacao, tipo_pendencia]):
            return jsonify({'success': False, 'message': 'Dados incompletos'})

        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)

        # Buscar registro atual
        cursor.execute("""
            SELECT * FROM banco_horas
            WHERE funcionario_id = %s AND data_referencia = %s
        """, (funcionario_id, data_referencia))

        registro = cursor.fetchone()
        if not registro:
            return jsonify({'success': False, 'message': 'Registro não encontrado'})

        # Preparar observação de classificação
        usuario_classificacao = session.get('username', 'Sistema')
        timestamp_classificacao = datetime.now().strftime('%d/%m/%Y %H:%M')
        nova_observacao = f"CLASSIFICADO: {tipo_classificacao.upper()} - {tipo_pendencia} - {usuario_classificacao} em {timestamp_classificacao}"

        if observacoes:
            nova_observacao += f" - Obs: {observacoes}"

        # Atualizar observações
        observacoes_atuais = registro.get('observacoes', '') or ''
        if observacoes_atuais:
            observacoes_finais = f"{observacoes_atuais}; {nova_observacao}"
        else:
            observacoes_finais = nova_observacao

        # Atualizar registro
        cursor.execute("""
            UPDATE banco_horas
            SET observacoes = %s,
                atualizado_em = CURRENT_TIMESTAMP
            WHERE funcionario_id = %s AND data_referencia = %s
        """, (observacoes_finais, funcionario_id, data_referencia))

        conn.commit()
        cursor.close()
        conn.close()

        logger.info(f"✅ CLASSIFICAÇÃO: {tipo_classificacao} para funcionário {funcionario_id} em {data_referencia}")

        return jsonify({
            'success': True,
            'message': f'Horas classificadas como {tipo_classificacao} com sucesso!'
        })

    except Exception as e:
        logger.error(f"Erro ao classificar horas: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do servidor'})

@controle_periodo_bp.route('/fechamento')
@require_admin
def fechamento_periodo():
    """Página de fechamento do período"""
    try:
        logger.info("🔒 CARREGANDO FECHAMENTO DE PERÍODO")

        periodo = calcular_periodo_apuracao()
        estatisticas = obter_estatisticas_periodo()

        # Verificar se há pendências
        decisoes_pendentes = len(obter_decisoes_pendentes())
        pode_fechar = decisoes_pendentes == 0 and periodo['dias_restantes'] <= 0

        context = {
            'titulo': 'Fechamento do Período',
            'periodo': periodo,
            'estatisticas': estatisticas,
            'decisoes_pendentes': decisoes_pendentes,
            'pode_fechar': pode_fechar
        }

        return render_template('controle_periodo/fechamento_periodo.html', **context)

    except Exception as e:
        logger.error(f"Erro no fechamento de período: {e}")
        return redirect(url_for('controle_periodo.dashboard'))

@controle_periodo_bp.route('/executar-fechamento', methods=['POST'])
@require_admin
def executar_fechamento():
    """Executa o fechamento do período atual"""
    try:
        data = request.get_json()
        confirmar_fechamento = data.get('confirmar', False)

        if not confirmar_fechamento:
            return jsonify({'success': False, 'message': 'Confirmação necessária'})

        periodo = calcular_periodo_apuracao()

        # Verificar se pode fechar
        decisoes_pendentes = len(obter_decisoes_pendentes())
        if decisoes_pendentes > 0:
            return jsonify({
                'success': False,
                'message': f'Ainda há {decisoes_pendentes} decisões pendentes'
            })

        if periodo['dias_restantes'] > 0:
            return jsonify({
                'success': False,
                'message': f'Período ainda não terminou. Restam {periodo["dias_restantes"]} dias'
            })

        conn = get_db_connection()
        cursor = conn.cursor()

        # Criar registro de fechamento (implementação futura)
        # Por enquanto, apenas marcar como processado

        usuario_fechamento = session.get('username', 'Sistema')
        timestamp_fechamento = datetime.now()

        # Atualizar todos os registros do período como fechados
        cursor.execute("""
            UPDATE banco_horas
            SET observacoes = CONCAT(
                COALESCE(observacoes, ''),
                '; PERÍODO FECHADO em ', %s, ' por ', %s
            )
            WHERE data_referencia BETWEEN %s AND %s
        """, (
            timestamp_fechamento.strftime('%d/%m/%Y %H:%M'),
            usuario_fechamento,
            periodo['inicio'],
            periodo['fim']
        ))

        registros_afetados = cursor.rowcount

        conn.commit()
        cursor.close()
        conn.close()

        logger.info(f"✅ FECHAMENTO EXECUTADO: Período {periodo['inicio']} a {periodo['fim']} - {registros_afetados} registros")

        return jsonify({
            'success': True,
            'message': f'Período fechado com sucesso! {registros_afetados} registros processados.',
            'registros_processados': registros_afetados
        })

    except Exception as e:
        logger.error(f"Erro ao executar fechamento: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do servidor'})
