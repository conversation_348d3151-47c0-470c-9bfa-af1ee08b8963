{% extends "base.html" %}

{% block title %}Gestão de Clientes{% if empresa_principal %} - {{ empresa_principal.razao_social }}{% endif %}{% endblock %}

{% block extra_css %}
<style>
    .cliente-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
        background: white;
    }
    
    .cliente-card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 15px rgba(0,123,255,0.1);
        transform: translateY(-2px);
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-ativo {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-pausado {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-finalizado {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-cancelado {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    
    .empresa-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .stats-mini {
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        margin: 0 5px;
    }
    
    .btn-action {
        margin: 2px;
        padding: 5px 10px;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header da Empresa Principal -->
    <div class="empresa-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-crown"></i> {% if empresa_principal %}{{ empresa_principal.razao_social }}{% else %}Empresa Principal{% endif %}</h2>
                <p class="mb-0">Gestão de Empresas Clientes</p>
            </div>
            <div class="col-md-4">
                <div class="row">
                    <div class="col-4">
                        <div class="stats-mini">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.total_clientes }}</div>
                            <small>Total</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-mini">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.clientes_ativos }}</div>
                            <small>Ativos</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-mini">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.funcionarios_alocados }}</div>
                            <small>Funcionários</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ações e Filtros -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h4><i class="fas fa-building"></i> Clientes Cadastrados</h4>
        </div>
        <div class="col-md-6 text-right">
            <button class="btn btn-success" onclick="mostrarModalAdicionarCliente()">
                <i class="fas fa-plus"></i> Adicionar Cliente
            </button>
            <a href="{{ url_for('empresa_principal.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar ao Dashboard
            </a>
        </div>
    </div>

    <!-- Filtros -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label>Status do Contrato:</label>
                    <select class="form-control" id="filtroStatus" onchange="filtrarClientes()">
                        <option value="">Todos</option>
                        <option value="ativo">Ativo</option>
                        <option value="pausado">Pausado</option>
                        <option value="finalizado">Finalizado</option>
                        <option value="cancelado">Cancelado</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label>Buscar por Nome:</label>
                    <input type="text" class="form-control" id="buscarNome" placeholder="Digite o nome..." onkeyup="filtrarClientes()">
                </div>
                <div class="col-md-3">
                    <label>Ordenar por:</label>
                    <select class="form-control" id="ordenacao" onchange="filtrarClientes()">
                        <option value="nome">Nome</option>
                        <option value="data_inicio">Data de Início</option>
                        <option value="funcionarios">Funcionários</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label>&nbsp;</label><br>
                    <button class="btn btn-outline-secondary" onclick="limparFiltros()">
                        <i class="fas fa-eraser"></i> Limpar Filtros
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de Clientes -->
    <div id="listaClientes">
        {% if clientes %}
            {% for cliente in clientes %}
            <div class="cliente-card" data-status="{{ cliente.status_contrato }}" data-nome="{{ cliente.razao_social.lower() }}">
                <div class="row">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-1">{{ cliente.razao_social }}</h5>
                                {% if cliente.nome_fantasia %}
                                    <p class="text-muted mb-1">{{ cliente.nome_fantasia }}</p>
                                {% endif %}
                                <small><strong>CNPJ:</strong> {{ cliente.cnpj }}</small>
                            </div>
                            <div class="col-md-6">
                                {% if cliente.nome_contrato %}
                                    <p class="mb-1"><strong>Contrato:</strong> {{ cliente.nome_contrato }}</p>
                                {% endif %}
                                {% if cliente.codigo_contrato %}
                                    <p class="mb-1"><strong>Código:</strong> {{ cliente.codigo_contrato }}</p>
                                {% endif %}
                                <p class="mb-1"><strong>Início:</strong> {{ cliente.data_inicio.strftime('%d/%m/%Y') if cliente.data_inicio else 'N/A' }}</p>
                                {% if cliente.data_fim %}
                                    <p class="mb-1"><strong>Fim:</strong> {{ cliente.data_fim.strftime('%d/%m/%Y') }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="mb-2">
                            <span class="status-badge status-{{ cliente.status_contrato }}">
                                {{ cliente.status_contrato.title() }}
                            </span>
                        </div>
                        <div class="mb-2">
                            <small><strong>Funcionários Alocados:</strong> {{ cliente.funcionarios_alocados }}</small><br>
                            <small><strong>Jornadas Disponíveis:</strong> {{ cliente.jornadas_disponiveis }}</small>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-primary btn-action" onclick="verDetalhesCliente({{ cliente.id }})">
                                <i class="fas fa-eye"></i> Detalhes
                            </button>
                            <button class="btn btn-sm btn-success btn-action" onclick="alocarFuncionario({{ cliente.empresa_cliente_id }})">
                                <i class="fas fa-user-plus"></i> Alocar
                            </button>
                            <button class="btn btn-sm btn-warning btn-action" onclick="editarCliente({{ cliente.id }})">
                                <i class="fas fa-edit"></i> Editar
                            </button>
                            {% if cliente.status_contrato == 'ativo' %}
                                <button class="btn btn-sm btn-secondary btn-action" onclick="pausarContrato({{ cliente.id }})">
                                    <i class="fas fa-pause"></i> Pausar
                                </button>
                            {% elif cliente.status_contrato == 'pausado' %}
                                <button class="btn btn-sm btn-success btn-action" onclick="reativarContrato({{ cliente.id }})">
                                    <i class="fas fa-play"></i> Reativar
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">Nenhum cliente cadastrado</h4>
                <p class="text-muted">Use o botão "Adicionar Cliente" acima para começar a gerenciar contratos e funcionários.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal Adicionar Cliente -->
<div class="modal fade" id="modalAdicionarCliente" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Adicionar Novo Cliente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formAdicionarCliente" onsubmit="adicionarCliente(event)">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Empresa Cliente *</label>
                                <select class="form-control" name="empresa_cliente_id" required>
                                    <option value="">Selecione uma empresa...</option>
                                    {% for empresa in empresas_disponiveis %}
                                        <option value="{{ empresa.id }}">{{ empresa.razao_social }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nome do Contrato</label>
                                <input type="text" class="form-control" name="nome_contrato" placeholder="Ex: Prestação de Serviços 2025">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Código do Contrato</label>
                                <input type="text" class="form-control" name="codigo_contrato" placeholder="Ex: CONT-2025-001">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Valor do Contrato</label>
                                <input type="number" class="form-control" name="valor_contrato" step="0.01" placeholder="0.00">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Início *</label>
                                <input type="date" class="form-control" name="data_inicio" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Fim</label>
                                <input type="date" class="form-control" name="data_fim">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Descrição do Projeto</label>
                        <textarea class="form-control" name="descricao_projeto" rows="3" placeholder="Descreva o projeto ou serviços a serem prestados..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Observações</label>
                        <textarea class="form-control" name="observacoes" rows="2" placeholder="Observações adicionais..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Adicionar Cliente
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Alocar Funcionário -->
<div class="modal fade" id="modalAlocarFuncionario" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-user-plus"></i> Alocar Funcionário</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formAlocarFuncionario" onsubmit="confirmarAlocacao(event)">
                <div class="modal-body">
                    <input type="hidden" name="empresa_cliente_id" id="empresaClienteId">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Funcionário *</label>
                                <select class="form-control" name="funcionario_id" id="funcionarioSelect" required>
                                    <option value="">Carregando funcionários...</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Jornada de Trabalho *</label>
                                <select class="form-control" name="jornada_trabalho_id" id="jornadaSelect" required>
                                    <option value="">Carregando jornadas...</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Cargo no Cliente</label>
                                <input type="text" class="form-control" name="cargo_no_cliente" placeholder="Ex: Analista de Sistemas">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Percentual de Alocação (%)</label>
                                <input type="number" class="form-control" name="percentual_alocacao" value="100" min="1" max="100">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Início *</label>
                                <input type="date" class="form-control" name="data_inicio" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Fim</label>
                                <input type="date" class="form-control" name="data_fim">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Valor da Hora (R$)</label>
                        <input type="number" class="form-control" name="valor_hora" step="0.01" placeholder="0.00">
                    </div>

                    <div class="form-group">
                        <label>Observações</label>
                        <textarea class="form-control" name="observacoes" rows="2" placeholder="Observações sobre a alocação..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> Alocar Funcionário
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Detalhes do Cliente -->
<div class="modal fade" id="modalDetalhesCliente" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-building"></i> Detalhes do Cliente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="detalhesClienteContent">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p>Carregando detalhes...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Editar Cliente -->
<div class="modal fade" id="modalEditarCliente" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Editar Cliente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formEditarCliente" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Empresa Cliente *</label>
                                <input type="text" class="form-control" id="editEmpresaNome" readonly>
                                <input type="hidden" id="editClienteId" name="cliente_id">
                                <input type="hidden" id="editEmpresaClienteId" name="empresa_cliente_id">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nome do Contrato</label>
                                <input type="text" class="form-control" id="editNomeContrato" name="nome_contrato" placeholder="Ex: Prestação de Serviços 2025">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Código do Contrato</label>
                                <input type="text" class="form-control" id="editCodigoContrato" name="codigo_contrato" placeholder="Ex: CONT-2025-001">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Valor do Contrato</label>
                                <input type="number" class="form-control" id="editValorContrato" name="valor_contrato" step="0.01" placeholder="0.00">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Início *</label>
                                <input type="date" class="form-control" id="editDataInicio" name="data_inicio" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Fim</label>
                                <input type="date" class="form-control" id="editDataFim" name="data_fim">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Status do Contrato</label>
                                <select class="form-control" id="editStatusContrato" name="status_contrato">
                                    <option value="ativo">Ativo</option>
                                    <option value="pausado">Pausado</option>
                                    <option value="finalizado">Finalizado</option>
                                    <option value="cancelado">Cancelado</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Descrição do Projeto</label>
                        <textarea class="form-control" id="editDescricaoProjeto" name="descricao_projeto" rows="3" placeholder="Descreva o projeto ou serviços a serem prestados..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Observações</label>
                        <textarea class="form-control" id="editObservacoes" name="observacoes" rows="3" placeholder="Observações adicionais..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Salvar Alterações
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function mostrarModalAdicionarCliente() {
    const modal = new bootstrap.Modal(document.getElementById('modalAdicionarCliente'));
    modal.show();
}

function adicionarCliente(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    
    fetch('{{ url_for("empresa_principal.adicionar_cliente_route") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalAdicionarCliente'));
            modal.hide();
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao adicionar cliente');
    });
}

function filtrarClientes() {
    const status = document.getElementById('filtroStatus').value.toLowerCase();
    const nome = document.getElementById('buscarNome').value.toLowerCase();
    const cards = document.querySelectorAll('.cliente-card');
    
    cards.forEach(card => {
        const cardStatus = card.getAttribute('data-status');
        const cardNome = card.getAttribute('data-nome');
        
        const statusMatch = !status || cardStatus === status;
        const nomeMatch = !nome || cardNome.includes(nome);
        
        if (statusMatch && nomeMatch) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function limparFiltros() {
    document.getElementById('filtroStatus').value = '';
    document.getElementById('buscarNome').value = '';
    document.getElementById('ordenacao').value = 'nome';
    filtrarClientes();
}

function verDetalhesCliente(clienteId) {
    const modal = new bootstrap.Modal(document.getElementById('modalDetalhesCliente'));
    modal.show();

    fetch(`{{ url_for("empresa_principal.detalhes_cliente", cliente_id=0) }}`.replace('0', clienteId))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('detalhesClienteContent').innerHTML = data.html;
            } else {
                document.getElementById('detalhesClienteContent').innerHTML =
                    '<div class="alert alert-danger">Erro ao carregar detalhes: ' + data.message + '</div>';
            }
        })
        .catch(error => {
            document.getElementById('detalhesClienteContent').innerHTML =
                '<div class="alert alert-danger">Erro ao carregar detalhes</div>';
        });
}

function alocarFuncionario(empresaId) {
    document.getElementById('empresaClienteId').value = empresaId;

    // Carregar funcionários disponíveis
    fetch('{{ url_for("empresa_principal.get_funcionarios_disponiveis_api") }}')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('funcionarioSelect');
            select.innerHTML = '<option value="">Selecione um funcionário...</option>';

            data.funcionarios.forEach(func => {
                const option = document.createElement('option');
                option.value = func.id;
                option.textContent = `${func.nome_completo} - ${func.cargo}`;
                select.appendChild(option);
            });
        });

    // Carregar jornadas disponíveis
    fetch('{{ url_for("empresa_principal.get_jornadas_disponiveis") }}')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('jornadaSelect');
            select.innerHTML = '<option value="">Selecione uma jornada...</option>';

            data.jornadas.forEach(jornada => {
                const option = document.createElement('option');
                option.value = jornada.id;
                option.textContent = `${jornada.nome} (${jornada.empresa_nome}) - ${jornada.carga_horaria}`;
                select.appendChild(option);
            });
        });

    const modal = new bootstrap.Modal(document.getElementById('modalAlocarFuncionario'));
    modal.show();
}

function editarCliente(clienteId) {
    // Buscar dados do cliente
    fetch(`{{ url_for("empresa_principal.get_cliente_dados", cliente_id=0) }}`.replace('0', clienteId))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const cliente = data.cliente;

                // Preencher o formulário
                document.getElementById('editClienteId').value = clienteId;
                document.getElementById('editEmpresaClienteId').value = cliente.empresa_cliente_id;
                document.getElementById('editEmpresaNome').value = cliente.razao_social;
                document.getElementById('editNomeContrato').value = cliente.nome_contrato || '';
                document.getElementById('editCodigoContrato').value = cliente.codigo_contrato || '';
                document.getElementById('editValorContrato').value = cliente.valor_contrato || '';
                document.getElementById('editDataInicio').value = cliente.data_inicio || '';
                document.getElementById('editDataFim').value = cliente.data_fim || '';
                document.getElementById('editStatusContrato').value = cliente.status_contrato || 'ativo';
                document.getElementById('editDescricaoProjeto').value = cliente.descricao_projeto || '';
                document.getElementById('editObservacoes').value = cliente.observacoes || '';

                // Mostrar modal
                const modal = new bootstrap.Modal(document.getElementById('modalEditarCliente'));
                modal.show();
            } else {
                alert('Erro ao carregar dados do cliente: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao carregar dados do cliente');
        });
}

function pausarContrato(clienteId) {
    // Primeiro, verificar o status atual
    fetch(`{{ url_for("empresa_principal.get_cliente_dados", cliente_id=0) }}`.replace('0', clienteId))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const statusAtual = data.cliente.status_contrato;
                let novoStatus, mensagem, acao;

                if (statusAtual === 'ativo') {
                    novoStatus = 'pausado';
                    mensagem = 'Deseja pausar este contrato?';
                    acao = 'pausar';
                } else if (statusAtual === 'pausado') {
                    novoStatus = 'ativo';
                    mensagem = 'Deseja reativar este contrato?';
                    acao = 'reativar';
                } else {
                    alert('Este contrato não pode ser pausado/reativado. Status atual: ' + statusAtual);
                    return;
                }

                if (confirm(mensagem)) {
                    alterarStatusContrato(clienteId, novoStatus);
                }
            } else {
                alert('Erro ao verificar status do contrato');
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao verificar status do contrato');
        });
}

function reativarContrato(clienteId) {
    if (confirm('Deseja reativar este contrato?')) {
        alterarStatusContrato(clienteId, 'ativo');
    }
}

function confirmarAlocacao(event) {
    event.preventDefault();

    const formData = new FormData(event.target);

    fetch('{{ url_for("empresa_principal.alocar_funcionario_route") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalAlocarFuncionario'));
            modal.hide();
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao alocar funcionário');
    });
}

function alterarStatusContrato(clienteId, novoStatus) {
    fetch(`{{ url_for("empresa_principal.alterar_status_contrato", cliente_id=0) }}`.replace('0', clienteId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: novoStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            filtrarClientes(); // Recarregar lista
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao alterar status do contrato');
    });
}

function editarClienteSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const clienteId = formData.get('cliente_id');

    fetch(`{{ url_for("empresa_principal.editar_cliente", cliente_id=0) }}`.replace('0', clienteId), {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Cliente editado com sucesso!');
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalEditarCliente'));
            modal.hide();
            filtrarClientes(); // Recarregar lista
        } else {
            alert('Erro ao editar cliente: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao editar cliente');
    });
}

// Event listeners e Animações
document.addEventListener('DOMContentLoaded', function() {
    // Event listeners
    document.getElementById('formAdicionarCliente').addEventListener('submit', adicionarCliente);
    document.getElementById('formAlocarFuncionario').addEventListener('submit', alocarFuncionario);
    document.getElementById('formEditarCliente').addEventListener('submit', editarClienteSubmit);

    // Carregar clientes ao carregar a página
    filtrarClientes();

    // Animações
    const cards = document.querySelectorAll('.cliente-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
