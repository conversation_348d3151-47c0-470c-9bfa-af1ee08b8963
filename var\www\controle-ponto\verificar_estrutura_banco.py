import pymysql
from pymysql.cursors import DictCursor

def verificar_estrutura_banco():
    try:
        # Conectar ao banco de dados
        conn = pymysql.connect(
            host='************',
            port=3306,
            user='cavalcrod',
            password='200381',
            database='controle_ponto',
            cursorclass=DictCursor
        )
        
        cursor = conn.cursor()
        
        # Verificar tabelas existentes
        cursor.execute("SHOW TABLES")
        tabelas = cursor.fetchall()
        print("\nTabelas existentes:")
        for tabela in tabelas:
            nome_tabela = list(tabela.values())[0]
            print(f"\n=== {nome_tabela} ===")
            
            # Verificar estrutura da tabela
            cursor.execute(f"DESCRIBE {nome_tabela}")
            colunas = cursor.fetchall()
            for coluna in colunas:
                print(f"- {coluna['Field']}: {coluna['Type']}")
            
            # Verificar quantidade de registros
            cursor.execute(f"SELECT COUNT(*) as total FROM {nome_tabela}")
            total = cursor.fetchone()['total']
            print(f"Total de registros: {total}")
            
        # Verificar views existentes
        cursor.execute("SHOW FULL TABLES WHERE Table_type = 'VIEW'")
        views = cursor.fetchall()
        print("\nViews existentes:")
        for view in views:
            print(f"- {list(view.values())[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"Erro ao verificar estrutura do banco: {str(e)}")

if __name__ == "__main__":
    verificar_estrutura_banco() 