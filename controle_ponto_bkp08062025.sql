﻿--
-- Script was generated by Devart dbForge Studio for MySQL, Version **********
-- Product home page: http://www.devart.com/dbforge/mysql/studio
-- Script date 06/06/2025 22:01:49
-- Server version: 8.0.42
--

--
-- Disable foreign keys
--
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;

--
-- Set SQL mode
--
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;

--
-- Set character set the client will use to send SQL statements to the server
--
SET NAMES 'utf8';

DROP DATABASE IF EXISTS controle_ponto;

CREATE DATABASE IF NOT EXISTS controle_ponto
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

--
-- Set default database
--
USE controle_ponto;

--
-- Create table `funcionarios`
--
CREATE TABLE IF NOT EXISTS funcionarios (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  empresa_id int DEFAULT NULL,
  horario_trabalho_id int DEFAULT NULL,
  nome_completo varchar(100) NOT NULL,
  cpf varchar(14) NOT NULL,
  rg varchar(20) NOT NULL,
  data_nascimento date NOT NULL,
  sexo enum ('M', 'F', 'Outro') NOT NULL,
  estado_civil enum ('Solteiro', 'Casado', 'Divorciado', 'Viuvo') NOT NULL,
  nacionalidade varchar(50) NOT NULL DEFAULT 'Brasileiro',
  ctps_numero varchar(20) NOT NULL,
  ctps_serie_uf varchar(20) NOT NULL,
  pis_pasep varchar(20) NOT NULL,
  endereco_rua varchar(100) DEFAULT NULL,
  endereco_bairro varchar(50) DEFAULT NULL,
  endereco_cidade varchar(50) DEFAULT NULL,
  endereco_cep varchar(10) NOT NULL,
  endereco_estado varchar(2) NOT NULL,
  telefone1 varchar(15) NOT NULL,
  telefone2 varchar(15) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  cargo varchar(50) NOT NULL,
  setor varchar(50) DEFAULT NULL,
  setor_obra varchar(50) NOT NULL,
  empresa varchar(100) DEFAULT 'Empresa Principal',
  matricula_empresa varchar(20) NOT NULL,
  data_admissao date NOT NULL,
  tipo_contrato enum ('CLT', 'PJ', 'Estagio', 'Temporario') NOT NULL,
  jornada_seg_qui_entrada time DEFAULT NULL,
  jornada_seg_qui_saida time DEFAULT NULL,
  jornada_sex_entrada time DEFAULT NULL,
  jornada_sex_saida time DEFAULT NULL,
  jornada_intervalo_entrada time DEFAULT NULL,
  jornada_intervalo_saida time DEFAULT NULL,
  digital_dedo1 longblob DEFAULT NULL COMMENT 'Template biométrico dedo 1',
  digital_dedo2 longblob DEFAULT NULL COMMENT 'Template biométrico dedo 2',
  foto_3x4 varchar(255) DEFAULT NULL COMMENT 'Caminho para foto 3x4',
  foto_url varchar(255) DEFAULT NULL COMMENT 'URL da foto para templates',
  nivel_acesso enum ('Funcionario', 'Supervisao', 'Gerencia') NOT NULL DEFAULT 'Funcionario',
  turno enum ('Diurno', 'Noturno', 'Misto') NOT NULL DEFAULT 'Diurno',
  tolerancia_ponto int UNSIGNED NOT NULL DEFAULT 5 COMMENT 'Tolerância em minutos',
  banco_horas tinyint(1) DEFAULT 0,
  hora_extra tinyint(1) DEFAULT 0,
  ativo tinyint(1) DEFAULT 1 COMMENT 'Compatibilidade com código existente',
  status_cadastro enum ('Ativo', 'Inativo') NOT NULL DEFAULT 'Ativo',
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  biometria_qualidade_1 tinyint UNSIGNED DEFAULT NULL COMMENT 'Qualidade da captura do dedo 1 (0-100)',
  biometria_qualidade_2 tinyint UNSIGNED DEFAULT NULL COMMENT 'Qualidade da captura do dedo 2 (0-100)',
  biometria_data_cadastro timestamp NULL DEFAULT NULL,
  horario_entrada_manha time GENERATED ALWAYS AS (`jornada_seg_qui_entrada`) VIRTUAL,
  horario_saida_almoco time GENERATED ALWAYS AS (`jornada_intervalo_entrada`) VIRTUAL,
  horario_entrada_tarde time GENERATED ALWAYS AS (`jornada_intervalo_saida`) VIRTUAL,
  horario_saida time GENERATED ALWAYS AS (`jornada_seg_qui_saida`) VIRTUAL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 6,
AVG_ROW_LENGTH = 3276,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Funcionários com dados biométricos e jornada de trabalho',
ROW_FORMAT = DYNAMIC;

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_cpf_format CHECK (regexp_like(`cpf`, _utf8mb4 '^[0-9]{3}\\.[0-9]{3}\\.[0-9]{3}-[0-9]{2}$'));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_email_format CHECK ((`email` IS NULL) OR regexp_like(`email`, _utf8mb4 '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_qualidade_range_1 CHECK ((`biometria_qualidade_1` IS NULL) OR (`biometria_qualidade_1` BETWEEN 0 AND 100));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_qualidade_range_2 CHECK ((`biometria_qualidade_2` IS NULL) OR (`biometria_qualidade_2` BETWEEN 0 AND 100));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_tolerancia_range CHECK (`tolerancia_ponto` BETWEEN 0 AND 60);

--
-- Create index `cpf` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD UNIQUE INDEX cpf (cpf);

--
-- Create index `idx_cpf` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_cpf (cpf);

--
-- Create index `idx_data_admissao` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_data_admissao (data_admissao);

--
-- Create index `idx_matricula` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_matricula (matricula_empresa);

--
-- Create index `idx_nome` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_nome (nome_completo);

--
-- Create index `idx_setor` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_setor (setor_obra);

--
-- Create index `idx_status` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_status (status_cadastro);

--
-- Create index `matricula_empresa` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD UNIQUE INDEX matricula_empresa (matricula_empresa);

--
-- Create view `vw_funcionarios_biometria`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_funcionarios_biometria
AS
SELECT
  `f`.`id` AS `id`,
  `f`.`nome_completo` AS `nome_completo`,
  `f`.`cpf` AS `cpf`,
  `f`.`setor_obra` AS `setor`,
  `f`.`cargo` AS `cargo`,
  `f`.`matricula_empresa` AS `matricula_empresa`,
  `f`.`empresa` AS `empresa`,
  `f`.`status_cadastro` AS `status_cadastro`,
  (CASE WHEN ((`f`.`digital_dedo1` IS NOT NULL) OR
      (`f`.`digital_dedo2` IS NOT NULL)) THEN 'Configurado' ELSE 'Não Configurado' END) AS `status_biometria`,
  `f`.`biometria_qualidade_1` AS `biometria_qualidade_1`,
  `f`.`biometria_qualidade_2` AS `biometria_qualidade_2`,
  `f`.`foto_url` AS `foto_url`,
  `f`.`data_cadastro` AS `data_cadastro`,
  `f`.`data_atualizacao` AS `data_atualizacao`
FROM `funcionarios` `f`
WHERE (`f`.`status_cadastro` = 'Ativo')
ORDER BY `f`.`nome_completo`;

--
-- Create table `epis`
--
CREATE TABLE IF NOT EXISTS epis (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL,
  epi_nome varchar(255) NOT NULL,
  epi_ca varchar(50) DEFAULT NULL,
  epi_data_entrega date DEFAULT NULL,
  epi_data_validade date DEFAULT NULL,
  epi_observacoes text DEFAULT NULL,
  status_epi enum ('entregue', 'vencido', 'devolvido') DEFAULT 'entregue',
  criado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  atualizado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Controle de EPIs dos funcionários',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_data_validade` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_data_validade (epi_data_validade);

--
-- Create index `idx_funcionario_epi` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_funcionario_epi (funcionario_id);

--
-- Create index `idx_status_epi` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_status_epi (status_epi);

--
-- Create foreign key
--
ALTER TABLE epis
ADD CONSTRAINT epis_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create table `usuarios`
--
CREATE TABLE IF NOT EXISTS usuarios (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  usuario varchar(50) NOT NULL,
  nome_completo varchar(100) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  ativo tinyint(1) DEFAULT 1,
  ultimo_login timestamp NULL DEFAULT NULL,
  senha varchar(255) NOT NULL,
  nivel_acesso enum ('usuario', 'admin') DEFAULT 'usuario',
  data_criacao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  data_ultimo_acesso timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 3,
AVG_ROW_LENGTH = 16384,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Usuários do sistema de controle de ponto',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_usuario` on table `usuarios`
--
ALTER TABLE usuarios
ADD INDEX idx_usuario (usuario);

--
-- Create index `usuario` on table `usuarios`
--
ALTER TABLE usuarios
ADD UNIQUE INDEX usuario (usuario);

--
-- Create table `registros_ponto`
--
CREATE TABLE IF NOT EXISTS registros_ponto (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL,
  tipo_registro enum ('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida') NOT NULL,
  data_hora timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  data_registro date GENERATED ALWAYS AS (CAST(`data_hora` AS date)) STORED COMMENT 'Data extraída para índices',
  metodo_registro enum ('biometrico', 'manual') NOT NULL,
  criado_por int UNSIGNED DEFAULT NULL COMMENT 'ID do usuário que fez registro manual',
  template_biometrico longblob DEFAULT NULL COMMENT 'Template biométrico usado',
  digital_capturada longblob DEFAULT NULL COMMENT 'Compatibilidade com código antigo',
  qualidade_biometria tinyint UNSIGNED DEFAULT NULL COMMENT 'Qualidade 0-100',
  observacoes text DEFAULT NULL,
  ip_origem varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  sincronizado tinyint(1) DEFAULT 0 COMMENT 'Compatibilidade',
  criado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  atualizado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 12,
AVG_ROW_LENGTH = 2048,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Registros de ponto biométrico e manual - RLPONTO-WEB v1.2',
ROW_FORMAT = DYNAMIC;

--
-- Create check constraint
--
ALTER TABLE registros_ponto
ADD CONSTRAINT chk_qualidade_biometria CHECK ((`qualidade_biometria` IS NULL) OR (`qualidade_biometria` BETWEEN 0 AND 100));

--
-- Create index `idx_criado_por` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_criado_por (criado_por);

--
-- Create index `idx_data_hora` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_data_hora (data_hora);

--
-- Create index `idx_data_registro` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_data_registro (data_registro);

--
-- Create index `idx_funcionario_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_funcionario_data (funcionario_id, data_hora);

--
-- Create index `idx_funcionario_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_funcionario_tipo_data (funcionario_id, tipo_registro, data_hora);

--
-- Create index `idx_metodo` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_metodo (metodo_registro);

--
-- Create index `idx_registros_ponto_metodo` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_registros_ponto_metodo (metodo_registro);

--
-- Create index `idx_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_tipo_data (tipo_registro, data_hora);

--
-- Create index `uk_funcionario_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD UNIQUE INDEX uk_funcionario_tipo_data (funcionario_id, tipo_registro, data_registro);

--
-- Create foreign key
--
ALTER TABLE registros_ponto
ADD CONSTRAINT registros_ponto_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create foreign key
--
ALTER TABLE registros_ponto
ADD CONSTRAINT registros_ponto_ibfk_2 FOREIGN KEY (criado_por)
REFERENCES usuarios (id) ON DELETE SET NULL ON UPDATE CASCADE;

DELIMITER $$

--
-- Create function `VerificarIntegridadeBiometrica`
--
CREATE
DEFINER = 'cavalcrod'@'%'
FUNCTION IF NOT EXISTS VerificarIntegridadeBiometrica (p_funcionario_id int UNSIGNED)
RETURNS json
DETERMINISTIC
READS SQL DATA
BEGIN
  DECLARE resultado json;
  DECLARE tem_dedo1 boolean DEFAULT FALSE;
  DECLARE tem_dedo2 boolean DEFAULT FALSE;
  DECLARE total_registros int DEFAULT 0;
  DECLARE nome_funcionario varchar(100) DEFAULT '';

  -- Verificar se funcionário existe
  SELECT
    (digital_dedo1 IS NOT NULL) AS dedo1,
    (digital_dedo2 IS NOT NULL) AS dedo2,
    nome_completo INTO tem_dedo1, tem_dedo2, nome_funcionario
  FROM funcionarios
  WHERE id = p_funcionario_id
  AND status_cadastro = 'Ativo'
  LIMIT 1;

  -- Se funcionário não existe, retornar erro
  IF nome_funcionario = '' THEN
    SET resultado = JSON_OBJECT('erro', TRUE,
    'mensagem', 'Funcionário não encontrado ou inativo');
    RETURN resultado;
  END IF;

  SELECT
    COUNT(*) INTO total_registros
  FROM registros_ponto
  WHERE funcionario_id = p_funcionario_id;

  SET resultado = JSON_OBJECT('funcionario_id', p_funcionario_id,
  'nome_funcionario', nome_funcionario,
  'tem_biometria_dedo1', tem_dedo1,
  'tem_biometria_dedo2', tem_dedo2,
  'total_registros_ponto', total_registros,
  'status_biometrico', CASE WHEN tem_dedo1 OR
      tem_dedo2 THEN 'configurado' ELSE 'nao_configurado' END,
  'erro', FALSE);

  RETURN resultado;
END
$$

DELIMITER ;

--
-- Create view `vw_relatorio_pontos`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_relatorio_pontos
AS
SELECT
  `rp`.`id` AS `id`,
  `rp`.`funcionario_id` AS `funcionario_id`,
  `f`.`nome_completo` AS `nome_completo`,
  `f`.`matricula_empresa` AS `matricula_empresa`,
  `f`.`cpf` AS `cpf`,
  CONCAT(SUBSTR(`f`.`cpf`, 1, 3), '.***.***-', SUBSTR(`f`.`cpf`, -(2))) AS `cpf_exibicao`,
  `rp`.`data_hora` AS `data_hora`,
  CAST(`rp`.`data_hora` AS date) AS `data_registro`,
  CAST(`rp`.`data_hora` AS time) AS `hora_registro`,
  `rp`.`tipo_registro` AS `tipo_registro`,
  (CASE `rp`.`tipo_registro` WHEN 'entrada_manha' THEN 'Entrada Manhã' WHEN 'saida_almoco' THEN 'Saída Almoço' WHEN 'entrada_tarde' THEN 'Entrada Tarde' WHEN 'saida' THEN 'Saída' END) AS `tipo_descricao`,
  `rp`.`metodo_registro` AS `metodo_registro`,
  (CASE WHEN (`rp`.`metodo_registro` = 'biometrico') THEN 'Biométrico' ELSE 'Manual' END) AS `metodo_descricao`,
  COALESCE(`f`.`setor_obra`, `f`.`setor`, 'Não informado') AS `setor`,
  `f`.`cargo` AS `cargo`,
  `f`.`empresa` AS `empresa`,
  `rp`.`qualidade_biometria` AS `qualidade_biometria`,
  `rp`.`observacoes` AS `observacoes`,
  `rp`.`ip_origem` AS `ip_origem`,
  `rp`.`criado_em` AS `criado_em`,
  `u`.`usuario` AS `criado_por_usuario`,
  (CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > '08:10:00')) THEN 'Atraso' WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > '13:10:00')) THEN 'Atraso' ELSE 'Pontual' END) AS `status_pontualidade`
FROM ((`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
  LEFT JOIN `usuarios` `u`
    ON ((`rp`.`criado_por` = `u`.`id`)))
WHERE (`f`.`ativo` = 1)
ORDER BY `rp`.`data_hora` DESC;

--
-- Create view `vw_horas_trabalhadas`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_horas_trabalhadas
AS
WITH `registros_diarios`
AS
(SELECT
    `f`.`id` AS `funcionario_id`,
    `f`.`nome_completo` AS `nome_completo`,
    `f`.`setor` AS `setor`,
    `f`.`cargo` AS `cargo`,
    CAST(`rp`.`data_hora` AS date) AS `data_registro`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN `rp`.`data_hora` END)) AS `entrada_manha`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN `rp`.`data_hora` END)) AS `saida_almoco`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN `rp`.`data_hora` END)) AS `entrada_tarde`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN `rp`.`data_hora` END)) AS `saida`
  FROM (`registros_ponto` `rp`
    JOIN `funcionarios` `f`
      ON ((`rp`.`funcionario_id` = `f`.`id`)))
  WHERE (`f`.`ativo` = 1)
  GROUP BY `f`.`id`,
           `f`.`nome_completo`,
           `f`.`setor`,
           `f`.`cargo`,
           CAST(`rp`.`data_hora` AS date))
SELECT
  `rd`.`funcionario_id` AS `funcionario_id`,
  `rd`.`nome_completo` AS `nome_completo`,
  `rd`.`setor` AS `setor`,
  `rd`.`cargo` AS `cargo`,
  `rd`.`data_registro` AS `data_registro`,
  `rd`.`entrada_manha` AS `entrada_manha`,
  `rd`.`saida_almoco` AS `saida_almoco`,
  `rd`.`entrada_tarde` AS `entrada_tarde`,
  `rd`.`saida` AS `saida`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NOT NULL) AND
      (`rd`.`saida_almoco` IS NOT NULL)) THEN TIMEDIFF(`rd`.`saida_almoco`, `rd`.`entrada_manha`) ELSE NULL END) AS `periodo_manha`,
  (CASE WHEN ((`rd`.`entrada_tarde` IS NOT NULL) AND
      (`rd`.`saida` IS NOT NULL)) THEN TIMEDIFF(`rd`.`saida`, `rd`.`entrada_tarde`) ELSE NULL END) AS `periodo_tarde`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NOT NULL) AND
      (`rd`.`saida_almoco` IS NOT NULL) AND
      (`rd`.`entrada_tarde` IS NOT NULL) AND
      (`rd`.`saida` IS NOT NULL)) THEN ADDTIME(TIMEDIFF(`rd`.`saida_almoco`, `rd`.`entrada_manha`), TIMEDIFF(`rd`.`saida`, `rd`.`entrada_tarde`)) ELSE NULL END) AS `total_horas_trabalhadas`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NULL) OR
      (`rd`.`saida_almoco` IS NULL) OR
      (`rd`.`entrada_tarde` IS NULL) OR
      (`rd`.`saida` IS NULL)) THEN 'Incompleto' ELSE 'Completo' END) AS `status_dia`
FROM `registros_diarios` `rd`;

--
-- Create view `vw_estatisticas_sistema`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_sistema
AS
SELECT
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (`funcionarios`.`status_cadastro` = 'Ativo')) AS `funcionarios_ativos`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (`funcionarios`.`status_cadastro` = 'Inativo')) AS `funcionarios_inativos`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (((`funcionarios`.`digital_dedo1` IS NOT NULL)
    OR (`funcionarios`.`digital_dedo2` IS NOT NULL))
    AND (`funcionarios`.`status_cadastro` = 'Ativo'))) AS `funcionarios_com_biometria`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE ((`funcionarios`.`digital_dedo1` IS NULL)
    AND (`funcionarios`.`digital_dedo2` IS NULL)
    AND (`funcionarios`.`status_cadastro` = 'Ativo'))) AS `funcionarios_sem_biometria`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE (CAST(`registros_ponto`.`data_hora` AS date) = CURDATE())) AS `registros_hoje`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE (YEARWEEK(`registros_ponto`.`data_hora`, 1) = YEARWEEK(NOW(), 1))) AS `registros_semana_atual`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE ((YEAR(`registros_ponto`.`data_hora`) = YEAR(NOW()))
    AND (MONTH(`registros_ponto`.`data_hora`) = MONTH(NOW())))) AS `registros_mes_atual`;

--
-- Create view `vw_estatisticas_pontos`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_pontos
AS
SELECT
  CAST(`rp`.`data_hora` AS date) AS `data_registro`,
  COUNT(0) AS `total_registros`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'biometrico') THEN 1 ELSE 0 END)) AS `registros_biometricos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'manual') THEN 1 ELSE 0 END)) AS `registros_manuais`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN 1 ELSE 0 END)) AS `entradas_manha`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN 1 ELSE 0 END)) AS `saidas_almoco`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN 1 ELSE 0 END)) AS `entradas_tarde`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN 1 ELSE 0 END)) AS `saidas`,
  SUM((CASE WHEN (((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > '08:10:00')) OR
      ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > '13:10:00'))) THEN 1 ELSE 0 END)) AS `atrasos`,
  COUNT(DISTINCT `rp`.`funcionario_id`) AS `funcionarios_registraram`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
WHERE (`f`.`ativo` = 1)
GROUP BY CAST(`rp`.`data_hora` AS date)
ORDER BY `data_registro` DESC;

--
-- Create view `vw_estatisticas_ponto_setor`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_ponto_setor
AS
SELECT
  `f`.`setor` AS `setor`,
  CAST(`rp`.`data_hora` AS date) AS `data_registro`,
  COUNT(0) AS `total_registros`,
  COUNT(DISTINCT `f`.`id`) AS `funcionarios_unicos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'biometrico') THEN 1 ELSE 0 END)) AS `registros_biometricos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'manual') THEN 1 ELSE 0 END)) AS `registros_manuais`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN 1 ELSE 0 END)) AS `entradas_manha`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN 1 ELSE 0 END)) AS `saidas_almoco`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN 1 ELSE 0 END)) AS `entradas_tarde`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN 1 ELSE 0 END)) AS `saidas`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > ADDTIME(`f`.`jornada_seg_qui_entrada`, SEC_TO_TIME((`f`.`tolerancia_ponto` * 60))))) THEN 1 ELSE 0 END)) AS `atrasos_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > ADDTIME(`f`.`jornada_intervalo_saida`, SEC_TO_TIME((`f`.`tolerancia_ponto` * 60))))) THEN 1 ELSE 0 END)) AS `atrasos_tarde`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
GROUP BY `f`.`setor`,
         CAST(`rp`.`data_hora` AS date);

--
-- Create view `vw_analise_pontualidade`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_analise_pontualidade
AS
SELECT
  `f`.`id` AS `funcionario_id`,
  `f`.`nome_completo` AS `nome_completo`,
  `f`.`setor` AS `setor`,
  `f`.`cargo` AS `cargo`,
  DATE_FORMAT(`rp`.`data_hora`, '%Y-%m') AS `mes_ano`,
  COUNT(DISTINCT CAST(`rp`.`data_hora` AS date)) AS `dias_trabalhados`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) <= '08:10:00')) THEN 1 ELSE 0 END)) AS `entradas_pontuais_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > '08:10:00')) THEN 1 ELSE 0 END)) AS `atrasos_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) <= '13:10:00')) THEN 1 ELSE 0 END)) AS `entradas_pontuais_tarde`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > '13:10:00')) THEN 1 ELSE 0 END)) AS `atrasos_tarde`,
  ROUND(((SUM((CASE WHEN ((`rp`.`tipo_registro` IN ('entrada_manha', 'entrada_tarde')) AND
      (((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) <= '08:10:00')) OR
      ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) <= '13:10:00')))) THEN 1 ELSE 0 END)) * 100.0) / NULLIF(SUM((CASE WHEN (`rp`.`tipo_registro` IN ('entrada_manha', 'entrada_tarde')) THEN 1 ELSE 0 END)), 0)), 2) AS `percentual_pontualidade`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
WHERE (`f`.`ativo` = 1)
GROUP BY `f`.`id`,
         `f`.`nome_completo`,
         `f`.`setor`,
         `f`.`cargo`,
         DATE_FORMAT(`rp`.`data_hora`, '%Y-%m');

--
-- Create table `permissoes`
--
CREATE TABLE IF NOT EXISTS permissoes (
  usuario_id int UNSIGNED NOT NULL,
  nivel_acesso enum ('admin', 'usuario') DEFAULT 'usuario',
  data_atribuicao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (usuario_id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Permissões e níveis de acesso dos usuários',
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE permissoes
ADD CONSTRAINT permissoes_ibfk_1 FOREIGN KEY (usuario_id)
REFERENCES usuarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create table `logs_sistema`
--
CREATE TABLE IF NOT EXISTS logs_sistema (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  usuario_id int UNSIGNED DEFAULT NULL,
  acao varchar(100) NOT NULL,
  tabela_afetada varchar(50) DEFAULT NULL,
  registro_id int UNSIGNED DEFAULT NULL,
  detalhes json DEFAULT NULL,
  ip_origem varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  data_hora timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 14,
AVG_ROW_LENGTH = 1489,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Logs de auditoria para segurança do sistema',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_acao_data` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_acao_data (acao, data_hora);

--
-- Create index `idx_data_hora` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_data_hora (data_hora);

--
-- Create index `idx_usuario_data` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_usuario_data (usuario_id, data_hora);

--
-- Create foreign key
--
ALTER TABLE logs_sistema
ADD CONSTRAINT logs_sistema_ibfk_1 FOREIGN KEY (usuario_id)
REFERENCES usuarios (id) ON DELETE SET NULL ON UPDATE CASCADE;

DELIMITER $$

--
-- Create procedure `LimparLogsAntigos`
--
CREATE
DEFINER = 'cavalcrod'@'%'
PROCEDURE IF NOT EXISTS LimparLogsAntigos (IN dias_para_manter int UNSIGNED)
BEGIN
  DECLARE registros_removidos int DEFAULT 0;

  -- Validar parâmetro
  IF dias_para_manter < 7 THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Mínimo de 7 dias deve ser mantido nos logs';
  END IF;

  DELETE
    FROM logs_sistema
  WHERE data_hora < DATE_SUB(NOW(), INTERVAL dias_para_manter DAY);

  SET registros_removidos = ROW_COUNT();

  SELECT
    registros_removidos AS registros_removidos,
    dias_para_manter AS dias_mantidos,
    NOW() AS data_limpeza;

  -- Log da limpeza
  INSERT INTO logs_sistema (acao, tabela_afetada, detalhes, data_hora)
    VALUES ('LIMPEZA_LOGS', 'logs_sistema', JSON_OBJECT('registros_removidos', registros_removidos, 'dias_mantidos', dias_para_manter), NOW());
END
$$

DELIMITER ;

--
-- Create table `empresas`
--
CREATE TABLE IF NOT EXISTS empresas (
  id int NOT NULL AUTO_INCREMENT,
  razao_social varchar(200) NOT NULL,
  nome_fantasia varchar(200) DEFAULT NULL,
  cnpj varchar(18) NOT NULL,
  telefone varchar(15) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  ativa tinyint(1) DEFAULT 1,
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `cnpj` on table `empresas`
--
ALTER TABLE empresas
ADD UNIQUE INDEX cnpj (cnpj);

--
-- Create table `horarios_trabalho`
--
CREATE TABLE IF NOT EXISTS horarios_trabalho (
  id int NOT NULL AUTO_INCREMENT,
  empresa_id int NOT NULL,
  nome_horario varchar(100) NOT NULL,
  entrada_manha time NOT NULL DEFAULT '08:00:00',
  saida_almoco time DEFAULT '12:00:00',
  entrada_tarde time DEFAULT '13:00:00',
  saida time NOT NULL DEFAULT '17:00:00',
  tolerancia_minutos int NOT NULL DEFAULT 10,
  ativo tinyint(1) DEFAULT 1,
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE horarios_trabalho
ADD CONSTRAINT horarios_trabalho_ibfk_1 FOREIGN KEY (empresa_id)
REFERENCES empresas (id) ON DELETE CASCADE;

-- 
-- Dumping data for table usuarios
--
INSERT INTO usuarios VALUES
(1, 'admin', 'Administrador do Sistema', '<EMAIL>', 1, NULL, 'pbkdf2:sha256:600000$IiIqilzPoADLAQ4v$edb07fc69adef3aa4eb1ef608eea4d41d5f272200c900fc252efc5c6085dea89', 'admin', '2025-06-05 16:06:48', '2025-06-05 16:06:48'),
(2, 'teste', NULL, NULL, 1, NULL, 'pbkdf2:sha256:600000$OmXNQh5XtS7BNSri$e174ced16e67d04869383d125a0665498d51b7bc82b6bbc0a6e00d20cb5bc2c0', 'usuario', '2025-06-06 10:56:33', NULL);

-- 
-- Dumping data for table empresas
--
INSERT INTO empresas VALUES
(1, 'Empresa Padrão Ltda', 'Empresa Padrão', '00.000.000/0000-00', NULL, NULL, 1, '2025-06-05 17:20:07');

-- 
-- Dumping data for table funcionarios
--
INSERT INTO funcionarios(id, empresa_id, horario_trabalho_id, nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade, ctps_numero, ctps_serie_uf, pis_pasep, endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado, telefone1, telefone2, email, cargo, setor, setor_obra, empresa, matricula_empresa, data_admissao, tipo_contrato, jornada_seg_qui_entrada, jornada_seg_qui_saida, jornada_sex_entrada, jornada_sex_saida, jornada_intervalo_entrada, jornada_intervalo_saida, digital_dedo1, digital_dedo2, foto_3x4, foto_url, nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, ativo, status_cadastro, data_cadastro, data_atualizacao, biometria_qualidade_1, biometria_qualidade_2, biometria_data_cadastro) VALUES
(1, 1, 1, 'RICHARDSON CARDOSO RODRIGUES', '711.256.042-04', '31.799.841', '1981-03-20', 'M', 'Casado', 'Brasileiro', '0000000', '0000000000', '000.00000.00-0', 'RUA GERMÂNIO', 'VILA DA PRATA', 'MANAUS', '69030-685', 'AM', '(92) 99245-5278', NULL, '<EMAIL>', 'ANALISTA', 'Administrativo', 'TI', 'Empresa Principal', '0001', '2025-01-01', 'PJ', '08:00:00', '18:00:00', '08:00:00', '18:00:00', '13:00:00', '14:00:00', x'3038313632373843394342303635343632344641454343434136383330373339323635303037314231394539303746413042413532363741304339313146373331324534314639323232423932354432303738463135353530363036314538313043363332354142313633333139313231453538313735323146354230443734314441313144443131353034303935373043374430343042313230443234334631374538314238353045424430373838323546303145324430394441313430313139363931433132304137343034384232333135304137303034393730393434323537443234464430394631313934353045344630464139313345433035383831373342323446333136363832303645304343433232383531393341313833333131303930463645323543313038354130443334304142423134434230343835303831343046393931313932323534463234344131393732314537463037324531324235313644363038444631364330313446443138393932363645323233383144374630414445314238463145463031373539303442443044443930394541323036323230443131313842313938363041433730383238313242323138343930423830314532313143323132333038304530453043303831453338313038453145323331303135304646443231374431303239314230313046453430363544', x'3543383044374432433732363831363142314544443839424637333244393646313444393235314131324644304541443046313132364241313938363145313030424143313631363042443931304434313739453136373231333241304131423135343931324142323136363034334432343631304546383130353131323132304542453035374330344632313745373131303931433145314332423038333330394236304442373136364331324137304339393136323531423731323138433235314230453243304641373041353431334337313935463144314432314236323245353134363630433139313346323144453330443537303437333034393431324437303741443138424331463746304439313136333631443444303534343236384531354138303936433230363132373046314343423232463531423335304139343130363031344443314237353230383730383731314239313039313031364632304642413141444432304542303939363041363031384646314346313230303530434235303635373235304230373136304439383131343130394238304243393134314331434546313833463043454532344130323231323143343431453336323534383143434432323239303645453043443131334439323730443036324430414338313731443046453631433245313046423142343831394436', 'fotos_funcionarios/funcionario_1_20250606T001537.jpg', NULL, 'Funcionario', 'Diurno', 5, 1, 1, 1, 'Ativo', '2025-06-05 16:27:38', '2025-06-06 00:15:37', NULL, NULL, NULL),
(2, 1, 1, 'João Silva Santos', '111.111.111-11', '1111111', '1990-05-15', 'M', 'Solteiro', 'Brasileiro', '11111111111', '001/AM', '111.11111.11-1', NULL, NULL, NULL, '69000-000', 'AM', '(92) 99999-1111', NULL, NULL, 'Desenvolvedor', 'Administrativo', 'TI', 'Empresa Principal', 'EMP001', '2025-01-01', 'CLT', '08:00:00', '17:00:00', NULL, NULL, '12:00:00', '13:00:00', NULL, NULL, '/static/images/avatars/funcionario_masculino_1.jpg', NULL, 'Funcionario', 'Diurno', 5, 0, 0, 1, 'Ativo', '2025-06-05 19:42:43', '2025-06-05 21:52:43', NULL, NULL, NULL),
(3, 1, 1, 'Maria Oliveira Costa', '222.222.222-22', '2222222', '1988-08-20', 'F', 'Casado', 'Brasileiro', '22222222222', '002/AM', '222.22222.22-2', NULL, NULL, NULL, '69000-000', 'AM', '(92) 99999-2222', NULL, NULL, 'Analista', 'Administrativo', 'Financeiro', 'Empresa Principal', 'EMP002', '2025-01-15', 'CLT', '08:00:00', '17:00:00', NULL, NULL, '12:00:00', '13:00:00', NULL, NULL, '/static/images/avatars/funcionario_feminino_1.jpg', NULL, 'Funcionario', 'Diurno', 5, 0, 0, 1, 'Ativo', '2025-06-05 19:42:43', '2025-06-05 21:52:43', NULL, NULL, NULL),
(4, 1, 1, 'Pedro Almeida Souza', '333.333.333-33', '3333333', '1985-12-10', 'M', 'Casado', 'Brasileiro', '33333333333', '003/AM', '333.33333.33-3', NULL, NULL, NULL, '69000-000', 'AM', '(92) 99999-3333', NULL, NULL, 'Gerente', 'Administrativo', 'Operações', 'Empresa Principal', 'EMP003', '2024-06-01', 'CLT', '08:00:00', '17:00:00', NULL, NULL, '12:00:00', '13:00:00', NULL, NULL, '/static/images/avatars/funcionario_masculino_2.jpg', NULL, 'Gerencia', 'Diurno', 5, 0, 0, 1, 'Ativo', '2025-06-05 19:42:43', '2025-06-05 21:52:43', NULL, NULL, NULL),
(5, 1, 1, 'Ana Carolina Lima', '744.582.563-24', '4444444', '1992-03-25', 'F', 'Solteiro', 'Brasileiro', '44444444444', '004/AM', '444.44444.44-4', NULL, NULL, NULL, '69000-000', 'AM', '(92) 99999-4444', NULL, NULL, 'Coordenadora', 'Administrativo', 'RH', 'Empresa Principal', 'EMP004', '2024-08-15', 'CLT', '08:00:00', '17:00:00', '07:00:00', '16:00:00', '12:00:00', '13:00:00', NULL, NULL, '/static/images/avatars/funcionario_feminino_2.jpg', NULL, 'Supervisao', 'Diurno', 5, 0, 0, 1, 'Ativo', '2025-06-05 19:42:43', '2025-06-05 21:52:43', NULL, NULL, NULL);

-- 
-- Dumping data for table registros_ponto
--
INSERT INTO registros_ponto(id, funcionario_id, tipo_registro, data_hora, metodo_registro, criado_por, template_biometrico, digital_capturada, qualidade_biometria, observacoes, ip_origem, user_agent, sincronizado, criado_em, atualizado_em) VALUES
(1, 1, 'entrada_manha', '2025-06-05 08:05:00', 'manual', NULL, NULL, NULL, NULL, 'Teste para relatórios', NULL, NULL, 0, '2025-06-05 22:57:37', '2025-06-05 22:57:37'),
(2, 1, 'saida', '2025-06-05 17:10:00', 'biometrico', NULL, NULL, NULL, NULL, 'Teste para relatórios', NULL, NULL, 0, '2025-06-05 22:57:37', '2025-06-05 22:57:37'),
(3, 2, 'entrada_manha', '2025-06-05 08:05:00', 'manual', NULL, NULL, NULL, NULL, 'Teste para relatórios', NULL, NULL, 0, '2025-06-05 22:57:37', '2025-06-05 22:57:37'),
(4, 2, 'saida', '2025-06-05 17:10:00', 'biometrico', NULL, NULL, NULL, NULL, 'Teste para relatórios', NULL, NULL, 0, '2025-06-05 22:57:37', '2025-06-05 22:57:37'),
(5, 1, 'entrada_manha', '2025-06-04 07:58:00', 'biometrico', NULL, NULL, NULL, NULL, 'Teste para relatórios', NULL, NULL, 0, '2025-06-05 22:57:37', '2025-06-05 22:57:37'),
(6, 2, 'entrada_manha', '2025-06-04 07:58:00', 'biometrico', NULL, NULL, NULL, NULL, 'Teste para relatórios', NULL, NULL, 0, '2025-06-05 22:57:37', '2025-06-05 22:57:37'),
(7, 1, 'saida_almoco', '2025-06-06 01:20:04', 'manual', NULL, NULL, NULL, NULL, NULL, '10.19.208.4', NULL, 0, '2025-06-06 01:20:04', '2025-06-06 01:20:04'),
(8, 1, 'entrada_tarde', '2025-06-06 01:22:30', 'manual', NULL, NULL, NULL, NULL, NULL, '10.19.208.4', NULL, 0, '2025-06-06 01:22:30', '2025-06-06 01:22:30'),
(9, 1, 'saida', '2025-06-06 01:23:22', 'manual', NULL, NULL, NULL, NULL, 'teste saida', '10.19.208.4', NULL, 0, '2025-06-06 01:23:22', '2025-06-06 01:23:22'),
(10, 1, 'entrada_manha', '2025-06-06 09:02:37', 'manual', NULL, NULL, NULL, NULL, NULL, '10.19.208.93', NULL, 0, '2025-06-06 09:02:37', '2025-06-06 09:02:37'),
(11, 3, 'entrada_tarde', '2025-06-06 14:13:20', 'manual', NULL, NULL, NULL, NULL, 'teste', '*************', NULL, 0, '2025-06-06 14:13:20', '2025-06-06 14:13:20');

-- 
-- Dumping data for table permissoes
--
INSERT INTO permissoes(usuario_id, nivel_acesso, data_atribuicao) VALUES
(1, 'admin', '2025-06-05 16:06:48'),
(2, 'usuario', '2025-06-06 10:56:33');

-- 
-- Dumping data for table logs_sistema
--
INSERT INTO logs_sistema(id, usuario_id, acao, tabela_afetada, registro_id, detalhes, ip_origem, user_agent, data_hora) VALUES
(1, NULL, 'INICIALIZACAO_BANCO', 'sistema', NULL, '{"status": "sucesso", "versao": "1.2", "data_criacao": "2025-06-05 20:06:48.000000"}', NULL, NULL, '2025-06-05 16:06:48'),
(2, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 1, '{"data_hora": "2025-06-05 22:29:09.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:29:09'),
(3, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 1, '{"data_hora": "2025-06-05 08:05:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(4, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 2, '{"data_hora": "2025-06-05 17:10:00.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(5, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 3, '{"data_hora": "2025-06-05 08:05:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 2, "metodo_registro": "manual", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(6, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 4, '{"data_hora": "2025-06-05 17:10:00.000000", "tipo_registro": "saida", "funcionario_id": 2, "metodo_registro": "biometrico", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(7, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 5, '{"data_hora": "2025-06-04 07:58:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(8, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 6, '{"data_hora": "2025-06-04 07:58:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 2, "metodo_registro": "biometrico", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(9, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 7, '{"data_hora": "2025-06-06 01:20:04.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:20:04'),
(10, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 8, '{"data_hora": "2025-06-06 01:22:30.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:22:30'),
(11, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 9, '{"data_hora": "2025-06-06 01:23:22.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:23:22'),
(12, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 10, '{"data_hora": "2025-06-06 09:02:37.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 09:02:37'),
(13, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 11, '{"data_hora": "2025-06-06 14:13:20.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 3, "metodo_registro": "manual", "nome_funcionario": "Maria Oliveira Costa", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 14:13:20');

-- 
-- Dumping data for table horarios_trabalho
--
INSERT INTO horarios_trabalho(id, empresa_id, nome_horario, entrada_manha, saida_almoco, entrada_tarde, saida, tolerancia_minutos, ativo, data_cadastro) VALUES
(1, 1, 'Horário Administrativo', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10, 1, '2025-06-05 17:20:07');

-- Table controle_ponto.epis does not contain any data (it is empty)

--
-- Set default database
--
USE controle_ponto;

DELIMITER $$

--
-- Create trigger `tr_registros_ponto_audit_insert`
--
CREATE
DEFINER = 'cavalcrod'@'%'
TRIGGER IF NOT EXISTS tr_registros_ponto_audit_insert
AFTER INSERT
ON registros_ponto
FOR EACH ROW
BEGIN
  DECLARE nome_funcionario varchar(100);

  -- Buscar nome do funcionário
  SELECT
    nome_completo INTO nome_funcionario
  FROM funcionarios
  WHERE id = NEW.funcionario_id
  LIMIT 1;

  INSERT INTO logs_sistema (acao, tabela_afetada, registro_id, usuario_id, detalhes, data_hora)
    VALUES ('INSERT_REGISTRO_PONTO', 'registros_ponto', NEW.id, NEW.criado_por, JSON_OBJECT('funcionario_id', NEW.funcionario_id, 'nome_funcionario', COALESCE(nome_funcionario, 'N/A'), 'tipo_registro', NEW.tipo_registro, 'metodo_registro', NEW.metodo_registro, 'data_hora', NEW.data_hora, 'qualidade_biometria', NEW.qualidade_biometria), NOW());
END
$$

--
-- Create trigger `tr_funcionarios_audit_update`
--
CREATE
DEFINER = 'cavalcrod'@'%'
TRIGGER IF NOT EXISTS tr_funcionarios_audit_update
AFTER UPDATE
ON funcionarios
FOR EACH ROW
BEGIN
  -- Só registra se houve mudança significativa
  IF (OLD.nome_completo != NEW.nome_completo
    OR OLD.status_cadastro != NEW.status_cadastro
    OR (OLD.digital_dedo1 IS NULL) != (NEW.digital_dedo1 IS NULL)
    OR (OLD.digital_dedo2 IS NULL) != (NEW.digital_dedo2 IS NULL)) THEN

    INSERT INTO logs_sistema (acao, tabela_afetada, registro_id, detalhes, data_hora)
      VALUES ('UPDATE_FUNCIONARIO', 'funcionarios', NEW.id, JSON_OBJECT('funcionario_id', NEW.id, 'nome', NEW.nome_completo, 'mudancas', JSON_OBJECT('nome_alterado', OLD.nome_completo != NEW.nome_completo, 'status_alterado', OLD.status_cadastro != NEW.status_cadastro, 'biometria_dedo1_alterado', (OLD.digital_dedo1 IS NULL) != (NEW.digital_dedo1 IS NULL), 'biometria_dedo2_alterado', (OLD.digital_dedo2 IS NULL) != (NEW.digital_dedo2 IS NULL))), NOW());
  END IF;
END
$$

DELIMITER ;

--
-- Restore previous SQL mode
--
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;

--
-- Enable foreign keys
--
/*!40014 SET FOREIGN_KEY_CHECKS = @OLD_FOREIGN_KEY_CHECKS */;