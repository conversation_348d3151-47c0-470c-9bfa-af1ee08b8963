-- ========================================
-- CRIAÇÃO COMPLETA DO BANCO RLPONTO-WEB
-- Data: 08/01/2025
-- Versão: 1.2 - REVISADO - Estrutura completa para registros de ponto
-- Descrição: Script completo para criar o banco do zero - REVISADO E CORRIGIDO
-- ========================================

-- CONFIGURAÇÕES INICIAIS
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';
SET NAMES utf8mb4;
SET time_zone = '+00:00';

-- 1. CRIAÇÃO DO DATABASE
DROP DATABASE IF EXISTS controle_ponto;

CREATE DATABASE controle_ponto
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE controle_ponto;

-- ========================================
-- 2. TABELAS PRINCIPAIS
-- ========================================

-- TABELA DE USUÁRIOS
CREATE TABLE usuarios (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    usuario VARCHAR(50) NOT NULL UNIQUE,
    senha VARCHAR(255) NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_ultimo_acesso TIMESTAMP NULL DEFAULT NULL,
    
    INDEX idx_usuario (usuario)
) ENGINE=InnoDB 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT = 'Usuários do sistema de controle de ponto';

-- TABELA DE PERMISSÕES
CREATE TABLE permissoes (
    usuario_id INT UNSIGNED NOT NULL,
    nivel_acesso ENUM('admin', 'usuario') DEFAULT 'usuario',
    data_atribuicao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (usuario_id),
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT = 'Permissões e níveis de acesso dos usuários';

-- TABELA DE LOGS DO SISTEMA
CREATE TABLE logs_sistema (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    usuario_id INT UNSIGNED DEFAULT NULL,
    acao VARCHAR(100) NOT NULL,
    tabela_afetada VARCHAR(50) DEFAULT NULL,
    registro_id INT UNSIGNED DEFAULT NULL,
    detalhes JSON DEFAULT NULL,
    ip_origem VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    data_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_acao_data (acao, data_hora),
    INDEX idx_usuario_data (usuario_id, data_hora),
    INDEX idx_data_hora (data_hora),
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT = 'Logs de auditoria para segurança do sistema';

-- TABELA DE FUNCIONÁRIOS
CREATE TABLE funcionarios (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    nome_completo VARCHAR(100) NOT NULL,
    cpf VARCHAR(14) NOT NULL UNIQUE,
    rg VARCHAR(20) NOT NULL,
    data_nascimento DATE NOT NULL,
    sexo ENUM('M', 'F', 'Outro') NOT NULL,
    estado_civil ENUM('Solteiro', 'Casado', 'Divorciado', 'Viuvo') NOT NULL,
    nacionalidade VARCHAR(50) NOT NULL DEFAULT 'Brasileiro',
    ctps_numero VARCHAR(20) NOT NULL,
    ctps_serie_uf VARCHAR(20) NOT NULL,
    pis_pasep VARCHAR(20) NOT NULL,
    endereco_rua VARCHAR(100) DEFAULT NULL,
    endereco_bairro VARCHAR(50) DEFAULT NULL,
    endereco_cidade VARCHAR(50) DEFAULT NULL,
    endereco_cep VARCHAR(10) NOT NULL,
    endereco_estado VARCHAR(2) NOT NULL,
    telefone1 VARCHAR(15) NOT NULL,
    telefone2 VARCHAR(15) DEFAULT NULL,
    email VARCHAR(100) DEFAULT NULL,
    cargo VARCHAR(50) NOT NULL,
    setor VARCHAR(50) DEFAULT NULL,
    setor_obra VARCHAR(50) NOT NULL,
    empresa VARCHAR(100) DEFAULT 'Empresa Principal',
    matricula_empresa VARCHAR(20) NOT NULL UNIQUE,
    data_admissao DATE NOT NULL,
    tipo_contrato ENUM('CLT', 'PJ', 'Estagio', 'Temporario') NOT NULL,
    jornada_seg_qui_entrada TIME DEFAULT NULL,
    jornada_seg_qui_saida TIME DEFAULT NULL,
    jornada_sex_entrada TIME DEFAULT NULL,
    jornada_sex_saida TIME DEFAULT NULL,
    jornada_intervalo_entrada TIME DEFAULT NULL,
    jornada_intervalo_saida TIME DEFAULT NULL,
    digital_dedo1 LONGBLOB DEFAULT NULL COMMENT 'Template biométrico dedo 1',
    digital_dedo2 LONGBLOB DEFAULT NULL COMMENT 'Template biométrico dedo 2',
    foto_3x4 VARCHAR(255) DEFAULT NULL COMMENT 'Caminho para foto 3x4',
    foto_url VARCHAR(255) DEFAULT NULL COMMENT 'URL da foto para templates',
    nivel_acesso ENUM('Funcionario', 'Supervisao', 'Gerencia') NOT NULL DEFAULT 'Funcionario',
    turno ENUM('Diurno', 'Noturno', 'Misto') NOT NULL DEFAULT 'Diurno',
    tolerancia_ponto INT UNSIGNED NOT NULL DEFAULT 5 COMMENT 'Tolerância em minutos',
    banco_horas BOOLEAN DEFAULT FALSE,
    hora_extra BOOLEAN DEFAULT FALSE,
    ativo BOOLEAN DEFAULT TRUE COMMENT 'Compatibilidade com código existente',
    status_cadastro ENUM('Ativo', 'Inativo') NOT NULL DEFAULT 'Ativo',
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    biometria_qualidade_1 TINYINT UNSIGNED DEFAULT NULL COMMENT 'Qualidade da captura do dedo 1 (0-100)',
    biometria_qualidade_2 TINYINT UNSIGNED DEFAULT NULL COMMENT 'Qualidade da captura do dedo 2 (0-100)',
    biometria_data_cadastro TIMESTAMP NULL DEFAULT NULL,
    
    -- Campos virtuais para compatibilidade com templates
    horario_entrada_manha TIME GENERATED ALWAYS AS (jornada_seg_qui_entrada) VIRTUAL,
    horario_saida_almoco TIME GENERATED ALWAYS AS (jornada_intervalo_entrada) VIRTUAL,
    horario_entrada_tarde TIME GENERATED ALWAYS AS (jornada_intervalo_saida) VIRTUAL,
    horario_saida TIME GENERATED ALWAYS AS (jornada_seg_qui_saida) VIRTUAL,
    
    INDEX idx_cpf (cpf),
    INDEX idx_matricula (matricula_empresa),
    INDEX idx_status (status_cadastro),
    INDEX idx_setor (setor_obra),
    INDEX idx_nome (nome_completo),
    INDEX idx_data_admissao (data_admissao),
    
    -- Validações com CHECK constraints
    CONSTRAINT chk_cpf_format CHECK (cpf REGEXP '^[0-9]{3}\\.[0-9]{3}\\.[0-9]{3}-[0-9]{2}$'),
    CONSTRAINT chk_email_format CHECK (email IS NULL OR email REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_tolerancia_range CHECK (tolerancia_ponto BETWEEN 0 AND 60),
    CONSTRAINT chk_qualidade_range_1 CHECK (biometria_qualidade_1 IS NULL OR (biometria_qualidade_1 BETWEEN 0 AND 100)),
    CONSTRAINT chk_qualidade_range_2 CHECK (biometria_qualidade_2 IS NULL OR (biometria_qualidade_2 BETWEEN 0 AND 100))
) ENGINE=InnoDB 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT = 'Funcionários com dados biométricos e jornada de trabalho';

-- TABELA DE REGISTROS DE PONTO
CREATE TABLE registros_ponto (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    funcionario_id INT UNSIGNED NOT NULL,
    tipo_registro ENUM('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida') NOT NULL,
    data_hora TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    data_registro DATE GENERATED ALWAYS AS (DATE(data_hora)) STORED COMMENT 'Data extraída para índices',
    metodo_registro ENUM('biometrico', 'manual') NOT NULL,
    criado_por INT UNSIGNED NULL COMMENT 'ID do usuário que fez registro manual',
    template_biometrico LONGBLOB NULL COMMENT 'Template biométrico usado',
    digital_capturada LONGBLOB NULL COMMENT 'Compatibilidade com código antigo',
    qualidade_biometria TINYINT UNSIGNED NULL COMMENT 'Qualidade 0-100',
    observacoes TEXT NULL,
    ip_origem VARCHAR(45) NULL,
    user_agent TEXT NULL,
    sincronizado BOOLEAN DEFAULT FALSE COMMENT 'Compatibilidade',
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_funcionario_data (funcionario_id, data_hora),
    INDEX idx_funcionario_tipo_data (funcionario_id, tipo_registro, data_hora),
    INDEX idx_metodo (metodo_registro),
    INDEX idx_criado_por (criado_por),
    INDEX idx_data_hora (data_hora),
    INDEX idx_tipo_data (tipo_registro, data_hora),
    INDEX idx_data_registro (data_registro),
    
    -- Constraint única usando coluna gerada
    UNIQUE KEY uk_funcionario_tipo_data (funcionario_id, tipo_registro, data_registro),
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (criado_por) REFERENCES usuarios(id) ON DELETE SET NULL ON UPDATE CASCADE,
    
    -- Validações
    CONSTRAINT chk_qualidade_biometria CHECK (qualidade_biometria IS NULL OR (qualidade_biometria BETWEEN 0 AND 100))
) ENGINE=InnoDB 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT = 'Registros de ponto biométrico e manual - RLPONTO-WEB v1.2';

-- TABELA DE EPIs
CREATE TABLE epis (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    funcionario_id INT UNSIGNED NOT NULL,
    epi_nome VARCHAR(255) NOT NULL,
    epi_ca VARCHAR(50) DEFAULT NULL,
    epi_data_entrega DATE DEFAULT NULL,
    epi_data_validade DATE DEFAULT NULL,
    epi_observacoes TEXT DEFAULT NULL,
    status_epi ENUM('entregue', 'vencido', 'devolvido') DEFAULT 'entregue',
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_funcionario_epi (funcionario_id),
    INDEX idx_status_epi (status_epi),
    INDEX idx_data_validade (epi_data_validade),
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT = 'Controle de EPIs dos funcionários';

-- ========================================
-- 3. VIEWS OTIMIZADAS
-- ========================================

-- VIEW DE FUNCIONÁRIOS COM BIOMETRIA
CREATE VIEW vw_funcionarios_biometria AS
SELECT 
    f.id,
    f.nome_completo,
    f.cpf,
    f.setor_obra as setor,
    f.cargo,
    f.matricula_empresa,
    f.empresa,
    f.status_cadastro,
    CASE 
        WHEN f.digital_dedo1 IS NOT NULL OR f.digital_dedo2 IS NOT NULL 
        THEN 'Configurado' 
        ELSE 'Não Configurado' 
    END as status_biometria,
    f.biometria_qualidade_1,
    f.biometria_qualidade_2,
    f.foto_url,
    f.data_cadastro,
    f.data_atualizacao
FROM funcionarios f
WHERE f.status_cadastro = 'Ativo'
ORDER BY f.nome_completo;

-- VIEW DE RELATÓRIOS DE PONTO - SIMPLIFICADA
CREATE VIEW vw_relatorio_pontos AS
SELECT 
    rp.id,
    rp.funcionario_id,
    f.nome_completo,
    f.matricula_empresa,
    f.cpf,
    rp.data_hora,
    DATE(rp.data_hora) as data_registro,
    TIME(rp.data_hora) as hora_registro,
    rp.tipo_registro,
    CASE rp.tipo_registro
        WHEN 'entrada_manha' THEN 'Entrada Manhã'
        WHEN 'saida_almoco' THEN 'Saída Almoço'
        WHEN 'entrada_tarde' THEN 'Entrada Tarde'
        WHEN 'saida' THEN 'Saída'
    END AS tipo_descricao,
    rp.metodo_registro,
    CASE 
        WHEN rp.metodo_registro = 'biometrico' THEN 'Biométrico'
        ELSE 'Manual'
    END AS metodo_descricao,
    COALESCE(f.setor_obra, f.setor, 'Não informado') as setor,
    f.cargo,
    COALESCE(f.empresa, 'Não informado') as empresa,
    rp.qualidade_biometria,
    rp.observacoes,
    rp.ip_origem,
    rp.criado_em,
    u.usuario as criado_por_usuario,
    -- Cálculo simplificado de pontualidade
    CASE 
        WHEN rp.tipo_registro = 'entrada_manha' AND f.jornada_seg_qui_entrada IS NOT NULL THEN
            CASE 
                WHEN TIME(rp.data_hora) <= f.jornada_seg_qui_entrada THEN 'Pontual'
                WHEN TIME(rp.data_hora) <= ADDTIME(f.jornada_seg_qui_entrada, SEC_TO_TIME(f.tolerancia_ponto * 60)) THEN 'Tolerância'
                ELSE 'Atraso'
            END
        WHEN rp.tipo_registro = 'entrada_tarde' AND f.jornada_intervalo_saida IS NOT NULL THEN
            CASE 
                WHEN TIME(rp.data_hora) <= f.jornada_intervalo_saida THEN 'Pontual'
                WHEN TIME(rp.data_hora) <= ADDTIME(f.jornada_intervalo_saida, SEC_TO_TIME(f.tolerancia_ponto * 60)) THEN 'Tolerância'
                ELSE 'Atraso'
            END
        ELSE 'Pontual'
    END AS status_pontualidade
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
LEFT JOIN usuarios u ON rp.criado_por = u.id
WHERE f.status_cadastro = 'Ativo'
ORDER BY rp.data_hora DESC;

-- VIEW DE ESTATÍSTICAS DE PONTO - SIMPLIFICADA
CREATE VIEW vw_estatisticas_pontos AS
SELECT 
    DATE(rp.data_hora) as data_registro,
    COUNT(*) as total_registros,
    SUM(CASE WHEN rp.metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
    SUM(CASE WHEN rp.metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais,
    SUM(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN 1 ELSE 0 END) as entradas_manha,
    SUM(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN 1 ELSE 0 END) as saidas_almoco,
    SUM(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN 1 ELSE 0 END) as entradas_tarde,
    SUM(CASE WHEN rp.tipo_registro = 'saida' THEN 1 ELSE 0 END) as saidas,
    COUNT(DISTINCT rp.funcionario_id) as funcionarios_registraram,
    ROUND(AVG(rp.qualidade_biometria), 2) as qualidade_media_biometria
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
WHERE f.status_cadastro = 'Ativo'
GROUP BY DATE(rp.data_hora)
ORDER BY data_registro DESC;

-- VIEW DE ESTATÍSTICAS DO SISTEMA
CREATE VIEW vw_estatisticas_sistema AS
SELECT
    (SELECT COUNT(*) FROM funcionarios WHERE status_cadastro = 'Ativo') AS funcionarios_ativos,
    (SELECT COUNT(*) FROM funcionarios WHERE status_cadastro = 'Inativo') AS funcionarios_inativos,
    (SELECT COUNT(*) FROM funcionarios WHERE (digital_dedo1 IS NOT NULL OR digital_dedo2 IS NOT NULL) AND status_cadastro = 'Ativo') AS funcionarios_com_biometria,
    (SELECT COUNT(*) FROM funcionarios WHERE (digital_dedo1 IS NULL AND digital_dedo2 IS NULL) AND status_cadastro = 'Ativo') AS funcionarios_sem_biometria,
    (SELECT COUNT(*) FROM registros_ponto WHERE DATE(data_hora) = CURDATE()) AS registros_hoje,
    (SELECT COUNT(*) FROM registros_ponto WHERE YEARWEEK(data_hora, 1) = YEARWEEK(NOW(), 1)) AS registros_semana_atual,
    (SELECT COUNT(*) FROM registros_ponto WHERE YEAR(data_hora) = YEAR(NOW()) AND MONTH(data_hora) = MONTH(NOW())) AS registros_mes_atual;

-- ========================================
-- 4. FUNCTIONS E PROCEDURES
-- ========================================

DELIMITER //

-- FUNÇÃO PARA VALIDAR REGISTROS DUPLICADOS - CORRIGIDA
CREATE FUNCTION ValidarRegistroDuplicado(
    p_funcionario_id INT UNSIGNED,
    p_tipo_registro VARCHAR(20),
    p_data_registro DATE
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
SQL SECURITY DEFINER
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    SELECT COUNT(*) INTO v_count
    FROM registros_ponto
    WHERE funcionario_id = p_funcionario_id
    AND tipo_registro = p_tipo_registro
    AND DATE(data_hora) = p_data_registro;
    
    RETURN v_count > 0;
END //

-- FUNÇÃO PARA VERIFICAR INTEGRIDADE BIOMÉTRICA - CORRIGIDA
CREATE FUNCTION VerificarIntegridadeBiometrica(p_funcionario_id INT UNSIGNED)
RETURNS JSON
DETERMINISTIC
READS SQL DATA
SQL SECURITY DEFINER
BEGIN
    DECLARE resultado JSON;
    DECLARE tem_dedo1 BOOLEAN DEFAULT FALSE;
    DECLARE tem_dedo2 BOOLEAN DEFAULT FALSE;
    DECLARE total_registros INT DEFAULT 0;
    DECLARE nome_funcionario VARCHAR(100) DEFAULT '';

    -- Verificar se funcionário existe
    SELECT
        (digital_dedo1 IS NOT NULL) AS dedo1,
        (digital_dedo2 IS NOT NULL) AS dedo2,
        nome_completo
    INTO tem_dedo1, tem_dedo2, nome_funcionario
    FROM funcionarios
    WHERE id = p_funcionario_id AND status_cadastro = 'Ativo'
    LIMIT 1;

    -- Se funcionário não existe, retornar erro
    IF nome_funcionario = '' THEN
        SET resultado = JSON_OBJECT(
            'erro', TRUE,
            'mensagem', 'Funcionário não encontrado ou inativo'
        );
        RETURN resultado;
    END IF;

    SELECT COUNT(*) INTO total_registros
    FROM registros_ponto
    WHERE funcionario_id = p_funcionario_id;

    SET resultado = JSON_OBJECT(
        'funcionario_id', p_funcionario_id,
        'nome_funcionario', nome_funcionario,
        'tem_biometria_dedo1', tem_dedo1,
        'tem_biometria_dedo2', tem_dedo2,
        'total_registros_ponto', total_registros,
        'status_biometrico', CASE 
            WHEN tem_dedo1 OR tem_dedo2 THEN 'configurado' 
            ELSE 'nao_configurado' 
        END,
        'erro', FALSE
    );

    RETURN resultado;
END //

-- PROCEDURE PARA RELATÓRIO DE HORAS TRABALHADAS - CORRIGIDA
CREATE PROCEDURE RelatorioHorasTrabalhadas(
    IN p_funcionario_id INT UNSIGNED,
    IN p_data_inicio DATE,
    IN p_data_fim DATE
)
READS SQL DATA
SQL SECURITY DEFINER
BEGIN
    -- Validar parâmetros
    IF p_data_inicio > p_data_fim THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Data de início não pode ser maior que data fim';
    END IF;
    
    IF DATEDIFF(p_data_fim, p_data_inicio) > 365 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Período não pode ser maior que 365 dias';
    END IF;

    SELECT 
        f.id as funcionario_id,
        f.nome_completo,
        f.matricula_empresa,
        f.cargo,
        f.setor_obra as setor,
        DATE(rp.data_hora) AS data_trabalho,
        MIN(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN TIME(rp.data_hora) END) AS entrada_manha,
        MAX(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN TIME(rp.data_hora) END) AS saida_almoco,
        MIN(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN TIME(rp.data_hora) END) AS entrada_tarde,
        MAX(CASE WHEN rp.tipo_registro = 'saida' THEN TIME(rp.data_hora) END) AS saida,
        COUNT(*) AS total_registros,
        -- Cálculo de horas trabalhadas mais preciso
        CASE 
            WHEN COUNT(CASE WHEN rp.tipo_registro IN ('entrada_manha', 'saida') THEN 1 END) >= 2 THEN
                ROUND(
                    (TIMESTAMPDIFF(MINUTE,
                        MIN(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN rp.data_hora END),
                        MAX(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN rp.data_hora END)
                    ) +
                    TIMESTAMPDIFF(MINUTE,
                        MIN(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN rp.data_hora END),
                        MAX(CASE WHEN rp.tipo_registro = 'saida' THEN rp.data_hora END)
                    )) / 60.0, 2
                )
            ELSE 0 
        END AS horas_trabalhadas
    FROM registros_ponto rp
    INNER JOIN funcionarios f ON rp.funcionario_id = f.id
    WHERE (p_funcionario_id IS NULL OR rp.funcionario_id = p_funcionario_id)
    AND DATE(rp.data_hora) BETWEEN p_data_inicio AND p_data_fim
    AND f.status_cadastro = 'Ativo'
    GROUP BY f.id, f.nome_completo, f.matricula_empresa, f.cargo, f.setor_obra, DATE(rp.data_hora)
    HAVING total_registros > 0
    ORDER BY f.nome_completo, DATE(rp.data_hora) DESC;
END //

-- PROCEDURE PARA LIMPEZA DE LOGS ANTIGOS - CORRIGIDA
CREATE PROCEDURE LimparLogsAntigos(IN dias_para_manter INT UNSIGNED)
SQL SECURITY DEFINER
BEGIN
    DECLARE registros_removidos INT DEFAULT 0;
    
    -- Validar parâmetro
    IF dias_para_manter < 7 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Mínimo de 7 dias deve ser mantido nos logs';
    END IF;

    DELETE FROM logs_sistema
    WHERE data_hora < DATE_SUB(NOW(), INTERVAL dias_para_manter DAY);
    
    SET registros_removidos = ROW_COUNT();
    
    SELECT 
        registros_removidos AS registros_removidos,
        dias_para_manter AS dias_mantidos,
        NOW() AS data_limpeza;
        
    -- Log da limpeza
    INSERT INTO logs_sistema (acao, tabela_afetada, detalhes, data_hora) VALUES 
    ('LIMPEZA_LOGS', 'logs_sistema', 
     JSON_OBJECT('registros_removidos', registros_removidos, 'dias_mantidos', dias_para_manter), 
     NOW());
END //

DELIMITER ;

-- ========================================
-- 5. TRIGGERS DE AUDITORIA - CORRIGIDOS
-- ========================================

DELIMITER //

-- TRIGGER PARA AUDITORIA DE FUNCIONÁRIOS - CORRIGIDO
CREATE TRIGGER tr_funcionarios_audit_update
AFTER UPDATE ON funcionarios
FOR EACH ROW
BEGIN
    -- Só registra se houve mudança significativa
    IF (OLD.nome_completo != NEW.nome_completo OR 
        OLD.status_cadastro != NEW.status_cadastro OR
        (OLD.digital_dedo1 IS NULL) != (NEW.digital_dedo1 IS NULL) OR
        (OLD.digital_dedo2 IS NULL) != (NEW.digital_dedo2 IS NULL)) THEN
        
        INSERT INTO logs_sistema (
            acao, tabela_afetada, registro_id, detalhes, data_hora
        ) VALUES (
            'UPDATE_FUNCIONARIO',
            'funcionarios',
            NEW.id,
            JSON_OBJECT(
                'funcionario_id', NEW.id,
                'nome', NEW.nome_completo,
                'mudancas', JSON_OBJECT(
                    'nome_alterado', OLD.nome_completo != NEW.nome_completo,
                    'status_alterado', OLD.status_cadastro != NEW.status_cadastro,
                    'biometria_dedo1_alterado', (OLD.digital_dedo1 IS NULL) != (NEW.digital_dedo1 IS NULL),
                    'biometria_dedo2_alterado', (OLD.digital_dedo2 IS NULL) != (NEW.digital_dedo2 IS NULL)
                )
            ),
            NOW()
        );
    END IF;
END //

-- TRIGGER PARA AUDITORIA DE REGISTROS DE PONTO - CORRIGIDO
CREATE TRIGGER tr_registros_ponto_audit_insert
AFTER INSERT ON registros_ponto
FOR EACH ROW
BEGIN
    DECLARE nome_funcionario VARCHAR(100);
    
    -- Buscar nome do funcionário
    SELECT nome_completo INTO nome_funcionario
    FROM funcionarios 
    WHERE id = NEW.funcionario_id
    LIMIT 1;
    
    INSERT INTO logs_sistema (
        acao, tabela_afetada, registro_id, usuario_id, detalhes, data_hora
    ) VALUES (
        'INSERT_REGISTRO_PONTO',
        'registros_ponto',
        NEW.id,
        NEW.criado_por,
        JSON_OBJECT(
            'funcionario_id', NEW.funcionario_id,
            'nome_funcionario', COALESCE(nome_funcionario, 'N/A'),
            'tipo_registro', NEW.tipo_registro,
            'metodo_registro', NEW.metodo_registro,
            'data_hora', NEW.data_hora,
            'qualidade_biometria', NEW.qualidade_biometria
        ),
        NOW()
    );
END //

DELIMITER ;

-- ========================================
-- 6. DADOS INICIAIS
-- ========================================

-- INSERIR USUÁRIO ADMINISTRADOR PADRÃO
INSERT INTO usuarios (usuario, senha, data_ultimo_acesso) VALUES 
('admin', 'scrypt:32768:8:1$By9zDLj5suoxDGqU$c9a6953681714a7075a3f0219c995c6ceab167ecfba9d8c4b74a9c6d1e07df4c03a37badc673e58035ae8210171dd2014939b4490ddb2cfe2947e246c71ff765', NOW());

-- INSERIR PERMISSÕES DO ADMINISTRADOR
INSERT INTO permissoes (usuario_id, nivel_acesso) VALUES 
(1, 'admin');

-- ========================================
-- 7. VERIFICAÇÃO FINAL
-- ========================================

-- RESTAURAR CONFIGURAÇÕES
SET FOREIGN_KEY_CHECKS = 1;

-- VERIFICAR CRIAÇÃO DAS ESTRUTURAS
SELECT 
    'Banco criado com sucesso!' as status,
    DATABASE() as banco_atual,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'controle_ponto' AND table_name = 'usuarios') as tb_usuarios,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'controle_ponto' AND table_name = 'funcionarios') as tb_funcionarios,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'controle_ponto' AND table_name = 'registros_ponto') as tb_registros_ponto,
    (SELECT COUNT(*) FROM information_schema.views WHERE table_schema = 'controle_ponto' AND table_name = 'vw_relatorio_pontos') as view_relatorios,
    (SELECT COUNT(*) FROM information_schema.views WHERE table_schema = 'controle_ponto' AND table_name = 'vw_estatisticas_pontos') as view_estatisticas,
    (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'controle_ponto' AND routine_name = 'ValidarRegistroDuplicado') as function_validacao,
    (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'controle_ponto' AND routine_name = 'RelatorioHorasTrabalhadas') as procedure_relatorio,
    NOW() as data_criacao;

-- LOG DE INICIALIZAÇÃO
INSERT INTO logs_sistema (acao, tabela_afetada, detalhes, data_hora) VALUES 
('INICIALIZACAO_BANCO', 'sistema', 
 JSON_OBJECT('versao', '1.2', 'data_criacao', NOW(), 'status', 'sucesso'), 
 NOW());

-- VERIFICAÇÃO DE INTEGRIDADE
SELECT 
    'Verificação de Integridade Concluída' as resultado,
    (SELECT COUNT(*) FROM usuarios) as usuarios_criados,
    (SELECT COUNT(*) FROM permissoes) as permissoes_criadas,
    (SELECT COUNT(*) FROM logs_sistema) as logs_iniciais;

-- ========================================
-- FIM DO SCRIPT - BANCO COMPLETO CRIADO E REVISADO
-- ======================================== 