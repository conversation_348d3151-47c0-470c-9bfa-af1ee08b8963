#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste das Rotas Corretas da Empresa Principal
=============================================

Script para testar as rotas corretas baseado na análise do blueprint.

Data: 07/07/2025
"""

import requests

# Configurações
BASE_URL = "http://************"
LOGIN_URL = f"{BASE_URL}/login"

def testar_rotas_corretas():
    """Testa as rotas corretas da empresa principal"""
    print("🧪 TESTE DAS ROTAS CORRETAS DA EMPRESA PRINCIPAL")
    print("=" * 60)
    
    # Criar sessão e fazer login
    session = requests.Session()
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    
    if response.status_code not in [200, 302]:
        print(f"❌ Falha no login: {response.status_code}")
        return
    
    print("✅ Login realizado com sucesso")
    
    # Baseado na análise do código, o blueprint não tem prefixo
    # As rotas são registradas diretamente
    urls_para_testar = [
        # Rota de teste do blueprint
        ("/teste", "Teste do blueprint"),
        
        # Rota principal (index) - pode estar em /empresa_principal ou /
        ("/", "Dashboard principal"),
        
        # Rotas específicas que vimos no código
        ("/clientes", "Página de clientes"),
        ("/alocacoes", "Página de alocações"),
        ("/api/empresa-principal", "API empresa principal"),
        
        # Tentar com prefixo também
        ("/empresa_principal/teste", "Teste com prefixo"),
        ("/empresa_principal/clientes", "Clientes com prefixo"),
        ("/empresa_principal/alocacoes", "Alocações com prefixo"),
    ]
    
    print(f"\n📋 TESTANDO ROTAS:")
    print("-" * 40)
    
    rotas_funcionando = []
    
    for url, descricao in urls_para_testar:
        try:
            full_url = BASE_URL + url
            response = session.get(full_url)
            
            status_icon = "✅" if response.status_code == 200 else "❌"
            print(f"   {status_icon} {url}: {response.status_code} - {descricao}")
            
            if response.status_code == 200:
                rotas_funcionando.append((url, descricao))
                
                # Verificar se é a página correta
                content = response.text.lower()
                if "alocação" in content:
                    print(f"      🎯 PÁGINA DE ALOCAÇÕES ENCONTRADA!")
                elif "empresa principal" in content:
                    print(f"      🏢 Conteúdo de empresa principal encontrado")
                elif "blueprint" in content and "funcionando" in content:
                    print(f"      🔧 Rota de teste do blueprint funcionando")
            
        except Exception as e:
            print(f"   ❌ {url}: Erro - {e}")
    
    print(f"\n📊 RESUMO:")
    print(f"   Rotas funcionando: {len(rotas_funcionando)}")
    
    if rotas_funcionando:
        print(f"\n✅ ROTAS FUNCIONAIS:")
        for url, desc in rotas_funcionando:
            print(f"   - {url}: {desc}")
    
    return rotas_funcionando

def testar_especificamente_alocacoes():
    """Testa especificamente a página de alocações"""
    print(f"\n🎯 TESTE ESPECÍFICO DA PÁGINA DE ALOCAÇÕES")
    print("-" * 50)
    
    session = requests.Session()
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    
    # Testar diferentes possibilidades para alocações
    urls_alocacoes = [
        "/alocacoes",
        "/empresa_principal/alocacoes", 
        "/alocacao",
        "/funcionarios/alocacoes"
    ]
    
    for url in urls_alocacoes:
        try:
            response = session.get(BASE_URL + url)
            print(f"   {url}: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text.lower()
                if "alocação" in content or "funcionário" in content:
                    print(f"      🎯 PÁGINA DE ALOCAÇÕES ENCONTRADA EM: {url}")
                    
                    # Verificar se há erro na página
                    if "erro ao carregar" in content:
                        print(f"      ❌ Erro detectado: 'erro ao carregar alocações'")
                    else:
                        print(f"      ✅ Página carregou sem erros aparentes")
                    
                    return url
                    
        except Exception as e:
            print(f"   ❌ {url}: {e}")
    
    return None

if __name__ == "__main__":
    # Testar rotas gerais
    rotas_funcionando = testar_rotas_corretas()
    
    # Testar especificamente alocações
    url_alocacoes = testar_especificamente_alocacoes()
    
    if url_alocacoes:
        print(f"\n🎉 PÁGINA DE ALOCAÇÕES ENCONTRADA!")
        print(f"✅ URL correta: {url_alocacoes}")
    else:
        print(f"\n❌ PÁGINA DE ALOCAÇÕES NÃO ENCONTRADA")
        print("💡 Pode haver problema na rota ou na implementação")
