{% extends "base.html" %}

{% block title %}{{ funcionario.nome_completo }} - Det<PERSON><PERSON>{% endblock %}

{% block extra_css %}
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #4fbdba;
            --primary-hover: #26a69a;
            --background-color: #f9fafb;
            --card-background: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        body {
            background-color: var(--background-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            color: white;
        }

        .info-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .registros-table {
            background: var(--card-background);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
            padding: 1rem;
        }

        .table td {
            border: none;
            padding: 1rem;
            vertical-align: middle;
        }

        .horario-cell {
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
        }

        .horario-cell:hover {
            background-color: #f3f4f6;
        }

        .horario-editavel {
            background-color: #fef3c7;
            border: 1px dashed #f59e0b;
        }

        .badge-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .modal-content {
            border-radius: 12px;
            border: none;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .form-control {
            border-radius: 6px;
            border: 1px solid var(--border-color);
            padding: 0.5rem 0.75rem;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
        }

        .logs-timeline {
            max-height: 400px;
            overflow-y: auto;
        }

        .timeline-item {
            border-left: 2px solid var(--border-color);
            padding-left: 1rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--primary-color);
        }

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background-color: rgba(79, 189, 186, 0.05);
        }

        .upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(79, 189, 186, 0.1);
        }
    </style>
{% endblock %}

{% block content %}
        <!-- Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-user me-3"></i>
                        {{ funcionario.nome_completo }}
                    </h1>
                    <p class="mb-0 opacity-90">
                        {{ funcionario.cargo }} - {{ funcionario.empresa_nome }}
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light me-2" onclick="window.history.back()">
                        <i class="fas fa-arrow-left me-2"></i>Voltar
                    </button>
                    <button class="btn btn-light" onclick="gerarRelatorio()">
                        <i class="fas fa-file-pdf me-2"></i>Relatório
                    </button>
                </div>
            </div>
        </div>

        <!-- Informações do Funcionário -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="info-card">
                    <h5 class="mb-3">Informações Pessoais</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>CPF:</strong> {{ funcionario.cpf or 'N/A' }}</p>
                            <p><strong>Setor:</strong> {{ funcionario.setor or 'N/A' }}</p>
                            <p><strong>Status:</strong> 
                                <span class="badge bg-success">{{ funcionario.status_cadastro }}</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Data Admissão:</strong> 
                                {% if funcionario.data_admissao %}
                                    {{ funcionario.data_admissao.strftime('%d/%m/%Y') }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </p>
                            <p><strong>Jornada:</strong> {{ funcionario.nome_jornada or 'N/A' }}</p>
                            {% if funcionario.seg_qui_entrada and funcionario.seg_qui_saida %}
                            <p><strong>Horário:</strong> 
                                {{ funcionario.seg_qui_entrada.strftime('%H:%M') }} às 
                                {{ funcionario.seg_qui_saida.strftime('%H:%M') }}
                            </p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-card text-center">
                    <h5 class="mb-3">Resumo do Mês</h5>
                    <div class="row">
                        <div class="col-6">
                            <div class="h4 text-success">{{ registros|length }}</div>
                            <small class="text-muted">Registros</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 text-warning">
                                {{ registros|selectattr('entrada', 'none')|list|length }}
                            </div>
                            <small class="text-muted">Faltas</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtros de Data -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Data Início</label>
                        <input type="date" class="form-control" id="dataInicio" 
                               value="{{ (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Data Fim</label>
                        <input type="date" class="form-control" id="dataFim" 
                               value="{{ datetime.now().strftime('%Y-%m-%d') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-primary" onclick="filtrarRegistros()">
                                <i class="fas fa-search me-2"></i>Filtrar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Histórico de Alocações -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    Histórico de Alocações em Clientes/Obras
                </h5>
            </div>
            <div class="card-body">
                {% if historico_alocacoes %}
                    <div class="row">
                        {% for alocacao in historico_alocacoes %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-start border-primary border-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="card-title mb-1">{{ alocacao.cliente_nome }}</h6>
                                            <small class="text-muted">{{ alocacao.cliente_fantasia }}</small>
                                        </div>
                                        <span class="badge {{ 'bg-success' if alocacao.ativo else 'bg-secondary' }}">
                                            {{ 'Ativo' if alocacao.ativo else 'Finalizado' }}
                                        </span>
                                    </div>
                                    <hr class="my-2">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="h6 text-primary mb-0">{{ alocacao.dias_trabalhados or 0 }}</div>
                                            <small class="text-muted">Dias</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h6 text-success mb-0">{{ "%.1f"|format(alocacao.total_horas_periodo or 0) }}h</div>
                                            <small class="text-muted">Horas</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h6 text-warning mb-0">{{ alocacao.faltas or 0 }}</div>
                                            <small class="text-muted">Faltas</small>
                                        </div>
                                    </div>
                                    <hr class="my-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ alocacao.data_inicio.strftime('%d/%m/%Y') }} -
                                        {{ alocacao.data_fim.strftime('%d/%m/%Y') if alocacao.data_fim else 'Atual' }}
                                    </small>
                                    {% if alocacao.nome_jornada %}
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ alocacao.nome_jornada }}
                                        ({{ alocacao.seg_qui_entrada.strftime('%H:%M') if alocacao.seg_qui_entrada else '' }} -
                                         {{ alocacao.seg_qui_saida.strftime('%H:%M') if alocacao.seg_qui_saida else '' }})
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p>Nenhuma alocação encontrada para este funcionário.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Tabela de Registros de Ponto -->
        <div class="registros-table">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Registros de Ponto com Histórico de Alocações
                </h5>
            </div>
            <div class="table-responsive">
                <table class="table table-hover" id="registrosTable">
                    <thead>
                        <tr>
                            <th>Data</th>
                            <th>Dia</th>
                            <th>Cliente/Obra</th>
                            <th>Entrada</th>
                            <th>Saída Almoço</th>
                            <th>Retorno Almoço</th>
                            <th>Saída</th>
                            <th>Total Horas</th>
                            <th>Status</th>
                            <th>Editar</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for registro in registros %}
                        <tr data-registro-id="{{ registro.id }}">
                            <td>{{ registro.data.strftime('%d/%m/%Y') }}</td>
                            <td>{{ registro.dia_semana }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if registro.status_trabalho == 'ALOCADO' %}
                                        <span class="badge bg-primary me-2">Cliente</span>
                                        <div>
                                            <div class="fw-semibold">{{ registro.cliente_nome }}</div>
                                            {% if registro.cliente_fantasia and registro.cliente_fantasia != registro.cliente_nome %}
                                                <small class="text-muted">{{ registro.cliente_fantasia }}</small>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <span class="badge bg-secondary me-2">Sede</span>
                                        <span>Empresa Principal</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'entrada', '{{ registro.entrada or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.entrada.strftime('%H:%M') if registro.entrada else '-' }}
                                </span>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'saida_almoco', '{{ registro.saida_almoco or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.saida_almoco.strftime('%H:%M') if registro.saida_almoco else '-' }}
                                </span>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'retorno_almoco', '{{ registro.retorno_almoco or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.retorno_almoco.strftime('%H:%M') if registro.retorno_almoco else '-' }}
                                </span>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'saida', '{{ registro.saida or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.saida.strftime('%H:%M') if registro.saida else '-' }}
                                </span>
                            </td>
                            <td>
                                <strong class="text-primary">{{ "%.2f"|format(registro.total_horas_dia) }}h</strong>
                            </td>
                            <td>
                                {% if registro.entrada %}
                                    <span class="badge bg-success">Presente</span>
                                {% else %}
                                    <span class="badge bg-danger">Falta</span>
                                {% endif %}
                                {% if registro.justificativa %}
                                    <span class="badge bg-info ms-1" title="{{ registro.justificativa }}">Justificado</span>
                                {% endif %}
                                {% if registro.editado_por %}
                                    <span class="badge bg-warning ms-1">Editado</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-primary"
                                        onclick="abrirModalEdicao('{{ registro.data.strftime('%Y-%m-%d') }}', {{ registro.id }})"
                                        title="Editar registro e justificativas">
                                    <i class="fas fa-edit me-1"></i>
                                    Editar
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Logs de Atividades -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    Histórico de Atividades
                </h5>
            </div>
            <div class="card-body">
                <div class="logs-timeline">
                    {% for log in logs %}
                    <div class="timeline-item">
                        <div class="d-flex justify-content-between">
                            <strong>{{ log.acao.replace('_', ' ').title() }}</strong>
                            <small class="text-muted">{{ log.data_acao.strftime('%d/%m/%Y %H:%M') }}</small>
                        </div>
                        <p class="mb-1">{{ log.detalhes }}</p>
                        <small class="text-muted">Por: {{ log.usuario_nome or 'Sistema' }}</small>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Editar Horário -->
    <div class="modal fade" id="modalEditarHorario" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        Editar Horário
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="formEditarHorario">
                        <input type="hidden" id="registroId">
                        <input type="hidden" id="campoHorario">
                        
                        <div class="mb-3">
                            <label class="form-label">Novo Horário</label>
                            <input type="time" class="form-control" id="novoHorario" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Justificativa (obrigatória)</label>
                            <textarea class="form-control" id="justificativa" rows="3" 
                                      placeholder="Descreva o motivo da alteração..." required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="salvarEdicao()">
                        <i class="fas fa-save me-2"></i>Salvar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Edição de Registro de Ponto -->
    <div class="modal fade" id="modalEdicaoRegistro" tabindex="-1" aria-labelledby="modalEdicaoRegistroLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-header border-0 bg-white pb-0">
                    <h4 class="modal-title text-dark fw-bold" id="modalEdicaoRegistroLabel">
                        <i class="fas fa-edit me-2 text-primary"></i>
                        Editar Registro de Ponto
                    </h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="formEdicaoRegistro">
                        <input type="hidden" id="registroData" name="data_registro">
                        <input type="hidden" id="funcionarioId" name="funcionario_id" value="{{ funcionario.id }}">

                        <!-- Informações do Funcionário -->
                        <div class="bg-light rounded-3 p-4 mb-4">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user text-primary me-2"></i>
                                        <div>
                                            <div class="text-muted small">Funcionário</div>
                                            <div class="fw-bold text-dark">{{ funcionario.nome_completo }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-id-badge text-primary me-2"></i>
                                        <div>
                                            <div class="text-muted small">Matrícula</div>
                                            <div class="fw-bold text-dark">{{ funcionario.matricula_empresa or '001' }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-calendar text-primary me-2"></i>
                                        <div>
                                            <div class="text-muted small">Data</div>
                                            <div class="fw-bold text-dark" id="dataRegistroDisplay">10/07/2025</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Registro de Ponto Atual -->
                        <div class="card mb-4 border-0 shadow-sm">
                            <div class="card-body p-4">
                                <!-- Header com badges -->
                                <div class="d-flex align-items-center justify-content-between mb-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-clock text-primary me-2"></i>
                                        <span class="fw-bold text-dark">Registro de Ponto</span>
                                        <span id="diaSemanaBadge" class="badge bg-secondary ms-3 px-2 py-1">QUI</span>
                                        <span id="statusBadge" class="badge bg-success ms-2 px-2 py-1">PRESENTE</span>
                                    </div>
                                    <div>
                                        <span id="empresaBadge" class="badge bg-primary px-3 py-2">EMPRESA PRINCIPAL</span>
                                    </div>
                                </div>

                                <!-- Horários em linha -->
                                <div class="row text-center mb-4" id="registroAtualDisplay">
                                    <div class="col">
                                        <div class="text-muted small mb-2">Entrada</div>
                                        <div class="fw-bold fs-2 text-dark" id="entradaDisplay">--:--</div>
                                    </div>
                                    <div class="col">
                                        <div class="text-muted small mb-2">Intervalo</div>
                                        <div class="fw-bold fs-2 text-dark" id="saidaAlmocoDisplay">--:--</div>
                                    </div>
                                    <div class="col">
                                        <div class="text-muted small mb-2">Retorno</div>
                                        <div class="fw-bold fs-2 text-dark" id="retornoAlmocoDisplay">--:--</div>
                                    </div>
                                    <div class="col">
                                        <div class="text-muted small mb-2">Saída</div>
                                        <div class="fw-bold fs-2 text-dark" id="saidaDisplay">--:--</div>
                                    </div>
                                    <div class="col">
                                        <div class="bg-light rounded p-3">
                                            <div class="text-muted small mb-2">Total</div>
                                            <div class="text-primary fw-bold fs-1" id="totalHorasDisplay">8.0h</div>
                                            <div class="text-muted small" id="horasTrabalhadasDisplay">Trabalhadas: 4.1h</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Área de Justificativa -->
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="d-flex align-items-center mb-2">
                                            <span id="justificadoBadge" class="badge bg-warning px-2 py-1 me-3">SAÍDA ANTECIPADA</span>
                                            <span class="text-dark fw-bold">Justificativa:</span>
                                        </div>
                                        <div class="border rounded p-3 bg-white" style="min-height: 80px;">
                                            <div class="text-muted" id="areaJustificativa">
                                                Aqui aparece a justificativa dada no momento da batida do ponto antecipado.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button class="btn btn-outline-primary btn-lg px-4 py-3" id="btnEnviarDocumento">
                                            <i class="fas fa-upload me-2"></i>
                                            AQUI BOTÃO ENVIAR DOCUMENTO
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Edição de Horários -->
                        <div class="bg-white rounded-3 border p-4 mb-4 shadow-sm">
                            <div class="d-flex align-items-center mb-4">
                                <i class="fas fa-edit text-primary me-2"></i>
                                <h6 class="mb-0 text-dark fw-bold">Editar Horários</h6>
                            </div>

                            <!-- Horários -->
                            <div class="row g-4 mb-4">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <label for="entrada" class="form-label text-muted fw-bold mb-3">Entrada</label>
                                        <input type="time" class="form-control form-control-lg text-center fw-bold fs-4 border-2"
                                               id="entrada" name="entrada" style="height: 60px;">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <label for="saida_almoco" class="form-label text-muted fw-bold mb-3">Intervalo</label>
                                        <input type="time" class="form-control form-control-lg text-center fw-bold fs-4 border-2"
                                               id="saida_almoco" name="saida_almoco" style="height: 60px;">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <label for="retorno_almoco" class="form-label text-muted fw-bold mb-3">Retorno</label>
                                        <input type="time" class="form-control form-control-lg text-center fw-bold fs-4 border-2"
                                               id="retorno_almoco" name="retorno_almoco" style="height: 60px;">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <label for="saida" class="form-label text-muted fw-bold mb-3">Saída</label>
                                        <input type="time" class="form-control form-control-lg text-center fw-bold fs-4 border-2"
                                               id="saida" name="saida" style="height: 60px;">
                                    </div>
                                </div>
                            </div>

                            <!-- Configurações -->
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <label for="metodo_registro" class="form-label text-muted fw-bold mb-3">Método de Registro</label>
                                    <select class="form-select form-select-lg border-2" id="metodo_registro" name="metodo_registro" style="height: 50px;">
                                        <option value="biometrico">Biométrico</option>
                                        <option value="manual">Manual</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="status_pontualidade" class="form-label text-muted fw-bold mb-3">Status Pontualidade</label>
                                    <select class="form-select form-select-lg border-2" id="status_pontualidade" name="status_pontualidade" style="height: 50px;">
                                        <option value="Pontual">Pontual</option>
                                        <option value="Atrasado">Atrasado</option>
                                        <option value="Saída Antecipada">Saída Antecipada</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Justificativa -->
                        <div class="card mb-4 border-0 bg-warning bg-opacity-10" id="cardJustificativa" style="display: none;">
                            <div class="card-header bg-transparent border-0 pb-2">
                                <h6 class="mb-0 text-warning"><i class="fas fa-exclamation-triangle me-2"></i>Justificativa Existente</h6>
                            </div>
                            <div class="card-body pt-2">
                                <div class="row g-3 mb-3">
                                    <div class="col-md-6">
                                        <div class="text-muted small">Tipo</div>
                                        <div class="fw-bold" id="tipoJustificativaDisplay">-</div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="text-muted small">Status</div>
                                        <div><span id="statusJustificativaDisplay" class="badge bg-warning fs-6 px-2 py-1">Pendente</span></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="text-muted small">Motivo</div>
                                    <div class="bg-white p-3 rounded border" id="motivoJustificativaDisplay">-</div>
                                </div>
                                <div id="documentoJustificativa" style="display: none;">
                                    <div class="text-muted small mb-2">Documento Anexado</div>
                                    <a href="#" id="linkDocumentoJustificativa" target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-file-pdf me-1"></i>Ver Documento
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Aprovação -->
                        <div class="card mb-4 border-0 bg-success bg-opacity-10" id="cardAprovacao" style="display: none;">
                            <div class="card-header bg-transparent border-0 pb-2">
                                <h6 class="mb-0 text-success"><i class="fas fa-check-circle me-2"></i>Aprovação da Justificativa</h6>
                            </div>
                            <div class="card-body pt-2">
                                <div class="row g-3 mb-3">
                                    <div class="col-md-6">
                                        <label for="status_aprovacao" class="form-label text-muted small">Status da Aprovação</label>
                                        <select class="form-select form-select-lg" id="status_aprovacao" name="status_aprovacao">
                                            <option value="pendente">Pendente</option>
                                            <option value="aprovada">Aprovada</option>
                                            <option value="reprovada">Reprovada</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="data_aprovacao" class="form-label text-muted small">Data de Aprovação</label>
                                        <input type="datetime-local" class="form-control form-control-lg" id="data_aprovacao" name="data_aprovacao">
                                    </div>
                                </div>
                                <div>
                                    <label for="observacoes_aprovador" class="form-label text-muted small">Observações do Aprovador</label>
                                    <textarea class="form-control form-control-lg" id="observacoes_aprovador" name="observacoes_aprovador" rows="3"
                                              placeholder="Observações sobre a aprovação/reprovação..."></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-0 pt-4">
                    <button type="button" class="btn btn-secondary btn-lg px-4" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-success btn-lg px-4" onclick="salvarRegistro()">
                        <i class="fas fa-save me-2"></i>Salvar Alterações
                    </button>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_js %}
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // Variáveis globais
        let registroAtual = null;
        let justificativaAtual = null;

        // ✅ CORREÇÃO: Função para carregar dados do registro atual automaticamente
        function carregarRegistroAtual() {
            console.log('=== CARREGANDO REGISTRO ATUAL ===');

            // Obter data atual no formato YYYY-MM-DD
            const hoje = new Date();
            const dataAtual = hoje.toISOString().split('T')[0];

            console.log('Data atual:', dataAtual);

            // Buscar dados do registro de hoje
            const funcionarioId = {{ funcionario.id }};
            const url = `/ponto-admin/api/registro-detalhes/${funcionarioId}/${dataAtual}`;

            console.log('Buscando registro atual em:', url);

            fetch(url, {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(data => {
                console.log('✅ Dados do registro atual recebidos:', data);

                if (data && data.success && data.registro) {
                    // Atualizar display com dados reais
                    preencherDisplayRegistro(data.registro);

                    // Preencher campos de edição também
                    preencherCamposEdicao(data.registro);

                    // Preencher justificativa se existir
                    if (data.justificativa) {
                        preencherJustificativa(data.justificativa);
                    }
                } else {
                    console.log('ℹ️ Nenhum registro encontrado para hoje');

                    // ✅ CORREÇÃO: Limpar campos quando não há dados
                    const registroVazio = {
                        entrada: null,
                        saida_almoco: null,
                        retorno_almoco: null,
                        saida: null,
                        metodo_registro: null,
                        status_pontualidade: 'Pontual'  // ✅ CORREÇÃO: Pontual para ocultar badge
                    };

                    // Atualizar display com valores vazios
                    preencherDisplayRegistro(registroVazio);

                    // Limpar campos de edição
                    preencherCamposEdicao(registroVazio);
                }
            })
            .catch(error => {
                console.warn('⚠️ Erro ao carregar registro atual:', error);
                // Manter valores padrão --:-- em caso de erro
            });
        }

        // Inicializar DataTable quando jQuery estiver disponível
        document.addEventListener('DOMContentLoaded', function() {
            // ✅ CORREÇÃO: Carregar registro atual automaticamente
            carregarRegistroAtual();

            // Aguardar jQuery carregar
            function initDataTable() {
                if (typeof $ !== 'undefined' && typeof $.fn.DataTable !== 'undefined') {
                    $('#registrosTable').DataTable({
                        language: {
                            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/pt-BR.json'
                        },
                        order: [[0, 'desc']], // Ordenar por data decrescente
                        pageLength: 10,
                        responsive: true
                    });
                } else {
                    // Tentar novamente em 100ms
                    setTimeout(initDataTable, 100);
                }
            }

            initDataTable();
        });

        // ✅ CORREÇÃO: Função para abrir modal de edição com sincronização forçada
        function abrirModalEdicao(data, registroId) {
            console.log('=== ABRINDO MODAL DE EDIÇÃO ===');
            console.log('Data:', data);
            console.log('Registro ID:', registroId);

            registroAtual = registroId;

            // Definir data no modal
            document.getElementById('registroData').value = data;
            document.getElementById('dataRegistroDisplay').textContent = formatarData(data);

            console.log('Dados definidos no modal, iniciando busca...');

            // ✅ CORREÇÃO: Forçar sincronização com dados atuais do display ANTES de abrir modal
            sincronizarCamposComDisplay();

            // Abrir modal primeiro
            const modal = new bootstrap.Modal(document.getElementById('modalEdicaoRegistro'));
            modal.show();

            // Aguardar o modal estar completamente aberto antes de buscar dados
            setTimeout(() => {
                console.log('Modal aberto, buscando dados...');
                buscarDadosRegistro(data);
            }, 500);

            console.log('Modal sendo aberto...');
        }

        // ✅ NOVA FUNÇÃO: Sincronizar campos de edição com o display atual
        function sincronizarCamposComDisplay() {
            console.log('=== SINCRONIZANDO CAMPOS COM DISPLAY ===');

            // Obter valores atuais do display
            const entradaDisplay = document.getElementById('entradaDisplay').textContent;
            const saidaAlmocoDisplay = document.getElementById('saidaAlmocoDisplay').textContent;
            const retornoAlmocoDisplay = document.getElementById('retornoAlmocoDisplay').textContent;
            const saidaDisplay = document.getElementById('saidaDisplay').textContent;

            // Criar objeto de registro baseado no display
            const registroAtualDisplay = {
                entrada: entradaDisplay !== '--:--' ? entradaDisplay : null,
                saida_almoco: saidaAlmocoDisplay !== '--:--' ? saidaAlmocoDisplay : null,
                retorno_almoco: retornoAlmocoDisplay !== '--:--' ? retornoAlmocoDisplay : null,
                saida: saidaDisplay !== '--:--' ? saidaDisplay : null,
                metodo_registro: 'Manual',
                status_pontualidade: 'Pontual'
            };

            console.log('Dados do display para sincronização:', registroAtualDisplay);

            // Preencher campos de edição com dados do display
            preencherCamposEdicao(registroAtualDisplay);

            console.log('✅ Sincronização concluída');
        }

        // Função para buscar dados do registro
        function buscarDadosRegistro(data) {
            console.log('=== INICIANDO BUSCA DE DADOS ===');

            const funcionarioId = {{ funcionario.id }};
            const url = `/ponto-admin/api/registro-detalhes/${funcionarioId}/${data}`;

            console.log('Funcionário ID:', funcionarioId);
            console.log('Data:', data);
            console.log('URL da API:', url);

            // Fazer requisição para buscar dados reais da API
            console.log('🔄 Fazendo requisição para:', url);

            // Fazer requisição para buscar dados reais
            fetch(url, {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => {
                    console.log('=== RESPOSTA RECEBIDA ===');
                    console.log('Status:', response.status);
                    console.log('Status Text:', response.statusText);
                    console.log('Content-Type:', response.headers.get('content-type'));

                    if (response.status === 200) {
                        // Verificar se é JSON ou HTML (página de login)
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            return response.json();
                        } else {
                            throw new Error('Resposta não é JSON - possível redirecionamento para login');
                        }
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                })
                .then(data => {
                    console.log('=== DADOS RECEBIDOS ===');
                    console.log('Dados completos:', data);

                    if (data && data.success) {
                        console.log('✅ API retornou sucesso! Dados recebidos:', data);
                        console.log('📝 Registro:', data.registro);
                        console.log('📄 Justificativa:', data.justificativa);
                        preencherFormulario(data.registro, data.justificativa);
                    } else {
                        console.error('❌ API retornou erro:', data.message || 'Erro desconhecido');
                        console.error('📊 Dados completos da resposta:', data);
                        alert('Erro ao carregar dados do registro: ' + (data.message || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    console.error('=== ERRO NA REQUISIÇÃO ===');
                    console.error('Erro:', error);

                    // Verificar se é problema de autenticação
                    if (error.message.includes('login') || error.message.includes('JSON')) {
                        console.error('❌ PROBLEMA DE AUTENTICAÇÃO DETECTADO');
                        alert('Sessão expirada. Por favor, faça login novamente.');
                        window.location.reload();
                    } else {
                        alert('Erro ao carregar dados do registro: ' + error.message);
                    }
                });
        }

        // Função para preencher formulário
        function preencherFormulario(registro, justificativa) {
            console.log('=== PREENCHENDO FORMULÁRIO ===');
            console.log('Registro recebido:', registro);
            console.log('Justificativa recebida:', justificativa);

            if (!registro) {
                console.error('❌ Registro é null ou undefined!');
                return;
            }

            // Preencher display do registro atual
            preencherDisplayRegistro(registro);

            // Preencher campos de edição
            preencherCamposEdicao(registro);

            // Preencher justificativa se existir
            if (justificativa) {
                preencherJustificativa(justificativa);
            }
        }

        // Função para preencher o display do registro atual
        function preencherDisplayRegistro(registro) {
            console.log('=== PREENCHENDO DISPLAY DO REGISTRO ===');

            // Atualizar horários no display (formato simples)
            document.getElementById('entradaDisplay').textContent = registro.entrada || '--:--';
            document.getElementById('saidaAlmocoDisplay').textContent = registro.saida_almoco || '--:--';
            document.getElementById('retornoAlmocoDisplay').textContent = registro.retorno_almoco || '--:--';
            document.getElementById('saidaDisplay').textContent = registro.saida || '--:--';

            // Calcular horas trabalhadas
            const horasTrabalhadas = calcularHorasTrabalhadas(registro);
            document.getElementById('totalHorasDisplay').textContent = '8.0h';
            document.getElementById('horasTrabalhadasDisplay').textContent = `Trabalhadas: ${horasTrabalhadas}`;

            // Atualizar status
            const statusBadge = document.getElementById('statusBadge');
            statusBadge.textContent = 'PRESENTE';
            statusBadge.className = 'badge bg-success ms-2 px-3 py-2';

            // ✅ CORREÇÃO: Atualizar badge de justificativa sempre (mostrar ou ocultar)
            const justificadoBadge = document.getElementById('justificadoBadge');
            if (registro.status_pontualidade && registro.status_pontualidade !== 'Pontual') {
                // Mostrar badge com status de irregularidade
                justificadoBadge.style.display = 'inline-block';
                justificadoBadge.textContent = registro.status_pontualidade.toUpperCase();
                justificadoBadge.className = 'badge bg-warning px-3 py-2 me-3';
                console.log('✅ Badge atualizado:', registro.status_pontualidade);
            } else {
                // ✅ CORREÇÃO: Ocultar badge quando status é Pontual
                justificadoBadge.style.display = 'none';
                console.log('✅ Badge ocultado - status Pontual');
            }
        }

        // Função para calcular horas trabalhadas
        function calcularHorasTrabalhadas(registro) {
            try {
                if (!registro.entrada) return '0h 0min';

                let totalMinutos = 0;

                // Cenário 1: Tem entrada e saída do almoço (B1 e B2)
                // Calcular período trabalhado antes do intervalo
                if (registro.entrada && registro.saida_almoco) {
                    const entrada = new Date(`2000-01-01 ${registro.entrada}`);
                    const saidaAlmoco = new Date(`2000-01-01 ${registro.saida_almoco}`);
                    const minutosAntes = (saidaAlmoco - entrada) / (1000 * 60);
                    totalMinutos += minutosAntes;
                    console.log(`Período antes do intervalo: ${minutosAntes} minutos`);
                }

                // Cenário 2: Tem retorno do almoço e saída (B3 e B4)
                // Calcular período trabalhado após o intervalo
                if (registro.retorno_almoco && registro.saida) {
                    const retornoAlmoco = new Date(`2000-01-01 ${registro.retorno_almoco}`);
                    const saida = new Date(`2000-01-01 ${registro.saida}`);
                    const minutosDepois = (saida - retornoAlmoco) / (1000 * 60);
                    totalMinutos += minutosDepois;
                    console.log(`Período após o intervalo: ${minutosDepois} minutos`);
                }

                // Cenário 3: Só tem entrada e saída (sem intervalo)
                // Calcular período trabalhado direto
                if (registro.entrada && registro.saida && !registro.saida_almoco && !registro.retorno_almoco) {
                    const entrada = new Date(`2000-01-01 ${registro.entrada}`);
                    const saida = new Date(`2000-01-01 ${registro.saida}`);
                    totalMinutos = (saida - entrada) / (1000 * 60);
                    console.log(`Período direto (sem intervalo): ${totalMinutos} minutos`);
                }

                // Cenário 4: Só tem entrada (ainda trabalhando ou saiu sem bater)
                // Neste caso, não podemos calcular horas trabalhadas
                if (registro.entrada && !registro.saida && !registro.saida_almoco) {
                    console.log('Só tem entrada, não é possível calcular');
                    return '0h 0min';
                }

                console.log(`Total de minutos trabalhados: ${totalMinutos}`);

                // Converter para horas e minutos
                const horas = Math.floor(totalMinutos / 60);
                const minutos = Math.round(totalMinutos % 60);

                return `${horas}h ${minutos}min`;
            } catch (error) {
                console.error('Erro ao calcular horas:', error);
                return '0h 0min';
            }
        }

        // ✅ CORREÇÃO: Função para preencher campos de edição com limpeza
        function preencherCamposEdicao(registro) {
            console.log('=== PREENCHENDO CAMPOS DE EDIÇÃO ===');
            console.log('Registro recebido para edição:', registro);

            // ✅ CORREÇÃO: SEMPRE limpar e preencher campos (não apenas se existir)
            // Isso garante que campos antigos sejam removidos quando não há dados

            // Preencher campos de horário (sempre definir, mesmo que vazio)
            document.getElementById('entrada').value = registro.entrada || '';
            document.getElementById('saida_almoco').value = registro.saida_almoco || '';
            document.getElementById('retorno_almoco').value = registro.retorno_almoco || '';
            document.getElementById('saida').value = registro.saida || '';

            // Preencher outros campos (sempre definir, mesmo que vazio)
            document.getElementById('metodo_registro').value = registro.metodo_registro || 'Manual';
            document.getElementById('status_pontualidade').value = registro.status_pontualidade || 'Pontual';

            console.log('✅ Campos de edição preenchidos e sincronizados');
            console.log('Entrada:', document.getElementById('entrada').value);
            console.log('Saída Almoço:', document.getElementById('saida_almoco').value);
            console.log('Retorno Almoço:', document.getElementById('retorno_almoco').value);
            console.log('Saída:', document.getElementById('saida').value);
        }

        // Função para preencher justificativa
        function preencherJustificativa(justificativa) {
            console.log('=== PREENCHENDO JUSTIFICATIVA ===');

            const areaJustificativa = document.getElementById('areaJustificativa');
            const cardJustificativa = document.getElementById('cardJustificativa');
            const cardAprovacao = document.getElementById('cardAprovacao');

            if (justificativa && justificativa.motivo) {
                // Preencher área de justificativa no layout principal
                areaJustificativa.textContent = justificativa.motivo;
                areaJustificativa.className = 'text-dark';

                // Mostrar cards de justificativa e aprovação se existirem
                if (cardJustificativa) {
                    cardJustificativa.style.display = 'block';
                    document.getElementById('tipoJustificativaDisplay').textContent = justificativa.tipo_justificativa || '-';
                    document.getElementById('motivoJustificativaDisplay').textContent = justificativa.motivo || '-';

                    // Status da justificativa
                    const statusJust = document.getElementById('statusJustificativaDisplay');
                    statusJust.textContent = justificativa.status_aprovacao || 'Pendente';
                    statusJust.className = `badge fs-6 px-2 py-1 ${justificativa.status_aprovacao === 'aprovada' ? 'bg-success' :
                                                    justificativa.status_aprovacao === 'reprovada' ? 'bg-danger' : 'bg-warning'}`;

                    // Documento se existir
                    if (justificativa.documento_nome) {
                        document.getElementById('documentoJustificativa').style.display = 'block';
                        document.getElementById('linkDocumentoJustificativa').href = justificativa.documento_caminho || '#';
                    }
                }

                // Mostrar card de aprovação se for admin
                if (cardAprovacao) {
                    cardAprovacao.style.display = 'block';
                    if (justificativa.status_aprovacao) document.getElementById('status_aprovacao').value = justificativa.status_aprovacao;
                    if (justificativa.data_aprovacao) document.getElementById('data_aprovacao').value = justificativa.data_aprovacao;
                    if (justificativa.observacoes_aprovador) document.getElementById('observacoes_aprovador').value = justificativa.observacoes_aprovador;
                }
            } else {
                // Manter texto padrão se não houver justificativa
                areaJustificativa.textContent = 'Aqui aparece a justificativa dada no momento da batida do ponto antecipado.';
                areaJustificativa.className = 'text-muted';
            }

            console.log('✅ Justificativa preenchida');
        }








        // Função para salvar registro
        function salvarRegistro() {
            const form = document.getElementById('formEdicaoRegistro');
            const formData = new FormData(form);

            // Adicionar dados adicionais
            formData.append('registro_id', registroAtual);
            if (justificativaAtual) {
                formData.append('justificativa_id', justificativaAtual.id);
            }

            // Mostrar loading
            const btnSalvar = event.target;
            const textoOriginal = btnSalvar.innerHTML;
            btnSalvar.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Salvando...';
            btnSalvar.disabled = true;

            fetch('/ponto-admin/salvar-registro', {
                method: 'POST',
                credentials: 'same-origin',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Registro salvo com sucesso!');
                    location.reload(); // Recarregar página para mostrar alterações
                } else {
                    alert('Erro ao salvar: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro de comunicação com o servidor');
            })
            .finally(() => {
                btnSalvar.innerHTML = textoOriginal;
                btnSalvar.disabled = false;
            });
        }

        // Função auxiliar para formatar data
        function formatarData(data) {
            const [ano, mes, dia] = data.split('-');
            return `${dia}/${mes}/${ano}`;
        }

        // Função para enviar documento
        function enviarDocumento() {
            // Criar input file oculto
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.pdf,.jpg,.jpeg,.png,.doc,.docx';
            input.style.display = 'none';

            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    // Verificar tamanho (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('Arquivo muito grande. Máximo 5MB permitido.');
                        return;
                    }

                    // Verificar tipo
                    const tiposPermitidos = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg',
                                           'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
                    if (!tiposPermitidos.includes(file.type)) {
                        alert('Tipo de arquivo não permitido. Use PDF, JPG, PNG, DOC ou DOCX.');
                        return;
                    }

                    // Aqui você pode implementar o upload do arquivo
                    alert(`Arquivo selecionado: ${file.name}\nTamanho: ${(file.size / 1024 / 1024).toFixed(2)} MB`);

                    // TODO: Implementar upload real
                    console.log('Arquivo para upload:', file);
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        // Adicionar evento ao botão de enviar documento
        document.addEventListener('DOMContentLoaded', function() {
            const btnEnviarDocumento = document.getElementById('btnEnviarDocumento');
            if (btnEnviarDocumento) {
                btnEnviarDocumento.addEventListener('click', enviarDocumento);
            }
        });


    </script>
{% endblock %}
