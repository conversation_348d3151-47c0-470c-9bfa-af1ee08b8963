<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title><PERSON>e de <PERSON></title>
    <link rel="stylesheet" href="/static/style-cadastrar.css">
    <style>
        .controle-btns {
            display: flex;
            gap: 10px;
 painel            margin-top: 20px;
        }
        .controle-btns button {
            padding: 8px 18px;
            border-radius: 4px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }
        .btn-visualizar { background: #4fbdba; color: #fff; }
        .btn-visualizar:hover { background: #3da8a6; }
        .btn-editar { background: #ffc107; color: #1a2634; }
        .btn-editar:hover { background: #e0a800; }
        .btn-apagar { background: #dc3545; color: #fff; }
        .btn-apagar:hover { background: #b52a37; }
        .controle-navegacao {
            display: flex;
            justify-content: space-between;
            margin: 30px 0 10px 0;
        }
        .controle-navegacao button {
            background: #1a2634;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 8px 18px;
            font-weight: 500;
            cursor: pointer;
        }
        .controle-navegacao button:disabled {
            background: #ccc;
            color: #888;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <button class="tab-btn active" onclick="window.location.href='/cadastrar'">Cadastro de Funcionários</button>
            {% if is_admin %}
            <button class="tab-btn" onclick="window.location.href='/configurar_usuarios'">Configurar Usuários</button>
            {% endif %}
            <button class="tab-btn" onclick="window.location.href='/logout'">Sair</button>
        </div>
        <div class="main-content">
            <h2>Controle de Ponto</h2>
            <h3>Funcionário Cadastrado</h3>
            {% if funcionarios|length > 0 %}
                {% set func = funcionarios[funcionario_index|default(0)] %}
                <table>
                    <tr><th>ID</th><td>{{ func.id }}</td></tr>
                    <tr><th>Nome Completo</th><td>{{ func.nome_completo }}</td></tr>
                    <tr><th>CPF</th><td>{{ func.cpf }}</td></tr>
                    <tr><th>Matrícula</th><td>{{ func.matricula_empresa }}</td></tr>
                    <tr><th>Setor/Obra</th><td>{{ func.setor_obra }}</td></tr>
                    <tr><th>Status</th><td>{{ func.status_cadastro }}</td></tr>
                </table>
                <div class="controle-btns">
                    <form method="GET" action="/funcionario/{{ func.id }}">
                        <button type="submit" class="btn-visualizar">Visualizar</button>
                    </form>
                    {% if is_admin %}
                    <form method="GET" action="/funcionario/{{ func.id }}/editar">
                        <button type="submit" class="btn-editar">Editar</button>
                    </form>
                    <form method="POST" action="/funcionario/{{ func.id }}/apagar" onsubmit="return confirm('Tem certeza que deseja apagar este funcionário? Esta ação não poderá ser desfeita!');">
                        <button type="submit" class="btn-apagar">Apagar</button>
                    </form>
                    {% endif %}
                </div>
                <div class="controle-navegacao">
                    <form method="GET" action="/index?funcionario_index={{ funcionario_index|default(0) - 1 }}">
                        <button type="submit" {% if funcionario_index|default(0) <= 0 %}disabled{% endif %}>Anterior</button>
                    </form>
                    <form method="GET" action="/index?funcionario_index={{ funcionario_index|default(0) + 1 }}">
                        <button type="submit" {% if funcionario_index|default(0) >= (funcionarios|length - 1) %}disabled{% endif %}>Próximo</button>
                    </form>
                </div>
            {% else %}
                <p>Nenhum funcionário cadastrado.</p>
            {% endif %}
        </div>
    </div>
</body>
</html>

