#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para criar arquivo .env seguro
RLPONTO-WEB v1.0 - Correção Urgente #4

Desenvolvido por: AiNexus Tecnologia
Data: 10/01/2025
Objetivo: Gerar SECRET_KEY segura e criar arquivo .env
"""

import secrets
import os
from datetime import datetime

def gerar_secret_key():
    """Gera uma SECRET_KEY segura"""
    return secrets.token_hex(64)

def criar_arquivo_env():
    """Cria arquivo .env com configurações seguras"""
    
    secret_key = gerar_secret_key()
    
    conteudo_env = f"""# ===============================================
# RLPONTO-WEB v1.0 - Configurações de Produção
# ===============================================
# ARQUIVO CRÍTICO DE SEGURANÇA - NÃO COMPARTILHAR!
# Gerado automaticamente em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}

# 🔒 SEGURANÇA CRÍTICA
SECRET_KEY={secret_key}
FLASK_DEBUG=False
REQUIRE_HTTPS=False

# 🗄️ BANCO DE DADOS
DB_HOST=************
DB_USER=cavalcrod
DB_PASSWORD=200381
DB_NAME=controle_ponto

# 🌐 REDE E SERVIDOR
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# 📊 LOGS E MONITORAMENTO
LOG_LEVEL=INFO
LOG_DIR=logs

# 🔧 CONFIGURAÇÕES DE SESSÃO
SESSION_LIFETIME=86400
MAX_LOGIN_ATTEMPTS=5

# 📁 UPLOAD E ARQUIVOS
UPLOAD_FOLDER=static/uploads
MAX_CONTENT_LENGTH=16777216

# 🎯 BIOMETRIA E HARDWARE
ZK4500_HOST=localhost
ZK4500_PORT=5001
ZK4500_TIMEOUT=30

# 📧 EMAIL (OPCIONAL)
SMTP_SERVER=localhost
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=

# 🔄 CACHE E PERFORMANCE
CACHE_TYPE=simple
REDIS_URL=redis://localhost:6379/0

# 🛡️ SEGURANÇA AVANÇADA
MAX_CONTENT_LENGTH=16777216
"""
    
    # Criar arquivo .env
    env_path = 'var/www/controle-ponto/.env'
    
    try:
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(conteudo_env)
        
        print(f"✅ Arquivo .env criado com sucesso: {env_path}")
        print(f"✅ SECRET_KEY gerada: {secret_key[:32]}...")
        print("✅ CORREÇÃO URGENTE #4 APLICADA!")
        
        # Criar backup do env.example também
        print("\n📋 Atualizando env.example...")
        
        conteudo_example = """# ===============================================
# RLPONTO-WEB - Configurações de Ambiente
# ===============================================
# NUNCA commite este arquivo com valores reais!
# Copie para .env e configure valores corretos

# 🔒 SEGURANÇA CRÍTICA
SECRET_KEY=sua_chave_secreta_gerada_automaticamente_aqui
FLASK_DEBUG=False

# 🗄️ BANCO DE DADOS
DB_HOST=************
DB_USER=cavalcrod
DB_PASSWORD=sua_senha_do_banco
DB_NAME=controle_ponto

# 🌐 REDE E SERVIDOR
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# 📊 LOGS E MONITORAMENTO
LOG_LEVEL=INFO
LOG_DIR=logs

# 🔧 CONFIGURAÇÕES OPCIONAIS
SESSION_LIFETIME=86400
UPLOAD_FOLDER=static/uploads
MAX_CONTENT_LENGTH=16777216

# 🎯 BIOMETRIA E HARDWARE
ZK4500_HOST=localhost
ZK4500_PORT=5001
ZK4500_TIMEOUT=30
"""
        
        with open('var/www/controle-ponto/env.example', 'w', encoding='utf-8') as f:
            f.write(conteudo_example)
        
        print("✅ env.example atualizado")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar .env: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Criando arquivo .env seguro...")
    print("📋 CORREÇÃO URGENTE #4: Configuração de SECRET_KEY fixa")
    
    if criar_arquivo_env():
        print("\n🎉 CORREÇÃO #4 CONCLUÍDA COM SUCESSO!")
        print("✅ SECRET_KEY fixa configurada")
        print("✅ Arquivo .env de produção criado")
        print("✅ env.example atualizado")
        print("\n⚠️ IMPORTANTE: O arquivo .env contém informações sensíveis!")
        print("⚠️ Não compartilhe ou faça commit deste arquivo!")
    else:
        print("\n❌ FALHA NA CORREÇÃO #4!")
        print("Verifique as permissões de escrita no diretório") 