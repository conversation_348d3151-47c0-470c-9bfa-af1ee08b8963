#!/usr/bin/env python3
"""
Script para testar o processamento de EPIs via formulário.
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from app_funcionarios import _extrair_dados_epis
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def simular_form_data():
    """
    Simula dados de formulário com EPIs.
    """
    # Simular dados como se viessem do formulário web
    form_data = {
        # Dados básicos do funcionário
        'nome_completo': 'Teste EPI',
        'cpf': '123.456.789-00',
        'empresa_id': '1',
        
        # Dados de EPI no formato correto
        'epis[0][epi_nome]': 'Capacete de Segurança',
        'epis[0][epi_ca]': '12345',
        'epis[0][epi_data_entrega]': '2025-07-08',
        'epis[0][epi_data_validade]': '2026-07-08',
        'epis[0][epi_observacoes]': 'EPI de teste via formulário',
        
        # Segundo EPI
        'epis[1][epi_nome]': 'Luvas de Proteção',
        'epis[1][epi_ca]': '67890',
        'epis[1][epi_data_entrega]': '2025-07-08',
        'epis[1][epi_data_validade]': '2026-07-08',
        'epis[1][epi_observacoes]': 'Segundo EPI de teste',
    }
    
    return form_data

def testar_extracao_epis():
    """
    Testa a extração de dados de EPIs.
    """
    print("🔍 TESTE: Extração de dados de EPIs do formulário")
    print("=" * 60)
    
    # Simular dados do formulário
    form_data = simular_form_data()
    
    print(f"📋 Dados simulados do formulário:")
    for key, value in form_data.items():
        if 'epi' in key.lower():
            print(f"   {key}: {value}")
    
    print(f"\n🔄 Executando _extrair_dados_epis...")
    
    try:
        # Testar extração
        epis_extraidos = _extrair_dados_epis(form_data)
        
        print(f"\n✅ Extração concluída!")
        print(f"📦 EPIs extraídos: {len(epis_extraidos)}")
        
        for i, epi in enumerate(epis_extraidos):
            print(f"\n   EPI {i+1}:")
            for campo, valor in epi.items():
                print(f"      {campo}: {valor}")
        
        return epis_extraidos
        
    except Exception as e:
        print(f"❌ Erro durante extração: {e}")
        import traceback
        traceback.print_exc()
        return []

def testar_processamento_completo():
    """
    Testa o processamento completo incluindo criação de EPI.
    """
    print(f"\n🔄 TESTE: Processamento completo de EPIs")
    print("=" * 60)
    
    # Extrair EPIs
    epis = testar_extracao_epis()
    
    if not epis:
        print(f"❌ Nenhum EPI extraído - teste falhou")
        return False
    
    # Simular criação de funcionário e EPIs
    print(f"\n🦺 Simulando criação de EPIs no banco...")
    
    try:
        from utils.database import DatabaseManager
        
        # Usar funcionário existente para teste
        funcionario_id = 10  # TESTE TESTE
        
        print(f"👤 Usando funcionário ID: {funcionario_id}")
        
        # Simular função _processar_epis_funcionario
        from app_funcionarios import _processar_epis_funcionario
        
        print(f"🔄 Executando _processar_epis_funcionario...")
        _processar_epis_funcionario(funcionario_id, epis)
        
        print(f"✅ EPIs processados com sucesso!")
        
        # Verificar se foram salvos
        epis_salvos = DatabaseManager.execute_query(
            "SELECT * FROM epis WHERE funcionario_id = %s ORDER BY id DESC",
            (funcionario_id,)
        )
        
        print(f"📦 EPIs salvos no banco: {len(epis_salvos)}")
        for epi in epis_salvos:
            print(f"   - {epi['epi_nome']} (CA: {epi['epi_ca']}) - ID: {epi['id']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante processamento: {e}")
        import traceback
        traceback.print_exc()
        return False

def limpar_epis_teste():
    """
    Remove EPIs de teste.
    """
    try:
        from utils.database import DatabaseManager
        
        print(f"\n🧹 Limpando EPIs de teste...")
        DatabaseManager.execute_query(
            "DELETE FROM epis WHERE epi_observacoes LIKE '%teste%'",
            fetch_all=False
        )
        print(f"✅ EPIs de teste removidos")
        
    except Exception as e:
        print(f"❌ Erro ao limpar: {e}")

if __name__ == "__main__":
    print("🚀 Iniciando teste de formulário de EPIs...")
    
    # Testar extração
    sucesso = testar_processamento_completo()
    
    if sucesso:
        print(f"\n✅ TESTE CONCLUÍDO COM SUCESSO!")
        
        # Perguntar se deve limpar
        resposta = input("\n🧹 Deseja remover os EPIs de teste? (s/n): ")
        if resposta.lower() == 's':
            limpar_epis_teste()
    else:
        print(f"\n❌ TESTE FALHOU!")
    
    print(f"\n🏁 Teste finalizado.")
