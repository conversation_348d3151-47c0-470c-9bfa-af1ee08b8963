@echo off
:: =====================================================================
:: STATUS BRIDGE ZK4500 - RLPONTO-WEB v1.0
:: Script para verificar status do serviço bridge
:: Desenvolvido por: <PERSON>rigues - AiNexus Tecnologia
:: =====================================================================

title STATUS BRIDGE ZK4500 - RLPONTO-WEB v1.0

:: Definir variáveis
set BRIDGE_NAME=RLPonto-BridgeZK4500
set BRIDGE_DIR=C:\RLPonto-Bridge
set BRIDGE_PORT=8080
set LOGGING_ENABLED=1

:: Cores para output (se suportado)
for /f %%i in ('echo prompt $E ^| cmd') do set ESC=%%i

echo.
echo ========================================================
echo   STATUS BRIDGE ZK4500 - RLPONTO-WEB v1.0
echo   AiNexus Tecnologia - Sistema Biometrico Empresarial
echo ========================================================

:: Log do início da verificação
if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :START_SECTION "STATUS_CHECK_BRIDGE"
if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :SYSTEM_INFO

echo.
echo [STATUS GERAL DO BRIDGE]
echo ========================================================

:: 1. Verificar se o serviço Windows existe
echo [1/6] Verificando servico Windows...
sc query "%BRIDGE_NAME%" >nul 2>&1
if !errorLevel! == 0 (
    echo [OK] Servico registrado no Windows
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Servico Windows registrado: %BRIDGE_NAME%"
    
    :: Obter status detalhado do serviço
    for /f "tokens=3" %%i in ('sc query "%BRIDGE_NAME%" ^| findstr "STATE"') do set SERVICE_STATE=%%i
    
    if "!SERVICE_STATE!"=="RUNNING" (
        echo [OK] Servico esta RODANDO
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Status do servico: RUNNING"
        set SERVICE_RUNNING=1
    ) else if "!SERVICE_STATE!"=="STOPPED" (
        echo [AVISO] Servico esta PARADO
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Status do servico: STOPPED"
        set SERVICE_RUNNING=0
    ) else (
        echo [INFO] Servico em estado: !SERVICE_STATE!
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Status do servico: !SERVICE_STATE!"
        set SERVICE_RUNNING=0
    )
) else (
    echo [ERRO] Servico NAO registrado no Windows
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Servico Windows nao encontrado: %BRIDGE_NAME%"
    set SERVICE_RUNNING=0
)

:: 2. Verificar processos Python relacionados
echo.
echo [2/6] Verificando processos Python...
tasklist /FI "IMAGENAME eq python.exe" /FO CSV | findstr "python.exe" >nul 2>&1
if !errorLevel! == 0 (
    echo [OK] Processos Python detectados:
    tasklist /FI "IMAGENAME eq python.exe" /FO TABLE | findstr "python.exe"
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Processos Python em execução detectados"
    set PYTHON_RUNNING=1
) else (
    echo [AVISO] Nenhum processo Python detectado
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Nenhum processo Python encontrado"
    set PYTHON_RUNNING=0
)

:: 3. Verificar porta 8080
echo.
echo [3/6] Verificando porta %BRIDGE_PORT%...
netstat -an | findstr ":%BRIDGE_PORT%" | findstr "LISTENING" >nul 2>&1
if !errorLevel! == 0 (
    echo [OK] Porta %BRIDGE_PORT% em uso ^(LISTENING^)
    netstat -an | findstr ":%BRIDGE_PORT%"
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Porta %BRIDGE_PORT% ativa e em listening"
    set PORT_ACTIVE=1
) else (
    echo [ERRO] Porta %BRIDGE_PORT% NAO esta em uso
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Porta %BRIDGE_PORT% nao esta ativa"
    set PORT_ACTIVE=0
)

:: 4. Teste de conectividade HTTP
echo.
echo [4/6] Testando conectividade HTTP...
curl -s --connect-timeout 5 http://localhost:%BRIDGE_PORT%/api/bridge-status >nul 2>&1
if !errorLevel! == 0 (
    echo [OK] Bridge respondendo via HTTP
    echo [INFO] Resposta da API:
    curl -s http://localhost:%BRIDGE_PORT%/api/bridge-status
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Bridge respondendo corretamente via HTTP"
    set HTTP_WORKING=1
) else (
    echo [ERRO] Bridge NAO responde via HTTP
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Bridge nao responde via HTTP na porta %BRIDGE_PORT%"
    set HTTP_WORKING=0
)

:: 5. Verificar arquivos de instalação
echo.
echo [5/6] Verificando instalacao...
if exist "%BRIDGE_DIR%\biometric_bridge_service.py" (
    echo [OK] Arquivo principal encontrado
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Arquivo principal bridge encontrado"
    set FILES_OK=1
) else (
    echo [ERRO] Arquivo principal NAO encontrado
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Arquivo principal bridge nao encontrado"
    set FILES_OK=0
)

if exist "%BRIDGE_DIR%\INSTALACAO_INFO.txt" (
    echo [INFO] Informacoes da instalacao:
    type "%BRIDGE_DIR%\INSTALACAO_INFO.txt"
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Arquivo de informacoes da instalacao encontrado"
) else (
    echo [AVISO] Arquivo de informacoes da instalacao nao encontrado
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Arquivo de informacoes da instalacao ausente"
)

:: 6. Verificar logs recentes
echo.
echo [6/6] Verificando logs...
if exist "%BRIDGE_DIR%\logs\bridge_operations.log" (
    echo [OK] Sistema de logs ativo
    echo [INFO] Ultimas 5 entradas do log:
    for /f "skip=1" %%i in ('wc -l "%BRIDGE_DIR%\logs\bridge_operations.log" 2^>nul') do set LOG_LINES=%%i
    if defined LOG_LINES (
        more +%LOG_LINES% "%BRIDGE_DIR%\logs\bridge_operations.log" | head -5
    )
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Sistema de logs operacional"
) else (
    echo [AVISO] Logs nao encontrados
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Sistema de logs nao encontrado"
)

echo.
echo ========================================================
echo   RESUMO DO STATUS
echo ========================================================

:: Calcular status geral
set TOTAL_CHECKS=0
set PASSED_CHECKS=0

if defined SERVICE_RUNNING if %SERVICE_RUNNING%==1 set /a PASSED_CHECKS+=1
set /a TOTAL_CHECKS+=1

if defined PYTHON_RUNNING if %PYTHON_RUNNING%==1 set /a PASSED_CHECKS+=1
set /a TOTAL_CHECKS+=1

if defined PORT_ACTIVE if %PORT_ACTIVE%==1 set /a PASSED_CHECKS+=1
set /a TOTAL_CHECKS+=1

if defined HTTP_WORKING if %HTTP_WORKING%==1 set /a PASSED_CHECKS+=1
set /a TOTAL_CHECKS+=1

if defined FILES_OK if %FILES_OK%==1 set /a PASSED_CHECKS+=1
set /a TOTAL_CHECKS+=1

:: Mostrar resultado final
if %PASSED_CHECKS%==%TOTAL_CHECKS% (
    echo [STATUS] BRIDGE TOTALMENTE OPERACIONAL ^(%PASSED_CHECKS%/%TOTAL_CHECKS%^)
    echo [INFO] O serviço bridge esta funcionando corretamente
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "STATUS: Bridge totalmente operacional (%PASSED_CHECKS%/%TOTAL_CHECKS%)"
) else if %PASSED_CHECKS% gtr 2 (
    echo [STATUS] BRIDGE PARCIALMENTE OPERACIONAL ^(%PASSED_CHECKS%/%TOTAL_CHECKS%^)
    echo [AVISO] Alguns componentes precisam de atencao
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "STATUS: Bridge parcialmente operacional (%PASSED_CHECKS%/%TOTAL_CHECKS%)"
) else (
    echo [STATUS] BRIDGE COM PROBLEMAS CRITICOS ^(%PASSED_CHECKS%/%TOTAL_CHECKS%^)
    echo [ERRO] Multiplos componentes falharam
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "STATUS: Bridge com problemas criticos (%PASSED_CHECKS%/%TOTAL_CHECKS%)"
)

echo.
echo ========================================================
echo   ACOES DISPONIVEIS
echo ========================================================
echo.
echo [1] Iniciar servico bridge       [sc start %BRIDGE_NAME%]
echo [2] Parar servico bridge         [sc stop %BRIDGE_NAME%]
echo [3] Reiniciar servico bridge     [sc stop + sc start]
echo [4] Ver logs detalhados          [type logs\bridge_operations.log]
echo [5] Executar analisador completo [analisar.bat]
echo [6] Reinstalar bridge            [desinstalar.bat + instalador.bat]
echo.

:: Finalizar log
if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :END_SECTION "STATUS_CHECK_BRIDGE" "SUCCESS"

echo Pressione qualquer tecla para finalizar...
pause >nul 