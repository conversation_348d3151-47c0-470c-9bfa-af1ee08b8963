{% extends "base.html" %}

{% block title %}Gestão de Empresas - RLPONTO-WEB{% endblock %}

{% block extra_css %}
<style>
    .empresa-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .empresa-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .empresa-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
    }
    
    .empresa-info h3 {
        margin: 0 0 5px 0;
        color: #2c3e50;
        font-size: 1.3em;
    }
    
    .empresa-info .fantasia {
        color: #7f8c8d;
        font-style: italic;
        margin-bottom: 10px;
    }
    
    .empresa-status {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85em;
        font-weight: 500;
    }
    
    .status-ativa {
        background: #d4edda;
        color: #155724;
    }
    
    .status-inativa {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-teste {
        background: #9b59b6;
        margin-left: 5px;
        color: white;
    }
    
    .empresa-details {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
        margin-bottom: 15px;
    }
    
    .detail-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #5a6c7d;
    }
    
    .detail-item i {
        color: #3498db;
        width: 16px;
    }
    
    .empresa-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 15px;
    }
    
    .stat-item {
        background: #f8f9fa;
        padding: 10px 15px;
        border-radius: 8px;
        text-align: center;
        flex: 1;
    }
    
    .stat-number {
        font-size: 1.5em;
        font-weight: bold;
        color: #2c3e50;
        display: block;
    }
    
    .stat-label {
        font-size: 0.85em;
        color: #7f8c8d;
    }
    
    .empresa-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }
    
    .btn-action {
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 0.9em;
        transition: all 0.2s ease;
    }
    
    .btn-primary {
        background: #3498db;
        color: white;
    }
    
    .btn-primary:hover {
        background: #2980b9;
        color: white;
    }
    
    .btn-success {
        background: #27ae60;
        color: white;
    }
    
    .btn-success:hover {
        background: #229954;
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #7f8c8d;
    }
    
    .empty-state i {
        font-size: 4em;
        margin-bottom: 20px;
        color: #bdc3c7;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
    }
    
    .page-header h1 {
        margin: 0 0 10px 0;
        font-size: 2em;
    }
    
    .page-header p {
        margin: 0;
        opacity: 0.9;
    }
    
    .header-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .search-box {
        display: flex;
        gap: 10px;
        align-items: center;
    }
    
    .search-box input {
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 6px;
        width: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-building"></i> Gestão de Empresas</h1>
    <p>Gerencie empresas e suas jornadas de trabalho</p>
</div>

<div class="header-actions">
    <div class="search-box">
        <input type="text" id="searchInput" placeholder="Buscar empresa..." onkeyup="filtrarEmpresas()">
        <i class="fas fa-search"></i>
    </div>
    
    <a href="{{ url_for('empresas.cadastrar') }}" class="btn btn-success">
        <i class="fas fa-plus"></i> Nova Empresa
    </a>
</div>

<div id="empresasContainer">
    {% if empresas %}
        {% for empresa in empresas %}
        <div class="empresa-card" data-search="{{ empresa.razao_social|lower }} {{ empresa.nome_fantasia|lower }} {{ empresa.cnpj }}">
            <div class="empresa-header">
                <div class="empresa-info">
                    <h3>{{ empresa.razao_social }}</h3>
                    {% if empresa.nome_fantasia %}
                    <div class="fantasia">{{ empresa.nome_fantasia }}</div>
                    {% endif %}
                </div>
                
                <div class="empresa-status">
                    <span class="status-badge {{ 'status-ativa' if empresa.ativa else 'status-inativa' }}">
                        {{ 'Ativa' if empresa.ativa else 'Inativa' }}
                    </span>
                    {% if empresa.empresa_teste %}
                    <span class="status-badge status-teste" title="Esta é uma empresa de teste">
                        <i class="fas fa-flask"></i> Teste
                    </span>
                    {% endif %}
                </div>
            </div>
            
            <div class="empresa-details">
                <div class="detail-item">
                    <i class="fas fa-id-card"></i>
                    <span>{{ empresa.cnpj }}</span>
                </div>
                
                {% if empresa.telefone %}
                <div class="detail-item">
                    <i class="fas fa-phone"></i>
                    <span>{{ empresa.telefone }}</span>
                </div>
                {% endif %}
                
                {% if empresa.email %}
                <div class="detail-item">
                    <i class="fas fa-envelope"></i>
                    <span>{{ empresa.email }}</span>
                </div>
                {% endif %}
            </div>
            
            <div class="empresa-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ empresa.total_jornadas or 0 }}</span>
                    <span class="stat-label">Jornadas</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-number">{{ empresa.total_funcionarios or 0 }}</span>
                    <span class="stat-label">Funcionários</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-number">{{ empresa.data_cadastro.strftime('%d/%m/%Y') }}</span>
                    <span class="stat-label">Cadastro</span>
                </div>
            </div>
            
            <div class="empresa-actions">
                <a href="{{ url_for('empresas.detalhes', id=empresa.id) }}" class="btn-action btn-primary">
                    <i class="fas fa-eye"></i> Detalhes
                </a>
                
                <a href="{{ url_for('empresas.cadastrar_jornada', empresa_id=empresa.id) }}" class="btn-action btn-success">
                    <i class="fas fa-clock"></i> Nova Jornada
                </a>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="empty-state">
            <i class="fas fa-building"></i>
            <h3>Nenhuma empresa cadastrada</h3>
            <p>Comece cadastrando sua primeira empresa</p>
            <a href="{{ url_for('empresas.cadastrar') }}" class="btn btn-success" style="margin-top: 20px;">
                <i class="fas fa-plus"></i> Cadastrar Primeira Empresa
            </a>
        </div>
    {% endif %}
</div>

<script>
function filtrarEmpresas() {
    const searchInput = document.getElementById('searchInput');
    const filter = searchInput.value.toLowerCase();
    const empresaCards = document.querySelectorAll('.empresa-card');
    
    empresaCards.forEach(card => {
        const searchData = card.getAttribute('data-search');
        if (searchData.includes(filter)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}
</script>
{% endblock %}
