#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar a funcionalidade de jornada automática por empresa
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def testar_jornada_empresa():
    """Testar a funcionalidade de jornada automática por empresa"""
    try:
        print("=" * 80)
        print("🕐 TESTANDO JORNADA AUTOMÁTICA POR EMPRESA")
        print("=" * 80)
        
        # Importar dependências
        from utils.database import DatabaseManager
        
        # Conectar ao banco
        db = DatabaseManager()
        print("✅ Conexão com banco de dados estabelecida")
        
        # 1. Listar todas as empresas e suas jornadas
        print("\n🏢 EMPRESAS E SUAS JORNADAS:")
        print("-" * 60)
        
        empresas_jornadas = db.execute_query("""
            SELECT
                e.id as empresa_id,
                e.nome_fantasia,
                e.razao_social,
                e.empresa_principal,
                COUNT(ht.id) as total_jornadas,
                GROUP_CONCAT(
                    CONCAT(ht.nome_horario, ' (ID:', ht.id, ')')
                    SEPARATOR ', '
                ) as jornadas
            FROM empresas e
            LEFT JOIN horarios_trabalho ht ON e.id = ht.empresa_id AND ht.ativo = 1
            WHERE e.ativa = TRUE
            GROUP BY e.id, e.nome_fantasia, e.razao_social, e.empresa_principal
            ORDER BY e.empresa_principal DESC, e.nome_fantasia
        """)
        
        for emp in empresas_jornadas:
            status = "⭐ PRINCIPAL" if emp['empresa_principal'] else "  TERCEIRIZADA"
            print(f"\n{status} {emp['nome_fantasia']}")
            print(f"   ID: {emp['empresa_id']} | Jornadas: {emp['total_jornadas']}")
            if emp['jornadas']:
                print(f"   Horários: {emp['jornadas']}")
            else:
                print(f"   ❌ Nenhuma jornada cadastrada!")
        
        # 2. Testar função de busca de jornada
        print(f"\n🔍 TESTANDO BUSCA DE JORNADA POR EMPRESA:")
        print("-" * 60)
        
        # Importar função de teste
        sys.path.append('/var/www/controle-ponto')
        from app_funcionarios import get_jornada_padrao_empresa
        
        for emp in empresas_jornadas:
            empresa_id = emp['empresa_id']
            print(f"\n🏢 Testando empresa: {emp['nome_fantasia']} (ID: {empresa_id})")
            
            jornada = get_jornada_padrao_empresa(empresa_id)
            if jornada:
                print(f"   ✅ Jornada encontrada: {jornada['nome_horario']} (ID: {jornada['id']})")
                print(f"   ⏰ Horário: {jornada['entrada_manha']} às {jornada['saida']}")
                if 'carga_horaria' in jornada:
                    print(f"   📊 Carga horária: {jornada['carga_horaria']}h")
            else:
                print(f"   ❌ Nenhuma jornada encontrada")
        
        # 3. Verificar funcionários e suas jornadas atuais
        print(f"\n👥 FUNCIONÁRIOS E SUAS JORNADAS ATUAIS:")
        print("-" * 60)
        
        funcionarios_jornadas = db.execute_query("""
            SELECT
                f.id,
                f.nome_completo,
                f.empresa_id,
                e.nome_fantasia as empresa_nome,
                f.horario_trabalho_id,
                ht.nome_horario,
                ht.entrada_manha,
                ht.saida
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
            ORDER BY f.nome_completo
        """)
        
        for func in funcionarios_jornadas:
            print(f"\n👤 {func['nome_completo']} (ID: {func['id']})")
            print(f"   🏢 Empresa: {func['empresa_nome']} (ID: {func['empresa_id']})")
            if func['nome_horario']:
                print(f"   ⏰ Jornada: {func['nome_horario']} (ID: {func['horario_trabalho_id']})")
                print(f"   📅 Horário: {func['entrada_manha']} às {func['saida']}")
            else:
                print(f"   ❌ Sem jornada definida!")
        
        # 4. Verificar problemas de configuração
        print(f"\n⚠️ PROBLEMAS IDENTIFICADOS:")
        print("-" * 60)
        
        problemas = []
        
        # Empresas sem jornada
        empresas_sem_jornada = [e for e in empresas_jornadas if e['total_jornadas'] == 0]
        if empresas_sem_jornada:
            problemas.append(f"❌ {len(empresas_sem_jornada)} empresas sem jornada cadastrada:")
            for emp in empresas_sem_jornada:
                problemas.append(f"   - {emp['nome_fantasia']} (ID: {emp['empresa_id']})")
        
        # Funcionários sem jornada
        funcionarios_sem_jornada = [f for f in funcionarios_jornadas if not f['nome_horario']]
        if funcionarios_sem_jornada:
            problemas.append(f"❌ {len(funcionarios_sem_jornada)} funcionários sem jornada:")
            for func in funcionarios_sem_jornada:
                problemas.append(f"   - {func['nome_completo']} (Empresa: {func['empresa_nome']})")
        
        if problemas:
            for problema in problemas:
                print(problema)
        else:
            print("✅ Nenhum problema crítico identificado!")
        
        # 5. Recomendações
        print(f"\n💡 RECOMENDAÇÕES:")
        print("-" * 60)
        
        if empresas_sem_jornada:
            print("1. 📋 Cadastrar jornadas para as empresas sem horário definido")
            print("2. ⭐ Marcar uma jornada como 'padrão' para cada empresa")
        
        if funcionarios_sem_jornada:
            print("3. 👥 Atualizar funcionários sem jornada para usar a jornada da empresa")
        
        print("4. 🔄 Testar cadastro de novo funcionário para verificar aplicação automática")
        print("5. ✅ Verificar se a jornada é herdada corretamente na alocação")
        
        print("\n" + "=" * 80)
        print("✅ TESTE CONCLUÍDO!")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def criar_jornada_exemplo():
    """Criar jornada de exemplo para empresas que não têm"""
    try:
        print("\n🔧 CRIANDO JORNADAS DE EXEMPLO...")
        
        from utils.database import DatabaseManager
        db = DatabaseManager()
        
        # Buscar empresas sem jornada
        empresas_sem_jornada = db.execute_query("""
            SELECT e.id, e.nome_fantasia
            FROM empresas e
            LEFT JOIN horarios_trabalho ht ON e.id = ht.empresa_id AND ht.ativo = 1
            WHERE e.ativa = TRUE AND ht.id IS NULL
        """)
        
        if not empresas_sem_jornada:
            print("✅ Todas as empresas já têm jornadas cadastradas!")
            return True
        
        print(f"📋 Criando jornadas para {len(empresas_sem_jornada)} empresas...")
        
        for empresa in empresas_sem_jornada:
            # Criar jornada padrão 8h (08:00-12:00 / 13:00-17:00)
            sql = """
            INSERT INTO horarios_trabalho (
                nome_horario, entrada_manha, saida_almoco, entrada_tarde, saida,
                tolerancia_minutos, ativo, empresa_id
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            nome_horario = f"Padrão {empresa['nome_fantasia']}"
            params = (
                nome_horario, '08:00:00', '12:00:00', '13:00:00', '17:00:00',
                10, 1, empresa['id']
            )
            
            resultado = db.execute_query(sql, params, fetch_all=False)
            if resultado:
                print(f"   ✅ Jornada criada para {empresa['nome_fantasia']}")
            else:
                print(f"   ❌ Erro ao criar jornada para {empresa['nome_fantasia']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar jornadas: {e}")
        return False

if __name__ == '__main__':
    print("Iniciando teste de jornada por empresa...")
    testar_jornada_empresa()
    
    # Perguntar se deve criar jornadas de exemplo
    print("\n" + "="*50)
    print("💡 Deseja criar jornadas de exemplo para empresas sem horário?")
    print("   Execute novamente com parâmetro 'criar' para criar jornadas automáticas")
    
    if len(sys.argv) > 1 and sys.argv[1] == 'criar':
        criar_jornada_exemplo()
