from flask import Blueprint, jsonify, request
from datetime import datetime
import json

# Blueprint público para conexão do agente
agent_api_bp = Blueprint('agent_api', __name__)

@agent_api_bp.route('/ping', methods=['GET'])
def agent_ping():
    """Endpoint de teste de conectividade"""
    return jsonify({
        'status': 'success',
        'message': 'Servidor conectado - APIs funcionando',
        'server': 'RLPONTO-WEB v1.0',
        'timestamp': str(datetime.now()),
        'available_endpoints': ['/ping', '/models', '/chat', '/process']
    })

@agent_api_bp.route('/models', methods=['GET'])
def get_available_models():
    """Lista modelos de IA disponíveis no servidor"""
    return jsonify({
        'status': 'success',
        'models': [
            {
                'id': 'gpt-4',
                'name': 'GPT-4',
                'provider': 'openai',
                'description': 'Modelo avançado para tarefas complexas'
            },
            {
                'id': 'claude-3',
                'name': 'Claude 3 Sonnet',
                'provider': 'anthropic', 
                'description': 'Modelo para análise e código'
            },
            {
                'id': 'local-llm',
                'name': 'Modelo Local',
                'provider': 'local',
                'description': 'Modelo hospedado no servidor'
            }
        ]
    })

@agent_api_bp.route('/chat', methods=['POST'])
def chat_with_model():
    """Endpoint para conversa com modelos de IA"""
    try:
        data = request.get_json()
        message = data.get('message', '')
        model = data.get('model', 'gpt-4')
        
        if not message:
            return jsonify({
                'status': 'error',
                'message': 'Mensagem é obrigatória'
            }), 400
        
        # Simular resposta de IA (substitua pela integração real com seus modelos)
        response = f'[{model}] Processando: "{message}"\n\nResposta simulada do servidor. Integre aqui seus modelos reais de IA.'
        
        return jsonify({
            'status': 'success',
            'response': response,
            'model_used': model,
            'timestamp': str(datetime.now())
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Erro no processamento: {str(e)}'
        }), 500

@agent_api_bp.route('/process', methods=['POST'])
def process_data():
    """Endpoint genérico para processamento de dados"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'status': 'error',
                'message': 'Dados são obrigatórios'
            }), 400
        
        # Processar dados (substitua pela lógica real)
        processed_result = {
            'input_data': data,
            'processed_at': str(datetime.now()),
            'result': 'Dados processados com sucesso pelo servidor'
        }
        
        return jsonify({
            'status': 'success',
            'processed_data': processed_result,
            'timestamp': str(datetime.now())
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Erro no processamento: {str(e)}'
        }), 500

@agent_api_bp.route('/status', methods=['GET'])
def server_status():
    """Status do servidor"""
    return jsonify({
        'status': 'success',
        'server_info': {
            'name': 'RLPONTO-WEB API Server',
            'version': '1.0',
            'apis_active': True,
            'timestamp': str(datetime.now()),
            'endpoints_count': 5
        }
    }) 