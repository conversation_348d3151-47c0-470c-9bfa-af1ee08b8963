-- ========================================
-- CRIAR FUNCIONÁRIO DE TESTE - SIMULAÇÕES
-- Data: 11/07/2025
-- ========================================

USE controle_ponto;

-- Inserir funcionário de teste
INSERT INTO funcionarios (
    empresa_id, nome_completo, cpf, rg, data_nascimento, sexo, estado_civil,
    ctps_numero, pis_pasep, endereco_cep, endereco_estado, telefone1,
    cargo, setor, setor_obra, matricula_empresa, data_admissao, tipo_contrato,
    jornada_trabalho_id, status_cadastro
) VALUES (
    1, '<PERSON>', '123.456.789-00', '12.345.678-9', '1990-05-15', 'M', '<PERSON><PERSON><PERSON>',
    '12345/001', '12345678901', '12345-678', 'SP', '(11) 99999-9999',
    'Desenvolvedor', 'TI', 'Desenvolvimento', 'DEV001', '2025-01-01', 'CLT',
    1, 'Ativo'
);

-- Verificar se foi criado
SELECT id, nome_completo, cargo, matricula_empresa, jornada_trabalho_id 
FROM funcionarios 
WHERE cpf = '123.456.789-00';

SELECT 'Funcionário de teste criado com sucesso!' as status;
