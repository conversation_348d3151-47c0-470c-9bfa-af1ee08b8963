from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from datetime import datetime, date, time, timedelta
from utils.auth import require_login
from utils.database import get_db_connection
from utils.helpers import mascarar_cpf, validar_cpf_formato, calcular_horas_trabalhadas
from pymysql.cursors import DictCursor
import logging
import ipaddress
import ntplib
import time as systime

# Este é um backup do arquivo app_registro_ponto.py antes da correção do campo setor
# Data de backup: 16/06/2025
# Correção: Troca do campo 'setor' para usar prioritariamente 'setor_obra' na página de registro manual
# Linhas afetadas: ~836-841

# Trecho original que foi modificado:
"""
CASE 
    WHEN f.setor IS NULL OR f.setor = '' THEN 'Não informado'
    ELSE f.setor
END as setor,
"""

# Trecho após correção:
"""
CASE 
    WHEN f.setor_obra IS NULL OR f.setor_obra = '' THEN 
        CASE 
            WHEN f.setor IS NULL OR f.setor = '' THEN 'Não informado'
            ELSE f.setor
        END
    ELSE f.setor_obra
END as setor,
""" 