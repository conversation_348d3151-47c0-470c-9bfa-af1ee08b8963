# ========================================
# BLUEPRINT RELATÓRIOS DE PONTO - RLPONTO-WEB
# Data: 08/01/2025
# Descrição: Sistema completo de relatórios e análises
# ========================================

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, make_response, current_app, json, Response, stream_with_context
import csv
import io
import base64
from datetime import datetime, timedelta, date
from utils.auth import require_login, require_admin
from utils.database import get_db_connection
from pymysql.cursors import DictCursor
from utils.helpers import (
    mascarar_dados_relatorio, RegistroPontoValidator, 
    gerar_dados_grafico_pontos, calcular_horas_trabalhadas,
    formatar_tipo_registro_descricao, obter_ip_usuario
)
import logging
import traceback
from decimal import Decimal

# Configurar logger específico para debug
debug_logger = logging.getLogger('relatorios_debug')
debug_logger.setLevel(logging.DEBUG)
handler = logging.StreamHandler()
formatter = logging.Formatter('[%(asctime)s] [%(name)s] [%(levelname)s] %(message)s')
handler.setFormatter(formatter)
debug_logger.addHandler(handler)

logger = logging.getLogger(__name__)

def converter_datetime_para_json(obj):
    """
    Converte objetos datetime para string em formato ISO.
    Baseado na documentação oficial do Flask para serialização JSON.
    """
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    if hasattr(obj, 'isoformat'):
        return obj.isoformat()
    return obj

def processar_registro_para_json(registro):
    """
    Processa um registro de ponto para ser seguro para serialização JSON.
    Aplica conversão de datetime e mascaramento de dados.
    """
    debug_logger.debug(f"🔄 PROCESSANDO REGISTRO: tipo={type(registro)}")
    
    if not isinstance(registro, dict):
        debug_logger.warning(f"⚠️ Registro não é dict: {type(registro)}")
        return registro
    
    # Criar cópia para evitar modificar o original
    reg_processado = {}
    
    for chave, valor in registro.items():
        debug_logger.debug(f"  📝 Processando campo {chave}: {type(valor)}")
        
        # Converter datetime para string
        if isinstance(valor, (datetime, date)):
            reg_processado[chave] = valor.isoformat()
            debug_logger.debug(f"  ✅ Convertido datetime: {chave}")
        elif isinstance(valor, Decimal):
            reg_processado[chave] = float(valor)
            debug_logger.debug(f"  ✅ Convertido Decimal: {chave}")
        elif hasattr(valor, 'isoformat'):
            reg_processado[chave] = valor.isoformat()
            debug_logger.debug(f"  ✅ Convertido com isoformat: {chave}")
        else:
            reg_processado[chave] = valor
    
    # Adicionar campos de descrição se não existirem
    if 'tipo_registro' in reg_processado and 'tipo_descricao' not in reg_processado:
        descricoes_tipo = {
            'entrada_manha': 'Entrada Manhã',
            'saida_almoco': 'Saída Almoço',
            'entrada_tarde': 'Entrada Tarde',
            'saida': 'Saída'
        }
        reg_processado['tipo_descricao'] = descricoes_tipo.get(
            reg_processado['tipo_registro'], 
            reg_processado['tipo_registro'].replace('_', ' ').title()
        )
        debug_logger.debug(f"  ✅ Adicionado tipo_descricao: {reg_processado['tipo_descricao']}")
    
    if 'metodo_registro' in reg_processado and 'metodo_descricao' not in reg_processado:
        descricoes_metodo = {
            'biometrico': 'Biométrico',
            'manual': 'Manual'
        }
        reg_processado['metodo_descricao'] = descricoes_metodo.get(
            reg_processado['metodo_registro'],
            reg_processado['metodo_registro'].title()
        )
        debug_logger.debug(f"  ✅ Adicionado metodo_descricao: {reg_processado['metodo_descricao']}")
    
    debug_logger.debug(f"✅ REGISTRO PROCESSADO COM SUCESSO")
    return reg_processado

# Blueprint para relatórios
relatorios_bp = Blueprint('relatorios', __name__, url_prefix='/relatorios')

# ========================================
# ROTAS PRINCIPAIS
# ========================================

@relatorios_bp.route('/pontos', methods=['GET', 'POST'])
@require_login
def pagina_relatorio_pontos():
    """
    Página principal de relatórios de ponto.
    Exibe formulário de filtros e tabela de resultados.
    """
    try:
        debug_logger.info("📄 CARREGANDO PÁGINA DE RELATÓRIOS")
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        
        # Obter lista de funcionários para filtro
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        cursor.execute("""
            SELECT id, nome_completo, setor, cargo 
            FROM funcionarios 
            WHERE ativo = 1 
            ORDER BY nome_completo
        """)
        
        funcionarios_raw = cursor.fetchall()
        
        # Processar dados se for POST (formulário enviado)
        registros = []
        estatisticas = {'presentes': 0, 'atrasados': 0, 'ausentes': 0}
        
        if request.method == 'POST':
            debug_logger.info("📬 PROCESSANDO FORMULÁRIO POST")
            
            try:
                # Obter filtros do formulário
                funcionario_id = request.form.get('funcionario_id', '')
                setor = request.form.get('setor', '')
                data_inicio = request.form.get('data_inicio', date.today().strftime('%Y-%m-%d'))
                data_fim = request.form.get('data_fim', date.today().strftime('%Y-%m-%d'))
                tipo_registro = request.form.get('tipo_registro', '')
                
                debug_logger.info(f"📝 Filtros: funcionario_id={funcionario_id}, setor={setor}, data_inicio={data_inicio}, data_fim={data_fim}, tipo_registro={tipo_registro}")
                
                # Construir query dinâmica
                where_clauses = ["f.ativo = 1"]
                params = []
                
                if funcionario_id:
                    where_clauses.append("rp.funcionario_id = %s")
                    params.append(funcionario_id)
                    
                if setor:
                    where_clauses.append("f.setor = %s")
                    params.append(setor)
                    
                if data_inicio:
                    where_clauses.append("DATE(rp.data_hora) >= %s")
                    params.append(data_inicio)
                    
                if data_fim:
                    where_clauses.append("DATE(rp.data_hora) <= %s")
                    params.append(data_fim)
                    
                if tipo_registro:
                    where_clauses.append("rp.tipo_registro = %s")
                    params.append(tipo_registro)
                
                where_sql = " AND ".join(where_clauses)
                
                # Query principal para registros
                query_registros = f"""
                    SELECT 
                        rp.id,
                        rp.funcionario_id,
                        f.nome_completo as nome_funcionario,
                        f.setor as setor_funcionario,
                        rp.data_hora,
                        rp.tipo_registro,
                        rp.metodo_registro,
                        rp.observacoes,
                        f.foto_url,
                        DATE(rp.data_hora) as data_formatada,
                        TIME(rp.data_hora) as horario_formatado,
                        CASE 
                            WHEN rp.tipo_registro = 'entrada' THEN 'Entrada'
                            WHEN rp.tipo_registro = 'entrada_manha' THEN 'Entrada Manhã'
                            WHEN rp.tipo_registro = 'saida' THEN 'Saída'
                            WHEN rp.tipo_registro = 'entrada_almoco' THEN 'Entrada Almoço'
                            WHEN rp.tipo_registro = 'saida_almoco' THEN 'Saída Almoço'
                            WHEN rp.tipo_registro = 'entrada_tarde' THEN 'Entrada Tarde'
                            ELSE rp.tipo_registro
                        END as tipo_registro_formatado,
                        CASE 
                            WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atrasado'
                            WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atrasado'
                            ELSE 'Pontual'
                        END as status_pontualidade
                    FROM registros_ponto rp
                    INNER JOIN funcionarios f ON rp.funcionario_id = f.id
                    WHERE {where_sql}
                    ORDER BY rp.data_hora DESC
                    LIMIT 500
                """
                
                debug_logger.info(f"🔍 Executando query: {query_registros}")
                debug_logger.info(f"🔍 Parâmetros: {params}")
                
                cursor.execute(query_registros, params)
                registros_raw = cursor.fetchall()
                
                debug_logger.info(f"✅ Query executada com sucesso. {len(registros_raw)} registros encontrados")
                
                # Processar registros para o template
                for i, reg in enumerate(registros_raw):
                    try:
                        debug_logger.debug(f"📝 Processando registro {i+1}/{len(registros_raw)}")
                        
                        # Processar foto_url (não precisa de base64, é uma URL)
                        foto_funcionario = reg['foto_url'] if reg['foto_url'] else None
                        
                        registro = {
                            'nome_funcionario': reg['nome_funcionario'] or 'Nome não informado',
                            'setor_funcionario': reg['setor_funcionario'] or 'Não informado',
                            'data_formatada': reg['data_formatada'].strftime('%d/%m/%Y') if reg['data_formatada'] else '',
                            'horario_formatado': str(reg['horario_formatado']) if reg['horario_formatado'] else '',
                            'tipo_registro': reg['tipo_registro'] or '',
                            'tipo_registro_formatado': reg['tipo_registro_formatado'] or reg['tipo_registro'] or '',
                            'status_pontualidade': reg['status_pontualidade'] or 'Não calculado',
                            'observacoes': reg['observacoes'] or '',
                            'foto_funcionario': foto_funcionario
                        }
                        registros.append(registro)
                        
                        debug_logger.debug(f"✅ Registro {i+1} processado com sucesso")
                        
                    except Exception as reg_error:
                        debug_logger.error(f"❌ Erro ao processar registro {i+1}: {str(reg_error)}")
                        # Continuar com os outros registros mesmo se um falhar
                        continue
                
                # Calcular estatísticas básicas
                try:
                    estatisticas = {
                        'presentes': len([r for r in registros if r.get('tipo_registro') == 'entrada']),
                        'atrasados': len([r for r in registros if r.get('status_pontualidade') == 'Atrasado']),
                        'ausentes': 0    # Implementar lógica de ausência se necessário
                    }
                    debug_logger.info(f"📊 Estatísticas calculadas: {estatisticas}")
                except Exception as stats_error:
                    debug_logger.error(f"❌ Erro ao calcular estatísticas: {str(stats_error)}")
                    estatisticas = {'presentes': 0, 'atrasados': 0, 'ausentes': 0}
                
                debug_logger.info(f"✅ {len(registros)} registros processados com sucesso")
                
            except Exception as post_error:
                debug_logger.error(f"❌ ERRO no processamento POST: {str(post_error)}")
                debug_logger.error(f"❌ TRACEBACK POST: {traceback.format_exc()}")
                # Manter listas vazias para não quebrar a página
                registros = []
                estatisticas = {'presentes': 0, 'atrasados': 0, 'ausentes': 0}
                # Adicionar mensagem de erro para o usuário
                debug_logger.error("❌ Erro processado, continuando com listas vazias")
        
        conn.close()
        
        debug_logger.info(f"✅ {len(funcionarios_raw)} funcionários carregados")
        
        # Preparar contexto da página
        context = {
            'titulo': 'Relatórios de Ponto',
            'funcionarios': [
                {
                    'id': f['id'],
                    'nome_completo': f['nome_completo'],
                    'setor': f['setor'] or 'Não informado',
                    'cargo': f['cargo'] or 'Não informado'
                }
                for f in funcionarios_raw
            ],
            'registros': registros,
            'estatisticas': estatisticas,
            'data_atual': date.today().strftime('%Y-%m-%d'),
            'nivel_acesso': nivel_acesso
        }
        
        debug_logger.info("✅ PÁGINA CARREGADA COM SUCESSO")
        return render_template('relatorios/pontos.html', **context)
        
    except Exception as e:
        debug_logger.error(f"❌ ERRO AO CARREGAR PÁGINA: {str(e)}")
        debug_logger.error(f"❌ TRACEBACK: {traceback.format_exc()}")
        logger.error(f"Erro ao carregar página de relatórios: {str(e)}")
        return redirect(url_for('main.index'))

@relatorios_bp.route('/estatisticas')
@require_login
def pagina_estatisticas():
    """
    Página de estatísticas e dashboards de ponto.
    Exibe gráficos e métricas resumidas.
    """
    try:
        # Obter estatísticas dos últimos 30 dias
        data_inicio = (date.today() - timedelta(days=30)).strftime('%Y-%m-%d')
        data_fim = date.today().strftime('%Y-%m-%d')
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Estatísticas gerais
        cursor.execute("""
            SELECT 
                COUNT(*) as total_registros,
                COUNT(DISTINCT funcionario_id) as funcionarios_ativos,
                SUM(CASE WHEN metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
                SUM(CASE WHEN metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais
            FROM registros_ponto rp
            INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            WHERE DATE(rp.data_hora) BETWEEN %s AND %s
            AND f.ativo = 1
        """, (data_inicio, data_fim))
        
        stats_gerais = cursor.fetchone()
        
        # Dados para gráficos (últimos 7 dias)
        cursor.execute("""
            SELECT * FROM vw_estatisticas_pontos 
            WHERE data_registro BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND CURDATE()
            ORDER BY data_registro DESC
        """)
        
        dados_graficos = cursor.fetchall()
        conn.close()
        
        # Processar dados para gráficos
        labels_dias = []
        dados_registros_diarios = []
        dados_pontualidade = []
        
        for row in dados_graficos:
            labels_dias.append(row[0].strftime('%d/%m'))
            dados_registros_diarios.append(row[1])  # total_registros
            dados_pontualidade.append(row[8])  # atrasos
        
        # Reverter para ordem cronológica
        labels_dias.reverse()
        dados_registros_diarios.reverse()
        dados_pontualidade.reverse()
        
        context = {
            'titulo': 'Estatísticas de Ponto',
            'stats': {
                'total_registros': stats_gerais["total_registros"] or 0,
                'funcionarios_ativos': stats_gerais["funcionarios_ativos"] or 0,
                'registros_biometricos': stats_gerais["registros_biometricos"] or 0,
                'registros_manuais': stats_gerais["registros_manuais"] or 0,
                'percentual_biometrico': round((stats_gerais["registros_biometricos"] or 0) / max(stats_gerais["total_registros"] or 1, 1) * 100, 1)
            },
            'graficos': {
                'labels_dias': labels_dias,
                'dados_registros_diarios': dados_registros_diarios,
                'dados_pontualidade': dados_pontualidade
            },
            'periodo': f"{data_inicio} a {data_fim}"
        }
        
        return render_template('relatorios/estatisticas.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar estatísticas: {str(e)}")
        return redirect(url_for('main.index'))

@relatorios_bp.route('/debug')
@require_login
def debug_relatorios():
    """Página de debug para testar a API de relatórios."""
    debug_logger.info("📄 CARREGANDO PÁGINA DE DEBUG")
    return render_template('debug_relatorios.html')

@relatorios_bp.route('/pontos_csv')
@require_login
def pontos_csv():
    """
    Exporta registros de ponto em formato CSV usando streaming.
    Baseado na documentação oficial Flask para grandes datasets.
    Implementa stream_with_context para manter request context ativo.
    """
    try:
        # Obter filtros da query string
        funcionario_id = request.args.get('funcionario_id', '')
        setor = request.args.get('setor', '')
        data_inicio = request.args.get('data_inicio', date.today().strftime('%Y-%m-%d'))
        data_fim = request.args.get('data_fim', date.today().strftime('%Y-%m-%d'))
        tipo_registro = request.args.get('tipo_registro', '')
        
        debug_logger.info(f"📥 Exportação CSV solicitada - Filtros: funcionario_id={funcionario_id}, setor={setor}, data_inicio={data_inicio}, data_fim={data_fim}, tipo_registro={tipo_registro}")
        
        # Função geradora com context management
        def generate_csv():
            debug_logger.info("🚀 Iniciando geração CSV")
            
            try:
                # Cabeçalho CSV com BOM para UTF-8
                debug_logger.info("📝 Enviando cabeçalho CSV")
                yield '\ufeff'  # BOM para UTF-8
                yield 'ID;Nome Completo;Setor;Data;Hora;Tipo Registro;Método;Observações\n'
                
                # Conectar ao banco dentro do gerador
                debug_logger.info("🔌 Conectando ao banco de dados")
                conn = get_db_connection()
                cursor = conn.cursor(DictCursor)
                
                # Construir query dinâmica
                where_clauses = ["f.ativo = 1"]
                params = []
                
                if funcionario_id:
                    where_clauses.append("rp.funcionario_id = %s")
                    params.append(funcionario_id)
                    debug_logger.debug(f"  ✅ Filtro funcionario_id: {funcionario_id}")
                    
                if setor:
                    where_clauses.append("f.setor = %s")
                    params.append(setor)
                    debug_logger.debug(f"  ✅ Filtro setor: {setor}")
                    
                if data_inicio:
                    where_clauses.append("DATE(rp.data_hora) >= %s")
                    params.append(data_inicio)
                    debug_logger.debug(f"  ✅ Filtro data_inicio: {data_inicio}")
                    
                if data_fim:
                    where_clauses.append("DATE(rp.data_hora) <= %s")
                    params.append(data_fim)
                    debug_logger.debug(f"  ✅ Filtro data_fim: {data_fim}")
                    
                if tipo_registro:
                    where_clauses.append("rp.tipo_registro = %s")
                    params.append(tipo_registro)
                    debug_logger.debug(f"  ✅ Filtro tipo_registro: {tipo_registro}")
                
                where_sql = " AND ".join(where_clauses)
                
                query = f"""
                    SELECT 
                        rp.id,
                        f.nome_completo,
                        f.setor,
                        rp.data_hora,
                        rp.tipo_registro,
                        rp.metodo_registro,
                        rp.observacoes
                    FROM registros_ponto rp
                    INNER JOIN funcionarios f ON rp.funcionario_id = f.id
                    WHERE {where_sql}
                    ORDER BY rp.data_hora DESC
                    LIMIT 10000
                """
                
                debug_logger.info(f"🔍 Query CSV: {query}")
                debug_logger.info(f"🔍 Parâmetros CSV: {params}")
                
                # Executar query
                cursor.execute(query, params)
                debug_logger.info("✅ Query executada com sucesso")
                
                # Processar registros linha por linha
                row_count = 0
                batch_size = 100
                
                while True:
                    # Buscar lote de registros
                    registros_lote = cursor.fetchmany(batch_size)
                    
                    if not registros_lote:
                        debug_logger.info(f"🏁 Fim dos registros. Total processado: {row_count}")
                        break
                    
                    debug_logger.debug(f"📦 Processando lote de {len(registros_lote)} registros")
                    
                    # Processar cada registro do lote
                    for reg in registros_lote:
                        try:
                            # Processar dados para CSV
                            id_reg = str(reg['id']) if reg['id'] else ''
                            nome = str(reg['nome_completo']) if reg['nome_completo'] else 'Nome não informado'
                            setor_reg = str(reg['setor']) if reg['setor'] else 'Não informado'
                            
                            # Formatação de data e hora
                            if reg['data_hora']:
                                data_formatada = reg['data_hora'].strftime('%d/%m/%Y')
                                hora_formatada = reg['data_hora'].strftime('%H:%M:%S')
                            else:
                                data_formatada = ''
                                hora_formatada = ''
                            
                            # Formatação de tipos
                            tipo_formatado = {
                                'entrada': 'Entrada',
                                'entrada_manha': 'Entrada Manhã',
                                'entrada_tarde': 'Entrada Tarde',
                                'saida': 'Saída',
                                'entrada_almoco': 'Entrada Almoço',
                                'saida_almoco': 'Saída Almoço'
                            }.get(reg['tipo_registro'], reg['tipo_registro'])
                            
                            metodo_reg = str(reg['metodo_registro']) if reg['metodo_registro'] else 'Não informado'
                            observacoes_reg = str(reg['observacoes']) if reg['observacoes'] else ''
                            
                            # Escapar caracteres especiais para CSV
                            nome = nome.replace('"', '""').replace(';', ',')
                            setor_reg = setor_reg.replace('"', '""').replace(';', ',')
                            observacoes_reg = observacoes_reg.replace('"', '""').replace(';', ',')
                            
                            # Linha CSV
                            linha = f'{id_reg};{nome};{setor_reg};{data_formatada};{hora_formatada};{tipo_formatado};{metodo_reg};{observacoes_reg}\n'
                            yield linha
                            
                            row_count += 1
                            
                        except Exception as reg_error:
                            debug_logger.error(f"❌ Erro ao processar registro: {str(reg_error)}")
                            continue
                    
                    # Log de progresso a cada lote
                    debug_logger.info(f"📊 Processados {row_count} registros até agora")
                
                # Fechar conexão
                conn.close()
                debug_logger.info(f"✅ Exportação CSV concluída - {row_count} registros gerados")
                
            except Exception as gen_error:
                debug_logger.error(f"💥 Erro crítico no gerador CSV: {str(gen_error)}")
                debug_logger.error(f"💥 Traceback: {traceback.format_exc()}")
                
                # Yield de erro para evitar CSV vazio
                yield f"# Erro na exportação: {str(gen_error)}\n"
                
                if 'conn' in locals():
                    conn.close()
                    debug_logger.info("🔌 Conexão fechada após erro")
        
        # Nome do arquivo com timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'relatorio_pontos_{timestamp}.csv'
        
        # Usar stream_with_context conforme documentação oficial Flask
        debug_logger.info("🌊 Criando response com stream_with_context")
        
        response = Response(
            stream_with_context(generate_csv()),
            mimetype='text/csv; charset=utf-8',
            headers={
                'Content-Disposition': f'attachment; filename="{filename}"',
                'Content-Type': 'text/csv; charset=utf-8',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        )
        
        debug_logger.info(f"📤 Resposta CSV criada com stream_with_context - Arquivo: {filename}")
        return response
        
    except Exception as e:
        debug_logger.error(f"❌ Erro na exportação CSV: {str(e)}")
        debug_logger.error(f"❌ Traceback CSV: {traceback.format_exc()}")
        logger.error(f"Erro ao exportar CSV: {str(e)}")
        
        # Retornar erro em formato texto
        return f"Erro ao gerar relatório CSV: {str(e)}", 500

# ========================================
# APIs PARA RELATÓRIOS
# ========================================

@relatorios_bp.route('/api/buscar-registros', methods=['POST'])
@require_login
def api_buscar_registros():
    """
    API para buscar registros de ponto com filtros.
    Retorna dados paginados e formatados.
    """
    error_id = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
    debug_logger.info(f"🎯 API BUSCAR REGISTROS INICIADA - ID: {error_id}")
    
    try:
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        debug_logger.info(f"👤 Nível de acesso: {nivel_acesso}")
        
        # Passo 1: Validar se é JSON
        debug_logger.info("🔍 PASSO 1: Validando formato da requisição")
        if not request.is_json:
            debug_logger.error("❌ Requisição não é JSON")
            return jsonify({
                'success': False,
                'message': 'Requisição deve ser JSON',
                'error_id': error_id
            }), 400
            
        filtros = request.get_json()
        debug_logger.info(f"📤 Filtros recebidos: {filtros}")
        
        # Passo 2: Validar filtros
        debug_logger.info("🔍 PASSO 2: Validando filtros")
        validator = RegistroPontoValidator()
        if not validator.validar_filtros_relatorio(filtros):
            erros = validator.get_errors()
            debug_logger.warning(f"❌ Filtros inválidos: {erros}")
            return jsonify({
                'success': False,
                'message': 'Filtros inválidos',
                'errors': erros,
                'error_id': error_id
            }), 400
        
        debug_logger.info("✅ Filtros válidos")
        
        # Passo 3: Construir query
        debug_logger.info("🔨 PASSO 3: Construindo query base")
        query_base = """
            SELECT 
                rp.id,
                rp.funcionario_id,
                f.nome_completo,
                f.matricula_empresa,
                f.cpf,
                f.setor,
                f.cargo,
                f.empresa,
                rp.data_hora,
                DATE(rp.data_hora) as data_registro,
                TIME(rp.data_hora) as hora_registro,
                rp.tipo_registro,
                rp.metodo_registro,
                rp.qualidade_biometria,
                rp.observacoes,
                rp.ip_origem,
                rp.criado_em,
                u.usuario as criado_por_usuario,
                CASE 
                    WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atraso'
                    WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atraso'
                    ELSE 'Pontual'
                END AS status_pontualidade
            FROM registros_ponto rp
            INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            LEFT JOIN usuarios u ON rp.criado_por = u.id
            WHERE f.ativo = 1
        """
        
        # Passo 4: Construir condições WHERE
        debug_logger.info("🔍 PASSO 4: Construindo condições WHERE")
        condicoes = []
        parametros = []
        
        if filtros.get('data_inicio'):
            condicoes.append("DATE(rp.data_hora) >= %s")
            parametros.append(filtros['data_inicio'])
            debug_logger.debug(f"  📅 Filtro data_inicio: {filtros['data_inicio']}")
        
        if filtros.get('data_fim'):
            condicoes.append("DATE(rp.data_hora) <= %s")
            parametros.append(filtros['data_fim'])
            debug_logger.debug(f"  📅 Filtro data_fim: {filtros['data_fim']}")
        
        if filtros.get('funcionario_id'):
            condicoes.append("rp.funcionario_id = %s")
            parametros.append(int(filtros['funcionario_id']))
            debug_logger.debug(f"  👤 Filtro funcionario_id: {filtros['funcionario_id']}")
        
        if filtros.get('setor'):
            condicoes.append("f.setor = %s")
            parametros.append(filtros['setor'])
            debug_logger.debug(f"  🏢 Filtro setor: {filtros['setor']}")
        
        if filtros.get('tipo_registro'):
            condicoes.append("rp.tipo_registro = %s")
            parametros.append(filtros['tipo_registro'])
            debug_logger.debug(f"  📝 Filtro tipo_registro: {filtros['tipo_registro']}")
        
        if filtros.get('metodo_registro'):
            condicoes.append("rp.metodo_registro = %s")
            parametros.append(filtros['metodo_registro'])
            debug_logger.debug(f"  🔐 Filtro metodo_registro: {filtros['metodo_registro']}")
        
        # Adicionar condições à query
        if condicoes:
            query_base += " AND " + " AND ".join(condicoes)
        
        debug_logger.info(f"🔨 Query construída com {len(condicoes)} condições")
        
        # Passo 5: Configurar paginação
        debug_logger.info("📄 PASSO 5: Configurando paginação")
        page = int(filtros.get('pagina', 1))
        per_page = min(int(filtros.get('registros_por_pagina', 50)), 100)
        offset = (page - 1) * per_page
        debug_logger.info(f"  📊 Página: {page}, Por página: {per_page}, Offset: {offset}")
        
        # Passo 6: Conectar ao banco
        debug_logger.info("🔌 PASSO 6: Conectando ao banco de dados")
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        debug_logger.info("✅ Conexão estabelecida")
        
        try:
            # Passo 7: Contar total de registros
            debug_logger.info("🔢 PASSO 7: Contando total de registros")
            query_count = f"SELECT COUNT(*) as total FROM ({query_base}) as subquery"
            cursor.execute(query_count, parametros)
            total_registros = cursor.fetchone()['total']
            total_pages = (total_registros + per_page - 1) // per_page
            debug_logger.info(f"✅ Total: {total_registros} registros, {total_pages} páginas")
            
            # Passo 8: Buscar registros paginados
            debug_logger.info("📋 PASSO 8: Buscando registros paginados")
            query_final = f"{query_base} ORDER BY rp.data_hora DESC LIMIT %s OFFSET %s"
            parametros_final = parametros + [per_page, offset]
            
            cursor.execute(query_final, parametros_final)
            registros = cursor.fetchall()
            debug_logger.info(f"✅ {len(registros)} registros recuperados")
            
            # Passo 9: Processar registros
            debug_logger.info("🔄 PASSO 9: Processando registros para JSON")
            registros_processados = []
            for i, registro in enumerate(registros):
                try:
                    debug_logger.debug(f"  🔄 Processando registro {i+1}/{len(registros)}")
                    reg_processado = processar_registro_para_json(dict(registro))
                    registros_processados.append(reg_processado)
                except Exception as e:
                    debug_logger.warning(f"⚠️ Erro ao processar registro {registro.get('id', 'unknown')}: {str(e)}")
                    continue
            
            debug_logger.info(f"✅ {len(registros_processados)} registros processados")
            
            # Passo 10: Mascarar dados sensíveis
            debug_logger.info("🔒 PASSO 10: Mascarando dados sensíveis")
            registros_mascarados = []
            for reg in registros_processados:
                try:
                    reg_mascarado = mascarar_dados_relatorio(reg, nivel_acesso)
                    registros_mascarados.append(reg_mascarado)
                except Exception as e:
                    debug_logger.warning(f"⚠️ Erro ao mascarar registro: {str(e)}")
                    registros_mascarados.append(reg)  # Usar sem máscara em caso de erro
            
            debug_logger.info(f"✅ {len(registros_mascarados)} registros mascarados")
            
            # Passo 11: Buscar estatísticas
            debug_logger.info("📊 PASSO 11: Buscando estatísticas")
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_registros,
                    COUNT(DISTINCT funcionario_id) as funcionarios_distintos,
                    SUM(CASE WHEN metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
                    SUM(CASE WHEN metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais
                FROM registros_ponto
                WHERE DATE(data_hora) BETWEEN %s AND %s
            """, (filtros.get('data_inicio', '2025-06-01'), filtros.get('data_fim', '2025-06-06')))
            
            estatisticas_raw = cursor.fetchone()
            debug_logger.info(f"✅ Estatísticas calculadas: {dict(estatisticas_raw)}")
            
            # Processar estatísticas para JSON
            estatisticas = {}
            for chave, valor in estatisticas_raw.items():
                if isinstance(valor, Decimal):
                    estatisticas[chave] = int(valor)
                else:
                    estatisticas[chave] = valor
            
            # Passo 12: Montar resposta final
            debug_logger.info("📦 PASSO 12: Montando resposta final")
            response_data = {
                'success': True,
                'registros': registros_mascarados,
                'total_registros': total_registros,
                'total_paginas': total_pages,
                'pagina_atual': page,
                'estatisticas': estatisticas,
                'debug_info': {
                    'error_id': error_id,
                    'filtros_aplicados': len(condicoes),
                    'registros_processados': len(registros_processados)
                }
            }
            
            debug_logger.info(f"🎉 SUCESSO! Resposta montada - ID: {error_id}")
            return jsonify(response_data)
            
        except Exception as e:
            # Log detalhado do erro
            debug_logger.error(f"💥 ERRO CRÍTICO - ID: {error_id}")
            debug_logger.error(f"💥 Tipo: {type(e).__name__}")
            debug_logger.error(f"💥 Mensagem: {str(e)}")
            debug_logger.error(f"💥 Traceback:\n{traceback.format_exc()}")
            debug_logger.error(f"💥 Filtros que causaram erro: {filtros}")
            
            # Resposta de erro estruturada
            error_response = {
                'success': False,
                'error_id': error_id,
                'message': 'Erro interno do sistema',
                'details': f'Ocorreu um erro inesperado. ID: {error_id}',
                'debug_info': {
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'filtros': filtros
                },
                'code': 500
            }
            
            return jsonify(error_response), 500
            
        finally:
            if 'conn' in locals():
                conn.close()
                debug_logger.info("🔌 Conexão com banco fechada")
        
    except Exception as e:
        debug_logger.error(f"💥 ERRO GERAL - ID: {error_id}: {str(e)}")
        debug_logger.error(f"💥 TRACEBACK:\n{traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': f'Erro ao buscar registros: {str(e)}',
            'error_id': error_id
        }), 500

@relatorios_bp.route('/api/exportar-csv', methods=['POST'])
@require_login
def api_exportar_csv():
    """
    API para exportar registros em formato CSV.
    Aplica mesmos filtros da consulta principal.
    """
    try:
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        
        # Obter filtros da requisição
        filtros = request.get_json() or {}
        
        # Validar filtros
        validator = RegistroPontoValidator()
        if not validator.validar_filtros_relatorio(filtros):
            return jsonify({
                'success': False,
                'message': 'Filtros inválidos',
                'errors': validator.get_errors()
            }), 400
        
        # Limitar exportação (performance)
        limite_exportacao = 5000
        
        # Usar view otimizada
        query_base = "SELECT * FROM vw_relatorio_pontos WHERE 1=1"
        parametros = []
        
        # Aplicar filtros
        if filtros.get('data_inicio'):
            query_base += " AND data_registro >= %s"
            parametros.append(filtros['data_inicio'])
        
        if filtros.get('data_fim'):
            query_base += " AND data_registro <= %s"
            parametros.append(filtros['data_fim'])
        
        if filtros.get('funcionario_id'):
            query_base += " AND funcionario_id = %s"
            parametros.append(int(filtros['funcionario_id']))
        
        if filtros.get('tipo_registro'):
            query_base += " AND tipo_registro = %s"
            parametros.append(filtros['tipo_registro'])
        
        if filtros.get('metodo_registro'):
            query_base += " AND metodo_registro = %s"
            parametros.append(filtros['metodo_registro'])
        
        query_final = f"{query_base} ORDER BY data_hora DESC LIMIT {limite_exportacao}"
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        cursor.execute(query_final, parametros)
        registros = cursor.fetchall()
        conn.close()
        
        # Criar arquivo CSV em memória
        output = io.StringIO()
        writer = csv.writer(output, delimiter=';')
        
        # Cabeçalhos
        cabecalhos = [
            'ID', 'Nome Completo', 'CPF', 'Matrícula', 'Setor', 'Cargo',
            'Data', 'Hora', 'Tipo Registro', 'Método', 'Qualidade Biometria',
            'Status Pontualidade', 'Observações', 'Criado Por', 'IP Origem'
        ]
        writer.writerow(cabecalhos)
        
        # Dados
        for reg in registros:
            # Aplicar mascaramento baseado no nível de acesso
            cpf_exibir = reg[4] if nivel_acesso == 'admin' else reg[5]  # cpf ou cpf_exibicao
            
            linha = [
                reg[0],  # id
                reg[2],  # nome_completo
                cpf_exibir,  # cpf (mascarado ou não)
                reg[3] or '',  # matricula_empresa
                reg[16] or 'Não informado',  # setor
                reg[17] or 'Não informado',  # cargo
                reg[7].strftime('%d/%m/%Y') if reg[7] else '',  # data_registro
                reg[8].strftime('%H:%M') if reg[8] else '',  # hora_registro
                reg[11],  # tipo_descricao
                reg[13],  # metodo_descricao
                reg[18] if reg[18] is not None else '',  # qualidade_biometria
                reg[24],  # status_pontualidade
                reg[19] or '',  # observacoes
                reg[21] or '',  # criado_por_usuario
                reg[20] or ''  # ip_origem
            ]
            writer.writerow(linha)
        
        # Preparar resposta
        csv_content = output.getvalue()
        output.close()
        
        # Nome do arquivo com timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'relatorio_pontos_{timestamp}.csv'
        
        response = make_response(csv_content)
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename={filename}'
        
        # Log da exportação
        logger.info(f"Exportação CSV realizada - Usuário: {session.get('user_id')} - Registros: {len(registros)}")
        
        return response
        
    except Exception as e:
        logger.error(f"Erro ao exportar CSV: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao exportar CSV: {str(e)}'
        }), 500

@relatorios_bp.route('/api/resumo-funcionario/<int:funcionario_id>')
@require_login
def api_resumo_funcionario(funcionario_id):
    """
    API para obter resumo de registros de um funcionário específico.
    Usado em modais de detalhes.
    """
    try:
        periodo_dias = int(request.args.get('periodo', 30))  # Últimos 30 dias por padrão
        data_inicio = (date.today() - timedelta(days=periodo_dias)).strftime('%Y-%m-%d')
        data_fim = date.today().strftime('%Y-%m-%d')
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Informações do funcionário
        cursor.execute("""
            SELECT nome_completo, cpf, setor, cargo, matricula_empresa, empresa, foto_url
            FROM funcionarios 
            WHERE id = %s AND ativo = 1
        """, (funcionario_id,))
        
        funcionario_info = cursor.fetchone()
        if not funcionario_info:
            conn.close()
            return jsonify({
                'success': False,
                'message': 'Funcionário não encontrado'
            }), 404
        
        # Registros do período
        cursor.execute("""
            SELECT 
                DATE(data_hora) as data_trabalho,
                tipo_registro,
                TIME(data_hora) as hora_registro,
                metodo_registro,
                CASE 
                    WHEN tipo_registro = 'entrada_manha' AND TIME(data_hora) > '08:10:00' THEN 'Atraso'
                    WHEN tipo_registro = 'entrada_tarde' AND TIME(data_hora) > '13:10:00' THEN 'Atraso'
                    ELSE 'Pontual'
                END AS status_pontualidade
            FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND DATE(data_hora) BETWEEN %s AND %s
            ORDER BY data_hora DESC
        """, (funcionario_id, data_inicio, data_fim))
        
        registros = cursor.fetchall()
        
        # Estatísticas do período
        cursor.execute("""
            SELECT 
                COUNT(*) as total_registros,
                COUNT(DISTINCT DATE(data_hora)) as dias_trabalhados,
                SUM(CASE WHEN metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
                SUM(CASE WHEN metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais,
                SUM(CASE 
                    WHEN (tipo_registro = 'entrada_manha' AND TIME(data_hora) > '08:10:00') OR
                         (tipo_registro = 'entrada_tarde' AND TIME(data_hora) > '13:10:00')
                    THEN 1 ELSE 0 
                END) as total_atrasos
            FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND DATE(data_hora) BETWEEN %s AND %s
        """, (funcionario_id, data_inicio, data_fim))
        
        estatisticas = cursor.fetchone()
        conn.close()
        
        # Processar registros por dia
        registros_por_dia = {}
        for reg in registros:
            data_str = reg[0].strftime('%Y-%m-%d')
            if data_str not in registros_por_dia:
                registros_por_dia[data_str] = []
            
            registros_por_dia[data_str].append({
                'tipo_registro': reg[1],
                'tipo_descricao': formatar_tipo_registro_descricao(reg[1]),
                'hora_registro': reg[2].strftime('%H:%M') if reg[2] else '',
                'metodo_registro': reg[3],
                'status_pontualidade': reg[4]
            })
        
        # Calcular horas trabalhadas por dia
        horas_por_dia = []
        for data_str, regs in registros_por_dia.items():
            horas_info = calcular_horas_trabalhadas(regs)
            horas_por_dia.append({
                'data': data_str,
                'data_formatada': datetime.strptime(data_str, '%Y-%m-%d').strftime('%d/%m/%Y'),
                'registros': regs,
                'horas_trabalhadas': horas_info['horas_trabalhadas'],
                'completo': horas_info['completo'],
                'observacao': horas_info['observacao']
            })
        
        # Ordenar por data decrescente
        horas_por_dia.sort(key=lambda x: x['data'], reverse=True)
        
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        
        return jsonify({
            'success': True,
            'funcionario': {
                'nome_completo': funcionario_info[0],
                'cpf': funcionario_info[1] if nivel_acesso == 'admin' else '***.***.***-**',
                'setor': funcionario_info[2] or 'Não informado',
                'cargo': funcionario_info[3] or 'Não informado',
                'matricula_empresa': funcionario_info[4] or '',
                'empresa': funcionario_info[5] or 'Não informado',
                'foto_url': funcionario_info[6] or '/static/images/funcionario_sem_foto.svg'
            },
            'estatisticas': {
                'total_registros': estatisticas[0] or 0,
                'dias_trabalhados': estatisticas[1] or 0,
                'registros_biometricos': estatisticas[2] or 0,
                'registros_manuais': estatisticas[3] or 0,
                'total_atrasos': estatisticas[4] or 0,
                'percentual_biometrico': round((estatisticas[2] or 0) / max(estatisticas[0] or 1, 1) * 100, 1)
            },
            'registros_por_dia': horas_por_dia,
            'periodo': f"{data_inicio} a {data_fim}"
        })
        
    except Exception as e:
        logger.error(f"Erro ao buscar resumo do funcionário {funcionario_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao buscar resumo: {str(e)}'
        }), 500

@relatorios_bp.route('/api/dados-graficos', methods=['POST'])
@require_login
def api_dados_graficos():
    """
    API para buscar dados dos gráficos de horas trabalhadas e pontualidade.
    Aceita filtros via POST para personalizar os dados.
    """
    try:
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        
        # Obter filtros da requisição
        filtros = {}
        if request.is_json:
            filtros = request.get_json() or {}
        
        # Definir período padrão se não fornecido
        data_inicio = filtros.get('data_inicio')
        data_fim = filtros.get('data_fim')
        
        if not data_inicio or not data_fim:
            # Usar último mês como padrão
            from datetime import datetime, timedelta
            hoje = datetime.now().date()
            data_inicio = (hoje - timedelta(days=30)).strftime('%Y-%m-%d')
            data_fim = hoje.strftime('%Y-%m-%d')
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Query para dados de horas trabalhadas (últimos 7 dias)
        query_horas = """
            SELECT 
                DATE(rp.data_hora) as data_registro,
                COUNT(DISTINCT rp.funcionario_id) as funcionarios_ativos,
                COUNT(*) as total_registros,
                ROUND(COUNT(*) / COUNT(DISTINCT rp.funcionario_id) * 2, 1) as horas_estimadas
            FROM registros_ponto rp
            INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            WHERE DATE(rp.data_hora) BETWEEN %s AND %s
            AND f.ativo = 1
        """
        
        # Adicionar filtros específicos
        parametros_horas = [data_inicio, data_fim]
        
        if filtros.get('funcionario_id'):
            query_horas += " AND rp.funcionario_id = %s"
            parametros_horas.append(int(filtros['funcionario_id']))
        
        if filtros.get('setor'):
            query_horas += " AND f.setor = %s"
            parametros_horas.append(filtros['setor'])
        
        query_horas += " GROUP BY DATE(rp.data_hora) ORDER BY data_registro DESC LIMIT 7"
        
        cursor.execute(query_horas, parametros_horas)
        dados_horas = cursor.fetchall()
        
        # Query para dados de pontualidade
        query_pontualidade = """
            SELECT 
                DATE(rp.data_hora) as data_registro,
                COUNT(*) as total_registros,
                SUM(CASE 
                    WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) <= '08:10:00' THEN 1
                    WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) <= '13:10:00' THEN 1
                    WHEN rp.tipo_registro IN ('saida_almoco', 'saida') THEN 1
                    ELSE 0
                END) as registros_pontuais
            FROM registros_ponto rp
            INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            WHERE DATE(rp.data_hora) BETWEEN %s AND %s
            AND f.ativo = 1
        """
        
        parametros_pontualidade = [data_inicio, data_fim]
        
        if filtros.get('funcionario_id'):
            query_pontualidade += " AND rp.funcionario_id = %s"
            parametros_pontualidade.append(int(filtros['funcionario_id']))
        
        if filtros.get('setor'):
            query_pontualidade += " AND f.setor = %s"
            parametros_pontualidade.append(filtros['setor'])
        
        query_pontualidade += " GROUP BY DATE(rp.data_hora) ORDER BY data_registro DESC LIMIT 7"
        
        cursor.execute(query_pontualidade, parametros_pontualidade)
        dados_pontualidade = cursor.fetchall()
        
        # Processar dados para gráficos
        labels_horas = []
        valores_horas = []
        
        for row in reversed(dados_horas):  # Reverter para ordem cronológica
            labels_horas.append(row['data_registro'].strftime('%d/%m'))
            valores_horas.append(float(row['horas_estimadas']))
        
        labels_pontualidade = []
        valores_pontualidade = []
        
        for row in reversed(dados_pontualidade):
            labels_pontualidade.append(row['data_registro'].strftime('%d/%m'))
            if row['total_registros'] > 0:
                taxa = (row['registros_pontuais'] / row['total_registros']) * 100
            else:
                taxa = 0
            valores_pontualidade.append(round(taxa, 1))
        
        # Calcular resumos
        total_horas_mes = sum(valores_horas)
        media_horas_dia = round(total_horas_mes / max(len(valores_horas), 1), 1)
        dias_trabalhados = len(dados_horas)
        
        taxa_pontualidade_media = round(sum(valores_pontualidade) / max(len(valores_pontualidade), 1), 1)
        total_atrasos = sum(1 for v in valores_pontualidade if v < 90)  # Considerar < 90% como atraso
        
        # Montar resposta
        response_data = {
            'success': True,
            'dados': {
                'horas': {
                    'labels': labels_horas,
                    'valores': valores_horas
                },
                'pontualidade': {
                    'labels': labels_pontualidade,
                    'valores': valores_pontualidade
                },
                'resumo': {
                    'total_horas_mes': f"{total_horas_mes:.1f}h",
                    'media_horas_dia': f"{media_horas_dia:.1f}h",
                    'dias_trabalhados': dias_trabalhados,
                    'taxa_pontualidade': taxa_pontualidade_media,
                    'total_atrasos': total_atrasos
                }
            }
        }
        
        conn.close()
        
        logger.info(f"[DADOS GRÁFICOS] Dados gerados com sucesso - Período: {data_inicio} a {data_fim}")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"[DADOS GRÁFICOS] Erro: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao gerar dados dos gráficos',
            'dados': {
                'horas': {'labels': [], 'valores': []},
                'pontualidade': {'labels': [], 'valores': []},
                'resumo': {
                    'total_horas_mes': '0h',
                    'media_horas_dia': '0h',
                    'dias_trabalhados': 0,
                    'taxa_pontualidade': 0,
                    'total_atrasos': 0
                }
            }
        })

# ========================================
# TRATAMENTO DE ERROS
# ========================================

@relatorios_bp.errorhandler(404)
def handle_404(error):
    """Tratamento de erro 404 para o blueprint."""
    return jsonify({
        'success': False,
        'message': 'Endpoint não encontrado'
    }), 404

@relatorios_bp.errorhandler(500)
def handle_500(error):
    """Tratamento de erro 500 para o blueprint."""
    logger.error(f"Erro interno no blueprint relatorios: {str(error)}")
    return jsonify({
        'success': False,
        'message': 'Erro interno do servidor'
    }), 500

# ========================================
# FIM DO BLUEPRINT RELATÓRIOS
# ========================================