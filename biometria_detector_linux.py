#!/usr/bin/env python3
"""
Detector Biométrico para Linux - Fallback quando bridge Windows não disponível
Sistema RLPONTO-WEB

Este módulo fornece detecção básica de dispositivos biométricos em sistemas Linux
quando o bridge Windows não está acessível via rede.
"""

import subprocess
import re
import time
import platform
import logging

logger = logging.getLogger(__name__)

def detect_biometric_devices():
    """
    Detecta dispositivos biométricos em sistemas Linux
    
    Returns:
        list: Lista de dispositivos biométricos detectados
    """
    devices = []
    
    try:
        logger.info("🐧 [LINUX DETECTOR] Iniciando detecção de dispositivos biométricos...")
        
        # MÉTODO 1: lsusb para dispositivos USB
        try:
            result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if any(keyword in line.lower() for keyword in ['fingerprint', 'biometric', 'zk4500', '1b55:0840']):
                        # Parse da linha lsusb
                        match = re.search(r'Bus (\d+) Device (\d+): ID ([0-9a-f]{4}):([0-9a-f]{4})\s+(.+)', line)
                        if match:
                            bus, device, vid, pid, name = match.groups()
                            
                            device_info = {
                                'friendly_name': name.strip(),
                                'instance_id': f"USB\\VID_{vid.upper()}&PID_{pid.upper()}\\Linux_{bus}_{device}",
                                'status': 'OK',
                                'class': 'Biometric',
                                'manufacturer': 'ZKTeco Inc.' if vid.lower() == '1b55' else 'Unknown',
                                'device_type': 'fingerprint',
                                'vendor_id': vid.upper(),
                                'product_id': pid.upper(),
                                'supported': vid.lower() == '1b55' and pid.lower() == '0840',
                                'detection_method': 'LSUSB_LINUX',
                                'bridge_timestamp': time.time(),
                                'local_machine': platform.node()
                            }
                            devices.append(device_info)
                            logger.info(f"✅ [LINUX DETECTOR] Dispositivo USB detectado: {name}")
        except Exception as e:
            logger.warning(f"❌ [LINUX DETECTOR] Erro no lsusb: {e}")
        
        # MÉTODO 2: /sys/bus/usb/devices para informações detalhadas
        try:
            usb_devices_result = subprocess.run(['find', '/sys/bus/usb/devices', '-name', 'idVendor'], 
                                              capture_output=True, text=True, timeout=10)
            if usb_devices_result.returncode == 0:
                for vendor_file in usb_devices_result.stdout.strip().split('\n'):
                    if vendor_file:
                        try:
                            # Ler vendor ID
                            with open(vendor_file, 'r') as f:
                                vid = f.read().strip()
                            
                            # Ler product ID
                            product_file = vendor_file.replace('idVendor', 'idProduct')
                            with open(product_file, 'r') as f:
                                pid = f.read().strip()
                            
                            # Verificar se é ZK4500
                            if vid.lower() == '1b55' and pid.lower() == '0840':
                                device_info = {
                                    'friendly_name': 'ZK4500 Fingerprint Reader (Linux)',
                                    'instance_id': f"USB\\VID_{vid.upper()}&PID_{pid.upper()}\\SYS_{int(time.time())}",
                                    'status': 'OK',
                                    'class': 'Biometric',
                                    'manufacturer': 'ZKTeco Inc.',
                                    'device_type': 'fingerprint',
                                    'vendor_id': vid.upper(),
                                    'product_id': pid.upper(),
                                    'supported': True,
                                    'detection_method': 'SYS_LINUX',
                                    'bridge_timestamp': time.time(),
                                    'local_machine': platform.node()
                                }
                                
                                # Verificar se já não foi adicionado
                                if not any(d['vendor_id'] == vid.upper() and d['product_id'] == pid.upper() for d in devices):
                                    devices.append(device_info)
                                    logger.info(f"✅ [LINUX DETECTOR] ZK4500 detectado via /sys")
                                    
                        except Exception as e:
                            logger.debug(f"Erro ao processar {vendor_file}: {e}")
                            continue
                            
        except Exception as e:
            logger.warning(f"❌ [LINUX DETECTOR] Erro no /sys: {e}")
        
        # MÉTODO 3: Simulação para desenvolvimento (apenas se não encontrar dispositivos reais)
        if not devices:
            logger.info("🔄 [LINUX DETECTOR] Nenhum dispositivo real encontrado, verificando configuração de simulação...")
            
            # Verificar se está em ambiente de desenvolvimento
            if platform.node().lower() in ['localhost', 'development', 'dev']:
                logger.info("🧪 [LINUX DETECTOR] Ambiente de desenvolvimento detectado")
                # Em ambiente real, remover esta seção
                
        logger.info(f"🎯 [LINUX DETECTOR] Total detectado: {len(devices)} dispositivos")
        return devices
        
    except Exception as e:
        logger.error(f"❌ [LINUX DETECTOR] Erro geral: {e}")
        return []

def detect_biometric_devices_fallback():
    """
    Fallback quando nem Windows nem Linux conseguem detectar
    Retorna configuração manual baseada em dados conhecidos
    """
    logger.warning("⚠️  [FALLBACK] Usando detecção fallback - configuração manual")
    
    # Esta função deve ser configurada com base no hardware conhecido
    # Em produção, remover ou substituir por configuração real
    return []

def get_device_status(device_id):
    """
    Verifica status específico de um dispositivo
    
    Args:
        device_id (str): ID do dispositivo
        
    Returns:
        dict: Status do dispositivo
    """
    try:
        # Verificar se dispositivo ainda está conectado
        devices = detect_biometric_devices()
        
        for device in devices:
            if device['instance_id'] == device_id:
                return {
                    'connected': True,
                    'status': device['status'],
                    'last_seen': device['bridge_timestamp']
                }
        
        return {
            'connected': False,
            'status': 'Disconnected',
            'last_seen': None
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao verificar status do dispositivo: {e}")
        return {
            'connected': False,
            'status': 'Error',
            'error': str(e)
        }

if __name__ == "__main__":
    # Teste do detector
    print("🧪 TESTE DO DETECTOR BIOMÉTRICO LINUX")
    print("=" * 50)
    
    devices = detect_biometric_devices()
    
    if devices:
        print(f"✅ {len(devices)} dispositivos encontrados:")
        for i, device in enumerate(devices, 1):
            print(f"  {i}. {device['friendly_name']}")
            print(f"     VID:PID = {device['vendor_id']}:{device['product_id']}")
            print(f"     Status = {device['status']}")
            print(f"     Método = {device['detection_method']}")
    else:
        print("❌ Nenhum dispositivo biométrico encontrado")
    
    print("=" * 50) 