{% extends "base.html" %}

{% block title %}Detalhes do Funcionário - {{ funcionario.nome_completo }}{% endblock %}

{% block extra_css %}
<style>
/* ✨ MAGIC UI INSPIRED DESIGN SYSTEM - @21st-dev/magic */

:root {
    /* Core Brand Colors */
    --primary: #667eea;
    --primary-foreground: #ffffff;
    --secondary: #764ba2;
    --accent: #4facfe;
    --accent-secondary: #00f2fe;

    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;

    /* Neutral Palette */
    --background: #fafbfc;
    --foreground: #0f172a;
    --muted: #f8fafc;
    --muted-foreground: #64748b;
    --border: #e2e8f0;
    --card: #ffffff;
    --card-foreground: #0f172a;

    /* Gradients - Magic UI Style */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

    /* Shadows - Layered Design */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-glow: 0 0 20px rgb(102 126 234 / 0.3);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;

    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;
    --space-16: 4rem;
}

/* 🌟 Global Styles */
body {
    background: var(--background);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    color: var(--foreground);
    line-height: 1.6;
}

.content-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-8) var(--space-4);
}

/* Modern Breadcrumbs */
.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 2rem;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.breadcrumb-item a:hover {
    color: var(--gray-800);
    transform: translateX(2px);
}

.breadcrumb-item.active {
    color: var(--gray-600);
    font-weight: 600;
}

/* 🎨 HERO SECTION - Magic UI Inspired */
.profile-hero {
    position: relative;
    background: var(--gradient-primary);
    border-radius: var(--radius-3xl);
    padding: 0;
    overflow: hidden;
    margin-bottom: var(--space-8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-xl);
}

/* Glassmorphism Effect */
.profile-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-glass);
    backdrop-filter: blur(10px);
    pointer-events: none;
    z-index: 1;
}

/* Animated Shimmer Effect */
.profile-hero::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: shimmer 4s ease-in-out infinite;
    pointer-events: none;
    z-index: 2;
}

@keyframes shimmer {
    0%, 100% {
        transform: translateX(-100%) translateY(-100%) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: translateX(0%) translateY(0%) rotate(180deg);
        opacity: 0.6;
    }
}

.profile-hero:hover {
    transform: translateY(-6px) scale(1.01);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.hero-content {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: var(--space-8);
    padding: var(--space-12);
}

/* 🖼️ GLARE CARD PROFILE IMAGE */
.profile-image-container {
    position: relative;
    width: 160px;
    height: 160px;
    border-radius: var(--radius-3xl);
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-lg);
}

/* Glare Effect on Hover */
.profile-image-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.4) 50%, transparent 70%);
    transform: rotate(-45deg);
    transition: all 0.6s ease;
    opacity: 0;
    z-index: 5;
}

.profile-image-container:hover::before {
    animation: glare 0.8s ease-in-out;
}

@keyframes glare {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(-45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(-45deg);
        opacity: 0;
    }
}

.profile-image-container:hover {
    transform: scale(1.08) rotate(3deg);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: var(--shadow-xl), 0 0 30px rgba(255, 255, 255, 0.3);
}

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 3;
}

.profile-image-container:hover .profile-image {
    transform: scale(1.1);
    filter: brightness(1.1) contrast(1.05);
}

/* 📝 PROFILE INFORMATION */
.profile-info h1 {
    color: white;
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 800;
    margin: 0 0 var(--space-3) 0;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.025em;
    line-height: 1.1;
    position: relative;
    z-index: 10;
}

.profile-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.25rem;
    margin: 0 0 var(--space-6) 0;
    font-weight: 500;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 10;
}

/* 🏷️ WOBBLE CARD INSPIRED BADGES */
.profile-badges {
    display: flex;
    gap: var(--space-3);
    flex-wrap: wrap;
    margin-bottom: var(--space-4);
    position: relative;
    z-index: 10;
}

.modern-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

/* Shimmer Animation for Badges */
.modern-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.modern-badge:hover::before {
    left: 100%;
}

.modern-badge:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(255, 255, 255, 0.2);
}

.badge-success {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success);
    border-color: rgba(16, 185, 129, 0.3);
}

.badge-success:hover {
    background: rgba(16, 185, 129, 0.3);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(16, 185, 129, 0.3);
}

.badge-danger {
    background: rgba(239, 68, 68, 0.2);
    color: var(--danger);
    border-color: rgba(239, 68, 68, 0.3);
}

.badge-danger:hover {
    background: rgba(239, 68, 68, 0.3);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(239, 68, 68, 0.3);
}

.badge-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.badge-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(255, 255, 255, 0.2);
}

/* 🎯 ACTION BUTTONS - Modern Interactive */
.action-buttons {
    display: flex;
    gap: var(--space-4);
    margin-left: auto;
    position: relative;
    z-index: 10;
}

.modern-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
}

/* Button Shimmer Effect */
.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:hover {
    transform: translateY(-3px) scale(1.02);
}

.btn-primary {
    background: rgba(255, 255, 255, 0.9);
    color: var(--foreground);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-primary:hover {
    background: white;
    box-shadow: var(--shadow-xl), 0 0 25px rgba(255, 255, 255, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(255, 255, 255, 0.2);
}

.btn-print {
    background: var(--gradient-accent);
    color: white;
    border: 1px solid rgba(79, 172, 254, 0.3);
}

.btn-print:hover {
    box-shadow: var(--shadow-xl), 0 0 25px rgba(79, 172, 254, 0.4);
}

.btn-pdf {
    background: var(--gradient-success);
    color: white;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.btn-pdf:hover {
    box-shadow: var(--shadow-xl), 0 0 25px rgba(16, 185, 129, 0.4);
}

/* 🃏 LAYOUT GRID INSPIRED CARDS */
.modern-card {
    background: var(--card);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--border);
    box-shadow: var(--shadow-md);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    height: 100%;
    position: relative;
}

/* Card Hover Effects */
.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-glass);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.modern-card:hover::before {
    opacity: 1;
}

.modern-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
    border-color: var(--primary);
}

.card-header-modern {
    background: linear-gradient(135deg, var(--muted) 0%, var(--card) 100%);
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid var(--border);
    position: relative;
    z-index: 2;
}

.card-title-modern {
    font-size: 1.375rem;
    font-weight: 800;
    color: var(--card-foreground);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    letter-spacing: -0.025em;
}

.card-title-modern i {
    color: var(--primary);
    font-size: 1.25rem;
    padding: var(--space-2);
    background: rgba(102, 126, 234, 0.1);
    border-radius: var(--radius);
}

.card-body-modern {
    padding: var(--space-8);
    position: relative;
    z-index: 2;
}

/* 📊 RESPONSIVE FIELD GRID */
.field-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
}

.field-item {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    padding: var(--space-4);
    background: var(--muted);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.field-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.field-item:hover::before {
    transform: scaleY(1);
}

.field-item:hover {
    background: var(--card);
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.field-label {
    font-size: 0.75rem;
    font-weight: 700;
    color: var(--muted-foreground);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: var(--space-1);
}

.field-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--card-foreground);
    line-height: 1.4;
}

.field-value.monospace {
    font-family: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
    background: var(--background);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    box-shadow: var(--shadow-sm);
}

.field-value a {
    color: var(--primary);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.field-value a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary);
    transition: width 0.3s ease;
}

.field-value a:hover::after {
    width: 100%;
}

.field-value a:hover {
    color: var(--secondary);
    transform: translateX(4px);
}

/* 🕐 SCHEDULE CARDS - Bento Grid Inspired */
.schedule-item {
    background: var(--muted);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.schedule-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.schedule-item:hover::before {
    transform: scaleX(1);
}

.schedule-item:hover {
    background: var(--card);
    border-color: var(--accent);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.schedule-title {
    font-size: 0.875rem;
    font-weight: 700;
    color: var(--muted-foreground);
    margin-bottom: var(--space-4);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.schedule-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.time-slot {
    text-align: center;
    flex: 1;
}

.time-label {
    font-size: 0.75rem;
    color: var(--muted-foreground);
    margin-bottom: var(--space-2);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.time-value {
    font-family: 'JetBrains Mono', 'SF Mono', monospace;
    font-size: 1.25rem;
    font-weight: 800;
    color: var(--primary);
    background: rgba(102, 126, 234, 0.1);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius);
    display: inline-block;
    min-width: 80px;
}

.schedule-arrow {
    color: var(--accent);
    font-size: 1.25rem;
    margin: 0 var(--space-4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* 🛡️ EPI SECTION - Feature Card Inspired */
.epi-item {
    background: var(--card);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.epi-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.05), transparent);
    transition: left 0.5s;
}

.epi-item:hover::before {
    left: 100%;
}

.epi-item:hover {
    background: var(--muted);
    border-color: var(--primary);
    transform: translateX(6px);
    box-shadow: var(--shadow-md);
}

.epi-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--space-4);
}

.epi-info h6 {
    font-size: 1rem;
    font-weight: 700;
    color: var(--card-foreground);
    margin-bottom: var(--space-1);
}

.epi-ca {
    font-size: 0.75rem;
    color: var(--muted-foreground);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.epi-status {
    margin-left: auto;
}

.status-badge {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 700;
    text-align: center;
    min-width: 80px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-expired {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-valid {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-no-date {
    background: rgba(107, 114, 128, 0.1);
    color: var(--muted-foreground);
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.epi-delivery {
    font-size: 0.75rem;
    color: var(--muted-foreground);
    margin-top: var(--space-2);
    font-style: italic;
}

.epi-empty {
    text-align: center;
    padding: var(--space-12) var(--space-4);
    color: var(--muted-foreground);
}

.epi-empty i {
    font-size: 3rem;
    color: var(--border);
    margin-bottom: var(--space-4);
    opacity: 0.5;
}

/* Schedule Cards */
.schedule-item {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.schedule-item:hover {
    background: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.schedule-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.schedule-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.time-slot {
    text-align: center;
}

.time-label {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.time-value {
    font-family: 'SF Mono', monospace;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--primary-color);
}

.schedule-arrow {
    color: var(--primary-color);
    font-size: 1.25rem;
}

/* EPI Section */
.epi-item {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.epi-item:hover {
    background: var(--gray-50);
    border-color: var(--primary-color);
    transform: translateX(4px);
}

.epi-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    gap: 1rem;
}

.epi-info h6 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.epi-ca {
    font-size: 0.75rem;
    color: var(--gray-500);
    font-weight: 500;
}

.epi-status {
    margin-left: auto;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    min-width: 60px;
}

.status-expired {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-valid {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-no-date {
    background: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
}

.epi-delivery {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: 0.5rem;
}

.epi-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--gray-500);
}

.epi-empty i {
    font-size: 3rem;
    color: var(--gray-300);
    margin-bottom: 1rem;
}

/* 📱 RESPONSIVE DESIGN */
@media (max-width: 1024px) {
    .hero-content {
        gap: var(--space-6);
        padding: var(--space-8);
    }

    .profile-image-container {
        width: 140px;
        height: 140px;
    }
}

@media (max-width: 768px) {
    .content-wrapper {
        padding: var(--space-4) var(--space-2);
    }

    .hero-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-6);
        padding: var(--space-6);
    }

    .action-buttons {
        margin-left: 0;
        justify-content: center;
        flex-wrap: wrap;
    }

    .profile-info h1 {
        font-size: clamp(1.5rem, 8vw, 2.5rem);
    }

    .field-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .profile-image-container {
        width: 120px;
        height: 120px;
    }
}

@media (max-width: 480px) {
    .modern-btn {
        padding: var(--space-2) var(--space-4);
        font-size: 0.8rem;
    }

    .profile-badges {
        justify-content: center;
    }
}

/* ✨ ADVANCED ANIMATIONS */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Staggered Animation */
.modern-card {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.modern-card:nth-child(1) { animation-delay: 0.1s; }
.modern-card:nth-child(2) { animation-delay: 0.2s; }
.modern-card:nth-child(3) { animation-delay: 0.3s; }
.modern-card:nth-child(4) { animation-delay: 0.4s; }

.profile-hero {
    animation: scaleIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.field-item {
    animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.field-item:nth-child(odd) { animation-delay: 0.1s; }
.field-item:nth-child(even) { animation-delay: 0.2s; }

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Modern Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ url_for('index') }}">
                    <i class="fas fa-home"></i> Início
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ url_for('funcionarios.index') }}">Funcionários</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">{{ funcionario.nome_completo }}</li>
        </ol>
    </nav>

    <!-- Modern Profile Hero -->
    <div class="profile-hero">
        <div class="hero-content">
                         <!-- Modern Profile Image -->
             <div class="profile-image-container">
                                  <img src="{{ url_for('funcionarios.foto_funcionario', funcionario_id=funcionario.id) }}" 
                      alt="Foto de {{ funcionario.nome_completo }}" 
                      class="profile-image"
                       onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjNGNEY2Ii8+CjxjaXJjbGUgY3g9IjYwIiBjeT0iNDUiIHI9IjE1IiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik02MCA2NkMzNy45IDY2IDIwIDc4LjMgMjAgOTNWMTEwSDEwMFY5M0MxMDAgNzguMyA4Mi4xIDY2IDYwIDY2WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'">
             </div>
            
            <!-- Profile Information -->
            <div class="profile-info flex-grow-1">
                <h1>{{ funcionario.nome_completo }}</h1>
                <p class="profile-subtitle">{{ funcionario.cargo }} • {{ funcionario.setor_obra }}</p>
                
                <div class="profile-badges">
                    {% if funcionario.status_cadastro == 'Ativo' %}
                        <span class="modern-badge badge-success">
                            <i class="fas fa-check-circle"></i>
                            {{ funcionario.status_cadastro }}
                        </span>
                    {% else %}
                        <span class="modern-badge badge-danger">
                            <i class="fas fa-times-circle"></i>
                            {{ funcionario.status_cadastro }}
                        </span>
                    {% endif %}
                    <span class="modern-badge badge-primary">
                        <i class="fas fa-id-card"></i>
                        {{ funcionario.matricula_empresa }}
                    </span>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
                {% if current_user.is_admin %}
                <a href="{{ url_for('funcionarios.editar', funcionario_id=funcionario.id) }}"
                   class="modern-btn btn-primary">
                    <i class="fas fa-edit"></i> Editar
                </a>
                {% endif %}

                <!-- Botões de Impressão e PDF -->
                <a href="{{ url_for('funcionarios.ficha_impressao', funcionario_id=funcionario.id) }}"
                   class="modern-btn btn-print"
                   target="_blank"
                   title="Abrir ficha para impressão">
                    <i class="fas fa-print"></i> Imprimir
                </a>

                <a href="{{ url_for('funcionarios.ficha_pdf', funcionario_id=funcionario.id) }}"
                   class="modern-btn btn-pdf"
                   target="_blank"
                   title="Baixar ficha em PDF">
                    <i class="fas fa-file-pdf"></i> PDF
                </a>

                <a href="{{ url_for('funcionarios.index') }}"
                   class="modern-btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>
    </div>

    <!-- Information Cards Grid -->
    <div class="row g-4">
        <!-- Personal Information -->
        <div class="col-lg-6">
            <div class="modern-card">
                <div class="card-header-modern">
                    <h5 class="card-title-modern">
                        <i class="fas fa-user"></i>
                        Informações Pessoais
                    </h5>
                </div>
                <div class="card-body-modern">
                    <div class="field-grid">
                        <div class="field-item">
                            <div class="field-label">Nome Completo</div>
                            <div class="field-value">{{ funcionario.nome_completo }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">CPF</div>
                            <div class="field-value monospace">{{ funcionario.cpf | format_cpf }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">RG</div>
                            <div class="field-value">{{ funcionario.rg or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Data de Nascimento</div>
                            <div class="field-value">{{ funcionario.data_nascimento | format_date }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Sexo</div>
                            <div class="field-value">{{ funcionario.sexo }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Estado Civil</div>
                            <div class="field-value">{{ funcionario.estado_civil or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Nacionalidade</div>
                            <div class="field-value">{{ funcionario.nacionalidade or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Telefone</div>
                            <div class="field-value">
                                {% if funcionario.telefone1 %}
                                    <a href="tel:{{ funcionario.telefone1 }}">
                                        {{ funcionario.telefone1 | format_telefone }}
                                    </a>
                                {% else %}
                                    Não informado
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contractual Information -->
        <div class="col-lg-6">
            <div class="modern-card">
                <div class="card-header-modern">
                    <h5 class="card-title-modern">
                        <i class="fas fa-briefcase"></i>
                        Informações Contratuais
                    </h5>
                </div>
                <div class="card-body-modern">
                    <div class="field-grid">
                        <div class="field-item">
                            <div class="field-label">Matrícula</div>
                            <div class="field-value monospace">{{ funcionario.matricula_empresa }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Status</div>
                            <div class="field-value">
                                {% if funcionario.status_cadastro == 'Ativo' %}
                                    <span class="modern-badge badge-success">{{ funcionario.status_cadastro }}</span>
                                {% elif funcionario.status_cadastro == 'Inativo' %}
                                    <span class="modern-badge badge-danger">{{ funcionario.status_cadastro }}</span>
                                {% else %}
                                    <span class="modern-badge badge-primary">{{ funcionario.status_cadastro }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Cargo</div>
                            <div class="field-value">{{ funcionario.cargo }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Setor</div>
                            <div class="field-value">{{ funcionario.setor_obra }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Data de Admissão</div>
                            <div class="field-value">{{ funcionario.data_admissao | format_date }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Tipo de Contrato</div>
                            <div class="field-value">{{ funcionario.tipo_contrato or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">CTPS</div>
                            <div class="field-value monospace">{{ funcionario.ctps_numero or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">PIS/PASEP</div>
                            <div class="field-value monospace">{{ funcionario.pis_pasep or 'Não informado' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Work Schedule -->
        <div class="col-lg-8">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title-modern mb-0">
                            <i class="fas fa-clock"></i>
                            Jornada de Trabalho
                        </h5>
                        <!-- Link temporariamente removido - rota em desenvolvimento -->
                        <span class="btn btn-outline-secondary btn-sm disabled">
                            <i class="fas fa-history me-1"></i>
                            Histórico (em breve)
                        </span>
                    </div>
                </div>
                <div class="card-body-modern">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="schedule-item">
                                <div class="schedule-title">Segunda a Quinta-feira</div>
                                <div class="schedule-time">
                                    <div class="time-slot">
                                        <div class="time-label">Entrada</div>
                                        <div class="time-value">{{ funcionario.jornada_seg_qui_entrada or '--:--' }}</div>
                                    </div>
                                    <i class="fas fa-arrow-right schedule-arrow"></i>
                                    <div class="time-slot">
                                        <div class="time-label">Saída</div>
                                        <div class="time-value">{{ funcionario.jornada_seg_qui_saida or '--:--' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="schedule-item">
                                <div class="schedule-title">Sexta-feira</div>
                                <div class="schedule-time">
                                    <div class="time-slot">
                                        <div class="time-label">Entrada</div>
                                        <div class="time-value">{{ funcionario.jornada_sexta_entrada or '--:--' }}</div>
                                    </div>
                                    <i class="fas fa-arrow-right schedule-arrow"></i>
                                    <div class="time-slot">
                                        <div class="time-label">Saída</div>
                                        <div class="time-value">{{ funcionario.jornada_sexta_saida or '--:--' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="schedule-item">
                                <div class="schedule-title">Intervalo para Almoço</div>
                                <div class="schedule-time">
                                    <div class="time-slot">
                                        <div class="time-label">Saída</div>
                                        <div class="time-value">{{ funcionario.jornada_intervalo_inicio or '--:--' }}</div>
                                    </div>
                                    <i class="fas fa-arrow-right schedule-arrow"></i>
                                    <div class="time-slot">
                                        <div class="time-label">Retorno</div>
                                        <div class="time-value">{{ funcionario.jornada_intervalo_fim or '--:--' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="schedule-item">
                                <div class="schedule-title">Configurações</div>
                                <div class="field-grid">
                                    <div class="field-item">
                                        <div class="field-label">Turno</div>
                                        <div class="field-value">{{ funcionario.tipo_jornada or 'Não definido' }}</div>
                                    </div>
                                    <div class="field-item">
                                        <div class="field-label">Tolerância</div>
                                        <div class="field-value">{{ funcionario.jornada_tolerancia_entrada_minutos or '0' }} minutos</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- EPIs -->
        <div class="col-lg-4">
            <div class="modern-card">
                <div class="card-header-modern">
                    <h5 class="card-title-modern">
                        <i class="fas fa-hard-hat"></i>
                        EPIs Ativos
                    </h5>
                </div>
                <div class="card-body-modern">
                    {% if funcionario.epis %}
                        {% for epi in funcionario.epis %}
                        <div class="epi-item">
                            <div class="epi-header">
                                <div class="epi-info">
                                    <h6>{{ epi.epi_nome }}</h6>
                                    <div class="epi-ca">CA: {{ epi.epi_ca or 'N/A' }}</div>
                                </div>
                                <div class="epi-status">
                                    {% if epi.epi_data_validade %}
                                        <span class="status-badge status-valid">Válido</span>
                                    {% else %}
                                        <span class="status-badge status-no-date">Sem validade</span>
                                    {% endif %}
                                </div>
                            </div>
                            {% if epi.epi_data_entrega %}
                            <div class="epi-delivery">
                                Entregue em: {{ epi.epi_data_entrega | format_date }}
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="epi-empty">
                            <i class="fas fa-hard-hat"></i>
                            <p>Nenhum EPI cadastrado</p>
                            {% if current_user.is_admin %}
                            <small>Edite o funcionário para adicionar EPIs</small>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Address Section (if available) -->
    {% if funcionario.endereco_rua or funcionario.endereco_cidade %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="modern-card">
                <div class="card-header-modern">
                    <h5 class="card-title-modern">
                        <i class="fas fa-map-marker-alt"></i>
                        Endereço
                    </h5>
                </div>
                <div class="card-body-modern">
                    <div class="field-grid">
                        <div class="field-item">
                            <div class="field-label">Logradouro</div>
                            <div class="field-value">{{ funcionario.endereco_rua or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">CEP</div>
                            <div class="field-value monospace">{{ funcionario.endereco_cep or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Cidade</div>
                            <div class="field-value">{{ funcionario.endereco_cidade or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Estado</div>
                            <div class="field-value">{{ funcionario.endereco_estado or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Bairro</div>
                            <div class="field-value">{{ funcionario.endereco_bairro or 'Não informado' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}