{"metadata": {"data_teste": "2025-07-17T22:31:18.904625", "sistema": "RLPONTO-WEB v1.0", "tipo_teste": "Robusto 30 Funcionários", "total_funcionarios": 30, "total_batidas": 660}, "estatisticas": {"total_funcionarios": 30, "total_batidas": 660, "funcionarios_corretos": 15, "funcionarios_problematicos": 15, "horas_totais": 4986.5, "banco_horas_total": -266.58, "horas_extras_total": 97.5, "erros_detectados": 48}, "funcionarios": [{"id": 1, "nome": "João01", "matricula": "MAT0001", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 4.474646244734075, "tipo_teste": "correto"}, {"id": 2, "nome": "João02", "matricula": "MAT0002", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -2.2962405533632646, "tipo_teste": "correto"}, {"id": 3, "nome": "João03", "matricula": "MAT0003", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -2.4817607762697937, "tipo_teste": "correto"}, {"id": 4, "nome": "João04", "matricula": "MAT0004", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -1.25197228523527, "tipo_teste": "correto"}, {"id": 5, "nome": "João05", "matricula": "MAT0005", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -1.3814531422954488, "tipo_teste": "correto"}, {"id": 6, "nome": "João06", "matricula": "MAT0006", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -0.9600911231617903, "tipo_teste": "correto"}, {"id": 7, "nome": "João07", "matricula": "MAT0007", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 2.223381341795239, "tipo_teste": "correto"}, {"id": 8, "nome": "João08", "matricula": "MAT0008", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 2.249822734876222, "tipo_teste": "correto"}, {"id": 9, "nome": "João09", "matricula": "MAT0009", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 1.4204141450415788, "tipo_teste": "correto"}, {"id": 10, "nome": "João10", "matricula": "MAT0010", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 1.8705268764263643, "tipo_teste": "correto"}, {"id": 11, "nome": "João11", "matricula": "MAT0011", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -2.3502839368835513, "tipo_teste": "correto"}, {"id": 12, "nome": "João<PERSON>", "matricula": "MAT0012", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 2.9179480110093063, "tipo_teste": "correto"}, {"id": 13, "nome": "João<PERSON>", "matricula": "MAT0013", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 1.5037553163840292, "tipo_teste": "correto"}, {"id": 14, "nome": "João<PERSON>", "matricula": "MAT0014", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 3.74229359006039, "tipo_teste": "correto"}, {"id": 15, "nome": "João<PERSON>", "matricula": "MAT0015", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 1.321749396955533, "tipo_teste": "correto"}, {"id": 16, "nome": "João<PERSON>", "matricula": "MAT0016", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 4.339673806797734, "tipo_teste": "problematico"}, {"id": 17, "nome": "João<PERSON>", "matricula": "MAT0017", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 2.5809853218002354, "tipo_teste": "problematico"}, {"id": 18, "nome": "João18", "matricula": "MAT0018", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 3.708442076045346, "tipo_teste": "problematico"}, {"id": 19, "nome": "João<PERSON>", "matricula": "MAT0019", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 1.0470349801589585, "tipo_teste": "problematico"}, {"id": 20, "nome": "João20", "matricula": "MAT0020", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -1.707102792489387, "tipo_teste": "problematico"}, {"id": 21, "nome": "João21", "matricula": "MAT0021", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 1.7448712306169343, "tipo_teste": "problematico"}, {"id": 22, "nome": "João22", "matricula": "MAT0022", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 1.2175318456763478, "tipo_teste": "problematico"}, {"id": 23, "nome": "João23", "matricula": "MAT0023", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 4.790205583116952, "tipo_teste": "problematico"}, {"id": 24, "nome": "João24", "matricula": "MAT0024", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -0.16014129633510166, "tipo_teste": "problematico"}, {"id": 25, "nome": "João25", "matricula": "MAT0025", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -4.815221201093084, "tipo_teste": "problematico"}, {"id": 26, "nome": "João<PERSON>", "matricula": "MAT0026", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -0.2667370052271423, "tipo_teste": "problematico"}, {"id": 27, "nome": "João27", "matricula": "MAT0027", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -3.6023832535811664, "tipo_teste": "problematico"}, {"id": 28, "nome": "João28", "matricula": "MAT0028", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 2.3131754020313746, "tipo_teste": "problematico"}, {"id": 29, "nome": "João29", "matricula": "MAT0029", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": -0.13557956495420598, "tipo_teste": "problematico"}, {"id": 30, "nome": "João30", "matricula": "MAT0030", "empresa_id": 1, "status": "ativo", "jornada": {"entrada_manha": "08:00:00", "saida_almoco": "12:00:00", "entrada_tarde": "13:00:00", "saida": "17:00:00", "horas_obrigatorias": 8.0, "tolerancia_minutos": 10}, "banco_horas_anterior": 4.894811967322907, "tipo_teste": "problematico"}], "resultados_calculos": [{"funcionario_id": 1, "funcionario_nome": "João01", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 168.57000000000002, "horas_obrigatorias_total": 176.0, "banco_horas_final": -2.96, "horas_extras_total": 0.5, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": 2.47, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": 2.97, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 3.14, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": 3.22, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 3.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 3.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": 1.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 1.56, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": 1.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": 1.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": 2.63, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 2.63, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": 0.63, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 0.63, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 0.63, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": 0.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -1.54, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -3.54, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": -3.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -2.96, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 2, "funcionario_nome": "João02", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 168.86999999999998, "horas_obrigatorias_total": 176.0, "banco_horas_final": -9.43, "horas_extras_total": 1.0, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -2.3, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -4.3, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -4.35, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -4.35, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -6.35, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.35, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.35, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -8.35, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -10.35, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -10.35, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -10.35, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": -10.27, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -9.1, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -9.27, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -9.27, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -9.27, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -9.27, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -8.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -10.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -10.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -9.6, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -9.43, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 3, "funcionario_nome": "João03", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 170.16000000000003, "horas_obrigatorias_total": 176.0, "banco_horas_final": -8.32, "horas_extras_total": 0.5, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -4.48, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -4.48, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -3.98, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": -3.9, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.9, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -5.9, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -5.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -6.0, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -5.83, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -4.66, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -4.49, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -6.49, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.49, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -6.32, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.32, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.32, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.32, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -8.32, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -8.49, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -8.32, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -8.32, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -8.32, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 4, "funcionario_nome": "João04", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 169.2, "horas_obrigatorias_total": 176.0, "banco_horas_final": -8.05, "horas_extras_total": 1.0, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -1.42, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.42, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -0.25, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.25, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -0.42, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.42, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": 0.08, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -1.92, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.92, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": -1.84, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -1.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -3.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -3.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -5.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -5.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -7.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -7.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -7.22, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -9.22, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -8.05, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -8.05, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 5, "funcionario_nome": "João05", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 172.16, "horas_obrigatorias_total": 176.0, "banco_horas_final": -5.22, "horas_extras_total": 0.5, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -1.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -1.04, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -1.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -0.59, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.59, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": 0.58, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -1.42, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -1.25, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.25, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": -1.17, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.17, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.17, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -3.17, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -3.22, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.22, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -5.22, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -5.22, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -5.22, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 6, "funcionario_nome": "João06", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 173.78999999999996, "horas_obrigatorias_total": 176.0, "banco_horas_final": -3.17, "horas_extras_total": 1.5, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.96, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -1.13, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -0.63, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.63, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.63, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -0.8, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": 0.37, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 0.54, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": 0.37, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -1.63, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.63, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -1.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -3.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -3.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -2.34, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -4.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -3.17, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.17, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.17, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 7, "funcionario_nome": "João07", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 172.07999999999998, "horas_obrigatorias_total": 176.0, "banco_horas_final": -1.7, "horas_extras_total": 0.5, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": 2.17, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": 0.17, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -1.83, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -1.33, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -0.83, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -0.33, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -0.38, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.38, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": 0.12, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 0.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": 0.12, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 0.12, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -1.88, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -3.88, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.88, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -3.71, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -3.54, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -2.37, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -1.87, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.87, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.87, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -1.7, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 8, "funcionario_nome": "João08", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 172.92999999999998, "horas_obrigatorias_total": 176.0, "banco_horas_final": -0.82, "horas_extras_total": 0.5, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 2.25, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": 2.08, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 2.08, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": 2.03, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 2.03, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 2.03, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": 1.86, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.86, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": 1.69, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.69, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -0.31, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": 0.86, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 0.86, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 1.03, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": 0.98, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 0.98, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": 1.06, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 1.23, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.23, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -0.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -0.82, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 9, "funcionario_nome": "João09", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 171.42, "horas_obrigatorias_total": 176.0, "banco_horas_final": -3.16, "horas_extras_total": 0.5, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 1.59, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": 1.54, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.54, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -0.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -0.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -0.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -0.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -2.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -2.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -2.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -1.17, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -3.17, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -3.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -3.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -3.44, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -2.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -3.11, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -3.16, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 10, "funcionario_nome": "João10", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 173.87, "horas_obrigatorias_total": 176.0, "banco_horas_final": -0.26, "horas_extras_total": 1.0, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": 2.37, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": 2.2, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": 2.28, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": 0.28, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": 0.23, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 0.23, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -1.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -1.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -1.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -0.6, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": 0.57, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 0.57, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 0.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": 1.24, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.24, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": 1.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -0.26, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 11, "funcionario_nome": "João11", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 176.69, "horas_obrigatorias_total": 176.0, "banco_horas_final": -1.66, "horas_extras_total": 1.0, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -2.52, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -2.02, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": -1.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -0.77, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -0.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -0.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -0.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -0.99, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -2.99, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": -2.91, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": -2.83, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -2.83, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -2.83, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -1.66, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -1.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -0.66, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -2.66, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -2.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -1.66, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 12, "funcionario_nome": "João<PERSON>", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 175.5, "horas_obrigatorias_total": 176.0, "banco_horas_final": 2.42, "horas_extras_total": 1.0, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 2.92, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 2.92, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 3.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 3.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 3.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 3.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 3.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": 1.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -0.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": 0.43, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": 0.38, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": 0.33, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": 1.5, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.5, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.5, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.5, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": 1.58, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": 2.08, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 2.25, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 2.25, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 2.42, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 13, "funcionario_nome": "João<PERSON>", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 166.26999999999998, "horas_obrigatorias_total": 176.0, "banco_horas_final": -8.23, "horas_extras_total": 1.0, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.5, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": 1.58, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.58, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.58, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": 1.53, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": 1.48, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -0.52, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -0.69, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.69, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -2.69, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -4.69, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -4.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -6.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -6.91, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -6.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -8.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -7.57, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -6.4, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.4, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -6.23, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.23, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -8.23, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 14, "funcionario_nome": "João<PERSON>", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 165.12, "horas_obrigatorias_total": 176.0, "banco_horas_final": -7.14, "horas_extras_total": 0.5, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 3.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": 1.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -0.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -2.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -2.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -4.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -4.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -4.43, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -4.43, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 9.17, "horas_obrigatorias": 8.0, "diferenca": 1.17, "banco_saldo": -3.26, "horas_extras": 0.5, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -3.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -5.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -5.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -4.92, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -4.92, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -5.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -7.09, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -7.14, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -7.31, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -7.14, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 15, "funcionario_nome": "João<PERSON>", "tipo_teste": "correto", "total_dias": 22, "horas_trabalhadas_total": 167.12, "horas_obrigatorias_total": 176.0, "banco_horas_final": -7.56, "horas_extras_total": 0.0, "dias_com_erro": 0, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": 1.15, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": 1.32, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": 1.15, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.15, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -0.85, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 7.95, "horas_obrigatorias": 8.0, "diferenca": -0.05, "banco_saldo": -0.9, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 7.83, "horas_obrigatorias": 8.0, "diferenca": -0.17, "banco_saldo": -1.07, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -1.07, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -0.57, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -2.57, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -2.4, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -2.23, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -4.23, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -6.23, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.23, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -6.06, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.06, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 6.0, "horas_obrigatorias": 8.0, "diferenca": -2.0, "banco_saldo": -8.06, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": -7.98, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -7.81, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.17, "horas_obrigatorias": 8.0, "diferenca": 0.17, "banco_saldo": -7.64, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 8.08, "horas_obrigatorias": 8.0, "diferenca": 0.08, "banco_saldo": -7.56, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 16, "funcionario_nome": "João<PERSON>", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 166.25, "horas_obrigatorias_total": 176.0, "banco_horas_final": -5.41, "horas_extras_total": 5.5, "dias_com_erro": 3, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": 9.34, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": 8.84, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": 3.84, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": 2.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": 0.84, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -0.66, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 3.34, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 3.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 7.34, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 6.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": 2.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": 0.84, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": 1.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": 1.84, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": 1.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 0.34, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -1.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -5.66, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "11/07/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -6.91, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -5.41, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "15/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -3.91, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "16/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -5.41, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 17, "funcionario_nome": "João<PERSON>", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 155.75, "horas_obrigatorias_total": 176.0, "banco_horas_final": -17.67, "horas_extras_total": 1.5, "dias_com_erro": 4, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 6.58, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": 5.08, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": 5.58, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": 4.08, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": 5.58, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "24/06/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": 7.08, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "25/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": 7.58, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 6.58, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 5.58, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": 0.58, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -0.92, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": 0.58, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "03/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -0.92, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -4.92, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -6.42, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -7.42, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -7.42, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -8.67, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -10.17, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -11.67, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -16.17, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "16/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -17.67, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 18, "funcionario_nome": "João18", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 158.5, "horas_obrigatorias_total": 176.0, "banco_horas_final": -13.79, "horas_extras_total": 9.0, "dias_com_erro": 2, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 2.71, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": 1.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -3.04, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "20/06/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -8.04, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -8.54, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -8.54, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -3.54, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -7.54, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -11.54, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -12.54, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -7.54, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -3.54, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": 1.46, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -0.04, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -1.04, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -2.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -0.79, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "10/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -2.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -3.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -8.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -12.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -13.79, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 19, "funcionario_nome": "João<PERSON>", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 147.5, "horas_obrigatorias_total": 176.0, "banco_horas_final": -27.45, "horas_extras_total": 4.0, "dias_com_erro": 2, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": 2.55, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "18/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 1.55, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -3.45, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -4.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -5.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -1.95, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -5.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -6.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -7.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -12.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -17.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -19.45, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -14.45, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -15.45, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -16.45, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -15.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -16.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -17.45, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -18.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -18.95, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -23.45, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "16/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -27.45, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 20, "funcionario_nome": "João20", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 172.5, "horas_obrigatorias_total": 176.0, "banco_horas_final": -5.21, "horas_extras_total": 6.5, "dias_com_erro": 3, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -3.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 0.79, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -0.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -5.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -5.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -0.21, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -1.71, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -5.71, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -5.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -6.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -4.96, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "04/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -6.46, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -7.71, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -7.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -5.71, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "10/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -5.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -9.71, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "14/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -9.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -4.21, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -5.21, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 21, "funcionario_nome": "João21", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 156.0, "horas_obrigatorias_total": 176.0, "banco_horas_final": -18.26, "horas_extras_total": 7.5, "dias_com_erro": 1, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.74, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -3.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": 1.74, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": 0.49, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -4.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -5.01, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -4.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -8.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -12.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -17.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -22.51, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -17.51, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -18.76, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -13.76, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -14.76, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -18.76, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -17.26, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "10/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -17.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -17.76, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -17.26, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -17.76, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -18.26, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 22, "funcionario_nome": "João22", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 162.75, "horas_obrigatorias_total": 176.0, "banco_horas_final": -12.03, "horas_extras_total": 8.0, "dias_com_erro": 2, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 0.22, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -0.28, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": 4.72, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 3.72, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": 2.47, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -2.03, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "25/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -3.53, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -5.03, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -9.03, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -10.03, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -14.03, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -14.53, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -14.03, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -9.03, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -9.53, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -14.03, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "09/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -15.53, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -11.53, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -11.03, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -7.03, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -11.03, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -12.03, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 23, "funcionario_nome": "João23", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 180.5, "horas_obrigatorias_total": 176.0, "banco_horas_final": 9.29, "horas_extras_total": 12.5, "dias_com_erro": 3, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 3.79, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 7.79, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": 3.79, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 7.79, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": 12.79, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 11.79, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": 13.29, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "26/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 12.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": 8.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": 7.79, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": 12.79, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": 8.29, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "03/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 7.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 7.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": 2.29, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -2.71, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -4.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -4.21, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -2.71, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "14/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 1.29, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 5.29, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 9.29, "horas_extras": 1.5, "valido": true, "erros": []}]}, {"funcionario_id": 24, "funcionario_nome": "João24", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 163.0, "horas_obrigatorias_total": 176.0, "banco_horas_final": -13.16, "horas_extras_total": 4.5, "dias_com_erro": 2, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -1.66, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -2.66, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -3.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -3.66, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -5.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -5.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -1.16, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 2.84, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -1.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -6.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -7.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -11.66, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "04/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -10.16, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "07/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -11.66, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -7.66, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -8.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -9.66, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -11.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -12.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -12.16, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -13.16, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 25, "funcionario_nome": "João25", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 159.25, "horas_obrigatorias_total": 176.0, "banco_horas_final": -21.57, "horas_extras_total": 1.5, "dias_com_erro": 4, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -5.82, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -6.82, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -6.82, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -8.07, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -7.57, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -6.07, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "25/06/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -11.07, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -12.07, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -16.07, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -17.07, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -18.57, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -22.57, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -23.57, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -28.07, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "07/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -28.57, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -28.07, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -29.07, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -27.57, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "11/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -27.57, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -23.57, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -22.07, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "16/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -21.57, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 26, "funcionario_nome": "João<PERSON>", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 145.25, "horas_obrigatorias_total": 176.0, "banco_horas_final": -31.02, "horas_extras_total": 3.0, "dias_com_erro": 3, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -1.27, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -2.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -2.27, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -3.52, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -4.52, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -8.52, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -9.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -10.77, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -12.27, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -16.77, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "01/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -17.27, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -18.52, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -22.52, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -18.52, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -18.52, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -22.52, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -21.02, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "10/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -25.02, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -29.02, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -30.52, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -35.02, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "16/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -31.02, "horas_extras": 1.5, "valido": true, "erros": []}]}, {"funcionario_id": 27, "funcionario_nome": "João27", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 176.0, "horas_obrigatorias_total": 176.0, "banco_horas_final": -3.6, "horas_extras_total": 4.0, "dias_com_erro": 9, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -8.1, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "18/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -9.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -9.1, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -7.6, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "23/06/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -6.1, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "24/06/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -1.1, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -2.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -2.1, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 1.9, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": 0.4, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -4.1, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "02/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -2.6, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "03/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -2.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -7.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -6.1, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "08/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -6.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -5.1, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "10/07/2025", "horas_trabalhadas": 8.5, "horas_obrigatorias": 8.0, "diferenca": 0.5, "banco_saldo": -4.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -5.6, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -4.1, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "15/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -2.6, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "16/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -3.6, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 28, "funcionario_nome": "João28", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 150.5, "horas_obrigatorias_total": 176.0, "banco_horas_final": -23.19, "horas_extras_total": 4.0, "dias_com_erro": 2, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": 1.06, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -2.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -3.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -3.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -4.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -5.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -6.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -8.44, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -3.44, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -4.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -9.44, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "02/07/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -14.44, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -15.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -20.94, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -22.19, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -23.69, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -19.69, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -20.69, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -19.19, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "14/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -20.19, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -21.69, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -23.19, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 29, "funcionario_nome": "João29", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 167.5, "horas_obrigatorias_total": 176.0, "banco_horas_final": -8.64, "horas_extras_total": 6.5, "dias_com_erro": 7, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -1.14, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": 0.36, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "19/06/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": 5.36, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "20/06/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": 6.86, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "23/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 5.86, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": 7.36, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "25/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 6.36, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": 4.86, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": 0.36, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "30/06/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": -4.14, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "01/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -0.14, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": -0.14, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -0.64, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -4.64, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -8.64, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -9.64, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -11.14, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -9.64, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "11/07/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -4.64, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 9.5, "horas_obrigatorias": 8.0, "diferenca": 1.5, "banco_saldo": -3.14, "horas_extras": 0.0, "valido": false, "erros": ["Entrada da tarde deve ser depois da saída para almoço", "Intervalo de almoço deve ser de pelo menos 30 minutos"]}, {"data": "15/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -7.14, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -8.64, "horas_extras": 0.0, "valido": true, "erros": []}]}, {"funcionario_id": 30, "funcionario_nome": "João30", "tipo_teste": "problematico", "total_dias": 22, "horas_trabalhadas_total": 161.5, "horas_obrigatorias_total": 176.0, "banco_horas_final": -9.61, "horas_extras_total": 8.5, "dias_com_erro": 1, "detalhes_dias": [{"data": "17/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 8.89, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "18/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": 7.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "19/06/2025", "horas_trabalhadas": 3.5, "horas_obrigatorias": 8.0, "diferenca": -4.5, "banco_saldo": 2.89, "horas_extras": 0.0, "valido": false, "erros": ["<PERSON><PERSON><PERSON> deve ser depois da entrada da tarde"]}, {"data": "20/06/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": 1.89, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "23/06/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": 5.89, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "24/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": 4.39, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "25/06/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": 2.89, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "26/06/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": 1.64, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "27/06/2025", "horas_trabalhadas": 8.0, "horas_obrigatorias": 8.0, "diferenca": 0.0, "banco_saldo": 1.64, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "30/06/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -3.36, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "01/07/2025", "horas_trabalhadas": 7.0, "horas_obrigatorias": 8.0, "diferenca": -1.0, "banco_saldo": -4.36, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "02/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -4.86, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "03/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -0.86, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "04/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -1.36, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "07/07/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -6.36, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "08/07/2025", "horas_trabalhadas": 6.75, "horas_obrigatorias": 8.0, "diferenca": -1.25, "banco_saldo": -7.61, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "09/07/2025", "horas_trabalhadas": 13.0, "horas_obrigatorias": 8.0, "diferenca": 5.0, "banco_saldo": -2.61, "horas_extras": 2.5, "valido": true, "erros": []}, {"data": "10/07/2025", "horas_trabalhadas": 6.5, "horas_obrigatorias": 8.0, "diferenca": -1.5, "banco_saldo": -4.11, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "11/07/2025", "horas_trabalhadas": 12.0, "horas_obrigatorias": 8.0, "diferenca": 4.0, "banco_saldo": -0.11, "horas_extras": 1.5, "valido": true, "erros": []}, {"data": "14/07/2025", "horas_trabalhadas": 3.0, "horas_obrigatorias": 8.0, "diferenca": -5.0, "banco_saldo": -5.11, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "15/07/2025", "horas_trabalhadas": 7.5, "horas_obrigatorias": 8.0, "diferenca": -0.5, "banco_saldo": -5.61, "horas_extras": 0.0, "valido": true, "erros": []}, {"data": "16/07/2025", "horas_trabalhadas": 4.0, "horas_obrigatorias": 8.0, "diferenca": -4.0, "banco_saldo": -9.61, "horas_extras": 0.0, "valido": true, "erros": []}]}]}