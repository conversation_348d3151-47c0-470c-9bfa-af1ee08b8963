#!/usr/bin/env python3
"""
Verificar Sincronização

Verifica se o arquivo corrigido foi sincronizado corretamente com o servidor.
"""

import requests
import time

def verificar_se_correcao_ativa():
    """Verifica se nossa correção está ativa no servidor"""
    print("🔄 VERIFICAÇÃO: Nossa correção está ativa no servidor?")
    print("=" * 70)
    
    servidor = "http://10.19.208.31"
    session = requests.Session()
    
    try:
        # Login
        print("🔐 Fazendo login...")
        login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
        response = session.post(f"{servidor}/login", data=login_data, timeout=10)
        
        if response.status_code != 200:
            print("❌ Login falhou")
            return False
        
        print("✅ Login OK")
        
        # Testar função detalhes
        print("\n🎯 Testando /funcionarios/1...")
        response = session.get(f"{servidor}/funcionarios/1", allow_redirects=False, timeout=10)
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("🎉 SUCESSO! Página de detalhes carrega sem redirecionamento!")
            
            # Verificar se é realmente a página de detalhes
            content = response.text
            
            if "RICHARDSON" in content and ("detalhes" in content.lower() or "Detalhes" in content):
                print("✅ Conteúdo da página de detalhes está correto!")
                return True
            else:
                print("⚠️ Status 200 mas conteúdo pode estar incorreto")
                return False
        
        elif response.status_code in [301, 302, 303, 307, 308]:
            location = response.headers.get('Location', 'N/A')
            print(f"❌ AINDA redirecionando para: {location}")
            
            if "/funcionarios/" in location and "/funcionarios/1" not in location:
                print("🔄 Servidor ainda usa versão antiga da função")
                return False
            else:
                print("🤔 Redirecionamento inesperado")
                return False
        else:
            print(f"❌ Status inesperado: {response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

def aguardar_e_testar():
    """Testa periodicamente até funcionar ou timeout"""
    print("\n🕐 MONITORAMENTO: Aguardando sincronização...")
    print("=" * 70)
    
    max_tentativas = 6  # 3 minutos no total
    intervalo = 30  # 30 segundos entre tentativas
    
    for tentativa in range(1, max_tentativas + 1):
        print(f"\n🔍 Tentativa {tentativa}/{max_tentativas}...")
        
        if verificar_se_correcao_ativa():
            print(f"\n🎉 SUCESSO na tentativa {tentativa}!")
            print("✅ Sincronização concluída e funcionando!")
            return True
        
        if tentativa < max_tentativas:
            print(f"⏳ Aguardando {intervalo} segundos para próxima tentativa...")
            time.sleep(intervalo)
        else:
            print(f"\n⏰ Timeout após {tentativa} tentativas")
    
    return False

def main():
    """Executa verificação de sincronização"""
    print("🚨 VERIFICAÇÃO DE SINCRONIZAÇÃO")
    print("🎯 OBJETIVO: Confirmar que correção está ativa no servidor")
    print("\n💡 INSTRUÇÕES:")
    print("   1. Copie o arquivo 'app_funcionarios.py' para o servidor")
    print("   2. Reinicie o serviço Flask no servidor")
    print("   3. Execute este script para verificar")
    print("\n" + "=" * 70)
    
    # Teste imediato
    print("🔍 TESTE IMEDIATO:")
    if verificar_se_correcao_ativa():
        print("\n🎉 PROBLEMA RESOLVIDO!")
        print("✅ A correção já está ativa no servidor!")
        print("✅ O botão 'Ver' agora funciona corretamente!")
        return
    
    # Perguntar se quer monitorar
    print("\n❌ Correção ainda não está ativa.")
    print("🤔 Quer monitorar automaticamente? (aguardar sincronização)")
    
    resposta = input("Digite 's' para monitorar ou qualquer tecla para sair: ").lower().strip()
    
    if resposta == 's':
        if aguardar_e_testar():
            print("\n🎉 PROBLEMA RESOLVIDO COMPLETAMENTE!")
            print("✅ Sincronização bem-sucedida!")
            print("✅ Página de detalhes funcionando!")
        else:
            print("\n❌ Sincronização não detectada no tempo limite")
            print("💡 Verifique se:")
            print("   - Arquivo foi copiado corretamente")
            print("   - Serviço Flask foi reiniciado")
            print("   - Não há erros no servidor")
    else:
        print("\n✋ OK, execute novamente após sincronizar")

if __name__ == "__main__":
    main() 