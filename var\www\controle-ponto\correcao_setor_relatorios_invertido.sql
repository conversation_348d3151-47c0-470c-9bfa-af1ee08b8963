-- =========================================
-- CORREÇÃO DA VIEW VW_RELATORIO_PONTOS
-- Data: 18/06/2025
-- Descrição: Correção da priorização do campo setor_obra para exibir o valor correto do cadastro do funcionário
-- =========================================

-- Atualização da view vw_relatorio_pontos para exibir o campo setor corretamente
DROP VIEW IF EXISTS vw_relatorio_pontos;

CREATE VIEW vw_relatorio_pontos AS
SELECT 
    rp.id,
    rp.funcionario_id,
    f.nome_completo,
    f.matricula_empresa,
    f.cpf,
    rp.data_hora,
    DATE(rp.data_hora) as data_registro,
    TIME(rp.data_hora) as hora_registro,
    rp.tipo_registro,
    CASE rp.tipo_registro
        WHEN 'entrada_manha' THEN 'Entrada Manhã'
        WHEN 'saida_almoco' THEN 'Saída Almoço'
        WHEN 'entrada_tarde' THEN 'Entrada Tarde'
        WHEN 'saida' THEN 'Saída'
    END AS tipo_descricao,
    rp.metodo_registro,
    CASE 
        WHEN rp.metodo_registro = 'biometrico' THEN 'Biométrico'
        ELSE 'Manual'
    END AS metodo_descricao,
    -- Correção da ordem: primeiro verificar setor_obra (campo Setor/Obra na interface), depois setor
    -- Isso garante que o valor correto cadastrado no formulário seja exibido
    COALESCE(f.setor_obra, f.setor, 'Não informado') as setor,
    f.cargo,
    COALESCE(f.empresa, 'Não informado') as empresa,
    rp.qualidade_biometria,
    rp.observacoes,
    rp.ip_origem,
    rp.criado_em,
    u.usuario as criado_por_usuario,
    CASE 
        WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atraso'
        WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atraso'
        ELSE 'Pontual'
    END AS status_pontualidade
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
LEFT JOIN usuarios u ON rp.criado_por = u.id
WHERE f.status_cadastro = 'Ativo'
ORDER BY rp.data_hora DESC;

-- Verificação da alteração
SELECT 
    'View vw_relatorio_pontos atualizada com sucesso!' as status,
    (SELECT COUNT(*) FROM information_schema.views 
     WHERE table_schema = 'controle_ponto' AND table_name = 'vw_relatorio_pontos') as view_existe; 