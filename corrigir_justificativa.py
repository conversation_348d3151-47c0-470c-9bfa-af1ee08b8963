#!/usr/bin/env python3
"""
Script para corrigir justificativas aprovadas sem histórico
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from app_ponto_admin import corrigir_justificativa_aprovada_sem_historico
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def main():
    justificativa_id = 5
    
    print(f"🔧 Corrigindo justificativa {justificativa_id}...")
    
    try:
        resultado = corrigir_justificativa_aprovada_sem_historico(justificativa_id)
        
        if resultado:
            print(f"✅ Justificativa {justificativa_id} corrigida com sucesso!")
        else:
            print(f"❌ Falha ao corrigir justificativa {justificativa_id}")
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
