#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para executar limpeza e correção do banco de dados
ATENÇÃO: Este script remove TODOS os dados dos funcionários!
"""

import pymysql
import sys
import os

def conectar_mysql():
    """Conecta com o servidor MySQL usando as credenciais do usuário criado"""
    try:
        connection = pymysql.connect(
            host='************',
            user='controle_user',
            password='controle123',
            database='controle_ponto',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        print("✓ Conectado ao MySQL servidor ************ como controle_user")
        return connection
        
    except pymysql.Error as e:
        print(f"✗ Erro ao conectar com MySQL: {e}")
        print("💡 Certifique-se de que executou o script de permissões primeiro!")
        return None

def executar_limpeza_banco(connection):
    """Executa a limpeza e correção da estrutura do banco"""
    
    print("⚠️  ATENÇÃO: Este processo vai REMOVER TODOS os dados dos funcionários!")
    confirmacao = input("Digite 'CONFIRMO' para prosseguir: ")
    
    if confirmacao != 'CONFIRMO':
        print("❌ Operação cancelada pelo usuário.")
        return False
    
    comandos = [
        # Desabilitar verificação de chaves estrangeiras
        "SET FOREIGN_KEY_CHECKS = 0;",
        
        # Limpar tabelas relacionadas
        "DROP TABLE IF EXISTS epis;",
        "DROP TABLE IF EXISTS registros_ponto;",
        
        # Remover dados dos funcionários
        "DELETE FROM funcionarios;",
        
        # Tentar remover colunas problemáticas
        "ALTER TABLE funcionarios DROP COLUMN IF EXISTS jornada_entrada;",
        "ALTER TABLE funcionarios DROP COLUMN IF EXISTS jornada_saida;",
        "ALTER TABLE funcionarios DROP COLUMN IF EXISTS assinatura;",
        
        # Adicionar colunas corretas
        """ALTER TABLE funcionarios 
           ADD COLUMN IF NOT EXISTS jornada_seg_qui_entrada TIME DEFAULT NULL,
           ADD COLUMN IF NOT EXISTS jornada_seg_qui_saida TIME DEFAULT NULL,
           ADD COLUMN IF NOT EXISTS jornada_sex_entrada TIME DEFAULT NULL,
           ADD COLUMN IF NOT EXISTS jornada_sex_saida TIME DEFAULT NULL,
           ADD COLUMN IF NOT EXISTS jornada_intervalo_entrada TIME DEFAULT NULL,
           ADD COLUMN IF NOT EXISTS jornada_intervalo_saida TIME DEFAULT NULL;""",
        
        # Recriar tabela registros_ponto
        """CREATE TABLE IF NOT EXISTS registros_ponto (
             id int NOT NULL AUTO_INCREMENT,
             funcionario_id int DEFAULT NULL,
             data_hora datetime DEFAULT CURRENT_TIMESTAMP,
             sincronizado tinyint DEFAULT 0,
             PRIMARY KEY (id),
             FOREIGN KEY (funcionario_id) REFERENCES funcionarios (id) ON DELETE CASCADE
           ) ENGINE = INNODB,
           CHARACTER SET utf8mb4,
           COLLATE utf8mb4_0900_ai_ci;""",
        
        # Recriar tabela EPIs
        """CREATE TABLE IF NOT EXISTS epis (
             id int NOT NULL AUTO_INCREMENT,
             funcionario_id int NOT NULL,
             epi_nome varchar(255) NOT NULL,
             epi_ca varchar(50) DEFAULT NULL,
             epi_data_entrega date DEFAULT NULL,
             epi_data_validade date DEFAULT NULL,
             epi_observacoes text DEFAULT NULL,
             PRIMARY KEY (id),
             FOREIGN KEY (funcionario_id) REFERENCES funcionarios (id) ON DELETE CASCADE
           ) ENGINE = INNODB,
           CHARACTER SET utf8mb4,
           COLLATE utf8mb4_0900_ai_ci;""",
        
        # Reabilitar chaves estrangeiras
        "SET FOREIGN_KEY_CHECKS = 1;",
        
        # Resetar auto_increment
        "ALTER TABLE funcionarios AUTO_INCREMENT = 1;",
        "ALTER TABLE registros_ponto AUTO_INCREMENT = 1;",
        "ALTER TABLE epis AUTO_INCREMENT = 1;"
    ]
    
    try:
        with connection.cursor() as cursor:
            for i, comando in enumerate(comandos, 1):
                print(f"[{i}/{len(comandos)}] Executando comando...")
                cursor.execute(comando)
                print("✓ Comando executado com sucesso")
                
        connection.commit()
        print("\n✓ Limpeza e correção do banco concluída com sucesso!")
        
        # Verificar estrutura final
        print("\n📋 Verificando estrutura das tabelas:")
        
        tabelas = ['funcionarios', 'epis', 'registros_ponto']
        for tabela in tabelas:
            with connection.cursor() as cursor:
                cursor.execute(f"DESCRIBE {tabela}")
                colunas = cursor.fetchall()
                print(f"\n🔧 Tabela '{tabela}':")
                for coluna in colunas:
                    print(f"   - {coluna['Field']} ({coluna['Type']})")
                
                # Contar registros
                cursor.execute(f"SELECT COUNT(*) as total FROM {tabela}")
                resultado = cursor.fetchone()
                print(f"   📊 Total de registros: {resultado['total']}")
                
        return True
        
    except pymysql.Error as e:
        print(f"✗ Erro ao executar limpeza: {e}")
        return False

def main():
    """Função principal"""
    print("🧹 Limpeza e correção do banco de dados - Controle de Ponto")
    print("=" * 60)
    
    # Conectar ao MySQL
    connection = conectar_mysql()
    if not connection:
        print("❌ Falha na conexão. Execute primeiro o script de permissões.")
        return False
    
    try:
        # Executar limpeza
        sucesso = executar_limpeza_banco(connection)
        
        if sucesso:
            print("\n🎉 Limpeza e correção concluída com sucesso!")
            print("\n✅ O sistema está pronto para uso!")
            print("\n🔄 Próximos passos:")
            print("   1. Testar a aplicação Python")
            print("   2. Cadastrar novos funcionários")
            print("   3. Verificar funcionalidades de EPI")
            return True
        else:
            print("\n❌ Falha na limpeza e correção.")
            return False
            
    finally:
        connection.close()
        print("\n🔌 Conexão MySQL fechada.")

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1) 