#!/usr/bin/env python3
"""
Script para debuggar erro no dashboard QA
Testa imports e execução das funções críticas
"""

import sys
import os
import traceback
from datetime import datetime

# Adicionar path do projeto
sys.path.append('/var/www/controle-ponto')

def test_qa_imports():
    """Testa importação de módulos do QA"""
    print("🔍 Testando imports do Quality Control...")
    
    try:
        from app_quality_control import dashboard, run_system_check
        print("✅ app_quality_control importado com sucesso")
        return True
    except Exception as e:
        print(f"❌ Erro ao importar app_quality_control: {e}")
        traceback.print_exc()
        return False

def test_run_system_check():
    """Testa execução da função run_system_check"""
    print("\n🔍 Testando função run_system_check...")
    
    try:
        from app_quality_control import run_system_check
        result = run_system_check()
        print("✅ run_system_check executado com sucesso")
        print(f"Status geral: {result.get('overall_status')}")
        print(f"Issues encontradas: {result.get('issues_found')}")
        print(f"Backup criado: {result.get('backup_created')}")
        
        if result.get('detailed_issues'):
            print("\n⚠️ Issues detalhadas:")
            for issue in result.get('detailed_issues', []):
                print(f"  - {issue}")
        
        return True
    except Exception as e:
        print(f"❌ Erro em run_system_check: {e}")
        traceback.print_exc()
        return False

def test_template_rendering():
    """Testa se o template existe"""
    print("\n🔍 Verificando template...")
    
    template_path = "/var/www/controle-ponto/templates/quality_control/dashboard.html"
    if os.path.exists(template_path):
        print(f"✅ Template encontrado: {template_path}")
        with open(template_path, 'r') as f:
            content = f.read()
            print(f"📄 Tamanho do template: {len(content)} caracteres")
        return True
    else:
        print(f"❌ Template não encontrado: {template_path}")
        return False

def test_dependencies():
    """Testa dependências críticas"""
    print("\n🔍 Testando dependências...")
    
    dependencies = [
        ('flask', 'Flask framework'),
        ('pymysql', 'MySQL connector'), 
        ('datetime', 'Date/time utilities'),
        ('os', 'OS utilities'),
        ('json', 'JSON handling')
    ]
    
    success_count = 0
    for dep, desc in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} - {desc}")
            success_count += 1
        except Exception as e:
            print(f"❌ {dep} - {desc}: {e}")
    
    print(f"\n📊 Dependências: {success_count}/{len(dependencies)} OK")
    return success_count == len(dependencies)

def main():
    """Execução principal do diagnóstico"""
    print("=" * 60)
    print("🔧 DIAGNÓSTICO DO DASHBOARD QA")
    print("=" * 60)
    print(f"⏰ Timestamp: {datetime.now()}")
    print(f"🐍 Python: {sys.version}")
    print(f"📂 Working dir: {os.getcwd()}")
    
    # Testes sequenciais
    tests = [
        ("Dependências", test_dependencies),
        ("Imports QA", test_qa_imports),
        ("Template", test_template_rendering),
        ("System Check", test_run_system_check)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Erro inesperado em {test_name}: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # Resumo final
    print("\n" + "="*60)
    print("📋 RESUMO DO DIAGNÓSTICO")
    print("="*60)
    
    success_count = 0
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {test_name}: {'PASSOU' if result else 'FALHOU'}")
        if result:
            success_count += 1
    
    print(f"\n📊 Total: {success_count}/{len(results)} testes passaram")
    
    if success_count == len(results):
        print("🎉 Todos os testes passaram! O problema pode estar em outro lugar.")
    else:
        print("⚠️ Falhas detectadas. Verificar logs acima para detalhes.")

if __name__ == "__main__":
    main() 