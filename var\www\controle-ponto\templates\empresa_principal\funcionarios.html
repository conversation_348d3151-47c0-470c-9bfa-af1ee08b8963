{% extends "base.html" %}

{% block title %}Gestão de Funcionários{% if empresa_principal %} - {{ empresa_principal.razao_social }}{% endif %}{% endblock %}

{% block extra_css %}
<style>
    /* ===== VARIÁVEIS CSS BASEADAS NO PADRÃO OFICIAL ===== */
    :root {
        --primary-color: #4fbdba;
        --primary-hover: #26a69a;
        --background-color: #f9fafb;
        --card-background: #ffffff;
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --text-muted: #9ca3af;
        --border-color: #e5e7eb;
        --border-light: #f3f4f6;
        --success-color: #10b981;
        --success-bg: #dcfce7;
        --success-text: #166534;
        --warning-color: #f59e0b;
        --warning-bg: #fef3c7;
        --warning-text: #92400e;
        --danger-color: #ef4444;
        --danger-bg: #fee2e2;
        --danger-text: #dc2626;
        --info-color: #3b82f6;
        --info-bg: #dbeafe;
        --info-text: #1e40af;
    }

    /* ===== LAYOUT PRINCIPAL ===== */
    .main-container {
        background-color: var(--background-color);
        min-height: 100vh;
        padding: 2rem;
    }

    /* ===== HEADER MODERNO ===== */
    .modern-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .modern-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .header-content {
        position: relative;
        z-index: 2;
    }

    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .stat-card:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        display: block;
    }

    .stat-label {
        font-size: 0.875rem;
        opacity: 0.9;
        font-weight: 500;
    }

    /* ===== SEÇÃO DE RELATÓRIOS MODERNIZADA ===== */
    .reports-section {
        margin-bottom: 2rem;
    }

    .reports-title {
        color: var(--text-primary);
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .reports-title i {
        color: var(--primary-color);
        font-size: 1.375rem;
    }

    .reports-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    /* ===== BOTÕES DE RELATÓRIO PROFISSIONAIS ===== */
    .report-btn {
        border-radius: 12px;
        padding: 1rem 1.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        min-height: 60px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .report-btn:hover {
        text-decoration: none;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .report-btn i {
        font-size: 1.25rem;
        transition: transform 0.3s ease;
    }

    .report-btn:hover i {
        transform: scale(1.1);
    }

    /* ===== CORES ESPECÍFICAS PARA CADA TIPO DE RELATÓRIO - SEMPRE VISÍVEIS ===== */
    .report-btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        border: 1px solid #3b82f6;
        color: white;
        border-left: 4px solid #1d4ed8;
    }

    .report-btn-primary:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        border-color: #1d4ed8;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    }

    .report-btn-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border: 1px solid #10b981;
        color: white;
        border-left: 4px solid #047857;
    }

    .report-btn-success:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        border-color: #047857;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
    }

    .report-btn-info {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        border: 1px solid #8b5cf6;
        color: white;
        border-left: 4px solid #6d28d9;
    }

    .report-btn-info:hover {
        background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
        border-color: #6d28d9;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
    }

    .report-btn-warning {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        border: 1px solid #f59e0b;
        color: white;
        border-left: 4px solid #b45309;
    }

    .report-btn-warning:hover {
        background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        border-color: #b45309;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
    }

    /* ===== EFEITO SHIMMER NOS BOTÕES ===== */
    .report-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
        );
        transition: left 0.5s ease;
    }

    .report-btn:hover::before {
        left: 100%;
    }

    /* ===== FILTROS MODERNOS ===== */
    .filters-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
        margin: 0;
    }

    .modern-input,
    .modern-select {
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-size: 0.875rem;
        background: var(--card-background);
        color: var(--text-primary);
        transition: all 0.3s ease;
        width: 100%;
    }

    .modern-input:focus,
    .modern-select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    }

    .modern-input::placeholder {
        color: var(--text-muted);
    }

    .modern-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        font-size: 0.875rem;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        width: 100%;
        justify-content: center;
    }

    .modern-btn-secondary {
        background: var(--card-background);
        color: var(--text-secondary);
        border: 1px solid var(--border-color);
    }

    .modern-btn-secondary:hover {
        background: var(--border-light);
        color: var(--text-primary);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* ===== CARDS DE FUNCIONÁRIOS MODERNOS ===== */
    .employees-grid {
        display: grid;
        gap: 1.5rem;
    }

    .employee-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .employee-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-color);
        transform: scaleY(0);
        transition: transform 0.3s ease;
    }

    .employee-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: var(--primary-color);
    }

    .employee-card:hover::before {
        transform: scaleY(1);
    }

    .employee-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .employee-info {
        flex: 1;
    }

    .employee-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 0.25rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .employee-name i {
        color: var(--primary-color);
        font-size: 1rem;
    }

    .employee-matricula {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin: 0 0 0.5rem 0;
    }

    .employee-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin: 1rem 0;
    }

    .detail-group {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .detail-label {
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--text-muted);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .detail-value {
        font-size: 0.875rem;
        color: var(--text-primary);
        font-weight: 500;
    }

    .jornada-section {
        background: var(--border-light);
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
    }

    .jornada-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .jornada-title i {
        color: var(--info-color);
    }

    .jornada-details {
        display: grid;
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .jornada-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.875rem;
    }

    .jornada-label {
        color: var(--text-secondary);
        font-weight: 500;
    }

    .jornada-time {
        color: var(--text-primary);
        font-weight: 600;
        font-family: 'Courier New', monospace;
    }

    .jornada-undefined {
        text-align: center;
        color: var(--text-muted);
        font-style: italic;
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
    }

    .status-ativo {
        background: var(--success-bg);
        color: var(--success-text);
    }

    .status-inativo {
        background: var(--danger-bg);
        color: var(--danger-text);
    }

    .employee-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
        justify-content: center;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 500;
        text-decoration: none;
        border: 1px solid;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .action-btn-success {
        background: var(--success-color);
        color: white;
        border-color: var(--success-color);
    }

    .action-btn-success:hover {
        background: #059669;
        transform: translateY(-1px);
    }

    .action-btn-danger {
        background: var(--danger-color);
        color: white;
        border-color: var(--danger-color);
    }

    .action-btn-danger:hover {
        background: #dc2626;
        transform: translateY(-1px);
    }

    .action-btn-info {
        background: var(--info-color);
        color: white;
        border-color: var(--info-color);
    }

    .action-btn-info:hover {
        background: #2563eb;
        transform: translateY(-1px);
    }

    /* ===== ESTADO VAZIO ===== */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        border-style: dashed;
    }

    .empty-icon {
        font-size: 4rem;
        color: var(--text-muted);
        margin-bottom: 1rem;
    }

    .empty-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
    }

    .empty-description {
        color: var(--text-muted);
        margin-bottom: 2rem;
    }

    /* ===== NAVEGAÇÃO ===== */
    .navigation-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
    }

    .nav-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        font-size: 0.875rem;
        text-decoration: none;
        border: 1px solid;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .nav-btn-secondary {
        background: var(--card-background);
        color: var(--text-secondary);
        border-color: var(--border-color);
    }

    .nav-btn-secondary:hover {
        background: var(--border-light);
        color: var(--text-primary);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .nav-btn-success {
        background: var(--success-color);
        color: white;
        border-color: var(--success-color);
    }

    .nav-btn-success:hover {
        background: #059669;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    /* ===== ANIMAÇÕES ===== */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .employee-card {
        animation: fadeInUp 0.5s ease-out;
    }

    .employee-card:nth-child(1) { animation-delay: 0.1s; }
    .employee-card:nth-child(2) { animation-delay: 0.2s; }
    .employee-card:nth-child(3) { animation-delay: 0.3s; }
    .employee-card:nth-child(4) { animation-delay: 0.4s; }

    /* ===== RESPONSIVIDADE ===== */
    @media (max-width: 768px) {
        .main-container {
            padding: 1rem;
        }

        .modern-header {
            padding: 1.5rem;
        }

        .stats-container {
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
        }

        .stat-card {
            padding: 1rem;
        }

        .stat-number {
            font-size: 1.5rem;
        }

        .filters-grid {
            grid-template-columns: 1fr;
        }

        .reports-grid {
            grid-template-columns: 1fr;
        }

        .employee-details {
            grid-template-columns: 1fr;
        }

        .employee-actions {
            flex-direction: column;
        }

        .navigation-section {
            flex-direction: column;
            gap: 1rem;
        }
    }

    @media (max-width: 480px) {
        .stats-container {
            grid-template-columns: 1fr;
        }

        .employee-header {
            flex-direction: column;
            gap: 1rem;
        }

        .action-btn {
            flex: 1;
            justify-content: center;
        }
    }

    /* ===== RESPONSIVIDADE DOS RELATÓRIOS ===== */
    @media (max-width: 768px) {
        .reports-grid {
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .report-btn {
            padding: 1rem;
            font-size: 0.8rem;
            min-height: 55px;
        }

        .report-btn i {
            font-size: 1.125rem;
        }
    }

    @media (max-width: 480px) {
        .reports-title {
            font-size: 1.125rem;
        }

        .reports-title i {
            font-size: 1.25rem;
        }

        .report-btn {
            padding: 0.875rem;
            gap: 0.5rem;
            min-height: 50px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Header Moderno -->
    <div class="modern-header">
        <div class="header-content">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 style="margin: 0; font-size: 2rem; font-weight: 700;">
                        <i class="fas fa-users" style="margin-right: 0.75rem;"></i>
                        Gestão de Funcionários
                    </h1>
                    <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-size: 1rem;">
                        {% if empresa_principal %}{{ empresa_principal.razao_social }}{% else %}Empresa Principal{% endif %}
                    </p>
                </div>
                <div class="col-md-4">
                    <div class="stats-container">
                        <div class="stat-card">
                            <span class="stat-number">{{ stats.total_funcionarios }}</span>
                            <span class="stat-label">Total</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number">{{ stats.funcionarios_ativos }}</span>
                            <span class="stat-label">Ativos</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number">{{ stats.jornadas_disponiveis }}</span>
                            <span class="stat-label">Jornadas</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Seção de Relatórios Modernos -->
    <div class="reports-section">
        <h3 class="reports-title">
            <i class="fas fa-chart-bar"></i>
            Relatórios Gerais
        </h3>
        <div class="reports-grid">
            <button class="report-btn report-btn-primary" onclick="gerarRelatorioGeral()">
                <i class="fas fa-file-pdf"></i>
                Relatório Geral
            </button>
            <button class="report-btn report-btn-success" onclick="gerarRelatorioFrequencia()">
                <i class="fas fa-clock"></i>
                Frequência Geral
            </button>
            <button class="report-btn report-btn-info" onclick="gerarRelatorioJornadas()">
                <i class="fas fa-calendar-alt"></i>
                Jornadas de Trabalho
            </button>
            <button class="report-btn report-btn-warning" onclick="exportarFuncionarios()">
                <i class="fas fa-download"></i>
                Exportar Lista
            </button>
        </div>
    </div>

    <!-- Filtros Modernos -->
    <div class="filters-card">
        <div class="filters-grid">
            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select class="modern-select" id="filtroStatus" onchange="aplicarFiltros()">
                    <option value="">Todos os status</option>
                    <option value="ativo">Ativos</option>
                    <option value="inativo">Inativos</option>
                </select>
            </div>
            <div class="filter-group">
                <label class="filter-label">Jornada</label>
                <select class="modern-select" id="filtroJornada" onchange="aplicarFiltros()">
                    <option value="">Todas as jornadas</option>
                    {% for jornada in jornadas %}
                        <option value="{{ jornada.id }}">{{ jornada.nome_jornada }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="filter-group">
                <label class="filter-label">Buscar Funcionário</label>
                <input type="text" class="modern-input" id="buscaFuncionario" placeholder="Nome, CPF ou cargo..." onkeyup="aplicarFiltros()">
            </div>
            <div class="filter-group">
                <label class="filter-label">&nbsp;</label>
                <button class="modern-btn modern-btn-secondary" onclick="limparFiltros()">
                    <i class="fas fa-eraser"></i>
                    Limpar Filtros
                </button>
            </div>
        </div>
    </div>

    <!-- Lista de Funcionários -->
    <div id="listaFuncionarios" class="employees-grid">
        {% if funcionarios %}
            {% for funcionario in funcionarios %}
            <div class="employee-card" 
                 data-status="{{ funcionario.status_cadastro.lower() if funcionario.status_cadastro else 'inativo' }}" 
                 data-jornada="{{ funcionario.jornada_trabalho_id or '' }}"
                 data-busca="{{ (funcionario.nome_completo + ' ' + (funcionario.cpf or '') + ' ' + (funcionario.cargo or '')).lower() }}">
                
                <div class="employee-header">
                    <div class="employee-info">
                        <h3 class="employee-name">
                            <i class="fas fa-user"></i>
                            {{ funcionario.nome_completo }}
                        </h3>
                        <p class="employee-matricula">Matrícula: {{ funcionario.matricula_empresa or 'N/A' }}</p>
                    </div>
                    <div>
                        <span class="status-badge status-{{ funcionario.status_cadastro.lower() if funcionario.status_cadastro else 'inativo' }}">
                            {{ funcionario.status_display }}
                        </span>
                    </div>
                </div>

                <div class="employee-details">
                    <div class="detail-group">
                        <span class="detail-label">CPF</span>
                        <span class="detail-value">{{ funcionario.cpf or 'N/A' }}</span>
                    </div>
                    <div class="detail-group">
                        <span class="detail-label">Cargo</span>
                        <span class="detail-value">{{ funcionario.cargo or 'N/A' }}</span>
                    </div>
                    <div class="detail-group">
                        <span class="detail-label">Telefone</span>
                        <span class="detail-value">{{ funcionario.telefone1 or 'N/A' }}</span>
                    </div>
                    <div class="detail-group">
                        <span class="detail-label">Admissão</span>
                        <span class="detail-value">{{ funcionario.data_admissao_formatada or 'N/A' }}</span>
                    </div>
                </div>

                <div class="jornada-section">
                    <h4 class="jornada-title">
                        <i class="fas fa-clock"></i>
                        Jornada de Trabalho
                    </h4>
                    {% if funcionario.jornada_nome %}
                        <div class="jornada-details">
                            <div class="jornada-item">
                                <span class="jornada-label">Jornada:</span>
                                <span class="jornada-time">{{ funcionario.jornada_nome }}</span>
                            </div>
                            <div class="jornada-item">
                                <span class="jornada-label">Seg-Qui:</span>
                                <span class="jornada-time">
                                    {% if funcionario.seg_qui_entrada %}
                                        {% set entrada_horas = (funcionario.seg_qui_entrada.total_seconds() // 3600) | int %}
                                        {% set entrada_minutos = ((funcionario.seg_qui_entrada.total_seconds() % 3600) // 60) | int %}
                                        {{ "%02d:%02d" | format(entrada_horas, entrada_minutos) }}
                                    {% else %}N/A{% endif %} às
                                    {% if funcionario.seg_qui_saida %}
                                        {% set saida_horas = (funcionario.seg_qui_saida.total_seconds() // 3600) | int %}
                                        {% set saida_minutos = ((funcionario.seg_qui_saida.total_seconds() % 3600) // 60) | int %}
                                        {{ "%02d:%02d" | format(saida_horas, saida_minutos) }}
                                    {% else %}N/A{% endif %}
                                </span>
                            </div>
                            <div class="jornada-item">
                                <span class="jornada-label">Sexta:</span>
                                <span class="jornada-time">
                                    {% if funcionario.sexta_entrada %}
                                        {% set entrada_horas = (funcionario.sexta_entrada.total_seconds() // 3600) | int %}
                                        {% set entrada_minutos = ((funcionario.sexta_entrada.total_seconds() % 3600) // 60) | int %}
                                        {{ "%02d:%02d" | format(entrada_horas, entrada_minutos) }}
                                    {% else %}N/A{% endif %} às
                                    {% if funcionario.sexta_saida %}
                                        {% set saida_horas = (funcionario.sexta_saida.total_seconds() // 3600) | int %}
                                        {% set saida_minutos = ((funcionario.sexta_saida.total_seconds() % 3600) // 60) | int %}
                                        {{ "%02d:%02d" | format(saida_horas, saida_minutos) }}
                                    {% else %}N/A{% endif %}
                                </span>
                            </div>
                        </div>
                    {% else %}
                        <div class="jornada-undefined">
                            <i class="fas fa-exclamation-triangle"></i>
                            Jornada não definida
                        </div>
                    {% endif %}
                </div>

                <div class="employee-actions">
                    <button class="action-btn action-btn-info" 
                            data-funcionario-id="{{ funcionario.id }}" 
                            data-funcionario-nome="{{ funcionario.nome_completo }}" 
                            onclick="verFrequenciaSeguro(this)">
                        <i class="fas fa-calendar-check"></i>
                        Frequência
                    </button>
                    {% if funcionario.status_cadastro == 'Ativo' %}
                        <button class="action-btn action-btn-danger" 
                                data-funcionario-id="{{ funcionario.id }}" 
                                data-funcionario-nome="{{ funcionario.nome_completo }}" 
                                onclick="inativarFuncionarioSeguro(this)">
                            <i class="fas fa-user-times"></i>
                            Inativar
                        </button>
                    {% else %}
                        <button class="action-btn action-btn-success" 
                                data-funcionario-id="{{ funcionario.id }}" 
                                data-funcionario-nome="{{ funcionario.nome_completo }}" 
                                onclick="ativarFuncionarioSeguro(this)">
                            <i class="fas fa-user-check"></i>
                            Ativar
                        </button>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <i class="fas fa-users empty-icon"></i>
                <h3 class="empty-title">Nenhum funcionário encontrado</h3>
                <p class="empty-description">Não há funcionários cadastrados para esta empresa principal.</p>
                <a href="/funcionarios/adicionar" class="nav-btn nav-btn-success">
                    <i class="fas fa-plus"></i>
                    Cadastrar Primeiro Funcionário
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Navegação -->
    <div class="navigation-section">
        <a href="{{ url_for('empresa_principal.index') }}" class="nav-btn nav-btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Voltar ao Dashboard
        </a>
        <a href="/funcionarios/adicionar" class="nav-btn nav-btn-success">
            <i class="fas fa-plus"></i>
            Novo Funcionário
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Teste de URLs dos relatórios
console.log('🔍 Testando URLs dos Relatórios:');
console.log('1️⃣ Relatório Geral:', '/empresa-principal/relatorios/funcionarios/geral');
console.log('2️⃣ Relatório Frequência:', '/empresa-principal/relatorios/funcionarios/frequencia');
console.log('3️⃣ Relatório Jornadas:', '/empresa-principal/relatorios/jornadas');
console.log('4️⃣ Exportar Funcionários:', '/empresa-principal/relatorios/funcionarios/exportar');

// Aplicar filtros
function aplicarFiltros() {
    const filtroStatus = document.getElementById('filtroStatus').value.toLowerCase();
    const filtroJornada = document.getElementById('filtroJornada').value;
    const buscaTexto = document.getElementById('buscaFuncionario').value.toLowerCase();
    
    const cards = document.querySelectorAll('.employee-card');
    
    cards.forEach(card => {
        const status = card.getAttribute('data-status');
        const jornada = card.getAttribute('data-jornada');
        const busca = card.getAttribute('data-busca');
        
        let mostrar = true;
        
        // Filtro por status
        if (filtroStatus && status !== filtroStatus) {
            mostrar = false;
        }
        
        // Filtro por jornada
        if (filtroJornada && jornada !== filtroJornada) {
            mostrar = false;
        }
        
        // Filtro por busca
        if (buscaTexto && !busca.includes(buscaTexto)) {
            mostrar = false;
        }
        
        // Mostrar/ocultar card
        if (mostrar) {
            card.style.display = 'block';
            card.style.animation = 'fadeInUp 0.5s ease-out';
        } else {
            card.style.display = 'none';
        }
    });
}

// Limpar filtros
function limparFiltros() {
    document.getElementById('filtroStatus').value = '';
    document.getElementById('filtroJornada').value = '';
    document.getElementById('buscaFuncionario').value = '';
    aplicarFiltros();
}

// Funções de relatórios e ações - CORRIGIDAS E MELHORADAS
function gerarRelatorioGeral() {
    console.log('🔄 Gerando Relatório Geral...');
    
    // Adicionar indicador visual de loading
    const btn = event.target.closest('.report-btn');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gerando...';
    btn.disabled = true;
    
    // Usar URL correta do módulo empresa_principal
    const url = '/empresa-principal/relatorios/funcionarios/geral';
    
    setTimeout(() => {
        window.open(url, '_blank');
        
        // Restaurar botão após 2 segundos
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }, 2000);
    }, 500);
}

function gerarRelatorioFrequencia() {
    console.log('🔄 Gerando Relatório de Frequência...');
    
    const btn = event.target.closest('.report-btn');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gerando...';
    btn.disabled = true;
    
    // Usar URL correta do módulo empresa_principal
    const url = '/empresa-principal/relatorios/funcionarios/frequencia';
    
    setTimeout(() => {
        window.open(url, '_blank');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }, 2000);
    }, 500);
}

function gerarRelatorioJornadas() {
    console.log('🔄 Gerando Relatório de Jornadas...');
    
    const btn = event.target.closest('.report-btn');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gerando...';
    btn.disabled = true;
    
    // Usar URL correta do módulo empresa_principal
    const url = '/empresa-principal/relatorios/jornadas';
    
    setTimeout(() => {
        window.open(url, '_blank');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }, 2000);
    }, 500);
}

function exportarFuncionarios() {
    console.log('🔄 Exportando Lista de Funcionários...');
    
    const btn = event.target.closest('.report-btn');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exportando...';
    btn.disabled = true;
    
    // Usar URL correta do módulo empresa_principal
    const url = '/empresa-principal/relatorios/funcionarios/exportar';
    
    setTimeout(() => {
        window.open(url, '_blank');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }, 2000);
    }, 500);
}

// ===== FUNÇÕES SEGURAS COM DATA ATTRIBUTES =====
function verFrequenciaSeguro(button) {
    const funcionarioId = button.getAttribute('data-funcionario-id');
    const nome = button.getAttribute('data-funcionario-nome');
    window.open(`/funcionarios/frequencia/${funcionarioId}`, '_blank');
}

function inativarFuncionarioSeguro(button) {
    const funcionarioId = button.getAttribute('data-funcionario-id');
    const nome = button.getAttribute('data-funcionario-nome');
    
    if (confirm(`Deseja realmente inativar o funcionário ${nome}?`)) {
        fetch(`/funcionarios/inativar/${funcionarioId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Funcionário inativado com sucesso!');
                location.reload();
            } else {
                alert('Erro ao inativar funcionário: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao inativar funcionário');
        });
    }
}

function ativarFuncionarioSeguro(button) {
    const funcionarioId = button.getAttribute('data-funcionario-id');
    const nome = button.getAttribute('data-funcionario-nome');
    
    if (confirm(`Deseja realmente ativar o funcionário ${nome}?`)) {
        fetch(`/funcionarios/ativar/${funcionarioId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Funcionário ativado com sucesso!');
                location.reload();
            } else {
                alert('Erro ao ativar funcionário: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao ativar funcionário');
        });
    }
}

// ===== FUNÇÕES LEGADAS (MANTIDAS PARA COMPATIBILIDADE) =====
function verFrequencia(funcionarioId, nome) {
    window.open(`/funcionarios/frequencia/${funcionarioId}`, '_blank');
}

function inativarFuncionario(funcionarioId, nome) {
    if (confirm(`Deseja realmente inativar o funcionário ${nome}?`)) {
        fetch(`/funcionarios/inativar/${funcionarioId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Funcionário inativado com sucesso!');
                location.reload();
            } else {
                alert('Erro ao inativar funcionário: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao inativar funcionário');
        });
    }
}

function ativarFuncionario(funcionarioId, nome) {
    if (confirm(`Deseja realmente ativar o funcionário ${nome}?`)) {
        fetch(`/funcionarios/ativar/${funcionarioId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Funcionário ativado com sucesso!');
                location.reload();
            } else {
                alert('Erro ao ativar funcionário: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao ativar funcionário');
        });
    }
}

// Animações de entrada
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.employee-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
