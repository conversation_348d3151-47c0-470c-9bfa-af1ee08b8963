@echo off
:: =====================================================================
:: CONTROLADOR BRIDGE ZK4500 - RLPONTO-WEB v1.0
:: Script para controlar serviço bridge (iniciar/parar/reiniciar)
:: Desenvolvido por: <PERSON> Rodrigues - AiNexus Tecnologia
:: =====================================================================

setlocal enabledelayedexpansion
title CONTROLADOR BRIDGE ZK4500

:: Definir variáveis
set BRIDGE_NAME=RLPonto-BridgeZK4500
set BRIDGE_DIR=C:\RLPonto-Bridge
set BRIDGE_PORT=8080
set LOGGING_ENABLED=1

:: Verificar parâmetro de ação
if "%1"=="" goto MENU
if /i "%1"=="start" goto START_SERVICE
if /i "%1"=="stop" goto STOP_SERVICE  
if /i "%1"=="restart" goto RESTART_SERVICE
if /i "%1"=="status" goto STATUS_SERVICE
if /i "%1"=="hide" goto START_HIDDEN
goto MENU

:MENU
echo.
echo ========================================================
echo   CONTROLADOR BRIDGE ZK4500 - RLPONTO-WEB v1.0
echo ========================================================
echo.
echo Escolha uma acao:
echo.
echo [1] Iniciar servico bridge
echo [2] Parar servico bridge  
echo [3] Reiniciar servico bridge
echo [4] Verificar status
echo [5] Iniciar em modo oculto
echo [6] Sair
echo.
set /p CHOICE="Digite sua opcao (1-6): "

if "%CHOICE%"=="1" goto START_SERVICE
if "%CHOICE%"=="2" goto STOP_SERVICE
if "%CHOICE%"=="3" goto RESTART_SERVICE  
if "%CHOICE%"=="4" goto STATUS_SERVICE
if "%CHOICE%"=="5" goto START_HIDDEN
if "%CHOICE%"=="6" goto END
goto MENU

:START_SERVICE
echo.
echo [INFO] Iniciando servico bridge...
if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Tentativa de iniciar servico via controlador"

sc start "%BRIDGE_NAME%" >nul 2>&1
if !errorLevel! == 0 (
    echo [OK] Comando de inicio enviado
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Comando start enviado ao servico"
    
    :: Aguardar e verificar se está rodando
    echo [INFO] Aguardando inicializacao...
    timeout /t 5 >nul
    sc query "%BRIDGE_NAME%" | findstr "RUNNING" >nul 2>&1
    if !errorLevel! == 0 (
        echo [OK] Servico confirmado como RODANDO
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Servico confirmado em execucao"
    ) else (
        echo [AVISO] Servico nao iniciou via SC - tentando modo oculto...
        goto START_HIDDEN
    )
) else (
    echo [AVISO] Falha ao iniciar via SC - tentando modo oculto...
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Falha SC, tentando modo oculto"
    goto START_HIDDEN
)
goto WAIT_KEY

:STOP_SERVICE
echo.
echo [INFO] Parando servico bridge...
if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Tentativa de parar servico via controlador"

sc stop "%BRIDGE_NAME%" >nul 2>&1
echo [INFO] Comando de parada enviado
timeout /t 2 >nul

:: Verificar processos Python e finalizar se necessário
tasklist /FI "IMAGENAME eq python.exe" | findstr "python.exe" >nul 2>&1
if !errorLevel! == 0 (
    echo [INFO] Finalizando processos Python relacionados...
    wmic process where "name='python.exe' and commandline like '%%biometric_bridge_service%%'" delete >nul 2>&1
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Processos Python do bridge finalizados"
)

echo [OK] Bridge parado
if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Bridge parado com sucesso"
goto WAIT_KEY

:RESTART_SERVICE
echo.
echo [INFO] Reiniciando servico bridge...
if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Reinicio do servico solicitado"

:: Parar primeiro
call :STOP_SERVICE_QUIET
timeout /t 3 >nul

:: Iniciar novamente
call :START_SERVICE_QUIET

echo [OK] Reinicio concluido
if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Servico reiniciado"
goto WAIT_KEY

:START_HIDDEN
echo.
echo [INFO] Iniciando bridge em modo oculto...
if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Iniciando bridge em modo oculto"

:: Verificar se Python está disponível
python --version >nul 2>&1
if !errorLevel! == 0 (
    echo [OK] Python detectado
    
    :: Finalizar qualquer instância anterior
    wmic process where "name='python.exe' and commandline like '%%biometric_bridge_service%%'" delete >nul 2>&1
    timeout /t 1 >nul
    
    :: Iniciar em background
    echo [INFO] Iniciando em background...
    start /B /MIN "Bridge-ZK4500" /D "%BRIDGE_DIR%" python biometric_bridge_service.py >nul 2>&1
    
    :: Aguardar inicialização
    timeout /t 8 >nul
    
    :: Verificar se está rodando
    netstat -an | findstr ":%BRIDGE_PORT%" | findstr "LISTENING" >nul 2>&1
    if !errorLevel! == 0 (
        echo [OK] Bridge rodando em background na porta %BRIDGE_PORT%
        echo [INFO] Servico oculto e operacional
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Bridge iniciado em modo oculto com sucesso"
        
        :: Teste de conectividade
        curl -s --connect-timeout 3 http://localhost:%BRIDGE_PORT%/api/bridge-status >nul 2>&1
        if !errorLevel! == 0 (
            echo [OK] Bridge respondendo via HTTP
            if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Bridge oculto respondendo HTTP"
        )
    ) else (
        echo [ERRO] Falha ao iniciar bridge
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Falha ao iniciar bridge em modo oculto"
    )
) else (
    echo [ERRO] Python nao encontrado
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Python nao disponivel"
)
goto WAIT_KEY

:STATUS_SERVICE
echo.
echo [INFO] Verificando status...
call "%~dp0status_bridge.bat"
goto END

:STOP_SERVICE_QUIET
sc stop "%BRIDGE_NAME%" >nul 2>&1
wmic process where "name='python.exe' and commandline like '%%biometric_bridge_service%%'" delete >nul 2>&1
exit /b

:START_SERVICE_QUIET  
sc start "%BRIDGE_NAME%" >nul 2>&1
if !errorLevel! neq 0 (
    start /B /MIN "Bridge-ZK4500" /D "%BRIDGE_DIR%" python biometric_bridge_service.py >nul 2>&1
)
exit /b

:WAIT_KEY
echo.
echo Pressione qualquer tecla para continuar...
pause >nul
goto MENU

:END
echo.
echo Encerrando controlador...
if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Controlador finalizado" 