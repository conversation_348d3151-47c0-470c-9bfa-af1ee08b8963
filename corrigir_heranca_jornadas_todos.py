#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para corrigir herança de jornadas de todos os funcionários
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def corrigir_heranca_jornadas_todos():
    """Corrigir herança de jornadas para todos os funcionários"""
    print("🔧 CORRIGINDO HERANÇA DE JORNADAS - TODOS OS FUNCIONÁRIOS")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Buscar todos os funcionários ativos
        print("\n1. Buscando funcionários ativos...")
        sql_funcionarios = """
        SELECT 
            f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id, f.horario_trabalho_id,
            e.razao_social as empresa_nome
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        WHERE f.status_cadastro = 'Ativo'
        ORDER BY e.razao_social, f.nome_completo
        """
        
        funcionarios = db.execute_query(sql_funcionarios)
        
        print(f"📊 Funcionários ativos encontrados: {len(funcionarios)}")
        for func in funcionarios:
            print(f"   - ID {func['id']}: {func['nome_completo']} ({func['empresa_nome']})")
            print(f"     Jornada ID: {func['jornada_trabalho_id']}, Horário ID: {func['horario_trabalho_id']}")
        
        # 2. Buscar jornadas padrão de cada empresa
        print("\n2. Buscando jornadas padrão das empresas...")
        sql_jornadas_empresas = """
        SELECT 
            e.id as empresa_id, e.razao_social,
            jt.id as jornada_id, jt.nome_jornada
        FROM empresas e
        LEFT JOIN jornadas_trabalho jt ON e.id = jt.empresa_id AND jt.padrao = TRUE AND jt.ativa = TRUE
        WHERE e.ativa = TRUE
        ORDER BY e.razao_social
        """
        
        jornadas_empresas = db.execute_query(sql_jornadas_empresas)
        
        print(f"📊 Empresas e suas jornadas:")
        jornadas_map = {}
        for item in jornadas_empresas:
            empresa_id = item['empresa_id']
            jornadas_map[empresa_id] = item
            status = f"Jornada ID {item['jornada_id']}: {item['nome_jornada']}" if item['jornada_id'] else "SEM JORNADA"
            print(f"   - {item['razao_social']} (ID {empresa_id}): {status}")
        
        # 3. Corrigir funcionários
        print("\n3. Corrigindo funcionários...")
        funcionarios_corrigidos = 0
        funcionarios_sem_jornada = 0
        
        for func in funcionarios:
            empresa_id = func['empresa_id']
            funcionario_id = func['id']
            nome = func['nome_completo']
            
            # Verificar se empresa tem jornada padrão
            jornada_empresa = jornadas_map.get(empresa_id)
            
            if not jornada_empresa or not jornada_empresa['jornada_id']:
                print(f"   ⚠️ {nome}: Empresa sem jornada padrão - mantendo configuração atual")
                funcionarios_sem_jornada += 1
                continue
            
            jornada_correta_id = jornada_empresa['jornada_id']
            
            # Verificar se funcionário já está correto
            if func['jornada_trabalho_id'] == jornada_correta_id and not func['horario_trabalho_id']:
                print(f"   ✅ {nome}: Já está correto (Jornada ID {jornada_correta_id})")
                continue
            
            # Corrigir funcionário
            print(f"   🔧 {nome}: Corrigindo para Jornada ID {jornada_correta_id}")
            
            sql_update = """
            UPDATE funcionarios 
            SET jornada_trabalho_id = %s,
                horario_trabalho_id = NULL,
                jornada_seg_qui_entrada = NULL,
                jornada_seg_qui_saida = NULL,
                jornada_sex_entrada = NULL,
                jornada_sex_saida = NULL,
                jornada_intervalo_entrada = NULL,
                jornada_intervalo_saida = NULL
            WHERE id = %s
            """
            
            result = db.execute_query(sql_update, (jornada_correta_id, funcionario_id), fetch_all=False)
            
            if result is not None:
                print(f"      ✅ Corrigido com sucesso!")
                funcionarios_corrigidos += 1
            else:
                print(f"      ❌ Erro ao corrigir")
        
        # 4. Resumo final
        print(f"\n4. RESUMO FINAL:")
        print(f"   ✅ Funcionários corrigidos: {funcionarios_corrigidos}")
        print(f"   ⚠️ Funcionários sem jornada da empresa: {funcionarios_sem_jornada}")
        print(f"   📊 Total de funcionários: {len(funcionarios)}")
        
        # 5. Verificar resultado final
        print(f"\n5. Verificando resultado final...")
        funcionarios_atualizados = db.execute_query(sql_funcionarios)
        
        print(f"📋 Status final dos funcionários:")
        for func in funcionarios_atualizados:
            empresa_id = func['empresa_id']
            jornada_empresa = jornadas_map.get(empresa_id)
            
            if jornada_empresa and jornada_empresa['jornada_id']:
                if func['jornada_trabalho_id'] == jornada_empresa['jornada_id']:
                    status = "✅ CORRETO"
                else:
                    status = f"⚠️ INCORRETO (deveria ser {jornada_empresa['jornada_id']})"
            else:
                status = "⚠️ EMPRESA SEM JORNADA"
            
            print(f"   - {func['nome_completo']}: Jornada ID {func['jornada_trabalho_id']} - {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a correção: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    corrigir_heranca_jornadas_todos()
