{% extends "base.html" %}

{% block title %}Relatório de Herança Dinâmica de Jornadas{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Início</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('funcionarios.index') }}">Funcionários</a></li>
            <li class="breadcrumb-item active">Herança de Jornadas</li>
        </ol>
    </nav>

    <!-- Cabeçalho -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-clock text-primary me-2"></i>
                Sistema de Herança Dinâmica de Jornadas
            </h1>
            <p class="text-muted mb-0">Relatório de consistência e status do sistema</p>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-1"></i>
                Atualizar
            </button>
        </div>
    </div>

    <!-- Cards de Estatísticas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <h4 class="card-title">{{ estatisticas.total_funcionarios }}</h4>
                    <p class="card-text text-muted">Total de Funcionários</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-link fa-2x"></i>
                    </div>
                    <h4 class="card-title">{{ estatisticas.funcionarios_heranca_empresa }}</h4>
                    <p class="card-text text-muted">Com Herança da Empresa</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h4 class="card-title">{{ estatisticas.funcionarios_com_jornada }}</h4>
                    <p class="card-text text-muted">Com Jornada Definida</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <h4 class="card-title">{{ relatorio.problemas|length }}</h4>
                    <p class="card-text text-muted">Problemas Detectados</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Relatório de Consistência -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        Relatório de Consistência
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Funcionários sem jornada:</label>
                                <span class="badge bg-{{ 'danger' if relatorio.funcionarios_sem_jornada > 0 else 'success' }} ms-2">
                                    {{ relatorio.funcionarios_sem_jornada }}
                                </span>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Funcionários com jornada inativa:</label>
                                <span class="badge bg-{{ 'warning' if relatorio.funcionarios_jornada_inativa > 0 else 'success' }} ms-2">
                                    {{ relatorio.funcionarios_jornada_inativa }}
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Empresas sem jornada padrão:</label>
                                <span class="badge bg-{{ 'warning' if relatorio.empresas_sem_jornada_padrao > 0 else 'success' }} ms-2">
                                    {{ relatorio.empresas_sem_jornada_padrao }}
                                </span>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Alocações inconsistentes:</label>
                                <span class="badge bg-{{ 'danger' if relatorio.alocacoes_jornada_inconsistente > 0 else 'success' }} ms-2">
                                    {{ relatorio.alocacoes_jornada_inconsistente }}
                                </span>
                            </div>
                        </div>
                    </div>

                    {% if relatorio.problemas %}
                    <div class="alert alert-warning mt-3">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Problemas Detectados:
                        </h6>
                        <ul class="mb-0">
                            {% for problema in relatorio.problemas %}
                            <li>{{ problema }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% else %}
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Sistema Consistente!</strong> Nenhum problema detectado.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Últimas Mudanças -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history text-info me-2"></i>
                        Últimas Mudanças de Jornada
                    </h5>
                </div>
                <div class="card-body">
                    {% if ultimas_mudancas %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Data/Hora</th>
                                    <th>Funcionário</th>
                                    <th>Tipo de Mudança</th>
                                    <th>Jornada Anterior</th>
                                    <th>Nova Jornada</th>
                                    <th>Motivo</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for mudanca in ultimas_mudancas %}
                                <tr>
                                    <td>
                                        <small class="text-muted">
                                            {{ mudanca.data_mudanca.strftime('%d/%m/%Y %H:%M') }}
                                        </small>
                                    </td>
                                    <td>
                                        <strong>{{ mudanca.nome_completo or 'N/A' }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 
                                            'primary' if mudanca.tipo_mudanca == 'EMPRESA_ALTEROU' 
                                            else 'info' if mudanca.tipo_mudanca == 'ALOCACAO_CLIENTE'
                                            else 'secondary' if mudanca.tipo_mudanca == 'ALTERACAO_MANUAL'
                                            else 'success' if mudanca.tipo_mudanca == 'CADASTRO_INICIAL'
                                            else 'warning'
                                        }}">
                                            {{ mudanca.tipo_mudanca.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ mudanca.jornada_anterior or 'N/A' }}
                                        </small>
                                    </td>
                                    <td>
                                        <strong>{{ mudanca.jornada_nova or 'N/A' }}</strong>
                                    </td>
                                    <td>
                                        <small>{{ mudanca.motivo[:50] }}{% if mudanca.motivo|length > 50 %}...{% endif %}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Nenhuma mudança de jornada registrada ainda.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmação para Sincronização -->
<div class="modal fade" id="modalSincronizar" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sincronizar Jornadas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja sincronizar as jornadas de todos os funcionários desta empresa?</p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Esta ação irá atualizar automaticamente as jornadas de todos os funcionários que usam herança da empresa.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="btnConfirmarSincronizar">
                    <i class="fas fa-sync-alt me-1"></i>
                    Sincronizar
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh a cada 30 segundos
    setInterval(function() {
        fetch('{{ url_for("funcionarios.api_status_heranca_jornadas") }}')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Atualizar badges de problemas
                    const problemasCount = data.data.problemas ? data.data.problemas.length : 0;
                    const problemasCard = document.querySelector('.border-warning .card-title');
                    if (problemasCard) {
                        problemasCard.textContent = problemasCount;
                    }
                }
            })
            .catch(error => console.log('Erro ao atualizar status:', error));
    }, 30000);
});
</script>
{% endblock %}
