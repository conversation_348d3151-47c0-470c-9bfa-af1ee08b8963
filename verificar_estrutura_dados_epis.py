#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar estrutura exata dos dados de EPIs
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def verificar_estrutura_dados():
    """Verificar estrutura exata dos dados de EPIs"""
    print("🔍 VERIFICANDO ESTRUTURA EXATA DOS DADOS DE EPIs")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar dados diretos da tabela epis
        print("\n1. DADOS DIRETOS DA TABELA epis...")
        sql_direto = "SELECT * FROM epis WHERE funcionario_id = 1 LIMIT 1"
        epi_direto = db.execute_query(sql_direto, fetch_one=True)
        
        if epi_direto:
            print(f"📋 Estrutura da tabela epis:")
            for key, value in epi_direto.items():
                print(f"   {key}: {value}")
        else:
            print(f"❌ Nenhum EPI encontrado na tabela")
        
        # 2. Verificar dados via get_with_epis
        print(f"\n2. DADOS VIA get_with_epis...")
        from utils.database import FuncionarioQueries
        
        funcionario = FuncionarioQueries.get_with_epis(1)
        
        if funcionario and funcionario.get('epis'):
            epis = funcionario['epis']
            print(f"📊 Total EPIs via get_with_epis: {len(epis)}")
            
            if epis:
                primeiro_epi = epis[0]
                print(f"📋 Estrutura do primeiro EPI:")
                for key, value in primeiro_epi.items():
                    print(f"   {key}: {value}")
        else:
            print(f"❌ Nenhum EPI via get_with_epis")
        
        # 3. Comparar com o que o template espera
        print(f"\n3. COMPARAÇÃO COM TEMPLATE...")
        
        template_esperado = {
            'nome': 'epi.nome',
            'ca': 'epi.ca', 
            'data_entrega': 'epi.data_entrega',
            'data_vencimento': 'epi.data_vencimento'
        }
        
        dados_reais = {
            'epi_nome': 'epi.epi_nome',
            'epi_ca': 'epi.epi_ca',
            'epi_data_entrega': 'epi.epi_data_entrega', 
            'epi_data_validade': 'epi.epi_data_validade'
        }
        
        print(f"📋 Template espera:")
        for key, value in template_esperado.items():
            print(f"   {key}: {value}")
        
        print(f"📋 Dados reais:")
        for key, value in dados_reais.items():
            print(f"   {key}: {value}")
        
        print(f"\n🎯 PROBLEMAS IDENTIFICADOS:")
        print(f"   ❌ Template usa: epi.nome")
        print(f"   ✅ Dados reais: epi.epi_nome")
        print(f"   ❌ Template usa: epi.ca")
        print(f"   ✅ Dados reais: epi.epi_ca")
        print(f"   ❌ Template usa: epi.data_vencimento")
        print(f"   ✅ Dados reais: epi.epi_data_validade")
        
        # 4. Testar correção do template
        print(f"\n4. SIMULANDO TEMPLATE CORRIGIDO...")
        
        if funcionario and funcionario.get('epis'):
            epis = funcionario['epis']
            print(f"📋 Como deveria aparecer no template:")
            
            for i, epi in enumerate(epis):
                print(f"   EPI {i+1}:")
                print(f"      Nome: {epi.get('epi_nome', 'N/A')}")
                print(f"      CA: {epi.get('epi_ca', 'N/A')}")
                print(f"      Data entrega: {epi.get('epi_data_entrega', 'N/A')}")
                print(f"      Data validade: {epi.get('epi_data_validade', 'N/A')}")
                print(f"      Status: {epi.get('status_epi', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a verificação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    verificar_estrutura_dados()
