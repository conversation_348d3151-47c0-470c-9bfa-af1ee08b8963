=== CONFIGURAÇÃO DE CHAVE SSH PARA RLPONTO-WEB ===

Servidor: 10.19.208.31
Usuário: root
Senha: @Ric6109

CHAVE PÚBLICA SSH GERADA:
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC9xQ3QszSo3EyW88tLKe+Pcc5WwTdoDEYGtohbcskOVlNUCdfaqYryigGqqDmlWIfGW8t+RWpeK08Ay6uIAYQU9gw1KsOo/qMLThIdyH/luMlKV6w8tnTULnq/S8CLJRHxkR5LPjJv5QShq8fBRAXaR6P1D5Mexzeq5jqPF2yk0y7yPugwEFyorHDf5iKlOVaHBW8TYxfvtLT+jev2dJjJNl0PjA5uIxX8jY33uUf0bfZfTyVDSW27aw+S/Lp5bU44fsQi0YO7DiOMnxR51PN2JobcBm3Muhv7EdEkuYCxmSmc4A2lDkqDS4w0VY3cunnt58R8v4s8HfxwFl/MF9nLTbjKrDHGvrCKGtxcmwfSZFXfj8uhbm7BJjK5apmDku8U9yPt57jQIAtIYajfOH0tgFxVvZCaaFF6qgy0Hg+qhXiDWOVS71IBYGBANPT0m07JaSU70W20s8ZNvbINEWx2y0YJMMeN0wgI8q/F2TFdq5s12RyWj77of4EVS9q2gRu4L79D1RDpCywbHvptbv9Hn5EN7NMIQBJ69kE1kLZrA7YqXo7sttrpB2b8N+mlB9mX/VJ1+bXNV19Tl+/3STFESUaJDhJBHaPRQnLcRwM0SYXfksP06LuLP3U7FjgX1aGq4JIWUfr8W26Nbv7Oxc3ugWyZ4TZmK11IEJfmykX1fw== rlponto-web-access

INSTRUÇÕES PARA CONFIGURAR NO SERVIDOR:

1. Conecte-se ao servidor:
   ssh root@10.19.208.31
   (Digite a senha: @Ric6109)

2. Execute os seguintes comandos no servidor:

   # Configurar SSH para usuário root
   mkdir -p /root/.ssh
   chmod 700 /root/.ssh
   echo 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC9xQ3QszSo3EyW88tLKe+Pcc5WwTdoDEYGtohbcskOVlNUCdfaqYryigGqqDmlWIfGW8t+RWpeK08Ay6uIAYQU9gw1KsOo/qMLThIdyH/luMlKV6w8tnTULnq/S8CLJRHxkR5LPjJv5QShq8fBRAXaR6P1D5Mexzeq5jqPF2yk0y7yPugwEFyorHDf5iKlOVaHBW8TYxfvtLT+jev2dJjJNl0PjA5uIxX8jY33uUf0bfZfTyVDSW27aw+S/Lp5bU44fsQi0YO7DiOMnxR51PN2JobcBm3Muhv7EdEkuYCxmSmc4A2lDkqDS4w0VY3cunnt58R8v4s8HfxwFl/MF9nLTbjKrDHGvrCKGtxcmwfSZFXfj8uhbm7BJjK5apmDku8U9yPt57jQIAtIYajfOH0tgFxVvZCaaFF6qgy0Hg+qhXiDWOVS71IBYGBANPT0m07JaSU70W20s8ZNvbINEWx2y0YJMMeN0wgI8q/F2TFdq5s12RyWj77of4EVS9q2gRu4L79D1RDpCywbHvptbv9Hn5EN7NMIQBJ69kE1kLZrA7YqXo7sttrpB2b8N+mlB9mX/VJ1+bXNV19Tl+/3STFESUaJDhJBHaPRQnLcRwM0SYXfksP06LuLP3U7FjgX1aGq4JIWUfr8W26Nbv7Oxc3ugWyZ4TZmK11IEJfmykX1fw== rlponto-web-access' >> /root/.ssh/authorized_keys
   chmod 600 /root/.ssh/authorized_keys

   # Configurar SSH para usuário cavalcrod
   mkdir -p /home/<USER>/.ssh
   chmod 700 /home/<USER>/.ssh
   echo 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC9xQ3QszSo3EyW88tLKe+Pcc5WwTdoDEYGtohbcskOVlNUCdfaqYryigGqqDmlWIfGW8t+RWpeK08Ay6uIAYQU9gw1KsOo/qMLThIdyH/luMlKV6w8tnTULnq/S8CLJRHxkR5LPjJv5QShq8fBRAXaR6P1D5Mexzeq5jqPF2yk0y7yPugwEFyorHDf5iKlOVaHBW8TYxfvtLT+jev2dJjJNl0PjA5uIxX8jY33uUf0bfZfTyVDSW27aw+S/Lp5bU44fsQi0YO7DiOMnxR51PN2JobcBm3Muhv7EdEkuYCxmSmc4A2lDkqDS4w0VY3cunnt58R8v4s8HfxwFl/MF9nLTbjKrDHGvrCKGtxcmwfSZFXfj8uhbm7BJjK5apmDku8U9yPt57jQIAtIYajfOH0tgFxVvZCaaFF6qgy0Hg+qhXiDWOVS71IBYGBANPT0m07JaSU70W20s8ZNvbINEWx2y0YJMMeN0wgI8q/F2TFdq5s12RyWj77of4EVS9q2gRu4L79D1RDpCywbHvptbv9Hn5EN7NMIQBJ69kE1kLZrA7YqXo7sttrpB2b8N+mlB9mX/VJ1+bXNV19Tl+/3STFESUaJDhJBHaPRQnLcRwM0SYXfksP06LuLP3U7FjgX1aGq4JIWUfr8W26Nbv7Oxc3ugWyZ4TZmK11IEJfmykX1fw== rlponto-web-access' >> /home/<USER>/.ssh/authorized_keys
   chmod 600 /home/<USER>/.ssh/authorized_keys
   chown -R cavalcrod:cavalcrod /home/<USER>/.ssh

   # Reiniciar serviço SSH
   systemctl restart ssh

3. Teste a conexão sem senha:
   ssh root@10.19.208.31
   ssh cavalcrod@10.19.208.31

INFORMAÇÕES DO PROJETO RLPONTO-WEB:
- Servidor: 10.19.208.31
- Usuário SSH: root / cavalcrod
- Usuário MySQL: cavalcrod
- Senha MySQL: 200381
- Porta MySQL: 3306
- Banco de Dados: controle_ponto
- Usuário Web: admin
- Senha Web: @Ric6109

APÓS CONFIGURAR A CHAVE SSH:
- Você poderá acessar o servidor sem digitar senha
- Use: ssh root@10.19.208.31 ou ssh cavalcrod@10.19.208.31
- Para deploy automático e gerenciamento remoto do projeto
