-- =====================================================
-- CORREÇÃO PASSO A PASSO
-- Data: 13/07/2025
-- =====================================================

-- 1. Primeiro, corrigir os dados existentes
UPDATE funcionarios_desligados SET turno = 'Diurno' WHERE turno = 'Integral';

-- 2. Verificar se a correção funcionou
SELECT DISTINCT turno FROM funcionarios_desligados;

-- 3. Agora alterar o ENUM
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN turno ENUM('Diurno','Noturno','Misto') NOT NULL;

-- 4. Verificar tipo_contrato (já está correto)
SELECT DISTINCT tipo_contrato FROM funcionarios_desligados;

-- 5. Alterar tipo_contrato
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN tipo_contrato ENUM('CLT','PJ','Estagio','Temporario') NOT NULL;

-- 6. Verificar status_cadastro
SELECT DISTINCT status_cadastro FROM funcionarios_desligados;

-- 7. Alterar status_cadastro
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN status_cadastro ENUM('Ativo','Inativo') DEFAULT 'Ativo';

SELECT 'Correção concluída!' as resultado;
