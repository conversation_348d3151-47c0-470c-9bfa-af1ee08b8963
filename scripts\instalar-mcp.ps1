# Script de Instalação dos Servidores MCP
# RLPONTO-WEB Project

Write-Host "=== Instalação dos Servidores MCP ===" -ForegroundColor Green
Write-Host ""

# Verificar se Node.js está instalado
Write-Host "1. Verificando Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "✓ Node.js encontrado: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js não encontrado"
    }
} catch {
    Write-Host "✗ Node.js não está instalado!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Por favor, instale o Node.js primeiro:" -ForegroundColor Yellow
    Write-Host "1. Acesse: https://nodejs.org/"
    Write-Host "2. Baixe a versão LTS"
    Write-Host "3. Execute o instalador"
    Write-Host "4. <PERSON><PERSON>cie o terminal e execute este script novamente"
    Write-Host ""
    Read-Host "Pressione Enter para abrir o site do Node.js"
    Start-Process "https://nodejs.org/"
    exit 1
}

# Verificar npm
Write-Host "2. Verificando npm..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version 2>$null
    Write-Host "✓ npm encontrado: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ npm não encontrado!" -ForegroundColor Red
    exit 1
}

# Verificar npx
Write-Host "3. Verificando npx..." -ForegroundColor Yellow
try {
    $npxVersion = npx --version 2>$null
    Write-Host "✓ npx encontrado: $npxVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ npx não encontrado!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== Instalando Servidores MCP ===" -ForegroundColor Green

# Lista de pacotes MCP para instalar
$mcpPackages = @(
    "@smithery/cli",
    "@agentdeskai/browser-tools-mcp",
    "@upstash/context7-mcp",
    "@21st-dev/magic"
)

# Instalar pacotes globalmente
Write-Host "4. Instalando pacotes MCP globalmente..." -ForegroundColor Yellow
foreach ($package in $mcpPackages) {
    Write-Host "   Instalando $package..." -ForegroundColor Cyan
    try {
        npm install -g $package --silent
        Write-Host "   ✓ $package instalado com sucesso" -ForegroundColor Green
    } catch {
        Write-Host "   ⚠ Erro ao instalar $package (será baixado quando necessário)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "=== Testando Servidores MCP ===" -ForegroundColor Green

# Testar cada servidor MCP
Write-Host "5. Testando servidores..." -ForegroundColor Yellow

# Teste GitHub MCP
Write-Host "   Testando GitHub MCP..." -ForegroundColor Cyan
try {
    $result = npx @smithery/cli@latest --help 2>$null
    if ($result) {
        Write-Host "   ✓ GitHub MCP funcionando" -ForegroundColor Green
    } else {
        Write-Host "   ⚠ GitHub MCP pode precisar de configuração adicional" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ⚠ Erro ao testar GitHub MCP" -ForegroundColor Yellow
}

# Teste Browser Tools MCP
Write-Host "   Testando Browser Tools MCP..." -ForegroundColor Cyan
try {
    $result = npx @agentdeskai/browser-tools-mcp@1.2.0 --help 2>$null
    if ($result) {
        Write-Host "   ✓ Browser Tools MCP funcionando" -ForegroundColor Green
    } else {
        Write-Host "   ⚠ Browser Tools MCP pode precisar de configuração adicional" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ⚠ Erro ao testar Browser Tools MCP" -ForegroundColor Yellow
}

# Teste Context7 MCP
Write-Host "   Testando Context7 MCP..." -ForegroundColor Cyan
try {
    $result = npx @upstash/context7-mcp --help 2>$null
    if ($result) {
        Write-Host "   ✓ Context7 MCP funcionando" -ForegroundColor Green
    } else {
        Write-Host "   ⚠ Context7 MCP pode precisar de configuração adicional" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ⚠ Erro ao testar Context7 MCP" -ForegroundColor Yellow
}

# Teste 21st-dev Magic MCP
Write-Host "   Testando 21st-dev Magic MCP..." -ForegroundColor Cyan
try {
    $result = npx @21st-dev/magic@latest --help 2>$null
    if ($result) {
        Write-Host "   ✓ 21st-dev Magic MCP funcionando" -ForegroundColor Green
    } else {
        Write-Host "   ⚠ 21st-dev Magic MCP pode precisar de configuração adicional" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ⚠ Erro ao testar 21st-dev Magic MCP" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Verificando Configuração ===" -ForegroundColor Green

# Verificar se o arquivo de configuração existe
Write-Host "6. Verificando arquivo de configuração..." -ForegroundColor Yellow
$configPath = ".vscode\settings.json"
if (Test-Path $configPath) {
    Write-Host "   ✓ Arquivo de configuração encontrado: $configPath" -ForegroundColor Green
} else {
    Write-Host "   ✗ Arquivo de configuração não encontrado!" -ForegroundColor Red
    Write-Host "   Criando arquivo de configuração..." -ForegroundColor Yellow
    
    # Criar diretório .vscode se não existir
    if (!(Test-Path ".vscode")) {
        New-Item -ItemType Directory -Path ".vscode" -Force | Out-Null
    }
    
    # Criar arquivo de configuração
    $configContent = @"
{
  "mcpServers": {
    "github": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@smithery-ai/github",
        "--key",
        "e68990f7-63a3-463a-9b83-736d2a5d4314",
        "--profile",
        "right-deer-A3RiHp"
      ]
    },
    "browser-tools": {
      "command": "cmd",
      "args": [
        "bash",
        "-c",
        "cmd /c npx -y @agentdeskai/browser-tools-mcp@1.2.0"
      ],
      "enabled": true
    },
    "context7": {
      "command": "npx",
      "args": [
        "-y",
        "@upstash/context7-mcp"
      ],
      "env": {
        "DEFAULT_MINIMUM_TOKENS": "6000"
      }
    },
    "@21st-dev/magic": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@21st-dev/magic@latest",
        "API_KEY=\"7bac1d77a079e8e3d511939c81bd0d248e8a7b56c1310eb13cecca0c1a948ef1\""
      ]
    }
  }
}
"@
    
    $configContent | Out-File -FilePath $configPath -Encoding UTF8
    Write-Host "   ✓ Arquivo de configuração criado" -ForegroundColor Green
}

Write-Host ""
Write-Host "=== Instalação Concluída ===" -ForegroundColor Green
Write-Host ""
Write-Host "Próximos passos:" -ForegroundColor Yellow
Write-Host "1. Feche completamente o VS Code"
Write-Host "2. Reabra o VS Code"
Write-Host "3. Abra o projeto RLPONTO-WEB"
Write-Host "4. Pressione Ctrl+Shift+P e digite 'MCP' para ver os comandos disponíveis"
Write-Host ""
Write-Host "Para mais informações, consulte: docs/guia-instalacao-mcp.md" -ForegroundColor Cyan
Write-Host ""
Read-Host "Pressione Enter para finalizar"
