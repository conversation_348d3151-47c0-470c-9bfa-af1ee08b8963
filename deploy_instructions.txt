=== DEPLOY CORREÇÃO JAVASCRIPT EMPRESAS ===
Data: 03/07/2025
Sistema: RLPONTO-WEB
Servidor: ************

INSTRUÇÕES PARA DEPLOY MANUAL:

1. CONECTAR AO SERVIDOR:
   ssh root@************
   Senha: @Ric6109

2. CRIAR BACKUP DO ARQUIVO ATUAL:
   cd /var/www/controle-ponto/templates/configuracoes/
   cp empresa_form.html empresa_form_backup_$(date +%Y%m%d_%H%M%S).html

3. VERIFICAR SERVIÇO:
   systemctl status rlponto-web
   # ou
   ps aux | grep python | grep app.py

4. COPIAR ARQUIVO CORRIGIDO:
   # Use WinSCP, FileZilla ou scp para copiar:
   # De: C:\Users\<USER>\OneDrive\Documentos\RLPONTO-WEB\var\www\controle-ponto\templates\configuracoes\empresa_form.html
   # Para: /var/www/controle-ponto/templates/configuracoes/empresa_form.html

5. VERIFICAR PERMISSÕES:
   chown www-data:www-data empresa_form.html
   chmod 644 empresa_form.html

6. REINICIAR SERVIÇO (se necessário):
   systemctl restart rlponto-web
   # ou
   pkill -f 'python.*app.py' && nohup python3 app.py &

PRINCIPAIS CORREÇÕES APLICADAS:

1. Proteção contra elementos nulos:
   if (!feedback) { console.error(...); return; }

2. Correção de inconsistência de IDs:
   mostrarFeedback('razao_social', ...) // era 'razao-social'

3. ID do feedback corrigido:
   id="razao_social-feedback" // era "razao-social-feedback"

COMANDOS DE TESTE APÓS DEPLOY:

1. Testar aplicação web:
   curl -I http://************:5000

2. Verificar logs de erro:
   tail -f /var/log/rlponto-web/app.log

3. Testar página de empresas:
   curl http://************:5000/configuracoes/empresas

TESTE FUNCIONAL:
1. Acesse: http://************:5000
2. Login: admin / @Ric6109
3. Vá para Configurações > Empresas
4. Edite uma empresa
5. Modifique o CNPJ
6. Clique em "Salvar Empresa"
7. Verifique se não há erro JavaScript no console (F12)
