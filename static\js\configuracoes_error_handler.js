/**
 * Tratamento de Erros para Configurações - RLPONTO-WEB
 * Data: 03/07/2025
 * Descrição: Melhora o tratamento de erros HTTP 500 no frontend
 */

// Função para exibir mensagens de erro de forma amigável
function exibirErroAmigavel(mensagem, detalhes = null) {
    // Criar container de erro se não existir
    let containerErro = document.getElementById('erro-sistema-container');
    
    if (!containerErro) {
        containerErro = document.createElement('div');
        containerErro.id = 'erro-sistema-container';
        containerErro.className = 'erro-sistema-overlay';
        
        // Estilo para o overlay
        containerErro.style.position = 'fixed';
        containerErro.style.top = '0';
        containerErro.style.left = '0';
        containerErro.style.width = '100%';
        containerErro.style.height = '100%';
        containerErro.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        containerErro.style.zIndex = '9999';
        containerErro.style.display = 'flex';
        containerErro.style.justifyContent = 'center';
        containerErro.style.alignItems = 'center';
        
        document.body.appendChild(containerErro);
    } else {
        containerErro.innerHTML = '';
        containerErro.style.display = 'flex';
    }
    
    // Criar conteúdo do modal
    const modalConteudo = document.createElement('div');
    modalConteudo.className = 'erro-sistema-modal';
    modalConteudo.style.backgroundColor = 'white';
    modalConteudo.style.borderRadius = '8px';
    modalConteudo.style.padding = '20px';
    modalConteudo.style.maxWidth = '500px';
    modalConteudo.style.width = '90%';
    modalConteudo.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.2)';
    
    // Cabeçalho
    const cabecalho = document.createElement('div');
    cabecalho.style.display = 'flex';
    cabecalho.style.justifyContent = 'space-between';
    cabecalho.style.alignItems = 'center';
    cabecalho.style.marginBottom = '15px';
    
    const titulo = document.createElement('h3');
    titulo.textContent = 'Erro no Sistema';
    titulo.style.margin = '0';
    titulo.style.color = '#dc3545';
    
    const btnFechar = document.createElement('button');
    btnFechar.innerHTML = '&times;';
    btnFechar.style.background = 'none';
    btnFechar.style.border = 'none';
    btnFechar.style.fontSize = '24px';
    btnFechar.style.cursor = 'pointer';
    btnFechar.style.padding = '0 5px';
    btnFechar.onclick = () => {
        containerErro.style.display = 'none';
    };
    
    cabecalho.appendChild(titulo);
    cabecalho.appendChild(btnFechar);
    
    // Corpo
    const corpo = document.createElement('div');
    
    const mensagemErro = document.createElement('p');
    mensagemErro.textContent = mensagem;
    mensagemErro.style.marginBottom = '15px';
    corpo.appendChild(mensagemErro);
    
    // Se houver detalhes, adicionar seção expandível
    if (detalhes) {
        const detalhesContainer = document.createElement('div');
        detalhesContainer.style.marginTop = '10px';
        
        const detalhesToggle = document.createElement('button');
        detalhesToggle.textContent = 'Mostrar Detalhes Técnicos';
        detalhesToggle.style.background = '#f8f9fa';
        detalhesToggle.style.border = '1px solid #dee2e6';
        detalhesToggle.style.borderRadius = '4px';
        detalhesToggle.style.padding = '5px 10px';
        detalhesToggle.style.cursor = 'pointer';
        
        const detalhesConteudo = document.createElement('pre');
        detalhesConteudo.textContent = JSON.stringify(detalhes, null, 2);
        detalhesConteudo.style.backgroundColor = '#f8f9fa';
        detalhesConteudo.style.padding = '10px';
        detalhesConteudo.style.borderRadius = '4px';
        detalhesConteudo.style.overflowX = 'auto';
        detalhesConteudo.style.marginTop = '10px';
        detalhesConteudo.style.display = 'none';
        
        detalhesToggle.onclick = () => {
            if (detalhesConteudo.style.display === 'none') {
                detalhesConteudo.style.display = 'block';
                detalhesToggle.textContent = 'Ocultar Detalhes Técnicos';
            } else {
                detalhesConteudo.style.display = 'none';
                detalhesToggle.textContent = 'Mostrar Detalhes Técnicos';
            }
        };
        
        detalhesContainer.appendChild(detalhesToggle);
        detalhesContainer.appendChild(detalhesConteudo);
        corpo.appendChild(detalhesContainer);
    }
    
    // Rodapé com botões
    const rodape = document.createElement('div');
    rodape.style.marginTop = '20px';
    rodape.style.display = 'flex';
    rodape.style.justifyContent = 'flex-end';
    
    const btnOk = document.createElement('button');
    btnOk.textContent = 'OK';
    btnOk.style.backgroundColor = '#6f42c1';
    btnOk.style.color = 'white';
    btnOk.style.border = 'none';
    btnOk.style.borderRadius = '4px';
    btnOk.style.padding = '8px 16px';
    btnOk.style.cursor = 'pointer';
    btnOk.onclick = () => {
        containerErro.style.display = 'none';
    };
    
    const btnRecarregar = document.createElement('button');
    btnRecarregar.textContent = 'Recarregar Página';
    btnRecarregar.style.backgroundColor = '#28a745';
    btnRecarregar.style.color = 'white';
    btnRecarregar.style.border = 'none';
    btnRecarregar.style.borderRadius = '4px';
    btnRecarregar.style.padding = '8px 16px';
    btnRecarregar.style.marginLeft = '10px';
    btnRecarregar.style.cursor = 'pointer';
    btnRecarregar.onclick = () => {
        window.location.reload();
    };
    
    rodape.appendChild(btnOk);
    rodape.appendChild(btnRecarregar);
    
    // Montar o modal
    modalConteudo.appendChild(cabecalho);
    modalConteudo.appendChild(corpo);
    modalConteudo.appendChild(rodape);
    
    containerErro.appendChild(modalConteudo);
}

// Interceptar erros AJAX para melhor tratamento
function configurarInterceptadorAjax() {
    // Salvar a referência original do XMLHttpRequest
    const XHROriginal = window.XMLHttpRequest;
    
    // Substituir com versão personalizada
    window.XMLHttpRequest = function() {
        const xhr = new XHROriginal();
        
        // Interceptar o método open para capturar a URL
        const open = xhr.open;
        xhr.open = function() {
            xhr.requestURL = arguments[1];
            return open.apply(this, arguments);
        };
        
        // Interceptar o evento de erro
        xhr.addEventListener('load', function() {
            if (this.status === 500) {
                console.error(`Erro 500 em requisição para ${xhr.requestURL}`);
                
                try {
                    const resposta = JSON.parse(this.responseText);
                    
                    // Tratamento específico para erro de exclusão de empresa
                    if (xhr.requestURL.includes('/empresas/') && xhr.requestURL.includes('/excluir')) {
                        exibirErroAmigavel(
                            'Ocorreu um erro ao tentar excluir a empresa. ' +
                            'A operação não pôde ser concluída devido a um problema no servidor.',
                            resposta
                        );
                    } else {
                        exibirErroAmigavel('Ocorreu um erro interno no servidor.', resposta);
                    }
                } catch (e) {
                    exibirErroAmigavel('Ocorreu um erro interno no servidor.');
                }
            }
        });
        
        return xhr;
    };
}

// Melhorar a função de confirmação de exclusão de empresa
function confirmarExclusaoEmpresa(empresaId, empresaNome) {
    const message = `Tem certeza que deseja excluir a empresa "${empresaNome}"? Esta ação não poderá ser desfeita.`;

    if (confirm(message)) {
        // Exibir indicador de carregamento
        const loadingOverlay = document.createElement('div');
        loadingOverlay.style.position = 'fixed';
        loadingOverlay.style.top = '0';
        loadingOverlay.style.left = '0';
        loadingOverlay.style.width = '100%';
        loadingOverlay.style.height = '100%';
        loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
        loadingOverlay.style.zIndex = '9998';
        loadingOverlay.style.display = 'flex';
        loadingOverlay.style.justifyContent = 'center';
        loadingOverlay.style.alignItems = 'center';
        
        const spinner = document.createElement('div');
        spinner.className = 'spinner-border text-light';
        spinner.setAttribute('role', 'status');
        
        const spinnerText = document.createElement('span');
        spinnerText.className = 'sr-only';
        spinnerText.textContent = 'Carregando...';
        
        spinner.appendChild(spinnerText);
        loadingOverlay.appendChild(spinner);
        document.body.appendChild(loadingOverlay);
        
        // Fazer requisição AJAX em vez de submeter formulário
        fetch(`/configuracoes/empresas/${empresaId}/excluir`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({ is_ajax: true })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Erro ao excluir empresa');
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Sucesso - recarregar a página ou atualizar a lista
                window.location.reload();
            } else {
                // Erro com mensagem do servidor
                exibirErroAmigavel(data.error || 'Erro ao excluir empresa');
            }
        })
        .catch(error => {
            // Erro de rede ou outro erro
            exibirErroAmigavel(`Erro ao excluir empresa: ${error.message}`);
        })
        .finally(() => {
            // Remover overlay de carregamento
            document.body.removeChild(loadingOverlay);
        });
    }
}

// Inicializar quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    configurarInterceptadorAjax();
    
    // Substituir a função global se existir
    if (typeof window.confirmarExclusaoEmpresa === 'function') {
        window.confirmarExclusaoEmpresa = confirmarExclusaoEmpresa;
    }
    
    console.log('Handler de erros para configurações inicializado');
}); 