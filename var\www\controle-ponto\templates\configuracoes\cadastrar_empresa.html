{% extends "base.html" %}

{% block title %}Cadastrar Nova Empresa{% endblock %}

{% block head %}
<style>
/* ===== INSPIRAÇÃO @21st-dev/magic: FORMULÁRIO MODERNO ===== */

.empresa-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

.empresa-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 800px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.empresa-header {
    background: linear-gradient(135deg, #4fbdba 0%, #3b82f6 100%);
    color: white;
    padding: 3rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.empresa-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empresa-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    position: relative;
    z-index: 2;
}

.empresa-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    position: relative;
    z-index: 2;
}

.empresa-form {
    padding: 3rem 2rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-input {
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.form-input:focus {
    outline: none;
    border-color: #4fbdba;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    transform: translateY(-2px);
}

.form-input:hover {
    border-color: #d1d5db;
    background: white;
}

.file-input-wrapper {
    position: relative;
    overflow: hidden;
    display: inline-block;
    width: 100%;
}

.file-input {
    position: absolute;
    left: -9999px;
}

.file-input-label {
    display: block;
    padding: 1rem;
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.file-input-label:hover {
    border-color: #4fbdba;
    background: rgba(79, 189, 186, 0.05);
}

.btn-salvar {
    background: linear-gradient(135deg, #4fbdba 0%, #3b82f6 100%);
    color: white;
    padding: 1.25rem 3rem;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 1rem;
    position: relative;
    overflow: hidden;
}

.btn-salvar:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(79, 189, 186, 0.3);
}

.btn-salvar:active {
    transform: translateY(0);
}

.btn-voltar {
    background: transparent;
    color: #6b7280;
    padding: 1rem 2rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-right: 1rem;
}

.btn-voltar:hover {
    border-color: #d1d5db;
    background: #f9fafb;
    color: #374151;
    text-decoration: none;
}

.alert {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.alert-success {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

@media (max-width: 768px) {
    .empresa-container {
        padding: 1rem;
    }
    
    .empresa-header h1 {
        font-size: 2rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .empresa-form {
        padding: 2rem 1.5rem;
    }
}

/* Animação de entrada */
.empresa-card {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="empresa-container">
    <div class="empresa-card">
        <div class="empresa-header">
            <h1>🏢 Nova Empresa</h1>
            <p>Cadastre uma nova empresa no sistema RLPONTO-WEB</p>
        </div>
        
        <div class="empresa-form">
            <!-- Mensagens Flash -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'error' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST" action="{{ url_for('configuracoes.salvar_empresa') }}" enctype="multipart/form-data">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="razao_social">Razão Social *</label>
                        <input type="text" 
                               id="razao_social" 
                               name="razao_social" 
                               class="form-input" 
                               placeholder="Ex: AiNexus Tecnologia Ltda"
                               required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="nome_fantasia">Nome Fantasia *</label>
                        <input type="text" 
                               id="nome_fantasia" 
                               name="nome_fantasia" 
                               class="form-input" 
                               placeholder="Ex: AiNexus"
                               required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="cnpj">CNPJ *</label>
                        <input type="text" 
                               id="cnpj" 
                               name="cnpj" 
                               class="form-input" 
                               placeholder="00.000.000/0000-00"
                               maxlength="18"
                               required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="telefone">Telefone</label>
                        <input type="tel" 
                               id="telefone" 
                               name="telefone" 
                               class="form-input" 
                               placeholder="(11) 99999-9999">
                    </div>
                    
                    <div class="form-group full-width">
                        <label class="form-label" for="email">E-mail</label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               class="form-input" 
                               placeholder="<EMAIL>">
                    </div>
                    
                    <div class="form-group full-width">
                        <label class="form-label">Logotipo da Empresa</label>
                        <div class="file-input-wrapper">
                            <input type="file" 
                                   id="logotipo" 
                                   name="logotipo" 
                                   class="file-input"
                                   accept="image/jpeg,image/png,image/gif">
                            <label for="logotipo" class="file-input-label">
                                📷 Clique aqui para selecionar o logotipo<br>
                                <small>Formatos: JPEG, PNG, GIF (máx. 5MB)</small>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 1rem; margin-top: 2rem;">
                    <a href="{{ url_for('configuracoes.index') }}" class="btn-voltar">
                        ← Voltar
                    </a>
                    
                    <button type="submit" class="btn-salvar" style="flex: 1; max-width: 300px;">
                        💾 Salvar Empresa
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Máscara para CNPJ
document.getElementById('cnpj').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    value = value.replace(/(\d{2})(\d)/, '$1.$2');
    value = value.replace(/(\d{3})(\d)/, '$1.$2');
    value = value.replace(/(\d{3})(\d)/, '$1/$2');
    value = value.replace(/(\d{4})(\d)/, '$1-$2');
    e.target.value = value;
});

// Máscara para telefone
document.getElementById('telefone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    value = value.replace(/(\d{2})(\d)/, '($1) $2');
    value = value.replace(/(\d{5})(\d)/, '$1-$2');
    e.target.value = value;
});

// Preview do arquivo de logotipo
document.getElementById('logotipo').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const label = document.querySelector('.file-input-label');
    
    if (file) {
        if (file.size > 5 * 1024 * 1024) {
            alert('Arquivo muito grande! Máximo permitido: 5MB');
            e.target.value = '';
            return;
        }
        
        label.innerHTML = `✅ ${file.name}<br><small>Arquivo selecionado (${(file.size/1024/1024).toFixed(2)}MB)</small>`;
        label.style.borderColor = '#4fbdba';
        label.style.background = 'rgba(79, 189, 186, 0.1)';
    } else {
        label.innerHTML = '📷 Clique aqui para selecionar o logotipo<br><small>Formatos: JPEG, PNG, GIF (máx. 5MB)</small>';
        label.style.borderColor = '#d1d5db';
        label.style.background = '#f9fafb';
    }
});
</script>
{% endblock %}