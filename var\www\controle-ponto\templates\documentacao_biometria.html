<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentação - Integração Biométrica ZK4500</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
        }
        h3 {
            color: #3498db;
        }
        code {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            padding: 2px 5px;
            font-size: 0.9em;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            overflow-x: auto;
        }
        .note {
            background-color: #e7f5fe;
            border-left: 4px solid #3498db;
            padding: 10px;
            margin: 15px 0;
        }
        .warning {
            background-color: #fff5e6;
            border-left: 4px solid #e67e22;
            padding: 10px;
            margin: 15px 0;
        }
        .error {
            background-color: #fee;
            border-left: 4px solid #e74c3c;
            padding: 10px;
            margin: 15px 0;
        }
        .success {
            background-color: #e7ffe6;
            border-left: 4px solid #2ecc71;
            padding: 10px;
            margin: 15px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        .step {
            margin-bottom: 20px;
            padding-left: 20px;
            border-left: 3px solid #3498db;
        }
        .step-number {
            font-weight: bold;
            color: #3498db;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Documentação - Integração Biométrica ZK4500</h1>
        
        <h2>Visão Geral</h2>
        <p>
            Esta documentação descreve a implementação da captura biométrica usando o leitor ZK4500 diretamente pelo navegador web no Windows.
            A solução permite que usuários registrem impressões digitais durante o cadastro de funcionários, usando uma arquitetura cliente-servidor
            que conecta o navegador ao dispositivo USB.
        </p>
        
        <div class="note">
            <strong>Nota:</strong> Esta solução foi projetada especificamente para o ambiente Windows, pois o leitor ZK4500 
            requer drivers e SDKs específicos do Windows.
        </div>
        
        <h2>Arquitetura da Solução</h2>
        <p>
            A solução utiliza uma arquitetura em três camadas:
        </p>
        <ol>
            <li><strong>Frontend (Navegador)</strong>: Interface de usuário em HTML/CSS/JavaScript que permite a interação com o leitor biométrico.</li>
            <li><strong>Bridge (Serviço Local)</strong>: Aplicativo Python que roda localmente no Windows e se comunica com o leitor ZK4500 via SDK nativo.</li>
            <li><strong>Backend (Servidor Web)</strong>: Aplicação Flask que recebe e armazena os templates biométricos no banco de dados.</li>
        </ol>
        
        <p>
            A comunicação entre o frontend e o bridge local é feita via WebSocket, permitindo comunicação bidirecional em tempo real.
        </p>
        
        <h2>Componentes da Solução</h2>
        
        <h3>1. Frontend (cadastrar_biometria.html)</h3>
        <p>
            Página HTML com JavaScript que implementa:
        </p>
        <ul>
            <li>Formulário de cadastro de funcionários</li>
            <li>Modal para captura de biometria</li>
            <li>Botão "X" para fechar o modal</li>
            <li>Seleção de dedos (Dedo 1 e Dedo 2)</li>
            <li>Visualização da qualidade da impressão digital</li>
            <li>Botões para capturar, limpar e salvar biometrias</li>
        </ul>
        
        <h3>2. Cliente JavaScript (biometria-service.js)</h3>
        <p>
            Biblioteca JavaScript que gerencia:
        </p>
        <ul>
            <li>Conexão WebSocket com o serviço local</li>
            <li>Captura de impressões digitais</li>
            <li>Verificação de qualidade</li>
            <li>Comparação de templates</li>
            <li>Modo de simulação para testes sem hardware</li>
        </ul>
        
        <h3>3. Serviço Bridge (zk4500_bridge.py)</h3>
        <p>
            Aplicativo Python que:
        </p>
        <ul>
            <li>Roda como serviço local no Windows</li>
            <li>Expõe uma API WebSocket na porta 8765</li>
            <li>Comunica-se com o leitor ZK4500 via DLL nativa</li>
            <li>Captura impressões digitais e extrai templates</li>
            <li>Verifica qualidade e realiza comparações</li>
            <li>Inclui ícone na bandeja do sistema para fácil gerenciamento</li>
        </ul>
        
        <h2>Requisitos</h2>
        
        <h3>Requisitos de Hardware</h3>
        <ul>
            <li>Leitor biométrico ZK4500 conectado via USB</li>
            <li>Computador com Windows 7 ou superior</li>
        </ul>
        
        <h3>Requisitos de Software</h3>
        <ul>
            <li>Windows 7/8/10/11</li>
            <li>Python 3.6 ou superior</li>
            <li>Navegador moderno (Chrome, Firefox, Edge)</li>
            <li>Drivers ZK4500 instalados</li>
            <li>SDK ZK4500 instalado</li>
        </ul>
        
        <h3>Dependências Python</h3>
        <ul>
            <li>websockets</li>
            <li>pywin32 (para integração com Windows)</li>
        </ul>
        
        <h2>Instalação e Configuração</h2>
        
        <div class="step">
            <p><span class="step-number">1.</span> <strong>Instale os drivers e SDK do ZK4500</strong></p>
            <p>
                Baixe e instale os drivers oficiais do ZK4500 do site do fabricante ou do CD que acompanha o dispositivo.
                Certifique-se de que o dispositivo é reconhecido pelo Windows após a instalação.
            </p>
        </div>
        
        <div class="step">
            <p><span class="step-number">2.</span> <strong>Instale o Python e as dependências</strong></p>
            <pre>pip install websockets pywin32</pre>
        </div>
        
        <div class="step">
            <p><span class="step-number">3.</span> <strong>Configure o serviço bridge</strong></p>
            <p>
                Copie o arquivo <code>zk4500_bridge.py</code> para uma pasta de sua escolha (ex: <code>C:\ZK4500Bridge</code>).
                Certifique-se de que o arquivo <code>zkfp.dll</code> está acessível (normalmente instalado em <code>C:\Program Files\ZKTeco\ZK4500</code>).
            </p>
        </div>
        
        <div class="step">
            <p><span class="step-number">4.</span> <strong>Configure o serviço para iniciar com o Windows (opcional)</strong></p>
            <p>
                Crie um atalho para <code>zk4500_bridge.py</code> na pasta de inicialização do Windows:
                <code>C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup</code>
            </p>
        </div>
        
        <div class="step">
            <p><span class="step-number">5.</span> <strong>Atualize os arquivos do sistema web</strong></p>
            <p>
                Copie os arquivos <code>cadastrar_biometria.html</code> e <code>biometria-service.js</code> para o servidor web:
            </p>
            <ul>
                <li>Substitua <code>/var/www/controle-ponto/templates/cadastrar.html</code> pelo conteúdo de <code>cadastrar_biometria.html</code></li>
                <li>Copie <code>biometria-service.js</code> para <code>/var/www/controle-ponto/static/</code></li>
                <li>Crie a pasta <code>/var/www/controle-ponto/static/images/</code> e adicione as imagens necessárias</li>
            </ul>
        </div>
        
        <div class="step">
            <p><span class="step-number">6.</span> <strong>Reinicie o serviço web</strong></p>
            <pre>sudo systemctl restart controle-ponto.service</pre>
        </div>
        
        <h2>Uso</h2>
        
        <h3>Iniciando o Serviço Bridge</h3>
        <ol>
            <li>Conecte o leitor ZK4500 ao computador via USB</li>
            <li>Execute o arquivo <code>zk4500_bridge.py</code> (duplo clique ou via linha de comando: <code>python zk4500_bridge.py</code>)</li>
            <li>Um ícone aparecerá na bandeja do sistema indicando que o serviço está em execução</li>
        </ol>
        
        <h3>Capturando Biometrias</h3>
        <ol>
            <li>Acesse a página de cadastro de funcionários no navegador</li>
            <li>Preencha os dados do funcionário normalmente</li>
            <li>Na seção "Equipamentos/EPIs", clique no botão "Capturar Biometria"</li>
            <li>No modal que aparece, selecione "Dedo 1"</li>
            <li>Clique em "Capturar" e posicione o dedo no leitor</li>
            <li>Após a captura bem-sucedida, selecione "Dedo 2"</li>
            <li>Clique em "Capturar" novamente e posicione o segundo dedo</li>
            <li>Clique em "Salvar Biometria" para armazenar os templates no formulário</li>
            <li>Complete o cadastro normalmente</li>
        </ol>
        
        <h2>Solução de Problemas</h2>
        
        <h3>O serviço bridge não inicia</h3>
        <div class="error">
            <p><strong>Problema:</strong> O serviço Python não inicia ou apresenta erros.</p>
            <p><strong>Soluções:</strong></p>
            <ul>
                <li>Verifique se o Python está instalado corretamente: <code>python --version</code></li>
                <li>Verifique se as dependências estão instaladas: <code>pip list</code></li>
                <li>Verifique os logs em <code>C:\Logs\controle-ponto\biometria.log</code></li>
                <li>Certifique-se de que a DLL do ZK4500 está acessível</li>
            </ul>
        </div>
        
        <h3>O navegador não consegue conectar ao serviço</h3>
        <div class="error">
            <p><strong>Problema:</strong> O frontend mostra "Falha na conexão com o serviço".</p>
            <p><strong>Soluções:</strong></p>
            <ul>
                <li>Verifique se o serviço bridge está em execução</li>
                <li>Verifique se a porta 8765 está disponível e não bloqueada pelo firewall</li>
                <li>Tente reiniciar o serviço bridge</li>
                <li>Verifique se o navegador suporta WebSockets</li>
            </ul>
        </div>
        
        <h3>O leitor não é detectado</h3>
        <div class="error">
            <p><strong>Problema:</strong> O serviço bridge não detecta o leitor ZK4500.</p>
            <p><strong>Soluções:</strong></p>
            <ul>
                <li>Verifique se o leitor está conectado corretamente via USB</li>
                <li>Verifique se os drivers estão instalados corretamente</li>
                <li>Tente desconectar e reconectar o dispositivo</li>
                <li>Verifique se o dispositivo aparece no Gerenciador de Dispositivos do Windows</li>
            </ul>
        </div>
        
        <h3>Falha na captura da impressão digital</h3>
        <div class="error">
            <p><strong>Problema:</strong> O processo de captura falha ou a qualidade é muito baixa.</p>
            <p><strong>Soluções:</strong></p>
            <ul>
                <li>Limpe o sensor do leitor com um pano macio e seco</li>
                <li>Certifique-se de que o dedo está posicionado corretamente</li>
                <li>Tente usar outro dedo</li>
                <li>Verifique se o dedo está limpo e seco</li>
            </ul>
        </div>
        
        <h2>Modo de Simulação</h2>
        <p>
            A solução inclui um modo de simulação que permite testar o fluxo de captura biométrica sem o hardware físico.
            Este modo é ativado automaticamente quando:
        </p>
        <ul>
            <li>O serviço bridge não está em execução</li>
            <li>O leitor ZK4500 não está conectado</li>
            <li>A DLL do ZK4500 não está disponível</li>
        </ul>
        
        <p>
            No modo de simulação, o sistema gera templates aleatórios e simula o processo de captura, permitindo testar
            toda a interface e fluxo de trabalho.
        </p>
        
        <div class="note">
            <strong>Nota:</strong> O modo de simulação é apenas para testes e desenvolvimento. Para uso em produção,
            é necessário o hardware físico e o serviço bridge em execução.
        </div>
        
        <h2>Segurança</h2>
        <p>
            A solução implementa as seguintes medidas de segurança:
        </p>
        <ul>
            <li>O serviço bridge escuta apenas em localhost (127.0.0.1), não expondo a API para a rede</li>
            <li>Os templates biométricos são transmitidos via WebSocket criptografado (wss://)</li>
            <li>Os templates são armazenados no banco de dados em formato binário</li>
            <li>O acesso ao serviço bridge é restrito ao usuário local</li>
        </ul>
        
        <h2>Limitações Conhecidas</h2>
        <ul>
            <li>A solução funciona apenas no Windows devido às dependências do SDK ZK4500</li>
            <li>É necessário instalar e configurar o serviço bridge em cada computador que utilizará o leitor</li>
            <li>Alguns navegadores podem bloquear WebSockets não seguros (http:// em vez de https://)</li>
            <li>O serviço bridge precisa ser reiniciado se o leitor for desconectado e reconectado</li>
        </ul>
        
        <h2>Suporte e Manutenção</h2>
        <p>
            Para suporte técnico ou dúvidas sobre a implementação, entre em contato com a equipe de desenvolvimento.
        </p>
        
        <div class="success">
            <p>
                Esta solução foi desenvolvida para atender às necessidades específicas do sistema de Controle de Ponto,
                permitindo a captura de biometria diretamente pelo navegador web no Windows.
            </p>
        </div>
    </div>
</body>
</html>
