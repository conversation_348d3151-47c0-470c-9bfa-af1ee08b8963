#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste para verificar se o módulo app_empresa_principal pode ser importado
"""

import sys
import os

# Adicionar o diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Tentando importar app_empresa_principal...")
    import app_empresa_principal
    print("✅ Módulo app_empresa_principal importado com sucesso!")
    
    # Verificar se o blueprint existe
    if hasattr(app_empresa_principal, 'empresa_principal_bp'):
        print("✅ Blueprint empresa_principal_bp encontrado!")
        print(f"Blueprint URL prefix: {app_empresa_principal.empresa_principal_bp.url_prefix}")
    else:
        print("❌ Blueprint empresa_principal_bp não encontrado!")
        
except ImportError as e:
    print(f"❌ Erro ao importar app_empresa_principal: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ Erro geral: {e}")
    import traceback
    traceback.print_exc()
