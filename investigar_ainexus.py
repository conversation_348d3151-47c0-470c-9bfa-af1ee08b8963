#!/usr/bin/env python3
"""
Investigar configuração da jornada da empresa Ainexus Tecnologia
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def investigar_ainexus():
    """Investigar configuração da Ainexus"""
    try:
        from utils.database import DatabaseManager
        from app_funcionarios import get_jornada_padrao_empresa
        import logging
        
        logging.basicConfig(level=logging.INFO)
        
        print("🔍 INVESTIGANDO JORNADA DA AINEXUS TECNOLOGIA")
        print("=" * 60)
        
        # Buscar empresa Ainexus
        empresa = DatabaseManager.execute_query("""
            SELECT id, nome_fantasia, razao_social
            FROM empresas
            WHERE nome_fantasia LIKE %s OR nome_fantasia LIKE %s
            LIMIT 1
        """, ('%Ainexus%', '%ainexus%'), fetch_one=True)
        
        if not empresa:
            print("❌ Empresa Ainexus não encontrada")
            return
            
        empresa_id = empresa['id']
        print(f"🏢 Empresa: {empresa['nome_fantasia']} (ID: {empresa_id})")
        
        # 1. Verificar configuração na tabela empresas_config
        print("\n1. CONFIGURAÇÃO EMPRESAS_CONFIG:")
        print("-" * 40)
        config = DatabaseManager.execute_query("""
            SELECT 
                jornada_segunda_entrada,
                jornada_segunda_saida_almoco,
                jornada_segunda_entrada_almoco,
                jornada_segunda_saida,
                jornada_sexta_entrada,
                jornada_sexta_saida_almoco,
                jornada_sexta_entrada_almoco,
                jornada_sexta_saida,
                tolerancia_empresa_minutos,
                intervalo_obrigatorio
            FROM empresas_config 
            WHERE empresa_id = %s
        """, (empresa_id,), fetch_one=True)
        
        if config:
            print("✅ Configuração encontrada:")
            for key, value in config.items():
                print(f"   {key}: {value}")
        else:
            print("❌ Nenhuma configuração encontrada em empresas_config")
        
        # 2. Verificar jornadas na tabela horarios_trabalho
        print("\n2. JORNADAS EM HORARIOS_TRABALHO:")
        print("-" * 40)
        jornadas = DatabaseManager.execute_query("""
            SELECT id, nome_horario, entrada_manha, saida_almoco, entrada_tarde, saida, tolerancia_minutos, ativo
            FROM horarios_trabalho 
            WHERE empresa_id = %s
            ORDER BY id
        """, (empresa_id,))
        
        if jornadas:
            for jornada in jornadas:
                print(f"📋 Jornada ID {jornada['id']}: {jornada['nome_horario']}")
                print(f"   Segunda-Quinta: {jornada['entrada_manha']} - {jornada['saida']} (Almoço: {jornada['saida_almoco']} - {jornada['entrada_tarde']})")
                print(f"   Tolerância: {jornada['tolerancia_minutos']} min | Ativo: {jornada['ativo']}")
        else:
            print("❌ Nenhuma jornada encontrada em horarios_trabalho")
        
        # 3. Testar função get_jornada_padrao_empresa
        print("\n3. TESTANDO FUNÇÃO get_jornada_padrao_empresa:")
        print("-" * 50)
        jornada_resultado = get_jornada_padrao_empresa(empresa_id)
        if jornada_resultado:
            print("✅ Resultado da função:")
            for key, value in jornada_resultado.items():
                print(f"   {key}: {value}")
        else:
            print("❌ Função retornou None")
        
        # 4. Verificar funcionários da empresa
        print("\n4. FUNCIONÁRIOS DA EMPRESA:")
        print("-" * 40)
        funcionarios = DatabaseManager.execute_query("""
            SELECT id, nome_completo, horario_trabalho_id, usa_horario_empresa,
                   jornada_seg_qui_entrada, jornada_seg_qui_saida,
                   jornada_sex_entrada, jornada_sex_saida,
                   jornada_intervalo_entrada, jornada_intervalo_saida,
                   tolerancia_ponto
            FROM funcionarios 
            WHERE empresa_id = %s AND ativo = 1
            LIMIT 3
        """, (empresa_id,))
        
        if funcionarios:
            for func in funcionarios:
                print(f"👤 {func['nome_completo']} (ID: {func['id']})")
                print(f"   horario_trabalho_id: {func['horario_trabalho_id']}")
                print(f"   usa_horario_empresa: {func['usa_horario_empresa']}")
                print(f"   Jornada individual:")
                print(f"     Seg-Qui: {func['jornada_seg_qui_entrada']} - {func['jornada_seg_qui_saida']}")
                print(f"     Sexta: {func['jornada_sex_entrada']} - {func['jornada_sex_saida']}")
                print(f"     Almoço: {func['jornada_intervalo_entrada']} - {func['jornada_intervalo_saida']}")
                print(f"     Tolerância: {func['tolerancia_ponto']} min")
                print()
        else:
            print("❌ Nenhum funcionário encontrado")
        
        # 5. Verificar se há divergência
        print("\n5. ANÁLISE DE DIVERGÊNCIA:")
        print("-" * 40)
        
        if config and funcionarios:
            func = funcionarios[0]  # Primeiro funcionário
            
            print("📊 COMPARAÇÃO:")
            print(f"   EMPRESA CONFIG vs FUNCIONÁRIO:")
            print(f"   Segunda entrada: {config['jornada_segunda_entrada']} vs {func['jornada_seg_qui_entrada']}")
            print(f"   Segunda saída: {config['jornada_segunda_saida']} vs {func['jornada_seg_qui_saida']}")
            print(f"   Sexta entrada: {config['jornada_sexta_entrada']} vs {func['jornada_sex_entrada']}")
            print(f"   Sexta saída: {config['jornada_sexta_saida']} vs {func['jornada_sex_saida']}")
            print(f"   Tolerância: {config['tolerancia_empresa_minutos']} vs {func['tolerancia_ponto']}")
            
            # Verificar se há divergência
            divergencias = []
            if str(config['jornada_segunda_entrada']) != str(func['jornada_seg_qui_entrada']):
                divergencias.append("Segunda entrada")
            if str(config['jornada_segunda_saida']) != str(func['jornada_seg_qui_saida']):
                divergencias.append("Segunda saída")
            if str(config['jornada_sexta_entrada']) != str(func['jornada_sex_entrada']):
                divergencias.append("Sexta entrada")
            if str(config['jornada_sexta_saida']) != str(func['jornada_sex_saida']):
                divergencias.append("Sexta saída")
            if config['tolerancia_empresa_minutos'] != func['tolerancia_ponto']:
                divergencias.append("Tolerância")
            
            if divergencias:
                print(f"\n🚨 DIVERGÊNCIAS ENCONTRADAS: {', '.join(divergencias)}")
                print("🔧 O funcionário NÃO está usando a jornada da empresa!")
                return False
            else:
                print("\n✅ Funcionário está usando a jornada da empresa corretamente")
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ Erro durante investigação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    investigar_ainexus()
