#!/usr/bin/env python3
"""
Script para verificar endpoints das APIs
"""

import sys
sys.path.append('/var/www/controle-ponto')

def check_endpoints():
    from app import app
    
    print("🔍 Verificando endpoints das APIs...")
    print("=" * 60)
    
    api_endpoints = []
    all_endpoints = []
    
    with app.app_context():
        for rule in app.url_map.iter_rules():
            all_endpoints.append(f"{rule.endpoint} -> {rule.rule}")
            
            if ('api' in rule.endpoint.lower() or 
                'buscar' in rule.endpoint.lower() or 
                'dados' in rule.endpoint.lower() or
                'validar' in rule.endpoint.lower() or
                'registrar' in rule.endpoint.lower()):
                api_endpoints.append(f"{rule.endpoint} -> {rule.rule}")
    
    print("📋 Endpoints relacionados a APIs:")
    for endpoint in sorted(api_endpoints):
        print(f"  {endpoint}")
    
    print(f"\n📊 Total de endpoints API: {len(api_endpoints)}")
    print(f"📊 Total de endpoints: {len(all_endpoints)}")
    
    # Salvar todos os endpoints em arquivo
    with open('/tmp/all_endpoints.txt', 'w') as f:
        for endpoint in sorted(all_endpoints):
            f.write(f"{endpoint}\n")
    
    print("\n✅ Lista completa de endpoints salva em /tmp/all_endpoints.txt")

if __name__ == "__main__":
    check_endpoints() 