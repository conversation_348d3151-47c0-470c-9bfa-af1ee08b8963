#!/usr/bin/env python3
"""
Teste final da função de restaurar
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

def test_final_restaurar():
    """Teste final via interface web"""
    print("🔍 TESTE FINAL: RESTAURAR VIA INTERFACE WEB")
    print("=" * 60)
    
    try:
        # Configurar sessão
        session = requests.Session()
        retry_strategy = Retry(total=3, backoff_factor=1, status_forcelist=[429, 500, 502, 503, 504])
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        base_url = "http://10.19.208.31:5000"
        
        # 1. Login
        print("📋 1. FAZENDO LOGIN:")
        login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=True)
        
        if login_response.status_code == 200:
            print("   ✅ Login realizado")
        else:
            print(f"   ❌ Falha no login: {login_response.status_code}")
            return False
        
        # 2. Acessar página de funcionários desligados
        print("\n📋 2. ACESSANDO PÁGINA DE FUNCIONÁRIOS DESLIGADOS:")
        desligados_response = session.get(f"{base_url}/funcionarios-desligados/")
        
        if desligados_response.status_code == 200:
            print("   ✅ Página carregada")
            
            # Verificar se há funcionários desligados
            content = desligados_response.text
            if "KALEBE" in content:
                print("   ✅ Kalebe encontrado na lista de desligados")
                
                # 3. Tentar restaurar Kalebe via interface
                print("\n📋 3. RESTAURANDO KALEBE VIA INTERFACE:")
                
                # ID do Kalebe é 32 (conforme visto nos testes anteriores)
                kalebe_id = 32
                
                restaurar_response = session.post(f"{base_url}/funcionarios/restaurar/{kalebe_id}")
                
                print(f"   Status Code: {restaurar_response.status_code}")
                print(f"   Response Headers: {dict(restaurar_response.headers)}")
                
                if restaurar_response.status_code == 200:
                    print("   ✅ Requisição de restauração bem-sucedida")
                    
                    # 4. Verificar se aparece na lista de funcionários
                    print("\n📋 4. VERIFICANDO LISTA DE FUNCIONÁRIOS:")
                    funcionarios_response = session.get(f"{base_url}/funcionarios/")
                    
                    if funcionarios_response.status_code == 200:
                        funcionarios_content = funcionarios_response.text
                        
                        if "KALEBE" in funcionarios_content:
                            print("   ✅ SUCESSO TOTAL! Kalebe aparece na lista de funcionários!")
                            return True
                        else:
                            print("   ❌ Kalebe não aparece na lista de funcionários")
                            
                            # Verificar se há algum funcionário na lista
                            if "Nenhum funcionário encontrado" in funcionarios_content:
                                print("   ❌ Lista de funcionários está vazia")
                            else:
                                print("   ⚠️ Lista tem outros funcionários mas não o Kalebe")
                            
                            return False
                    else:
                        print(f"   ❌ Erro ao carregar lista de funcionários: {funcionarios_response.status_code}")
                        return False
                else:
                    print(f"   ❌ Falha na restauração: {restaurar_response.status_code}")
                    print(f"   Response: {restaurar_response.text[:200]}...")
                    return False
            else:
                print("   ❌ Kalebe não encontrado na lista de desligados")
                return False
        else:
            print(f"   ❌ Erro ao carregar página: {desligados_response.status_code}")
            return False
        
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🎯 TESTE FINAL: FUNÇÃO RESTAURAR FUNCIONÁRIO")
    print("=" * 70)
    
    sucesso = test_final_restaurar()
    
    if sucesso:
        print("\n🎉 SUCESSO TOTAL!")
        print("✅ Função de restaurar funcionário funcionando perfeitamente")
        print("✅ Interface web funcionando corretamente")
        print("✅ Funcionário restaurado aparece na lista de ativos")
    else:
        print("\n❌ FALHA!")
        print("❌ Função de restaurar ainda tem problemas")
        print("❌ Verificar logs e implementação")
