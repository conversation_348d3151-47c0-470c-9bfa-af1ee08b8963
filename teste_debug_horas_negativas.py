#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 DEBUG - HORAS NEGATIVAS NA TABELA
====================================

Verifica por que as horas negativas não estão aparecendo na tabela.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'var', 'www', 'controle-ponto'))

from utils.database import DatabaseManager
from datetime import datetime, date
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger('debug_horas_negativas')

def buscar_registro_17_07():
    """
    Busca o registro específico do dia 17/07/2025 que aparece na imagem
    """
    try:
        db = DatabaseManager()
        
        # Buscar registros do dia 17/07/2025 agrupados por funcionário
        query = """
        SELECT
            pr.funcionario_id,
            f.nome_completo,
            e.razao_social as empresa_nome,
            DATE(pr.data_hora) as data_registro,
            MAX(CASE WHEN pr.tipo_registro = 'entrada_manha' THEN TIME(pr.data_hora) END) as entrada,
            MAX(CASE WHEN pr.tipo_registro = 'saida_almoco' THEN TIME(pr.data_hora) END) as saida_almoco,
            MAX(CASE WHEN pr.tipo_registro = 'entrada_tarde' THEN TIME(pr.data_hora) END) as retorno_almoco,
            MAX(CASE WHEN pr.tipo_registro = 'saida' THEN TIME(pr.data_hora) END) as saida
        FROM registros_ponto pr
        LEFT JOIN funcionarios f ON pr.funcionario_id = f.id
        LEFT JOIN empresas e ON f.empresa_id = e.id
        WHERE DATE(pr.data_hora) = '2025-07-17'
        GROUP BY pr.funcionario_id, f.nome_completo, e.razao_social, DATE(pr.data_hora)
        ORDER BY pr.funcionario_id
        """
        
        resultado = db.execute_query(query)
        
        if resultado:
            print("📋 REGISTROS ENCONTRADOS PARA 17/07/2025:")
            print("-" * 60)
            for reg in resultado:
                print(f"   Funcionário: {reg['nome_completo']} (ID: {reg['funcionario_id']})")
                print(f"   Empresa: {reg['empresa_nome']}")
                print(f"   Data: {reg['data_registro']}")
                print(f"   Entrada: {reg['entrada']}")
                print(f"   Saída Almoço: {reg['saida_almoco']}")
                print(f"   Retorno Almoço: {reg['retorno_almoco']}")
                print(f"   Saída: {reg['saida']}")
                print("-" * 40)

            return resultado
        else:
            print("❌ Nenhum registro encontrado para 17/07/2025")
            return None
            
    except Exception as e:
        logger.error(f"Erro ao buscar registros: {e}")
        return None

def simular_calculo_backend(registro):
    """
    Simula o cálculo que o backend faz para um registro
    """
    try:
        print(f"\n🧮 SIMULANDO CÁLCULO DO BACKEND")
        print("=" * 50)
        
        funcionario_id = registro['funcionario_id']
        nome_funcionario = registro['nome_completo']
        
        print(f"👤 Funcionário: {nome_funcionario} (ID: {funcionario_id})")
        print(f"📅 Data: {registro['data_registro']}")
        print(f"🕐 Entrada: {registro['entrada']}")
        print(f"🕐 Saída Almoço: {registro['saida_almoco']}")
        print(f"🕐 Retorno Almoço: {registro['retorno_almoco']}")
        print(f"🕐 Saída: {registro['saida']}")
        
        # Importar função do backend
        from app_ponto_admin import calcular_horas_separadas_dia, calcular_horas_esperadas_jornada_real
        
        # Converter registro para formato esperado pelo backend
        registro_backend = {
            'funcionario_id': funcionario_id,
            'data_registro': registro['data_registro'],
            'entrada': registro['entrada'],
            'saida_almoco': registro['saida_almoco'],
            'retorno_almoco': registro['retorno_almoco'],
            'saida': registro['saida'],
            'inicio_extra': None,  # Não há horas extras neste registro
            'fim_extra': None
        }
        
        print(f"\n📊 CALCULANDO HORAS...")
        
        # Calcular horas esperadas da jornada
        horas_esperadas = calcular_horas_esperadas_jornada_real(funcionario_id)
        print(f"   Horas esperadas (jornada): {horas_esperadas}h")
        
        # Calcular horas separadas
        horas_normais, horas_extras, horas_negativas = calcular_horas_separadas_dia(registro_backend, funcionario_id)
        
        print(f"\n✅ RESULTADO DO CÁLCULO:")
        print(f"   Horas normais: {horas_normais}h")
        print(f"   Horas extras: {horas_extras}h")
        print(f"   Horas negativas: {horas_negativas}h")
        
        # Verificar se horas negativas > 0
        if horas_negativas > 0:
            print(f"\n🎯 HORAS NEGATIVAS DETECTADAS!")
            print(f"   Valor: {horas_negativas}h")
            print(f"   Condição (horas_negativas > 0): {horas_negativas > 0}")
            print(f"   Deveria aparecer na tabela: SIM ✅")
            
            # Converter para formato HH:MM
            horas_int = int(horas_negativas)
            minutos_int = int((horas_negativas - horas_int) * 60)
            formato_hhmm = f"{horas_int:02d}:{minutos_int:02d}"
            print(f"   Formato HH:MM: -{formato_hhmm}")
        else:
            print(f"\n❌ SEM HORAS NEGATIVAS")
            print(f"   Valor: {horas_negativas}h")
            print(f"   Deveria aparecer na tabela: NÃO")
        
        return {
            'horas_normais': horas_normais,
            'horas_extras': horas_extras,
            'horas_negativas': horas_negativas,
            'horas_esperadas': horas_esperadas
        }
        
    except Exception as e:
        logger.error(f"Erro ao simular cálculo: {e}")
        return None

def verificar_template_condicao():
    """
    Verifica a condição do template que controla a exibição
    """
    print(f"\n🔍 VERIFICANDO CONDIÇÃO DO TEMPLATE")
    print("=" * 50)
    
    print("📋 CONDIÇÃO ATUAL NO TEMPLATE:")
    print("   {% if registro.horas_negativas and registro.horas_negativas > 0 %}")
    print("       <span class=\"badge bg-danger\">-{{ horas }}:{{ minutos }}</span>")
    print("   {% else %}")
    print("       <span class=\"text-muted\">-</span>")
    print("   {% endif %}")
    
    print(f"\n💡 ANÁLISE:")
    print("   ✅ Condição está correta")
    print("   ✅ Se horas_negativas > 0, deveria mostrar badge vermelho")
    print("   ✅ Se horas_negativas = 0, mostra '-'")
    
    print(f"\n🎯 POSSÍVEIS PROBLEMAS:")
    print("   1. Campo 'horas_negativas' não está sendo populado")
    print("   2. Valor está sendo calculado como 0 quando deveria ser > 0")
    print("   3. Template não está recebendo o valor correto")

def main():
    print("🔍 DEBUG - HORAS NEGATIVAS NA TABELA")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    # Buscar registro específico
    registros = buscar_registro_17_07()
    
    if registros:
        # Pegar o primeiro registro (que deve ser o da imagem)
        registro = registros[0]
        
        # Simular cálculo do backend
        resultado = simular_calculo_backend(registro)
        
        if resultado:
            # Verificar condição do template
            verificar_template_condicao()
            
            print(f"\n" + "=" * 60)
            print(f"📊 DIAGNÓSTICO FINAL")
            print(f"=" * 60)
            
            if resultado['horas_negativas'] > 0:
                print(f"✅ CÁLCULO CORRETO:")
                print(f"   Funcionário trabalhou {resultado['horas_normais']}h")
                print(f"   Jornada esperada: {resultado['horas_esperadas']}h")
                print(f"   Deficit: {resultado['horas_negativas']}h")
                print(f"   ✅ DEVERIA APARECER NA TABELA!")
                
                print(f"\n🔧 SE NÃO ESTÁ APARECENDO:")
                print(f"   1. Verificar se o backend está populando o campo")
                print(f"   2. Verificar se o template está recebendo os dados")
                print(f"   3. Limpar cache do navegador")
            else:
                print(f"❌ PROBLEMA NO CÁLCULO:")
                print(f"   Horas negativas calculadas: {resultado['horas_negativas']}h")
                print(f"   Deveria ser > 0 para funcionário com apenas entrada")
    else:
        print(f"❌ Não foi possível encontrar registros para análise")

if __name__ == "__main__":
    main()
