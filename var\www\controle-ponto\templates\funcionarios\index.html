{% extends "base.html" %}

{% block title %}Gestão de Funcionários - Controle de Ponto{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Header Moderno -->
    <div class="modern-header">
        <div class="header-content">
            <div class="header-info">
                <h1>
                    <i class="fas fa-users"></i>
                    Gestão de Funcionários
                </h1>
                <p>Sistema de Controle de Ponto - Lista Completa</p>
            </div>
            <div class="stats-container">
                <div class="stat-card">
                    <span class="stat-number">{{ funcionarios|length if funcionarios else 0 }}</span>
                    <span class="stat-label">Total</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">{{ funcionarios|selectattr('status_cadastro', 'equalto', 'Ativo')|list|length if funcionarios else 0 }}</span>
                    <span class="stat-label">Ativos</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros de busca modernos -->
    <div class="filters-section">
        <form method="GET" action="/funcionarios" class="filters-form">
            <div class="filters-grid">
                <div class="filter-group">
                    <label for="search">Buscar Funcionário</label>
                    <div class="search-input-wrapper">
                        <i class="fas fa-search"></i>
                        <input type="text" id="search" name="search" value="{{ search or '' }}"
                               placeholder="Nome, CPF, cargo...">
                    </div>
                </div>
                <div class="filter-group">
                    <label for="status">Status</label>
                    <select id="status" name="status">
                        <option value="">Todos os Status</option>
                        <option value="Ativo" {% if status == 'Ativo' %}selected{% endif %}>Ativo</option>
                        <option value="Inativo" {% if status == 'Inativo' %}selected{% endif %}>Inativo</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="per_page">Registros por Página</label>
                    <select id="per_page" name="per_page">
                        <option value="10" {% if pagination.per_page == 10 %}selected{% endif %}>10 registros</option>
                        <option value="25" {% if pagination.per_page == 25 %}selected{% endif %}>25 registros</option>
                        <option value="50" {% if pagination.per_page == 50 %}selected{% endif %}>50 registros</option>
                    </select>
                </div>
                <div class="filter-actions">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-filter"></i>
                        Filtrar
                    </button>
                    <a href="/funcionarios/cadastrar" class="btn-success">
                        <i class="fas fa-plus"></i>
                        Novo Funcionário
                    </a>
                </div>
            </div>
        {% if search or status %}
        <div style="margin-top: 10px;">
            <a href="/funcionarios" style="color: #6c757d; font-size: 14px;">Limpar filtros</a>
        </div>
        {% endif %}
    </form>
</div>

<!-- Informações de resultado -->
<div style="margin-bottom: 15px; color: #6c757d; font-size: 14px;">
    Exibindo {{ funcionarios|length }} de {{ pagination.total }} funcionários
    {% if search or status %}
        (filtrado)
    {% endif %}
</div>

    <!-- Grid de Funcionários Moderno -->
    {% if funcionarios %}
    <div id="listaFuncionarios" class="employees-grid">
        {% for funcionario in funcionarios %}
        <div class="employee-card"
             data-status="{{ funcionario.status_cadastro.lower() if funcionario.status_cadastro else 'inativo' }}"
             data-busca="{{ (funcionario.nome_completo + ' ' + (funcionario.cpf or '') + ' ' + (funcionario.cargo or '')).lower() }}">

            <!-- Header do Card -->
            <div class="employee-header">
                <div class="employee-avatar">
                    {{ funcionario.nome_completo[0].upper() if funcionario.nome_completo else 'F' }}
                </div>
                <div class="employee-info">
                    <h3>{{ funcionario.nome_completo }}</h3>
                    <p>{{ funcionario.cargo or 'Cargo não informado' }}</p>
                </div>
                <div class="employee-status">
                    {% if funcionario.status_cadastro == 'Ativo' %}
                        <span class="status-ativo">{{ funcionario.status_cadastro }}</span>
                    {% elif funcionario.status_cadastro == 'Inativo' %}
                        <span class="status-inativo">{{ funcionario.status_cadastro }}</span>
                    {% else %}
                        <span class="status-inativo">{{ funcionario.status_cadastro or 'Inativo' }}</span>
                    {% endif %}
                </div>
            </div>

            <!-- Informações do Card -->
            <div class="employee-details">
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-hashtag"></i>
                        ID:
                    </span>
                    <span class="detail-value">{{ funcionario.id }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-file-alt"></i>
                        CPF:
                    </span>
                    <span class="detail-value">{{ funcionario.cpf | format_cpf if funcionario.cpf else 'Não informado' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-id-card"></i>
                        Matrícula:
                    </span>
                    <span class="detail-value">{{ funcionario.matricula_empresa or 'Não informada' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-industry"></i>
                        Setor:
                    </span>
                    <span class="detail-value">{{ funcionario.setor_obra or 'Não informado' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-calendar-alt"></i>
                        Admissão:
                    </span>
                    <span class="detail-value">{{ funcionario.data_admissao | format_date if funcionario.data_admissao else 'Não informada' }}</span>
                </div>
            </div>

            <!-- Ações do Card -->
            <div class="employee-actions">
                <a href="/funcionarios/{{ funcionario.id }}" class="btn-sm btn-info" title="Ver detalhes">
                    <i class="fas fa-eye"></i>
                    Ver
                </a>
                {% if current_user.is_admin %}
                <a href="/funcionarios/{{ funcionario.id }}/editar" class="btn-sm btn-warning" title="Editar">
                    <i class="fas fa-edit"></i>
                    Editar
                </a>
                <button type="button"
                        class="btn-sm btn-danger"
                        title="Excluir funcionário"
                        data-id="{{ funcionario.id }}"
                        data-nome="{{ funcionario.nome_completo | escape }}"
                        onclick="confirmarExclusao({{ funcionario.id }}, '{{ funcionario.nome_completo | escape }}')">
                    <i class="fas fa-trash"></i>
                    Excluir
                </button>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

<!-- Paginação -->
{% if pagination.total_pages > 1 %}
<div class="pagination">
    <!-- Primeira página -->
    {% if pagination.has_prev %}
        <a href="?page=1{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">&laquo; Primeira</a>
    {% else %}
        <span class="disabled">&laquo; Primeira</span>
    {% endif %}
    
    <!-- Página anterior -->
    {% if pagination.has_prev %}
        <a href="?page={{ pagination.prev_num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">&lt; Anterior</a>
    {% else %}
        <span class="disabled">&lt; Anterior</span>
    {% endif %}
    
    <!-- Páginas numeradas -->
    {% set start_page = pagination.page - 2 if pagination.page > 2 else 1 %}
    {% set end_page = pagination.page + 2 if pagination.page + 2 < pagination.total_pages else pagination.total_pages %}
    
    {% if start_page > 1 %}
        <a href="?page=1{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">1</a>
        {% if start_page > 2 %}
            <span>...</span>
        {% endif %}
    {% endif %}
    
    {% for page_num in range(start_page, end_page + 1) %}
        {% if page_num == pagination.page %}
            <span class="current">{{ page_num }}</span>
        {% else %}
            <a href="?page={{ page_num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">{{ page_num }}</a>
        {% endif %}
    {% endfor %}
    
    {% if end_page < pagination.total_pages %}
        {% if end_page < pagination.total_pages - 1 %}
            <span>...</span>
        {% endif %}
        <a href="?page={{ pagination.total_pages }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">{{ pagination.total_pages }}</a>
    {% endif %}
    
    <!-- Próxima página -->
    {% if pagination.has_next %}
        <a href="?page={{ pagination.next_num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">Próxima &gt;</a>
    {% else %}
        <span class="disabled">Próxima &gt;</span>
    {% endif %}
    
    <!-- Última página -->
    {% if pagination.has_next %}
        <a href="?page={{ pagination.total_pages }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">Última &raquo;</a>
    {% else %}
        <span class="disabled">Última &raquo;</span>
    {% endif %}
</div>

<div style="text-align: center; margin-top: 10px; color: #6c757d; font-size: 14px;">
    Página {{ pagination.page }} de {{ pagination.total_pages }}
</div>
{% endif %}

{% else %}
<!-- Estado vazio -->
<div style="text-align: center; padding: 40px; color: #6c757d;">
    <h3>Nenhum funcionário encontrado</h3>
    {% if search or status %}
        <p>Tente ajustar os filtros de busca.</p>
        <a href="/funcionarios" style="color: #4fbdba;">Limpar filtros</a>
    {% else %}
        <p>Comece cadastrando o primeiro funcionário.</p>
        <a href="/funcionarios/cadastrar" class="btn-sm" style="background: #28a745; color: white; margin-top: 10px;">
            Cadastrar Primeiro Funcionário
        </a>
    {% endif %}
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    /* ===== VARIÁVEIS CSS BASEADAS NO PADRÃO OFICIAL layout-rlponto.md ===== */
    :root {
        --primary-color: #4fbdba;
        --primary-hover: #26a69a;
        --background-color: #f9fafb;
        --card-background: #ffffff;
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --text-muted: #9ca3af;
        --border-color: #e5e7eb;
        --border-light: #f3f4f6;
        --success-color: #10b981;
        --success-bg: #dcfce7;
        --success-text: #166534;
        --warning-color: #f59e0b;
        --warning-bg: #fef3c7;
        --warning-text: #92400e;
        --danger-color: #ef4444;
        --danger-bg: #fee2e2;
        --danger-text: #dc2626;
        --info-color: #3b82f6;
        --info-bg: #dbeafe;
        --info-text: #1e40af;
        --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        --transition-fast: all 0.2s ease;
    }

    /* ===== LAYOUT PRINCIPAL ===== */
    .main-container {
        background-color: var(--background-color);
        min-height: 100vh;
        padding: 2rem;
    }

    /* ===== HEADER MODERNO ===== */
    .modern-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        box-shadow: var(--card-shadow);
    }

    .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .header-info h1 {
        margin: 0;
        font-size: 2rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .header-info p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 1rem;
    }

    /* ===== CARDS DE ESTATÍSTICAS ===== */
    .stats-container {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem;
        text-align: center;
        min-width: 100px;
    }

    .stat-number {
        display: block;
        font-size: 2rem;
        font-weight: 700;
        color: white;
    }

    .stat-label {
        display: block;
        font-size: 0.875rem;
        opacity: 0.8;
        margin-top: 0.25rem;
    }



    /* ===== FILTROS MODERNOS ===== */
    .filters-section {
        background: var(--card-background);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--card-shadow);
    }

    .filters-form {
        width: 100%;
    }

    .filters-grid {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr auto;
        gap: 1rem;
        align-items: end;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-group label {
        font-weight: 500;
        color: var(--text-primary);
        font-size: 0.875rem;
    }

    .search-input-wrapper {
        position: relative;
    }

    .search-input-wrapper i {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
    }

    .search-input-wrapper input {
        padding-left: 40px;
    }

    .filter-group input,
    .filter-group select {
        padding: 12px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-size: 14px;
        transition: var(--transition-fast);
        background: white;
    }

    .filter-group input:focus,
    .filter-group select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    }

    .filter-actions {
        display: flex;
        gap: 0.5rem;
    }

    /* ===== GRID DE FUNCIONÁRIOS ===== */
    .employees-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }

    /* ===== CARDS DE FUNCIONÁRIOS ===== */
    .employee-card {
        background: var(--card-background);
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: var(--card-shadow);
        border: 1px solid var(--border-color);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        opacity: 0;
        transform: translateY(20px);
    }

    .employee-card.animate-in {
        opacity: 1;
        transform: translateY(0);
    }

    .employee-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--card-shadow-hover);
    }

    /* Header do Card */
    .employee-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
        position: relative;
    }

    .employee-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.125rem;
        flex-shrink: 0;
    }

    .employee-info {
        flex: 1;
        min-width: 0;
    }

    .employee-info h3 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .employee-info p {
        margin: 0.25rem 0 0 0;
        font-size: 0.875rem;
        color: var(--text-secondary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .employee-status {
        position: absolute;
        top: 0;
        right: 0;
    }

    /* Status Badges */
    .status-ativo {
        background-color: var(--success-bg);
        color: var(--success-text);
        border: 1px solid var(--success-color);
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-inativo {
        background-color: var(--danger-bg);
        color: var(--danger-text);
        border: 1px solid var(--danger-color);
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
    }

    /* Detalhes do Funcionário */
    .employee-details {
        margin-bottom: 1rem;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid var(--border-light);
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
    }

    .detail-label i {
        width: 16px;
        color: var(--primary-color);
    }

    .detail-value {
        font-size: 0.875rem;
        color: var(--text-primary);
        font-weight: 500;
        text-align: right;
        max-width: 60%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Ações do Card */
    .employee-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    /* ===== BOTÕES ===== */
    .btn-primary {
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
        transition: var(--transition-fast);
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    .btn-primary:hover {
        background-color: var(--primary-hover);
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-success {
        background-color: var(--success-color);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
        transition: var(--transition-fast);
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    .btn-success:hover {
        background-color: #059669;
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: var(--transition-fast);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;
    }

    .btn-info {
        background-color: var(--info-color);
        color: white;
    }

    .btn-info:hover {
        background-color: #1e40af;
        transform: translateY(-1px);
    }

    .btn-warning {
        background-color: var(--warning-color);
        color: white;
    }

    .btn-warning:hover {
        background-color: #d97706;
        transform: translateY(-1px);
    }

    .btn-danger {
        background-color: var(--danger-color);
        color: white;
    }

    .btn-danger:hover {
        background-color: #dc2626;
        transform: translateY(-1px);
    }

    /* ===== RESPONSIVIDADE ===== */
    @media (max-width: 768px) {
        .main-container {
            padding: 1rem;
        }

        .employees-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .modern-header {
            padding: 1.5rem;
        }

        .modern-header h1 {
            font-size: 1.5rem;
        }

        .header-content {
            flex-direction: column;
            align-items: flex-start;
        }

        .stats-container {
            width: 100%;
            justify-content: space-between;
        }

        .filters-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .filter-actions {
            flex-direction: column;
            width: 100%;
        }

        .btn-primary,
        .btn-success {
            width: 100%;
            justify-content: center;
        }

        .employee-actions {
            flex-direction: column;
            gap: 0.25rem;
        }

        .btn-sm {
            width: 100%;
            justify-content: center;
        }
    }

    /* Tablet */
    @media (min-width: 769px) and (max-width: 1024px) {
        .employees-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .filters-grid {
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .filter-actions {
            grid-column: 1 / -1;
            justify-content: flex-start;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // ===== ANIMAÇÕES DE ENTRADA DOS CARDS =====
    document.addEventListener('DOMContentLoaded', function() {
        // Animar entrada dos cards
        const cards = document.querySelectorAll('.employee-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('animate-in');
            }, index * 100);
        });

        // Auto-submit do formulário de filtros quando mudar registros por página
        const perPageSelect = document.getElementById('per_page');
        if (perPageSelect) {
            perPageSelect.addEventListener('change', function() {
                this.form.submit();
            });
        }

        // Event listeners para botões de exclusão
        const deleteButtons = document.querySelectorAll('.btn-danger[data-id]');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const nome = this.getAttribute('data-nome');
                confirmarExclusao(id, nome);
            });
        });

        // Inicializar filtros em tempo real
        filtrarFuncionarios();
    });

    // ===== ATALHOS DE TECLADO =====
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K para focar na busca
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('search');
            if (searchInput) searchInput.focus();
        }

        // Ctrl/Cmd + N para novo funcionário
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            window.location.href = '/funcionarios/cadastrar';
        }
    });


    // ===== FILTRO EM TEMPO REAL =====
    function filtrarFuncionarios() {
        const searchInput = document.getElementById('search');
        const statusSelect = document.getElementById('status');
        const cards = document.querySelectorAll('.employee-card');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const statusFilter = statusSelect ? statusSelect.value.toLowerCase() : '';

                cards.forEach(card => {
                    const searchData = card.getAttribute('data-busca') || '';
                    const statusData = card.getAttribute('data-status') || '';

                    const matchesSearch = !searchTerm || searchData.includes(searchTerm);
                    const matchesStatus = !statusFilter || statusData === statusFilter;

                    if (matchesSearch && matchesStatus) {
                        card.style.display = 'block';
                        setTimeout(() => card.classList.add('animate-in'), 50);
                    } else {
                        card.style.display = 'none';
                        card.classList.remove('animate-in');
                    }
                });
            });
        }
    }

    // ===== MODAL MODERNO DE EXCLUSÃO =====
    function confirmarExclusao(funcionarioId, funcionarioNome) {
        // Modal de confirmação moderno
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-exclamation-triangle" style="color: var(--danger-color);"></i> Confirmar Exclusão</h3>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja excluir o funcionário:</p>
                    <p><strong>"${funcionarioNome}"</strong></p>
                    <p style="color: var(--danger-color); font-size: 0.875rem;">
                        <i class="fas fa-warning"></i> Esta ação não pode ser desfeita.
                    </p>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn-secondary" onclick="fecharModal()">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="button" class="btn-danger" onclick="executarExclusao(${funcionarioId})">
                        <i class="fas fa-trash"></i> Excluir
                    </button>
                </div>
            </div>
        `;

        // Adicionar estilos do modal
        const modalStyles = document.createElement('style');
        modalStyles.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            }

            .modal-content {
                background: white;
                border-radius: 12px;
                padding: 0;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
                animation: slideIn 0.3s ease;
            }

            .modal-header {
                padding: 1.5rem;
                border-bottom: 1px solid var(--border-color);
            }

            .modal-header h3 {
                margin: 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                color: var(--text-primary);
            }

            .modal-body {
                padding: 1.5rem;
            }

            .modal-body p {
                margin: 0.5rem 0;
                color: var(--text-primary);
            }

            .modal-actions {
                padding: 1rem 1.5rem;
                border-top: 1px solid var(--border-color);
                display: flex;
                gap: 0.5rem;
                justify-content: flex-end;
            }

            .btn-secondary {
                background-color: var(--border-color);
                color: var(--text-secondary);
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                cursor: pointer;
                transition: var(--transition-fast);
                display: inline-flex;
                align-items: center;
                gap: 4px;
            }

            .btn-secondary:hover {
                background-color: var(--text-muted);
                color: white;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideIn {
                from { transform: translateY(-20px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;

        document.head.appendChild(modalStyles);
        document.body.appendChild(modal);
    }

    function fecharModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.remove();
        }
    }

    function executarExclusao(funcionarioId) {
        // Criar formulário para enviar DELETE
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/funcionarios/${funcionarioId}/apagar`;

        // Adicionar token CSRF se necessário
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();

        // Fechar modal
        fecharModal();
    }

    // ===== TORNAR FUNÇÕES GLOBAIS =====
    window.confirmarExclusao = confirmarExclusao;
    window.fecharModal = fecharModal;
    window.executarExclusao = executarExclusao;
</script>
{% endblock %}