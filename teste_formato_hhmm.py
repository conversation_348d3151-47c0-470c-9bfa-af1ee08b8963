#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🕐 TESTE DO FORMATO HH:MM
========================

Testa se o sistema está usando formato HH:MM em vez de decimal.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'var', 'www', 'controle-ponto'))

from utils.helpers import decimal_para_hhmm, hhmm_para_decimal, formatar_tempo_trabalho

def testar_conversoes():
    """
    Testa as funções de conversão entre decimal e HH:MM
    """
    print("🕐 TESTE DAS FUNÇÕES DE CONVERSÃO")
    print("=" * 50)
    
    # Casos de teste
    casos_teste = [
        (2.9, "02:54"),      # 2.9h = 2h 54min
        (8.0, "08:00"),      # 8.0h = 8h 00min
        (1.5, "01:30"),      # 1.5h = 1h 30min
        (0.25, "00:15"),     # 0.25h = 15min
        (10.75, "10:45"),    # 10.75h = 10h 45min
        (0.0, "00:00"),      # 0.0h = 00:00
    ]
    
    print("📊 TESTE: decimal_para_hhmm()")
    print("-" * 30)
    
    todos_corretos = True
    
    for decimal, esperado in casos_teste:
        resultado = decimal_para_hhmm(decimal)
        status = "✅" if resultado == esperado else "❌"
        print(f"   {decimal}h → {resultado} (esperado: {esperado}) {status}")
        
        if resultado != esperado:
            todos_corretos = False
    
    print(f"\n📊 TESTE: hhmm_para_decimal()")
    print("-" * 30)
    
    for decimal_esperado, hhmm in casos_teste:
        if decimal_esperado == 0.0:
            continue  # Pular caso especial
            
        resultado = hhmm_para_decimal(hhmm)
        # Permitir pequena diferença devido a arredondamento
        diferenca = abs(resultado - decimal_esperado)
        correto = diferenca < 0.02  # Tolerância de ~1 minuto
        status = "✅" if correto else "❌"
        print(f"   {hhmm} → {resultado:.2f}h (esperado: {decimal_esperado}h) {status}")
        
        if not correto:
            todos_corretos = False
    
    print(f"\n📊 TESTE: formatar_tempo_trabalho()")
    print("-" * 30)
    
    for decimal, esperado in casos_teste:
        resultado = formatar_tempo_trabalho(decimal)
        status = "✅" if resultado == esperado else "❌"
        print(f"   {decimal}h → {resultado} (esperado: {esperado}) {status}")
        
        if resultado != esperado:
            todos_corretos = False
    
    return todos_corretos

def testar_casos_especiais():
    """
    Testa casos especiais e edge cases
    """
    print(f"\n🔍 TESTE DE CASOS ESPECIAIS")
    print("=" * 50)
    
    casos_especiais = [
        (None, "00:00", "Valor None"),
        (-1.5, "00:00", "Valor negativo"),
        (24.0, "24:00", "24 horas"),
        (25.5, "25:30", "Mais de 24 horas"),
    ]
    
    todos_corretos = True
    
    for valor, esperado, descricao in casos_especiais:
        resultado = decimal_para_hhmm(valor)
        status = "✅" if resultado == esperado else "❌"
        print(f"   {descricao}: {valor} → {resultado} (esperado: {esperado}) {status}")
        
        if resultado != esperado:
            todos_corretos = False
    
    return todos_corretos

def main():
    print("🕐 TESTE COMPLETO DO FORMATO HH:MM")
    print("=" * 60)
    print(f"Testando conversões entre formato decimal e HH:MM")
    print("=" * 60)
    
    # Teste 1: Conversões básicas
    conversoes_ok = testar_conversoes()
    
    # Teste 2: Casos especiais
    especiais_ok = testar_casos_especiais()
    
    # Resultado final
    print(f"\n" + "=" * 60)
    print(f"📊 RESULTADO FINAL DOS TESTES")
    print(f"=" * 60)
    
    if conversoes_ok and especiais_ok:
        print(f"🎉 TODOS OS TESTES PASSARAM!")
        print(f"✅ Funções de conversão funcionando corretamente")
        print(f"✅ Sistema pronto para usar formato HH:MM")
    else:
        print(f"❌ ALGUNS TESTES FALHARAM!")
        if not conversoes_ok:
            print(f"❌ Problemas nas conversões básicas")
        if not especiais_ok:
            print(f"❌ Problemas nos casos especiais")
    
    print(f"\n💡 EXEMPLOS DE USO:")
    print(f"   2.9h (decimal) = {decimal_para_hhmm(2.9)} (HH:MM)")
    print(f"   8.5h (decimal) = {decimal_para_hhmm(8.5)} (HH:MM)")
    print(f"   02:54 (HH:MM) = {hhmm_para_decimal('02:54'):.2f}h (decimal)")
    print(f"   08:30 (HH:MM) = {hhmm_para_decimal('08:30'):.2f}h (decimal)")

if __name__ == "__main__":
    main()
