#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para testar se os elementos foram removidos corretamente do dashboard
Data: 05/07/2025
"""

import requests
import sys
from datetime import datetime

def test_dashboard_elements():
    """Testa se os elementos marcados em vermelho foram removidos"""
    
    print("🔍 TESTE DE REMOÇÃO DE ELEMENTOS DO DASHBOARD")
    print("=" * 50)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    # URL do dashboard
    dashboard_url = "http://************:5000/empresa-principal/"
    
    try:
        print("1. Acessando dashboard da empresa principal...")
        response = requests.get(dashboard_url, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Erro ao acessar dashboard: Status {response.status_code}")
            return False
            
        content = response.text.lower()
        print("✅ Dashboard acessado com sucesso")
        print()
        
        # Elementos que DEVEM ter sido removidos
        elementos_removidos = [
            ("jornadas", "Botão Jornadas"),
            ("adicionar cliente", "Botão Adicionar Cliente"), 
            ("alocar funcionário", "Botão Alocar Funcionário"),
            ("clientes recentes", "Seção Clientes Recentes"),
            ("funcionários", "Seção Funcionários"),
            ("adicionar primeiro cliente", "Botão Adicionar Primeiro Cliente")
        ]
        
        print("2. Verificando elementos removidos...")
        elementos_encontrados = []
        
        for elemento, descricao in elementos_removidos:
            if elemento in content:
                elementos_encontrados.append(descricao)
                print(f"❌ ENCONTRADO: {descricao}")
            else:
                print(f"✅ REMOVIDO: {descricao}")
        
        print()
        
        # Elementos que DEVEM estar presentes
        elementos_mantidos = [
            ("gerenciar clientes", "Botão Gerenciar Clientes"),
            ("alocações", "Botão Alocações"),
            ("relatórios", "Botão Relatórios"),
            ("informações da empresa principal", "Seção Informações da Empresa")
        ]
        
        print("3. Verificando elementos mantidos...")
        elementos_faltando = []
        
        for elemento, descricao in elementos_mantidos:
            if elemento in content:
                print(f"✅ PRESENTE: {descricao}")
            else:
                elementos_faltando.append(descricao)
                print(f"❌ AUSENTE: {descricao}")
        
        print()
        print("=" * 50)
        print("📊 RESULTADO DO TESTE:")
        
        if elementos_encontrados:
            print(f"❌ FALHA: {len(elementos_encontrados)} elementos ainda presentes:")
            for elemento in elementos_encontrados:
                print(f"   - {elemento}")
        else:
            print("✅ SUCESSO: Todos os elementos marcados foram removidos")
            
        if elementos_faltando:
            print(f"⚠️ ATENÇÃO: {len(elementos_faltando)} elementos essenciais ausentes:")
            for elemento in elementos_faltando:
                print(f"   - {elemento}")
        else:
            print("✅ SUCESSO: Todos os elementos essenciais estão presentes")
            
        print()
        
        # Resultado final
        if not elementos_encontrados and not elementos_faltando:
            print("🎉 TESTE PASSOU: Remoção realizada com sucesso!")
            return True
        else:
            print("❌ TESTE FALHOU: Verificar elementos listados acima")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Erro de conexão: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

def test_dashboard_functionality():
    """Testa se o dashboard ainda funciona corretamente"""
    
    print("\n🔧 TESTE DE FUNCIONALIDADE DO DASHBOARD")
    print("=" * 50)
    
    dashboard_url = "http://************:5000/empresa-principal/"
    
    try:
        print("1. Testando carregamento da página...")
        response = requests.get(dashboard_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Página carrega corretamente")
        else:
            print(f"❌ Erro no carregamento: Status {response.status_code}")
            return False
            
        content = response.text
        
        print("2. Verificando estrutura HTML...")
        if "<html" in content and "</html>" in content:
            print("✅ Estrutura HTML válida")
        else:
            print("❌ Estrutura HTML inválida")
            return False
            
        print("3. Verificando elementos essenciais...")
        elementos_essenciais = [
            "empresa principal",
            "ações rápidas", 
            "estatísticas",
            "informações"
        ]
        
        for elemento in elementos_essenciais:
            if elemento.lower() in content.lower():
                print(f"✅ {elemento.title()} presente")
            else:
                print(f"❌ {elemento.title()} ausente")
                return False
                
        print("\n✅ FUNCIONALIDADE: Dashboard funcionando corretamente")
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste de funcionalidade: {e}")
        return False

if __name__ == "__main__":
    print("🚀 INICIANDO TESTES DE VALIDAÇÃO")
    print("Sistema: RLPONTO-WEB v1.0")
    print("Servidor: ************:5000")
    print()
    
    # Executar testes
    test1 = test_dashboard_elements()
    test2 = test_dashboard_functionality()
    
    print("\n" + "=" * 50)
    print("📋 RESUMO FINAL:")
    
    if test1 and test2:
        print("🎉 TODOS OS TESTES PASSARAM!")
        print("✅ Elementos removidos com sucesso")
        print("✅ Dashboard funcionando corretamente")
        sys.exit(0)
    else:
        print("❌ ALGUNS TESTES FALHARAM!")
        if not test1:
            print("❌ Remoção de elementos incompleta")
        if not test2:
            print("❌ Problemas de funcionalidade detectados")
        sys.exit(1)
