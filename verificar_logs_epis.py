#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar logs de EPIs e testar criação
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def verificar_logs_epis():
    """Verificar logs de EPIs"""
    print("🔍 VERIFICANDO LOGS DE EPIs")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar estrutura da tabela epis novamente
        print("\n1. Verificando estrutura da tabela epis...")
        sql_desc = "DESCRIBE epis"
        campos = db.execute_query(sql_desc)
        
        campos_obrigatorios = []
        for campo in campos:
            if campo['Null'] == 'NO' and campo['Field'] != 'id':
                campos_obrigatorios.append(campo['Field'])
                print(f"   ⚠️ Campo OBRIGATÓRIO: {campo['Field']} ({campo['Type']})")
        
        print(f"📋 Campos obrigatórios: {campos_obrigatorios}")
        
        # 2. Testar criação de EPI com todos os campos obrigatórios
        print(f"\n2. Testando criação de EPI com todos os campos...")
        
        sql_inserir_completo = """
        INSERT INTO epis (
            funcionario_id, 
            epi_nome, 
            epi_ca, 
            epi_data_entrega, 
            epi_data_validade, 
            epi_observacoes, 
            status_epi,
            criado_em,
            atualizado_em
        ) VALUES (
            1, 
            'Capacete TESTE COMPLETO', 
            '54321', 
            CURDATE(), 
            DATE_ADD(CURDATE(), INTERVAL 1 YEAR), 
            'EPI de teste com todos os campos', 
            'entregue',
            NOW(),
            NOW()
        )
        """
        
        result = db.execute_query(sql_inserir_completo, fetch_all=False)
        
        if result is not None:
            print(f"   ✅ EPI criado com sucesso! ID: {result}")
            
            # Verificar se aparece na função get_with_epis
            from utils.database import FuncionarioQueries
            funcionario = FuncionarioQueries.get_with_epis(1)
            
            if funcionario and funcionario.get('epis'):
                print(f"   ✅ EPI aparece na função get_with_epis!")
                print(f"   📊 Total EPIs: {len(funcionario['epis'])}")
                for epi in funcionario['epis']:
                    print(f"      - {epi.get('epi_nome')} (Status: {epi.get('status_epi')})")
            else:
                print(f"   ❌ EPI não aparece na função get_with_epis")
        else:
            print(f"   ❌ Erro ao criar EPI completo")
        
        # 3. Testar criação como a função _criar_epi atual faz (sem status_epi)
        print(f"\n3. Testando criação SEM status_epi (como função atual)...")
        
        sql_inserir_incompleto = """
        INSERT INTO epis (funcionario_id, epi_nome, epi_ca, epi_data_entrega, epi_data_validade, epi_observacoes)
        VALUES (1, 'Capacete TESTE INCOMPLETO', '99999', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 YEAR), 'Teste sem status')
        """
        
        try:
            result2 = db.execute_query(sql_inserir_incompleto, fetch_all=False)
            if result2 is not None:
                print(f"   ✅ EPI sem status_epi criado! ID: {result2}")
            else:
                print(f"   ❌ Erro ao criar EPI sem status_epi")
        except Exception as e:
            print(f"   ❌ ERRO ao criar EPI sem status_epi: {e}")
            print(f"   🎯 PROBLEMA IDENTIFICADO: Campo status_epi é obrigatório!")
        
        # 4. Verificar logs do sistema
        print(f"\n4. Verificando logs do sistema...")
        
        # Verificar se há tabela de logs
        sql_logs = """
        SELECT * FROM logs_sistema 
        WHERE mensagem LIKE '%EPI%' OR mensagem LIKE '%epi%'
        ORDER BY data_hora DESC 
        LIMIT 10
        """
        
        try:
            logs = db.execute_query(sql_logs)
            if logs:
                print(f"📋 Logs relacionados a EPIs: {len(logs)}")
                for log in logs:
                    print(f"   - {log.get('data_hora')}: {log.get('mensagem')}")
            else:
                print(f"   ❌ Nenhum log relacionado a EPIs encontrado")
        except Exception as e:
            print(f"   ⚠️ Erro ao buscar logs: {e}")
        
        # 5. Limpar EPIs de teste
        print(f"\n5. Limpando EPIs de teste...")
        sql_limpar = "DELETE FROM epis WHERE funcionario_id = 1 AND epi_nome LIKE '%TESTE%'"
        db.execute_query(sql_limpar, fetch_all=False)
        print(f"   🧹 EPIs de teste removidos")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a verificação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    verificar_logs_epis()
