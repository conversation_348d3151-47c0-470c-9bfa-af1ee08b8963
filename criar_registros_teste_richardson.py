#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CRIAR REGISTROS DE TESTE PARA RICHARDSON
===========================================

Cria registros controlados para testar os cálculos.
"""

import requests
from datetime import datetime, time

def criar_registros_teste():
    """
    Cria registros de teste para Richardson.
    """
    print("🔧 CRIANDO REGISTROS DE TESTE PARA RICHARDSON")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # 1. Fazer login
        login_response = session.post(
            "http://************:5000/login",
            data={'usuario': 'admin', 'senha': '@Ric6109'}
        )
        
        if login_response.status_code != 200:
            print("❌ Falha no login")
            return False
        
        print("✅ Login realizado")
        
        # 2. Registrar entrada às 08:00
        print("\n🕐 Registrando entrada às 08:00...")
        entrada_response = session.post(
            "http://************:5000/registro-ponto/api/registrar-manual",
            data={
                'funcionario_id': 35,
                'tipo_registro': 'entrada_manha',
                'observacoes': 'TESTE_CALCULO_CORRIGIDO_08:00'
            }
        )
        
        if entrada_response.status_code == 200:
            print("✅ Entrada registrada")
        else:
            print(f"❌ Erro na entrada: {entrada_response.status_code}")
            return False
        
        # 3. Registrar saída almoço às 09:05 (1h 5min depois)
        print("\n🕐 Registrando saída almoço às 09:05...")
        saida_response = session.post(
            "http://************:5000/registro-ponto/api/registrar-manual",
            data={
                'funcionario_id': 35,
                'tipo_registro': 'saida_almoco',
                'observacoes': 'TESTE_CALCULO_CORRIGIDO_09:05'
            }
        )
        
        if saida_response.status_code == 200:
            saida_result = saida_response.json()
            print("✅ Saída almoço registrada")
            
            # Verificar horas calculadas
            if 'horas_calculadas' in saida_result:
                horas_calc = saida_result['horas_calculadas']
                horas_trabalhadas = horas_calc.get('horas_trabalhadas', 0)
                print(f"📊 Horas calculadas: {horas_trabalhadas}")
                
                # Deve ser aproximadamente 1.083h (1h 5min)
                if 1.08 <= horas_trabalhadas <= 1.09:
                    print("✅ Cálculo parece correto (1h 5min)")
                else:
                    print(f"⚠️ Cálculo inesperado: {horas_trabalhadas}h")
        else:
            print(f"❌ Erro na saída: {saida_response.status_code}")
            return False
        
        # 4. Verificar no relatório
        print("\n📊 Verificando relatório...")
        relatorio_response = session.get(
            f"http://************:5000/ponto-admin/funcionario/35/imprimir",
            params={'data_inicio': datetime.now().strftime('%Y-%m-%d'), 
                   'data_fim': datetime.now().strftime('%Y-%m-%d')}
        )
        
        if relatorio_response.status_code == 200:
            html_content = relatorio_response.text
            
            # Procurar pelo total no rodapé
            import re
            total_match = re.search(r'<span id="total-horas"[^>]*>(.*?)</span>', html_content, re.DOTALL)
            
            if total_match:
                total_content = total_match.group(1)
                total_clean = re.sub(r'<[^>]+>', '', total_content).strip()
                total_clean = re.sub(r'\s+', ' ', total_clean)
                
                print(f"📊 Total no rodapé: '{total_clean}'")
                
                # Deve mostrar 1h 05min
                if "1h 05min" in total_clean:
                    print("🎉 SUCESSO! Rodapé mostra 1h 05min (CORRETO)")
                    return True
                elif "1h 06min" in total_clean:
                    print("❌ AINDA INCORRETO! Rodapé mostra 1h 06min")
                    return False
                else:
                    print(f"⚠️ Resultado inesperado: {total_clean}")
                    return False
            else:
                print("❌ Não encontrou total no rodapé")
                return False
        else:
            print(f"❌ Erro no relatório: {relatorio_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        return False

def main():
    """
    Função principal.
    """
    print("🔧 TESTE DE CÁLCULO COM REGISTROS CONTROLADOS")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    sucesso = criar_registros_teste()
    
    print("\n" + "=" * 60)
    if sucesso:
        print("🎉 TESTE CONCLUÍDO COM SUCESSO!")
        print("✅ Cálculos corrigidos funcionando!")
    else:
        print("⚠️ TESTE AINDA APRESENTA PROBLEMAS!")
        print("❌ Cálculos precisam de mais correções")
    
    return sucesso

if __name__ == "__main__":
    sucesso = main()
    exit(0 if sucesso else 1)
