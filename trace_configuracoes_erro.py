#!/usr/bin/env python3
"""
TRACE ESPECÍFICO - Problema da página de configurações
"""

import logging
import traceback
import sys
from datetime import datetime

# Setup de logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('TRACE_CONFIG')

print("🔍 TRACE DETALHADO - PÁGINA DE CONFIGURAÇÕES")
print("=" * 60)

try:
    print("\n1. IMPORTANDO MÓDULOS...")
    
    # Import app_configuracoes
    print("   📦 Importando app_configuracoes...")
    import app_configuracoes
    print("   ✅ app_configuracoes importado")
    
    # Import app principal  
    print("   📦 Importando app principal...")
    from app import app
    print("   ✅ app principal importado")
    
    print("\n2. TESTANDO FUNÇÕES DE CONFIGURAÇÃO...")
    
    # Testar obter_configuracao
    print("   🧪 Testando obter_configuracao...")
    try:
        resultado = app_configuracoes.obter_configuracao('formato_data', 'default')
        print(f"   ✅ obter_configuracao: {resultado}")
    except Exception as e:
        print(f"   ❌ Erro em obter_configuracao: {e}")
        traceback.print_exc()
    
    # Testar obter_configuracoes_por_categoria
    print("   🧪 Testando obter_configuracoes_por_categoria...")
    try:
        configs = app_configuracoes.obter_configuracoes_por_categoria('sistema')
        print(f"   ✅ Configurações sistema: {len(configs)} encontradas")
    except Exception as e:
        print(f"   ❌ Erro em obter_configuracoes_por_categoria: {e}")
        traceback.print_exc()
    
    print("\n3. TESTANDO ACESSO À PÁGINA...")
    
    with app.test_client() as client:
        # Configurar sessão de admin
        with client.session_transaction() as sess:
            sess['usuario'] = 'admin'
            sess['nivel_acesso'] = 'admin'
        
        print("   🌐 Fazendo requisição GET /configuracoes/...")
        
        try:
            response = client.get('/configuracoes/')
            print(f"   📊 Status Code: {response.status_code}")
            
            if response.status_code == 500:
                print("   ❌ ERRO 500 DETECTADO!")
                print("   📜 Conteúdo da resposta:")
                content = response.data.decode('utf-8', errors='ignore')
                print(content[:1000])  # Primeiros 1000 caracteres
                
            elif response.status_code == 200:
                print("   ✅ Página carregada com sucesso!")
                print(f"   📏 Tamanho da resposta: {len(response.data)} bytes")
                
            elif response.status_code == 302:
                print("   🔄 Redirecionamento detectado")
                print(f"   📍 Location: {response.headers.get('Location', 'N/A')}")
                
            else:
                print(f"   ⚠️ Status inesperado: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Erro durante requisição: {e}")
            traceback.print_exc()
    
    print("\n4. VERIFICANDO TEMPLATE...")
    
    try:
        import os
        template_path = 'templates/configuracoes/index.html'
        if os.path.exists(template_path):
            print(f"   ✅ Template encontrado: {template_path}")
        else:
            print(f"   ❌ Template NÃO encontrado: {template_path}")
            # Listar templates disponíveis
            templates_dir = 'templates'
            if os.path.exists(templates_dir):
                print("   📁 Templates disponíveis:")
                for root, dirs, files in os.walk(templates_dir):
                    for file in files:
                        if file.endswith('.html'):
                            rel_path = os.path.relpath(os.path.join(root, file), templates_dir)
                            print(f"      - {rel_path}")
    except Exception as e:
        print(f"   ❌ Erro ao verificar template: {e}")
    
    print("\n5. TESTANDO FUNÇÃO INDEX DO BLUEPRINT...")
    
    try:
        from app_configuracoes import index
        print("   📦 Função index importada")
        
        # Testar chamada direta (simulando contexto Flask)
        with app.app_context():
            with app.test_request_context('/configuracoes/', method='GET'):
                # Simular sessão
                from flask import session
                session['usuario'] = 'admin'
                session['nivel_acesso'] = 'admin'
                
                print("   🔧 Executando função index...")
                try:
                    resultado = index()
                    print(f"   ✅ Função executada: tipo={type(resultado)}")
                except Exception as e:
                    print(f"   ❌ Erro na função index: {e}")
                    traceback.print_exc()
    
    except Exception as e:
        print(f"   ❌ Erro ao testar função index: {e}")
        traceback.print_exc()

except Exception as e:
    print(f"\n❌ ERRO CRÍTICO NO TRACE: {e}")
    traceback.print_exc()

print("\n" + "=" * 60)
print("🏁 TRACE CONCLUÍDO")
print("=" * 60) 