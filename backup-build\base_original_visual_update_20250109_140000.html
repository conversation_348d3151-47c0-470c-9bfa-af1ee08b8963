<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}<PERSON><PERSON>{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/relatorios.css') }}" rel="stylesheet">
    
    <link rel="stylesheet" href="/static/style-cadastrar.css">
    {% block extra_css %}{% endblock %}
    <style>
        /* Estilos para breadcrumbs */
        .breadcrumbs {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .breadcrumbs a {
            color: #4fbdba;
            text-decoration: none;
        }
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        .breadcrumbs span {
            color: #6c757d;
            margin: 0 5px;
        }
        
        /* Estilos para mensagens flash */
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 12px 15px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-weight: 500;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .flash-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .flash-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        /* Estilos para tabelas */
        .table-responsive {
            overflow-x: auto;
            margin-bottom: 20px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }
        .data-table tbody tr:hover {
            background: #f8f9fa;
        }
        
        /* Estilos para ações */
        .action-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 3px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .btn-view {
            background: #4fbdba;
            color: white;
        }
        .btn-view:hover {
            background: #3da8a6;
            color: white;
        }
        .btn-edit {
            background: #ffc107;
            color: #1a2634;
        }
        .btn-edit:hover {
            background: #e0a800;
        }
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        .btn-delete:hover {
            background: #b52a37;
        }
        
        /* Estilos para paginação */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .pagination a,
        .pagination span {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            text-decoration: none;
            color: #495057;
            background: white;
            transition: all 0.2s ease;
        }
        .pagination a:hover {
            background: #e9ecef;
        }
        .pagination .current {
            background: #4fbdba;
            color: white;
            border-color: #4fbdba;
        }
        .pagination .disabled {
            color: #6c757d;
            background: #f8f9fa;
            cursor: not-allowed;
        }
        
        /* Estilos para badges de status */
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }
        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }
        .badge-secondary {
            background: #e2e3e5;
            color: #383d41;
        }
        
        /* Estilos para filtros */
        .filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .filters-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }
        .filter-group {
            flex: 1;
            min-width: 200px;
        }
        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }
        .filter-group input,
        .filter-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .filter-group button {
            background: #4fbdba;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.2s ease;
        }
        .filter-group button:hover {
            background: #3da8a6;
        }
        
        /* Responsividade */
        @media (max-width: 768px) {
            .filters-row {
                flex-direction: column;
            }
            .filter-group {
                min-width: 100%;
            }
            .action-buttons {
                flex-direction: column;
                gap: 5px;
            }
            .pagination {
                font-size: 14px;
            }
            .pagination a,
            .pagination span {
                padding: 6px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <button class="tab-btn" onclick="window.location.href='/funcionarios'">Lista de Funcionários</button>
            <button class="tab-btn" onclick="window.location.href='/funcionarios/cadastrar'">Cadastrar Funcionário</button>
            
            <!-- ✅ NOVOS RECURSOS DE PONTO -->
            <button class="tab-btn" onclick="window.location.href='/registro-ponto/biometrico'">Registrar Ponto Biométrico</button>
            <button class="tab-btn" onclick="window.location.href='/registro-ponto/manual'">Registrar Ponto Manual</button>
            <button class="tab-btn" onclick="window.location.href='/relatorios/pontos'">Relatórios de Ponto</button>
            <button class="tab-btn" onclick="window.location.href='/relatorios/estatisticas'">Estatísticas</button>
            
            <!-- ✅ CONFIGURAÇÕES -->
            {% if session.get('nivel_acesso') == 'admin' %}
            <button class="tab-btn" onclick="window.location.href='/configuracoes'">⚙️ Configurações</button>
            <button class="tab-btn" onclick="window.location.href='/configurar_usuarios'">Configurar Usuários</button>
            {% endif %}
            <button class="tab-btn" onclick="window.location.href='/logout'">Sair</button>
        </div>
        
        <div class="main-content">
            <!-- Breadcrumbs -->
            {% if breadcrumbs %}
            <nav class="breadcrumbs">
                {% for crumb in breadcrumbs %}
                    {% if crumb.url %}
                        <a href="{{ crumb.url }}">{{ crumb.text }}</a>
                    {% else %}
                        {{ crumb.text }}
                    {% endif %}
                    {% if not loop.last %}
                        <span>/</span>
                    {% endif %}
                {% endfor %}
            </nav>
            {% endif %}
            
            <!-- Mensagens Flash -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="flash-messages">
                        {% for category, message in messages %}
                            <div class="flash-message flash-{{ category }}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}
            
            <!-- Conteúdo principal -->
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- Modal de confirmação para exclusão -->
    <div id="confirmModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; max-width: 400px; width: 90%;">
            <h3 style="margin-top: 0; color: #dc3545;">Confirmar Exclusão</h3>
            <p id="confirmMessage">Tem certeza que deseja excluir este funcionário?</p>
            <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                <button onclick="closeConfirmModal()" style="padding: 8px 16px; border: 1px solid #ced4da; background: white; border-radius: 4px; cursor: pointer;">Cancelar</button>
                <button id="confirmDelete" style="padding: 8px 16px; border: none; background: #dc3545; color: white; border-radius: 4px; cursor: pointer;">Excluir</button>
            </div>
        </div>
    </div>
    
    {% block extra_js %}{% endblock %}
    
    <script>
        // Função para modal de confirmação
        function showConfirmModal(message, onConfirm) {
            document.getElementById('confirmMessage').textContent = message;
            document.getElementById('confirmModal').style.display = 'block';
            
            document.getElementById('confirmDelete').onclick = function() {
                closeConfirmModal();
                onConfirm();
            };
        }
        
        function closeConfirmModal() {
            document.getElementById('confirmModal').style.display = 'none';
        }
        
        // Fechar modal clicando fora
        document.getElementById('confirmModal').onclick = function(e) {
            if (e.target === this) {
                closeConfirmModal();
            }
        };
        
        // Função para exclusão com confirmação
        function confirmarExclusao(funcionarioId, funcionarioNome) {
            const message = `Tem certeza que deseja excluir o funcionário "${funcionarioNome}"? Esta ação não poderá ser desfeita.`;
            
            showConfirmModal(message, function() {
                // Criar e submeter formulário
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/funcionarios/${funcionarioId}/apagar`;
                document.body.appendChild(form);
                form.submit();
            });
        }
        
        // Auto-hide para mensagens flash
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 300);
                }, 5000);
            });
        });
    </script>
</body>
</html> 