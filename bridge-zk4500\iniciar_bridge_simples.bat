@echo off
:: =====================================================================
:: INICIADOR SIMPLES BRIDGE ZK4500 - RLPONTO-WEB v1.0
:: Script simples para iniciar bridge em modo oculto
:: Desenvolvido por: <PERSON> Rodrigues - AiNexus Tecnologia
:: =====================================================================

setlocal enabledelayedexpansion
title INICIANDO BRIDGE ZK4500

echo.
echo ========================================================
echo   INICIADOR BRIDGE ZK4500 - MODO OCULTO
echo ========================================================

:: Verificar se Python está disponível
echo [1/5] Verificando Python...
python --version >nul 2>&1
if !errorLevel! == 0 (
    echo [OK] Python disponivel
) else (
    echo [ERRO] Python nao encontrado
    pause
    exit /b 1
)

:: Verificar se o arquivo bridge existe
echo [2/5] Verificando arquivo bridge...
if exist "C:\RLPonto-Bridge\biometric_bridge_service.py" (
    echo [OK] Arquivo bridge encontrado
) else (
    echo [ERRO] Arquivo bridge nao encontrado
    echo        Execute instalador.bat primeiro
    pause
    exit /b 1
)

:: Finalizar qualquer instância anterior
echo [3/5] Finalizando instancias anteriores...
taskkill /F /IM python.exe >nul 2>&1
echo [OK] Instancias anteriores finalizadas

:: Aguardar um momento
timeout /t 2 >nul

:: Iniciar bridge em background
echo [4/5] Iniciando bridge em background...
cd /d "C:\RLPonto-Bridge"
start /B /MIN "" python biometric_bridge_service.py

:: Aguardar inicialização
echo [5/5] Aguardando inicializacao...
timeout /t 10 >nul

:: Verificar se está rodando
netstat -an | findstr ":8080" | findstr "LISTENING" >nul 2>&1
if !errorLevel! == 0 (
    echo.
    echo ========================================================
    echo   BRIDGE INICIADO COM SUCESSO!
    echo ========================================================
    echo.
    echo [OK] Bridge rodando na porta 8080
    echo [INFO] Servico oculto e operacional
    echo.
    
    :: Teste de conectividade
    curl -s --connect-timeout 5 http://localhost:8080/api/bridge-status >nul 2>&1
    if !errorLevel! == 0 (
        echo [OK] Bridge respondendo via HTTP
        echo [INFO] API funcionando corretamente
    ) else (
        echo [AVISO] Bridge iniciou mas API pode estar carregando...
    )
    
    echo.
    echo O bridge agora esta rodando de forma oculta
    echo URL: http://localhost:8080
    echo.
    echo Para verificar status: status_bridge.bat
    echo Para parar: controlar_bridge.bat stop
    echo.
) else (
    echo.
    echo ========================================================
    echo   FALHA AO INICIAR BRIDGE
    echo ========================================================
    echo.
    echo [ERRO] Bridge nao iniciou corretamente
    echo [INFO] Verifique os logs para mais detalhes
    echo.
    echo Acoes recomendadas:
    echo 1. Execute analisar.bat para diagnostico
    echo 2. Verifique se a porta 8080 esta livre
    echo 3. Reinstale usando instalador.bat
    echo.
)

echo Pressione qualquer tecla para finalizar...
pause >nul 