{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    .empresa-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }

    .empresa-card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 8px rgba(0,123,255,0.1);
        transform: translateY(-2px);
    }
    
    .empresa-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .empresa-dados h5 {
        margin-bottom: 5px;
        color: #333;
    }
    
    .empresa-dados small {
        color: #666;
        display: block;
        margin-bottom: 3px;
    }
    
    .btn-definir {
        opacity: 1 !important;
        visibility: visible !important;
        display: inline-block !important;
        transition: all 0.3s ease;
    }

    .empresa-card:hover .btn-definir {
        background-color: #218838;
        border-color: #1e7e34;
        transform: scale(1.05);
    }
    
    .alert-info {
        border-left: 4px solid #17a2b8;
    }

    /* Garantir que o botão seja sempre visível */
    .empresa-acoes .btn-definir {
        opacity: 1 !important;
        visibility: visible !important;
        display: inline-block !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Cabeçalho -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-crown text-warning"></i> {{ titulo }}</h2>
                    <p class="text-muted">Selecione qual empresa será a principal/proprietária do sistema</p>
                </div>
                <a href="{{ url_for('configuracoes.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>
    </div>

    <!-- Informações importantes -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> Importante:</h5>
                <ul class="mb-0">
                    <li><strong>Empresa Principal:</strong> É a empresa proprietária do sistema</li>
                    <li><strong>Funcionários:</strong> Pertencem à empresa principal</li>
                    <li><strong>Clientes:</strong> Outras empresas podem ser clientes da principal</li>
                    <li><strong>Alocação:</strong> Funcionários da principal trabalham nos clientes</li>
                    <li><strong>Jornadas:</strong> Funcionários herdam jornadas dos clientes onde trabalham</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Lista de empresas -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-building"></i> Empresas Cadastradas</h5>
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        <strong>Instruções:</strong> Clique no botão "Definir como Principal" da empresa que deseja tornar a empresa principal do sistema
                    </small>
                </div>
                <div class="card-body">
                    {% if empresas %}
                        {% for empresa in empresas %}
                        <div class="empresa-card" data-empresa-id="{{ empresa.id }}">
                            <div class="empresa-info">
                                <div class="empresa-dados">
                                    <h5>{{ empresa.razao_social }}</h5>
                                    {% if empresa.nome_fantasia %}
                                        <small><strong>Nome Fantasia:</strong> {{ empresa.nome_fantasia }}</small>
                                    {% endif %}
                                    <small><strong>CNPJ:</strong> {{ empresa.cnpj }}</small>
                                    {% if empresa.telefone %}
                                        <small><strong>Telefone:</strong> {{ empresa.telefone }}</small>
                                    {% endif %}
                                    {% if empresa.email %}
                                        <small><strong>Email:</strong> {{ empresa.email }}</small>
                                    {% endif %}
                                    <small><strong>Cadastrada em:</strong> {{ empresa.data_cadastro.strftime('%d/%m/%Y') if empresa.data_cadastro else 'N/A' }}</small>
                                </div>
                                <div class="empresa-acoes">
                                    <form method="POST" action="{{ url_for('empresa_principal.definir_principal') }}" style="display: inline;">
                                        <input type="hidden" name="empresa_id" value="{{ empresa.id }}">
                                        <button type="submit" class="btn btn-success btn-definir" style="opacity: 1 !important; visibility: visible !important; display: inline-block !important;">
                                            <i class="fas fa-crown"></i> Definir como Principal
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Nenhuma empresa cadastrada</h5>
                            <p class="text-muted">Cadastre primeiro uma empresa para defini-la como principal</p>
                            <a href="{{ url_for('configuracoes.cadastrar_empresa') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Cadastrar Empresa
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script>
// Debug: verificar estado do botão
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 Verificando botões...');
    const botoes = document.querySelectorAll('.btn-definir');
    botoes.forEach((btn, index) => {
        const computedStyle = window.getComputedStyle(btn);
        console.log(`Botão ${index + 1}:`, {
            opacity: computedStyle.opacity,
            visibility: computedStyle.visibility,
            display: computedStyle.display
        });

        // Forçar visibilidade
        btn.style.setProperty('opacity', '1', 'important');
        btn.style.setProperty('visibility', 'visible', 'important');
        btn.style.setProperty('display', 'inline-block', 'important');
    });
});
// Animação de entrada (sem afetar opacity do card)
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.empresa-card');
    cards.forEach((card, index) => {
        // Aplicar apenas transform, sem opacity
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'transform 0.5s ease';

        setTimeout(() => {
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// Confirmação antes de definir empresa principal
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.btn-definir').forEach(btn => {
        btn.addEventListener('click', function(e) {
            const empresaNome = this.closest('.empresa-card').querySelector('h5').textContent;
            const confirmacao = confirm(`Tem certeza que deseja definir "${empresaNome}" como empresa principal?\n\nEsta ação irá remover a flag de empresa principal de qualquer outra empresa.`);

            if (!confirmacao) {
                e.preventDefault();
            }
        });
    });
});
</script>
{% endblock %}
