#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para debugar especificamente o problema do cálculo 7.27h vs 7.97h
"""

import sys
import os
from datetime import datetime, time

# Adicionar o diretório do projeto ao path
sys.path.insert(0, '/var/www/controle-ponto')

def testar_calculo_problema():
    """Testa o cálculo com os dados exatos do problema"""
    
    print("🔍 DEBUG: PROBLEMA 7.27h vs 7.97h")
    print("=" * 60)
    
    # Dados exatos do problema
    entrada = "08:43"
    saida_almoco = "12:56"
    retorno_almoco = "14:10"
    saida = "17:55"
    
    print(f"📋 DADOS DO PROBLEMA:")
    print(f"   Entrada: {entrada}")
    print(f"   Saída Almoço: {saida_almoco}")
    print(f"   Retorno Almoço: {retorno_almoco}")
    print(f"   Saída: {saida}")
    print()
    
    # Cálculo manual correto
    print("🧮 CÁLCULO MANUAL CORRETO:")
    
    # Converter para datetime para cálculos
    entrada_dt = datetime.strptime(f"2000-01-01 {entrada}", "%Y-%m-%d %H:%M")
    saida_almoco_dt = datetime.strptime(f"2000-01-01 {saida_almoco}", "%Y-%m-%d %H:%M")
    retorno_almoco_dt = datetime.strptime(f"2000-01-01 {retorno_almoco}", "%Y-%m-%d %H:%M")
    saida_dt = datetime.strptime(f"2000-01-01 {saida}", "%Y-%m-%d %H:%M")
    
    # Período manhã
    periodo_manha_seconds = (saida_almoco_dt - entrada_dt).total_seconds()
    periodo_manha_horas = periodo_manha_seconds / 3600
    periodo_manha_min = int(periodo_manha_seconds / 60)
    
    # Período tarde
    periodo_tarde_seconds = (saida_dt - retorno_almoco_dt).total_seconds()
    periodo_tarde_horas = periodo_tarde_seconds / 3600
    periodo_tarde_min = int(periodo_tarde_seconds / 60)
    
    total_horas_correto = periodo_manha_horas + periodo_tarde_horas
    total_min_correto = int(total_horas_correto * 60)
    h_correto = total_min_correto // 60
    m_correto = total_min_correto % 60
    
    print(f"   Período Manhã: {periodo_manha_min} min = {periodo_manha_horas:.3f}h")
    print(f"   Período Tarde: {periodo_tarde_min} min = {periodo_tarde_horas:.3f}h")
    print(f"   Total: {total_horas_correto:.3f}h = {h_correto}h{m_correto:02d}min")
    print(f"   ✅ ESPERADO: {total_horas_correto:.2f}h")
    print()
    
    # Testar função do sistema
    try:
        from app_ponto_admin import calcular_horas_normais_fallback
        
        # Criar registro no formato esperado
        registro = {
            'entrada': time(8, 43),
            'saida_almoco': time(12, 56),
            'retorno_almoco': time(14, 10),
            'saida': time(17, 55)
        }
        
        resultado_sistema = calcular_horas_normais_fallback(registro)
        
        print(f"🔍 FUNÇÃO DO SISTEMA:")
        print(f"   Resultado: {resultado_sistema:.3f}h")
        print(f"   Formatado: {resultado_sistema:.2f}h")
        
        # Comparar
        diferenca = abs(total_horas_correto - resultado_sistema)
        print(f"   Diferença: {diferenca:.3f}h")
        
        if diferenca < 0.01:
            print(f"   ✅ CORRETO!")
        else:
            print(f"   ❌ INCORRETO!")
            
            # Analisar onde pode estar o erro
            print(f"\n🔍 ANÁLISE DO ERRO:")
            
            # Verificar se 7.27h corresponde a algum cálculo específico
            if abs(resultado_sistema - 7.27) < 0.01:
                print(f"   Sistema retorna 7.27h")
                total_min_sistema = int(7.27 * 60)
                h_sistema = total_min_sistema // 60
                m_sistema = total_min_sistema % 60
                print(f"   7.27h = {h_sistema}h{m_sistema:02d}min")
                
                # Verificar se está perdendo minutos na conversão
                diferenca_min = total_min_correto - total_min_sistema
                print(f"   Diferença: {diferenca_min} minutos perdidos")
                
                # Possíveis causas
                print(f"\n💡 POSSÍVEIS CAUSAS:")
                print(f"   1. Arredondamento prematuro")
                print(f"   2. Conversão incorreta de time para datetime")
                print(f"   3. Perda de precisão em algum cálculo")
                
    except Exception as e:
        print(f"❌ Erro ao testar função do sistema: {e}")
        import traceback
        print(traceback.format_exc())
    
    # Testar conversões específicas
    print(f"\n🔬 TESTE DE CONVERSÕES:")
    
    # Teste 1: Conversão time para datetime
    entrada_time = time(8, 43)
    entrada_dt_test = datetime.combine(datetime.today(), entrada_time)
    print(f"   time(8, 43) -> datetime: {entrada_dt_test}")
    
    # Teste 2: Cálculo de diferença
    saida_almoco_time = time(12, 56)
    saida_almoco_dt_test = datetime.combine(datetime.today(), saida_almoco_time)
    diff_test = saida_almoco_dt_test - entrada_dt_test
    horas_test = diff_test.total_seconds() / 3600
    print(f"   Diferença manhã: {diff_test} = {horas_test:.6f}h")
    
    # Teste 3: Verificar se há problema com segundos
    retorno_time = time(14, 10)
    saida_time = time(17, 55)
    retorno_dt_test = datetime.combine(datetime.today(), retorno_time)
    saida_dt_test = datetime.combine(datetime.today(), saida_time)
    diff_tarde_test = saida_dt_test - retorno_dt_test
    horas_tarde_test = diff_tarde_test.total_seconds() / 3600
    print(f"   Diferença tarde: {diff_tarde_test} = {horas_tarde_test:.6f}h")
    
    total_test = horas_test + horas_tarde_test
    print(f"   Total teste: {total_test:.6f}h = {total_test:.2f}h")

def main():
    testar_calculo_problema()

if __name__ == "__main__":
    main()
