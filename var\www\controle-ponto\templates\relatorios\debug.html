<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Manutenção de Relatórios</h5>
    </div>
    <div class="card-body">
        
        <!-- Novo botão para correção da view setor -->
        <div class="mt-4">
            <h6 class="mb-3">Correção da Exibição de Setor nos Relatórios</h6>
            <p class="text-muted">
                Corrige o problema que faz o setor aparecer como "Não informado" mesmo quando há um setor cadastrado.
            </p>
            <button id="btnCorrigirViewSetor" class="btn btn-warning">
                <i class="bi bi-tools"></i> Corrigir Exibição de Setor
            </button>
            <div id="resultadoCorrecaoSetor" class="mt-3" style="display:none;">
                <div class="alert alert-success">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Processando...</span>
                    </div>
                    <span id="msgCorrecaoSetor">Aplicando correção...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts específicos da página -->
<script>
$(document).ready(function() {
    // Código JavaScript existente
    
    // Adicionar handler para o novo botão de correção de setor
    $("#btnCorrigirViewSetor").click(function() {
        // Mostrar indicador de processamento
        $("#resultadoCorrecaoSetor").show();
        $("#msgCorrecaoSetor").text("Aplicando correção...");
        $("#resultadoCorrecaoSetor div").removeClass("alert-success alert-danger").addClass("alert-info");
        
        // Chamar a API de correção
        $.ajax({
            url: "{{ url_for('relatorios.corrigir_view_setor') }}",
            type: "GET",
            dataType: "json",
            success: function(response) {
                if (response.success) {
                    $("#resultadoCorrecaoSetor div").removeClass("alert-info").addClass("alert-success");
                    $("#msgCorrecaoSetor").html("<strong>Sucesso!</strong> " + response.message);
                } else {
                    $("#resultadoCorrecaoSetor div").removeClass("alert-info").addClass("alert-danger");
                    $("#msgCorrecaoSetor").html("<strong>Erro!</strong> " + response.message);
                }
            },
            error: function(xhr, status, error) {
                $("#resultadoCorrecaoSetor div").removeClass("alert-info").addClass("alert-danger");
                $("#msgCorrecaoSetor").html("<strong>Erro!</strong> Falha na comunicação com o servidor.");
            }
        });
    });
    
    // Resto do código JavaScript existente
});
</script> 