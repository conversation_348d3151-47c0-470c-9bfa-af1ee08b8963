-- Migration: Adicionar campo de status de justificativa aos registros de ponto
-- Data: 2025-07-16
-- Descrição: Campo para sinalizar se o dia foi abonado/reprovado pelo RH

USE controle_ponto;

-- Adicionar coluna status_justificativa à tabela registros_ponto
ALTER TABLE registros_ponto 
ADD COLUMN status_justificativa ENUM('abonado', 'reprovado', 'pendente') NULL DEFAULT NULL 
COMMENT 'Status da justificativa: abonado, reprovado ou NULL se não há justificativa';

-- Adicionar índice para melhor performance
ALTER TABLE registros_ponto 
ADD INDEX idx_status_justificativa (status_justificativa);

-- Atualizar registros existentes baseado nas justificativas aprovadas
UPDATE registros_ponto rp
JOIN justificativas_ponto jp ON rp.funcionario_id = jp.funcionario_id 
    AND DATE(rp.data_hora) = jp.data_registro
SET rp.status_justificativa = CASE 
    WHEN jp.status_aprovacao = 'aprovada' THEN 'abonado'
    WHEN jp.status_aprovacao = 'reprovada' THEN 'reprovado'
    WHEN jp.status_aprovacao = 'pendente' THEN 'pendente'
    ELSE NULL
END
WHERE jp.status_aprovacao IS NOT NULL;

-- Verificar resultados
SELECT 
    COUNT(*) as total_registros,
    COUNT(status_justificativa) as com_justificativa,
    SUM(CASE WHEN status_justificativa = 'abonado' THEN 1 ELSE 0 END) as abonados,
    SUM(CASE WHEN status_justificativa = 'reprovado' THEN 1 ELSE 0 END) as reprovados,
    SUM(CASE WHEN status_justificativa = 'pendente' THEN 1 ELSE 0 END) as pendentes
FROM registros_ponto;
