#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
RLPONTO-WEB v1.0 - Configurações Biométricas
============================================

Módulo de configuração e gerenciamento de leitores biométricos universais.
Integra com o serviço universal_biometric_service.py para detectar, 
registrar e configurar leitores biométricos de qualquer fabricante.

Características:
✓ Auto-detecção de dispositivos
✓ Registro permanente de leitores
✓ Configurações de sensibilidade
✓ Testes de funcionamento
✓ Diagnósticos completos

Autor: Richardson Rodrigues - AiNexus Tecnologia
Sistema: RLPONTO-WEB v1.0  
Data: Junho 2025
"""

import json
import logging
import os
import subprocess
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

from flask import Blueprint, render_template, request, jsonify, session, flash
from utils.auth import require_admin
import requests

# Configuração de logging
logger = logging.getLogger(__name__)

# Blueprint
biometric_config_bp = Blueprint('biometric_config', __name__, url_prefix='/configuracoes/biometria')

# Constantes
BIOMETRIC_SERVICE_URL = "http://localhost:5001"
CONFIG_FILE = "biometric_settings.json"

class BiometricConfigManager:
    """Gerenciador de configurações biométricas"""
    
    def __init__(self):
        self.settings_file = Path(CONFIG_FILE)
        self.default_settings = {
            'service_enabled': True,
            'auto_discovery': True,
            'sensitivity_level': 'medium',
            'timeout_seconds': 30,
            'max_attempts': 3,
            'quality_threshold': 60,
            'registered_devices': {},
            'last_discovery': None
        }
        self.settings = self._load_settings()
    
    def _load_settings(self) -> Dict:
        """Carrega configurações salvas"""
        if self.settings_file.exists():
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded = json.load(f)
                    # Mescla com defaults para garantir todas as chaves
                    settings = self.default_settings.copy()
                    settings.update(loaded)
                    return settings
            except Exception as e:
                logger.error(f"Erro ao carregar configurações: {e}")
        
        return self.default_settings.copy()
    
    def save_settings(self) -> bool:
        """Salva configurações"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False, default=str)
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar configurações: {e}")
            return False

# Instância global do gerenciador
config_manager = BiometricConfigManager()

# ========================================
# ROTAS DE CONFIGURAÇÃO BIOMÉTRICA
# ========================================

@biometric_config_bp.route('/')
@require_admin
def index():
    """Página principal de configurações biométricas"""
    try:
        # Dispositivos registrados
        registered_devices = config_manager.settings.get('registered_devices', {})
        
        # Última descoberta
        last_discovery = config_manager.settings.get('last_discovery')
        if last_discovery:
            try:
                last_discovery = datetime.fromisoformat(last_discovery).strftime('%d/%m/%Y %H:%M')
            except:
                last_discovery = 'Nunca'
        else:
            last_discovery = 'Nunca'
        
        context = {
            'titulo': 'Configurações Biométricas',
            'service_running': False,  # Isso não é mais usado, já que verificamos o bridge local via JS
            'registered_devices': registered_devices,
            'last_discovery': last_discovery,
            'settings': config_manager.settings
        }
        
        return render_template('configuracoes/biometria.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar configurações biométricas: {e}")
        flash('Erro ao carregar configurações biométricas', 'error')
        return render_template('configuracoes/biometria.html', 
                             titulo='Configurações Biométricas',
                             service_running=False,
                             registered_devices={},
                             last_discovery='Erro',
                             settings=config_manager.default_settings)

# COMENTADO: Removido uso do backend para iniciar serviço - agora usamos bridge local
# @biometric_config_bp.route('/api/service/start', methods=['POST'])
# @require_admin
# def api_start_service():
#     """API para iniciar o serviço biométrico"""
#     result = config_manager.start_service()
#     return jsonify(result)

# COMENTADO: Removido uso do backend para checar status - agora checamos bridge local direto
# @biometric_config_bp.route('/api/service/status', methods=['GET'])
# @require_admin
# def api_service_status():
#     """API para verificar status do serviço biométrico"""
#     is_running = config_manager.is_service_running()
#     return jsonify({'running': is_running})

# COMENTADO: Removido uso do backend para descoberta - agora acessamos bridge local direto
# @biometric_config_bp.route('/api/devices/discover', methods=['POST'])
# @require_admin
# def api_discover_devices():
#     """API para descoberta de dispositivos biométricos"""
#     result = config_manager.discover_devices()
#     return jsonify(result)

# COMENTADO: Removido uso do backend para testar dispositivo - agora acessamos bridge local direto
# @biometric_config_bp.route('/api/devices/test', methods=['POST'])
# @require_admin
# def api_test_device():
#     """API para testar captura biométrica"""
#     device_id = request.json.get('device_id')
#     result = config_manager.test_device(device_id)
#     return jsonify(result)

# Mantido: API para atualização de configurações (sem envolvimento direto com detecção hardware)
@biometric_config_bp.route('/api/settings/update', methods=['POST'])
@require_admin
def api_update_settings():
    """API para atualizar configurações biométricas"""
    try:
        data = request.json
        
        # Atualizar apenas campos permitidos
        allowed_fields = ['sensitivity_level', 'timeout_seconds', 'max_attempts', 'quality_threshold', 'auto_discovery']
        
        for field in allowed_fields:
            if field in data:
                config_manager.settings[field] = data[field]
        
        # Salvar configurações
        if config_manager.save_settings():
            return jsonify({
                'success': True,
                'message': 'Configurações atualizadas com sucesso',
                'settings': config_manager.settings
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Erro ao salvar configurações'
            }), 500
    
    except Exception as e:
        logger.error(f"Erro ao atualizar configurações: {e}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

# Mantido: API para remover dispositivo (gerenciamento apenas)
@biometric_config_bp.route('/api/devices/remove', methods=['POST'])
@require_admin
def api_remove_device():
    """API para remover um dispositivo registrado"""
    try:
        device_id = request.json.get('device_id')
        
        if not device_id:
            return jsonify({
                'success': False,
                'message': 'ID do dispositivo não fornecido'
            }), 400
        
        # Remover do dicionário
        if device_id in config_manager.settings['registered_devices']:
            del config_manager.settings['registered_devices'][device_id]
            
            # Salvar configurações
            if config_manager.save_settings():
                return jsonify({
                    'success': True,
                    'message': 'Dispositivo removido com sucesso'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Erro ao salvar configurações'
                }), 500
        else:
            return jsonify({
                'success': False,
                'message': 'Dispositivo não encontrado'
            }), 404
    
    except Exception as e:
        logger.error(f"Erro ao remover dispositivo: {e}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

# Mantido: API para registrar dispositivo (gerenciamento)
@biometric_config_bp.route('/api/devices/register', methods=['POST'])
@require_admin
def api_register_device():
    """API para registrar um dispositivo permanentemente"""
    try:
        data = request.json
        device_id = data.get('device_id')
        device_info = data.get('device_info', {})
        
        if not device_id:
            return jsonify({
                'success': False,
                'message': 'ID do dispositivo não fornecido'
            }), 400
        
        # Registrar dispositivo localmente
        config_manager.settings['registered_devices'][device_id] = {
            'info': device_info,
            'registered_at': datetime.now().isoformat(),
            'status': 'active'
        }
        
        # Salvar configurações
        if config_manager.save_settings():
            return jsonify({
                'success': True,
                'message': 'Dispositivo registrado com sucesso'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Erro ao salvar configurações'
            }), 500
    
    except Exception as e:
        logger.error(f"Erro ao registrar dispositivo: {e}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500 