#!/bin/bash

# Nome do arquivo de saída
OUTPUT_FILE="/root/info_servidor_$(date +%Y%m%d_%H%M%S).txt"

# Função para adicionar separador
add_separator() {
    echo -e "\n===========================================================" >> "$OUTPUT_FILE"
    echo "📌 $1" >> "$OUTPUT_FILE"
    echo "===========================================================" >> "$OUTPUT_FILE"
}

# Iniciar arquivo
echo "🔍 Relatório de Análise do Servidor" > "$OUTPUT_FILE"
echo "Data: $(date '+%d/%m/%Y %H:%M:%S')" >> "$OUTPUT_FILE"
echo "Hostname: $(hostname)" >> "$OUTPUT_FILE"
echo "Sistema: $(uname -a)" >> "$OUTPUT_FILE"

# Informações do Sistema
add_separator "Informações do Sistema"
echo "🖥️ CPU:" >> "$OUTPUT_FILE"
lscpu | grep -E "^(Model name|Architecture|CPU\(s\)|Thread|Core|Socket)" >> "$OUTPUT_FILE"
echo -e "\n💾 Memória:" >> "$OUTPUT_FILE"
free -h >> "$OUTPUT_FILE"
echo -e "\n💽 Disco:" >> "$OUTPUT_FILE"
df -h >> "$OUTPUT_FILE"

# Serviços em Execução
add_separator "Status dos Serviços"
echo "🚦 Nginx Status:" >> "$OUTPUT_FILE"
systemctl status nginx >> "$OUTPUT_FILE" 2>&1
echo -e "\n🚦 MySQL Status:" >> "$OUTPUT_FILE"
systemctl status mysql >> "$OUTPUT_FILE" 2>&1
echo -e "\n🚦 Supervisor Status:" >> "$OUTPUT_FILE"
systemctl status supervisor >> "$OUTPUT_FILE" 2>&1
echo -e "\n🚦 Todos os Serviços Ativos:" >> "$OUTPUT_FILE"
systemctl list-units --type=service --state=running >> "$OUTPUT_FILE"

# Portas em Uso
add_separator "Portas em Uso"
netstat -tulpn >> "$OUTPUT_FILE" 2>&1

# Configurações do Nginx
add_separator "Configurações do Nginx"
echo "📁 Sites Disponíveis:" >> "$OUTPUT_FILE"
ls -la /etc/nginx/sites-available/ >> "$OUTPUT_FILE" 2>&1
echo -e "\n📁 Sites Habilitados:" >> "$OUTPUT_FILE"
ls -la /etc/nginx/sites-enabled/ >> "$OUTPUT_FILE" 2>&1
echo -e "\n📄 Configuração do Virtual Host:" >> "$OUTPUT_FILE"
cat /etc/nginx/sites-available/controle-ponto >> "$OUTPUT_FILE" 2>&1

# Configurações do MySQL
add_separator "Informações do MySQL"
echo "🗃️ Bancos de Dados:" >> "$OUTPUT_FILE"
mysql -e "SHOW DATABASES;" >> "$OUTPUT_FILE" 2>&1
echo -e "\n📊 Tabelas do controle_ponto:" >> "$OUTPUT_FILE"
mysql -e "USE controle_ponto; SHOW TABLES;" >> "$OUTPUT_FILE" 2>&1
echo -e "\n👥 Usuários MySQL:" >> "$OUTPUT_FILE"
mysql -e "SELECT user, host FROM mysql.user;" >> "$OUTPUT_FILE" 2>&1

# Estrutura de Diretórios
add_separator "Estrutura de Diretórios"
echo "📂 /var/www:" >> "$OUTPUT_FILE"
ls -la /var/www/ >> "$OUTPUT_FILE" 2>&1
echo -e "\n📂 /var/www/controle-ponto:" >> "$OUTPUT_FILE"
ls -la /var/www/controle-ponto/ >> "$OUTPUT_FILE" 2>&1
echo -e "\n📂 /etc:" >> "$OUTPUT_FILE"
ls -la /etc/nginx/ /etc/supervisor/conf.d/ >> "$OUTPUT_FILE" 2>&1
echo -e "\n📂 /var/log:" >> "$OUTPUT_FILE"
ls -la /var/log/nginx/ /var/log/mysql/ >> "$OUTPUT_FILE" 2>&1

# Logs
add_separator "Últimas Entradas de Log"
echo "📝 Nginx Error Log (últimas 50 linhas):" >> "$OUTPUT_FILE"
tail -n 50 /var/log/nginx/error.log >> "$OUTPUT_FILE" 2>&1
echo -e "\n📝 MySQL Error Log (últimas 50 linhas):" >> "$OUTPUT_FILE"
tail -n 50 /var/log/mysql/error.log >> "$OUTPUT_FILE" 2>&1

# Processos Python
add_separator "Processos Python em Execução"
ps aux | grep python >> "$OUTPUT_FILE"

# Informações do Git
add_separator "Informações do Git"
if [ -d "/var/www/controle-ponto" ]; then
    cd /var/www/controle-ponto
    echo "📦 Branch Atual:" >> "$OUTPUT_FILE"
    git branch >> "$OUTPUT_FILE" 2>&1
    echo -e "\n📦 Último Commit:" >> "$OUTPUT_FILE"
    git log -1 >> "$OUTPUT_FILE" 2>&1
    echo -e "\n📦 Status:" >> "$OUTPUT_FILE"
    git status >> "$OUTPUT_FILE" 2>&1
fi

# Permissões e Propriedade
add_separator "Permissões e Propriedade"
echo "👤 Usuários do Sistema:" >> "$OUTPUT_FILE"
cat /etc/passwd | grep -E "www-data|nginx|mysql" >> "$OUTPUT_FILE"
echo -e "\n👥 Grupos:" >> "$OUTPUT_FILE"
cat /etc/group | grep -E "www-data|nginx|mysql" >> "$OUTPUT_FILE"

# Cron Jobs
add_separator "Cron Jobs"
echo "⏰ Crontab do Root:" >> "$OUTPUT_FILE"
crontab -l >> "$OUTPUT_FILE" 2>&1

# Firewall
add_separator "Configuração do Firewall"
echo "🛡️ Status do UFW:" >> "$OUTPUT_FILE"
ufw status verbose >> "$OUTPUT_FILE" 2>&1

# Finalização
add_separator "Resumo"
echo "✅ Coleta de informações concluída" >> "$OUTPUT_FILE"
echo "📍 Arquivo salvo em: $OUTPUT_FILE" >> "$OUTPUT_FILE"

# Permissões do arquivo
chmod 600 "$OUTPUT_FILE"

echo "✅ Coleta de informações concluída!"
echo "📍 Arquivo salvo em: $OUTPUT_FILE"
echo "⚠️ Por segurança, o arquivo tem permissões restritas (600)" 