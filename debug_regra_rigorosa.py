#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para debugar a regra rigorosa que está limitando as horas
"""

import sys
import os
from datetime import datetime, time

# Adicionar o diretório do projeto ao path
sys.path.insert(0, '/var/www/controle-ponto')

def testar_regra_rigorosa():
    """Testa a regra rigorosa com os dados do problema"""
    
    print("🔍 DEBUG: REGRA RIGOROSA - LIMITAÇÃO DE HORAS")
    print("=" * 60)
    
    # Dados exatos do problema
    registro = {
        'entrada': time(8, 43),
        'saida_almoco': time(12, 56),
        'retorno_almoco': time(14, 10),
        'saida': time(17, 55)
    }
    
    print(f"📋 DADOS DO PROBLEMA:")
    print(f"   Entrada: {registro['entrada']}")
    print(f"   Saída Almoço: {registro['saida_almoco']}")
    print(f"   Retorno Almoço: {registro['retorno_almoco']}")
    print(f"   Saída: {registro['saida']}")
    print()
    
    try:
        from app_ponto_admin import aplicar_regra_rigorosa_com_jornada, calcular_horas_separadas_dia
        
        # Testar regra rigorosa (assumindo funcionario_id = 49 do exemplo)
        funcionario_id = 49
        
        print(f"🔧 TESTANDO REGRA RIGOROSA (funcionario_id={funcionario_id}):")
        horas_limitadas, alertas = aplicar_regra_rigorosa_com_jornada(registro, funcionario_id)
        
        print(f"   Resultado: {horas_limitadas}h")
        print(f"   Alertas: {len(alertas)} alertas")
        
        for i, alerta in enumerate(alertas, 1):
            print(f"   {i}. {alerta}")
        
        print()
        
        # Testar função completa
        print(f"🔧 TESTANDO FUNÇÃO COMPLETA:")
        horas_normais, horas_extras, horas_negativas = calcular_horas_separadas_dia(registro, funcionario_id)
        
        print(f"   Horas Normais: {horas_normais}h")
        print(f"   Horas Extras: {horas_extras}h")
        print(f"   Horas Negativas: {horas_negativas}h")
        print(f"   Total: {horas_normais + horas_extras}h")
        
        # Comparar com cálculo sem limitação
        print(f"\n📊 COMPARAÇÃO:")
        
        # Cálculo sem limitação (manual)
        entrada_dt = datetime.combine(datetime.today(), registro['entrada'])
        saida_almoco_dt = datetime.combine(datetime.today(), registro['saida_almoco'])
        retorno_almoco_dt = datetime.combine(datetime.today(), registro['retorno_almoco'])
        saida_dt = datetime.combine(datetime.today(), registro['saida'])
        
        periodo_manha_real = (saida_almoco_dt - entrada_dt).total_seconds() / 3600
        periodo_tarde_real = (saida_dt - retorno_almoco_dt).total_seconds() / 3600
        total_real = periodo_manha_real + periodo_tarde_real
        
        print(f"   Sem limitação: {total_real:.2f}h")
        print(f"   Com limitação: {horas_limitadas:.2f}h")
        print(f"   Diferença: {total_real - horas_limitadas:.2f}h")
        
        # Calcular minutos perdidos
        minutos_perdidos = (total_real - horas_limitadas) * 60
        print(f"   Minutos perdidos: {minutos_perdidos:.0f} min")
        
        # Verificar se é exatamente o problema reportado
        if abs(horas_limitadas - 7.27) < 0.05:
            print(f"\n🎯 PROBLEMA IDENTIFICADO!")
            print(f"   A regra rigorosa está limitando as horas de {total_real:.2f}h para {horas_limitadas:.2f}h")
            print(f"   Isso explica o valor 7.27h mostrado no sistema")
            
            # Analisar limitações específicas
            print(f"\n🔍 ANÁLISE DAS LIMITAÇÕES:")
            
            # Verificar limitação de entrada
            entrada_oficial = time(9, 0)  # Assumindo 09:00 como entrada oficial
            if registro['entrada'] < entrada_oficial:
                minutos_entrada_perdidos = (datetime.combine(datetime.today(), entrada_oficial) - 
                                          datetime.combine(datetime.today(), registro['entrada'])).total_seconds() / 60
                print(f"   Entrada limitada: {registro['entrada']} → {entrada_oficial} ({minutos_entrada_perdidos:.0f}min perdidos)")
            
            # Verificar limitação de saída
            saida_oficial = time(18, 0)  # Assumindo 18:00 como saída oficial
            if registro['saida'] > saida_oficial:
                minutos_saida_perdidos = (datetime.combine(datetime.today(), registro['saida']) - 
                                        datetime.combine(datetime.today(), saida_oficial)).total_seconds() / 60
                print(f"   Saída limitada: {registro['saida']} → {saida_oficial} ({minutos_saida_perdidos:.0f}min perdidos)")
            else:
                print(f"   Saída OK: {registro['saida']} (dentro do limite)")
        
    except Exception as e:
        print(f"❌ Erro ao testar regra rigorosa: {e}")
        import traceback
        print(traceback.format_exc())

def main():
    testar_regra_rigorosa()

if __name__ == "__main__":
    main()
