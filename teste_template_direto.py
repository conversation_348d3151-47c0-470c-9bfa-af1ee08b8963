#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TESTE DIRETO DO TEMPLATE
===========================

Simula a lógica do template corrigido diretamente.
"""

def testar_logica_template():
    """
    Testa a lógica do template corrigido.
    """
    print("🧪 TESTE DIRETO DA LÓGICA DO TEMPLATE")
    print("=" * 50)
    
    # Simular dados como vem do backend
    # Richardson: 1.095556h (1h 05min 44seg)
    total_decimal = 1.095556
    
    print(f"📊 Dados de entrada:")
    print(f"   Total decimal: {total_decimal}")
    
    # Lógica ANTIGA (problemática)
    print(f"\n❌ LÓGICA ANTIGA:")
    horas_inteiras_old = int(total_decimal)
    minutos_decimal_old = (total_decimal - horas_inteiras_old) * 60
    minutos_old = int(minutos_decimal_old)
    print(f"   Horas: {horas_inteiras_old}")
    print(f"   Minutos decimal: {minutos_decimal_old}")
    print(f"   Minutos: {minutos_old}")
    print(f"   Resultado: {horas_inteiras_old}h {minutos_old:02d}min")
    
    # Lógica NOVA (corrigida)
    print(f"\n✅ LÓGICA NOVA (via segundos):")
    segundos_totais = int(total_decimal * 3600)
    horas_inteiras_new = int(segundos_totais // 3600)
    minutos_inteiros_new = int((segundos_totais % 3600) // 60)
    print(f"   Segundos totais: {segundos_totais}")
    print(f"   Horas: {horas_inteiras_new}")
    print(f"   Minutos: {minutos_inteiros_new}")
    print(f"   Resultado: {horas_inteiras_new}h {minutos_inteiros_new:02d}min")
    
    # Verificação manual
    print(f"\n🔍 VERIFICAÇÃO MANUAL:")
    print(f"   1.095556h = {total_decimal * 3600} segundos")
    print(f"   3944 segundos = 1h 5min 44seg")
    print(f"   Esperado: 1h 05min")
    
    if f"{horas_inteiras_new}h {minutos_inteiros_new:02d}min" == "1h 05min":
        print(f"\n🎉 LÓGICA CORRIGIDA FUNCIONA!")
        return True
    else:
        print(f"\n❌ LÓGICA AINDA TEM PROBLEMA!")
        return False

if __name__ == "__main__":
    testar_logica_template()
