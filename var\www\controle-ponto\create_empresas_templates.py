#!/usr/bin/env python3
import paramiko
import os

def create_empresas_templates():
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("Conectado ao servidor com sucesso!")
        
        # Template para listar empresas
        empresas_list_template = '''{% extends "base.html" %}

{% block title %}Gerenciar Empresas{% endblock %}

{% block extra_css %}
<style>
    .empresas-container {
        padding: 2rem 0;
    }
    
    .empresa-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: transform 0.2s ease;
    }
    
    .empresa-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .empresa-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .empresa-info h5 {
        color: #2563eb;
        margin-bottom: 0.5rem;
    }
    
    .empresa-info p {
        color: #64748b;
        margin: 0;
    }
    
    .empresa-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .status-ativa {
        background: #dcfce7;
        color: #166534;
    }
    
    .status-inativa {
        background: #fee2e2;
        color: #991b1b;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #64748b;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #cbd5e1;
    }
</style>
{% endblock %}

{% block content %}
<div class="empresas-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-building me-2"></i>Gerenciar Empresas</h2>
            <p class="text-muted">Visualize e gerencie as empresas cadastradas no sistema</p>
        </div>
        <a href="{{ url_for('configuracoes.nova_empresa') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Nova Empresa
        </a>
    </div>

    {% if empresas %}
        <div class="row">
            {% for empresa in empresas %}
            <div class="col-md-6 col-lg-4">
                <div class="empresa-card">
                    <div class="empresa-header">
                        <div class="empresa-info flex-grow-1">
                            <h5>{{ empresa.razao_social }}</h5>
                            {% if empresa.nome_fantasia %}
                                <p><strong>Nome Fantasia:</strong> {{ empresa.nome_fantasia }}</p>
                            {% endif %}
                            {% if empresa.cnpj %}
                                <p><strong>CNPJ:</strong> {{ empresa.cnpj }}</p>
                            {% endif %}
                            <p><strong>Funcionários:</strong> {{ empresa.total_funcionarios or 0 }}</p>
                        </div>
                        <div>
                            {% if empresa.ativa %}
                                <span class="status-badge status-ativa">Ativa</span>
                            {% else %}
                                <span class="status-badge status-inativa">Inativa</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="empresa-actions">
                        <a href="{{ url_for('configuracoes.editar_empresa', empresa_id=empresa.id) }}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Editar
                        </a>
                        {% if empresa.ativa %}
                            <button class="btn btn-outline-danger btn-sm" 
                                    onclick="confirmarExclusao({{ empresa.id }}, '{{ empresa.razao_social }}')">
                                <i class="fas fa-trash"></i> Desativar
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <i class="fas fa-building"></i>
            <h4>Nenhuma empresa cadastrada</h4>
            <p>Comece cadastrando a primeira empresa do sistema</p>
            <a href="{{ url_for('configuracoes.nova_empresa') }}" class="btn btn-primary mt-3">
                <i class="fas fa-plus me-2"></i>Cadastrar Primeira Empresa
            </a>
        </div>
    {% endif %}
</div>

<!-- Modal de Confirmação -->
<div class="modal fade" id="modalExclusao" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Desativação</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja desativar a empresa <strong id="nomeEmpresa"></strong>?</p>
                <p class="text-muted">Esta ação pode ser revertida posteriormente.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form id="formExclusao" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Desativar</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmarExclusao(empresaId, nomeEmpresa) {
    document.getElementById('nomeEmpresa').textContent = nomeEmpresa;
    document.getElementById('formExclusao').action = `/configuracoes/empresas/${empresaId}/excluir`;
    new bootstrap.Modal(document.getElementById('modalExclusao')).show();
}
</script>
{% endblock %}'''

        # Template para nova empresa
        nova_empresa_template = '''{% extends "base.html" %}

{% block title %}Nova Empresa{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 800px;
        margin: 2rem auto;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 2rem;
    }
    
    .form-header {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .form-header h2 {
        color: #1e293b;
        margin-bottom: 0.5rem;
    }
    
    .form-header p {
        color: #64748b;
        margin: 0;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
    }
    
    .form-control {
        border-radius: 8px;
        border: 1px solid #d1d5db;
        padding: 0.75rem;
    }
    
    .form-control:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .btn-group-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }
    
    .required {
        color: #ef4444;
    }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
    <div class="form-header">
        <h2><i class="fas fa-building me-2"></i>Nova Empresa</h2>
        <p>Cadastre uma nova empresa no sistema</p>
    </div>

    <form method="POST" action="{{ url_for('configuracoes.nova_empresa') }}">
        <div class="row">
            <div class="col-md-8">
                <div class="form-group">
                    <label for="razao_social" class="form-label">
                        Razão Social <span class="required">*</span>
                    </label>
                    <input type="text" class="form-control" id="razao_social" name="razao_social" 
                           value="{{ request.form.get('razao_social', '') }}" required>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="ativa" class="form-label">Status</label>
                    <select class="form-control" id="ativa" name="ativa">
                        <option value="1" {{ 'selected' if request.form.get('ativa') == '1' else '' }}>Ativa</option>
                        <option value="0" {{ 'selected' if request.form.get('ativa') == '0' else '' }}>Inativa</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="form-group">
                    <label for="nome_fantasia" class="form-label">Nome Fantasia</label>
                    <input type="text" class="form-control" id="nome_fantasia" name="nome_fantasia" 
                           value="{{ request.form.get('nome_fantasia', '') }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="cnpj" class="form-label">CNPJ</label>
                    <input type="text" class="form-control" id="cnpj" name="cnpj" 
                           value="{{ request.form.get('cnpj', '') }}" placeholder="00.000.000/0000-00">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="telefone" class="form-label">Telefone</label>
                    <input type="text" class="form-control" id="telefone" name="telefone" 
                           value="{{ request.form.get('telefone', '') }}" placeholder="(00) 0000-0000">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="email" class="form-label">E-mail</label>
                    <input type="email" class="form-control" id="email" name="email" 
                           value="{{ request.form.get('email', '') }}" placeholder="<EMAIL>">
                </div>
            </div>
        </div>

        <div class="btn-group-actions">
            <a href="{{ url_for('configuracoes.listar_empresas') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Voltar
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>Salvar Empresa
            </button>
        </div>
    </form>
</div>

<script>
// Máscara para CNPJ
document.getElementById('cnpj').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    value = value.replace(/^(\d{2})(\d)/, '$1.$2');
    value = value.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
    value = value.replace(/\.(\d{3})(\d)/, '.$1/$2');
    value = value.replace(/(\d{4})(\d)/, '$1-$2');
    e.target.value = value;
});

// Máscara para telefone
document.getElementById('telefone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    value = value.replace(/^(\d{2})(\d)/, '($1) $2');
    value = value.replace(/(\d{4})(\d)/, '$1-$2');
    e.target.value = value;
});
</script>
{% endblock %}'''

        # Criar diretório de templates se não existir
        print("Criando diretório de templates...")
        stdin, stdout, stderr = ssh.exec_command('mkdir -p /var/www/controle-ponto/templates/configuracoes')
        
        # Criar template de listagem
        print("Criando template de listagem de empresas...")
        stdin, stdout, stderr = ssh.exec_command(f'cat > /var/www/controle-ponto/templates/configuracoes/empresas.html << "EOF"\n{empresas_list_template}\nEOF')
        
        error = stderr.read().decode()
        if error:
            print(f"Erro ao criar template de listagem: {error}")
        else:
            print("Template de listagem criado com sucesso!")
        
        # Criar template de nova empresa
        print("Criando template de nova empresa...")
        stdin, stdout, stderr = ssh.exec_command(f'cat > /var/www/controle-ponto/templates/configuracoes/nova_empresa.html << "EOF"\n{nova_empresa_template}\nEOF')
        
        error = stderr.read().decode()
        if error:
            print(f"Erro ao criar template de nova empresa: {error}")
        else:
            print("Template de nova empresa criado com sucesso!")
        
        # Verificar se os arquivos foram criados
        print("Verificando arquivos criados...")
        stdin, stdout, stderr = ssh.exec_command('ls -la /var/www/controle-ponto/templates/configuracoes/')
        files = stdout.read().decode()
        print("Arquivos no diretório templates/configuracoes:")
        print(files)
        
        # Reiniciar o serviço
        print("Reiniciando serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl restart controle-ponto')
        
        ssh.close()
        print("Templates criados com sucesso!")
        
    except Exception as e:
        print(f"Erro durante a criação dos templates: {e}")

if __name__ == "__main__":
    create_empresas_templates()
