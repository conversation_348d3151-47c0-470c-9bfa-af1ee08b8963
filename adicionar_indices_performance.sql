-- ✅ CORREÇÃO #6: Adicionar Índices de Performance
-- Script para melhorar performance do sistema RLPONTO-WEB
-- Data: 10/01/2025
-- Versão: 1.0

USE controle_ponto;

-- Desabilitar verificação de foreign key temporariamente
SET foreign_key_checks = 0;

-- =========================================================================
-- 📧 ÍNDICE PARA funcionarios.email
-- =========================================================================
-- Melhora performance em consultas por email (login, validações, etc)
DROP INDEX IF EXISTS idx_funcionarios_email ON funcionarios;
CREATE INDEX idx_funcionarios_email ON funcionarios (email);

-- =========================================================================
-- 📊 ÍNDICE COMPOSTO OTIMIZADO para registros_ponto
-- =========================================================================
-- Índice para consultas de relatórios: funcionario_id + data_ponto
-- Este é o índice mais importante para performance dos relatórios
DROP INDEX IF EXISTS idx_funcionario_data_ponto ON registros_ponto;
CREATE INDEX idx_funcionario_data_ponto ON registros_ponto (funcionario_id, data_registro);

-- =========================================================================
-- 🔍 ÍNDICES ADICIONAIS PARA logs_sistema  
-- =========================================================================
-- Já existe idx_data_hora, verificando se precisamos de outros

-- Índice para consultas por usuário + data (relatórios de auditoria)
DROP INDEX IF EXISTS idx_usuario_data_logs ON logs_sistema;
CREATE INDEX idx_usuario_data_logs ON logs_sistema (usuario_id, data_hora);

-- Índice para consultas por ação + data (monitoramento específico)
DROP INDEX IF EXISTS idx_acao_data_logs ON logs_sistema;
CREATE INDEX idx_acao_data_logs ON logs_sistema (acao, data_hora);

-- =========================================================================
-- 📈 ÍNDICES PARA OTIMIZAÇÃO DE VIEWS E RELATÓRIOS
-- =========================================================================

-- Índice para funcionarios por setor (relatórios por departamento)
DROP INDEX IF EXISTS idx_funcionarios_setor_status ON funcionarios;
CREATE INDEX idx_funcionarios_setor_status ON funcionarios (setor_obra, status_cadastro);

-- Índice para data de admissão (relatórios de RH)
DROP INDEX IF EXISTS idx_funcionarios_data_admissao ON funcionarios;
CREATE INDEX idx_funcionarios_data_admissao ON funcionarios (data_admissao);

-- Índice para registros_ponto por método (análise biométrica vs manual)
DROP INDEX IF EXISTS idx_registros_metodo_data ON registros_ponto;
CREATE INDEX idx_registros_metodo_data ON registros_ponto (metodo_registro, data_registro);

-- Índice para análise de qualidade biométrica
DROP INDEX IF EXISTS idx_registros_qualidade ON registros_ponto;
CREATE INDEX idx_registros_qualidade ON registros_ponto (qualidade_biometria, metodo_registro);

-- =========================================================================
-- 🔧 VERIFICAÇÃO DOS ÍNDICES CRIADOS
-- =========================================================================

-- Consulta para verificar os índices criados
SELECT 
    TABLE_NAME as 'Tabela',
    INDEX_NAME as 'Índice',
    COLUMN_NAME as 'Coluna',
    SEQ_IN_INDEX as 'Ordem',
    NON_UNIQUE as 'Não_Único'
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'controle_ponto' 
    AND INDEX_NAME LIKE 'idx_%'
    AND INDEX_NAME NOT LIKE 'PRIMARY'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- =========================================================================
-- 📊 ANÁLISE DE PERFORMANCE
-- =========================================================================

-- Verificar tamanho das tabelas após criação dos índices
SELECT 
    table_name AS 'Tabela',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Tamanho_MB',
    ROUND((index_length / 1024 / 1024), 2) AS 'Índices_MB',
    table_rows AS 'Linhas'
FROM information_schema.tables 
WHERE table_schema = 'controle_ponto' 
    AND table_type = 'BASE TABLE'
ORDER BY (data_length + index_length) DESC;

-- Reabilitar verificação de foreign key
SET foreign_key_checks = 1;

-- =========================================================================
-- 📝 LOG DA OPERAÇÃO
-- =========================================================================

INSERT INTO logs_sistema (
    usuario_id, 
    acao, 
    tabela_afetada, 
    detalhes, 
    data_hora
) VALUES (
    NULL,
    'INDICES_PERFORMANCE_CRIADOS',
    'funcionarios,registros_ponto,logs_sistema',
    JSON_OBJECT(
        'indices_criados', 8,
        'tabelas_afetadas', 3,
        'descricao', 'Criação de índices para otimização de performance - Correção #6',
        'script_versao', '1.0'
    ),
    NOW()
);

-- =========================================================================
-- ✅ SCRIPT CONCLUÍDO
-- =========================================================================

SELECT 
    '✅ ÍNDICES DE PERFORMANCE CRIADOS COM SUCESSO!' as Status,
    NOW() as Timestamp,
    '8 novos índices criados' as Resultado,
    'funcionarios, registros_ponto, logs_sistema' as Tabelas_Afetadas; 