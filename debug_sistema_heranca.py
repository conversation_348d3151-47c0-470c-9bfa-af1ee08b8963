#!/usr/bin/env python3
"""
Debug: Por que o sistema de herança dinâmica não está funcionando?
Investigar triggers, lógica de herança e aplicação automática
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def debug_sistema_heranca():
    """Debug completo do sistema de herança"""
    
    print("🔍 DEBUG: SISTEMA DE HERANÇA DINÂMICA")
    print("=" * 60)
    
    db = DatabaseManager()
    
    # 1. Verificar se triggers existem
    print("\n📋 PASSO 1: Verificando triggers...")
    triggers = db.execute_query("""
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, ACTION_TIMING, ACTION_STATEMENT
        FROM INFORMATION_SCHEMA.TRIGGERS 
        WHERE TRIGGER_SCHEMA = 'controle_ponto'
        AND TRIGGER_NAME LIKE '%jornada%'
    """)
    
    if triggers:
        print(f"✅ Encontrados {len(triggers)} triggers:")
        for trigger in triggers:
            print(f"   • {trigger['TRIGGER_NAME']} - {trigger['ACTION_TIMING']} {trigger['EVENT_MANIPULATION']}")
    else:
        print("❌ NENHUM trigger encontrado!")
        return
    
    # 2. Verificar funcionários que deveriam usar herança
    print("\n📋 PASSO 2: Verificando funcionários que deveriam usar herança...")
    
    funcionarios_problema = db.execute_query("""
        SELECT 
            f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id, f.usa_horario_empresa,
            e.razao_social as empresa_nome,
            jt_func.nome_jornada as jornada_funcionario,
            jt_func.seg_qui_entrada as func_seg_entrada,
            jt_func.seg_qui_saida as func_seg_saida,
            jt_empresa.id as empresa_jornada_id,
            jt_empresa.nome_jornada as jornada_empresa,
            jt_empresa.seg_qui_entrada as emp_seg_entrada,
            jt_empresa.seg_qui_saida as emp_seg_saida
        FROM funcionarios f
        INNER JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN jornadas_trabalho jt_func ON f.jornada_trabalho_id = jt_func.id
        LEFT JOIN jornadas_trabalho jt_empresa ON (
            jt_empresa.empresa_id = f.empresa_id 
            AND jt_empresa.padrao = 1 
            AND jt_empresa.ativa = 1
        )
        WHERE f.ativo = 1
        AND jt_empresa.id IS NOT NULL
        AND (
            f.jornada_trabalho_id != jt_empresa.id OR
            f.jornada_trabalho_id IS NULL OR
            f.usa_horario_empresa = FALSE
        )
        ORDER BY f.empresa_id, f.id
    """)
    
    if funcionarios_problema:
        print(f"❌ Encontrados {len(funcionarios_problema)} funcionários com problemas de herança:")
        
        for func in funcionarios_problema:
            print(f"\n   👤 {func['nome_completo']} (ID: {func['id']})")
            print(f"      • Empresa: {func['empresa_nome']} (ID: {func['empresa_id']})")
            print(f"      • Jornada funcionário: {func['jornada_funcionario']} (ID: {func['jornada_trabalho_id']})")
            print(f"      • Jornada empresa: {func['jornada_empresa']} (ID: {func['empresa_jornada_id']})")
            print(f"      • Usa herança: {func['usa_horario_empresa']}")
            
            if func['func_seg_entrada'] and func['emp_seg_entrada']:
                print(f"      • Horário funcionário: {func['func_seg_entrada']}-{func['func_seg_saida']}")
                print(f"      • Horário empresa: {func['emp_seg_entrada']}-{func['emp_seg_saida']}")
                
                if func['func_seg_entrada'] != func['emp_seg_entrada']:
                    print(f"      ❌ HORÁRIOS DIFERENTES!")
    else:
        print("✅ Todos os funcionários estão com herança correta")
    
    # 3. Verificar se o sistema está sendo chamado no cadastro
    print("\n📋 PASSO 3: Verificando lógica de cadastro...")
    
    # Simular cadastro de funcionário
    print("🧪 Simulando cadastro de funcionário na empresa 4...")
    
    # Verificar se existe jornada padrão para empresa 4
    jornada_empresa_4 = db.execute_query("""
        SELECT id, nome_jornada, seg_qui_entrada, seg_qui_saida
        FROM jornadas_trabalho 
        WHERE empresa_id = 4 AND padrao = 1 AND ativa = 1
    """, fetch_one=True)
    
    if jornada_empresa_4:
        print(f"✅ Empresa 4 tem jornada padrão: {jornada_empresa_4['nome_jornada']} (ID: {jornada_empresa_4['id']})")
        print(f"   Horários: {jornada_empresa_4['seg_qui_entrada']}-{jornada_empresa_4['seg_qui_saida']}")
    else:
        print("❌ Empresa 4 NÃO tem jornada padrão!")
    
    # 4. Verificar se função de herança está sendo chamada
    print("\n📋 PASSO 4: Testando função de herança...")
    
    try:
        from sistema_heranca_jornadas import SistemaHerancaJornadas
        
        # Testar função de aplicar herança
        if funcionarios_problema:
            func_teste = funcionarios_problema[0]
            print(f"🧪 Testando aplicação de herança para {func_teste['nome_completo']}...")
            
            resultado = SistemaHerancaJornadas.aplicar_jornada_empresa_funcionario(
                func_teste['id'], 
                func_teste['empresa_id'], 
                'admin'
            )
            
            print(f"✅ Resultado: {resultado}")
        
    except Exception as e:
        print(f"❌ Erro ao testar função de herança: {e}")
    
    # 5. Verificar se triggers estão sendo executados
    print("\n📋 PASSO 5: Verificando execução de triggers...")
    
    # Verificar logs de mudança recentes
    logs_recentes = db.execute_query("""
        SELECT COUNT(*) as total FROM log_mudancas_jornada 
        WHERE data_mudanca >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    """, fetch_one=True)
    
    print(f"📊 Logs de mudança na última hora: {logs_recentes['total']}")
    
    if logs_recentes['total'] == 0:
        print("⚠️ Nenhum log de mudança recente - triggers podem não estar funcionando")
    
    print(f"\n🎯 DIAGNÓSTICO:")
    if funcionarios_problema:
        print(f"❌ PROBLEMA IDENTIFICADO: {len(funcionarios_problema)} funcionários sem herança correta")
        print(f"💡 SOLUÇÃO: O sistema de herança precisa ser aplicado automaticamente")
    else:
        print(f"✅ Sistema de herança funcionando corretamente")

if __name__ == "__main__":
    debug_sistema_heranca()
