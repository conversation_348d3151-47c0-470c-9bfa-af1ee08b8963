@echo off
:: =====================================================================
:: INSTALADOR BRIDGE ZK4500 - RLPONTO-WEB v1.0
:: Instala o bridge biométrico como serviço do Windows
:: Desenvolvido por: <PERSON> Rodrigues - AiNexus Tecnologia
:: =====================================================================

setlocal enabledelayedexpansion

:: Incluir sistema de logging
if exist "%~dp0bridge_logger.bat" (
    call "%~dp0bridge_logger.bat" :START_SECTION "INSTALACAO_BRIDGE_ZK4500"
    call "%~dp0bridge_logger.bat" :SYSTEM_INFO
    set LOGGING_ENABLED=1
) else (
    set LOGGING_ENABLED=0
)

echo.
echo ========================================================
echo   INSTALADOR BRIDGE ZK4500 - RLPONTO-WEB v1.0
echo   AiNexus Tecnologia - Sistema Biometrico Empresarial
echo ========================================================
echo.

:: Verificar se está executando como administrador
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Executando como Administrador
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Instalacao iniciada como Administrador"
) else (
    echo [ERRO] Este instalador precisa ser executado como Administrador!
    echo        Clique com botao direito e selecione "Executar como administrador"
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Tentativa de instalacao sem permissoes de administrador"
    pause
    exit /b 1
)

:: Definir variáveis
set BRIDGE_NAME=RLPonto-BridgeZK4500
set BRIDGE_DIR=C:\RLPonto-Bridge
set SERVICE_DESC=RLPONTO-WEB Bridge Biometrico ZK4500 - Comunicacao hibrida Windows-Linux
set PYTHON_EXE=python.exe
set SOURCE_DIR=%~dp0

if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Diretorio de origem: %SOURCE_DIR%"
if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Diretorio de destino: %BRIDGE_DIR%"

echo.
echo [1/7] Verificando Python...
%PYTHON_EXE% --version >nul 2>&1
if %errorLevel% == 0 (
    for /f "tokens=*" %%i in ('%PYTHON_EXE% --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo [OK] !PYTHON_VERSION! encontrado
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "PYTHON_CHECK" "SUCCESS" "!PYTHON_VERSION!"
) else (
    echo [ERRO] Python nao encontrado no PATH!
    echo        Instale Python 3.8+ e adicione ao PATH do sistema
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Python nao encontrado no PATH"
    pause
    exit /b 1
)

echo.
echo [2/7] Criando diretorio de instalacao...
if not exist "%BRIDGE_DIR%" (
    mkdir "%BRIDGE_DIR%"
    if %errorLevel% == 0 (
        echo [OK] Diretorio criado: %BRIDGE_DIR%
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "CREATE_DIR" "SUCCESS" "%BRIDGE_DIR%"
    ) else (
        echo [ERRO] Falha ao criar diretorio: %BRIDGE_DIR%
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Falha ao criar diretorio %BRIDGE_DIR%"
        pause
        exit /b 1
    )
) else (
    echo [OK] Diretorio ja existe: %BRIDGE_DIR%
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "CREATE_DIR" "EXISTS" "%BRIDGE_DIR%"
)

echo.
echo [3/7] Copiando arquivos do bridge...

:: Verificar se os arquivos existem no diretório de origem
set FILES_TO_COPY=biometric_bridge_service.py requirements.txt bridge_logger.bat
set COPY_SUCCESS=1

for %%F in (%FILES_TO_COPY%) do (
    if exist "%SOURCE_DIR%%%F" (
        echo [INFO] Copiando %%F...
        copy "%SOURCE_DIR%%%F" "%BRIDGE_DIR%\" >nul 2>&1
        if !errorLevel! == 0 (
            echo [OK] %%F copiado com sucesso
            if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "COPY_FILE" "SUCCESS" "%%F"
        ) else (
            echo [ERRO] Falha ao copiar %%F
            if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Falha ao copiar %%F"
            set COPY_SUCCESS=0
        )
    ) else (
        if "%%F"=="biometric_bridge_service.py" (
            echo [ERRO] Arquivo principal nao encontrado: %%F
            echo        Verifique se todos os arquivos estao no diretorio de instalacao
            if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Arquivo principal nao encontrado: %%F"
            set COPY_SUCCESS=0
        ) else (
            echo [AVISO] Arquivo opcional nao encontrado: %%F
            if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Arquivo opcional nao encontrado: %%F"
        )
    )
)

if %COPY_SUCCESS% == 0 (
    echo [ERRO] Falha critica na copia de arquivos
    echo        Verifique se todos os arquivos necessarios estao presentes
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Falha critica na copia de arquivos"
    pause
    exit /b 1
)

echo.
echo [4/7] Instalando dependencias Python...
cd /d "%BRIDGE_DIR%"
if exist "requirements.txt" (
    echo [INFO] Instalando do requirements.txt...
    %PYTHON_EXE% -m pip install -r requirements.txt >nul 2>&1
    if !errorLevel! == 0 (
        echo [OK] Dependencias instaladas do requirements.txt
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "DEPENDENCIES" "SUCCESS" "requirements.txt"
    ) else (
        echo [AVISO] Erro ao instalar do requirements.txt - tentando metodo alternativo...
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Falha no requirements.txt, tentando metodo alternativo"
        %PYTHON_EXE% -m pip install Flask==3.0.0 Flask-CORS==4.0.0 >nul 2>&1
        if !errorLevel! == 0 (
            echo [OK] Dependencias instaladas ^(Flask 3.0.0, Flask-CORS 4.0.0^)
            if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "DEPENDENCIES" "SUCCESS" "Flask manual"
        ) else (
            echo [ERRO] Falha ao instalar dependencias - verifique Python e conectividade
            if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Falha ao instalar dependencias Python"
        )
    )
) else (
    echo [INFO] requirements.txt nao encontrado - instalando dependencias basicas...
    %PYTHON_EXE% -m pip install Flask==3.0.0 Flask-CORS==4.0.0 >nul 2>&1
    if !errorLevel! == 0 (
        echo [OK] Dependencias basicas instaladas
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "DEPENDENCIES" "SUCCESS" "Flask basico"
    ) else (
        echo [ERRO] Falha ao instalar dependencias - verifique Python e conectividade
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Falha ao instalar dependencias basicas"
    )
)

echo.
echo [5/7] Criando script de servico...
echo @echo off > "%BRIDGE_DIR%\bridge_service.bat"
echo cd /d "%BRIDGE_DIR%" >> "%BRIDGE_DIR%\bridge_service.bat"
echo %PYTHON_EXE% biometric_bridge_service.py >> "%BRIDGE_DIR%\bridge_service.bat"
if !errorLevel! == 0 (
    echo [OK] Script de servico criado
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "SERVICE_SCRIPT" "SUCCESS" "bridge_service.bat"
) else (
    echo [ERRO] Falha ao criar script de servico
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Falha ao criar script de servico"
)

echo.
echo [6/7] Registrando como servico do Windows...
sc create "%BRIDGE_NAME%" binPath= "cmd /c \"%BRIDGE_DIR%\bridge_service.bat\"" start= auto DisplayName= "%SERVICE_DESC%" >nul 2>&1
if !errorLevel! == 0 (
    echo [OK] Servico registrado com sucesso
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "SERVICE_CREATE" "SUCCESS" "%BRIDGE_NAME%"
) else (
    echo [AVISO] Servico pode ja existir - tentando reconfigurar...
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Servico ja existe, reconfigurando"
    sc config "%BRIDGE_NAME%" start= auto >nul 2>&1
    if !errorLevel! == 0 (
        echo [OK] Servico reconfigurado
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "SERVICE_CONFIG" "SUCCESS" "%BRIDGE_NAME%"
    ) else (
        echo [ERRO] Falha ao configurar servico
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Falha ao configurar servico %BRIDGE_NAME%"
    )
)

:: Configurar para reiniciar automaticamente em caso de falha
sc failure "%BRIDGE_NAME%" reset= 86400 actions= restart/5000/restart/10000/restart/30000 >nul 2>&1
if !errorLevel! == 0 (
    echo [OK] Configuracao de recuperacao definida
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "SERVICE_RECOVERY" "SUCCESS" "Auto-restart configurado"
) else (
    echo [AVISO] Falha ao configurar recuperacao automatica
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Falha ao configurar recuperacao automatica"
)

echo.
echo [7/7] Iniciando servico...
:: Aguardar um pouco antes de tentar iniciar
timeout /t 2 >nul
sc start "%BRIDGE_NAME%" >nul 2>&1
if !errorLevel! == 0 (
    echo [OK] Servico iniciado com sucesso
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "SERVICE_START" "SUCCESS" "%BRIDGE_NAME%"
    
    :: Aguardar o serviço inicializar e verificar se está rodando
    echo [INFO] Aguardando inicializacao do servico...
    timeout /t 3 >nul
    sc query "%BRIDGE_NAME%" | findstr "RUNNING" >nul 2>&1
    if !errorLevel! == 0 (
        echo [OK] Servico esta rodando corretamente
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "SERVICE_STATUS" "RUNNING" "Servico operacional"
    ) else (
        echo [AVISO] Servico iniciado mas pode estar carregando...
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Servico iniciado mas status pendente"
    )
) else (
    echo [AVISO] Falha ao iniciar servico - tentando metodo alternativo...
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Falha ao iniciar servico, tentando metodo alternativo"
    
    :: Tentar iniciar diretamente em background
    echo [INFO] Iniciando bridge em background...
    start /B "Bridge-ZK4500" /D "%BRIDGE_DIR%" %PYTHON_EXE% biometric_bridge_service.py >nul 2>&1
    timeout /t 3 >nul
    
    :: Verificar se está rodando na porta 8080
    netstat -an | findstr "8080" | findstr "LISTENING" >nul 2>&1
    if !errorLevel! == 0 (
        echo [OK] Bridge iniciado em background na porta 8080
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "BRIDGE_START" "BACKGROUND" "Rodando na porta 8080"
    ) else (
        echo [ERRO] Falha ao iniciar bridge - verifique logs
        if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :ERROR "Falha completa ao iniciar bridge"
    )
)

:: Criar arquivo de informações da instalação
echo RLPONTO-WEB Bridge ZK4500 > "%BRIDGE_DIR%\INSTALACAO_INFO.txt"
echo Instalado em: %date% %time% >> "%BRIDGE_DIR%\INSTALACAO_INFO.txt"
echo Servico: %BRIDGE_NAME% >> "%BRIDGE_DIR%\INSTALACAO_INFO.txt"
echo URL: http://localhost:8080 >> "%BRIDGE_DIR%\INSTALACAO_INFO.txt"
echo Diretorio: %BRIDGE_DIR% >> "%BRIDGE_DIR%\INSTALACAO_INFO.txt"
echo Python: !PYTHON_VERSION! >> "%BRIDGE_DIR%\INSTALACAO_INFO.txt"
echo Computador: %COMPUTERNAME% >> "%BRIDGE_DIR%\INSTALACAO_INFO.txt"
echo Usuario: %USERNAME% >> "%BRIDGE_DIR%\INSTALACAO_INFO.txt"
echo Desenvolvido por: AiNexus Tecnologia >> "%BRIDGE_DIR%\INSTALACAO_INFO.txt"

if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "INFO_FILE" "SUCCESS" "INSTALACAO_INFO.txt criado"

echo.
echo =======================================================
echo   INSTALACAO CONCLUIDA COM SUCESSO!
echo =======================================================
echo.
echo Detalhes da instalacao:
echo   Nome do Servico: %BRIDGE_NAME%
echo   Diretorio: %BRIDGE_DIR%
echo   URL Local: http://localhost:8080
echo   Inicio: Automatico (com o Windows)
echo   Logs: %BRIDGE_DIR%\logs\
echo.
echo Para verificar o status:
echo   sc query %BRIDGE_NAME%
echo.
echo Para ver logs do servico:
echo   Visualizador de Eventos ^> Logs do Windows ^> Sistema
echo   OU: %BRIDGE_DIR%\logs\bridge_operations.log
echo.
echo O bridge agora detectara automaticamente o ZK4500
echo e permitira comunicacao com o servidor RLPONTO-WEB.
echo.

:: Testar conectividade
echo Testando conectividade do bridge...
timeout /t 5 >nul
curl -s http://localhost:8080/api/bridge-status >nul 2>&1
if !errorLevel! == 0 (
    echo [OK] Bridge respondendo corretamente!
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :INSTALL_LOG "CONNECTIVITY_TEST" "SUCCESS" "Bridge respondendo"
) else (
    echo [AVISO] Bridge pode ainda estar inicializando...
    echo         Aguarde alguns segundos e teste: http://localhost:8080
    if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :LOG "Bridge ainda inicializando ou problema de conectividade"
)

if %LOGGING_ENABLED%==1 call "%~dp0bridge_logger.bat" :END_SECTION "INSTALACAO_BRIDGE_ZK4500" "SUCCESS"

echo.
echo Pressione qualquer tecla para finalizar...
pause >nul 