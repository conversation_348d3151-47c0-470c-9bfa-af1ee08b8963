# ========================================
# DETECTOR BIOMÉTRICO ROBUSTO - RLPONTO-WEB
# Data: 11/01/2025
# Descrição: Detector robusto usando múltiplas abordagens
# Baseado em Context7 Python Windows API practices
# ========================================

import subprocess
import json
import logging
import platform
import os
import sys
from typing import List, Dict, Any

# Configurar logger
logger = logging.getLogger(__name__)

class RobustBiometricDetector:
    """
    Detector biométrico robusto usando múltiplas abordagens.
    Baseado nas melhores práticas do Context7 para Windows API.
    """
    
    def __init__(self):
        self.known_biometric_vendors = {
            '1B55': 'ZKTeco Inc.',
            '045E': 'Microsoft Corp.',
            '138A': 'Validity Sensors',
            '06CB': 'Synaptics',
            '27C6': 'Goodix Technology',
            '04F3': 'Elan Microelectronics'
        }
        
        self.known_biometric_devices = {
            '1B55:0840': 'ZK4500 Fingerprint Reader',
            '045E:00BB': 'Microsoft Fingerprint Reader',
            '138A:0017': 'Validity VFS495 Fingerprint Reader',
            '06CB:009A': 'Synaptics Fingerprint Reader'
        }
    
    def detect_via_direct_powershell(self) -> List[Dict]:
        """
        Método 1: PowerShell direto sem JSON (mais confiável)
        """
        devices = []
        try:
            logger.info("🔍 Método 1: PowerShell direto...")
            
            # Script PowerShell mais simples e robusto
            ps_script = '''
Get-PnpDevice | Where-Object {
    $_.Class -eq "Biometric" -or 
    $_.FriendlyName -like "*fingerprint*" -or 
    $_.FriendlyName -like "*biometric*" -or 
    $_.FriendlyName -like "*ZK*" -or
    $_.DeviceID -like "*VID_1B55*"
} | ForEach-Object {
    Write-Output "DEVICE_START"
    Write-Output "FriendlyName: $($_.FriendlyName)"
    Write-Output "InstanceId: $($_.InstanceId)"
    Write-Output "Status: $($_.Status)"
    Write-Output "Class: $($_.Class)"
    Write-Output "Manufacturer: $($_.Manufacturer)"
    Write-Output "DEVICE_END"
}
'''
            
            cmd = ['powershell.exe', '-ExecutionPolicy', 'Bypass', '-Command', ps_script]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0 and result.stdout:
                devices = self._parse_powershell_output(result.stdout, 'PowerShell Direct')
                logger.info(f"✅ PowerShell Direct encontrou {len(devices)} dispositivos")
            else:
                logger.info("PowerShell Direct não encontrou dispositivos")
                
        except Exception as e:
            logger.error(f"Erro no PowerShell Direct: {e}")
        
        return devices
    
    def detect_via_wmic(self) -> List[Dict]:
        """
        Método 2: Usar WMIC (mais compatível)
        """
        devices = []
        try:
            logger.info("🔍 Método 2: WMIC...")
            
            cmd = [
                'wmic', 'path', 'Win32_PnPEntity', 
                'where', '"DeviceID like \'%VID_1B55%\' OR Name like \'%fingerprint%\' OR Name like \'%biometric%\'"',
                'get', 'Name,DeviceID,Status,Manufacturer', '/format:csv'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0 and result.stdout:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:  # Primeira linha é header
                    for line in lines[1:]:
                        if line.strip():
                            parts = line.split(',')
                            if len(parts) >= 5:
                                device_info = {
                                    'friendly_name': parts[4].strip() if len(parts) > 4 else 'Unknown',
                                    'instance_id': parts[1].strip() if len(parts) > 1 else '',
                                    'status': parts[3].strip() if len(parts) > 3 else 'Unknown',
                                    'class': 'Biometric',
                                    'manufacturer': parts[2].strip() if len(parts) > 2 else 'Unknown',
                                    'device_type': 'Biometric',
                                    'vendor_id': self._extract_vid_from_instance(parts[1].strip() if len(parts) > 1 else ''),
                                    'product_id': self._extract_pid_from_instance(parts[1].strip() if len(parts) > 1 else ''),
                                    'supported': True,
                                    'detection_method': 'WMIC'
                                }
                                devices.append(device_info)
                                logger.info(f"✅ WMIC encontrou: {device_info['friendly_name']}")
                
        except Exception as e:
            logger.error(f"Erro no WMIC: {e}")
        
        return devices
    
    def detect_via_pnputil(self) -> List[Dict]:
        """
        Método 3: PnPUtil nativo do Windows
        """
        devices = []
        try:
            logger.info("🔍 Método 3: PnPUtil...")
            
            cmd = ['pnputil.exe', '/enum-devices', '/connected']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0 and result.stdout:
                current_device = {}
                lines = result.stdout.split('\n')
                
                for line in lines:
                    line = line.strip()
                    
                    if 'Instance ID:' in line:
                        current_device['instance_id'] = line.split(':', 1)[1].strip()
                    elif 'Device Description:' in line:
                        current_device['friendly_name'] = line.split(':', 1)[1].strip()
                    elif 'Class Name:' in line:
                        current_device['class'] = line.split(':', 1)[1].strip()
                    elif 'Driver Name:' in line:
                        current_device['driver'] = line.split(':', 1)[1].strip()
                    elif line == '' and current_device:
                        # Verificar se é dispositivo biométrico
                        instance_id = current_device.get('instance_id', '')
                        friendly_name = current_device.get('friendly_name', '').lower()
                        class_name = current_device.get('class', '').lower()
                        
                        if ('vid_1b55' in instance_id.lower() or 
                            'fingerprint' in friendly_name or 
                            'biometric' in friendly_name or 
                            'biometric' in class_name or
                            'zk' in friendly_name):
                            
                            device_info = {
                                'friendly_name': current_device.get('friendly_name', 'Unknown Biometric Device'),
                                'instance_id': instance_id,
                                'status': 'OK',
                                'class': current_device.get('class', 'Biometric'),
                                'manufacturer': 'Unknown',
                                'device_type': 'Biometric',
                                'vendor_id': self._extract_vid_from_instance(instance_id),
                                'product_id': self._extract_pid_from_instance(instance_id),
                                'supported': True,
                                'detection_method': 'PnPUtil'
                            }
                            devices.append(device_info)
                            logger.info(f"✅ PnPUtil encontrou: {device_info['friendly_name']}")
                        
                        current_device = {}
                
        except Exception as e:
            logger.error(f"Erro no PnPUtil: {e}")
        
        return devices
    
    def detect_via_device_search(self) -> List[Dict]:
        """
        Método 4: Busca direta por dispositivos conhecidos
        """
        devices = []
        try:
            logger.info("🔍 Método 4: Busca por dispositivos conhecidos...")
            
            # Buscar especificamente por ZK4500
            cmd = [
                'powershell.exe', '-ExecutionPolicy', 'Bypass', '-Command',
                'Get-PnpDevice | Where-Object {$_.InstanceId -like "*VID_1B55&PID_0840*"} | Select-Object FriendlyName,InstanceId,Status'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and 'ZK' in result.stdout:
                device_info = {
                    'friendly_name': 'ZK4500 Fingerprint Reader',
                    'instance_id': 'USB\\VID_1B55&PID_0840',
                    'status': 'OK',
                    'class': 'Biometric',
                    'manufacturer': 'ZKTeco Inc.',
                    'device_type': 'Biometric',
                    'vendor_id': '1B55',
                    'product_id': '0840',
                    'supported': True,
                    'detection_method': 'Direct Search'
                }
                devices.append(device_info)
                logger.info("✅ ZK4500 encontrado via busca direta")
                
        except Exception as e:
            logger.error(f"Erro na busca direta: {e}")
        
        return devices
    
    def _parse_powershell_output(self, output: str, method: str) -> List[Dict]:
        """Parse da saída do PowerShell"""
        devices = []
        current_device = {}
        
        for line in output.split('\n'):
            line = line.strip()
            
            if line == 'DEVICE_START':
                current_device = {}
            elif line == 'DEVICE_END':
                if current_device:
                    device_info = {
                        'friendly_name': current_device.get('FriendlyName', 'Unknown Device'),
                        'instance_id': current_device.get('InstanceId', ''),
                        'status': current_device.get('Status', 'Unknown'),
                        'class': current_device.get('Class', 'Biometric'),
                        'manufacturer': current_device.get('Manufacturer', 'Unknown'),
                        'device_type': 'Biometric',
                        'vendor_id': self._extract_vid_from_instance(current_device.get('InstanceId', '')),
                        'product_id': self._extract_pid_from_instance(current_device.get('InstanceId', '')),
                        'supported': True,
                        'detection_method': method
                    }
                    devices.append(device_info)
                current_device = {}
            elif ':' in line:
                key, value = line.split(':', 1)
                current_device[key.strip()] = value.strip()
        
        return devices
    
    def _extract_vid_from_instance(self, instance_id: str) -> str:
        """Extrair Vendor ID do Instance ID"""
        if 'VID_' in instance_id.upper():
            try:
                start = instance_id.upper().find('VID_') + 4
                return instance_id[start:start+4].upper()
            except:
                pass
        return ''
    
    def _extract_pid_from_instance(self, instance_id: str) -> str:
        """Extrair Product ID do Instance ID"""
        if 'PID_' in instance_id.upper():
            try:
                start = instance_id.upper().find('PID_') + 4
                return instance_id[start:start+4].upper()
            except:
                pass
        return ''
    
    def detect_biometric_devices(self) -> List[Dict]:
        """
        Método principal que executa todos os métodos de detecção
        """
        logger.info("🚀 Iniciando detecção biométrica robusta...")
        
        all_devices = []
        seen_instances = set()
        
        # Executar todos os métodos de detecção
        detection_methods = [
            self.detect_via_direct_powershell,
            self.detect_via_wmic,
            self.detect_via_pnputil,
            self.detect_via_device_search
        ]
        
        for method in detection_methods:
            try:
                devices = method()
                for device in devices:
                    instance_id = device.get('instance_id', '')
                    if instance_id and instance_id not in seen_instances:
                        all_devices.append(device)
                        seen_instances.add(instance_id)
                    elif not instance_id:
                        # Dispositivo sem instance_id único, adicionar mesmo assim
                        all_devices.append(device)
            except Exception as e:
                logger.error(f"Erro em método de detecção: {e}")
        
        # Enriquecer dados dos dispositivos
        for device in all_devices:
            vid = device.get('vendor_id', '')
            pid = device.get('product_id', '')
            vid_pid = f"{vid}:{pid}"
            
            if vid_pid in self.known_biometric_devices:
                device['friendly_name'] = self.known_biometric_devices[vid_pid]
            if vid in self.known_biometric_vendors:
                device['manufacturer'] = self.known_biometric_vendors[vid]
        
        logger.info(f"✅ Detecção robusta concluída: {len(all_devices)} dispositivos encontrados")
        
        # Log dos dispositivos encontrados
        for i, device in enumerate(all_devices, 1):
            logger.info(f"  {i}. {device['friendly_name']} ({device['detection_method']})")
        
        return all_devices

# Instância global
robust_detector = RobustBiometricDetector()

def detect_biometric_devices() -> List[Dict]:
    """
    Função principal para detecção robusta de dispositivos biométricos
    """
    if platform.system() != 'Windows':
        logger.warning("⚠️ Detector Windows chamado em sistema não-Windows")
        return []
    
    return robust_detector.detect_biometric_devices()

# Para teste direto
if __name__ == "__main__":
    print("🧪 Testando detector biométrico robusto...")
    devices = detect_biometric_devices()
    
    if devices:
        print(f"✅ {len(devices)} dispositivo(s) encontrado(s):")
        for i, device in enumerate(devices, 1):
            print(f"\n--- Dispositivo {i} ---")
            for key, value in device.items():
                print(f"{key}: {value}")
    else:
        print("❌ Nenhum dispositivo biométrico detectado") 