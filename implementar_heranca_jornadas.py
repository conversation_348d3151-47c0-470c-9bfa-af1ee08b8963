#!/usr/bin/env python3
"""
Script de Implementação: Sistema de Herança Dinâmica de Jornadas
Sistema de Controle de Ponto - RLPONTO-WEB
Data: 14/07/2025

Executa a implementação completa do sistema de herança dinâmica de jornadas.
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def executar_sql_arquivo(arquivo_sql):
    """
    Executa comandos SQL de um arquivo.
    
    Args:
        arquivo_sql (str): Caminho para o arquivo SQL
        
    Returns:
        bool: True se executou com sucesso
    """
    try:
        logger.info(f"Executando arquivo SQL: {arquivo_sql}")
        
        # Ler arquivo SQL
        with open(arquivo_sql, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # Dividir em comandos individuais
        comandos = []
        comando_atual = ""
        em_delimiter = False
        
        for linha in sql_content.split('\n'):
            linha = linha.strip()
            
            # Ignorar comentários e linhas vazias
            if not linha or linha.startswith('--'):
                continue
            
            # Verificar DELIMITER
            if linha.startswith('DELIMITER'):
                em_delimiter = True
                continue
            elif linha == 'DELIMITER ;':
                em_delimiter = False
                if comando_atual.strip():
                    comandos.append(comando_atual.strip())
                    comando_atual = ""
                continue
            
            comando_atual += linha + "\n"
            
            # Se não estamos em um bloco DELIMITER e a linha termina com ;
            if not em_delimiter and linha.endswith(';'):
                comandos.append(comando_atual.strip())
                comando_atual = ""
        
        # Adicionar último comando se houver
        if comando_atual.strip():
            comandos.append(comando_atual.strip())
        
        # Executar comandos
        db = DatabaseManager()
        sucessos = 0
        erros = 0
        
        for i, comando in enumerate(comandos):
            if not comando.strip():
                continue
                
            try:
                logger.info(f"Executando comando {i+1}/{len(comandos)}")
                resultado = db.execute_query(comando, fetch_all=False)
                if resultado is not None:
                    sucessos += 1
                    logger.info(f"✅ Comando {i+1} executado com sucesso")
                else:
                    logger.warning(f"⚠️ Comando {i+1} executado mas sem retorno")
                    sucessos += 1
                    
            except Exception as e:
                erros += 1
                logger.error(f"❌ Erro no comando {i+1}: {e}")
                logger.error(f"Comando: {comando[:100]}...")
                
                # Para alguns erros, continuar
                if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                    logger.info("Erro esperado (objeto já existe), continuando...")
                    continue
                else:
                    # Para erros críticos, parar
                    logger.error("Erro crítico, parando execução")
                    return False
        
        logger.info(f"Execução concluída: {sucessos} sucessos, {erros} erros")
        return erros == 0
        
    except Exception as e:
        logger.error(f"Erro ao executar arquivo SQL: {e}")
        return False

def verificar_implementacao():
    """
    Verifica se a implementação foi bem-sucedida.
    
    Returns:
        bool: True se implementação está correta
    """
    try:
        logger.info("Verificando implementação...")
        db = DatabaseManager()
        
        # Verificar se novos campos foram adicionados
        campos_funcionarios = db.execute_query("""
            SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'controle_ponto' 
            AND TABLE_NAME = 'funcionarios'
            AND COLUMN_NAME IN ('usa_horario_empresa', 'data_atualizacao_jornada', 'jornada_alterada_por')
        """)
        
        if len(campos_funcionarios) != 3:
            logger.error("Campos não foram adicionados à tabela funcionarios")
            return False
        
        # Verificar se tabela de log foi criada
        tabela_log = db.execute_query("""
            SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'controle_ponto' 
            AND TABLE_NAME = 'log_mudancas_jornada'
        """)
        
        if not tabela_log:
            logger.error("Tabela log_mudancas_jornada não foi criada")
            return False
        
        # Verificar se triggers foram criados
        triggers = db.execute_query("""
            SELECT TRIGGER_NAME FROM INFORMATION_SCHEMA.TRIGGERS 
            WHERE TRIGGER_SCHEMA = 'controle_ponto'
            AND TRIGGER_NAME IN (
                'tr_atualizar_jornadas_funcionarios',
                'tr_historico_mudanca_jornada_funcionario',
                'tr_historico_alocacao_criada',
                'tr_historico_alocacao_finalizada'
            )
        """)
        
        if len(triggers) < 4:
            logger.warning(f"Apenas {len(triggers)} triggers criados (esperado: 4)")
        
        # Verificar se enum foi expandido
        enum_check = db.execute_query("""
            SELECT COLUMN_TYPE FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'controle_ponto' 
            AND TABLE_NAME = 'historico_funcionario'
            AND COLUMN_NAME = 'tipo_evento'
        """, fetch_one=True)
        
        if enum_check and 'JORNADA_ALTERADA' not in enum_check['COLUMN_TYPE']:
            logger.error("Enum tipo_evento não foi expandido")
            return False
        
        logger.info("✅ Implementação verificada com sucesso!")
        return True
        
    except Exception as e:
        logger.error(f"Erro na verificação: {e}")
        return False

def testar_sistema():
    """
    Testa o sistema de herança dinâmica.
    
    Returns:
        bool: True se testes passaram
    """
    try:
        logger.info("Testando sistema de herança dinâmica...")
        
        # Importar sistema
        sys.path.append('/var/www/controle-ponto')
        from sistema_heranca_jornadas import SistemaHerancaJornadas
        
        # Teste 1: Verificar consistência
        logger.info("Teste 1: Verificando consistência...")
        relatorio = SistemaHerancaJornadas.verificar_consistencia_jornadas()
        
        if 'erro' in relatorio:
            logger.error(f"Erro na verificação de consistência: {relatorio['erro']}")
            return False
        
        logger.info(f"Relatório de consistência: {relatorio}")
        
        # Teste 2: Buscar um funcionário para teste
        db = DatabaseManager()
        funcionario_teste = db.execute_query("""
            SELECT f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id
            FROM funcionarios f 
            WHERE f.ativo = TRUE 
            LIMIT 1
        """, fetch_one=True)
        
        if funcionario_teste:
            logger.info(f"Teste 2: Testando com funcionário {funcionario_teste['nome_completo']}")
            
            # Obter histórico
            historico = SistemaHerancaJornadas.obter_historico_jornadas_funcionario(
                funcionario_teste['id'], 10
            )
            logger.info(f"Histórico obtido: {len(historico)} registros")
            
        logger.info("✅ Testes concluídos com sucesso!")
        return True
        
    except Exception as e:
        logger.error(f"Erro nos testes: {e}")
        return False

def main():
    """Função principal de implementação."""
    logger.info("🚀 INICIANDO IMPLEMENTAÇÃO DO SISTEMA DE HERANÇA DINÂMICA DE JORNADAS")
    logger.info("=" * 80)
    
    try:
        # Passo 1: Executar SQL de implementação
        logger.info("📋 PASSO 1: Executando SQL de implementação...")
        arquivo_sql = "sql/implementar_heranca_dinamica_jornadas.sql"
        
        if not os.path.exists(arquivo_sql):
            logger.error(f"Arquivo SQL não encontrado: {arquivo_sql}")
            return False
        
        if not executar_sql_arquivo(arquivo_sql):
            logger.error("Falha na execução do SQL")
            return False
        
        # Passo 2: Verificar implementação
        logger.info("🔍 PASSO 2: Verificando implementação...")
        if not verificar_implementacao():
            logger.error("Falha na verificação da implementação")
            return False
        
        # Passo 3: Testar sistema
        logger.info("🧪 PASSO 3: Testando sistema...")
        if not testar_sistema():
            logger.error("Falha nos testes do sistema")
            return False
        
        # Sucesso!
        logger.info("🎉 IMPLEMENTAÇÃO CONCLUÍDA COM SUCESSO!")
        logger.info("=" * 80)
        logger.info("✅ Sistema de Herança Dinâmica de Jornadas implementado")
        logger.info("✅ Triggers de atualização automática criados")
        logger.info("✅ Sistema de histórico expandido")
        logger.info("✅ Logs de mudanças implementados")
        logger.info("✅ Testes de verificação passaram")
        logger.info("")
        logger.info("📋 PRÓXIMOS PASSOS:")
        logger.info("1. Testar mudanças de jornada em empresas")
        logger.info("2. Verificar propagação automática para funcionários")
        logger.info("3. Validar histórico de mudanças")
        logger.info("4. Testar alocações de funcionários para clientes")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ ERRO CRÍTICO NA IMPLEMENTAÇÃO: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
