#!/usr/bin/env python3
"""
Monitor em tempo real para detectar alterações nas horas semanais
"""
import sys
import os
import time
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager

def monitor_horas_tempo_real():
    print("🔍 MONITOR EM TEMPO REAL - HORAS SEMANAIS")
    print("=" * 60)
    print("Monitorando funcionário ID 35 (Richardson)")
    print("Pressione Ctrl+C para parar")
    print("=" * 60)
    
    try:
        funcionario_id = 35
        db = DatabaseManager()
        
        # Valor inicial
        inicial = db.execute_query("""
            SELECT horas_semanais_obrigatorias, data_atualizacao
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,), fetch_one=True)
        
        valor_anterior = float(inicial['horas_semanais_obrigatorias'])
        data_anterior = inicial['data_atualizacao']
        
        print(f"📊 Valor inicial: {valor_anterior}h (Atualizado: {data_anterior})")
        print("🔄 Monitorando alterações...")
        
        contador = 0
        
        while True:
            time.sleep(2)  # Verificar a cada 2 segundos
            contador += 1
            
            # Verificar valor atual
            atual = db.execute_query("""
                SELECT horas_semanais_obrigatorias, data_atualizacao
                FROM funcionarios 
                WHERE id = %s
            """, (funcionario_id,), fetch_one=True)
            
            valor_atual = float(atual['horas_semanais_obrigatorias'])
            data_atual = atual['data_atualizacao']
            
            # Detectar mudança
            if valor_atual != valor_anterior or data_atual != data_anterior:
                print(f"\n🚨 ALTERAÇÃO DETECTADA! (Check #{contador})")
                print(f"   📊 Valor: {valor_anterior}h → {valor_atual}h")
                print(f"   📅 Data: {data_anterior} → {data_atual}")
                
                # Verificar quem/o que alterou
                print(f"\n🔍 INVESTIGANDO A ALTERAÇÃO...")
                
                # Verificar logs recentes
                try:
                    logs_recentes = db.execute_query("""
                        SELECT * FROM funcionarios_log 
                        WHERE funcionario_id = %s 
                        AND data_alteracao >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
                        ORDER BY data_alteracao DESC
                    """, (funcionario_id,))
                    
                    if logs_recentes:
                        print(f"   📝 Logs encontrados:")
                        for log in logs_recentes:
                            print(f"      - {log.get('data_alteracao')}: {log.get('descricao', 'N/A')}")
                    else:
                        print(f"   ⚠️ Nenhum log de alteração encontrado")
                except:
                    print(f"   ℹ️ Tabela de logs não disponível")
                
                # Verificar processos MySQL ativos
                try:
                    processos = db.execute_query("SHOW PROCESSLIST")
                    processos_ativos = [p for p in processos if p.get('Command') != 'Sleep' and p.get('Info')]
                    
                    if processos_ativos:
                        print(f"   🔧 Processos MySQL ativos:")
                        for proc in processos_ativos[:3]:  # Mostrar apenas os 3 primeiros
                            print(f"      - User: {proc.get('User')}, Info: {proc.get('Info', '')[:50]}...")
                except:
                    print(f"   ℹ️ Não foi possível verificar processos MySQL")
                
                # Verificar se há triggers que podem ter executado
                try:
                    trigger_info = db.execute_query("""
                        SELECT TRIGGER_NAME, ACTION_STATEMENT 
                        FROM information_schema.TRIGGERS 
                        WHERE EVENT_OBJECT_TABLE = 'funcionarios' 
                        AND EVENT_OBJECT_SCHEMA = 'controle_ponto'
                        AND EVENT_MANIPULATION = 'UPDATE'
                    """)
                    
                    if trigger_info:
                        print(f"   ⚙️ Triggers UPDATE encontrados:")
                        for trigger in trigger_info:
                            print(f"      - {trigger['TRIGGER_NAME']}")
                            # Verificar se o trigger mexe com horas_semanais_obrigatorias
                            if 'horas_semanais_obrigatorias' in trigger['ACTION_STATEMENT']:
                                print(f"        🚨 ESTE TRIGGER ALTERA HORAS SEMANAIS!")
                                print(f"        📝 Código: {trigger['ACTION_STATEMENT'][:100]}...")
                except:
                    print(f"   ℹ️ Não foi possível verificar triggers")
                
                valor_anterior = valor_atual
                data_anterior = data_atual
                
                print(f"\n🔄 Continuando monitoramento...")
            
            # Mostrar progresso a cada 30 checks (1 minuto)
            if contador % 30 == 0:
                print(f"   ✅ Check #{contador} - Valor: {valor_atual}h (sem alterações)")
        
    except KeyboardInterrupt:
        print(f"\n\n⏹️ Monitor interrompido pelo usuário")
        print(f"📊 Último valor: {valor_atual}h")
        
    except Exception as e:
        print(f"\n❌ ERRO NO MONITOR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    monitor_horas_tempo_real()
