# 🔧 **INSTRUÇÕES PARA CONFIGURAR ZKAGENT NO SERVIDOR**

## 📋 **SITUAÇÃO ATUAL DETECTADA**

Baseado no diagnóstico realizado:

1. ✅ **Serviço biometria.service detectado** - em estado "auto-restart" (tentando iniciar)
2. ✅ **Bridge ZK4500 encontrado** - `zk4500_bridge.py` (para Windows)
3. ❌ **Porta 5001 vazia** - Nenhum serviço rodando
4. ❌ **ZKAgent não encontrado** - Apenas os arquivos de simulação

---

## 🎯 **OPÇÕES DE SOLUÇÃO**

### **OPÇÃO A: USAR SIMULADOR (RECOMENDADO PARA TESTES)**

```bash
# 1. Instalar dependências no servidor
ssh root@************
cd /var/www/controle-ponto/
pip3 install flask requests pymysql werkzeug websockets

# 2. Executar simulador ZKAgent na porta 5001
python3 zkagent_simulador.py &

# 3. Verificar se funcionou
curl http://localhost:5001/test

# 4. Iniciar Flask
python3 app.py &

# 5. Testar sistema completo
curl http://localhost/api/zkagent/test
```

### **OPÇÃO B: CONFIGURAR SERVIÇO REAL BIOMETRIA**

```bash
# 1. Verificar status do serviço
systemctl status biometria.service

# 2. Ver logs do serviço
journalctl -u biometria.service -f

# 3. Editar configuração do serviço
systemctl edit biometria.service

# 4. Reiniciar serviço
systemctl restart biometria.service
```

---

## 🚀 **EXECUÇÃO RECOMENDADA (OPÇÃO A)**

### **Comandos para executar NO SERVIDOR ************:**

```bash
# Passo 1: Conectar ao servidor
ssh root@************

# Passo 2: Navegar para o diretório
cd /var/www/controle-ponto/

# Passo 3: Instalar dependências
pip3 install flask requests pymysql werkzeug websockets

# Passo 4: Verificar se app.py carrega
python3 -c "import app; print('✅ App OK')"

# Passo 5: Criar arquivo do simulador (copie o conteúdo abaixo)
cat > zkagent_simulador.py << 'EOF'
#!/usr/bin/env python3
"""
Simulador ZKAgent para testes
Simula as respostas do ZKAgent quando o hardware não está disponível
"""

from flask import Flask, jsonify, request
import random
import time
import base64

app = Flask(__name__)

@app.route('/test', methods=['GET'])
def test():
    """Simula teste de conexão"""
    return jsonify({"status": "ok"})

@app.route('/list-devices', methods=['GET'])
def list_devices():
    """Simula listagem de dispositivos"""
    return jsonify({"devices": 1})

@app.route('/capture', methods=['POST'])
def capture():
    """Simula captura de digital"""
    # Simula tempo de captura
    time.sleep(2)
    
    # Gera template simulado
    template = base64.b64encode(f"template_sim_{int(time.time())}".encode()).decode()
    quality = random.randint(70, 95)
    
    return jsonify({
        "success": True,
        "template": template,
        "quality": quality,
        "timestamp": time.time()
    })

@app.route('/identify', methods=['POST'])
def identify():
    """Simula identificação"""
    return jsonify({
        "success": False,
        "message": "Nenhuma digital cadastrada encontrada"
    })

@app.route('/status', methods=['GET'])
def status():
    """Status do simulador"""
    return jsonify({
        "service": "ZKAgent Simulador",
        "version": "1.0",
        "devices": 1,
        "status": "online"
    })

if __name__ == '__main__':
    print("🔐 ZKAgent Simulador iniciando na porta 5001...")
    print("Este é um simulador para testes sem hardware ZK4500")
    print("Para parar: Ctrl+C")
    app.run(host='0.0.0.0', port=5001, debug=False)
EOF

# Passo 6: Executar simulador em background
nohup python3 zkagent_simulador.py > zkagent.log 2>&1 &

# Passo 7: Verificar se funcionou
sleep 2
curl http://localhost:5001/test

# Passo 8: Executar Flask principal em background
nohup python3 app.py > flask.log 2>&1 &

# Passo 9: Verificar portas
ss -tulpn | grep -E "5000|5001"

# Passo 10: Reiniciar nginx
systemctl restart nginx

# Passo 11: Testar sistema completo
curl http://localhost/api/zkagent/test
```

---

## 🔍 **VERIFICAÇÃO FINAL**

### **Testes para confirmar que funcionou:**

```bash
# Teste 1: ZKAgent Simulador
curl http://localhost:5001/test
# Esperado: {"status":"ok"}

# Teste 2: Proxy Flask
curl http://localhost/api/zkagent/test
# Esperado: {"status":"ok"}

# Teste 3: Portal principal
curl http://localhost/
# Esperado: HTML da página de login

# Teste 4: Verificar processos
ps aux | grep -E "python3.*app|python3.*zkagent"
```

---

## 📊 **ESTRUTURA FINAL**

```
nginx (porta 80) → Flask (porta 5000) → ZKAgent Simulador (porta 5001)
         ↓                  ↓                      ↓
    Portal Web        Proxy API           Simulação Biométrica
```

---

## ⚠️ **TROUBLESHOOTING**

### **Se der erro de módulo:**
```bash
pip3 install --upgrade pip
pip3 install flask requests pymysql werkzeug websockets
```

### **Se a porta estiver ocupada:**
```bash
ss -tulpn | grep -E "5000|5001"
kill -9 [PID_DO_PROCESSO]
```

### **Se nginx não conectar:**
```bash
systemctl restart nginx
systemctl status nginx
``` 