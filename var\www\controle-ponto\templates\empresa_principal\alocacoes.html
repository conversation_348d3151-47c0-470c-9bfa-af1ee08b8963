{% extends "base.html" %}

{% block title %}Alocação de Funcionários - {{ empresa_principal.razao_social }}{% endblock %}

{% block extra_css %}
<style>
    .alocacao-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
        background: white;
    }
    
    .alocacao-card:hover {
        border-color: #28a745;
        box-shadow: 0 4px 15px rgba(40,167,69,0.1);
        transform: translateY(-2px);
    }
    
    .funcionario-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
    }
    
    .cliente-info {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
    }
    
    .status-alocacao {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-ativo {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-inativo {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .stats-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .stats-box {
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        margin: 0 5px;
    }
    
    .filter-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header com Estatísticas -->
    <div class="stats-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-users-cog"></i> Alocação de Funcionários</h2>
                <p class="mb-0">Gestão de alocações da {{ empresa_principal.razao_social }}</p>
            </div>
            <div class="col-md-4">
                <div class="row">
                    <div class="col-4">
                        <div class="stats-box">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.total_alocacoes }}</div>
                            <small>Total</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-box">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.alocacoes_ativas }}</div>
                            <small>Ativas</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-box">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.funcionarios_unicos }}</div>
                            <small>Funcionários</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ações e Navegação -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h4><i class="fas fa-list"></i> Alocações Ativas</h4>
        </div>
        <div class="col-md-6 text-right">
            <button class="btn btn-success" onclick="mostrarModalNovaAlocacao()">
                <i class="fas fa-plus"></i> Nova Alocação
            </button>
            <a href="/empresa-principal/clientes" class="btn btn-info">
                <i class="fas fa-building"></i> Gerenciar Clientes
            </a>
            <a href="/empresa-principal/" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Dashboard
            </a>
        </div>
    </div>

    <!-- Filtros -->
    <div class="filter-card">
        <div class="row">
            <div class="col-md-3">
                <label>Status:</label>
                <select class="form-control" id="filtroStatus" onchange="filtrarAlocacoes()">
                    <option value="">Todos</option>
                    <option value="ativo">Ativo</option>
                    <option value="inativo">Inativo</option>
                </select>
            </div>
            <div class="col-md-3">
                <label>Cliente:</label>
                <select class="form-control" id="filtroCliente" onchange="filtrarAlocacoes()">
                    <option value="">Todos os clientes</option>
                    {% for cliente in clientes %}
                        <option value="{{ cliente.empresa_cliente_id }}">{{ cliente.razao_social }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label>Funcionário:</label>
                <input type="text" class="form-control" id="buscarFuncionario" placeholder="Nome do funcionário..." onkeyup="filtrarAlocacoes()">
            </div>
            <div class="col-md-3">
                <label>&nbsp;</label><br>
                <button class="btn btn-outline-secondary" onclick="limparFiltros()">
                    <i class="fas fa-eraser"></i> Limpar
                </button>
                <button class="btn btn-outline-primary" onclick="exportarAlocacoes()">
                    <i class="fas fa-download"></i> Exportar
                </button>
            </div>
        </div>
    </div>

    <!-- Lista de Alocações -->
    <div id="listaAlocacoes">
        {% if alocacoes %}
            {% for alocacao in alocacoes %}
            <div class="alocacao-card" 
                 data-status="{{ 'ativo' if alocacao.ativo else 'inativo' }}" 
                 data-cliente="{{ alocacao.empresa_cliente_id }}"
                 data-funcionario="{{ alocacao.nome.lower() }}">
                
                <div class="row">
                    <!-- Informações do Funcionário -->
                    <div class="col-md-4">
                        <div class="funcionario-info">
                            <h6><i class="fas fa-user"></i> {{ alocacao.nome }}</h6>
                            <p class="mb-1"><strong>Cargo:</strong> {{ alocacao.cargo }}</p>
                            <p class="mb-1"><strong>CPF:</strong> {{ alocacao.cpf }}</p>
                            {% if alocacao.cargo_no_cliente %}
                                <p class="mb-0"><strong>Cargo no Cliente:</strong> {{ alocacao.cargo_no_cliente }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Informações do Cliente -->
                    <div class="col-md-4">
                        <div class="cliente-info">
                            <h6><i class="fas fa-building"></i> {{ alocacao.razao_social }}</h6>
                            {% if alocacao.nome_fantasia %}
                                <p class="mb-1">{{ alocacao.nome_fantasia }}</p>
                            {% endif %}
                            <p class="mb-1"><strong>CNPJ:</strong> {{ alocacao.cnpj }}</p>
                            {% if alocacao.nome_contrato %}
                                <p class="mb-0"><strong>Contrato:</strong> {{ alocacao.nome_contrato }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Detalhes da Alocação -->
                    <div class="col-md-4">
                        <div class="mb-2">
                            <span class="status-alocacao status-{{ 'ativo' if alocacao.ativo else 'inativo' }}">
                                {{ 'Ativo' if alocacao.ativo else 'Inativo' }}
                            </span>
                        </div>
                        
                        <table class="table table-sm table-borderless text-white">
                            <tr>
                                <td><strong>Jornada:</strong></td>
                                <td>{{ alocacao.jornada_nome }}</td>
                            </tr>
                            <tr>
                                <td><strong>Carga:</strong></td>
                                <td>{{ alocacao.carga_horaria }}h</td>
                            </tr>
                            <tr>
                                <td><strong>Alocação:</strong></td>
                                <td>{{ alocacao.percentual_alocacao }}%</td>
                            </tr>
                            <tr>
                                <td><strong>Início:</strong></td>
                                <td>{{ alocacao.data_inicio.strftime('%d/%m/%Y') if alocacao.data_inicio else 'N/A' }}</td>
                            </tr>
                            {% if alocacao.data_fim %}
                            <tr>
                                <td><strong>Fim:</strong></td>
                                <td>{{ alocacao.data_fim.strftime('%d/%m/%Y') }}</td>
                            </tr>
                            {% endif %}
                            {% if alocacao.valor_hora %}
                            <tr>
                                <td><strong>Valor/h:</strong></td>
                                <td>R$ {{ "%.2f"|format(alocacao.valor_hora) }}</td>
                            </tr>
                            {% endif %}
                        </table>
                        
                        <div class="text-right mt-2">
                            <button class="btn btn-sm btn-light" onclick="verDetalhesAlocacao({{ alocacao.id }})">
                                <i class="fas fa-eye"></i> Detalhes
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="editarAlocacao({{ alocacao.id }})">
                                <i class="fas fa-edit"></i> Editar
                            </button>
                            {% if alocacao.ativo %}
                                <button class="btn btn-sm btn-danger" onclick="desativarAlocacao({{ alocacao.id }})">
                                    <i class="fas fa-stop"></i> Desativar
                                </button>
                            {% else %}
                                <button class="btn btn-sm btn-success" onclick="reativarAlocacao({{ alocacao.id }})">
                                    <i class="fas fa-play"></i> Reativar
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                {% if alocacao.observacoes %}
                <div class="row mt-2">
                    <div class="col-12">
                        <div class="alert alert-info mb-0">
                            <strong>Observações:</strong> {{ alocacao.observacoes }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users-slash fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">Nenhuma alocação encontrada</h4>
                <p class="text-muted">Comece alocando funcionários aos clientes para gerenciar suas atividades.</p>
                <button class="btn btn-primary btn-lg" onclick="mostrarModalNovaAlocacao()">
                    <i class="fas fa-plus"></i> Criar Primeira Alocação
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal Nova Alocação -->
<div class="modal fade" id="modalNovaAlocacao" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-user-plus"></i> Nova Alocação de Funcionário</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="formNovaAlocacao" onsubmit="criarAlocacao(event)">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Cliente *</label>
                                <select class="form-control" name="empresa_cliente_id" required>
                                    <option value="">Selecione um cliente...</option>
                                    {% for cliente in clientes %}
                                        <option value="{{ cliente.empresa_cliente_id }}">{{ cliente.razao_social }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Funcionário *</label>
                                <select class="form-control" name="funcionario_id" required>
                                    <option value="">Carregando funcionários...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Jornada de Trabalho *</label>
                                <select class="form-control" name="jornada_trabalho_id" required>
                                    <option value="">Carregando jornadas...</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Cargo no Cliente</label>
                                <input type="text" class="form-control" name="cargo_no_cliente" placeholder="Ex: Analista de Sistemas">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Data de Início *</label>
                                <input type="date" class="form-control" name="data_inicio" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Data de Fim</label>
                                <input type="date" class="form-control" name="data_fim">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Alocação (%)</label>
                                <input type="number" class="form-control" name="percentual_alocacao" value="100" min="1" max="100">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Valor da Hora (R$)</label>
                                <input type="number" class="form-control" name="valor_hora" step="0.01" placeholder="0.00">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Observações</label>
                                <textarea class="form-control" name="observacoes" rows="2" placeholder="Observações sobre a alocação..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Criar Alocação
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function mostrarModalNovaAlocacao() {
    // Carregar funcionários e jornadas
    carregarFuncionarios();
    carregarJornadas();
    $('#modalNovaAlocacao').modal('show');
}

function carregarFuncionarios() {
    fetch('/empresa-principal/api/funcionarios-disponiveis')
        .then(response => response.json())
        .then(data => {
            const select = document.querySelector('#modalNovaAlocacao select[name="funcionario_id"]');
            select.innerHTML = '<option value="">Selecione um funcionário...</option>';

            data.funcionarios.forEach(func => {
                const option = document.createElement('option');
                option.value = func.id;
                option.textContent = `${func.nome_completo} - ${func.cargo}`;
                select.appendChild(option);
            });
        });
}

function carregarJornadas() {
    fetch('/empresa-principal/api/jornadas-disponiveis')
        .then(response => response.json())
        .then(data => {
            const select = document.querySelector('#modalNovaAlocacao select[name="jornada_trabalho_id"]');
            select.innerHTML = '<option value="">Selecione uma jornada...</option>';
            
            data.jornadas.forEach(jornada => {
                const option = document.createElement('option');
                option.value = jornada.id;
                option.textContent = `${jornada.nome} (${jornada.empresa_nome}) - ${jornada.carga_horaria}`;
                select.appendChild(option);
            });
        });
}

function criarAlocacao(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    
    fetch('/empresa-principal/funcionarios/alocar', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#modalNovaAlocacao').modal('hide');
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao criar alocação');
    });
}

function filtrarAlocacoes() {
    const status = document.getElementById('filtroStatus').value;
    const cliente = document.getElementById('filtroCliente').value;
    const funcionario = document.getElementById('buscarFuncionario').value.toLowerCase();
    const cards = document.querySelectorAll('.alocacao-card');
    
    cards.forEach(card => {
        const cardStatus = card.getAttribute('data-status');
        const cardCliente = card.getAttribute('data-cliente');
        const cardFuncionario = card.getAttribute('data-funcionario');
        
        const statusMatch = !status || cardStatus === status;
        const clienteMatch = !cliente || cardCliente === cliente;
        const funcionarioMatch = !funcionario || cardFuncionario.includes(funcionario);
        
        if (statusMatch && clienteMatch && funcionarioMatch) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function limparFiltros() {
    document.getElementById('filtroStatus').value = '';
    document.getElementById('filtroCliente').value = '';
    document.getElementById('buscarFuncionario').value = '';
    filtrarAlocacoes();
}

function verDetalhesAlocacao(alocacaoId) {
    alert('Detalhes da alocação ' + alocacaoId + ' - será implementado');
}

function editarAlocacao(alocacaoId) {
    alert('Editar alocação ' + alocacaoId + ' - será implementado');
}

function desativarAlocacao(alocacaoId) {
    if (confirm('Deseja desativar esta alocação?')) {
        alterarStatusAlocacao(alocacaoId, false);
    }
}

function reativarAlocacao(alocacaoId) {
    if (confirm('Deseja reativar esta alocação?')) {
        alterarStatusAlocacao(alocacaoId, true);
    }
}

function alterarStatusAlocacao(alocacaoId, ativo) {
    fetch('/empresa-principal/alocacoes/alterar-status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            alocacao_id: alocacaoId,
            ativo: ativo
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao alterar status da alocação');
    });
}

function exportarAlocacoes() {
    window.open('/empresa-principal/exportar_alocacoes', '_blank');
}

// Animações
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.alocacao-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
