🔍 Relatório de Análise do Servidor
Data: 29/05/2025 16:11:14
Hostname: RLPONTO
Sistema: Linux RLPONTO 6.8.12-10-pve #1 SMP PREEMPT_DYNAMIC PMX 6.8.12-10 (2025-04-18T07:39Z) x86_64 x86_64 x86_64 GNU/Linux

===========================================================
📌 Informações do Sistema
===========================================================
🖥️ CPU:
Architecture:                         x86_64
CPU(s):                               4
Model name:                           Intel(R) Core(TM) i5-2500S CPU @ 2.70GHz
Thread(s) per core:                   1
Core(s) per socket:                   4
Socket(s):                            1

💾 Memória:
               total        used        free      shared  buff/cache   available
Mem:           2.0Gi       493Mi       1.2Gi       0.0Ki       259Mi       1.5Gi
Swap:          2.0Gi          0B       2.0Gi

💽 Disco:
Filesystem                        Size  Used Avail Use% Mounted on
/dev/mapper/pve-vm--100--disk--0   49G  1.9G   45G   4% /
none                              492K  4.0K  488K   1% /dev
efivarfs                          128K   65K   59K  53% /sys/firmware/efi/efivars
tmpfs                             7.8G   16K  7.8G   1% /dev/shm
tmpfs                             3.2G  120K  3.2G   1% /run
tmpfs                             5.0M     0  5.0M   0% /run/lock
tmpfs                             1.6G     0  1.6G   0% /run/user/0

===========================================================
📌 Status dos Serviços
===========================================================
🚦 Nginx Status:
* nginx.service - A high performance web server and a reverse proxy server
     Loaded: loaded (/lib/systemd/system/nginx.service; enabled; vendor preset: enabled)
     Active: active (running) since Wed 2025-05-28 18:15:05 -04; 21h ago
       Docs: man:nginx(8)
    Process: 114 ExecStartPre=/usr/sbin/nginx -t -q -g daemon on; master_process on; (code=exited, status=0/SUCCESS)
    Process: 167 ExecStart=/usr/sbin/nginx -g daemon on; master_process on; (code=exited, status=0/SUCCESS)
   Main PID: 180 (nginx)
      Tasks: 2 (limit: 18978)
     Memory: 11.6M
        CPU: 38ms
     CGroup: /system.slice/nginx.service
             |-180 "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;"
             `-181 "nginx: worker process" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" "" ""

May 28 18:15:04 RLPONTO systemd[1]: Starting A high performance web server and a reverse proxy server...
May 28 18:15:05 RLPONTO systemd[1]: Started A high performance web server and a reverse proxy server.

🚦 MySQL Status:
* mysql.service - MySQL Community Server
     Loaded: loaded (/lib/systemd/system/mysql.service; enabled; vendor preset: enabled)
     Active: active (running) since Wed 2025-05-28 18:15:18 -04; 21h ago
    Process: 113 ExecStartPre=/usr/share/mysql/mysql-systemd-start pre (code=exited, status=0/SUCCESS)
   Main PID: 238 (mysqld)
     Status: "Server is operational"
      Tasks: 39 (limit: 18978)
     Memory: 443.1M
        CPU: 5min 12.578s
     CGroup: /system.slice/mysql.service
             `-238 /usr/sbin/mysqld

May 28 18:15:04 RLPONTO systemd[1]: Starting MySQL Community Server...
May 28 18:15:18 RLPONTO systemd[1]: Started MySQL Community Server.

🚦 Supervisor Status:
Unit supervisor.service could not be found.

🚦 Todos os Serviços Ativos:
  UNIT                                              LOAD   ACTIVE SUB     DESCRIPTION
  biometria.service                                 loaded active running Serviço de Biometria ZK4500
  console-getty.service                             loaded active running Console Getty
  <EMAIL>                         loaded active running Container Getty on /dev/tty1
  <EMAIL>                         loaded active running Container Getty on /dev/tty2
  controle-ponto.service                            loaded active running Controle de Ponto
  cron.service                                      loaded active running Regular background program processing daemon
  dbus.service                                      loaded active running D-Bus System Message Bus
  mysql.service                                     loaded active running MySQL Community Server
  networkd-dispatcher.service                       loaded active running Dispatcher daemon for systemd-networkd
  nginx.service                                     loaded active running A high performance web server and a reverse proxy server
  <EMAIL>                                 loaded active running Postfix Mail Transport Agent (instance -)
  rsyslog.service                                   loaded active running System Logging Service
  ssh@13-10.19.208.31:22-10.19.208.93:55693.service loaded active running OpenBSD Secure Shell server per-connection daemon (10.19.208.93:55693)
  ssh@14-10.19.208.31:22-10.19.208.93:55694.service loaded active running OpenBSD Secure Shell server per-connection daemon (10.19.208.93:55694)
  systemd-journald.service                          loaded active running Journal Service
  systemd-logind.service                            loaded active running User Login Management
  systemd-networkd.service                          loaded active running Network Configuration
  systemd-resolved.service                          loaded active running Network Name Resolution
  <EMAIL>                                    loaded active running User Manager for UID 0
  uuidd.service                                     loaded active running Daemon for generating UUIDs

LOAD   = Reflects whether the unit definition was properly loaded.
ACTIVE = The high-level unit activation state, i.e. generalization of SUB.
SUB    = The low-level unit activation state, values depend on unit type.
20 loaded units listed.

===========================================================
📌 Portas em Uso
===========================================================
./coletar_info_servidor.sh: line 41: netstat: command not found

===========================================================
📌 Configurações do Nginx
===========================================================
📁 Sites Disponíveis:
total 16
drwxr-xr-x 2 <USER> <GROUP> 4096 May 19 10:40 .
drwxr-xr-x 8 <USER> <GROUP> 4096 May 19 10:39 ..
-rw-r--r-- 1 <USER> <GROUP>  417 May 19 10:43 controle-ponto
-rw-r--r-- 1 <USER> <GROUP> 2412 May 30  2023 default

📁 Sites Habilitados:
total 8
drwxr-xr-x 2 <USER> <GROUP> 4096 May 19 10:43 .
drwxr-xr-x 8 <USER> <GROUP> 4096 May 19 10:39 ..
lrwxrwxrwx 1 root root   41 May 19 10:43 controle-ponto -> /etc/nginx/sites-available/controle-ponto

📄 Configuração do Virtual Host:
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
        proxy_read_timeout 300;
    }
}

===========================================================
📌 Informações do MySQL
===========================================================
🗃️ Bancos de Dados:
Database
controle_ponto
information_schema
mysql
performance_schema
sys

📊 Tabelas do controle_ponto:
Tables_in_controle_ponto
epis
funcionarios
permissoes
registros_ponto
usuarios

👥 Usuários MySQL:
user	host
cavalcrod	%
controle_user	localhost
debian-sys-maint	localhost
mysql.infoschema	localhost
mysql.session	localhost
mysql.sys	localhost
root	localhost

===========================================================
📌 Estrutura de Diretórios
===========================================================
📂 /var/www:
total 16
drwxr-xr-x  4 <USER>     <GROUP>     4096 May 28 12:40 .
drwxr-xr-x 12 <USER>     <GROUP>     4096 May 19 10:39 ..
drwxr-xr-x  4 <USER> <GROUP> 4096 May 28 12:40 controle-ponto
drwxr-xr-x  2 <USER>     <GROUP>     4096 May 19 10:39 html

📂 /var/www/controle-ponto:
total 100
drwxr-xr-x 4 <USER> <GROUP>  4096 May 28 12:40 .
drwxr-xr-x 4 <USER>     <GROUP>      4096 May 28 12:40 ..
-rw-r--r-- 1 <USER> <GROUP> 39908 May 28 12:40 app.py
-rw-r--r-- 1 <USER> <GROUP> 17584 May 28 12:40 biometria_service.py
drwxr-xr-x 3 <USER> <GROUP>  4096 May 28 12:40 static
drwxr-xr-x 2 <USER> <GROUP>  4096 May 28 18:14 templates
-rw-r--r-- 1 <USER> <GROUP> 23330 May 28 12:40 zk4500_bridge.py

📂 /etc:
ls: cannot access '/etc/supervisor/conf.d/': No such file or directory
/etc/nginx/:
total 72
drwxr-xr-x  8 <USER> <GROUP> 4096 May 19 10:39 .
drwxr-xr-x 78 <USER> <GROUP> 4096 May 28 18:15 ..
drwxr-xr-x  2 <USER> <GROUP> 4096 Feb 14 14:40 conf.d
-rw-r--r--  1 <USER> <GROUP> 1125 May 30  2023 fastcgi.conf
-rw-r--r--  1 <USER> <GROUP> 1055 May 30  2023 fastcgi_params
-rw-r--r--  1 <USER> <GROUP> 2837 May 30  2023 koi-utf
-rw-r--r--  1 <USER> <GROUP> 2223 May 30  2023 koi-win
-rw-r--r--  1 <USER> <GROUP> 3957 May 30  2023 mime.types
drwxr-xr-x  2 <USER> <GROUP> 4096 Feb 14 14:40 modules-available
drwxr-xr-x  2 <USER> <GROUP> 4096 May 19 10:39 modules-enabled
-rw-r--r--  1 <USER> <GROUP> 1447 May 30  2023 nginx.conf
-rw-r--r--  1 <USER> <GROUP>  180 May 30  2023 proxy_params
-rw-r--r--  1 <USER> <GROUP>  636 May 30  2023 scgi_params
drwxr-xr-x  2 <USER> <GROUP> 4096 May 19 10:40 sites-available
drwxr-xr-x  2 <USER> <GROUP> 4096 May 19 10:43 sites-enabled
drwxr-xr-x  2 <USER> <GROUP> 4096 May 19 10:39 snippets
-rw-r--r--  1 <USER> <GROUP>  664 May 30  2023 uwsgi_params
-rw-r--r--  1 <USER> <GROUP> 3071 May 30  2023 win-utf

📂 /var/log:
/var/log/mysql/:
total 36
drwxr-x--- 2 <USER> <GROUP>    4096 May 29 00:00 .
drwxrwxr-x 9 <USER>  <GROUP> 4096 May 25 00:00 ..
-rw-r----- 1 <USER> <GROUP>       0 May 29 00:00 error.log
-rw-r----- 1 <USER> <GROUP>     850 May 28 18:15 error.log.1.gz
-rw-r----- 1 <USER> <GROUP>     750 May 27 23:27 error.log.2.gz
-rw-r----- 1 <USER> <GROUP>      20 May 26 00:00 error.log.3.gz
-rw-r----- 1 <USER> <GROUP>      20 May 25 00:00 error.log.4.gz
-rw-r----- 1 <USER> <GROUP>      20 May 24 00:00 error.log.5.gz
-rw-r----- 1 <USER> <GROUP>      20 May 23 00:00 error.log.6.gz
-rw-r----- 1 <USER> <GROUP>      20 May 22 00:00 error.log.7.gz

/var/log/nginx/:
total 68
drwxr-xr-x 2 <USER>     <GROUP>     4096 May 29 00:00 .
drwxrwxr-x 9 <USER>     <GROUP>  4096 May 25 00:00 ..
-rw-r----- 1 <USER> <GROUP>      228 May 29 09:30 access.log
-rw-r----- 1 <USER> <GROUP>    19008 May 28 20:11 access.log.1
-rw-r----- 1 <USER> <GROUP>     1378 May 27 23:47 access.log.2.gz
-rw-r----- 1 <USER> <GROUP>      305 May 26 17:38 access.log.3.gz
-rw-r----- 1 <USER> <GROUP>      407 May 22 11:39 access.log.4.gz
-rw-r----- 1 <USER> <GROUP>     1166 May 21 23:46 access.log.5.gz
-rw-r----- 1 <USER> <GROUP>     7010 May 20 22:40 access.log.6.gz
-rw-r----- 1 <USER> <GROUP>        0 May 22 00:00 error.log
-rw-r----- 1 <USER> <GROUP>     3271 May 21 08:38 error.log.1
-rw-r----- 1 <USER> <GROUP>      127 May 19 10:43 error.log.2.gz

===========================================================
📌 Últimas Entradas de Log
===========================================================
📝 Nginx Error Log (últimas 50 linhas):

📝 MySQL Error Log (últimas 50 linhas):

===========================================================
📌 Processos Python em Execução
===========================================================
root         105  0.0  0.9  30120 19328 ?        Ss   May28   0:00 /usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers
www-data     111  0.0  1.1  29124 22820 ?        Ss   May28   0:00 /usr/bin/python3 /var/www/controle-ponto/biometria_service.py
www-data     618  0.0  1.9  51380 39956 ?        Ss   00:00   0:00 /usr/bin/python3 /var/www/controle-ponto/app.py
www-data     620  0.0  1.9 198784 40344 ?        Sl   00:00   0:53 /usr/bin/python3 /var/www/controle-ponto/app.py
root        1354  0.0  0.0   3472  1792 pts/3    S+   16:11   0:00 grep python

===========================================================
📌 Informações do Git
===========================================================
📦 Branch Atual:
./coletar_info_servidor.sh: line 88: git: command not found

📦 Último Commit:
./coletar_info_servidor.sh: line 90: git: command not found

📦 Status:
./coletar_info_servidor.sh: line 92: git: command not found

===========================================================
📌 Permissões e Propriedade
===========================================================
👤 Usuários do Sistema:
www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin
mysql:x:110:118:MySQL Server,,,:/nonexistent:/bin/false

👥 Grupos:
www-data:x:33:
mysql:x:118:

===========================================================
📌 Cron Jobs
===========================================================
⏰ Crontab do Root:
no crontab for root

===========================================================
📌 Configuração do Firewall
===========================================================
🛡️ Status do UFW:
Status: inactive

===========================================================
📌 Resumo
===========================================================
✅ Coleta de informações concluída
📍 Arquivo salvo em: /root/info_servidor_20250529_161114.txt
