-- ========================================
-- SISTEMA DE REGISTROS DE PONTO - RLPONTO-WEB
-- Data: 08/01/2025
-- Descrição: Script BÁSICO compatível com qualquer MySQL
-- ========================================

-- 0. CONFIGURAÇÕES INICIAIS
SET FOREIGN_KEY_CHECKS = 0;

-- 1. CRIAÇÃO DO DATABASE
CREATE DATABASE IF NOT EXISTS controle_ponto
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE controle_ponto;

-- 2. REMOÇÃO SEGURA DE ESTRUTURAS (ignorar erros)
DROP TRIGGER IF EXISTS tr_audit_registros_ponto;
DROP VIEW IF EXISTS vw_relatorio_pontos;
DROP VIEW IF EXISTS vw_estatisticas_pontos;
DROP PROCEDURE IF EXISTS RelatorioHorasTrabalhadas;
DROP FUNCTION IF EXISTS ValidarRegistroDuplicado;

-- 3. CRIAÇÃO DA TABELA PRINCIPAL
CREATE TABLE IF NOT EXISTS registros_ponto (
    id INT PRIMARY KEY AUTO_INCREMENT,
    funcionario_id INT NOT NULL,
    tipo_registro ENUM('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida') NOT NULL,
    data_hora DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metodo_registro ENUM('biometrico', 'manual') NOT NULL,
    criado_por INT NULL COMMENT 'ID do usuário que fez registro manual',
    template_biometrico LONGBLOB NULL COMMENT 'Template biométrico',
    qualidade_biometria INT NULL COMMENT 'Qualidade 0-100',
    observacoes TEXT NULL,
    ip_origem VARCHAR(45) NULL,
    user_agent TEXT NULL,
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_funcionario_data (funcionario_id, data_hora),
    INDEX idx_data_tipo (funcionario_id, tipo_registro),
    INDEX idx_metodo (metodo_registro),
    INDEX idx_criado_por (criado_por)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT = 'Registros de ponto biométrico e manual - RLPONTO-WEB v1.1';

-- 4. CRIAR VIEW DE RELATÓRIOS
CREATE VIEW vw_relatorio_pontos AS
SELECT 
    rp.id,
    rp.funcionario_id,
    f.nome_completo,
    f.matricula_empresa,
    f.cpf,
    rp.data_hora,
    DATE(rp.data_hora) as data_registro,
    TIME(rp.data_hora) as hora_registro,
    rp.tipo_registro,
    CASE rp.tipo_registro
        WHEN 'entrada_manha' THEN 'Entrada Manhã'
        WHEN 'saida_almoco' THEN 'Saída Almoço'
        WHEN 'entrada_tarde' THEN 'Entrada Tarde'
        WHEN 'saida' THEN 'Saída'
    END AS tipo_descricao,
    rp.metodo_registro,
    CASE 
        WHEN rp.metodo_registro = 'biometrico' THEN 'Biométrico'
        ELSE 'Manual'
    END AS metodo_descricao,
    COALESCE(f.setor_obra, f.setor, 'Não informado') as setor,
    f.cargo,
    COALESCE(f.empresa, 'Não informado') as empresa,
    rp.qualidade_biometria,
    rp.observacoes,
    rp.ip_origem,
    rp.criado_em,
    u.usuario as criado_por_usuario,
    CASE 
        WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atraso'
        WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atraso'
        ELSE 'Pontual'
    END AS status_pontualidade
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
LEFT JOIN usuarios u ON rp.criado_por = u.id
WHERE f.status_cadastro = 'Ativo'
ORDER BY rp.data_hora DESC;

-- 5. CRIAR VIEW DE ESTATÍSTICAS
CREATE VIEW vw_estatisticas_pontos AS
SELECT 
    DATE(rp.data_hora) as data_registro,
    COUNT(*) as total_registros,
    SUM(CASE WHEN rp.metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
    SUM(CASE WHEN rp.metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais,
    SUM(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN 1 ELSE 0 END) as entradas_manha,
    SUM(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN 1 ELSE 0 END) as saidas_almoco,
    SUM(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN 1 ELSE 0 END) as entradas_tarde,
    SUM(CASE WHEN rp.tipo_registro = 'saida' THEN 1 ELSE 0 END) as saidas,
    SUM(CASE 
        WHEN (rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00') OR
             (rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00')
        THEN 1 ELSE 0 
    END) as atrasos,
    COUNT(DISTINCT rp.funcionario_id) as funcionarios_registraram
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
WHERE f.status_cadastro = 'Ativo'
GROUP BY DATE(rp.data_hora)
ORDER BY data_registro DESC;

-- 6. CRIAR FUNÇÃO DE VALIDAÇÃO
DELIMITER //
CREATE FUNCTION ValidarRegistroDuplicado(
    p_funcionario_id INT,
    p_tipo_registro VARCHAR(20),
    p_data_registro DATE
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    SELECT COUNT(*) INTO v_count
    FROM registros_ponto
    WHERE funcionario_id = p_funcionario_id
    AND tipo_registro = p_tipo_registro
    AND DATE(data_hora) = p_data_registro;
    
    RETURN v_count > 0;
END //
DELIMITER ;

-- 7. CRIAR PROCEDURE DE RELATÓRIO
DELIMITER //
CREATE PROCEDURE RelatorioHorasTrabalhadas(
    IN p_funcionario_id INT,
    IN p_data_inicio DATE,
    IN p_data_fim DATE
)
BEGIN
    SELECT 
        f.nome_completo,
        f.matricula_empresa,
        DATE(rp.data_hora) as data_trabalho,
        MIN(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN TIME(rp.data_hora) END) as entrada_manha,
        MAX(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN TIME(rp.data_hora) END) as saida_almoco,
        MIN(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN TIME(rp.data_hora) END) as entrada_tarde,
        MAX(CASE WHEN rp.tipo_registro = 'saida' THEN TIME(rp.data_hora) END) as saida,
        COUNT(*) as total_registros
    FROM registros_ponto rp
    INNER JOIN funcionarios f ON rp.funcionario_id = f.id
    WHERE (p_funcionario_id IS NULL OR rp.funcionario_id = p_funcionario_id)
    AND DATE(rp.data_hora) BETWEEN p_data_inicio AND p_data_fim
    AND f.status_cadastro = 'Ativo'
    GROUP BY f.id, f.nome_completo, f.matricula_empresa, DATE(rp.data_hora)
    ORDER BY f.nome_completo, DATE(rp.data_hora) DESC;
END //
DELIMITER ;

-- 8. CRIAR TRIGGER DE AUDITORIA
DELIMITER //
CREATE TRIGGER tr_audit_registros_ponto
    AFTER INSERT ON registros_ponto
    FOR EACH ROW
BEGIN
    -- Inserir apenas se tabela logs_sistema existir
    INSERT IGNORE INTO logs_sistema (
        acao,
        tabela_afetada,
        registro_id,
        usuario_id,
        detalhes,
        data_hora
    ) VALUES (
        'INSERT',
        'registros_ponto',
        NEW.id,
        NEW.criado_por,
        CONCAT('{"funcionario_id":', NEW.funcionario_id, ',"tipo":"', NEW.tipo_registro, '"}'),
        NOW()
    );
END //
DELIMITER ;

-- 9. RESTAURAR CONFIGURAÇÕES
SET FOREIGN_KEY_CHECKS = 1;

-- 10. VERIFICAÇÃO FINAL
SELECT 
    'Script executado com sucesso!' as status,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'controle_ponto' AND table_name = 'registros_ponto') as tabela_criada,
    (SELECT COUNT(*) FROM information_schema.views WHERE table_schema = 'controle_ponto' AND table_name = 'vw_relatorio_pontos') as view_relatorios,
    (SELECT COUNT(*) FROM information_schema.views WHERE table_schema = 'controle_ponto' AND table_name = 'vw_estatisticas_pontos') as view_estatisticas,
    (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'controle_ponto' AND routine_name = 'ValidarRegistroDuplicado') as function_criada,
    (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'controle_ponto' AND routine_name = 'RelatorioHorasTrabalhadas') as procedure_criada;

-- ========================================
-- FIM DO SCRIPT - VERSÃO ULTRA-BÁSICA
-- ======================================== 