#!/usr/bin/env python3
"""
Debug da estrutura da tabela funcionarios
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager

def debug_estrutura():
    """Debug da estrutura da tabela funcionarios"""
    print("🔍 DEBUG: ESTRUTURA DA TABELA FUNCIONARIOS")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar estrutura da tabela
        print("📋 1. ESTRUTURA DA TABELA FUNCIONARIOS:")
        estrutura = db.execute_query("DESCRIBE funcionarios")
        
        campos_problematicos = []
        for campo in estrutura:
            null_permitido = "SIM" if campo['Null'] == 'YES' else "NÃO"
            default_value = campo['Default'] if campo['Default'] is not None else "NENHUM"
            
            print(f"   {campo['Field']}: {campo['Type']} | NULL: {null_permitido} | Default: {default_value}")
            
            # Identificar campos que podem causar problemas
            if campo['Null'] == 'NO' and campo['Default'] is None and campo['Field'] not in ['id']:
                campos_problematicos.append(campo['Field'])
        
        if campos_problematicos:
            print(f"\n⚠️ CAMPOS PROBLEMÁTICOS (NOT NULL sem default):")
            for campo in campos_problematicos:
                print(f"   - {campo}")
        
        # 2. Verificar dados do Kalebe
        print(f"\n📋 2. DADOS DO KALEBE NA TABELA DE DESLIGADOS:")
        kalebe_dados = db.execute_query("""
            SELECT rg, nome_completo, cpf, matricula_empresa, data_nascimento, sexo, estado_civil
            FROM funcionarios_desligados 
            WHERE nome_completo LIKE %s
        """, ('%KALEBE%',))
        
        if kalebe_dados:
            kalebe = kalebe_dados[0]
            print(f"   ✅ Dados encontrados:")
            
            # Verificar campos problemáticos específicos
            campos_verificar = ['rg', 'data_nascimento', 'sexo', 'estado_civil']
            for campo in campos_verificar:
                if campo in kalebe:
                    valor = kalebe[campo]
                    status = "✅ OK" if valor is not None else "❌ NULL"
                    print(f"      {campo}: {valor} ({status})")
        
        return campos_problematicos
        
    except Exception as e:
        print(f"\n❌ ERRO NO DEBUG: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return []

if __name__ == "__main__":
    print("🎯 DEBUG COMPLETO: ESTRUTURA DA TABELA FUNCIONARIOS")
    print("=" * 70)
    
    campos_problematicos = debug_estrutura()
    
    if campos_problematicos:
        print(f"\n⚠️ PROBLEMAS ENCONTRADOS!")
        print(f"❌ {len(campos_problematicos)} campos NOT NULL sem default")
        print(f"❌ Isso impede a restauração de funcionários")
        print(f"\n💡 SOLUÇÃO: Corrigir função de restauração para usar valores padrão")
    else:
        print(f"\n✅ ESTRUTURA OK!")
        print(f"✅ Nenhum problema encontrado na estrutura")
