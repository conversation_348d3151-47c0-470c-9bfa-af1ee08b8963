#!/usr/bin/env python3
"""
CAPTURA DO ERRO REAL - Admin logado ainda vendo erro
"""

print("🚨 CAPTURANDO ERRO REAL - ADMIN LOGADO")
print("="*50)

try:
    # Importar aplicação
    from app import app
    print("✅ App importado")
    
    # Criar cliente de teste
    client = app.test_client()
    
    # Simular exatamente a situação do usuário - admin logado
    with client.session_transaction() as sess:
        sess['usuario'] = 'admin'
        sess['nivel_acesso'] = 'admin'
        sess['force_password_change'] = False
    
    print("✅ Admin logado simulado")
    
    # Fazer requisição para configurações
    print("\n🌐 Acessando /configuracoes/ como admin...")
    response = client.get('/configuracoes/')
    
    print(f"\n📊 RESULTADO:")
    print(f"Status Code: {response.status_code}")
    print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
    print(f"Tamanho: {len(response.data)} bytes")
    
    if response.status_code == 500:
        print("\n❌ CONFIRMADO: ERRO 500 COM ADMIN LOGADO!")
        print("\n📜 CONTEÚDO DO ERRO 500:")
        print("-" * 60)
        
        # Decodificar conteúdo do erro
        error_content = response.data.decode('utf-8', errors='ignore')
        
        # Procurar por informações específicas de erro
        if 'Traceback' in error_content:
            print("🔍 TRACEBACK ENCONTRADO:")
            lines = error_content.split('\n')
            for i, line in enumerate(lines):
                if 'Traceback' in line:
                    # Imprimir traceback completo
                    for j in range(i, min(i+20, len(lines))):
                        if lines[j].strip():
                            print(lines[j])
                    break
        else:
            # Imprimir primeiros 1000 caracteres do erro
            print("🔍 CONTEÚDO DO ERRO:")
            print(error_content[:1000])
        
        print("-" * 60)
        
        # Verificar logs da aplicação
        print("\n📄 VERIFICANDO LOGS RECENTES...")
        try:
            with open('logs/app.log', 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                recent_errors = []
                for line in lines[-50:]:  # Últimas 50 linhas
                    if 'ERROR' in line or 'configuracoes' in line.lower():
                        recent_errors.append(line.strip())
                
                if recent_errors:
                    print("🚨 ERROS RECENTES NOS LOGS:")
                    for error in recent_errors[-5:]:  # Últimos 5 erros
                        print(f"   {error}")
                else:
                    print("   Nenhum erro recente encontrado nos logs")
        except Exception as e:
            print(f"   Erro ao ler logs: {e}")
    
    elif response.status_code == 200:
        print("\n✅ SUCESSO! Página carregou normalmente")
        print("   O erro pode estar intermitente ou em outra parte")
    
    elif response.status_code == 302:
        print(f"\n🔄 REDIRECIONAMENTO para: {response.headers.get('Location', 'N/A')}")
        print("   Isso indica problema de autenticação")
    
    else:
        print(f"\n⚠️ STATUS INESPERADO: {response.status_code}")
        print(f"   Conteúdo: {response.data[:300]}")

except Exception as e:
    print(f"\n❌ ERRO DURANTE TESTE: {e}")
    import traceback
    traceback.print_exc()

print(f"\n{'='*50}")
print("🎯 ANÁLISE CONCLUÍDA")
print(f"{'='*50}") 