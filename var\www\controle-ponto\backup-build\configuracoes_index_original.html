{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        --card-shadow: 0 10px 25px rgba(0,0,0,0.08);
        --card-shadow-hover: 0 15px 35px rgba(0,0,0,0.12);
    }

    .settings-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 2rem 0;
    }

    .settings-header {
        background: var(--primary-gradient);
        border-radius: 20px;
        color: white;
        padding: 3rem 2rem;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }

    .settings-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 50%);
        transform: rotate(45deg);
        pointer-events: none;
    }

    .settings-header h1 {
        font-size: 2.8rem;
        font-weight: 700;
        margin: 0 0 1rem 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .settings-header p {
        font-size: 1.2rem;
        margin: 0;
        opacity: 0.9;
    }

    .stats-section {
        margin-bottom: 3rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        box-shadow: var(--card-shadow);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(255,255,255,0.8);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: var(--gradient);
    }

    .stat-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--card-shadow-hover);
    }

    .stat-card.empresas::before { background: var(--success-gradient); }
    .stat-card.funcionarios::before { background: var(--primary-gradient); }
    .stat-card.horarios::before { background: var(--warning-gradient); }
    .stat-card.registros::before { background: var(--danger-gradient); }

    .stat-number {
        font-size: 3.5rem;
        font-weight: 800;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        line-height: 1;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }

    .stat-subtitle {
        font-size: 0.9rem;
        color: #718096;
    }

    .config-sections {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
    }

    .config-card {
        background: white;
        border-radius: 20px;
        padding: 0;
        box-shadow: var(--card-shadow);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(255,255,255,0.8);
        overflow: hidden;
    }

    .config-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-shadow-hover);
    }

    .config-card-header {
        padding: 2rem;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.05));
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .config-icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 1.8rem;
        color: white;
        box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    }

    .config-icon.empresas { background: var(--success-gradient); }
    .config-icon.horarios { background: var(--warning-gradient); }
    .config-icon.sistema { background: var(--primary-gradient); }
    .config-icon.seguranca { background: var(--danger-gradient); }

    .config-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        text-align: center;
        margin-bottom: 0.5rem;
    }

    .config-description {
        color: #718096;
        text-align: center;
        line-height: 1.6;
        margin: 0;
    }

    .config-card-body {
        padding: 2rem;
    }

    .config-actions {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .btn-config {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .btn-config::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-config:hover::before {
        left: 100%;
    }

    .btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .btn-primary:hover {
        transform: translateX(5px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .btn-success:hover {
        transform: translateX(5px);
        box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        color: white;
    }

    .btn-warning {
        background: var(--warning-gradient);
        color: white;
    }

    .btn-warning:hover {
        transform: translateX(5px);
        box-shadow: 0 8px 25px rgba(250, 112, 154, 0.3);
        color: white;
    }

    .btn-icon {
        font-size: 1.2rem;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }

    .btn-config:hover .btn-icon {
        opacity: 1;
    }

    .quick-actions {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-top: 2rem;
        box-shadow: var(--card-shadow);
    }

    .quick-actions h3 {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .quick-action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1.5rem 1rem;
        border-radius: 12px;
        background: #f7fafc;
        border: 2px solid #e2e8f0;
        text-decoration: none;
        color: #4a5568;
        transition: all 0.3s ease;
        font-weight: 600;
    }

    .quick-action-btn:hover {
        background: #edf2f7;
        border-color: #cbd5e0;
        transform: translateY(-2px);
        color: #2d3748;
    }

    .quick-action-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #667eea;
    }

    @media (max-width: 768px) {
        .settings-container {
            padding: 1rem 0;
        }
        
        .settings-header {
            padding: 2rem 1rem;
            margin-bottom: 2rem;
        }
        
        .settings-header h1 {
            font-size: 2rem;
        }
        
        .config-sections {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }
    }

    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="settings-container">
    <div class="container">
        <!-- Header -->
        <div class="settings-header fade-in">
            <h1>⚙️ Configurações do Sistema</h1>
            <p>Gerencie todas as configurações do sistema de controle de ponto</p>
            {% if erro %}
            <div style="background: rgba(255,0,0,0.1); border: 1px solid rgba(255,0,0,0.3); border-radius: 8px; padding: 1rem; margin-top: 1rem;">
                <strong>⚠️ Aviso:</strong> {{ erro }}
            </div>
            {% endif %}
        </div>

        <!-- Estatísticas -->
        <div class="stats-section fade-in">
            <div class="stats-grid">
                <div class="stat-card empresas">
                    <div class="stat-number">{{ estatisticas.total_empresas }}</div>
                    <div class="stat-label">Empresas Ativas</div>
                    <div class="stat-subtitle">Cadastradas no sistema</div>
                </div>
                
                <div class="stat-card funcionarios">
                    <div class="stat-number">{{ estatisticas.total_funcionarios }}</div>
                    <div class="stat-label">Funcionários</div>
                    <div class="stat-subtitle">Usuários ativos</div>
                </div>
                
                <div class="stat-card horarios">
                    <div class="stat-number">{{ estatisticas.total_horarios }}</div>
                    <div class="stat-label">Horários</div>
                    <div class="stat-subtitle">Configurações ativas</div>
                </div>
                
                <div class="stat-card registros">
                    <div class="stat-number">{{ estatisticas.registros_mes }}</div>
                    <div class="stat-label">Registros</div>
                    <div class="stat-subtitle">Este mês</div>
                </div>
            </div>
        </div>

        <!-- Seções de Configuração -->
        <div class="config-sections fade-in">
            <!-- Gestão de Empresas -->
            <div class="config-card">
                <div class="config-card-header">
                    <div class="config-icon empresas">
                        <i class="fas fa-building"></i>
                    </div>
                    <h3 class="config-title">Gestão de Empresas</h3>
                    <p class="config-description">
                        Configure informações das empresas, dados corporativos e estrutura organizacional
                    </p>
                </div>
                <div class="config-card-body">
                    <div class="config-actions">
                        <a href="{{ url_for('configuracoes.listar_empresas') }}" class="btn-config btn-success">
                            <span>Gerenciar Empresas</span>
                            <i class="fas fa-arrow-right btn-icon"></i>
                        </a>
                        <a href="{{ url_for('configuracoes.nova_empresa') }}" class="btn-config btn-primary">
                            <span>Nova Empresa</span>
                            <i class="fas fa-plus btn-icon"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Horários de Trabalho -->
            <div class="config-card">
                <div class="config-card-header">
                    <div class="config-icon horarios">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="config-title">Horários de Trabalho</h3>
                    <p class="config-description">
                        Defina escalas, turnos e configurações de horários para diferentes perfis de funcionários
                    </p>
                </div>
                <div class="config-card-body">
                    <div class="config-actions">
                        <a href="{{ url_for('horarios_trabalho.index') }}" class="btn-config btn-warning">
                            <span>Configurar Horários</span>
                            <i class="fas fa-arrow-right btn-icon"></i>
                        </a>
                        <a href="#" class="btn-config btn-primary" onclick="alert('Funcionalidade em desenvolvimento')">
                            <span>Novo Horário</span>
                            <i class="fas fa-plus btn-icon"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Configurações do Sistema -->
            <div class="config-card">
                <div class="config-card-header">
                    <div class="config-icon sistema">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3 class="config-title">Sistema</h3>
                    <p class="config-description">
                        Configurações gerais do sistema, backup, logs e manutenção
                    </p>
                </div>
                <div class="config-card-body">
                    <div class="config-actions">
                        <a href="#" class="btn-config btn-primary">
                            <span>Configurações Gerais</span>
                            <i class="fas fa-arrow-right btn-icon"></i>
                        </a>
                        <a href="#" class="btn-config btn-warning">
                            <span>Backup e Logs</span>
                            <i class="fas fa-download btn-icon"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Segurança e Usuários -->
            <div class="config-card">
                <div class="config-card-header">
                    <div class="config-icon seguranca">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="config-title">Segurança</h3>
                    <p class="config-description">
                        Gerenciamento de usuários, permissões e configurações de segurança
                    </p>
                </div>
                <div class="config-card-body">
                    <div class="config-actions">
                        <a href="{{ url_for('configurar_usuarios') }}" class="btn-config btn-success">
                            <span>Gerenciar Usuários</span>
                            <i class="fas fa-arrow-right btn-icon"></i>
                        </a>
                        <a href="#" class="btn-config btn-warning">
                            <span>Permissões</span>
                            <i class="fas fa-key btn-icon"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ações Rápidas -->
        <div class="quick-actions fade-in">
            <h3>🚀 Ações Rápidas</h3>
            <div class="quick-actions-grid">
                <a href="{{ url_for('funcionarios.index') }}" class="quick-action-btn">
                    <i class="fas fa-users quick-action-icon"></i>
                    <span>Funcionários</span>
                </a>
                <a href="{{ url_for('relatorios.pontos') }}" class="quick-action-btn">
                    <i class="fas fa-chart-bar quick-action-icon"></i>
                    <span>Relatórios</span>
                </a>
                <a href="{{ url_for('registro_ponto.biometrico') }}" class="quick-action-btn">
                    <i class="fas fa-fingerprint quick-action-icon"></i>
                    <span>Biometria</span>
                </a>
                <a href="{{ url_for('configuracoes.index') }}" class="quick-action-btn">
                    <i class="fas fa-database quick-action-icon"></i>
                    <span>Backup</span>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Adicionar animação aos elementos quando entram na viewport
document.addEventListener('DOMContentLoaded', function() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observar todos os cards
    document.querySelectorAll('.config-card, .stat-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>
{% endblock %} 