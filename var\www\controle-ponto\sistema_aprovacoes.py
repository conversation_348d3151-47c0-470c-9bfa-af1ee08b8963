"""
RLPONTO-WEB v2.0 - Sistema de Aprovações de Horas Extras
Gerenciamento completo de aprovações RH
Data: 11/07/2025
"""

import logging
from datetime import datetime
from utils.database import get_db_connection
from pymysql.cursors import DictCursor

logger = logging.getLogger(__name__)

def criar_solicitacao_aprovacao(funcionario_id, inicio_extra, fim_extra, motivo=None, porcentagem=50.00):
    """
    Cria uma solicitação de aprovação de hora extra
    
    Args:
        funcionario_id (int): ID do funcionário
        inicio_extra (datetime): Horário de início da hora extra
        fim_extra (datetime): Hor<PERSON>rio de fim da hora extra
        motivo (str): Motivo da hora extra
        porcentagem (float): Porcentagem de hora extra
    
    Returns:
        int: ID da aprovação criada
    """
    try:
        # Calcular duração
        duracao = fim_extra - inicio_extra
        duracao_minutos = int(duracao.total_seconds() / 60)
        
        # Inserir solicitação
        query = """
            INSERT INTO aprovacoes_horas_extras (
                funcionario_id, data_referencia, inicio_extra, fim_extra,
                duracao_minutos, motivo_hora_extra, porcentagem_extra,
                status_aprovacao
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, 'PENDENTE')
        """
        
        params = (
            funcionario_id,
            inicio_extra.date(),
            inicio_extra,
            fim_extra,
            duracao_minutos,
            motivo or 'Hora extra solicitada',
            porcentagem
        )
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        aprovacao_id = cursor.lastrowid
        conn.commit()
        cursor.close()
        conn.close()
        
        # Registrar no histórico
        registrar_historico_funcionario(
            funcionario_id=funcionario_id,
            tipo_evento='HORA_EXTRA_SOLICITADA',
            data_evento=datetime.now(),
            data_referencia=inicio_extra.date(),
            detalhes=f'Solicitação #{aprovacao_id} - {duracao_minutos}min - {motivo or "Sem motivo especificado"}',
            valor_minutos=duracao_minutos,
            status_aprovacao='PENDENTE'
        )
        
        # Criar notificação para RH
        criar_notificacao_rh(
            tipo='HORA_EXTRA_PENDENTE',
            funcionario_id=funcionario_id,
            aprovacao_id=aprovacao_id,
            titulo=f'Nova solicitação de hora extra',
            mensagem=f'Funcionário solicitou aprovação para {duracao_minutos}min de hora extra.'
        )
        
        logger.info(f"Solicitação de aprovação criada: ID {aprovacao_id}")
        return aprovacao_id
        
    except Exception as e:
        logger.error(f"Erro ao criar solicitação de aprovação: {e}")
        return None

def aprovar_hora_extra(aprovacao_id, aprovado_por, observacoes=None):
    """
    Aprova uma solicitação de hora extra
    
    Args:
        aprovacao_id (int): ID da aprovação
        aprovado_por (int): ID do usuário que aprovou
        observacoes (str): Observações da aprovação
    
    Returns:
        bool: True se aprovado com sucesso
    """
    try:
        # Atualizar status da aprovação
        query_update = """
            UPDATE aprovacoes_horas_extras 
            SET status_aprovacao = 'APROVADO',
                aprovado_por = %s,
                data_aprovacao = NOW(),
                observacoes_aprovacao = %s
            WHERE id = %s
        """
        
        DatabaseManager.execute_query(query_update, (aprovado_por, observacoes, aprovacao_id))
        
        # Obter dados da aprovação
        aprovacao = obter_aprovacao_por_id(aprovacao_id)
        if not aprovacao:
            return False
        
        # Registrar no histórico
        registrar_historico_funcionario(
            funcionario_id=aprovacao['funcionario_id'],
            tipo_evento='HORA_EXTRA_APROVADA',
            data_evento=datetime.now(),
            data_referencia=aprovacao['data_referencia'],
            detalhes=f'Hora extra aprovada - {aprovacao["duracao_minutos"]}min creditados - {observacoes or "Sem observações"}',
            valor_minutos=aprovacao['duracao_minutos'],
            status_aprovacao='APROVADO',
            aprovado_por=aprovado_por
        )
        
        logger.info(f"Hora extra aprovada: ID {aprovacao_id}")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao aprovar hora extra: {e}")
        return False

def rejeitar_hora_extra(aprovacao_id, rejeitado_por, observacoes):
    """
    Rejeita uma solicitação de hora extra
    
    Args:
        aprovacao_id (int): ID da aprovação
        rejeitado_por (int): ID do usuário que rejeitou
        observacoes (str): Motivo da rejeição
    
    Returns:
        bool: True se rejeitado com sucesso
    """
    try:
        # Atualizar status da aprovação
        query_update = """
            UPDATE aprovacoes_horas_extras 
            SET status_aprovacao = 'REJEITADO',
                aprovado_por = %s,
                data_aprovacao = NOW(),
                observacoes_aprovacao = %s
            WHERE id = %s
        """
        
        DatabaseManager.execute_query(query_update, (rejeitado_por, observacoes, aprovacao_id))
        
        # Obter dados da aprovação
        aprovacao = obter_aprovacao_por_id(aprovacao_id)
        if not aprovacao:
            return False
        
        # Registrar no histórico
        registrar_historico_funcionario(
            funcionario_id=aprovacao['funcionario_id'],
            tipo_evento='HORA_EXTRA_REJEITADA',
            data_evento=datetime.now(),
            data_referencia=aprovacao['data_referencia'],
            detalhes=f'Hora extra rejeitada - {aprovacao["duracao_minutos"]}min - Motivo: {observacoes}',
            valor_minutos=0,
            status_aprovacao='REJEITADO',
            aprovado_por=rejeitado_por
        )
        
        logger.info(f"Hora extra rejeitada: ID {aprovacao_id}")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao rejeitar hora extra: {e}")
        return False

def obter_aprovacao_por_id(aprovacao_id):
    """
    Obtém dados de uma aprovação por ID
    
    Args:
        aprovacao_id (int): ID da aprovação
    
    Returns:
        dict: Dados da aprovação
    """
    try:
        query = """
            SELECT 
                a.*,
                f.nome_completo,
                u.nome as aprovado_por_nome
            FROM aprovacoes_horas_extras a
            INNER JOIN funcionarios f ON a.funcionario_id = f.id
            LEFT JOIN usuarios u ON a.aprovado_por = u.id
            WHERE a.id = %s
        """
        
        result = DatabaseManager.execute_query(query, (aprovacao_id,))
        return result[0] if result else None
        
    except Exception as e:
        logger.error(f"Erro ao obter aprovação: {e}")
        return None

def listar_aprovacoes_pendentes():
    """
    Lista todas as aprovações pendentes
    
    Returns:
        list: Lista de aprovações pendentes
    """
    try:
        query = """
            SELECT 
                a.*,
                f.nome_completo,
                f.setor,
                e.razao_social as empresa
            FROM aprovacoes_horas_extras a
            INNER JOIN funcionarios f ON a.funcionario_id = f.id
            LEFT JOIN empresas e ON f.empresa_id = e.id
            WHERE a.status_aprovacao = 'PENDENTE'
            ORDER BY a.criado_em ASC
        """
        
        result = DatabaseManager.execute_query(query)
        return result if result else []
        
    except Exception as e:
        logger.error(f"Erro ao listar aprovações pendentes: {e}")
        return []

def registrar_historico_funcionario(funcionario_id, tipo_evento, data_evento, data_referencia, 
                                  detalhes, valor_minutos=0, status_aprovacao='NAO_APLICAVEL', 
                                  aprovado_por=None, observacoes=None):
    """
    Registra evento no histórico do funcionário
    
    Args:
        funcionario_id (int): ID do funcionário
        tipo_evento (str): Tipo do evento
        data_evento (datetime): Data/hora do evento
        data_referencia (date): Data de referência
        detalhes (str): Detalhes do evento
        valor_minutos (int): Valor em minutos
        status_aprovacao (str): Status de aprovação
        aprovado_por (int): ID de quem aprovou
        observacoes (str): Observações
    
    Returns:
        int: ID do registro criado
    """
    try:
        query = """
            INSERT INTO historico_funcionario (
                funcionario_id, tipo_evento, data_evento, data_referencia,
                detalhes, valor_minutos, status_aprovacao, aprovado_por, observacoes
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            funcionario_id, tipo_evento, data_evento, data_referencia,
            detalhes, valor_minutos, status_aprovacao, aprovado_por, observacoes
        )
        
        historico_id = DatabaseManager.execute_insert(query, params)
        logger.info(f"Histórico registrado: ID {historico_id}")
        return historico_id
        
    except Exception as e:
        logger.error(f"Erro ao registrar histórico: {e}")
        return None

def criar_notificacao_rh(tipo, funcionario_id, titulo, mensagem, aprovacao_id=None):
    """
    Cria notificação para o RH
    
    Args:
        tipo (str): Tipo da notificação
        funcionario_id (int): ID do funcionário
        titulo (str): Título da notificação
        mensagem (str): Mensagem da notificação
        aprovacao_id (int): ID da aprovação (opcional)
    
    Returns:
        int: ID da notificação criada
    """
    try:
        query = """
            INSERT INTO notificacoes_rh (
                tipo_notificacao, funcionario_id, aprovacao_id, titulo, mensagem
            ) VALUES (%s, %s, %s, %s, %s)
        """
        
        params = (tipo, funcionario_id, aprovacao_id, titulo, mensagem)
        
        notificacao_id = DatabaseManager.execute_insert(query, params)
        logger.info(f"Notificação RH criada: ID {notificacao_id}")
        return notificacao_id
        
    except Exception as e:
        logger.error(f"Erro ao criar notificação RH: {e}")
        return None

def obter_historico_funcionario(funcionario_id, data_inicio=None, data_fim=None):
    """
    Obtém histórico completo do funcionário
    
    Args:
        funcionario_id (int): ID do funcionário
        data_inicio (date): Data de início (opcional)
        data_fim (date): Data de fim (opcional)
    
    Returns:
        list: Lista de eventos do histórico
    """
    try:
        query = """
            SELECT 
                h.*,
                u.nome as aprovado_por_nome
            FROM historico_funcionario h
            LEFT JOIN usuarios u ON h.aprovado_por = u.id
            WHERE h.funcionario_id = %s
        """
        
        params = [funcionario_id]
        
        if data_inicio:
            query += " AND h.data_referencia >= %s"
            params.append(data_inicio)
            
        if data_fim:
            query += " AND h.data_referencia <= %s"
            params.append(data_fim)
            
        query += " ORDER BY h.data_evento DESC"
        
        result = DatabaseManager.execute_query(query, params)
        return result if result else []
        
    except Exception as e:
        logger.error(f"Erro ao obter histórico do funcionário: {e}")
        return []
