#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar se a edição da jornada da AiNexus está funcionando
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def verificar_edicao_jornada():
    """Verificar se a edição da jornada está funcionando"""
    print("🔍 VERIFICANDO EDIÇÃO DA JORNADA DA AINEXUS")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar jornada atual da AiNexus
        print("\n1. Verificando jornada atual da AiNexus...")
        sql_jornada_atual = """
        SELECT 
            jt.id, jt.nome_jornada, jt.tipo_jornada,
            jt.seg_qui_entrada, jt.seg_qui_saida,
            jt.sexta_entrada, jt.sexta_saida,
            jt.intervalo_inicio, jt.intervalo_fim,
            jt.tolerancia_entrada_minutos,
            jt.padrao, jt.ativa, jt.data_cadastro, jt.data_atualizacao,
            e.razao_social as empresa_nome, e.id as empresa_id
        FROM jornadas_trabalho jt
        JOIN empresas e ON jt.empresa_id = e.id
        WHERE e.razao_social LIKE '%%AiNexus%%' AND jt.padrao = TRUE AND jt.ativa = TRUE
        """
        
        jornada_atual = db.execute_query(sql_jornada_atual, fetch_one=True)
        
        if jornada_atual:
            print(f"📋 Jornada atual da AiNexus:")
            print(f"   ID: {jornada_atual['id']}")
            print(f"   Nome: {jornada_atual['nome_jornada']}")
            print(f"   Seg-Qui: {jornada_atual['seg_qui_entrada']} às {jornada_atual['seg_qui_saida']}")
            print(f"   Sexta: {jornada_atual['sexta_entrada']} às {jornada_atual['sexta_saida']}")
            print(f"   Intervalo: {jornada_atual['intervalo_inicio']} às {jornada_atual['intervalo_fim']}")
            print(f"   Tolerância: {jornada_atual['tolerancia_entrada_minutos']} minutos")
            print(f"   Última atualização: {jornada_atual['data_atualizacao']}")
        else:
            print(f"❌ Nenhuma jornada padrão ativa encontrada para AiNexus!")
            return False
        
        # 2. Simular atualização da jornada para os valores corretos
        print(f"\n2. Atualizando jornada para os valores corretos...")
        print(f"   📋 Valores que deveriam estar salvos:")
        print(f"   Seg-Qui: 09:00 às 18:00")
        print(f"   Sexta: 09:00 às 18:00")
        print(f"   Intervalo: 13:00 às 14:00")
        print(f"   Tolerância: 5 minutos")
        
        sql_update = """
        UPDATE jornadas_trabalho 
        SET seg_qui_entrada = '09:00:00',
            seg_qui_saida = '18:00:00',
            sexta_entrada = '09:00:00',
            sexta_saida = '18:00:00',
            intervalo_inicio = '13:00:00',
            intervalo_fim = '14:00:00',
            tolerancia_entrada_minutos = 5,
            data_atualizacao = CURRENT_TIMESTAMP
        WHERE id = %s
        """
        
        result = db.execute_query(sql_update, (jornada_atual['id'],), fetch_all=False)
        
        if result is not None:
            print(f"✅ Jornada atualizada com sucesso!")
        else:
            print(f"❌ Erro ao atualizar jornada")
            return False
        
        # 3. Verificar se a atualização foi salva
        print(f"\n3. Verificando se a atualização foi salva...")
        jornada_atualizada = db.execute_query(sql_jornada_atual, fetch_one=True)
        
        if jornada_atualizada:
            print(f"📋 Jornada após atualização:")
            print(f"   ID: {jornada_atualizada['id']}")
            print(f"   Nome: {jornada_atualizada['nome_jornada']}")
            print(f"   Seg-Qui: {jornada_atualizada['seg_qui_entrada']} às {jornada_atualizada['seg_qui_saida']}")
            print(f"   Sexta: {jornada_atualizada['sexta_entrada']} às {jornada_atualizada['sexta_saida']}")
            print(f"   Intervalo: {jornada_atualizada['intervalo_inicio']} às {jornada_atualizada['intervalo_fim']}")
            print(f"   Tolerância: {jornada_atualizada['tolerancia_entrada_minutos']} minutos")
            print(f"   Última atualização: {jornada_atualizada['data_atualizacao']}")
            
            # Verificar se os valores estão corretos
            if (jornada_atualizada['seg_qui_entrada'].strftime('%H:%M:%S') == '09:00:00' and
                jornada_atualizada['seg_qui_saida'].strftime('%H:%M:%S') == '18:00:00' and
                jornada_atualizada['tolerancia_entrada_minutos'] == 5):
                print(f"✅ Valores corretos salvos!")
            else:
                print(f"❌ Valores não foram salvos corretamente")
        
        # 4. Verificar como o funcionário Richardson verá agora
        print(f"\n4. Verificando como Richardson verá a jornada agora...")
        from utils.database import FuncionarioQueries
        
        funcionario_atualizado = FuncionarioQueries.get_with_epis(1)
        
        if funcionario_atualizado:
            print(f"📋 Richardson após atualização da jornada:")
            print(f"   Jornada: {funcionario_atualizado.get('nome_jornada')}")
            print(f"   Seg-Qui: {funcionario_atualizado.get('jornada_seg_qui_entrada')} às {funcionario_atualizado.get('jornada_seg_qui_saida')}")
            print(f"   Sexta: {funcionario_atualizado.get('jornada_sex_entrada')} às {funcionario_atualizado.get('jornada_sex_saida')}")
            print(f"   Intervalo: {funcionario_atualizado.get('jornada_intervalo_entrada')} às {funcionario_atualizado.get('jornada_intervalo_saida')}")
            print(f"   Tolerância: {funcionario_atualizado.get('tolerancia_entrada_minutos')} minutos")
        
        print(f"\n✅ CORREÇÃO CONCLUÍDA!")
        print(f"   A jornada da AiNexus foi atualizada para os valores corretos.")
        print(f"   O funcionário Richardson agora herdará automaticamente a jornada correta.")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a verificação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    verificar_edicao_jornada()
