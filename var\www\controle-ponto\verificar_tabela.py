#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar estrutura da tabela usuarios
Sistema: RLPONTO-WEB v1.0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database import get_db_connection
import pymysql

def verificar_estrutura():
    """Verifica a estrutura da tabela usuarios"""
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Verificar estrutura da tabela
        cursor.execute("DESCRIBE usuarios")
        estrutura = cursor.fetchall()
        
        print("=" * 80)
        print("📋 ESTRUTURA DA TABELA 'usuarios'")
        print("=" * 80)
        
        for campo in estrutura:
            print(f"Campo: {campo[0]}")
            print(f"Tipo: {campo[1]}")
            print(f"Null: {campo[2]}")
            print(f"Key: {campo[3]}")
            print(f"Default: {campo[4]}")
            print(f"Extra: {campo[5]}")
            print("-" * 40)
        
        # Verificar valores únicos de nivel_acesso
        cursor.execute("SELECT DISTINCT nivel_acesso FROM usuarios")
        niveis = cursor.fetchall()
        
        print("\n" + "=" * 80)
        print("🔐 NÍVEIS DE ACESSO EXISTENTES")
        print("=" * 80)
        
        for nivel in niveis:
            print(f"- '{nivel[0]}'")
        
        # Tentar alterar a estrutura da tabela se necessário
        print("\n" + "=" * 80)
        print("🔧 AJUSTANDO ESTRUTURA DA TABELA")
        print("=" * 80)
        
        try:
            # Alterar o campo nivel_acesso para suportar 'administrador'
            cursor.execute("""
                ALTER TABLE usuarios 
                MODIFY COLUMN nivel_acesso VARCHAR(20) NOT NULL DEFAULT 'usuario'
            """)
            conn.commit()
            print("✅ Campo 'nivel_acesso' expandido para VARCHAR(20)")
            
            # Agora tentar atualizar o usuário admin
            cursor.execute("""
                UPDATE usuarios 
                SET nivel_acesso = 'administrador' 
                WHERE usuario = 'admin'
            """)
            conn.commit()
            print("✅ Usuário 'admin' atualizado para nível 'administrador'")
            
        except Exception as e:
            print(f"⚠️ Erro ao alterar estrutura: {e}")
            
            # Tentar uma abordagem alternativa - usar 'admin' mesmo
            print("\n🔄 Tentando abordagem alternativa...")
            print("Vou ajustar o código para aceitar 'admin' em vez de 'administrador'")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Erro ao verificar estrutura: {e}")

if __name__ == "__main__":
    verificar_estrutura()
