-- ================================================================
-- RLPONTO-WEB v1.0 - Estruturas para Configurações MCP
-- Implementação dos Parâmetros Mínimos de Configuração
-- Desenvolvido por: AiNexus Tecnologia
-- Autor: <PERSON> - Full Stack Developer
-- Data: 11/01/2025
-- ================================================================

-- Tabela para configurações da empresa (conforme MCP)
CREATE TABLE IF NOT EXISTS cad_empresas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome_empresa VARCHAR(255) NOT NULL,
    logotipo LONGBLOB NULL COMMENT 'Logotipo da empresa em formato base64 ou binário',
    logotipo_mime_type VARCHAR(100) NULL COMMENT 'Tipo MIME do logotipo (image/png, image/jpeg, etc.)',
    regras_especificas TEXT NULL COMMENT 'Regras específicas da empresa',
    tolerancia_atraso INT DEFAULT 10 COMMENT 'Tolerância para atraso em minutos',
    tolerancia_saida_antecipada INT DEFAULT 10 COMMENT 'Tolerância para saída antecipada em minutos',
    jornada_trabalho_padrao TIME DEFAULT '08:00:00' COMMENT 'Jornada de trabalho padrão',
    intervalo_almoco_inicio TIME DEFAULT '12:00:00' COMMENT 'Início do intervalo de almoço',
    intervalo_almoco_fim TIME DEFAULT '13:00:00' COMMENT 'Fim do intervalo de almoço',
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ativa BOOLEAN DEFAULT TRUE,
    INDEX idx_ativa (ativa),
    INDEX idx_data_criacao (data_criacao)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT 'Configurações detalhadas da empresa conforme MCP';

-- Tabela para dispositivos biométricos (conforme MCP)
CREATE TABLE IF NOT EXISTS dispositivos_biometricos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL UNIQUE COMMENT 'ID único do dispositivo (independente da porta USB)',
    nome_dispositivo VARCHAR(255) NOT NULL,
    fabricante ENUM('SecuGen', 'Suprema', 'Nitgen', 'Integrated Biometrics', 'Goodix', 'Synaptics', 'Outros') NOT NULL,
    modelo VARCHAR(255) NULL,
    numero_serie VARCHAR(255) NULL,
    vendor_id VARCHAR(10) NULL COMMENT 'Vendor ID USB',
    product_id VARCHAR(10) NULL COMMENT 'Product ID USB',
    porta_usb VARCHAR(50) NULL COMMENT 'Porta USB atual (pode mudar)',
    firmware_version VARCHAR(100) NULL,
    driver_version VARCHAR(100) NULL,
    resolucao_sensor VARCHAR(50) NULL COMMENT 'Resolução do sensor (ex: 500dpi)',
    area_captura VARCHAR(50) NULL COMMENT 'Área de captura (ex: 15.7mm x 19.9mm)',
    status_dispositivo ENUM('Ativo', 'Inativo', 'Erro', 'Desconectado') DEFAULT 'Ativo',
    data_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_ultima_deteccao TIMESTAMP NULL,
    data_ultima_utilizacao TIMESTAMP NULL,
    configuracoes_json JSON NULL COMMENT 'Configurações específicas do dispositivo em formato JSON',
    observacoes TEXT NULL,
    removido BOOLEAN DEFAULT FALSE COMMENT 'Soft delete para auditoria',
    data_remocao TIMESTAMP NULL,
    usuario_registro VARCHAR(100) NULL COMMENT 'Usuário que registrou o dispositivo',
    INDEX idx_device_id (device_id),
    INDEX idx_fabricante (fabricante),
    INDEX idx_status (status_dispositivo),
    INDEX idx_data_registro (data_registro),
    INDEX idx_removido (removido)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT 'Registro permanente de dispositivos biométricos detectados conforme MCP';

-- Tabela para histórico de detecções de dispositivos
CREATE TABLE IF NOT EXISTS historico_deteccoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    dispositivo_id INT NOT NULL,
    evento ENUM('Conectado', 'Desconectado', 'Erro', 'Teste') NOT NULL,
    porta_usb VARCHAR(50) NULL,
    detalhes_evento TEXT NULL,
    data_evento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usuario VARCHAR(100) NULL,
    FOREIGN KEY (dispositivo_id) REFERENCES dispositivos_biometricos(id) ON DELETE CASCADE,
    INDEX idx_dispositivo_evento (dispositivo_id, evento),
    INDEX idx_data_evento (data_evento)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT 'Histórico de eventos dos dispositivos biométricos';

-- Tabela para configurações do sistema biométrico
CREATE TABLE IF NOT EXISTS configuracoes_biometria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    parametro VARCHAR(100) NOT NULL UNIQUE,
    valor TEXT NOT NULL,
    tipo_parametro ENUM('INTEGER', 'FLOAT', 'STRING', 'BOOLEAN', 'JSON') NOT NULL,
    descricao TEXT NULL,
    categoria ENUM('Captura', 'Qualidade', 'Timeout', 'Interface', 'Seguranca') NOT NULL,
    valor_padrao TEXT NULL,
    valor_minimo TEXT NULL,
    valor_maximo TEXT NULL,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    usuario_atualizacao VARCHAR(100) NULL,
    INDEX idx_categoria (categoria),
    INDEX idx_parametro (parametro)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT 'Configurações detalhadas do sistema biométrico';

-- Inserir configurações padrão do sistema biométrico
INSERT INTO configuracoes_biometria (parametro, valor, tipo_parametro, descricao, categoria, valor_padrao) VALUES
('timeout_captura', '10000', 'INTEGER', 'Timeout para captura biométrica em milissegundos', 'Timeout', '10000'),
('qualidade_minima', '60', 'INTEGER', 'Qualidade mínima aceitável para template (0-100)', 'Qualidade', '60'),
('sensibilidade_sensor', '5', 'INTEGER', 'Sensibilidade do sensor biométrico (1-10)', 'Captura', '5'),
('tentativas_maximas', '3', 'INTEGER', 'Número máximo de tentativas de captura', 'Captura', '3'),
('deteccao_automatica', 'true', 'BOOLEAN', 'Habilitar detecção automática de dispositivos', 'Interface', 'true'),
('log_capturas', 'true', 'BOOLEAN', 'Registrar log detalhado de capturas', 'Seguranca', 'true'),
('modo_debug', 'false', 'BOOLEAN', 'Habilitar modo debug do sistema biométrico', 'Interface', 'false')
ON DUPLICATE KEY UPDATE 
    descricao = VALUES(descricao),
    categoria = VALUES(categoria),
    valor_padrao = VALUES(valor_padrao);

-- Inserir configuração padrão da empresa (se não existir)
INSERT IGNORE INTO cad_empresas (nome_empresa, regras_especificas) VALUES
('RLPONTO-WEB - Sistema de Controle de Ponto', 'Configurações padrão do sistema. Personalize conforme necessário.');

-- ================================================================
-- MELHORIAS NA ESTRUTURA EXISTENTE
-- ================================================================

-- Adicionar campos faltantes na tabela empresas existente (se necessário)
ALTER TABLE empresas 
ADD COLUMN IF NOT EXISTS configuracoes_json JSON NULL COMMENT 'Configurações específicas da empresa em formato JSON',
ADD COLUMN IF NOT EXISTS logotipo_path VARCHAR(500) NULL COMMENT 'Caminho para o arquivo de logotipo da empresa',
ADD INDEX IF NOT EXISTS idx_empresas_configuracoes (id, ativa);

-- Adicionar campos para melhor rastreamento de funcionários
ALTER TABLE funcionarios 
ADD COLUMN IF NOT EXISTS ultimo_dispositivo_usado INT NULL COMMENT 'Último dispositivo biométrico utilizado pelo funcionário',
ADD COLUMN IF NOT EXISTS preferencias_json JSON NULL COMMENT 'Preferências específicas do funcionário',
ADD INDEX IF NOT EXISTS idx_funcionarios_ultimo_dispositivo (ultimo_dispositivo_usado);

-- ================================================================
-- VIEWS PARA FACILITAR CONSULTAS
-- ================================================================

-- View para dispositivos ativos
CREATE OR REPLACE VIEW vw_dispositivos_ativos AS
SELECT 
    d.id,
    d.device_id,
    d.nome_dispositivo,
    d.fabricante,
    d.modelo,
    d.status_dispositivo,
    d.porta_usb,
    d.data_registro,
    d.data_ultima_deteccao,
    COUNT(h.id) as total_eventos,
    MAX(h.data_evento) as ultimo_evento
FROM dispositivos_biometricos d
LEFT JOIN historico_deteccoes h ON d.id = h.dispositivo_id
WHERE d.removido = FALSE
GROUP BY d.id, d.device_id, d.nome_dispositivo, d.fabricante, d.modelo, d.status_dispositivo, d.porta_usb, d.data_registro, d.data_ultima_deteccao
ORDER BY d.data_ultima_deteccao DESC;

-- View para configurações da empresa com dados formatados
CREATE OR REPLACE VIEW vw_configuracoes_empresa AS
SELECT 
    e.id,
    e.nome_empresa,
    e.logotipo IS NOT NULL as tem_logotipo,
    e.regras_especificas,
    e.tolerancia_atraso,
    e.tolerancia_saida_antecipada,
    TIME_FORMAT(e.jornada_trabalho_padrao, '%H:%i') as jornada_formatada,
    TIME_FORMAT(e.intervalo_almoco_inicio, '%H:%i') as almoco_inicio_formatado,
    TIME_FORMAT(e.intervalo_almoco_fim, '%H:%i') as almoco_fim_formatado,
    e.data_criacao,
    e.data_atualizacao,
    e.ativa
FROM cad_empresas e
WHERE e.ativa = TRUE;

-- ================================================================
-- TRIGGERS PARA AUDITORIA
-- ================================================================

-- Trigger para registrar mudanças em dispositivos
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_dispositivos_historico 
AFTER UPDATE ON dispositivos_biometricos
FOR EACH ROW
BEGIN
    IF OLD.status_dispositivo != NEW.status_dispositivo THEN
        INSERT INTO historico_deteccoes (dispositivo_id, evento, porta_usb, detalhes_evento, usuario)
        VALUES (NEW.id, NEW.status_dispositivo, NEW.porta_usb, 
                CONCAT('Status alterado de ', OLD.status_dispositivo, ' para ', NEW.status_dispositivo),
                USER());
    END IF;
END$$
DELIMITER ;

-- ================================================================
-- COMENTÁRIOS FINAIS
-- ================================================================

-- Esta estrutura implementa:
-- 1. Configuração completa da empresa com logotipo (conforme MCP)
-- 2. Sistema robusto de gerenciamento de dispositivos biométricos
-- 3. Histórico completo de eventos para auditoria
-- 4. Configurações parametrizáveis do sistema biométrico
-- 5. Views otimizadas para consultas frequentes
-- 6. Triggers para auditoria automática
-- 7. Conformidade total com os Parâmetros Mínimos de Configuração (MCP) 