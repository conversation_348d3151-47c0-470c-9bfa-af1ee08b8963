#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste da Funcionalidade de Alteração de Nível via Web - RLPONTO-WEB
===================================================================

Este script testa a funcionalidade de alteração de nível de acesso
através da interface web, simulando as requisições AJAX.

Data: 07/07/2025
Autor: <PERSON>ues - AiNexus Tecnologia
"""

import requests
import json
from datetime import datetime

# Configurações
BASE_URL = "http://************"
LOGIN_URL = f"{BASE_URL}/login"
ALTERAR_NIVEL_URL = f"{BASE_URL}/alterar_nivel"
CONFIGURAR_USUARIOS_URL = f"{BASE_URL}/configurar_usuarios"

# Credenciais de admin
ADMIN_CREDENTIALS = {
    'usuario': 'admin',
    'senha': '@Ric6109'
}

def fazer_login():
    """Faz login como admin e retorna a sessão"""
    session = requests.Session()
    
    try:
        # Primeiro, acessar a página de login para obter cookies
        response = session.get(LOGIN_URL)
        if response.status_code != 200:
            print(f"❌ Erro ao acessar página de login: {response.status_code}")
            return None
        
        # Fazer login
        login_data = {
            'usuario': ADMIN_CREDENTIALS['usuario'],
            'senha': ADMIN_CREDENTIALS['senha']
        }
        
        response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
        
        if response.status_code in [302, 200]:
            print("✅ Login realizado com sucesso")
            return session
        else:
            print(f"❌ Falha no login: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Erro durante login: {e}")
        return None

def obter_usuarios(session):
    """Obtém lista de usuários da página de configuração"""
    try:
        response = session.get(CONFIGURAR_USUARIOS_URL)
        if response.status_code == 200:
            print("✅ Página de configuração de usuários acessada")
            # Aqui poderíamos fazer parsing do HTML para extrair usuários
            # Por simplicidade, vamos usar dados conhecidos
            return [
                {'id': 2, 'usuario': 'teste', 'nivel_atual': 'usuario'},
                {'id': 5, 'usuario': 'cavalcrod', 'nivel_atual': 'usuario'}
            ]
        else:
            print(f"❌ Erro ao acessar configuração de usuários: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Erro ao obter usuários: {e}")
        return []

def testar_alteracao_nivel(session, usuario_id, novo_nivel):
    """Testa a alteração de nível de um usuário"""
    try:
        # Dados da requisição (simulando o JavaScript da página)
        data = {
            'id': usuario_id,
            'nivel': novo_nivel
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'  # Simular requisição AJAX
        }
        
        response = session.post(ALTERAR_NIVEL_URL, data=data, headers=headers)
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print(f"✅ Alteração bem-sucedida: {result.get('message')}")
                    return True
                else:
                    print(f"❌ Falha na alteração: {result.get('message')}")
                    return False
            except json.JSONDecodeError:
                print(f"❌ Resposta não é JSON válido: {response.text}")
                return False
        else:
            print(f"❌ Erro HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro durante alteração: {e}")
        return False

def verificar_alteracao_banco(usuario_id, nivel_esperado):
    """Verifica se a alteração foi aplicada no banco de dados"""
    import pymysql
    
    try:
        conn = pymysql.connect(
            host='************',
            user='cavalcrod',
            password='200381',
            database='controle_ponto',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT p.nivel_acesso 
                FROM permissoes p 
                WHERE p.usuario_id = %s
            """, (usuario_id,))
            
            result = cursor.fetchone()
            if result and result['nivel_acesso'] == nivel_esperado:
                print(f"✅ Verificação no banco: nível '{nivel_esperado}' aplicado corretamente")
                return True
            else:
                nivel_atual = result['nivel_acesso'] if result else 'NULL'
                print(f"❌ Verificação no banco: esperado '{nivel_esperado}', encontrado '{nivel_atual}'")
                return False
                
    except Exception as e:
        print(f"❌ Erro ao verificar banco: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Função principal de teste"""
    print("🧪 TESTE DE ALTERAÇÃO DE NÍVEL VIA WEB")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"URL Base: {BASE_URL}")
    print("=" * 60)
    
    # 1. Fazer login
    print("\n1️⃣ Fazendo login como admin...")
    session = fazer_login()
    if not session:
        print("❌ Teste abortado: falha no login")
        return
    
    # 2. Obter usuários
    print("\n2️⃣ Obtendo lista de usuários...")
    usuarios = obter_usuarios(session)
    if not usuarios:
        print("❌ Teste abortado: não foi possível obter usuários")
        return
    
    # 3. Testar alteração de nível
    print("\n3️⃣ Testando alteração de nível...")
    
    # Escolher um usuário para teste (não admin)
    usuario_teste = None
    for user in usuarios:
        if user['usuario'] not in ['admin', 'status']:
            usuario_teste = user
            break
    
    if not usuario_teste:
        print("❌ Nenhum usuário adequado para teste encontrado")
        return
    
    print(f"📋 Usuário de teste: {usuario_teste['usuario']} (ID: {usuario_teste['id']})")
    print(f"📋 Nível atual: {usuario_teste['nivel_atual']}")
    
    # Determinar novo nível para teste
    novo_nivel = 'admin' if usuario_teste['nivel_atual'] == 'usuario' else 'usuario'
    
    print(f"\n🔄 Alterando para '{novo_nivel}'...")
    if testar_alteracao_nivel(session, usuario_teste['id'], novo_nivel):
        # Verificar no banco
        print("🔍 Verificando alteração no banco de dados...")
        if verificar_alteracao_banco(usuario_teste['id'], novo_nivel):
            print("✅ Teste de alteração: SUCESSO!")
            
            # Reverter para o nível original
            print(f"\n🔄 Revertendo para '{usuario_teste['nivel_atual']}'...")
            if testar_alteracao_nivel(session, usuario_teste['id'], usuario_teste['nivel_atual']):
                if verificar_alteracao_banco(usuario_teste['id'], usuario_teste['nivel_atual']):
                    print("✅ Teste de reversão: SUCESSO!")
                    print("\n🎉 TODOS OS TESTES PASSARAM!")
                    print("✅ Sistema de alteração de nível está funcionando corretamente")
                else:
                    print("❌ Falha na verificação da reversão")
            else:
                print("❌ Falha na reversão")
        else:
            print("❌ Falha na verificação do banco")
    else:
        print("❌ Falha na alteração de nível")

if __name__ == "__main__":
    main()
