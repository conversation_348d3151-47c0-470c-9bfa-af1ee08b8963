# Script PowerShell para configurar chave SSH no servidor RLPONTO-WEB
# Servidor: ************
# Usuário: root / Senha: @Ric6109

param(
    [string]$ServerIP = "************",
    [string]$Username = "root",
    [string]$Password = "@Ric6109"
)

Write-Host "=== CONFIGURAÇÃO AUTOMÁTICA DE CHAVE SSH ===" -ForegroundColor Green
Write-Host "Servidor: $ServerIP" -ForegroundColor Cyan
Write-Host "Usuário: $Username" -ForegroundColor Cyan
Write-Host ""

# Verificar se a chave SSH existe
$sshKeyPath = "$env:USERPROFILE\.ssh\id_rsa.pub"
if (-not (Test-Path $sshKeyPath)) {
    Write-Host "❌ Chave SSH não encontrada!" -ForegroundColor Red
    exit 1
}

# Ler a chave pública
$publicKey = Get-Content $sshKeyPath -Raw
$publicKey = $publicKey.Trim()

Write-Host "✅ Chave SSH encontrada" -ForegroundColor Green
Write-Host "📋 Chave pública:" -ForegroundColor Yellow
Write-Host $publicKey -ForegroundColor Gray
Write-Host ""

Write-Host "📝 Comandos para executar no servidor:" -ForegroundColor Green
Write-Host ""

# Instruções manuais para o usuário
Write-Host "📋 INSTRUÇÕES PARA CONFIGURAR NO SERVIDOR:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Conecte-se ao servidor:" -ForegroundColor White
Write-Host "   ssh root@$ServerIP" -ForegroundColor Cyan
Write-Host "   Senha: $Password" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Execute os seguintes comandos:" -ForegroundColor White
Write-Host ""
Write-Host "   # Criar diretório SSH para root" -ForegroundColor Green
Write-Host "   mkdir -p /root/.ssh" -ForegroundColor Cyan
Write-Host "   chmod 700 /root/.ssh" -ForegroundColor Cyan
Write-Host ""
Write-Host "   # Adicionar chave pública para root" -ForegroundColor Green
Write-Host "   echo '$publicKey' >> /root/.ssh/authorized_keys" -ForegroundColor Cyan
Write-Host "   chmod 600 /root/.ssh/authorized_keys" -ForegroundColor Cyan
Write-Host ""
Write-Host "   # Criar diretório SSH para cavalcrod" -ForegroundColor Green
Write-Host "   mkdir -p /home/<USER>/.ssh" -ForegroundColor Cyan
Write-Host "   chmod 700 /home/<USER>/.ssh" -ForegroundColor Cyan
Write-Host ""
Write-Host "   # Adicionar chave pública para cavalcrod" -ForegroundColor Green
Write-Host "   echo '$publicKey' >> /home/<USER>/.ssh/authorized_keys" -ForegroundColor Cyan
Write-Host "   chmod 600 /home/<USER>/.ssh/authorized_keys" -ForegroundColor Cyan
Write-Host "   chown -R cavalcrod:cavalcrod /home/<USER>/.ssh" -ForegroundColor Cyan
Write-Host ""
Write-Host "   # Reiniciar serviço SSH" -ForegroundColor Green
Write-Host "   systemctl restart ssh" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. Teste a conexão sem senha:" -ForegroundColor White
Write-Host "   ssh root@$ServerIP" -ForegroundColor Cyan
Write-Host "   ssh cavalcrod@$ServerIP" -ForegroundColor Cyan
Write-Host ""

# Teste de conectividade
Write-Host "🔍 TESTANDO CONECTIVIDADE..." -ForegroundColor Blue
$testResult = Test-NetConnection -ComputerName $ServerIP -Port 22 -WarningAction SilentlyContinue

if ($testResult.TcpTestSucceeded) {
    Write-Host "✅ Servidor acessível na porta 22 (SSH)" -ForegroundColor Green
} else {
    Write-Host "❌ Servidor não acessível na porta 22" -ForegroundColor Red
}

Write-Host ""
Write-Host "📊 INFORMAÇÕES DO PROJETO RLPONTO-WEB:" -ForegroundColor Green
Write-Host "   Servidor: $ServerIP" -ForegroundColor White
Write-Host "   Usuário SSH: root / cavalcrod" -ForegroundColor White
Write-Host "   Usuário MySQL: cavalcrod" -ForegroundColor White
Write-Host "   Porta MySQL: 3306" -ForegroundColor White
Write-Host "   Banco de Dados: controle_ponto" -ForegroundColor White
Write-Host ""
Write-Host "🎯 Após executar os comandos no servidor, você terá acesso SSH sem senha!" -ForegroundColor Green
