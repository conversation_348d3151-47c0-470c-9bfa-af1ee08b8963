#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para atualizar funcionários existentes para pertencerem à empresa principal
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def atualizar_funcionarios_empresa_principal():
    """Atualizar funcionários existentes para pertencerem à empresa principal"""
    try:
        print("=" * 80)
        print("🔄 ATUALIZANDO FUNCIONÁRIOS PARA EMPRESA PRINCIPAL")
        print("=" * 80)
        
        # Importar dependências
        from utils.database import DatabaseManager
        
        # Conectar ao banco
        db = DatabaseManager()
        print("✅ Conexão com banco de dados estabelecida")
        
        # 1. Buscar empresa principal
        print("\n🔍 Buscando empresa principal...")
        empresa_principal = db.execute_query(
            "SELECT id, razao_social, nome_fantasia FROM empresas WHERE empresa_principal = TRUE AND ativa = TRUE LIMIT 1"
        )
        
        if not empresa_principal:
            print("❌ ERRO: Nenhuma empresa principal encontrada!")
            print("   Configure uma empresa como principal antes de executar este script.")
            return False
        
        empresa_principal = empresa_principal[0]
        empresa_id = empresa_principal['id']
        print(f"✅ Empresa principal encontrada:")
        print(f"   ID: {empresa_id}")
        print(f"   Nome: {empresa_principal['nome_fantasia']}")
        print(f"   Razão Social: {empresa_principal['razao_social']}")
        
        # 2. Buscar funcionários sem empresa ou com empresa incorreta
        print("\n🔍 Buscando funcionários para atualizar...")
        funcionarios_para_atualizar = db.execute_query("""
            SELECT id, nome_completo, empresa_id 
            FROM funcionarios 
            WHERE empresa_id IS NULL OR empresa_id != %s
        """, (empresa_id,))
        
        if not funcionarios_para_atualizar:
            print("✅ Todos os funcionários já estão associados à empresa principal!")
            return True
        
        print(f"📊 Encontrados {len(funcionarios_para_atualizar)} funcionários para atualizar:")
        for func in funcionarios_para_atualizar:
            empresa_atual = func['empresa_id'] if func['empresa_id'] else "Sem empresa"
            print(f"   - {func['nome_completo']} (ID: {func['id']}) - Empresa atual: {empresa_atual}")
        
        # 3. Confirmar atualização
        print(f"\n⚠️ ATENÇÃO: Todos os funcionários listados acima serão associados à empresa principal:")
        print(f"   {empresa_principal['nome_fantasia']} (ID: {empresa_id})")
        
        # 4. Executar atualização
        print("\n🔄 Executando atualização...")
        
        funcionarios_atualizados = 0
        for func in funcionarios_para_atualizar:
            try:
                # Atualizar funcionário
                resultado = db.execute_query(
                    "UPDATE funcionarios SET empresa_id = %s WHERE id = %s",
                    (empresa_id, func['id']),
                    fetch_all=False
                )
                
                if resultado:
                    funcionarios_atualizados += 1
                    print(f"   ✅ {func['nome_completo']} atualizado com sucesso")
                else:
                    print(f"   ❌ Erro ao atualizar {func['nome_completo']}")
                    
            except Exception as e:
                print(f"   ❌ Erro ao atualizar {func['nome_completo']}: {e}")
        
        # 5. Verificar resultado
        print(f"\n📊 RESULTADO DA ATUALIZAÇÃO:")
        print(f"   Funcionários processados: {len(funcionarios_para_atualizar)}")
        print(f"   Funcionários atualizados: {funcionarios_atualizados}")
        print(f"   Erros: {len(funcionarios_para_atualizar) - funcionarios_atualizados}")
        
        # 6. Verificar estado final
        print("\n🔍 Verificando estado final...")
        funcionarios_final = db.execute_query("""
            SELECT 
                f.id, 
                f.nome_completo, 
                f.empresa_id,
                COALESCE(e.nome_fantasia, 'Sem empresa') as empresa_nome
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            ORDER BY f.nome_completo
        """)
        
        print("\n📋 ESTADO FINAL DOS FUNCIONÁRIOS:")
        for func in funcionarios_final:
            status = "✅" if func['empresa_id'] == empresa_id else "❌"
            print(f"   {status} {func['nome_completo']} → {func['empresa_nome']}")
        
        # 7. Estatísticas finais
        funcionarios_empresa_principal = len([f for f in funcionarios_final if f['empresa_id'] == empresa_id])
        total_funcionarios = len(funcionarios_final)
        
        print(f"\n📊 ESTATÍSTICAS FINAIS:")
        print(f"   Total de funcionários: {total_funcionarios}")
        print(f"   Na empresa principal: {funcionarios_empresa_principal}")
        print(f"   Percentual: {(funcionarios_empresa_principal/total_funcionarios)*100:.1f}%")
        
        if funcionarios_empresa_principal == total_funcionarios:
            print("\n🎉 SUCESSO! Todos os funcionários estão agora na empresa principal!")
        else:
            print(f"\n⚠️ ATENÇÃO: {total_funcionarios - funcionarios_empresa_principal} funcionários ainda não estão na empresa principal")
        
        print("\n" + "=" * 80)
        print("✅ ATUALIZAÇÃO CONCLUÍDA!")
        print("=" * 80)
        
        return funcionarios_atualizados > 0
        
    except Exception as e:
        print(f"❌ Erro durante a atualização: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def verificar_estado_atual():
    """Verificar estado atual dos funcionários"""
    try:
        print("=" * 80)
        print("🔍 VERIFICANDO ESTADO ATUAL DOS FUNCIONÁRIOS")
        print("=" * 80)
        
        from utils.database import DatabaseManager
        db = DatabaseManager()
        
        # Buscar empresa principal
        empresa_principal = db.execute_query(
            "SELECT id, nome_fantasia FROM empresas WHERE empresa_principal = TRUE AND ativa = TRUE LIMIT 1"
        )
        
        if not empresa_principal:
            print("❌ Nenhuma empresa principal encontrada!")
            return
        
        empresa_principal = empresa_principal[0]
        print(f"⭐ Empresa Principal: {empresa_principal['nome_fantasia']} (ID: {empresa_principal['id']})")
        
        # Buscar funcionários
        funcionarios = db.execute_query("""
            SELECT 
                f.id, 
                f.nome_completo, 
                f.empresa_id,
                COALESCE(e.nome_fantasia, 'Sem empresa') as empresa_nome,
                CASE WHEN f.empresa_id = %s THEN 1 ELSE 0 END as na_empresa_principal
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            ORDER BY na_empresa_principal DESC, f.nome_completo
        """, (empresa_principal['id'],))
        
        print(f"\n📋 FUNCIONÁRIOS CADASTRADOS ({len(funcionarios)}):")
        na_empresa_principal = 0
        for func in funcionarios:
            if func['na_empresa_principal']:
                status = "✅"
                na_empresa_principal += 1
            else:
                status = "❌"
            print(f"   {status} {func['nome_completo']} → {func['empresa_nome']}")
        
        print(f"\n📊 RESUMO:")
        print(f"   Na empresa principal: {na_empresa_principal}/{len(funcionarios)}")
        print(f"   Percentual: {(na_empresa_principal/len(funcionarios))*100:.1f}%")
        
        if na_empresa_principal < len(funcionarios):
            print(f"\n⚠️ {len(funcionarios) - na_empresa_principal} funcionários precisam ser atualizados!")
        else:
            print("\n🎉 Todos os funcionários estão na empresa principal!")
        
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == '__main__':
    print("Iniciando verificação e atualização...")
    verificar_estado_atual()
    print("\n" + "="*50)
    atualizar_funcionarios_empresa_principal()
