#!/usr/bin/env python3
"""
ZKAgent Local Puro - COM DADOS REAIS DO ZK4500
Integração direta com hardware através do zk4500_bridge.py
SEM conexões de backend, SEM monitoramento remoto, SEM dados simulados
"""

import json
import time
import threading
import sys
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import socketserver

# Importa o bridge real do ZK4500
try:
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from zk4500_bridge import BiometriaDevice
    print("✅ Bridge ZK4500 importado com sucesso")
    HARDWARE_DISPONIVEL = True
except ImportError as e:
    print(f"⚠️ Bridge ZK4500 não disponível: {e}")
    HARDWARE_DISPONIVEL = False

class ZKAgentRealHandler(BaseHTTPRequestHandler):
    """Handler HTTP para ZKAgent com dados REAIS do ZK4500"""
    
    # Instância única do dispositivo ZK4500
    _device = None
    
    @classmethod
    def get_device(cls):
        """Obtém ou cria instância do dispositivo ZK4500"""
        if cls._device is None and HARDWARE_DISPONIVEL:
            cls._device = BiometriaDevice()
        return cls._device
    
    def do_OPTIONS(self):
        """Handle CORS preflight"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            path = urlparse(self.path).path
            
            if path == '/test':
                device = self.get_device()
                status = 'ok' if device and device.initialized else 'hardware_error'
                self.send_json_response({
                    'status': status, 
                    'mode': 'HARDWARE_REAL',
                    'device': device.device_info if device else None
                })
                
            elif path == '/status':
                device = self.get_device()
                self.send_json_response({
                    'status': 'ok' if device and device.initialized else 'hardware_error',
                    'mode': 'HARDWARE_REAL',
                    'sdkInitialized': device.initialized if device else False,
                    'device_info': device.device_info if device else None,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                })
                
            elif path == '/list-devices':
                device = self.get_device()
                devices_count = 1 if device and device.initialized else 0
                self.send_json_response({
                    'devices': devices_count,
                    'sdkInitialized': device.initialized if device else False,
                    'hardware_available': HARDWARE_DISPONIVEL,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                })
            else:
                self.send_error_response(404, 'Endpoint não encontrado')
                
        except Exception as e:
            self.send_error_response(500, f'Erro interno: {str(e)}')
    
    def do_POST(self):
        """Handle POST requests"""
        try:
            path = urlparse(self.path).path
            
            if path == '/capture':
                self.capture_real_fingerprint()
            else:
                self.send_error_response(404, 'Endpoint não encontrado')
                
        except Exception as e:
            self.send_error_response(500, f'Erro interno: {str(e)}')
    
    def capture_real_fingerprint(self):
        """Captura impressão digital REAL do hardware ZK4500"""
        device = self.get_device()
        
        if not HARDWARE_DISPONIVEL:
            self.send_error_response(500, 'Bridge ZK4500 não disponível - instale dependências')
            return
            
        if not device:
            self.send_error_response(500, 'Dispositivo ZK4500 não inicializado')
            return
            
        if not device.initialized:
            error_msg = device.last_error or 'Dispositivo não inicializado'
            self.send_error_response(500, f'Hardware ZK4500: {error_msg}')
            return
        
        try:
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Iniciando captura REAL do ZK4500...")
            
            # Captura dados REAIS do hardware
            resultado = device.capture_fingerprint()
            
            if resultado['success']:
                # Dados REAIS capturados!
                response = {
                    'template': resultado['template'],
                    'quality': resultado['quality'],
                    'captureTime': resultado.get('capture_time', 5000),
                    'real': True,  # ✅ DADOS REAIS!
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'mode': 'HARDWARE_REAL',
                    'device': device.device_info
                }
                
                print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] ✅ CAPTURA REAL SUCESSO - Qualidade: {resultado['quality']}%")
                self.send_json_response(response)
                
            else:
                # Erro na captura
                error_msg = resultado.get('error', 'Erro desconhecido na captura')
                print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] ❌ CAPTURA REAL FALHOU: {error_msg}")
                self.send_error_response(500, f'Captura ZK4500: {error_msg}')
                
        except Exception as e:
            error_msg = f'Erro na captura real: {str(e)}'
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] ❌ EXCEÇÃO: {error_msg}")
            self.send_error_response(500, error_msg)
    
    def send_json_response(self, data):
        """Envia resposta JSON com headers CORS"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        response_json = json.dumps(data, ensure_ascii=False)
        self.wfile.write(response_json.encode('utf-8'))
        
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] RESPOSTA: {response_json[:200]}...")
    
    def send_error_response(self, code, message):
        """Envia resposta de erro"""
        self.send_response(code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_data = {'error': message, 'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')}
        response_json = json.dumps(error_data)
        self.wfile.write(response_json.encode('utf-8'))
        
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] ERRO {code}: {message}")
    
    def log_message(self, format, *args):
        """Sobrescreve log para formato personalizado"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

class ZKAgentRealServer:
    """Servidor ZKAgent - DADOS REAIS DO HARDWARE ZK4500"""
    
    def __init__(self, port=5001):
        self.port = port
        self.httpd = None
        
    def start(self):
        """Inicia o servidor"""
        try:
            self.httpd = HTTPServer(('localhost', self.port), ZKAgentRealHandler)
            print(f"""
🔐 ZKAgent COM DADOS REAIS DO ZK4500
📍 Modo: HARDWARE REAL + LOCAL
🔗 URL: http://localhost:{self.port}
🔌 Hardware: ZK4500 (bridge integrado)
🚫 Backend: DESABILITADO

✅ Endpoints disponíveis:
   GET  /test         - Teste hardware ZK4500
   GET  /status       - Status dispositivo real  
   GET  /list-devices - Conta dispositivos reais
   POST /capture      - ⭐ CAPTURA REAL ZK4500 ⭐

🎯 IMPORTANTE: 
   ✅ USA DADOS REAIS do leitor ZK4500
   ❌ SEM dados simulados
   ❌ SEM conexões de backend
   🔌 Conecte o ZK4500 via USB antes de testar
""")
            
            # Verifica hardware na inicialização
            device = ZKAgentRealHandler.get_device()
            if device and device.initialized:
                print(f"✅ ZK4500 CONECTADO: {device.device_info}")
            elif HARDWARE_DISPONIVEL:
                print(f"⚠️ ZK4500 não detectado: {device.last_error if device else 'Erro de inicialização'}")
            else:
                print("❌ Bridge ZK4500 não disponível - verifique dependências")
            
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Servidor REAL iniciado na porta {self.port}")
            self.httpd.serve_forever()
            
        except KeyboardInterrupt:
            print(f"\n[{time.strftime('%Y-%m-%d %H:%M:%S')}] Servidor interrompido pelo usuário")
        except Exception as e:
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] ERRO: {e}")
        finally:
            if self.httpd:
                self.httpd.shutdown()
                print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Servidor finalizado")
            
            # Fecha dispositivo ao encerrar
            device = ZKAgentRealHandler.get_device()
            if device:
                device.close()

if __name__ == '__main__':
    print("🚀 Iniciando ZKAgent com DADOS REAIS do ZK4500...")
    
    # Inicia ZKAgent com hardware real
    server = ZKAgentRealServer(port=5001)
    server.start() 