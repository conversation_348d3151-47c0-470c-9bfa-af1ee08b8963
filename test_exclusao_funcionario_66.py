#!/usr/bin/env python3
import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import FuncionarioQueries
import traceback

print("=== TESTE DE EXCLUSÃO FUNCIONÁRIO ID 66 ===")

try:
    # 1. Verificar se o funcionário existe
    print("\n1. Verificando funcionário ID 66...")
    funcionario = FuncionarioQueries.get_by_id(66)
    
    if funcionario:
        print(f"✅ Funcionário encontrado: {funcionario['nome_completo']}")
        print(f"   Matrícula: {funcionario['matricula_empresa']}")
        print(f"   Ativo: {funcionario.get('ativo', 'N/A')}")
        print(f"   Status: {funcionario.get('status_cadastro', 'N/A')}")
    else:
        print("❌ Funcionário não encontrado")
        exit(1)
    
    # 2. Testar função de desligamento
    print("\n2. Testando função de desligamento...")
    
    resultado = FuncionarioQueries.desligar_funcionario(
        funcionario_id=66,
        motivo_desligamento='Demissao_sem_justa_causa',
        observacoes='Teste de exclusão via script de debug',
        usuario_responsavel=1
    )
    
    print(f"📊 Resultado: {resultado}")
    
    if resultado:
        print("✅ Desligamento executado com sucesso")
    else:
        print("❌ Falha no desligamento")
        
except Exception as e:
    print(f"❌ ERRO: {e}")
    print(f"📋 Traceback completo:")
    print(traceback.format_exc())
