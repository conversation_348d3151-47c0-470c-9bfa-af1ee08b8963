#!/usr/bin/env python3
"""
Teste para verificar se a correção do bug 'id' funcionou
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def testar_correcao():
    """Testar se a correção do campo 'id' funcionou"""
    try:
        from utils.database import DatabaseManager
        from app_funcionarios import get_jornada_padrao_empresa
        import logging
        
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        print("🔧 TESTANDO CORREÇÃO DO BUG 'ID'")
        print("=" * 50)
        
        # Buscar uma empresa para testar
        empresa = DatabaseManager.execute_query(
            "SELECT id, nome_fantasia FROM empresas LIMIT 1", 
            fetch_one=True
        )
        
        if not empresa:
            print("❌ Nenhuma empresa encontrada")
            return False
            
        empresa_id = empresa['id']
        print(f"📊 Testando com empresa: {empresa['nome_fantasia']} (ID: {empresa_id})")
        
        # Testar a função corrigida
        print("\n1. TESTANDO FUNÇÃO CORRIGIDA:")
        print("-" * 40)
        
        resultado = get_jornada_padrao_empresa(empresa_id)
        if resultado:
            print(f"✅ Resultado da função:")
            for key, value in resultado.items():
                print(f"   {key}: {value}")
                
            # Verificar se ainda há campo 'id'
            if 'id' in resultado:
                print(f"❌ FALHA: Campo 'id' ainda está presente!")
                return False
            else:
                print(f"✅ SUCESSO: Campo 'id' NÃO está presente!")
                
            # Verificar se o campo foi renomeado corretamente
            if 'horario_trabalho_id' in resultado:
                print(f"✅ SUCESSO: Campo renomeado para 'horario_trabalho_id': {resultado['horario_trabalho_id']}")
                return True
            else:
                print(f"⚠️ AVISO: Campo 'horario_trabalho_id' não encontrado")
                return True  # Ainda é sucesso se não há 'id'
        else:
            print("⚠️ Nenhuma jornada encontrada")
            return True  # Não é erro se não há jornada
        
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def simular_processamento_funcionario():
    """Simular o processamento de dados de funcionário"""
    try:
        from app_funcionarios import _processar_dados_funcionario
        
        print("\n2. SIMULANDO PROCESSAMENTO DE FUNCIONÁRIO:")
        print("-" * 50)
        
        # Dados simulados de um funcionário
        dados_teste = {
            'nome_completo': 'Teste Correção',
            'cpf': '12345678901',
            'empresa_id': '4',  # Usar empresa que sabemos que existe
            'cargo': 'Desenvolvedor',
            'data_admissao': '2025-01-01',
            'status_cadastro': 'ativo'
        }
        
        print(f"📋 Dados de entrada:")
        for key, value in dados_teste.items():
            print(f"   {key}: {value}")
        
        # Processar dados (sem salvar no banco)
        resultado = _processar_dados_funcionario(dados_teste)
        
        print(f"\n📋 Dados processados:")
        for key, value in resultado.items():
            print(f"   {key}: {value}")
            
        # Verificar se há campo 'id' problemático
        if 'id' in resultado:
            print(f"❌ FALHA: Campo 'id' encontrado nos dados processados!")
            return False
        else:
            print(f"✅ SUCESSO: Nenhum campo 'id' problemático encontrado!")
            return True
            
    except Exception as e:
        print(f"❌ Erro durante simulação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 VERIFICAÇÃO DA CORREÇÃO DO BUG 'ID'")
    print("=" * 60)
    
    sucesso1 = testar_correcao()
    sucesso2 = simular_processamento_funcionario()
    
    print("\n" + "=" * 60)
    if sucesso1 and sucesso2:
        print("🎉 CORREÇÃO FUNCIONOU!")
        print("✅ Campo 'id' não está mais causando conflito")
        print("✅ Função get_jornada_padrao_empresa corrigida")
        print("✅ Processamento de funcionário sem problemas")
        print("🚀 O bug foi resolvido!")
    else:
        print("❌ CORREÇÃO FALHOU!")
        print("🔍 Verifique os logs acima para detalhes")
    print("=" * 60)
