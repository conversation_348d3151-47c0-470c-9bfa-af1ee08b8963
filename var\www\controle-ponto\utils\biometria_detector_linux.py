"""
Módulo de Detecção de Dispositivos Biométricos - Versão Linux
Sistema: RLPONTO-WEB v1.0
Desenvolvido por: <PERSON> - AiNexus Tecnologia
Data: 10/06/2025

Versão adaptada para servidor Linux de produção
RETORNA DADOS REAIS - SEM SIMULAÇÃO FALSA
"""

import subprocess
import logging
from typing import Dict, List, Optional

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BiometricDeviceDetectorLinux:
    """
    Detector de dispositivos biométricos para servidor Linux
    DETECÇÃO REAL - SEM DADOS FALSOS
    """
    
    def __init__(self):
        # Sem dados pré-definidos - só detecta se realmente existir
        pass

    def detect_biometric_devices(self) -> List[Dict]:
        """
        Detecção REAL de dispositivos no Linux usando lsusb
        NÃO retorna dados falsos
        """
        logger.info("🔍 Detectando dispositivos biométricos REAIS no Linux...")
        
        devices = []
        
        try:
            # Executar lsusb para detectar dispositivos USB reais
            result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                usb_devices = result.stdout
                logger.info(f"Saída lsusb: {usb_devices}")
                
                # Procurar especificamente por ZKTeco (1b55) ou outros fabricantes de biometria
                biometric_vendors = {
                    '1b55': 'ZKTeco Inc.',
                    '27c6': 'Goodix Technology', 
                    '138a': 'Validity Sensors',
                    '04f3': 'Elan Microelectronics'
                }
                
                for line in usb_devices.split('\n'):
                    line_lower = line.lower()
                    for vid, manufacturer in biometric_vendors.items():
                        if vid in line_lower:
                            # Dispositivo biométrico REAL detectado
                            device_info = {
                                'friendly_name': f'Dispositivo Biométrico {manufacturer}',
                                'status': 'OK',
                                'class': 'USB',
                                'instance_id': f'USB_{vid.upper()}',
                                'detection_method': 'lsusb_real',
                                'vendor_id': vid.upper(),
                                'product_id': 'UNKNOWN',
                                'manufacturer': manufacturer,
                                'device_type': 'fingerprint',
                                'supported': vid == '1b55'  # Só ZKTeco é suportado
                            }
                            devices.append(device_info)
                            logger.info(f"✅ Dispositivo REAL encontrado: {manufacturer}")
                            
        except subprocess.TimeoutExpired:
            logger.error("❌ Timeout ao executar lsusb")
        except FileNotFoundError:
            logger.error("❌ Comando lsusb não encontrado")
        except Exception as e:
            logger.error(f"❌ Erro na detecção real: {e}")
        
        # Também verificar /dev/hidraw* para dispositivos HID
        try:
            import glob
            hidraw_devices = glob.glob('/dev/hidraw*')
            if hidraw_devices:
                logger.info(f"Dispositivos HID encontrados: {hidraw_devices}")
                # Aqui poderia fazer verificação mais específica se necessário
                
        except Exception as e:
            logger.warning(f"Erro ao verificar dispositivos HID: {e}")
        
        if devices:
            logger.info(f"✅ Detecção REAL concluída: {len(devices)} dispositivos encontrados")
        else:
            logger.info("❌ NENHUM dispositivo biométrico detectado no sistema")
            
        return devices

# Instância global do detector
biometric_detector = BiometricDeviceDetectorLinux()

def detect_biometric_devices():
    """
    Função principal de detecção REAL para Linux
    """
    return biometric_detector.detect_biometric_devices()

if __name__ == "__main__":
    # Teste do detector Linux REAL
    print("🔍 Iniciando detecção REAL de dispositivos biométricos...")
    devices = detect_biometric_devices()
    
    if devices:
        print(f"\n✅ Encontrados {len(devices)} dispositivos REAIS:")
        for device in devices:
            print(f"- {device['friendly_name']} ({device['status']})")
            if device['vendor_id'] and device['product_id']:
                print(f"  VID:PID = {device['vendor_id']}:{device['product_id']}")
            print(f"  Suportado: {'✅' if device['supported'] else '❌'}")
            print()
    else:
        print("\n❌ NENHUM dispositivo biométrico detectado no sistema.")
        print("✅ Esta é a resposta HONESTA - sem dados falsos!") 