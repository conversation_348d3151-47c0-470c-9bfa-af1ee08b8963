#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para Limpar Empresas - RLPONTO-WEB
----------------------------------------
Remove todas as empresas e dados relacionados diretamente.
"""

from utils.database import DatabaseManager

def limpar_empresas():
    """
    Remove todas as empresas e dados relacionados
    """
    print("=" * 60)
    print("🚫 LIMPEZA DE TODAS AS EMPRESAS - RLPONTO-WEB")
    print("=" * 60)
    
    try:
        # Desabilitar verificação de chaves estrangeiras
        DatabaseManager.execute_query("SET FOREIGN_KEY_CHECKS = 0")
        print("✅ Verificação de chaves estrangeiras desabilitada")
        
        # Remover registros de ponto
        DatabaseManager.execute_query("TRUNCATE TABLE registros_ponto")
        print("✅ Registros de ponto removidos")
        
        # Tentar remover EPIs se existir
        try:
            DatabaseManager.execute_query("TRUNCATE TABLE epis")
            print("✅ EPIs removidos")
        except:
            print("ℹ️ Tabela EPIs não existe ou está vazia")
        
        # Remover funcionários
        DatabaseManager.execute_query("TRUNCATE TABLE funcionarios")
        print("✅ Funcionários removidos")
        
        # Remover jornadas
        try:
            DatabaseManager.execute_query("TRUNCATE TABLE jornadas_trabalho")
            print("✅ Jornadas de trabalho removidas")
        except:
            print("ℹ️ Tabela jornadas_trabalho não existe ou está vazia")
        
        # Remover empresas
        DatabaseManager.execute_query("TRUNCATE TABLE empresas")
        print("✅ Empresas removidas")
        
        # Resetar auto_increment
        DatabaseManager.execute_query("ALTER TABLE empresas AUTO_INCREMENT = 1")
        print("✅ Auto_increment resetado para 1")
        
        # Habilitar verificação de chaves estrangeiras
        DatabaseManager.execute_query("SET FOREIGN_KEY_CHECKS = 1")
        print("✅ Verificação de chaves estrangeiras habilitada")
        
        # Verificar se todas foram removidas
        empresas = DatabaseManager.fetch_all("SELECT * FROM empresas")
        if not empresas:
            print("\n🎉 TODAS AS EMPRESAS FORAM REMOVIDAS COM SUCESSO!")
            print("🔄 O sistema está pronto para começar do zero")
        else:
            print(f"⚠️ Ainda existem {len(empresas)} empresas no banco")
        
        print("\n✅ Processo finalizado!")
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    limpar_empresas()