{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <!-- Header Section -->
    <div class="dashboard-header">
        <div class="header-content">
            <h1 class="dashboard-title">
                <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                </svg>
                {{ titulo }}
            </h1>
            <p class="dashboard-subtitle">Dashboard com métricas e análises de registros de ponto</p>
            <div class="period-badge">
                <svg class="period-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                    <line x1="16" y1="2" x2="16" y2="6"/>
                    <line x1="8" y1="2" x2="8" y2="6"/>
                    <line x1="3" y1="10" x2="21" y2="10"/>
                </svg>
                <span>{{ periodo }}</span>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card stat-card-indigo">
            <div class="stat-card-gradient"></div>
            <div class="stat-card-content">
                <div class="stat-info">
                    <p class="stat-label">Total de Registros</p>
                    <div class="stat-value-container">
                        <h3 class="stat-value">{{ "{:,}".format(stats.total_registros) }}</h3>
                    </div>
                </div>
                <div class="stat-icon stat-icon-indigo">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="stat-card stat-card-pink">
            <div class="stat-card-gradient"></div>
            <div class="stat-card-content">
                <div class="stat-info">
                    <p class="stat-label">Funcionários Ativos</p>
                    <div class="stat-value-container">
                        <h3 class="stat-value">{{ "{:,}".format(stats.funcionarios_ativos) }}</h3>
                    </div>
                </div>
                <div class="stat-icon stat-icon-pink">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                        <circle cx="9" cy="7" r="4"/>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="stat-card stat-card-cyan">
            <div class="stat-card-gradient"></div>
            <div class="stat-card-content">
                <div class="stat-info">
                    <p class="stat-label">Registros Biométricos</p>
                    <div class="stat-value-container">
                        <h3 class="stat-value">{{ "{:,}".format(stats.registros_biometricos) }}</h3>
                        <span class="stat-percentage">({{ stats.percentual_biometrico }}%)</span>
                    </div>
                </div>
                <div class="stat-icon stat-icon-cyan">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 10a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"/>
                        <path d="M8.21 13.89 7 23l5-3 5 3-1.21-9.12"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="stat-card stat-card-emerald">
            <div class="stat-card-gradient"></div>
            <div class="stat-card-content">
                <div class="stat-info">
                    <p class="stat-label">Registros Manuais</p>
                    <div class="stat-value-container">
                        <h3 class="stat-value">{{ "{:,}".format(stats.registros_manuais) }}</h3>
                    </div>
                </div>
                <div class="stat-icon stat-icon-emerald">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 11V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v0"/>
                        <path d="M14 10V4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v2"/>
                        <path d="M10 10.5V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v8"/>
                        <path d="M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
        <div class="tabs-container">
            <div class="tabs-list">
                <button class="tab-trigger active" data-tab="diarios">Registros Diários</button>
                <button class="tab-trigger" data-tab="metodos">Métodos de Registro</button>
                <button class="tab-trigger" data-tab="pontualidade">Análise de Pontualidade</button>
            </div>

            <div class="tab-content active" id="tab-diarios">
                <div class="chart-card">
                    <div class="chart-header">
                        <h4 class="chart-title">
                            <svg class="chart-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                            </svg>
                            Registros por Dia (Últimos 7 dias)
                        </h4>
                    </div>
                    <div class="chart-content">
                        <canvas id="chartRegistrosDiarios" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="tab-metodos">
                <div class="chart-card">
                    <div class="chart-header">
                        <h4 class="chart-title">
                            <svg class="chart-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 10a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"/>
                                <path d="M8.21 13.89 7 23l5-3 5 3-1.21-9.12"/>
                            </svg>
                            Distribuição por Método
                        </h4>
                    </div>
                    <div class="chart-content chart-content-center">
                        <canvas id="chartMetodos" width="300" height="300"></canvas>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="tab-pontualidade">
                <div class="chart-card">
                    <div class="chart-header">
                        <h4 class="chart-title">
                            <svg class="chart-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                            Análise de Pontualidade
                        </h4>
                    </div>
                    <div class="chart-content">
                        <canvas id="chartPontualidade" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-section">
        <div class="action-buttons">
            <a href="/relatorios/pontos" class="btn btn-primary">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                    <polyline points="14,2 14,8 20,8"/>
                    <line x1="16" y1="13" x2="8" y2="13"/>
                    <line x1="16" y1="17" x2="8" y2="17"/>
                    <polyline points="10,9 9,9 8,9"/>
                </svg>
                Ver Relatórios Detalhados
            </a>
            <a href="/registro-ponto/manual" class="btn btn-outline">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M18 11V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v0"/>
                    <path d="M14 10V4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v2"/>
                    <path d="M10 10.5V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v8"/>
                    <path d="M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15"/>
                </svg>
                Registrar Ponto Manual
            </a>
            <a href="/registro-ponto/biometrico" class="btn btn-outline">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M12 10a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"/>
                    <path d="M8.21 13.89 7 23l5-3 5 3-1.21-9.12"/>
                </svg>
                Registrar Ponto Biométrico
            </a>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Tab functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabTriggers = document.querySelectorAll('.tab-trigger');
    const tabContents = document.querySelectorAll('.tab-content');

    tabTriggers.forEach(trigger => {
        trigger.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active class from all triggers and contents
            tabTriggers.forEach(t => t.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked trigger and corresponding content
            this.classList.add('active');
            document.getElementById(`tab-${targetTab}`).classList.add('active');
        });
    });

    // Initialize charts
    initializeCharts();
});

function initializeCharts() {
    // Data from Flask template
    const labelsData = {{ graficos.labels_dias | tojson }};
    const registrosDiariosData = {{ graficos.dados_registros_diarios | tojson }};
    const pontualidadeData = {{ graficos.dados_pontualidade | tojson }};
    const biometricosData = {{ stats.registros_biometricos }};
    const manuaisData = {{ stats.registros_manuais }};

    // Chart colors
    const colors = {
        primary: 'rgb(99, 102, 241)',
        success: 'rgb(16, 185, 129)',
        warning: 'rgb(249, 115, 22)',
        danger: 'rgb(244, 63, 94)'
    };

    // Bar Chart - Registros Diários
    const ctxBar = document.getElementById('chartRegistrosDiarios').getContext('2d');
    new Chart(ctxBar, {
        type: 'bar',
        data: {
            labels: labelsData,
            datasets: [{
                label: 'Registros por Dia',
                data: registrosDiariosData,
                backgroundColor: colors.primary + '80',
                borderColor: colors.primary,
                borderWidth: 2,
                borderRadius: 4,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // Pie Chart - Métodos de Registro
    const ctxPie = document.getElementById('chartMetodos').getContext('2d');
    new Chart(ctxPie, {
        type: 'doughnut',
        data: {
            labels: ['Biométrico', 'Manual'],
            datasets: [{
                data: [biometricosData, manuaisData],
                backgroundColor: [colors.success + '80', colors.warning + '80'],
                borderColor: [colors.success, colors.warning],
                borderWidth: 2,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });

    // Line Chart - Pontualidade
    const ctxLine = document.getElementById('chartPontualidade').getContext('2d');
    new Chart(ctxLine, {
        type: 'line',
        data: {
            labels: labelsData,
            datasets: [{
                label: 'Atrasos',
                data: pontualidadeData,
                fill: true,
                backgroundColor: colors.danger + '20',
                borderColor: colors.danger,
                tension: 0.4,
                pointBackgroundColor: colors.danger,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}
</script>

<style>
/* Dashboard Styles */
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1.5rem;
    background: hsl(var(--background, 0 0% 100%));
    min-height: 100vh;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.dashboard-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.875rem;
    font-weight: 700;
    color: hsl(var(--foreground, 222.2 84% 4.9%));
    margin: 0;
}

.title-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: hsl(var(--primary, 221.2 83.2% 53.3%));
}

.dashboard-subtitle {
    color: hsl(var(--muted-foreground, 215.4 16.3% 46.9%));
    margin: 0;
}

.period-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: hsl(var(--muted, 210 40% 98%));
    border: 1px solid hsl(var(--border, 214.3 31.8% 91.4%));
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.period-icon {
    width: 1rem;
    height: 1rem;
    color: hsl(var(--muted-foreground, 215.4 16.3% 46.9%));
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    position: relative;
    background: hsl(var(--card, 0 0% 100%));
    border: 1px solid hsl(var(--border, 214.3 31.8% 91.4%));
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.stat-card-gradient {
    position: absolute;
    inset: 0;
    opacity: 0.1;
}

.stat-card-indigo .stat-card-gradient {
    background: linear-gradient(135deg, rgb(99, 102, 241), rgb(79, 70, 229));
}

.stat-card-pink .stat-card-gradient {
    background: linear-gradient(135deg, rgb(236, 72, 153), rgb(190, 24, 93));
}

.stat-card-cyan .stat-card-gradient {
    background: linear-gradient(135deg, rgb(6, 182, 212), rgb(59, 130, 246));
}

.stat-card-emerald .stat-card-gradient {
    background: linear-gradient(135deg, rgb(16, 185, 129), rgb(5, 150, 105));
}

.stat-card-content {
    position: relative;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.stat-info {
    flex: 1;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: hsl(var(--muted-foreground, 215.4 16.3% 46.9%));
    margin: 0 0 0.25rem 0;
}

.stat-value-container {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: hsl(var(--foreground, 222.2 84% 4.9%));
    margin: 0;
}

.stat-percentage {
    font-size: 0.75rem;
    font-weight: 500;
    color: hsl(var(--muted-foreground, 215.4 16.3% 46.9%));
}

.stat-icon {
    padding: 0.5rem;
    border-radius: 50%;
    color: white;
}

.stat-icon svg {
    width: 1.25rem;
    height: 1.25rem;
}

.stat-icon-indigo {
    background: linear-gradient(135deg, rgb(99, 102, 241), rgb(79, 70, 229));
}

.stat-icon-pink {
    background: linear-gradient(135deg, rgb(236, 72, 153), rgb(190, 24, 93));
}

.stat-icon-cyan {
    background: linear-gradient(135deg, rgb(6, 182, 212), rgb(59, 130, 246));
}

.stat-icon-emerald {
    background: linear-gradient(135deg, rgb(16, 185, 129), rgb(5, 150, 105));
}

/* Charts Section */
.charts-section {
    margin-bottom: 2rem;
}

.tabs-container {
    background: hsl(var(--card, 0 0% 100%));
    border: 1px solid hsl(var(--border, 214.3 31.8% 91.4%));
    border-radius: 0.75rem;
    overflow: hidden;
}

.tabs-list {
    display: flex;
    background: hsl(var(--muted, 210 40% 98%));
    border-bottom: 1px solid hsl(var(--border, 214.3 31.8% 91.4%));
}

.tab-trigger {
    flex: 1;
    padding: 1rem;
    background: none;
    border: none;
    font-size: 0.875rem;
    font-weight: 500;
    color: hsl(var(--muted-foreground, 215.4 16.3% 46.9%));
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
}

.tab-trigger:hover {
    background: hsl(var(--accent, 210 40% 96%));
}

.tab-trigger.active {
    color: hsl(var(--primary, 221.2 83.2% 53.3%));
    background: hsl(var(--background, 0 0% 100%));
    border-bottom-color: hsl(var(--primary, 221.2 83.2% 53.3%));
}

.tab-content {
    display: none;
    padding: 1.5rem;
}

.tab-content.active {
    display: block;
}

.chart-card {
    width: 100%;
}

.chart-header {
    margin-bottom: 1.5rem;
}

.chart-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: hsl(var(--foreground, 222.2 84% 4.9%));
    margin: 0;
}

.chart-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: hsl(var(--primary, 221.2 83.2% 53.3%));
}

.chart-content {
    height: 20rem;
    position: relative;
}

.chart-content-center {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 20rem;
}

.chart-content-center canvas {
    max-width: 18rem;
    max-height: 18rem;
}

/* Action Section */
.action-section {
    padding-top: 2rem;
    border-top: 1px solid hsl(var(--border, 214.3 31.8% 91.4%));
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.btn-icon {
    width: 1rem;
    height: 1rem;
}

.btn-primary {
    background: hsl(var(--primary, 221.2 83.2% 53.3%));
    color: hsl(var(--primary-foreground, 210 40% 98%));
}

.btn-primary:hover {
    background: hsl(var(--primary, 221.2 83.2% 53.3%) / 0.9);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: hsl(var(--foreground, 222.2 84% 4.9%));
    border-color: hsl(var(--border, 214.3 31.8% 91.4%));
}

.btn-outline:hover {
    background: hsl(var(--accent, 210 40% 96%));
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }
    
    .dashboard-title {
        font-size: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .tabs-list {
        flex-direction: column;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        justify-content: center;
    }
}

@media (max-width: 640px) {
    .stat-value {
        font-size: 1.5rem;
    }
    
    .chart-content {
        height: 16rem;
    }
}
</style>
{% endblock %} 