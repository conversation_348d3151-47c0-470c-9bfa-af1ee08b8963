#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Diagnóstico e Correção do Sistema de Usuários - RLPONTO-WEB
===========================================================

Este script diagnostica e corrige problemas no sistema de gerenciamento
de usuários, especificamente na alteração de níveis de acesso.

Data: 07/07/2025
Autor: <PERSON> - AiNexus Tecnologia
"""

import pymysql
import logging
from datetime import datetime

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configurações do banco
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_db_connection():
    """Estabelece conexão com o banco de dados"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"Erro ao conectar ao banco: {e}")
        return None

def diagnosticar_estrutura_usuarios():
    """Diagnostica a estrutura das tabelas de usuários"""
    print("🔍 DIAGNÓSTICO DA ESTRUTURA DE USUÁRIOS")
    print("=" * 60)
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cursor:
            # Verificar estrutura da tabela usuarios
            print("\n📋 Estrutura da tabela 'usuarios':")
            cursor.execute("DESCRIBE usuarios")
            usuarios_structure = cursor.fetchall()
            for field in usuarios_structure:
                print(f"   {field['Field']}: {field['Type']} - {field['Null']} - {field['Key']} - {field['Default']}")
            
            # Verificar estrutura da tabela permissoes
            print("\n📋 Estrutura da tabela 'permissoes':")
            cursor.execute("DESCRIBE permissoes")
            permissoes_structure = cursor.fetchall()
            for field in permissoes_structure:
                print(f"   {field['Field']}: {field['Type']} - {field['Null']} - {field['Key']} - {field['Default']}")
            
            # Verificar dados atuais
            print("\n👥 Usuários cadastrados:")
            cursor.execute("""
                SELECT u.id, u.usuario, u.nivel_acesso as nivel_tabela_usuarios, 
                       p.nivel_acesso as nivel_tabela_permissoes, u.ativo
                FROM usuarios u 
                LEFT JOIN permissoes p ON u.id = p.usuario_id 
                ORDER BY u.id
            """)
            usuarios = cursor.fetchall()
            
            for user in usuarios:
                status_permissao = "✅" if user['nivel_tabela_permissoes'] else "❌"
                print(f"   ID: {user['id']} | Usuário: {user['usuario']} | "
                      f"Nível (usuarios): {user['nivel_tabela_usuarios']} | "
                      f"Nível (permissoes): {user['nivel_tabela_permissoes']} {status_permissao} | "
                      f"Ativo: {user['ativo']}")
            
            return True
            
    except Exception as e:
        logger.error(f"Erro no diagnóstico: {e}")
        return False
    finally:
        conn.close()

def identificar_inconsistencias():
    """Identifica inconsistências entre as tabelas"""
    print("\n🔍 IDENTIFICANDO INCONSISTÊNCIAS")
    print("=" * 60)
    
    conn = get_db_connection()
    if not conn:
        return []
    
    inconsistencias = []
    
    try:
        with conn.cursor() as cursor:
            # Usuários sem registro na tabela permissoes
            cursor.execute("""
                SELECT u.id, u.usuario, u.nivel_acesso 
                FROM usuarios u 
                LEFT JOIN permissoes p ON u.id = p.usuario_id 
                WHERE p.usuario_id IS NULL
            """)
            usuarios_sem_permissao = cursor.fetchall()
            
            if usuarios_sem_permissao:
                print("\n❌ Usuários SEM registro na tabela permissões:")
                for user in usuarios_sem_permissao:
                    print(f"   ID: {user['id']} | Usuário: {user['usuario']} | Nível: {user['nivel_acesso']}")
                    inconsistencias.append({
                        'tipo': 'sem_permissao',
                        'usuario_id': user['id'],
                        'usuario': user['usuario'],
                        'nivel_usuarios': user['nivel_acesso']
                    })
            
            # Usuários com níveis diferentes entre tabelas
            cursor.execute("""
                SELECT u.id, u.usuario, u.nivel_acesso as nivel_usuarios, p.nivel_acesso as nivel_permissoes
                FROM usuarios u 
                JOIN permissoes p ON u.id = p.usuario_id 
                WHERE u.nivel_acesso != p.nivel_acesso
            """)
            usuarios_niveis_diferentes = cursor.fetchall()
            
            if usuarios_niveis_diferentes:
                print("\n⚠️ Usuários com níveis DIFERENTES entre tabelas:")
                for user in usuarios_niveis_diferentes:
                    print(f"   ID: {user['id']} | Usuário: {user['usuario']} | "
                          f"Nível (usuarios): {user['nivel_usuarios']} | "
                          f"Nível (permissoes): {user['nivel_permissoes']}")
                    inconsistencias.append({
                        'tipo': 'niveis_diferentes',
                        'usuario_id': user['id'],
                        'usuario': user['usuario'],
                        'nivel_usuarios': user['nivel_usuarios'],
                        'nivel_permissoes': user['nivel_permissoes']
                    })
            
            # Registros órfãos na tabela permissoes
            cursor.execute("""
                SELECT p.usuario_id, p.nivel_acesso 
                FROM permissoes p 
                LEFT JOIN usuarios u ON p.usuario_id = u.id 
                WHERE u.id IS NULL
            """)
            permissoes_orfas = cursor.fetchall()
            
            if permissoes_orfas:
                print("\n🗑️ Registros ÓRFÃOS na tabela permissões:")
                for perm in permissoes_orfas:
                    print(f"   Usuario_ID: {perm['usuario_id']} | Nível: {perm['nivel_acesso']}")
                    inconsistencias.append({
                        'tipo': 'permissao_orfa',
                        'usuario_id': perm['usuario_id'],
                        'nivel_permissoes': perm['nivel_acesso']
                    })
            
            if not inconsistencias:
                print("\n✅ Nenhuma inconsistência encontrada!")
            
            return inconsistencias
            
    except Exception as e:
        logger.error(f"Erro ao identificar inconsistências: {e}")
        return []
    finally:
        conn.close()

def corrigir_inconsistencias(inconsistencias):
    """Corrige as inconsistências identificadas"""
    if not inconsistencias:
        print("\n✅ Nenhuma correção necessária!")
        return True
    
    print(f"\n🔧 CORRIGINDO {len(inconsistencias)} INCONSISTÊNCIAS")
    print("=" * 60)
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cursor:
            for inc in inconsistencias:
                if inc['tipo'] == 'sem_permissao':
                    # Criar registro na tabela permissoes
                    print(f"🔧 Criando permissão para usuário {inc['usuario']} (ID: {inc['usuario_id']})")
                    cursor.execute("""
                        INSERT INTO permissoes (usuario_id, nivel_acesso, data_atribuicao) 
                        VALUES (%s, %s, NOW())
                    """, (inc['usuario_id'], inc['nivel_usuarios']))
                    
                elif inc['tipo'] == 'niveis_diferentes':
                    # Usar o nível da tabela permissoes como referência (mais específica)
                    print(f"🔧 Sincronizando níveis para usuário {inc['usuario']} (ID: {inc['usuario_id']})")
                    print(f"   Mantendo nível da tabela permissões: {inc['nivel_permissoes']}")
                    cursor.execute("""
                        UPDATE usuarios SET nivel_acesso = %s WHERE id = %s
                    """, (inc['nivel_permissoes'], inc['usuario_id']))
                    
                elif inc['tipo'] == 'permissao_orfa':
                    # Remover registro órfão
                    print(f"🗑️ Removendo permissão órfã para usuario_id: {inc['usuario_id']}")
                    cursor.execute("""
                        DELETE FROM permissoes WHERE usuario_id = %s
                    """, (inc['usuario_id'],))
            
            conn.commit()
            print("\n✅ Todas as correções foram aplicadas com sucesso!")
            return True
            
    except Exception as e:
        conn.rollback()
        logger.error(f"Erro ao corrigir inconsistências: {e}")
        return False
    finally:
        conn.close()

def testar_alteracao_nivel():
    """Testa a funcionalidade de alteração de nível"""
    print("\n🧪 TESTANDO ALTERAÇÃO DE NÍVEL DE ACESSO")
    print("=" * 60)
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cursor:
            # Buscar um usuário de teste (não admin)
            cursor.execute("""
                SELECT u.id, u.usuario, p.nivel_acesso 
                FROM usuarios u 
                JOIN permissoes p ON u.id = p.usuario_id 
                WHERE u.usuario NOT IN ('admin', 'cavalcrod', 'status') 
                AND p.nivel_acesso = 'usuario'
                LIMIT 1
            """)
            usuario_teste = cursor.fetchone()
            
            if not usuario_teste:
                print("❌ Não foi possível encontrar usuário para teste")
                return False
            
            print(f"📋 Testando com usuário: {usuario_teste['usuario']} (ID: {usuario_teste['id']})")
            print(f"📋 Nível atual: {usuario_teste['nivel_acesso']}")
            
            # Simular alteração para admin
            print("🔄 Simulando alteração para 'admin'...")
            cursor.execute("""
                UPDATE permissoes SET nivel_acesso = %s WHERE usuario_id = %s
            """, ('admin', usuario_teste['id']))
            
            # Verificar se foi aplicado
            cursor.execute("SELECT nivel_acesso FROM permissoes WHERE usuario_id = %s", (usuario_teste['id'],))
            novo_nivel = cursor.fetchone()
            
            if novo_nivel['nivel_acesso'] == 'admin':
                print("✅ Alteração para 'admin' funcionou!")
                
                # Reverter para usuário
                print("🔄 Revertendo para 'usuario'...")
                cursor.execute("""
                    UPDATE permissoes SET nivel_acesso = %s WHERE usuario_id = %s
                """, ('usuario', usuario_teste['id']))
                
                cursor.execute("SELECT nivel_acesso FROM permissoes WHERE usuario_id = %s", (usuario_teste['id'],))
                nivel_revertido = cursor.fetchone()
                
                if nivel_revertido['nivel_acesso'] == 'usuario':
                    print("✅ Reversão para 'usuario' funcionou!")
                    conn.commit()
                    print("\n🎉 TESTE DE ALTERAÇÃO DE NÍVEL: SUCESSO!")
                    return True
                else:
                    print("❌ Falha na reversão!")
                    conn.rollback()
                    return False
            else:
                print("❌ Falha na alteração para admin!")
                conn.rollback()
                return False
                
    except Exception as e:
        conn.rollback()
        logger.error(f"Erro no teste: {e}")
        return False
    finally:
        conn.close()

def main():
    """Função principal"""
    print("🚀 DIAGNÓSTICO E CORREÇÃO DO SISTEMA DE USUÁRIOS")
    print("=" * 70)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("Sistema: RLPONTO-WEB v1.0")
    print("=" * 70)
    
    # 1. Diagnosticar estrutura
    if not diagnosticar_estrutura_usuarios():
        print("❌ Falha no diagnóstico da estrutura")
        return
    
    # 2. Identificar inconsistências
    inconsistencias = identificar_inconsistencias()
    
    # 3. Corrigir inconsistências
    if not corrigir_inconsistencias(inconsistencias):
        print("❌ Falha na correção das inconsistências")
        return
    
    # 4. Testar funcionalidade
    if not testar_alteracao_nivel():
        print("❌ Falha no teste de alteração de nível")
        return
    
    print("\n🎉 DIAGNÓSTICO E CORREÇÃO CONCLUÍDOS COM SUCESSO!")
    print("✅ Sistema de gerenciamento de usuários está funcionando corretamente")

if __name__ == "__main__":
    main()
