# ========================================
# DETECTOR BIOMÉTRICO WINDOWS REAL - RLPONTO-WEB
# Data: 11/01/2025
# Descrição: Detector robusto usando múltiplas abordagens
# Baseado em Context7 Python Windows API practices
# ========================================

import subprocess
import json
import logging
import platform
import winreg
import os
from typing import List, Dict, Any

# Configurar logger
logger = logging.getLogger(__name__)

class WindowsBiometricDetector:
    """
    Detector biométrico para Windows usando múltiplas abordagens.
    Baseado nas melhores práticas do Context7 para Windows API.
    """
    
    def __init__(self):
        self.known_biometric_vendors = {
            '1B55': 'ZKTeco Inc.',
            '045E': 'Microsoft Corp.',
            '138A': 'Validity Sensors',
            '06CB': 'Synaptics',
            '27C6': 'Goodix Technology',
            '04F3': 'Elan Microelectronics'
        }
        
        self.known_biometric_devices = {
            '1B55:0840': 'ZK4500 Fingerprint Reader',
            '045E:00BB': 'Microsoft Fingerprint Reader',
            '138A:0017': 'Validity VFS495 Fingerprint Reader',
            '06CB:009A': 'Synaptics Fingerprint Reader'
        }
    
    def detect_via_powershell_pnp(self) -> List[Dict]:
        """
        Método 1: Usar Get-PnpDevice via PowerShell
        """
        devices = []
        try:
            logger.info("🔍 Método 1: Detectando via PowerShell Get-PnpDevice...")
            
            # Comando PowerShell mais robusto
            cmd = [
                'powershell.exe', '-ExecutionPolicy', 'Bypass', '-Command',
                'Get-PnpDevice | Where-Object {$_.Class -eq "Biometric" -or $_.FriendlyName -match "fingerprint|biometric|ZK4500"} | ConvertTo-Json'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                try:
                    pnp_data = json.loads(result.stdout)
                    if isinstance(pnp_data, dict):
                        pnp_data = [pnp_data]  # Converter single object para lista
                    
                    for device in pnp_data:
                        if isinstance(device, dict):
                            device_info = {
                                'friendly_name': device.get('FriendlyName', 'Unknown Device'),
                                'instance_id': device.get('InstanceId', ''),
                                'status': device.get('Status', 'Unknown'),
                                'class': device.get('Class', ''),
                                'manufacturer': device.get('Manufacturer', ''),
                                'device_type': 'Biometric',
                                'vendor_id': self._extract_vid_from_instance(device.get('InstanceId', '')),
                                'product_id': self._extract_pid_from_instance(device.get('InstanceId', '')),
                                'supported': True,
                                'detection_method': 'PowerShell PnP'
                            }
                            devices.append(device_info)
                            logger.info(f"✅ Dispositivo encontrado via PnP: {device_info['friendly_name']}")
                    
                except json.JSONDecodeError as e:
                    logger.warning(f"Erro ao parsear JSON do PowerShell: {e}")
            else:
                logger.info("Nenhum dispositivo biométrico encontrado via PowerShell PnP")
                
        except Exception as e:
            logger.error(f"Erro no método PowerShell PnP: {e}")
        
        return devices
    
    def detect_via_wmi(self) -> List[Dict]:
        """
        Método 2: Usar WMI para detecção
        """
        devices = []
        try:
            logger.info("🔍 Método 2: Detectando via WMI...")
            
            cmd = [
                'powershell.exe', '-ExecutionPolicy', 'Bypass', '-Command',
                'Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.DeviceID -match "VID_1B55|fingerprint|biometric"} | ConvertTo-Json'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                try:
                    wmi_data = json.loads(result.stdout)
                    if isinstance(wmi_data, dict):
                        wmi_data = [wmi_data]
                    
                    for device in wmi_data:
                        if isinstance(device, dict):
                            device_info = {
                                'friendly_name': device.get('Name', 'Unknown Device'),
                                'instance_id': device.get('DeviceID', ''),
                                'status': 'OK' if device.get('Status') == 'OK' else 'Error',
                                'class': device.get('PNPClass', ''),
                                'manufacturer': device.get('Manufacturer', ''),
                                'device_type': 'Biometric',
                                'vendor_id': self._extract_vid_from_instance(device.get('DeviceID', '')),
                                'product_id': self._extract_pid_from_instance(device.get('DeviceID', '')),
                                'supported': True,
                                'detection_method': 'WMI'
                            }
                            devices.append(device_info)
                            logger.info(f"✅ Dispositivo encontrado via WMI: {device_info['friendly_name']}")
                    
                except json.JSONDecodeError as e:
                    logger.warning(f"Erro ao parsear JSON do WMI: {e}")
            else:
                logger.info("Nenhum dispositivo encontrado via WMI")
                
        except Exception as e:
            logger.error(f"Erro no método WMI: {e}")
        
        return devices
    
    def detect_via_registry(self) -> List[Dict]:
        """
        Método 3: Verificar registro do Windows
        """
        devices = []
        try:
            logger.info("🔍 Método 3: Detectando via Registry...")
            
            # Verificar chaves do registro relacionadas a dispositivos biométricos
            registry_paths = [
                r"SYSTEM\CurrentControlSet\Enum\USB",
                r"SYSTEM\CurrentControlSet\Services\WinBio\Parameters\SystemSensorPool"
            ]
            
            for reg_path in registry_paths:
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path)
                    
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            if 'VID_1B55' in subkey_name or 'biometric' in subkey_name.lower():
                                device_info = {
                                    'friendly_name': f'Registry Biometric Device ({subkey_name})',
                                    'instance_id': subkey_name,
                                    'status': 'OK',
                                    'class': 'Biometric',
                                    'manufacturer': 'Unknown',
                                    'device_type': 'Biometric',
                                    'vendor_id': self._extract_vid_from_instance(subkey_name),
                                    'product_id': self._extract_pid_from_instance(subkey_name),
                                    'supported': True,
                                    'detection_method': 'Registry'
                                }
                                devices.append(device_info)
                                logger.info(f"✅ Dispositivo encontrado via Registry: {subkey_name}")
                            i += 1
                        except OSError:
                            break
                    
                    winreg.CloseKey(key)
                    
                except OSError as e:
                    logger.debug(f"Não foi possível acessar {reg_path}: {e}")
                    
        except Exception as e:
            logger.error(f"Erro no método Registry: {e}")
        
        return devices
    
    def detect_via_devicemanager(self) -> List[Dict]:
        """
        Método 4: Usando DevCon ou PnPUtil
        """
        devices = []
        try:
            logger.info("🔍 Método 4: Detectando via Device Manager commands...")
            
            # Tentar usando pnputil
            cmd = [
                'pnputil.exe', '/enum-devices', '/class', 'Biometric'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout:
                lines = result.stdout.split('\n')
                current_device = {}
                
                for line in lines:
                    line = line.strip()
                    if 'Instance ID:' in line:
                        current_device['instance_id'] = line.split(':', 1)[1].strip()
                    elif 'Device Description:' in line:
                        current_device['friendly_name'] = line.split(':', 1)[1].strip()
                    elif 'Class Name:' in line and line.split(':', 1)[1].strip() == 'Biometric':
                        current_device['class'] = 'Biometric'
                    elif line == '' and current_device:
                        if 'instance_id' in current_device:
                            device_info = {
                                'friendly_name': current_device.get('friendly_name', 'Unknown Biometric Device'),
                                'instance_id': current_device.get('instance_id', ''),
                                'status': 'OK',
                                'class': 'Biometric',
                                'manufacturer': 'Unknown',
                                'device_type': 'Biometric',
                                'vendor_id': self._extract_vid_from_instance(current_device.get('instance_id', '')),
                                'product_id': self._extract_pid_from_instance(current_device.get('instance_id', '')),
                                'supported': True,
                                'detection_method': 'PnPUtil'
                            }
                            devices.append(device_info)
                            logger.info(f"✅ Dispositivo encontrado via PnPUtil: {device_info['friendly_name']}")
                        current_device = {}
                        
        except Exception as e:
            logger.error(f"Erro no método Device Manager: {e}")
        
        return devices
    
    def _extract_vid_from_instance(self, instance_id: str) -> str:
        """Extrair Vendor ID do Instance ID"""
        if 'VID_' in instance_id:
            try:
                start = instance_id.find('VID_') + 4
                return instance_id[start:start+4].upper()
            except:
                pass
        return ''
    
    def _extract_pid_from_instance(self, instance_id: str) -> str:
        """Extrair Product ID do Instance ID"""
        if 'PID_' in instance_id:
            try:
                start = instance_id.find('PID_') + 4
                return instance_id[start:start+4].upper()
            except:
                pass
        return ''
    
    def detect_biometric_devices(self) -> List[Dict]:
        """
        Método principal que combina todas as abordagens
        """
        logger.info("🚀 Iniciando detecção biométrica robusta com múltiplos métodos...")
        
        all_devices = []
        seen_instances = set()
        
        # Método 1: PowerShell PnP
        devices_pnp = self.detect_via_powershell_pnp()
        for device in devices_pnp:
            if device['instance_id'] not in seen_instances:
                all_devices.append(device)
                seen_instances.add(device['instance_id'])
        
        # Método 2: WMI
        devices_wmi = self.detect_via_wmi()
        for device in devices_wmi:
            if device['instance_id'] not in seen_instances:
                all_devices.append(device)
                seen_instances.add(device['instance_id'])
        
        # Método 3: Registry
        devices_registry = self.detect_via_registry()
        for device in devices_registry:
            if device['instance_id'] not in seen_instances:
                all_devices.append(device)
                seen_instances.add(device['instance_id'])
        
        # Método 4: Device Manager
        devices_dm = self.detect_via_devicemanager()
        for device in devices_dm:
            if device['instance_id'] not in seen_instances:
                all_devices.append(device)
                seen_instances.add(device['instance_id'])
        
        # Enriquecer dados dos dispositivos
        for device in all_devices:
            vid_pid = f"{device['vendor_id']}:{device['product_id']}"
            if vid_pid in self.known_biometric_devices:
                device['friendly_name'] = self.known_biometric_devices[vid_pid]
            if device['vendor_id'] in self.known_biometric_vendors:
                device['manufacturer'] = self.known_biometric_vendors[device['vendor_id']]
        
        logger.info(f"✅ Detecção concluída: {len(all_devices)} dispositivos únicos encontrados")
        
        return all_devices

# Instância global
detector = WindowsBiometricDetector()

def detect_biometric_devices() -> List[Dict]:
    """
    Função principal para detecção de dispositivos biométricos
    """
    if platform.system() != 'Windows':
        logger.warning("⚠️ Detector Windows chamado em sistema não-Windows")
        return []
    
    return detector.detect_biometric_devices()

# Para teste direto
if __name__ == "__main__":
    print("🧪 Testando detector biométrico Windows...")
    devices = detect_biometric_devices()
    
    if devices:
        print(f"✅ {len(devices)} dispositivo(s) encontrado(s):")
        for i, device in enumerate(devices, 1):
            print(f"\n--- Dispositivo {i} ---")
            for key, value in device.items():
                print(f"{key}: {value}")
    else:
        print("❌ Nenhum dispositivo biométrico detectado") 