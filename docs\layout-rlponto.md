# 🎨 Layout Padrão RLPONTO-WEB - Documentação Completa

**Data de Criação:** 14/07/2025  
**Versão:** 1.0  
**Sistema:** RLPONTO-WEB v1.0  
**Página Analisada:** `/empresa-principal/funcionarios`  
**Objetivo:** Documentar todos os elementos visuais, cores, fontes, layout e animações utilizados no sistema

---

## 📋 Índice

1. [Variáveis CSS Principais](#variáveis-css-principais)
2. [Tipografia](#tipografia)
3. [Layout e Estrutura](#layout-e-estrutura)
4. [Header Moderno](#header-moderno)
5. [Cards e Componentes](#cards-e-componentes)
6. [Botõ<PERSON>](#botões)
7. [Cores e Estados](#cores-e-estados)
8. [Animações](#animações)
9. [Responsividade](#responsividade)
10. [Sidebar](#sidebar)

---

## 🎨 Variáveis CSS Principais

### Paleta de Cores Oficial
```css
:root {
    /* ===== COR PRIMÁRIA ===== */
    --primary-color: #4fbdba;          /* Verde-azulado principal */
    --primary-hover: #26a69a;          /* Hover da cor primária */
    --primary-light: #80cbc4;          /* Versão clara */
    --primary-dark: #00695c;           /* Versão escura */

    /* ===== BACKGROUNDS ===== */
    --background-color: #f9fafb;       /* Background principal */
    --card-background: #ffffff;        /* Fundo de cards */
    --hover-bg: #f3f4f6;               /* Estados de hover */
    --sidebar-bg: #ffffff;             /* Background da sidebar */

    /* ===== TEXTOS ===== */
    --text-primary: #1f2937;           /* Texto principal (preto) */
    --text-secondary: #6b7280;         /* Texto secundário (cinza médio) */
    --text-muted: #9ca3af;             /* Texto desabilitado */
    --text-white: #ffffff;             /* Texto branco */

    /* ===== BORDAS ===== */
    --border-color: #e5e7eb;           /* Bordas padrão */
    --border-light: #f3f4f6;           /* Bordas claras */

    /* ===== ESTADOS ===== */
    --success-color: #10b981;          /* Verde de sucesso */
    --success-bg: #dcfce7;             /* Background de sucesso */
    --success-text: #166534;           /* Texto de sucesso */
    
    --warning-color: #f59e0b;          /* Amarelo de aviso */
    --warning-bg: #fef3c7;             /* Background de aviso */
    --warning-text: #92400e;           /* Texto de aviso */
    
    --danger-color: #ef4444;           /* Vermelho de erro */
    --danger-bg: #fee2e2;              /* Background de erro */
    --danger-text: #dc2626;            /* Texto de erro */
    
    --info-color: #3b82f6;             /* Azul informativo */
    --info-bg: #dbeafe;                /* Background informativo */
    --info-text: #1e40af;              /* Texto informativo */

    /* ===== SOMBRAS ===== */
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* ===== TRANSIÇÕES ===== */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;
}
```

---

## 📝 Tipografia

### Fonte Principal
```css
font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
```

### Hierarquia de Títulos
```css
/* Título Principal (H1) */
h1 {
    font-size: 2rem;           /* 32px */
    font-weight: 700;          /* Bold */
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.025em;
}

/* Subtítulo (H2) */
h2 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 24px;
    letter-spacing: -0.025em;
}

/* Seção (H3) */
h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 16px;
}

/* Texto Corpo */
body {
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-primary);
}

/* Texto Secundário */
.text-secondary {
    font-size: 1rem;
    opacity: 0.9;
    color: var(--text-secondary);
}
```

---

## 🏗️ Layout e Estrutura

### Container Principal
```css
.main-container {
    background-color: var(--background-color);
    min-height: 100vh;
    padding: 2rem;
}
```

### Grid System
```css
/* Grid Responsivo */
.employees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

/* Grid de Estatísticas */
.stats-container {
    display: flex;
    gap: 1rem;
    align-items: center;
}
```

---

## 🎯 Header Moderno

### Estrutura do Header
```css
.modern-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: var(--card-shadow);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Título do Header */
.modern-header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Subtítulo do Header */
.modern-header p {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 1rem;
}
```

### Cards de Estatísticas
```css
.stat-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    min-width: 100px;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: white;
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}
```

---

## 🃏 Cards e Componentes

### Card de Funcionário
```css
.employee-card {
    background: var(--card-background);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.employee-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
}

/* Header do Card */
.employee-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

/* Avatar do Funcionário */
.employee-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.125rem;
}

/* Informações do Funcionário */
.employee-info h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.employee-info p {
    margin: 0.25rem 0 0 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
}
```

### Seção de Relatórios
```css
.reports-section {
    background: var(--card-background);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
}

.reports-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.reports-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}
```

---

## 🔘 Botões

### Botão Primário
```css
.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
```

### Botões de Relatório
```css
.report-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    width: 100%;
    justify-content: flex-start;
}

/* Relatório Geral - Azul */
.report-btn.geral {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    color: white;
}

/* Frequência Geral - Verde */
.report-btn.frequencia {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

/* Jornadas de Trabalho - Roxo */
.report-btn.jornadas {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

/* Exportar Lista - Laranja */
.report-btn.exportar {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.report-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}
```

### Botões de Ação
```css
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

/* Botão Ver */
.btn-info {
    background-color: var(--info-color);
    color: white;
}

/* Botão Editar */
.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

/* Botão Excluir */
.btn-danger {
    background-color: var(--danger-color);
    color: white;
}
```

---

## 🎨 Cores e Estados

### Status de Funcionários
```css
/* Status Ativo */
.status-ativo {
    background-color: var(--success-bg);
    color: var(--success-text);
    border: 1px solid var(--success-color);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

/* Status Inativo */
.status-inativo {
    background-color: var(--danger-bg);
    color: var(--danger-text);
    border: 1px solid var(--danger-color);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}
```

### Gradientes Utilizados
```css
/* Gradiente Principal (Header) */
.gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Gradiente de Sucesso */
.gradient-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Gradiente de Aviso */
.gradient-warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* Gradiente de Erro */
.gradient-danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}
```

---

## ✨ Animações

### Animação de Entrada dos Cards
```css
/* Animação aplicada via JavaScript */
.employee-card {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.employee-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}
```

### JavaScript para Animações
```javascript
// Animações de entrada
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.employee-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
```

### Efeitos de Hover
```css
/* Hover em Cards */
.employee-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
}

/* Hover em Botões */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Hover em Botões de Relatório */
.report-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}
```

---

## 📱 Responsividade

### Breakpoints
```css
/* Mobile First */
@media (max-width: 768px) {
    .main-container {
        padding: 1rem;
    }
    
    .employees-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .modern-header {
        padding: 1.5rem;
    }
    
    .modern-header h1 {
        font-size: 1.5rem;
    }
    
    .stats-container {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .reports-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
        width: 100%;
    }
    
    .btn-sm {
        width: 100%;
        justify-content: center;
    }
}

/* Tablet */
@media (min-width: 769px) and (max-width: 1024px) {
    .employees-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .reports-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Desktop */
@media (min-width: 1025px) {
    .employees-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
    
    .reports-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}
```

---

## 🔧 Sidebar

### Estrutura da Sidebar
```css
:root {
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --sidebar-bg: #ffffff;
    --sidebar-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --sidebar-border: #e5e7eb;
}

.sidebar {
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--sidebar-border);
    box-shadow: var(--sidebar-shadow);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: var(--transition);
}

/* Logo da Sidebar */
.sidebar-logo {
    padding: 1.5rem;
    border-bottom: 1px solid var(--sidebar-border);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background-color: var(--primary-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

/* Menu da Sidebar */
.sidebar-menu {
    padding: 1rem 0;
}

.menu-section {
    margin-bottom: 2rem;
}

.menu-section-title {
    padding: 0 1.5rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.menu-item:hover {
    background-color: var(--hover-bg);
    color: var(--text-primary);
}

.menu-item.active {
    background-color: var(--active-bg);
    color: var(--active-text);
    border-left-color: var(--primary-color);
    font-weight: 500;
}

.menu-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}
```

---

## 📋 Resumo dos Elementos Principais

### Componentes Identificados:
1. **Header com Gradiente** - Roxo/azul com estatísticas
2. **Cards de Funcionários** - Grid responsivo com hover
3. **Seção de Relatórios** - 4 botões coloridos
4. **Sidebar Moderna** - Branca com ícones
5. **Botões de Ação** - Pequenos e coloridos
6. **Animações de Entrada** - Fade in com delay
7. **Sistema de Cores** - Baseado em variáveis CSS
8. **Tipografia** - Inter font com hierarquia clara

### Paleta de Cores Principal:
- **Primária:** `#4fbdba` (Verde-azulado)
- **Hover:** `#26a69a`
- **Background:** `#f9fafb`
- **Cards:** `#ffffff`
- **Texto:** `#1f2937`

### Características Visuais:
- **Border Radius:** 8px-16px
- **Sombras:** Suaves com blur
- **Transições:** 0.3s cubic-bezier
- **Grid:** Auto-fill responsivo
- **Fonte:** Inter + fallbacks

---

**📅 Documento criado em:** 14/07/2025  
**🎯 Sistema:** RLPONTO-WEB v1.0  
**👨‍💻 Baseado na página:** `/empresa-principal/funcionarios`  
**🎨 Inspiração:** MCP @21st-dev/magic + Shadcn UI
