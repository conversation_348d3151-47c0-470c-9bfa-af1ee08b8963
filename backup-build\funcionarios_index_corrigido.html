{% extends "base.html" %}

{% block title %}Lista de Funcionários - Controle de Ponto{% endblock %}

{% block content %}
<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
    <h2>Lista de Funcionários</h2>
    <a href="/funcionarios/cadastrar" class="btn-sm" style="background: #28a745; color: white; padding: 10px 20px; font-size: 14px;">
        + Novo Funcionário
    </a>
</div>

<!-- Filtros de busca -->
<div class="filters">
    <form method="GET" action="/funcionarios">
        <div class="filters-row">
            <div class="filter-group">
                <label for="search">Buscar:</label>
                <input type="text" id="search" name="search" value="{{ search }}" placeholder="Nome, CPF ou matrícula">
            </div>
            <div class="filter-group">
                <label for="status">Status:</label>
                <select id="status" name="status">
                    <option value="">Todos</option>
                    <option value="Ativo" {% if status == 'Ativo' %}selected{% endif %}>Ativo</option>
                    <option value="Inativo" {% if status == 'Inativo' %}selected{% endif %}>Inativo</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="per_page">Por página:</label>
                <select id="per_page" name="per_page">
                    <option value="10" {% if pagination.per_page == 10 %}selected{% endif %}>10</option>
                    <option value="25" {% if pagination.per_page == 25 %}selected{% endif %}>25</option>
                    <option value="50" {% if pagination.per_page == 50 %}selected{% endif %}>50</option>
                </select>
            </div>
            <div class="filter-group">
                <label>&nbsp;</label>
                <button type="submit">Filtrar</button>
            </div>
        </div>
        {% if search or status %}
        <div style="margin-top: 10px;">
            <a href="/funcionarios" style="color: #6c757d; font-size: 14px;">Limpar filtros</a>
        </div>
        {% endif %}
    </form>
</div>

<!-- Informações de resultado -->
<div style="margin-bottom: 15px; color: #6c757d; font-size: 14px;">
    Exibindo {{ funcionarios|length }} de {{ pagination.total }} funcionários
    {% if search or status %}
        (filtrado)
    {% endif %}
</div>

<!-- Tabela de funcionários -->
{% if funcionarios %}
<div class="table-responsive">
    <table class="data-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nome</th>
                <th>CPF</th>
                <th>Matrícula</th>
                <th>Cargo</th>
                <th>Setor</th>
                <th>Status</th>
                <th>Admissão</th>
                <th style="width: 200px;">Ações</th>
            </tr>
        </thead>
        <tbody>
            {% for funcionario in funcionarios %}
            <tr>
                <td>{{ funcionario.id }}</td>
                <td>
                    <strong>{{ funcionario.nome_completo }}</strong>
                </td>
                <td>{{ funcionario.cpf | format_cpf }}</td>
                <td>{{ funcionario.matricula_empresa }}</td>
                <td>{{ funcionario.cargo }}</td>
                <td>{{ funcionario.setor_obra }}</td>
                <td>
                    {% if funcionario.status_cadastro == 'Ativo' %}
                        <span class="badge badge-success">{{ funcionario.status_cadastro }}</span>
                    {% elif funcionario.status_cadastro == 'Inativo' %}
                        <span class="badge badge-danger">{{ funcionario.status_cadastro }}</span>
                    {% else %}
                        <span class="badge badge-secondary">{{ funcionario.status_cadastro }}</span>
                    {% endif %}
                </td>
                <td>{{ funcionario.data_admissao | format_date }}</td>
                <td>
                    <div class="action-buttons">
                        <!-- Botão Visualizar - disponível para todos -->
                        <a href="/funcionarios/{{ funcionario.id }}" class="btn-sm btn-view" title="Visualizar detalhes">
                            Ver
                        </a>
                        
                        {% if current_user.is_admin %}
                        <!-- Botão Editar - apenas admin -->
                        <a href="/funcionarios/{{ funcionario.id }}/editar" class="btn-sm btn-edit" title="Editar funcionário">
                            Editar
                        </a>
                        
                        <!-- Botão Apagar - apenas admin -->
                        <button type="button" 
                                class="btn-sm btn-delete" 
                                title="Excluir funcionário" 
                                data-id="{{ funcionario.id }}" 
                                data-nome="{{ funcionario.nome_completo | escape }}">
                            Excluir
                        </button>
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Paginação -->
{% if pagination.total_pages > 1 %}
<div class="pagination">
    <!-- Primeira página -->
    {% if pagination.has_prev %}
        <a href="?page=1{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">&laquo; Primeira</a>
    {% else %}
        <span class="disabled">&laquo; Primeira</span>
    {% endif %}
    
    <!-- Página anterior -->
    {% if pagination.has_prev %}
        <a href="?page={{ pagination.prev_num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">&lt; Anterior</a>
    {% else %}
        <span class="disabled">&lt; Anterior</span>
    {% endif %}
    
    <!-- Páginas numeradas -->
    {% set start_page = pagination.page - 2 if pagination.page > 2 else 1 %}
    {% set end_page = pagination.page + 2 if pagination.page + 2 < pagination.total_pages else pagination.total_pages %}
    
    {% if start_page > 1 %}
        <a href="?page=1{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">1</a>
        {% if start_page > 2 %}
            <span>...</span>
        {% endif %}
    {% endif %}
    
    {% for page_num in range(start_page, end_page + 1) %}
        {% if page_num == pagination.page %}
            <span class="current">{{ page_num }}</span>
        {% else %}
            <a href="?page={{ page_num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">{{ page_num }}</a>
        {% endif %}
    {% endfor %}
    
    {% if end_page < pagination.total_pages %}
        {% if end_page < pagination.total_pages - 1 %}
            <span>...</span>
        {% endif %}
        <a href="?page={{ pagination.total_pages }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">{{ pagination.total_pages }}</a>
    {% endif %}
    
    <!-- Próxima página -->
    {% if pagination.has_next %}
        <a href="?page={{ pagination.next_num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">Próxima &gt;</a>
    {% else %}
        <span class="disabled">Próxima &gt;</span>
    {% endif %}
    
    <!-- Última página -->
    {% if pagination.has_next %}
        <a href="?page={{ pagination.total_pages }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if pagination.per_page != 10 %}&per_page={{ pagination.per_page }}{% endif %}">Última &raquo;</a>
    {% else %}
        <span class="disabled">Última &raquo;</span>
    {% endif %}
</div>

<div style="text-align: center; margin-top: 10px; color: #6c757d; font-size: 14px;">
    Página {{ pagination.page }} de {{ pagination.total_pages }}
</div>
{% endif %}

{% else %}
<!-- Estado vazio -->
<div style="text-align: center; padding: 40px; color: #6c757d;">
    <h3>Nenhum funcionário encontrado</h3>
    {% if search or status %}
        <p>Tente ajustar os filtros de busca.</p>
        <a href="/funcionarios" style="color: #4fbdba;">Limpar filtros</a>
    {% else %}
        <p>Comece cadastrando o primeiro funcionário.</p>
        <a href="/funcionarios/cadastrar" class="btn-sm" style="background: #28a745; color: white; margin-top: 10px;">
            Cadastrar Primeiro Funcionário
        </a>
    {% endif %}
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
/* Correção específica para botões de ação */
.action-buttons {
    display: flex;
    gap: 5px;
    align-items: center;
    flex-wrap: wrap;
}

.action-buttons .btn-sm {
    padding: 4px 8px;
    font-size: 11px;
    white-space: nowrap;
    min-width: auto;
}

/* Ajuste da coluna de ações */
.data-table th:last-child,
.data-table td:last-child {
    min-width: 200px;
    text-align: center;
}

@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 3px;
        width: 100%;
    }
    
    .action-buttons .btn-sm {
        width: 100%;
        text-align: center;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit do formulário de filtros quando mudar registros por página
document.getElementById('per_page').addEventListener('change', function() {
    this.form.submit();
});

// Atalhos de teclado
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K para focar na busca
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        document.getElementById('search').focus();
    }
    
    // Ctrl/Cmd + N para novo funcionário
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        window.location.href = '/funcionarios/cadastrar';
    }
});

// Highlight da busca e inicialização quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Highlight da busca
    const searchTerm = document.getElementById('search').value.toLowerCase();
    if (searchTerm) {
        const cells = document.querySelectorAll('.data-table td');
        cells.forEach(function(cell) {
            const text = cell.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                cell.style.backgroundColor = '#fff3cd';
            }
        });
    }
    
    // Adicionar event listeners nos botões de exclusão
    document.querySelectorAll('.btn-delete').forEach(function(button) {
        button.addEventListener('click', function() {
            var funcionarioId = this.getAttribute('data-id');
            var funcionarioNome = this.getAttribute('data-nome');
            confirmarExclusao(funcionarioId, funcionarioNome);
        });
    });
});

// Função de exclusão de funcionário com fallbacks
function confirmarExclusao(funcionarioId, funcionarioNome) {
    const message = `Tem certeza que deseja excluir o funcionário "${funcionarioNome}"? Esta ação não poderá ser desfeita.`;
    
    try {
        // Verificar se a modal global está funcionando
        if (typeof showConfirmModal === 'function') {
            showConfirmModal(message, function() {
                submitExclusaoForm(funcionarioId);
            });
        } else {
            // Fallback para confirm padrão do navegador
            if (confirm(message)) {
                submitExclusaoForm(funcionarioId);
            }
        }
    } catch (error) {
        console.error('Erro ao confirmar exclusão:', error);
        // Último fallback - apenas confirmação padrão
        if (confirm(message + "\n\n(Modo de fallback ativado devido a um erro)")) {
            submitExclusaoForm(funcionarioId);
        }
    }
}

// Função auxiliar para submeter o formulário
function submitExclusaoForm(funcionarioId) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/funcionarios/${funcionarioId}/apagar`;
    document.body.appendChild(form);
    form.submit();
}
</script>
{% endblock %}