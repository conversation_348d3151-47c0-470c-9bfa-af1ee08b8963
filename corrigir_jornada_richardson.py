#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para corrigir a jornada do funcionário Richardson
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def corrigir_jornada_richardson():
    """Corrigir a jornada do funcionário Richardson"""
    print("🔧 CORRIGINDO JORNADA DO FUNCIONÁRIO RICHARDSON")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar situação atual do Richardson
        print("\n1. Verificando situação atual do Richardson...")
        sql_richardson = """
        SELECT 
            f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id, f.horario_trabalho_id,
            f.jornada_seg_qui_entrada, f.jornada_seg_qui_saida,
            e.razao_social as empresa_nome
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        WHERE f.id = 1
        """
        
        richardson = db.execute_query(sql_richardson, fetch_one=True)
        
        if richardson:
            print(f"📋 Funcionário: {richardson['nome_completo']}")
            print(f"   Empresa: {richardson['empresa_nome']} (ID: {richardson['empresa_id']})")
            print(f"   Jornada Trabalho ID: {richardson['jornada_trabalho_id']}")
            print(f"   Horário Trabalho ID: {richardson['horario_trabalho_id']}")
            print(f"   Jornada Individual: {richardson['jornada_seg_qui_entrada']} às {richardson['jornada_seg_qui_saida']}")
        
        # 2. Verificar jornada padrão da empresa AiNexus
        print("\n2. Verificando jornada padrão da AiNexus...")
        sql_jornada_ainexus = """
        SELECT id, nome_jornada, seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
               intervalo_inicio, intervalo_fim, tolerancia_entrada_minutos
        FROM jornadas_trabalho
        WHERE empresa_id = %s AND padrao = TRUE AND ativa = TRUE
        """
        
        jornada_ainexus = db.execute_query(sql_jornada_ainexus, (richardson['empresa_id'],), fetch_one=True)
        
        if jornada_ainexus:
            print(f"📋 Jornada da AiNexus:")
            print(f"   ID: {jornada_ainexus['id']}")
            print(f"   Nome: {jornada_ainexus['nome_jornada']}")
            print(f"   Seg-Qui: {jornada_ainexus['seg_qui_entrada']} às {jornada_ainexus['seg_qui_saida']}")
            print(f"   Sexta: {jornada_ainexus['sexta_entrada']} às {jornada_ainexus['sexta_saida']}")
            print(f"   Intervalo: {jornada_ainexus['intervalo_inicio']} às {jornada_ainexus['intervalo_fim']}")
        else:
            print("❌ Nenhuma jornada padrão encontrada para a AiNexus!")
            return False
        
        # 3. Corrigir o funcionário Richardson
        print("\n3. Corrigindo jornada do Richardson...")
        
        # Atualizar o campo jornada_trabalho_id para apontar para a jornada correta da empresa
        sql_update = """
        UPDATE funcionarios 
        SET jornada_trabalho_id = %s,
            horario_trabalho_id = NULL,
            jornada_seg_qui_entrada = NULL,
            jornada_seg_qui_saida = NULL,
            jornada_sex_entrada = NULL,
            jornada_sex_saida = NULL,
            jornada_intervalo_entrada = NULL,
            jornada_intervalo_saida = NULL
        WHERE id = 1
        """
        
        result = db.execute_query(sql_update, (jornada_ainexus['id'],), fetch_all=False)
        
        if result is not None:
            print(f"✅ Richardson atualizado com sucesso!")
            print(f"   Jornada Trabalho ID: {jornada_ainexus['id']}")
            print(f"   Campos individuais de jornada limpos (NULL)")
        else:
            print("❌ Erro ao atualizar Richardson")
            return False
        
        # 4. Verificar resultado
        print("\n4. Verificando resultado...")
        richardson_atualizado = db.execute_query(sql_richardson, fetch_one=True)
        
        if richardson_atualizado:
            print(f"📋 Richardson após correção:")
            print(f"   Jornada Trabalho ID: {richardson_atualizado['jornada_trabalho_id']}")
            print(f"   Horário Trabalho ID: {richardson_atualizado['horario_trabalho_id']}")
            print(f"   Jornada Individual: {richardson_atualizado['jornada_seg_qui_entrada']} às {richardson_atualizado['jornada_seg_qui_saida']}")
        
        # 5. Testar função get_with_epis
        print("\n5. Testando função get_with_epis...")
        from utils.database import FuncionarioQueries
        
        funcionario_completo = FuncionarioQueries.get_with_epis(1)
        
        if funcionario_completo:
            print(f"📋 Dados via get_with_epis:")
            print(f"   Nome: {funcionario_completo.get('nome_completo')}")
            print(f"   Jornada: {funcionario_completo.get('nome_jornada')}")
            print(f"   Seg-Qui: {funcionario_completo.get('jornada_seg_qui_entrada')} às {funcionario_completo.get('jornada_seg_qui_saida')}")
            print(f"   Sexta: {funcionario_completo.get('jornada_sex_entrada')} às {funcionario_completo.get('jornada_sex_saida')}")
            print(f"   Intervalo: {funcionario_completo.get('jornada_intervalo_entrada')} às {funcionario_completo.get('jornada_intervalo_saida')}")
            print(f"   Tolerância: {funcionario_completo.get('tolerancia_entrada_minutos')} minutos")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a correção: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    corrigir_jornada_richardson()
