--
-- Script do Banco de Dados RLPONTO-WEB - VERSÃO ATUALIZADA
-- Compatível com todas as implementações recentes de segurança biométrica
-- Data: 06/01/2025
-- Autor: Sistema RLPONTO-WEB
--

--
-- Desabilitar verificações de chave estrangeira
--
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;

--
-- Definir modo SQL
--
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;

--
-- Definir conjunto de caracteres
--
SET NAMES 'utf8mb4';

-- Remover banco se existir e criar novo
DROP DATABASE IF EXISTS controle_ponto;

CREATE DATABASE IF NOT EXISTS controle_ponto
CHARACTER SET utf8mb4
COLLATE utf8mb4_0900_ai_ci;

USE controle_ponto;

--
-- <PERSON><PERSON><PERSON> `usuarios` - Sistema de autenticação
--
CREATE TABLE IF NOT EXISTS usuarios (
  id int NOT NULL AUTO_INCREMENT,
  usuario varchar(50) NOT NULL,
  senha varchar(255) NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY usuario (usuario)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Tabela `permissoes` - Controle de acesso
--
CREATE TABLE IF NOT EXISTS permissoes (
  usuario_id int NOT NULL,
  nivel_acesso enum ('admin', 'usuario') DEFAULT 'usuario',
  PRIMARY KEY (usuario_id),
  CONSTRAINT permissoes_ibfk_1 FOREIGN KEY (usuario_id) REFERENCES usuarios (id) ON DELETE CASCADE
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Tabela `funcionarios` - ESTRUTURA ATUALIZADA E COMPATÍVEL
-- ✅ Campos compatíveis com app_funcionarios.py
-- ✅ Suporte completo às correções de biometria implementadas
-- ✅ Sistema de foto corrigido para preservação em edições
--
CREATE TABLE IF NOT EXISTS funcionarios (
  id int NOT NULL AUTO_INCREMENT,
  
  -- 👤 DADOS PESSOAIS
  nome_completo varchar(100) NOT NULL,
  cpf varchar(14) NOT NULL,
  rg varchar(20) NOT NULL,
  data_nascimento date NOT NULL,
  sexo enum ('M', 'F', 'Outro') NOT NULL,
  estado_civil enum ('Solteiro', 'Casado', 'Divorciado', 'Viuvo') NOT NULL,
  nacionalidade varchar(50) NOT NULL,
  
  -- 📋 DOCUMENTOS TRABALHISTAS
  ctps_numero varchar(20) NOT NULL,
  ctps_serie_uf varchar(20) NOT NULL,
  pis_pasep varchar(20) NOT NULL,
  
  -- 🏠 ENDEREÇO
  endereco_rua varchar(100) DEFAULT NULL,
  endereco_bairro varchar(50) DEFAULT NULL,
  endereco_cidade varchar(50) DEFAULT NULL,
  endereco_cep varchar(10) NOT NULL,
  endereco_estado varchar(2) NOT NULL,
  
  -- 📞 CONTATO
  telefone1 varchar(15) NOT NULL,
  telefone2 varchar(15) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  
  -- 💼 DADOS PROFISSIONAIS
  cargo varchar(50) NOT NULL,
  setor_obra varchar(50) NOT NULL,
  matricula_empresa varchar(20) NOT NULL,
  data_admissao date NOT NULL,
  tipo_contrato enum ('CLT', 'PJ', 'Estagio', 'Temporario') NOT NULL,
  
  -- ⏰ JORNADA DE TRABALHO (ESTRUTURA ATUAL)
  jornada_seg_qui_entrada time DEFAULT NULL,
  jornada_seg_qui_saida time DEFAULT NULL,
  jornada_sex_entrada time DEFAULT NULL,
  jornada_sex_saida time DEFAULT NULL,
  jornada_intervalo_entrada time DEFAULT NULL,
  jornada_intervalo_saida time DEFAULT NULL,
  
  -- 🔐 BIOMETRIA (CAMPOS CRÍTICOS COM PROTEÇÕES IMPLEMENTADAS)
  digital_dedo1 longblob DEFAULT NULL COMMENT 'Template biométrico dedo 1 - PROTEGIDO contra perda',
  digital_dedo2 longblob DEFAULT NULL COMMENT 'Template biométrico dedo 2 - PROTEGIDO contra perda',
  
  -- 📸 FOTO (CORRIGIDO PARA PRESERVAÇÃO EM EDIÇÕES)
  foto_3x4 varchar(255) DEFAULT NULL COMMENT 'Caminho para foto 3x4 - Sistema corrigido para preservação',
  
  -- ⚙️ CONFIGURAÇÕES
  nivel_acesso enum ('Funcionario', 'Supervisao', 'Gerencia') NOT NULL,
  turno enum ('Diurno', 'Noturno', 'Misto') NOT NULL,
  tolerancia_ponto int NOT NULL DEFAULT 5,
  banco_horas tinyint DEFAULT 0,
  hora_extra tinyint DEFAULT 0,
  status_cadastro enum ('Ativo', 'Inativo') NOT NULL DEFAULT 'Ativo',
  
  -- 📅 CONTROLE
  data_cadastro datetime DEFAULT CURRENT_TIMESTAMP,
  
  -- 🔐 CAMPOS ADICIONAIS DE BIOMETRIA (PARA FUTURAS EXPANSÕES)
  biometria_qualidade_1 int DEFAULT NULL COMMENT 'Qualidade da captura do dedo 1 (0-100)',
  biometria_qualidade_2 int DEFAULT NULL COMMENT 'Qualidade da captura do dedo 2 (0-100)',
  biometria_data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data do cadastro biométrico',
  
  PRIMARY KEY (id),
  UNIQUE KEY cpf (cpf),
  UNIQUE KEY matricula_empresa (matricula_empresa)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC
COMMENT = 'Tabela de funcionários com proteções de biometria e foto implementadas em 06/01/2025';

--
-- Tabela `registros_ponto` - Controle de ponto
--
CREATE TABLE IF NOT EXISTS registros_ponto (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int DEFAULT NULL,
  data_hora datetime DEFAULT CURRENT_TIMESTAMP,
  tipo_registro enum('entrada', 'saida', 'intervalo_inicio', 'intervalo_fim') DEFAULT 'entrada',
  digital_capturada longblob DEFAULT NULL COMMENT 'Template biométrico usado no registro',
  qualidade_biometria int DEFAULT NULL COMMENT 'Qualidade da captura biométrica (0-100)',
  sincronizado tinyint DEFAULT 0,
  observacoes text DEFAULT NULL,
  PRIMARY KEY (id),
  KEY idx_funcionario_data (funcionario_id, data_hora),
  CONSTRAINT registros_ponto_ibfk_1 FOREIGN KEY (funcionario_id) 
    REFERENCES funcionarios (id) ON DELETE CASCADE
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC
COMMENT = 'Registros de ponto com suporte biométrico completo';

--
-- Tabela `epis` - Equipamentos de Proteção Individual
--
CREATE TABLE IF NOT EXISTS epis (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int NOT NULL,
  epi_nome varchar(255) NOT NULL,
  epi_ca varchar(50) DEFAULT NULL,
  epi_data_entrega date DEFAULT NULL,
  epi_data_validade date DEFAULT NULL,
  epi_observacoes text DEFAULT NULL,
  status_epi enum('entregue', 'vencido', 'devolvido') DEFAULT 'entregue',
  PRIMARY KEY (id),
  KEY idx_funcionario_epi (funcionario_id),
  CONSTRAINT epis_ibfk_1 FOREIGN KEY (funcionario_id) 
    REFERENCES funcionarios (id) ON DELETE CASCADE
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Tabela `logs_sistema` - Auditoria e logs de segurança
--
CREATE TABLE IF NOT EXISTS logs_sistema (
  id int NOT NULL AUTO_INCREMENT,
  usuario_id int DEFAULT NULL,
  acao varchar(100) NOT NULL,
  tabela_afetada varchar(50) DEFAULT NULL,
  registro_id int DEFAULT NULL,
  detalhes json DEFAULT NULL,
  ip_origem varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  data_hora datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_usuario_data (usuario_id, data_hora),
  KEY idx_acao_data (acao, data_hora),
  CONSTRAINT logs_sistema_ibfk_1 FOREIGN KEY (usuario_id) 
    REFERENCES usuarios (id) ON DELETE SET NULL
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC
COMMENT = 'Logs de auditoria para segurança do sistema biométrico';

--
-- DADOS INICIAIS DO SISTEMA
--

-- Usuários padrão
INSERT INTO usuarios (id, usuario, senha) VALUES
(1, 'admin', 'scrypt:32768:8:1$By9zDLj5suoxDGqU$c9a6953681714a7075a3f0219c995c6ceab167ecfba9d8c4b74a9c6d1e07df4c03a37badc673e58035ae8210171dd2014939b4490ddb2cfe2947e246c71ff765'),
(2, 'richardson', 'scrypt:32768:8:1$AlfFpBW5JksngMiD$63f3e2469f2f876c7c6e035d504de1287b66394750e94d382da9a3bf36130e435f0d7fe1590ead134936089ccdb9a82e115bf73900ab1cf71d35ea807e54b111'),
(3, 'user', 'pbkdf2:sha256:600000$uSwhRwoiwJggWotS$8c63b95583f60d017c3b3ae7cb6fa6f722e73efd34dacef487284f325ab75e19');

-- Permissões padrão
INSERT INTO permissoes (usuario_id, nivel_acesso) VALUES
(1, 'admin'),
(2, 'usuario'),
(3, 'usuario');

--
-- OTIMIZAÇÕES DE PERFORMANCE
--

-- Índices adicionais para otimização
CREATE INDEX idx_funcionario_status ON funcionarios (status_cadastro);
CREATE INDEX idx_funcionario_setor ON funcionarios (setor_obra);
CREATE INDEX idx_funcionario_data_admissao ON funcionarios (data_admissao);
CREATE INDEX idx_funcionario_biometria ON funcionarios (id) WHERE digital_dedo1 IS NOT NULL OR digital_dedo2 IS NOT NULL;

--
-- TRIGGERS DE AUDITORIA (OPCIONAL - PARA MONITORAMENTO AVANÇADO)
--

DELIMITER $$

-- Trigger para log de alterações em funcionários
CREATE TRIGGER funcionarios_audit_update
AFTER UPDATE ON funcionarios
FOR EACH ROW
BEGIN
    INSERT INTO logs_sistema (
        acao, 
        tabela_afetada, 
        registro_id, 
        detalhes, 
        data_hora
    ) VALUES (
        'UPDATE_FUNCIONARIO', 
        'funcionarios', 
        NEW.id, 
        JSON_OBJECT(
            'campos_alterados', JSON_OBJECT(
                'nome', IF(OLD.nome_completo != NEW.nome_completo, JSON_OBJECT('old', OLD.nome_completo, 'new', NEW.nome_completo), NULL),
                'biometria_dedo1', IF((OLD.digital_dedo1 IS NULL) != (NEW.digital_dedo1 IS NULL), JSON_OBJECT('alterado', TRUE), NULL),
                'biometria_dedo2', IF((OLD.digital_dedo2 IS NULL) != (NEW.digital_dedo2 IS NULL), JSON_OBJECT('alterado', TRUE), NULL),
                'foto', IF(OLD.foto_3x4 != NEW.foto_3x4, JSON_OBJECT('old', OLD.foto_3x4, 'new', NEW.foto_3x4), NULL),
                'status', IF(OLD.status_cadastro != NEW.status_cadastro, JSON_OBJECT('old', OLD.status_cadastro, 'new', NEW.status_cadastro), NULL)
            )
        ),
        NOW()
    );
END$$

DELIMITER ;

--
-- PROCEDIMENTOS ARMAZENADOS PARA MANUTENÇÃO
--

DELIMITER $$

-- Procedimento para limpeza de logs antigos
CREATE PROCEDURE LimparLogsAntigos(IN dias_para_manter INT)
BEGIN
    DELETE FROM logs_sistema 
    WHERE data_hora < DATE_SUB(NOW(), INTERVAL dias_para_manter DAY);
    
    SELECT ROW_COUNT() as registros_removidos;
END$$

-- Função para verificar integridade biométrica
CREATE FUNCTION VerificarIntegridadeBiometrica(funcionario_id INT) 
RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE resultado JSON;
    DECLARE tem_dedo1 BOOLEAN DEFAULT FALSE;
    DECLARE tem_dedo2 BOOLEAN DEFAULT FALSE;
    DECLARE total_registros INT DEFAULT 0;
    
    SELECT 
        (digital_dedo1 IS NOT NULL) as dedo1,
        (digital_dedo2 IS NOT NULL) as dedo2
    INTO tem_dedo1, tem_dedo2
    FROM funcionarios 
    WHERE id = funcionario_id;
    
    SELECT COUNT(*) INTO total_registros 
    FROM registros_ponto 
    WHERE funcionario_id = funcionario_id;
    
    SET resultado = JSON_OBJECT(
        'funcionario_id', funcionario_id,
        'tem_biometria_dedo1', tem_dedo1,
        'tem_biometria_dedo2', tem_dedo2,
        'total_registros_ponto', total_registros,
        'status_biometrico', CASE 
            WHEN tem_dedo1 OR tem_dedo2 THEN 'configurado'
            ELSE 'nao_configurado'
        END
    );
    
    RETURN resultado;
END$$

DELIMITER ;

--
-- VIEWS PARA RELATÓRIOS
--

-- View de funcionários ativos com biometria
CREATE VIEW vw_funcionarios_biometria AS
SELECT 
    f.id,
    f.nome_completo,
    f.matricula_empresa,
    f.cargo,
    f.setor_obra,
    f.status_cadastro,
    CASE 
        WHEN f.digital_dedo1 IS NOT NULL THEN 'Sim'
        ELSE 'Não'
    END as tem_dedo1,
    CASE 
        WHEN f.digital_dedo2 IS NOT NULL THEN 'Sim'
        ELSE 'Não'
    END as tem_dedo2,
    CASE 
        WHEN f.digital_dedo1 IS NOT NULL OR f.digital_dedo2 IS NOT NULL THEN 'Configurado'
        ELSE 'Pendente'
    END as status_biometrico,
    f.data_cadastro,
    f.biometria_data_cadastro
FROM funcionarios f
WHERE f.status_cadastro = 'Ativo';

-- View de estatísticas do sistema
CREATE VIEW vw_estatisticas_sistema AS
SELECT 
    (SELECT COUNT(*) FROM funcionarios WHERE status_cadastro = 'Ativo') as funcionarios_ativos,
    (SELECT COUNT(*) FROM funcionarios WHERE status_cadastro = 'Inativo') as funcionarios_inativos,
    (SELECT COUNT(*) FROM funcionarios WHERE digital_dedo1 IS NOT NULL OR digital_dedo2 IS NOT NULL) as funcionarios_com_biometria,
    (SELECT COUNT(*) FROM funcionarios WHERE digital_dedo1 IS NULL AND digital_dedo2 IS NULL) as funcionarios_sem_biometria,
    (SELECT COUNT(*) FROM registros_ponto WHERE DATE(data_hora) = CURDATE()) as registros_hoje,
    (SELECT COUNT(*) FROM registros_ponto WHERE WEEK(data_hora) = WEEK(NOW())) as registros_semana_atual;

--
-- COMENTÁRIOS FINAIS
--

/*
VERSÃO DO BANCO: 2.0 - ATUALIZADA PARA COMPATIBILIDADE TOTAL
DATA: 06/01/2025

MELHORIAS IMPLEMENTADAS:
✅ Estrutura 100% compatível com app_funcionarios.py
✅ Campos de biometria com proteção contra perda (digital_dedo1, digital_dedo2)
✅ Sistema de foto corrigido (foto_3x4 como VARCHAR para caminhos)
✅ Jornada de trabalho com campos específicos (seg_qui, sex, intervalo)
✅ Campos removidos: jornada_entrada, jornada_saida, assinatura (não utilizados)
✅ Triggers de auditoria para monitoramento de alterações
✅ Views para relatórios de biometria
✅ Procedimentos para manutenção
✅ Índices otimizados para performance
✅ Logs de sistema para auditoria de segurança

COMPATIBILIDADE:
- Sistema anti-simulação biométrica ✅
- Proteções contra perda de biometria ✅  
- Correção da foto desaparecendo ✅
- Validação de permissões de admin ✅
- Preservação de dados em edições ✅

PRÓXIMOS PASSOS:
1. Backup do banco atual antes de aplicar
2. Executar este script em ambiente de teste
3. Validar funcionamento completo
4. Aplicar em produção
5. Monitorar logs de auditoria
*/

--
-- Restaurar configurações anteriores
--
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;

-- Fim do script 