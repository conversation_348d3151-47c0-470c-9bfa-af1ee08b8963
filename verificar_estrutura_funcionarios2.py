#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Verificação da Estrutura da Tabela Funcionários
===============================================

Script para verificar a estrutura correta da tabela funcionários
e corrigir a consulta de alocações.

Data: 07/07/2025
"""

import pymysql

# Configurações do banco
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def verificar_estruturas():
    """Verifica estruturas das tabelas"""
    print("🔍 VERIFICAÇÃO DAS ESTRUTURAS DAS TABELAS")
    print("=" * 60)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        with conn.cursor() as cursor:
            
            # Verificar estrutura da tabela funcionarios
            print("\n📋 ESTRUTURA DA TABELA funcionarios:")
            print("-" * 40)
            cursor.execute("DESCRIBE funcionarios")
            campos_funcionarios = cursor.fetchall()
            for campo in campos_funcionarios:
                print(f"   {campo['Field']}: {campo['Type']}")
            
            # Verificar estrutura da tabela jornadas_trabalho
            print("\n📋 ESTRUTURA DA TABELA jornadas_trabalho:")
            print("-" * 40)
            cursor.execute("DESCRIBE jornadas_trabalho")
            campos_jornadas = cursor.fetchall()
            for campo in campos_jornadas:
                print(f"   {campo['Field']}: {campo['Type']}")
            
            # Verificar estrutura da tabela empresas
            print("\n📋 ESTRUTURA DA TABELA empresas:")
            print("-" * 40)
            cursor.execute("DESCRIBE empresas")
            campos_empresas = cursor.fetchall()
            for campo in campos_empresas:
                print(f"   {campo['Field']}: {campo['Type']}")
            
            # Testar consulta corrigida
            print("\n🧪 TESTANDO CONSULTA CORRIGIDA:")
            print("-" * 40)
            
            # Consulta corrigida baseada na estrutura real
            sql_corrigida = """
            SELECT fa.*, 
                   f.nome_completo as nome, f.cargo, f.cpf, f.telefone,
                   e.razao_social, e.nome_fantasia, e.cnpj,
                   jt.nome_jornada as jornada_nome, jt.carga_horaria_semanal as carga_horaria,
                   ec.nome_contrato, ec.codigo_contrato
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            JOIN empresas e ON fa.empresa_cliente_id = e.id
            JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
            LEFT JOIN empresa_clientes ec ON fa.contrato_id = ec.id
            ORDER BY fa.created_at DESC
            """
            
            cursor.execute(sql_corrigida)
            resultados = cursor.fetchall()
            
            print(f"   ✅ Consulta corrigida executada com sucesso!")
            print(f"   📊 Resultados: {len(resultados)} alocações encontradas")
            
            if resultados:
                print(f"\n   📋 Primeira alocação:")
                primeiro = resultados[0]
                print(f"      ID: {primeiro['id']}")
                print(f"      Funcionário: {primeiro['nome']}")
                print(f"      Cliente: {primeiro['razao_social']}")
                print(f"      Jornada: {primeiro['jornada_nome']}")
                print(f"      Status: {'Ativo' if primeiro['ativo'] else 'Inativo'}")
            
            return sql_corrigida
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return None
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    sql_corrigida = verificar_estruturas()
    
    if sql_corrigida:
        print(f"\n✅ CONSULTA CORRIGIDA PRONTA PARA USO!")
        print("🔧 A consulta SQL foi ajustada para os nomes corretos dos campos")
    else:
        print(f"\n❌ FALHA NA CORREÇÃO DA CONSULTA")
