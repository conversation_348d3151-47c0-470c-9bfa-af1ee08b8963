#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar se a jornada do funcionário está sendo carregada corretamente
"""

import sys
sys.path.append('/var/www/controle-ponto')

def testar_jornada_funcionario():
    """Testar se os dados de jornada estão sendo carregados"""
    try:
        from utils.database import FuncionarioQueries, DatabaseManager
        
        print("🔍 TESTANDO CARREGAMENTO DE JORNADA DO FUNCIONÁRIO")
        print("=" * 60)
        
        # 1. Buscar funcionários da empresa Ainexus Tecnologia
        print("\n1. BUSCANDO FUNCIONÁRIOS DA AINEXUS TECNOLOGIA:")
        print("-" * 50)
        
        funcionarios_ainexus = DatabaseManager.execute_query("""
            SELECT 
                f.id, f.nome_completo, f.empresa_id,
                e.nome_fantasia as empresa_nome,
                f.horario_trabalho_id, f.jornada_trabalho_id
            FROM funcionarios f
            INNER JOIN empresas e ON f.empresa_id = e.id
            WHERE e.nome_fantasia LIKE '%Ainexus%' OR e.nome_fantasia LIKE '%ainexus%'
            ORDER BY f.nome_completo
            LIMIT 5
        """, fetch_all=True)
        
        if not funcionarios_ainexus:
            print("   ❌ Nenhum funcionário encontrado na Ainexus Tecnologia")
            
            # Buscar qualquer funcionário para teste
            print("\n   🔍 Buscando qualquer funcionário para teste:")
            funcionarios_ainexus = DatabaseManager.execute_query("""
                SELECT 
                    f.id, f.nome_completo, f.empresa_id,
                    e.nome_fantasia as empresa_nome,
                    f.horario_trabalho_id, f.jornada_trabalho_id
                FROM funcionarios f
                INNER JOIN empresas e ON f.empresa_id = e.id
                WHERE f.status_cadastro = 'Ativo'
                ORDER BY f.nome_completo
                LIMIT 3
            """, fetch_all=True)
        
        for func in funcionarios_ainexus:
            print(f"   - {func['nome_completo']} (ID: {func['id']})")
            print(f"     Empresa: {func['empresa_nome']} (ID: {func['empresa_id']})")
            print(f"     Horário ID: {func['horario_trabalho_id']}")
            print(f"     Jornada ID: {func['jornada_trabalho_id']}")
        
        # 2. Testar função get_with_epis com o primeiro funcionário
        if funcionarios_ainexus:
            funcionario_teste = funcionarios_ainexus[0]
            funcionario_id = funcionario_teste['id']
            
            print(f"\n2. TESTANDO FUNÇÃO get_with_epis COM FUNCIONÁRIO {funcionario_id}:")
            print("-" * 50)
            
            funcionario_completo = FuncionarioQueries.get_with_epis(funcionario_id)
            
            if funcionario_completo:
                print(f"   ✅ Funcionário carregado: {funcionario_completo['nome_completo']}")
                print(f"   🏢 Empresa: {funcionario_completo.get('empresa_nome', 'N/A')}")
                
                # Verificar campos de jornada
                campos_jornada = [
                    'jornada_seg_qui_entrada',
                    'jornada_seg_qui_saida', 
                    'jornada_sex_entrada',
                    'jornada_sex_saida',
                    'jornada_intervalo_entrada',
                    'jornada_intervalo_saida'
                ]
                
                print("\n   📅 DADOS DE JORNADA:")
                jornada_encontrada = False
                for campo in campos_jornada:
                    valor = funcionario_completo.get(campo)
                    if valor:
                        jornada_encontrada = True
                        print(f"     ✅ {campo}: {valor}")
                    else:
                        print(f"     ❌ {campo}: --:--")
                
                if jornada_encontrada:
                    print("\n   🎉 SUCESSO: Dados de jornada encontrados!")
                else:
                    print("\n   ⚠️ PROBLEMA: Nenhum dado de jornada encontrado")
                    
                    # Verificar dados de horário como fallback
                    print("\n   🔍 Verificando dados de horário (fallback):")
                    campos_horario = ['entrada_manha', 'saida', 'saida_almoco', 'entrada_tarde']
                    for campo in campos_horario:
                        valor = funcionario_completo.get(campo)
                        print(f"     {campo}: {valor or 'N/A'}")
                
                # Verificar EPIs
                epis = funcionario_completo.get('epis', [])
                print(f"\n   🦺 EPIs: {len(epis)} encontrados")
                
            else:
                print(f"   ❌ Erro: Funcionário {funcionario_id} não encontrado")
        
        # 3. Verificar jornadas da empresa
        if funcionarios_ainexus:
            empresa_id = funcionarios_ainexus[0]['empresa_id']
            
            print(f"\n3. VERIFICANDO JORNADAS DA EMPRESA {empresa_id}:")
            print("-" * 50)
            
            # Verificar tabela jornadas_trabalho
            jornadas = DatabaseManager.execute_query("""
                SELECT id, nome_jornada, tipo_jornada, ativa, padrao
                FROM jornadas_trabalho 
                WHERE empresa_id = %s AND ativa = TRUE
                ORDER BY padrao DESC, nome_jornada
            """, (empresa_id,), fetch_all=True)
            
            print(f"   📋 Jornadas na tabela jornadas_trabalho: {len(jornadas)}")
            for jornada in jornadas:
                status = "⭐ PADRÃO" if jornada.get('padrao') else "📝 Normal"
                print(f"     - {jornada['nome_jornada']} ({status})")
            
            # Verificar tabela horarios_trabalho
            horarios = DatabaseManager.execute_query("""
                SELECT id, nome_horario, entrada_manha, saida, ativo
                FROM horarios_trabalho 
                WHERE empresa_id = %s AND ativo = TRUE
                ORDER BY nome_horario
            """, (empresa_id,), fetch_all=True)
            
            print(f"   ⏰ Horários na tabela horarios_trabalho: {len(horarios)}")
            for horario in horarios:
                print(f"     - {horario['nome_horario']} ({horario['entrada_manha']}-{horario['saida']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    testar_jornada_funcionario()
