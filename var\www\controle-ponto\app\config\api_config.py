"""
Configuração de APIs Externas e MCP Servers
Sistema centralizado para gerenciar conexões externas do agente
"""
import os
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import re

@dataclass
class APIConfig:
    """Configuração de API externa"""
    name: str
    base_url: str
    api_key: str
    headers: Dict[str, str]
    timeout: int = 30
    enabled: bool = True
    created_at: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()

    def _validate_and_clean_url(self, url: str) -> str:
        """Valida e limpa uma URL"""
        if not url:
            raise ValueError("URL não pode estar vazia")
        
        # Remove espaços
        url = url.strip()
        
        # Adiciona protocolo se não tiver
        if not url.startswith(('http://', 'https://')):
            url = f"http://{url}"
        
        # Valida formato básico
        url_pattern = re.compile(
            r'^https?://'  # protocolo
            r'(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)*'  # domínio
            r'[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?'  # último nível do domínio
            r'(?::\d+)?'  # porta opcional
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        # Também aceita IPs
        ip_pattern = re.compile(
            r'^https?://'
            r'(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}'
            r'(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)'
            r'(?::\d+)?'
            r'(?:/?|[/?]\S+)$')
        
        if not (url_pattern.match(url) or ip_pattern.match(url)):
            raise ValueError(f"URL inválida: {url}")
        
        return url

@dataclass
class MCPServerConfig:
    """Configuração de MCP Server"""
    name: str
    server_url: str
    auth_token: str
    capabilities: list
    transport: str = "http"  # http, websocket, sse
    enabled: bool = True
    created_at: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()

class APIManager:
    """Gerenciador centralizado de APIs e MCP Servers"""
    
    def __init__(self, config_file: str = "config/connections.json"):
        self.config_file = config_file
        self.apis: Dict[str, APIConfig] = {}
        self.mcp_servers: Dict[str, MCPServerConfig] = {}
        self.load_configurations()
    
    def load_configurations(self):
        """Carrega configurações do arquivo JSON"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Carregar APIs
                for api_data in data.get('apis', []):
                    api_config = APIConfig(**api_data)
                    self.apis[api_config.name] = api_config
                
                # Carregar MCP Servers
                for mcp_data in data.get('mcp_servers', []):
                    mcp_config = MCPServerConfig(**mcp_data)
                    self.mcp_servers[mcp_config.name] = mcp_config
                    
        except Exception as e:
            print(f"Erro ao carregar configurações: {e}")
    
    def save_configurations(self):
        """Salva configurações no arquivo JSON"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            data = {
                'apis': [asdict(api) for api in self.apis.values()],
                'mcp_servers': [asdict(mcp) for mcp in self.mcp_servers.values()]
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Erro ao salvar configurações: {e}")
    
    def add_api(self, name: str, base_url: str, api_key: str, 
                headers: Dict[str, str] = None, timeout: int = 30) -> bool:
        """Adiciona nova API externa"""
        try:
            if headers is None:
                headers = {"Content-Type": "application/json"}
            
            api_config = APIConfig(
                name=name,
                base_url=base_url,
                api_key=api_key,
                headers=headers,
                timeout=timeout
            )
            
            self.apis[name] = api_config
            self.save_configurations()
            return True
            
        except Exception as e:
            print(f"Erro ao adicionar API {name}: {e}")
            return False
    
    def add_mcp_server(self, name: str, server_url: str, auth_token: str,
                       capabilities: list = None, transport: str = "http") -> bool:
        """Adiciona novo MCP Server"""
        try:
            if capabilities is None:
                capabilities = ["tools", "resources", "prompts"]
            
            mcp_config = MCPServerConfig(
                name=name,
                server_url=server_url,
                auth_token=auth_token,
                capabilities=capabilities,
                transport=transport
            )
            
            self.mcp_servers[name] = mcp_config
            self.save_configurations()
            return True
            
        except Exception as e:
            print(f"Erro ao adicionar MCP Server {name}: {e}")
            return False
    
    def get_api(self, name: str) -> Optional[APIConfig]:
        """Obtém configuração de API por nome"""
        return self.apis.get(name)
    
    def get_mcp_server(self, name: str) -> Optional[MCPServerConfig]:
        """Obtém configuração de MCP Server por nome"""
        return self.mcp_servers.get(name)
    
    def list_apis(self, enabled_only: bool = True) -> Dict[str, APIConfig]:
        """Lista todas as APIs configuradas"""
        if enabled_only:
            return {name: api for name, api in self.apis.items() if api.enabled}
        return self.apis.copy()
    
    def list_mcp_servers(self, enabled_only: bool = True) -> Dict[str, MCPServerConfig]:
        """Lista todos os MCP Servers configurados"""
        if enabled_only:
            return {name: mcp for name, mcp in self.mcp_servers.items() if mcp.enabled}
        return self.mcp_servers.copy()
    
    def toggle_api(self, name: str) -> bool:
        """Alterna status de uma API"""
        if name in self.apis:
            self.apis[name].enabled = not self.apis[name].enabled
            self.save_configurations()
            return True
        return False
    
    def toggle_mcp_server(self, name: str) -> bool:
        """Alterna status de um MCP Server"""
        if name in self.mcp_servers:
            self.mcp_servers[name].enabled = not self.mcp_servers[name].enabled
            self.save_configurations()
            return True
        return False
    
    def remove_api(self, name: str) -> bool:
        """Remove API configurada"""
        if name in self.apis:
            del self.apis[name]
            self.save_configurations()
            return True
        return False
    
    def remove_mcp_server(self, name: str) -> bool:
        """Remove MCP Server configurado"""
        if name in self.mcp_servers:
            del self.mcp_servers[name]
            self.save_configurations()
            return True
        return False

# Instância global do gerenciador
api_manager = APIManager() 