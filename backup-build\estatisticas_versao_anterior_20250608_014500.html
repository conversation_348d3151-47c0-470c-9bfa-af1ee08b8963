{% extends "base.html" %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-blue-950 dark:to-indigo-950">
    <!-- Modern Header Section -->
    <div class="relative overflow-hidden bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-b border-slate-200/60 dark:border-slate-700/60">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-indigo-600/5 to-purple-600/5"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <div class="flex items-center justify-center mb-4">
                    <div class="p-3 rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-600 shadow-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                </div>
                <h1 class="text-4xl font-bold text-slate-900 dark:text-white mb-2">{{ titulo }}</h1>
                <p class="text-xl text-slate-600 dark:text-slate-300 mb-4">Dashboard avançado com análises de ponto em tempo real</p>
                <div class="inline-flex items-center gap-2 px-4 py-2 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-full border border-slate-200/60 dark:border-slate-700/60">
                    <svg class="w-4 h-4 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    <span class="text-sm font-medium text-slate-700 dark:text-slate-300">{{ periodo }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Stats Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total de Registros -->
            <div class="group relative overflow-hidden rounded-2xl bg-white dark:bg-slate-800 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-indigo-600/10"></div>
                <div class="relative p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-2 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        <div class="text-right">
                            <p class="text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Total</p>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <p class="text-3xl font-bold text-slate-900 dark:text-white">{{ "{:,}".format(stats.total_registros) }}</p>
                        <p class="text-sm text-slate-600 dark:text-slate-300">Registros de Ponto</p>
                    </div>
                </div>
            </div>

            <!-- Funcionários Ativos -->
            <div class="group relative overflow-hidden rounded-2xl bg-white dark:bg-slate-800 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-green-600/10"></div>
                <div class="relative p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-2 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                        </div>
                        <div class="text-right">
                            <p class="text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Ativos</p>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <p class="text-3xl font-bold text-slate-900 dark:text-white">{{ "{:,}".format(stats.funcionarios_ativos) }}</p>
                        <p class="text-sm text-slate-600 dark:text-slate-300">Funcionários</p>
                    </div>
                </div>
            </div>

            <!-- Registros Biométricos -->
            <div class="group relative overflow-hidden rounded-2xl bg-white dark:bg-slate-800 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-violet-600/10"></div>
                <div class="relative p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-2 rounded-xl bg-gradient-to-r from-purple-500 to-violet-600 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0h-2m8-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="text-right">
                            <p class="text-xs font-medium text-purple-600 dark:text-purple-400 font-semibold">{{ stats.percentual_biometrico }}%</p>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <p class="text-3xl font-bold text-slate-900 dark:text-white">{{ "{:,}".format(stats.registros_biometricos) }}</p>
                        <p class="text-sm text-slate-600 dark:text-slate-300">Biométricos</p>
                    </div>
                </div>
            </div>

            <!-- Registros Manuais -->
            <div class="group relative overflow-hidden rounded-2xl bg-white dark:bg-slate-800 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-amber-500/10 to-orange-600/10"></div>
                <div class="relative p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-2 rounded-xl bg-gradient-to-r from-amber-500 to-orange-600 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                        </div>
                        <div class="text-right">
                            <p class="text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Manual</p>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <p class="text-3xl font-bold text-slate-900 dark:text-white">{{ "{:,}".format(stats.registros_manuais) }}</p>
                        <p class="text-sm text-slate-600 dark:text-slate-300">Manuais</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Registros Diários Chart -->
            <div class="rounded-2xl bg-white dark:bg-slate-800 shadow-lg overflow-hidden">
                <div class="p-6 border-b border-slate-200 dark:border-slate-700">
                    <div class="flex items-center gap-3 mb-2">
                        <div class="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-600">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-slate-900 dark:text-white">Registros por Dia</h3>
                    </div>
                    <p class="text-sm text-slate-600 dark:text-slate-300">Últimos 7 dias de atividade</p>
                </div>
                <div class="p-6">
                    <div class="h-64">
                        <canvas id="chartRegistrosDiarios"></canvas>
                    </div>
                </div>
            </div>

            <!-- Métodos de Registro Chart -->
            <div class="rounded-2xl bg-white dark:bg-slate-800 shadow-lg overflow-hidden">
                <div class="p-6 border-b border-slate-200 dark:border-slate-700">
                    <div class="flex items-center gap-3 mb-2">
                        <div class="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-violet-600">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-slate-900 dark:text-white">Distribuição por Método</h3>
                    </div>
                    <p class="text-sm text-slate-600 dark:text-slate-300">Biométrico vs Manual</p>
                </div>
                <div class="p-6 flex items-center justify-center">
                    <div class="w-64 h-64">
                        <canvas id="chartMetodos"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pontualidade Chart Full Width -->
        <div class="rounded-2xl bg-white dark:bg-slate-800 shadow-lg overflow-hidden mb-8">
            <div class="p-6 border-b border-slate-200 dark:border-slate-700">
                <div class="flex items-center gap-3 mb-2">
                    <div class="p-2 rounded-lg bg-gradient-to-r from-red-500 to-pink-600">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-slate-900 dark:text-white">Análise de Pontualidade</h3>
                </div>
                <p class="text-sm text-slate-600 dark:text-slate-300">Acompanhamento de atrasos por dia</p>
            </div>
            <div class="p-6">
                <div class="h-64">
                    <canvas id="chartPontualidade"></canvas>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-4 justify-center">
            <a href="/relatorios/pontos" class="group inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
                <svg class="w-5 h-5 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                Ver Relatórios Detalhados
            </a>
            <a href="/registro-ponto/manual" class="group inline-flex items-center gap-3 px-6 py-3 bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200 font-medium rounded-xl shadow-lg hover:shadow-xl border border-slate-200 dark:border-slate-700 transform hover:-translate-y-0.5 transition-all duration-200">
                <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                Registrar Ponto Manual
            </a>
            <a href="/registro-ponto/biometrico" class="group inline-flex items-center gap-3 px-6 py-3 bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200 font-medium rounded-xl shadow-lg hover:shadow-xl border border-slate-200 dark:border-slate-700 transform hover:-translate-y-0.5 transition-all duration-200">
                <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0h-2m8-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                Registrar Ponto Biométrico
            </a>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Chart themes and colors
    const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    const themes = {
        light: {
            background: 'rgb(255, 255, 255)',
            text: 'rgb(51, 65, 85)',
            grid: 'rgb(226, 232, 240)',
            colors: {
                primary: 'rgb(59, 130, 246)',
                secondary: 'rgb(139, 92, 246)',
                success: 'rgb(34, 197, 94)',
                warning: 'rgb(251, 146, 60)',
                danger: 'rgb(239, 68, 68)'
            }
        },
        dark: {
            background: 'rgb(30, 41, 59)',
            text: 'rgb(203, 213, 225)',
            grid: 'rgb(71, 85, 105)',
            colors: {
                primary: 'rgb(96, 165, 250)',
                secondary: 'rgb(167, 139, 250)',
                success: 'rgb(74, 222, 128)',
                warning: 'rgb(251, 191, 36)',
                danger: 'rgb(248, 113, 113)'
            }
        }
    };
    
    const theme = isDarkMode ? themes.dark : themes.light;
    
    // Data from Flask template
    const labelsData = {{ graficos.labels_dias | tojson }};
    const registrosDiariosData = {{ graficos.dados_registros_diarios | tojson }};
    const pontualidadeData = {{ graficos.dados_pontualidade | tojson }};
    const biometricosData = {{ stats.registros_biometricos }};
    const manuaisData = {{ stats.registros_manuais }};

    // Chart configurations
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: theme.grid
                },
                ticks: {
                    color: theme.text,
                    stepSize: 1
                }
            },
            x: {
                grid: {
                    color: theme.grid
                },
                ticks: {
                    color: theme.text
                }
            }
        }
    };

    // Bar Chart - Registros Diários
    const ctxBar = document.getElementById('chartRegistrosDiarios').getContext('2d');
    new Chart(ctxBar, {
        type: 'bar',
        data: {
            labels: labelsData,
            datasets: [{
                label: 'Registros por Dia',
                data: registrosDiariosData,
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: chartOptions
    });

    // Doughnut Chart - Métodos de Registro
    const ctxDoughnut = document.getElementById('chartMetodos').getContext('2d');
    new Chart(ctxDoughnut, {
        type: 'doughnut',
        data: {
            labels: ['Biométrico', 'Manual'],
            datasets: [{
                data: [biometricosData, manuaisData],
                backgroundColor: [
                    'rgba(139, 92, 246, 0.8)',
                    'rgba(251, 146, 60, 0.8)'
                ],
                borderColor: [
                    'rgb(139, 92, 246)',
                    'rgb(251, 146, 60)'
                ],
                borderWidth: 3,
                hoverOffset: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        color: theme.text,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                }
            }
        }
    });

    // Line Chart - Pontualidade
    const ctxLine = document.getElementById('chartPontualidade').getContext('2d');
    new Chart(ctxLine, {
        type: 'line',
        data: {
            labels: labelsData,
            datasets: [{
                label: 'Atrasos',
                data: pontualidadeData,
                fill: true,
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderColor: 'rgb(239, 68, 68)',
                tension: 0.4,
                pointBackgroundColor: 'rgb(239, 68, 68)',
                pointBorderColor: 'white',
                pointBorderWidth: 3,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: chartOptions
    });
});
</script>

<style>
/* Additional custom styles for modern look */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

body {
    font-family: 'Inter', sans-serif;
}

/* Smooth animations */
* {
    transition: all 0.2s ease;
}

/* Glass morphism effect */
.backdrop-blur-sm {
    backdrop-filter: blur(8px);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    ::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
    }
    
    ::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
    }
}

/* Loading animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Mobile responsiveness */
@media (max-width: 640px) {
    .grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %} 