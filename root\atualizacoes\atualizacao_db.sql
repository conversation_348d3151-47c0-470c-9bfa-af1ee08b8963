-- Arquivo de atualização do banco de dados (vazio por enquanto, mas pode ser preenchido com alterações futuras)
-- Exemplo de uso futuro:
-- ALTER TABLE funcionarios ADD COLUMN novo_campo VARCHAR(50);

-- Adiciona campos detalhados de controle de acesso
ALTER TABLE funcionarios
  ADD COLUMN jornada_seg_qui_entrada TIME DEFAULT NULL,
  ADD COLUMN jornada_seg_qui_saida TIME DEFAULT NULL,
  ADD COLUMN jornada_sex_entrada TIME DEFAULT NULL,
  ADD COLUMN jornada_sex_saida TIME DEFAULT NULL,
  ADD COLUMN jornada_intervalo_entrada TIME DEFAULT NULL,
  ADD COLUMN jornada_intervalo_saida TIME DEFAULT NULL;

-- Observação: Após executar este script, ajuste o backend para salvar e ler esses novos campos.
