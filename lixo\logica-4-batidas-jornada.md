# Lógica das 4 Batidas da Jornada - Sistema RLPONTO-WEB
**Data:** 09/07/2025  
**Versão:** 2.0 - Sistema Flexível  
**Arquivo:** `app_registro_ponto.py`  

## 📋 **VISÃO GERAL DO SISTEMA**

O sistema RLPONTO-WEB implementa uma **lógica flexível e automática** para controle de jornada de trabalho com **4 batidas obrigatórias**:

1. **🌅 Entrada Manhã** - Início do expediente (Período: 00:00 - 11:59)
2. **🍽️ Saída Intervalo** - Início do intervalo (Período: 10:00 - 15:59)
3. **🔄 Retorno Intervalo** - Retorno do intervalo (Período: 11:00 - 17:59)
4. **🌆 Saída Final** - Fim do expediente (Período: 14:00 - 23:59)

---

## 🎯 **LÓGICA DE PRIORIDADE PARA JORNADAS**

### **Sistema Automático de Detecção:**
```sql
-- ✅ PRIORIDADE 1: Jornada da alocação ativa (funcionário alocado para cliente)
-- ✅ PRIORIDADE 2: Configuração da empresa (empresas_config)
-- ✅ PRIORIDADE 3: Jornada específica do funcionário (fallback)
```

### **Fonte de Dados por Prioridade:**
1. **🎯 Funcionário Alocado:** Usa jornada específica da alocação
2. **🏢 Funcionário da Empresa:** Usa configuração da empresa (`empresas_config`)
3. **👤 Funcionário Específico:** Usa jornada individual (`horarios_trabalho`)

---

## 🕐 **DETALHAMENTO DAS 4 BATIDAS**

### **1. 🌅 ENTRADA MANHÃ**

#### **Configuração Exemplo (AiNexus):**
- **Horário Programado:** 09:00
- **Tolerância:** 5 minutos
- **Janela Flexível:** Até o fim do período (00:00 - 11:59)

#### **Lógica de Validação:**
```python
# ✅ FLEXIBILIDADE: Permite entrada antecipada até 15 minutos antes
limite_antecipacao = 15  # minutos
hora_min_antecipada = 08:45  # (09:00 - 15 min)

# ⚠️ IMPORTANTE: Para cálculo de pagamento, sempre usar horário programado da jornada

# ✅ PERÍODOS PERMITIDOS:
# 08:45 - 09:00 → Status: "Pontual" (Antecipado - conta como 09:00 para pagamento)
# 09:00 - 09:05 → Status: "Pontual" (No horário/tolerância)
# 09:06 - 11:59 → Status: "Atrasado" (Após tolerância - permitido até fim do período)
# 12:00+       → Status: "Ausente Manhã" (Primeira batida em outro período)
```

#### **Mensagens do Sistema:**

**Exemplo 1 - Jornada 08:00 (Tolerância 15min):**
- ⚠️ **08:36:** "Entrada registrada com atraso (36 min)" - Status: Atrasado
- ✅ **08:10:** "Entrada registrada com sucesso (Pontual)" - Status: Pontual

**Exemplo 2 - Jornada 09:00 (Tolerância 5min):**
- ✅ **09:03:** "Entrada registrada com sucesso (Pontual)" - Status: Pontual
- ⚠️ **09:06:** "Entrada registrada com atraso (1 min)" - Status: Atrasado
- ⚠️ **10:30:** "Entrada registrada com atraso (1h30min)" - Status: Atrasado

---

### **2. 🍽️ SAÍDA PARA INTERVALO**

#### **Configuração Exemplo (AiNexus):**
- **Horário Programado:** 13:00 (Simbólico)
- **Tolerância:** Não há tolerância para saída (flexível)
- **Regra:** Flexível após entrada da manhã
- **Período:** 10:00 - 15:59

#### **Lógica de Validação:**
```python
# ✅ HORÁRIO SIMBÓLICO: Saída flexível após entrada da manhã
# ✅ REGRA PRINCIPAL: Deve ter registrado entrada da manhã
# ✅ CONTAGEM: Inicia contagem do intervalo na 2ª batida

# PERÍODOS PERMITIDOS:
# Após entrada manhã → Status: "Saída Intervalo" (Flexível)
# Qualquer horário no período → Status: "Saída Intervalo"
```

#### **Mensagens do Sistema:**
- ✅ **12:30:** "Saída para intervalo registrada"
- ✅ **13:00:** "Saída para intervalo registrada"
- ✅ **13:30:** "Saída para intervalo registrada"

**⚠️ Importante:** Contagem do intervalo inicia na 2ª batida para cálculo do retorno.

---

### **3. 🔄 RETORNO DO INTERVALO**

#### **Configuração Exemplo (AiNexus):**
- **Horário Programado:** 14:00
- **Tolerância:** 5 minutos
- **Regra:** Mínimo 1 hora de intervalo
- **Período:** 11:00 - 17:59

#### **Lógica de Validação:**
```python
# ✅ REGRA OBRIGATÓRIA: Mínimo 1 hora de intervalo
intervalo_minimo = 60  # minutos
hora_min_retorno = saida_intervalo + 1 hora

# ✅ CÁLCULO DE ATRASO: Minutos além da tolerância geram desconto negativo
# PERÍODOS PERMITIDOS:
# Antes de 1h        → Status: "Intervalo 1h" (Força 1h completa)
# 1h até tolerância  → Status: "Pontual" (Retorno no prazo)
# Após tolerância    → Status: "Atraso Retorno" + minutos negativos
```

#### **Regras de Cálculo:**
- **Intervalo < 1h:** Considera 1 hora completa exata
- **Retorno após tolerância:** Gera desconto negativo nas horas trabalhadas
- **Exemplo:** Saída 13:00, Retorno 14:10 = 5 min de atraso (desconto)

---

### **4. 🌆 SAÍDA FINAL**

#### **Configuração Exemplo (AiNexus):**
- **Horário Programado:** 18:00
- **Tolerância:** 5 minutos
- **Período:** 14:00 - 23:59
- **Saída Antecipada:** Permitida após 3ª batida

#### **Lógica de Validação:**
```python
# ✅ 4ª BATIDA: Saída final (não conta hora extra)
# ✅ 5ª BATIDA: Início de hora extra
# ✅ 6ª BATIDA: Fim de hora extra

# PERÍODOS PERMITIDOS:
# Após 3ª batida   → Status: "Saída Antecipada" (antes do fim da jornada)
# 18:00 - 18:05    → Status: "Pontual" (No horário/tolerância)
# Após 18:05       → Status: "Atraso Saída" (4ª batida atrasada)
# 5ª batida        → Status: "Início Hora Extra"
# 6ª batida        → Status: "Fim Hora Extra"
```

#### **Mensagens do Sistema:**

**Jornada 18:00 (Tolerância 5min):**
- ✅ **17:30:** "Saída antecipada registrada" - Status: Saída Antecipada
- ✅ **18:00:** "Saída registrada com sucesso (Pontual)" - Status: Pontual
- ✅ **18:03:** "Saída registrada com sucesso (Pontual)" - Status: Pontual
- ⚠️ **18:10:** "Saída registrada com atraso (5 min)" - Status: Atraso Saída

**Sistema de Hora Extra:**
- **5ª Batida:** "Início de hora extra registrado"
- **6ª Batida:** "Fim de hora extra registrado" + cálculo de horas extras

---

## 🔄 **FLUXO COMPLETO DA JORNADA**

### **Exemplo Prático - Richardson (AiNexus):**

```
📅 Jornada: 09:00-13:00/14:00-18:00 (Tolerância: 5 min)

🌅 08:45 - ENTRADA MANHÃ
   ✅ Status: Pontual (Antecipado)
   💰 Cálculo: Conta como 09:00 para pagamento
   📱 "Entrada registrada com sucesso (Pontual)"

🍽️ 12:45 - SAÍDA INTERVALO
   ✅ Status: Saída Intervalo
   📱 "Saída para intervalo registrada"

🔄 13:50 - RETORNO INTERVALO
   ✅ Status: Pontual (Intervalo: 1h05min)
   📱 "Retorno do intervalo registrado (Pontual)"

🌆 18:02 - SAÍDA FINAL
   ✅ Status: Pontual (Dentro da tolerância)
   📱 "Saída registrada com sucesso (Pontual)"

💼 Horas Trabalhadas: 09:00-12:45 + 13:50-18:02 = 8h07min
```

---

## ⚙️ **CONFIGURAÇÕES POR EMPRESA**

### **Fonte de Dados:**
- **Tabela:** `empresas_config`
- **Campos:** `jornada_segunda_entrada`, `jornada_segunda_saida`, etc.
- **Tolerância:** `tolerancia_empresa_minutos`

### **Exemplos de Configuração:**

#### **AiNexus Tecnologia:**
```
Entrada: 09:00 | Saída Intervalo: 13:00 | Retorno: 14:00 | Saída: 18:00
Tolerância: 5 minutos
Antecipação: 15 minutos antes
Período Entrada: 00:00-11:59 | Período Saída: 14:00-23:59
```

#### **MSV Engenharia:**
```
Entrada: 08:00 | Saída Intervalo: 12:00 | Retorno: 13:00 | Saída: 17:00
Tolerância: 10 minutos
Antecipação: 15 minutos antes
Período Entrada: 00:00-11:59 | Período Saída: 14:00-23:59
```

---

## 🎯 **REGRAS DE NEGÓCIO**

### **Horários OBRIGATÓRIOS (com tolerância):**
- ✅ **Entrada Manhã:** Controle com tolerância e antecipação (15 min)
- ✅ **Retorno Intervalo:** Mínimo 1h de intervalo + tolerância
- ✅ **Saída Final:** Controle com tolerância

### **Horários SIMBÓLICOS (flexíveis):**
- 📋 **Saída Intervalo:** Flexível após entrada da manhã
- 📋 **Hora Extra:** 5ª e 6ª batidas para controle de extras

### **Status Possíveis:**
- ✅ **Pontual:** No horário, antecipado ou dentro da tolerância
- ⚠️ **Atrasado:** Após tolerância (gera desconto se aplicável)
- 📋 **Saída Antecipada:** Saída antes do fim da jornada
- 🕐 **Ausente Período:** Primeira batida em período diferente
- ⏰ **Hora Extra:** 5ª e 6ª batidas para extras

---

## 🚀 **BENEFÍCIOS DO SISTEMA**

### **✅ Flexibilidade:**
- Permite entrada antecipada e saída flexível
- Adapta-se a diferentes empresas automaticamente
- Respeita regras específicas de cada organização

### **✅ Automação:**
- Detecta empresa do funcionário automaticamente
- Aplica configurações corretas sem intervenção manual
- Funciona para funcionários alocados e não alocados

### **✅ Controle:**
- Mantém controle de jornada sem ser excessivamente restritivo
- Registra status correto (Pontual/Atrasado)
- Permite relatórios precisos de pontualidade

**O sistema garante flexibilidade operacional mantendo o controle necessário para gestão de jornada de trabalho!**
