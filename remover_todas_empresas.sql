-- Script para remover todas as empresas e dados relacionados
-- Data: 03/07/2025
-- Autor: Claude 3.7 Sonnet

-- Desabilitar verificação de chaves estrangeiras temporariamente
SET FOREIGN_KEY_CHECKS = 0;

-- Remover registros de ponto
TRUNCATE TABLE registros_ponto;

-- Remover EPIs (se existir)
DROP TABLE IF EXISTS epis;

-- Remover funcionários
TRUNCATE TABLE funcionarios;

-- Remover jornadas de trabalho
TRUNCATE TABLE jornadas_trabalho;

-- Remover empresas
TRUNCATE TABLE empresas;

-- Resetar auto_increment
ALTER TABLE empresas AUTO_INCREMENT = 1;

-- Habilitar verificação de chaves estrangeiras novamente
SET FOREIGN_KEY_CHECKS = 1;

-- Verificar se as tabelas estão vazias
SELECT 'Registros de ponto restantes: ' AS mensagem, COUNT(*) AS total FROM registros_ponto
UNION ALL
SELECT 'Funcionários restantes: ', COUNT(*) FROM funcionarios
UNION ALL
SELECT 'Jornadas restantes: ', COUNT(*) FROM jornadas_trabalho
UNION ALL
SELECT 'Empresas restantes: ', COUNT(*) FROM empresas; 