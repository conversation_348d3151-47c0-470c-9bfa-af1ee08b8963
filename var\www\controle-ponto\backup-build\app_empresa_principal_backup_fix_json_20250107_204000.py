#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MÓDULO: EMPRESA PRINCIPAL E GESTÃO DE CLIENTES
Sistema: RLPONTO-WEB
Data: 03/07/2025
Descrição: Gerenciamento da empresa principal e relacionamento com clientes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, session
from datetime import datetime, date
import logging
import json
from decimal import Decimal
from utils.database import DatabaseManager
from utils.auth import require_admin

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Blueprint para empresa principal
empresa_principal_bp = Blueprint('empresa_principal', __name__, url_prefix='/empresa-principal')

# Rota de teste simples
@empresa_principal_bp.route('/teste')
def teste():
    """Rota de teste para verificar se o blueprint está funcionando"""
    return jsonify({
        'status': 'ok',
        'message': 'Blueprint empresa_principal está funcionando!',
        'timestamp': datetime.now().isoformat()
    })



# ================================================================
# FUNÇÕES AUXILIARES
# ================================================================

def get_empresa_principal():
    """Obter dados da empresa principal"""
    try:
        sql = """
        SELECT e.*,
               COALESCE((SELECT COUNT(*) FROM empresa_clientes ec WHERE ec.empresa_principal_id = e.id AND ec.ativo = TRUE), 0) as total_clientes,
               COALESCE((SELECT COUNT(DISTINCT fa.funcionario_id) FROM funcionario_alocacoes fa
                INNER JOIN empresa_clientes ec ON fa.contrato_id = ec.id
                WHERE ec.empresa_principal_id = e.id AND fa.ativo = TRUE), 0) as funcionarios_alocados
        FROM empresas e
        WHERE e.empresa_principal = TRUE AND e.ativa = TRUE
        LIMIT 1
        """
        db = DatabaseManager()
        result = db.execute_query(sql)
        # Retornar o primeiro item da lista ou None se não houver resultado
        return result[0] if result and len(result) > 0 else None
    except Exception as e:
        logger.error(f"Erro ao obter empresa principal: {e}")
        return None

def definir_empresa_principal(empresa_id):
    """Definir uma empresa como principal"""
    try:
        logger.info(f"Iniciando definição da empresa {empresa_id} como principal")

        # Primeiro, remover flag de todas as empresas
        db = DatabaseManager()
        sql_remove = "UPDATE empresas SET empresa_principal = FALSE"
        logger.info(f"Executando: {sql_remove}")
        db.execute_query(sql_remove, fetch_all=False)
        logger.info("Flag removida de todas as empresas")

        # Depois, definir a nova empresa principal
        sql_define = """
        UPDATE empresas
        SET empresa_principal = TRUE, tipo_empresa = 'principal'
        WHERE id = %s AND ativa = TRUE
        """
        logger.info(f"Executando: {sql_define} com empresa_id={empresa_id}")
        result = db.execute_query(sql_define, (empresa_id,), fetch_all=False)
        logger.info(f"Resultado da atualização: {result}")

        logger.info(f"Empresa {empresa_id} definida como principal com sucesso")
        return True

    except Exception as e:
        logger.error(f"Erro ao definir empresa principal: {e}")
        import traceback
        logger.error(f"Traceback completo: {traceback.format_exc()}")
        return False

def get_empresas_disponiveis_para_cliente():
    """Obter empresas que podem ser clientes"""
    try:
        # Query simplificada sem LEFT JOIN
        sql = """
        SELECT e.id, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email,
               e.tipo_empresa, e.ativa,
               FALSE as ja_e_cliente
        FROM empresas e
        WHERE e.empresa_principal = FALSE AND e.ativa = TRUE
        ORDER BY e.razao_social
        """
        db = DatabaseManager()
        empresas = db.execute_query(sql)

        # Verificar quais já são clientes separadamente
        if empresas:
            for empresa in empresas:
                try:
                    sql_check = "SELECT COUNT(*) as count FROM empresa_clientes WHERE empresa_cliente_id = %s AND ativo = TRUE"
                    result = db.execute_query(sql_check, (empresa['id'],))
                    empresa['ja_e_cliente'] = result[0]['count'] > 0 if result else False
                except Exception as e:
                    logger.error(f"Erro ao verificar se empresa {empresa['id']} é cliente: {e}")
                    empresa['ja_e_cliente'] = False

        return empresas
    except Exception as e:
        logger.error(f"Erro ao obter empresas disponíveis: {e}")
        return []

def get_clientes_da_empresa_principal():
    """Obter todos os clientes da empresa principal"""
    try:
        # Query simplificada para evitar problemas de JOIN
        sql = """
        SELECT ec.*, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email,
               0 as funcionarios_alocados,
               0 as jornadas_disponiveis
        FROM empresa_clientes ec
        INNER JOIN empresas e ON ec.empresa_cliente_id = e.id
        INNER JOIN empresas ep ON ec.empresa_principal_id = ep.id
        WHERE ep.empresa_principal = TRUE AND ec.ativo = TRUE
        ORDER BY ec.data_inicio DESC
        """
        db = DatabaseManager()
        clientes = db.execute_query(sql)

        # Calcular funcionários alocados e jornadas separadamente para cada cliente
        if clientes:
            for cliente in clientes:
                try:
                    # ✅ CORREÇÃO: Contar funcionários alocados + funcionários cadastrados na empresa
                    sql_func = """
                    SELECT COUNT(DISTINCT funcionario_id) as count FROM (
                        -- Funcionários alocados via tabela funcionario_alocacoes
                        SELECT fa.funcionario_id
                        FROM funcionario_alocacoes fa
                        WHERE fa.contrato_id = %s AND fa.ativo = TRUE

                        UNION

                        -- Funcionários cadastrados diretamente na empresa
                        SELECT f.id as funcionario_id
                        FROM funcionarios f
                        WHERE f.empresa_id = %s AND f.status_cadastro = 'Ativo'
                    ) as todos_funcionarios
                    """
                    result_func = db.execute_query(sql_func, (cliente['id'], cliente['empresa_cliente_id']))
                    cliente['funcionarios_alocados'] = result_func[0]['count'] if result_func else 0

                    # Contar jornadas disponíveis
                    sql_jorn = """
                    SELECT COUNT(DISTINCT jt.id) as count
                    FROM jornadas_trabalho jt
                    WHERE jt.empresa_id = %s AND jt.ativa = TRUE
                    """
                    result_jorn = db.execute_query(sql_jorn, (cliente['empresa_cliente_id'],))
                    cliente['jornadas_disponiveis'] = result_jorn[0]['count'] if result_jorn else 0

                except Exception as e:
                    logger.error(f"Erro ao calcular dados do cliente {cliente.get('id', 'N/A')}: {e}")
                    cliente['funcionarios_alocados'] = 0
                    cliente['jornadas_disponiveis'] = 0

        return clientes
    except Exception as e:
        logger.error(f"Erro ao obter clientes: {e}")
        return []

def adicionar_cliente(empresa_principal_id, dados_cliente):
    """Adicionar uma empresa como cliente"""
    try:
        logger.info(f"🔍 Verificando estrutura da tabela empresa_clientes...")

        # Primeiro, verificar se a tabela existe e quais campos tem
        db = DatabaseManager()

        try:
            describe_result = db.execute_query("DESCRIBE empresa_clientes")
            logger.info(f"📊 Estrutura da tabela empresa_clientes: {describe_result}")
        except Exception as desc_error:
            logger.error(f"❌ Erro ao verificar estrutura da tabela: {desc_error}")

        # Query simplificada primeiro para testar
        logger.info("🔍 Tentando inserção com campos básicos...")
        sql_simples = """
        INSERT INTO empresa_clientes
        (empresa_principal_id, empresa_cliente_id, data_inicio, status_contrato, ativo)
        VALUES (%s, %s, %s, %s, %s)
        """

        params_simples = (
            empresa_principal_id,
            dados_cliente['empresa_cliente_id'],
            dados_cliente['data_inicio'],
            dados_cliente.get('status_contrato', 'ativo'),
            True
        )

        logger.info(f"📊 Parâmetros simples: {params_simples}")
        result = db.execute_query(sql_simples, params_simples, fetch_all=False)

        logger.info(f"✅ Cliente {dados_cliente['empresa_cliente_id']} adicionado à empresa principal {empresa_principal_id}")
        return True

    except Exception as e:
        logger.error(f"❌ Erro ao adicionar cliente: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def editar_cliente_dados(cliente_id, dados):
    """Editar dados de um cliente"""
    try:
        logger.info(f"🔄 Editando cliente {cliente_id} com dados: {dados}")

        db = DatabaseManager()

        sql = """
        UPDATE empresa_clientes SET
            nome_contrato = %s,
            codigo_contrato = %s,
            descricao_projeto = %s,
            data_inicio = %s,
            data_fim = %s,
            valor_contrato = %s,
            status_contrato = %s,
            observacoes = %s,
            updated_at = NOW()
        WHERE id = %s
        """

        params = (
            dados.get('nome_contrato'),
            dados.get('codigo_contrato'),
            dados.get('descricao_projeto'),
            dados['data_inicio'],
            dados.get('data_fim'),
            dados.get('valor_contrato'),
            dados.get('status_contrato', 'ativo'),
            dados.get('observacoes'),
            cliente_id
        )

        result = db.execute_query(sql, params, fetch_all=False)

        logger.info(f"✅ Cliente {cliente_id} editado com sucesso")
        return True

    except Exception as e:
        logger.error(f"❌ Erro ao editar cliente {cliente_id}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def get_funcionarios_disponiveis():
    """Obter funcionários disponíveis para alocação"""
    try:
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            return []

        sql = """
        SELECT f.id, f.nome_completo, f.cpf, f.cargo, f.setor, f.status_cadastro,
               f.data_admissao, f.empresa_id,
               CASE WHEN fa.id IS NOT NULL THEN TRUE ELSE FALSE END as ja_alocado,
               fa.empresa_cliente_id as cliente_atual_id,
               e.razao_social as cliente_atual_nome
        FROM funcionarios f
        LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id AND fa.ativo = TRUE
        LEFT JOIN empresas e ON fa.empresa_cliente_id = e.id
        WHERE f.empresa_id = %s AND f.status_cadastro = 'Ativo'
        ORDER BY f.nome_completo
        """
        db = DatabaseManager()
        return db.execute_query(sql, (empresa_principal['id'],))
    except Exception as e:
        logger.error(f"Erro ao obter funcionários disponíveis: {e}")
        return []

# ================================================================
# ROTAS PRINCIPAIS
# ================================================================

@empresa_principal_bp.route('/')
def index():
    """Página principal da empresa principal"""
    try:
        logger.info("=== ACESSANDO PÁGINA PRINCIPAL EMPRESA PRINCIPAL ===")

        # Verificação simples de sessão
        if 'usuario' not in session:
            return redirect(url_for('login'))

        # Buscar empresa principal real
        try:
            db = DatabaseManager()
            sql_empresa = "SELECT * FROM empresas WHERE empresa_principal = TRUE AND ativa = TRUE LIMIT 1"
            empresa_result = db.execute_query(sql_empresa)
            empresa_principal = empresa_result[0] if empresa_result else None
            logger.info(f"Empresa principal encontrada: {empresa_principal is not None}")
        except Exception as e:
            logger.error(f"Erro ao buscar empresa principal: {e}")
            empresa_principal = None

        if not empresa_principal:
            # Se não há empresa principal, mostrar lista para definir
            try:
                sql_empresas = """
                SELECT id, razao_social, nome_fantasia, cnpj, telefone, email, ativa, data_cadastro
                FROM empresas
                WHERE ativa = TRUE
                ORDER BY razao_social
                """
                empresas = db.execute_query(sql_empresas)
                return render_template('empresa_principal/definir_principal.html',
                                     empresas=empresas,
                                     titulo='Definir Empresa Principal')
            except Exception as e:
                logger.error(f"Erro ao buscar empresas: {e}")
                # Fallback: usar dados fictícios
                empresa_principal = {
                    'id': 1,
                    'razao_social': 'Empresa Principal (Configuração Necessária)',
                    'nome_fantasia': 'Configure uma empresa principal',
                    'cnpj': '00.000.000/0001-00',
                    'telefone': '',
                    'email': ''
                }

        # Dados básicos para o dashboard (simplificados)
        try:
            # Buscar estatísticas de empresas
            # Total de empresas
            sql_total_empresas = "SELECT COUNT(*) as total FROM empresas WHERE ativa = TRUE"
            result_total = db.execute_query(sql_total_empresas)
            total_empresas = result_total[0]['total'] if result_total else 0

            # Empresas ativas (não principal)
            sql_empresas_ativas = "SELECT COUNT(*) as total FROM empresas WHERE ativa = TRUE AND empresa_principal = FALSE"
            result_ativas = db.execute_query(sql_empresas_ativas)
            empresas_ativas = result_ativas[0]['total'] if result_ativas else 0

            # Total de funcionários
            sql_total_funcionarios = "SELECT COUNT(*) as total FROM funcionarios WHERE status_cadastro = 'Ativo'"
            result_funcionarios = db.execute_query(sql_total_funcionarios)
            total_funcionarios = result_funcionarios[0]['total'] if result_funcionarios else 0

        except Exception as e:
            logger.warning(f"Erro ao buscar estatísticas: {e}")
            total_empresas = 0
            empresas_ativas = 0
            total_funcionarios = 0

        # Estatísticas básicas
        stats = {
            'total_empresas': total_empresas,
            'empresas_ativas': empresas_ativas,
            'total_funcionarios': total_funcionarios
        }

        context = {
            'titulo': 'Empresa Principal - Dashboard',
            'empresa_principal': empresa_principal,
            'stats': stats
        }

        logger.info("Renderizando dashboard...")
        return render_template('empresa_principal/dashboard.html', **context)

    except Exception as e:
        logger.error(f"ERRO CRÍTICO na página principal: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Retornar página de erro simples
        return f"""
        <html>
        <head><title>Erro - Empresa Principal</title></head>
        <body>
            <h1>Erro na Página Principal</h1>
            <p><strong>Erro:</strong> {str(e)}</p>
            <p><a href="/">Voltar ao Início</a></p>
            <pre>{traceback.format_exc()}</pre>
        </body>
        </html>
        """, 500


@empresa_principal_bp.route('/definir', methods=['POST'])
def definir_principal():
    """Define uma empresa como principal"""
    try:
        data = request.get_json()
        empresa_id = data.get('empresa_id')

        if not empresa_id:
            return jsonify({'success': False, 'message': 'ID da empresa é obrigatório'})

        db = DatabaseManager()

        # Primeiro, remover flag de principal de todas as empresas
        sql_remove = "UPDATE empresas SET empresa_principal = FALSE"
        db.execute_query(sql_remove)

        # Definir a empresa selecionada como principal
        sql_define = "UPDATE empresas SET empresa_principal = TRUE WHERE id = %s"
        db.execute_query(sql_define, (empresa_id,))

        logger.info(f"Empresa {empresa_id} definida como principal")
        return jsonify({'success': True, 'message': 'Empresa principal definida com sucesso'})

    except Exception as e:
        logger.error(f"Erro ao definir empresa principal: {e}")
        return jsonify({'success': False, 'message': str(e)})

@empresa_principal_bp.route('/definir-principal', methods=['POST'])
def definir_principal_form():
    """Definir uma empresa como principal via formulário"""
    try:
        # Verificação simples de sessão
        if 'usuario' not in session:
            return redirect(url_for('login'))
        empresa_id = request.form.get('empresa_id')

        if not empresa_id:
            flash('ID da empresa não informado', 'error')
            return redirect(url_for('empresa_principal.index'))

        if definir_empresa_principal(empresa_id):
            flash('Empresa principal definida com sucesso!', 'success')
        else:
            flash('Erro ao definir empresa principal', 'error')

        return redirect(url_for('empresa_principal.index'))

    except Exception as e:
        logger.error(f"Erro ao definir empresa principal: {e}")
        flash('Erro interno do servidor', 'error')
        return redirect(url_for('empresa_principal.index'))

@empresa_principal_bp.route('/definir/<int:empresa_id>', methods=['POST'])
def definir_principal_old(empresa_id):
    """Definir uma empresa como principal"""
    try:
        if definir_empresa_principal(empresa_id):
            flash('Empresa principal definida com sucesso!', 'success')
        else:
            flash('Erro ao definir empresa principal', 'error')
            
    except Exception as e:
        logger.error(f"Erro ao definir empresa principal: {e}")
        flash('Erro interno do servidor', 'error')
    
    return redirect(url_for('empresa_principal.index'))



@empresa_principal_bp.route('/clientes')
@require_admin
def clientes():
    """Listar todos os clientes da empresa principal"""
    try:
        logger.info("=== INICIANDO LISTAGEM DE CLIENTES ===")

        # Buscar empresa principal diretamente do banco
        db = DatabaseManager()
        logger.info("✅ Conexão com banco estabelecida")

        # Query simplificada para empresa principal
        logger.info("🔍 Buscando empresa principal...")
        empresa_result = db.execute_query("""
            SELECT id, razao_social, nome_fantasia, cnpj, telefone, email
            FROM empresas
            WHERE empresa_principal = TRUE AND ativa = TRUE
            LIMIT 1
        """)
        logger.info(f"📊 Resultado empresa principal: {len(empresa_result) if empresa_result else 0} registros")

        if not empresa_result:
            logger.warning("⚠️ Nenhuma empresa principal encontrada")
            flash('Nenhuma empresa principal foi definida. Configure primeiro a empresa principal.', 'warning')
            return redirect(url_for('empresa_principal.index'))

        empresa_principal = empresa_result[0]
        logger.info(f"✅ Empresa principal: {empresa_principal.get('razao_social', 'N/A')}")

        # ✅ CORREÇÃO: Buscar clientes com funcionários alocados + cadastrados
        logger.info("🔍 Buscando clientes da empresa principal...")
        clientes_result = db.execute_query("""
            SELECT ec.*, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email,
                   COALESCE(funcionarios_alocados.total, 0) + COALESCE(funcionarios_cadastrados.total, 0) as funcionarios_alocados
            FROM empresa_clientes ec
            INNER JOIN empresas e ON ec.empresa_cliente_id = e.id

            -- Funcionários alocados via tabela funcionario_alocacoes
            LEFT JOIN (
                SELECT fa.empresa_cliente_id, COUNT(DISTINCT fa.funcionario_id) as total
                FROM funcionario_alocacoes fa
                WHERE fa.ativo = TRUE
                GROUP BY fa.empresa_cliente_id
            ) funcionarios_alocados ON ec.empresa_cliente_id = funcionarios_alocados.empresa_cliente_id

            -- Funcionários cadastrados diretamente na empresa
            LEFT JOIN (
                SELECT f.empresa_id, COUNT(DISTINCT f.id) as total
                FROM funcionarios f
                WHERE f.status_cadastro = 'Ativo'
                GROUP BY f.empresa_id
            ) funcionarios_cadastrados ON ec.empresa_cliente_id = funcionarios_cadastrados.empresa_id

            WHERE ec.empresa_principal_id = %s AND ec.ativo = TRUE
            ORDER BY e.razao_social
        """, (empresa_principal['id'],))

        clientes = clientes_result if clientes_result else []
        logger.info(f"📊 Clientes encontrados: {len(clientes)}")

        # Buscar empresas disponíveis para adicionar como clientes
        logger.info("🔍 Buscando empresas disponíveis...")
        empresas_result = db.execute_query("""
            SELECT e.id, e.razao_social, e.nome_fantasia, e.cnpj
            FROM empresas e
            WHERE e.ativa = TRUE
            AND e.empresa_principal = FALSE
            AND e.id NOT IN (
                SELECT DISTINCT ec.empresa_cliente_id
                FROM empresa_clientes ec
                WHERE ec.empresa_principal_id = %s AND ec.ativo = TRUE
            )
            ORDER BY e.razao_social
        """, (empresa_principal['id'],))

        empresas_disponiveis = empresas_result if empresas_result else []
        logger.info(f"📊 Empresas disponíveis: {len(empresas_disponiveis)}")

        # Calcular estatísticas para o template
        total_clientes = len(clientes)
        clientes_ativos = len([c for c in clientes if c.get('status_contrato') == 'ativo'])
        funcionarios_alocados = sum([c.get('funcionarios_alocados', 0) for c in clientes])

        stats = {
            'total_clientes': total_clientes,
            'clientes_ativos': clientes_ativos,
            'funcionarios_alocados': funcionarios_alocados
        }

        # Atualizar empresa_principal com estatísticas
        empresa_principal['total_clientes'] = total_clientes
        empresa_principal['funcionarios_alocados'] = funcionarios_alocados

        logger.info(f"📊 Estatísticas calculadas: {stats}")
        logger.info("✅ Renderizando template clientes.html")
        return render_template('empresa_principal/clientes.html',
                             empresa_principal=empresa_principal,
                             clientes=clientes,
                             empresas_disponiveis=empresas_disponiveis,
                             stats=stats)

    except Exception as e:
        logger.error(f"❌ ERRO na listagem de clientes: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        flash(f'Erro ao carregar lista de clientes: {str(e)}', 'error')
        return redirect(url_for('empresa_principal.index'))

@empresa_principal_bp.route('/clientes/adicionar', methods=['POST'])
def adicionar_cliente_route():
    """Adicionar novo cliente"""
    try:
        logger.info("=== INICIANDO ADIÇÃO DE CLIENTE ===")

        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            logger.error("❌ Empresa principal não definida")
            return jsonify({'success': False, 'message': 'Empresa principal não definida'})

        logger.info(f"✅ Empresa principal: {empresa_principal['razao_social']}")

        dados = {
            'empresa_cliente_id': request.form.get('empresa_cliente_id'),
            'nome_contrato': request.form.get('nome_contrato'),
            'codigo_contrato': request.form.get('codigo_contrato'),
            'descricao_projeto': request.form.get('descricao_projeto'),
            'data_inicio': request.form.get('data_inicio'),
            'data_fim': request.form.get('data_fim') if request.form.get('data_fim') else None,
            'valor_contrato': request.form.get('valor_contrato') if request.form.get('valor_contrato') else None,
            'status_contrato': request.form.get('status_contrato', 'ativo'),
            'observacoes': request.form.get('observacoes')
        }

        logger.info(f"📊 Dados recebidos: {dados}")

        # Validações
        if not dados['empresa_cliente_id'] or not dados['data_inicio']:
            logger.error("❌ Validação falhou: empresa_cliente_id ou data_inicio vazios")
            return jsonify({'success': False, 'message': 'Empresa e data de início são obrigatórios'})

        logger.info("✅ Validações passaram, tentando adicionar cliente...")

        if adicionar_cliente(empresa_principal['id'], dados):
            logger.info("✅ Cliente adicionado com sucesso!")
            return jsonify({'success': True, 'message': 'Cliente adicionado com sucesso!'})
        else:
            logger.error("❌ Falha na função adicionar_cliente")
            return jsonify({'success': False, 'message': 'Erro ao adicionar cliente'})

    except Exception as e:
        logger.error(f"❌ ERRO GERAL ao adicionar cliente: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'Erro interno: {str(e)}'})

# ================================================================
# API ENDPOINTS
# ================================================================

@empresa_principal_bp.route('/api/empresa-principal')
@require_admin
def api_empresa_principal():
    """API: Dados da empresa principal"""
    try:
        empresa = get_empresa_principal()
        if empresa:
            # Converter tipos não serializáveis
            for key, value in empresa.items():
                if isinstance(value, (date, datetime)):
                    empresa[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    empresa[key] = float(value)
            
        return jsonify({'success': True, 'data': empresa})
    except Exception as e:
        logger.error(f"Erro na API empresa principal: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/api/clientes')
@require_admin
def api_clientes():
    """API: Lista de clientes"""
    try:
        clientes = get_clientes_da_empresa_principal()
        
        # Converter tipos não serializáveis
        for cliente in clientes:
            for key, value in cliente.items():
                if isinstance(value, (date, datetime)):
                    cliente[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    cliente[key] = float(value)
        
        return jsonify({'success': True, 'data': clientes})
    except Exception as e:
        logger.error(f"Erro na API clientes: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/api/funcionarios-disponiveis')
def get_funcionarios_disponiveis_api():
    """API: Funcionários disponíveis para alocação"""
    try:
        funcionarios = get_funcionarios_disponiveis()
        
        # Converter tipos não serializáveis
        for funcionario in funcionarios:
            for key, value in funcionario.items():
                if isinstance(value, (date, datetime)):
                    funcionario[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    funcionario[key] = float(value)
        
        return jsonify({'success': True, 'funcionarios': funcionarios})
    except Exception as e:
        logger.error(f"Erro na API funcionários: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/api/jornadas-disponiveis')
def get_jornadas_disponiveis():
    """API: Jornadas de trabalho disponíveis"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT
            j.id,
            j.nome_jornada as nome,
            j.descricao,
            j.tipo_jornada,
            j.categoria_funcionario,
            j.seg_qui_entrada,
            j.seg_qui_saida,
            j.sexta_entrada,
            j.sexta_saida,
            j.intervalo_inicio,
            j.intervalo_fim,
            j.ativa,
            j.padrao,
            e.razao_social as empresa_nome,
            CONCAT(
                TIME_FORMAT(j.seg_qui_entrada, '%%H:%%i'),
                ' às ',
                TIME_FORMAT(j.seg_qui_saida, '%%H:%%i')
            ) as carga_horaria
        FROM jornadas_trabalho j
        JOIN empresas e ON j.empresa_id = e.id
        WHERE j.ativa = TRUE
        ORDER BY e.razao_social, j.padrao DESC, j.nome_jornada
        """

        jornadas = db.execute_query(sql)

        # Converter tipos não serializáveis
        for jornada in jornadas:
            for key, value in jornada.items():
                if isinstance(value, (date, datetime)):
                    jornada[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    jornada[key] = float(value)
                elif hasattr(value, 'total_seconds'):  # timedelta
                    jornada[key] = str(value)
                elif value is None:
                    jornada[key] = None

        return jsonify({'success': True, 'jornadas': jornadas})
    except Exception as e:
        logger.error(f"Erro na API jornadas: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/api/empresas-disponiveis')
def get_empresas_disponiveis():
    """API: Empresas disponíveis para se tornarem clientes"""
    try:
        empresas = get_empresas_disponiveis_para_cliente()

        # Converter tipos não serializáveis
        for empresa in empresas:
            for key, value in empresa.items():
                if isinstance(value, (date, datetime)):
                    empresa[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    empresa[key] = float(value)

        return jsonify({'success': True, 'empresas': empresas})
    except Exception as e:
        logger.error(f"Erro na API empresas disponíveis: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/api/clientes-para-alocacao')
def get_clientes_para_alocacao():
    """API: Clientes disponíveis para alocação de funcionários"""
    try:
        clientes = get_clientes_da_empresa_principal()

        # Converter tipos não serializáveis
        for cliente in clientes:
            for key, value in cliente.items():
                if isinstance(value, (date, datetime)):
                    cliente[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    cliente[key] = float(value)

        return jsonify({'success': True, 'clientes': clientes})
    except Exception as e:
        logger.error(f"Erro na API clientes para alocação: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/clientes/<int:cliente_id>/detalhes')
@require_admin
def detalhes_cliente(cliente_id):
    """Detalhes completos de um cliente"""
    try:
        logger.info(f"🔍 Buscando detalhes do cliente ID: {cliente_id}")
        db = DatabaseManager()

        # ✅ CORREÇÃO: Buscar dados do cliente sem campos inexistentes
        sql_cliente = """
        SELECT ec.*, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email
        FROM empresa_clientes ec
        JOIN empresas e ON ec.empresa_cliente_id = e.id
        WHERE ec.id = %s
        """

        logger.info(f"📊 Executando query para cliente: {sql_cliente}")
        cliente = db.execute_query(sql_cliente, (cliente_id,))

        if not cliente:
            logger.warning(f"❌ Cliente {cliente_id} não encontrado")
            return jsonify({'success': False, 'message': 'Cliente não encontrado'})

        cliente = cliente[0]
        logger.info(f"✅ Cliente encontrado: {cliente['razao_social']}")

        # ✅ CORREÇÃO: Buscar funcionários alocados + funcionários cadastrados na empresa
        sql_funcionarios = """
        SELECT
            f.id as funcionario_id,
            f.nome_completo as nome,
            f.cargo,
            f.cpf,
            f.telefone1 as telefone,
            COALESCE(jt_alocacao.nome_jornada, jt_empresa.nome_jornada) as jornada_nome,
            -- ✅ CORREÇÃO: Calcular carga horária baseada nos horários
            CASE
                WHEN COALESCE(jt_alocacao.seg_qui_entrada, jt_empresa.seg_qui_entrada) IS NOT NULL
                     AND COALESCE(jt_alocacao.seg_qui_saida, jt_empresa.seg_qui_saida) IS NOT NULL
                THEN ROUND(
                    TIME_TO_SEC(TIMEDIFF(
                        COALESCE(jt_alocacao.seg_qui_saida, jt_empresa.seg_qui_saida),
                        COALESCE(jt_alocacao.seg_qui_entrada, jt_empresa.seg_qui_entrada)
                    )) / 3600, 1
                )
                ELSE NULL
            END as carga_horaria,
            CASE
                WHEN fa.id IS NOT NULL THEN 'Alocado'
                ELSE 'Funcionário da Empresa'
            END as tipo_vinculo,
            fa.data_inicio,
            fa.data_fim,
            fa.percentual_alocacao,
            fa.valor_hora,
            fa.ativo as alocacao_ativa
        FROM funcionarios f

        -- Alocações ativas (se existir)
        LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id
            AND fa.empresa_cliente_id = %s AND fa.ativo = TRUE

        -- Jornada da alocação
        LEFT JOIN jornadas_trabalho jt_alocacao ON fa.jornada_trabalho_id = jt_alocacao.id

        -- Jornada da empresa (fallback)
        LEFT JOIN jornadas_trabalho jt_empresa ON f.jornada_trabalho_id = jt_empresa.id

        WHERE (
            -- Funcionários alocados para esta empresa
            fa.empresa_cliente_id = %s AND fa.ativo = TRUE
        ) OR (
            -- Funcionários cadastrados diretamente nesta empresa
            f.empresa_id = %s AND f.status_cadastro = 'Ativo'
        )

        ORDER BY f.nome_completo
        """

        funcionarios = db.execute_query(sql_funcionarios, (
            cliente['empresa_cliente_id'],
            cliente['empresa_cliente_id'],
            cliente['empresa_cliente_id']
        ))

        # Renderizar HTML dos detalhes
        html = render_template('empresa_principal/detalhes_cliente_modal.html',
                             cliente=cliente, funcionarios=funcionarios)

        return jsonify({'success': True, 'html': html})

    except Exception as e:
        logger.error(f"Erro ao buscar detalhes do cliente: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do servidor'})

@empresa_principal_bp.route('/clientes/<int:cliente_id>/dados')
@require_admin
def get_cliente_dados(cliente_id):
    """Obter dados do cliente para edição"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT ec.*, e.razao_social, e.nome_fantasia, e.cnpj
        FROM empresa_clientes ec
        JOIN empresas e ON ec.empresa_cliente_id = e.id
        WHERE ec.id = %s
        """

        cliente = db.execute_query(sql, (cliente_id,), fetch_one=True)

        if not cliente:
            return jsonify({'success': False, 'message': 'Cliente não encontrado'})

        # Converter datas para string para JSON
        if cliente.get('data_inicio'):
            cliente['data_inicio'] = cliente['data_inicio'].strftime('%Y-%m-%d')
        if cliente.get('data_fim'):
            cliente['data_fim'] = cliente['data_fim'].strftime('%Y-%m-%d')

        return jsonify({'success': True, 'cliente': cliente})

    except Exception as e:
        logger.error(f"Erro ao obter dados do cliente: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do servidor'})

@empresa_principal_bp.route('/clientes/<int:cliente_id>/editar', methods=['POST'])
@require_admin
def editar_cliente(cliente_id):
    """Editar dados do cliente"""
    try:
        dados = {
            'nome_contrato': request.form.get('nome_contrato'),
            'codigo_contrato': request.form.get('codigo_contrato'),
            'descricao_projeto': request.form.get('descricao_projeto'),
            'data_inicio': request.form.get('data_inicio'),
            'data_fim': request.form.get('data_fim') if request.form.get('data_fim') else None,
            'valor_contrato': request.form.get('valor_contrato') if request.form.get('valor_contrato') else None,
            'status_contrato': request.form.get('status_contrato', 'ativo'),
            'observacoes': request.form.get('observacoes')
        }

        # Validação
        if not dados['data_inicio']:
            return jsonify({'success': False, 'message': 'Data de início é obrigatória'})

        # Atualizar cliente
        if editar_cliente_dados(cliente_id, dados):
            return jsonify({'success': True, 'message': 'Cliente editado com sucesso!'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao editar cliente'})

    except Exception as e:
        logger.error(f"Erro ao editar cliente: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do servidor'})

@empresa_principal_bp.route('/clientes/<int:cliente_id>/status', methods=['POST'])
@require_admin
def alterar_status_contrato(cliente_id):
    """Alterar status do contrato (pausar/reativar)"""
    try:
        data = request.get_json()
        novo_status = data.get('status')

        if novo_status not in ['ativo', 'pausado', 'finalizado', 'cancelado']:
            return jsonify({'success': False, 'message': 'Status inválido'})

        db = DatabaseManager()

        sql = """
        UPDATE empresa_clientes SET
            status_contrato = %s,
            updated_at = NOW()
        WHERE id = %s
        """

        result = db.execute_query(sql, (novo_status, cliente_id), fetch_all=False)

        logger.info(f"✅ Status do cliente {cliente_id} alterado para: {novo_status}")
        return jsonify({'success': True, 'message': f'Status alterado para {novo_status} com sucesso!'})

    except Exception as e:
        logger.error(f"Erro ao alterar status do cliente: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do servidor'})

@empresa_principal_bp.route('/funcionarios/alocar', methods=['POST'])
def alocar_funcionario_route():
    """Alocar funcionário a um cliente"""
    try:
        dados = {
            'funcionario_id': request.form.get('funcionario_id'),
            'empresa_cliente_id': request.form.get('empresa_cliente_id'),
            'jornada_trabalho_id': request.form.get('jornada_trabalho_id'),
            'cargo_no_cliente': request.form.get('cargo_no_cliente'),
            'data_inicio': request.form.get('data_inicio'),
            'data_fim': request.form.get('data_fim') if request.form.get('data_fim') else None,
            'percentual_alocacao': request.form.get('percentual_alocacao', 100),
            'valor_hora': request.form.get('valor_hora') if request.form.get('valor_hora') else None,
            'observacoes': request.form.get('observacoes')
        }

        # Validações
        if not all([dados['funcionario_id'], dados['empresa_cliente_id'],
                   dados['jornada_trabalho_id'], dados['data_inicio']]):
            return jsonify({'success': False, 'message': 'Campos obrigatórios não preenchidos'})

        db = DatabaseManager()

        # Verificar se funcionário já está alocado para este cliente
        sql_check = """
        SELECT id FROM funcionario_alocacoes
        WHERE funcionario_id = %s AND empresa_cliente_id = %s
        AND ativo = 1 AND (data_fim IS NULL OR data_fim >= CURDATE())
        """

        existing = db.execute_query(sql_check, (dados['funcionario_id'], dados['empresa_cliente_id']))
        if existing:
            return jsonify({'success': False, 'message': 'Funcionário já está alocado para este cliente'})

        # Inserir alocação
        sql_insert = """
        INSERT INTO funcionario_alocacoes
        (funcionario_id, empresa_cliente_id, jornada_trabalho_id, cargo_no_cliente,
         data_inicio, data_fim, percentual_alocacao, valor_hora, observacoes, ativo)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, 1)
        """

        params = (
            dados['funcionario_id'], dados['empresa_cliente_id'], dados['jornada_trabalho_id'],
            dados['cargo_no_cliente'], dados['data_inicio'], dados['data_fim'],
            dados['percentual_alocacao'], dados['valor_hora'], dados['observacoes']
        )

        result = db.execute_query(sql_insert, params, fetch_all=False)
        if result is not None:
            return jsonify({'success': True, 'message': 'Funcionário alocado com sucesso!'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao alocar funcionário'})

    except Exception as e:
        logger.error(f"Erro ao alocar funcionário: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/clientes/alterar-status', methods=['POST'])
@require_admin
def alterar_status_cliente():
    """Alterar status de um contrato"""
    try:
        data = request.get_json()
        cliente_id = data.get('cliente_id')
        novo_status = data.get('status')

        if not cliente_id or not novo_status:
            return jsonify({'success': False, 'message': 'Dados incompletos'})

        if novo_status not in ['ativo', 'pausado', 'finalizado', 'cancelado']:
            return jsonify({'success': False, 'message': 'Status inválido'})

        db = DatabaseManager()

        sql = """
        UPDATE empresa_clientes
        SET status_contrato = %s, updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """

        if db.execute_update(sql, (novo_status, cliente_id)):
            return jsonify({'success': True, 'message': f'Status alterado para {novo_status}'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao alterar status'})

    except Exception as e:
        logger.error(f"Erro ao alterar status: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/funcionarios')
@require_admin
def funcionarios():
    """Página de gestão de funcionários da empresa principal"""
    try:
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            flash('Empresa principal não definida', 'error')
            return redirect(url_for('empresa_principal.index'))

        # Buscar funcionários da empresa principal
        funcionarios = get_funcionarios_empresa_principal()
        jornadas = get_jornadas_empresa_principal()

        # Calcular estatísticas
        stats = {
            'total_funcionarios': len(funcionarios) if funcionarios else 0,
            'funcionarios_ativos': len([f for f in funcionarios if f.get('status_cadastro') == 'Ativo']) if funcionarios else 0,
            'jornadas_disponiveis': len(jornadas) if jornadas else 0
        }

        context = {
            'titulo': 'Gestão de Funcionários',
            'empresa_principal': empresa_principal,
            'funcionarios': funcionarios,
            'jornadas': jornadas,
            'stats': stats
        }

        return render_template('empresa_principal/funcionarios.html', **context)

    except Exception as e:
        import traceback
        logger.error(f"❌ ERRO DETALHADO NA PÁGINA DE FUNCIONÁRIOS:")
        logger.error(f"   Tipo do erro: {type(e).__name__}")
        logger.error(f"   Mensagem: {str(e)}")
        logger.error(f"   Traceback completo: {traceback.format_exc()}")

        flash('Erro ao carregar funcionários - verifique os logs para detalhes', 'error')
        return redirect(url_for('empresa_principal.index'))

def get_funcionarios_empresa_principal():
    """Obter todos os funcionários da empresa principal"""
    try:
        db = DatabaseManager()

        # Buscar empresa principal
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            return []

        sql = """
        SELECT f.*,
               e.razao_social as empresa_nome,
               jt.nome_jornada as jornada_nome,
               jt.seg_qui_entrada, jt.seg_qui_saida,
               jt.sexta_entrada, jt.sexta_saida,
               CASE
                   WHEN f.status_cadastro = 'Ativo' THEN 'Ativo'
                   ELSE 'Inativo'
               END as status_display,
               DATE_FORMAT(f.data_admissao, '%%d/%%m/%%Y') as data_admissao_formatada,
               DATEDIFF(CURDATE(), f.data_admissao) as dias_empresa
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE e.empresa_principal = TRUE
        ORDER BY f.nome_completo
        """

        return db.execute_query(sql)

    except Exception as e:
        logger.error(f"Erro ao buscar funcionários da empresa principal: {e}")
        return []

def get_jornadas_empresa_principal():
    """Obter jornadas de trabalho da empresa principal"""
    try:
        logger.info("🔍 Iniciando busca de jornadas da empresa principal")

        # Buscar empresa principal
        empresa_principal = get_empresa_principal()
        logger.info(f"🔍 Empresa principal: {empresa_principal}")

        if not empresa_principal:
            logger.warning("⚠️ Empresa principal não encontrada")
            return []

        # SQL simplificado para debug
        sql = """
        SELECT jt.id, jt.nome_jornada, jt.seg_qui_entrada, jt.seg_qui_saida,
               jt.sexta_entrada, jt.sexta_saida, jt.padrao, jt.ativa
        FROM jornadas_trabalho jt
        WHERE jt.empresa_id = %s AND jt.ativa = TRUE
        ORDER BY jt.padrao DESC, jt.nome_jornada
        """

        logger.info(f"🔍 Executando SQL para empresa ID: {empresa_principal['id']}")

        db = DatabaseManager()
        result = db.execute_query(sql, (empresa_principal['id'],))

        logger.info(f"🔍 Resultado da consulta: {result}")

        # Adicionar campo funcionarios_vinculados como 0 para compatibilidade
        if result:
            for jornada in result:
                jornada['funcionarios_vinculados'] = 0

        return result

    except Exception as e:
        logger.error(f"❌ Erro ao buscar jornadas da empresa principal: {e}")
        import traceback
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        return []

@empresa_principal_bp.route('/api/test')
def api_test():
    """API de teste"""
    return jsonify({'success': True, 'message': 'API funcionando', 'endpoint': request.endpoint})

@empresa_principal_bp.route('/api/jornadas')
def api_jornadas_empresa_principal():
    """API: Listar jornadas da empresa principal"""
    try:
        logger.info(f"🔍 API jornadas chamada - endpoint: {request.endpoint}")

        # Buscar dados diretamente do banco para debug
        db = DatabaseManager()
        sql = """
        SELECT id, nome_jornada, seg_qui_entrada, seg_qui_saida,
               sexta_entrada, sexta_saida, intervalo_inicio, intervalo_fim,
               tolerancia_entrada_minutos, padrao, ativa
        FROM jornadas_trabalho
        WHERE empresa_id = 4 AND ativa = TRUE
        ORDER BY padrao DESC, nome_jornada
        """

        jornadas = db.execute_query(sql)
        logger.info(f"API jornadas: encontradas {len(jornadas) if jornadas else 0} jornadas")
        logger.info(f"Dados das jornadas: {jornadas}")

        # Adicionar campo funcionarios_vinculados
        if jornadas:
            for jornada in jornadas:
                jornada['funcionarios_vinculados'] = 0

        return jsonify({
            'success': True,
            'jornadas': jornadas or []
        })

    except Exception as e:
        logger.error(f"Erro na API de jornadas da empresa principal: {e}")
        import traceback
        logger.error(f"Traceback completo: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': 'Erro interno do servidor'
        }), 500

@empresa_principal_bp.route('/api/jornadas/criar', methods=['POST'])
def api_criar_jornada_empresa_principal():
    """API: Criar nova jornada para a empresa principal"""
    try:
        dados = request.get_json()

        # Buscar empresa principal
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            return jsonify({
                'success': False,
                'message': 'Empresa principal não encontrada'
            }), 404

        # Validações básicas
        if not dados.get('nome_jornada'):
            return jsonify({
                'success': False,
                'message': 'Nome da jornada é obrigatório'
            }), 400

        if not dados.get('seg_qui_entrada') or not dados.get('seg_qui_saida'):
            return jsonify({
                'success': False,
                'message': 'Horários de segunda a quinta são obrigatórios'
            }), 400

        db = DatabaseManager()

        # Se for padrão, remover padrão das outras jornadas
        if dados.get('padrao'):
            sql_remove_padrao = """
            UPDATE jornadas_trabalho
            SET padrao = 0
            WHERE empresa_id = %s
            """
            db.execute_update(sql_remove_padrao, (empresa_principal['id'],))

        # Inserir nova jornada
        sql_insert = """
        INSERT INTO jornadas_trabalho (
            empresa_id, nome_jornada, tipo_jornada, categoria_funcionario,
            seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
            intervalo_inicio, intervalo_fim, intervalo_obrigatorio,
            tolerancia_entrada_minutos, ativa, padrao, cadastrado_por
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        params = (
            empresa_principal['id'],
            dados['nome_jornada'],
            dados.get('tipo_jornada', 'Diurno'),
            dados.get('categoria_funcionario', 'Geral'),
            dados['seg_qui_entrada'],
            dados['seg_qui_saida'],
            dados.get('sexta_entrada') or dados['seg_qui_entrada'],
            dados.get('sexta_saida') or dados['seg_qui_saida'],
            dados.get('intervalo_inicio', '12:00:00'),
            dados.get('intervalo_fim', '13:00:00'),
            1 if dados.get('intervalo_obrigatorio') else 0,
            int(dados.get('tolerancia_entrada_minutos', 5)),
            1 if dados.get('ativa') else 0,
            1 if dados.get('padrao') else 0,
            'admin'
        )

        jornada_id = db.execute_insert(sql_insert, params)

        logger.info(f"✅ Jornada '{dados['nome_jornada']}' criada para empresa principal (ID: {jornada_id})")

        return jsonify({
            'success': True,
            'message': 'Jornada criada com sucesso',
            'jornada_id': jornada_id
        })

    except Exception as e:
        logger.error(f"Erro ao criar jornada da empresa principal: {e}")
        return jsonify({
            'success': False,
            'message': 'Erro interno do servidor'
        }), 500

@empresa_principal_bp.route('/alocacoes/alterar-status', methods=['POST'])
@require_admin
def alterar_status_alocacao():
    """Alterar status de uma alocação"""
    try:
        data = request.get_json()
        alocacao_id = data.get('alocacao_id')
        ativo = data.get('ativo')

        if alocacao_id is None or ativo is None:
            return jsonify({'success': False, 'message': 'Dados incompletos'})

        db = DatabaseManager()

        sql = """
        UPDATE funcionario_alocacoes
        SET ativo = %s, updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """

        result = db.execute_query(sql, (ativo, alocacao_id), fetch_all=False)
        if result is not None:
            status_texto = 'ativada' if ativo else 'desativada'
            return jsonify({'success': True, 'message': f'Alocação {status_texto} com sucesso'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao alterar status da alocação'})

    except Exception as e:
        logger.error(f"Erro ao alterar status da alocação: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/jornadas-clientes')
def jornadas_clientes():
    """Página de gestão de jornadas por cliente"""
    try:
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            flash('Empresa principal não definida', 'error')
            return redirect(url_for('empresa_principal.index'))

        # Buscar clientes e suas jornadas
        clientes_jornadas = get_clientes_com_jornadas()
        clientes_disponiveis = get_clientes_da_empresa_principal()

        # Calcular estatísticas
        stats = {
            'total_clientes': len(clientes_jornadas) if clientes_jornadas else 0,
            'total_jornadas': sum([len(c.get('jornadas', [])) for c in clientes_jornadas]) if clientes_jornadas else 0,
            'funcionarios_afetados': sum([c.get('funcionarios_alocados', 0) for c in clientes_jornadas]) if clientes_jornadas else 0
        }

        context = {
            'titulo': 'Jornadas por Cliente',
            'empresa_principal': empresa_principal,
            'clientes_jornadas': clientes_jornadas,
            'clientes_disponiveis': clientes_disponiveis,
            'stats': stats
        }

        return render_template('empresa_principal/jornadas_clientes.html', **context)

    except Exception as e:
        logger.error(f"Erro na página de jornadas por cliente: {e}")
        flash('Erro ao carregar jornadas', 'error')
        return redirect(url_for('empresa_principal.index'))

def get_clientes_com_jornadas():
    """Obter clientes com suas jornadas configuradas"""
    try:
        db = DatabaseManager()

        # Buscar clientes
        sql_clientes = """
        SELECT DISTINCT ec.empresa_cliente_id, e.razao_social, e.nome_fantasia, e.cnpj,
               COUNT(fa.id) as funcionarios_alocados
        FROM empresa_clientes ec
        JOIN empresas e ON ec.empresa_cliente_id = e.id
        LEFT JOIN funcionario_alocacoes fa ON ec.empresa_cliente_id = fa.empresa_cliente_id AND fa.ativo = 1
        GROUP BY ec.empresa_cliente_id, e.razao_social, e.nome_fantasia, e.cnpj
        ORDER BY e.razao_social
        """

        clientes = db.execute_query(sql_clientes)

        # Para cada cliente, buscar suas jornadas
        for cliente in clientes:
            # Jornadas específicas do cliente
            sql_jornadas = """
            SELECT j.*, COUNT(fa.id) as funcionarios_usando
            FROM jornadas_trabalho j
            LEFT JOIN funcionario_alocacoes fa ON j.id = fa.jornada_trabalho_id
                AND fa.empresa_cliente_id = %s AND fa.ativo = 1
            WHERE j.empresa_id = %s
            GROUP BY j.id
            ORDER BY j.padrao DESC, j.nome_jornada
            """

            jornadas = db.execute_query(sql_jornadas, (cliente['empresa_cliente_id'], cliente['empresa_cliente_id']))
            cliente['jornadas'] = jornadas if jornadas else []

            # Resumo dos funcionários alocados
            sql_funcionarios = """
            SELECT f.nome_completo as nome, fa.jornada_trabalho_id, j.nome_jornada,
                   CASE WHEN fa.jornada_trabalho_id IS NOT NULL THEN 1 ELSE 0 END as jornada_aplicada
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            LEFT JOIN jornadas_trabalho j ON fa.jornada_trabalho_id = j.id
            WHERE fa.empresa_cliente_id = %s AND fa.ativo = 1
            ORDER BY f.nome
            """

            funcionarios = db.execute_query(sql_funcionarios, (cliente['empresa_cliente_id'],))
            cliente['funcionarios_resumo'] = funcionarios if funcionarios else []

        return clientes

    except Exception as e:
        logger.error(f"Erro ao buscar clientes com jornadas: {e}")
        return []

@empresa_principal_bp.route('/jornadas/criar', methods=['POST'])
@require_admin
def criar_jornada_cliente():
    """Criar nova jornada para um cliente"""
    try:
        dados = {
            'empresa_cliente_id': request.form.get('empresa_cliente_id'),
            'nome_jornada': request.form.get('nome_jornada'),
            'tipo_jornada': request.form.get('tipo_jornada', 'Diurno'),
            'categoria_funcionario': request.form.get('categoria_funcionario'),
            'seg_qui_entrada': request.form.get('seg_qui_entrada'),
            'seg_qui_saida': request.form.get('seg_qui_saida'),
            'sexta_entrada': request.form.get('sexta_entrada') if request.form.get('sexta_entrada') else None,
            'sexta_saida': request.form.get('sexta_saida') if request.form.get('sexta_saida') else None,
            'sabado_entrada': request.form.get('sabado_entrada') if request.form.get('sabado_entrada') else None,
            'sabado_saida': request.form.get('sabado_saida') if request.form.get('sabado_saida') else None,
            'intervalo_inicio': request.form.get('intervalo_inicio') if request.form.get('intervalo_inicio') else None,
            'intervalo_fim': request.form.get('intervalo_fim') if request.form.get('intervalo_fim') else None,
            'tolerancia_entrada_minutos': request.form.get('tolerancia_entrada_minutos', 15),
            'descricao': request.form.get('descricao'),
            'padrao': 1 if request.form.get('padrao') else 0,
            'aplicar_automaticamente': 1 if request.form.get('aplicar_automaticamente') else 0
        }

        # Validações
        if not all([dados['empresa_cliente_id'], dados['nome_jornada'],
                   dados['seg_qui_entrada'], dados['seg_qui_saida']]):
            return jsonify({'success': False, 'message': 'Campos obrigatórios não preenchidos'})

        db = DatabaseManager()

        # Se for padrão, remover padrão das outras jornadas do cliente
        if dados['padrao']:
            sql_remove_padrao = """
            UPDATE jornadas_trabalho
            SET padrao = 0
            WHERE empresa_id = %s
            """
            db.execute_update(sql_remove_padrao, (dados['empresa_cliente_id'],))

        # Inserir nova jornada
        sql_insert = """
        INSERT INTO jornadas_trabalho
        (empresa_id, nome_jornada, tipo_jornada, categoria_funcionario, descricao,
         seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
         sabado_entrada, sabado_saida, intervalo_inicio, intervalo_fim,
         tolerancia_entrada_minutos, padrao, ativa)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 1)
        """

        params = (
            dados['empresa_cliente_id'], dados['nome_jornada'], dados['tipo_jornada'],
            dados['categoria_funcionario'], dados['descricao'], dados['seg_qui_entrada'],
            dados['seg_qui_saida'], dados['sexta_entrada'], dados['sexta_saida'],
            dados['sabado_entrada'], dados['sabado_saida'], dados['intervalo_inicio'],
            dados['intervalo_fim'], dados['tolerancia_entrada_minutos'], dados['padrao']
        )

        jornada_id = db.execute_insert(sql_insert, params)

        if jornada_id:
            # Se deve aplicar automaticamente, aplicar aos funcionários alocados
            if dados['aplicar_automaticamente']:
                aplicar_jornada_aos_funcionarios(dados['empresa_cliente_id'], jornada_id)

            return jsonify({'success': True, 'message': 'Jornada criada com sucesso!', 'jornada_id': jornada_id})
        else:
            return jsonify({'success': False, 'message': 'Erro ao criar jornada'})

    except Exception as e:
        logger.error(f"Erro ao criar jornada: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/jornadas/aplicar', methods=['POST'])
@require_admin
def aplicar_jornada_cliente():
    """Aplicar jornada específica aos funcionários de um cliente"""
    try:
        data = request.get_json()
        cliente_id = data.get('cliente_id')
        jornada_id = data.get('jornada_id')

        if not cliente_id or not jornada_id:
            return jsonify({'success': False, 'message': 'Dados incompletos'})

        funcionarios_afetados = aplicar_jornada_aos_funcionarios(cliente_id, jornada_id)

        return jsonify({
            'success': True,
            'message': f'Jornada aplicada a {funcionarios_afetados} funcionários',
            'funcionarios_afetados': funcionarios_afetados
        })

    except Exception as e:
        logger.error(f"Erro ao aplicar jornada: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

def aplicar_jornada_aos_funcionarios(cliente_id, jornada_id):
    """Aplicar jornada aos funcionários alocados de um cliente"""
    try:
        db = DatabaseManager()

        # Atualizar alocações ativas do cliente
        sql_update = """
        UPDATE funcionario_alocacoes
        SET jornada_trabalho_id = %s, updated_at = CURRENT_TIMESTAMP
        WHERE empresa_cliente_id = %s AND ativo = 1
        """

        result = db.execute_query(sql_update, (jornada_id, cliente_id), fetch_all=False)

        # Contar funcionários afetados
        sql_count = """
        SELECT COUNT(*) as total
        FROM funcionario_alocacoes
        WHERE empresa_cliente_id = %s AND ativo = 1 AND jornada_trabalho_id = %s
        """

        count_result = db.execute_query(sql_count, (cliente_id, jornada_id))
        funcionarios_afetados = count_result[0]['total'] if count_result else 0

        return funcionarios_afetados

    except Exception as e:
        logger.error(f"Erro ao aplicar jornada aos funcionários: {e}")
        return 0

@empresa_principal_bp.route('/jornadas/aplicar-automaticamente', methods=['POST'])
@require_admin
def aplicar_jornadas_automaticamente():
    """Aplicar jornadas padrão automaticamente a todos os clientes"""
    try:
        db = DatabaseManager()

        # Buscar clientes com jornadas padrão
        sql_clientes_jornadas = """
        SELECT ec.empresa_cliente_id, j.id as jornada_id
        FROM empresa_clientes ec
        JOIN jornadas_trabalho j ON ec.empresa_cliente_id = j.empresa_id
        WHERE j.padrao = 1 AND j.ativa = 1
        """

        clientes_jornadas = db.execute_query(sql_clientes_jornadas)

        total_funcionarios = 0

        for item in clientes_jornadas:
            funcionarios_afetados = aplicar_jornada_aos_funcionarios(
                item['empresa_cliente_id'],
                item['jornada_id']
            )
            total_funcionarios += funcionarios_afetados

        return jsonify({
            'success': True,
            'message': f'Jornadas aplicadas automaticamente',
            'funcionarios_afetados': total_funcionarios,
            'clientes_processados': len(clientes_jornadas)
        })

    except Exception as e:
        logger.error(f"Erro ao aplicar jornadas automaticamente: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/jornadas/definir-padrao', methods=['POST'])
@require_admin
def definir_jornada_padrao():
    """Definir uma jornada como padrão para seu cliente"""
    try:
        data = request.get_json()
        jornada_id = data.get('jornada_id')

        if not jornada_id:
            return jsonify({'success': False, 'message': 'ID da jornada não informado'})

        db = DatabaseManager()

        # Buscar empresa da jornada
        sql_empresa = """
        SELECT empresa_id FROM jornadas_trabalho WHERE id = %s
        """

        empresa_result = db.execute_query(sql_empresa, (jornada_id,))
        if not empresa_result:
            return jsonify({'success': False, 'message': 'Jornada não encontrada'})

        empresa_id = empresa_result[0]['empresa_id']

        # Remover padrão das outras jornadas da mesma empresa
        sql_remove_padrao = """
        UPDATE jornadas_trabalho
        SET padrao = 0
        WHERE empresa_id = %s
        """
        db.execute_update(sql_remove_padrao, (empresa_id,))

        # Definir nova jornada padrão
        sql_set_padrao = """
        UPDATE jornadas_trabalho
        SET padrao = 1
        WHERE id = %s
        """

        if db.execute_update(sql_set_padrao, (jornada_id,)):
            return jsonify({'success': True, 'message': 'Jornada definida como padrão'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao definir jornada padrão'})

    except Exception as e:
        logger.error(f"Erro ao definir jornada padrão: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})



@empresa_principal_bp.route('/relatorios')
def relatorios():
    """Página de relatórios e análises"""
    try:
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            flash('Empresa principal não definida', 'error')
            return redirect(url_for('empresa_principal.index'))

        # Buscar dados para relatórios
        clientes = get_clientes_da_empresa_principal()
        alocacoes = get_todas_alocacoes()

        # Calcular estatísticas
        stats = calcular_estatisticas_relatorios()

        # Top clientes
        top_clientes = get_top_clientes()

        # Resumo financeiro
        resumo_financeiro = calcular_resumo_financeiro()

        # Dados para gráficos
        dados_graficos = gerar_dados_graficos()

        # Alertas
        alertas = gerar_alertas_sistema()

        context = {
            'titulo': 'Relatórios e Análises',
            'empresa_principal': empresa_principal,
            'clientes': clientes,
            'stats': stats,
            'top_clientes': top_clientes,
            'resumo_financeiro': resumo_financeiro,
            'dados_graficos': dados_graficos,
            'alertas': alertas
        }

        return render_template('empresa_principal/relatorios.html', **context)

    except Exception as e:
        logger.error(f"Erro na página de relatórios: {e}")
        flash('Erro ao carregar relatórios', 'error')
        return redirect(url_for('empresa_principal.index'))

def calcular_estatisticas_relatorios():
    """Calcular estatísticas para relatórios"""
    try:
        db = DatabaseManager()

        # Total de clientes
        sql_clientes = "SELECT COUNT(DISTINCT empresa_cliente_id) as total FROM empresa_clientes"
        result_clientes = db.execute_query(sql_clientes)
        total_clientes = result_clientes[0]['total'] if result_clientes else 0

        # Total de funcionários alocados
        sql_funcionarios = "SELECT COUNT(DISTINCT funcionario_id) as total FROM funcionario_alocacoes WHERE ativo = 1"
        result_funcionarios = db.execute_query(sql_funcionarios)
        total_funcionarios = result_funcionarios[0]['total'] if result_funcionarios else 0

        # Total de alocações ativas
        sql_alocacoes = "SELECT COUNT(*) as total FROM funcionario_alocacoes WHERE ativo = 1"
        result_alocacoes = db.execute_query(sql_alocacoes)
        total_alocacoes = result_alocacoes[0]['total'] if result_alocacoes else 0

        return {
            'total_clientes': total_clientes,
            'total_funcionarios': total_funcionarios,
            'total_alocacoes': total_alocacoes
        }

    except Exception as e:
        logger.error(f"Erro ao calcular estatísticas: {e}")
        return {'total_clientes': 0, 'total_funcionarios': 0, 'total_alocacoes': 0}

def get_top_clientes():
    """Obter top clientes por número de funcionários"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT e.razao_social, ec.status_contrato,
               COUNT(fa.funcionario_id) as funcionarios_alocados
        FROM empresa_clientes ec
        JOIN empresas e ON ec.empresa_cliente_id = e.id
        LEFT JOIN funcionario_alocacoes fa ON ec.empresa_cliente_id = fa.empresa_cliente_id AND fa.ativo = 1
        GROUP BY ec.empresa_cliente_id, e.razao_social, ec.status_contrato
        ORDER BY funcionarios_alocados DESC
        LIMIT 10
        """

        return db.execute_query(sql)

    except Exception as e:
        logger.error(f"Erro ao buscar top clientes: {e}")
        return []

def calcular_resumo_financeiro():
    """Calcular resumo financeiro"""
    try:
        db = DatabaseManager()

        # Valor total dos contratos
        sql_valor_total = """
        SELECT COALESCE(SUM(valor_contrato), 0) as total
        FROM empresa_clientes
        WHERE status_contrato = 'ativo' AND valor_contrato IS NOT NULL
        """
        result_total = db.execute_query(sql_valor_total)
        valor_total_contratos = float(result_total[0]['total']) if result_total else 0.0

        # Valor médio por hora
        sql_valor_hora = """
        SELECT AVG(valor_hora) as media
        FROM funcionario_alocacoes
        WHERE ativo = 1 AND valor_hora IS NOT NULL
        """
        result_hora = db.execute_query(sql_valor_hora)
        valor_medio_hora = float(result_hora[0]['media']) if result_hora and result_hora[0]['media'] else 0.0

        # Contratos ativos
        sql_contratos = """
        SELECT COUNT(*) as total
        FROM empresa_clientes
        WHERE status_contrato = 'ativo'
        """
        result_contratos = db.execute_query(sql_contratos)
        contratos_ativos = result_contratos[0]['total'] if result_contratos else 0

        # Estimativa de horas por mês (funcionários * 160 horas médias)
        sql_horas = """
        SELECT COUNT(*) * 160 as horas_estimadas
        FROM funcionario_alocacoes
        WHERE ativo = 1
        """
        result_horas = db.execute_query(sql_horas)
        horas_mes_estimadas = result_horas[0]['horas_estimadas'] if result_horas else 0

        return {
            'valor_total_contratos': valor_total_contratos,
            'valor_medio_hora': valor_medio_hora,
            'contratos_ativos': contratos_ativos,
            'horas_mes_estimadas': horas_mes_estimadas
        }

    except Exception as e:
        logger.error(f"Erro ao calcular resumo financeiro: {e}")
        return {
            'valor_total_contratos': 0.0,
            'valor_medio_hora': 0.0,
            'contratos_ativos': 0,
            'horas_mes_estimadas': 0
        }

def gerar_dados_graficos():
    """Gerar dados para os gráficos"""
    try:
        db = DatabaseManager()

        # Clientes por status
        sql_status = """
        SELECT status_contrato, COUNT(*) as total
        FROM empresa_clientes
        GROUP BY status_contrato
        """
        result_status = db.execute_query(sql_status)

        clientes_status = {
            'labels': [item['status_contrato'].title() for item in result_status] if result_status else [],
            'data': [item['total'] for item in result_status] if result_status else []
        }

        # Funcionários por cliente (top 10)
        sql_funcionarios = """
        SELECT e.razao_social, COUNT(fa.funcionario_id) as total
        FROM empresa_clientes ec
        JOIN empresas e ON ec.empresa_cliente_id = e.id
        LEFT JOIN funcionario_alocacoes fa ON ec.empresa_cliente_id = fa.empresa_cliente_id AND fa.ativo = 1
        GROUP BY ec.empresa_cliente_id, e.razao_social
        ORDER BY total DESC
        LIMIT 10
        """
        result_funcionarios = db.execute_query(sql_funcionarios)

        funcionarios_cliente = {
            'labels': [item['razao_social'][:20] + '...' if len(item['razao_social']) > 20 else item['razao_social'] for item in result_funcionarios] if result_funcionarios else [],
            'data': [item['total'] for item in result_funcionarios] if result_funcionarios else []
        }

        # Evolução de alocações (últimos 6 meses)
        sql_evolucao = """
        SELECT DATE_FORMAT(created_at, '%Y-%m') as mes, COUNT(*) as total
        FROM funcionario_alocacoes
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY mes
        """
        result_evolucao = db.execute_query(sql_evolucao)

        evolucao = {
            'labels': [item['mes'] for item in result_evolucao] if result_evolucao else [],
            'data': [item['total'] for item in result_evolucao] if result_evolucao else []
        }

        return {
            'clientes_status': clientes_status,
            'funcionarios_cliente': funcionarios_cliente,
            'evolucao': evolucao
        }

    except Exception as e:
        logger.error(f"Erro ao gerar dados dos gráficos: {e}")
        return {
            'clientes_status': {'labels': [], 'data': []},
            'funcionarios_cliente': {'labels': [], 'data': []},
            'evolucao': {'labels': [], 'data': []}
        }

def gerar_alertas_sistema():
    """Gerar alertas do sistema"""
    try:
        alertas = []
        db = DatabaseManager()

        # Verificar contratos próximos do vencimento
        sql_vencimento = """
        SELECT COUNT(*) as total
        FROM empresa_clientes
        WHERE status_contrato = 'ativo'
        AND data_fim IS NOT NULL
        AND data_fim <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        """
        result_vencimento = db.execute_query(sql_vencimento)

        if result_vencimento and result_vencimento[0]['total'] > 0:
            alertas.append({
                'tipo': 'warning',
                'titulo': 'Contratos Vencendo',
                'mensagem': f"{result_vencimento[0]['total']} contrato(s) vencem nos próximos 30 dias"
            })

        # Verificar funcionários sem jornada definida
        sql_sem_jornada = """
        SELECT COUNT(*) as total
        FROM funcionario_alocacoes fa
        WHERE fa.ativo = 1 AND fa.jornada_trabalho_id IS NULL
        """
        result_sem_jornada = db.execute_query(sql_sem_jornada)

        if result_sem_jornada and result_sem_jornada[0]['total'] > 0:
            alertas.append({
                'tipo': 'info',
                'titulo': 'Jornadas Pendentes',
                'mensagem': f"{result_sem_jornada[0]['total']} funcionário(s) sem jornada definida"
            })

        # Verificar clientes sem funcionários alocados
        sql_sem_funcionarios = """
        SELECT COUNT(*) as total
        FROM empresa_clientes ec
        LEFT JOIN funcionario_alocacoes fa ON ec.empresa_cliente_id = fa.empresa_cliente_id AND fa.ativo = 1
        WHERE ec.status_contrato = 'ativo' AND fa.id IS NULL
        """
        result_sem_funcionarios = db.execute_query(sql_sem_funcionarios)

        if result_sem_funcionarios and result_sem_funcionarios[0]['total'] > 0:
            alertas.append({
                'tipo': 'warning',
                'titulo': 'Clientes sem Funcionários',
                'mensagem': f"{result_sem_funcionarios[0]['total']} cliente(s) ativo(s) sem funcionários alocados"
            })

        return alertas

    except Exception as e:
        logger.error(f"Erro ao gerar alertas: {e}")
        return []

@empresa_principal_bp.route('/relatorios/dados-graficos', methods=['POST'])
@require_admin
def dados_graficos():
    """API para atualizar dados dos gráficos com filtros"""
    try:
        # Aqui você pode implementar filtros específicos
        dados = gerar_dados_graficos()
        return jsonify({'success': True, 'dados': dados})

    except Exception as e:
        logger.error(f"Erro ao buscar dados dos gráficos: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/relatorios/gerar', methods=['POST'])
@require_admin
def gerar_relatorio():
    """Gerar relatório personalizado"""
    try:
        # Por enquanto, retornar um PDF simples
        # Em uma implementação completa, você usaria bibliotecas como ReportLab ou WeasyPrint

        from io import BytesIO

        # Simular geração de PDF
        buffer = BytesIO()
        buffer.write(b"Relatorio de Empresa Principal - Em desenvolvimento")
        buffer.seek(0)

        return buffer.getvalue(), 200, {
            'Content-Type': 'application/pdf',
            'Content-Disposition': 'attachment; filename=relatorio_empresa_principal.pdf'
        }

    except Exception as e:
        logger.error(f"Erro ao gerar relatório: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

# ===== ROTAS DE RELATÓRIOS DE FUNCIONÁRIOS =====

@empresa_principal_bp.route('/relatorios/funcionarios/geral')
@require_admin
def relatorio_funcionarios_geral():
    """Relatório geral de funcionários da empresa principal"""
    try:
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            return "Empresa principal não definida", 404

        funcionarios = get_funcionarios_empresa_principal()

        # Gerar HTML do relatório com layout melhorado
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Relatório Geral de Funcionários</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <!-- Bootstrap CSS -->
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <!-- Font Awesome -->
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <!-- PDF Export Libraries -->
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    font-size: 11pt;
                    line-height: 1.4;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    margin: 0;
                    padding: 20px;
                }}

                .report-container {{
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                    overflow: hidden;
                    max-width: 1200px;
                    margin: 0 auto;
                }}

                .report-header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                    position: relative;
                }}

                .report-header h1 {{
                    margin: 0;
                    font-size: 18pt;
                    font-weight: 600;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                }}

                .empresa-info {{
                    font-size: 12pt;
                    margin: 10px 0;
                    opacity: 0.9;
                }}

                .data-geracao {{
                    font-size: 10pt;
                    opacity: 0.8;
                }}

                .action-buttons {{
                    position: absolute;
                    top: 20px;
                    right: 20px;
                    display: flex;
                    gap: 10px;
                }}

                .btn-action {{
                    background: rgba(255,255,255,0.2);
                    border: 1px solid rgba(255,255,255,0.3);
                    color: white;
                    padding: 8px 15px;
                    border-radius: 25px;
                    text-decoration: none;
                    transition: all 0.3s ease;
                    font-size: 10pt;
                }}

                .btn-action:hover {{
                    background: rgba(255,255,255,0.3);
                    color: white;
                    transform: translateY(-2px);
                }}

                .report-content {{
                    padding: 30px;
                }}

                .summary-cards {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 20px;
                    margin-bottom: 30px;
                }}

                .summary-card {{
                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                    color: white;
                    padding: 20px;
                    border-radius: 10px;
                    text-align: center;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }}

                .summary-card.active {{
                    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                }}

                .summary-card.inactive {{
                    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
                }}

                .summary-card h3 {{
                    margin: 0;
                    font-size: 16pt;
                    font-weight: bold;
                }}

                .summary-card p {{
                    margin: 5px 0 0 0;
                    font-size: 10pt;
                    opacity: 0.9;
                }}

                .table-container {{
                    background: white;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }}

                .table {{
                    margin: 0;
                    font-size: 9pt;
                }}

                .table thead th {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    padding: 12px 10px;
                    font-weight: 600;
                    text-transform: uppercase;
                    font-size: 9pt;
                    letter-spacing: 0.5px;
                }}

                .table tbody td {{
                    padding: 10px;
                    border-bottom: 1px solid #f0f0f0;
                    vertical-align: middle;
                    font-size: 9pt;
                }}

                .table tbody tr:hover {{
                    background-color: #f8f9fa;
                }}

                .status-badge {{
                    padding: 4px 10px;
                    border-radius: 15px;
                    font-size: 9pt;
                    font-weight: 600;
                    text-transform: uppercase;
                }}

                .status-ativo {{
                    background: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                }}

                .status-inativo {{
                    background: #f8d7da;
                    color: #721c24;
                    border: 1px solid #f5c6cb;
                }}

                .footer {{
                    background: #f8f9fa;
                    padding: 15px;
                    text-align: center;
                    color: #6c757d;
                    font-size: 10pt;
                    border-top: 1px solid #dee2e6;
                }}

                .no-data {{
                    text-align: center;
                    padding: 30px;
                    color: #6c757d;
                    font-style: italic;
                    font-size: 11pt;
                }}

                @media print {{
                    body {{
                        background: white !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }}
                    .action-buttons {{ display: none !important; }}
                    .report-container {{
                        box-shadow: none !important;
                        border-radius: 0 !important;
                        margin: 0 !important;
                    }}
                    .report-header {{
                        background: white !important;
                        color: #333 !important;
                        padding: 20px !important;
                        border-bottom: 2px solid #333;
                    }}
                    .report-header h1 {{
                        color: #333 !important;
                        font-size: 14pt !important;
                        text-shadow: none !important;
                        margin-bottom: 8px !important;
                    }}
                    .empresa-info {{
                        color: #666 !important;
                        font-size: 11pt !important;
                    }}
                    .data-geracao {{
                        color: #666 !important;
                        font-size: 9pt !important;
                    }}
                    .summary-cards {{
                        display: none !important;
                    }}
                    .report-content {{
                        padding: 20px !important;
                    }}
                    .table-container {{
                        box-shadow: none !important;
                        border: 1px solid #333;
                    }}
                    .table thead th {{
                        background: #f8f9fa !important;
                        color: #333 !important;
                        border: 1px solid #333 !important;
                        font-size: 9pt !important;
                        padding: 6px !important;
                    }}
                    .table tbody td {{
                        border: 1px solid #333 !important;
                        padding: 6px !important;
                        font-size: 9pt !important;
                    }}
                    .footer {{
                        background: white !important;
                        border-top: 1px solid #333 !important;
                        padding: 8px !important;
                        font-size: 9pt !important;
                    }}
                    .status-badge {{
                        background: none !important;
                        border: 1px solid #333 !important;
                        color: #333 !important;
                    }}
                }}

                @media (max-width: 768px) {{
                    .action-buttons {{
                        position: static;
                        justify-content: center;
                        margin-top: 20px;
                    }}

                    .report-header h1 {{
                        font-size: 1.8rem;
                    }}

                    .table-container {{
                        overflow-x: auto;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="report-container" id="reportContent">
                <div class="report-header">
                    <div class="action-buttons">
                        <button class="btn-action" onclick="voltarPagina()" title="Voltar à página anterior">
                            <i class="fas fa-arrow-left"></i> Voltar
                        </button>
                        <button class="btn-action" onclick="window.print()" title="Imprimir Relatório">
                            <i class="fas fa-print"></i> Imprimir
                        </button>
                        <button class="btn-action" onclick="exportToPDF()" title="Exportar como PDF">
                            <i class="fas fa-file-pdf"></i> PDF
                        </button>
                    </div>

                    <h1><i class="fas fa-users"></i> Relatório Geral de Funcionários</h1>
                    <div class="empresa-info">
                        <i class="fas fa-building"></i> {empresa_principal.get('nome_fantasia', empresa_principal['razao_social'])}
                    </div>
                    <div class="empresa-info">
                        <i class="fas fa-id-card"></i> CNPJ: {empresa_principal.get('cnpj', 'Não informado')}
                    </div>
                    <div class="data-geracao">
                        <i class="fas fa-calendar-alt"></i> Gerado em: {datetime.now().strftime('%d/%m/%Y às %H:%M')}
                    </div>
                </div>

                <div class="report-content">
                    <div class="summary-cards">
                        <div class="summary-card">
                            <h3>{len(funcionarios) if funcionarios else 0}</h3>
                            <p><i class="fas fa-users"></i> Total de Funcionários</p>
                        </div>
                        <div class="summary-card active">
                            <h3>{len([f for f in funcionarios if f.get('status_cadastro') == 'Ativo']) if funcionarios else 0}</h3>
                            <p><i class="fas fa-user-check"></i> Funcionários Ativos</p>
                        </div>
                        <div class="summary-card inactive">
                            <h3>{len([f for f in funcionarios if f.get('status_cadastro') != 'Ativo']) if funcionarios else 0}</h3>
                            <p><i class="fas fa-user-times"></i> Funcionários Inativos</p>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-user"></i> Nome</th>
                                    <th><i class="fas fa-id-card"></i> CPF</th>
                                    <th><i class="fas fa-hashtag"></i> Matrícula</th>
                                    <th><i class="fas fa-briefcase"></i> Cargo</th>
                                    <th><i class="fas fa-calendar-plus"></i> Data Admissão</th>
                                    <th><i class="fas fa-toggle-on"></i> Status</th>
                                </tr>
                            </thead>
                            <tbody>
        """

        if funcionarios:
            for func in funcionarios:
                status_class = "status-ativo" if func.get('status_cadastro') == 'Ativo' else "status-inativo"
                html_content += f"""
                                <tr>
                                    <td><strong>{func.get('nome_completo', 'N/A')}</strong></td>
                                    <td>{func.get('cpf', 'N/A')}</td>
                                    <td><span class="badge bg-secondary">{func.get('matricula', 'N/A')}</span></td>
                                    <td>{func.get('cargo', 'N/A')}</td>
                                    <td>{func.get('data_admissao_formatada', 'N/A')}</td>
                                    <td><span class="status-badge {status_class}">{func.get('status_display', 'N/A')}</span></td>
                                </tr>
                """
        else:
            html_content += '''
                                <tr>
                                    <td colspan="6" class="no-data">
                                        <i class="fas fa-inbox fa-2x"></i><br>
                                        Nenhum funcionário encontrado
                                    </td>
                                </tr>
            '''

        html_content += f"""
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="footer">
                    <p>
                        <i class="fas fa-shield-alt"></i>
                        Sistema de Controle de Ponto - {empresa_principal.get('nome_fantasia', empresa_principal['razao_social'])}
                    </p>
                </div>
            </div>

            <!-- Bootstrap JS -->
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

            <script>
                function exportToPDF() {{
                    // Aplicar estilos de impressão temporariamente para PDF
                    applyPrintStylesForPDF();

                    const element = document.getElementById('reportContent');
                    const opt = {{
                        margin: [0.3, 0.3, 0.3, 0.3],
                        filename: 'relatorio-funcionarios-{empresa_principal.get("nome_fantasia", "empresa").replace(" ", "-").lower()}-{datetime.now().strftime("%Y%m%d_%H%M")}.pdf',
                        image: {{
                            type: 'jpeg',
                            quality: 0.95,
                            format: 'a4'
                        }},
                        html2canvas: {{
                            scale: 2,
                            useCORS: true,
                            letterRendering: true,
                            backgroundColor: '#ffffff',
                            logging: false,
                            allowTaint: false,
                            removeContainer: true
                        }},
                        jsPDF: {{
                            unit: 'mm',
                            format: 'a4',
                            orientation: 'portrait',
                            compress: true
                        }},
                        pagebreak: {{ mode: ['avoid-all', 'css', 'legacy'] }}
                    }};

                    // Usar html2pdf se disponível, senão usar jsPDF
                    if (typeof html2pdf !== 'undefined') {{
                        html2pdf().set(opt).from(element).save().then(() => {{
                            restoreOriginalStyles();
                        }});
                    }} else {{
                        // Fallback para jsPDF básico
                        html2canvas(element, {{
                            scale: 2,
                            useCORS: true,
                            backgroundColor: '#ffffff'
                        }}).then(canvas => {{
                            const imgData = canvas.toDataURL('image/png');
                            const {{ jsPDF }} = window.jspdf;
                            const pdf = new jsPDF('p', 'mm', 'a4');

                            const imgWidth = 210;
                            const pageHeight = 295;
                            const imgHeight = (canvas.height * imgWidth) / canvas.width;
                            let heightLeft = imgHeight;
                            let position = 0;

                            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                            heightLeft -= pageHeight;

                            while (heightLeft >= 0) {{
                                position = heightLeft - imgHeight;
                                pdf.addPage();
                                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                                heightLeft -= pageHeight;
                            }}

                            pdf.save('relatorio-funcionarios-{datetime.now().strftime("%Y%m%d_%H%M")}.pdf');
                            restoreOriginalStyles();
                        }});
                    }}
                }}

                function applyPrintStylesForPDF() {{
                    // Ocultar botões de ação para PDF
                    const actionButtons = document.querySelector('.action-buttons');
                    if (actionButtons) actionButtons.style.display = 'none';

                    // Ocultar cards de resumo para layout minimalista
                    const summaryCards = document.querySelector('.summary-cards');
                    if (summaryCards) summaryCards.style.display = 'none';

                    // Aplicar estilos de impressão ao body
                    document.body.style.background = 'white';
                    document.body.style.margin = '0';
                    document.body.style.padding = '0';

                    // Aplicar estilos ao container do relatório
                    const reportContainer = document.querySelector('.report-container');
                    if (reportContainer) {{
                        reportContainer.style.boxShadow = 'none';
                        reportContainer.style.borderRadius = '0';
                        reportContainer.style.margin = '0';
                        reportContainer.style.background = 'white';
                    }}

                    // Aplicar estilos ao cabeçalho
                    const reportHeader = document.querySelector('.report-header');
                    if (reportHeader) {{
                        reportHeader.style.background = 'white';
                        reportHeader.style.color = '#333';
                        reportHeader.style.padding = '20px';
                        reportHeader.style.borderBottom = '2px solid #333';
                    }}

                    const headerTitle = document.querySelector('.report-header h1');
                    if (headerTitle) {{
                        headerTitle.style.color = '#333';
                        headerTitle.style.fontSize = '14pt'; // Padrão para títulos em PDF
                        headerTitle.style.textShadow = 'none';
                        headerTitle.style.marginBottom = '8px';
                        headerTitle.style.fontWeight = '600';
                    }}

                    // Aplicar estilos às informações da empresa (12pt conforme especificação)
                    const empresaInfo = document.querySelectorAll('.empresa-info');
                    empresaInfo.forEach(info => {{
                        info.style.color = '#666';
                        info.style.fontSize = '12pt'; // Padrão para informações da empresa
                        info.style.fontWeight = '400';
                    }});

                    const dataGeracao = document.querySelector('.data-geracao');
                    if (dataGeracao) {{
                        dataGeracao.style.color = '#666';
                        dataGeracao.style.fontSize = '10pt'; // Padrão para data de geração
                        dataGeracao.style.fontWeight = '400';
                    }}

                    // Aplicar estilos à tabela
                    const tableContainer = document.querySelector('.table-container');
                    if (tableContainer) {{
                        tableContainer.style.boxShadow = 'none';
                        tableContainer.style.border = '1px solid #333';
                    }}

                    const tableHeaders = document.querySelectorAll('.table thead th');
                    tableHeaders.forEach(th => {{
                        th.style.background = '#f8f9fa';
                        th.style.color = '#333';
                        th.style.border = '1px solid #333';
                        th.style.fontSize = '9pt';
                        th.style.padding = '6px';
                    }});

                    const tableCells = document.querySelectorAll('.table tbody td');
                    tableCells.forEach(td => {{
                        td.style.border = '1px solid #333';
                        td.style.padding = '6px';
                        td.style.fontSize = '9pt';
                    }});

                    // Aplicar estilos aos badges de status
                    const statusBadges = document.querySelectorAll('.status-badge');
                    statusBadges.forEach(badge => {{
                        badge.style.background = 'none';
                        badge.style.border = '1px solid #333';
                        badge.style.color = '#333';
                    }});

                    // Aplicar estilos ao rodapé
                    const footer = document.querySelector('.footer');
                    if (footer) {{
                        footer.style.background = 'white';
                        footer.style.borderTop = '1px solid #333';
                        footer.style.padding = '8px';
                        footer.style.fontSize = '9pt';
                    }}
                }}

                function restoreOriginalStyles() {{
                    // Recarregar a página para restaurar estilos originais
                    setTimeout(() => {{
                        window.location.reload();
                    }}, 1000);
                }}

                function voltarPagina() {{
                    // Tentar voltar para a página anterior
                    if (window.history.length > 1) {{
                        window.history.back();
                    }} else {{
                        // Se não há histórico, ir para a página de funcionários da empresa principal
                        window.location.href = '/empresa-principal/funcionarios';
                    }}
                }}

                // Adicionar animações suaves
                document.addEventListener('DOMContentLoaded', function() {{
                    const cards = document.querySelectorAll('.summary-card');
                    cards.forEach((card, index) => {{
                        setTimeout(() => {{
                            card.style.opacity = '0';
                            card.style.transform = 'translateY(20px)';
                            card.style.transition = 'all 0.5s ease';
                            setTimeout(() => {{
                                card.style.opacity = '1';
                                card.style.transform = 'translateY(0)';
                            }}, 100);
                        }}, index * 100);
                    }});
                }});
            </script>
        </body>
        </html>
        """

        return html_content

    except Exception as e:
        logger.error(f"Erro ao gerar relatório geral de funcionários: {e}")
        return f"Erro ao gerar relatório: {str(e)}", 500

@empresa_principal_bp.route('/relatorios/funcionarios/frequencia')
@require_admin
def relatorio_funcionarios_frequencia():
    """Relatório de frequência geral dos funcionários"""
    try:
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Relatório de Frequência</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; text-align: center; }
                .em-desenvolvimento { color: #666; margin-top: 50px; }
            </style>
        </head>
        <body>
            <h1>Relatório de Frequência Geral</h1>
            <div class="em-desenvolvimento">
                <h3>🚧 Em Desenvolvimento</h3>
                <p>Esta funcionalidade será implementada em breve.</p>
                <p>Incluirá dados de presença, faltas, atrasos e horas extras.</p>
            </div>
        </body>
        </html>
        """

    except Exception as e:
        logger.error(f"Erro ao gerar relatório de frequência: {e}")
        return f"Erro ao gerar relatório: {str(e)}", 500

@empresa_principal_bp.route('/relatorios/jornadas')
@require_admin
def relatorio_jornadas():
    """Relatório de jornadas de trabalho"""
    try:
        jornadas = get_jornadas_empresa_principal()
        empresa_principal = get_empresa_principal()

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Relatório de Jornadas de Trabalho</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .horario {{ font-family: monospace; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Relatório de Jornadas de Trabalho</h1>
                <div>{empresa_principal['razao_social'] if empresa_principal else 'Empresa Principal'}</div>
                <div>Gerado em: {datetime.now().strftime('%d/%m/%Y %H:%M')}</div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>Nome da Jornada</th>
                        <th>Segunda a Quinta</th>
                        <th>Sexta-feira</th>
                        <th>Carga Horária Semanal</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
        """

        if jornadas:
            for jornada in jornadas:
                seg_qui = f"{jornada.get('seg_qui_entrada', 'N/A')} às {jornada.get('seg_qui_saida', 'N/A')}"
                sexta = f"{jornada.get('sexta_entrada', 'N/A')} às {jornada.get('sexta_saida', 'N/A')}"

                html_content += f"""
                    <tr>
                        <td>{jornada.get('nome_jornada', 'N/A')}</td>
                        <td class="horario">{seg_qui}</td>
                        <td class="horario">{sexta}</td>
                        <td>{jornada.get('carga_horaria_semanal', 'N/A')}h</td>
                        <td>{'Ativa' if jornada.get('ativa') else 'Inativa'}</td>
                    </tr>
                """
        else:
            html_content += '<tr><td colspan="5" style="text-align: center;">Nenhuma jornada encontrada</td></tr>'

        html_content += """
                </tbody>
            </table>
        </body>
        </html>
        """

        return html_content

    except Exception as e:
        logger.error(f"Erro ao gerar relatório de jornadas: {e}")
        return f"Erro ao gerar relatório: {str(e)}", 500

@empresa_principal_bp.route('/relatorios/funcionarios/exportar')
@require_admin
def exportar_funcionarios():
    """Exportar lista de funcionários em CSV"""
    try:
        funcionarios = get_funcionarios_empresa_principal()

        # Criar CSV
        import csv
        from io import StringIO

        output = StringIO()
        writer = csv.writer(output)

        # Cabeçalho
        writer.writerow(['Nome', 'CPF', 'Matrícula', 'Cargo', 'Data Admissão', 'Status', 'Jornada', 'Telefone'])

        # Dados
        if funcionarios:
            for func in funcionarios:
                writer.writerow([
                    func.get('nome_completo', ''),
                    func.get('cpf', ''),
                    func.get('matricula', ''),
                    func.get('cargo', ''),
                    func.get('data_admissao_formatada', ''),
                    func.get('status_display', ''),
                    func.get('jornada_nome', ''),
                    func.get('telefone1', '')
                ])

        output.seek(0)

        from flask import Response
        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': 'attachment; filename=funcionarios_empresa_principal.csv'}
        )

    except Exception as e:
        logger.error(f"Erro ao exportar funcionários: {e}")
        return f"Erro ao exportar: {str(e)}", 500

@empresa_principal_bp.route('/relatorios/funcionario/<int:funcionario_id>')
@require_admin
def relatorio_funcionario_individual(funcionario_id):
    """Relatório individual de um funcionário"""
    try:
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Relatório Individual</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; text-align: center; }}
                .em-desenvolvimento {{ color: #666; margin-top: 50px; }}
            </style>
        </head>
        <body>
            <h1>Relatório Individual - Funcionário ID: {funcionario_id}</h1>
            <div class="em-desenvolvimento">
                <h3>🚧 Em Desenvolvimento</h3>
                <p>Esta funcionalidade será implementada em breve.</p>
                <p>Incluirá histórico completo do funcionário, registros de ponto, faltas, etc.</p>
            </div>
        </body>
        </html>
        """

    except Exception as e:
        logger.error(f"Erro ao gerar relatório individual: {e}")
        return f"Erro ao gerar relatório: {str(e)}", 500

# ================================================================
# REGISTRAR BLUEPRINT
# ================================================================

def register_empresa_principal_routes(app):
    """Registrar rotas da empresa principal"""
    app.register_blueprint(empresa_principal_bp)
    logger.info("Rotas da empresa principal registradas")

if __name__ == '__main__':
    print("Módulo de Empresa Principal carregado")
