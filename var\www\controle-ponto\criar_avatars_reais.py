#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CRIAÇÃO DE AVATARS REAIS - RLPONTO-WEB
=====================================

Este script cria avatars SVG simples para substituir os placeholders
e resolver o problema das fotos dos funcionários.

Data: 09/01/2025
Autor: Sistema RLPONTO-WEB
"""

import os
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def criar_avatar_svg(nome_arquivo, cor_fundo, cor_icone, genero):
    """
    Cria um avatar SVG simples.
    
    Args:
        nome_arquivo (str): Nome do arquivo SVG
        cor_fundo (str): Cor de fundo do avatar
        cor_icone (str): Cor do ícone
        genero (str): Tipo de avatar (masculino, feminino, neutro)
    """
    
    # Escolher ícone baseado no gênero
    if genero == 'masculino':
        icone_path = '''<path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7C14.5 7 14 7.2 13.6 7.6L12 9.2L10.4 7.6C10 7.2 9.5 7 9 7L3 7V9C3 9.6 3.4 10 4 10L8.2 10L12 13.8L15.8 10L20 10C20.6 10 21 9.6 21 9ZM8 11.5V21C8 21.6 8.4 22 9 22L11 22C11.6 22 12 21.6 12 21V11.5L8 11.5ZM12 11.5V21C12 21.6 12.4 22 13 22L15 22C15.6 22 16 21.6 16 21V11.5L12 11.5Z"/>'''
    elif genero == 'feminino':
        icone_path = '''<path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM8.5 7C8.2 7 8 7.2 8 7.5L8 11C8 11.6 8.4 12 9 12L11 12V21C11 21.6 11.4 22 12 22C12.6 22 13 21.6 13 21L13 12L15 12C15.6 12 16 11.6 16 11L16 7.5C16 7.2 15.8 7 15.5 7L8.5 7Z"/>'''
    else:  # neutro
        icone_path = '''<circle cx="12" cy="6" r="4"/><path d="M12 14C8.7 14 6 16.7 6 20V22H18V20C18 16.7 15.3 14 12 14Z"/>'''
    
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <rect width="24" height="24" fill="{cor_fundo}" rx="4"/>
  <g fill="{cor_icone}">
    {icone_path}
  </g>
</svg>'''
    
    return svg_content

def criar_avatars_sistema():
    """
    Cria todos os avatars necessários para o sistema.
    """
    try:
        logger.info("🎨 Criando avatars SVG para o sistema...")
        
        # Criar diretório se não existir
        avatars_dir = "static/images/avatars"
        os.makedirs(avatars_dir, exist_ok=True)
        
        # Definir avatars para criar
        avatars_config = [
            ("funcionario_masculino_1.svg", "#4A90E2", "#FFFFFF", "masculino"),  # Azul
            ("funcionario_feminino_1.svg", "#E24A90", "#FFFFFF", "feminino"),   # Rosa
            ("funcionario_masculino_2.svg", "#28A745", "#FFFFFF", "masculino"),  # Verde
            ("funcionario_feminino_2.svg", "#FD7E14", "#FFFFFF", "feminino"),   # Laranja
            ("funcionario_neutro_1.svg", "#6C757D", "#FFFFFF", "neutro")        # Cinza
        ]
        
        avatars_criados = []
        
        for nome_arquivo, cor_fundo, cor_icone, genero in avatars_config:
            arquivo_path = os.path.join(avatars_dir, nome_arquivo)
            
            # Criar conteúdo SVG
            svg_content = criar_avatar_svg(nome_arquivo, cor_fundo, cor_icone, genero)
            
            # Salvar arquivo
            with open(arquivo_path, 'w', encoding='utf-8') as f:
                f.write(svg_content)
            
            avatars_criados.append(arquivo_path)
            logger.info(f"   ✅ Avatar criado: {nome_arquivo} ({genero}, {cor_fundo})")
        
        logger.info(f"🎉 {len(avatars_criados)} avatars SVG criados com sucesso!")
        return avatars_criados
        
    except Exception as e:
        logger.error(f"❌ Erro ao criar avatars: {str(e)}")
        raise

def atualizar_fotos_funcionarios():
    """
    Atualiza as fotos dos funcionários para usar os novos avatars SVG.
    """
    try:
        logger.info("🔄 Atualizando fotos dos funcionários para SVG...")
        
        import sys
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from utils.database import get_db_connection
        from pymysql.cursors import DictCursor
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Buscar funcionários com fotos placeholder
        cursor.execute("""
            SELECT id, nome_completo, foto_3x4, sexo
            FROM funcionarios 
            WHERE foto_3x4 LIKE '%funcionario_%jpg'
        """)
        funcionarios = cursor.fetchall()
        
        if funcionarios:
            logger.info(f"👥 Atualizando {len(funcionarios)} funcionários...")
            
            # Mapear avatars SVG
            avatars_svg = {
                '/static/images/avatars/funcionario_masculino_1.jpg': '/static/images/avatars/funcionario_masculino_1.svg',
                '/static/images/avatars/funcionario_feminino_1.jpg': '/static/images/avatars/funcionario_feminino_1.svg',
                '/static/images/avatars/funcionario_masculino_2.jpg': '/static/images/avatars/funcionario_masculino_2.svg',
                '/static/images/avatars/funcionario_feminino_2.jpg': '/static/images/avatars/funcionario_feminino_2.svg',
                '/static/images/avatars/funcionario_neutro_1.jpg': '/static/images/avatars/funcionario_neutro_1.svg'
            }
            
            for func in funcionarios:
                foto_atual = func['foto_3x4']
                foto_nova = avatars_svg.get(foto_atual, '/static/images/avatars/funcionario_neutro_1.svg')
                
                cursor.execute("""
                    UPDATE funcionarios 
                    SET foto_3x4 = %s
                    WHERE id = %s
                """, (foto_nova, func['id']))
                
                logger.info(f"   🔄 {func['nome_completo']}: {foto_atual} → {foto_nova}")
        
        conn.commit()
        logger.info("✅ Fotos dos funcionários atualizadas para SVG!")
        
    except Exception as e:
        logger.error(f"❌ Erro ao atualizar fotos: {str(e)}")
        raise
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """
    Função principal.
    """
    try:
        logger.info("🚀 INICIANDO CRIAÇÃO DE AVATARS REAIS")
        logger.info("=" * 50)
        
        # 1. Criar avatars SVG
        avatars_criados = criar_avatars_sistema()
        
        # 2. Atualizar funcionários para usar SVG
        atualizar_fotos_funcionarios()
        
        logger.info("=" * 50)
        logger.info("🎉 CRIAÇÃO DE AVATARS CONCLUÍDA!")
        logger.info("📋 Próximos passos:")
        logger.info("   1. Reiniciar servidor web")
        logger.info("   2. Testar página de registro manual")
        logger.info("   3. Verificar se as fotos aparecem corretamente")
        
        return True
        
    except Exception as e:
        logger.error(f"💥 ERRO: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 