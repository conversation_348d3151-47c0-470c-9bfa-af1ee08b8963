# ========================================
# BLUEPRINT CONFIGURAÇÕES - RLPONTO-WEB
# Data: 08/01/2025
# Descrição: Sistema de configurações e cadastro de empresas
# ========================================

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash
from datetime import datetime, date
from utils.auth import require_login, require_admin
from utils.database import get_db_connection
from utils.helpers import validar_cnpj, formatar_cnpj, validar_email
import logging
import re

# Configurar logger
logger = logging.getLogger(__name__)

# Criar Blueprint
configuracoes_bp = Blueprint('configuracoes', __name__, url_prefix='/configuracoes')

# ========================================
# FUNÇÕES AUXILIARES
# ========================================

def validar_dados_empresa(dados):
    """
    Valida os dados de uma empresa.
    
    Args:
        dados (dict): Dados da empresa
        
    Returns:
        dict: Resultado da validação
    """
    erros = []
    
    # Validar razão social
    if not dados.get('razao_social'):
        erros.append('Razão social é obrigatória')
    elif len(dados['razao_social']) < 3:
        erros.append('Razão social deve ter pelo menos 3 caracteres')
    elif len(dados['razao_social']) > 200:
        erros.append('Razão social deve ter no máximo 200 caracteres')
    
    # Validar CNPJ
    cnpj = dados.get('cnpj', '').strip()
    if not cnpj:
        erros.append('CNPJ é obrigatório')
    else:
        # Remover formatação
        cnpj_limpo = re.sub(r'[^\d]', '', cnpj)
        if not validar_cnpj(cnpj_limpo):
            erros.append('CNPJ inválido')
        else:
            dados['cnpj'] = formatar_cnpj(cnpj_limpo)
    
    # Validar nome fantasia (opcional)
    if dados.get('nome_fantasia') and len(dados['nome_fantasia']) > 200:
        erros.append('Nome fantasia deve ter no máximo 200 caracteres')
    
    # Validar telefone (opcional)
    if dados.get('telefone'):
        telefone = re.sub(r'[^\d]', '', dados['telefone'])
        if len(telefone) < 10 or len(telefone) > 11:
            erros.append('Telefone deve ter 10 ou 11 dígitos')
        dados['telefone'] = telefone
    
    # Validar email (opcional)
    if dados.get('email') and not validar_email(dados['email']):
        erros.append('Email inválido')
    
    return {
        'valido': len(erros) == 0,
        'erros': erros,
        'dados': dados
    }

def verificar_cnpj_duplicado(cnpj, empresa_id=None):
    """
    Verifica se já existe uma empresa com o mesmo CNPJ.
    
    Args:
        cnpj (str): CNPJ a verificar
        empresa_id (int, optional): ID da empresa para excluir da verificação
        
    Returns:
        bool: True se existe duplicata
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if empresa_id:
            cursor.execute("""
                SELECT COUNT(*) as total FROM empresas 
                WHERE cnpj = %s AND id != %s
            """, (cnpj, empresa_id))
        else:
            cursor.execute("""
                SELECT COUNT(*) as total FROM empresas 
                WHERE cnpj = %s
            """, (cnpj,))
        
        count = cursor.fetchone()['total']
        conn.close()
        
        return count > 0
        
    except Exception as e:
        logger.error(f"Erro ao verificar CNPJ duplicado: {str(e)}")
        return False

# ========================================
# ROTAS PRINCIPAIS
# ========================================

@configuracoes_bp.route('/')
@require_admin
def index():
    """
    Página principal de configurações.
    Dashboard com opções de configuração do sistema.
    """
    try:
        # Obter estatísticas básicas
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Contar empresas
        cursor.execute('SELECT COUNT(*) as total FROM empresas WHERE ativa = TRUE')
        total_empresas = cursor.fetchone()['total']
        
        # Contar funcionários
        cursor.execute('SELECT COUNT(*) as total FROM funcionarios WHERE ativo = TRUE')
        total_funcionarios = cursor.fetchone()['total']
        
        # Contar horários de trabalho (com fallback se tabela não existir)
        total_horarios = 0
        try:
            cursor.execute('SELECT COUNT(*) as total FROM horarios_trabalho WHERE ativo = TRUE')
            total_horarios = cursor.fetchone()['total']
        except Exception:
            logger.warning("Tabela horarios_trabalho não encontrada, usando valor 0")
        
        # Registros de ponto do mês atual (com fallback se tabela não existir)
        registros_mes = 0
        try:
            cursor.execute("""
                SELECT COUNT(*) as total FROM registros_ponto 
                WHERE YEAR(data_hora) = YEAR(CURDATE()) 
                AND MONTH(data_hora) = MONTH(CURDATE())
            """)
            registros_mes = cursor.fetchone()['total']
        except Exception:
            logger.warning("Tabela registros_ponto não encontrada, usando valor 0")
        
        conn.close()
        
        context = {
            'titulo': 'Configurações do Sistema',
            'estatisticas': {
                'total_empresas': total_empresas,
                'total_funcionarios': total_funcionarios,
                'total_horarios': total_horarios,
                'registros_mes': registros_mes
            }
        }
        
        return render_template('configuracoes/index.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar configurações: {str(e)}")
        # Retornar página de erro em vez de redirect que pode falhar
        context = {
            'titulo': 'Configurações do Sistema',
            'estatisticas': {
                'total_empresas': 0,
                'total_funcionarios': 0,
                'total_horarios': 0,
                'registros_mes': 0
            },
            'erro': 'Erro ao carregar dados do sistema'
        }
        return render_template('configuracoes/index.html', **context)

@configuracoes_bp.route('/empresas')
@require_admin
def listar_empresas():
    """
    Lista todas as empresas cadastradas.
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Buscar empresas com estatísticas
        cursor.execute("""
            SELECT 
                e.id,
                e.razao_social,
                e.nome_fantasia,
                e.cnpj,
                e.telefone,
                e.email,
                e.ativa,
                e.data_cadastro,
                COUNT(f.id) as total_funcionarios
            FROM empresas e
            LEFT JOIN funcionarios f ON e.id = f.empresa_id AND f.ativo = TRUE
            GROUP BY e.id, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email, e.ativa, e.data_cadastro
            ORDER BY e.razao_social
        """)
        
        empresas_raw = cursor.fetchall()
        conn.close()
        
        # Processar dados das empresas
        empresas = []
        for emp in empresas_raw:
            empresa = {
                'id': emp[0],
                'razao_social': emp[1],
                'nome_fantasia': emp[2] or '',
                'cnpj': emp[3],
                'telefone': emp[4] or '',
                'email': emp[5] or '',
                'ativa': emp[6],
                'data_cadastro': emp[7].strftime('%d/%m/%Y') if emp[7] else '',
                'total_funcionarios': emp[8] or 0
            }
            empresas.append(empresa)
        
        context = {
            'titulo': 'Gerenciar Empresas',
            'empresas': empresas,
            'total_empresas': len(empresas)
        }
        
        return render_template('configuracoes/empresas.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao listar empresas: {str(e)}")
        flash('Erro ao carregar lista de empresas', 'error')
        return redirect(url_for('configuracoes.index'))

@configuracoes_bp.route('/empresas/nova', methods=['GET', 'POST'])
@require_admin
def nova_empresa():
    """
    Formulário para cadastrar nova empresa.
    """
    if request.method == 'POST':
        try:
            # Obter dados do formulário
            dados = {
                'razao_social': request.form.get('razao_social', '').strip(),
                'nome_fantasia': request.form.get('nome_fantasia', '').strip(),
                'cnpj': request.form.get('cnpj', '').strip(),
                'telefone': request.form.get('telefone', '').strip(),
                'email': request.form.get('email', '').strip(),
                'ativa': bool(request.form.get('ativa'))
            }
            
            # Validar dados
            validacao = validar_dados_empresa(dados)
            if not validacao['valido']:
                for erro in validacao['erros']:
                    flash(erro, 'error')
                return render_template('configuracoes/empresa_form.html', 
                                     titulo='Nova Empresa', dados=dados)
            
            dados = validacao['dados']
            
            # Verificar CNPJ duplicado
            if verificar_cnpj_duplicado(dados['cnpj']):
                flash('Já existe uma empresa cadastrada com este CNPJ', 'error')
                return render_template('configuracoes/empresa_form.html', 
                                     titulo='Nova Empresa', dados=dados)
            
            # Inserir no banco
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO empresas 
                (razao_social, nome_fantasia, cnpj, telefone, email, ativa, data_cadastro)
                VALUES (%s, %s, %s, %s, %s, %s, NOW())
            """, (
                dados['razao_social'],
                dados['nome_fantasia'] if dados['nome_fantasia'] else None,
                dados['cnpj'],
                dados['telefone'] if dados['telefone'] else None,
                dados['email'] if dados['email'] else None,
                dados['ativa']
            ))
            
            empresa_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            logger.info(f"Nova empresa cadastrada - ID: {empresa_id}, Razão Social: {dados['razao_social']}")
            flash('Empresa cadastrada com sucesso!', 'success')
            return redirect(url_for('configuracoes.listar_empresas'))
            
        except Exception as e:
            logger.error(f"Erro ao cadastrar empresa: {str(e)}")
            flash('Erro ao cadastrar empresa', 'error')
            return render_template('configuracoes/empresa_form.html', 
                                 titulo='Nova Empresa', dados=dados)
    
    # GET - exibir formulário
    context = {
        'titulo': 'Nova Empresa',
        'dados': {
            'razao_social': '',
            'nome_fantasia': '',
            'cnpj': '',
            'telefone': '',
            'email': '',
            'ativa': True
        }
    }
    
    return render_template('configuracoes/empresa_form.html', **context)

@configuracoes_bp.route('/empresas/<int:empresa_id>/editar', methods=['GET', 'POST'])
@require_admin
def editar_empresa(empresa_id):
    """
    Formulário para editar empresa existente.
    """
    try:
        if request.method == 'POST':
            # Obter dados do formulário
            dados = {
                'razao_social': request.form.get('razao_social', '').strip(),
                'nome_fantasia': request.form.get('nome_fantasia', '').strip(),
                'cnpj': request.form.get('cnpj', '').strip(),
                'telefone': request.form.get('telefone', '').strip(),
                'email': request.form.get('email', '').strip(),
                'ativa': bool(request.form.get('ativa'))
            }
            
            # Validar dados
            validacao = validar_dados_empresa(dados)
            if not validacao['valido']:
                for erro in validacao['erros']:
                    flash(erro, 'error')
                return render_template('configuracoes/empresa_form.html', 
                                     titulo='Editar Empresa', dados=dados, empresa_id=empresa_id)
            
            dados = validacao['dados']
            
            # Verificar CNPJ duplicado
            if verificar_cnpj_duplicado(dados['cnpj'], empresa_id):
                flash('Já existe outra empresa cadastrada com este CNPJ', 'error')
                return render_template('configuracoes/empresa_form.html', 
                                     titulo='Editar Empresa', dados=dados, empresa_id=empresa_id)
            
            # Atualizar no banco
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE empresas SET 
                    razao_social = %s,
                    nome_fantasia = %s,
                    cnpj = %s,
                    telefone = %s,
                    email = %s,
                    ativa = %s
                WHERE id = %s
            """, (
                dados['razao_social'],
                dados['nome_fantasia'] if dados['nome_fantasia'] else None,
                dados['cnpj'],
                dados['telefone'] if dados['telefone'] else None,
                dados['email'] if dados['email'] else None,
                dados['ativa'],
                empresa_id
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Empresa atualizada - ID: {empresa_id}, Razão Social: {dados['razao_social']}")
            flash('Empresa atualizada com sucesso!', 'success')
            return redirect(url_for('configuracoes.listar_empresas'))
        
        # GET - buscar dados da empresa
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT razao_social, nome_fantasia, cnpj, telefone, email, ativa
            FROM empresas 
            WHERE id = %s
        """, (empresa_id,))
        
        empresa_data = cursor.fetchone()
        conn.close()
        
        if not empresa_data:
            flash('Empresa não encontrada', 'error')
            return redirect(url_for('configuracoes.listar_empresas'))
        
        dados = {
            'razao_social': empresa_data[0],
            'nome_fantasia': empresa_data[1] or '',
            'cnpj': empresa_data[2],
            'telefone': empresa_data[3] or '',
            'email': empresa_data[4] or '',
            'ativa': empresa_data[5]
        }
        
        context = {
            'titulo': 'Editar Empresa',
            'dados': dados,
            'empresa_id': empresa_id
        }
        
        return render_template('configuracoes/empresa_form.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao editar empresa {empresa_id}: {str(e)}")
        flash('Erro ao carregar dados da empresa', 'error')
        return redirect(url_for('configuracoes.listar_empresas'))

@configuracoes_bp.route('/empresas/<int:empresa_id>/excluir', methods=['POST'])
@require_admin
def excluir_empresa(empresa_id):
    """
    Exclui uma empresa (soft delete).
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Verificar se a empresa tem funcionários ativos
        cursor.execute("""
            SELECT COUNT(*) FROM funcionarios 
            WHERE empresa_id = %s AND ativo = TRUE
        """, (empresa_id,))
        
        funcionarios_ativos = cursor.fetchone()[0]
        
        if funcionarios_ativos > 0:
            flash(f'Não é possível excluir a empresa. Há {funcionarios_ativos} funcionário(s) ativo(s) vinculado(s).', 'error')
            conn.close()
            return redirect(url_for('configuracoes.listar_empresas'))
        
        # Buscar nome da empresa para log
        cursor.execute('SELECT razao_social FROM empresas WHERE id = %s', (empresa_id,))
        empresa_data = cursor.fetchone()
        
        if not empresa_data:
            flash('Empresa não encontrada', 'error')
            conn.close()
            return redirect(url_for('configuracoes.listar_empresas'))
        
        # Soft delete - marcar como inativa
        cursor.execute("""
            UPDATE empresas SET ativa = FALSE 
            WHERE id = %s
        """, (empresa_id,))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Empresa excluída (soft delete) - ID: {empresa_id}, Razão Social: {empresa_data[0]}")
        flash('Empresa excluída com sucesso!', 'success')
        
    except Exception as e:
        logger.error(f"Erro ao excluir empresa {empresa_id}: {str(e)}")
        flash('Erro ao excluir empresa', 'error')
    
    return redirect(url_for('configuracoes.listar_empresas'))

# ========================================
# APIs
# ========================================

@configuracoes_bp.route('/api/validar-cnpj', methods=['POST'])
@require_admin
def api_validar_cnpj():
    """
    API para validar CNPJ em tempo real.
    """
    try:
        dados = request.get_json()
        cnpj = dados.get('cnpj', '').strip()
        empresa_id = dados.get('empresa_id')
        
        if not cnpj:
            return jsonify({
                'valido': False,
                'message': 'CNPJ é obrigatório'
            })
        
        # Remover formatação
        cnpj_limpo = re.sub(r'[^\d]', '', cnpj)
        
        # Validar formato
        if not validar_cnpj(cnpj_limpo):
            return jsonify({
                'valido': False,
                'message': 'CNPJ inválido'
            })
        
        # Verificar duplicata
        if verificar_cnpj_duplicado(formatar_cnpj(cnpj_limpo), empresa_id):
            return jsonify({
                'valido': False,
                'message': 'CNPJ já cadastrado'
            })
        
        return jsonify({
            'valido': True,
            'cnpj_formatado': formatar_cnpj(cnpj_limpo),
            'message': 'CNPJ válido'
        })
        
    except Exception as e:
        logger.error(f"Erro na validação de CNPJ: {str(e)}")
        return jsonify({
            'valido': False,
            'message': 'Erro na validação'
        }), 500

# ========================================
# TRATAMENTO DE ERROS
# ========================================

@configuracoes_bp.errorhandler(404)
def handle_404(error):
    """Tratamento de erro 404 para o blueprint."""
    flash('Página não encontrada', 'error')
    return redirect(url_for('configuracoes.index'))

@configuracoes_bp.errorhandler(500)
def handle_500(error):
    """Tratamento de erro 500 para o blueprint."""
    logger.error(f"Erro interno no blueprint configuracoes: {str(error)}")
    flash('Erro interno do servidor', 'error')
    return redirect(url_for('configuracoes.index'))

# ========================================
# FIM DO BLUEPRINT CONFIGURAÇÕES
# ======================================== 