#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 ANÁLISE ESTATÍSTICA AVANÇADA - TESTE ROBUSTO 30 FUNCIONÁRIOS
==============================================================

Sistema: RLPONTO-WEB v1.0
Data: 18/07/2025
Objetivo: Análise estatística detalhada dos resultados do teste robusto

ANÁLISES INCLUÍDAS:
- Distribuição de horas trabalhadas
- Análise de padrões de erro
- Correlações entre variáveis
- Métricas de performance do sistema
- Relatórios executivos
- Gráficos e visualizações (texto)
"""

import json
import statistics
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('analise_estatistica')

class AnaliseEstatisticaRobusto:
    """
    Classe para análise estatística avançada dos resultados.
    """
    
    def __init__(self, arquivo_resultados=None):
        self.dados = None
        self.estatisticas_avancadas = {}
        
        if arquivo_resultados:
            self.carregar_dados(arquivo_resultados)
    
    def carregar_dados(self, arquivo):
        """
        Carrega dados do arquivo JSON de resultados.
        """
        try:
            with open(arquivo, 'r', encoding='utf-8') as f:
                self.dados = json.load(f)
            logger.info(f"✅ Dados carregados de: {arquivo}")
            return True
        except Exception as e:
            logger.error(f"❌ Erro ao carregar dados: {e}")
            return False
    
    def calcular_estatisticas_descritivas(self):
        """
        Calcula estatísticas descritivas básicas.
        """
        if not self.dados:
            return
        
        resultados = self.dados['resultados_calculos']
        
        # Extrair métricas
        horas_trabalhadas = [r['horas_trabalhadas_total'] for r in resultados]
        banco_horas = [r['banco_horas_final'] for r in resultados]
        horas_extras = [r['horas_extras_total'] for r in resultados]
        dias_erro = [r['dias_com_erro'] for r in resultados]
        
        self.estatisticas_avancadas = {
            'horas_trabalhadas': {
                'media': statistics.mean(horas_trabalhadas),
                'mediana': statistics.median(horas_trabalhadas),
                'desvio_padrao': statistics.stdev(horas_trabalhadas) if len(horas_trabalhadas) > 1 else 0,
                'minimo': min(horas_trabalhadas),
                'maximo': max(horas_trabalhadas),
                'amplitude': max(horas_trabalhadas) - min(horas_trabalhadas)
            },
            'banco_horas': {
                'media': statistics.mean(banco_horas),
                'mediana': statistics.median(banco_horas),
                'desvio_padrao': statistics.stdev(banco_horas) if len(banco_horas) > 1 else 0,
                'minimo': min(banco_horas),
                'maximo': max(banco_horas),
                'positivos': len([x for x in banco_horas if x > 0]),
                'negativos': len([x for x in banco_horas if x < 0]),
                'zeros': len([x for x in banco_horas if x == 0])
            },
            'horas_extras': {
                'media': statistics.mean(horas_extras),
                'mediana': statistics.median(horas_extras),
                'total': sum(horas_extras),
                'funcionarios_com_extras': len([x for x in horas_extras if x > 0])
            },
            'erros': {
                'media_erros_por_funcionario': statistics.mean(dias_erro),
                'total_erros': sum(dias_erro),
                'funcionarios_sem_erro': len([x for x in dias_erro if x == 0]),
                'funcionarios_com_erro': len([x for x in dias_erro if x > 0]),
                'max_erros_funcionario': max(dias_erro)
            }
        }
    
    def analisar_por_tipo_funcionario(self):
        """
        Analisa diferenças entre funcionários corretos e problemáticos.
        """
        if not self.dados:
            return
        
        resultados = self.dados['resultados_calculos']
        
        corretos = [r for r in resultados if r['tipo_teste'] == 'correto']
        problematicos = [r for r in resultados if r['tipo_teste'] == 'problematico']
        
        def calcular_metricas_grupo(grupo):
            if not grupo:
                return {}
            
            horas = [r['horas_trabalhadas_total'] for r in grupo]
            erros = [r['dias_com_erro'] for r in grupo]
            extras = [r['horas_extras_total'] for r in grupo]
            
            return {
                'quantidade': len(grupo),
                'horas_media': statistics.mean(horas),
                'horas_total': sum(horas),
                'erros_media': statistics.mean(erros),
                'erros_total': sum(erros),
                'extras_media': statistics.mean(extras),
                'extras_total': sum(extras)
            }
        
        self.estatisticas_avancadas['comparacao_tipos'] = {
            'corretos': calcular_metricas_grupo(corretos),
            'problematicos': calcular_metricas_grupo(problematicos)
        }
    
    def identificar_padroes_erro(self):
        """
        Identifica padrões nos erros detectados.
        """
        if not self.dados:
            return
        
        resultados = self.dados['resultados_calculos']
        
        # Analisar tipos de erro mais comuns
        tipos_erro = {}
        funcionarios_problema = []
        
        for resultado in resultados:
            if resultado['dias_com_erro'] > 0:
                funcionarios_problema.append({
                    'nome': resultado['funcionario_nome'],
                    'tipo': resultado['tipo_teste'],
                    'erros': resultado['dias_com_erro'],
                    'horas': resultado['horas_trabalhadas_total']
                })
                
                # Analisar detalhes dos erros
                for dia in resultado['detalhes_dias']:
                    if not dia['valido']:
                        for erro in dia['erros']:
                            if erro not in tipos_erro:
                                tipos_erro[erro] = 0
                            tipos_erro[erro] += 1
        
        self.estatisticas_avancadas['padroes_erro'] = {
            'tipos_erro_frequencia': tipos_erro,
            'funcionarios_problema': sorted(funcionarios_problema, 
                                          key=lambda x: x['erros'], reverse=True),
            'taxa_erro_geral': len(funcionarios_problema) / len(resultados) * 100
        }
    
    def calcular_metricas_performance(self):
        """
        Calcula métricas de performance do sistema.
        """
        if not self.dados:
            return
        
        total_batidas = self.dados['estatisticas']['total_batidas']
        total_erros = self.dados['estatisticas']['erros_detectados']
        total_funcionarios = self.dados['estatisticas']['total_funcionarios']
        
        self.estatisticas_avancadas['performance'] = {
            'taxa_sucesso_batidas': ((total_batidas - total_erros) / total_batidas * 100) if total_batidas > 0 else 0,
            'taxa_erro_batidas': (total_erros / total_batidas * 100) if total_batidas > 0 else 0,
            'media_batidas_por_funcionario': total_batidas / total_funcionarios,
            'robustez_sistema': 'Alta' if total_erros < total_batidas * 0.1 else 'Média' if total_erros < total_batidas * 0.2 else 'Baixa'
        }
    
    def gerar_relatorio_executivo(self):
        """
        Gera relatório executivo com insights principais.
        """
        logger.info("\n" + "=" * 80)
        logger.info("📊 RELATÓRIO EXECUTIVO - ANÁLISE ESTATÍSTICA AVANÇADA")
        logger.info("=" * 80)
        
        if not self.estatisticas_avancadas:
            logger.error("❌ Dados não processados. Execute as análises primeiro.")
            return
        
        # Resumo geral
        logger.info("📈 RESUMO EXECUTIVO:")
        logger.info(f"   Sistema testado com {self.dados['metadata']['total_funcionarios']} funcionários")
        logger.info(f"   Total de {self.dados['metadata']['total_batidas']} batidas processadas")
        logger.info(f"   Taxa de sucesso: {self.estatisticas_avancadas['performance']['taxa_sucesso_batidas']:.1f}%")
        logger.info(f"   Robustez do sistema: {self.estatisticas_avancadas['performance']['robustez_sistema']}")
        
        # Horas trabalhadas
        horas_stats = self.estatisticas_avancadas['horas_trabalhadas']
        logger.info(f"\n⏰ ANÁLISE DE HORAS TRABALHADAS:")
        logger.info(f"   Média por funcionário: {horas_stats['media']:.2f}h")
        logger.info(f"   Desvio padrão: {horas_stats['desvio_padrao']:.2f}h")
        logger.info(f"   Amplitude: {horas_stats['amplitude']:.2f}h ({horas_stats['minimo']:.2f}h - {horas_stats['maximo']:.2f}h)")
        
        # Banco de horas
        banco_stats = self.estatisticas_avancadas['banco_horas']
        logger.info(f"\n💰 ANÁLISE BANCO DE HORAS:")
        logger.info(f"   Saldo médio: {banco_stats['media']:.2f}h")
        logger.info(f"   Funcionários com saldo positivo: {banco_stats['positivos']}")
        logger.info(f"   Funcionários com saldo negativo: {banco_stats['negativos']}")
        logger.info(f"   Funcionários com saldo zero: {banco_stats['zeros']}")
        
        # Horas extras
        extras_stats = self.estatisticas_avancadas['horas_extras']
        logger.info(f"\n🕐 ANÁLISE HORAS EXTRAS:")
        logger.info(f"   Total de horas extras: {extras_stats['total']:.2f}h")
        logger.info(f"   Funcionários que fizeram extras: {extras_stats['funcionarios_com_extras']}")
        logger.info(f"   Média por funcionário: {extras_stats['media']:.2f}h")
        
        # Comparação por tipo
        comp = self.estatisticas_avancadas['comparacao_tipos']
        logger.info(f"\n🔍 COMPARAÇÃO POR TIPO:")
        logger.info(f"   FUNCIONÁRIOS CORRETOS:")
        logger.info(f"     Horas médias: {comp['corretos']['horas_media']:.2f}h")
        logger.info(f"     Erros médios: {comp['corretos']['erros_media']:.1f}")
        logger.info(f"   FUNCIONÁRIOS PROBLEMÁTICOS:")
        logger.info(f"     Horas médias: {comp['problematicos']['horas_media']:.2f}h")
        logger.info(f"     Erros médios: {comp['problematicos']['erros_media']:.1f}")
        
        # Padrões de erro
        erros = self.estatisticas_avancadas['padroes_erro']
        logger.info(f"\n⚠️  ANÁLISE DE ERROS:")
        logger.info(f"   Taxa geral de erro: {erros['taxa_erro_geral']:.1f}%")
        logger.info(f"   Funcionários com problemas: {len(erros['funcionarios_problema'])}")
        
        if erros['tipos_erro_frequencia']:
            logger.info(f"   Tipos de erro mais comuns:")
            for tipo, freq in sorted(erros['tipos_erro_frequencia'].items(), 
                                   key=lambda x: x[1], reverse=True)[:3]:
                logger.info(f"     - {tipo}: {freq} ocorrências")
    
    def gerar_recomendacoes(self):
        """
        Gera recomendações baseadas na análise.
        """
        logger.info(f"\n💡 RECOMENDAÇÕES:")
        
        performance = self.estatisticas_avancadas['performance']
        
        if performance['taxa_sucesso_batidas'] > 95:
            logger.info("   ✅ Sistema demonstra excelente robustez")
            logger.info("   ✅ Pronto para ambiente de produção")
        elif performance['taxa_sucesso_batidas'] > 90:
            logger.info("   ⚠️  Sistema robusto, mas com pontos de melhoria")
            logger.info("   📋 Revisar validações para casos específicos")
        else:
            logger.info("   ❌ Sistema precisa de melhorias na robustez")
            logger.info("   🔧 Implementar validações adicionais")
        
        # Recomendações específicas
        banco_stats = self.estatisticas_avancadas['banco_horas']
        if banco_stats['negativos'] > banco_stats['positivos']:
            logger.info("   📊 Muitos funcionários com saldo negativo")
            logger.info("   💼 Considerar revisão de jornadas ou políticas")
        
        extras_stats = self.estatisticas_avancadas['horas_extras']
        if extras_stats['funcionarios_com_extras'] > len(self.dados['resultados_calculos']) * 0.5:
            logger.info("   🕐 Alto volume de horas extras detectado")
            logger.info("   👥 Considerar contratação ou redistribuição")
    
    def executar_analise_completa(self):
        """
        Executa análise estatística completa.
        """
        logger.info("🔄 Executando análise estatística completa...")
        
        self.calcular_estatisticas_descritivas()
        self.analisar_por_tipo_funcionario()
        self.identificar_padroes_erro()
        self.calcular_metricas_performance()
        
        self.gerar_relatorio_executivo()
        self.gerar_recomendacoes()
        
        logger.info("\n✅ Análise estatística concluída!")
    
    def salvar_analise(self, nome_arquivo=None):
        """
        Salva análise em arquivo JSON.
        """
        if not nome_arquivo:
            nome_arquivo = f"analise_estatistica_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            dados_analise = {
                'metadata': {
                    'data_analise': datetime.now().isoformat(),
                    'arquivo_origem': self.dados['metadata'] if self.dados else None
                },
                'estatisticas_avancadas': self.estatisticas_avancadas
            }
            
            with open(nome_arquivo, 'w', encoding='utf-8') as f:
                json.dump(dados_analise, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Análise salva em: {nome_arquivo}")
            return nome_arquivo
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar análise: {e}")
            return None

def main():
    """
    Função principal para análise estatística.
    """
    print("📊 ANÁLISE ESTATÍSTICA AVANÇADA - TESTE ROBUSTO")
    print("=" * 60)
    
    # Procurar arquivo de resultados mais recente
    import glob
    arquivos_resultado = glob.glob("resultados_teste_robusto_*.json")
    
    if not arquivos_resultado:
        print("❌ Nenhum arquivo de resultados encontrado.")
        print("   Execute primeiro o teste robusto para gerar dados.")
        return False
    
    # Usar arquivo mais recente
    arquivo_mais_recente = max(arquivos_resultado)
    print(f"📁 Usando arquivo: {arquivo_mais_recente}")
    
    # Executar análise
    analise = AnaliseEstatisticaRobusto(arquivo_mais_recente)
    analise.executar_analise_completa()
    
    # Salvar análise
    analise.salvar_analise()
    
    return True

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
