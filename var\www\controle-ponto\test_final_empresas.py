#!/usr/bin/env python3
import paramiko
import os

def test_final_empresas():
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🧪 TESTE FINAL - FUNCIONALIDADE EMPRESAS")
        print("=" * 50)
        
        # 1. Verificar se o serviço está rodando
        print("1. Verificando serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl status controle-ponto --no-pager -l')
        status = stdout.read().decode()
        if "active (running)" in status:
            print("✅ Serviço está rodando")
        else:
            print("❌ Problema com o serviço")
            print(status)
        
        # 2. Verificar se as rotas estão registradas
        print("\n2. Verificando rotas de empresas...")
        stdin, stdout, stderr = ssh.exec_command('grep -n "empresas" /var/www/controle-ponto/app_configuracoes.py | head -10')
        routes = stdout.read().decode()
        print("Rotas encontradas:")
        print(routes)
        
        # 3. Verificar se a tabela empresas existe
        print("\n3. Verificando tabela no banco...")
        stdin, stdout, stderr = ssh.exec_command('mysql -u root -p@Ric6109 controle_ponto -e "DESCRIBE empresas;" 2>/dev/null')
        table_desc = stdout.read().decode()
        if table_desc:
            print("✅ Tabela empresas existe")
            print(table_desc)
        else:
            print("❌ Tabela empresas não encontrada")
        
        # 4. Verificar templates
        print("\n4. Verificando templates...")
        stdin, stdout, stderr = ssh.exec_command('ls -la /var/www/controle-ponto/templates/configuracoes/ | grep empresa')
        templates = stdout.read().decode()
        print("Templates de empresas:")
        print(templates)
        
        # 5. Verificar JavaScript no template
        print("\n5. Verificando JavaScript...")
        stdin, stdout, stderr = ssh.exec_command('tail -20 /var/www/controle-ponto/templates/configuracoes/index.html')
        js_check = stdout.read().decode()
        if "bootstrap.Tab" in js_check:
            print("✅ JavaScript Bootstrap correto implementado")
        else:
            print("❌ JavaScript pode ter problemas")
        print("Últimas linhas do template:")
        print(js_check)
        
        # 6. Teste de conectividade HTTP
        print("\n6. Testando conectividade HTTP...")
        
        # Teste página principal de configurações
        stdin, stdout, stderr = ssh.exec_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/configuracoes/')
        code1 = stdout.read().decode().strip()
        print(f"GET /configuracoes/ → {code1}")
        
        # Teste página de empresas
        stdin, stdout, stderr = ssh.exec_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/configuracoes/empresas')
        code2 = stdout.read().decode().strip()
        print(f"GET /configuracoes/empresas → {code2}")
        
        # Teste página nova empresa
        stdin, stdout, stderr = ssh.exec_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/configuracoes/empresas/nova')
        code3 = stdout.read().decode().strip()
        print(f"GET /configuracoes/empresas/nova → {code3}")
        
        # 7. Verificar logs recentes
        print("\n7. Verificando logs recentes...")
        stdin, stdout, stderr = ssh.exec_command('journalctl -u controle-ponto -n 5 --no-pager')
        logs = stdout.read().decode()
        print("Logs recentes:")
        print(logs)
        
        # 8. Verificar se há erros Python
        print("\n8. Testando importação Python...")
        test_import = '''
        cd /var/www/controle-ponto
        python3 -c "
import sys
sys.path.append('.')
try:
    from app_configuracoes import configuracoes_bp
    print('✅ Blueprint configuracoes importado com sucesso')
    print(f'URL prefix: {configuracoes_bp.url_prefix}')
except Exception as e:
    print(f'❌ Erro ao importar: {e}')
"
        '''
        stdin, stdout, stderr = ssh.exec_command(test_import)
        import_result = stdout.read().decode()
        import_error = stderr.read().decode()
        print("Resultado do teste de importação:")
        print(import_result)
        if import_error:
            print("Erros:")
            print(import_error)
        
        ssh.close()
        
        print("\n" + "=" * 50)
        print("📋 RESUMO DO TESTE")
        print("=" * 50)
        
        # Análise dos resultados
        all_good = True
        
        if code1 == "302":
            print("✅ Página de configurações: OK (redirecionamento para login)")
        else:
            print(f"❌ Página de configurações: Problema (código {code1})")
            all_good = False
            
        if code2 == "302":
            print("✅ Página de empresas: OK (redirecionamento para login)")
        else:
            print(f"❌ Página de empresas: Problema (código {code2})")
            all_good = False
            
        if code3 == "302":
            print("✅ Página nova empresa: OK (redirecionamento para login)")
        else:
            print(f"❌ Página nova empresa: Problema (código {code3})")
            all_good = False
        
        if all_good:
            print("\n🎉 TODOS OS TESTES PASSARAM!")
            print("🔥 A ABA EMPRESAS DEVE ESTAR FUNCIONANDO!")
            print("\n📝 INSTRUÇÕES PARA TESTE:")
            print("1. Acesse: http://************:5000")
            print("2. Faça login como admin")
            print("3. Vá para Configurações")
            print("4. Clique na aba 'Empresas'")
            print("5. A aba deve abrir mostrando as opções de empresas")
        else:
            print("\n❌ ALGUNS TESTES FALHARAM")
            print("Verifique os logs acima para mais detalhes")
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")

if __name__ == "__main__":
    test_final_empresas()
