#!/bin/bash

# Script para configurar acesso SSH no servidor RLPONTO-WEB
# Servidor: ************
# Este script deve ser executado no servidor

echo "=== CONFIGURAÇÃO DE ACESSO SSH PARA RLPONTO-WEB ==="
echo ""

# Chave pública SSH gerada no cliente
SSH_PUBLIC_KEY="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC9xQ3QszSo3EyW88tLKe+Pcc5WwTdoDEYGtohbcskOVlNUCdfaqYryigGqqDmlWIfGW8t+RWpeK08Ay6uIAYQU9gw1KsOo/qMLThIdyH/luMlKV6w8tnTULnq/S8CLJRHxkR5LPjJv5QShq8fBRAXaR6P1D5Mexzeq5jqPF2yk0y7yPugwEFyorHDf5iKlOVaHBW8TYxfvtLT+jev2dJjJNl0PjA5uIxX8jY33uUf0bfZfTyVDSW27aw+S/Lp5bU44fsQi0YO7DiOMnxR51PN2JobcBm3Muhv7EdEkuYCxmSmc4A2lDkqDS4w0VY3cunnt58R8v4s8HfxwFl/MF9nLTbjKrDHGvrCKGtxcmwfSZFXfj8uhbm7BJjK5apmDku8U9yPt57jQIAtIYajfOH0tgFxVvZCaaFF6qgy0Hg+qhXiDWOVS71IBYGBANPT0m07JaSU70W20s8ZNvbINEWx2y0YJMMeN0wgI8q/F2TFdq5s12RyWj77of4EVS9q2gRu4L79D1RDpCywbHvptbv9Hn5EN7NMIQBJ69kE1kLZrA7YqXo7sttrpB2b8N+mlB9mX/VJ1+bXNV19Tl+/3STFESUaJDhJBHaPRQnLcRwM0SYXfksP06LuLP3U7FjgX1aGq4JIWUfr8W26Nbv7Oxc3ugWyZ4TZmK11IEJfmykX1fw== rlponto-web-access"

# Configurar para o usuário root
echo "🔧 Configurando acesso SSH para o usuário root..."
mkdir -p /root/.ssh
chmod 700 /root/.ssh

# Adicionar chave pública ao authorized_keys
echo "$SSH_PUBLIC_KEY" >> /root/.ssh/authorized_keys
chmod 600 /root/.ssh/authorized_keys

echo "✅ Chave SSH adicionada para o usuário root"

# Configurar para o usuário cavalcrod
echo "🔧 Configurando acesso SSH para o usuário cavalcrod..."
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# Adicionar chave pública ao authorized_keys do cavalcrod
echo "$SSH_PUBLIC_KEY" >> /home/<USER>/.ssh/authorized_keys
chmod 600 /home/<USER>/.ssh/authorized_keys
chown -R cavalcrod:cavalcrod /home/<USER>/.ssh

echo "✅ Chave SSH adicionada para o usuário cavalcrod"

# Verificar configuração do SSH
echo ""
echo "🔍 Verificando configuração do SSH..."

# Verificar se o SSH está rodando
if systemctl is-active --quiet ssh; then
    echo "✅ Serviço SSH está ativo"
else
    echo "❌ Serviço SSH não está ativo"
    echo "   Iniciando serviço SSH..."
    systemctl start ssh
    systemctl enable ssh
fi

# Verificar configuração do sshd_config
echo ""
echo "📋 Configurações importantes do SSH:"
echo "   PubkeyAuthentication: $(grep -E '^PubkeyAuthentication' /etc/ssh/sshd_config || echo 'yes (padrão)')"
echo "   PasswordAuthentication: $(grep -E '^PasswordAuthentication' /etc/ssh/sshd_config || echo 'yes (padrão)')"
echo "   PermitRootLogin: $(grep -E '^PermitRootLogin' /etc/ssh/sshd_config || echo 'yes (padrão)')"

# Reiniciar SSH para aplicar mudanças
echo ""
echo "🔄 Reiniciando serviço SSH..."
systemctl restart ssh

echo ""
echo "✅ Configuração SSH concluída!"
echo ""
echo "📝 TESTE DE CONEXÃO:"
echo "   ssh root@************"
echo "   ssh cavalcrod@************"
echo ""
echo "🔐 Agora você pode acessar o servidor sem senha!"
