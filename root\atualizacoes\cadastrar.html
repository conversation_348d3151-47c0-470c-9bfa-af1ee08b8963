<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastrar Funcionário - Controle de Ponto</title>
    <link rel="stylesheet" href="/static/style-cadastrar.css">
    <script src="/static/busca_cep.js" defer></script>
    <style>
        /* Estilos para o modal de biometria */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.7);
        }
        
        .modal-content {
            background-color: #1a2634;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #4fbdba;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            color: #fff;
            box-shadow: 0 0 20px rgba(79, 189, 186, 0.5);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #4fbdba;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .modal-header h3 {
            margin: 0;
            color: #4fbdba;
        }
        
        .close {
            color: #4fbdba;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #fff;
        }
        
        .biometria-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .fingerprint-display {
            width: 200px;
            height: 250px;
            border: 2px solid #4fbdba;
            border-radius: 5px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #0f1925;
            position: relative;
        }
        
        .fingerprint-display img {
            max-width: 100%;
            max-height: 100%;
        }
        
        .fingerprint-status {
            position: absolute;
            bottom: 5px;
            width: 100%;
            text-align: center;
            font-size: 14px;
            color: #4fbdba;
        }
        
        .finger-selector {
            display: flex;
            justify-content: center;
            margin: 15px 0;
        }
        
        .finger-btn {
            background-color: #1a2634;
            color: #fff;
            border: 1px solid #4fbdba;
            padding: 8px 15px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .finger-btn.active {
            background-color: #4fbdba;
            color: #1a2634;
        }
        
        .finger-btn:hover:not(.active) {
            background-color: #2a3644;
        }
        
        .modal-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        
        .modal-btn {
            background-color: #4fbdba;
            color: #1a2634;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            min-width: 120px;
            text-align: center;
        }
        
        .modal-btn:hover {
            background-color: #3da8a6;
        }
        
        .modal-btn:disabled {
            background-color: #2a3644;
            color: #6c757d;
            cursor: not-allowed;
        }
        
        .status-message {
            text-align: center;
            margin: 10px 0;
            padding: 8px;
            border-radius: 5px;
        }
        
        .status-message.error {
            background-color: rgba(220, 53, 69, 0.2);
            color: #ff6b6b;
        }
        
        .status-message.success {
            background-color: rgba(40, 167, 69, 0.2);
            color: #51cf66;
        }
        
        .status-message.info {
            background-color: rgba(79, 189, 186, 0.2);
            color: #4fbdba;
        }
        
        /* Indicador de biometria capturada */
        .biometria-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
            background-color: #dc3545;
        }
        
        .biometria-indicator.captured {
            background-color: #28a745;
        }
        
        /* Botão de captura de biometria */
        .biometria-btn {
            background-color: #4fbdba;
            color: #1a2634;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 20px;
            margin-bottom: 20px;
            width: 100%;
            transition: all 0.3s;
        }
        
        .biometria-btn:hover {
            background-color: #3da8a6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <button class="tab-btn active" onclick="openTab('identificacao')">Identificação Pessoal</button>
            <button class="tab-btn" onclick="openTab('documentos')">Documentos Trabalhistas</button>
            <button class="tab-btn" onclick="openTab('contato')">Informações de Contato</button>
            <button class="tab-btn" onclick="openTab('profissional')">Informações Profissionais</button>
            <button class="tab-btn" onclick="openTab('acesso')">Controle de Acesso</button>
            <button class="tab-btn" onclick="openTab('epis')">Equipamentos/EPIs</button>
            <button class="tab-btn" onclick="openTab('biometria')">Biometria</button>
        </div>
        <div class="main-content">
            <h2>Cadastrar Funcionário</h2>
            <form method="POST" action="/cadastrar" id="cadastroForm">
                <!-- Identificação Pessoal -->
                <div class="tab-content active" id="identificacao">
                    <h3>Identificação Pessoal</h3>
                    <label>Nome Completo *</label>
                    <input type="text" name="nome_completo" value="{{ data.nome_completo|default('') }}" 
                           pattern="[A-Za-zÀ-ÿ\s]+" inputmode="text" required 
                           oninput="toUpperCase(this); validateName(this);">

                    <label>CPF *</label>
                    <input type="text" name="cpf" value="{{ data.cpf|default('') }}" 
                           maxlength="14" inputmode="numeric" required 
                           oninput="applyCpfMask(this); validateCpf(this);">

                    <label>RG / CNH *</label>
                    <input type="text" name="rg" value="{{ data.rg|default('') }}" 
                           pattern="\d{1,12}" inputmode="numeric" required 
                           oninput="validateNumber(this);">

                    <label>Data de Nascimento *</label>
                    <input type="date" name="data_nascimento" value="{{ data.data_nascimento|default('') }}" 
                           required oninput="validateDate(this);">

                    <label>Sexo *</label>
                    <select name="sexo" required onchange="autoFillNationality(this);">
                        <option value="" disabled selected>Selecione</option>
                        <option value="M" {{ "selected" if data.sexo == "M" }}>Masculino</option>
                        <option value="F" {{ "selected" if data.sexo == "F" }}>Feminino</option>
                        <option value="Outro" {{ "selected" if data.sexo == "Outro" }}>Outro</option>
                    </select>

                    <label>Estado Civil *</label>
                    <select name="estado_civil" required>
                        <option value="" disabled selected>Selecione</option>
                        <option value="Solteiro" {{ "selected" if data.estado_civil == "Solteiro" }}>Solteiro</option>
                        <option value="Casado" {{ "selected" if data.estado_civil == "Casado" }}>Casado</option>
                        <option value="Divorciado" {{ "selected" if data.estado_civil == "Divorciado" }}>Divorciado</option>
                        <option value="Viúvo" {{ "selected" if data.estado_civil == "Viúvo" }}>Viúvo</option>
                    </select>

                    <label>Nacionalidade *</label>
                    <input type="text" name="nacionalidade" id="nacionalidade" value="{{ data.nacionalidade|default('') }}" 
                           pattern="[A-Za-zÀ-ÿ\s]+" inputmode="text" required 
                           oninput="toUpperCase(this); validateName(this);">
                </div>
                <!-- Documentos Trabalhistas -->
                <div class="tab-content" id="documentos">
                    <h3>Documentos Trabalhistas</h3>
                    <label>CTPS - Número *</label>
                    <input type="text" name="ctps_numero" value="{{ data.ctps_numero|default('') }}" 
                           pattern="\d{7}" inputmode="numeric" required placeholder="Ex: 1234567"
                           oninput="validateNumberCTPS(this);">

                    <label>CTPS - Série/UF *</label>
                    <input type="text" name="ctps_serie_uf" value="{{ data.ctps_serie_uf|default('') }}" 
                           pattern="\d{4}/[A-Z]{2}" required placeholder="Ex: 0001/SP"
                           oninput="formatCtpsSerieUf(this); toUpperCase(this); validateCtpsSerieUf(this);">

                    <label>PIS/PASEP *</label>
                    <input type="text" name="pis_pasep" value="{{ data.pis_pasep|default('') }}" 
                           maxlength="14" inputmode="numeric" required placeholder="Ex: 123.45678.90-1"
                           oninput="applyPisMask(this); validatePis(this);">
                </div>
                <!-- Informações de Contato -->
                <div class="tab-content" id="contato">
                    <h3>Informações de Contato</h3>
                    <label>CEP *</label>
                    <input type="text" name="endereco_cep" id="cep" value="{{ data.endereco_cep|default('') }}" 
                           maxlength="9" required oninput="applyCepMask(this);" onblur="buscarCep(this);">

                    <label>Rua</label>
                    <input type="text" name="endereco_rua" id="rua" value="{{ data.endereco_rua|default('') }}" 
                           oninput="toUpperCase(this);">

                    <label>Bairro</label>
                    <input type="text" name="endereco_bairro" id="bairro" value="{{ data.endereco_bairro|default('') }}" 
                           oninput="toUpperCase(this);">

                    <label>Cidade</label>
                    <input type="text" name="endereco_cidade" id="cidade" value="{{ data.endereco_cidade|default('') }}" 
                           oninput="toUpperCase(this);">

                    <label>Estado *</label>
                    <select name="endereco_estado" required>
                        <option value="" disabled selected>Selecione</option>
                        {% for estado in ["AC", "AL", "AP", "AM", "BA", "CE", "DF", "ES", "GO", "MA", "MT", "MS", "MG", "PA", "PB", "PR", "PE", "PI", "RJ", "RN", "RS", "RO", "RR", "SC", "SP", "SE", "TO"] %}
                        <option value="{{ estado }}" {{ "selected" if data.endereco_estado == estado }}>{{ estado }}</option>
                        {% endfor %}
                    </select>

                    <label>Telefone 1 *</label>
                    <input type="text" name="telefone1" value="{{ data.telefone1|default('') }}" 
                           maxlength="15" required oninput="applyPhoneMask(this); toUpperCase(this);">

                    <label>Telefone 2</label>
                    <input type="text" name="telefone2" value="{{ data.telefone2|default('') }}" 
                           maxlength="15" oninput="applyPhoneMask(this); toUpperCase(this);">

                    <label>E-mail</label>
                    <input type="email" name="email" value="{{ data.email|default('') }}" 
                           oninput="toLowerCase(this);">
                </div>
                <!-- Informações Profissionais -->
                <div class="tab-content" id="profissional">
                    <h3>Informações Profissionais</h3>
                    <label>Cargo *</label>
                    <input type="text" name="cargo" value="{{ data.cargo|default('') }}" required 
                           oninput="toUpperCase(this);">

                    <label>Setor/Obra *</label>
                    <input type="text" name="setor_obra" value="{{ data.setor_obra|default('') }}" required 
                           oninput="toUpperCase(this);">

                    <label>Matrícula Empresa *</label>
                    <input type="text" name="matricula_empresa" id="matricula_empresa" value="{{ proxima_matricula|default('') }}" readonly required>

                    <label>Data de Admissão *</label>
                    <input type="date" name="data_admissao" value="{{ data.data_admissao|default('') }}" required>

                    <label>Tipo de Contrato *</label>
                    <select name="tipo_contrato" required>
                        <option value="" disabled selected>Selecione</option>
                        <option value="CLT" {{ "selected" if data.tipo_contrato == "CLT" }}>CLT</option>
                        <option value="PJ" {{ "selected" if data.tipo_contrato == "PJ" }}>PJ</option>
                        <option value="Estágio" {{ "selected" if data.tipo_contrato == "Estágio" }}>Estágio</option>
                        <option value="Temporário" {{ "selected" if data.tipo_contrato == "Temporário" }}>Temporário</option>
                    </select>
                </div>
                <!-- Controle de Acesso -->
                <div class="tab-content" id="acesso">
                    <h3>Controle de Acesso</h3>
                    
                    <label>Jornada Seg a Qui - Entrada *</label>
                    <input type="time" name="jornada_seg_qui_entrada" value="{{ data.jornada_seg_qui_entrada|default('07:00') }}" required>
                    <label>Jornada Seg a Qui - Saída *</label>
                    <input type="time" name="jornada_seg_qui_saida" value="{{ data.jornada_seg_qui_saida|default('17:00') }}" required>

                    <label>Jornada Sexta - Entrada *</label>
                    <input type="time" name="jornada_sex_entrada" value="{{ data.jornada_sex_entrada|default('07:00') }}" required>
                    <label>Jornada Sexta - Saída *</label>
                    <input type="time" name="jornada_sex_saida" value="{{ data.jornada_sex_saida|default('16:00') }}" required>

                    <label>Intervalo - Início *</label>
                    <input type="time" name="jornada_intervalo_entrada" value="{{ data.jornada_intervalo_entrada|default('12:00') }}" required>
                    <label>Intervalo - Fim *</label>
                    <input type="time" name="jornada_intervalo_saida" value="{{ data.jornada_intervalo_saida|default('13:00') }}" required>

                    <label>Nível de Acesso *</label>
                    <select name="nivel_acesso" required>
                        <option value="" disabled selected>Selecione</option>
                        <option value="Funcionário" {{ "selected" if data.nivel_acesso == "Funcionário" }}>Funcionário</option>
                        <option value="Supervisão" {{ "selected" if data.nivel_acesso == "Supervisão" }}>Supervisão</option>
                        <option value="Gerência" {{ "selected" if data.nivel_acesso == "Gerência" }}>Gerência</option>
                    </select>

                    <label>Turno *</label>
                    <select name="turno" required>
                        <option value="" disabled selected>Selecione</option>
                        <option value="Diurno" {{ "selected" if data.turno == "Diurno" }}>Diurno</option>
                        <option value="Noturno" {{ "selected" if data.turno == "Noturno" }}>Noturno</option>
                        <option value="Misto" {{ "selected" if data.turno == "Misto" }}>Misto</option>
                    </select>

                    <label>Tolerância de Ponto (minutos) *</label>
                    <input type="number" name="tolerancia_ponto" value="{{ data.tolerancia_ponto|default('5') }}" min="0" required>

                    <div class="checkbox-group">
                        <label>Banco de Horas</label>
                        <input type="checkbox" name="banco_horas" {{ "checked" if data.banco_horas }}>
                    </div>
                    <div class="checkbox-group">
                        <label>Hora Extra</label>
                        <input type="checkbox" name="hora_extra" {{ "checked" if data.hora_extra }}>
                    </div>

                    <label>Status do Cadastro *</label>
                    <select name="status_cadastro" required>
                        <option value="" disabled selected>Selecione</option>
                        <option value="Ativo" {{ "selected" if data.status_cadastro == "Ativo" }}>Ativo</option>
                        <option value="Inativo" {{ "selected" if data.status_cadastro == "Inativo" }}>Inativo</option>
                    </select>
                </div>
                <!-- Equipamentos/EPIs -->
                <div class="tab-content" id="epis">
                    <h3>Equipamentos/EPIs (Opcional)</h3>
                    <label>EPI/Equipamento</label>
                    <input type="text" name="epi_nome" value="{{ data.epi_nome|default('') }}" 
                           oninput="toUpperCase(this);">

                    <label>CA</label>
                    <input type="text" name="epi_ca" value="{{ data.epi_ca|default('') }}" 
                           oninput="toUpperCase(this);">

                    <label>Data de Entrega</label>
                    <input type="date" name="epi_data_entrega" value="{{ data.epi_data_entrega|default('') }}">

                    <label>Data de Validade</label>
                    <input type="date" name="epi_data_validade" value="{{ data.epi_data_validade|default('') }}">

                    <label>Observações</label>
                    <textarea name="epi_observacoes" oninput="toUpperCase(this);">{{ data.epi_observacoes|default('') }}</textarea>
                </div>
                
                <!-- Biometria -->
                <div class="tab-content" id="biometria">
                    <h3>Biometria</h3>
                    <p>Capture as impressões digitais do funcionário utilizando o leitor biométrico ZK4500.</p>
                    
                    <div class="biometria-status">
                        <div>
                            <span>Dedo 1:</span>
                            <span class="biometria-indicator" id="dedo1-indicator"></span>
                            <span id="dedo1-status">Não capturado</span>
                        </div>
                        <div>
                            <span>Dedo 2:</span>
                            <span class="biometria-indicator" id="dedo2-indicator"></span>
                            <span id="dedo2-status">Não capturado</span>
                        </div>
                    </div>
                    
                    <button type="button" class="biometria-btn" onclick="openBiometriaModal()">Capturar Biometria</button>
                    
                    <!-- Campos ocultos para armazenar os dados biométricos -->
                    <input type="hidden" name="digital_dedo1" id="digital_dedo1" value="{{ data.digital_dedo1|default('') }}">
                    <input type="hidden" name="digital_dedo2" id="digital_dedo2" value="{{ data.digital_dedo2|default('') }}">
                </div>
                
                <!-- Botões -->
                <div class="form-buttons">
                    <button type="submit">Cadastrar</button>
                    <button type="button" class="voltar-btn" onclick="window.location.href='/'">Voltar</button>
                </div>
            </form>
            {% if errors %}
            <div class="errors">
                <h3>Erros no formulário:</h3>
                <ul>
                    {% for error in errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Modal de Captura de Biometria -->
    <div id="biometriaModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Captura de Biometria</h3>
                <span class="close" onclick="closeBiometriaModal()">&times;</span>
            </div>
            
            <div class="status-message info" id="biometria-message">
                Selecione um dedo e posicione-o no leitor biométrico.
            </div>
            
            <div class="finger-selector">
                <button type="button" class="finger-btn active" id="dedo1-btn" onclick="selectFinger(1)">Dedo 1</button>
                <button type="button" class="finger-btn" id="dedo2-btn" onclick="selectFinger(2)">Dedo 2</button>
            </div>
            
            <div class="biometria-container">
                <div class="fingerprint-display" id="fingerprint-display">
                    <img id="fingerprint-image" src="/static/images/fingerprint-placeholder.png" alt="Impressão Digital">
                    <div class="fingerprint-status" id="fingerprint-status">Aguardando leitura...</div>
                </div>
            </div>
            
            <div class="modal-buttons">
                <button type="button" class="modal-btn" id="limpar-btn" onclick="limparBiometria()">Limpar</button>
                <button type="button" class="modal-btn" id="capturar-btn" onclick="capturarBiometria()">Capturar</button>
                <button type="button" class="modal-btn" id="salvar-btn" onclick="salvarBiometria()" disabled>Salvar Biometria</button>
            </div>
        </div>
    </div>
    
    <script>
        // Variáveis globais para controle de biometria
        let currentFinger = 1; // 1 ou 2
        let fingerData = {
            dedo1: null,
            dedo2: null
        };
        let biometriaCapturada = false;
        let biometriaServico = null;
        
        function openTab(tabName) {
            document.querySelectorAll(".tab-content").forEach(tab => tab.classList.remove("active"));
            document.querySelectorAll(".tab-btn").forEach(btn => btn.classList.remove("active"));
            document.getElementById(tabName).classList.add("active");
            document.querySelector(`button[onclick="openTab('${tabName}')"]`).classList.add("active");
        }

        function toUpperCase(input) {
            input.value = input.value.toUpperCase();
        }

        function toLowerCase(input) {
            input.value = input.value.toLowerCase();
        }

        // Funções para o modal de biometria
        function openBiometriaModal() {
            document.getElementById("biometriaModal").style.display = "block";
            inicializarLeitorBiometrico();
            selectFinger(1); // Começa com o dedo 1 selecionado
        }
        
        function closeBiometriaModal() {
            document.getElementById("biometriaModal").style.display = "none";
            if (biometriaServico) {
                // Desconectar do serviço de biometria
                desconectarLeitorBiometrico();
            }
        }
        
        function selectFinger(finger) {
            currentFinger = finger;
            document.querySelectorAll(".finger-btn").forEach(btn => btn.classList.remove("active"));
            document.getElementById(`dedo${finger}-btn`).classList.add("active");
            
            // Atualiza a mensagem e o status
            document.getElementById("biometria-message").className = "status-message info";
            document.getElementById("biometria-message").textContent = `Posicione o Dedo ${finger} no leitor biométrico.`;
            document.getElementById("fingerprint-status").textContent = `Aguardando leitura do Dedo ${finger}...`;
            
            // Verifica se já existe biometria capturada para este dedo
            if (fingerData[`dedo${finger}`]) {
                document.getElementById("fingerprint-image").src = fingerData[`dedo${finger}`].imagemPreview;
                document.getElementById("fingerprint-status").textContent = `Dedo ${finger} já capturado`;
                document.getElementById("capturar-btn").disabled = false;
            } else {
                document.getElementById("fingerprint-image").src = "/static/images/fingerprint-placeholder.png";
                document.getElementById("capturar-btn").disabled = false;
            }
            
            // Atualiza o estado do botão salvar
            atualizarBotaoSalvar();
        }
        
        function inicializarLeitorBiometrico() {
            // Esta função seria responsável por inicializar a comunicação com o serviço local
            // que controla o leitor biométrico ZK4500
            try {
                // Simulação: Na implementação real, aqui seria feita uma chamada para o serviço local
                console.log("Inicializando leitor biométrico...");
                document.getElementById("biometria-message").textContent = "Conectando ao leitor biométrico...";
                
                // Simulação de conexão bem-sucedida após 1 segundo
                setTimeout(() => {
                    biometriaServico = {
                        conectado: true,
                        dispositivo: "ZK4500",
                        status: "pronto"
                    };
                    document.getElementById("biometria-message").className = "status-message success";
                    document.getElementById("biometria-message").textContent = "Leitor biométrico conectado. Selecione um dedo e clique em Capturar.";
                }, 1000);
            } catch (error) {
                console.error("Erro ao inicializar leitor biométrico:", error);
                document.getElementById("biometria-message").className = "status-message error";
                document.getElementById("biometria-message").textContent = "Erro ao conectar ao leitor biométrico. Verifique se o dispositivo está conectado.";
            }
        }
        
        function desconectarLeitorBiometrico() {
            // Esta função seria responsável por desconectar o leitor biométrico
            try {
                // Simulação: Na implementação real, aqui seria feita uma chamada para desconectar o serviço local
                console.log("Desconectando leitor biométrico...");
                biometriaServico = null;
            } catch (error) {
                console.error("Erro ao desconectar leitor biométrico:", error);
            }
        }
        
        function capturarBiometria() {
            // Esta função seria responsável por capturar a biometria do dedo atual
            if (!biometriaServico || !biometriaServico.conectado) {
                document.getElementById("biometria-message").className = "status-message error";
                document.getElementById("biometria-message").textContent = "Leitor biométrico não conectado. Tente novamente.";
                return;
            }
            
            document.getElementById("capturar-btn").disabled = true;
            document.getElementById("biometria-message").className = "status-message info";
            document.getElementById("biometria-message").textContent = `Capturando impressão digital do Dedo ${currentFinger}...`;
            document.getElementById("fingerprint-status").textContent = "Capturando...";
            
            // Simulação: Na implementação real, aqui seria feita uma chamada para o serviço local
            // para capturar a impressão digital
            setTimeout(() => {
                // Simulação de captura bem-sucedida
                const dadosBiometricos = {
                    // Na implementação real, aqui seriam os dados biométricos reais
                    template: `template_dedo${currentFinger}_${Date.now()}`,
                    qualidade: 85,
                    imagemPreview: "/static/images/fingerprint-captured.png", // Caminho para uma imagem de exemplo
                    timestamp: new Date().toISOString()
                };
                
                // Armazena os dados biométricos
                fingerData[`dedo${currentFinger}`] = dadosBiometricos;
                
                // Atualiza a interface
                document.getElementById("fingerprint-image").src = dadosBiometricos.imagemPreview;
                document.getElementById("biometria-message").className = "status-message success";
                document.getElementById("biometria-message").textContent = `Impressão digital do Dedo ${currentFinger} capturada com sucesso!`;
                document.getElementById("fingerprint-status").textContent = `Qualidade: ${dadosBiometricos.qualidade}%`;
                document.getElementById("capturar-btn").disabled = false;
                
                // Atualiza o indicador de status
                document.getElementById(`dedo${currentFinger}-indicator`).classList.add("captured");
                document.getElementById(`dedo${currentFinger}-status`).textContent = "Capturado";
                
                // Atualiza o estado do botão salvar
                atualizarBotaoSalvar();
                
                // Se este for o primeiro dedo, sugere mudar para o segundo
                if (currentFinger === 1 && !fingerData.dedo2) {
                    setTimeout(() => {
                        selectFinger(2);
                    }, 1500);
                }
            }, 2000);
        }
        
        function limparBiometria() {
            // Esta função limpa a biometria do dedo atual
            fingerData[`dedo${currentFinger}`] = null;
            
            // Atualiza a interface
            document.getElementById("fingerprint-image").src = "/static/images/fingerprint-placeholder.png";
            document.getElementById("biometria-message").className = "status-message info";
            document.getElementById("biometria-message").textContent = `Impressão digital do Dedo ${currentFinger} foi limpa. Pronto para nova captura.`;
            document.getElementById("fingerprint-status").textContent = "Aguardando leitura...";
            
            // Atualiza o indicador de status
            document.getElementById(`dedo${currentFinger}-indicator`).classList.remove("captured");
            document.getElementById(`dedo${currentFinger}-status`).textContent = "Não capturado";
            
            // Atualiza o estado do botão salvar
            atualizarBotaoSalvar();
        }
        
        function atualizarBotaoSalvar() {
            // Habilita o botão salvar apenas se pelo menos um dedo foi capturado
            const temBiometria = fingerData.dedo1 !== null || fingerData.dedo2 !== null;
            document.getElementById("salvar-btn").disabled = !temBiometria;
        }
        
        function salvarBiometria() {
            // Esta função salva as biometrias capturadas nos campos ocultos do formulário
            if (fingerData.dedo1) {
                document.getElementById("digital_dedo1").value = JSON.stringify(fingerData.dedo1);
            }
            
            if (fingerData.dedo2) {
                document.getElementById("digital_dedo2").value = JSON.stringify(fingerData.dedo2);
            }
            
            // Fecha o modal
            closeBiometriaModal();
            
            // Atualiza os indicadores na página principal
            atualizarIndicadoresBiometria();
            
            // Exibe mensagem de sucesso
            alert("Biometrias salvas com sucesso!");
        }
        
        function atualizarIndicadoresBiometria() {
            // Atualiza os indicadores de biometria na página principal
            if (fingerData.dedo1) {
                document.getElementById("dedo1-indicator").classList.add("captured");
                document.getElementById("dedo1-status").textContent = "Capturado";
            } else {
                document.getElementById("dedo1-indicator").classList.remove("captured");
                document.getElementById("dedo1-status").textContent = "Não capturado";
            }
            
            if (fingerData.dedo2) {
                document.getElementById("dedo2-indicator").classList.add("captured");
                document.getElementById("dedo2-status").textContent = "Capturado";
            } else {
                document.getElementById("dedo2-indicator").classList.remove("captured");
                document.getElementById("dedo2-status").textContent = "Não capturado";
            }
        }
        
        // Inicializa os indicadores de biometria ao carregar a página
        document.addEventListener("DOMContentLoaded", function() {
            // Verifica se já existem dados de biometria (em caso de recarregamento da página)
            const dedo1Data = document.getElementById("digital_dedo1").value;
            const dedo2Data = document.getElementById("digital_dedo2").value;
            
            if (dedo1Data) {
                try {
                    fingerData.dedo1 = JSON.parse(dedo1Data);
                    document.getElementById("dedo1-indicator").classList.add("captured");
                    document.getElementById("dedo1-status").textContent = "Capturado";
                } catch (e) {
                    console.error("Erro ao processar dados do dedo 1:", e);
                }
            }
            
            if (dedo2Data) {
                try {
                    fingerData.dedo2 = JSON.parse(dedo2Data);
                    document.getElementById("dedo2-indicator").classList.add("captured");
                    document.getElementById("dedo2-status").textContent = "Capturado";
                } catch (e) {
                    console.error("Erro ao processar dados do dedo 2:", e);
                }
            }
        });
        
        // Funções de validação existentes
        function validateName(input) {
            const regex = /^[A-Za-zÀ-ÿ\s]+$/;
            if (!regex.test(input.value)) {
                input.classList.add("input-error");
                input.setCustomValidity("Apenas letras, espaços e acentos são permitidos.");
            } else {
                input.classList.remove("input-error");
                input.setCustomValidity("");
            }
        }

        function validateNumber(input) {
            const regex = /^\d{1,12}$/;
            if (!regex.test(input.value)) {
                input.classList.add("input-error");
                input.setCustomValidity("Apenas números são permitidos (máximo 12 dígitos).");
            } else {
                input.classList.remove("input-error");
                input.setCustomValidity("");
            }
        }

        function validateNumberCTPS(input) {
            const regex = /^\d{7}$/;
            if (!regex.test(input.value)) {
                input.classList.add("input-error");
                input.setCustomValidity("CTPS deve ter exatamente 7 dígitos numéricos.");
            } else {
                input.classList.remove("input-error");
                input.setCustomValidity("");
            }
        }

        function formatCtpsSerieUf(input) {
            let value = input.value.replace(/[^0-9A-Z\/]/g, '');
            
            if (value.length > 0) {
                // Formata como DDDD/UF
                const numPart = value.replace(/[^0-9]/g, '').substring(0, 4);
                let ufPart = value.replace(/[^A-Z]/g, '').substring(0, 2);
                
                if (numPart.length === 4 && !value.includes('/')) {
                    value = numPart + '/' + ufPart;
                } else if (value.includes('/')) {
                    const parts = value.split('/');
                    value = parts[0].substring(0, 4) + '/' + parts[1].substring(0, 2);
                }
            }
            
            input.value = value;
        }

        function validateCtpsSerieUf(input) {
            const regex = /^\d{4}\/[A-Z]{2}$/;
            if (!regex.test(input.value)) {
                input.classList.add("input-error");
                input.setCustomValidity("Formato deve ser DDDD/UF (ex: 0050/AM).");
            } else {
                input.classList.remove("input-error");
                input.setCustomValidity("");
            }
        }

        function validateDate(input) {
            const date = new Date(input.value);
            const today = new Date();
            
            if (isNaN(date.getTime())) {
                input.classList.add("input-error");
                input.setCustomValidity("Data inválida.");
            } else if (date > today) {
                input.classList.add("input-error");
                input.setCustomValidity("A data não pode ser futura.");
            } else {
                input.classList.remove("input-error");
                input.setCustomValidity("");
            }
        }

        function applyCpfMask(input) {
            let value = input.value.replace(/\D/g, '');
            
            if (value.length > 0) {
                value = value.substring(0, 11);
                value = value.replace(/(\d{3})(\d)/, '$1.$2');
                value = value.replace(/(\d{3})(\d)/, '$1.$2');
                value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
            }
            
            input.value = value;
        }

        function validateCpf(input) {
            const cpf = input.value.replace(/\D/g, '');
            
            if (cpf.length !== 11) {
                input.classList.add("input-error");
                input.setCustomValidity("CPF deve ter 11 dígitos.");
                return;
            }
            
            // Validação simplificada do CPF
            if (/^(\d)\1{10}$/.test(cpf)) {
                input.classList.add("input-error");
                input.setCustomValidity("CPF inválido.");
                return;
            }
            
            input.classList.remove("input-error");
            input.setCustomValidity("");
        }

        function applyPisMask(input) {
            let value = input.value.replace(/\D/g, '');
            
            if (value.length > 0) {
                value = value.substring(0, 11);
                value = value.replace(/(\d{3})(\d)/, '$1.$2');
                value = value.replace(/(\d{5})(\d)/, '$1.$2');
                value = value.replace(/(\d{5}\.\d{2})(\d{1})$/, '$1-$2');
            }
            
            input.value = value;
        }

        function validatePis(input) {
            const pis = input.value.replace(/\D/g, '');
            
            if (pis.length !== 11) {
                input.classList.add("input-error");
                input.setCustomValidity("PIS/PASEP deve ter 11 dígitos.");
            } else {
                input.classList.remove("input-error");
                input.setCustomValidity("");
            }
        }

        function applyCepMask(input) {
            let value = input.value.replace(/\D/g, '');
            
            if (value.length > 0) {
                value = value.substring(0, 8);
                value = value.replace(/^(\d{5})(\d)/, '$1-$2');
            }
            
            input.value = value;
        }

        function applyPhoneMask(input) {
            let value = input.value.replace(/\D/g, '');
            
            if (value.length > 0) {
                if (value.length > 11) {
                    value = value.substring(0, 11);
                }
                
                if (value.length > 2) {
                    value = '(' + value.substring(0, 2) + ')' + value.substring(2);
                }
                
                if (value.length > 9) {
                    if (value.length === 13) { // Com 9 dígito
                        value = value.substring(0, 9) + '-' + value.substring(9);
                    } else { // Sem 9 dígito
                        value = value.substring(0, 8) + '-' + value.substring(8);
                    }
                }
            }
            
            input.value = value;
        }

        function autoFillNationality(select) {
            if (select.value && document.getElementById('nacionalidade').value === '') {
                document.getElementById('nacionalidade').value = 'BRASILEIRA';
            }
        }
    </script>
</body>
</html>
