# 🖨️ INSTRUÇÕES PARA CORRIGIR PÁGINA DE IMPRESSÃO DE PONTO

## 📋 Problema
A página de impressão de ponto estava retornando erro 404 porque:
- Rota `/ponto-admin/funcionario/<id>/imprimir` não existia
- Template `relatorio_funcionario.html` não existia

## ✅ Solução Implementada

### 1. Conectar ao Servidor
```bash
ssh user@************
# Senha: @Ric6109
```

### 2. Fazer Backup
```bash
cd /var/www/controle-ponto
cp app_ponto_admin.py app_ponto_admin.py.backup.$(date +%Y%m%d_%H%M%S)
```

### 3. <PERSON><PERSON>r Template relatorio_funcionario.html
```bash
cd /var/www/controle-ponto/templates/ponto_admin

cat > relatorio_funcionario.html << 'EOF'
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Ponto - {{ funcionario.nome_completo }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 15px; margin-bottom: 20px; }
        .funcionario-info { background: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 8px; }
        .registros-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .registros-table th, .registros-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        .registros-table th { background: #f8f9fa; font-weight: 600; }
        .action-buttons { position: fixed; top: 20px; right: 20px; display: flex; gap: 10px; }
        .btn { padding: 10px 15px; text-decoration: none; border-radius: 5px; border: none; cursor: pointer; }
        .btn-print { background: #28a745; color: white; }
        .btn-back { background: #6c757d; color: white; }
        .periodo-info { text-align: center; background: #e3f2fd; padding: 10px; margin-bottom: 20px; border-radius: 6px; }
        @media print { .action-buttons { display: none !important; } }
    </style>
</head>
<body>
    <div class="action-buttons">
        <button onclick="window.print()" class="btn btn-print">🖨️ Imprimir</button>
        <a href="{{ url_for('ponto_admin.detalhes_funcionario', funcionario_id=funcionario.id) }}" class="btn btn-back">← Voltar</a>
    </div>

    <div class="header">
        <h1>RELATÓRIO DE REGISTROS DE PONTO</h1>
        <p><strong>{{ funcionario.empresa_nome or 'EMPRESA' }}</strong></p>
        <p>Gerado em: {{ data_atual.strftime('%d/%m/%Y %H:%M') if data_atual else '' }}</p>
    </div>

    <div class="funcionario-info">
        <h2>{{ funcionario.nome_completo }}</h2>
        <p><strong>Matrícula:</strong> {{ funcionario.matricula or 'N/A' }}</p>
        <p><strong>Cargo:</strong> {{ funcionario.cargo or 'N/A' }}</p>
        <p><strong>CPF:</strong> {{ funcionario.cpf or 'N/A' }}</p>
        <p><strong>Empresa:</strong> {{ funcionario.empresa_nome or 'N/A' }}</p>
    </div>

    {% if data_inicio or data_fim %}
    <div class="periodo-info">
        <strong>Período:</strong> 
        {{ data_inicio or 'Início' }} até {{ data_fim or data_atual.strftime('%d/%m/%Y') if data_atual else '' }}
    </div>
    {% endif %}

    <h3>Registros de Ponto ({{ registros|length }} registros)</h3>
    
    {% if registros %}
    <table class="registros-table">
        <thead>
            <tr>
                <th>Data</th>
                <th>Dia Semana</th>
                <th>Entrada</th>
                <th>Saída Almoço</th>
                <th>Retorno Almoço</th>
                <th>Saída</th>
                <th>Horas Trabalhadas</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            {% for registro in registros %}
            <tr>
                <td>{{ registro.data_registro.strftime('%d/%m/%Y') if registro.data_registro else 'N/A' }}</td>
                <td>{{ registro.dia_semana or 'N/A' }}</td>
                <td>{{ registro.entrada.strftime('%H:%M') if registro.entrada else '-' }}</td>
                <td>{{ registro.saida_almoco.strftime('%H:%M') if registro.saida_almoco else '-' }}</td>
                <td>{{ registro.retorno_almoco.strftime('%H:%M') if registro.retorno_almoco else '-' }}</td>
                <td>{{ registro.saida.strftime('%H:%M') if registro.saida else '-' }}</td>
                <td>{{ registro.horas_trabalhadas or '-' }}</td>
                <td>{{ registro.status_texto or 'Incompleto' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div style="text-align: center; padding: 20px; color: #666;">
        Nenhum registro encontrado para o período selecionado.
    </div>
    {% endif %}

    <div style="margin-top: 30px; text-align: center; font-size: 10pt; color: #666; border-top: 1px solid #ddd; padding-top: 15px;">
        <p>Sistema de Controle de Ponto - RLPONTO-WEB v1.0</p>
        <p>Relatório gerado automaticamente em {{ data_atual.strftime('%d/%m/%Y %H:%M:%S') if data_atual else '' }}</p>
    </div>
</body>
</html>
EOF

echo "✅ Template criado com sucesso"
```

### 4. Adicionar Rota no app_ponto_admin.py
```bash
cd /var/www/controle-ponto

# Adicionar a nova rota antes da seção "# ROTAS PARA SISTEMA DE JUSTIFICATIVAS"
nano app_ponto_admin.py
```

**Adicionar este código antes da linha `# ================================================================`:**

```python
@ponto_admin_bp.route('/funcionario/<int:funcionario_id>/imprimir')
@require_login
def imprimir_ponto_funcionario(funcionario_id):
    """Página de impressão de ponto de um funcionário"""
    try:
        from datetime import datetime
        
        funcionario = get_funcionario_detalhes(funcionario_id)
        if not funcionario:
            flash('Funcionário não encontrado', 'error')
            return redirect(url_for('ponto_admin.index'))

        # Parâmetros de filtro
        data_inicio = request.args.get('data_inicio')
        data_fim = request.args.get('data_fim')

        registros = get_registros_ponto_funcionario(funcionario_id, data_inicio, data_fim)

        return render_template('ponto_admin/relatorio_funcionario.html',
                             funcionario=funcionario,
                             registros=registros,
                             data_inicio=data_inicio,
                             data_fim=data_fim,
                             data_atual=datetime.now())
    except Exception as e:
        logger.error(f"Erro ao gerar página de impressão do funcionário {funcionario_id}: {e}")
        flash('Erro ao gerar página de impressão', 'error')
        return redirect(url_for('ponto_admin.index'))
```

### 5. Atualizar Função relatorio_funcionario
**Encontrar a função `relatorio_funcionario` e modificar:**

1. Adicionar import datetime:
```python
def relatorio_funcionario(funcionario_id):
    """Gerar relatório de ponto de um funcionário"""
    try:
        from datetime import datetime  # ← ADICIONAR ESTA LINHA
```

2. Atualizar o render_template:
```python
        return render_template('ponto_admin/relatorio_funcionario.html',
                             funcionario=funcionario,
                             registros=registros,
                             data_inicio=data_inicio,
                             data_fim=data_fim,
                             data_atual=datetime.now())  # ← ADICIONAR ESTA LINHA
```

### 6. Reiniciar Serviço
```bash
sudo systemctl restart controle-ponto
sudo systemctl status controle-ponto
```

### 7. Testar
```bash
curl -I http://localhost:5000/ponto-admin/
```

## 🎯 Resultado Esperado
- ✅ Página de impressão funcionando em: `/ponto-admin/funcionario/<id>/imprimir`
- ✅ Botão "Imprimir Ponto" funcionando na página de detalhes do funcionário
- ✅ Layout otimizado para impressão
- ✅ Suporte a filtros de data

## 🔍 URLs de Teste
- Página principal: `http://************/ponto-admin/`
- Detalhes funcionário: `http://************/ponto-admin/funcionario/35`
- Impressão: `http://************/ponto-admin/funcionario/35/imprimir`
