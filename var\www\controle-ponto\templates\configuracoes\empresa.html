{% extends "base.html" %}

{% block title %}{{ titulo }} - RLPONTO-WEB{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/configuracoes.css') }}">
<style>
    /* ======================================================
       ESTILOS ESPECÍFICOS PARA CONFIGURAÇÃO DA EMPRESA
       ====================================================== */
    
    .empresa-config-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
        background: #f8fafc;
        min-height: 100vh;
    }
    
    .config-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .config-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 700;
    }
    
    .config-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }
    
    .config-content {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        overflow: hidden;
    }
    
    .config-form {
        padding: 2.5rem;
    }
    
    .form-section {
        margin-bottom: 3rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
    
    .section-title i {
        color: #667eea;
        font-size: 1.3rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }
    
    .form-control {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 0.5rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-control.textarea {
        min-height: 120px;
        resize: vertical;
        font-family: inherit;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .form-row.two-cols {
        grid-template-columns: 1fr 1fr;
    }
    
    .form-row.three-cols {
        grid-template-columns: 1fr 1fr 1fr;
    }
    
    /* UPLOAD DE LOGOTIPO */
    .logotipo-section {
        background: #f7fafc;
        padding: 2rem;
        border-radius: 0.75rem;
        border: 2px dashed #cbd5e0;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .logotipo-preview {
        margin-bottom: 1.5rem;
    }
    
    .logotipo-atual {
        max-width: 200px;
        max-height: 120px;
        border-radius: 0.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .logotipo-placeholder {
        width: 200px;
        height: 120px;
        background: #e2e8f0;
        border: 2px dashed #a0aec0;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #718096;
        font-size: 3rem;
        margin: 0 auto;
    }
    
    .upload-instructions {
        color: #4a5568;
        font-size: 0.9rem;
        margin-top: 1rem;
    }
    
    .file-input-wrapper {
        position: relative;
        display: inline-block;
        margin-top: 1rem;
    }
    
    .file-input {
        position: absolute;
        left: -9999px;
    }
    
    .file-input-label {
        display: inline-block;
        padding: 0.75rem 1.5rem;
        background: #667eea;
        color: white;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;
    }
    
    .file-input-label:hover {
        background: #5a67d8;
        transform: translateY(-1px);
    }
    
    .remove-logo-btn {
        margin-left: 0.75rem;
        padding: 0.75rem 1.5rem;
        background: #e53e3e;
        color: white;
        border: none;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;
    }
    
    .remove-logo-btn:hover {
        background: #c53030;
        transform: translateY(-1px);
    }
    
    /* BOTÕES DE AÇÃO */
    .action-buttons {
        background: #f7fafc;
        padding: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
    }
    
    .btn-primary {
        padding: 1rem 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .btn-secondary {
        padding: 1rem 2rem;
        background: #e2e8f0;
        color: #4a5568;
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-secondary:hover {
        background: #cbd5e0;
        color: #2d3748;
        text-decoration: none;
    }
    
    /* ALERTAS */
    .alert {
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
        border: 1px solid transparent;
    }
    
    .alert-success {
        background: #f0fff4;
        color: #22543d;
        border-color: #9ae6b4;
    }
    
    .alert-error {
        background: #fed7d7;
        color: #742a2a;
        border-color: #feb2b2;
    }
    
    /* RESPONSIVIDADE */
    @media (max-width: 768px) {
        .empresa-config-container {
            padding: 1rem;
        }
        
        .config-header {
            padding: 1.5rem;
        }
        
        .config-header h1 {
            font-size: 2rem;
        }
        
        .config-form {
            padding: 1.5rem;
        }
        
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: stretch;
        }
        
        .logotipo-atual {
            max-width: 150px;
            max-height: 90px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="empresa-config-container">
    <!-- Header -->
    <div class="config-header">
        <h1><i class="fas fa-building"></i> {{ titulo }}</h1>
        <p>Configure as informações e regras específicas da sua empresa</p>
    </div>

    <!-- Alertas -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'error' if category == 'error' else 'success' }}">
                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }}"></i>
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Conteúdo Principal -->
    <div class="config-content">
        <form method="POST" enctype="multipart/form-data" class="config-form">
            
            <!-- Seção: Informações Básicas -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    Informações Básicas
                </h3>
                
                <div class="form-group">
                    <label for="nome_empresa" class="form-label">Nome da Empresa *</label>
                    <input type="text" 
                           id="nome_empresa" 
                           name="nome_empresa" 
                           class="form-control" 
                           value="{{ empresa.nome_empresa or '' }}" 
                           required 
                           placeholder="Digite o nome da sua empresa">
                </div>
                
                <div class="form-group">
                    <label for="regras_especificas" class="form-label">Regras Específicas da Empresa</label>
                    <textarea id="regras_especificas" 
                              name="regras_especificas" 
                              class="form-control textarea" 
                              placeholder="Descreva as regras específicas, políticas de ponto, tolerâncias especiais, etc.">{{ empresa.regras_especificas or '' }}</textarea>
                </div>
            </div>

            <!-- Seção: Logotipo -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-image"></i>
                    Logotipo da Empresa
                </h3>
                
                <div class="logotipo-section">
                    <div class="logotipo-preview">
                        {% if empresa.tem_logotipo %}
                            <img src="{{ url_for('empresa_config.obter_logotipo') }}" 
                                 alt="Logotipo da empresa" 
                                 class="logotipo-atual" 
                                 id="logoPreview">
                        {% else %}
                            <div class="logotipo-placeholder" id="logoPlaceholder">
                                <i class="fas fa-image"></i>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="upload-instructions">
                        <strong>Formatos aceitos:</strong> PNG, JPG, JPEG, GIF, SVG<br>
                        <strong>Tamanho máximo:</strong> 5MB<br>
                        <strong>Recomendado:</strong> 400x200 pixels para melhor visualização
                    </div>
                    
                    <div class="file-input-wrapper">
                        <input type="file" 
                               id="logotipo" 
                               name="logotipo" 
                               class="file-input" 
                               accept=".png,.jpg,.jpeg,.gif,.svg"
                               onchange="previewLogo(this)">
                        <label for="logotipo" class="file-input-label">
                            <i class="fas fa-upload"></i> Escolher Arquivo
                        </label>
                        
                        {% if empresa.tem_logotipo %}
                            <button type="button" class="remove-logo-btn" onclick="removerLogotipo()">
                                <i class="fas fa-trash"></i> Remover Logotipo
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Seção: Tolerâncias e Políticas -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-clock"></i>
                    Tolerâncias e Políticas de Ponto
                </h3>
                
                <div class="form-row two-cols">
                    <div class="form-group">
                        <label for="tolerancia_atraso" class="form-label">Tolerância para Atraso (minutos) *</label>
                        <input type="number" 
                               id="tolerancia_atraso" 
                               name="tolerancia_atraso" 
                               class="form-control" 
                               value="{{ empresa.tolerancia_atraso or 10 }}" 
                               min="0" 
                               max="60" 
                               required>
                    </div>
                    
                    <div class="form-group">
                        <label for="tolerancia_saida_antecipada" class="form-label">Tolerância para Saída Antecipada (minutos) *</label>
                        <input type="number" 
                               id="tolerancia_saida_antecipada" 
                               name="tolerancia_saida_antecipada" 
                               class="form-control" 
                               value="{{ empresa.tolerancia_saida_antecipada or 10 }}" 
                               min="0" 
                               max="60" 
                               required>
                    </div>
                </div>
            </div>

            <!-- Seção: Horários Padrão -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-calendar-alt"></i>
                    Horários Padrão de Trabalho
                </h3>
                
                <div class="form-row three-cols">
                    <div class="form-group">
                        <label for="jornada_trabalho_padrao" class="form-label">Jornada de Trabalho Padrão *</label>
                        <input type="time" 
                               id="jornada_trabalho_padrao" 
                               name="jornada_trabalho_padrao" 
                               class="form-control" 
                               value="{{ empresa.jornada_trabalho_padrao.strftime('%H:%M') if empresa.jornada_trabalho_padrao else '08:00' }}" 
                               required>
                    </div>
                    
                    <div class="form-group">
                        <label for="intervalo_almoco_inicio" class="form-label">Início do Almoço *</label>
                        <input type="time" 
                               id="intervalo_almoco_inicio" 
                               name="intervalo_almoco_inicio" 
                               class="form-control" 
                               value="{{ empresa.intervalo_almoco_inicio.strftime('%H:%M') if empresa.intervalo_almoco_inicio else '12:00' }}" 
                               required>
                    </div>
                    
                    <div class="form-group">
                        <label for="intervalo_almoco_fim" class="form-label">Fim do Almoço *</label>
                        <input type="time" 
                               id="intervalo_almoco_fim" 
                               name="intervalo_almoco_fim" 
                               class="form-control" 
                               value="{{ empresa.intervalo_almoco_fim.strftime('%H:%M') if empresa.intervalo_almoco_fim else '13:00' }}" 
                               required>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Botões de Ação -->
    <div class="action-buttons">
        <a href="{{ url_for('configuracoes.index') }}" class="btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
        
        <button type="submit" form="configForm" class="btn-primary">
            <i class="fas fa-save"></i> Salvar Configurações
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Preview do logotipo
    function previewLogo(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const preview = document.getElementById('logoPreview');
                const placeholder = document.getElementById('logoPlaceholder');
                
                if (preview) {
                    preview.src = e.target.result;
                } else if (placeholder) {
                    placeholder.outerHTML = `<img src="${e.target.result}" alt="Preview do logotipo" class="logotipo-atual" id="logoPreview">`;
                }
            };
            
            reader.readAsDataURL(input.files[0]);
        }
    }
    
    // Remover logotipo
    function removerLogotipo() {
        if (confirm('Tem certeza que deseja remover o logotipo da empresa?')) {
            fetch('{{ url_for("empresa_config.remover_logotipo") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remover preview e mostrar placeholder
                    const preview = document.getElementById('logoPreview');
                    if (preview) {
                        preview.outerHTML = `<div class="logotipo-placeholder" id="logoPlaceholder"><i class="fas fa-image"></i></div>`;
                    }
                    
                    // Remover botão de remoção
                    const removeBtn = document.querySelector('.remove-logo-btn');
                    if (removeBtn) {
                        removeBtn.remove();
                    }
                    
                    // Limpar input de arquivo
                    document.getElementById('logotipo').value = '';
                    
                    // Mostrar mensagem de sucesso
                    showAlert('success', 'Logotipo removido com sucesso!');
                } else {
                    showAlert('error', data.message || 'Erro ao remover logotipo');
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                showAlert('error', 'Erro de comunicação com o servidor');
            });
        }
    }
    
    // Função para mostrar alertas
    function showAlert(type, message) {
        const alertClass = type === 'error' ? 'alert-error' : 'alert-success';
        const iconClass = type === 'error' ? 'fa-exclamation-triangle' : 'fa-check-circle';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass}`;
        alert.innerHTML = `<i class="fas ${iconClass}"></i> ${message}`;
        
        const container = document.querySelector('.empresa-config-container');
        const header = container.querySelector('.config-header');
        container.insertBefore(alert, header.nextSibling);
        
        // Remover alerta após 5 segundos
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
    
    // Submeter formulário
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('.config-form');
        const submitBtn = document.querySelector('.btn-primary[type="submit"]');
        
        if (submitBtn) {
            submitBtn.addEventListener('click', function(e) {
                e.preventDefault();
                form.submit();
            });
        }
    });
    
    // Validação em tempo real
    document.getElementById('tolerancia_atraso').addEventListener('input', function() {
        validateRange(this, 0, 60);
    });
    
    document.getElementById('tolerancia_saida_antecipada').addEventListener('input', function() {
        validateRange(this, 0, 60);
    });
    
    function validateRange(input, min, max) {
        const value = parseInt(input.value);
        if (value < min || value > max) {
            input.setCustomValidity(`Valor deve estar entre ${min} e ${max}`);
        } else {
            input.setCustomValidity('');
        }
    }
</script>
{% endblock %} 