"""
RLPONTO-WEB v1.0 - Módulo de Configuração da Empresa

Implementação dos Parâmetros Mínimos de Configuração (MCP) para gestão
centralizada da empresa incluindo upload de logotipo e regras específicas.

Desenvolvido por: AiNexus Tecnologia
Autor: <PERSON>ues - Full Stack Developer
Data: 11/01/2025
© 2025 AiNexus Tecnologia. Todos os direitos reservados.
"""

import os
import logging
import base64
import mimetypes
from datetime import datetime, time
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash
from werkzeug.utils import secure_filename
from utils.auth import require_admin
from utils.database import get_db_connection
from utils.config import Config
import json

# Configurar logger
logger = logging.getLogger('controle-ponto.empresa-config')

# Criar Blueprint
empresa_config_bp = Blueprint('empresa_config', __name__, url_prefix='/configuracoes/empresa')

# Configurações para upload de arquivo
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'svg'}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

def allowed_file(filename):
    """Verifica se o arquivo é permitido"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_time_format(time_str):
    """Valida formato de hora HH:MM"""
    try:
        if not time_str:
            return None
        return datetime.strptime(time_str, '%H:%M').time()
    except ValueError:
        return None

def get_empresa_config():
    """Obtém configurações da empresa"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT * FROM cad_empresas 
            WHERE ativa = TRUE 
            ORDER BY data_criacao DESC 
            LIMIT 1
        """)
        
        empresa = cursor.fetchone()
        conn.close()
        
        if empresa:
            # Converter dados para dict se necessário
            if hasattr(empresa, 'keys'):
                return dict(empresa)
            else:
                # Se for tupla, mapear para dict
                columns = ['id', 'nome_empresa', 'logotipo', 'logotipo_mime_type', 
                          'regras_especificas', 'tolerancia_atraso', 'tolerancia_saida_antecipada',
                          'jornada_trabalho_padrao', 'intervalo_almoco_inicio', 'intervalo_almoco_fim',
                          'data_criacao', 'data_atualizacao', 'ativa']
                return dict(zip(columns, empresa))
        
        return None
        
    except Exception as e:
        logger.error(f"Erro ao obter configuração da empresa: {e}")
        return None

def save_empresa_config(dados):
    """Salva configurações da empresa no banco"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Verificar se já existe configuração
        cursor.execute("SELECT id FROM cad_empresas WHERE ativa = TRUE LIMIT 1")
        empresa_existente = cursor.fetchone()
        
        if empresa_existente:
            # Atualizar configuração existente
            cursor.execute("""
                UPDATE cad_empresas SET
                    nome_empresa = %s,
                    regras_especificas = %s,
                    tolerancia_atraso = %s,
                    tolerancia_saida_antecipada = %s,
                    jornada_trabalho_padrao = %s,
                    intervalo_almoco_inicio = %s,
                    intervalo_almoco_fim = %s,
                    data_atualizacao = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (
                dados['nome_empresa'],
                dados['regras_especificas'],
                dados['tolerancia_atraso'],
                dados['tolerancia_saida_antecipada'],
                dados['jornada_trabalho_padrao'],
                dados['intervalo_almoco_inicio'],
                dados['intervalo_almoco_fim'],
                empresa_existente['id'] if hasattr(empresa_existente, 'keys') else empresa_existente[0]
            ))
            empresa_id = empresa_existente['id'] if hasattr(empresa_existente, 'keys') else empresa_existente[0]
        else:
            # Criar nova configuração
            cursor.execute("""
                INSERT INTO cad_empresas 
                (nome_empresa, regras_especificas, tolerancia_atraso, tolerancia_saida_antecipada,
                 jornada_trabalho_padrao, intervalo_almoco_inicio, intervalo_almoco_fim)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                dados['nome_empresa'],
                dados['regras_especificas'],
                dados['tolerancia_atraso'],
                dados['tolerancia_saida_antecipada'],
                dados['jornada_trabalho_padrao'],
                dados['intervalo_almoco_inicio'],
                dados['intervalo_almoco_fim']
            ))
            empresa_id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        
        logger.info(f"Configuração da empresa salva com sucesso - ID: {empresa_id}")
        return True, empresa_id
        
    except Exception as e:
        logger.error(f"Erro ao salvar configuração da empresa: {e}")
        return False, None

def save_logotipo(empresa_id, arquivo, mime_type):
    """Salva logotipo da empresa no banco"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE cad_empresas SET
                logotipo = %s,
                logotipo_mime_type = %s,
                data_atualizacao = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (arquivo, mime_type, empresa_id))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Logotipo salvo para empresa ID: {empresa_id}")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao salvar logotipo: {e}")
        return False

# ========================================
# ROTAS PRINCIPAIS
# ========================================

@empresa_config_bp.route('/')
@require_admin
def index():
    """Página principal de configuração da empresa"""
    try:
        empresa = get_empresa_config()
        
        # Se não existe configuração, criar dados padrão
        if not empresa:
            empresa = {
                'nome_empresa': 'Sua Empresa',
                'regras_especificas': 'Defina aqui as regras específicas da sua empresa',
                'tolerancia_atraso': 10,
                'tolerancia_saida_antecipada': 10,
                'jornada_trabalho_padrao': time(8, 0),
                'intervalo_almoco_inicio': time(12, 0),
                'intervalo_almoco_fim': time(13, 0),
                'tem_logotipo': False
            }
        else:
            empresa['tem_logotipo'] = empresa.get('logotipo') is not None
        
        context = {
            'titulo': 'Configuração da Empresa',
            'empresa': empresa
        }
        
        return render_template('configuracoes/empresa.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar configuração da empresa: {e}")
        flash('Erro ao carregar dados da empresa', 'error')
        return redirect(url_for('configuracoes.index'))

@empresa_config_bp.route('/salvar', methods=['POST'])
@require_admin
def salvar():
    """Salva configurações da empresa"""
    try:
        # Validar dados do formulário
        dados = {
            'nome_empresa': request.form.get('nome_empresa', '').strip(),
            'regras_especificas': request.form.get('regras_especificas', '').strip(),
            'tolerancia_atraso': int(request.form.get('tolerancia_atraso', 10)),
            'tolerancia_saida_antecipada': int(request.form.get('tolerancia_saida_antecipada', 10)),
            'jornada_trabalho_padrao': validate_time_format(request.form.get('jornada_trabalho_padrao')),
            'intervalo_almoco_inicio': validate_time_format(request.form.get('intervalo_almoco_inicio')),
            'intervalo_almoco_fim': validate_time_format(request.form.get('intervalo_almoco_fim'))
        }
        
        # Validações
        erros = []
        
        if not dados['nome_empresa']:
            erros.append('Nome da empresa é obrigatório')
        
        if dados['tolerancia_atraso'] < 0 or dados['tolerancia_atraso'] > 60:
            erros.append('Tolerância de atraso deve estar entre 0 e 60 minutos')
            
        if dados['tolerancia_saida_antecipada'] < 0 or dados['tolerancia_saida_antecipada'] > 60:
            erros.append('Tolerância de saída antecipada deve estar entre 0 e 60 minutos')
        
        if not dados['jornada_trabalho_padrao']:
            erros.append('Jornada de trabalho padrão é obrigatória')
            
        if not dados['intervalo_almoco_inicio']:
            erros.append('Início do intervalo de almoço é obrigatório')
            
        if not dados['intervalo_almoco_fim']:
            erros.append('Fim do intervalo de almoço é obrigatório')
        
        if dados['intervalo_almoco_inicio'] and dados['intervalo_almoco_fim']:
            if dados['intervalo_almoco_inicio'] >= dados['intervalo_almoco_fim']:
                erros.append('Início do almoço deve ser anterior ao fim do almoço')
        
        if erros:
            for erro in erros:
                flash(erro, 'error')
            return redirect(url_for('empresa_config.index'))
        
        # Salvar configurações
        sucesso, empresa_id = save_empresa_config(dados)
        
        if sucesso:
            # Processar upload de logotipo se houver
            if 'logotipo' in request.files:
                arquivo = request.files['logotipo']
                if arquivo and arquivo.filename and allowed_file(arquivo.filename):
                    try:
                        # Verificar tamanho do arquivo
                        arquivo.seek(0, os.SEEK_END)
                        tamanho = arquivo.tell()
                        arquivo.seek(0)
                        
                        if tamanho > MAX_FILE_SIZE:
                            flash('Arquivo muito grande. Máximo permitido: 5MB', 'error')
                        else:
                            # Ler arquivo e converter para base64
                            conteudo = arquivo.read()
                            mime_type = mimetypes.guess_type(arquivo.filename)[0] or 'image/jpeg'
                            
                            # Salvar no banco
                            if save_logotipo(empresa_id, conteudo, mime_type):
                                flash('Logotipo carregado com sucesso!', 'success')
                            else:
                                flash('Erro ao salvar logotipo', 'error')
                    except Exception as e:
                        logger.error(f"Erro ao processar upload: {e}")
                        flash('Erro ao processar arquivo de logotipo', 'error')
            
            flash('Configurações da empresa salvas com sucesso!', 'success')
        else:
            flash('Erro ao salvar configurações da empresa', 'error')
        
        return redirect(url_for('empresa_config.index'))
        
    except ValueError as e:
        flash('Valores inválidos nos campos numéricos', 'error')
        return redirect(url_for('empresa_config.index'))
    except Exception as e:
        logger.error(f"Erro ao salvar configuração da empresa: {e}")
        flash('Erro interno do sistema', 'error')
        return redirect(url_for('empresa_config.index'))

@empresa_config_bp.route('/logotipo')
@require_admin
def obter_logotipo():
    """Retorna o logotipo da empresa"""
    try:
        empresa = get_empresa_config()
        
        if empresa and empresa.get('logotipo'):
            from flask import Response
            return Response(
                empresa['logotipo'],
                mimetype=empresa.get('logotipo_mime_type', 'image/jpeg'),
                headers={
                    'Cache-Control': 'public, max-age=3600',
                    'Content-Disposition': 'inline; filename=logotipo.jpg'
                }
            )
        else:
            # Retornar imagem padrão se não houver logotipo
            return redirect(url_for('static', filename='images/logo-placeholder.png'))
            
    except Exception as e:
        logger.error(f"Erro ao obter logotipo: {e}")
        return redirect(url_for('static', filename='images/logo-placeholder.png'))

@empresa_config_bp.route('/remover-logotipo', methods=['POST'])
@require_admin
def remover_logotipo():
    """Remove o logotipo da empresa"""
    try:
        empresa = get_empresa_config()
        
        if empresa:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE cad_empresas SET
                    logotipo = NULL,
                    logotipo_mime_type = NULL,
                    data_atualizacao = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (empresa['id'],))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Logotipo removido da empresa ID: {empresa['id']}")
            return jsonify({'success': True, 'message': 'Logotipo removido com sucesso'})
        else:
            return jsonify({'success': False, 'message': 'Empresa não encontrada'})
            
    except Exception as e:
        logger.error(f"Erro ao remover logotipo: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do sistema'})

@empresa_config_bp.route('/api/dados', methods=['GET'])
@require_admin  
def api_dados_empresa():
    """API para obter dados da empresa em formato JSON"""
    try:
        empresa = get_empresa_config()
        
        if empresa:
            # Converter objetos time para string
            if isinstance(empresa.get('jornada_trabalho_padrao'), time):
                empresa['jornada_trabalho_padrao'] = empresa['jornada_trabalho_padrao'].strftime('%H:%M')
            if isinstance(empresa.get('intervalo_almoco_inicio'), time):
                empresa['intervalo_almoco_inicio'] = empresa['intervalo_almoco_inicio'].strftime('%H:%M')
            if isinstance(empresa.get('intervalo_almoco_fim'), time):
                empresa['intervalo_almoco_fim'] = empresa['intervalo_almoco_fim'].strftime('%H:%M')
            
            # Remover logotipo dos dados (muito grande para JSON)
            empresa_json = {k: v for k, v in empresa.items() if k != 'logotipo'}
            empresa_json['tem_logotipo'] = empresa.get('logotipo') is not None
            
            return jsonify({
                'success': True,
                'empresa': empresa_json
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Nenhuma configuração encontrada'
            })
            
    except Exception as e:
        logger.error(f"Erro na API de dados da empresa: {e}")
        return jsonify({
            'success': False,
            'message': 'Erro interno do sistema'
        })

# ========================================
# HANDLERS DE ERRO
# ========================================

@empresa_config_bp.errorhandler(404)
def handle_404(error):
    """Handler para erro 404"""
    return render_template('errors/404.html', 
                         titulo='Página não encontrada'), 404

@empresa_config_bp.errorhandler(500)
def handle_500(error):
    """Handler para erro 500"""
    logger.error(f"Erro 500 no módulo empresa_config: {error}")
    return render_template('errors/500.html', 
                         titulo='Erro interno do sistema'), 500 