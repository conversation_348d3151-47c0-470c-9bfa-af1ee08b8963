#!/usr/bin/env python3
"""
Serviço Biométrico Universal Simplificado - RLPONTO-WEB
=======================================================

Versão simplificada e funcional do serviço biométrico universal.
Detecta dispositivos reais e integra com o sistema existente.

Autor: <PERSON>ues - AiNexus Tecnologia
Sistema: RLPONTO-WEB v1.0
Data: Junho 2025
"""

import os
import sys
import json
import logging
import hashlib
import subprocess
import platform
from datetime import datetime
from typing import Dict, List, Any
from flask import Flask, request, jsonify
from flask_cors import CORS

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/biometric_service.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('biometric-service')

# Configurações
SERVICE_PORT = 5001
DEVICE_REGISTRY_FILE = 'biometric_devices.json'

class BiometricDeviceDetector:
    """Detector de dispositivos biométricos"""
    
    def __init__(self):
        self.is_windows = platform.system() == 'Windows'
        logger.info("Detector de dispositivos biometricos iniciado")
    
    def detect_devices(self) -> List[Dict[str, Any]]:
        """Detecta todos os dispositivos biométricos disponíveis"""
        devices = []
        
        try:
            # Detecção via Windows Biometric Framework
            wbf_devices = self._detect_wbf_devices()
            devices.extend(wbf_devices)
            
            # Detecção via USB
            usb_devices = self._detect_usb_devices()
            devices.extend(usb_devices)
            
            logger.info(f"Detectados {len(devices)} dispositivos biometricos")
            
        except Exception as e:
            logger.error(f"Erro na deteccao de dispositivos: {e}")
        
        return devices
    
    def _detect_wbf_devices(self) -> List[Dict[str, Any]]:
        """Detecta dispositivos via Windows Biometric Framework"""
        devices = []
        
        if not self.is_windows:
            return devices
        
        try:
            # Script PowerShell para detectar dispositivos biométricos
            ps_script = '''
            Get-WmiObject -Class Win32_PnPEntity | Where-Object {
                ($_.Name -match "biometric|fingerprint|sensor") -and 
                ($_.Status -eq "OK")
            } | Select-Object Name, DeviceID, Manufacturer
            '''
            
            result = subprocess.run(
                ['powershell', '-Command', ps_script],
                capture_output=True, text=True, timeout=15
            )
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.split('\n')
                for line in lines[3:]:  # Pula cabeçalho
                    if line.strip() and not line.startswith('-'):
                        device = self._parse_wbf_device(line)
                        if device:
                            devices.append(device)
                            
        except Exception as e:
            logger.warning(f"Deteccao WBF falhou: {e}")
        
        return devices
    
    def _detect_usb_devices(self) -> List[Dict[str, Any]]:
        """Detecta dispositivos biométricos via USB"""
        devices = []
        
        # Fabricantes conhecidos de leitores biométricos
        known_vendors = {
            '1234': 'SecuGen',
            '16d1': 'Suprema', 
            '08ff': 'AuthenTec',
            '0483': 'Integrated Biometrics',
            '27c6': 'Goodix',
            '06cb': 'Synaptics',
            '138a': 'Validity Sensors',
            '147e': 'Upek',
            '0a5c': 'Broadcom'
        }
        
        if not self.is_windows:
            return devices
        
        try:
            ps_script = '''
            Get-WmiObject -Class Win32_USBHub | Where-Object {
                $_.DeviceID -match "VID_" -and $_.Status -eq "OK"
            } | Select-Object DeviceID, Name
            '''
            
            result = subprocess.run(
                ['powershell', '-Command', ps_script],
                capture_output=True, text=True, timeout=15
            )
            
            if result.returncode == 0:
                import re
                for line in result.stdout.split('\n'):
                    if 'VID_' in line:
                        vid_match = re.search(r'VID_([0-9A-F]{4})', line.upper())
                        if vid_match:
                            vid = vid_match.group(1).lower()
                            if vid in known_vendors:
                                device = self._create_usb_device(line, known_vendors[vid])
                                devices.append(device)
                                
        except Exception as e:
            logger.warning(f"Deteccao USB falhou: {e}")
        
        return devices
    
    def _parse_wbf_device(self, line: str) -> Dict[str, Any]:
        """Parseia linha de dispositivo WBF"""
        try:
            parts = line.strip().split()
            if len(parts) < 2:
                return None
                
            name = ' '.join(parts[:-1])
            device_id = hashlib.md5(line.encode()).hexdigest()[:16]
            
            # Detecta fabricante
            manufacturer = "Unknown"
            for vendor in ['SecuGen', 'Suprema', 'Nitgen', 'Integrated', 'Goodix', 'Synaptics']:
                if vendor.lower() in name.lower():
                    manufacturer = vendor
                    break
            
            return {
                'id': device_id,
                'name': name,
                'manufacturer': manufacturer,
                'type': 'fingerprint',
                'status': 'ready',
                'driver': 'wbf',
                'detected_at': datetime.now().isoformat()
            }
        except Exception:
            return None
    
    def _create_usb_device(self, device_line: str, manufacturer: str) -> Dict[str, Any]:
        """Cria objeto de dispositivo USB"""
        device_id = hashlib.md5((device_line + manufacturer).encode()).hexdigest()[:16]
        
        return {
            'id': device_id,
            'name': f'{manufacturer} Biometric Reader',
            'manufacturer': manufacturer,
            'type': 'fingerprint',
            'status': 'detected',
            'driver': 'usb',
            'detected_at': datetime.now().isoformat()
        }

class BiometricService:
    """Serviço principal de biometria"""
    
    def __init__(self):
        self.detector = BiometricDeviceDetector()
        self.devices = []
        self.registered_devices = self._load_registry()
        self.app = Flask(__name__)
        CORS(self.app)
        self._setup_routes()
        
        # Criar diretório de logs se não existir
        os.makedirs('logs', exist_ok=True)
        
    def _load_registry(self) -> Dict:
        """Carrega dispositivos registrados"""
        try:
            if os.path.exists(DEVICE_REGISTRY_FILE):
                with open(DEVICE_REGISTRY_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Erro ao carregar registro: {e}")
        return {}
    
    def _save_registry(self):
        """Salva dispositivos registrados"""
        try:
            with open(DEVICE_REGISTRY_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.registered_devices, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Erro ao salvar registro: {e}")
    
    def _setup_routes(self):
        """Configura rotas da API"""
        
        @self.app.route('/test', methods=['GET'])
        def test():
            return jsonify({
                'status': 'ok',
                'service': 'biometric-service',
                'version': '1.0.0',
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/devices/discover', methods=['GET', 'POST'])
        def discover_devices():
            try:
                self.devices = self.detector.detect_devices()
                return jsonify({
                    'success': True,
                    'devices': self.devices,
                    'count': len(self.devices),
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                logger.error(f"Erro na descoberta: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/devices/register', methods=['POST'])
        def register_device():
            try:
                data = request.get_json()
                device_id = data.get('device_id')
                device_info = data.get('device_info')
                
                if not device_id or not device_info:
                    return jsonify({
                        'success': False,
                        'error': 'device_id e device_info obrigatorios'
                    }), 400
                
                self.registered_devices[device_id] = {
                    'device_info': device_info,
                    'registered_at': datetime.now().isoformat(),
                    'usage_count': 0
                }
                
                self._save_registry()
                logger.info(f"Dispositivo registrado: {device_info.get('name')}")
                
                return jsonify({'success': True})
                
            except Exception as e:
                logger.error(f"Erro no registro: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/capture', methods=['POST'])
        def capture():
            try:
                data = request.get_json() or {}
                device_id = data.get('device_id')
                
                # Encontra o dispositivo
                target_device = None
                if device_id:
                    target_device = next((d for d in self.devices if d['id'] == device_id), None)
                else:
                    target_device = self.devices[0] if self.devices else None
                
                if not target_device:
                    return jsonify({
                        'success': False,
                        'error': 'Nenhum dispositivo disponivel'
                    })
                
                logger.info(f"Captura solicitada para: {target_device['name']}")
                
                # Por enquanto retorna erro informativo
                return jsonify({
                    'success': False,
                    'error': 'Captura real requer integracao com drivers especificos do dispositivo',
                    'device': target_device['name'],
                    'manufacturer': target_device['manufacturer'],
                    'next_steps': 'Implementar SDK do fabricante'
                })
                
            except Exception as e:
                logger.error(f"Erro na captura: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/status', methods=['GET'])
        def status():
            return jsonify({
                'service': 'Biometric Service',
                'version': '1.0.0',
                'devices_detected': len(self.devices),
                'devices_registered': len(self.registered_devices),
                'platform': platform.system(),
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/health', methods=['GET'])
        def health():
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat()
            })
    
    def run(self, host='localhost', port=SERVICE_PORT):
        """Inicia o serviço"""
        logger.info(f"Iniciando servico biometrico em {host}:{port}")
        
        # Descoberta inicial
        self.devices = self.detector.detect_devices()
        
        try:
            self.app.run(host=host, port=port, debug=False, threaded=True)
        except Exception as e:
            logger.error(f"Erro ao iniciar servico: {e}")
            raise

def main():
    """Função principal"""
    print("=" * 60)
    print("RLPONTO-WEB - Servico Biometrico Universal v1.0")
    print("Sistema universal para leitores biometricos")
    print("Compativel com qualquer dispositivo com drivers Windows")
    print("=" * 60)
    
    try:
        service = BiometricService()
        service.run()
    except KeyboardInterrupt:
        logger.info("Servico interrompido pelo usuario")
    except Exception as e:
        logger.error(f"Erro critico: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main()) 