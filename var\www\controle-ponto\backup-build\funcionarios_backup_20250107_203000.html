{% extends "base.html" %}

{% block title %}Gestão de Funcionários - {{ empresa_principal.razao_social }}{% endblock %}

{% block extra_css %}
<style>
    .funcionario-card {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.08);
    }

    .funcionario-card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 12px rgba(0,123,255,0.15);
        transform: translateY(-1px);
    }

    .funcionario-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 6px;
        padding: 12px 15px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .funcionario-info {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 6px;
        padding: 10px 12px;
        margin-bottom: 10px;
        font-size: 0.9rem;
    }
    
    .jornada-info {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 6px;
        padding: 10px 12px;
        margin-bottom: 10px;
        font-size: 0.9rem;
    }

    .funcionario-nome {
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
    }

    .funcionario-matricula {
        font-size: 0.85rem;
        opacity: 0.9;
        margin: 0;
    }

    .info-item {
        margin-bottom: 4px;
        font-size: 0.85rem;
    }

    .info-item:last-child {
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 500;
        margin-right: 5px;
    }

    .action-buttons-compact {
        padding: 10px 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .btn-group-vertical .btn {
        font-size: 0.8rem;
        padding: 4px 8px;
        border-radius: 4px !important;
    }
    
    .status-funcionario {
        padding: 6px 15px;
        border-radius: 25px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-ativo {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .status-inativo {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .stats-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 25px;
        color: white;
        margin-bottom: 25px;
    }
    
    .stats-box {
        text-align: center;
        padding: 15px;
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        justify-content: center;
        margin-top: 15px;
    }
    
    .btn-action {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }
    
    .btn-action:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }
    
    .filter-section {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 25px;
        text-align: center;
    }
    
    .reports-section {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header da Página -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-users"></i> Gestão de Funcionários</h2>
                <p class="mb-0">Funcionários da {{ empresa_principal.razao_social }}</p>
            </div>
            <div class="col-md-4">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="stats-box">
                            <div class="stats-number">{{ stats.total_funcionarios }}</div>
                            <div class="stats-label">Total</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-box">
                            <div class="stats-number">{{ stats.funcionarios_ativos }}</div>
                            <div class="stats-label">Ativos</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-box">
                            <div class="stats-number">{{ stats.jornadas_disponiveis }}</div>
                            <div class="stats-label">Jornadas</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Seção de Relatórios Gerais -->
    <div class="reports-section">
        <h5><i class="fas fa-chart-bar"></i> Relatórios Gerais</h5>
        <div class="row mt-3">
            <div class="col-md-3">
                <button class="btn btn-primary btn-block" onclick="gerarRelatorioGeral()">
                    <i class="fas fa-file-pdf"></i> Relatório Geral
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-success btn-block" onclick="gerarRelatorioFrequencia()">
                    <i class="fas fa-clock"></i> Frequência Geral
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-info btn-block" onclick="gerarRelatorioJornadas()">
                    <i class="fas fa-calendar-alt"></i> Jornadas de Trabalho
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-warning btn-block" onclick="exportarFuncionarios()">
                    <i class="fas fa-download"></i> Exportar Lista
                </button>
            </div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="filter-section">
        <div class="row align-items-end">
            <div class="col-md-3">
                <label class="form-label"><strong>Status:</strong></label>
                <select class="form-select" id="filtroStatus" onchange="aplicarFiltros()">
                    <option value="">Todos os status</option>
                    <option value="ativo">Ativos</option>
                    <option value="inativo">Inativos</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label"><strong>Jornada:</strong></label>
                <select class="form-select" id="filtroJornada" onchange="aplicarFiltros()">
                    <option value="">Todas as jornadas</option>
                    {% for jornada in jornadas %}
                        <option value="{{ jornada.id }}">{{ jornada.nome_jornada }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label"><strong>Buscar Funcionário:</strong></label>
                <input type="text" class="form-control" id="buscaFuncionario" placeholder="Nome, CPF ou cargo..." onkeyup="aplicarFiltros()">
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary btn-block" onclick="limparFiltros()">
                    <i class="fas fa-eraser"></i> Limpar
                </button>
            </div>
        </div>
    </div>

    <!-- Lista de Funcionários -->
    <div id="listaFuncionarios">
        {% if funcionarios %}
            {% for funcionario in funcionarios %}
            <div class="funcionario-card" 
                 data-status="{{ funcionario.status_cadastro.lower() if funcionario.status_cadastro else 'inativo' }}" 
                 data-jornada="{{ funcionario.jornada_trabalho_id or '' }}"
                 data-busca="{{ (funcionario.nome_completo + ' ' + (funcionario.cpf or '') + ' ' + (funcionario.cargo or '')).lower() }}">
                
                <div class="row">
                    <!-- Informações do Funcionário -->
                    <div class="col-md-3">
                        <div class="funcionario-header">
                            <div>
                                <h6 class="funcionario-nome"><i class="fas fa-user"></i> {{ funcionario.nome_completo }}</h6>
                                <p class="funcionario-matricula">Mat: {{ funcionario.matricula_empresa or 'N/A' }}</p>
                            </div>
                            <span class="status-funcionario status-{{ funcionario.status_cadastro.lower() if funcionario.status_cadastro else 'inativo' }}">
                                {{ funcionario.status_display }}
                            </span>
                        </div>
                    </div>

                    <!-- Informações do Funcionário -->
                    <div class="col-md-3">
                        <div class="funcionario-info">
                            <div class="info-item"><span class="info-label">CPF:</span>{{ funcionario.cpf or 'N/A' }}</div>
                            <div class="info-item"><span class="info-label">Cargo:</span>{{ funcionario.cargo or 'N/A' }}</div>
                            <div class="info-item"><span class="info-label">Telefone:</span>{{ funcionario.telefone1 or 'N/A' }}</div>
                            <div class="info-item"><span class="info-label">Admissão:</span>{{ funcionario.data_admissao_formatada or 'N/A' }}</div>
                        </div>
                    </div>
                    
                    <!-- Jornada de Trabalho -->
                    <div class="col-md-3">
                        <div class="jornada-info">
                            <h6 style="margin-bottom: 8px; font-size: 0.9rem;"><i class="fas fa-clock"></i> Jornada de Trabalho</h6>
                            {% if funcionario.jornada_nome %}
                                <div class="info-item"><span class="info-label">Jornada:</span>{{ funcionario.jornada_nome }}</div>
                                <div class="info-item"><span class="info-label">Seg-Qui:</span>{{ funcionario.seg_qui_entrada.strftime('%H:%M') if funcionario.seg_qui_entrada else 'N/A' }} às {{ funcionario.seg_qui_saida.strftime('%H:%M') if funcionario.seg_qui_saida else 'N/A' }}</div>
                                <div class="info-item"><span class="info-label">Sexta:</span>{{ funcionario.sexta_entrada.strftime('%H:%M') if funcionario.sexta_entrada else 'N/A' }} às {{ funcionario.sexta_saida.strftime('%H:%M') if funcionario.sexta_saida else 'N/A' }}</div>
                            {% else %}
                                <div class="info-item text-center" style="color: #6c757d; font-style: italic; padding: 20px 0;">
                                    <i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i>
                                    Jornada não definida
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Botões de Ação -->
                    <div class="col-md-3">
                        <div class="action-buttons-compact">
                            <div class="btn-group-vertical w-100" role="group">
                                <a href="/funcionarios/detalhes/{{ funcionario.id }}" class="btn btn-sm btn-primary mb-1">
                                    <i class="fas fa-eye"></i> Detalhes
                                </a>
                                <a href="/funcionarios/editar/{{ funcionario.id }}" class="btn btn-sm btn-warning mb-1">
                                    <i class="fas fa-edit"></i> Editar
                                </a>
                                <button class="btn btn-sm btn-info mb-1" onclick="gerarRelatorioIndividual({{ funcionario.id }}, '{{ funcionario.nome_completo }}')">
                                    <i class="fas fa-file-alt"></i> Relatório
                                </button>
                                <button class="btn btn-sm btn-success mb-1" onclick="verFrequencia({{ funcionario.id }}, '{{ funcionario.nome_completo }}')">
                                    <i class="fas fa-calendar-check"></i> Frequência
                                </button>
                                {% if funcionario.status_cadastro == 'Ativo' %}
                                    <button class="btn btn-sm btn-danger" onclick="inativarFuncionario({{ funcionario.id }}, '{{ funcionario.nome_completo }}')">
                                        <i class="fas fa-user-times"></i> Inativar
                                    </button>
                                {% else %}
                                    <button class="btn btn-sm btn-success" onclick="ativarFuncionario({{ funcionario.id }}, '{{ funcionario.nome_completo }}')">
                                        <i class="fas fa-user-check"></i> Ativar
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-5">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                    <h5>Nenhum funcionário encontrado</h5>
                    <p class="mb-0">Não há funcionários cadastrados para esta empresa principal.</p>
                    <a href="/funcionarios/adicionar" class="btn btn-primary mt-3">
                        <i class="fas fa-plus"></i> Cadastrar Primeiro Funcionário
                    </a>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Botões de Navegação -->
    <div class="row mt-4">
        <div class="col-md-6">
            <a href="{{ url_for('empresa_principal.index') }}" class="btn btn-secondary btn-lg">
                <i class="fas fa-arrow-left"></i> Voltar ao Dashboard
            </a>
        </div>
        <div class="col-md-6 text-end">
            <a href="/funcionarios/adicionar" class="btn btn-success btn-lg">
                <i class="fas fa-plus"></i> Novo Funcionário
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Aplicar filtros
function aplicarFiltros() {
    const filtroStatus = document.getElementById('filtroStatus').value.toLowerCase();
    const filtroJornada = document.getElementById('filtroJornada').value;
    const buscaTexto = document.getElementById('buscaFuncionario').value.toLowerCase();

    const funcionarios = document.querySelectorAll('.funcionario-card');
    let visiveisCount = 0;

    funcionarios.forEach(card => {
        const status = card.dataset.status;
        const jornada = card.dataset.jornada;
        const textoCard = card.dataset.busca;

        let mostrar = true;

        // Filtro por status
        if (filtroStatus && status !== filtroStatus) {
            mostrar = false;
        }

        // Filtro por jornada
        if (filtroJornada && jornada !== filtroJornada) {
            mostrar = false;
        }

        // Filtro por texto
        if (buscaTexto && !textoCard.includes(buscaTexto)) {
            mostrar = false;
        }

        if (mostrar) {
            card.style.display = 'block';
            visiveisCount++;
        } else {
            card.style.display = 'none';
        }
    });

    // Mostrar mensagem se nenhum resultado
    const container = document.getElementById('listaFuncionarios');
    let msgSemResultados = container.querySelector('.sem-resultados');

    if (visiveisCount === 0 && funcionarios.length > 0) {
        if (!msgSemResultados) {
            msgSemResultados = document.createElement('div');
            msgSemResultados.className = 'sem-resultados text-center py-5';
            msgSemResultados.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-search fa-2x mb-3"></i>
                    <h5>Nenhum funcionário encontrado</h5>
                    <p class="mb-0">Tente ajustar os filtros para encontrar funcionários.</p>
                </div>
            `;
            container.appendChild(msgSemResultados);
        }
        msgSemResultados.style.display = 'block';
    } else if (msgSemResultados) {
        msgSemResultados.style.display = 'none';
    }
}

// Limpar filtros
function limparFiltros() {
    document.getElementById('filtroStatus').value = '';
    document.getElementById('filtroJornada').value = '';
    document.getElementById('buscaFuncionario').value = '';
    aplicarFiltros();
}

// Relatórios Gerais
function gerarRelatorioGeral() {
    window.open('/empresa-principal/relatorios/funcionarios/geral', '_blank');
}

function gerarRelatorioFrequencia() {
    window.open('/empresa-principal/relatorios/funcionarios/frequencia', '_blank');
}

function gerarRelatorioJornadas() {
    window.open('/empresa-principal/relatorios/jornadas', '_blank');
}

function exportarFuncionarios() {
    window.open('/empresa-principal/relatorios/funcionarios/exportar', '_blank');
}

// Relatório Individual
function gerarRelatorioIndividual(funcionarioId, nomeFuncionario) {
    if (confirm(`Gerar relatório individual para ${nomeFuncionario}?`)) {
        window.open(`/empresa_principal/relatorios/funcionario/${funcionarioId}`, '_blank');
    }
}

// Ver Frequência
function verFrequencia(funcionarioId, nomeFuncionario) {
    window.open(`/funcionarios/frequencia/${funcionarioId}`, '_blank');
}

// Ativar/Inativar Funcionário
function inativarFuncionario(funcionarioId, nomeFuncionario) {
    if (confirm(`Tem certeza que deseja INATIVAR o funcionário ${nomeFuncionario}?`)) {
        fetch(`/funcionarios/inativar/${funcionarioId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Funcionário inativado com sucesso!', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert(data.message || 'Erro ao inativar funcionário', 'error');
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            showAlert('Erro ao inativar funcionário', 'error');
        });
    }
}

function ativarFuncionario(funcionarioId, nomeFuncionario) {
    if (confirm(`Tem certeza que deseja ATIVAR o funcionário ${nomeFuncionario}?`)) {
        fetch(`/funcionarios/ativar/${funcionarioId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Funcionário ativado com sucesso!', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert(data.message || 'Erro ao ativar funcionário', 'error');
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            showAlert('Erro ao ativar funcionário', 'error');
        });
    }
}

// Funções auxiliares
function getCsrfToken() {
    return document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Inicializar página
document.addEventListener('DOMContentLoaded', function() {
    console.log('Página de funcionários carregada');
});
</script>
{% endblock %}
