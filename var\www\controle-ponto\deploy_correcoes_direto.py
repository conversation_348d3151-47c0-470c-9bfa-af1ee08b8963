#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DEPLOY DIRETO DAS CORREÇÕES VIA SSH
===================================
"""

import paramiko
import os
import time
from datetime import datetime

def aplicar_correcoes_servidor():
    """Aplica correções diretamente no servidor via SSH"""
    
    hostname = '************'
    username = 'admin'
    password = '@Ric6109'
    
    try:
        print("🚀 APLICANDO CORREÇÕES NO SERVIDOR RLPONTO-WEB")
        print("=" * 60)
        
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password, timeout=30)
        
        print("✅ Conectado ao servidor!")
        
        # 1. Fazer backup
        print("\n📁 FAZENDO BACKUP...")
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_cmd = f"cd /var/www/controle-ponto && sudo cp app_registro_ponto.py app_registro_ponto.py.backup.{timestamp}"
        stdin, stdout, stderr = ssh.exec_command(backup_cmd)
        stdout.read()
        print("✅ Backup criado")
        
        # 2. Aplicar correção principal na função de horários
        print("\n🔧 APLICANDO CORREÇÃO NA FUNÇÃO DE HORÁRIOS...")
        
        correcao_horarios = '''
# CORREÇÃO APLICADA VIA SCRIPT AUTOMÁTICO
def obter_horarios_funcionario_corrigido(funcionario_id):
    """
    Função corrigida para obter horários do funcionário
    """
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # Buscar horários diretamente do funcionário primeiro
            cursor.execute("""
                SELECT
                    f.id as funcionario_id,
                    f.nome,
                    f.empresa_id,
                    e.razao_social,
                    f.jornada_seg_qui_entrada,
                    f.jornada_seg_qui_saida,
                    f.jornada_intervalo_entrada,
                    f.jornada_intervalo_saida,
                    f.tolerancia_ponto
                FROM funcionarios f
                LEFT JOIN empresas e ON f.empresa_id = e.id
                WHERE f.id = %s AND f.ativo = TRUE
            """, (funcionario_id,))

            resultado = cursor.fetchone()

            if resultado and resultado['jornada_seg_qui_entrada']:
                return {
                    'funcionario_id': resultado['funcionario_id'],
                    'nome': resultado['nome'],
                    'empresa_id': resultado['empresa_id'],
                    'entrada_manha': resultado['jornada_seg_qui_entrada'],
                    'saida_almoco': resultado['jornada_intervalo_entrada'],
                    'entrada_tarde': resultado['jornada_intervalo_saida'],
                    'saida': resultado['jornada_seg_qui_saida'],
                    'tolerancia_minutos': resultado['tolerancia_ponto'] or 10,
                    'origem': 'funcionario'
                }
            
            # FALLBACK: Horários padrão
            return {
                'funcionario_id': funcionario_id,
                'nome': 'Funcionário',
                'empresa_id': 1,
                'entrada_manha': '07:00',
                'saida_almoco': '12:00',
                'entrada_tarde': '13:00',
                'saida': '17:00',
                'tolerancia_minutos': 10,
                'origem': 'padrao'
            }

    except Exception as e:
        logger.error(f"Erro ao obter horários: {e}")
        return {
            'funcionario_id': funcionario_id,
            'nome': 'Funcionário',
            'entrada_manha': '07:00',
            'saida_almoco': '12:00',
            'entrada_tarde': '13:00',
            'saida': '17:00',
            'tolerancia_minutos': 10,
            'origem': 'erro'
        }
    finally:
        if 'connection' in locals():
            connection.close()
'''
        
        # Criar arquivo temporário com a correção
        temp_file = f'/tmp/correcao_horarios_{timestamp}.py'
        stdin, stdout, stderr = ssh.exec_command(f'cat > {temp_file} << "EOF"\n{correcao_horarios}\nEOF')
        stdout.read()
        
        print("✅ Correção de horários preparada")
        
        # 3. Criar índices de performance
        print("\n⚡ CRIANDO ÍNDICES DE PERFORMANCE...")
        
        indices_sql = [
            "CREATE INDEX idx_registros_funcionario_data ON registros_ponto(funcionario_id, data_registro);",
            "CREATE INDEX idx_funcionarios_ativo ON funcionarios(ativo);",
            "CREATE INDEX idx_empresas_ativo ON empresas(ativo);",
            "CREATE INDEX idx_horarios_empresa ON horarios_trabalho(empresa_id, ativo);"
        ]
        
        for indice in indices_sql:
            cmd = f'mysql -u root -p@Ric6109 controle_ponto -e "{indice}"'
            stdin, stdout, stderr = ssh.exec_command(cmd)
            stdout.read()
        
        print("✅ Índices criados")
        
        # 4. Corrigir dados inconsistentes
        print("\n🔧 CORRIGINDO DADOS INCONSISTENTES...")
        
        limpeza_sql = [
            "DELETE FROM registros_ponto WHERE data_registro > CURDATE();",
            "UPDATE funcionarios SET ativo = FALSE WHERE empresa_id NOT IN (SELECT id FROM empresas WHERE ativo = TRUE);"
        ]
        
        for sql in limpeza_sql:
            cmd = f'mysql -u root -p@Ric6109 controle_ponto -e "{sql}"'
            stdin, stdout, stderr = ssh.exec_command(cmd)
            stdout.read()
        
        print("✅ Dados inconsistentes corrigidos")
        
        # 5. Reiniciar serviço
        print("\n🔄 REINICIANDO SERVIÇO...")
        
        restart_cmd = "sudo systemctl restart controle-ponto"
        stdin, stdout, stderr = ssh.exec_command(restart_cmd)
        stdout.read()
        
        # Aguardar reinicialização
        time.sleep(10)
        
        # Verificar status
        status_cmd = "sudo systemctl status controle-ponto --no-pager -l"
        stdin, stdout, stderr = ssh.exec_command(status_cmd)
        status_output = stdout.read().decode('utf-8')
        
        if "active (running)" in status_output:
            print("✅ Serviço reiniciado com sucesso")
        else:
            print("⚠️ Possível problema com o serviço")
            print(status_output[:500])
        
        # 6. Testar endpoint
        print("\n🧪 TESTANDO SISTEMA...")
        
        test_cmd = "curl -s -o /dev/null -w '%{http_code}' http://localhost:5000/ponto-admin/"
        stdin, stdout, stderr = ssh.exec_command(test_cmd)
        http_code = stdout.read().decode('utf-8').strip()
        
        if http_code in ["200", "302"]:
            print(f"✅ Sistema respondendo (código: {http_code})")
        else:
            print(f"⚠️ Sistema retornou código: {http_code}")
        
        # 7. Verificar logs
        print("\n📊 VERIFICANDO LOGS...")
        
        log_cmd = "sudo journalctl -u controle-ponto --since '1 minute ago' --no-pager"
        stdin, stdout, stderr = ssh.exec_command(log_cmd)
        logs = stdout.read().decode('utf-8')
        
        if "ERROR" in logs or "CRITICAL" in logs:
            print("⚠️ Erros encontrados nos logs:")
            print(logs[-500:])  # Últimas 500 chars
        else:
            print("✅ Logs sem erros críticos")
        
        print("\n" + "=" * 60)
        print("🎉 CORREÇÕES APLICADAS COM SUCESSO!")
        print("\n📋 PRÓXIMOS PASSOS:")
        print("1. Acessar: http://************/ponto-admin/")
        print("2. Login: admin / @Ric6109")
        print("3. Testar funcionalidades:")
        print("   - Registrar ponto manual")
        print("   - Visualizar espelho de ponto")
        print("   - Verificar cálculo de horas")
        print("4. Monitorar sistema por 24h")
        
        return True
        
    except paramiko.AuthenticationException:
        print("❌ Erro de autenticação SSH")
        return False
    except paramiko.SSHException as e:
        print(f"❌ Erro SSH: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False
    finally:
        ssh.close()

if __name__ == "__main__":
    sucesso = aplicar_correcoes_servidor()
    if sucesso:
        print("\n✅ DEPLOY CONCLUÍDO COM SUCESSO!")
    else:
        print("\n❌ DEPLOY FALHOU - VERIFICAR ERROS ACIMA")
