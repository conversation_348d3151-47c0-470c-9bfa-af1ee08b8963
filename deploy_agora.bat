@echo off
echo Deploy da correcao do bug de usuarios...
echo.

echo 1. Backup...
ssh root@10.19.208.31 "cp /var/www/controle-ponto/app.py /var/www/controle-ponto/backup-build/app_backup_bug_usuarios_%date:~-4,4%%date:~-10,2%%date:~-7,2%.py"

echo 2. Enviando app.py corrigido...
scp "var\www\controle-ponto\app.py" root@10.19.208.31:/var/www/controle-ponto/app.py

echo 3. Corrigindo permissoes no banco...
ssh root@10.19.208.31 "mysql -u cavalcrod -p200381 controle_ponto -e \"INSERT INTO permissoes (usuario_id, nivel_acesso) SELECT 5, 'admin' WHERE NOT EXISTS (SELECT 1 FROM permissoes WHERE usuario_id = 5);\""

echo 4. Reiniciando Apache...
ssh root@10.19.208.31 "systemctl restart apache2"

echo.
echo Deploy concluido!
echo Teste em: http://10.19.208.31/configurar_usuarios
pause
