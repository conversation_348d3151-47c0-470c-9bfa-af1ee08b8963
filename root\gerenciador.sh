#!/bin/bash

# Configuração do log
LOG_DIR="/var/log/controle-ponto"
LOG_FILE="$LOG_DIR/atualizacoes.log"
[ ! -d "$LOG_DIR" ] && mkdir -p "$LOG_DIR" && chown root:root "$LOG_DIR" && chmod 755 "$LOG_DIR"
[ ! -f "$LOG_FILE" ] && touch "$LOG_FILE" && chown root:root "$LOG_FILE" && chmod 664 "$LOG_FILE"
log() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $1" | tee -a "$LOG_FILE"
}

log "🚀 Iniciando gerenciador do sistema de controle de ponto..."

# Funções do gerenciador
atualizar_layout() {
    log "🔄 Executando atualização de layout..."
    /root/atualizador.sh atualizar_layout
    log "✅ Atualização de layout concluída via gerenciador."
}

atualizar_base() {
    log "🔄 Executando atualização da base de dados..."
    /root/atualizador.sh atualizar_base
    log "✅ Atualização da base de dados concluída via gerenciador."
}

atualizar_app() {
    log "🔄 Executando atualização da aplicação..."
    /root/atualizador.sh atualizar_app
    log "✅ Atualização da aplicação concluída via gerenciador."
}

atualizar_tudo() {
    log "🔄 Executando atualização completa do sistema..."
    /root/atualizador.sh atualizar_tudo
    log "✅ Atualização completa concluída via gerenciador."
}

configurar_rotacao_logs() {
    echo "Digite o número de dias para rotação dos logs (padrão é 7, mínimo 1):"
    read -r ROTATE_DAYS
    if [ -z "$ROTATE_DAYS" ] || ! [[ "$ROTATE_DAYS" =~ ^[0-9]+$ ]] || [ "$ROTATE_DAYS" -lt 1 ]; then
        ROTATE_DAYS=7
        log "⚠️ Nenhum número válido fornecido ou valor < 1. Usando padrão: $ROTATE_DAYS dias."
    fi
    log "🔍 Configurando rotação de logs para $ROTATE_DAYS dias..."
    /root/atualizador.sh configurar_logrotate
    log "✅ Rotação de logs configurada via gerenciador."
}

ver_logs() {
    log "📜 Exibindo logs do sistema..."
    cat "$LOG_FILE"
}

# Submenu de atualizações
submenu_atualizacoes() {
    while true; do
        clear
        echo "=== Submenu: Atualizações ==="
        echo "1. Atualizar layout (style.css e templates)"
        echo "2. Atualizar base de dados (atualizacao_db.sql)"
        echo "3. Atualizar aplicação (app.py)"
        echo "4. Atualizar tudo"
        echo "5. Voltar ao menu principal"
        echo -n "Escolha uma opção [1-5]: "
        read -r opcao

        case "$opcao" in
            1)
                atualizar_layout
                echo "Pressione Enter para continuar..."
                read -r
                ;;
            2)
                atualizar_base
                echo "Pressione Enter para continuar..."
                read -r
                ;;
            3)
                atualizar_app
                echo "Pressione Enter para continuar..."
                read -r
                ;;
            4)
                atualizar_tudo
                echo "Pressione Enter para continuar..."
                read -r
                ;;
            5)
                return 0
                ;;
            *)
                log "⚠️ Opção inválida no submenu Atualizações: $opcao"
                echo "Opção inválida! Pressione Enter para tentar novamente..."
                read -r
                ;;
        esac
    done
}

# Menu principal
while true; do
    clear
    echo "=== Gerenciador do Sistema de Controle de Ponto ==="
    echo "1. Atualizações"
    echo "2. Configurar rotação de logs"
    echo "3. Ver logs"
    echo "4. Sair"
    echo -n "Escolha uma opção [1-4]: "
    read -r opcao

    case "$opcao" in
        1)
            submenu_atualizacoes
            ;;
        2)
            configurar_rotacao_logs
            echo "Pressione Enter para continuar..."
            read -r
            ;;
        3)
            ver_logs
            echo "Pressione Enter para continuar..."
            read -r
            ;;
        4)
            log "🏁 Gerenciamento finalizado."
            exit 0
            ;;
        *)
            log "⚠️ Opção inválida: $opcao"
            echo "Opção inválida! Pressione Enter para tentar novamente..."
            read -r
            ;;
    esac
done
