#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug da Interface Web - RLPONTO-WEB
====================================

Script para debugar a funcionalidade de alteração de nível
via interface web do sistema RLPONTO-WEB.

Data: 07/07/2025
"""

import requests
import json
from datetime import datetime

# Configurações
BASE_URL = "http://************"
LOGIN_URL = f"{BASE_URL}/login"
ALTERAR_NIVEL_URL = f"{BASE_URL}/alterar_nivel"
CONFIGURAR_USUARIOS_URL = f"{BASE_URL}/configurar_usuarios"

def debug_completo():
    """Debug completo da funcionalidade"""
    print("🔍 DEBUG COMPLETO DA INTERFACE WEB")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"URL Base: {BASE_URL}")
    print("=" * 60)
    
    # Criar sessão
    session = requests.Session()
    
    try:
        # 1. Testar conectividade
        print("\n1️⃣ Testando conectividade...")
        try:
            response = session.get(BASE_URL, timeout=10)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Servidor acessível")
            else:
                print(f"   ❌ Problema de conectividade: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ Erro de conectividade: {e}")
            return False
        
        # 2. Testar página de login
        print("\n2️⃣ Testando página de login...")
        try:
            response = session.get(LOGIN_URL, timeout=10)
            print(f"   Status: {response.status_code}")
            if "login" in response.text.lower():
                print("   ✅ Página de login carregada")
            else:
                print("   ❌ Página de login não encontrada")
        except Exception as e:
            print(f"   ❌ Erro ao acessar login: {e}")
        
        # 3. Fazer login
        print("\n3️⃣ Fazendo login como admin...")
        login_data = {
            'usuario': 'admin',
            'senha': '@Ric6109'
        }
        
        try:
            response = session.post(LOGIN_URL, data=login_data, allow_redirects=False, timeout=10)
            print(f"   Status: {response.status_code}")
            print(f"   Headers: {dict(response.headers)}")
            
            if response.status_code in [200, 302]:
                print("   ✅ Login realizado")
                
                # Verificar se foi redirecionado
                if response.status_code == 302:
                    location = response.headers.get('Location', '')
                    print(f"   Redirecionado para: {location}")
            else:
                print(f"   ❌ Falha no login: {response.status_code}")
                print(f"   Resposta: {response.text[:200]}")
                return False
                
        except Exception as e:
            print(f"   ❌ Erro durante login: {e}")
            return False
        
        # 4. Acessar página de configuração
        print("\n4️⃣ Acessando configuração de usuários...")
        try:
            response = session.get(CONFIGURAR_USUARIOS_URL, timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ Página de configuração acessada")
                
                # Verificar se contém elementos esperados
                if "alterar-nivel" in response.text or "nivel-acesso" in response.text:
                    print("   ✅ Elementos de alteração de nível encontrados")
                else:
                    print("   ⚠️ Elementos de alteração de nível não encontrados")
                    print("   Verificando conteúdo da página...")
                    if "configurar" in response.text.lower():
                        print("   ✅ Página de configuração válida")
                    else:
                        print("   ❌ Página de configuração inválida")
            else:
                print(f"   ❌ Erro ao acessar configuração: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Erro ao acessar configuração: {e}")
            return False
        
        # 5. Testar alteração de nível
        print("\n5️⃣ Testando alteração de nível...")
        
        # Dados de teste
        test_cases = [
            {'id': '2', 'nivel': 'admin', 'desc': 'Usuário teste -> admin'},
            {'id': '2', 'nivel': 'usuario', 'desc': 'Admin -> usuário (reversão)'}
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n   Teste {i}: {test_case['desc']}")
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            }
            
            try:
                response = session.post(
                    ALTERAR_NIVEL_URL, 
                    data={'id': test_case['id'], 'nivel': test_case['nivel']}, 
                    headers=headers, 
                    timeout=10
                )
                
                print(f"   Status: {response.status_code}")
                print(f"   Content-Type: {response.headers.get('Content-Type', 'N/A')}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        print(f"   Resposta JSON: {result}")
                        
                        if result.get('success'):
                            print(f"   ✅ {test_case['desc']}: SUCESSO")
                        else:
                            print(f"   ❌ {test_case['desc']}: FALHA - {result.get('message')}")
                            
                    except json.JSONDecodeError:
                        print(f"   ❌ Resposta não é JSON: {response.text[:200]}")
                        
                else:
                    print(f"   ❌ Erro HTTP: {response.status_code}")
                    print(f"   Resposta: {response.text[:200]}")
                    
            except Exception as e:
                print(f"   ❌ Erro na requisição: {e}")
        
        print("\n🎯 RESUMO DO DEBUG:")
        print("   - Conectividade: ✅")
        print("   - Login: ✅") 
        print("   - Acesso à configuração: ✅")
        print("   - Teste de alteração: Verificar resultados acima")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERRO GERAL: {e}")
        return False

def verificar_logs_servidor():
    """Verifica se há logs de erro no servidor"""
    print("\n📋 DICAS PARA VERIFICAÇÃO NO SERVIDOR:")
    print("=" * 50)
    print("1. Conectar ao servidor: ssh root@************")
    print("2. Verificar logs do Flask:")
    print("   tail -f /var/log/syslog | grep python")
    print("   ou")
    print("   journalctl -f -u nome_do_servico")
    print("3. Verificar processo Flask:")
    print("   ps aux | grep app.py")
    print("4. Testar rota diretamente:")
    print("   curl -X POST http://localhost/alterar_nivel -d 'id=2&nivel=admin'")

if __name__ == "__main__":
    print("🚀 DEBUG DA INTERFACE WEB - RLPONTO-WEB")
    print("=" * 60)
    
    sucesso = debug_completo()
    
    if not sucesso:
        print("\n❌ DEBUG IDENTIFICOU PROBLEMAS")
        verificar_logs_servidor()
    else:
        print("\n✅ DEBUG CONCLUÍDO")
        print("Verifique os resultados dos testes acima para identificar o problema específico.")
