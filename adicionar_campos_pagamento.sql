-- =======================================================================
-- SCRIPT DE ATUALIZAÇÃO - CAMPOS DE PAGAMENTO PARA FUNCIONÁRIOS
-- SISTEMA RLPONTO-WEB
-- =======================================================================
-- 
-- Descrição: Adiciona campos relacionados a pagamento/salário na tabela funcionarios
-- Data: 09/01/2025
-- Responsável: AI Assistant (Claude) - Seguindo Guia do Projeto
--
-- =======================================================================

USE controle_ponto;

-- Verificar se a tabela funcionarios existe
SELECT COUNT(*) as tabela_existe 
FROM information_schema.tables 
WHERE table_schema = 'controle_ponto' 
AND table_name = 'funcionarios';

-- Adicionar campos de pagamento à tabela funcionarios
ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS salario_base DECIMAL(10,2) DEFAULT NULL COMMENT 'Salário base do funcionário';

ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS tipo_pagamento ENUM('Mensal', 'Quinzenal', 'Semanal', 'Diario', 'Por_Hora') DEFAULT 'Mensal' COMMENT 'Tipo/frequência de pagamento';

ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS valor_hora DECIMAL(8,2) DEFAULT NULL COMMENT 'Valor da hora de trabalho';

ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS valor_hora_extra DECIMAL(8,2) DEFAULT NULL COMMENT 'Valor da hora extra (geralmente 50% ou 100% a mais)';

ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS percentual_hora_extra DECIMAL(5,2) DEFAULT 50.00 COMMENT 'Percentual adicional para hora extra (ex: 50.00 para 50%)';

ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS vale_transporte DECIMAL(8,2) DEFAULT NULL COMMENT 'Valor do vale transporte';

ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS vale_alimentacao DECIMAL(8,2) DEFAULT NULL COMMENT 'Valor do vale alimentação/refeição';

ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS outros_beneficios DECIMAL(8,2) DEFAULT NULL COMMENT 'Outros benefícios monetários';

ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS desconto_inss TINYINT(1) DEFAULT 1 COMMENT 'Aplicar desconto de INSS (1=sim, 0=não)';

ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS desconto_irrf TINYINT(1) DEFAULT 1 COMMENT 'Aplicar desconto de IRRF (1=sim, 0=não)';

ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS observacoes_pagamento TEXT DEFAULT NULL COMMENT 'Observações sobre pagamento e benefícios';

ALTER TABLE funcionarios ADD COLUMN IF NOT EXISTS data_ultima_alteracao_salario TIMESTAMP NULL DEFAULT NULL COMMENT 'Data da última alteração salarial';

-- Adicionar índices para otimização de consultas
ALTER TABLE funcionarios ADD INDEX IF NOT EXISTS idx_salario_base (salario_base);
ALTER TABLE funcionarios ADD INDEX IF NOT EXISTS idx_tipo_pagamento (tipo_pagamento);

-- Adicionar constraint para validar valores positivos
ALTER TABLE funcionarios ADD CONSTRAINT IF NOT EXISTS chk_salario_positivo CHECK (salario_base IS NULL OR salario_base >= 0);
ALTER TABLE funcionarios ADD CONSTRAINT IF NOT EXISTS chk_valor_hora_positivo CHECK (valor_hora IS NULL OR valor_hora >= 0);
ALTER TABLE funcionarios ADD CONSTRAINT IF NOT EXISTS chk_percentual_range CHECK (percentual_hora_extra IS NULL OR percentual_hora_extra BETWEEN 0 AND 200);

-- Calcular valor da hora baseado no salário para registros existentes (220 horas/mês padrão CLT)
UPDATE funcionarios 
SET valor_hora = ROUND(salario_base / 220, 2)
WHERE salario_base IS NOT NULL AND valor_hora IS NULL;

-- Calcular valor da hora extra baseado no percentual
UPDATE funcionarios 
SET valor_hora_extra = ROUND(valor_hora * (1 + percentual_hora_extra/100), 2)
WHERE valor_hora IS NOT NULL AND valor_hora_extra IS NULL AND percentual_hora_extra IS NOT NULL;

-- Verificar se as alterações foram aplicadas
DESCRIBE funcionarios;

-- Mostrar estatísticas após a atualização
SELECT 
    COUNT(*) as total_funcionarios,
    COUNT(salario_base) as funcionarios_com_salario,
    AVG(salario_base) as salario_medio,
    MIN(salario_base) as menor_salario,
    MAX(salario_base) as maior_salario
FROM funcionarios;

-- =======================================================================
-- FIM DO SCRIPT
-- ======================================================================= 