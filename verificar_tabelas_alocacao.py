#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Verificação das Tabelas de Alocação - RLPONTO-WEB
=================================================

Script para verificar se as tabelas necessárias para o sistema
de alocações existem e estão configuradas corretamente.

Data: 07/07/2025
"""

import pymysql
from datetime import datetime

# Configurações do banco
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def verificar_tabelas():
    """Verifica se as tabelas necessárias existem"""
    print("🔍 VERIFICAÇÃO DAS TABELAS DE ALOCAÇÃO")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        with conn.cursor() as cursor:
            
            # Verificar tabelas necessárias
            tabelas_necessarias = [
                'funcionario_alocacoes',
                'empresa_clientes', 
                'jornadas_trabalho',
                'funcionarios',
                'empresas'
            ]
            
            print("\n📋 VERIFICANDO TABELAS NECESSÁRIAS:")
            print("-" * 40)
            
            tabelas_existentes = []
            tabelas_faltantes = []
            
            for tabela in tabelas_necessarias:
                cursor.execute(f"SHOW TABLES LIKE '{tabela}'")
                result = cursor.fetchone()
                
                if result:
                    print(f"   ✅ {tabela}: EXISTE")
                    tabelas_existentes.append(tabela)
                else:
                    print(f"   ❌ {tabela}: NÃO EXISTE")
                    tabelas_faltantes.append(tabela)
            
            # Verificar estrutura das tabelas existentes
            if 'funcionario_alocacoes' in tabelas_existentes:
                print(f"\n📋 ESTRUTURA DA TABELA funcionario_alocacoes:")
                print("-" * 40)
                cursor.execute("DESCRIBE funcionario_alocacoes")
                campos = cursor.fetchall()
                for campo in campos:
                    print(f"   {campo['Field']}: {campo['Type']} - {campo['Null']} - {campo['Key']}")
            
            if 'empresa_clientes' in tabelas_existentes:
                print(f"\n📋 ESTRUTURA DA TABELA empresa_clientes:")
                print("-" * 40)
                cursor.execute("DESCRIBE empresa_clientes")
                campos = cursor.fetchall()
                for campo in campos:
                    print(f"   {campo['Field']}: {campo['Type']} - {campo['Null']} - {campo['Key']}")
            
            # Verificar dados existentes
            print(f"\n📊 DADOS EXISTENTES:")
            print("-" * 40)
            
            for tabela in tabelas_existentes:
                try:
                    cursor.execute(f"SELECT COUNT(*) as total FROM {tabela}")
                    result = cursor.fetchone()
                    total = result['total'] if result else 0
                    print(f"   {tabela}: {total} registros")
                except Exception as e:
                    print(f"   {tabela}: Erro ao contar - {e}")
            
            # Resumo
            print(f"\n🎯 RESUMO:")
            print("-" * 40)
            print(f"   Tabelas existentes: {len(tabelas_existentes)}/{len(tabelas_necessarias)}")
            print(f"   Tabelas faltantes: {len(tabelas_faltantes)}")
            
            if tabelas_faltantes:
                print(f"\n❌ TABELAS FALTANTES:")
                for tabela in tabelas_faltantes:
                    print(f"   - {tabela}")
                return False
            else:
                print(f"\n✅ TODAS AS TABELAS NECESSÁRIAS EXISTEM!")
                return True
                
    except Exception as e:
        print(f"❌ Erro na verificação: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def testar_consulta_alocacoes():
    """Testa a consulta que está falhando"""
    print(f"\n🧪 TESTANDO CONSULTA DE ALOCAÇÕES:")
    print("-" * 40)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        with conn.cursor() as cursor:
            
            # Consulta original que está falhando
            sql = """
            SELECT fa.*, f.nome, f.cargo, f.cpf, f.telefone,
                   e.razao_social, e.nome_fantasia, e.cnpj,
                   jt.nome as jornada_nome, jt.carga_horaria,
                   ec.nome_contrato, ec.codigo_contrato
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            JOIN empresas e ON fa.empresa_cliente_id = e.id
            JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
            LEFT JOIN empresa_clientes ec ON fa.contrato_id = ec.id
            ORDER BY fa.created_at DESC
            """
            
            print("   Executando consulta...")
            cursor.execute(sql)
            resultados = cursor.fetchall()
            
            print(f"   ✅ Consulta executada com sucesso!")
            print(f"   📊 Resultados encontrados: {len(resultados)}")
            
            if resultados:
                print(f"\n   📋 Primeira alocação encontrada:")
                primeiro = resultados[0]
                for key, value in primeiro.items():
                    print(f"      {key}: {value}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ Erro na consulta: {e}")
        print(f"   💡 Detalhes do erro: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🚀 VERIFICAÇÃO DO SISTEMA DE ALOCAÇÕES")
    print("=" * 60)
    
    # Verificar tabelas
    tabelas_ok = verificar_tabelas()
    
    if tabelas_ok:
        # Testar consulta
        consulta_ok = testar_consulta_alocacoes()
        
        if consulta_ok:
            print(f"\n🎉 SISTEMA DE ALOCAÇÕES PRONTO PARA USO!")
            print("✅ Todas as tabelas existem e a consulta funciona")
        else:
            print(f"\n⚠️ PROBLEMA NA CONSULTA DE ALOCAÇÕES")
            print("❌ Tabelas existem mas há erro na consulta SQL")
    else:
        print(f"\n❌ SISTEMA DE ALOCAÇÕES NÃO ESTÁ PRONTO")
        print("❌ Tabelas necessárias estão faltantes")
