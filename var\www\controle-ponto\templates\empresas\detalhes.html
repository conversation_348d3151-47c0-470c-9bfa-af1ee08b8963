{% extends "base.html" %}

{% block title %}{{ empresa.razao_social }} - Detalhes da Empresa{% endblock %}

{% block extra_css %}
<style>
    .empresa-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
    }
    
    .empresa-header h1 {
        margin: 0 0 10px 0;
        font-size: 2em;
    }
    
    .empresa-header .fantasia {
        opacity: 0.9;
        font-style: italic;
        margin-bottom: 15px;
    }
    
    .empresa-info {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .info-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .info-card h3 {
        margin: 0 0 15px 0;
        color: #2c3e50;
        font-size: 1.1em;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f1f1f1;
    }
    
    .info-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .info-label {
        font-weight: 500;
        color: #7f8c8d;
    }
    
    .info-value {
        color: #2c3e50;
    }
    
    .jornadas-section {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 30px;
    }
    
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f1f1f1;
    }
    
    .section-header h2 {
        margin: 0;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .jornada-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        transition: transform 0.2s ease;
    }
    
    .jornada-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .jornada-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
    }
    
    .jornada-title {
        margin: 0 0 5px 0;
        color: #2c3e50;
        font-size: 1.2em;
    }
    
    .jornada-categoria {
        color: #7f8c8d;
        font-style: italic;
    }
    
    .jornada-badges {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }
    
    .badge {
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.8em;
        font-weight: 500;
    }
    
    .badge-tipo {
        background: #e3f2fd;
        color: #1976d2;
    }
    
    .badge-padrao {
        background: #e8f5e8;
        color: #2e7d32;
    }
    
    .badge-ativa {
        background: #e8f5e8;
        color: #2e7d32;
    }
    
    .badge-inativa {
        background: #ffebee;
        color: #c62828;
    }
    
    .jornada-horarios {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .horario-item {
        background: white;
        padding: 12px;
        border-radius: 6px;
        border-left: 4px solid #3498db;
    }
    
    .horario-dia {
        font-weight: 500;
        color: #2c3e50;
        margin-bottom: 5px;
    }
    
    .horario-tempo {
        color: #3498db;
        font-family: monospace;
        font-size: 1.1em;
    }
    
    .jornada-stats {
        display: flex;
        gap: 15px;
        margin-top: 15px;
    }
    
    .stat-item {
        background: white;
        padding: 10px 15px;
        border-radius: 6px;
        text-align: center;
        border: 1px solid #e9ecef;
    }
    
    .stat-number {
        font-size: 1.3em;
        font-weight: bold;
        color: #2c3e50;
        display: block;
    }
    
    .stat-label {
        font-size: 0.8em;
        color: #7f8c8d;
    }
    
    .empty-jornadas {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
    }
    
    .empty-jornadas i {
        font-size: 3em;
        margin-bottom: 15px;
        color: #bdc3c7;
    }

    /* Animação para o botão Criar Jornada */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .btn-criar-jornada {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        transition: all 0.3s ease;
        animation: pulse 2s infinite;
    }

    .btn-criar-jornada:hover {
        background: linear-gradient(45deg, #218838, #1ea080);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        transform: translateY(-2px);
        animation: none;
    }

    .jornada-protegida {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: white;
        border: none;
        animation: glow 2s ease-in-out infinite alternate;
    }

    @keyframes glow {
        from { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
        to { box-shadow: 0 0 20px rgba(255, 193, 7, 0.8); }
    }
    
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-primary {
        background: #3498db;
        color: white;
    }
    
    .btn-primary:hover {
        background: #2980b9;
        color: white;
    }
    
    .btn-success {
        background: #27ae60;
        color: white;
    }
    
    .btn-success:hover {
        background: #229954;
        color: white;
    }
    
    .btn-secondary {
        background: #95a5a6;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #7f8c8d;
        color: white;
    }

    /* Estilos para o modal */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        overflow: auto;
    }

    .modal-content {
        background-color: #fff;
        margin: 10% auto;
        padding: 0;
        width: 600px;
        max-width: 90%;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        animation: modalFadeIn 0.3s;
    }

    @keyframes modalFadeIn {
        from {opacity: 0; transform: translateY(-50px);}
        to {opacity: 1; transform: translateY(0);}
    }

    .modal-header {
        padding: 15px 20px;
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        border-radius: 8px 8px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 1.5em;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .close {
        color: white;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        padding: 15px 20px;
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .alert {
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .alert-warning {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeeba;
    }

    .alert-info {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }

    .empresa-info-modal {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        margin-top: 20px;
    }

    .badge {
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 0.85em;
        font-weight: 600;
    }

    .badge-danger {
        background-color: #dc3545;
        color: white;
    }

    .badge-warning {
        background-color: #ffc107;
        color: #212529;
    }

    .btn-danger {
        background: #e74c3c;
        color: white;
    }

    .btn-danger:hover {
        background: #c0392b;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="empresa-header">
    <h1><i class="fas fa-building"></i> {{ empresa.razao_social }}</h1>
    {% if empresa.nome_fantasia %}
    <div class="fantasia">{{ empresa.nome_fantasia }}</div>
    {% endif %}
    <div style="display: flex; gap: 15px; margin-top: 15px;">
        <a href="{{ url_for('empresas.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
        <a href="{{ url_for('empresas.cadastrar_jornada', empresa_id=empresa.id) }}" class="btn btn-success">
            <i class="fas fa-plus"></i> Nova Jornada
        </a>
        <button type="button" class="btn btn-danger" onclick="abrirModalExclusao()">
            <i class="fas fa-trash"></i> Excluir Empresa
        </button>
    </div>
</div>

<!-- Modal de Confirmação de Exclusão -->
<div id="modalExclusao" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-exclamation-triangle"></i> Confirmação de Exclusão</h2>
            <span class="close" onclick="fecharModalExclusao()">&times;</span>
        </div>
        <div class="modal-body">
            {% if empresa.empresa_teste and total_funcionarios == 0 %}
                <div class="alert alert-danger">
                    <strong>Atenção!</strong> Você está prestes a <strong>excluir fisicamente</strong> uma empresa de teste.
                </div>
                <p>Esta ação <strong>não pode ser desfeita</strong> e todos os dados relacionados a esta empresa serão removidos permanentemente.</p>
            {% elif empresa.empresa_teste %}
                <div class="alert alert-warning">
                    <strong>Aviso!</strong> Esta empresa de teste possui funcionários vinculados.
                </div>
                <p>A empresa será apenas <strong>inativada</strong>, não excluída fisicamente.</p>
                <p>Para exclusão física, remova todos os funcionários vinculados primeiro.</p>
            {% else %}
                <div class="alert alert-info">
                    <strong>Informação!</strong> Esta é uma empresa real, não marcada como teste.
                </div>
                <p>Empresas reais <strong>não podem ser excluídas fisicamente</strong>, apenas inativadas para preservar o histórico.</p>
                <p>A empresa será marcada como inativa e não aparecerá nas listagens padrão.</p>
            {% endif %}
            
            <div class="empresa-info-modal">
                <p><strong>Empresa:</strong> {{ empresa.razao_social }}</p>
                <p><strong>CNPJ:</strong> {{ empresa.cnpj }}</p>
                <p><strong>Tipo:</strong> {{ 'Empresa de Teste' if empresa.empresa_teste else 'Empresa Real' }}</p>
                <p><strong>Funcionários:</strong> {{ total_funcionarios }}</p>
                <p><strong>Ação a ser executada:</strong> 
                    {% if empresa.empresa_teste and total_funcionarios == 0 %}
                        <span class="badge badge-danger">Exclusão Física</span>
                    {% else %}
                        <span class="badge badge-warning">Inativação</span>
                    {% endif %}
                </p>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="fecharModalExclusao()">
                <i class="fas fa-times"></i> Cancelar
            </button>
            <form method="POST" action="{{ url_for('empresas.excluir', id=empresa.id) }}" style="display: inline;">
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-check"></i> Confirmar Exclusão
                </button>
            </form>
        </div>
    </div>
</div>

<div class="empresa-info">
    <div class="info-card">
        <h3><i class="fas fa-info-circle"></i> Dados Básicos</h3>
        <div class="info-item">
            <span class="info-label">CNPJ:</span>
            <span class="info-value">{{ empresa.cnpj }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Status:</span>
            <span class="info-value">{{ 'Ativa' if empresa.ativa else 'Inativa' }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Cadastro:</span>
            <span class="info-value">{{ empresa.data_cadastro.strftime('%d/%m/%Y') }}</span>
        </div>
    </div>
    
    <div class="info-card">
        <h3><i class="fas fa-phone"></i> Contatos</h3>
        {% if empresa.telefone %}
        <div class="info-item">
            <span class="info-label">Telefone:</span>
            <span class="info-value">{{ empresa.telefone }}</span>
        </div>
        {% endif %}
        {% if empresa.email %}
        <div class="info-item">
            <span class="info-label">Email:</span>
            <span class="info-value">{{ empresa.email }}</span>
        </div>
        {% endif %}
        {% if not empresa.telefone and not empresa.email %}
        <div style="color: #7f8c8d; font-style: italic;">
            Nenhum contato cadastrado
        </div>
        {% endif %}
    </div>
    
    <div class="info-card">
        <h3><i class="fas fa-chart-bar"></i> Estatísticas</h3>
        <div class="info-item">
            <span class="info-label">Jornadas:</span>
            <span class="info-value">{{ jornadas|length }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Funcionários:</span>
            <span class="info-value">{{ jornadas|sum(attribute='total_funcionarios') or 0 }}</span>
        </div>
    </div>
</div>

<div class="jornadas-section">
    <div class="section-header">
        <h2><i class="fas fa-clock"></i> Jornadas de Trabalho</h2>
        <a href="{{ url_for('empresas.cadastrar_jornada', empresa_id=empresa.id) }}" class="btn btn-criar-jornada btn-lg">
            <i class="fas fa-plus-circle"></i> Criar Jornada
        </a>
    </div>
    
    {% if jornadas %}
        {% for jornada in jornadas %}
        <div class="jornada-card">
            <div class="jornada-header">
                <div>
                    <h3 class="jornada-title">{{ jornada.nome_jornada }}</h3>
                    {% if jornada.categoria_funcionario %}
                    <div class="jornada-categoria">{{ jornada.categoria_funcionario }}</div>
                    {% endif %}
                </div>
                
                <div class="jornada-badges">
                    <span class="badge badge-tipo">{{ jornada.tipo_jornada }}</span>
                    {% if jornada.padrao %}
                    <span class="badge badge-padrao">Padrão</span>
                    {% endif %}
                    <span class="badge {{ 'badge-ativa' if jornada.ativa else 'badge-inativa' }}">
                        {{ 'Ativa' if jornada.ativa else 'Inativa' }}
                    </span>
                </div>
            </div>
            
            <div class="jornada-horarios">
                <div class="horario-item">
                    <div class="horario-dia">Segunda a Quinta</div>
                    <div class="horario-tempo">
                        {{ jornada.seg_qui_entrada.strftime('%H:%M') }} - {{ jornada.seg_qui_saida.strftime('%H:%M') }}
                    </div>
                </div>
                
                {% if jornada.sexta_entrada and jornada.sexta_saida %}
                <div class="horario-item">
                    <div class="horario-dia">Sexta-feira</div>
                    <div class="horario-tempo">
                        {{ jornada.sexta_entrada.strftime('%H:%M') }} - {{ jornada.sexta_saida.strftime('%H:%M') }}
                    </div>
                </div>
                {% endif %}
                
                {% if jornada.sabado_entrada and jornada.sabado_saida %}
                <div class="horario-item">
                    <div class="horario-dia">Sábado</div>
                    <div class="horario-tempo">
                        {{ jornada.sabado_entrada.strftime('%H:%M') }} - {{ jornada.sabado_saida.strftime('%H:%M') }}
                    </div>
                </div>
                {% endif %}
                
                {% if jornada.intervalo_inicio and jornada.intervalo_fim %}
                <div class="horario-item">
                    <div class="horario-dia">Intervalo</div>
                    <div class="horario-tempo">
                        {{ jornada.intervalo_inicio.strftime('%H:%M') }} - {{ jornada.intervalo_fim.strftime('%H:%M') }}
                    </div>
                </div>
                {% endif %}
            </div>
            
            <div class="jornada-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ jornada.total_funcionarios or 0 }}</span>
                    <span class="stat-label">Funcionários</span>
                </div>

                <div class="stat-item">
                    <span class="stat-number">{{ jornada.tolerancia_entrada_minutos or 0 }}</span>
                    <span class="stat-label">Tolerância (min)</span>
                </div>

                <div class="jornada-actions" style="margin-left: auto; display: flex; gap: 10px;">
                    <button class="btn btn-sm btn-primary" onclick="editarJornada({{ jornada.id }})">
                        <i class="fas fa-edit"></i> Editar
                    </button>
                    {% if not jornada.padrao %}
                        <button class="btn btn-sm btn-danger" onclick="excluirJornada({{ jornada.id }}, '{{ jornada.nome_jornada }}')">
                            <i class="fas fa-trash"></i> Excluir
                        </button>
                    {% else %}
                        <span class="badge jornada-protegida" style="padding: 8px 12px;">
                            <i class="fas fa-shield-alt"></i> Protegida
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="empty-jornadas">
            <i class="fas fa-clock"></i>
            <h3>Nenhuma jornada cadastrada</h3>
            <p>Esta empresa ainda não possui jornadas de trabalho configuradas.</p>
            <a href="{{ url_for('empresas.cadastrar_jornada', empresa_id=empresa.id) }}" class="btn btn-success">
                <i class="fas fa-plus"></i> Cadastrar Primeira Jornada
            </a>
        </div>
    {% endif %}
</div>

<script>
// Funções para controlar o modal
function abrirModalExclusao() {
    document.getElementById('modalExclusao').style.display = 'block';
}

function fecharModalExclusao() {
    document.getElementById('modalExclusao').style.display = 'none';
}

// Fechar modal se clicar fora dele
window.onclick = function(event) {
    const modal = document.getElementById('modalExclusao');
    if (event.target == modal) {
        fecharModalExclusao();
    }
}

// Funções para gerenciamento de jornadas
function editarJornada(jornadaId) {
    // TODO: Implementar edição de jornada
    alert('Funcionalidade de edição será implementada em breve');
}

function excluirJornada(jornadaId, nomeJornada) {
    if (confirm(`Tem certeza que deseja excluir a jornada "${nomeJornada}"?\n\nEsta ação não pode ser desfeita.`)) {
        const empresaId = {{ empresa.id }};

        fetch(`/empresas/${empresaId}/jornadas/${jornadaId}/excluir`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao excluir jornada');
        });
    }
}
</script>

{% endblock %}
