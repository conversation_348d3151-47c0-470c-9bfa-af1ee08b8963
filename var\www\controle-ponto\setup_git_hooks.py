#!/usr/bin/env python3
"""
CONFIGURAR GIT HOOKS AUTOMÁTICOS

Este script configura hooks do Git para executar verificações automáticas.
Após executar este script, toda vez que você tentar fazer commit,
os testes serão executados automaticamente.
"""

import os
import stat
from pathlib import Path

def create_pre_commit_hook():
    """Criar hook pre-commit do Git"""
    
    # Caminho do hook
    git_hooks_dir = Path(".git/hooks")
    git_hooks_dir.mkdir(exist_ok=True)
    
    pre_commit_file = git_hooks_dir / "pre-commit"
    
    # Conteúdo do hook
    hook_content = """#!/bin/bash
# Hook automático para prevenção de regressões
# Gerado automaticamente pelo setup_git_hooks.py

echo "🛡️ Executando verificação anti-regressão..."
echo "⏰ $(date '+%H:%M:%S')"

# Executar verificação
python pre_commit_check.py

# Verificar resultado
if [ $? -eq 0 ]; then
    echo "✅ Commit aprovado!"
    exit 0
else
    echo "❌ Commit rejeitado - corrija os problemas acima"
    echo "💡 Dica: Para forçar commit (não recomendado): git commit --no-verify"
    exit 1
fi
"""
    
    # Escrever arquivo
    with open(pre_commit_file, 'w', encoding='utf-8') as f:
        f.write(hook_content)
    
    # Tornar executável (Unix/Linux)
    if os.name != 'nt':  # Se não for Windows
        os.chmod(pre_commit_file, stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)
    
    print(f"✅ Hook criado: {pre_commit_file}")
    return pre_commit_file

def create_manual_commands():
    """Criar comandos manuais úteis"""
    
    # Script para bypass (emergência)
    bypass_script = Path("emergency_commit.py")
    
    bypass_content = '''#!/usr/bin/env python3
"""
COMMIT DE EMERGÊNCIA

Use apenas em emergências quando precisar fazer commit 
mesmo com problemas detectados.
"""

import subprocess
import sys

def emergency_commit():
    message = input("Mensagem do commit de emergência: ")
    
    print("⚠️  ATENÇÃO: Fazendo commit de emergência!")
    print("⚠️  Bypassing verificações de segurança!")
    
    # Commit sem verificações
    subprocess.run(["git", "add", "."])
    subprocess.run(["git", "commit", "--no-verify", "-m", f"EMERGENCY: {message}"])
    
    print("❌ COMMIT REALIZADO SEM VERIFICAÇÕES")
    print("🔧 CORRIJA OS PROBLEMAS O MAIS RÁPIDO POSSÍVEL!")

if __name__ == "__main__":
    emergency_commit()
'''
    
    with open(bypass_script, 'w', encoding='utf-8') as f:
        f.write(bypass_content)
    
    print(f"✅ Script de emergência criado: {bypass_script}")

def main():
    """Configurar sistema completo"""
    print("🛠️ CONFIGURANDO SISTEMA ANTI-REGRESSÃO")
    print("=" * 50)
    
    # Verificar se estamos em um repo Git
    if not Path(".git").exists():
        print("❌ Este não é um repositório Git!")
        print("💡 Execute: git init")
        return False
    
    # Verificar se pre_commit_check.py existe
    if not Path("pre_commit_check.py").exists():
        print("❌ Arquivo pre_commit_check.py não encontrado!")
        print("💡 Execute este script no diretório correto")
        return False
    
    # Criar hook
    hook_file = create_pre_commit_hook()
    
    # Criar comandos manuais
    create_manual_commands()
    
    print()
    print("✅ CONFIGURAÇÃO CONCLUÍDA!")
    print()
    print("📋 COMO USAR:")
    print("   🔄 Commits normais: git add . && git commit -m 'mensagem'")
    print("      → Verificação automática antes de cada commit")
    print()
    print("   🚨 Emergência: python emergency_commit.py")
    print("      → Bypass das verificações (use com cuidado)")
    print()
    print("   🧪 Manual: python pre_commit_check.py") 
    print("      → Verificação manual quando quiser")
    print()
    print("🎯 RESULTADO:")
    print("   ✅ Seus commits agora são protegidos contra regressões")
    print("   ✅ Problemas serão detectados ANTES de quebrar")
    print("   ✅ Backups automáticos a cada verificação")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 Sistema anti-regressão ativo!")
    else:
        print("\n❌ Configuração falhou")
        sys.exit(1) 