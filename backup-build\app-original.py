from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
import pymysql
import hashlib
import secrets
import os
from datetime import datetime, timedelta
import logging
import base64
import numpy as np

# ===================================================
# NOVAS ROTAS API PARA SISTEMA BIOMÉTRICO V2.0
# ===================================================

@app.route('/api/biometric/verify', methods=['POST'])
def api_biometric_verify():
    """
    API para verificação de template biométrico contra banco de dados
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'Dados inválidos'}), 400
            
        template = data.get('template')
        security_hash = data.get('security_hash')
        timestamp = data.get('timestamp')
        
        if not template or not security_hash:
            return jsonify({'error': 'Template biométrico ou hash de segurança ausente'}), 400
        
        # Validar timestamp (não pode ser muito antigo)
        try:
            req_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            if (datetime.now() - req_time).total_seconds() > 300:  # 5 minutos
                return jsonify({'error': 'Timestamp expirado'}), 400
        except:
            return jsonify({'error': 'Timestamp inválido'}), 400
        
        # Buscar funcionário pela biometria
        employee = verify_biometric_template(template)
        
        if employee:
            logging.info(f"[BIOMETRIC] Verificação bem-sucedida para funcionário ID: {employee['id']}")
            return jsonify({
                'success': True,
                'employee': {
                    'id': employee['id'],
                    'name': employee['nome'],
                    'department': employee.get('departamento', 'N/A')
                }
            })
        else:
            logging.warning(f"[BIOMETRIC] Template não reconhecido no sistema")
            return jsonify({
                'success': False,
                'error': 'Biometria não reconhecida'
            })
            
    except Exception as e:
        logging.error(f"[BIOMETRIC] Erro na verificação: {str(e)}")
        return jsonify({'error': 'Erro interno do servidor'}), 500

@app.route('/api/attendance/register', methods=['POST'])
def api_attendance_register():
    """
    API para registro de presença com verificação biométrica
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'Dados inválidos'}), 400
        
        employee_id = data.get('employee_id')
        attendance_type = data.get('type')
        timestamp = data.get('timestamp')
        biometric_verified = data.get('biometric_verified', False)
        device_info = data.get('device_info', {})
        
        if not all([employee_id, attendance_type, timestamp]):
            return jsonify({'error': 'Dados obrigatórios ausentes'}), 400
        
        # Validar se funcionário existe
        connection = get_db_connection()
        cursor = connection.cursor()
        
        cursor.execute("SELECT * FROM funcionarios WHERE id = %s", (employee_id,))
        employee = cursor.fetchone()
        
        if not employee:
            cursor.close()
            connection.close()
            return jsonify({'error': 'Funcionário não encontrado'}), 404
        
        # Verificar se já existe registro recente (evitar duplicatas)
        cursor.execute("""
            SELECT id FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND ABS(TIMESTAMPDIFF(SECOND, data_hora, %s)) < 60
        """, (employee_id, timestamp))
        
        recent_record = cursor.fetchone()
        if recent_record:
            cursor.close()
            connection.close()
            return jsonify({'error': 'Registro já efetuado recentemente'}), 409
        
        # Inserir registro de presença
        cursor.execute("""
            INSERT INTO registros_ponto 
            (funcionario_id, data_hora, tipo, biometria_verificada, device_hash, user_agent)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            employee_id,
            timestamp,
            attendance_type,
            biometric_verified,
            device_info.get('hardware_hash', ''),
            device_info.get('user_agent', '')
        ))
        
        record_id = cursor.lastrowid
        connection.commit()
        
        cursor.close()
        connection.close()
        
        logging.info(f"[ATTENDANCE] Registro criado: ID {record_id}, Funcionário: {employee_id}, Tipo: {attendance_type}")
        
        return jsonify({
            'success': True,
            'record_id': record_id,
            'message': 'Presença registrada com sucesso'
        })
        
    except Exception as e:
        logging.error(f"[ATTENDANCE] Erro no registro: {str(e)}")
        return jsonify({'error': 'Erro interno do servidor'}), 500

def verify_biometric_template(template):
    """
    Verificar template biométrico contra todos os funcionários cadastrados
    """
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # Buscar todos os funcionários com biometria cadastrada
        cursor.execute("""
            SELECT id, nome, departamento, template_biometrico 
            FROM funcionarios 
            WHERE template_biometrico IS NOT NULL 
            AND template_biometrico != ''
        """)
        
        employees = cursor.fetchall()
        cursor.close()
        connection.close()
        
        # Comparar template com cada funcionário
        for employee in employees:
            if compare_biometric_templates(template, employee['template_biometrico']):
                return employee
                
        return None
        
    except Exception as e:
        logging.error(f"[BIOMETRIC] Erro na verificação de template: {str(e)}")
        return None

def compare_biometric_templates(template1, template2, threshold=0.7):
    """
    Comparar dois templates biométricos
    Retorna True se a similaridade for maior que o threshold
    """
    try:
        # Decodificar templates base64
        t1_bytes = base64.b64decode(template1)
        t2_bytes = base64.b64decode(template2)
        
        # Converter para arrays numpy para comparação
        t1_array = np.frombuffer(t1_bytes, dtype=np.uint8)
        t2_array = np.frombuffer(t2_bytes, dtype=np.uint8)
        
        # Garantir que os templates têm o mesmo tamanho
        if len(t1_array) != len(t2_array):
            return False
        
        # Calcular similaridade usando correlação
        similarity = np.corrcoef(t1_array, t2_array)[0, 1]
        
        # Lidar com NaN (quando não há variação nos dados)
        if np.isnan(similarity):
            similarity = 1.0 if np.array_equal(t1_array, t2_array) else 0.0
        
        logging.info(f"[BIOMETRIC] Similaridade calculada: {similarity:.3f}")
        
        return similarity >= threshold
        
    except Exception as e:
        logging.error(f"[BIOMETRIC] Erro na comparação de templates: {str(e)}")
        return False

@app.route('/api/system/status', methods=['GET'])
def api_system_status():
    """
    API para verificar status do sistema biométrico
    """
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # Verificar status do banco
        cursor.execute("SELECT COUNT(*) as total FROM funcionarios")
        employees_count = cursor.fetchone()['total']
        
        cursor.execute("SELECT COUNT(*) as total FROM funcionarios WHERE template_biometrico IS NOT NULL")
        biometric_count = cursor.fetchone()['total']
        
        cursor.execute("SELECT COUNT(*) as total FROM registros_ponto WHERE DATE(data_hora) = CURDATE()")
        today_records = cursor.fetchone()['total']
        
        cursor.close()
        connection.close()
        
        return jsonify({
            'status': 'online',
            'database': 'connected',
            'employees_total': employees_count,
            'employees_with_biometric': biometric_count,
            'today_records': today_records,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logging.error(f"[SYSTEM] Erro no status: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

# ===================================================
# ATUALIZAÇÃO DA ROTA BIOMÉTRICA EXISTENTE
# ===================================================

@app.route('/registro-ponto/biometrico')
def registro_biometrico():
    """
    Página de registro biométrico atualizada
    """
    if 'user_id' not in session:
        flash('Acesso negado. Faça login primeiro.', 'error')
        return redirect(url_for('login'))
    
    return render_template('biometrico.html') 