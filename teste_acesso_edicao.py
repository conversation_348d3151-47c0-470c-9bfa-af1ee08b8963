#!/usr/bin/env python3
"""
Teste de acesso à página de edição
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

import requests
from requests.auth import HTTPBasicAuth

def teste_acesso_edicao():
    """Teste de acesso à edição"""
    print("🌐 TESTE: ACESSO À PÁGINA DE EDIÇÃO")
    print("=" * 50)
    
    try:
        # URL da edição do Richardson
        url = "http://localhost:5000/funcionarios/1/editar"
        
        print(f"📋 Testando acesso a: {url}")
        
        # Fazer requisição sem autenticação primeiro
        print(f"\n🔓 1. TESTE SEM AUTENTICAÇÃO:")
        response = requests.get(url, allow_redirects=False)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 302:
            print(f"   ✅ Redirecionamento esperado (login necessário)")
            location = response.headers.get('Location', 'N/A')
            print(f"   📍 Redirecionando para: {location}")
        elif response.status_code == 200:
            print(f"   ⚠️ Acesso permitido sem autenticação (pode ser problema)")
        else:
            print(f"   ❌ Status inesperado: {response.status_code}")
            
        # Testar com sessão (simulando login)
        print(f"\n🔐 2. TESTE COM SESSÃO:")
        session = requests.Session()
        
        # Primeiro, tentar fazer login
        login_url = "http://localhost:5000/login"
        login_data = {
            'usuario': 'admin',  # Assumindo que existe um usuário admin
            'senha': 'admin'     # Senha padrão
        }
        
        login_response = session.post(login_url, data=login_data, allow_redirects=False)
        print(f"   Login Status: {login_response.status_code}")
        
        if login_response.status_code in [200, 302]:
            # Tentar acessar a edição com sessão
            edit_response = session.get(url, allow_redirects=False)
            print(f"   Edição Status: {edit_response.status_code}")
            
            if edit_response.status_code == 200:
                print(f"   ✅ Acesso à edição bem-sucedido!")
                
                # Verificar se há erros no HTML
                content = edit_response.text
                if "erro" in content.lower() or "error" in content.lower():
                    print(f"   ⚠️ Possíveis erros encontrados no HTML")
                else:
                    print(f"   ✅ HTML carregado sem erros aparentes")
                    
                # Verificar se a jornada está sendo exibida
                if "Jornada da Empresa" in content:
                    print(f"   ✅ Seção de jornada encontrada no HTML")
                else:
                    print(f"   ⚠️ Seção de jornada não encontrada")
                    
            elif edit_response.status_code == 302:
                print(f"   ⚠️ Ainda redirecionando (problema de autenticação)")
            else:
                print(f"   ❌ Erro ao acessar edição: {edit_response.status_code}")
        else:
            print(f"   ❌ Falha no login")
            
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

def verificar_servico():
    """Verificar se o serviço está rodando"""
    print(f"\n🔍 VERIFICANDO SERVIÇO:")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:5000/", timeout=5)
        print(f"   Status da aplicação: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ Aplicação está rodando")
        else:
            print(f"   ⚠️ Aplicação com problemas")
            
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Aplicação não está rodando ou não acessível")
    except Exception as e:
        print(f"   ❌ Erro ao verificar serviço: {e}")

if __name__ == "__main__":
    verificar_servico()
    sucesso = teste_acesso_edicao()
    print(f"\n{'✅ TESTE CONCLUÍDO' if sucesso else '❌ TESTE COM ERROS'}")
