# 📋 ANÁLISE COMPLETA - SISTEMA DE CONTROLE DE PONTO

**Data:** 11/07/2025  
**Sistema:** RLPONTO-WEB v1.0  
**Objetivo:** Análise completa do fluxo desde cadastro até batida de ponto

---

## 🎯 **1. ARMAZENAMENTO DAS JORNADAS**

### **1.1 Estrutura Atual Identificada:**

#### **Tabela `jornadas_trabalho` (PRINCIPAL):**
```sql
CREATE TABLE jornadas_trabalho (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_id INT NOT NULL,
    nome_jornada VARCHAR(100) NOT NULL,
    seg_qui_entrada TIME NOT NULL DEFAULT '08:00:00',
    seg_qui_saida TIME NOT NULL DEFAULT '17:00:00',
    sexta_entrada TIME DEFAULT '08:00:00',
    sexta_saida TIME DEFAULT '16:30:00',
    intervalo_inicio TIME DEFAULT '12:00:00',
    intervalo_fim TIME DEFAULT '13:00:00',
    tolerancia_entrada_minutos INT DEFAULT 15,
    ativa BOOLEAN DEFAULT TRUE,
    padrao BOOLEAN DEFAULT FALSE
);
```

#### **Tabela `funcionarios` (REFERÊNCIA):**
```sql
-- Campo de ligação
jornada_trabalho_id INT DEFAULT NULL,

-- Campos legados (não utilizados atualmente)
jornada_seg_qui_entrada TIME,
jornada_seg_qui_saida TIME,
jornada_sex_entrada TIME,
jornada_sex_saida TIME,
jornada_intervalo_entrada TIME,
jornada_intervalo_saida TIME,
tolerancia_ponto INT
```

### **1.2 Relacionamento:**
```
empresas (id) → jornadas_trabalho (empresa_id)
jornadas_trabalho (id) → funcionarios (jornada_trabalho_id)
```

---

## 🔄 **2. FONTE DA JORNADA NO CADASTRO**

### **2.1 Processo de Cadastro:**

#### **Novo Funcionário:**
```python
# 1. Buscar jornada padrão da empresa
jornada_empresa = DatabaseManager.execute_query("""
    SELECT id FROM jornadas_trabalho
    WHERE empresa_id = %s AND padrao = 1 AND ativa = 1
    LIMIT 1
""", (empresa_id,))

# 2. Aplicar automaticamente
if jornada_empresa:
    data['jornada_trabalho_id'] = jornada_empresa[0]['id']
    data['usa_horario_empresa'] = True
```

#### **Edição de Funcionário:**
```python
# Preservar jornada existente
funcionario_atual = FuncionarioQueries.get_with_epis(funcionario_id)
if funcionario_atual and funcionario_atual.get('jornada_trabalho_id'):
    data['jornada_trabalho_id'] = funcionario_atual['jornada_trabalho_id']
```

### **2.2 Interface de Cadastro:**
- **Jornada definida pela empresa** (automática)
- **Campos individuais** (legados, não utilizados)
- **Herança automática** da jornada padrão da empresa

---

## 🕐 **3. INTERFACE DE BATIDA DE PONTO**

### **3.1 Função de Obtenção de Horários:**

#### **Função Atual (PROBLEMÁTICA):**
```python
def obter_horarios_funcionario(funcionario_id):
    # ❌ PROBLEMA: Busca na tabela horarios_trabalho via empresa
    cursor.execute("""
        SELECT ht.entrada_manha, ht.saida_almoco, ht.entrada_tarde, ht.saida
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN horarios_trabalho ht ON e.id = ht.empresa_id
        WHERE f.id = %s
    """, (funcionario_id,))
```

#### **Função Correta (NECESSÁRIA):**
```python
def obter_horarios_funcionario(funcionario_id):
    # ✅ CORREÇÃO: Buscar na tabela jornadas_trabalho
    cursor.execute("""
        SELECT 
            jt.seg_qui_entrada as entrada_manha,
            jt.intervalo_inicio as saida_almoco,
            jt.intervalo_fim as entrada_tarde,
            jt.seg_qui_saida as saida,
            jt.tolerancia_entrada_minutos as tolerancia_minutos
        FROM funcionarios f
        INNER JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.id = %s AND jt.ativa = 1
    """, (funcionario_id,))
```

### **3.2 Validação de Horários:**
```python
def validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario, funcionario_id):
    # ❌ PROBLEMA: Se horarios_funcionario é None, validação falha
    if not horarios_funcionario:
        return {'permitido': False, 'mensagem': 'Horário não configurado'}
    
    # Validação por período do dia
    entrada_manha = horarios_funcionario['entrada_manha']
    saida_almoco = horarios_funcionario['saida_almoco']
    # ... resto da validação
```

---

## ⚙️ **4. LÓGICA B1, B2, B3, B4**

### **4.1 Sequência Padrão:**
```python
sequencia_padrao = {
    1: 'entrada_manha',    # B1 - Entrada
    2: 'saida_almoco',     # B2 - Início intervalo
    3: 'entrada_tarde',    # B3 - Retorno intervalo
    4: 'saida'             # B4 - Saída
}
```

### **4.2 Classificação Inteligente:**

#### **Por Período do Dia:**
```python
def classificar_batida_inteligente(funcionario_id, numero_batida, turno_info, hora_atual):
    # 1. Obter horários do funcionário
    horarios = obter_horarios_funcionario(funcionario_id)
    
    # 2. Determinar período atual
    if entrada_manha <= hora_atual <= saida_almoco:
        periodo_atual = "manha"
    elif saida_almoco < hora_atual < entrada_tarde:
        periodo_atual = "almoco"
    elif entrada_tarde <= hora_atual <= saida:
        periodo_atual = "tarde"
    
    # 3. Classificar baseado no período
    if numero_batida == 1:
        if periodo_atual in ["antes_expediente", "manha"]:
            return 'entrada_manha'
        elif periodo_atual == "tarde":
            return 'entrada_tarde'
```

### **4.3 Validação de Sequência:**
```python
def validar_sequencia_batidas(funcionario_id, data_referencia):
    sequencias_esperadas = {
        1: ['entrada_manha'],
        2: ['entrada_manha', 'saida'],
        3: ['entrada_manha', 'saida_almoco', 'entrada_tarde'],
        4: ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']
    }
    
    # Validar ordem cronológica
    # Validar tipos em sequência
    # Validar regras específicas
```

---

## 🚨 **5. PROBLEMAS IDENTIFICADOS**

### **5.1 Inconsistência na Consulta de Horários:**
- **Função busca:** `horarios_trabalho` via empresa
- **Dados estão em:** `jornadas_trabalho` via funcionário
- **Resultado:** `NULL` retornado, validações ignoradas

### **5.2 Validação Falha:**
- Funcionários sem horário passam por todas as validações
- Registros fora de sequência são aceitos
- Horários inadequados são permitidos

### **5.3 Caso TESTE 5:**
- **Jornada configurada:** 8:00-17:00 (seg-qui), 8:00-16:30 (sex)
- **Registros aceitos:** 08:59, 14:47, 20:30 (fora de sequência)
- **Causa:** `obter_horarios_funcionario()` retorna `NULL`

---

## 🔧 **6. CORREÇÕES NECESSÁRIAS**

### **6.1 Prioridade CRÍTICA:**
1. **Corrigir `obter_horarios_funcionario()`** - Buscar em `jornadas_trabalho`
2. **Implementar validação de funcionários sem jornada**
3. **Bloquear registros fora de sequência**

### **6.2 Prioridade ALTA:**
1. **Validar horários permitidos por período**
2. **Implementar tolerância correta**
3. **Melhorar classificação inteligente**

### **6.3 Prioridade MÉDIA:**
1. **Migrar dados legados**
2. **Limpar campos não utilizados**
3. **Documentar nova estrutura**

---

## 📊 **7. FLUXO CORRETO PROPOSTO**

### **7.1 Cadastro → Jornada:**
```
Empresa → Jornada Padrão → Funcionário.jornada_trabalho_id
```

### **7.2 Batida → Validação:**
```
Funcionário.jornada_trabalho_id → jornadas_trabalho → Horários → Validação
```

### **7.3 Sequência B1-B4:**
```
B1: entrada_manha (período manhã)
B2: saida_almoco (período manhã/almoço)
B3: entrada_tarde (período tarde)
B4: saida (período tarde/após)
```

---

**Status:** ANÁLISE COMPLETA  
**Próximo Passo:** Implementar correções críticas
