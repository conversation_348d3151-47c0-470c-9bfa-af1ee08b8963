#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Funções Auxiliares - Controle de Ponto
---------------------------------------

<PERSON><PERSON> módulo contém funções auxiliares e utilitários gerais
utilizados em todo o sistema.
"""

import json
import re
import logging
from datetime import datetime, date
from typing import Any, Dict, List, Optional
from werkzeug.datastructures import FileStorage
import os
import hashlib

logger = logging.getLogger('controle-ponto.helpers')

def format_cpf(cpf):
    """
    Formata CPF para exibição.
    
    Args:
        cpf (str): CPF sem formatação
        
    Returns:
        str: CPF formatado (000.000.000-00)
    """
    if not cpf:
        return ""
    
    # Remove caracteres não numéricos
    cpf = re.sub(r'\D', '', cpf)
    
    if len(cpf) == 11:
        return f"{cpf[:3]}.{cpf[3:6]}.{cpf[6:9]}-{cpf[9:]}"
    return cpf

def format_telefone(telefone):
    """
    Formata telefone para exibição.
    
    Args:
        telefone (str): Telefone sem formatação
        
    Returns:
        str: Telefone formatado
    """
    if not telefone:
        return ""
    
    # Remove caracteres não numéricos
    telefone = re.sub(r'\D', '', telefone)
    
    if len(telefone) == 11:  # Celular
        return f"({telefone[:2]}) {telefone[2:7]}-{telefone[7:]}"
    elif len(telefone) == 10:  # Fixo
        return f"({telefone[:2]}) {telefone[2:6]}-{telefone[6:]}"
    return telefone

def format_cep(cep):
    """
    Formata CEP para exibição.
    
    Args:
        cep (str): CEP sem formatação
        
    Returns:
        str: CEP formatado (00000-000)
    """
    if not cep:
        return ""
    
    # Remove caracteres não numéricos
    cep = re.sub(r'\D', '', cep)
    
    if len(cep) == 8:
        return f"{cep[:5]}-{cep[5:]}"
    return cep

def format_date(date_obj, format_str='%d/%m/%Y'):
    """
    Formata data para exibição.
    
    Args:
        date_obj: Objeto date, datetime ou string
        format_str (str): Formato de saída
        
    Returns:
        str: Data formatada
    """
    if not date_obj:
        return ""
    
    if isinstance(date_obj, str):
        try:
            # Tenta parsing de diferentes formatos
            for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%Y-%m-%d %H:%M:%S']:
                try:
                    date_obj = datetime.strptime(date_obj, fmt)
                    break
                except ValueError:
                    continue
            else:
                return date_obj  # Retorna original se não conseguir fazer parse
        except:
            return date_obj
    
    if isinstance(date_obj, (date, datetime)):
        return date_obj.strftime(format_str)
    
    return str(date_obj)

def format_time(time_obj, format_str='%H:%M'):
    """
    Formata horário para exibição.
    
    Args:
        time_obj: Objeto time, datetime ou string
        format_str (str): Formato de saída
        
    Returns:
        str: Horário formatado
    """
    if not time_obj:
        return ""
    
    if isinstance(time_obj, str):
        try:
            # Tenta parsing de diferentes formatos
            for fmt in ['%H:%M:%S', '%H:%M', '%Y-%m-%d %H:%M:%S']:
                try:
                    time_obj = datetime.strptime(time_obj, fmt)
                    break
                except ValueError:
                    continue
            else:
                return time_obj
        except:
            return time_obj
    
    if hasattr(time_obj, 'strftime'):
        return time_obj.strftime(format_str)
    
    return str(time_obj)

def validate_cpf(cpf):
    """
    Valida CPF.
    
    Args:
        cpf (str): CPF a ser validado
        
    Returns:
        bool: True se válido, False caso contrário
    """
    if not cpf:
        return False
    
    # Remove caracteres não numéricos
    cpf = re.sub(r'\D', '', cpf)
    
    # Verifica se tem 11 dígitos
    if len(cpf) != 11:
        return False
    
    # Verifica se não é uma sequência de números iguais
    if cpf == cpf[0] * 11:
        return False
    
    # Calcula primeiro dígito verificador
    soma = sum(int(cpf[i]) * (10 - i) for i in range(9))
    resto = soma % 11
    digito1 = 0 if resto < 2 else 11 - resto
    
    # Calcula segundo dígito verificador
    soma = sum(int(cpf[i]) * (11 - i) for i in range(10))
    resto = soma % 11
    digito2 = 0 if resto < 2 else 11 - resto
    
    # Verifica se os dígitos estão corretos
    return cpf[9:11] == f"{digito1}{digito2}"

def validate_email(email):
    """
    Valida email básico.
    
    Args:
        email (str): Email a ser validado
        
    Returns:
        bool: True se válido, False caso contrário
    """
    if not email:
        return True  # Email é opcional
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def clean_numeric_string(value):
    """
    Remove caracteres não numéricos de uma string.
    
    Args:
        value (str): String a ser limpa
        
    Returns:
        str: String apenas com números
    """
    if not value:
        return ""
    return re.sub(r'\D', '', str(value))

def safe_int(value, default=0):
    """
    Converte valor para int de forma segura.
    
    Args:
        value: Valor a ser convertido
        default: Valor padrão se conversão falhar
        
    Returns:
        int: Valor convertido ou padrão
    """
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def safe_float(value, default=0.0):
    """
    Converte valor para float de forma segura.
    
    Args:
        value: Valor a ser convertido
        default (float): Valor padrão em caso de erro
        
    Returns:
        float: Valor convertido ou padrão
    """
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def parse_biometria_data(biometria_json):
    """
    Faz parse seguro dos dados de biometria.
    
    Args:
        biometria_json (str): JSON com dados biométricos
        
    Returns:
        dict: Dados parseados ou None
    """
    if not biometria_json:
        return None
    
    try:
        data = json.loads(biometria_json)
        if isinstance(data, dict) and 'template' in data:
            return data
    except (json.JSONDecodeError, TypeError):
        pass
    
    return None

def get_status_badge_class(status):
    """
    Retorna classe CSS para badge de status.
    
    Args:
        status (str): Status do funcionário
        
    Returns:
        str: Classe CSS do badge
    """
    status_classes = {
        'Ativo': 'badge-success',
        'Inativo': 'badge-danger',
        'Pendente': 'badge-warning',
        'Suspenso': 'badge-secondary'
    }
    return status_classes.get(status, 'badge-secondary')

def truncate_string(text, max_length=50, suffix='...'):
    """
    Trunca string se exceder tamanho máximo.
    
    Args:
        text (str): Texto a ser truncado
        max_length (int): Tamanho máximo
        suffix (str): Sufixo para texto truncado
        
    Returns:
        str: Texto truncado se necessário
    """
    if not text:
        return ""
    
    text = str(text)
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def generate_breadcrumbs(current_page, **kwargs):
    """
    Gera breadcrumbs para navegação.
    
    Args:
        current_page (str): Página atual
        **kwargs: Parâmetros adicionais
        
    Returns:
        list: Lista de breadcrumbs
    """
    breadcrumbs = [
        {'text': 'Início', 'url': '/'}
    ]
    
    if current_page == 'funcionarios':
        breadcrumbs.append({'text': 'Funcionários', 'url': '/funcionarios'})
    elif current_page == 'funcionario_detalhes':
        breadcrumbs.extend([
            {'text': 'Funcionários', 'url': '/funcionarios'},
            {'text': f"#{kwargs.get('funcionario_id', '')}", 'url': None}
        ])
    elif current_page == 'funcionario_editar':
        breadcrumbs.extend([
            {'text': 'Funcionários', 'url': '/funcionarios'},
            {'text': f"#{kwargs.get('funcionario_id', '')}", 'url': f"/funcionarios/{kwargs.get('funcionario_id', '')}"},
            {'text': 'Editar', 'url': None}
        ])
    elif current_page == 'cadastrar':
        breadcrumbs.append({'text': 'Cadastrar Funcionário', 'url': None})
    
    return breadcrumbs

class FormValidator:
    """
    Classe para validação de formulários.
    """
    
    def __init__(self):
        self.errors = {}
    
    def add_error(self, field, message):
        """Adiciona erro de validação."""
        if field not in self.errors:
            self.errors[field] = []
        self.errors[field].append(message)
    
    def has_errors(self):
        """Verifica se há erros."""
        return bool(self.errors)
    
    def get_errors(self):
        """Obtém todos os erros."""
        return self.errors
    
    def validate_required(self, value, field_name, display_name=None):
        """Valida campo obrigatório."""
        if not value or (isinstance(value, str) and not value.strip()):
            display_name = display_name or field_name.replace('_', ' ').title()
            self.add_error(field_name, f"{display_name} é obrigatório")
            return False
        return True
    
    def validate_cpf(self, cpf, field_name='cpf'):
        """Valida CPF."""
        if not cpf:
            return True  # Deixa validação de obrigatório para validate_required
        
        # Remove caracteres não numéricos
        cpf_limpo = re.sub(r'\D', '', str(cpf))
        
        if len(cpf_limpo) != 11:
            self.add_error(field_name, "CPF deve ter 11 dígitos")
            return False
        
        # Validação básica do algoritmo do CPF
        if cpf_limpo == cpf_limpo[0] * 11:  # Todos os dígitos iguais
            self.add_error(field_name, "CPF inválido")
            return False
        
        return True
    
    def validate_email(self, email, field_name='email'):
        """Valida email."""
        if not email:
            return True  # Email é opcional
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            self.add_error(field_name, "Email inválido")
            return False
        
        return True
    
    def validate_date(self, date_str, field_name, display_name=None):
        """Valida formato de data."""
        if not date_str:
            return True
        
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            display_name = display_name or field_name.replace('_', ' ').title()
            self.add_error(field_name, f"{display_name} deve estar no formato DD/MM/AAAA")
            return False
    
    def validate_time(self, time_str, field_name, display_name=None):
        """Valida formato de hora."""
        if not time_str:
            return True
        
        try:
            datetime.strptime(time_str, '%H:%M')
            return True
        except ValueError:
            display_name = display_name or field_name.replace('_', ' ').title()
            self.add_error(field_name, f"{display_name} deve estar no formato HH:MM")
            return False
    
    def validate_file_upload(self, file, field_name, allowed_types=None, max_size_mb=2):
        """Valida upload de arquivo."""
        if not file or not isinstance(file, FileStorage) or not file.filename:
            return True  # Arquivo é opcional
        
        # Verifica tamanho
        if hasattr(file, 'content_length') and file.content_length:
            size_mb = file.content_length / (1024 * 1024)
            if size_mb > max_size_mb:
                self.add_error(field_name, f"Arquivo deve ter no máximo {max_size_mb}MB")
                return False
        
        # Verifica tipo
        if allowed_types:
            file_ext = file.filename.rsplit('.', 1)[-1].lower()
            if file_ext not in allowed_types:
                self.add_error(field_name, f"Tipo de arquivo não permitido. Use: {', '.join(allowed_types)}")
                return False
        
        return True
    
    def validate_choice(self, value, valid_choices, field_name, display_name=None):
        """Valida se o valor está entre as opções permitidas."""
        if value not in valid_choices:
            display_name = display_name or field_name.replace('_', ' ').title()
            self.add_error(field_name, f"{display_name} inválido. Valor recebido: '{value}'. Opções válidas: {', '.join(str(c) for c in valid_choices)}")
            return False
        return True

def gerar_nome_foto_funcionario(funcionario_id, original_filename):
    ext = os.path.splitext(original_filename)[1].lower()
    if ext not in ['.jpg', '.jpeg', '.png', '.gif']:
        ext = '.jpg'
    timestamp = datetime.now().strftime('%Y%m%dT%H%M%S')
    return f'funcionario_{funcionario_id}_{timestamp}{ext}'

# ========================================
# NOVAS FUNÇÕES PARA SISTEMA DE REGISTROS DE PONTO
# Data: 08/01/2025
# ========================================

def validar_registro_duplicado(funcionario_id, tipo_registro, data_registro=None):
    """
    Verifica se já existe registro para o funcionário no mesmo período.
    
    Args:
        funcionario_id (int): ID do funcionário
        tipo_registro (str): Tipo do registro (entrada_manha, saida_almoco, etc.)
        data_registro (date, optional): Data do registro. Se None, usa data atual.
    
    Returns:
        bool: True se existe duplicata, False caso contrário
    """
    try:
        from .database import get_db_connection
        
        if data_registro is None:
            data_registro = datetime.now().date()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT COUNT(*) 
            FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND tipo_registro = %s 
            AND DATE(data_hora) = %s
        """, (funcionario_id, tipo_registro, data_registro))
        
        count = cursor.fetchone()[0]
        conn.close()
        
        return count > 0
        
    except Exception as e:
        logger.error(f"Erro ao validar duplicata de registro: {str(e)}")
        return False

def mascarar_cpf(cpf, nivel_acesso='usuario'):
    """
    Mascara um CPF para exibição.
    
    Args:
        cpf (str): CPF sem formatação
        nivel_acesso (str): Nível de acesso do usuário
        
    Returns:
        str: CPF mascarado ou completo
    """
    if not cpf:
        return "***.***.***-**"
        
    if nivel_acesso == 'admin':
        return format_cpf(cpf)
    
    # ✅ USUÁRIO COMUM VÊ APENAS OS ÚLTIMOS 4 DÍGITOS (CORRIGIDO)
    cpf_limpo = re.sub(r'[^0-9]', '', str(cpf))
    if len(cpf_limpo) == 11:
        # Mostrar apenas os 4 últimos dígitos: ***.***.***-XXXX
        ultimos_4 = cpf_limpo[-4:]
        return f"***.***.***-{ultimos_4}"
    return "***.***.***-**"

def mascarar_dados_relatorio(dados, nivel_acesso='usuario'):
    """
    Aplica mascaramento em dados sensíveis para relatórios.
    
    Args:
        dados (dict or list): Dados do relatório
        nivel_acesso (str): Nível de acesso do usuário
    
    Returns:
        dict or list: Dados com mascaramento aplicado
    """
    if isinstance(dados, list):
        return [mascarar_dados_relatorio(item, nivel_acesso) for item in dados]
    
    if isinstance(dados, dict):
        dados_mascarados = dados.copy()
        
        # Mascarar CPF
        if 'cpf' in dados_mascarados:
            dados_mascarados['cpf'] = mascarar_cpf(dados_mascarados['cpf'], nivel_acesso)
        
        # Remover template biométrico para usuários não admin
        if nivel_acesso != 'admin' and 'template_biometrico' in dados_mascarados:
            dados_mascarados.pop('template_biometrico', None)
        
        return dados_mascarados
    
    return dados

def log_acao_ponto(acao, funcionario_id=None, usuario_id=None, tipo_registro=None, metodo_registro=None, detalhes=None):
    """
    Registra ação relacionada a ponto no sistema para auditoria.
    
    Args:
        acao (str): Descrição da ação
        funcionario_id (int, optional): ID do funcionário
        usuario_id (int, optional): ID do usuário que executou ação
        tipo_registro (str, optional): Tipo do registro de ponto
        metodo_registro (str, optional): Método do registro (biometrico/manual)
        detalhes (dict, optional): Detalhes adicionais
    """
    try:
        # Preparar detalhes estruturados
        detalhes_log = {
            'funcionario_id': funcionario_id,
            'tipo_registro': tipo_registro,
            'metodo_registro': metodo_registro,
            'timestamp': datetime.now().isoformat(),
            'user_agent': request.headers.get('User-Agent', '') if 'request' in globals() else '',
            'ip_origem': request.remote_addr if 'request' in globals() else ''
        }
        
        if detalhes:
            detalhes_log.update(detalhes)
        
        # Log estruturado
        logger.info(f"[PONTO] {acao} - Funcionário: {funcionario_id} - Usuário: {usuario_id} - Detalhes: {detalhes_log}")
        
        # Opcional: salvar em tabela de auditoria específica
        # Pode ser implementado depois se necessário
        
    except Exception as e:
        logger.error(f"Erro ao registrar log de ação de ponto: {str(e)}")

def obter_ip_usuario():
    """
    Obtém o IP real do usuário, considerando proxies.
    
    Returns:
        str: Endereço IP do usuário
    """
    try:
        from flask import request
        
        # Verificar headers de proxy primeiro
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        return request.remote_addr or 'unknown'
        
    except Exception:
        return 'unknown'

def validar_tipo_registro(tipo_registro):
    """
    Valida se o tipo de registro é válido.
    
    Args:
        tipo_registro (str): Tipo do registro
    
    Returns:
        bool: True se válido, False caso contrário
    """
    tipos_validos = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']
    return tipo_registro in tipos_validos

def validar_metodo_registro(metodo_registro):
    """
    Valida se o método de registro é válido.
    
    Args:
        metodo_registro (str): Método do registro
    
    Returns:
        bool: True se válido, False caso contrário
    """
    metodos_validos = ['biometrico', 'manual']
    return metodo_registro in metodos_validos

def formatar_tipo_registro_descricao(tipo_registro):
    """
    Converte tipo de registro em descrição legível.
    
    Args:
        tipo_registro (str): Tipo do registro
    
    Returns:
        str: Descrição formatada
    """
    descricoes = {
        'entrada_manha': 'Entrada Manhã',
        'saida_almoco': 'Saída Almoço',
        'entrada_tarde': 'Entrada Tarde',
        'saida': 'Saída'
    }
    return descricoes.get(tipo_registro, tipo_registro.replace('_', ' ').title())

def calcular_horas_trabalhadas(registros_dia, funcionario_info=None):
    """
    Calcula horas trabalhadas aproximadas baseado nos registros do dia.
    
    Args:
        registros_dia (list): Lista de registros do dia com data_hora e tipo_registro
        funcionario_info (dict, optional): Informações do funcionário incluindo jornada e configurações
    
    Returns:
        dict: Informações sobre horas trabalhadas, horas extras e banco de horas
    """
    if not registros_dia:
        return {'horas_trabalhadas': 0, 'completo': False, 'observacao': 'Nenhum registro', 'horas_extras': 0, 'banco_horas': 0}
    
    # Organizar registros por tipo
    registros_organizados = {}
    for registro in registros_dia:
        tipo = registro.get('tipo_registro')
        data_hora = registro.get('data_hora')
        if tipo and data_hora:
            registros_organizados[tipo] = data_hora
    
    horas_manha = 0
    horas_tarde = 0
    observacoes = []
    
    # Calcular período manhã
    if 'entrada_manha' in registros_organizados and 'saida_almoco' in registros_organizados:
        entrada_manha = registros_organizados['entrada_manha']
        saida_almoco = registros_organizados['saida_almoco']
        
        if isinstance(entrada_manha, str):
            entrada_manha = datetime.fromisoformat(entrada_manha.replace('Z', '+00:00'))
        if isinstance(saida_almoco, str):
            saida_almoco = datetime.fromisoformat(saida_almoco.replace('Z', '+00:00'))
        
        delta_manha = saida_almoco - entrada_manha
        horas_manha = delta_manha.total_seconds() / 3600
    else:
        observacoes.append('Período manhã incompleto')
    
    # Calcular período tarde
    if 'entrada_tarde' in registros_organizados and 'saida' in registros_organizados:
        entrada_tarde = registros_organizados['entrada_tarde']
        saida = registros_organizados['saida']
        
        if isinstance(entrada_tarde, str):
            entrada_tarde = datetime.fromisoformat(entrada_tarde.replace('Z', '+00:00'))
        if isinstance(saida, str):
            saida = datetime.fromisoformat(saida.replace('Z', '+00:00'))
        
        delta_tarde = saida - entrada_tarde
        horas_tarde = delta_tarde.total_seconds() / 3600
    else:
        observacoes.append('Período tarde incompleto')
    
    total_horas = horas_manha + horas_tarde
    completo = len(registros_organizados) == 4  # Todos os 4 registros
    
    # Calcular horas extras e banco de horas
    horas_extras = 0
    banco_horas = 0
    
    # Se temos informações do funcionário, calcular horas extras e banco de horas
    if funcionario_info and completo:
        # Determinar jornada padrão do funcionário
        jornada_padrao = 8.0  # Padrão de 8 horas
        
        # Se houver informações específicas de jornada no funcionario_info, usar elas
        if funcionario_info.get('jornada_seg_qui_entrada') and funcionario_info.get('jornada_seg_qui_saida') and \
           funcionario_info.get('jornada_intervalo_entrada') and funcionario_info.get('jornada_intervalo_saida'):
            
            # Converter strings para objetos time
            try:
                entrada_padrao = datetime.strptime(funcionario_info.get('jornada_seg_qui_entrada'), '%H:%M').time()
                saida_padrao = datetime.strptime(funcionario_info.get('jornada_seg_qui_saida'), '%H:%M').time()
                intervalo_entrada = datetime.strptime(funcionario_info.get('jornada_intervalo_entrada'), '%H:%M').time()
                intervalo_saida = datetime.strptime(funcionario_info.get('jornada_intervalo_saida'), '%H:%M').time()
                
                # Calcular duração da jornada em horas
                entrada_dt = datetime.combine(date.today(), entrada_padrao)
                saida_dt = datetime.combine(date.today(), saida_padrao)
                intervalo_entrada_dt = datetime.combine(date.today(), intervalo_entrada)
                intervalo_saida_dt = datetime.combine(date.today(), intervalo_saida)
                
                jornada_manha = (intervalo_entrada_dt - entrada_dt).total_seconds() / 3600
                jornada_tarde = (saida_dt - intervalo_saida_dt).total_seconds() / 3600
                jornada_padrao = jornada_manha + jornada_tarde
            except (ValueError, TypeError):
                # Se houver erro na conversão, manter o padrão de 8 horas
                pass
        
        # Calcular horas extras (total - jornada padrão)
        horas_excedentes = max(0, total_horas - jornada_padrao)
        
        # Distribuir horas excedentes conforme configuração do funcionário
        if horas_excedentes > 0:
            # Se funcionário tem hora extra habilitada
            if funcionario_info.get('hora_extra'):
                horas_extras = horas_excedentes
            # Se funcionário tem banco de horas habilitado
            elif funcionario_info.get('banco_horas'):
                banco_horas = horas_excedentes
            # Se nenhum dos dois está habilitado, não contabiliza
    
    return {
        'horas_trabalhadas': round(total_horas, 2),
        'horas_manha': round(horas_manha, 2),
        'horas_tarde': round(horas_tarde, 2),
        'completo': completo,
        'observacao': '; '.join(observacoes) if observacoes else 'Dia completo',
        'horas_extras': round(horas_extras, 2),
        'banco_horas': round(banco_horas, 2)
    }

def gerar_dados_grafico_pontos(dados_relatorio):
    """
    Gera dados formatados para gráficos de relatórios de ponto.
    
    Args:
        dados_relatorio (list): Lista de registros de ponto
    
    Returns:
        dict: Dados formatados para gráficos
    """
    if not dados_relatorio:
        return {
            'tipos_registro': {},
            'metodos_registro': {},
            'pontualidade': {},
            'dados_diarios': []
        }
    
    # Contadores
    tipos_registro = {}
    metodos_registro = {}
    pontualidade = {'Pontual': 0, 'Atraso': 0}
    dados_diarios = {}
    
    for registro in dados_relatorio:
        # Contar tipos de registro
        tipo = registro.get('tipo_descricao', 'Não informado')
        tipos_registro[tipo] = tipos_registro.get(tipo, 0) + 1
        
        # Contar métodos de registro
        metodo = registro.get('metodo_registro', 'Não informado')
        metodos_registro[metodo] = metodos_registro.get(metodo, 0) + 1
        
        # Contar pontualidade
        status_pont = registro.get('status_pontualidade', 'Pontual')
        if status_pont in pontualidade:
            pontualidade[status_pont] += 1
        
        # Dados diários
        data_registro = registro.get('data_registro')
        if data_registro:
            if isinstance(data_registro, str):
                data_str = data_registro
            else:
                data_str = data_registro.strftime('%Y-%m-%d')
            
            if data_str not in dados_diarios:
                dados_diarios[data_str] = 0
            dados_diarios[data_str] += 1
    
    # Converter dados diários para lista ordenada
    dados_diarios_lista = []
    for data in sorted(dados_diarios.keys()):
        dados_diarios_lista.append({
            'data': data,
            'total': dados_diarios[data]
        })
    
    return {
        'tipos_registro': tipos_registro,
        'metodos_registro': metodos_registro,
        'pontualidade': pontualidade,
        'dados_diarios': dados_diarios_lista[-30:]  # Últimos 30 dias
    }

class RegistroPontoValidator:
    """
    Classe específica para validação de registros de ponto.
    Estende a funcionalidade do FormValidator existente.
    """
    
    def __init__(self):
        self.errors = {}
    
    def add_error(self, field, message):
        """Adiciona erro de validação."""
        if field not in self.errors:
            self.errors[field] = []
        self.errors[field].append(message)
    
    def has_errors(self):
        """Verifica se há erros."""
        return bool(self.errors)
    
    def get_errors(self):
        """Obtém todos os erros."""
        return self.errors
    
    def validar_registro_biometrico(self, dados):
        """
        Valida dados de registro biométrico.
        
        Args:
            dados (dict): Dados do registro biométrico
        
        Returns:
            bool: True se válido, False caso contrário
        """
        funcionario_id = dados.get('funcionario_id')
        tipo_registro = dados.get('tipo_registro')
        template_biometrico = dados.get('template_biometrico')
        qualidade = dados.get('qualidade_biometria')
        
        # Validar funcionário
        if not funcionario_id:
            self.add_error('funcionario_id', 'ID do funcionário é obrigatório')
        elif not isinstance(funcionario_id, int) or funcionario_id <= 0:
            self.add_error('funcionario_id', 'ID do funcionário deve ser um número positivo')
        
        # Validar tipo de registro
        if not validar_tipo_registro(tipo_registro):
            self.add_error('tipo_registro', 'Tipo de registro inválido')
        
        # Validar template biométrico
        if not template_biometrico:
            self.add_error('template_biometrico', 'Template biométrico é obrigatório')
        elif len(str(template_biometrico)) < 100:  # Template muito pequeno
            self.add_error('template_biometrico', 'Template biométrico inválido - muito pequeno')
        
        # Validar qualidade (opcional)
        if qualidade is not None:
            if not isinstance(qualidade, (int, float)) or not (0 <= qualidade <= 100):
                self.add_error('qualidade_biometria', 'Qualidade deve ser um número entre 0 e 100')
        
        # Verificar duplicata
        if funcionario_id and tipo_registro:
            if validar_registro_duplicado(funcionario_id, tipo_registro):
                self.add_error('duplicata', 'Já existe um registro deste tipo para hoje')
        
        return not self.has_errors()
    
    def validar_registro_manual(self, dados):
        """
        Valida dados de registro manual.
        
        Args:
            dados (dict): Dados do registro manual
        
        Returns:
            bool: True se válido, False caso contrário
        """
        funcionario_id = dados.get('funcionario_id')
        tipo_registro = dados.get('tipo_registro')
        criado_por = dados.get('criado_por')
        observacoes = dados.get('observacoes', '')
        
        # Validar funcionário
        if not funcionario_id:
            self.add_error('funcionario_id', 'ID do funcionário é obrigatório')
        elif not isinstance(funcionario_id, int) or funcionario_id <= 0:
            self.add_error('funcionario_id', 'ID do funcionário deve ser um número positivo')
        
        # Validar tipo de registro
        if not validar_tipo_registro(tipo_registro):
            self.add_error('tipo_registro', 'Tipo de registro inválido')
        
        # Validar usuário que criou
        if not criado_por:
            self.add_error('criado_por', 'ID do usuário criador é obrigatório')
        elif not isinstance(criado_por, int) or criado_por <= 0:
            self.add_error('criado_por', 'ID do usuário criador deve ser um número positivo')
        
        # Validar observações (opcional)
        if observacoes and len(observacoes) > 500:
            self.add_error('observacoes', 'Observações não podem exceder 500 caracteres')
        
        # Verificar duplicata
        if funcionario_id and tipo_registro:
            if validar_registro_duplicado(funcionario_id, tipo_registro):
                self.add_error('duplicata', 'Já existe um registro deste tipo para hoje')
        
        return not self.has_errors()
    
    def validar_filtros_relatorio(self, filtros):
        """
        Valida filtros de relatório.
        
        Args:
            filtros (dict): Filtros do relatório
        
        Returns:
            bool: True se válido, False caso contrário
        """
        try:
            data_inicio = filtros.get('data_inicio')
            data_fim = filtros.get('data_fim')
            funcionario_id = filtros.get('funcionario_id')
            tipo_registro = filtros.get('tipo_registro')
            setor = filtros.get('setor')
            metodo_registro = filtros.get('metodo_registro')
            
            # Validar datas
            if data_inicio:
                try:
                    data_inicio_obj = datetime.strptime(data_inicio, '%Y-%m-%d').date()
                except ValueError:
                    self.add_error('data_inicio', 'Data de início inválida')
                    data_inicio_obj = None
            else:
                data_inicio_obj = None
            
            if data_fim:
                try:
                    data_fim_obj = datetime.strptime(data_fim, '%Y-%m-%d').date()
                except ValueError:
                    self.add_error('data_fim', 'Data de fim inválida')
                    data_fim_obj = None
            else:
                data_fim_obj = None
            
            # Validar período
            if data_inicio_obj and data_fim_obj:
                if data_inicio_obj > data_fim_obj:
                    self.add_error('periodo', 'Data de início deve ser anterior à data de fim')
                
                # Limitar período de consulta (performance)
                delta = data_fim_obj - data_inicio_obj
                if delta.days > 365:
                    self.add_error('periodo', 'Período de consulta não pode exceder 1 ano')
            
            # Validar funcionário (opcional)
            if funcionario_id is not None:
                try:
                    funcionario_id_int = int(funcionario_id)
                    if funcionario_id_int <= 0:
                        self.add_error('funcionario_id', 'ID do funcionário deve ser um número positivo')
                except (ValueError, TypeError):
                    self.add_error('funcionario_id', 'ID do funcionário deve ser um número válido')
            
            # Validar tipo de registro (opcional)
            if tipo_registro and not validar_tipo_registro(tipo_registro):
                self.add_error('tipo_registro', 'Tipo de registro inválido')
            
            # Validar método de registro (opcional)
            if metodo_registro and metodo_registro not in ['biometrico', 'manual']:
                self.add_error('metodo_registro', 'Método de registro inválido')
            
            # Validar setor (opcional)
            if setor and not isinstance(setor, str):
                self.add_error('setor', 'Setor deve ser uma string')
            
            return not self.has_errors()
            
        except Exception as e:
            logger.error(f"Erro ao validar filtros: {str(e)}")
            self.add_error('geral', f'Erro ao validar filtros: {str(e)}')
            return False

# ========================================
# FIM DAS NOVAS FUNÇÕES PARA REGISTROS DE PONTO
# ========================================

# ========================================
# FUNÇÕES AUXILIARES - RLPONTO-WEB
# Data: 08/01/2025
# Descrição: Funções de validação e formatação
# ========================================

def validar_cpf(cpf):
    """
    Valida um CPF brasileiro.
    
    Args:
        cpf (str): CPF sem formatação
        
    Returns:
        bool: True se válido
    """
    # Remove caracteres não numéricos
    cpf = re.sub(r'[^0-9]', '', cpf)
    
    # Verifica se tem 11 dígitos
    if len(cpf) != 11:
        return False
    
    # Verifica se todos os dígitos são iguais
    if cpf == cpf[0] * 11:
        return False
    
    # Calcula primeiro dígito verificador
    soma = sum(int(cpf[i]) * (10 - i) for i in range(9))
    primeiro_digito = (soma * 10) % 11
    if primeiro_digito == 10:
        primeiro_digito = 0
    
    # Calcula segundo dígito verificador
    soma = sum(int(cpf[i]) * (11 - i) for i in range(10))
    segundo_digito = (soma * 10) % 11
    if segundo_digito == 10:
        segundo_digito = 0
    
    # Verifica se os dígitos calculados coincidem com os informados
    return cpf[-2:] == f"{primeiro_digito}{segundo_digito}"

def validar_cnpj(cnpj):
    """
    Valida um CNPJ brasileiro.
    
    Args:
        cnpj (str): CNPJ sem formatação
        
    Returns:
        bool: True se válido
    """
    # Remove caracteres não numéricos
    cnpj = re.sub(r'[^0-9]', '', cnpj)
    
    # Verifica se tem 14 dígitos
    if len(cnpj) != 14:
        return False
    
    # Verifica se todos os dígitos são iguais
    if cnpj == cnpj[0] * 14:
        return False
    
    # Calcula primeiro dígito verificador
    pesos = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2]
    soma = sum(int(cnpj[i]) * pesos[i] for i in range(12))
    primeiro_digito = 11 - (soma % 11)
    if primeiro_digito >= 10:
        primeiro_digito = 0
    
    # Calcula segundo dígito verificador
    pesos = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2]
    soma = sum(int(cnpj[i]) * pesos[i] for i in range(13))
    segundo_digito = 11 - (soma % 11)
    if segundo_digito >= 10:
        segundo_digito = 0
    
    # Verifica se os dígitos calculados coincidem com os informados
    return cnpj[-2:] == f"{primeiro_digito}{segundo_digito}"

def validar_email(email):
    """
    Valida um endereço de email.
    
    Args:
        email (str): Endereço de email
        
    Returns:
        bool: True se válido
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def formatar_cpf(cpf):
    """
    Formata um CPF com pontos e hífen.
    
    Args:
        cpf (str): CPF sem formatação
        
    Returns:
        str: CPF formatado
    """
    cpf_limpo = re.sub(r'[^0-9]', '', cpf)
    if len(cpf_limpo) == 11:
        return f"{cpf_limpo[:3]}.{cpf_limpo[3:6]}.{cpf_limpo[6:9]}-{cpf_limpo[9:]}"
    return cpf

def formatar_cnpj(cnpj):
    """
    Formata um CNPJ com pontos, barra e hífen.
    
    Args:
        cnpj (str): CNPJ sem formatação
        
    Returns:
        str: CNPJ formatado
    """
    cnpj_limpo = re.sub(r'[^0-9]', '', cnpj)
    if len(cnpj_limpo) == 14:
        return f"{cnpj_limpo[:2]}.{cnpj_limpo[2:5]}.{cnpj_limpo[5:8]}/{cnpj_limpo[8:12]}-{cnpj_limpo[12:]}"
    return cnpj

def formatar_telefone(telefone):
    """
    Formata um telefone brasileiro.
    
    Args:
        telefone (str): Telefone sem formatação
        
    Returns:
        str: Telefone formatado
    """
    telefone_limpo = re.sub(r'[^0-9]', '', telefone)
    
    if len(telefone_limpo) == 10:  # Telefone fixo
        return f"({telefone_limpo[:2]}) {telefone_limpo[2:6]}-{telefone_limpo[6:]}"
    elif len(telefone_limpo) == 11:  # Celular
        return f"({telefone_limpo[:2]}) {telefone_limpo[2:7]}-{telefone_limpo[7:]}"
    
    return telefone

def validar_cpf_formato(cpf):
    """
    Valida apenas o formato do CPF (não verifica dígitos).
    
    Args:
        cpf (str): CPF
        
    Returns:
        bool: True se o formato está correto
    """
    cpf_limpo = re.sub(r'[^0-9]', '', cpf)
    return len(cpf_limpo) == 11

def gerar_hash_arquivo(arquivo_path):
    """
    Gera hash MD5 de um arquivo.
    
    Args:
        arquivo_path (str): Caminho do arquivo
        
    Returns:
        str: Hash MD5
    """
    hash_md5 = hashlib.md5()
    try:
        with open(arquivo_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
    except FileNotFoundError:
        return None
    
    return hash_md5.hexdigest()

def calcular_idade(data_nascimento):
    """
    Calcula a idade com base na data de nascimento.
    
    Args:
        data_nascimento (date): Data de nascimento
        
    Returns:
        int: Idade em anos
    """
    hoje = datetime.now().date()
    idade = hoje.year - data_nascimento.year
    
    # Verifica se já fez aniversário este ano
    if (hoje.month, hoje.day) < (data_nascimento.month, data_nascimento.day):
        idade -= 1
    
    return idade

def formatar_data_br(data):
    """
    Formata uma data para o padrão brasileiro.
    
    Args:
        data (date/datetime): Data
        
    Returns:
        str: Data formatada (dd/mm/aaaa)
    """
    if data:
        return data.strftime('%d/%m/%Y')
    return ''

def formatar_datetime_br(datetime_obj):
    """
    Formata uma data/hora para o padrão brasileiro.
    
    Args:
        datetime_obj (datetime): Data/hora
        
    Returns:
        str: Data/hora formatada (dd/mm/aaaa hh:mm)
    """
    if datetime_obj:
        return datetime_obj.strftime('%d/%m/%Y %H:%M')
    return ''

def formatar_valor_monetario(valor):
    """
    Formata um valor para o padrão monetário brasileiro.
    
    Args:
        valor (float): Valor
        
    Returns:
        str: Valor formatado (R$ 1.234,56)
    """
    if valor is None:
        return 'R$ 0,00'
    
    return f"R$ {valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')

def limpar_string(texto):
    """
    Remove caracteres especiais de uma string.
    
    Args:
        texto (str): Texto
        
    Returns:
        str: Texto limpo
    """
    if not texto:
        return ''
    return re.sub(r'\D', '', str(texto))

def truncar_texto(texto, tamanho_max=50):
    """
    Trunca um texto se exceder o tamanho máximo.
    
    Args:
        texto (str): Texto
        tamanho_max (int): Tamanho máximo
        
    Returns:
        str: Texto truncado
    """
    if not texto:
        return ''
    
    if len(texto) <= tamanho_max:
        return texto
    
    return texto[:tamanho_max-3] + '...'

# ========================================
# FIM DAS FUNÇÕES AUXILIARES - RLPONTO-WEB
# ========================================