/* Estilos gerais */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f2f5;
    color: #1a2634;
}

.container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background-color: #1a2634;
    padding: 20px 0;
}

.tab-btn {
    display: block;
    width: 100%;
    padding: 15px 20px;
    border: none;
    background: none;
    color: #fff;
    text-align: left;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.tab-btn:hover {
    background-color: #2a3644;
}

.tab-btn.active {
    background-color: #4fbdba;
    color: #1a2634;
    font-weight: bold;
}

/* Conteúdo principal */
.main-content {
    flex: 1;
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
}

h2 {
    color: #1a2634;
    margin-bottom: 30px;
    font-size: 24px;
}

h3 {
    color: #4fbdba;
    margin-bottom: 20px;
    font-size: 18px;
}

/* Formulário */
form {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

label {
    display: block;
    margin-bottom: 8px;
    color: #1a2634;
    font-weight: 500;
}

input[type="text"],
input[type="email"],
input[type="date"],
input[type="number"],
select,
textarea {
    width: 100%;
    padding: 10px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #4fbdba;
    box-shadow: 0 0 0 2px rgba(79, 189, 186, 0.2);
}

.input-error {
    border-color: #dc3545 !important;
}

.checkbox-group {
    margin-bottom: 20px;
}

.checkbox-group label {
    display: inline-block;
    margin-right: 10px;
}

/* Botões */
.form-buttons {
    margin-top: 30px;
    display: flex;
    gap: 15px;
}

button {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
}

button[type="submit"] {
    background-color: #4fbdba;
    color: #fff;
}

button[type="submit"]:hover {
    background-color: #3da8a6;
}

.voltar-btn {
    background-color: #6c757d;
    color: #fff;
}

.voltar-btn:hover {
    background-color: #5a6268;
}

/* Mensagens de erro */
.errors {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    color: #721c24;
}

.errors h3 {
    color: #721c24;
    margin-bottom: 10px;
}

.errors ul {
    margin: 0;
    padding-left: 20px;
}

/* Responsividade */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        padding: 10px;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .form-buttons {
        flex-direction: column;
    }
    
    button {
        width: 100%;
    }
}

/* =================================
   MODAL DE BIOMETRIA - ESTILO PROFISSIONAL
   ================================= */

/* Modal Container */
.modal-biometria {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-container {
    position: relative;
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Modal Header */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 30px;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 16px 16px 0 0;
}

.modal-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Modal Content */
.modal-content {
    padding: 30px;
}

/* Status Bar - Progresso */
.biometria-status {
    margin-bottom: 30px;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding: 0 20px;
}

.status-bar::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 60px;
    right: 60px;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.status-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.status-step.active .step-number {
    background: #007bff;
    color: white;
    box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.2);
}

.status-step.completed .step-number {
    background: #28a745;
    color: white;
}

.status-step.active .step-label {
    color: #007bff;
    font-weight: 600;
}

/* Área de Display Biométrico */
.biometria-display {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* Scanner Visual */
.biometria-scanner {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.scanner-frame {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 3px solid #007bff;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

/* Preview da Biometria Capturada */
.biometria-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.preview-frame {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 3px solid #28a745;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 200px;
}

.preview-area {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 8px;
    background: #ffffff;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    overflow: hidden;
}

.preview-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 10px;
}

.qualidade-display, .dedo-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.8);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
}

.qualidade-label, .dedo-label {
    font-weight: 600;
    color: #6c757d;
}

.qualidade-valor, .dedo-valor {
    font-weight: bold;
    color: #28a745;
}

.preview-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
    margin-top: 5px;
}

/* Instruções */
.biometria-instructions {
    background: white;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.instruction-card {
    padding: 25px;
}

.instruction-card h4 {
    margin: 0 0 15px 0;
    color: #343a40;
    font-size: 16px;
    font-weight: 600;
}

.instruction-card ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.instruction-card li {
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

.instruction-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 8px;
    color: #28a745;
    font-weight: bold;
}

/* Resultado das Capturas */
.capturas-resultado {
    margin-bottom: 20px;
}

.resultado-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.resultado-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.resultado-card.success {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.resultado-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.resultado-header h5 {
    margin: 0;
    color: #343a40;
    font-size: 16px;
    font-weight: 600;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.pending {
    background: #ffc107;
    color: #856404;
}

.status-badge.success {
    background: #28a745;
    color: white;
}

.status-badge.error {
    background: #dc3545;
    color: white;
}

/* Medidor de Qualidade */
.qualidade-meter {
    display: flex;
    align-items: center;
    gap: 12px;
}

.qualidade-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.qualidade-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    width: 0%;
    transition: width 0.6s ease;
    border-radius: 4px;
}

.qualidade-valor {
    font-size: 14px;
    font-weight: 600;
    color: #343a40;
    min-width: 35px;
    text-align: right;
}

/* Status Message */
.status-message {
    text-align: center;
    margin-bottom: 20px;
}

.status-text {
    font-size: 16px;
    padding: 15px 20px;
    border-radius: 10px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #6c757d;
    transition: all 0.3s ease;
}

.status-text.info {
    background: rgba(0, 123, 255, 0.1);
    border-color: #007bff;
    color: #0056b3;
}

.status-text.success {
    background: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
    color: #155724;
}

.status-text.error {
    background: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
    color: #721c24;
}

/* Modal Actions */
.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 0 30px 30px 30px;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;
    padding-top: 20px;
}

/* Responsividade */
@media (max-width: 768px) {
    .modal-container {
        width: 95%;
        margin: 20px auto;
    }
    
    .biometria-display {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .resultado-grid {
        grid-template-columns: 1fr;
    }
    
    .status-bar {
        padding: 0 10px;
    }
    
    .step-number {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
    
    .scanner-area {
        width: 120px;
        height: 120px;
    }
    
    .fingerprint-icon svg {
        width: 90px;
        height: 90px;
    }
}

/* Estados do Scanner */
.scanner-area.connecting {
    border-color: #ffc107;
    background: radial-gradient(circle, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 70%);
}

.scanner-area.capturing {
    border-color: #007bff;
    background: radial-gradient(circle, rgba(0, 123, 255, 0.2) 0%, rgba(0, 123, 255, 0.1) 70%);
}

.scanner-area.success {
    border-color: #28a745;
    background: radial-gradient(circle, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 70%);
}

.scanner-area.error {
    border-color: #dc3545;
    background: radial-gradient(circle, rgba(220, 53, 69, 0.2) 0%, rgba(220, 53, 69, 0.1) 70%);
}

/* Animação de Loading */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* =================================
   ESTILOS PARA MODO SIMULAÇÃO
   ================================= */

/* Status text para simulação */
.status-text.simulation {
    color: #17a2b8 !important;
    font-weight: bold;
    background: rgba(23, 162, 184, 0.1);
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #17a2b8;
}

/* Scanner area para simulação */
.scanner-area.simulation {
    border-color: #17a2b8 !important;
    background-color: rgba(23, 162, 184, 0.1) !important;
    animation: simulationPulse 2s infinite;
}

@keyframes simulationPulse {
    0%, 100% { 
        box-shadow: 0 0 0 0 rgba(23, 162, 184, 0.4);
    }
    50% { 
        box-shadow: 0 0 0 10px rgba(23, 162, 184, 0.1);
    }
}

/* Status badge para simulação */
.status-badge.success-simulation {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    position: relative;
}

.status-badge.success-simulation::after {
    content: " 🌐";
    font-size: 12px;
}

/* Resultado card para simulação */
.resultado-card.simulation {
    border: 2px solid #17a2b8;
    background: rgba(23, 162, 184, 0.05);
}

.resultado-card.simulation .qualidade-fill {
    background: linear-gradient(90deg, #17a2b8, #20c997);
}

/* Alerta visual para simulação (informativo) */
.alerta-simulacao.alert-info {
    border-left: 5px solid #17a2b8;
    animation: slideInFromTop 0.5s ease-out;
}

@keyframes slideInFromTop {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ícone de simulação no scanner */
.scanner-area.simulation .fingerprint-icon svg {
    animation: simulationRotate 3s linear infinite;
}

@keyframes simulationRotate {
    0% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(90deg) scale(1.1); }
    50% { transform: rotate(180deg) scale(1); }
    75% { transform: rotate(270deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
}

/* Badge especial de simulação */
.badge-simulation {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    margin-left: 8px;
}

/* Highlight para elementos de simulação */
.simulation-highlight {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(32, 201, 151, 0.1));
    border: 1px dashed #17a2b8;
    border-radius: 6px;
    padding: 10px;
    margin: 10px 0;
}

.simulation-highlight::before {
    content: "🌐 SIMULAÇÃO ";
    font-weight: bold;
    color: #17a2b8;
    font-size: 12px;
} 