-- ========================================
-- ATUALIZAÇÃO DOS PERÍODOS FIXOS
-- Data: 11/07/2025
-- Descrição: Atualizar tabela dia_dados com os novos horários especificados
-- ========================================

USE controle_ponto;

-- ========================================
-- 1. LIMPAR DADOS EXISTENTES
-- ========================================

DELETE FROM dia_dados;

-- ========================================
-- 2. INSERIR NOVOS PERÍODOS CONFORME ESPECIFICAÇÃO
-- ========================================

-- TURNO DIURNO
INSERT INTO dia_dados (turno, horario_inicio, horario_fim, descricao, ordem_prioridade, ativo) VALUES
('Manha', '06:00:00', '11:00:00', 'Período da manhã - 06:00 às 11:00', 1, TRUE),
('Intervalo', '11:00:00', '14:00:00', 'Período de intervalo - 11:00 às 14:00', 2, TRUE),
('Tarde', '14:00:00', '18:00:00', 'Período da tarde - 14:00 às 18:00', 3, TRUE),
('Fim_Diurno', '18:00:00', '21:00:00', 'Fim da jornada diurna - 18:00 às 21:00', 4, TRUE);

-- TURNO NOTURNO  
INSERT INTO dia_dados (turno, horario_inicio, horario_fim, descricao, ordem_prioridade, ativo) VALUES
('Noite_Inicio', '21:00:00', '00:00:00', 'Início período noturno - 21:00 às 00:00', 5, TRUE),
('Noite_Intervalo', '00:00:00', '02:00:00', 'Intervalo noturno - 00:00 às 02:00', 6, TRUE),
('Noite_Fim', '02:00:00', '05:59:00', 'Fim período noturno - 02:00 às 05:59', 7, TRUE);

-- ========================================
-- 3. VERIFICAR DADOS INSERIDOS
-- ========================================

SELECT 
    id,
    turno,
    TIME_FORMAT(horario_inicio, '%H:%i') as inicio,
    TIME_FORMAT(horario_fim, '%H:%i') as fim,
    descricao,
    ativo,
    ordem_prioridade
FROM dia_dados 
ORDER BY ordem_prioridade;

-- ========================================
-- 4. ATUALIZAR ENUM DA TABELA (se necessário)
-- ========================================

-- Verificar se precisa alterar o ENUM para incluir novos tipos
-- ALTER TABLE dia_dados MODIFY COLUMN turno ENUM('Manha', 'Intervalo', 'Tarde', 'Fim_Diurno', 'Noite_Inicio', 'Noite_Intervalo', 'Noite_Fim') NOT NULL;

-- ========================================
-- 5. ATUALIZAR FUNÇÃO MYSQL
-- ========================================

DELIMITER //

DROP FUNCTION IF EXISTS determinar_periodo_por_horario//

CREATE FUNCTION determinar_periodo_por_horario(hora_batida TIME)
RETURNS VARCHAR(20)
READS SQL DATA
DETERMINISTIC
COMMENT 'Determina o período baseado no horário da batida usando tabela dia_dados'
BEGIN
    DECLARE periodo_resultado VARCHAR(20) DEFAULT 'Manha';
    
    -- TURNO DIURNO
    -- 06:00 - 11:00: Período da manhã
    IF hora_batida >= '06:00:00' AND hora_batida < '11:00:00' THEN
        SET periodo_resultado = 'Manha';
    
    -- 11:00 - 14:00: Período de intervalo
    ELSEIF hora_batida >= '11:00:00' AND hora_batida < '14:00:00' THEN
        SET periodo_resultado = 'Intervalo';
    
    -- 14:00 - 18:00: Período da tarde
    ELSEIF hora_batida >= '14:00:00' AND hora_batida < '18:00:00' THEN
        SET periodo_resultado = 'Tarde';
        
    -- 18:00 - 21:00: Fim da jornada diurna
    ELSEIF hora_batida >= '18:00:00' AND hora_batida < '21:00:00' THEN
        SET periodo_resultado = 'Fim_Diurno';
    
    -- TURNO NOTURNO
    -- 21:00 - 00:00: Início noturno
    ELSEIF hora_batida >= '21:00:00' AND hora_batida <= '23:59:59' THEN
        SET periodo_resultado = 'Noite_Inicio';
    
    -- 00:00 - 02:00: Intervalo noturno
    ELSEIF hora_batida >= '00:00:00' AND hora_batida < '02:00:00' THEN
        SET periodo_resultado = 'Noite_Intervalo';
    
    -- 02:00 - 05:59: Fim noturno
    ELSEIF hora_batida >= '02:00:00' AND hora_batida < '06:00:00' THEN
        SET periodo_resultado = 'Noite_Fim';
    
    END IF;
    
    RETURN periodo_resultado;
END//

DELIMITER ;

-- ========================================
-- 6. TESTAR FUNÇÃO
-- ========================================

SELECT 
    '08:00:00' as horario,
    determinar_periodo_por_horario('08:00:00') as periodo,
    'Deve ser: Manha' as esperado
UNION ALL
SELECT 
    '12:00:00' as horario,
    determinar_periodo_por_horario('12:00:00') as periodo,
    'Deve ser: Intervalo' as esperado
UNION ALL
SELECT 
    '15:00:00' as horario,
    determinar_periodo_por_horario('15:00:00') as periodo,
    'Deve ser: Tarde' as esperado
UNION ALL
SELECT 
    '19:00:00' as horario,
    determinar_periodo_por_horario('19:00:00') as periodo,
    'Deve ser: Fim_Diurno' as esperado
UNION ALL
SELECT 
    '22:00:00' as horario,
    determinar_periodo_por_horario('22:00:00') as periodo,
    'Deve ser: Noite_Inicio' as esperado
UNION ALL
SELECT 
    '01:00:00' as horario,
    determinar_periodo_por_horario('01:00:00') as periodo,
    'Deve ser: Noite_Intervalo' as esperado
UNION ALL
SELECT 
    '03:00:00' as horario,
    determinar_periodo_por_horario('03:00:00') as periodo,
    'Deve ser: Noite_Fim' as esperado;

-- ========================================
-- 7. CRIAR VIEW ATUALIZADA
-- ========================================

CREATE OR REPLACE VIEW v_periodos_ativos AS
SELECT 
    id,
    turno,
    horario_inicio,
    horario_fim,
    descricao,
    CASE 
        WHEN turno LIKE 'Noite_%' THEN 
            CONCAT(TIME_FORMAT(horario_inicio, '%H:%i'), ' - ', TIME_FORMAT(horario_fim, '%H:%i'), ' (noturno)')
        ELSE 
            CONCAT(TIME_FORMAT(horario_inicio, '%H:%i'), ' - ', TIME_FORMAT(horario_fim, '%H:%i'))
    END as faixa_horaria,
    ordem_prioridade,
    CASE 
        WHEN turno = 'Manha' THEN 'entrada_manha'
        WHEN turno = 'Intervalo' THEN 'saida_almoco'
        WHEN turno = 'Tarde' THEN 'entrada_tarde'
        WHEN turno = 'Fim_Diurno' THEN 'saida'
        WHEN turno = 'Noite_Inicio' THEN 'entrada_manha'
        WHEN turno = 'Noite_Intervalo' THEN 'saida_almoco'
        WHEN turno = 'Noite_Fim' THEN 'entrada_tarde'
        ELSE 'entrada_manha'
    END as tipo_registro_sugerido
FROM dia_dados 
WHERE ativo = TRUE
ORDER BY ordem_prioridade;

-- Testar view
SELECT * FROM v_periodos_ativos;

-- ========================================
-- FIM DO SCRIPT
-- ========================================
