<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastrar Funcionário - Controle de Ponto</title>
    <link rel="stylesheet" href="/static/style-cadastrar.css">
    <script src="/static/busca_cep.js" defer></script>
    <style>
        /* Estilos para o modal de biometria */
        .biometria-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            overflow: auto;
        }

        .biometria-content {
            background-color: #f8f8f8;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            position: relative;
        }

        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-btn:hover {
            color: #555;
        }

        .finger-selection {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            gap: 20px;
        }

        .finger-btn {
            padding: 10px 20px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .finger-btn.active {
            background-color: #4CAF50;
            color: white;
            border-color: #45a049;
        }

        .biometria-display {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .digital-preview {
            text-align: center;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background-color: white;
            width: 200px;
        }

        .digital-preview img {
            max-width: 150px;
            max-height: 150px;
        }

        .biometria-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .control-btn {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .control-btn:hover {
            background-color: #45a049;
        }

        .control-btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .status-message {
            text-align: center;
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }

        .status-message.success {
            background-color: #dff0d8;
            color: #3c763d;
            border: 1px solid #d6e9c6;
        }

        .status-message.error {
            background-color: #f2dede;
            color: #a94442;
            border: 1px solid #ebccd1;
        }

        .status-message.info {
            background-color: #d9edf7;
            color: #31708f;
            border: 1px solid #bce8f1;
        }

        .connection-status {
            text-align: center;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .connection-status.connected {
            color: #3c763d;
        }

        .connection-status.disconnected {
            color: #a94442;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <button class="tab-btn active" onclick="openTab('identificacao')">Identificação Pessoal</button>
            <button class="tab-btn" onclick="openTab('documentos')">Documentos Trabalhistas</button>
            <button class="tab-btn" onclick="openTab('contato')">Informações de Contato</button>
            <button class="tab-btn" onclick="openTab('profissional')">Informações Profissionais</button>
            <button class="tab-btn" onclick="openTab('acesso')">Controle de Acesso</button>
            <button class="tab-btn" onclick="openTab('epis')">Equipamentos/EPIs</button>
        </div>
        <div class="main-content">
            <h2>Cadastrar Funcionário</h2>
            <form method="POST" action="/cadastrar" id="cadastroForm">
                <!-- Identificação Pessoal (Sem Alterações) -->
                <div class="tab-content active" id="identificacao">
                    <h3>Identificação Pessoal</h3>
                    <label>Nome Completo *</label>
                    <input type="text" name="nome_completo" value="{{ data.nome_completo|default('') }}" 
                           pattern="[A-Za-zÀ-ÿ\s]+" inputmode="text" required 
                           oninput="toUpperCase(this); validateName(this);">

                    <label>CPF *</label>
                    <input type="text" name="cpf" value="{{ data.cpf|default('') }}" 
                           maxlength="14" inputmode="numeric" required 
                           oninput="applyCpfMask(this); validateCpf(this);">

                    <label>RG / CNH *</label>
                    <input type="text" name="rg" value="{{ data.rg|default('') }}" 
                           pattern="\d{1,12}" inputmode="numeric" required 
                           oninput="validateNumber(this);">

                    <label>Data de Nascimento *</label>
                    <input type="date" name="data_nascimento" value="{{ data.data_nascimento|default('') }}" 
                           required oninput="validateDate(this);">

                    <label>Sexo *</label>
                    <select name="sexo" required onchange="autoFillNationality(this);">
                        <option value="" disabled selected>Selecione</option>
                        <option value="M" {{ 'selected' if data.sexo == 'M' }}>Masculino</option>
                        <option value="F" {{ 'selected' if data.sexo == 'F' }}>Feminino</option>
                        <option value="Outro" {{ 'selected' if data.sexo == 'Outro' }}>Outro</option>
                    </select>

                    <label>Estado Civil *</label>
                    <select name="estado_civil" required>
                        <option value="" disabled selected>Selecione</option>
                        <option value="Solteiro" {{ 'selected' if data.estado_civil == 'Solteiro' }}>Solteiro</option>
                        <option value="Casado" {{ 'selected' if data.estado_civil == 'Casado' }}>Casado</option>
                        <option value="Divorciado" {{ 'selected' if data.estado_civil == 'Divorciado' }}>Divorciado</option>
                        <option value="Viúvo" {{ 'selected' if data.estado_civil == 'Viúvo' }}>Viúvo</option>
                    </select>

                    <label>Nacionalidade *</label>
                    <input type="text" name="nacionalidade" id="nacionalidade" value="{{ data.nacionalidade|default('') }}" 
                           pattern="[A-Za-zÀ-ÿ\s]+" inputmode="text" required 
                           oninput="toUpperCase(this); validateName(this);">
                </div>
                <!-- Documentos Trabalhistas (Sem Alterações) -->
                <div class="tab-content" id="documentos">
                    <h3>Documentos Trabalhistas</h3>
                    <label>CTPS - Número *</label>
                    <input type="text" name="ctps_numero" value="" 
                           pattern="\d{7}" inputmode="numeric" required 
                           oninput="validateNumberCTPS(this);"
                           placeholder="Exemplo: 1234567">

                    <label>CTPS - Série/UF *</label>
                    <input type="text" name="ctps_serie_uf" value="" 
                           pattern="\d{4}/[A-Z]{2}" required 
                           oninput="formatCtpsSerieUf(this); toUpperCase(this); validateCtpsSerieUf(this);"
                           placeholder="Exemplo: 0001/SP">

                    <label>PIS/PASEP *</label>
                    <input type="text" name="pis_pasep" value="" 
                           maxlength="14" inputmode="numeric" required 
                           oninput="applyPisMask(this); validatePis(this);"
                           placeholder="Exemplo: 123.45678.90-1">
                </div>
                <!-- Informações de Contato (Sem Alterações) -->
                <div class="tab-content" id="contato">
                    <h3>Informações de Contato</h3>
                    <label>CEP *</label>
                    <input type="text" name="endereco_cep" id="cep" value="{{ data.endereco_cep|default('') }}" 
                           maxlength="9" required oninput="applyCepMask(this);" onblur="buscarCep(this);">

                    <label>Rua</label>
                    <input type="text" name="endereco_rua" id="rua" value="{{ data.endereco_rua|default('') }}" 
                           oninput="toUpperCase(this);">

                    <label>Bairro</label>
                    <input type="text" name="endereco_bairro" id="bairro" value="{{ data.endereco_bairro|default('') }}" 
                           oninput="toUpperCase(this);">

                    <label>Cidade</label>
                    <input type="text" name="endereco_cidade" id="cidade" value="{{ data.endereco_cidade|default('') }}" 
                           oninput="toUpperCase(this);">

                    <label>Estado *</label>
                    <select name="endereco_estado" required>
                        <option value="" disabled selected>Selecione</option>
                        {% for estado in ['AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'] %}
                        <option value="{{ estado }}" {{ 'selected' if data.endereco_estado == estado }}>{{ estado }}</option>
                        {% endfor %}
                    </select>

                    <label>Telefone 1 *</label>
                    <input type="text" name="telefone1" value="{{ data.telefone1|default('') }}" 
                           maxlength="15" required oninput="applyPhoneMask(this); toUpperCase(this);">

                    <label>Telefone 2</label>
                    <input type="text" name="telefone2" value="{{ data.telefone2|default('') }}" 
                           maxlength="15" oninput="applyPhoneMask(this); toUpperCase(this);">

                    <label>E-mail</label>
                    <input type="email" name="email" value="{{ data.email|default('') }}" 
                           oninput="toLowerCase(this);">
                </div>
                <!-- Informações Profissionais (Ajustes de Maiúsculas) -->
                <div class="tab-content" id="profissional">
                    <h3>Informações Profissionais</h3>
                    <label>Cargo *</label>
                    <input type="text" name="cargo" value="{{ data.cargo|default('') }}" required 
                           oninput="toUpperCase(this);">

                    <label>Setor/Obra *</label>
                    <input type="text" name="setor_obra" value="{{ data.setor_obra|default('') }}" required 
                           oninput="toUpperCase(this);">

                    <label>Matrícula Empresa *</label>
                    <input type="text" name="matricula_empresa" value="{{ data.matricula_empresa|default('') }}" required 
                           oninput="toUpperCase(this);">

                    <label>Data de Admissão *</label>
                    <input type="date" name="data_admissao" value="{{ data.data_admissao|default('') }}" required>

                    <label>Tipo de Contrato *</label>
                    <select name="tipo_contrato" required>
                        <option value="" disabled selected>Selecione</option>
                        <option value="CLT" {{ 'selected' if data.tipo_contrato == 'CLT' }}>CLT</option>
                        <option value="PJ" {{ 'selected' if data.tipo_contrato == 'PJ' }}>PJ</option>
                        <option value="Estágio" {{ 'selected' if data.tipo_contrato == 'Estágio' }}>Estágio</option>
                        <option value="Temporário" {{ 'selected' if data.tipo_contrato == 'Temporário' }}>Temporário</option>
                    </select>
                </div>
                <!-- Controle de Acesso (Ajustes de Maiúsculas) -->
                <div class="tab-content" id="acesso">
                    <h3>Controle de Acesso</h3>
                    <label>Jornada - Segunda a Quinta - Entrada *</label>
                    <input type="time" name="jornada_seg_qui_entrada" value="{{ data.jornada_seg_qui_entrada|default('07:00') }}" required>

                    <label>Jornada - Segunda a Quinta - Saída *</label>
                    <input type="time" name="jornada_seg_qui_saida" value="{{ data.jornada_seg_qui_saida|default('17:00') }}" required>

                    <label>Jornada - Sexta - Entrada *</label>
                    <input type="time" name="jornada_sex_entrada" value="{{ data.jornada_sex_entrada|default('07:00') }}" required>

                    <label>Jornada - Sexta - Saída *</label>
                    <input type="time" name="jornada_sex_saida" value="{{ data.jornada_sex_saida|default('16:00') }}" required>

                    <label>Intervalo - Início *</label>
                    <input type="time" name="jornada_intervalo_entrada" value="{{ data.jornada_intervalo_entrada|default('12:00') }}" required>

                    <label>Intervalo - Fim *</label>
                    <input type="time" name="jornada_intervalo_saida" value="{{ data.jornada_intervalo_saida|default('13:00') }}" required>

                    <label>Nível de Acesso *</label>
                    <select name="nivel_acesso" required>
                        <option value="" disabled selected>Selecione</option>
                        <option value="Funcionário" {{ 'selected' if data.nivel_acesso == 'Funcionário' }}>Funcionário</option>
                        <option value="Supervisão" {{ 'selected' if data.nivel_acesso == 'Supervisão' }}>Supervisão</option>
                        <option value="Gerência" {{ 'selected' if data.nivel_acesso == 'Gerência' }}>Gerência</option>
                    </select>

                    <label>Turno *</label>
                    <select name="turno" required>
                        <option value="" disabled selected>Selecione</option>
                        <option value="Diurno" {{ 'selected' if data.turno == 'Diurno' }}>Diurno</option>
                        <option value="Noturno" {{ 'selected' if data.turno == 'Noturno' }}>Noturno</option>
                        <option value="Misto" {{ 'selected' if data.turno == 'Misto' }}>Misto</option>
                    </select>

                    <label>Tolerância de Ponto (minutos) *</label>
                    <input type="number" name="tolerancia_ponto" value="{{ data.tolerancia_ponto|default('5') }}" min="0" required>

                    <div class="checkbox-group">
                        <label>Banco de Horas</label>
                        <input type="checkbox" name="banco_horas" {{ 'checked' if data.banco_horas }}>
                    </div>
                    <div class="checkbox-group">
                        <label>Hora Extra</label>
                        <input type="checkbox" name="hora_extra" {{ 'checked' if data.hora_extra }}>
                    </div>

                    <label>Status do Cadastro *</label>
                    <select name="status_cadastro" required>
                        <option value="" disabled selected>Selecione</option>
                        <option value="Ativo" {{ 'selected' if data.status_cadastro == 'Ativo' }}>Ativo</option>
                        <option value="Inativo" {{ 'selected' if data.status_cadastro == 'Inativo' }}>Inativo</option>
                    </select>
                </div>
                <!-- Equipamentos/EPIs (Ajustes de Maiúsculas) -->
                <div class="tab-content" id="epis">
                    <h3>Equipamentos/EPIs (Opcional)</h3>
                    <label>EPI/Equipamento</label>
                    <input type="text" name="epi_nome" value="{{ data.epi_nome|default('') }}" 
                           oninput="toUpperCase(this);">

                    <label>CA</label>
                    <input type="text" name="epi_ca" value="{{ data.epi_ca|default('') }}" 
                           oninput="toUpperCase(this);">

                    <label>Data de Entrega</label>
                    <input type="date" name="epi_data_entrega" value="{{ data.epi_data_entrega|default('') }}">

                    <label>Data de Validade</label>
                    <input type="date" name="epi_data_validade" value="{{ data.epi_data_validade|default('') }}">

                    <label>Observações</label>
                    <textarea name="epi_observacoes" oninput="toUpperCase(this);">{{ data.epi_observacoes|default('') }}</textarea>
                    
                    <div style="margin-top: 20px;">
                        <button type="button" id="capturarBiometriaBtn" class="control-btn" onclick="abrirModalBiometria()">Capturar Biometria</button>
                    </div>
                    
                    <!-- Campos ocultos para armazenar os templates biométricos -->
                    <input type="hidden" name="digital_dedo1" id="digital_dedo1">
                    <input type="hidden" name="digital_dedo2" id="digital_dedo2">
                </div>
                <!-- Botões -->
                <div class="form-buttons">
                    <button type="submit">Cadastrar</button>
                    <button type="button" class="voltar-btn" onclick="window.location.href='/'">Voltar</button>
                </div>
            </form>
            {% if errors %}
            <div class="errors">
                <h3>Erros no formulário:</h3>
                <ul>
                    {% for error in errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Modal de Biometria -->
    <div id="biometriaModal" class="biometria-modal">
        <div class="biometria-content">
            <span class="close-btn" onclick="fecharModalBiometria()">&times;</span>
            <h2>Captura de Biometria</h2>
            
            <div class="connection-status" id="connectionStatus">
                Verificando conexão com o leitor...
            </div>
            
            <div id="statusMessage" class="status-message info">
                Selecione um dedo para iniciar a captura
            </div>
            
            <div class="finger-selection">
                <button id="dedo1Btn" class="finger-btn" onclick="selecionarDedo(1)">Dedo 1</button>
                <button id="dedo2Btn" class="finger-btn" onclick="selecionarDedo(2)">Dedo 2</button>
            </div>
            
            <div class="biometria-display">
                <div class="digital-preview">
                    <img id="digitalImg" src="/static/images/fingerprint-placeholder.png" alt="Impressão Digital">
                    <p>Qualidade: <span id="qualidade">0%</span></p>
                </div>
            </div>
            
            <div class="biometria-controls">
                <button id="limparBtn" class="control-btn" onclick="limparDigital()">Limpar</button>
                <button id="capturarBtn" class="control-btn" onclick="capturarDigital()">Capturar</button>
                <button id="salvarBtn" class="control-btn" onclick="salvarBiometria()" disabled>Salvar Biometria</button>
                <button id="fecharBtn" class="control-btn" onclick="fecharModalBiometria()">Fechar</button>
            </div>
        </div>
    </div>

    <script>
        // Funções de navegação entre abas
        function openTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            document.querySelector(`button[onclick="openTab('${tabName}')"]`).classList.add('active');
        }

        // Converter para maiúsculo
        function toUpperCase(input) {
            input.value = input.value.toUpperCase();
        }

        // Converter para minúsculo (para e-mail)
        function toLowerCase(input) {
            input.value = input.value.toLowerCase();
        }

        // Validar nome e nacionalidade (apenas letras, espaços, acentos)
        function validateName(input) {
            const regex = /^[A-Za-zÀ-ÿ\s]+$/;
            if (!regex.test(input.value)) {
                input.classList.add('input-error');
                input.setCustomValidity('Apenas letras, espaços e acentos são permitidos.');
            } else {
                input.classList.remove('input-error');
                input.setCustomValidity('');
            }
        }

        // Validar campos numéricos (geral, usado para RG / CNH)
        function validateNumber(input) {
            const regex = /^\d{1,12}$/;
            if (!regex.test(input.value)) {
                input.classList.add('input-error');
                input.setCustomValidity('Apenas números são permitidos (máximo 12 dígitos).');
            } else {
                input.classList.remove('input-error');
                input.setCustomValidity('');
            }
        }

        // Validar CTPS - Número (7 dígitos)
        function validateNumberCTPS(input) {
            const regex = /^\d{7}$/;
            if (!regex.test(input.value)) {
                input.classList.add('input-error');
                input.setCustomValidity('CTPS deve ter exatamente 7 dígitos numéricos.');
            } else {
                input.classList.remove('input-error');
                input.setCustomValidity('');
            }
        }

        // Formatar e validar CTPS - Série/UF (ex.: 0050/AM)
        function formatCtpsSerieUf(input) {
            let value = input.value.replace(/[^\d\/A-Z]/g, '');
            
            // Adiciona a barra após 4 dígitos se não existir
            if (value.length >= 4 && !value.includes('/')) {
                value = value.substring(0, 4) + '/' + value.substring(4);
            }
            
            input.value = value;
        }

        function validateCtpsSerieUf(input) {
            const regex = /^\d{4}\/[A-Z]{2}$/;
            if (!regex.test(input.value)) {
                input.classList.add('input-error');
                input.setCustomValidity('Formato deve ser: 0000/UF (ex.: 0050/AM)');
            } else {
                input.classList.remove('input-error');
                input.setCustomValidity('');
            }
        }

        // Aplicar máscara de CPF (000.000.000-00)
        function applyCpfMask(input) {
            let value = input.value.replace(/\D/g, '');
            
            if (value.length > 11) {
                value = value.substring(0, 11);
            }
            
            if (value.length > 9) {
                value = value.replace(/(\d{3})(\d{3})(\d{3})(\d{1,2})/, '$1.$2.$3-$4');
            } else if (value.length > 6) {
                value = value.replace(/(\d{3})(\d{3})(\d{1,3})/, '$1.$2.$3');
            } else if (value.length > 3) {
                value = value.replace(/(\d{3})(\d{1,3})/, '$1.$2');
            }
            
            input.value = value;
        }

        // Validar CPF
        function validateCpf(input) {
            const cpf = input.value.replace(/\D/g, '');
            
            if (cpf.length !== 11) {
                input.classList.add('input-error');
                input.setCustomValidity('CPF deve ter 11 dígitos.');
                return;
            }
            
            // Verificar se todos os dígitos são iguais
            if (/^(\d)\1+$/.test(cpf)) {
                input.classList.add('input-error');
                input.setCustomValidity('CPF inválido.');
                return;
            }
            
            // Validação do dígito verificador
            let soma = 0;
            let resto;
            
            for (let i = 1; i <= 9; i++) {
                soma += parseInt(cpf.substring(i - 1, i)) * (11 - i);
            }
            
            resto = (soma * 10) % 11;
            if (resto === 10 || resto === 11) resto = 0;
            if (resto !== parseInt(cpf.substring(9, 10))) {
                input.classList.add('input-error');
                input.setCustomValidity('CPF inválido.');
                return;
            }
            
            soma = 0;
            for (let i = 1; i <= 10; i++) {
                soma += parseInt(cpf.substring(i - 1, i)) * (12 - i);
            }
            
            resto = (soma * 10) % 11;
            if (resto === 10 || resto === 11) resto = 0;
            if (resto !== parseInt(cpf.substring(10, 11))) {
                input.classList.add('input-error');
                input.setCustomValidity('CPF inválido.');
                return;
            }
            
            input.classList.remove('input-error');
            input.setCustomValidity('');
        }

        // Aplicar máscara de PIS/PASEP (000.00000.00-0)
        function applyPisMask(input) {
            let value = input.value.replace(/\D/g, '');
            
            if (value.length > 11) {
                value = value.substring(0, 11);
            }
            
            if (value.length > 10) {
                value = value.replace(/(\d{3})(\d{5})(\d{2})(\d{1})/, '$1.$2.$3-$4');
            } else if (value.length > 8) {
                value = value.replace(/(\d{3})(\d{5})(\d{1,2})/, '$1.$2.$3');
            } else if (value.length > 3) {
                value = value.replace(/(\d{3})(\d{1,5})/, '$1.$2');
            }
            
            input.value = value;
        }

        // Validar PIS/PASEP
        function validatePis(input) {
            const pis = input.value.replace(/\D/g, '');
            
            if (pis.length !== 11) {
                input.classList.add('input-error');
                input.setCustomValidity('PIS/PASEP deve ter 11 dígitos.');
                return;
            }
            
            // Verificar se todos os dígitos são iguais
            if (/^(\d)\1+$/.test(pis)) {
                input.classList.add('input-error');
                input.setCustomValidity('PIS/PASEP inválido.');
                return;
            }
            
            // Validação do dígito verificador
            let multiplicador = [3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
            let soma = 0;
            
            for (let i = 0; i < 10; i++) {
                soma += parseInt(pis.charAt(i)) * multiplicador[i];
            }
            
            let resto = soma % 11;
            let dv = 11 - resto;
            if (dv === 10 || dv === 11) dv = 0;
            
            if (dv !== parseInt(pis.charAt(10))) {
                input.classList.add('input-error');
                input.setCustomValidity('PIS/PASEP inválido.');
                return;
            }
            
            input.classList.remove('input-error');
            input.setCustomValidity('');
        }

        // Aplicar máscara de CEP (00000-000)
        function applyCepMask(input) {
            let value = input.value.replace(/\D/g, '');
            
            if (value.length > 8) {
                value = value.substring(0, 8);
            }
            
            if (value.length > 5) {
                value = value.replace(/(\d{5})(\d{1,3})/, '$1-$2');
            }
            
            input.value = value;
        }

        // Aplicar máscara de telefone ((00) 00000-0000)
        function applyPhoneMask(input) {
            let value = input.value.replace(/\D/g, '');
            
            if (value.length > 11) {
                value = value.substring(0, 11);
            }
            
            if (value.length > 10) {
                value = value.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
            } else if (value.length > 6) {
                value = value.replace(/(\d{2})(\d{4})(\d{0,4})/, '($1) $2-$3');
            } else if (value.length > 2) {
                value = value.replace(/(\d{2})(\d{0,5})/, '($1) $2');
            }
            
            input.value = value;
        }

        // Validar data
        function validateDate(input) {
            const date = new Date(input.value);
            const today = new Date();
            
            if (date > today) {
                input.classList.add('input-error');
                input.setCustomValidity('Data não pode ser futura.');
            } else {
                input.classList.remove('input-error');
                input.setCustomValidity('');
            }
        }

        // Auto-preencher nacionalidade com base no sexo
        function autoFillNationality(select) {
            const nacionalidadeInput = document.getElementById('nacionalidade');
            if (!nacionalidadeInput.value) {
                if (select.value === 'M') {
                    nacionalidadeInput.value = 'BRASILEIRO';
                } else if (select.value === 'F') {
                    nacionalidadeInput.value = 'BRASILEIRA';
                }
            }
        }

        // Código para o modal de biometria
        let biometriaService = null;
        let dedoSelecionado = null;
        let digitaisCapturadas = {
            dedo1: null,
            dedo2: null
        };

        // Abrir o modal de biometria
        function abrirModalBiometria() {
            document.getElementById('biometriaModal').style.display = 'block';
            inicializarBiometria();
        }

        // Fechar o modal de biometria
        function fecharModalBiometria() {
            document.getElementById('biometriaModal').style.display = 'none';
            if (biometriaService && biometriaService.isConnected) {
                biometriaService.disconnect();
            }
        }

        // Inicializar o serviço de biometria
        function inicializarBiometria() {
            atualizarStatusConexao('Conectando ao serviço de biometria...', false);
            
            if (!biometriaService) {
                biometriaService = new BiometriaService();
                
                // Configurar callbacks
                biometriaService.on('onConnect', function() {
                    atualizarStatusConexao('Leitor conectado', true);
                    atualizarStatusMensagem('Selecione um dedo para iniciar a captura', 'info');
                    document.getElementById('capturarBtn').disabled = !dedoSelecionado;
                });
                
                biometriaService.on('onDisconnect', function() {
                    atualizarStatusConexao('Leitor desconectado', false);
                    atualizarStatusMensagem('Não foi possível conectar ao leitor biométrico', 'error');
                    document.getElementById('capturarBtn').disabled = true;
                    document.getElementById('salvarBtn').disabled = true;
                });
                
                biometriaService.on('onError', function(error) {
                    atualizarStatusMensagem('Erro: ' + error.message, 'error');
                });
                
                biometriaService.on('onCapture', function(data) {
                    if (data.success) {
                        // Atualiza a imagem e a qualidade
                        document.getElementById('digitalImg').src = '/static/images/fingerprint-captured.png';
                        document.getElementById('qualidade').textContent = data.quality + '%';
                        
                        // Armazena o template
                        if (dedoSelecionado === 1) {
                            digitaisCapturadas.dedo1 = data.template;
                        } else if (dedoSelecionado === 2) {
                            digitaisCapturadas.dedo2 = data.template;
                        }
                        
                        // Atualiza o status
                        atualizarStatusMensagem('Digital capturada com sucesso! Qualidade: ' + data.quality + '%', 'success');
                        
                        // Habilita o botão de salvar se ambos os dedos foram capturados
                        document.getElementById('salvarBtn').disabled = !(digitaisCapturadas.dedo1 && digitaisCapturadas.dedo2);
                    } else {
                        atualizarStatusMensagem('Falha na captura: ' + data.error, 'error');
                    }
                });
            }
            
            // Conectar ao serviço
            biometriaService.connect().catch(function(error) {
                atualizarStatusConexao('Falha na conexão com o serviço', false);
                atualizarStatusMensagem('Não foi possível conectar ao serviço de biometria. Verifique se o aplicativo ZK4500 Bridge está instalado e em execução.', 'error');
            });
        }

        // Selecionar um dedo para captura
        function selecionarDedo(dedo) {
            dedoSelecionado = dedo;
            
            // Atualiza a UI
            document.querySelectorAll('.finger-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById('dedo' + dedo + 'Btn').classList.add('active');
            
            // Reseta a imagem se não houver digital capturada para este dedo
            if ((dedo === 1 && !digitaisCapturadas.dedo1) || (dedo === 2 && !digitaisCapturadas.dedo2)) {
                document.getElementById('digitalImg').src = '/static/images/fingerprint-placeholder.png';
                document.getElementById('qualidade').textContent = '0%';
            } else {
                // Mostra a digital já capturada
                document.getElementById('digitalImg').src = '/static/images/fingerprint-captured.png';
                document.getElementById('qualidade').textContent = 'Capturada';
            }
            
            // Habilita o botão de captura se o serviço estiver conectado
            document.getElementById('capturarBtn').disabled = !biometriaService || !biometriaService.isConnected;
            
            atualizarStatusMensagem('Dedo ' + dedo + ' selecionado. Clique em "Capturar" para iniciar a leitura.', 'info');
        }

        // Capturar a digital
        function capturarDigital() {
            if (!biometriaService || !biometriaService.isConnected) {
                atualizarStatusMensagem('Serviço de biometria não está conectado', 'error');
                return;
            }
            
            if (!dedoSelecionado) {
                atualizarStatusMensagem('Selecione um dedo primeiro', 'error');
                return;
            }
            
            // Desabilita o botão durante a captura
            document.getElementById('capturarBtn').disabled = true;
            atualizarStatusMensagem('Posicione o dedo no leitor...', 'info');
            
            // Inicia a captura
            biometriaService.capturarDigital(dedoSelecionado)
                .then(function(resultado) {
                    // O callback onCapture já trata o resultado
                    document.getElementById('capturarBtn').disabled = false;
                })
                .catch(function(error) {
                    atualizarStatusMensagem('Erro na captura: ' + error.message, 'error');
                    document.getElementById('capturarBtn').disabled = false;
                });
        }

        // Limpar a digital do dedo selecionado
        function limparDigital() {
            if (!dedoSelecionado) {
                atualizarStatusMensagem('Selecione um dedo primeiro', 'error');
                return;
            }
            
            // Limpa a digital do dedo selecionado
            if (dedoSelecionado === 1) {
                digitaisCapturadas.dedo1 = null;
            } else if (dedoSelecionado === 2) {
                digitaisCapturadas.dedo2 = null;
            }
            
            // Atualiza a UI
            document.getElementById('digitalImg').src = '/static/images/fingerprint-placeholder.png';
            document.getElementById('qualidade').textContent = '0%';
            
            // Desabilita o botão de salvar se algum dedo não tiver digital
            document.getElementById('salvarBtn').disabled = !(digitaisCapturadas.dedo1 && digitaisCapturadas.dedo2);
            
            atualizarStatusMensagem('Digital do dedo ' + dedoSelecionado + ' foi limpa', 'info');
        }

        // Salvar as biometrias no formulário
        function salvarBiometria() {
            if (!digitaisCapturadas.dedo1 || !digitaisCapturadas.dedo2) {
                atualizarStatusMensagem('Capture as digitais dos dois dedos antes de salvar', 'error');
                return;
            }
            
            // Armazena os templates nos campos ocultos do formulário
            document.getElementById('digital_dedo1').value = digitaisCapturadas.dedo1;
            document.getElementById('digital_dedo2').value = digitaisCapturadas.dedo2;
            
            atualizarStatusMensagem('Biometrias salvas com sucesso!', 'success');
            
            // Fecha o modal após um breve delay
            setTimeout(function() {
                fecharModalBiometria();
            }, 1500);
        }

        // Atualizar o status de conexão
        function atualizarStatusConexao(mensagem, conectado) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.textContent = mensagem;
            
            if (conectado) {
                statusElement.classList.add('connected');
                statusElement.classList.remove('disconnected');
            } else {
                statusElement.classList.add('disconnected');
                statusElement.classList.remove('connected');
            }
        }

        // Atualizar a mensagem de status
        function atualizarStatusMensagem(mensagem, tipo) {
            const statusElement = document.getElementById('statusMessage');
            statusElement.textContent = mensagem;
            
            statusElement.className = 'status-message';
            statusElement.classList.add(tipo);
        }
    </script>
</body>
</html>
