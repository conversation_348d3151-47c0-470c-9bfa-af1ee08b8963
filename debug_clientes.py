#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para debugar o problema na função get_clientes_da_empresa_principal
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_empresa_principal():
    """Debug da empresa principal"""
    try:
        print("=== DEBUG EMPRESA PRINCIPAL ===")
        
        # 1. Testar conexão
        db = DatabaseManager()
        test_result = db.execute_query("SELECT 1 as test")
        print(f"✅ Conexão OK: {test_result}")
        
        # 2. Verificar empresa principal
        sql_empresa = """
        SELECT e.*,
               COALESCE((SELECT COUNT(*) FROM empresa_clientes ec WHERE ec.empresa_principal_id = e.id AND ec.ativo = TRUE), 0) as total_clientes,
               COALESCE((SELECT COUNT(DISTINCT fa.funcionario_id) FROM funcionario_alocacoes fa
                INNER JOIN empresa_clientes ec ON fa.contrato_id = ec.id
                WHERE ec.empresa_principal_id = e.id AND fa.ativo = TRUE), 0) as funcionarios_alocados
        FROM empresas e
        WHERE e.empresa_principal = TRUE AND e.ativa = TRUE
        LIMIT 1
        """
        
        print("\n2. Testando query da empresa principal...")
        empresa_result = db.execute_query(sql_empresa)
        print(f"Resultado: {empresa_result}")
        
        if empresa_result:
            empresa = empresa_result[0]
            print(f"✅ Empresa principal encontrada: {empresa['razao_social']}")
        else:
            print("❌ Nenhuma empresa principal encontrada")
            return
        
        # 3. Testar query de clientes
        print("\n3. Testando query de clientes...")
        sql_clientes = """
        SELECT ec.*, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email,
               0 as funcionarios_alocados,
               0 as jornadas_disponiveis
        FROM empresa_clientes ec
        INNER JOIN empresas e ON ec.empresa_cliente_id = e.id
        INNER JOIN empresas ep ON ec.empresa_principal_id = ep.id
        WHERE ep.empresa_principal = TRUE AND ec.ativo = TRUE
        ORDER BY ec.data_inicio DESC
        """
        
        try:
            clientes = db.execute_query(sql_clientes)
            print(f"✅ Query de clientes executada. Resultado: {len(clientes) if clientes else 0} clientes")
            if clientes:
                for cliente in clientes:
                    print(f"  - {cliente['razao_social']}")
            else:
                print("  Nenhum cliente encontrado")
        except Exception as e:
            print(f"❌ Erro na query de clientes: {e}")
        
        # 4. Testar query de empresas disponíveis
        print("\n4. Testando query de empresas disponíveis...")
        sql_disponiveis = """
        SELECT e.id, e.razao_social, e.nome_fantasia, e.cnpj
        FROM empresas e
        WHERE e.ativa = TRUE 
        AND e.empresa_principal = FALSE
        AND e.id NOT IN (
            SELECT DISTINCT ec.empresa_cliente_id 
            FROM empresa_clientes ec 
            INNER JOIN empresas ep ON ec.empresa_principal_id = ep.id
            WHERE ep.empresa_principal = TRUE AND ec.ativo = TRUE
        )
        ORDER BY e.razao_social
        """
        
        try:
            disponiveis = db.execute_query(sql_disponiveis)
            print(f"✅ Query de empresas disponíveis executada. Resultado: {len(disponiveis) if disponiveis else 0} empresas")
            if disponiveis:
                for empresa in disponiveis[:5]:  # Mostrar apenas as primeiras 5
                    print(f"  - {empresa['razao_social']}")
        except Exception as e:
            print(f"❌ Erro na query de empresas disponíveis: {e}")
        
        # 5. Verificar se há dados na tabela empresa_clientes
        print("\n5. Verificando dados na tabela empresa_clientes...")
        count_clientes = db.execute_query("SELECT COUNT(*) as total FROM empresa_clientes")
        print(f"Total de registros em empresa_clientes: {count_clientes[0]['total'] if count_clientes else 0}")
        
        # 6. Verificar estrutura da tabela
        print("\n6. Verificando estrutura da tabela empresa_clientes...")
        estrutura = db.execute_query("DESCRIBE empresa_clientes")
        print("Campos da tabela:")
        for campo in estrutura:
            print(f"  - {campo['Field']}: {campo['Type']}")
            
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == '__main__':
    debug_empresa_principal()
