#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para aplicar a correção da view vw_relatorio_pontos para exibição correta do setor.
"""

import os
import sys
import logging
import pymysql
from datetime import datetime

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Dados de conexão com o banco de dados de produção
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

def aplicar_correcao_view():
    """
    Aplica a correção SQL para a view vw_relatorio_pontos.
    """
    try:
        # Conexão com o banco de dados usando configuração central
        logger.info(f"✓ Conectando ao servidor de produção {DB_CONFIG['host']}...")
        conn = pymysql.connect(**DB_CONFIG)
        
        cursor = conn.cursor()
        logger.info("✓ Conectado ao banco de dados")
        
        # Carregar o script SQL de um arquivo
        script_path = os.path.join("var", "www", "controle-ponto", "correcao_setor_relatorios.sql")
        
        # Verificar se o arquivo existe
        if not os.path.exists(script_path):
            logger.error(f"❌ Arquivo SQL não encontrado: {script_path}")
            sys.exit(1)
        
        logger.info(f"✓ Arquivo SQL encontrado: {script_path}")
        
        # Ler o conteúdo do arquivo
        with open(script_path, 'r', encoding='utf-8') as sql_file:
            sql_script = sql_file.read()
        
        # Dividir o script em comandos separados por ';'
        commands = sql_script.split(';')
        
        # Executar cada comando SQL
        for command in commands:
            # Ignorar linhas em branco ou comentários
            command = command.strip()
            if command and not command.startswith('--'):
                logger.info(f"Executando comando: {command[:100]}...")
                cursor.execute(command)
                logger.info("✓ Comando executado com sucesso")
        
        # Confirmar as alterações
        conn.commit()
        logger.info("✓ Alterações confirmadas no banco de dados")
        
        # Verificar se a view foi criada corretamente
        cursor.execute("SHOW CREATE VIEW vw_relatorio_pontos")
        view_def = cursor.fetchone()
        
        if view_def:
            logger.info("✅ View vw_relatorio_pontos criada/atualizada com sucesso!")
            logger.info(f"Definição: {view_def[1][:200]}...")
        else:
            logger.warning("⚠️ View vw_relatorio_pontos não foi encontrada após a execução do script")
        
        # Verificar os resultados da view
        cursor.execute("SELECT id, nome_completo, setor FROM vw_relatorio_pontos LIMIT 5")
        resultados = cursor.fetchall()
        
        logger.info("\n==== Primeiros registros da view atualizada ====")
        for reg in resultados:
            logger.info(f"ID: {reg[0]}, Nome: {reg[1]}, Setor: {reg[2]}")
        
        # Fechar a conexão
        cursor.close()
        conn.close()
        logger.info("✓ Conexão com o banco de dados fechada")
        
        return True
    except Exception as e:
        logger.error(f"❌ Erro ao aplicar correção: {str(e)}")
        logger.error("Detalhes:", exc_info=True)
        return False

def verificar_campos_setor():
    """
    Verifica quais funcionários possuem os campos setor e setor_obra preenchidos.
    """
    try:
        # Conexão com o banco de dados usando configuração central
        logger.info(f"✓ Conectando ao servidor de produção {DB_CONFIG['host']} para verificação de campos...")
        conn = pymysql.connect(**DB_CONFIG)
        
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # Consultar dados dos funcionários
        cursor.execute("""
            SELECT 
                id, nome_completo, 
                setor, setor_obra,
                COALESCE(setor, setor_obra, 'Não informado') as setor_exibido
            FROM funcionarios 
            WHERE ativo = TRUE
            LIMIT 20
        """)
        
        funcionarios = cursor.fetchall()
        
        logger.info("\n==== Análise dos campos setor e setor_obra ====")
        logger.info("ID | Nome | setor | setor_obra | Campo que será exibido")
        
        for func in funcionarios:
            setor = func['setor'] or 'NULL'
            setor_obra = func['setor_obra'] or 'NULL'
            setor_exibido = func['setor_exibido']
            
            logger.info(f"{func['id']} | {func['nome_completo']} | {setor} | {setor_obra} | {setor_exibido}")
        
        # Fechar a conexão
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"❌ Erro ao verificar campos: {str(e)}")

if __name__ == "__main__":
    logger.info("=== INÍCIO DO SCRIPT DE CORREÇÃO DA VIEW vw_relatorio_pontos ===")
    logger.info(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    
    # Verificar os valores dos campos setor e setor_obra
    verificar_campos_setor()
    
    # Aplicar a correção
    sucesso = aplicar_correcao_view()
    
    if sucesso:
        logger.info("✅ CORREÇÃO APLICADA COM SUCESSO!")
        logger.info("A view vw_relatorio_pontos foi atualizada para exibir corretamente os setores dos funcionários.")
    else:
        logger.error("❌ FALHA AO APLICAR CORREÇÃO!")
        
    logger.info("=== FIM DO SCRIPT DE CORREÇÃO ===") 