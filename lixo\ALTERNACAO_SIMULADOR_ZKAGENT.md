# 🔄 Alternação: ZKAgent ↔ Simulador

**Ambos funcionam na mesma porta 5001**  
**Frontend não muda nada - só alterna qual processo está rodando**

---

## 🚀 **Como Alternar**

### **🌐 Para Usar SIMULADOR (Dados Fake)**
```bash
# Clique duplo em:
simulacao/usar-simulador.bat

# Ou manualmente:
taskkill /F /IM java.exe
python simulacao/simulador-servidor.py
```

### **🤖 Para Usar ZKAGENT REAL (Hardware)**
```bash
# Clique duplo em:
simulacao/usar-zkagent.bat

# Ou manualmente:
taskkill /F /IM python.exe
java -cp "lib/*;src" ZKAgentProfessional
```

---

## ✅ **Status do Sistema**

### **🌐 Simulador Ativo:**
- ✅ Porta: 5001
- ✅ Dados: Simulados (5 usuários fake)
- ✅ Capturas: 85% sucesso, 70% identificação
- ✅ Frontend: Funciona normalmente
- ✅ Hardware: Não necessário

### **🤖 ZKAgent Real Ativo:**
- ✅ Porta: 5001  
- ✅ Dados: Reais do hardware ZK4500
- ✅ Capturas: Dependem do hardware
- ✅ Frontend: Funciona normalmente
- ✅ Hardware: ZK4500 necessário

---

## 🔗 **Endpoints Idênticos**

**Frontend faz as mesmas chamadas:**
```javascript
// Ping
fetch('http://localhost:5001/ping')

// Status  
fetch('http://localhost:5001/status')

// Captura (principal)
fetch('http://localhost:5001/capture', { method: 'POST' })

// Dispositivos
fetch('http://localhost:5001/list-devices')
```

---

## 📊 **Comparação de Respostas**

### **Simulador Response:**
```json
{
  "success": true,
  "identified": true,
  "user": {
    "name": "João Silva",
    "role": "Desenvolvedor"
  },
  "device": "ZK4500-SIMULADO",
  "quality": 94
}
```

### **ZKAgent Real Response:**
```json
{
  "success": true,
  "template": "ABC123...",
  "quality": 87,
  "timestamp": "2025-06-05T01:00:00"
}
```

---

## 🎯 **Casos de Uso**

### **🌐 Use Simulador Para:**
- ✅ Desenvolvimento do frontend
- ✅ Testes sem hardware
- ✅ Demonstrações para clientes
- ✅ Treinamento da equipe
- ✅ Debug de integrações

### **🤖 Use ZKAgent Real Para:**
- ✅ Produção com funcionários
- ✅ Capturas reais de biometria
- ✅ Validação final
- ✅ Ambiente real de trabalho

---

## 🛠️ **Resolução de Problemas**

### **❌ Erro "Porta em uso":**
```bash
# Ver qual processo está usando porta 5001
netstat -an | findstr :5001

# Finalizar todos os processos
taskkill /F /IM java.exe
taskkill /F /IM python.exe
```

### **❌ Frontend não conecta:**
```bash
# Verificar se algo está rodando na porta 5001
curl http://localhost:5001/ping

# Se não responder, inicie um dos dois:
# Simulador: python simulacao/simulador-servidor.py
# ZKAgent: java -cp "lib/*;src" ZKAgentProfessional
```

---

## 📋 **Logs e Monitoramento**

### **Simulador:**
- **Console:** Mostra requests em tempo real
- **Log:** `simulacao/capturas_servidor_log.txt`
- **Dados:** `simulacao/usuarios_simulados.json`

### **ZKAgent Real:**
- **Console:** Logs detalhados do SDK
- **System Tray:** Ícone de status
- **Config:** `zkagent-config.properties`

---

**🔄 Alternação simples: Execute o script correspondente ao modo desejado!** 