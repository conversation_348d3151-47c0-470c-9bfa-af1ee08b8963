-- ================================================================
-- IMPLEMENTAÇÃO: EMPRESA PRINCIPAL E GESTÃO DE CLIENTES
-- Sistema: RLPONTO-WEB
-- Data: 03/07/2025
-- Descrição: Estrutura para empresa principal, clientes e alocação de funcionários
-- ================================================================

USE controle_ponto;

-- ================================================================
-- 1. MODIFICAÇÕES NA TABELA EMPRESAS
-- ================================================================

-- Adicionar campos para empresa principal (verificar se já existem)
SET @sql = 'ALTER TABLE empresas ADD COLUMN empresa_principal BOOLEAN DEFAULT FALSE COMMENT ''Define se é a empresa principal/proprietária do sistema''';
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'empresas' AND COLUMN_NAME = 'empresa_principal');
SET @sql = IF(@col_exists = 0, @sql, 'SELECT ''Coluna empresa_principal já existe''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE empresas ADD COLUMN empresa_matriz_id INT NULL COMMENT ''ID da empresa matriz (para filiais)''';
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'empresas' AND COLUMN_NAME = 'empresa_matriz_id');
SET @sql = IF(@col_exists = 0, @sql, 'SELECT ''Coluna empresa_matriz_id já existe''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE empresas ADD COLUMN tipo_empresa ENUM(''principal'', ''cliente'', ''filial'', ''independente'') DEFAULT ''independente'' COMMENT ''Tipo da empresa no sistema''';
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'empresas' AND COLUMN_NAME = 'tipo_empresa');
SET @sql = IF(@col_exists = 0, @sql, 'SELECT ''Coluna tipo_empresa já existe''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE empresas ADD COLUMN configuracoes_cliente JSON NULL COMMENT ''Configurações específicas quando atua como cliente''';
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'empresas' AND COLUMN_NAME = 'configuracoes_cliente');
SET @sql = IF(@col_exists = 0, @sql, 'SELECT ''Coluna configuracoes_cliente já existe''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Adicionar índices (verificar se já existem)
SET @sql = 'ALTER TABLE empresas ADD INDEX idx_empresa_principal (empresa_principal)';
SET @idx_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'empresas' AND INDEX_NAME = 'idx_empresa_principal');
SET @sql = IF(@idx_exists = 0, @sql, 'SELECT ''Índice idx_empresa_principal já existe''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE empresas ADD INDEX idx_empresa_matriz (empresa_matriz_id)';
SET @idx_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'empresas' AND INDEX_NAME = 'idx_empresa_matriz');
SET @sql = IF(@idx_exists = 0, @sql, 'SELECT ''Índice idx_empresa_matriz já existe''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE empresas ADD INDEX idx_tipo_empresa (tipo_empresa)';
SET @idx_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'empresas' AND INDEX_NAME = 'idx_tipo_empresa');
SET @sql = IF(@idx_exists = 0, @sql, 'SELECT ''Índice idx_tipo_empresa já existe''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Adicionar foreign key para empresa matriz (verificar se já existe)
SET @sql = 'ALTER TABLE empresas ADD CONSTRAINT fk_empresa_matriz FOREIGN KEY (empresa_matriz_id) REFERENCES empresas(id) ON DELETE SET NULL';
SET @fk_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'empresas' AND CONSTRAINT_NAME = 'fk_empresa_matriz');
SET @sql = IF(@fk_exists = 0, @sql, 'SELECT ''Foreign key fk_empresa_matriz já existe''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ================================================================
-- 2. TABELA EMPRESA_CLIENTES (Relacionamento Principal-Cliente)
-- ================================================================

CREATE TABLE IF NOT EXISTS empresa_clientes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_principal_id INT NOT NULL COMMENT 'ID da empresa principal',
    empresa_cliente_id INT NOT NULL COMMENT 'ID da empresa que atua como cliente',
    nome_contrato VARCHAR(200) NULL COMMENT 'Nome do contrato/projeto',
    codigo_contrato VARCHAR(50) NULL COMMENT 'Código único do contrato',
    descricao_projeto TEXT NULL COMMENT 'Descrição detalhada do projeto',
    data_inicio DATE NOT NULL COMMENT 'Data de início do contrato',
    data_fim DATE NULL COMMENT 'Data prevista de fim do contrato',
    valor_contrato DECIMAL(15,2) NULL COMMENT 'Valor total do contrato',
    status_contrato ENUM('ativo', 'pausado', 'finalizado', 'cancelado') DEFAULT 'ativo',
    ativo BOOLEAN DEFAULT TRUE,
    observacoes TEXT NULL,
    configuracoes_especiais JSON NULL COMMENT 'Configurações específicas do cliente',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL COMMENT 'ID do usuário que criou',
    
    -- Índices e constraints
    FOREIGN KEY (empresa_principal_id) REFERENCES empresas(id) ON DELETE CASCADE,
    FOREIGN KEY (empresa_cliente_id) REFERENCES empresas(id) ON DELETE CASCADE,
    UNIQUE KEY unique_cliente_principal (empresa_principal_id, empresa_cliente_id),
    INDEX idx_empresa_principal (empresa_principal_id),
    INDEX idx_empresa_cliente (empresa_cliente_id),
    INDEX idx_status_contrato (status_contrato),
    INDEX idx_data_inicio (data_inicio),
    INDEX idx_ativo (ativo)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ================================================================
-- 3. TABELA FUNCIONARIO_ALOCACOES (Alocação de Funcionários)
-- ================================================================

CREATE TABLE IF NOT EXISTS funcionario_alocacoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT UNSIGNED NOT NULL COMMENT 'ID do funcionário alocado',
    empresa_cliente_id INT NOT NULL COMMENT 'ID da empresa cliente',
    contrato_id INT NULL COMMENT 'ID do contrato específico (referência a empresa_clientes)',
    jornada_trabalho_id INT NOT NULL COMMENT 'ID da jornada de trabalho herdada',
    cargo_no_cliente VARCHAR(100) NULL COMMENT 'Cargo específico no cliente',
    data_inicio DATE NOT NULL COMMENT 'Data de início da alocação',
    data_fim DATE NULL COMMENT 'Data de fim da alocação',
    percentual_alocacao DECIMAL(5,2) DEFAULT 100.00 COMMENT 'Percentual de tempo alocado (0-100%)',
    valor_hora DECIMAL(10,2) NULL COMMENT 'Valor da hora para este cliente',
    ativo BOOLEAN DEFAULT TRUE,
    observacoes TEXT NULL,
    configuracoes_alocacao JSON NULL COMMENT 'Configurações específicas da alocação',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL COMMENT 'ID do usuário que criou',
    
    -- Índices e constraints
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
    FOREIGN KEY (empresa_cliente_id) REFERENCES empresas(id) ON DELETE CASCADE,
    FOREIGN KEY (contrato_id) REFERENCES empresa_clientes(id) ON DELETE SET NULL,
    FOREIGN KEY (jornada_trabalho_id) REFERENCES jornadas_trabalho(id) ON DELETE RESTRICT,
    INDEX idx_funcionario (funcionario_id),
    INDEX idx_empresa_cliente (empresa_cliente_id),
    INDEX idx_contrato (contrato_id),
    INDEX idx_jornada (jornada_trabalho_id),
    INDEX idx_data_inicio (data_inicio),
    INDEX idx_ativo (ativo),
    INDEX idx_periodo (data_inicio, data_fim)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ================================================================
-- 4. TABELA HISTORICO_ALOCACOES (Histórico de Mudanças)
-- ================================================================

CREATE TABLE IF NOT EXISTS historico_alocacoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    alocacao_id INT NOT NULL COMMENT 'ID da alocação original',
    funcionario_id INT UNSIGNED NOT NULL,
    empresa_cliente_id INT NOT NULL,
    acao ENUM('criada', 'modificada', 'finalizada', 'cancelada') NOT NULL,
    dados_anteriores JSON NULL COMMENT 'Dados antes da modificação',
    dados_novos JSON NULL COMMENT 'Dados após a modificação',
    motivo TEXT NULL COMMENT 'Motivo da alteração',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT NULL,
    
    FOREIGN KEY (alocacao_id) REFERENCES funcionario_alocacoes(id) ON DELETE CASCADE,
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
    FOREIGN KEY (empresa_cliente_id) REFERENCES empresas(id) ON DELETE CASCADE,
    INDEX idx_alocacao (alocacao_id),
    INDEX idx_funcionario (funcionario_id),
    INDEX idx_acao (acao),
    INDEX idx_data (created_at)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ================================================================
-- 5. VIEWS PARA CONSULTAS OTIMIZADAS
-- ================================================================

-- View: Empresa Principal com estatísticas
CREATE OR REPLACE VIEW vw_empresa_principal AS
SELECT 
    e.id,
    e.razao_social,
    e.nome_fantasia,
    e.cnpj,
    e.telefone,
    e.email,
    e.ativa,
    e.data_cadastro,
    COUNT(DISTINCT ec.id) as total_clientes,
    COUNT(DISTINCT fa.funcionario_id) as total_funcionarios_alocados,
    COUNT(DISTINCT CASE WHEN ec.status_contrato = 'ativo' THEN ec.id END) as clientes_ativos
FROM empresas e
LEFT JOIN empresa_clientes ec ON e.id = ec.empresa_principal_id
LEFT JOIN funcionario_alocacoes fa ON ec.empresa_cliente_id = fa.empresa_cliente_id AND fa.ativo = TRUE
WHERE e.empresa_principal = TRUE
GROUP BY e.id, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email, e.ativa, e.data_cadastro;

-- View: Clientes com informações detalhadas
CREATE OR REPLACE VIEW vw_clientes_detalhados AS
SELECT 
    ec.id as contrato_id,
    ec.empresa_principal_id,
    ep.razao_social as empresa_principal_nome,
    ec.empresa_cliente_id,
    ecl.razao_social as cliente_razao_social,
    ecl.nome_fantasia as cliente_nome_fantasia,
    ecl.cnpj as cliente_cnpj,
    ec.nome_contrato,
    ec.codigo_contrato,
    ec.data_inicio,
    ec.data_fim,
    ec.valor_contrato,
    ec.status_contrato,
    ec.ativo,
    COUNT(DISTINCT fa.funcionario_id) as funcionarios_alocados,
    COUNT(DISTINCT jt.id) as jornadas_disponiveis
FROM empresa_clientes ec
INNER JOIN empresas ep ON ec.empresa_principal_id = ep.id
INNER JOIN empresas ecl ON ec.empresa_cliente_id = ecl.id
LEFT JOIN funcionario_alocacoes fa ON ec.empresa_cliente_id = fa.empresa_cliente_id AND fa.ativo = TRUE
LEFT JOIN jornadas_trabalho jt ON ecl.id = jt.empresa_id AND jt.ativa = TRUE
GROUP BY ec.id, ec.empresa_principal_id, ep.razao_social, ec.empresa_cliente_id, 
         ecl.razao_social, ecl.nome_fantasia, ecl.cnpj, ec.nome_contrato, 
         ec.codigo_contrato, ec.data_inicio, ec.data_fim, ec.valor_contrato, 
         ec.status_contrato, ec.ativo;

-- View: Funcionários alocados com detalhes
CREATE OR REPLACE VIEW vw_funcionarios_alocados AS
SELECT 
    fa.id as alocacao_id,
    fa.funcionario_id,
    f.nome_completo as funcionario_nome,
    f.cpf as funcionario_cpf,
    fa.empresa_cliente_id,
    e.razao_social as cliente_nome,
    fa.contrato_id,
    ec.nome_contrato,
    fa.jornada_trabalho_id,
    jt.nome_jornada as jornada_nome,
    jt.seg_qui_entrada as horario_entrada,
    jt.seg_qui_saida as horario_saida,
    fa.cargo_no_cliente,
    fa.data_inicio,
    fa.data_fim,
    fa.percentual_alocacao,
    fa.valor_hora,
    fa.ativo
FROM funcionario_alocacoes fa
INNER JOIN funcionarios f ON fa.funcionario_id = f.id
INNER JOIN empresas e ON fa.empresa_cliente_id = e.id
LEFT JOIN empresa_clientes ec ON fa.contrato_id = ec.id
INNER JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
WHERE fa.ativo = TRUE;

-- ================================================================
-- 6. CONFIGURAÇÕES INICIAIS
-- ================================================================

-- Inserir configurações específicas para empresa principal
INSERT IGNORE INTO configuracoes_sistema (chave, valor, descricao, tipo, categoria, editavel) VALUES
('empresa_principal_id', '0', 'ID da empresa principal do sistema', 'integer', 'empresa', 1),
('permitir_multiplas_alocacoes', 'true', 'Permitir funcionário em múltiplos clientes simultaneamente', 'boolean', 'funcionarios', 1),
('heranca_jornada_automatica', 'true', 'Herança automática de jornada ao alocar funcionário', 'boolean', 'funcionarios', 1),
('notificar_mudancas_alocacao', 'true', 'Notificar mudanças nas alocações de funcionários', 'boolean', 'notificacoes', 1);

-- ================================================================
-- 7. TRIGGERS PARA AUDITORIA E VALIDAÇÕES
-- ================================================================

-- Trigger: Garantir apenas uma empresa principal
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_empresa_principal_unica
BEFORE UPDATE ON empresas
FOR EACH ROW
BEGIN
    IF NEW.empresa_principal = TRUE AND OLD.empresa_principal = FALSE THEN
        UPDATE empresas SET empresa_principal = FALSE WHERE empresa_principal = TRUE AND id != NEW.id;
    END IF;
END$$
DELIMITER ;

-- Trigger: Histórico de alocações
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_historico_alocacao_update
AFTER UPDATE ON funcionario_alocacoes
FOR EACH ROW
BEGIN
    INSERT INTO historico_alocacoes (
        alocacao_id, funcionario_id, empresa_cliente_id, acao, 
        dados_anteriores, dados_novos, created_at
    ) VALUES (
        NEW.id, NEW.funcionario_id, NEW.empresa_cliente_id, 'modificada',
        JSON_OBJECT(
            'data_inicio', OLD.data_inicio,
            'data_fim', OLD.data_fim,
            'jornada_trabalho_id', OLD.jornada_trabalho_id,
            'ativo', OLD.ativo
        ),
        JSON_OBJECT(
            'data_inicio', NEW.data_inicio,
            'data_fim', NEW.data_fim,
            'jornada_trabalho_id', NEW.jornada_trabalho_id,
            'ativo', NEW.ativo
        ),
        NOW()
    );
END$$
DELIMITER ;

-- ================================================================
-- SCRIPT CONCLUÍDO
-- ================================================================

SELECT 'Estrutura de Empresa Principal e Clientes criada com sucesso!' as status;
