import pymysql
import os
from datetime import datetime

def aplicar_melhorias_views():
    try:
        # Conectar ao banco de dados
        conn = pymysql.connect(
            host='************',
            port=3306,
            user='cavalcrod',
            password='200381',
            database='controle_ponto'
        )
        
        cursor = conn.cursor()
        
        # Ler o arquivo SQL
        with open('var/www/controle-ponto/sql/melhorias_views.sql', 'r', encoding='utf-8') as file:
            sql_commands = file.read()
        
        # Executar cada comando SQL separadamente
        for command in sql_commands.split(';'):
            if command.strip():
                print(f"\nExecutando comando:\n{command}\n")
                cursor.execute(command)
        
        # Commit das alterações
        conn.commit()
        
        print("\nMelhorias aplicadas com sucesso!")
        
        # Verificar as views criadas
        cursor.execute("SHOW FULL TABLES WHERE Table_type = 'VIEW'")
        views = cursor.fetchall()
        
        print("\nViews disponíveis no sistema:")
        for view in views:
            print(f"- {view[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"Erro ao aplicar melhorias: {str(e)}")

if __name__ == "__main__":
    aplicar_melhorias_views() 