{% extends "base.html" %}

{% block title %}Histórico de Jornadas - {{ funcionario.nome_completo }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Início</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('funcionarios.index') }}">Funcionários</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('funcionarios.detalhes', funcionario_id=funcionario.id) }}">{{ funcionario.nome_completo }}</a></li>
            <li class="breadcrumb-item active">Histórico de Jornadas</li>
        </ol>
    </nav>

    <!-- Cabeçalho -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-history text-primary me-2"></i>
                Histórico de Jornadas
            </h1>
            <p class="text-muted mb-0">{{ funcionario.nome_completo }}</p>
        </div>
        <div>
            <a href="{{ url_for('funcionarios.detalhes', funcionario_id=funcionario.id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Voltar
            </a>
        </div>
    </div>

    <!-- Informações do Funcionário -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user text-info me-2"></i>
                        Informações Atuais
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Funcionário:</label>
                                <p class="mb-0">{{ funcionario.nome_completo }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Empresa:</label>
                                <p class="mb-0">{{ funcionario.empresa_nome or 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Jornada Atual:</label>
                                <p class="mb-0">{{ funcionario.jornada_nome_jornada or 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Usa Herança da Empresa:</label>
                                <span class="badge bg-{{ 'success' if funcionario.usa_horario_empresa else 'secondary' }}">
                                    {{ 'Sim' if funcionario.usa_horario_empresa else 'Não' }}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    {% if funcionario.jornada_seg_qui_entrada %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Horário Segunda a Quinta:</label>
                                <p class="mb-0">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ funcionario.jornada_seg_qui_entrada }} às {{ funcionario.jornada_seg_qui_saida }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Horário Sexta-feira:</label>
                                <p class="mb-0">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ funcionario.jornada_sexta_entrada }} às {{ funcionario.jornada_sexta_saida }}
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Histórico de Mudanças -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list text-primary me-2"></i>
                        Histórico de Mudanças ({{ historico|length }} registros)
                    </h5>
                </div>
                <div class="card-body">
                    {% if historico %}
                    <div class="timeline">
                        {% for mudanca in historico %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-{{ 
                                'primary' if mudanca.tipo_mudanca == 'EMPRESA_ALTEROU' 
                                else 'info' if mudanca.tipo_mudanca == 'ALOCACAO_CLIENTE'
                                else 'secondary' if mudanca.tipo_mudanca == 'ALTERACAO_MANUAL'
                                else 'success' if mudanca.tipo_mudanca == 'CADASTRO_INICIAL'
                                else 'warning'
                            }}">
                                <i class="fas fa-{{ 
                                    'building' if mudanca.tipo_mudanca == 'EMPRESA_ALTEROU'
                                    else 'project-diagram' if mudanca.tipo_mudanca == 'ALOCACAO_CLIENTE'
                                    else 'edit' if mudanca.tipo_mudanca == 'ALTERACAO_MANUAL'
                                    else 'plus' if mudanca.tipo_mudanca == 'CADASTRO_INICIAL'
                                    else 'clock'
                                }}"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">
                                            <span class="badge bg-{{ 
                                                'primary' if mudanca.tipo_mudanca == 'EMPRESA_ALTEROU' 
                                                else 'info' if mudanca.tipo_mudanca == 'ALOCACAO_CLIENTE'
                                                else 'secondary' if mudanca.tipo_mudanca == 'ALTERACAO_MANUAL'
                                                else 'success' if mudanca.tipo_mudanca == 'CADASTRO_INICIAL'
                                                else 'warning'
                                            }} me-2">
                                                {{ mudanca.tipo_mudanca.replace('_', ' ').title() }}
                                            </span>
                                        </h6>
                                        <p class="mb-1">{{ mudanca.motivo }}</p>
                                    </div>
                                    <small class="text-muted">
                                        {{ mudanca.data_mudanca.strftime('%d/%m/%Y %H:%M') }}
                                    </small>
                                </div>
                                
                                <div class="row">
                                    {% if mudanca.jornada_anterior_nome %}
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <small class="text-muted">Jornada Anterior:</small>
                                                <p class="mb-0 fw-bold">{{ mudanca.jornada_anterior_nome }}</p>
                                                {% if mudanca.dados_jornada_anterior %}
                                                <small class="text-muted">
                                                    {% set dados_ant = mudanca.dados_jornada_anterior|from_json %}
                                                    {{ dados_ant.seg_qui_entrada }} às {{ dados_ant.seg_qui_saida }}
                                                </small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                    
                                    <div class="col-md-6">
                                        <div class="card bg-success bg-opacity-10">
                                            <div class="card-body py-2">
                                                <small class="text-muted">Nova Jornada:</small>
                                                <p class="mb-0 fw-bold text-success">{{ mudanca.jornada_nova_nome }}</p>
                                                {% if mudanca.dados_jornada_nova %}
                                                <small class="text-success">
                                                    {% set dados_nova = mudanca.dados_jornada_nova|from_json %}
                                                    {{ dados_nova.seg_qui_entrada }} às {{ dados_nova.seg_qui_saida }}
                                                </small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                {% if mudanca.usuario_responsavel_nome %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        Responsável: {{ mudanca.usuario_responsavel_nome }}
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhuma mudança registrada</h5>
                        <p class="text-muted">Este funcionário ainda não possui histórico de mudanças de jornada.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    border: 3px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.timeline-item:last-child {
    margin-bottom: 0;
}
</style>

<script>
// Adicionar filtro JSON para templates
document.addEventListener('DOMContentLoaded', function() {
    // Função para parsear JSON nos templates
    window.parseJSON = function(jsonString) {
        try {
            return JSON.parse(jsonString);
        } catch (e) {
            return {};
        }
    };
});
</script>
{% endblock %}
