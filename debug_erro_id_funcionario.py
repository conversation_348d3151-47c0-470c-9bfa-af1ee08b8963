#!/usr/bin/env python3
"""
Script para diagnosticar e corrigir o erro 'id' no formulário de funcionários.

O problema identificado:
- O campo epis[X][id] está sendo processado incorretamente
- Está sendo incluído nos dados principais do formulário
- A validação tenta validar 'id' como campo obrigatório
- Isso causa o erro "Erro ao processar formulário: 'id'"

Solução:
- Filtrar campos 'id' dos EPIs para não incluí-los nos dados principais
- Manter apenas nos dados específicos dos EPIs
"""

import sys
import os

# Adicionar o diretório do projeto ao path
sys.path.append('/var/www/controle-ponto')

def diagnosticar_problema():
    """
    Simula o processamento do formulário para identificar o problema.
    """
    print("🔍 DIAGNÓSTICO: Erro 'id' no formulário de funcionários")
    print("=" * 60)
    
    # Simular dados do formulário com EPIs
    form_data_simulado = {
        'nome_completo': '<PERSON>',
        'cpf': '123.456.789-00',
        'empresa_id': '1',
        'epis[0][id]': '123',  # ❌ PROBLEMA: Este campo está sendo processado
        'epis[0][epi_nome]': 'Capacete',
        'epis[0][epi_ca]': '12345',
        'epis[1][id]': '124',  # ❌ PROBLEMA: Este campo também
        'epis[1][epi_nome]': 'Luvas',
        'epis[1][epi_ca]': '67890'
    }
    
    print("📋 Dados simulados do formulário:")
    for key, value in form_data_simulado.items():
        print(f"  {key}: {value}")
    
    print("\n🔍 Processando campos EPIs...")
    
    # Simular o processamento atual (com problema)
    dados_principais = {}
    epis = []
    
    for key in form_data_simulado.keys():
        if key.startswith('epis[') and '][' in key:
            # Extrair índice e campo
            try:
                start = key.find('[') + 1
                end = key.find(']')
                index = int(key[start:end])
                
                campo_start = key.find('][') + 2
                campo_end = key.rfind(']')
                campo = key[campo_start:campo_end]
                
                print(f"  Campo EPI: {key} -> índice={index}, campo={campo}")
                
                # ❌ PROBLEMA: Todos os campos, incluindo 'id', vão para dados principais
                if campo not in dados_principais:
                    dados_principais[campo] = form_data_simulado[key]
                    
            except (ValueError, IndexError):
                print(f"  ❌ Campo EPI inválido: {key}")
    
    print(f"\n❌ PROBLEMA IDENTIFICADO:")
    print(f"  Dados principais contêm: {list(dados_principais.keys())}")
    print(f"  Campo 'id' está presente: {'id' in dados_principais}")
    
    return dados_principais

def mostrar_solucao():
    """
    Mostra a solução para o problema.
    """
    print("\n" + "=" * 60)
    print("✅ SOLUÇÃO PROPOSTA")
    print("=" * 60)
    
    print("""
1. PROBLEMA IDENTIFICADO:
   - O campo epis[X][id] está sendo incluído nos dados principais
   - A validação tenta validar 'id' como campo obrigatório
   - Isso causa erro "Erro ao processar formulário: 'id'"

2. CAUSA RAIZ:
   - Função _extrair_dados_formulario() processa todos os campos EPIs
   - Não filtra campos específicos como 'id' que são apenas para EPIs
   - Campo 'id' não deveria estar nos dados principais do funcionário

3. CORREÇÃO NECESSÁRIA:
   - Modificar _extrair_dados_formulario() para filtrar campo 'id'
   - Manter 'id' apenas nos dados específicos dos EPIs
   - Não incluir 'id' nos dados principais que são validados

4. ARQUIVO A SER CORRIGIDO:
   - /var/www/controle-ponto/app_funcionarios.py
   - Função _extrair_dados_formulario() linha ~720-760
    """)

def gerar_correcao():
    """
    Gera o código corrigido.
    """
    print("\n" + "=" * 60)
    print("🔧 CÓDIGO CORRIGIDO")
    print("=" * 60)
    
    codigo_corrigido = '''
def _extrair_dados_formulario():
    """
    Extrai e organiza dados do formulário.
    
    Returns:
        dict: Dados organizados do formulário
    """
    form_data = request.form.to_dict()
    
    # Dados principais do funcionário (SEM campos EPIs)
    dados_principais = {
        # Dados pessoais
        'nome_completo': request.form.get('nome_completo', '').strip(),
        'cpf': request.form.get('cpf', '').strip(),
        'rg': request.form.get('rg', '').strip(),
        # ... outros campos principais
    }
    
    # ✅ CORREÇÃO: Processar EPIs separadamente
    epis = []
    for key in form_data.keys():
        if key.startswith('epis[') and '][' in key:
            try:
                start = key.find('[') + 1
                end = key.find(']')
                index = int(key[start:end])
                
                campo_start = key.find('][') + 2
                campo_end = key.rfind(']')
                campo = key[campo_start:campo_end]
                
                # Garantir que existe EPI no índice
                while len(epis) <= index:
                    epis.append({})
                
                # ✅ CORREÇÃO: Adicionar APENAS aos EPIs, não aos dados principais
                valor = form_data.get(key, '').strip()
                epis[index][campo] = valor
                
            except (ValueError, IndexError):
                logger.warning(f"Campo EPI inválido: {key}")
                continue
    
    # Adicionar EPIs aos dados principais
    dados_principais['epis'] = epis
    
    return dados_principais
    '''
    
    print(codigo_corrigido)

if __name__ == "__main__":
    diagnosticar_problema()
    mostrar_solucao()
    gerar_correcao()
    
    print("\n" + "=" * 60)
    print("🚀 PRÓXIMOS PASSOS")
    print("=" * 60)
    print("""
1. Aplicar a correção no arquivo app_funcionarios.py
2. Reiniciar o servidor Flask
3. Testar edição de funcionário sem alterar dados
4. Verificar se o erro 'id' foi resolvido
    """)
