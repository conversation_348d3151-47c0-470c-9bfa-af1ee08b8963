#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de Diagnóstico - Sistema de Relatórios
Data: 06/06/2025
Objetivo: Diagnosticar problemas no sistema de relatórios do RLPONTO-WEB
"""

import sys
import os
import logging
import pymysql
import traceback
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configurações do banco
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def cabecalho():
    """Exibe cabeçalho do diagnóstico."""
    print("=" * 80)
    print("DIAGNÓSTICO DO SISTEMA DE RELATÓRIOS - RLPONTO-WEB")
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 80)

def diagnostico_banco_dados():
    """Verifica conexão e views do banco de dados."""
    print("\n📊 TESTE 1: Conexão com Banco de Dados")
    print("-" * 50)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print("✅ Conexão com banco estabelecida com sucesso")
        
        cursor = conn.cursor()
        
        # Verificar views existentes
        print("\n📋 Verificando views SQL necessárias:")
        views_necessarias = [
            'vw_relatorio_pontos',
            'vw_estatisticas_pontos', 
            'vw_horas_trabalhadas',
            'vw_analise_pontualidade'
        ]
        
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM information_schema.views 
            WHERE TABLE_SCHEMA = 'controle_ponto'
            AND TABLE_NAME IN ('vw_relatorio_pontos', 'vw_estatisticas_pontos', 'vw_horas_trabalhadas', 'vw_analise_pontualidade')
        """)
        
        views_existentes = [row['TABLE_NAME'] for row in cursor.fetchall()]
        
        views_faltando = []
        for view in views_necessarias:
            if view in views_existentes:
                print(f"  ✅ {view} - EXISTE")
            else:
                print(f"  ❌ {view} - FALTANDO")
                views_faltando.append(view)
        
        # Verificar tabela registros_ponto
        print("\n📋 Verificando estrutura da tabela registros_ponto:")
        try:
            cursor.execute("DESCRIBE registros_ponto")
            colunas = cursor.fetchall()
            print(f"  ✅ Tabela registros_ponto existe com {len(colunas)} colunas")
            
            # Verificar se há registros
            cursor.execute("SELECT COUNT(*) as total FROM registros_ponto")
            total_registros = cursor.fetchone()['total']
            print(f"  📊 Total de registros na tabela: {total_registros}")
            
        except Exception as e:
            print(f"  ❌ Erro ao verificar tabela registros_ponto: {e}")
        
        conn.close()
        
        if views_faltando:
            print(f"\n⚠️ PROBLEMA ENCONTRADO: {len(views_faltando)} view(s) faltando")
            return False, views_faltando
        else:
            print("\n✅ Todas as views necessárias estão presentes")
            return True, []
            
    except Exception as e:
        print(f"❌ Erro na conexão com banco: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False, []

def diagnostico_dependencias():
    """Verifica se todas as dependências estão disponíveis."""
    print("\n📦 TESTE 2: Dependências Python")
    print("-" * 50)
    
    dependencias = [
        'flask',
        'pymysql',
        'werkzeug'
    ]
    
    dependencias_ok = True
    
    for dep in dependencias:
        try:
            __import__(dep)
            print(f"  ✅ {dep}")
        except ImportError:
            print(f"  ❌ {dep} - FALTANDO")
            dependencias_ok = False
    
    # Verificar módulos do projeto
    print("\n📋 Verificando módulos do projeto:")
    modulos_projeto = [
        'utils.auth',
        'utils.database', 
        'utils.helpers',
        'app_relatorios'
    ]
    
    for modulo in modulos_projeto:
        try:
            __import__(modulo)
            print(f"  ✅ {modulo}")
        except ImportError as e:
            print(f"  ❌ {modulo} - ERRO: {e}")
            dependencias_ok = False
    
    return dependencias_ok

def diagnostico_blueprint():
    """Verifica se o blueprint está sendo registrado corretamente."""
    print("\n🔗 TESTE 3: Registro do Blueprint")
    print("-" * 50)
    
    try:
        # Tentar importar e verificar blueprint
        from app_relatorios import relatorios_bp
        print("  ✅ Blueprint relatorios_bp importado com sucesso")
        
        # Verificar se o blueprint tem as funções principais
        print("  📋 Verificando funções principais do blueprint:")
        
        # Verificar se as funções existem no módulo
        import app_relatorios
        funcoes_esperadas = [
            'pagina_relatorio_pontos',
            'api_buscar_registros', 
            'api_exportar_csv',
            'api_dados_graficos'
        ]
        
        for funcao in funcoes_esperadas:
            if hasattr(app_relatorios, funcao):
                print(f"    ✅ {funcao}")
            else:
                print(f"    ❌ {funcao} - FALTANDO")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erro ao importar blueprint: {e}")
        print(f"  Traceback: {traceback.format_exc()}")
        return False

def diagnostico_endpoint_especifico():
    """Testa especificamente o endpoint que está falhando."""
    print("\n🎯 TESTE 4: Endpoint Específico")
    print("-" * 50)
    
    try:
        # Simular requisição para o endpoint problemático
        from app_relatorios import api_buscar_registros
        print("  ✅ Função api_buscar_registros encontrada")
        
        # Verificar se as funções de utils estão disponíveis
        from utils.helpers import mascarar_dados_relatorio, RegistroPontoValidator
        print("  ✅ Funções de utils.helpers importadas")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erro ao verificar endpoint: {e}")
        print(f"  Traceback: {traceback.format_exc()}")
        return False

def aplicar_views_faltando(views_faltando):
    """Aplica as views que estão faltando."""
    if not views_faltando:
        return True
        
    print(f"\n🔧 APLICANDO VIEWS FALTANDO ({len(views_faltando)})")
    print("-" * 50)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Views SQL
        views_sql = {
            'vw_relatorio_pontos': """
                CREATE VIEW vw_relatorio_pontos AS
                SELECT 
                    rp.id,
                    rp.funcionario_id,
                    f.nome_completo,
                    f.matricula_empresa,
                    f.cpf,
                    CONCAT(
                        SUBSTRING(f.cpf, 1, 3), '.***.***-',
                        SUBSTRING(f.cpf, -2)
                    ) as cpf_exibicao,
                    rp.data_hora,
                    DATE(rp.data_hora) as data_registro,
                    TIME(rp.data_hora) as hora_registro,
                    rp.tipo_registro,
                    CASE rp.tipo_registro
                        WHEN 'entrada_manha' THEN 'Entrada Manhã'
                        WHEN 'saida_almoco' THEN 'Saída Almoço'
                        WHEN 'entrada_tarde' THEN 'Entrada Tarde'
                        WHEN 'saida' THEN 'Saída'
                    END AS tipo_descricao,
                    rp.metodo_registro,
                    CASE 
                        WHEN rp.metodo_registro = 'biometrico' THEN 'Biométrico'
                        ELSE 'Manual'
                    END AS metodo_descricao,
                    COALESCE(f.setor_obra, f.setor, 'Não informado') as setor,
                    f.cargo,
                    f.empresa,
                    rp.qualidade_biometria,
                    rp.observacoes,
                    rp.ip_origem,
                    rp.criado_em,
                    u.usuario as criado_por_usuario,
                    CASE 
                        WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atraso'
                        WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atraso'
                        ELSE 'Pontual'
                    END AS status_pontualidade
                FROM registros_ponto rp
                INNER JOIN funcionarios f ON rp.funcionario_id = f.id
                LEFT JOIN usuarios u ON rp.criado_por = u.id
                WHERE f.ativo = 1
                ORDER BY rp.data_hora DESC
            """,
            
            'vw_estatisticas_pontos': """
                CREATE VIEW vw_estatisticas_pontos AS
                SELECT 
                    DATE(rp.data_hora) as data_registro,
                    COUNT(*) as total_registros,
                    SUM(CASE WHEN rp.metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
                    SUM(CASE WHEN rp.metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais,
                    SUM(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN 1 ELSE 0 END) as entradas_manha,
                    SUM(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN 1 ELSE 0 END) as saidas_almoco,
                    SUM(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN 1 ELSE 0 END) as entradas_tarde,
                    SUM(CASE WHEN rp.tipo_registro = 'saida' THEN 1 ELSE 0 END) as saidas,
                    SUM(CASE 
                        WHEN (rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00') OR
                             (rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00')
                        THEN 1 ELSE 0 
                    END) as atrasos,
                    COUNT(DISTINCT rp.funcionario_id) as funcionarios_registraram
                FROM registros_ponto rp
                INNER JOIN funcionarios f ON rp.funcionario_id = f.id
                WHERE f.ativo = 1
                GROUP BY DATE(rp.data_hora)
                ORDER BY data_registro DESC
            """,
            
            'vw_horas_trabalhadas': """
                CREATE VIEW vw_horas_trabalhadas AS
                WITH registros_diarios AS (
                    SELECT 
                        f.id as funcionario_id,
                        f.nome_completo,
                        f.setor,
                        f.cargo,
                        DATE(rp.data_hora) as data_registro,
                        MAX(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN rp.data_hora END) as entrada_manha,
                        MAX(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN rp.data_hora END) as saida_almoco,
                        MAX(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN rp.data_hora END) as entrada_tarde,
                        MAX(CASE WHEN rp.tipo_registro = 'saida' THEN rp.data_hora END) as saida
                    FROM 
                        registros_ponto rp
                        INNER JOIN funcionarios f ON rp.funcionario_id = f.id
                    WHERE f.ativo = 1
                    GROUP BY 
                        f.id,
                        f.nome_completo,
                        f.setor,
                        f.cargo,
                        DATE(rp.data_hora)
                )
                SELECT 
                    rd.*,
                    CASE
                        WHEN rd.entrada_manha IS NOT NULL AND rd.saida_almoco IS NOT NULL
                        THEN TIMEDIFF(rd.saida_almoco, rd.entrada_manha) 
                        ELSE NULL
                    END as periodo_manha,
                    CASE
                        WHEN rd.entrada_tarde IS NOT NULL AND rd.saida IS NOT NULL
                        THEN TIMEDIFF(rd.saida, rd.entrada_tarde)
                        ELSE NULL
                    END as periodo_tarde,
                    CASE
                        WHEN rd.entrada_manha IS NOT NULL AND rd.saida_almoco IS NOT NULL
                             AND rd.entrada_tarde IS NOT NULL AND rd.saida IS NOT NULL
                        THEN ADDTIME(
                            TIMEDIFF(rd.saida_almoco, rd.entrada_manha),
                            TIMEDIFF(rd.saida, rd.entrada_tarde)
                        )
                        ELSE NULL
                    END as total_horas_trabalhadas,
                    CASE 
                        WHEN rd.entrada_manha IS NULL OR rd.saida_almoco IS NULL 
                        OR rd.entrada_tarde IS NULL OR rd.saida IS NULL 
                        THEN 'Incompleto'
                        ELSE 'Completo'
                    END as status_dia
                FROM 
                    registros_diarios rd
            """,
            
            'vw_analise_pontualidade': """
                CREATE VIEW vw_analise_pontualidade AS
                SELECT 
                    f.id as funcionario_id,
                    f.nome_completo,
                    f.setor,
                    f.cargo,
                    DATE_FORMAT(rp.data_hora, '%Y-%m') as mes_ano,
                    COUNT(DISTINCT DATE(rp.data_hora)) as dias_trabalhados,
                    SUM(CASE 
                        WHEN rp.tipo_registro = 'entrada_manha' 
                        AND TIME(rp.data_hora) <= '08:10:00'
                        THEN 1 
                        ELSE 0 
                    END) as entradas_pontuais_manha,
                    SUM(CASE 
                        WHEN rp.tipo_registro = 'entrada_manha' 
                        AND TIME(rp.data_hora) > '08:10:00'
                        THEN 1 
                        ELSE 0 
                    END) as atrasos_manha,
                    SUM(CASE 
                        WHEN rp.tipo_registro = 'entrada_tarde' 
                        AND TIME(rp.data_hora) <= '13:10:00'
                        THEN 1 
                        ELSE 0 
                    END) as entradas_pontuais_tarde,
                    SUM(CASE 
                        WHEN rp.tipo_registro = 'entrada_tarde' 
                        AND TIME(rp.data_hora) > '13:10:00'
                        THEN 1 
                        ELSE 0 
                    END) as atrasos_tarde,
                    ROUND(
                        (SUM(CASE 
                            WHEN rp.tipo_registro IN ('entrada_manha', 'entrada_tarde')
                            AND ((rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) <= '08:10:00')
                              OR (rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) <= '13:10:00'))
                            THEN 1 
                            ELSE 0 
                        END) * 100.0) / 
                        NULLIF(SUM(CASE WHEN rp.tipo_registro IN ('entrada_manha', 'entrada_tarde') THEN 1 ELSE 0 END), 0),
                        2
                    ) as percentual_pontualidade
                FROM 
                    registros_ponto rp
                    INNER JOIN funcionarios f ON rp.funcionario_id = f.id
                WHERE f.ativo = 1
                GROUP BY 
                    f.id,
                    f.nome_completo,
                    f.setor,
                    f.cargo,
                    DATE_FORMAT(rp.data_hora, '%Y-%m')
            """
        }
        
        views_criadas = 0
        for view_name in views_faltando:
            if view_name in views_sql:
                try:
                    cursor.execute(f"DROP VIEW IF EXISTS {view_name}")
                    cursor.execute(views_sql[view_name])
                    print(f"  ✅ {view_name} criada com sucesso")
                    views_criadas += 1
                except Exception as e:
                    print(f"  ❌ Erro ao criar {view_name}: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ {views_criadas} view(s) criada(s) com sucesso!")
        return views_criadas == len(views_faltando)
        
    except Exception as e:
        print(f"❌ Erro ao aplicar views: {e}")
        return False

def main():
    """Função principal do diagnóstico."""
    cabecalho()
    
    problemas_encontrados = []
    
    # Teste 1: Banco de dados e views
    banco_ok, views_faltando = diagnostico_banco_dados()
    if not banco_ok:
        problemas_encontrados.append("Problema no banco de dados")
    
    # Teste 2: Dependências
    deps_ok = diagnostico_dependencias()
    if not deps_ok:
        problemas_encontrados.append("Dependências faltando")
    
    # Teste 3: Blueprint
    blueprint_ok = diagnostico_blueprint()
    if not blueprint_ok:
        problemas_encontrados.append("Problema no blueprint")
    
    # Teste 4: Endpoint específico
    endpoint_ok = diagnostico_endpoint_especifico()
    if not endpoint_ok:
        problemas_encontrados.append("Problema no endpoint")
    
    # Aplicar correções se necessário
    if views_faltando:
        print(f"\n🔧 TENTANDO CORRIGIR VIEWS FALTANDO...")
        if aplicar_views_faltando(views_faltando):
            print("✅ Views aplicadas com sucesso!")
        else:
            print("❌ Falha ao aplicar views")
    
    # Resumo final
    print("\n" + "=" * 80)
    print("RESUMO DO DIAGNÓSTICO")
    print("=" * 80)
    
    if not problemas_encontrados:
        print("✅ NENHUM PROBLEMA ENCONTRADO")
        print("\nO sistema de relatórios deve estar funcionando.")
        print("Se o erro 500 persistir, verifique:")
        print("1. Se o servidor web foi reiniciado")
        print("2. Os logs do Flask/Gunicorn")
        print("3. Permissões de arquivo")
    else:
        print(f"❌ {len(problemas_encontrados)} PROBLEMA(S) ENCONTRADO(S):")
        for i, problema in enumerate(problemas_encontrados, 1):
            print(f"  {i}. {problema}")
        
        print("\nAÇÕES RECOMENDADAS:")
        if "Dependências faltando" in problemas_encontrados:
            print("  - Execute: pip install -r requirements.txt")
        if "Problema no banco de dados" in problemas_encontrados:
            print("  - Verifique credenciais e conexão do banco")
        if "Problema no blueprint" in problemas_encontrados:
            print("  - Verifique se app_relatorios.py está no local correto")
        if "Problema no endpoint" in problemas_encontrados:
            print("  - Verifique se utils/helpers.py existe e está correto")
    
    print(f"\nDiagnóstico concluído em {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    return len(problemas_encontrados) == 0

if __name__ == "__main__":
    sys.exit(0 if main() else 1) 