#!/usr/bin/env python3
import requests
import paramiko
from bs4 import BeautifulSoup

def test_real_browser():
    print("🔍 TESTE REAL - SIMULANDO NAVEGADOR")
    print("=" * 50)
    
    # Criar sessão para manter cookies
    session = requests.Session()
    base_url = "http://10.19.208.31:5000"
    
    try:
        # 1. Acessar página de login
        print("1. Acessando página de login...")
        login_response = session.get(f"{base_url}/login")
        print(f"Status login: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print("❌ Não conseguiu acessar página de login")
            return
        
        # 2. Fazer login
        print("2. Fazendo login...")
        login_data = {
            'username': 'admin',
            'password': '@Ric6109'
        }
        
        login_post = session.post(f"{base_url}/login", data=login_data)
        print(f"Status após login: {login_post.status_code}")
        
        # 3. Acessar página de configurações
        print("3. Acessando página de configurações...")
        config_response = session.get(f"{base_url}/configuracoes/")
        print(f"Status configurações: {config_response.status_code}")
        
        if config_response.status_code == 200:
            print("✅ Conseguiu acessar configurações!")
            
            # 4. Analisar HTML da página
            print("4. Analisando HTML da página...")
            soup = BeautifulSoup(config_response.text, 'html.parser')
            
            # Verificar se existe a aba empresas
            empresas_tab = soup.find('button', {'id': 'empresas-tab'})
            if empresas_tab:
                print("✅ Aba Empresas encontrada no HTML")
                print(f"Atributos: {empresas_tab.attrs}")
            else:
                print("❌ Aba Empresas NÃO encontrada no HTML")
            
            # Verificar se existe o conteúdo da aba empresas
            empresas_content = soup.find('div', {'id': 'empresas'})
            if empresas_content:
                print("✅ Conteúdo da aba Empresas encontrado")
                print(f"Primeiros 200 caracteres: {str(empresas_content)[:200]}...")
            else:
                print("❌ Conteúdo da aba Empresas NÃO encontrado")
            
            # Verificar JavaScript
            scripts = soup.find_all('script')
            bootstrap_js_found = False
            for script in scripts:
                if script.string and 'bootstrap.Tab' in script.string:
                    bootstrap_js_found = True
                    print("✅ JavaScript Bootstrap encontrado")
                    break
            
            if not bootstrap_js_found:
                print("❌ JavaScript Bootstrap NÃO encontrado")
            
            # Salvar HTML para análise
            with open('debug_configuracoes.html', 'w', encoding='utf-8') as f:
                f.write(config_response.text)
            print("📄 HTML salvo em debug_configuracoes.html")
            
        else:
            print(f"❌ Não conseguiu acessar configurações (código {config_response.status_code})")
            
        # 5. Testar acesso direto à página de empresas
        print("5. Testando acesso direto à página de empresas...")
        empresas_response = session.get(f"{base_url}/configuracoes/empresas")
        print(f"Status empresas: {empresas_response.status_code}")
        
        if empresas_response.status_code == 200:
            print("✅ Página de empresas acessível")
            # Salvar para análise
            with open('debug_empresas.html', 'w', encoding='utf-8') as f:
                f.write(empresas_response.text)
            print("📄 HTML da página de empresas salvo em debug_empresas.html")
        else:
            print(f"❌ Página de empresas não acessível (código {empresas_response.status_code})")
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
    
    print("\n" + "=" * 50)
    print("📋 RESULTADO DO TESTE REAL")
    print("=" * 50)

if __name__ == "__main__":
    test_real_browser()
