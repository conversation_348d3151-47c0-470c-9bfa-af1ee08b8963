-- =====================================================
-- IMPLEMENTAÇÃO: HERANÇA DINÂMICA DE JORNADAS
-- Sistema de Controle de Ponto - RLPONTO-WEB
-- Data: 14/07/2025
-- Objetivo: Implementar herança automática e histórico completo
-- =====================================================

-- =====================================================
-- 1. EXPANDIR SISTEMA DE HISTÓRICO
-- =====================================================

-- Adicionar novos tipos de eventos para mudanças de jornada
ALTER TABLE historico_funcionario 
MODIFY COLUMN tipo_evento ENUM(
    'HORA_EXTRA_SOLICITADA',
    'HORA_EXTRA_APROVADA', 
    'HORA_EXTRA_REJEITADA',
    'BANCO_HORAS_CREDITADO',
    'BANCO_HORAS_DEBITADO',
    'AUSENCIA_REGISTRADA',
    'ATRASO_REGISTRADO',
    'JORNADA_ALTERADA',           -- ✅ NOVO: Jornada alterada manualmente
    'JORNADA_HERDADA_EMPRESA',    -- ✅ NOVO: Jornada herdada da empresa
    'JORNADA_HERDADA_CLIENTE',    -- ✅ NOVO: Jornada herdada do cliente (alocação)
    'ALOCACAO_CRIADA',           -- ✅ NOVO: Funcionário alocado para cliente
    'ALOCACAO_FINALIZADA',       -- ✅ NOVO: Alocação finalizada
    'EMPRESA_MUDOU_JORNADA'      -- ✅ NOVO: Empresa mudou jornada padrão
) NOT NULL;

-- =====================================================
-- 2. ADICIONAR CAMPOS DE CONTROLE
-- =====================================================

-- Adicionar campo para rastrear se funcionário usa horário da empresa
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'usa_horario_empresa') = 0,
    'ALTER TABLE funcionarios ADD COLUMN usa_horario_empresa BOOLEAN DEFAULT TRUE COMMENT ''Se TRUE, funcionário herda automaticamente mudanças da empresa''',
    'SELECT ''Campo usa_horario_empresa já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar campo para rastrear última atualização de jornada
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'data_atualizacao_jornada') = 0,
    'ALTER TABLE funcionarios ADD COLUMN data_atualizacao_jornada TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''Data da última atualização de jornada''',
    'SELECT ''Campo data_atualizacao_jornada já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar campo para rastrear quem fez a última alteração
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'jornada_alterada_por') = 0,
    'ALTER TABLE funcionarios ADD COLUMN jornada_alterada_por INT NULL COMMENT ''ID do usuário que fez a última alteração de jornada''',
    'SELECT ''Campo jornada_alterada_por já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- =====================================================
-- 3. CRIAR TABELA DE LOG DE MUDANÇAS DE JORNADA
-- =====================================================

CREATE TABLE IF NOT EXISTS log_mudancas_jornada (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT UNSIGNED NOT NULL,
    jornada_anterior_id INT NULL COMMENT 'ID da jornada anterior',
    jornada_nova_id INT NOT NULL COMMENT 'ID da nova jornada',
    tipo_mudanca ENUM(
        'CADASTRO_INICIAL',      -- Primeira jornada no cadastro
        'ALOCACAO_CLIENTE',      -- Mudança por alocação para cliente
        'EMPRESA_ALTEROU',       -- Empresa alterou jornada padrão
        'ALTERACAO_MANUAL',      -- Alteração manual pelo admin
        'RETORNO_EMPRESA'        -- Retorno para jornada da empresa
    ) NOT NULL,
    motivo TEXT NULL COMMENT 'Motivo da mudança',
    dados_jornada_anterior JSON NULL COMMENT 'Dados da jornada anterior',
    dados_jornada_nova JSON NOT NULL COMMENT 'Dados da nova jornada',
    usuario_responsavel INT NULL COMMENT 'ID do usuário responsável',
    data_mudanca TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
    FOREIGN KEY (jornada_anterior_id) REFERENCES jornadas_trabalho(id) ON DELETE SET NULL,
    FOREIGN KEY (jornada_nova_id) REFERENCES jornadas_trabalho(id) ON DELETE CASCADE,
    
    INDEX idx_funcionario_data (funcionario_id, data_mudanca),
    INDEX idx_tipo_mudanca (tipo_mudanca),
    INDEX idx_data_mudanca (data_mudanca)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT 'Log detalhado de todas as mudanças de jornada dos funcionários';

-- =====================================================
-- 4. TRIGGERS DE ATUALIZAÇÃO AUTOMÁTICA
-- =====================================================

-- Trigger: Atualizar funcionários quando jornada da empresa muda
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_atualizar_jornadas_funcionarios
AFTER UPDATE ON jornadas_trabalho
FOR EACH ROW
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE func_id INT;
    DECLARE func_cursor CURSOR FOR 
        SELECT id FROM funcionarios 
        WHERE empresa_id = NEW.empresa_id 
        AND usa_horario_empresa = TRUE 
        AND (jornada_trabalho_id = OLD.id OR jornada_trabalho_id IS NULL);
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- Se é jornada padrão e foi modificada
    IF NEW.padrao = 1 AND NEW.ativa = 1 AND (
        OLD.seg_qui_entrada != NEW.seg_qui_entrada OR
        OLD.seg_qui_saida != NEW.seg_qui_saida OR
        OLD.sexta_entrada != NEW.sexta_entrada OR
        OLD.sexta_saida != NEW.sexta_saida OR
        OLD.intervalo_inicio != NEW.intervalo_inicio OR
        OLD.intervalo_fim != NEW.intervalo_fim OR
        OLD.tolerancia_entrada_minutos != NEW.tolerancia_entrada_minutos
    ) THEN
        
        -- Atualizar funcionários da empresa que usam horário da empresa
        UPDATE funcionarios 
        SET jornada_trabalho_id = NEW.id,
            data_atualizacao_jornada = CURRENT_TIMESTAMP,
            jornada_alterada_por = NULL  -- Sistema automático
        WHERE empresa_id = NEW.empresa_id 
        AND usa_horario_empresa = TRUE;
        
        -- Registrar mudança no log para cada funcionário afetado
        OPEN func_cursor;
        read_loop: LOOP
            FETCH func_cursor INTO func_id;
            IF done THEN
                LEAVE read_loop;
            END IF;
            
            -- Inserir no log de mudanças
            INSERT INTO log_mudancas_jornada (
                funcionario_id, jornada_anterior_id, jornada_nova_id, 
                tipo_mudanca, motivo, dados_jornada_nova
            ) VALUES (
                func_id, OLD.id, NEW.id, 'EMPRESA_ALTEROU',
                CONCAT('Empresa alterou jornada padrão: ', OLD.nome_jornada, ' → ', NEW.nome_jornada),
                JSON_OBJECT(
                    'nome_jornada', NEW.nome_jornada,
                    'seg_qui_entrada', NEW.seg_qui_entrada,
                    'seg_qui_saida', NEW.seg_qui_saida,
                    'sexta_entrada', NEW.sexta_entrada,
                    'sexta_saida', NEW.sexta_saida,
                    'intervalo_inicio', NEW.intervalo_inicio,
                    'intervalo_fim', NEW.intervalo_fim,
                    'tolerancia_entrada_minutos', NEW.tolerancia_entrada_minutos
                )
            );
            
            -- Registrar no histórico do funcionário
            INSERT INTO historico_funcionario (
                funcionario_id, tipo_evento, data_evento, data_referencia,
                detalhes, status_aprovacao
            ) VALUES (
                func_id, 'EMPRESA_MUDOU_JORNADA', NOW(), CURDATE(),
                CONCAT('Empresa alterou jornada padrão. Nova jornada: ', NEW.nome_jornada, 
                       ' (', NEW.seg_qui_entrada, ' às ', NEW.seg_qui_saida, ')'),
                'NAO_APLICAVEL'
            );
            
        END LOOP;
        CLOSE func_cursor;
        
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- 5. TRIGGER PARA HISTÓRICO DE MUDANÇAS MANUAIS
-- =====================================================

-- Trigger: Registrar mudanças manuais de jornada no histórico
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_historico_mudanca_jornada_funcionario
AFTER UPDATE ON funcionarios
FOR EACH ROW
BEGIN
    -- Se a jornada foi alterada
    IF OLD.jornada_trabalho_id != NEW.jornada_trabalho_id THEN
        
        -- Inserir no log de mudanças
        INSERT INTO log_mudancas_jornada (
            funcionario_id, jornada_anterior_id, jornada_nova_id, 
            tipo_mudanca, motivo, usuario_responsavel
        ) VALUES (
            NEW.id, OLD.jornada_trabalho_id, NEW.jornada_trabalho_id, 
            'ALTERACAO_MANUAL',
            'Jornada alterada manualmente',
            NEW.jornada_alterada_por
        );
        
        -- Inserir no histórico do funcionário
        INSERT INTO historico_funcionario (
            funcionario_id, tipo_evento, data_evento, data_referencia,
            detalhes, status_aprovacao
        ) VALUES (
            NEW.id, 'JORNADA_ALTERADA', NOW(), CURDATE(),
            CONCAT('Jornada alterada manualmente de ID ', COALESCE(OLD.jornada_trabalho_id, 'NULL'), 
                   ' para ID ', COALESCE(NEW.jornada_trabalho_id, 'NULL')),
            'NAO_APLICAVEL'
        );
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- 6. TRIGGER PARA ALOCAÇÕES
-- =====================================================

-- Trigger: Registrar criação de alocação no histórico
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_historico_alocacao_criada
AFTER INSERT ON funcionario_alocacoes
FOR EACH ROW
BEGIN
    -- Registrar no histórico do funcionário
    INSERT INTO historico_funcionario (
        funcionario_id, tipo_evento, data_evento, data_referencia,
        detalhes, status_aprovacao
    ) VALUES (
        NEW.funcionario_id, 'ALOCACAO_CRIADA', NOW(), CURDATE(),
        CONCAT('Funcionário alocado para cliente ID ', NEW.empresa_cliente_id, 
               ' com jornada ID ', NEW.jornada_trabalho_id),
        'NAO_APLICAVEL'
    );
    
    -- Inserir no log de mudanças de jornada
    INSERT INTO log_mudancas_jornada (
        funcionario_id, jornada_anterior_id, jornada_nova_id, 
        tipo_mudanca, motivo
    ) VALUES (
        NEW.funcionario_id, 
        (SELECT jornada_trabalho_id FROM funcionarios WHERE id = NEW.funcionario_id),
        NEW.jornada_trabalho_id, 
        'ALOCACAO_CLIENTE',
        CONCAT('Alocado para cliente - herdando jornada do cliente')
    );
END$$
DELIMITER ;

-- Trigger: Registrar finalização de alocação no histórico
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_historico_alocacao_finalizada
AFTER UPDATE ON funcionario_alocacoes
FOR EACH ROW
BEGIN
    -- Se alocação foi desativada
    IF OLD.ativo = TRUE AND NEW.ativo = FALSE THEN
        -- Registrar no histórico do funcionário
        INSERT INTO historico_funcionario (
            funcionario_id, tipo_evento, data_evento, data_referencia,
            detalhes, status_aprovacao
        ) VALUES (
            NEW.funcionario_id, 'ALOCACAO_FINALIZADA', NOW(), CURDATE(),
            CONCAT('Alocação finalizada para cliente ID ', NEW.empresa_cliente_id),
            'NAO_APLICAVEL'
        );
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- 7. ATUALIZAR FUNCIONÁRIOS EXISTENTES
-- =====================================================

-- Marcar todos os funcionários existentes para usar horário da empresa
UPDATE funcionarios 
SET usa_horario_empresa = TRUE,
    data_atualizacao_jornada = CURRENT_TIMESTAMP
WHERE usa_horario_empresa IS NULL;

-- =====================================================
-- 8. ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para melhorar performance das consultas (com verificação de existência)
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND INDEX_NAME = 'idx_funcionarios_usa_horario_empresa') = 0,
    'CREATE INDEX idx_funcionarios_usa_horario_empresa ON funcionarios(empresa_id, usa_horario_empresa, jornada_trabalho_id)',
    'SELECT ''Índice idx_funcionarios_usa_horario_empresa já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'jornadas_trabalho' AND INDEX_NAME = 'idx_jornadas_trabalho_empresa_padrao') = 0,
    'CREATE INDEX idx_jornadas_trabalho_empresa_padrao ON jornadas_trabalho(empresa_id, padrao, ativa)',
    'SELECT ''Índice idx_jornadas_trabalho_empresa_padrao já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionario_alocacoes' AND INDEX_NAME = 'idx_funcionario_alocacoes_ativo') = 0,
    'CREATE INDEX idx_funcionario_alocacoes_ativo ON funcionario_alocacoes(funcionario_id, ativo, empresa_cliente_id)',
    'SELECT ''Índice idx_funcionario_alocacoes_ativo já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- =====================================================
-- VERIFICAÇÃO FINAL
-- =====================================================

SELECT 'Sistema de Herança Dinâmica de Jornadas implementado com sucesso!' as status;
SELECT 'Triggers criados, histórico expandido, logs implementados' as detalhes;
