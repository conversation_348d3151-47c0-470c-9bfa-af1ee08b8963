# Estrutura da pasta var/www/controle-ponto

```
C:\Users\<USER>\Documents\RLPONTO-WEB\var\www\controle-ponto
├── .env
├── .gitignore
├── adicionar_biometria_mysql.py
├── agent_api.py
├── analise_banco_relatorios_final.md
├── api_startup.log
├── APLICAR_CONFIGURACOES_FINAL.py
├── aplicar_correcao_servidor.sh
├── aplicar_correcao_setor.py
├── aplicar_correcao_setor_agora.py
├── aplicar_correcao_setor_invertido.py
├── aplicar_fix_config.py
├── aplicar_melhorias_views.py
├── aplicar_views_relatorios.py
├── app/
│   ├── api/
│   │   ├── external_routes.py
│   │   └── __init__.py
│   ├── config/
│   │   ├── api_config.py
│   │   └── __init__.py
│   ├── services/
│   │   ├── external_api_service.py
│   │   └── __init__.py
│   └── __init__.py
├── app.py
├── app_backup.py
├── app_banco_horas.py
├── app_biometric_config.py
├── app_configuracoes.py
├── app_controle_jornada.py
├── app_device_manager.py
├── app_empresas.py
├── app_empresa_config.py
├── app_epis.py
├── app_funcionarios.py
├── app_horarios.py
├── app_horarios_trabalho.py
├── app_quality_control.py
├── app_registro_ponto.py
├── app_registro_ponto_backup_20250616.py
├── app_relatorios.py
├── app_relatorios.py.backup_20250606_215158
├── app_status.py
├── atualizacao_banco_registro_ponto.sql
├── atualizacao_banco_registro_ponto_v2.sql
├── backup-build/
├── biometria_service.py
├── biometric_service_simple.py
├── check_empresas_status.py
├── check_fields_simple.py
├── completar_estrutura_sql.py
├── configurar_mysql.sh
├── configurar_mysql_permissoes.sql
├── configurar_permissoes_ip.py
├── cookies.txt
├── correcao_funcionarios.sql
├── CORRECAO_MODAL_EMBASADO_CONTEXT7.md
├── correcao_relatorio_ausentes.sql
├── CORRECAO_SEGURANCA_STATUS.md
├── correcao_setor_relatorios.sql
├── correcao_setor_relatorios_invertido.sql
├── corrigir_aba_empresas.py
├── corrigir_banco_funcionarios.sql
├── corrigir_erro_500_relatorios.py
├── corrigir_erro_estatisticas.py
├── create_emergency_user.py
├── create_empresas_templates.py
├── criar_avatars_reais.py
├── criar_banco_completo.sql
├── criar_estruturas_basicas.py
├── criar_funcionarios_teste.py
├── criar_usuario_status.sql
├── criar_usuario_status_simples.py
├── debug_aba_empresas_real.py
├── debug_auth_real.py
├── debug_configuracoes.py
├── debug_login_issue.py
├── debug_relatorios_detalhado.py
├── deploy_ssh.py
├── diagnostico_empresas.py
├── diagnostico_erro_500_relatorios.md
├── diagnostico_relatorios.py
├── env.example
├── executar_atualizacao_banco.py
├── executar_configuracao_mysql.py
├── executar_limpeza_banco.py
├── executar_sql_registros.py
├── fix_admin_user.py
├── fix_empresas_links.py
├── fix_tabs_definitivo.py
├── fix_template_configuracoes.py
├── GUIA_INICIALIZACAO.md
├── IMPLEMENTACAO_MCP_CONFIGURACOES.md
├── instrucoes_correcao_remota.md
├── INSTRUCOES_SERVIDOR.md
├── limpar_e_corrigir_banco.sql
├── logs/
│   ├── app.log
│   └── ...
├── milestones_log.json
├── pre_commit_check.py
├── quick_test.py
├── REFATORACAO_BIOMETRIA_CONTEXT7.md
├── relatorio_final_correcoes_relatorios.md
├── requirements.txt
├── scripts/
│   └── README.md
├── setup_git_hooks.py
├── setup_security.py
├── sql/
│   ├── configuracoes_mcp.sql
│   └── melhorias_views.sql
├── sql_registros_ponto.sql
├── static/
│   ├── biometria-service.js
│   ├── busca_cep.js
│   ├── configurar_usuarios.js
│   ├── css/
│   │   ├── modal-biometria-fix.css
│   │   └── relatorios.css
│   ├── fotos_funcionarios/
│   │   ├── funcionario_1_20250617T074603.jpg
│   │   ├── funcionario_8_20250617T095959.png
│   │   └── funcionario_9_20250618T091757.png
│   ├── images/
│   │   ├── avatars/
│   │   │   ├── funcionario_feminino_1.jpg
│   │   │   ├── funcionario_feminino_2.jpg
│   │   │   ├── funcionario_masculino_1.jpg
│   │   │   ├── funcionario_masculino_2.jpg
│   │   │   └── funcionario_neutro_1.jpg
│   │   ├── fingerprint-placeholder.png
│   │   └── funcionario_sem_foto.svg
│   ├── js/
│   │   ├── biometria-unified.js
│   │   ├── biometria-zkagent.js
│   │   ├── biometria-zkagent_temp.js
│   │   ├── cadastro-funcionarios.js
│   │   ├── gestao-epis.js
│   │   └── universal-biometria.js
│   ├── style-cadastrar.css
│   ├── style-config-usuarios.css
│   ├── style-index.css
│   ├── style-leitor-biometrico.css
│   ├── style-login.css
│   └── style.css
├── templates/
│   ├── base.html
│   ├── busca_cep.js
│   ├── cadastrar_biometria.html
│   ├── configuracoes/
│   │   ├── biometria.html
│   │   ├── cadastrar_empresa.html
│   │   ├── empresa.html
│   │   ├── empresas.html
│   │   ├── empresa_form.html
│   │   ├── index.html
│   │   ├── index_backup.html
│   │   ├── index_novo.html
│   │   ├── index_old.html
│   │   └── index_professional.html
│   ├── configurar_usuarios.html
│   ├── debug_relatorios.html
│   ├── documentacao_biometria.html
│   ├── empresas/
│   │   ├── cadastrar.html
│   │   ├── cadastrar_jornada.html
│   │   ├── detalhes.html
│   │   └── index.html
│   ├── epis/
│   │   ├── adicionar.html
│   │   └── listar.html
│   ├── erro.html
│   ├── erro_upload.html
│   ├── funcionarios/
│   │   ├── cadastrar.html
│   │   ├── detalhes.html
│   │   └── index.html
│   ├── horarios_trabalho/
│   │   └── index.html
│   ├── index.html
│   ├── leitor_biometrico.html
│   ├── login.html
│   ├── quality_control/
│   │   └── dashboard.html
│   ├── registro_ponto/
│   │   ├── biometrico.html
│   │   └── manual.html
│   ├── relatorios/
│   │   ├── debug.html
│   │   ├── estatisticas.html
│   │   └── pontos.html
│   ├── status/
│   │   └── dashboard.html
│   ├── teste-api.html
│   ├── teste-biometria.html
│   └── trocar_senha_obrigatoria.html
├── teste_api_relatorios.py
├── teste_conexao.py
├── teste_debug_epis.py
├── TESTE_ENDERECO_CORRETO.py
├── TESTE_FINAL_DIRETO.py
├── teste_modal_debug.html
├── teste_modal_final.html
├── teste_responsivo.html
├── tests/
│   └── regression/ (vazio)
├── test_api.log
├── test_api_direct.py
├── test_config_authenticated.py
├── test_config_content.py
├── test_config_page.py
├── test_final_empresas.py
├── test_final_real.py
├── test_real_browser.py
├── universal_biometric_service.py
├── utils/
│   ├── auth.py
│   ├── biometria_detector.py
│   ├── biometria_detector_linux.py
│   ├── config.py
│   ├── database.py
│   ├── helpers.py
│   ├── __init__.py
│   └── __pycache__/
├── verificar_campos_funcionario.py
├── verificar_estrutura_banco.py
├── verificar_funcionarios.py
├── verificar_usuario_status.py
├── version_info.py
├── zk4500_bridge.py
├── zkagent_local_puro.py
├── zkagent_simulador.py
├── ZKFinger_SDK_Java.markdown
└── __pycache__/
```

*Observação: Estrutura detalhada da pasta `var/www/controle-ponto`, incluindo subpastas e arquivos principais. Algumas pastas e arquivos foram resumidos com reticências para facilitar a leitura, mas a estrutura principal está completa.*
