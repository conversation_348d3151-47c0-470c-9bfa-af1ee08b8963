#!/usr/bin/env python3
import paramiko

def fix_template_configuracoes():
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🔧 CORRIGINDO TEMPLATE DE CONFIGURAÇÕES")
        print("=" * 50)
        
        # 1. Verificar se há classes CSS conflitantes
        print("1. Verificando classes CSS que podem estar escondendo a aba...")
        stdin, stdout, stderr = ssh.exec_command('grep -n "display.*none\|visibility.*hidden\|opacity.*0" /var/www/controle-ponto/templates/configuracoes/index.html')
        css_conflicts = stdout.read().decode()
        print("CSS que pode estar escondendo:")
        print(css_conflicts)
        
        # 2. Verificar se a aba empresas tem as classes corretas
        print("\n2. Verificando classes da aba empresas...")
        stdin, stdout, stderr = ssh.exec_command('grep -A 5 -B 5 "id=\\"empresas\\"" /var/www/controle-ponto/templates/configuracoes/index.html')
        empresas_classes = stdout.read().decode()
        print("Classes da aba empresas:")
        print(empresas_classes)
        
        # 3. Corrigir o template adicionando as classes corretas
        print("\n3. Corrigindo classes da aba empresas...")
        
        fix_command = '''
        cd /var/www/controle-ponto/templates/configuracoes
        
        # Backup
        cp index.html index.html.backup_fix_$(date +%Y%m%d_%H%M%S)
        
        # Corrigir a div da aba empresas para ter as classes corretas
        sed -i 's|<div class="tab-pane" id="empresas"|<div class="tab-pane fade" id="empresas"|g' index.html
        
        # Garantir que a primeira aba tenha as classes corretas
        sed -i 's|<div class="tab-pane active show" id="geral"|<div class="tab-pane fade show active" id="geral"|g' index.html
        
        echo "Classes corrigidas!"
        '''
        
        stdin, stdout, stderr = ssh.exec_command(fix_command)
        fix_result = stdout.read().decode()
        print(fix_result)
        
        # 4. Verificar se há JavaScript que pode estar interferindo
        print("\n4. Verificando JavaScript que pode interferir...")
        stdin, stdout, stderr = ssh.exec_command('grep -n "preventDefault\|stopPropagation\|return false" /var/www/controle-ponto/templates/configuracoes/index.html')
        js_interference = stdout.read().decode()
        print("JavaScript que pode interferir:")
        print(js_interference)
        
        # 5. Simplificar o JavaScript para usar apenas Bootstrap nativo
        print("\n5. Simplificando JavaScript...")
        
        simple_js = '''
        cd /var/www/controle-ponto/templates/configuracoes
        
        # Remover JavaScript customizado e usar apenas Bootstrap
        sed -i '/===== SISTEMA DE ABAS BOOTSTRAP NATIVO/,$d' index.html
        
        # Adicionar JavaScript simples
        cat >> index.html << 'EOF'

<script>
// JavaScript Bootstrap simples - apenas inicialização
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Inicializando abas...');
    
    // Deixar o Bootstrap fazer tudo automaticamente
    // Não precisamos de JavaScript customizado
    
    console.log('✅ Abas inicializadas!');
});
</script>
</body>
</html>
EOF
        
        echo "JavaScript simplificado!"
        '''
        
        stdin, stdout, stderr = ssh.exec_command(simple_js)
        js_result = stdout.read().decode()
        print(js_result)
        
        # 6. Verificar se o template está bem formado
        print("\n6. Verificando estrutura final...")
        stdin, stdout, stderr = ssh.exec_command('tail -10 /var/www/controle-ponto/templates/configuracoes/index.html')
        final_structure = stdout.read().decode()
        print("Final do template:")
        print(final_structure)
        
        # 7. Reiniciar serviço
        print("\n7. Reiniciando serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl restart controle-ponto')
        
        # 8. Verificar status
        stdin, stdout, stderr = ssh.exec_command('systemctl is-active controle-ponto')
        status = stdout.read().decode().strip()
        print(f"Status: {status}")
        
        ssh.close()
        
        print("\n" + "=" * 50)
        print("🎉 CORREÇÃO APLICADA!")
        print("=" * 50)
        print("✅ Classes CSS corrigidas")
        print("✅ JavaScript simplificado")
        print("✅ Serviço reiniciado")
        print("\n🔗 TESTE AGORA:")
        print("Acesse: http://************:5000/configuracoes/")
        print("E clique na aba Empresas!")
        
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    fix_template_configuracoes()
