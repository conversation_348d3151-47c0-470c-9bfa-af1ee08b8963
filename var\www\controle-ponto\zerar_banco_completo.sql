-- ========================================
-- SCRIPT PARA ZERAR BANCO COMPLETO RLPONTO-WEB
-- Data: 11/07/2025
-- Versão: 1.0
-- Descrição: Remove TODOS os dados exceto usuários e configurações essenciais
-- ========================================

-- ATENÇÃO: ESTE SCRIPT REMOVE TODOS OS DADOS!
-- Execute apenas se tiver certeza absoluta!
-- Faça backup antes de executar!

USE controle_ponto;

-- ========================================
-- 1. BACKUP DE SEGURANÇA DOS USUÁRIOS
-- ========================================

-- Criar backup dos usuários antes de qualquer operação
CREATE TABLE IF NOT EXISTS usuarios_backup_zerar AS SELECT * FROM usuarios;
CREATE TABLE IF NOT EXISTS permissoes_backup_zerar AS SELECT * FROM permissoes;

-- ========================================
-- 2. DESABILITAR VERIFICAÇÕES
-- ========================================

SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

-- ========================================
-- 3. REMOVER DADOS DE TODAS AS TABELAS
-- ========================================

-- Tabelas de registros e dados operacionais
TRUNCATE TABLE registros_ponto;
TRUNCATE TABLE epis;
TRUNCATE TABLE funcionarios;
TRUNCATE TABLE empresas;
TRUNCATE TABLE clientes;
TRUNCATE TABLE empresa_clientes;
TRUNCATE TABLE funcionario_alocacoes;
TRUNCATE TABLE funcionario_cliente_alocacao;
TRUNCATE TABLE horarios_trabalho;
TRUNCATE TABLE jornadas_trabalho;

-- Tabelas de justificativas e alertas
TRUNCATE TABLE justificativas_ponto;
TRUNCATE TABLE alertas_ponto;
TRUNCATE TABLE historico_alocacoes;
TRUNCATE TABLE historico_alteracoes_ponto;
TRUNCATE TABLE historico_inferencias;

-- Tabelas de logs operacionais (manter estrutura, limpar dados antigos)
DELETE FROM logs_sistema WHERE DATE(data_hora) < DATE_SUB(NOW(), INTERVAL 7 DAYS);
DELETE FROM logs_seguranca WHERE DATE(timestamp) < DATE_SUB(NOW(), INTERVAL 7 DAYS);
DELETE FROM logs_biometria WHERE DATE(timestamp) < DATE_SUB(NOW(), INTERVAL 7 DAYS);

-- Tabelas de dados específicos
TRUNCATE TABLE banco_horas;
TRUNCATE TABLE backup_jornada_funcionarios;
TRUNCATE TABLE cad_empresas;
TRUNCATE TABLE dia_dados;
TRUNCATE TABLE dispositivos_biometricos;
TRUNCATE TABLE empresas_config;
TRUNCATE TABLE log_exclusao_empresas;
TRUNCATE TABLE tentativas_biometria;

-- ========================================
-- 4. REMOVER VIEWS (serão recriadas automaticamente)
-- ========================================

DROP VIEW IF EXISTS v_alertas_pendentes;
DROP VIEW IF EXISTS v_estatisticas_alertas;
DROP VIEW IF EXISTS v_turnos_ativos;
DROP VIEW IF EXISTS vw_analise_pontualidade;
DROP VIEW IF EXISTS vw_clientes_detalhados;
DROP VIEW IF EXISTS vw_empresa_principal;
DROP VIEW IF EXISTS vw_estatisticas_biometria;
DROP VIEW IF EXISTS vw_estatisticas_ponto_setor;
DROP VIEW IF EXISTS vw_estatisticas_pontos;
DROP VIEW IF EXISTS vw_estatisticas_sistema;
DROP VIEW IF EXISTS vw_funcionarios_alocados;
DROP VIEW IF EXISTS vw_funcionarios_biometria;
DROP VIEW IF EXISTS vw_horas_trabalhadas;
DROP VIEW IF EXISTS vw_relatorio_pontos;

-- ========================================
-- 5. RESETAR AUTO_INCREMENT
-- ========================================

ALTER TABLE empresas AUTO_INCREMENT = 1;
ALTER TABLE funcionarios AUTO_INCREMENT = 1;
ALTER TABLE clientes AUTO_INCREMENT = 1;
ALTER TABLE registros_ponto AUTO_INCREMENT = 1;
ALTER TABLE epis AUTO_INCREMENT = 1;
ALTER TABLE horarios_trabalho AUTO_INCREMENT = 1;
ALTER TABLE jornadas_trabalho AUTO_INCREMENT = 1;
ALTER TABLE justificativas_ponto AUTO_INCREMENT = 1;
ALTER TABLE alertas_ponto AUTO_INCREMENT = 1;
ALTER TABLE empresa_clientes AUTO_INCREMENT = 1;
ALTER TABLE funcionario_alocacoes AUTO_INCREMENT = 1;
ALTER TABLE funcionario_cliente_alocacao AUTO_INCREMENT = 1;
ALTER TABLE historico_alocacoes AUTO_INCREMENT = 1;
ALTER TABLE historico_alteracoes_ponto AUTO_INCREMENT = 1;
ALTER TABLE banco_horas AUTO_INCREMENT = 1;
ALTER TABLE dispositivos_biometricos AUTO_INCREMENT = 1;
ALTER TABLE empresas_config AUTO_INCREMENT = 1;
ALTER TABLE log_exclusao_empresas AUTO_INCREMENT = 1;
ALTER TABLE tentativas_biometria AUTO_INCREMENT = 1;

-- ========================================
-- 6. MANTER CONFIGURAÇÕES ESSENCIAIS
-- ========================================

-- Manter configurações do sistema (não limpar configuracoes_sistema)
-- Manter usuários e permissões (não limpar usuarios e permissoes)

-- ========================================
-- 7. REABILITAR VERIFICAÇÕES
-- ========================================

SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 8. VERIFICAÇÃO FINAL
-- ========================================

-- Verificar se usuários foram preservados
SELECT 'USUÁRIOS PRESERVADOS' as status, COUNT(*) as total FROM usuarios;
SELECT 'PERMISSÕES PRESERVADAS' as status, COUNT(*) as total FROM permissoes;
SELECT 'CONFIGURAÇÕES PRESERVADAS' as status, COUNT(*) as total FROM configuracoes_sistema;

-- Verificar se dados foram removidos
SELECT 'EMPRESAS REMOVIDAS' as status, COUNT(*) as total FROM empresas;
SELECT 'FUNCIONÁRIOS REMOVIDOS' as status, COUNT(*) as total FROM funcionarios;
SELECT 'REGISTROS REMOVIDOS' as status, COUNT(*) as total FROM registros_ponto;
SELECT 'CLIENTES REMOVIDOS' as status, COUNT(*) as total FROM clientes;

-- ========================================
-- 9. LOG DA OPERAÇÃO
-- ========================================

INSERT INTO logs_sistema (acao, tabela_afetada, detalhes, data_hora)
VALUES ('ZERAR_BANCO_COMPLETO', 'TODAS_TABELAS', 
        JSON_OBJECT(
            'operacao', 'LIMPEZA_COMPLETA_BANCO',
            'preservado', 'usuarios,permissoes,configuracoes_sistema',
            'removido', 'empresas,funcionarios,registros_ponto,clientes,epis,etc',
            'data_execucao', NOW()
        ), 
        NOW());

-- ========================================
-- SCRIPT CONCLUÍDO
-- ========================================

SELECT '✅ BANCO ZERADO COM SUCESSO!' as resultado;
SELECT '🔒 USUÁRIOS E CONFIGURAÇÕES PRESERVADOS' as status;
SELECT '🗑️ TODOS OS DADOS OPERACIONAIS REMOVIDOS' as operacao;
SELECT '📊 BANCO PRONTO PARA NOVO INÍCIO' as proximo_passo;
