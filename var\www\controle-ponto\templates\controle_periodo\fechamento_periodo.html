{% extends "base.html" %}

{% block title %}{{ titulo }} - RLPONTO-WEB{% endblock %}

{% block extra_css %}
<style>
    /* ========================================
       RLPONTO-WEB VISUAL IDENTITY - v1.1
       FECHAMENTO DE PERÍODO - DESIGN MODERNO
       ======================================== */

    :root {
        --primary-color: #4fbdba;
        --primary-hover: #26a69a;
        --background-color: #f9fafb;
        --card-background: #ffffff;
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --border-color: #e5e7eb;
        --success-color: #10b981;
        --success-bg: #dcfce7;
        --warning-color: #f59e0b;
        --warning-bg: #fef3c7;
        --danger-color: #ef4444;
        --danger-bg: #fee2e2;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
    }

    .main-container {
        background-color: var(--background-color);
        min-height: 100vh;
        padding: var(--spacing-xl);
    }

    .page-header {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        border-radius: 12px;
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        color: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 60%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        transform: rotate(15deg);
    }

    .page-header h1, .page-header p {
        position: relative;
        z-index: 1;
    }

    .status-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-lg);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .status-header {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .status-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .status-icon.success { background: var(--success-color); }
    .status-icon.warning { background: var(--warning-color); }
    .status-icon.danger { background: var(--danger-color); }

    .status-content h3 {
        margin: 0 0 0.25rem 0;
        color: var(--text-primary);
        font-weight: 600;
    }

    .status-content p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .checklist {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .checklist-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md) 0;
        border-bottom: 1px solid #f1f5f9;
    }

    .checklist-item:last-child {
        border-bottom: none;
    }

    .checklist-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        color: white;
        flex-shrink: 0;
    }

    .checklist-icon.check { background: var(--success-color); }
    .checklist-icon.warning { background: var(--warning-color); }
    .checklist-icon.error { background: var(--danger-color); }

    .checklist-text {
        flex: 1;
        color: var(--text-primary);
        font-weight: 500;
    }

    .checklist-detail {
        color: var(--text-secondary);
        font-size: 0.875rem;
        margin-left: 40px;
        margin-top: 0.25rem;
    }

    .periodo-info {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 8px;
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }

    .periodo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
    }

    .periodo-item {
        text-align: center;
    }

    .periodo-label {
        font-size: 0.75rem;
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.25rem;
    }

    .periodo-value {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-primary);
    }

    .estatisticas-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .estatistica-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: var(--spacing-md);
        text-align: center;
    }

    .estatistica-numero {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }

    .estatistica-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .btn-fechamento {
        background: var(--danger-color);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0 auto;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-fechamento:hover:not(:disabled) {
        background: #dc2626;
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-fechamento:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .alert {
        padding: var(--spacing-md);
        border-radius: 8px;
        margin-bottom: var(--spacing-lg);
        border: 1px solid;
    }

    .alert-warning {
        background: var(--warning-bg);
        border-color: var(--warning-color);
        color: #92400e;
    }

    .alert-danger {
        background: var(--danger-bg);
        border-color: var(--danger-color);
        color: #991b1b;
    }

    .alert-success {
        background: var(--success-bg);
        border-color: var(--success-color);
        color: #065f46;
    }

    .progress-container {
        margin: var(--spacing-md) 0;
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--success-color), #059669);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .progress-text {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: 0.5rem;
        text-align: center;
    }

    /* Modal de Confirmação */
    .modal-confirmacao {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: white;
        border-radius: 12px;
        padding: var(--spacing-xl);
        max-width: 500px;
        width: 90%;
        text-align: center;
    }

    .modal-icon {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background: var(--danger-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin: 0 auto var(--spacing-md) auto;
    }

    .modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .modal-text {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xl);
        line-height: 1.5;
    }

    .modal-actions {
        display: flex;
        gap: var(--spacing-md);
        justify-content: center;
    }

    .btn-confirmar {
        background: var(--danger-color);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
    }

    .btn-cancelar {
        background: #6b7280;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
    }

    /* Responsividade */
    @media (max-width: 767px) {
        .main-container {
            padding: var(--spacing-md);
        }
        
        .periodo-grid {
            grid-template-columns: 1fr;
        }
        
        .estatisticas-grid {
            grid-template-columns: 1fr;
        }
        
        .modal-actions {
            flex-direction: column;
        }
    }

    /* Loading state */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Header da Página -->
    <div class="page-header">
        <h1>
            <i class="fas fa-lock me-3"></i>
            {{ titulo }}
        </h1>
        <p>Finalize o período de apuração e processe os dados</p>
    </div>

    <!-- Informações do Período -->
    <div class="status-card">
        <div class="status-header">
            <div class="status-icon {{ 'success' if periodo.dias_restantes <= 0 else 'warning' }}">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="status-content">
                <h3>Período de Apuração</h3>
                <p>{{ periodo.inicio.strftime('%d/%m/%Y') }} a {{ periodo.fim.strftime('%d/%m/%Y') }}</p>
            </div>
        </div>
        
        <div class="periodo-info">
            <div class="periodo-grid">
                <div class="periodo-item">
                    <div class="periodo-label">Início</div>
                    <div class="periodo-value">{{ periodo.inicio.strftime('%d/%m/%Y') }}</div>
                </div>
                <div class="periodo-item">
                    <div class="periodo-label">Fim</div>
                    <div class="periodo-value">{{ periodo.fim.strftime('%d/%m/%Y') }}</div>
                </div>
                <div class="periodo-item">
                    <div class="periodo-label">Dias Restantes</div>
                    <div class="periodo-value">{{ periodo.dias_restantes }}</div>
                </div>
                <div class="periodo-item">
                    <div class="periodo-label">Progresso</div>
                    <div class="periodo-value">{{ periodo.percentual_concluido }}%</div>
                </div>
            </div>
            
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ periodo.percentual_concluido }}%"></div>
                </div>
                <div class="progress-text">
                    {{ periodo.dias_decorridos }} de {{ periodo.dias_totais }} dias concluídos
                </div>
            </div>
        </div>
    </div>

    <!-- Estatísticas do Período -->
    <div class="status-card">
        <h3>
            <i class="fas fa-chart-bar me-2"></i>
            Estatísticas do Período
        </h3>
        <div class="estatisticas-grid">
            <div class="estatistica-card">
                <div class="estatistica-numero">{{ estatisticas.funcionarios_ativos }}</div>
                <div class="estatistica-label">Funcionários Ativos</div>
            </div>
            <div class="estatistica-card">
                <div class="estatistica-numero">{{ estatisticas.dias_registrados }}</div>
                <div class="estatistica-label">Dias Registrados</div>
            </div>
            <div class="estatistica-card">
                <div class="estatistica-numero">{{ estatisticas.horas_extras_formatadas }}</div>
                <div class="estatistica-label">Horas Extras</div>
            </div>
            <div class="estatistica-card">
                <div class="estatistica-numero">{{ estatisticas.atrasos_formatados }}</div>
                <div class="estatistica-label">Atrasos</div>
            </div>
        </div>
    </div>

    <!-- Checklist de Pré-requisitos -->
    <div class="status-card">
        <h3>
            <i class="fas fa-clipboard-check me-2"></i>
            Pré-requisitos para Fechamento
        </h3>
        
        <ul class="checklist">
            <li class="checklist-item">
                <div class="checklist-icon {{ 'check' if periodo.dias_restantes <= 0 else 'warning' }}">
                    <i class="fas fa-{{ 'check' if periodo.dias_restantes <= 0 else 'clock' }}"></i>
                </div>
                <div class="checklist-text">
                    Período Encerrado
                    {% if periodo.dias_restantes > 0 %}
                    <div class="checklist-detail">
                        Aguardando {{ periodo.dias_restantes }} dias para encerramento
                    </div>
                    {% endif %}
                </div>
            </li>
            
            <li class="checklist-item">
                <div class="checklist-icon {{ 'check' if decisoes_pendentes == 0 else 'error' }}">
                    <i class="fas fa-{{ 'check' if decisoes_pendentes == 0 else 'exclamation-triangle' }}"></i>
                </div>
                <div class="checklist-text">
                    Decisões Classificadas
                    {% if decisoes_pendentes > 0 %}
                    <div class="checklist-detail">
                        {{ decisoes_pendentes }} decisões pendentes de classificação
                    </div>
                    {% endif %}
                </div>
            </li>
            
            <li class="checklist-item">
                <div class="checklist-icon check">
                    <i class="fas fa-check"></i>
                </div>
                <div class="checklist-text">
                    Dados Consistentes
                    <div class="checklist-detail">
                        Todos os registros estão íntegros
                    </div>
                </div>
            </li>
        </ul>
    </div>

    <!-- Alertas e Status -->
    {% if not pode_fechar %}
        {% if periodo.dias_restantes > 0 %}
        <div class="alert alert-warning">
            <i class="fas fa-clock me-2"></i>
            <strong>Período ainda ativo:</strong> O fechamento só pode ser realizado após o dia {{ periodo.fim.strftime('%d/%m/%Y') }}.
        </div>
        {% endif %}
        
        {% if decisoes_pendentes > 0 %}
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Decisões pendentes:</strong> Existem {{ decisoes_pendentes }} decisões que precisam ser classificadas antes do fechamento.
            <a href="{{ url_for('controle_periodo.decisoes_diarias') }}" class="btn btn-sm btn-outline-danger ms-2">
                Classificar Agora
            </a>
        </div>
        {% endif %}
    {% else %}
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            <strong>Pronto para fechamento:</strong> Todos os pré-requisitos foram atendidos. O período pode ser fechado.
        </div>
    {% endif %}

    <!-- Botão de Fechamento -->
    <div class="text-center">
        <button class="btn-fechamento" 
                onclick="confirmarFechamento()" 
                {{ 'disabled' if not pode_fechar else '' }}
                id="btnFechamento">
            <i class="fas fa-lock"></i>
            <span id="btnTexto">
                {% if pode_fechar %}
                    Executar Fechamento do Período
                {% else %}
                    Fechamento Indisponível
                {% endif %}
            </span>
        </button>
        
        {% if not pode_fechar %}
        <p class="text-muted mt-3">
            <small>Complete todos os pré-requisitos para habilitar o fechamento</small>
        </p>
        {% endif %}
    </div>
</div>

<!-- Modal de Confirmação -->
<div class="modal-confirmacao" id="modalConfirmacao">
    <div class="modal-content">
        <div class="modal-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h3 class="modal-title">Confirmar Fechamento do Período</h3>
        <p class="modal-text">
            Esta ação irá finalizar o período de apuração de <strong>{{ periodo.inicio.strftime('%d/%m/%Y') }}</strong> 
            a <strong>{{ periodo.fim.strftime('%d/%m/%Y') }}</strong>.<br><br>
            Após o fechamento, não será possível fazer alterações nos registros deste período.
            <br><br>
            <strong>Deseja continuar?</strong>
        </p>
        <div class="modal-actions">
            <button class="btn-confirmar" onclick="executarFechamento()">
                <i class="fas fa-check me-2"></i>
                Sim, Fechar Período
            </button>
            <button class="btn-cancelar" onclick="fecharModal()">
                <i class="fas fa-times me-2"></i>
                Cancelar
            </button>
        </div>
    </div>
</div>

<script>
function confirmarFechamento() {
    document.getElementById('modalConfirmacao').style.display = 'flex';
}

function fecharModal() {
    document.getElementById('modalConfirmacao').style.display = 'none';
}

function executarFechamento() {
    const btnFechamento = document.getElementById('btnFechamento');
    const btnTexto = document.getElementById('btnTexto');
    
    // Mostrar loading
    btnFechamento.disabled = true;
    btnTexto.innerHTML = '<span class="spinner"></span> Processando Fechamento...';
    
    // Fechar modal
    fecharModal();
    
    // Executar fechamento
    fetch('{{ url_for("controle_periodo.executar_fechamento") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            confirmar: true
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Sucesso
            btnTexto.innerHTML = '<i class="fas fa-check"></i> Período Fechado com Sucesso!';
            btnFechamento.style.background = '#10b981';
            
            // Mostrar mensagem de sucesso
            alert(`Fechamento concluído!\n\n${data.message}`);
            
            // Redirecionar para dashboard
            setTimeout(() => {
                window.location.href = '{{ url_for("controle_periodo.dashboard") }}';
            }, 2000);
            
        } else {
            // Erro
            alert('Erro: ' + data.message);
            btnFechamento.disabled = false;
            btnTexto.innerHTML = '<i class="fas fa-lock"></i> Executar Fechamento do Período';
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao processar fechamento');
        btnFechamento.disabled = false;
        btnTexto.innerHTML = '<i class="fas fa-lock"></i> Executar Fechamento do Período';
    });
}

// Fechar modal com ESC
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        fecharModal();
    }
});

// Fechar modal clicando fora
document.getElementById('modalConfirmacao').addEventListener('click', function(e) {
    if (e.target === this) {
        fecharModal();
    }
});

// Animação da barra de progresso
document.addEventListener('DOMContentLoaded', function() {
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
        const targetWidth = progressFill.style.width;
        progressFill.style.width = '0%';
        setTimeout(() => {
            progressFill.style.width = targetWidth;
        }, 500);
    }
});
</script>
{% endblock %}
