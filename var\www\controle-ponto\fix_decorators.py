#!/usr/bin/env python3
"""
Script para remover decoradores @require_admin do arquivo app_empresa_principal.py
"""

import re

def fix_decorators():
    """Remove decoradores @require_admin"""
    
    # Ler o arquivo
    with open('app_empresa_principal.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remover linhas com @require_admin
    lines = content.split('\n')
    fixed_lines = []
    
    for line in lines:
        if '@require_admin' not in line:
            fixed_lines.append(line)
    
    # Escrever o arquivo corrigido
    with open('app_empresa_principal.py', 'w', encoding='utf-8') as f:
        f.write('\n'.join(fixed_lines))
    
    print("Decoradores @require_admin removidos com sucesso!")

if __name__ == '__main__':
    fix_decorators()
