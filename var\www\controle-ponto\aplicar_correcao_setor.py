#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para registrar a correção da exibição de setor na página de registro de ponto manual.
"""

from utils.database import get_db_connection
from pymysql.cursors import DictCursor
import logging
from datetime import datetime

# Configuração do logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def registrar_correcao_logs():
    """
    Registra a correção realizada no sistema de logs.
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        timestamp = datetime.now()
        
        # Verificar estrutura da tabela logs_sistema
        cursor.execute("DESCRIBE logs_sistema")
        colunas = cursor.fetchall()
        nomes_colunas = [coluna['Field'] for coluna in colunas]
        logger.info(f"Colunas da tabela logs_sistema: {', '.join(nomes_colunas)}")
        
        # Registrar a correção nos logs do sistema usando as colunas corretas
        if 'tipo_log' in nomes_colunas:
            cursor.execute("""
                INSERT INTO logs_sistema 
                (tipo_log, descricao, data_hora, usuario_id, detalhes) 
                VALUES (%s, %s, %s, %s, %s)
            """, (
                'CORRECAO',
                'Correção da exibição do setor dos funcionários na página de registro ponto manual',
                timestamp,
                1,  # ID do usuário admin ou sistema
                'Foi corrigida a consulta SQL em app_registro_ponto.py para priorizar o campo setor_obra em vez de setor.'
            ))
        else:
            # Fallback caso os campos sejam diferentes
            campos = ", ".join(nomes_colunas)
            logger.warning(f"Estrutura da tabela logs_sistema não é a esperada. Colunas: {campos}")
            placeholder_values = ", ".join(["%s"] * len(nomes_colunas))
            
            # Criar valores adaptados ao número de colunas
            valores = []
            for coluna in nomes_colunas:
                if 'tipo' in coluna.lower() or coluna.lower() == 'acao':
                    valores.append('CORRECAO')
                elif 'desc' in coluna.lower() or 'mensagem' in coluna.lower():
                    valores.append('Correção da exibição do setor dos funcionários na página de registro ponto manual')
                elif 'data' in coluna.lower() or 'hora' in coluna.lower():
                    valores.append(timestamp)
                elif 'usuario' in coluna.lower():
                    valores.append(1)  # ID do usuário admin ou sistema - deve ser um inteiro
                elif 'detalhes' in coluna.lower() or 'detail' in coluna.lower():
                    import json
                    detalhes_json = json.dumps({
                        "mensagem": "Foi corrigida a consulta SQL em app_registro_ponto.py para priorizar o campo setor_obra em vez de setor.",
                        "arquivo": "app_registro_ponto.py",
                        "data_correcao": timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                        "autor": "sistema"
                    })
                    valores.append(detalhes_json)
                else:
                    valores.append(None)
            
            # Registrar com os campos disponíveis
            query = f"INSERT INTO logs_sistema ({campos}) VALUES ({placeholder_values})"
            cursor.execute(query, valores)
        
        conn.commit()
        logger.info("✅ Correção registrada com sucesso nos logs do sistema")
        
        # Mostrar os funcionários afetados pela correção
        cursor.execute("""
            SELECT 
                id, nome_completo, 
                setor as setor_antigo, 
                setor_obra as setor_novo
            FROM funcionarios
            WHERE ativo = TRUE
        """)
        
        funcionarios = cursor.fetchall()
        logger.info("=== FUNCIONÁRIOS AFETADOS PELA CORREÇÃO ===")
        logger.info("ID | Nome Completo | Setor Antigo | Setor Novo")
        
        for func in funcionarios:
            logger.info(f"{func['id']} | {func['nome_completo']} | {func['setor_antigo'] or 'NULL'} | {func['setor_novo'] or 'NULL'}")
        
        conn.close()
        logger.info("✅ Script de correção executado com sucesso!")
        
    except Exception as e:
        logger.error(f"❌ Erro ao registrar correção: {str(e)}")
        logger.error("Detalhes:", exc_info=True)

if __name__ == "__main__":
    registrar_correcao_logs() 