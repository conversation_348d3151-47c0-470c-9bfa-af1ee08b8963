#!/usr/bin/env python3
import pymysql
import sys

DB_CONFIG = {
    'host': 'localhost',
    'user': 'cavalcrod', 
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

def main():
    print("🔧 CORREÇÃO DAS CONFIGURAÇÕES")
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Adicionar coluna editavel
        try:
            cursor.execute("ALTER TABLE configuracoes_sistema ADD COLUMN editavel BOOLEAN DEFAULT TRUE")
            print("✅ Coluna editavel adicionada")
        except:
            print("ℹ️ Coluna editavel já existe")
        
        # Inserir configurações essenciais
        configs = [
            ('tema_sistema', 'claro', 'string', 'interface', 'Tema do sistema', 1),
            ('mostrar_fotos_funcionarios', 'true', 'boolean', 'interface', 'Mostrar fotos', 1),
            ('registros_por_pagina', '50', 'integer', 'interface', 'Registros por página', 1),
            ('versao_sistema', '1.0', 'string', 'tecnico', 'Versão', 0)
        ]
        
        for chave, valor, tipo, categoria, descricao, editavel in configs:
            cursor.execute("SELECT id FROM configuracoes_sistema WHERE chave = %s", (chave,))
            if not cursor.fetchone():
                cursor.execute("""
                    INSERT INTO configuracoes_sistema (chave, valor, tipo, categoria, descricao, editavel)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (chave, valor, tipo, categoria, descricao, editavel))
        
        conn.commit()
        
        # Verificar total
        cursor.execute("SELECT COUNT(*) FROM configuracoes_sistema")
        total = cursor.fetchone()[0]
        print(f"📊 Total: {total} configurações")
        
        conn.close()
        print("✅ CORREÇÃO CONCLUÍDA!")
        
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    main() 