#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DEBUG DAS FOTOS DOS FUNCIONÁRIOS
===============================
"""

import os
import sys
sys.path.append('var/www/controle-ponto')

from utils.database import get_db_connection
from pymysql.cursors import DictCursor

def main():
    print("🔍 DEBUG DAS FOTOS DOS FUNCIONÁRIOS")
    print("=" * 60)
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # 1. Verificar dados de fotos no banco
        print("\n📋 DADOS DAS FOTOS NO BANCO:")
        cursor.execute("""
            SELECT id, nome_completo, foto_3x4, sexo 
            FROM funcionarios 
            WHERE ativo = TRUE 
            ORDER BY nome_completo 
            LIMIT 10
        """)
        
        funcionarios = cursor.fetchall()
        for func in funcionarios:
            print(f"  ID: {func['id']:2d} | {func['nome_completo']:25s} | Foto: {func['foto_3x4']}")
        
        # 2. Verificar caminhos únicos
        print("\n📂 CAMINHOS DE FOTO ÚNICOS:")
        cursor.execute("""
            SELECT DISTINCT foto_3x4, COUNT(*) as qtd
            FROM funcionarios 
            WHERE ativo = TRUE
            GROUP BY foto_3x4
            ORDER BY qtd DESC
        """)
        
        caminhos = cursor.fetchall()
        for caminho in caminhos:
            print(f"  {caminho['foto_3x4']:50s} | Qtd: {caminho['qtd']}")
        
        # 3. Verificar se arquivos existem
        print("\n📁 VERIFICANDO SE ARQUIVOS EXISTEM:")
        base_path = "var/www/controle-ponto"
        
        for caminho in caminhos:
            foto_path = caminho['foto_3x4']
            if foto_path and foto_path.startswith('/static/'):
                # Converter caminho web para caminho do sistema
                arquivo_local = foto_path.replace('/static/', f'{base_path}/static/')
                existe = os.path.exists(arquivo_local)
                print(f"  {foto_path:50s} | Existe: {existe}")
                if not existe:
                    print(f"    ❌ Caminho esperado: {arquivo_local}")
        
        # 4. Verificar estrutura do diretório static
        print("\n📂 ESTRUTURA DO DIRETÓRIO STATIC:")
        static_dir = f"{base_path}/static"
        if os.path.exists(static_dir):
            for root, dirs, files in os.walk(static_dir):
                level = root.replace(static_dir, '').count(os.sep)
                indent = '  ' * level
                print(f"{indent}{os.path.basename(root)}/")
                sub_indent = '  ' * (level + 1)
                for file in files[:10]:  # Limitar a 10 arquivos por diretório
                    print(f"{sub_indent}{file}")
                if len(files) > 10:
                    print(f"{sub_indent}... e mais {len(files) - 10} arquivos")
        else:
            print(f"  ❌ Diretório {static_dir} não existe!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ ERRO: {str(e)}")

if __name__ == "__main__":
    main() 