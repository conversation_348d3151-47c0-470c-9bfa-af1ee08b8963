#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar as funções específicas do módulo empresa_principal
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from app_empresa_principal import get_empresa_principal, get_clientes_da_empresa_principal, get_empresas_disponiveis_para_cliente
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def testar_funcoes():
    """Testa todas as funções relacionadas a clientes"""
    try:
        print("=== TESTE DAS FUNÇÕES DE CLIENTES ===")
        
        # 1. Testar get_empresa_principal
        print("\n1. Testando get_empresa_principal()...")
        empresa_principal = get_empresa_principal()
        if empresa_principal:
            print(f"✅ Empresa principal: {empresa_principal['razao_social']}")
            print(f"   ID: {empresa_principal['id']}")
            print(f"   Total clientes: {empresa_principal.get('total_clientes', 'N/A')}")
        else:
            print("❌ Nenhuma empresa principal encontrada")
            return
        
        # 2. Testar get_clientes_da_empresa_principal
        print("\n2. Testando get_clientes_da_empresa_principal()...")
        clientes = get_clientes_da_empresa_principal()
        if clientes:
            print(f"✅ Encontrados {len(clientes)} clientes:")
            for cliente in clientes:
                print(f"   - {cliente['razao_social']}")
        else:
            print("✅ Nenhum cliente encontrado (esperado)")
        
        # 3. Testar get_empresas_disponiveis_para_cliente
        print("\n3. Testando get_empresas_disponiveis_para_cliente()...")
        empresas_disponiveis = get_empresas_disponiveis_para_cliente()
        if empresas_disponiveis:
            print(f"✅ Encontradas {len(empresas_disponiveis)} empresas disponíveis:")
            for empresa in empresas_disponiveis:
                print(f"   - {empresa['razao_social']} (ID: {empresa['id']}, Já cliente: {empresa.get('ja_e_cliente', 'N/A')})")
        else:
            print("❌ Nenhuma empresa disponível encontrada")
        
        # 4. Simular renderização do template
        print("\n4. Simulando dados para template...")
        template_data = {
            'empresa_principal': empresa_principal,
            'clientes': clientes,
            'empresas_disponiveis': empresas_disponiveis
        }
        
        print("Dados que seriam passados para o template:")
        print(f"   - empresa_principal: {'OK' if template_data['empresa_principal'] else 'NONE'}")
        print(f"   - clientes: {len(template_data['clientes']) if template_data['clientes'] else 0} itens")
        print(f"   - empresas_disponiveis: {len(template_data['empresas_disponiveis']) if template_data['empresas_disponiveis'] else 0} itens")
        
        # 5. Verificar se há problemas de encoding
        print("\n5. Verificando encoding...")
        if empresas_disponiveis:
            for empresa in empresas_disponiveis:
                try:
                    # Tentar acessar todos os campos
                    nome = empresa['razao_social']
                    fantasia = empresa.get('nome_fantasia', '')
                    cnpj = empresa.get('cnpj', '')
                    print(f"   ✅ {nome} - encoding OK")
                except Exception as e:
                    print(f"   ❌ Erro de encoding em {empresa.get('id', 'ID_UNKNOWN')}: {e}")
        
        print("\n=== TESTE CONCLUÍDO ===")
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == '__main__':
    testar_funcoes()
