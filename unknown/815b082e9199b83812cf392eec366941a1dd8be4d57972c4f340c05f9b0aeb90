{% extends "base.html" %}

{% block title %}{{ titulo }} - <PERSON><PERSON>{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.css">
<style>
    .filtros-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
    }
    
    .filtros-card h5 {
        color: white;
        margin-bottom: 20px;
        font-weight: 600;
    }
    
    .form-control-white {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: #2c3e50;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .form-control-white:focus {
        background: white;
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .btn-filtrar {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        transition: all 0.3s ease;
    }
    
    .btn-filtrar:hover {
        background-color: #218838;
        border-color: #1e7e34;
        color: white;
        transform: none;
        box-shadow: none;
    }
    
    .btn-exportar {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }
    
    .btn-exportar:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
        color: white;
    }
    
    .resultados-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 25px;
    }
    
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .table-custom {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }
    
    .table-custom thead {
        background: linear-gradient(135deg, #495057 0%, #343a40 100%);
        color: white;
    }
    
    .table-custom thead th {
        border: none;
        padding: 15px 12px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85em;
        letter-spacing: 0.5px;
    }
    
    .table-custom tbody tr {
        border-bottom: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .table-custom tbody tr:hover {
        background-color: #f8f9fa;
        transform: scale(1.001);
    }
    
    .table-custom tbody td {
        padding: 12px;
        vertical-align: middle;
        border: none;
    }
    
    .badge-tipo {
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.8em;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .badge-entrada-manha {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }
    
    .badge-saida-almoco {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: #212529;
    }
    
    .badge-entrada-tarde {
        background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
        color: white;
    }
    
    .badge-saida {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }
    
    .badge-metodo {
        padding: 4px 8px;
        border-radius: 10px;
        font-size: 0.75em;
        font-weight: 500;
    }
    
    .badge-biometrico {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .badge-manual {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .stats-row {
        margin-bottom: 25px;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border-left: 4px solid;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    }
    
    .stat-card.total {
        border-left-color: #007bff;
    }
    
    .stat-card.biometrico {
        border-left-color: #28a745;
    }
    
    .stat-card.manual {
        border-left-color: #ffc107;
    }
    
    .stat-card.funcionarios {
        border-left-color: #17a2b8;
    }
    
    .stat-number {
        font-size: 2.5em;
        font-weight: 300;
        color: #2c3e50;
        margin: 0;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9em;
        margin-top: 5px;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 40px;
    }
    
    .loading-spinner .spinner-border {
        width: 3rem;
        height: 3rem;
        color: #007bff;
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .nav-tabs {
        border-bottom: 2px solid #dee2e6;
        margin-bottom: 20px;
    }
    
    .nav-tabs .nav-link {
        border: none;
        color: #495057;
        font-weight: 600;
        padding: 12px 20px;
        margin-right: 10px;
        transition: all 0.3s ease;
    }
    
    .nav-tabs .nav-link:hover {
        border: none;
        color: #007bff;
    }
    
    .nav-tabs .nav-link.active {
        border: none;
        color: #007bff;
        position: relative;
    }
    
    .nav-tabs .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 2px;
        background: #007bff;
    }
    
    .tab-content {
        padding: 20px 0;
    }
    
    .resumo-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .resumo-card h6 {
        color: #495057;
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    .resumo-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .resumo-info:last-child {
        border-bottom: none;
    }
    
    .resumo-label {
        color: #6c757d;
        font-size: 0.9em;
    }
    
    .resumo-valor {
        font-weight: 600;
        color: #212529;
    }
    
    .progress {
        height: 8px;
        border-radius: 4px;
        margin-top: 5px;
    }
    
    .progress-bar-pontualidade {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }
    
    .progress-bar-atrasos {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }
    
    .btn-buscar {
        background-color: #28a745;
        border: none;
        color: white;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
    }
    
    .btn-buscar:hover {
        background-color: #218838;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    }
    
    .btn-buscar:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Filtros -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filtros-card">
                <h5 class="mb-4"><i class="fas fa-filter"></i> Filtros de Busca</h5>
                <form id="filtrosForm" method="POST" class="row align-items-center">
                    <div class="col-md-3">
                        <label for="funcionario_id" class="form-label">Funcionário</label>
                        <select class="form-control form-control-white" id="funcionario_id" name="funcionario_id">
                            <option value="">Todos os funcionários</option>
                            {% for f in funcionarios %}
                            <option value="{{ f.id }}">{{ f.nome_completo }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="setor" class="form-label">Setor</label>
                        <select class="form-control form-control-white" id="setor" name="setor">
                            <option value="">Todos os setores</option>
                            {% for f in funcionarios|unique(attribute='setor') %}
                            <option value="{{ f.setor }}">{{ f.setor }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="tipo_registro" class="form-label">Tipo de Registro</label>
                        <select class="form-control form-control-white" id="tipo_registro" name="tipo_registro">
                            <option value="">Todos os tipos</option>
                            {% for tipo in tipos_registro %}
                            <option value="{{ tipo.value }}">{{ tipo.text }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="metodo_registro" class="form-label">Método de Registro</label>
                        <select class="form-control form-control-white" id="metodo_registro" name="metodo_registro">
                            <option value="">Todos os métodos</option>
                            {% for metodo in metodos_registro %}
                            <option value="{{ metodo.value }}">{{ metodo.text }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="data_inicio" class="form-label">Data Inicial</label>
                        <input type="date" class="form-control form-control-white" id="data_inicio" name="data_inicio" required>
                    </div>
                    <div class="col-md-2">
                        <label for="data_fim" class="form-label">Data Final</label>
                        <input type="date" class="form-control form-control-white" id="data_fim" name="data_fim" required>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-buscar w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Abas -->
    <ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" id="registros-tab" data-toggle="tab" href="#registros" role="tab">
                <i class="fas fa-list"></i> Registros
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="horas-tab" data-toggle="tab" href="#horas" role="tab">
                <i class="fas fa-clock"></i> Horas Trabalhadas
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="pontualidade-tab" data-toggle="tab" href="#pontualidade" role="tab">
                <i class="fas fa-chart-line"></i> Pontualidade
            </a>
        </li>
    </ul>

    <!-- Conteúdo das Abas -->
    <div class="tab-content">
        <!-- Aba de Registros -->
        <div class="tab-pane fade show active" id="registros" role="tabpanel">
            <!-- Cards de Estatísticas -->
            <div class="row stats-row">
                <div class="col-md-3">
                    <div class="stat-card total">
                        <h6>Total de Registros</h6>
                        <h3 id="statTotal">0</h3>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card biometrico">
                        <h6>Registros Biométricos</h6>
                        <h3 id="statBiometrico">0</h3>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card manual">
                        <h6>Registros Manuais</h6>
                        <h3 id="statManual">0</h3>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card funcionarios">
                        <h6>Funcionários Ativos</h6>
                        <h3 id="statFuncionarios">0</h3>
                    </div>
                </div>
            </div>

            <!-- Tabela de Resultados -->
            <div class="resultados-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5>Registros de Ponto</h5>
                    <button class="btn btn-exportar" onclick="exportarCSV()">
                        <i class="fas fa-download"></i> Exportar CSV
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-custom" id="tabelaRegistros">
                        <thead>
                            <tr>
                                <th>Funcionário</th>
                                <th>Data/Hora</th>
                                <th>Tipo</th>
                                <th>Método</th>
                                <th>Setor</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Preenchido via JavaScript -->
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span class="text-muted">Mostrando <span id="registrosExibidos">0</span> de <span id="totalRegistrosGeral">0</span> registros</span>
                    </div>
                    <nav>
                        <ul class="pagination" id="paginacao">
                            <!-- Preenchido via JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Aba de Horas Trabalhadas -->
        <div class="tab-pane fade" id="horas" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="chart-container">
                        <canvas id="graficoHoras"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="resumo-card">
                        <h6>Resumo de Horas</h6>
                        <div class="resumo-info">
                            <span class="resumo-label">Total de Horas (Mês)</span>
                            <span class="resumo-valor" id="totalHorasMes">0h</span>
                        </div>
                        <div class="resumo-info">
                            <span class="resumo-label">Média Diária</span>
                            <span class="resumo-valor" id="mediaHorasDia">0h</span>
                        </div>
                        <div class="resumo-info">
                            <span class="resumo-label">Dias Trabalhados</span>
                            <span class="resumo-valor" id="diasTrabalhados">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Aba de Pontualidade -->
        <div class="tab-pane fade" id="pontualidade" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="chart-container">
                        <canvas id="graficoPontualidade"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="resumo-card">
                        <h6>Análise de Pontualidade</h6>
                        <div class="resumo-info">
                            <span class="resumo-label">Taxa de Pontualidade</span>
                            <div class="w-100">
                                <div class="d-flex justify-content-between">
                                    <span class="resumo-valor" id="taxaPontualidade">0%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-pontualidade" id="progressPontualidade" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="resumo-info">
                            <span class="resumo-label">Atrasos (Mês)</span>
                            <div class="w-100">
                                <div class="d-flex justify-content-between">
                                    <span class="resumo-valor" id="totalAtrasos">0</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-atrasos" id="progressAtrasos" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
<script>
let dadosAtuais = [];
let paginaAtual = 1;
let registrosPorPagina = 20;
let graficoHoras = null;
let graficoPontualidade = null;

// Função para mostrar loading
function mostrarLoading() {
    const tbody = document.querySelector('#tabelaRegistros tbody');
    tbody.innerHTML = `
        <tr>
            <td colspan="6" class="text-center">
                <div class="d-flex justify-content-center align-items-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <span class="ms-2">Carregando registros...</span>
                </div>
            </td>
        </tr>
    `;
}

// Função para mostrar mensagem de erro
function mostrarErro(mensagem) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        <strong>Erro!</strong> ${mensagem}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Remover alerta após 5 segundos
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Função para mostrar mensagem de sucesso
function mostrarSucesso(mensagem) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        <strong>Sucesso!</strong> ${mensagem}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Remover alerta após 3 segundos
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// Buscar registros com filtros
async function buscarRegistros(pagina = 1) {
    try {
        // Mostrar loading
        mostrarLoading();
        
        // Obter dados do formulário
        const form = document.getElementById('filtrosForm');
        const formData = new FormData(form);
        
        // Converter FormData para objeto
        const filtros = {
            funcionario_id: formData.get('funcionario_id') ? parseInt(formData.get('funcionario_id')) : null,
            setor: formData.get('setor') || null,
            tipo_registro: formData.get('tipo_registro') || null,
            metodo_registro: formData.get('metodo_registro') || null,
            data_inicio: formData.get('data_inicio') || null,
            data_fim: formData.get('data_fim') || null,
            pagina: pagina,
            registros_por_pagina: registrosPorPagina
        };

        // Validar datas
        if (filtros.data_inicio && filtros.data_fim && new Date(filtros.data_fim) < new Date(filtros.data_inicio)) {
            throw new Error('A data final não pode ser menor que a data inicial');
        }

        // Buscar registros
        const response = await fetch('/relatorios/api/buscar-registros', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filtros)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Erro ao buscar registros');
        }

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message || 'Erro ao processar registros');
        }

        // Armazenar dados atuais para exportação
        dadosAtuais = data.registros;
        
        // Atualizar tabela e paginação
        atualizarTabela(data.registros);
        atualizarPaginacao(data.total_paginas, data.pagina_atual);
        atualizarEstatisticas(data.estatisticas);

        // Atualizar gráficos se disponível
        if (data.graficos) {
            atualizarGraficos(data.graficos);
        }
    } catch (error) {
        mostrarErro(error.message);
    } finally {
        // Remover loading
        const tbody = document.querySelector('#tabelaRegistros tbody');
        tbody.classList.remove('loading');
    }
}

// Inicializar gráficos
function inicializarGraficos() {
    const ctxHoras = document.getElementById('graficoHoras').getContext('2d');
    graficoHoras = new Chart(ctxHoras, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Horas Trabalhadas',
                data: [],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + 'h';
                        }
                    }
                }
            }
        }
    });

    const ctxPontualidade = document.getElementById('graficoPontualidade').getContext('2d');
    graficoPontualidade = new Chart(ctxPontualidade, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Taxa de Pontualidade',
                data: [],
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
}

// Inicializar datas do formulário e eventos
document.addEventListener('DOMContentLoaded', function() {
    // Definir data inicial como primeiro dia do mês atual
    const hoje = new Date();
    const primeiroDiaMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
    
    // Formatar datas para o formato YYYY-MM-DD
    const dataInicial = primeiroDiaMes.toISOString().split('T')[0];
    const dataFinal = hoje.toISOString().split('T')[0];
    
    // Definir valores nos campos
    document.querySelector('input[name="data_inicio"]').value = dataInicial;
    document.querySelector('input[name="data_fim"]').value = dataFinal;
    
    // Inicializar gráficos
    inicializarGraficos();
    
    // Adicionar evento de submit ao formulário
    document.getElementById('filtrosForm').addEventListener('submit', function(e) {
        e.preventDefault();
        buscarRegistros(1);
    });
    
    // Buscar registros iniciais
    buscarRegistros(1);
});

// Atualizar tabela de resultados
function atualizarTabela(registros) {
    const tbody = document.querySelector('#tabelaRegistros tbody');
    
    if (!registros || registros.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">Nenhum registro encontrado</td></tr>';
        return;
    }
    
    tbody.innerHTML = '';
    
    registros.forEach(registro => {
        const linha = document.createElement('tr');
        
        // Formatação de data
        const dataHora = new Date(registro.data_hora).toLocaleString('pt-BR');
        
        linha.innerHTML = `
            <td>${registro.nome_completo}</td>
            <td>${dataHora}</td>
            <td><span class="badge badge-tipo badge-${registro.tipo_registro}">${registro.tipo_descricao}</span></td>
            <td><span class="badge badge-metodo badge-${registro.metodo_registro}">${registro.metodo_descricao}</span></td>
            <td>${registro.setor}</td>
            <td>${registro.status_pontualidade}</td>
        `;
        
        tbody.appendChild(linha);
    });
}

// Atualizar estatísticas
function atualizarEstatisticas(stats) {
    if (!stats) return;
    
    document.getElementById('statTotal').textContent = stats.total_registros || 0;
    document.getElementById('statBiometrico').textContent = stats.registros_biometricos || 0;
    document.getElementById('statManual').textContent = stats.registros_manuais || 0;
    document.getElementById('statFuncionarios').textContent = stats.funcionarios_distintos || 0;
    
    document.getElementById('statsContainer').style.display = 'block';
}

// Atualizar paginação
function atualizarPaginacao(totalPaginas, paginaAtual) {
    const container = document.getElementById('paginacaoContainer');
    const paginacao = document.getElementById('paginacao');
    
    if (totalPaginas <= 1) {
        container.style.display = 'none';
        return;
    }
    
    paginacao.innerHTML = '';
    
    // Botão anterior
    if (paginaAtual > 1) {
        paginacao.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="buscarRegistros(${paginaAtual - 1})">&laquo;</a>
            </li>
        `;
    }
    
    // Páginas
    const inicio = Math.max(1, paginaAtual - 2);
    const fim = Math.min(totalPaginas, paginaAtual + 2);
    
    for (let i = inicio; i <= fim; i++) {
        const ativo = i === paginaAtual ? 'active' : '';
        paginacao.innerHTML += `
            <li class="page-item ${ativo}">
                <a class="page-link" href="#" onclick="buscarRegistros(${i})">${i}</a>
            </li>
        `;
    }
    
    // Botão próximo
    if (paginaAtual < totalPaginas) {
        paginacao.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="buscarRegistros(${paginaAtual + 1})">&raquo;</a>
            </li>
        `;
    }
    
    container.style.display = 'block';
}

// Exportar CSV
async function exportarCSV() {
    try {
        if (dadosAtuais.length === 0) {
            alert('Não há dados para exportar. Faça uma busca primeiro.');
            return;
        }
        
        // Obter filtros atuais
        const filtros = {
            funcionario_id: document.getElementById('funcionario_id').value || null,
            setor: document.getElementById('setor').value || null,
            tipo_registro: document.getElementById('tipo_registro').value || null,
            metodo_registro: document.getElementById('metodo_registro').value || null,
            data_inicio: document.getElementById('data_inicio').value,
            data_fim: document.getElementById('data_fim').value
        };
        
        const response = await fetch('/relatorios/api/exportar-csv', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filtros)
        });
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `relatorio_pontos_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            mostrarSucesso('Relatório exportado com sucesso!');
        } else {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Erro ao exportar relatório');
        }
        
    } catch (error) {
        console.error('Erro ao exportar CSV:', error);
        mostrarErro(error.message || 'Erro interno do sistema');
    }
}

// Função para atualizar os gráficos
function atualizarGraficos(dados) {
    if (!dados) return;

    // Atualizar gráfico de horas trabalhadas
    if (graficoHoras && dados.horas) {
        graficoHoras.data.labels = dados.horas.labels;
        graficoHoras.data.datasets[0].data = dados.horas.valores.map(horas => {
            // Converter string de horas (HH:MM:SS) para número decimal
            const [horas_str, minutos_str, segundos_str] = horas.split(':');
            return parseFloat(horas_str) + (parseFloat(minutos_str || 0) / 60);
        });
        graficoHoras.update();
    }

    // Atualizar gráfico de pontualidade
    if (graficoPontualidade && dados.pontualidade) {
        graficoPontualidade.data.labels = dados.pontualidade.labels.map(mes => {
            // Formatar mês/ano para exibição (YYYY-MM para MM/YYYY)
            const [ano, mes] = mes.split('-');
            return `${mes}/${ano}`;
        });
        graficoPontualidade.data.datasets[0].data = dados.pontualidade.valores;
        graficoPontualidade.update();
    }

    // Atualizar resumos
    if (dados.resumo) {
        // Atualizar resumo de horas
        document.getElementById('totalHorasMes').textContent = dados.resumo.total_horas_mes;
        document.getElementById('mediaHorasDia').textContent = dados.resumo.media_horas_dia;
        document.getElementById('diasTrabalhados').textContent = dados.resumo.dias_trabalhados;

        // Atualizar resumo de pontualidade
        const taxaPontualidade = dados.resumo.taxa_pontualidade;
        document.getElementById('taxaPontualidade').textContent = `${taxaPontualidade.toFixed(1)}%`;
        document.getElementById('progressPontualidade').style.width = `${taxaPontualidade}%`;

        const totalAtrasos = dados.resumo.total_atrasos;
        document.getElementById('totalAtrasos').textContent = totalAtrasos;
        // Calcular porcentagem de atrasos em relação ao total de registros (máximo 100%)
        const porcentagemAtrasos = Math.min((totalAtrasos / dados.resumo.dias_trabalhados) * 100, 100);
        document.getElementById('progressAtrasos').style.width = `${porcentagemAtrasos}%`;
    }
}
</script>
{% endblock %}