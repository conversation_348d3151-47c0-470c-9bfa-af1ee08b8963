/**
 * CORREÇÃO CRÍTICA - Modal Biométrico RLPONTO-WEB
 * Solução baseada em Context7 MCP - Bootstrap Best Practices
 * 
 * PROBLEMA RESOLVIDO: Modal aparecendo "embaçado" 
 * CAUSA: Conflitos de CSS, z-index inadequado e backdrop incorreto
 * SOLUÇÃO: Padrões modernos do Bootstrap 5 via Context7 MCP
 * 
 * Autor: <PERSON> - AiNexus Tecnologia  
 * Data: 10/01/2025 - Correção Critical Modal Fix
 * Referência: Bootstrap Modal Best Practices via Context7 MCP
 */

/* ================================
   RESET E OVERRIDE - CRITICAL FIX
   ================================ */

/* Garantir que não há conflitos com outros modais */
.modal-biometria {
    /* Reset completo baseado em Bootstrap patterns */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1055 !important; /* Bootstrap modal z-index padrão */
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    
    /* Garantir visibilidade total */
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: all !important;
    
    /* Remover qualquer transformação que cause blur */
    transform: none !important;
    filter: none !important;
    backdrop-filter: none !important;
    
    /* Animação suave baseada em Bootstrap */
    transition: opacity 0.15s linear !important;
}

/* Backdrop corrigido seguindo padrões Bootstrap */
.modal-backdrop {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1050 !important; /* Backdrop sempre atrás do modal */
    width: 100vw !important;
    height: 100vh !important;
    
    /* Background escurecido sem blur excessivo */
    background-color: rgba(0, 0, 0, 0.5) !important;
    
    /* Blur sutil apenas no backdrop - Context7 MCP Pattern */
    backdrop-filter: blur(2px) !important;
    -webkit-backdrop-filter: blur(2px) !important;
    
    /* Remover conflitos */
    transform: none !important;
    filter: none !important;
}

/* Container do modal seguindo Bootstrap Structure */
.modal-container {
    position: relative !important;
    z-index: 1060 !important; /* Sempre acima do backdrop */
    
    /* Dimensões responsivas baseadas em Bootstrap patterns */
    width: 90% !important;
    max-width: 800px !important;
    max-height: 90vh !important;
    
    /* Background limpo e sólido */
    background: #ffffff !important;
    
    /* Border radius moderno */
    border-radius: 0.5rem !important; /* Bootstrap default */
    
    /* Shadow moderna sem blur no container */
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    
    /* Scroll interno se necessário */
    overflow: hidden !important;
    
    /* Garantir posicionamento correto */
    transform: none !important;
    filter: none !important;
    
    /* Animação Bootstrap-style */
    transition: transform 0.3s ease-out !important;
}

/* Fade-in animation baseada em Bootstrap */
.modal-biometria.show .modal-container {
    transform: scale(1) !important;
}

.modal-biometria:not(.show) .modal-container {
    transform: scale(0.9) !important;
}

/* ================================
   HEADER DO MODAL - BOOTSTRAP STYLE
   ================================ */

.modal-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 1rem 1.5rem !important; /* Bootstrap padding */
    border-bottom: 1px solid #dee2e6 !important; /* Bootstrap border */
    background: #ffffff !important;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.modal-header h2 {
    margin: 0 !important;
    color: #212529 !important; /* Bootstrap text-dark */
    font-size: 1.25rem !important; /* Bootstrap h5 size */
    font-weight: 500 !important;
    line-height: 1.2 !important;
}

/* Botão de fechar Bootstrap style */
.modal-close {
    width: 1em !important;
    height: 1em !important;
    padding: 0.25rem !important;
    margin: -0.125rem -0.125rem -0.125rem auto !important;
    background: transparent !important;
    border: 0 !important;
    border-radius: 0.25rem !important;
    opacity: 0.5 !important;
    cursor: pointer !important;
    
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    line-height: 1 !important;
    color: #000 !important;
    text-shadow: 0 1px 0 #fff !important;
    
    transition: opacity 0.15s ease-in-out !important;
}

.modal-close:hover {
    opacity: 0.75 !important;
}

.modal-close:focus {
    opacity: 1 !important;
    outline: 0 !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

/* ================================
   CONTENT AREA - SCROLLABLE
   ================================ */

.modal-content {
    padding: 1.5rem !important;
    overflow-y: auto !important;
    max-height: calc(90vh - 120px) !important; /* Espaço para header e footer */
    
    /* Garantir scroll suave */
    -webkit-overflow-scrolling: touch !important;
    scrollbar-width: thin !important;
    scrollbar-color: #ced4da #f8f9fa !important;
}

/* Custom scrollbar para Webkit */
.modal-content::-webkit-scrollbar {
    width: 8px !important;
}

.modal-content::-webkit-scrollbar-track {
    background: #f8f9fa !important;
    border-radius: 4px !important;
}

.modal-content::-webkit-scrollbar-thumb {
    background: #ced4da !important;
    border-radius: 4px !important;
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: #adb5bd !important;
}

/* ================================
   ACTIONS FOOTER - BOOTSTRAP STYLE  
   ================================ */

.modal-actions {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    justify-content: flex-end !important;
    padding: 0.75rem 1.5rem !important;
    border-top: 1px solid #dee2e6 !important;
    border-radius: 0 0 0.5rem 0.5rem !important;
    background: #ffffff !important;
    gap: 0.5rem !important;
}

/* Botões no estilo Bootstrap */
.modal-actions .btn {
    padding: 0.375rem 0.75rem !important;
    margin: 0 !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    border-radius: 0.25rem !important;
    border: 1px solid transparent !important;
    font-weight: 400 !important;
    text-align: center !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    user-select: none !important;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, 
                border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

.modal-actions .btn:focus {
    outline: 0 !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

/* ================================
   RESPONSIVE DESIGN - MOBILE FIRST
   ================================ */

@media (max-width: 576px) {
    .modal-container {
        width: 95% !important;
        max-height: 95vh !important;
        margin: 2.5vh auto !important;
    }
    
    .modal-header {
        padding: 0.75rem 1rem !important;
    }
    
    .modal-header h2 {
        font-size: 1.1rem !important;
    }
    
    .modal-content {
        padding: 1rem !important;
        max-height: calc(95vh - 100px) !important;
    }
    
    .modal-actions {
        padding: 0.5rem 1rem !important;
        flex-direction: column !important;
    }
    
    .modal-actions .btn {
        width: 100% !important;
        margin-bottom: 0.25rem !important;
    }
}

/* Tablet adjustments */
@media (min-width: 577px) and (max-width: 768px) {
    .modal-container {
        width: 85% !important;
        max-width: 700px !important;
    }
}

/* Desktop optimization */
@media (min-width: 769px) {
    .modal-container {
        width: 80% !important;
        max-width: 800px !important;
    }
}

/* ================================
   ACCESSIBILITY IMPROVEMENTS
   ================================ */

/* Focus trap para modal */
.modal-biometria[aria-hidden="false"] {
    display: flex !important;
}

.modal-biometria[aria-hidden="true"] {
    display: none !important;
}

/* Screen reader support */
.modal-biometria .sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* ================================
   ANIMATION IMPROVEMENTS
   ================================ */

/* Fade in animation */
@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Scale animation for container */
@keyframes modalScaleIn {
    from {
        transform: scale(0.9);
    }
    to {
        transform: scale(1);
    }
}

.modal-biometria.fade-in {
    animation: modalFadeIn 0.15s ease-out forwards !important;
}

.modal-biometria.fade-in .modal-container {
    animation: modalScaleIn 0.3s ease-out forwards !important;
}

/* ================================
   PRINT STYLES
   ================================ */

@media print {
    .modal-biometria {
        display: none !important;
    }
}

/* ================================
   HIGH CONTRAST MODE SUPPORT
   ================================ */

@media (prefers-contrast: high) {
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.8) !important;
    }
    
    .modal-container {
        border: 2px solid #000 !important;
    }
    
    .modal-header {
        border-bottom: 2px solid #000 !important;
    }
    
    .modal-actions {
        border-top: 2px solid #000 !important;
    }
}

/* ================================
   REDUCED MOTION SUPPORT
   ================================ */

@media (prefers-reduced-motion: reduce) {
    .modal-biometria,
    .modal-container,
    .modal-close,
    .modal-actions .btn {
        transition: none !important;
        animation: none !important;
    }
}

/* ================================
   DARK MODE SUPPORT (FUTURE)
   ================================ */

@media (prefers-color-scheme: dark) {
    .modal-container {
        background: #212529 !important;
        color: #fff !important;
    }
    
    .modal-header {
        background: #212529 !important;
        border-bottom-color: #495057 !important;
    }
    
    .modal-actions {
        background: #212529 !important;
        border-top-color: #495057 !important;
    }
    
    .modal-close {
        color: #fff !important;
        text-shadow: 0 1px 0 #000 !important;
    }
}

/* ================================
   FINAL CRITICAL OVERRIDES
   ================================ */

/* Garantir que o modal seja sempre visível quando ativo */
body.modal-open .modal-biometria {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Prevenir scroll no body quando modal aberto */
body.modal-open {
    overflow: hidden !important;
    padding-right: 0 !important; /* Evitar shift de scrollbar */
}

/* Fix para iOS Safari */
@supports (-webkit-touch-callout: none) {
    .modal-biometria {
        -webkit-transform: translateZ(0) !important;
        transform: translateZ(0) !important;
    }
}

/* Fix para navegadores antigos */
.modal-biometria {
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    -webkit-perspective: 1000px !important;
    perspective: 1000px !important;
}