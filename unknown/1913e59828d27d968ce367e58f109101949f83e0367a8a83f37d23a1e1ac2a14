{% extends "base.html" %}

{% block title %}Cadastrar Empresa - RLPONTO-WEB{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 30px;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .page-header h1 {
        margin: 0 0 10px 0;
        font-size: 2em;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: #f8f9fa;
    }
    
    .section-title {
        margin: 0 0 20px 0;
        color: #2c3e50;
        font-size: 1.2em;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group.full-width {
        grid-column: 1 / -1;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #2c3e50;
    }
    
    .form-group.required label::after {
        content: " *";
        color: #e74c3c;
    }
    
    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s ease;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 10px;
    }
    
    .checkbox-group input[type="checkbox"] {
        width: auto;
        margin: 0;
    }
    
    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }
    
    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-primary {
        background: #3498db;
        color: white;
    }
    
    .btn-primary:hover {
        background: #2980b9;
    }
    
    .btn-secondary {
        background: #95a5a6;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #7f8c8d;
        color: white;
    }
    
    .alert {
        padding: 12px 15px;
        border-radius: 6px;
        margin-bottom: 20px;
    }
    
    .alert-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
    
    .form-text {
        font-size: 0.85em;
        color: #6c757d;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-building"></i> Cadastrar Nova Empresa</h1>
    <p>Preencha os dados da empresa para cadastro no sistema</p>
</div>

<div class="form-container">
    <form method="POST" enctype="multipart/form-data">
        <!-- Dados Básicos -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-info-circle"></i> Dados Básicos
            </h3>
            
            <div class="form-grid">
                <div class="form-group required">
                    <label for="razao_social">Razão Social</label>
                    <input type="text" 
                           id="razao_social" 
                           name="razao_social" 
                           class="form-control"
                           value="{{ dados.razao_social if dados else '' }}"
                           required
                           maxlength="200"
                           placeholder="Ex: Empresa Exemplo Ltda">
                    <div class="form-text">Nome oficial da empresa conforme registro</div>
                </div>
                
                <div class="form-group">
                    <label for="nome_fantasia">Nome Fantasia</label>
                    <input type="text" 
                           id="nome_fantasia" 
                           name="nome_fantasia" 
                           class="form-control"
                           value="{{ dados.nome_fantasia if dados else '' }}"
                           maxlength="200"
                           placeholder="Ex: Empresa Exemplo">
                    <div class="form-text">Nome comercial da empresa</div>
                </div>
            </div>
            
            <div class="form-group required">
                <label for="cnpj">CNPJ</label>
                <input type="text" 
                       id="cnpj" 
                       name="cnpj" 
                       class="form-control"
                       value="{{ dados.cnpj if dados else '' }}"
                       required
                       placeholder="00.000.000/0000-00"
                       maxlength="18">
                <div class="form-text">CNPJ da empresa (apenas números ou formatado)</div>
            </div>
        </div>
        
        <!-- Contatos -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-phone"></i> Informações de Contato
            </h3>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="telefone">Telefone Principal</label>
                    <input type="tel" 
                           id="telefone" 
                           name="telefone" 
                           class="form-control"
                           value="{{ dados.telefone if dados else '' }}"
                           placeholder="(11) 99999-9999"
                           maxlength="15">
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           class="form-control"
                           value="{{ dados.email if dados else '' }}"
                           placeholder="<EMAIL>"
                           maxlength="100">
                </div>
            </div>
        </div>
        
        <!-- Configurações -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-cog"></i> Configurações
            </h3>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Informação:</strong> Após o cadastro, você poderá configurar as jornadas de trabalho específicas desta empresa.
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" 
                       id="ativa" 
                       name="ativa" 
                       {{ 'checked' if not dados or dados.ativa else '' }}>
                <label for="ativa">Empresa ativa no sistema</label>
            </div>
        </div>
        
        <!-- Ações -->
        <div class="form-actions">
            <a href="{{ url_for('empresas.index') }}" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancelar
            </a>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Cadastrar Empresa
            </button>
        </div>
    </form>
</div>

<script>
// Máscara para CNPJ
document.getElementById('cnpj').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    
    if (value.length <= 14) {
        value = value.replace(/^(\d{2})(\d)/, '$1.$2');
        value = value.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
        value = value.replace(/\.(\d{3})(\d)/, '.$1/$2');
        value = value.replace(/(\d{4})(\d)/, '$1-$2');
    }
    
    e.target.value = value;
});

// Máscara para telefone
document.getElementById('telefone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    
    if (value.length <= 11) {
        if (value.length <= 10) {
            value = value.replace(/^(\d{2})(\d)/, '($1) $2');
            value = value.replace(/(\d{4})(\d)/, '$1-$2');
        } else {
            value = value.replace(/^(\d{2})(\d)/, '($1) $2');
            value = value.replace(/(\d{5})(\d)/, '$1-$2');
        }
    }
    
    e.target.value = value;
});
</script>
{% endblock %}
