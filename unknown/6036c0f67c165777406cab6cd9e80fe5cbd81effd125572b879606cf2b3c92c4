{% extends "base.html" %}

{% block title %}Detalhes do Funcionário - {{ funcionario.nome_completo }}{% endblock %}

{% block extra_css %}
<style>
/* 🎨 MODERN EMPLOYEE DETAILS - Inspirado no MCP @21st-dev/magic */
@import url('https://fonts.googleapis.com/css2?family=SF+Mono:wght@400;500;600&display=swap');

:root {
    --primary-color: #4fbdba;
    --secondary-color: #3b82f6;
    --background-color: #fafbfc;
    --card-background: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
}

.employee-details-container {
    background: var(--background-color);
    min-height: 100vh;
    padding: 24px;
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 📱 Breadcrumbs modernos */
.modern-breadcrumb {
    background: var(--card-background);
    padding: 16px 24px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: 24px;
    border: 1px solid var(--border-color);
}

.modern-breadcrumb ol {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.modern-breadcrumb a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.modern-breadcrumb a:hover {
    color: var(--primary-color);
}

.modern-breadcrumb .active {
    color: var(--text-primary);
    font-weight: 500;
}

/* 👤 Header Profile Card */
.profile-header-card {
    background: linear-gradient(135deg, var(--primary-color), #26a69a);
    border-radius: var(--radius-xl);
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: var(--shadow-lg);
    color: white;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out 0.1s both;
}

.profile-header-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.profile-header-content {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 24px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
}

.profile-avatar:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.4);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info h1 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-info .job-info {
    font-size: 16px;
    margin: 0 0 12px 0;
    opacity: 0.9;
}

.profile-badges {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.status-badge {
    padding: 6px 12px;
    border-radius: var(--radius-md);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.9);
}

.profile-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.action-btn {
    padding: 12px 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-2px);
}

/* 📊 Cards Grid */
.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.detail-card {
    background: var(--card-background);
    border-radius: var(--radius-xl);
    padding: 32px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    animation: fadeInUp 0.6s ease-out var(--delay, 0s) both;
    position: relative;
    overflow: hidden;
}

.detail-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.detail-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.card-icon {
    font-size: 24px;
    color: var(--primary-color);
}

.card-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 15px;
    font-weight: 500;
    color: var(--text-primary);
    font-family: 'SF Mono', monospace;
}

.info-value.empty {
    color: var(--text-secondary);
    font-style: italic;
}

/* 📧 Contact links */
.contact-link {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.contact-link:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

/* ⏰ Schedule Grid */
.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.schedule-item {
    background: var(--background-color);
    padding: 20px;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.schedule-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0 0 12px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.time-display {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.time-item {
    text-align: center;
    padding: 8px;
    background: white;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
}

.time-label {
    font-size: 11px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.time-value {
    font-family: 'SF Mono', monospace;
    font-weight: 600;
    color: var(--text-primary);
}

/* 🦺 EPIs Table */
.epis-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;
}

.epis-table th {
    background: var(--background-color);
    padding: 12px 16px;
    text-align: left;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid var(--border-color);
}

.epis-table td {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
}

.epis-table tr:hover {
    background: var(--background-color);
}

.epi-status {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.epi-status.valid {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.epi-status.expired {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

/* 📦 Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--text-primary);
}

.empty-description {
    margin: 0 0 24px 0;
    line-height: 1.5;
}

.empty-action {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: var(--transition);
}

.empty-action:hover {
    background: #3da8a6;
    transform: translateY(-2px);
    color: white;
}

/* 🎯 Action Buttons */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 40px;
    flex-wrap: wrap;
}

.btn {
    padding: 14px 28px;
    border-radius: var(--radius-md);
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #3da8a6;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-secondary {
    background: var(--text-secondary);
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-2px);
    color: white;
}

/* 📱 Responsive Design */
@media (max-width: 768px) {
    .employee-details-container {
        padding: 16px;
    }
    
    .profile-header-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 20px;
    }
    
    .profile-avatar {
        justify-self: center;
    }
    
    .profile-actions {
        flex-direction: row;
        justify-content: center;
    }
    
    .details-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .detail-card {
        padding: 24px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .schedule-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

/* Animation delays for staggered effects */
.detail-card:nth-child(1) { --delay: 0.2s; }
.detail-card:nth-child(2) { --delay: 0.3s; }
.detail-card:nth-child(3) { --delay: 0.4s; }
.detail-card:nth-child(4) { --delay: 0.5s; }
.detail-card:nth-child(5) { --delay: 0.6s; }
.detail-card:nth-child(6) { --delay: 0.7s; }
</style>
{% endblock %}

{% block content %}
<div class="employee-details-container">
    
    <!-- Modern Breadcrumbs -->
    {% if breadcrumbs %}
    <nav class="modern-breadcrumb" aria-label="breadcrumb">
        <ol>
            {% for breadcrumb in breadcrumbs %}
                {% if loop.last %}
                    <li class="active">{{ breadcrumb.label }}</li>
                {% else %}
                    <li><a href="{{ breadcrumb.url }}">{{ breadcrumb.label }}</a></li>
                    <li>›</li>
                {% endif %}
            {% endfor %}
        </ol>
    </nav>
    {% endif %}

    <!-- Profile Header Card -->
    <div class="profile-header-card">
        <div class="profile-header-content">
            <div class="profile-avatar">
                <img src="/funcionarios/{{ funcionario.id }}/foto" 
                     alt="Foto de {{ funcionario.nome_completo }}" 
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik02MCA1MEM2NS41MjI4IDUwIDcwIDQ1LjUyMjggNzAgNDBDNzAgMzQuNDc3MiA2NS41MjI4IDMwIDYwIDMwQzU0LjQ3NzIgMzAgNTAgMzQuNDc3MiA1MCA0MEM1MCA0NS41MjI4IDU0LjQ3NzIgNTAgNjAgNTBaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik02MCA2MEMzNS44NDYgNjAgMjAgNzUuODQ2IDIwIDEwMFYxMTBIOTBWMTAwQzkwIDc1Ljg0NiA3NC4xNTQgNjAgNjAgNjBaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo='">
            </div>
            
            <div class="profile-info">
                <h1>{{ funcionario.nome_completo }}</h1>
                <p class="job-info">
                    <strong>{{ funcionario.cargo }}</strong> • {{ funcionario.setor_obra }}
                </p>
                <div class="profile-badges">
                    <span class="status-badge {% if funcionario.status_cadastro == 'Ativo' %}active{% endif %}">
                        {% if funcionario.status_cadastro == 'Ativo' %}✓{% else %}✗{% endif %} {{ funcionario.status_cadastro }}
                    </span>
                    <span class="status-badge">
                        ID: {{ funcionario.matricula_empresa }}
                    </span>
                </div>
            </div>
            
            <div class="profile-actions">
                {% if current_user.is_admin %}
                <a href="/funcionarios/{{ funcionario.id }}/editar" class="action-btn">
                    <i class="fas fa-edit"></i> Editar
                </a>
                {% endif %}
                <a href="/epis/funcionario/{{ funcionario.id }}" class="action-btn">
                    <i class="fas fa-hard-hat"></i> EPIs
                </a>
            </div>
        </div>
    </div>

    <!-- Details Grid -->
    <div class="details-grid">
        
        <!-- Personal Information -->
        <div class="detail-card">
            <div class="card-header">
                <span class="card-icon">👤</span>
                <h3 class="card-title">Dados Pessoais</h3>
            </div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">CPF</span>
                    <span class="info-value">{{ funcionario.cpf | format_cpf }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">RG</span>
                    <span class="info-value">{{ funcionario.rg or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Data Nascimento</span>
                    <span class="info-value">{{ funcionario.data_nascimento | format_date }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Sexo</span>
                    <span class="info-value">{{ funcionario.sexo or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Estado Civil</span>
                    <span class="info-value">{{ funcionario.estado_civil or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Nacionalidade</span>
                    <span class="info-value">{{ funcionario.nacionalidade or '-' }}</span>
                </div>
            </div>
        </div>

        <!-- Work Documents -->
        <div class="detail-card">
            <div class="card-header">
                <span class="card-icon">📄</span>
                <h3 class="card-title">Documentos Trabalhistas</h3>
            </div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">CTPS Número</span>
                    <span class="info-value">{{ funcionario.ctps_numero or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">CTPS Série/UF</span>
                    <span class="info-value">{{ funcionario.ctps_serie_uf or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">PIS/PASEP</span>
                    <span class="info-value">{{ funcionario.pis_pasep or '-' }}</span>
                </div>
            </div>
        </div>

        <!-- Address -->
        <div class="detail-card">
            <div class="card-header">
                <span class="card-icon">🏠</span>
                <h3 class="card-title">Endereço</h3>
            </div>
            <div class="info-grid">
                <div class="info-item" style="grid-column: 1 / -1;">
                    <span class="info-label">Rua</span>
                    <span class="info-value">{{ funcionario.endereco_rua or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Bairro</span>
                    <span class="info-value">{{ funcionario.endereco_bairro or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Cidade</span>
                    <span class="info-value">{{ funcionario.endereco_cidade or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">CEP</span>
                    <span class="info-value">{{ funcionario.endereco_cep or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Estado</span>
                    <span class="info-value">{{ funcionario.endereco_estado or '-' }}</span>
                </div>
            </div>
        </div>

        <!-- Contact -->
        <div class="detail-card">
            <div class="card-header">
                <span class="card-icon">📞</span>
                <h3 class="card-title">Contato</h3>
            </div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Telefone Principal</span>
                    <span class="info-value">{{ funcionario.telefone1 | format_telefone }}</span>
                </div>
                {% if funcionario.telefone2 %}
                <div class="info-item">
                    <span class="info-label">Telefone Secundário</span>
                    <span class="info-value">{{ funcionario.telefone2 | format_telefone }}</span>
                </div>
                {% endif %}
                {% if funcionario.email %}
                <div class="info-item">
                    <span class="info-label">E-mail</span>
                    <span class="info-value">
                        <a href="mailto:{{ funcionario.email }}" class="contact-link">{{ funcionario.email }}</a>
                    </span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Professional Data -->
        <div class="detail-card">
            <div class="card-header">
                <span class="card-icon">💼</span>
                <h3 class="card-title">Dados Profissionais</h3>
            </div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Data Admissão</span>
                    <span class="info-value">{{ funcionario.data_admissao | format_date }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Tipo Contrato</span>
                    <span class="info-value">{{ funcionario.tipo_contrato or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Nível Acesso</span>
                    <span class="info-value">{{ funcionario.nivel_acesso or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Turno</span>
                    <span class="info-value">{{ funcionario.turno or '-' }}</span>
                </div>
            </div>
        </div>

        <!-- Work Schedule -->
        <div class="detail-card">
            <div class="card-header">
                <span class="card-icon">⏰</span>
                <h3 class="card-title">Jornada de Trabalho</h3>
            </div>
            
            <div class="schedule-grid">
                <div class="schedule-item">
                    <h4 class="schedule-title">Segunda a Quinta</h4>
                    <div class="time-display">
                        <div class="time-item">
                            <div class="time-label">Entrada</div>
                            <div class="time-value">{{ funcionario.jornada_seg_qui_entrada or '-' }}</div>
                        </div>
                        <div class="time-item">
                            <div class="time-label">Saída</div>
                            <div class="time-value">{{ funcionario.jornada_seg_qui_saida or '-' }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="schedule-item">
                    <h4 class="schedule-title">Sexta-feira</h4>
                    <div class="time-display">
                        <div class="time-item">
                            <div class="time-label">Entrada</div>
                            <div class="time-value">{{ funcionario.jornada_sex_entrada or '-' }}</div>
                        </div>
                        <div class="time-item">
                            <div class="time-label">Saída</div>
                            <div class="time-value">{{ funcionario.jornada_sex_saida or '-' }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="schedule-item">
                    <h4 class="schedule-title">Intervalo</h4>
                    <div class="time-display">
                        <div class="time-item">
                            <div class="time-label">Início</div>
                            <div class="time-value">{{ funcionario.jornada_intervalo_entrada or '-' }}</div>
                        </div>
                        <div class="time-item">
                            <div class="time-label">Fim</div>
                            <div class="time-value">{{ funcionario.jornada_intervalo_saida or '-' }}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="info-grid" style="margin-top: 20px; padding-top: 20px; border-top: 1px solid var(--border-color);">
                <div class="info-item">
                    <span class="info-label">Tolerância Ponto</span>
                    <span class="info-value">{{ funcionario.tolerancia_ponto or '0' }} min</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Banco de Horas</span>
                    <span class="info-value">{% if funcionario.banco_horas %}✓ Sim{% else %}✗ Não{% endif %}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Hora Extra</span>
                    <span class="info-value">{% if funcionario.hora_extra %}✓ Sim{% else %}✗ Não{% endif %}</span>
                </div>
            </div>
        </div>

    </div>

    <!-- EPIs Section -->
    {% if funcionario.epis and funcionario.epis|length > 0 %}
    <div class="detail-card">
        <div class="card-header">
            <span class="card-icon">🦺</span>
            <h3 class="card-title">EPIs (Equipamentos de Proteção Individual)</h3>
            <a href="/epis/funcionario/{{ funcionario.id }}" class="action-btn" style="margin-left: auto;">
                <i class="fas fa-cog"></i> Gerenciar EPIs
            </a>
        </div>
        
        <table class="epis-table">
            <thead>
                <tr>
                    <th>EPI</th>
                    <th>CA</th>
                    <th>Data Entrega</th>
                    <th>Validade</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for epi in funcionario.epis[:5] %}
                <tr>
                    <td><strong>{{ epi.epi_nome }}</strong></td>
                    <td>{{ epi.epi_ca or '-' }}</td>
                    <td>
                        {% if epi.epi_data_entrega %}
                            {{ epi.epi_data_entrega | format_date }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if epi.epi_data_validade %}
                            {{ epi.epi_data_validade | format_date }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if epi.epi_data_validade %}
                            <span class="epi-status valid">Válido</span>
                        {% else %}
                            <span class="epi-status expired">Sem validade</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        {% if funcionario.epis|length > 5 %}
            <p style="margin: 16px 0 0 0; color: var(--text-secondary); font-size: 14px; text-align: center;">
                Mostrando 5 de {{ funcionario.epis|length }} EPIs. 
                <a href="/epis/funcionario/{{ funcionario.id }}" style="color: var(--primary-color);">Ver todos →</a>
            </p>
        {% endif %}
    </div>
    {% else %}
    <div class="detail-card">
        <div class="card-header">
            <span class="card-icon">🦺</span>
            <h3 class="card-title">EPIs (Equipamentos de Proteção Individual)</h3>
        </div>
        <div class="empty-state">
            <div class="empty-icon">📦</div>
            <h4 class="empty-title">Nenhum EPI cadastrado</h4>
            <p class="empty-description">Este funcionário ainda não possui EPIs registrados no sistema.</p>
            <a href="/epis/funcionario/{{ funcionario.id }}" class="empty-action">
                <i class="fas fa-plus"></i> Adicionar EPI
            </a>
        </div>
    </div>
    {% endif %}

    <!-- System Information -->
    <div class="detail-card">
        <div class="card-header">
            <span class="card-icon">⚙️</span>
            <h3 class="card-title">Informações do Sistema</h3>
        </div>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Data Cadastro</span>
                <span class="info-value">{{ funcionario.data_cadastro | format_date }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Biometria Dedo 1</span>
                <span class="info-value">
                    {% if funcionario.digital_dedo1 %}
                        <span class="epi-status valid">✓ Configurada</span>
                    {% else %}
                        <span class="epi-status expired">✗ Não configurada</span>
                    {% endif %}
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">Biometria Dedo 2</span>
                <span class="info-value">
                    {% if funcionario.digital_dedo2 %}
                        <span class="epi-status valid">✓ Configurada</span>
                    {% else %}
                        <span class="epi-status expired">✗ Não configurada</span>
                    {% endif %}
                </span>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="/funcionarios" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar à Lista
        </a>
        {% if current_user.is_admin %}
        <a href="/funcionarios/{{ funcionario.id }}/editar" class="btn btn-primary">
            <i class="fas fa-edit"></i> Editar Funcionário
        </a>
        {% endif %}
    </div>

</div>
{% endblock %}
{% endblock %}