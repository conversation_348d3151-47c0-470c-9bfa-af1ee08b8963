#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para Criar Estruturas Básicas - RLPONTO-WEB
-------------------------------------------------
Cria tabelas e estruturas necessárias de forma incremental.
"""

import pymysql
from utils.config import Config

def criar_estruturas():
    """
    Cria estruturas básicas necessárias.
    """
    try:
        config = Config.get_database_url()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        print('🔧 Criando tabelas uma por uma...')
        
        # 1. Criar tabela empresas
        try:
            cursor.execute('''
            CREATE TABLE empresas (
                id INT AUTO_INCREMENT PRIMARY KEY,
                razao_social VARCHAR(200) NOT NULL,
                nome_fantasia VARCHAR(200) NULL,
                cnpj VARCHAR(18) UNIQUE NOT NULL,
                telefone VARCHAR(15) NULL,
                email VARCHAR(100) NULL,
                ativa BOOLEAN DEFAULT TRUE,
                data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
            ''')
            print('✅ Tabela empresas criada')
        except Exception as e:
            print(f'⚠️ Empresas: {e}')
        
        # 2. Criar tabela horarios_trabalho
        try:
            cursor.execute('''
            CREATE TABLE horarios_trabalho (
                id INT AUTO_INCREMENT PRIMARY KEY,
                empresa_id INT NOT NULL,
                nome_horario VARCHAR(100) NOT NULL,
                entrada_manha TIME NOT NULL DEFAULT '08:00:00',
                saida_almoco TIME NULL DEFAULT '12:00:00',
                entrada_tarde TIME NULL DEFAULT '13:00:00',
                saida TIME NOT NULL DEFAULT '17:00:00',
                tolerancia_minutos INT NOT NULL DEFAULT 10,
                ativo BOOLEAN DEFAULT TRUE,
                data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (empresa_id) REFERENCES empresas(id) ON DELETE CASCADE
            ) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
            ''')
            print('✅ Tabela horarios_trabalho criada')
        except Exception as e:
            print(f'⚠️ Horarios: {e}')
        
        # 3. Adicionar campos em funcionarios
        try:
            cursor.execute('ALTER TABLE funcionarios ADD COLUMN empresa_id INT NULL AFTER id')
            print('✅ Campo empresa_id adicionado')
        except Exception as e:
            print(f'⚠️ empresa_id: {e}')
            
        try:
            cursor.execute('ALTER TABLE funcionarios ADD COLUMN horario_trabalho_id INT NULL AFTER empresa_id')
            print('✅ Campo horario_trabalho_id adicionado')
        except Exception as e:
            print(f'⚠️ horario_trabalho_id: {e}')
        
        # 4. Inserir dados padrão
        try:
            cursor.execute('''
            INSERT INTO empresas (id, razao_social, nome_fantasia, cnpj, ativa) 
            VALUES (1, 'Empresa Padrão Ltda', 'Empresa Padrão', '00.000.000/0000-00', TRUE)
            ON DUPLICATE KEY UPDATE razao_social = VALUES(razao_social)
            ''')
            print('✅ Empresa padrão inserida')
        except Exception as e:
            print(f'⚠️ Empresa padrão: {e}')
            
        try:
            cursor.execute('''
            INSERT INTO horarios_trabalho (id, empresa_id, nome_horario, entrada_manha, saida_almoco, entrada_tarde, saida, tolerancia_minutos) 
            VALUES (1, 1, 'Horário Administrativo', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10)
            ON DUPLICATE KEY UPDATE nome_horario = VALUES(nome_horario)
            ''')
            print('✅ Horário padrão inserido')
        except Exception as e:
            print(f'⚠️ Horário padrão: {e}')
        
        # 5. Atualizar funcionários existentes
        try:
            cursor.execute('UPDATE funcionarios SET empresa_id = 1, horario_trabalho_id = 1 WHERE empresa_id IS NULL')
            affected = cursor.rowcount
            print(f'✅ {affected} funcionários atualizados')
        except Exception as e:
            print(f'⚠️ Update funcionários: {e}')
        
        # 6. Verificar resultado
        cursor.execute('SHOW TABLES')
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f'\n📊 Tabelas no banco: {len(tables)}')
        for table in ['empresas', 'horarios_trabalho', 'funcionarios', 'registros_ponto']:
            if table in tables:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                print(f'   ✅ {table}: {count} registros')
        
        conn.commit()
        cursor.close()
        conn.close()
        print('\n🎯 Estruturas básicas criadas com sucesso!')
        return True
        
    except Exception as e:
        print(f'❌ Erro: {e}')
        return False

if __name__ == '__main__':
    print('='*50)
    print('🚀 CRIAÇÃO DE ESTRUTURAS BÁSICAS')
    print('='*50)
    
    if criar_estruturas():
        print('\n✅ Processo concluído!')
    else:
        print('\n❌ Processo falhou!') 