-- =====================================================
-- CORREÇÃO: Mapear dados existentes antes de alterar ENUMs
-- Data: 13/07/2025
-- =====================================================

-- 1. Mapear valores de turno incompatíveis
UPDATE funcionarios_desligados 
SET turno = 'Diurno' 
WHERE turno = 'Integral';

UPDATE funcionarios_desligados 
SET turno = 'Diurno' 
WHERE turno = 'Manha';

UPDATE funcionarios_desligados 
SET turno = 'Diurno' 
WHERE turno = 'Tarde';

UPDATE funcionarios_desligados 
SET turno = 'Noturno' 
WHERE turno = 'Noite';

-- 2. Verificar valores de tipo_contrato
SELECT DISTINCT tipo_contrato FROM funcionarios_desligados;

-- Mapear tipo_contrato se necessário
UPDATE funcionarios_desligados 
SET tipo_contrato = 'CLT' 
WHERE tipo_contrato = 'Terceirizado';

UPDATE funcionarios_desligados 
SET tipo_contrato = 'Estagio' 
WHERE tipo_contrato = 'Estagiario';

UPDATE funcionarios_desligados 
SET tipo_contrato = 'Temporario' 
WHERE tipo_contrato = 'Temporario';

-- 3. Verificar valores de status_cadastro
SELECT DISTINCT status_cadastro FROM funcionarios_desligados;

-- Mapear status_cadastro se necessário
UPDATE funcionarios_desligados 
SET status_cadastro = 'Inativo' 
WHERE status_cadastro = 'Pendente';

-- 4. Agora aplicar as alterações de ENUM
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN turno ENUM('Diurno','Noturno','Misto') NOT NULL;

ALTER TABLE funcionarios_desligados 
MODIFY COLUMN tipo_contrato ENUM('CLT','PJ','Estagio','Temporario') NOT NULL;

ALTER TABLE funcionarios_desligados 
MODIFY COLUMN status_cadastro ENUM('Ativo','Inativo') DEFAULT 'Ativo';

-- 5. Verificação final
SELECT 'Verificando dados após correção...' as status;

SELECT DISTINCT turno FROM funcionarios_desligados;
SELECT DISTINCT tipo_contrato FROM funcionarios_desligados;
SELECT DISTINCT status_cadastro FROM funcionarios_desligados;

SELECT 'Correção de dados e ENUMs concluída!' as resultado;
