#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DEBUG ESPECÍFICO DO RICHARDSON
==============================
"""

import os
import sys
sys.path.append('var/www/controle-ponto')

from utils.database import get_db_connection
from pymysql.cursors import DictCursor
from utils.helpers import mascarar_cpf

def main():
    print("🔍 DEBUG ESPECÍFICO DO RICHARDSON")
    print("=" * 50)
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Buscar Richardson especificamente
        cursor.execute("""
            SELECT 
                f.id,
                f.nome_completo,
                f.cpf,
                f.foto_3x4,
                e.nome_fantasia as empresa,
                ht.nome_horario
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
            WHERE f.nome_completo LIKE "%RICHARDSON%"
            AND f.ativo = TRUE
        """)
        
        funcionario = cursor.fetchone()
        
        if funcionario:
            print(f"📋 DADOS NO BANCO:")
            print(f"   Nome: {funcionario['nome_completo']}")
            print(f"   CPF original: {funcionario['cpf']}")
            print(f"   Foto original: {funcionario['foto_3x4']}")
            print(f"   Empresa: {funcionario['empresa']}")
            print(f"   Horário: {funcionario.get('horario_trabalho', 'Não informado')}")
            
            print(f"\n🔧 PROCESSAMENTO:")
            
            # Testar mascaramento de CPF
            cpf_mascarado_usuario = mascarar_cpf(funcionario['cpf'], 'usuario')
            cpf_mascarado_admin = mascarar_cpf(funcionario['cpf'], 'admin')
            print(f"   CPF para usuário: {cpf_mascarado_usuario}")
            print(f"   CPF para admin: {cpf_mascarado_admin}")
            
            # Processar foto como no código original
            foto_url = funcionario['foto_3x4'] if funcionario['foto_3x4'] and funcionario['foto_3x4'] != '/static/images/funcionario_sem_foto.svg' else None
            print(f"   Foto processada: {foto_url}")
            
            # Verificar se arquivo existe
            if foto_url:
                arquivo_local = foto_url.replace('/static/', 'var/www/controle-ponto/static/')
                existe = os.path.exists(arquivo_local)
                print(f"   Arquivo existe: {existe}")
                print(f"   Caminho local: {arquivo_local}")
                
                if existe:
                    # Verificar tamanho do arquivo
                    tamanho = os.path.getsize(arquivo_local)
                    print(f"   Tamanho do arquivo: {tamanho} bytes")
                else:
                    print(f"   ❌ PROBLEMA: Arquivo não existe no caminho esperado!")
            else:
                print(f"   ❌ PROBLEMA: foto_url é None!")
                
        else:
            print("❌ Funcionário Richardson não encontrado!")
        
        # Verificar template da página manual
        print(f"\n🔍 VERIFICANDO TEMPLATE:")
        template_path = "var/www/controle-ponto/templates/registro_ponto/manual.html"
        if os.path.exists(template_path):
            print(f"   ✅ Template existe: {template_path}")
        else:
            print(f"   ❌ Template não existe: {template_path}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ ERRO: {str(e)}")

if __name__ == "__main__":
    main() 