#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCRIPT DE CRIAÇÃO DE FUNCIONÁRIOS PARA TESTE COMPLETO DO SISTEMA
================================================================

Este script cria 30 funcionários fictícios com cenários de teste abrangentes:
- 15 funcionários para empresa RENOVAR CONSTRUÇÃO CIVIL LTDA (ID: 3)
- 15 funcionários para empresa AINEXUS TECNOLOGIA (ID: 4)
- 31 dias de registros de ponto para cada funcionário
- 15 dias com problemas diversos (atrasos, faltas, justificativas, etc.)
- 15 dias com horas extras (B5/B6)
- Cenários realistas de problemas de ponto

Autor: Sistema RLPONTO-WEB
Data: 17/07/2025
"""

import mysql.connector
import random
from datetime import datetime, timedelta, time
import hashlib
import os
from decimal import Decimal

# Configuração do banco de dados
DB_CONFIG = {
    'host': 'localhost',
    'user': 'controle_ponto_user',
    'password': 'senha_segura_2024',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

# Dados das empresas
EMPRESAS = {
    3: "RENOVAR CONSTRUÇÃO CIVIL LTDA",
    4: "AINEXUS TECNOLOGIA"
}

# Jornadas de trabalho padrão
JORNADAS = {
    3: {  # RENOVAR - Construção Civil
        'entrada': '07:00',
        'saida_almoco': '12:00',
        'retorno_almoco': '13:00',
        'saida': '17:00',
        'horas_semanais': 44.0
    },
    4: {  # AINEXUS - Tecnologia
        'entrada': '08:00',
        'saida_almoco': '12:00',
        'retorno_almoco': '13:00',
        'saida': '18:00',
        'horas_semanais': 44.0
    }
}

# Lista de nomes fictícios
NOMES_BASE = [
    "JOÃO", "MARIA", "JOSÉ", "ANA", "CARLOS", "FERNANDA", "PAULO", "JULIANA",
    "RICARDO", "PATRICIA", "MARCOS", "LUCIANA", "ANTONIO", "SANDRA", "LUIS",
    "MONICA", "FRANCISCO", "CARLA", "PEDRO", "AMANDA", "RAFAEL", "BEATRIZ",
    "DANIEL", "CAMILA", "RODRIGO", "PRISCILA", "BRUNO", "VANESSA", "FELIPE", "GABRIELA"
]

# CPFs fictícios (apenas para teste)
def gerar_cpf_ficticio(numero):
    """Gera CPF fictício para teste"""
    base = f"{numero:011d}"
    return f"{base[:3]}.{base[3:6]}.{base[6:9]}-{base[9:11]}"

def conectar_banco():
    """Conecta ao banco de dados"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Erro ao conectar ao banco: {e}")
        return None

def criar_funcionario(conn, numero, empresa_id):
    """Cria um funcionário no banco de dados"""
    cursor = conn.cursor()
    
    nome = f"{NOMES_BASE[numero-1]} {numero:02d}"
    cpf = gerar_cpf_ficticio(numero + 10000000000)
    matricula = f"FUNC{numero:03d}"
    
    # Dados do funcionário
    funcionario_data = {
        'nome': nome,
        'cpf': cpf,
        'matricula': matricula,
        'empresa_id': empresa_id,
        'cargo': 'FUNCIONÁRIO TESTE',
        'setor': 'TESTE',
        'data_admissao': '2024-01-01',
        'status': 'ativo',
        'email': f"joao{numero:02d}@teste.com",
        'telefone': f"(11) 9999-{numero:04d}",
        'endereco': f"Rua Teste, {numero}",
        'salario': Decimal('2500.00'),
        'horas_trabalho_obrigatorias': JORNADAS[empresa_id]['horas_semanais']
    }
    
    # Inserir funcionário
    insert_query = """
    INSERT INTO funcionarios (
        nome, cpf, matricula, empresa_id, cargo, setor, data_admissao,
        status, email, telefone, endereco, salario, horas_trabalho_obrigatorias
    ) VALUES (
        %(nome)s, %(cpf)s, %(matricula)s, %(empresa_id)s, %(cargo)s, %(setor)s,
        %(data_admissao)s, %(status)s, %(email)s, %(telefone)s, %(endereco)s,
        %(salario)s, %(horas_trabalho_obrigatorias)s
    )
    """
    
    try:
        cursor.execute(insert_query, funcionario_data)
        funcionario_id = cursor.lastrowid
        
        # Criar jornada de trabalho
        jornada = JORNADAS[empresa_id]
        jornada_query = """
        INSERT INTO horarios_trabalho (
            funcionario_id, empresa_id, segunda_entrada, segunda_saida,
            terca_entrada, terca_saida, quarta_entrada, quarta_saida,
            quinta_entrada, quinta_saida, sexta_entrada, sexta_saida,
            sabado_entrada, sabado_saida, domingo_entrada, domingo_saida,
            almoco_inicio, almoco_fim
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            NULL, NULL, NULL, NULL, %s, %s
        )
        """
        
        cursor.execute(jornada_query, (
            funcionario_id, empresa_id,
            jornada['entrada'], jornada['saida'],  # Segunda
            jornada['entrada'], jornada['saida'],  # Terça
            jornada['entrada'], jornada['saida'],  # Quarta
            jornada['entrada'], jornada['saida'],  # Quinta
            jornada['entrada'], jornada['saida'],  # Sexta
            jornada['saida_almoco'], jornada['retorno_almoco']  # Almoço
        ))
        
        print(f"✅ Funcionário criado: {nome} (ID: {funcionario_id}) - Empresa: {EMPRESAS[empresa_id]}")
        return funcionario_id
        
    except Exception as e:
        print(f"❌ Erro ao criar funcionário {nome}: {e}")
        return None
    finally:
        cursor.close()

def gerar_horario_com_variacao(horario_base, variacao_minutos=0, atraso=False):
    """Gera horário com variação"""
    hora, minuto = map(int, horario_base.split(':'))
    dt = datetime.combine(datetime.today(), time(hora, minuto))

    if atraso:
        dt += timedelta(minutes=random.randint(5, 60))
    elif variacao_minutos > 0:
        variacao = random.randint(-variacao_minutos, variacao_minutos)
        dt += timedelta(minutes=variacao)

    return dt.strftime('%H:%M:%S')

def criar_registro_ponto_normal(conn, funcionario_id, empresa_id, data):
    """Cria registro de ponto normal"""
    cursor = conn.cursor()
    jornada = JORNADAS[empresa_id]

    # Horários com pequenas variações normais
    entrada = gerar_horario_com_variacao(jornada['entrada'], 10)
    saida_almoco = gerar_horario_com_variacao(jornada['saida_almoco'], 5)
    retorno_almoco = gerar_horario_com_variacao(jornada['retorno_almoco'], 5)
    saida = gerar_horario_com_variacao(jornada['saida'], 10)

    # Inserir registro
    insert_query = """
    INSERT INTO registro_ponto (
        funcionario_id, empresa_id, data, entrada, saida_almoco,
        retorno_almoco, saida, observacoes
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    """

    try:
        cursor.execute(insert_query, (
            funcionario_id, empresa_id, data, entrada,
            saida_almoco, retorno_almoco, saida, 'Registro normal'
        ))
        return True
    except Exception as e:
        print(f"❌ Erro ao criar registro normal: {e}")
        return False
    finally:
        cursor.close()

def criar_registro_ponto_problema(conn, funcionario_id, empresa_id, data, tipo_problema):
    """Cria registro de ponto com problemas específicos"""
    cursor = conn.cursor()
    jornada = JORNADAS[empresa_id]

    entrada = None
    saida_almoco = None
    retorno_almoco = None
    saida = None
    observacoes = ""
    justificativa = None

    if tipo_problema == "atraso_entrada":
        entrada = gerar_horario_com_variacao(jornada['entrada'], atraso=True)
        saida_almoco = gerar_horario_com_variacao(jornada['saida_almoco'], 5)
        retorno_almoco = gerar_horario_com_variacao(jornada['retorno_almoco'], 5)
        saida = gerar_horario_com_variacao(jornada['saida'], 10)
        observacoes = "Atraso na entrada"

    elif tipo_problema == "atraso_com_justificativa":
        entrada = gerar_horario_com_variacao(jornada['entrada'], atraso=True)
        saida_almoco = gerar_horario_com_variacao(jornada['saida_almoco'], 5)
        retorno_almoco = gerar_horario_com_variacao(jornada['retorno_almoco'], 5)
        saida = gerar_horario_com_variacao(jornada['saida'], 10)
        observacoes = "Atraso justificado"
        justificativa = "Trânsito intenso devido a acidente na via"

    elif tipo_problema == "sem_saida_almoco":
        entrada = gerar_horario_com_variacao(jornada['entrada'], 10)
        # Sem saída para almoço
        retorno_almoco = gerar_horario_com_variacao(jornada['retorno_almoco'], 5)
        saida = gerar_horario_com_variacao(jornada['saida'], 10)
        observacoes = "Não registrou saída para almoço"

    elif tipo_problema == "sem_retorno_almoco":
        entrada = gerar_horario_com_variacao(jornada['entrada'], 10)
        saida_almoco = gerar_horario_com_variacao(jornada['saida_almoco'], 5)
        # Sem retorno do almoço
        saida = gerar_horario_com_variacao(jornada['saida'], 10)
        observacoes = "Não registrou retorno do almoço"

    elif tipo_problema == "saida_antecipada":
        entrada = gerar_horario_com_variacao(jornada['entrada'], 10)
        saida_almoco = gerar_horario_com_variacao(jornada['saida_almoco'], 5)
        retorno_almoco = gerar_horario_com_variacao(jornada['retorno_almoco'], 5)
        # Saída 1-3 horas antes
        hora_saida = datetime.strptime(jornada['saida'], '%H:%M')
        hora_saida -= timedelta(hours=random.randint(1, 3))
        saida = hora_saida.strftime('%H:%M:%S')
        observacoes = "Saída antecipada"

    elif tipo_problema == "saida_antecipada_justificada":
        entrada = gerar_horario_com_variacao(jornada['entrada'], 10)
        saida_almoco = gerar_horario_com_variacao(jornada['saida_almoco'], 5)
        retorno_almoco = gerar_horario_com_variacao(jornada['retorno_almoco'], 5)
        hora_saida = datetime.strptime(jornada['saida'], '%H:%M')
        hora_saida -= timedelta(hours=random.randint(1, 2))
        saida = hora_saida.strftime('%H:%M:%S')
        observacoes = "Saída antecipada justificada"
        justificativa = "Consulta médica agendada"

    elif tipo_problema == "sem_saida":
        entrada = gerar_horario_com_variacao(jornada['entrada'], 10)
        saida_almoco = gerar_horario_com_variacao(jornada['saida_almoco'], 5)
        retorno_almoco = gerar_horario_com_variacao(jornada['retorno_almoco'], 5)
        # Sem registro de saída
        observacoes = "Não registrou saída"

    elif tipo_problema == "falta_total":
        # Sem nenhum registro
        observacoes = "Falta não justificada"

    elif tipo_problema == "falta_justificada":
        # Sem nenhum registro
        observacoes = "Falta justificada"
        justificativa = "Atestado médico"

    # Inserir registro
    insert_query = """
    INSERT INTO registro_ponto (
        funcionario_id, empresa_id, data, entrada, saida_almoco,
        retorno_almoco, saida, observacoes
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    """

    try:
        cursor.execute(insert_query, (
            funcionario_id, empresa_id, data, entrada,
            saida_almoco, retorno_almoco, saida, observacoes
        ))

        registro_id = cursor.lastrowid

        # Adicionar justificativa se houver
        if justificativa:
            justificativa_query = """
            INSERT INTO justificativas_ponto (
                funcionario_id, empresa_id, data, motivo, descricao,
                status, data_criacao
            ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(justificativa_query, (
                funcionario_id, empresa_id, data, 'outros', justificativa,
                'pendente', datetime.now()
            ))

        return True
    except Exception as e:
        print(f"❌ Erro ao criar registro com problema: {e}")
        return False
    finally:
        cursor.close()

def criar_registro_ponto_horas_extras(conn, funcionario_id, empresa_id, data):
    """Cria registro de ponto com horas extras (B5/B6)"""
    cursor = conn.cursor()
    jornada = JORNADAS[empresa_id]

    # Horários normais
    entrada = gerar_horario_com_variacao(jornada['entrada'], 10)
    saida_almoco = gerar_horario_com_variacao(jornada['saida_almoco'], 5)
    retorno_almoco = gerar_horario_com_variacao(jornada['retorno_almoco'], 5)

    # Saída normal
    saida_normal = gerar_horario_com_variacao(jornada['saida'], 10)

    # Horas extras - B5 (saída para horas extras) e B6 (retorno das horas extras)
    hora_saida_normal = datetime.strptime(saida_normal, '%H:%M:%S')

    # B5 - Saída para jantar/descanso (opcional)
    b5 = None
    if random.choice([True, False]):  # 50% chance de ter B5
        b5 = (hora_saida_normal + timedelta(minutes=random.randint(30, 60))).strftime('%H:%M:%S')

    # B6 - Saída final após horas extras (1-4 horas extras)
    horas_extras = random.randint(1, 4)
    if b5:
        # Se tem B5, B6 é após o retorno
        b6 = (datetime.strptime(b5, '%H:%M:%S') + timedelta(hours=horas_extras, minutes=30)).strftime('%H:%M:%S')
    else:
        # Se não tem B5, B6 é direto após a saída normal
        b6 = (hora_saida_normal + timedelta(hours=horas_extras)).strftime('%H:%M:%S')

    # Inserir registro
    insert_query = """
    INSERT INTO registro_ponto (
        funcionario_id, empresa_id, data, entrada, saida_almoco,
        retorno_almoco, saida, b5, b6, observacoes
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """

    try:
        cursor.execute(insert_query, (
            funcionario_id, empresa_id, data, entrada,
            saida_almoco, retorno_almoco, saida_normal, b5, b6,
            f'Horas extras: {horas_extras}h'
        ))
        return True
    except Exception as e:
        print(f"❌ Erro ao criar registro com horas extras: {e}")
        return False
    finally:
        cursor.close()

def criar_registros_mes_completo(conn, funcionario_id, empresa_id):
    """Cria registros de ponto para um mês completo (31 dias)"""
    print(f"📅 Criando registros para funcionário ID {funcionario_id}...")

    # Data base: julho de 2025
    data_base = datetime(2025, 7, 1)

    # Tipos de problemas para distribuir
    tipos_problemas = [
        "atraso_entrada", "atraso_com_justificativa", "sem_saida_almoco",
        "sem_retorno_almoco", "saida_antecipada", "saida_antecipada_justificada",
        "sem_saida", "falta_total", "falta_justificada"
    ]

    # Distribuir os 31 dias
    dias_normais = []
    dias_problemas = []
    dias_horas_extras = []

    # 15 dias com problemas
    dias_problemas = random.sample(range(1, 32), 15)

    # 15 dias com horas extras (pode sobrepor com alguns problemas)
    dias_horas_extras = random.sample(range(1, 32), 15)

    # Resto são dias normais
    for dia in range(1, 32):
        if dia not in dias_problemas and dia not in dias_horas_extras:
            dias_normais.append(dia)

    # Criar registros
    for dia in range(1, 32):
        data = data_base.replace(day=dia)
        data_str = data.strftime('%Y-%m-%d')

        # Pular fins de semana para alguns funcionários (realismo)
        if data.weekday() >= 5 and random.choice([True, False]):  # 50% chance de trabalhar no fim de semana
            continue

        if dia in dias_problemas:
            # Dia com problema
            tipo_problema = random.choice(tipos_problemas)
            criar_registro_ponto_problema(conn, funcionario_id, empresa_id, data_str, tipo_problema)
            print(f"  📍 Dia {dia}: {tipo_problema}")

        elif dia in dias_horas_extras:
            # Dia com horas extras
            criar_registro_ponto_horas_extras(conn, funcionario_id, empresa_id, data_str)
            print(f"  ⏰ Dia {dia}: horas extras")

        else:
            # Dia normal
            criar_registro_ponto_normal(conn, funcionario_id, empresa_id, data_str)
            print(f"  ✅ Dia {dia}: normal")

def main():
    """Função principal"""
    print("🚀 INICIANDO CRIAÇÃO DE FUNCIONÁRIOS PARA TESTE COMPLETO")
    print("=" * 60)

    # Conectar ao banco
    conn = conectar_banco()
    if not conn:
        return

    try:
        # Criar 30 funcionários
        funcionarios_criados = []

        for i in range(1, 31):
            # Alternar entre as empresas
            empresa_id = 3 if i <= 15 else 4

            print(f"\n👤 Criando funcionário {i}/30...")
            funcionario_id = criar_funcionario(conn, i, empresa_id)

            if funcionario_id:
                funcionarios_criados.append((funcionario_id, empresa_id))

                # Criar registros de ponto para o mês
                criar_registros_mes_completo(conn, funcionario_id, empresa_id)

        # Commit das transações
        conn.commit()

        print("\n" + "=" * 60)
        print(f"✅ TESTE COMPLETO CRIADO COM SUCESSO!")
        print(f"📊 Funcionários criados: {len(funcionarios_criados)}")
        print(f"📅 Registros por funcionário: ~31 dias")
        print(f"⚠️  Problemas por funcionário: ~15 dias")
        print(f"⏰ Horas extras por funcionário: ~15 dias")
        print("\n🧪 Sistema pronto para bateria de testes!")

    except Exception as e:
        print(f"❌ Erro durante a criação: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    main()
