#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para testar a exclusão de uma nova empresa
Data: 03/07/2025
"""

from utils.database import get_db_connection
import requests
import json
import sys
import datetime
import time

def main():
    try:
        # Criar uma nova empresa de teste
        conn = get_db_connection()
        cursor = conn.cursor()
        
        empresa_nome = f"Empresa Teste Final {datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Gerar um CNPJ único
        timestamp = datetime.datetime.now().strftime('%H%M%S')
        cnpj = f"00.000.{timestamp[:3]}/{timestamp[3:]}-00"
        
        cursor.execute("""
            INSERT INTO empresas (razao_social, nome_fantasia, cnpj, telefone, email, ativa, data_cadastro)
            VALUES (%s, %s, %s, %s, %s, TRUE, NOW())
        """, (
            empresa_nome,
            f"Teste Final {datetime.datetime.now().strftime('%H%M%S')}",
            cnpj, 
            "(11) 1234-5678", 
            "<EMAIL>"
        ))
        
        conn.commit()
        
        # Verificar se a empresa foi criada
        cursor.execute("SELECT id, razao_social FROM empresas WHERE razao_social = %s", (empresa_nome,))
        empresa = cursor.fetchone()
        
        if not empresa:
            print("Falha ao criar empresa de teste.")
            conn.close()
            sys.exit(1)
            
        empresa_id = empresa['id'] if isinstance(empresa, dict) else empresa[0]
        print(f"Empresa de teste criada com sucesso! ID: {empresa_id}, Nome: {empresa_nome}")
        
        # Verificar se a empresa está ativa
        cursor.execute("SELECT id, razao_social, ativa FROM empresas WHERE id = %s", (empresa_id,))
        empresa_antes = cursor.fetchone()
        
        if empresa_antes:
            ativa_antes = empresa_antes['ativa'] if isinstance(empresa_antes, dict) else empresa_antes[2]
            print(f"Empresa antes da exclusão - ID: {empresa_id}, Ativa: {ativa_antes}")
        
        # Login e exclusão da empresa
        session = requests.Session()
        login_url = "http://10.19.208.31/login"
        login_data = {"usuario": "admin", "senha": "@Ric6109"}
        session.post(login_url, data=login_data)
        
        # Excluir empresa
        print(f"\nExcluindo empresa ID {empresa_id}...")
        url = f"http://10.19.208.31/configuracoes/empresas/{empresa_id}/excluir"
        headers = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
        data = {'is_ajax': True}
        
        response = session.post(url, headers=headers, data=json.dumps(data))
        print(f"Status code: {response.status_code}")
        print(f"Resposta: {response.text}")
        
        # Aguardar um pouco para garantir que a operação seja concluída
        print("Aguardando 2 segundos para garantir que a operação seja concluída...")
        time.sleep(2)
        
        # Verificar se a empresa foi marcada como inativa
        cursor.execute("SELECT id, razao_social, ativa FROM empresas WHERE id = %s", (empresa_id,))
        empresa_depois = cursor.fetchone()
        
        if empresa_depois:
            ativa_depois = empresa_depois['ativa'] if isinstance(empresa_depois, dict) else empresa_depois[2]
            print(f"Empresa depois da exclusão - ID: {empresa_id}, Ativa: {ativa_depois}")
            
            if not ativa_depois:
                print(f"✅ SUCESSO! Empresa ID {empresa_id} foi marcada como inativa!")
                print(f"✅ A CORREÇÃO ESTÁ FUNCIONANDO CORRETAMENTE!")
            else:
                print(f"❌ FALHA! Empresa ID {empresa_id} ainda está ativa no banco de dados!")
                print(f"❌ A CORREÇÃO NÃO ESTÁ FUNCIONANDO CORRETAMENTE!")
        else:
            print(f"❌ Empresa ID {empresa_id} não encontrada após a exclusão!")
            
        conn.close()
        
    except Exception as e:
        print(f"Erro: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 