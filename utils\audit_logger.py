# ✅ CORREÇÃO #8: Sistema de Logs de Auditoria Detalhados
# RLPONTO-WEB v1.0 - Sistema de Auditoria Avançado
# Data: 10/01/2025

import json
import hashlib
import datetime
from typing import Dict, Any, Optional, List
from flask import request, session, g
from functools import wraps
import logging
import traceback
import os

logger = logging.getLogger(__name__)

class AuditLogger:
    """
    Sistema de auditoria avançado para RLPONTO-WEB
    
    Características:
    - Logs estruturados em JSON
    - Classificação por criticidade
    - Rastreamento de mudanças
    - Detecção de anomalias
    - Conformidade com LGPD
    """
    
    # Níveis de criticidade
    CRITICO = 'CRITICO'
    ALTO = 'ALTO'
    MEDIO = 'MEDIO'
    BAIXO = 'BAIXO'
    INFO = 'INFO'
    
    # Categorias de eventos
    CATEGORIA_LOGIN = 'LOGIN'
    CATEGORIA_BIOMETRIA = 'BIOMETRIA'
    CATEGORIA_FUNCIONARIOS = 'FUNCIONARIOS'
    CATEGORIA_PONTO = 'PONTO'
    CATEGORIA_CONFIGURACAO = 'CONFIGURACAO'
    CATEGORIA_RELATORIO = 'RELATORIO'
    CATEGORIA_SISTEMA = 'SISTEMA'
    CATEGORIA_SEGURANCA = 'SEGURANCA'
    
    def __init__(self):
        self.session_id = self._generate_session_id()
        
    def _generate_session_id(self) -> str:
        """Gera ID único da sessão"""
        base = f"{datetime.datetime.now().isoformat()}_{id(self)}"
        return hashlib.md5(base.encode()).hexdigest()[:16]
        
    def _get_user_context(self) -> Dict[str, Any]:
        """Obtém contexto do usuário atual"""
        try:
            return {
                'usuario_id': session.get('usuario_id'),
                'usuario': session.get('usuario'),
                'nivel_acesso': session.get('nivel_acesso'),
                'is_admin': session.get('is_admin', False),
                'session_id': self.session_id,
                'ip_origem': self._get_real_ip(),
                'user_agent': request.headers.get('User-Agent', '') if request else '',
                'referrer': request.referrer if request else None,
                'endpoint': request.endpoint if request else None,
                'method': request.method if request else None
            }
        except RuntimeError:
            # Fora do contexto do Flask
            return {
                'usuario_id': None,
                'usuario': 'SISTEMA',
                'nivel_acesso': 'sistema',
                'is_admin': True,
                'session_id': self.session_id,
                'ip_origem': 'localhost',
                'user_agent': 'Sistema Interno',
                'referrer': None,
                'endpoint': None,
                'method': None
            }
    
    def _get_real_ip(self) -> str:
        """Obtém IP real do usuário, considerando proxies"""
        if not request:
            return 'localhost'
            
        # Verificar headers de proxy
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        return request.remote_addr or 'unknown'
    
    def log_event(self, 
                  acao: str,
                  categoria: str,
                  criticidade: str = 'INFO',
                  detalhes: Optional[Dict[str, Any]] = None,
                  tabela_afetada: Optional[str] = None,
                  registro_id: Optional[int] = None,
                  sucesso: bool = True,
                  erro: Optional[str] = None) -> bool:
        """
        Registra evento de auditoria no banco de dados
        """
        try:
            from ..database import DatabaseManager
            
            user_context = self._get_user_context()
            
            # Preparar detalhes estruturados
            detalhes_completos = {
                'timestamp': datetime.datetime.now().isoformat(),
                'categoria': categoria,
                'criticidade': criticidade,
                'sucesso': sucesso,
                'session_context': user_context,
                'detalhes_operacao': detalhes or {},
                'erro': erro
            }
            
            # Inserir no banco de dados
            sql = """
                INSERT INTO logs_sistema (
                    usuario_id, acao, tabela_afetada, registro_id, detalhes, 
                    ip_origem, user_agent, data_hora
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            params = (
                user_context['usuario_id'],
                f"[{categoria}:{criticidade}] {acao}",
                tabela_afetada,
                registro_id,
                json.dumps(detalhes_completos, ensure_ascii=False, default=str),
                user_context['ip_origem'],
                user_context['user_agent'],
                datetime.datetime.now()
            )
            
            DatabaseManager.execute_query(sql, params, fetch_all=False)
            
            # Log adicional para eventos críticos
            if criticidade in [self.CRITICO, self.ALTO]:
                logger.critical(f"[AUDIT:{categoria}:{criticidade}] {acao} - Usuário: {user_context['usuario']} - IP: {user_context['ip_origem']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao registrar log de auditoria: {e}")
            return False

# Instância global do audit logger
audit_logger = AuditLogger() 