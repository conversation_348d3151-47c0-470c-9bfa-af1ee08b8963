-- Investigação das justificativas na administração de ponto
-- Sistema: RLPONTO-WEB v1.0

-- 1. Verificar registros com observações (justificativas)
SELECT 
    f.nome_completo,
    rp.data_registro,
    rp.tipo_registro,
    rp.observacoes,
    rp.criado_em
FROM funcionarios f
LEFT JOIN registros_ponto rp ON f.id = rp.funcionario_id
WHERE f.status_cadastro = 'Ativo'
AND rp.observacoes IS NOT NULL
AND MONTH(rp.data_registro) = 7
AND YEAR(rp.data_registro) = 2025
ORDER BY f.nome_completo, rp.data_registro DESC;

-- 2. Contar justificativas por funcionário
SELECT 
    f.nome_completo,
    COUNT(CASE WHEN rp.observacoes IS NOT NULL THEN 1 END) as total_justificativas
FROM funcionarios f
LEFT JOIN registros_ponto rp ON f.id = rp.funcionario_id
WHERE f.status_cadastro = 'Ativo'
AND MONTH(rp.data_registro) = 7
AND YEAR(rp.data_registro) = 2025
GROUP BY f.id, f.nome_completo
HAVING total_justificativas > 0
ORDER BY total_justificativas DESC;

-- 3. Verificar se existe tabela de justificativas separada
SHOW TABLES LIKE '%justificativa%';

-- 4. Se existir, verificar dados da tabela justificativas_ponto
SELECT 
    f.nome_completo,
    jp.data_referencia,
    jp.tipo_justificativa,
    jp.motivo,
    jp.status_aprovacao,
    jp.criado_em
FROM justificativas_ponto jp
INNER JOIN funcionarios f ON jp.funcionario_id = f.id
WHERE MONTH(jp.data_referencia) = 7
AND YEAR(jp.data_referencia) = 2025
ORDER BY f.nome_completo, jp.data_referencia DESC;

-- 5. Verificar estrutura da tabela registros_ponto
DESCRIBE registros_ponto;

-- 6. Verificar últimos registros com observações
SELECT 
    f.nome_completo,
    rp.data_registro,
    rp.tipo_registro,
    rp.observacoes,
    rp.criado_em,
    rp.atualizado_em
FROM funcionarios f
INNER JOIN registros_ponto rp ON f.id = rp.funcionario_id
WHERE rp.observacoes IS NOT NULL
ORDER BY rp.criado_em DESC
LIMIT 20;
