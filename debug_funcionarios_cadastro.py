#!/usr/bin/env python3
"""
Script para debugar problemas no cadastro de funcionários.
Verifica estrutura da tabela, campos obrigatórios e testa inserção.
"""

import sys
import os
import logging
from datetime import datetime

# Adicionar o diretório do projeto ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'var', 'www', 'controle-ponto'))

from utils.database import DatabaseManager, get_db_connection

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verificar_estrutura_tabela():
    """Verifica a estrutura da tabela funcionarios"""
    print("\n🔍 VERIFICANDO ESTRUTURA DA TABELA FUNCIONARIOS")
    print("=" * 60)
    
    try:
        # Buscar estrutura da tabela
        estrutura = DatabaseManager.execute_query("DESCRIBE funcionarios")
        
        print(f"📋 Tabela 'funcionarios' possui {len(estrutura)} campos:")
        print()
        
        campos_obrigatorios = []
        campos_opcionais = []
        
        for campo in estrutura:
            nome = campo['Field']
            tipo = campo['Type']
            nulo = campo['Null']
            padrao = campo['Default']
            
            status = "OBRIGATÓRIO" if nulo == 'NO' and padrao is None else "OPCIONAL"
            
            if nulo == 'NO' and padrao is None:
                campos_obrigatorios.append(nome)
            else:
                campos_opcionais.append(nome)
            
            print(f"  {nome:25} | {tipo:20} | {status:12} | Padrão: {padrao}")
        
        print(f"\n✅ Campos obrigatórios ({len(campos_obrigatorios)}):")
        for campo in campos_obrigatorios:
            print(f"  - {campo}")
        
        print(f"\n📝 Campos opcionais ({len(campos_opcionais)}):")
        for campo in campos_opcionais:
            print(f"  - {campo}")
            
        return campos_obrigatorios, campos_opcionais
        
    except Exception as e:
        print(f"❌ Erro ao verificar estrutura: {e}")
        return [], []

def verificar_empresas_disponiveis():
    """Verifica empresas disponíveis para teste"""
    print("\n🏢 VERIFICANDO EMPRESAS DISPONÍVEIS")
    print("=" * 60)
    
    try:
        empresas = DatabaseManager.execute_query("""
            SELECT id, razao_social, nome_fantasia, empresa_principal
            FROM empresas
            WHERE ativa = TRUE
            ORDER BY empresa_principal DESC, razao_social ASC
        """)
        
        if empresas:
            print(f"✅ {len(empresas)} empresas encontradas:")
            for empresa in empresas:
                principal = "⭐ PRINCIPAL" if empresa['empresa_principal'] else ""
                print(f"  ID {empresa['id']:2} | {empresa['nome_fantasia']:30} | {principal}")
            return empresas[0]['id']  # Retorna ID da primeira empresa
        else:
            print("❌ Nenhuma empresa encontrada!")
            return None
            
    except Exception as e:
        print(f"❌ Erro ao verificar empresas: {e}")
        return None

def verificar_jornadas_trabalho():
    """Verifica jornadas de trabalho disponíveis"""
    print("\n🕐 VERIFICANDO JORNADAS DE TRABALHO")
    print("=" * 60)
    
    try:
        jornadas = DatabaseManager.execute_query("""
            SELECT id, nome, empresa_id, padrao, ativa
            FROM jornadas_trabalho
            WHERE ativa = 1
            ORDER BY empresa_id, padrao DESC
        """)
        
        if jornadas:
            print(f"✅ {len(jornadas)} jornadas encontradas:")
            for jornada in jornadas:
                padrao = "PADRÃO" if jornada['padrao'] else ""
                print(f"  ID {jornada['id']:2} | Empresa {jornada['empresa_id']:2} | {jornada['nome']:30} | {padrao}")
            return jornadas[0]['id']  # Retorna ID da primeira jornada
        else:
            print("❌ Nenhuma jornada encontrada!")
            return None
            
    except Exception as e:
        print(f"❌ Erro ao verificar jornadas: {e}")
        return None

def testar_insercao_funcionario():
    """Testa inserção de um funcionário com dados mínimos"""
    print("\n🧪 TESTANDO INSERÇÃO DE FUNCIONÁRIO")
    print("=" * 60)
    
    # Verificar dependências
    empresa_id = verificar_empresas_disponiveis()
    if not empresa_id:
        print("❌ Não é possível testar sem empresas cadastradas")
        return False
    
    jornada_id = verificar_jornadas_trabalho()
    
    try:
        # Dados mínimos para teste
        dados_teste = {
            'nome_completo': 'TESTE FUNCIONARIO DEBUG',
            'cpf': '12345678901',
            'rg': 'MG1234567',
            'data_nascimento': '1990-01-01',
            'sexo': 'M',
            'estado_civil': 'Solteiro',
            'nacionalidade': 'Brasileira',
            'ctps_numero': '123456',
            'ctps_serie_uf': '001/MG',
            'pis_pasep': '12345678901',
            'endereco_rua': 'Rua Teste, 123',
            'endereco_bairro': 'Centro',
            'endereco_cidade': 'Teste',
            'endereco_cep': '12345-678',
            'endereco_estado': 'MG',
            'telefone1': '(31) 99999-9999',
            'telefone2': '',
            'email': '<EMAIL>',
            'cargo': 'Teste',
            'setor_obra': 'Teste',
            'matricula_empresa': 'TEST001',
            'data_admissao': datetime.now().strftime('%Y-%m-%d'),
            'tipo_contrato': 'CLT',
            'nivel_acesso': 'funcionario',
            'turno': 'diurno',
            'tolerancia_ponto': 10,
            'banco_horas': 1,
            'hora_extra': 1,
            'status_cadastro': 'ativo',
            'horas_trabalho_obrigatorias': 8.00,
            'usa_horario_empresa': True,
            'horario_trabalho_id': jornada_id,
            'empresa_id': empresa_id,
            'digital_dedo1': None,
            'digital_dedo2': None,
            'foto_3x4': None
        }
        
        print("📝 Dados de teste preparados:")
        print(f"  - Nome: {dados_teste['nome_completo']}")
        print(f"  - CPF: {dados_teste['cpf']}")
        print(f"  - Empresa ID: {dados_teste['empresa_id']}")
        print(f"  - Jornada ID: {dados_teste['horario_trabalho_id']}")
        
        # Montar SQL de inserção
        sql = """
        INSERT INTO funcionarios (
            nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
            ctps_numero, ctps_serie_uf, pis_pasep,
            endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
            telefone1, telefone2, email,
            cargo, setor_obra, matricula_empresa, data_admissao, tipo_contrato,
            nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, status_cadastro,
            horas_trabalho_obrigatorias, usa_horario_empresa, horario_trabalho_id, empresa_id,
            digital_dedo1, digital_dedo2, foto_3x4
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s
        )
        """
        
        params = (
            dados_teste['nome_completo'], dados_teste['cpf'], dados_teste['rg'], dados_teste['data_nascimento'],
            dados_teste['sexo'], dados_teste['estado_civil'], dados_teste['nacionalidade'],
            dados_teste['ctps_numero'], dados_teste['ctps_serie_uf'], dados_teste['pis_pasep'],
            dados_teste['endereco_rua'], dados_teste['endereco_bairro'], dados_teste['endereco_cidade'],
            dados_teste['endereco_cep'], dados_teste['endereco_estado'],
            dados_teste['telefone1'], dados_teste['telefone2'], dados_teste['email'],
            dados_teste['cargo'], dados_teste['setor_obra'], dados_teste['matricula_empresa'],
            dados_teste['data_admissao'], dados_teste['tipo_contrato'],
            dados_teste['nivel_acesso'], dados_teste['turno'], dados_teste['tolerancia_ponto'],
            dados_teste['banco_horas'], dados_teste['hora_extra'], dados_teste['status_cadastro'],
            dados_teste['horas_trabalho_obrigatorias'], dados_teste['usa_horario_empresa'],
            dados_teste['horario_trabalho_id'], dados_teste['empresa_id'],
            dados_teste['digital_dedo1'], dados_teste['digital_dedo2'], dados_teste['foto_3x4']
        )
        
        print(f"\n🔍 Executando inserção com {len(params)} parâmetros...")
        
        # Executar inserção
        funcionario_id = DatabaseManager.execute_query(sql, params, fetch_all=False)
        
        if funcionario_id:
            print(f"✅ SUCESSO! Funcionário criado com ID: {funcionario_id}")
            
            # Limpar teste
            DatabaseManager.execute_query("DELETE FROM funcionarios WHERE id = %s", (funcionario_id,), fetch_all=False)
            print(f"🧹 Funcionário de teste removido (ID: {funcionario_id})")
            
            return True
        else:
            print("❌ FALHA! Inserção não retornou ID")
            return False
            
    except Exception as e:
        print(f"❌ ERRO na inserção: {e}")
        print(f"   Tipo: {type(e).__name__}")
        return False

def main():
    """Função principal de debug"""
    print("🔧 DEBUG DO CADASTRO DE FUNCIONÁRIOS")
    print("=" * 60)
    
    try:
        # Verificar estrutura da tabela
        campos_obrigatorios, campos_opcionais = verificar_estrutura_tabela()
        
        # Testar inserção
        sucesso = testar_insercao_funcionario()
        
        print(f"\n📊 RESUMO DO DEBUG")
        print("=" * 60)
        print(f"✅ Estrutura da tabela: OK")
        print(f"✅ Campos obrigatórios: {len(campos_obrigatorios)}")
        print(f"✅ Campos opcionais: {len(campos_opcionais)}")
        print(f"{'✅' if sucesso else '❌'} Teste de inserção: {'SUCESSO' if sucesso else 'FALHA'}")
        
        if not sucesso:
            print("\n🚨 PROBLEMA IDENTIFICADO NO CADASTRO DE FUNCIONÁRIOS!")
            print("   Verifique os logs acima para detalhes do erro.")
        else:
            print("\n✅ CADASTRO DE FUNCIONÁRIOS FUNCIONANDO CORRETAMENTE!")
        
    except Exception as e:
        print(f"❌ Erro geral no debug: {e}")

if __name__ == "__main__":
    main()
