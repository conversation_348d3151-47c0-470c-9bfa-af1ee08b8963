#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simples para debugar o erro de contexto de request
"""

import sys
import os
import traceback
from datetime import datetime

# Adicionar o diretório do projeto ao path
sys.path.insert(0, '/var/www/controle-ponto')

try:
    from utils.database import DatabaseManager
    print("✅ DatabaseManager importado")
except ImportError as e:
    print(f"❌ Erro ao importar DatabaseManager: {e}")
    sys.exit(1)

def teste_sql_direto():
    """Testa inserção SQL direta"""
    print("\n🔍 TESTE SQL DIRETO")
    print("-" * 50)
    
    try:
        # SQL de inserção simples
        sql = """
        INSERT INTO funcionarios (
            nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
            ctps_numero, ctps_serie_uf, pis_pasep,
            endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
            telefone1, telefone2, email,
            cargo, setor_obra, matricula_empresa, data_admissao, tipo_contrato,
            nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, status_cadastro,
            horas_semanais_obrigatorias, empresa_id, jornada_trabalho_id,
            digital_dedo1, digital_dedo2, foto_3x4
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s
        )
        """
        
        # Parâmetros simples
        timestamp = datetime.now().strftime("%H%M%S")
        params = (
            'JOÃO TESTE DIRETO', f'111.222.333-{timestamp[-2:]}', '12.345.678-9', '1990-01-01',
            'M', 'Solteiro', 'Brasileira',
            '1234567', '001/MG', '123.45678.90-1',
            'RUA TESTE, 123', 'CENTRO', 'BELO HORIZONTE', '30000-000', 'MG',
            '(31) 99999-9999', '(31) 3333-4444', '<EMAIL>',
            'ANALISTA DE TESTE', 'DESENVOLVIMENTO', f'TEST{timestamp}', '2025-01-01', 'CLT',
            'Funcionario', 'Diurno', 5, True, True, 'Ativo',
            44.0, 11, 1,
            None, None, None
        )
        
        print(f"📊 Executando SQL com {len(params)} parâmetros")
        
        # Executar inserção
        funcionario_id = DatabaseManager.execute_query(sql, params, fetch_all=False)
        
        if funcionario_id:
            print(f"✅ Funcionário criado com ID: {funcionario_id}")
            
            # Limpar teste
            try:
                DatabaseManager.execute_query(
                    "DELETE FROM funcionarios WHERE id = %s", 
                    (funcionario_id,), 
                    fetch_all=False
                )
                print(f"🧹 Funcionário de teste removido (ID: {funcionario_id})")
            except Exception as cleanup_error:
                print(f"⚠️ Erro ao limpar teste: {cleanup_error}")
            
            return True
        else:
            print("❌ Inserção não retornou ID")
            return False
            
    except Exception as e:
        print(f"❌ ERRO na inserção SQL: {e}")
        print(f"   Tipo: {type(e).__name__}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Função principal"""
    print("🔧 DEBUG SIMPLES - TESTE SQL DIRETO")
    print("=" * 60)
    print(f"⏰ Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Teste de conexão
    try:
        result = DatabaseManager.execute_query("SELECT 1 as test", fetch_one=True)
        if result and result.get('test') == 1:
            print("✅ Conexão com banco funcionando")
        else:
            print("❌ Conexão com banco falhou")
            return
    except Exception as e:
        print(f"❌ ERRO de conexão: {e}")
        return
    
    # Teste SQL direto
    if teste_sql_direto():
        print("\n🎉 TESTE SQL DIRETO PASSOU!")
        print("✅ O problema NÃO está no SQL básico")
        print("\n💡 O erro deve estar em alguma função específica que usa contexto Flask")
    else:
        print("\n❌ TESTE SQL DIRETO FALHOU!")
        print("❌ Há um problema mais fundamental")

if __name__ == "__main__":
    main()
