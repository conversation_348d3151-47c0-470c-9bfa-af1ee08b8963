from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
import pymysql
import hashlib
import secrets
import os
from datetime import datetime, timedelta
import logging
import base64
import numpy as np
import json
from flask_cors import cross_origin

# ===================================================
# NOVAS ROTAS API PARA SISTEMA BIOMÉTRICO V2.0
# ===================================================

@app.route('/api/biometric/verify', methods=['POST'])
@cross_origin()
def api_biometric_verify():
    """
    API para verificação de template biométrico
    Compara template capturado com banco de dados
    """
    try:
        data = request.get_json()
        
        # Validações de segurança
        if not data or 'template' not in data:
            return jsonify({'success': False, 'error': 'Template biométrico não fornecido'}), 400
        
        template = data['template']
        device_info = data.get('device_info', {})
        
        # Validar integridade do template
        if not isinstance(template, list) or len(template) < 10:
            return jsonify({'success': False, 'error': 'Template inválido'}), 400
        
        # Verificar hash de segurança
        security_hash = request.headers.get('X-Security-Hash')
        timestamp = request.headers.get('X-Timestamp')
        
        if not security_hash or not timestamp:
            return jsonify({'success': False, 'error': 'Cabeçalhos de segurança ausentes'}), 400
        
        # Validar timestamp (máximo 5 minutos)
        current_time = datetime.now().timestamp() * 1000
        request_time = int(timestamp)
        
        if abs(current_time - request_time) > 300000:  # 5 minutos
            return jsonify({'success': False, 'error': 'Request expirado'}), 400
        
        # Buscar todos os funcionários com biometria
        cur = mysql.connection.cursor()
        cur.execute("""
            SELECT f.id, f.nome, f.cpf, f.template_biometrico 
            FROM funcionarios f 
            WHERE f.template_biometrico IS NOT NULL 
            AND f.template_biometrico != ''
            AND f.status = 'ativo'
        """)
        
        funcionarios = cur.fetchall()
        cur.close()
        
        # Comparar template com banco de dados
        best_match = None
        best_similarity = 0.0
        threshold = 0.7  # Limite mínimo de similaridade
        
        for funcionario in funcionarios:
            try:
                # Converter template do banco para array numpy
                stored_template = json.loads(funcionario['template_biometrico'])
                if isinstance(stored_template, list):
                    stored_array = np.array(stored_template, dtype=np.float32)
                    captured_array = np.array(template, dtype=np.float32)
                    
                    # Calcular similaridade usando correlação
                    if len(stored_array) == len(captured_array):
                        correlation = np.corrcoef(stored_array, captured_array)[0, 1]
                        similarity = abs(correlation) if not np.isnan(correlation) else 0.0
                        
                        if similarity > best_similarity and similarity >= threshold:
                            best_similarity = similarity
                            best_match = funcionario
                            
            except (json.JSONDecodeError, ValueError, TypeError) as e:
                continue  # Ignorar templates corrompidos
        
        if best_match:
            # Registrar tentativa de verificação bem-sucedida
            cur = mysql.connection.cursor()
            cur.execute("""
                INSERT INTO logs_biometria (
                    funcionario_id, 
                    timestamp, 
                    similarity_score,
                    device_info,
                    status
                ) VALUES (%s, %s, %s, %s, 'success')
            """, (
                best_match['id'],
                datetime.now(),
                best_similarity,
                json.dumps(device_info)
            ))
            mysql.connection.commit()
            cur.close()
            
            return jsonify({
                'success': True,
                'employee': {
                    'id': best_match['id'],
                    'nome': best_match['nome'],
                    'cpf': best_match['cpf']
                },
                'similarity': best_similarity,
                'message': 'Biometria verificada com sucesso'
            })
        else:
            # Registrar tentativa falhada
            cur = mysql.connection.cursor()
            cur.execute("""
                INSERT INTO logs_biometria (
                    funcionario_id, 
                    timestamp, 
                    similarity_score,
                    device_info,
                    status
                ) VALUES (NULL, %s, %s, %s, 'failed')
            """, (
                datetime.now(),
                best_similarity,
                json.dumps(device_info)
            ))
            mysql.connection.commit()
            cur.close()
            
            return jsonify({
                'success': False,
                'error': 'Biometria não encontrada',
                'message': 'Template não corresponde a nenhum funcionário cadastrado'
            }), 404
            
    except Exception as e:
        logger.error(f"Erro na verificação biométrica: {str(e)}")
        return jsonify({'success': False, 'error': 'Erro interno do servidor'}), 500

@app.route('/api/attendance/register', methods=['POST'])
@cross_origin()
def api_attendance_register():
    """
    API para registro automático de ponto
    Detecta automaticamente o tipo baseado no horário
    """
    try:
        data = request.get_json()
        
        # Validações
        if not data or 'employee_id' not in data:
            return jsonify({'success': False, 'error': 'ID do funcionário não fornecido'}), 400
        
        employee_id = data['employee_id']
        template_hash = data.get('template_hash')
        device_info = data.get('device_info', {})
        
        # Validar funcionário
        cur = mysql.connection.cursor()
        cur.execute("SELECT * FROM funcionarios WHERE id = %s AND status = 'ativo'", (employee_id,))
        funcionario = cur.fetchone()
        
        if not funcionario:
            cur.close()
            return jsonify({'success': False, 'error': 'Funcionário não encontrado'}), 404
        
        # Obter horário atual
        now = datetime.now()
        today = now.date()
        current_time = now.time()
        
        # Verificar se já existe registro duplicado recente (últimos 5 minutos)
        cur.execute("""
            SELECT id FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND DATE(data_hora) = %s 
            AND data_hora > %s
        """, (employee_id, today, now - timedelta(minutes=5)))
        
        recent_record = cur.fetchone()
        if recent_record:
            cur.close()
            return jsonify({'success': False, 'error': 'Registro duplicado detectado'}), 409
        
        # Determinar tipo de registro baseado no horário
        attendance_type = determine_attendance_type(current_time, employee_id, today, cur)
        
        # Determinar status (pontual, atrasado, adiantado)
        status = determine_attendance_status(current_time, attendance_type)
        
        # Registrar ponto
        cur.execute("""
            INSERT INTO registros_ponto (
                funcionario_id,
                data_hora,
                tipo,
                status,
                biometria_verificada,
                device_hash,
                user_agent,
                security_score
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            employee_id,
            now,
            attendance_type,
            status,
            1,  # Biometria verificada
            template_hash,
            request.headers.get('User-Agent', ''),
            calculate_security_score(device_info)
        ))
        
        registro_id = cur.lastrowid
        
        # Registrar log de auditoria
        cur.execute("""
            INSERT INTO logs_seguranca (
                tipo_evento,
                funcionario_id,
                detalhes,
                timestamp,
                ip_address,
                user_agent
            ) VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            'registro_ponto',
            employee_id,
            json.dumps({
                'tipo': attendance_type,
                'status': status,
                'device_info': device_info,
                'registro_id': registro_id
            }),
            now,
            request.remote_addr,
            request.headers.get('User-Agent', '')
        ))
        
        mysql.connection.commit()
        cur.close()
        
        return jsonify({
            'success': True,
            'registro_id': registro_id,
            'employee_name': funcionario['nome'],
            'attendance_type': get_attendance_type_name(attendance_type),
            'status': get_status_name(status),
            'timestamp': now.strftime('%d/%m/%Y %H:%M:%S'),
            'message': f'Ponto registrado: {get_attendance_type_name(attendance_type)}'
        })
        
    except Exception as e:
        logger.error(f"Erro no registro de ponto: {str(e)}")
        return jsonify({'success': False, 'error': 'Erro interno do servidor'}), 500

@app.route('/api/system/status', methods=['GET'])
@cross_origin()
def api_system_status():
    """
    API para status do sistema em tempo real
    """
    try:
        cur = mysql.connection.cursor()
        
        # Estatísticas do dia
        today = datetime.now().date()
        cur.execute("""
            SELECT 
                COUNT(*) as total_registros,
                COUNT(DISTINCT funcionario_id) as funcionarios_ativos,
                SUM(CASE WHEN status = 'atrasado' THEN 1 ELSE 0 END) as atrasos,
                AVG(security_score) as security_score_medio
            FROM registros_ponto 
            WHERE DATE(data_hora) = %s
        """, (today,))
        
        stats = cur.fetchone()
        
        # Últimos registros
        cur.execute("""
            SELECT 
                f.nome,
                rp.tipo,
                rp.status,
                rp.data_hora
            FROM registros_ponto rp
            JOIN funcionarios f ON rp.funcionario_id = f.id
            WHERE DATE(rp.data_hora) = %s
            ORDER BY rp.data_hora DESC
            LIMIT 10
        """, (today,))
        
        recent_records = cur.fetchall()
        
        # Status de segurança
        cur.execute("""
            SELECT COUNT(*) as tentativas_falhadas
            FROM logs_biometria 
            WHERE DATE(timestamp) = %s AND status = 'failed'
        """, (today,))
        
        security_stats = cur.fetchone()
        
        cur.close()
        
        return jsonify({
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'statistics': {
                'total_registros': stats['total_registros'] or 0,
                'funcionarios_ativos': stats['funcionarios_ativos'] or 0,
                'atrasos': stats['atrasos'] or 0,
                'security_score_medio': round(stats['security_score_medio'] or 0, 2),
                'tentativas_falhadas': security_stats['tentativas_falhadas'] or 0
            },
            'recent_records': [
                {
                    'funcionario': record['nome'],
                    'tipo': get_attendance_type_name(record['tipo']),
                    'status': get_status_name(record['status']),
                    'horario': record['data_hora'].strftime('%H:%M:%S')
                }
                for record in recent_records
            ],
            'system_health': 'operational'
        })
        
    except Exception as e:
        logger.error(f"Erro ao obter status do sistema: {str(e)}")
        return jsonify({'success': False, 'error': 'Erro interno do servidor'}), 500

# ===================================================
# ATUALIZAÇÃO DA ROTA BIOMÉTRICA EXISTENTE
# ===================================================

@app.route('/registro-ponto/biometrico')
def registro_biometrico():
    """
    Página de registro biométrico atualizada
    """
    if 'user_id' not in session:
        flash('Acesso negado. Faça login primeiro.', 'error')
        return redirect(url_for('login'))
    
    return render_template('biometrico.html')

# Funções auxiliares

def determine_attendance_type(current_time, employee_id, date, cursor):
    """
    Determina o tipo de registro baseado no horário e registros existentes
    """
    # Buscar registros do dia
    cursor.execute("""
        SELECT tipo FROM registros_ponto 
        WHERE funcionario_id = %s AND DATE(data_hora) = %s 
        ORDER BY data_hora ASC
    """, (employee_id, date))
    
    existing_records = [row['tipo'] for row in cursor.fetchall()]
    
    # Janelas de horário (configuráveis)
    morning_start = time(7, 0)   # 7:00
    morning_end = time(9, 30)    # 9:30
    lunch_out_start = time(11, 30)  # 11:30
    lunch_out_end = time(13, 30)    # 13:30
    lunch_return_start = time(13, 30)  # 13:30
    lunch_return_end = time(15, 0)     # 15:00
    evening_start = time(17, 0)        # 17:00
    evening_end = time(19, 0)          # 19:00
    
    # Lógica de determinação
    if 'entrada' not in existing_records:
        if morning_start <= current_time <= morning_end:
            return 'entrada'
        else:
            return 'entrada'  # Entrada fora do horário
    
    elif 'saida_almoco' not in existing_records:
        if lunch_out_start <= current_time <= lunch_out_end:
            return 'saida_almoco'
        elif current_time >= evening_start:
            return 'saida'  # Pular almoço e ir direto para saída
        else:
            return 'saida_almoco'
    
    elif 'entrada_almoco' not in existing_records:
        if lunch_return_start <= current_time <= lunch_return_end:
            return 'entrada_almoco'
        elif current_time >= evening_start:
            return 'saida'  # Pular volta do almoço
        else:
            return 'entrada_almoco'
    
    else:
        return 'saida'  # Último registro do dia

def determine_attendance_status(current_time, attendance_type):
    """
    Determina se está pontual, atrasado ou adiantado
    """
    # Horários esperados (configuráveis)
    expected_times = {
        'entrada': time(8, 0),      # 8:00
        'saida_almoco': time(12, 0), # 12:00
        'entrada_almoco': time(13, 0), # 13:00
        'saida': time(18, 0)         # 18:00
    }
    
    tolerance = timedelta(minutes=15)  # Tolerância de 15 minutos
    
    expected_time = expected_times.get(attendance_type)
    if not expected_time:
        return 'pontual'
    
    # Converter para datetime para comparação
    today = datetime.now().date()
    expected_datetime = datetime.combine(today, expected_time)
    current_datetime = datetime.combine(today, current_time)
    
    diff = current_datetime - expected_datetime
    
    if abs(diff) <= tolerance:
        return 'pontual'
    elif diff > tolerance:
        return 'atrasado' if attendance_type in ['entrada', 'entrada_almoco'] else 'adiantado'
    else:
        return 'adiantado' if attendance_type in ['entrada', 'entrada_almoco'] else 'atrasado'

def calculate_security_score(device_info):
    """
    Calcula score de segurança baseado nas informações do dispositivo
    """
    score = 100
    
    # Verificar vendor_id e product_id corretos
    if device_info.get('vendor_id') != 0x1b55:
        score -= 50
    if device_info.get('product_id') != 0x4500:
        score -= 50
    
    # Verificar user_agent
    user_agent = device_info.get('user_agent', '')
    if 'Chrome' not in user_agent:
        score -= 10
    
    return max(score, 0)

def get_attendance_type_name(tipo):
    """
    Converte tipo de registro para nome amigável
    """
    types = {
        'entrada': 'Entrada',
        'saida_almoco': 'Saída para Almoço',
        'entrada_almoco': 'Volta do Almoço',
        'saida': 'Saída'
    }
    return types.get(tipo, tipo)

def get_status_name(status):
    """
    Converte status para nome amigável
    """
    statuses = {
        'pontual': 'Pontual',
        'atrasado': 'Atrasado',
        'adiantado': 'Adiantado'
    }
    return statuses.get(status, status)

@app.route('/teste-clientes-direto')
def teste_clientes_direto():
    """Rota de teste direta no app.py"""
    return """
    <h1>Teste Direto - Clientes</h1>
    <p>Esta rota está funcionando diretamente no app.py!</p>
    <p>Sessão: {}</p>
    """.format(dict(session))