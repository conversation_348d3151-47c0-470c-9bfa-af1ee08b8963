-- Script para corrigir inconsistências na tabela funcionários
-- Execute este script no banco de dados controle_ponto

USE controle_ponto;

-- 1. Remover campos redundantes de jornada (mantém apenas os específicos)
-- Os campos jornada_entrada e jornada_saida são redundantes pois temos:
-- jornada_seg_qui_entrada, jornada_seg_qui_saida, jornada_sex_entrada, jornada_sex_saida
ALTER TABLE funcionarios 
DROP COLUMN IF EXISTS jornada_entrada,
DROP COLUMN IF EXISTS jornada_saida;

-- 2. Remover campo assinatura (conforme solicitado)
ALTER TABLE funcionarios 
DROP COLUMN IF EXISTS assinatura;

-- 3. Verificar se existe tabela EPIs (criar se não existir)
CREATE TABLE IF NOT EXISTS epis (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int DEFAULT NULL,
  epi_nome varchar(255) DEFAULT NULL,
  epi_ca varchar(50) DEFAULT NULL,
  epi_data_entrega date DEFAULT NULL,
  epi_data_validade date DEFAULT NULL,
  epi_observacoes text DEFAULT NULL,
  PRIMARY KEY (id),
  FOREIGN KEY (funcionario_id) REFERENCES funcionarios (id) ON DELETE CASCADE
) ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci;

-- 4. Verificar estrutura atual da tabela
DESCRIBE funcionarios; 