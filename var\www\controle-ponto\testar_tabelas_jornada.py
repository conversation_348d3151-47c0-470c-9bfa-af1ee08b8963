#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar qual tabela de jornadas tem dados
"""

import sys
sys.path.append('/var/www/controle-ponto')

def testar_tabelas_jornada():
    """Testar qual tabela de jornadas tem dados"""
    try:
        from utils.database import DatabaseManager
        
        print("🔍 TESTANDO TABELAS DE JORNADA")
        print("=" * 50)
        
        # 1. Testar tabela jornadas_trabalho
        print("\n1. TABELA jornadas_trabalho:")
        print("-" * 30)
        
        try:
            jornadas_trabalho = DatabaseManager.execute_query("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(DISTINCT empresa_id) as empresas_com_jornada
                FROM jornadas_trabalho 
                WHERE ativa = TRUE
            """, fetch_one=True)
            
            print(f"   Total de jornadas: {jornadas_trabalho['total']}")
            print(f"   Empresas com jornada: {jornadas_trabalho['empresas_com_jornada']}")
            
            if jornadas_trabalho['total'] > 0:
                # Mostrar algumas jornadas de exemplo
                exemplos = DatabaseManager.execute_query("""
                    SELECT 
                        j.id, j.empresa_id, j.nome_jornada, j.tipo_jornada,
                        e.nome_fantasia
                    FROM jornadas_trabalho j
                    INNER JOIN empresas e ON j.empresa_id = e.id
                    WHERE j.ativa = TRUE
                    ORDER BY j.empresa_id, j.id
                    LIMIT 5
                """, fetch_all=True)
                
                print("   Exemplos:")
                for ex in exemplos:
                    print(f"     - {ex['nome_jornada']} (Empresa: {ex['nome_fantasia']})")
                    
        except Exception as e:
            print(f"   ❌ Erro ao consultar jornadas_trabalho: {e}")
        
        # 2. Testar tabela horarios_trabalho
        print("\n2. TABELA horarios_trabalho:")
        print("-" * 30)
        
        try:
            horarios_trabalho = DatabaseManager.execute_query("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(DISTINCT empresa_id) as empresas_com_horario
                FROM horarios_trabalho 
                WHERE ativo = TRUE
            """, fetch_one=True)
            
            print(f"   Total de horários: {horarios_trabalho['total']}")
            print(f"   Empresas com horário: {horarios_trabalho['empresas_com_horario']}")
            
            if horarios_trabalho['total'] > 0:
                # Mostrar alguns horários de exemplo
                exemplos = DatabaseManager.execute_query("""
                    SELECT 
                        h.id, h.empresa_id, h.nome_horario, h.entrada_manha, h.saida,
                        e.nome_fantasia
                    FROM horarios_trabalho h
                    INNER JOIN empresas e ON h.empresa_id = e.id
                    WHERE h.ativo = TRUE
                    ORDER BY h.empresa_id, h.id
                    LIMIT 5
                """, fetch_all=True)
                
                print("   Exemplos:")
                for ex in exemplos:
                    print(f"     - {ex['nome_horario']} ({ex['entrada_manha']}-{ex['saida']}) (Empresa: {ex['nome_fantasia']})")
                    
        except Exception as e:
            print(f"   ❌ Erro ao consultar horarios_trabalho: {e}")
        
        # 3. Verificar qual tabela os funcionários estão usando
        print("\n3. FUNCIONÁRIOS E JORNADAS:")
        print("-" * 30)
        
        try:
            # Verificar campo jornada_trabalho_id
            func_jornada = DatabaseManager.execute_query("""
                SELECT COUNT(*) as total
                FROM funcionarios 
                WHERE jornada_trabalho_id IS NOT NULL
            """, fetch_one=True)
            
            print(f"   Funcionários com jornada_trabalho_id: {func_jornada['total']}")
            
            # Verificar campo horario_trabalho_id
            func_horario = DatabaseManager.execute_query("""
                SELECT COUNT(*) as total
                FROM funcionarios 
                WHERE horario_trabalho_id IS NOT NULL
            """, fetch_one=True)
            
            print(f"   Funcionários com horario_trabalho_id: {func_horario['total']}")
            
        except Exception as e:
            print(f"   ❌ Erro ao consultar funcionários: {e}")
        
        # 4. Recomendação
        print("\n💡 RECOMENDAÇÃO:")
        print("-" * 30)
        
        if jornadas_trabalho and jornadas_trabalho['total'] > 0:
            print("   ✅ Usar tabela jornadas_trabalho (mais completa)")
            print("   📝 Endpoint correto: /empresas/api/jornadas/<empresa_id>")
        elif horarios_trabalho and horarios_trabalho['total'] > 0:
            print("   ⚠️ Usar tabela horarios_trabalho (dados existentes)")
            print("   📝 Precisa ajustar endpoint para usar horarios_trabalho")
        else:
            print("   ❌ Nenhuma tabela tem dados - precisa criar jornadas")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False

if __name__ == '__main__':
    testar_tabelas_jornada()
