#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
REDIRECIONAMENTO PARA SERVIÇO BIOMÉTRICO UNIVERSAL v2.0
======================================================

IMPORTANTE: Este arquivo agora redireciona para o novo sistema universal.

ALTERAÇÃO FUNDAMENTAL:
❌ ANTIGO: Sistema específico para ZK4500
✅ NOVO: Sistema universal para qualquer leitor biométrico

COMANDOS:
- Para usar o novo sistema: python universal_biometric_service.py
- Para voltar ao antigo: python backup-build/biometria_service_zkspecific_backup.py

BENEFÍCIOS DA MIGRAÇÃO:
✓ Suporta qualquer fabricante de leitor biométrico
✓ Auto-detecção via Windows Biometric Framework
✓ Sistema de registro permanente de dispositivos  
✓ Zero dependência de hardware específico
✓ Interface de configuração administrativa
✓ Logs estruturados e diagnóstico avançado

Autor: <PERSON> - AiNexus Tecnologia
Sistema: RLPONTO-WEB v1.0
Data: Junho 2025
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configuração básica de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("biometria-redirect")

def main():
    """
    REDIRECIONAMENTO AUTOMÁTICO PARA SERVIÇO UNIVERSAL
    """
    print("=" * 70)
    print("🔄 MIGRAÇÃO AUTOMÁTICA - SISTEMA BIOMÉTRICO UNIVERSAL")
    print("=" * 70)
    print()
    print("❌ SISTEMA ANTERIOR: Específico para ZK4500")
    print("✅ SISTEMA ATUAL: Universal (qualquer leitor biométrico)")
    print()
    print("🚀 Iniciando serviço biométrico universal...")
    print("📡 Porta: 5001")
    print("🔧 Arquivo: universal_biometric_service.py")
    print()
    
    # Verifica se o arquivo universal existe
    universal_service = Path("universal_biometric_service.py")
    if not universal_service.exists():
        print("❌ ERRO: Arquivo universal_biometric_service.py não encontrado!")
        print("💡 Verifique se o arquivo foi criado corretamente.")
        return 1
    
    try:
        # Redireciona para o serviço universal
        logger.info("Redirecionando para serviço biométrico universal...")
        subprocess.run([sys.executable, "universal_biometric_service.py"], check=True)
        
    except KeyboardInterrupt:
        print("\n👋 Serviço interrompido pelo usuário")
        return 0
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Erro ao executar serviço universal: {e}")
        print("💡 Tente executar diretamente: python universal_biometric_service.py")
        return 1
        
    except Exception as e:
        print(f"\n💥 Erro inesperado: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
