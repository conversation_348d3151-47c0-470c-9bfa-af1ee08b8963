#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar usuário com nível gerente
Sistema: RLPONTO-WEB v1.0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database import get_db_connection
from werkzeug.security import generate_password_hash
import pymysql

def criar_usuario_gerente():
    """Cria um usuário com nível gerente para testar o controle de período"""
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Dados do usuário gerente
        usuario = "gerente"
        senha = "gerente123"  # Senha temporária
        nome_completo = "Gerente de Período"
        email = "<EMAIL>"
        nivel_acesso = "gerente"
        
        # Hash da senha
        senha_hash = generate_password_hash(senha)
        
        # Verificar se usuário já existe
        cursor.execute("SELECT id FROM usuarios WHERE usuario = %s", (usuario,))
        if cursor.fetchone():
            print(f"❌ Usuário '{usuario}' já existe!")
            
            # Atualizar nível de acesso
            cursor.execute("""
                UPDATE usuarios 
                SET nivel_acesso = %s 
                WHERE usuario = %s
            """, (nivel_acesso, usuario))
            conn.commit()
            print(f"✅ Nível de acesso do usuário '{usuario}' atualizado para '{nivel_acesso}'")
            
        else:
            # Criar novo usuário
            cursor.execute("""
                INSERT INTO usuarios (usuario, senha, nome_completo, email, nivel_acesso, ativo, data_criacao)
                VALUES (%s, %s, %s, %s, %s, 1, NOW())
            """, (usuario, senha_hash, nome_completo, email, nivel_acesso))
            conn.commit()
            print(f"✅ Usuário '{usuario}' criado com sucesso!")
            print(f"   📧 Email: {email}")
            print(f"   🔑 Senha: {senha}")
            print(f"   👤 Nível: {nivel_acesso}")
        
        cursor.close()
        conn.close()
        
        print("\n🎯 INSTRUÇÕES:")
        print(f"1. Acesse: http://10.19.208.31:5000/login")
        print(f"2. Usuário: {usuario}")
        print(f"3. Senha: {senha}")
        print(f"4. Teste o acesso ao Controle de Período")
        
    except Exception as e:
        print(f"❌ Erro ao criar usuário: {e}")

if __name__ == "__main__":
    criar_usuario_gerente()
