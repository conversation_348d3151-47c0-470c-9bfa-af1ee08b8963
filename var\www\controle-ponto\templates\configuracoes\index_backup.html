{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    /* DESIGN SYSTEM PROFISSIONAL - BASEADO EM @21ST-DEV/MAGIC */
    :root {
        --config-primary: #0f172a;
        --config-secondary: #64748b;
        --config-accent: #3b82f6;
        --config-success: #10b981;
        --config-warning: #f59e0b;
        --config-danger: #ef4444;
        --config-surface: #ffffff;
        --config-muted: #f8fafc;
        --config-border: #e2e8f0;
        --config-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --config-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    }

    /* LAYOUT PRINCIPAL */
    .config-container {
        padding: 2rem 0;
        max-width: 1200px;
        margin: 0 auto;
    }

    /* HEADER PROFISSIONAL */
    .config-header {
        background: linear-gradient(135deg, var(--config-primary) 0%, var(--config-secondary) 100%);
        color: white;
        padding: 2rem;
        border-radius: 0.75rem;
        margin-bottom: 2rem;
        box-shadow: var(--config-shadow-lg);
    }

    .config-header h1 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 600;
        letter-spacing: -0.025em;
    }

    .config-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 1rem;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        backdrop-filter: blur(8px);
    }



    /* TABS PROFISSIONAIS */
    .config-tabs {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.75rem;
        box-shadow: var(--config-shadow);
        overflow: hidden;
    }

    .nav-tabs {
        background: var(--config-muted);
        border-bottom: 1px solid var(--config-border);
        padding: 0;
        margin: 0;
        display: flex;
    }

    .nav-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 1.5rem;
        color: var(--config-secondary);
        background: transparent;
        border: none;
        font-weight: 500;
        font-size: 0.875rem;
        text-decoration: none;
        transition: all 0.2s ease;
        border-radius: 0;
        position: relative;
    }

    .nav-link:hover {
        background: rgba(59, 130, 246, 0.05);
        color: var(--config-accent);
    }

    .nav-link.active {
        background: var(--config-surface);
        color: var(--config-accent);
        border-bottom: 2px solid var(--config-accent);
    }

    .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--config-accent);
    }

    /* CONTEÚDO DAS TABS - FORÇAR EXIBIÇÃO */
    .tab-content {
        padding: 2rem;
        min-height: 400px;
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .tab-pane {
        display: none !important;
    }

    .tab-pane.active {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        height: auto !important;
        overflow: visible !important;
        position: relative !important;
        z-index: 10 !important;
        background: white !important;
        width: 100% !important;
        min-height: 200px !important;
    }
    
    .tab-pane.show {
        display: block !important;
        opacity: 1 !important;
    }
    
    .tab-pane.active.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        position: relative !important;
        z-index: 10 !important;
    }

    /* FORÇAR EXIBIÇÃO DO CONTEÚDO BIOMÉTRICO */
    #biometria.active {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        background: #f8f9fa !important;
        border: 2px solid #28a745 !important;
        padding: 20px !important;
        margin: 10px 0 !important;
    }

    #biometria .config-section {
        display: block !important;
        visibility: visible !important;
    }

    #biometria .action-grid {
        display: grid !important;
        visibility: visible !important;
    }

    /* SEÇÕES DE CONFIGURAÇÃO */
    .config-section {
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--config-primary);
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--config-border);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* GRID DE AÇÕES */
    .action-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    /* CARDS DE AÇÃO */
    .action-card {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .action-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--config-shadow-lg);
        border-color: var(--config-accent);
    }

    .action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--config-accent), var(--config-success));
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .action-card:hover::before {
        opacity: 1;
    }

    .action-card .icon {
        width: 3.5rem;
        height: 3.5rem;
        background: var(--config-muted);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: var(--config-accent);
        font-size: 1.5rem;
        transition: all 0.2s ease;
    }

    .action-card:hover .icon {
        background: var(--config-accent);
        color: white;
        transform: scale(1.1);
    }

    .action-card h5 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--config-primary);
        margin-bottom: 0.5rem;
    }

    .action-card p {
        color: var(--config-secondary);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    /* BOTÕES PROFISSIONAIS */
    .btn-professional {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.625rem 1.25rem;
        background: var(--config-accent);
        color: white;
        text-decoration: none;
        border-radius: 0.5rem;
        font-weight: 500;
        font-size: 0.875rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .btn-professional:hover {
        background: #2563eb;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-success {
        background: var(--config-success);
    }

    .btn-success:hover {
        background: #059669;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    .btn-warning {
        background: var(--config-warning);
    }

    .btn-warning:hover {
        background: #d97706;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
    }

    .btn-outline {
        background: transparent;
        color: var(--config-accent);
        border: 1px solid var(--config-accent);
    }

    .btn-outline:hover {
        background: var(--config-accent);
        color: white;
    }

    /* DESTACAR SISTEMA BIOMÉTRICO */
    .biometric-card {
        border-color: var(--config-success);
        background: linear-gradient(145deg, #f0fdf4, #dcfce7);
    }

    .biometric-card .icon {
        background: var(--config-success);
        color: white;
    }

    .biometric-card::before {
        background: var(--config-success);
        opacity: 1;
    }

    /* RESPONSIVIDADE */
    @media (max-width: 768px) {
        .config-container {
            padding: 1rem;
        }

        .config-header {
            padding: 1.5rem;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .tab-content {
            padding: 1.5rem;
        }

        .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.8rem;
        }
    }

    /* ANIMAÇÕES SUTIS */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .tab-pane.active {
        animation: fadeIn 0.3s ease-out;
    }

    /* AJUSTES DE ÍCONES */
    .fas, .far {
        font-size: inherit;
    }

    /* STATUS ONLINE BIOMÉTRICO */
    .stat-card.online {
        border-left-color: var(--config-success);
        background: linear-gradient(135deg, #f8fffe 0%, #f0fff8 100%);
    }
    
    .stat-card.online .icon {
        color: var(--config-success);
    }

    /* MODAL RESPONSIVO */
    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }

    /* NOTIFICAÇÕES */
    .alert {
        border: none;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* ESTILOS PARA DISPOSITIVOS DETECTADOS */
    .detected-devices-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .device-card {
        background: var(--config-surface);
        border: 1px solid var(--config-border);
        border-radius: 0.5rem;
        padding: 1rem;
        transition: all 0.2s ease;
    }

    .device-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--config-shadow-lg);
    }

    .device-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .device-icon {
        width: 2.5rem;
        height: 2.5rem;
        background: var(--config-muted);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--config-accent);
        font-size: 1.1rem;
    }

    .device-info h6 {
        margin: 0;
        font-weight: 600;
        color: var(--config-primary);
    }

    .device-info p {
        margin: 0;
        font-size: 0.85rem;
        color: var(--config-secondary);
    }

    .device-details {
        font-size: 0.8rem;
        color: var(--config-secondary);
        margin-bottom: 1rem;
    }

    .device-actions {
        display: flex;
        gap: 0.5rem;
    }

    .btn-device {
        flex: 1;
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.375rem;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        text-align: center;
    }

    .btn-device.primary {
        background: var(--config-accent);
        color: white;
    }

    .btn-device.primary:hover {
        background: #5a67d8;
        color: white;
    }

    .btn-device.secondary {
        background: var(--config-muted);
        color: var(--config-secondary);
    }

    .btn-device.secondary:hover {
        background: #e2e8f0;
        color: var(--config-primary);
    }

    /* CARD ESPECIAL PARA CONFIGURAÇÃO DA EMPRESA */
    .empresa-config-card {
        border: 2px solid #667eea;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    }

    .empresa-config-card::before {
        background: linear-gradient(90deg, #667eea, #764ba2);
        opacity: 1;
    }

    .empresa-config-card .icon {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="config-container">
    <!-- Header Profissional -->
    <div class="config-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1><i class="fas fa-cog me-2"></i>Configurações do Sistema</h1>
                <p>Painel de administração e configuração do RLPONTO-WEB</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="status-badge">
                    <i class="fas fa-circle"></i>Sistema Online
                </div>
            </div>
        </div>
    </div>



    <!-- Tabs de Configuração -->
    <div class="config-tabs">
        <ul class="nav nav-tabs" id="configTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="geral-tab" data-bs-toggle="tab" data-bs-target="#geral" type="button" role="tab" aria-controls="geral" aria-selected="true">
                    <i class="fas fa-cog"></i>
                    <span>Geral</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="empresas-tab" data-bs-toggle="tab" data-bs-target="#empresas" type="button" role="tab" aria-controls="empresas" aria-selected="false">
                    <i class="fas fa-building"></i>
                    <span>Empresas</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="usuarios-tab" data-bs-toggle="tab" data-bs-target="#usuarios" type="button" role="tab" aria-controls="usuarios" aria-selected="false">
                    <i class="fas fa-users"></i>
                    <span>Usuários</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="biometria-tab" data-bs-toggle="tab" data-bs-target="#biometria" type="button" role="tab" aria-controls="biometria" aria-selected="false">
                    <i class="fas fa-fingerprint"></i>
                    <span>Biometria</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="dispositivos-tab" data-bs-toggle="tab" data-bs-target="#dispositivos" type="button" role="tab" aria-controls="dispositivos" aria-selected="false">
                    <i class="fas fa-usb"></i>
                    <span>Dispositivos</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sistema-tab" data-bs-toggle="tab" data-bs-target="#sistema" type="button" role="tab" aria-controls="sistema" aria-selected="false">
                    <i class="fas fa-server"></i>
                    <span>Sistema</span>
                </button>
            </li>
        </ul>

        <div class="tab-content" id="configTabsContent">
            <!-- Tab Geral -->
            <div class="tab-pane active show" id="geral" role="tabpanel" aria-labelledby="geral-tab">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-cog"></i>
                        Configurações Gerais do Sistema
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-shield-alt"></i></div>
                            <h5>Configurações de Segurança</h5>
                            <p>Configurar políticas de senha e sessão</p>
                            <button class="btn-professional btn-success" onclick="mostrarSeguranca()">
                                <i class="fas fa-shield-alt"></i>Configurar
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-database"></i></div>
                            <h5>Backup do Sistema</h5>
                            <p>Criar backup completo do banco de dados</p>
                            <button class="btn-professional" onclick="criarBackup()">
                                <i class="fas fa-download"></i>Criar Backup
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-broom"></i></div>
                            <h5>Limpar Cache</h5>
                            <p>Limpar cache do sistema e arquivos temporários</p>
                            <button class="btn-professional btn-warning" onclick="limparCache()">
                                <i class="fas fa-broom"></i>Limpar Cache
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-chart-bar"></i></div>
                            <h5>Ver Estatísticas</h5>
                            <p>Visualizar estatísticas e relatórios do sistema</p>
                            <button class="btn-professional btn-outline" onclick="verEstatisticas()">
                                <i class="fas fa-chart-bar"></i>Ver Estatísticas
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-tools"></i></div>
                            <h5>Configurações Avançadas</h5>
                            <p>Acesso a configurações avançadas do sistema</p>
                            <button class="btn-professional btn-outline" onclick="configurarAvancado()">
                                <i class="fas fa-tools"></i>Configurar
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Empresas -->
            <div class="tab-pane" id="empresas" role="tabpanel" aria-labelledby="empresas-tab">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-building"></i>
                        Gerenciamento de Empresas
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card empresa-config-card">
                            <div class="icon"><i class="fas fa-building"></i></div>
                            <h5>Configurar Empresa</h5>
                            <p>Configure informações da empresa, logotipo e regras específicas conforme MCP</p>
                            <a href="/configuracoes/empresa" class="btn-professional btn-success">
                                <i class="fas fa-cog"></i>Configurar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-plus-circle"></i></div>
                            <h5>Nova Empresa</h5>
                            <p>Cadastrar uma nova empresa no sistema</p>
                            <a href="/configuracoes/empresas/nova" class="btn-professional">
                                <i class="fas fa-plus"></i>Cadastrar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-list"></i></div>
                            <h5>Listar Empresas</h5>
                            <p>Visualizar e editar empresas cadastradas</p>
                            <a href="/configuracoes/empresas" class="btn-professional">
                                <i class="fas fa-eye"></i>Listar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-edit"></i></div>
                            <h5>Editar Empresas</h5>
                            <p>Modificar dados das empresas cadastradas</p>
                            <a href="/configuracoes/empresas" class="btn-professional btn-warning">
                                <i class="fas fa-edit"></i>Editar
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Usuários -->
            <div class="tab-pane" id="usuarios" role="tabpanel" aria-labelledby="usuarios-tab">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-users"></i>
                        Gerenciamento de Usuários
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-user-plus"></i></div>
                            <h5>Novo Usuário</h5>
                            <p>Criar novo usuário administrativo</p>
                            <a href="/configurar_usuarios" class="btn-professional btn-success">
                                <i class="fas fa-user-plus"></i>Criar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-users-cog"></i></div>
                            <h5>Gerenciar Usuários</h5>
                            <p>Editar usuários e permissões</p>
                            <a href="/configurar_usuarios" class="btn-professional">
                                <i class="fas fa-cog"></i>Gerenciar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-key"></i></div>
                            <h5>Alterar Senha</h5>
                            <p>Alterar senha do usuário atual</p>
                            <button class="btn-professional btn-warning" onclick="mostrarFormSenha()">
                                <i class="fas fa-key"></i>Alterar
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Biometria -->
            <div class="tab-pane" id="biometria" role="tabpanel" aria-labelledby="biometria-tab">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-fingerprint"></i>
                        Configurações Biométricas
                    </h4>
                    
                    <!-- Status Cards -->
                    <div class="stats-grid mb-4">
                        <div class="stat-card" id="service-status-card">
                            <div class="icon" id="service-icon"><i class="fas fa-power-off"></i></div>
                            <div class="value" id="service-status">Verificando...</div>
                            <div class="label">Status do Serviço</div>
                        </div>
                        <div class="stat-card" id="devices-count-card">
                            <div class="icon"><i class="fas fa-fingerprint"></i></div>
                            <div class="value" id="devices-count">0</div>
                            <div class="label">Dispositivos Registrados</div>
                        </div>
                        <div class="stat-card" id="last-discovery-card">
                            <div class="icon"><i class="fas fa-search"></i></div>
                            <div class="value" id="last-discovery">Nunca</div>
                            <div class="label">Última Descoberta</div>
                        </div>
                        <div class="stat-card" id="tests-today-card">
                            <div class="icon"><i class="fas fa-check-circle"></i></div>
                            <div class="value" id="tests-today">0</div>
                            <div class="label">Testes Hoje</div>
                        </div>
                    </div>
                    
                    <!-- Action Cards -->
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-play-circle"></i></div>
                            <h5>Iniciar Serviço</h5>
                            <p>Ativa o serviço biométrico universal para detectar leitores</p>
                            <button class="btn-professional btn-success" onclick="startBiometricService()">
                                <i class="fas fa-power-off"></i>Iniciar Serviço
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-search-plus"></i></div>
                            <h5>Descobrir Dispositivos</h5>
                            <p>Detecta automaticamente leitores biométricos conectados</p>
                            <button class="btn-professional" onclick="discoverDevices()" id="discover-btn">
                                <i class="fas fa-search"></i>Descobrir
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-cog"></i></div>
                            <h5>Configurar Parâmetros</h5>
                            <p>Ajusta sensibilidade, timeout e qualidade da captura</p>
                            <button class="btn-professional btn-warning" onclick="showSettingsModal()">
                                <i class="fas fa-sliders-h"></i>Configurar
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-hand-paper"></i></div>
                            <h5>Testar Captura</h5>
                            <p>Realiza teste de captura biométrica para validar funcionamento</p>
                            <button class="btn-professional btn-outline" onclick="testCapture()" id="test-btn">
                                <i class="fas fa-fingerprint"></i>Testar Agora
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Dispositivos -->
            <div class="tab-pane" id="dispositivos" role="tabpanel" aria-labelledby="dispositivos-tab">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-usb"></i>
                        Gerenciamento de Dispositivos Biométricos
                    </h4>
                    
                    <!-- Status Cards -->
                    <div class="stats-grid mb-4">
                        <div class="stat-card" id="total-devices-card">
                            <div class="icon"><i class="fas fa-fingerprint"></i></div>
                            <div class="value" id="total-devices">0</div>
                            <div class="label">Dispositivos Registrados</div>
                        </div>
                        <div class="stat-card" id="active-devices-card">
                            <div class="icon"><i class="fas fa-check-circle"></i></div>
                            <div class="value" id="active-devices">0</div>
                            <div class="label">Dispositivos Ativos</div>
                        </div>
                        <div class="stat-card" id="detected-devices-card">
                            <div class="icon"><i class="fas fa-search"></i></div>
                            <div class="value" id="detected-devices">0</div>
                            <div class="label">Detectados na Última Varredura</div>
                        </div>
                        <div class="stat-card" id="scan-status-card">
                            <div class="icon"><i class="fas fa-sync"></i></div>
                            <div class="value" id="scan-status">Nunca</div>
                            <div class="label">Última Varredura</div>
                        </div>
                    </div>
                    
                    <!-- Action Cards -->
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-search-plus"></i></div>
                            <h5>Detectar Dispositivos</h5>
                            <p>Detecta automaticamente dispositivos biométricos via Windows Biometric Framework</p>
                            <button class="btn-professional btn-success" onclick="scanDevices()" id="scan-btn">
                                <i class="fas fa-search"></i>Iniciar Detecção
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-list"></i></div>
                            <h5>Gerenciar Dispositivos</h5>
                            <p>Visualize e gerencie dispositivos biométricos registrados no sistema</p>
                            <a href="/configuracoes/dispositivos" class="btn-professional">
                                <i class="fas fa-cog"></i>Gerenciar
                            </a>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-plus-circle"></i></div>
                            <h5>Registrar Dispositivos</h5>
                            <p>Registra dispositivos detectados permanentemente no sistema</p>
                            <button class="btn-professional btn-warning" onclick="showRegisterModal()" id="register-btn" disabled>
                                <i class="fas fa-plus"></i>Registrar
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-history"></i></div>
                            <h5>Histórico de Dispositivos</h5>
                            <p>Visualize o histórico de conexões e eventos dos dispositivos</p>
                            <button class="btn-professional btn-outline" onclick="showDeviceHistory()">
                                <i class="fas fa-history"></i>Ver Histórico
                            </button>
                        </div>
                    </div>
                    
                    <!-- Lista de Dispositivos Detectados -->
                    <div id="detected-devices-section" class="mt-4" style="display: none;">
                        <h5><i class="fas fa-search"></i> Dispositivos Detectados</h5>
                        <div id="detected-devices-list" class="detected-devices-grid">
                            <!-- Dispositivos serão carregados via JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Sistema -->
            <div class="tab-pane" id="sistema" role="tabpanel" aria-labelledby="sistema-tab">
                <div class="config-section">
                    <h4 class="section-title">
                        <i class="fas fa-server"></i>
                        Configurações do Sistema
                    </h4>
                    
                    <div class="action-grid">
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-database"></i></div>
                            <h5>Backup do Sistema</h5>
                            <p>Criar backup completo do banco de dados</p>
                            <button class="btn-professional btn-success">
                                <i class="fas fa-download"></i>Criar Backup
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-broom"></i></div>
                            <h5>Limpeza de Cache</h5>
                            <p>Limpar cache e arquivos temporários</p>
                            <button class="btn-professional btn-warning">
                                <i class="fas fa-broom"></i>Limpar Cache
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-chart-bar"></i></div>
                            <h5>Monitoramento</h5>
                            <p>Visualizar estatísticas de performance</p>
                            <button class="btn-professional">
                                <i class="fas fa-chart-bar"></i>Ver Estatísticas
                            </button>
                        </div>
                        
                        <div class="action-card">
                            <div class="icon"><i class="fas fa-cogs"></i></div>
                            <h5>Configurações Avançadas</h5>
                            <p>Configurações técnicas do sistema</p>
                            <button class="btn-professional btn-outline">
                                <i class="fas fa-cogs"></i>Configurar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Configurações de Segurança -->
<div class="modal fade" id="securityModal" tabindex="-1" aria-labelledby="securityModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="securityModalLabel">
                    <i class="fas fa-shield-alt me-2"></i>Configurações de Segurança
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-key me-2"></i>Políticas de Senha</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" checked id="complexPassword">
                            <label class="form-check-label" for="complexPassword">
                                Exigir senhas complexas
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" checked id="passwordExpiry">
                            <label class="form-check-label" for="passwordExpiry">
                                Expiração de senha (90 dias)
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-clock me-2"></i>Sessões</h6>
                        <div class="mb-3">
                            <label for="sessionTimeout" class="form-label">Timeout de Sessão (minutos)</label>
                            <input type="number" class="form-control" id="sessionTimeout" value="30">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="salvarSeguranca()">Salvar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Alterar Senha -->
<div class="modal fade" id="passwordModal" tabindex="-1" aria-labelledby="passwordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="passwordModalLabel">
                    <i class="fas fa-key me-2"></i>Alterar Senha
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="passwordForm">
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">Senha Atual</label>
                        <input type="password" class="form-control" id="currentPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">Nova Senha</label>
                        <input type="password" class="form-control" id="newPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirmar Nova Senha</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="alterarSenha()">Alterar Senha</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Configurações Biométricas -->
<div class="modal fade" id="biometricModal" tabindex="-1" aria-labelledby="biometricModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="biometricModalLabel">
                    <i class="fas fa-sliders-h me-2"></i>Configurações Biométricas
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-fingerprint me-2"></i>Qualidade da Captura</h6>
                        <div class="mb-3">
                            <label for="qualityLevel" class="form-label">Nível de Qualidade</label>
                            <select class="form-select" id="qualityLevel">
                                <option value="baixa">Baixa (Rápida)</option>
                                <option value="media" selected>Média (Recomendada)</option>
                                <option value="alta">Alta (Precisa)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="timeout" class="form-label">Timeout (segundos)</label>
                            <input type="number" class="form-control" id="timeout" value="10" min="5" max="30">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-cog me-2"></i>Configurações Avançadas</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" checked id="autoDetect">
                            <label class="form-check-label" for="autoDetect">
                                Auto-detecção de dispositivos
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="debugMode">
                            <label class="form-check-label" for="debugMode">
                                Modo debug (logs detalhados)
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="salvarConfigBiometrica()">Salvar</button>
            </div>
        </div>
    </div>
</div>

<script>
// JAVASCRIPT - FORÇAR FUNCIONAMENTO DAS ABAS
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Inicializando sistema de abas...');
    
    // Função para mostrar aba específica
    function showTabContent(targetId) {
        console.log('📋 Mostrando aba:', targetId);
        
        // Esconder todas as abas
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.style.display = 'none';
            pane.style.opacity = '0';
            pane.style.visibility = 'hidden';
            pane.classList.remove('active', 'show');
        });
        
        // Remover active de todos os botões
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            link.setAttribute('aria-selected', 'false');
        });
        
        // Mostrar aba selecionada
        const targetPane = document.getElementById(targetId);
        const targetButton = document.getElementById(targetId + '-tab');
        
        if (targetPane && targetButton) {
            // FORÇAR EXIBIÇÃO COMPLETA
            targetPane.style.display = 'block';
            targetPane.style.opacity = '1';
            targetPane.style.visibility = 'visible';
            targetPane.style.height = 'auto';
            targetPane.style.overflow = 'visible';
            targetPane.classList.add('active', 'show');
            
            targetButton.classList.add('active');
            targetButton.setAttribute('aria-selected', 'true');
            
            console.log('✅ Aba', targetId, 'ativada com sucesso!');
            console.log('📋 Conteúdo da aba:', targetPane.innerHTML.length, 'caracteres');
            
            // BYPASS ABSOLUTO - CRIAR ELEMENTO INDEPENDENTE
            if (targetId === 'biometria') {
                console.log('🔥 BYPASS ABSOLUTO DA BIOMETRIA...');
                
                // REMOVER QUALQUER ELEMENTO EXISTENTE
                const existingBypass = document.getElementById('biometria-bypass');
                if (existingBypass) {
                    existingBypass.remove();
                }
                
                // CRIAR INTERFACE PROFISSIONAL MODERNA
                const bypassElement = document.createElement('div');
                bypassElement.id = 'biometria-bypass';
                bypassElement.setAttribute('style', `
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100vw !important;
                    height: 100vh !important;
                    background: rgba(15, 23, 42, 0.95) !important;
                    z-index: 99999 !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    backdrop-filter: blur(20px) !important;
                    animation: fadeIn 0.3s ease-out !important;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                `);
                
                bypassElement.innerHTML = `
                    <style>
                        @keyframes fadeIn {
                            from { opacity: 0; }
                            to { opacity: 1; }
                        }
                        @keyframes slideUp {
                            from { transform: translateY(30px); opacity: 0; }
                            to { transform: translateY(0); opacity: 1; }
                        }
                        @keyframes gradientShift {
                            0% { background-position: 0% 50%; }
                            50% { background-position: 100% 50%; }
                            100% { background-position: 0% 50%; }
                        }
                        @keyframes pulse {
                            0%, 100% { opacity: 1; }
                            50% { opacity: 0.5; }
                        }
                        .gradient-animated {
                            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c) !important;
                            background-size: 400% 400% !important;
                            animation: gradientShift 8s ease infinite !important;
                        }
                        .card-hover:hover {
                            transform: translateY(-8px) scale(1.02) !important;
                            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4) !important;
                        }
                        .glass-effect {
                            background: rgba(255, 255, 255, 0.1) !important;
                            backdrop-filter: blur(20px) !important;
                            border: 1px solid rgba(255, 255, 255, 0.2) !important;
                        }
                    </style>
                    
                    <div class="glass-effect" style="
                        border-radius: 24px !important;
                        padding: 48px !important;
                        max-width: 95vw !important;
                        max-height: 95vh !important;
                        overflow-y: auto !important;
                        box-shadow: 0 32px 64px rgba(0, 0, 0, 0.4) !important;
                        position: relative !important;
                        animation: slideUp 0.4s ease-out !important;
                    ">
                        <!-- Botão Fechar Moderno -->
                        <button onclick="document.getElementById('biometria-bypass').remove()" 
                                style="
                                    position: absolute !important;
                                    top: 20px !important;
                                    right: 24px !important;
                                    background: rgba(239, 68, 68, 0.1) !important;
                                    border: 1px solid rgba(239, 68, 68, 0.3) !important;
                                    color: #ef4444 !important;
                                    font-size: 20px !important;
                                    width: 44px !important;
                                    height: 44px !important;
                                    border-radius: 12px !important;
                                    cursor: pointer !important;
                                    display: flex !important;
                                    align-items: center !important;
                                    justify-content: center !important;
                                    transition: all 0.2s ease !important;
                                    font-weight: 600 !important;
                                " 
                                onmouseover="this.style.background='rgba(239, 68, 68, 0.2)'; this.style.transform='scale(1.1)'" 
                                onmouseout="this.style.background='rgba(239, 68, 68, 0.1)'; this.style.transform='scale(1)'">
                            ✕
                        </button>
                        
                        <!-- Header Profissional -->
                        <div style="text-align: center !important; margin-bottom: 40px !important;">
                            <div style="
                                display: inline-flex !important;
                                align-items: center !important;
                                gap: 16px !important;
                                background: rgba(59, 130, 246, 0.1) !important;
                                padding: 16px 32px !important;
                                border-radius: 16px !important;
                                border: 1px solid rgba(59, 130, 246, 0.2) !important;
                                margin-bottom: 24px !important;
                            ">
                                <div style="
                                    width: 56px !important;
                                    height: 56px !important;
                                    border-radius: 16px !important;
                                    display: flex !important;
                                    align-items: center !important;
                                    justify-content: center !important;
                                    font-size: 28px !important;
                                " class="gradient-animated">
                                    🧬
                                </div>
                                <div style="text-align: left !important;">
                                    <h2 style="
                                        color: white !important; 
                                        font-size: 28px !important; 
                                        margin: 0 !important; 
                                        font-weight: 700 !important;
                                        letter-spacing: -0.5px !important;
                                    ">
                                        Sistema Biométrico
                                    </h2>
                                    <p style="
                                        color: rgba(255,255,255,0.7) !important; 
                                        font-size: 14px !important; 
                                        margin: 4px 0 0 0 !important;
                                        font-weight: 500 !important;
                                    ">
                                        RLPONTO-WEB • Configuração Avançada
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Grid de Cards Modernos -->
                        <div style="
                            display: grid !important; 
                            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important; 
                            gap: 24px !important;
                            margin-bottom: 32px !important;
                        ">
                            <!-- Card 1: Iniciar Serviço -->
                            <div class="card-hover" style="
                                background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(22, 163, 74, 0.1)) !important;
                                border: 1px solid rgba(34, 197, 94, 0.3) !important;
                                border-radius: 20px !important;
                                padding: 32px !important;
                                color: white !important;
                                cursor: pointer !important;
                                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                                position: relative !important;
                                overflow: hidden !important;
                            " onclick="startBiometricService()">
                                <div style="
                                    position: absolute !important;
                                    top: -50% !important;
                                    right: -50% !important;
                                    width: 100% !important;
                                    height: 100% !important;
                                    background: radial-gradient(circle, rgba(34, 197, 94, 0.1) 0%, transparent 70%) !important;
                                    pointer-events: none !important;
                                "></div>
                                <div style="position: relative !important; z-index: 2 !important;">
                                    <div style="
                                        width: 64px !important;
                                        height: 64px !important;
                                        background: rgba(34, 197, 94, 0.2) !important;
                                        border-radius: 16px !important;
                                        display: flex !important;
                                        align-items: center !important;
                                        justify-content: center !important;
                                        font-size: 32px !important;
                                        margin-bottom: 20px !important;
                                        border: 1px solid rgba(34, 197, 94, 0.4) !important;
                                    ">🚀</div>
                                    <h3 style="
                                        margin: 0 0 12px 0 !important; 
                                        font-size: 22px !important;
                                        font-weight: 600 !important;
                                        color: #22c55e !important;
                                    ">Iniciar Serviço</h3>
                                    <p style="
                                        margin: 0 !important; 
                                        opacity: 0.8 !important; 
                                        font-size: 15px !important;
                                        line-height: 1.5 !important;
                                        color: rgba(255,255,255,0.9) !important;
                                    ">
                                        Ativa o serviço biométrico universal para detectar e gerenciar leitores conectados
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Card 2: Descobrir Dispositivos -->
                            <div class="card-hover" style="
                                background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.1)) !important;
                                border: 1px solid rgba(59, 130, 246, 0.3) !important;
                                border-radius: 20px !important;
                                padding: 32px !important;
                                color: white !important;
                                cursor: pointer !important;
                                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                                position: relative !important;
                                overflow: hidden !important;
                            " onclick="discoverDevices()">
                                <div style="
                                    position: absolute !important;
                                    top: -50% !important;
                                    right: -50% !important;
                                    width: 100% !important;
                                    height: 100% !important;
                                    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%) !important;
                                    pointer-events: none !important;
                                "></div>
                                <div style="position: relative !important; z-index: 2 !important;">
                                    <div style="
                                        width: 64px !important;
                                        height: 64px !important;
                                        background: rgba(59, 130, 246, 0.2) !important;
                                        border-radius: 16px !important;
                                        display: flex !important;
                                        align-items: center !important;
                                        justify-content: center !important;
                                        font-size: 32px !important;
                                        margin-bottom: 20px !important;
                                        border: 1px solid rgba(59, 130, 246, 0.4) !important;
                                    ">🔍</div>
                                    <h3 style="
                                        margin: 0 0 12px 0 !important; 
                                        font-size: 22px !important;
                                        font-weight: 600 !important;
                                        color: #3b82f6 !important;
                                    ">Descobrir Dispositivos</h3>
                                    <p style="
                                        margin: 0 !important; 
                                        opacity: 0.8 !important; 
                                        font-size: 15px !important;
                                        line-height: 1.5 !important;
                                        color: rgba(255,255,255,0.9) !important;
                                    ">
                                        Detecta automaticamente leitores biométricos conectados via USB ou rede
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Card 3: Configurar Parâmetros -->
                            <div class="card-hover" style="
                                background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(217, 119, 6, 0.1)) !important;
                                border: 1px solid rgba(245, 158, 11, 0.3) !important;
                                border-radius: 20px !important;
                                padding: 32px !important;
                                color: white !important;
                                cursor: pointer !important;
                                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                                position: relative !important;
                                overflow: hidden !important;
                            " onclick="showSettingsModal()">
                                <div style="
                                    position: absolute !important;
                                    top: -50% !important;
                                    right: -50% !important;
                                    width: 100% !important;
                                    height: 100% !important;
                                    background: radial-gradient(circle, rgba(245, 158, 11, 0.1) 0%, transparent 70%) !important;
                                    pointer-events: none !important;
                                "></div>
                                <div style="position: relative !important; z-index: 2 !important;">
                                    <div style="
                                        width: 64px !important;
                                        height: 64px !important;
                                        background: rgba(245, 158, 11, 0.2) !important;
                                        border-radius: 16px !important;
                                        display: flex !important;
                                        align-items: center !important;
                                        justify-content: center !important;
                                        font-size: 32px !important;
                                        margin-bottom: 20px !important;
                                        border: 1px solid rgba(245, 158, 11, 0.4) !important;
                                    ">⚙️</div>
                                    <h3 style="
                                        margin: 0 0 12px 0 !important; 
                                        font-size: 22px !important;
                                        font-weight: 600 !important;
                                        color: #f59e0b !important;
                                    ">Configurar Parâmetros</h3>
                                    <p style="
                                        margin: 0 !important; 
                                        opacity: 0.8 !important; 
                                        font-size: 15px !important;
                                        line-height: 1.5 !important;
                                        color: rgba(255,255,255,0.9) !important;
                                    ">
                                        Define configurações avançadas de sensibilidade e qualidade biométrica
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Card 4: Testar Captura -->
                            <div class="card-hover" style="
                                background: linear-gradient(135deg, rgba(168, 85, 247, 0.15), rgba(147, 51, 234, 0.1)) !important;
                                border: 1px solid rgba(168, 85, 247, 0.3) !important;
                                border-radius: 20px !important;
                                padding: 32px !important;
                                color: white !important;
                                cursor: pointer !important;
                                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                                position: relative !important;
                                overflow: hidden !important;
                            " onclick="testCapture()">
                                <div style="
                                    position: absolute !important;
                                    top: -50% !important;
                                    right: -50% !important;
                                    width: 100% !important;
                                    height: 100% !important;
                                    background: radial-gradient(circle, rgba(168, 85, 247, 0.1) 0%, transparent 70%) !important;
                                    pointer-events: none !important;
                                "></div>
                                <div style="position: relative !important; z-index: 2 !important;">
                                    <div style="
                                        width: 64px !important;
                                        height: 64px !important;
                                        background: rgba(168, 85, 247, 0.2) !important;
                                        border-radius: 16px !important;
                                        display: flex !important;
                                        align-items: center !important;
                                        justify-content: center !important;
                                        font-size: 32px !important;
                                        margin-bottom: 20px !important;
                                        border: 1px solid rgba(168, 85, 247, 0.4) !important;
                                    ">🧪</div>
                                    <h3 style="
                                        margin: 0 0 12px 0 !important; 
                                        font-size: 22px !important;
                                        font-weight: 600 !important;
                                        color: #a855f7 !important;
                                    ">Testar Captura</h3>
                                    <p style="
                                        margin: 0 !important; 
                                        opacity: 0.8 !important; 
                                        font-size: 15px !important;
                                        line-height: 1.5 !important;
                                        color: rgba(255,255,255,0.9) !important;
                                    ">
                                        Executa teste completo de captura biométrica em tempo real
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Footer com Status -->
                        <div style="
                            text-align: center !important;
                            padding: 24px !important;
                            background: rgba(255, 255, 255, 0.05) !important;
                            border-radius: 16px !important;
                            border: 1px solid rgba(255, 255, 255, 0.1) !important;
                        ">
                            <div style="
                                display: inline-flex !important;
                                align-items: center !important;
                                gap: 12px !important;
                                color: rgba(255,255,255,0.8) !important;
                                font-size: 14px !important;
                                font-weight: 500 !important;
                            ">
                                <div style="
                                    width: 8px !important;
                                    height: 8px !important;
                                    background: #22c55e !important;
                                    border-radius: 50% !important;
                                    animation: pulse 2s infinite !important;
                                "></div>
                                Sistema Biométrico Ativo • Pronto para Configuração
                            </div>
                        </div>
                    </div>
                `;
                
                // ADICIONAR AO BODY (INDEPENDENTE DE QUALQUER ESTRUTURA)
                document.body.appendChild(bypassElement);
                
                console.log('🎯 BYPASS ABSOLUTO APLICADO - ELEMENTO INDEPENDENTE CRIADO!');
            }
        } else {
            console.error('❌ Elementos não encontrados:', targetId);
        }
    }
    
    // Adicionar event listeners para todos os botões de aba
    document.querySelectorAll('#configTabs .nav-link').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const target = this.getAttribute('data-bs-target');
            if (target) {
                const targetId = target.replace('#', '');
                showTabContent(targetId);
            }
        });
    });
    
    // Mostrar primeira aba por padrão
    showTabContent('geral');
    
    // DEBUG: Verificar se aba biometria existe
    const biometriaTab = document.getElementById('biometria');
    if (biometriaTab) {
        console.log('🔍 Aba biometria encontrada! Conteúdo:', biometriaTab.innerHTML.length, 'caracteres');
        console.log('🔍 Primeira linha do conteúdo:', biometriaTab.innerHTML.substring(0, 100));
    } else {
        console.error('❌ Aba biometria NÃO encontrada!');
    }
    
    console.log('🎉 Sistema de abas inicializado!');
});

// Funções para os botões
function mostrarSeguranca() {
    showNotification('Configurações de segurança em desenvolvimento', 'info');
}

function criarBackup() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Criando backup...';
    
    setTimeout(() => {
        button.disabled = false;
        button.innerHTML = originalText;
        showNotification('Backup criado com sucesso!', 'success');
    }, 3000);
}

function limparCache() {
    if (confirm('Deseja realmente limpar o cache do sistema?')) {
        showNotification('Cache do sistema limpo com sucesso!', 'success');
    }
}

function verEstatisticas() {
    window.location.href = '/relatorios/estatisticas';
}

function configurarAvancado() {
    showNotification('Configurações avançadas do sistema em desenvolvimento', 'info');
}

function showSettingsModal() {
    showNotification('Funcionalidade de configuração de parâmetros será implementada em breve', 'info');
}

function startBiometricService() {
    showNotification('Serviço biométrico sendo iniciado...', 'info');
}

function discoverDevices() {
    showNotification('Descobrindo dispositivos biométricos...', 'info');
}

function testCapture() {
    showNotification('Teste de captura em andamento...', 'info');
}

function scanDevices() {
    showNotification('Escaneando dispositivos...', 'info');
}

function registerDevice() {
    showNotification('Registrando dispositivo...', 'info');
}

function loadDeviceHistory() {
    showNotification('Carregando histórico...', 'info');
}

function loadDeviceStatus() {
    showNotification('Carregando status dos dispositivos...', 'info');
}

function mostrarFormSenha() {
    showNotification('Formulário de alteração de senha em desenvolvimento', 'info');
}

function showNotification(message, type = 'info') {
    // Criar elemento de notificação
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Remover após 4 segundos
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 4000);
}
</script>
{% endblock %} 