#!/usr/bin/env python3
"""
Script para reiniciar o Flask
"""

import paramiko

def restart_flask():
    """Reinicia o Flask no servidor"""
    
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    print("🔄 REINICIANDO FLASK")
    print("=" * 40)
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password, timeout=30)
        
        print("✅ Conectado ao servidor")
        
        # Parar Flask
        print("\n🛑 Parando Flask...")
        stdin, stdout, stderr = ssh.exec_command("pkill -f 'python.*app.py' || true")
        stdin, stdout, stderr = ssh.exec_command("sleep 3")
        
        # Limpar cache
        print("🧹 Limpando cache...")
        stdin, stdout, stderr = ssh.exec_command("find /var/www/controle-ponto -name '*.pyc' -delete")
        stdin, stdout, stderr = ssh.exec_command("find /var/www/controle-ponto -name '__pycache__' -type d -exec rm -rf {} + 2>/dev/null || true")
        
        # Iniciar Flask
        print("🚀 Iniciando Flask...")
        stdin, stdout, stderr = ssh.exec_command("cd /var/www/controle-ponto && nohup python3 app.py > flask.log 2>&1 &")
        stdin, stdout, stderr = ssh.exec_command("sleep 5")
        
        # Verificar se iniciou
        print("✅ Verificando Flask...")
        stdin, stdout, stderr = ssh.exec_command("ps aux | grep python | grep app.py | grep -v grep")
        flask_status = stdout.read().decode()
        
        if flask_status:
            print("✅ Flask reiniciado com sucesso!")
            print(f"📋 Processo: {flask_status.strip()}")
        else:
            print("❌ Flask não iniciou!")
        
        ssh.close()
        
        print("\n" + "="*40)
        print("🎉 FLASK REINICIADO!")
        print("🌐 Acesse: http://************/registro-ponto/biometrico")
        
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    restart_flask()
