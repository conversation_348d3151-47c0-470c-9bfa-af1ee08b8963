"""
Blueprint simplificado para teste de funcionários desligados.
"""

import logging
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, make_response
from utils.auth import require_admin, get_current_user
from utils.database import DatabaseManager

# Configuração do logger
logger = logging.getLogger('controle-ponto.funcionarios_desligados')

# Criação do Blueprint
funcionarios_desligados_bp = Blueprint('funcionarios_desligados', __name__, url_prefix='/funcionarios-desligados')

@funcionarios_desligados_bp.route('/')
@require_admin
def index():
    """
    Página principal de funcionários desligados - versão simplificada para teste.
    """
    try:
        logger.info("🔍 Iniciando carregamento de funcionários desligados")
        
        # Teste simples - apenas contar registros
        try:
            total_result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM funcionarios_desligados")
            total_desligados = total_result[0]['total'] if total_result else 0
            logger.info(f"✅ Total de funcionários desligados: {total_desligados}")
        except Exception as e:
            logger.error(f"❌ Erro ao contar funcionários desligados: {e}")
            total_desligados = 0

        # Buscar funcionários desligados - query simples
        try:
            funcionarios_desligados = DatabaseManager.execute_query("""
                SELECT 
                    funcionario_id_original,
                    nome_completo,
                    matricula_empresa,
                    cargo,
                    data_desligamento,
                    motivo_desligamento
                FROM funcionarios_desligados 
                ORDER BY data_desligamento DESC
                LIMIT 10
            """)
            logger.info(f"✅ Funcionários encontrados: {len(funcionarios_desligados) if funcionarios_desligados else 0}")
        except Exception as e:
            logger.error(f"❌ Erro ao buscar funcionários desligados: {e}")
            funcionarios_desligados = []

        context = {
            'funcionarios_desligados': funcionarios_desligados or [],
            'total_desligados': total_desligados,
            'desligados_mes': 0,
            'total_demissoes': 0,
            'total_pedidos': 0,
            'filtros': {
                'motivo': '',
                'periodo': '',
                'busca': ''
            }
        }

        logger.info("✅ Renderizando template")
        return render_template('funcionarios_desligados/index.html', **context)

    except Exception as e:
        logger.error(f"❌ Erro geral ao carregar funcionários desligados: {e}")
        logger.error(f"❌ Tipo do erro: {type(e)}")
        logger.error(f"❌ Args do erro: {e.args}")
        flash("Erro ao carregar dados dos funcionários desligados", "error")
        return redirect(url_for('index'))

@funcionarios_desligados_bp.route('/<int:funcionario_id_original>/detalhes')
@require_admin
def detalhes(funcionario_id_original):
    """
    Página de detalhes simplificada.
    """
    try:
        funcionario_result = DatabaseManager.execute_query("""
            SELECT * FROM funcionarios_desligados 
            WHERE funcionario_id_original = %s
        """, (funcionario_id_original,))
        
        if not funcionario_result:
            flash("Funcionário desligado não encontrado", "error")
            return redirect(url_for('funcionarios_desligados.index'))
        
        funcionario = funcionario_result[0]

        context = {
            'funcionario': funcionario,
            'estatisticas': {
                'total_pontos': 0,
                'total_epis': 0,
                'total_banco_horas': 0,
                'tempo_empresa': 'Não calculado'
            },
            'registros_ponto': []
        }

        return render_template('funcionarios_desligados/detalhes.html', **context)

    except Exception as e:
        logger.error(f"❌ Erro ao carregar detalhes do funcionário desligado {funcionario_id_original}: {e}")
        flash("Erro ao carregar detalhes do funcionário", "error")
        return redirect(url_for('funcionarios_desligados.index'))
