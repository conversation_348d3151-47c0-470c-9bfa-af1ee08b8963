#!/usr/bin/env python3
"""
Script para testar com dados reais do funcionário ID 1.
"""

import sys
import os
import traceback

# Adicionar o diretório do projeto ao path
sys.path.append('/var/www/controle-ponto')

try:
    from app_funcionarios import _extrair_dados_formulario, _validar_dados_funcionario, REQUIRED_FIELDS
    from utils.helpers import FormValidator
    from flask import Flask, request
    from werkzeug.test import EnvironBuilder
    from werkzeug.wrappers import Request
    import pymysql
    
    print("✅ Imports realizados com sucesso")
    
    # Criar uma aplicação Flask de teste
    app = Flask(__name__)
    
    def buscar_dados_funcionario_real():
        """
        Busca dados reais do funcionário ID 1 do banco.
        """
        try:
            connection = pymysql.connect(
                host='localhost',
                user='cavalcrod',
                password='200381',
                database='controle_ponto',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT * FROM funcionarios WHERE id = 1")
                funcionario = cursor.fetchone()
                
                if funcionario:
                    print("✅ Funcionário encontrado no banco:")
                    print(f"  ID: {funcionario['id']}")
                    print(f"  Nome: {funcionario['nome_completo']}")
                    print(f"  Empresa ID: {funcionario['empresa_id']}")
                    return funcionario
                else:
                    print("❌ Funcionário ID 1 não encontrado")
                    return None
                    
        except Exception as e:
            print(f"❌ Erro ao buscar funcionário: {e}")
            return None
        finally:
            if 'connection' in locals():
                connection.close()
    
    def simular_edicao_real():
        """
        Simula edição com dados reais do funcionário.
        """
        print("\n🔍 SIMULANDO EDIÇÃO COM DADOS REAIS")
        print("=" * 50)
        
        funcionario_real = buscar_dados_funcionario_real()
        if not funcionario_real:
            return None, None
        
        # Converter dados do banco para formato do formulário
        form_data = {}
        
        # Mapear campos do banco para campos do formulário
        campo_mapping = {
            'nome_completo': 'nome_completo',
            'cpf': 'cpf',
            'rg': 'rg',
            'data_nascimento': 'data_nascimento',
            'sexo': 'sexo',
            'estado_civil': 'estado_civil',
            'nacionalidade': 'nacionalidade',
            'ctps_numero': 'ctps_numero',
            'ctps_serie_uf': 'ctps_serie_uf',
            'pis_pasep': 'pis_pasep',
            'endereco_cep': 'endereco_cep',
            'endereco_estado': 'endereco_estado',
            'telefone1': 'telefone1',
            'cargo': 'cargo',
            'setor_obra': 'setor_obra',
            'matricula_empresa': 'matricula_empresa',
            'data_admissao': 'data_admissao',
            'tipo_contrato': 'tipo_contrato',
            'nivel_acesso': 'nivel_acesso',
            'turno': 'turno',
            'tolerancia_ponto': 'tolerancia_ponto',
            'status_cadastro': 'status_cadastro',
            'empresa_id': 'empresa_id'
        }
        
        for campo_form, campo_db in campo_mapping.items():
            valor = funcionario_real.get(campo_db)
            if valor is not None:
                # Converter datas para string
                if hasattr(valor, 'strftime'):
                    valor = valor.strftime('%Y-%m-%d')
                form_data[campo_form] = str(valor)
            else:
                form_data[campo_form] = ''
        
        # Adicionar alguns EPIs simulados (como se estivessem sendo editados)
        form_data.update({
            'epis[0][id]': '123',  # ❌ POTENCIAL PROBLEMA
            'epis[0][epi_nome]': 'Capacete',
            'epis[0][epi_ca]': '12345'
        })
        
        print("📋 Dados do formulário (primeiros 10):")
        for i, (key, value) in enumerate(form_data.items()):
            if i < 10:
                print(f"  {key}: {value}")
            elif i == 10:
                print("  ... (mais campos)")
                break
        
        # Criar contexto de requisição e testar
        with app.test_request_context('/', method='POST', data=form_data):
            try:
                print("\n🔍 Extraindo dados...")
                dados_extraidos = _extrair_dados_formulario()
                
                print("✅ Dados extraídos com sucesso")
                
                # Verificar se 'id' está nos dados principais
                if 'id' in dados_extraidos:
                    print(f"\n❌ PROBLEMA: Campo 'id' encontrado: {dados_extraidos['id']}")
                else:
                    print("\n✅ Campo 'id' NÃO está nos dados principais")
                
                print("\n🔍 Validando dados...")
                validator = FormValidator()
                
                # Adicionar funcionario_id como no código real
                dados_extraidos['funcionario_id'] = 1
                
                _validar_dados_funcionario(dados_extraidos, validator)
                
                if validator.has_errors():
                    print("❌ ERROS ENCONTRADOS:")
                    for field, errors in validator.get_errors().items():
                        print(f"  {field}: {errors}")
                        
                        # Se o erro for no campo 'id', investigar mais
                        if field == 'id':
                            print(f"    🔍 INVESTIGANDO ERRO 'id':")
                            print(f"    Valor: {dados_extraidos.get('id', 'NÃO ENCONTRADO')}")
                            print(f"    Tipo: {type(dados_extraidos.get('id', None))}")
                else:
                    print("✅ Validação passou sem erros")
                
                return dados_extraidos, validator
                
            except Exception as e:
                print(f"❌ ERRO durante processamento: {e}")
                print(f"Traceback: {traceback.format_exc()}")
                return None, None
    
    if __name__ == "__main__":
        print("🚀 TESTE COM DADOS REAIS DO FUNCIONÁRIO")
        print("=" * 60)
        
        dados, validator = simular_edicao_real()
        
        print("\n" + "=" * 60)
        print("📊 CONCLUSÃO")
        print("=" * 60)
        
        if dados and validator:
            if validator.has_errors():
                erros = validator.get_errors()
                if 'id' in erros:
                    print("❌ CONFIRMADO: Erro 'id' reproduzido!")
                    print("🔍 Causa identificada nos logs acima")
                else:
                    print("✅ Erro 'id' NÃO reproduzido")
                    print("🤔 Pode ser outro tipo de erro")
            else:
                print("✅ Nenhum erro encontrado")
                print("🤔 O problema pode estar em condições específicas")

except ImportError as e:
    print(f"❌ Erro de import: {e}")
except Exception as e:
    print(f"❌ Erro inesperado: {e}")
    print(f"Traceback: {traceback.format_exc()}")
