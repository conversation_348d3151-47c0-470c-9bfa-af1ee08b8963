﻿--
-- <PERSON>ript was generated by Devart dbForge Studio for MySQL, Version **********
-- Product home page: http://www.devart.com/dbforge/mysql/studio
-- Script date 05/06/2025 10:17:35
-- Server version: 8.0.42
--

--
-- Disable foreign keys
--
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;

--
-- Set SQL mode
--
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;

--
-- Set character set the client will use to send SQL statements to the server
--
SET NAMES 'utf8';

DROP DATABASE IF EXISTS controle_ponto;

CREATE DATABASE IF NOT EXISTS controle_ponto
CHARACTER SET utf8mb4
COLLATE utf8mb4_0900_ai_ci;

--
-- Set default database
--
USE controle_ponto;

--
-- Create table `usuarios`
--
CREATE TABLE IF NOT EXISTS usuarios (
  id int NOT NULL AUTO_INCREMENT,
  usuario varchar(50) NOT NULL,
  senha varchar(255) NOT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 4,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `usuario` on table `usuarios`
--
ALTER TABLE usuarios
ADD UNIQUE INDEX usuario (usuario);

--
-- Create table `permissoes`
--
CREATE TABLE IF NOT EXISTS permissoes (
  usuario_id int NOT NULL,
  nivel_acesso enum ('admin', 'usuario') DEFAULT 'usuario',
  PRIMARY KEY (usuario_id)
)
ENGINE = INNODB,
AVG_ROW_LENGTH = 8192,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE permissoes
ADD CONSTRAINT permissoes_ibfk_1 FOREIGN KEY (usuario_id)
REFERENCES usuarios (id);

--
-- Create table `funcionarios`
--
CREATE TABLE IF NOT EXISTS funcionarios (
  id int NOT NULL AUTO_INCREMENT,
  nome_completo varchar(100) NOT NULL,
  cpf varchar(14) NOT NULL,
  rg varchar(20) NOT NULL,
  data_nascimento date NOT NULL,
  sexo enum ('M', 'F', 'Outro') NOT NULL,
  estado_civil enum ('Solteiro', 'Casado', 'Divorciado', 'Viuvo') NOT NULL,
  nacionalidade varchar(50) NOT NULL,
  ctps_numero varchar(20) NOT NULL,
  ctps_serie_uf varchar(20) NOT NULL,
  pis_pasep varchar(20) NOT NULL,
  endereco_rua varchar(100) DEFAULT NULL,
  endereco_bairro varchar(50) DEFAULT NULL,
  endereco_cidade varchar(50) DEFAULT NULL,
  endereco_cep varchar(10) NOT NULL,
  endereco_estado varchar(2) NOT NULL,
  telefone1 varchar(15) NOT NULL,
  telefone2 varchar(15) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  cargo varchar(50) NOT NULL,
  setor_obra varchar(50) NOT NULL,
  matricula_empresa varchar(20) NOT NULL,
  data_admissao date NOT NULL,
  tipo_contrato enum ('CLT', 'PJ', 'Estagio', 'Temporario') NOT NULL,
  digital_dedo1 blob DEFAULT NULL,
  digital_dedo2 blob DEFAULT NULL,
  foto_3x4 varchar(255) DEFAULT NULL,
  nivel_acesso enum ('Funcionario', 'Supervisao', 'Gerencia') NOT NULL,
  turno enum ('Diurno', 'Noturno', 'Misto') NOT NULL,
  tolerancia_ponto int NOT NULL DEFAULT 5,
  banco_horas tinyint DEFAULT 0,
  hora_extra tinyint DEFAULT 0,
  status_cadastro enum ('Ativo', 'Inativo') NOT NULL DEFAULT 'Ativo',
  data_cadastro datetime DEFAULT CURRENT_TIMESTAMP,
  jornada_seg_qui_entrada time DEFAULT NULL,
  jornada_seg_qui_saida time DEFAULT NULL,
  jornada_sex_entrada time DEFAULT NULL,
  jornada_sex_saida time DEFAULT NULL,
  jornada_intervalo_entrada time DEFAULT NULL,
  jornada_intervalo_saida time DEFAULT NULL,
  biometria_qualidade_1 int DEFAULT NULL COMMENT 'Qualidade da captura do dedo 1 (0-100)',
  biometria_qualidade_2 int DEFAULT NULL COMMENT 'Qualidade da captura do dedo 2 (0-100)',
  biometria_data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data do cadastro biométrico',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 3,
AVG_ROW_LENGTH = 8192,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `cpf` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD UNIQUE INDEX cpf (cpf);

--
-- Create index `matricula_empresa` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD UNIQUE INDEX matricula_empresa (matricula_empresa);

--
-- Create table `registros_ponto`
--
CREATE TABLE IF NOT EXISTS registros_ponto (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int DEFAULT NULL,
  data_hora datetime DEFAULT CURRENT_TIMESTAMP,
  sincronizado tinyint DEFAULT 0,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE registros_ponto
ADD CONSTRAINT registros_ponto_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE;

--
-- Create table `epis`
--
CREATE TABLE IF NOT EXISTS epis (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int NOT NULL,
  epi_nome varchar(255) NOT NULL,
  epi_ca varchar(50) DEFAULT NULL,
  epi_data_entrega date DEFAULT NULL,
  epi_data_validade date DEFAULT NULL,
  epi_observacoes text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE epis
ADD CONSTRAINT epis_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE;

-- 
-- Dumping data for table usuarios
--
INSERT INTO usuarios VALUES
(1, 'admin', 'scrypt:32768:8:1$By9zDLj5suoxDGqU$c9a6953681714a7075a3f0219c995c6ceab167ecfba9d8c4b74a9c6d1e07df4c03a37badc673e58035ae8210171dd2014939b4490ddb2cfe2947e246c71ff765'),
(2, 'richardson', 'scrypt:32768:8:1$AlfFpBW5JksngMiD$63f3e2469f2f876c7c6e035d504de1287b66394750e94d382da9a3bf36130e435f0d7fe1590ead134936089ccdb9a82e115bf73900ab1cf71d35ea807e54b111'),
(3, 'user', 'pbkdf2:sha256:600000$uSwhRwoiwJggWotS$8c63b95583f60d017c3b3ae7cb6fa6f722e73efd34dacef487284f325ab75e19');

-- 
-- Dumping data for table funcionarios
--
INSERT INTO funcionarios VALUES
(1, 'RICHARDSON RODRIGUES', '711.256.042-07', '31.799.841', '1981-03-20', 'M', 'Casado', 'Brasileiro', '0000000', '0000000000', '000.00000.00-0', 'RUA GERMÂNIO', 'VILA DA PRATA', 'MANAUS', '69030-685', 'AM', '(92) 99245-5278', NULL, '<EMAIL>', 'TÉCNICO EM SEGURANÇA', 'MANUTENÇÃO', '0001', '2025-06-03', 'PJ', x'3546383633363142323638444236304433464141323030413831303733363139323034333232394631314442313437393139433332324536303646453231363930393742313437433035453030374444323244303130303231413632304545303043314630423636323636303131364432363245303638323135454531453841313744383138414131373842303941303038354532363141314536303039414631324330303834383041303530453335313231323146453730424137313744343036323931353544303935343137463730393933313044323131463031303637313337393143434630343134314438383138384630383138303642333036313431334233313936313146353131354135303932433139333831423043323641423038333131303235313132443144363330453635313934303039364530454334313539343042343931463138304532323234343230453439323436373041363831393738313944353137343030463031304131393146454132323443313634443043363330344442323033393045334231414431313245463042344430443038314336383145434631383939313330393137423130363535313232453037303630374535314231373045454131353737313641313144313531313439314642323038453431413942303444363042303331434438313235313134414331324443', x'4543313645453233344136304443424234304533374244383037323432413842304432303230443031424144304538313036383330434344313145353146433830444144314142423038433631333334323039333230343732364641323135353132303630384635323033313046373430373645304636433237304631433934303946383232454430373246313337333143363131413444313333303138344531374144323430443137413030463337304242333231303131423831323336313132393530463744323346303139434332343738323432453131354532354532314341433234423030364636304239363046443931394638313238343133343730434632304633313231334331444145314534423234363231393343313042323133444230373733304238373042413830464246304331363231313731423143323533433135354631413239314545373135343331364431313533353139443132343441323642303042303831304144323334313043343330383931314143453233393730424345304239443236364231463830303939463045464332304130323243453034343731433346314146303132374231453138313238433042413731394432313633333236453731434232314532453145394630443635313733463130363131453645303832333037453530413831314544313133313931333939', 'fotos_funcionarios/funcionario_1_20250605T094145.jpg', 'Funcionario', 'Diurno', 5, 1, 1, 'Ativo', '2025-06-02 20:20:12', '07:00:00', '17:00:00', '07:00:00', '16:00:00', '12:00:00', '13:00:00', NULL, NULL, '2025-06-03 17:39:01'),
(2, 'FUNCIONARIO 1', '711.254.854-78', '00000000000000000000', '2000-01-01', 'M', 'Solteiro', 'Brasileiro', '0000000', '0000000000', '000.00000.00-0', 'RUA MENDELÉVIO', 'VILA DA PRATA', 'MANAUS', '69030-170', 'AM', '(92) 99458-8745', NULL, NULL, 'TÉCNICO EM SEGURANÇA', 'MANUTENÇÃO', '0002', '2525-01-01', 'CLT', NULL, NULL, NULL, 'Funcionario', 'Diurno', 5, 1, 1, 'Ativo', '2025-06-03 16:06:05', '07:00:00', '17:00:00', '07:00:00', '16:00:00', '12:00:00', '13:00:00', NULL, NULL, '2025-06-03 17:39:01');

-- Table controle_ponto.registros_ponto does not contain any data (it is empty)

-- 
-- Dumping data for table permissoes
--
INSERT INTO permissoes VALUES
(1, 'admin'),
(2, 'usuario'),
(3, 'usuario');

-- Table controle_ponto.epis does not contain any data (it is empty)

--
-- Restore previous SQL mode
--
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;

--
-- Enable foreign keys
--
/*!40014 SET FOREIGN_KEY_CHECKS = @OLD_FOREIGN_KEY_CHECKS */;