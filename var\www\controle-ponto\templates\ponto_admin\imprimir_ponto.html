<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Ponto - {{ funcionario.nome_completo }}</title>
    <style>
        /* CSS Version: 2025-07-17-v2 - Cabeçalho compacto para impressão */
        /* ========================================
           LAYOUT ORIGINAL - DESIGN MODERNO
           ======================================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.5;
        }

        /* Container principal */
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* Cabeçalho roxo gradiente */
        .header-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            position: relative;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-icon {
            font-size: 24px;
        }

        .header-text h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .header-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .header-buttons {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }

        .btn-print {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-print:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .btn-back {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-back:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Card do funcionário */
        .employee-card {
            margin: 24px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .employee-header {
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .employee-avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 600;
        }

        .employee-info {
            flex: 1;
        }

        .employee-name {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .employee-role {
            color: #718096;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .employee-period {
            color: #4a5568;
            font-size: 13px;
        }

        .employee-details {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            padding: 20px;
            background: #f7fafc;
            border-top: 1px solid #e2e8f0;
        }

        .detail-item {
            text-align: center;
        }

        .detail-label {
            font-size: 12px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
        }

        .signature-line {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
        }

        .signature-text {
            color: #718096;
            font-size: 12px;
            margin-bottom: 40px;
        }

        .signature-border {
            width: 300px;
            height: 1px;
            background: #2d3748;
            margin: 0 auto;
        }

        /* Seção de registros */
        .records-section {
            margin: 24px;
        }

        .records-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .records-icon {
            font-size: 20px;
        }

        .records-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
        }

        /* Tabela moderna */
        .records-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .records-table th {
            background: #f7fafc;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            color: #4a5568;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 2px solid #e2e8f0;
        }

        .records-table td {
            padding: 12px 8px;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
            font-size: 14px;
            color: #2d3748;
        }

        .records-table tr:hover {
            background: #f7fafc;
        }

        .records-table tr:last-child td {
            border-bottom: none;
        }

        /* Badges de justificativa */
        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-abonado {
            background: #c6f6d5;
            color: #22543d;
        }

        .badge-reprovado {
            background: #fed7d7;
            color: #742a2a;
        }

        .badge-pendente {
            background: #fef5e7;
            color: #744210;
        }

        .badge-vazio {
            color: #a0aec0;
            font-style: italic;
            background: none;
        }

        /* Data destacada */
        .date-cell {
            font-weight: 600;
            color: #2d3748;
        }

        .day-cell {
            color: #718096;
            font-size: 12px;
        }

        .hours-cell {
            font-weight: 600;
            color: #2b6cb0;
        }

        /* Estilos para impressão - VERSÃO COMPACTA FORÇADA */
        @media print {
            /* RESET TOTAL E FORÇADO */
            * {
                box-sizing: border-box !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            html, body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                font-size: 12px !important;
            }

            .container {
                box-shadow: none !important;
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .header-buttons {
                display: none !important;
            }

            /* CABEÇALHO SUPER COMPACTO - FORÇADO */
            .header-gradient {
                padding: 4px 10px !important;
                margin: 0 !important;
                height: 35px !important;
                max-height: 35px !important;
                min-height: 35px !important;
                background: #667eea !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                display: flex !important;
                align-items: center !important;
            }

            .header-content {
                margin: 0 !important;
                padding: 0 !important;
                height: auto !important;
                display: flex !important;
                align-items: center !important;
                width: 100% !important;
            }

            .header-title {
                margin: 0 !important;
                padding: 0 !important;
                display: flex !important;
                align-items: center !important;
                gap: 6px !important;
            }

            .header-icon {
                font-size: 12px !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .header-text {
                margin: 0 !important;
                padding: 0 !important;
                line-height: 1 !important;
            }

            .header-text h1 {
                font-size: 13px !important;
                margin: 0 !important;
                padding: 0 !important;
                line-height: 1 !important;
                font-weight: 600 !important;
                color: white !important;
            }

            .header-subtitle {
                font-size: 9px !important;
                margin: 0 !important;
                padding: 0 !important;
                line-height: 1 !important;
                color: rgba(255,255,255,0.9) !important;
                margin-top: 1px !important;
            }

            /* CARD DO FUNCIONÁRIO COMPACTO */
            .employee-card {
                margin: 8px 0 !important;
                padding: 10px !important;
                box-shadow: none !important;
            }

            .employee-header {
                margin-bottom: 8px !important;
            }

            .employee-avatar {
                width: 30px !important;
                height: 30px !important;
                font-size: 12px !important;
            }

            .employee-name {
                font-size: 14px !important;
                margin-bottom: 1px !important;
            }

            .employee-role {
                font-size: 10px !important;
                margin-bottom: 2px !important;
            }

            .employee-period {
                font-size: 9px !important;
            }

            .employee-details {
                margin: 8px 0 !important;
                gap: 10px !important;
            }

            .detail-label {
                font-size: 8px !important;
                margin-bottom: 1px !important;
            }

            .detail-value {
                font-size: 10px !important;
            }

            .signature-line {
                margin-top: 10px !important;
            }

            .signature-text {
                font-size: 9px !important;
                margin-bottom: 6px !important;
            }

            /* SEÇÃO DE REGISTROS COMPACTA */
            .records-section {
                margin: 8px 0 !important;
                padding: 0 !important;
            }

            .records-header {
                margin-bottom: 6px !important;
            }

            .records-title {
                font-size: 12px !important;
            }

            .records-icon {
                font-size: 12px !important;
            }

            .records-table {
                box-shadow: none !important;
                font-size: 9px !important;
                width: 100% !important;
            }

            .records-table th,
            .records-table td {
                border: 1px solid #333 !important;
                padding: 3px 2px !important;
                font-size: 9px !important;
            }

            .records-table th {
                background: #f0f0f0 !important;
                font-size: 8px !important;
                font-weight: 600 !important;
                padding: 2px 1px !important;
            }

            /* CONFIGURAÇÃO DE PÁGINA */
            @page {
                margin: 0.4in !important;
                size: A4 portrait !important;
            }

            /* QUEBRA DE PÁGINA E FINALIZAÇÃO */
            .page-break {
                page-break-before: always !important;
            }

            .employee-card,
            .records-section {
                page-break-inside: avoid !important;
            }
        }



        /* Estado vazio */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #4a5568;
        }

        .empty-state p {
            font-size: 14px;
        }

        /* ========================================
           NOVO CABEÇALHO PROFISSIONAL
           ======================================== */
        .professional-header {
            border: 2px solid #333;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
        }

        .header-title-section h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 8px 0;
            text-align: left;
        }

        .company-info {
            font-size: 12px;
            margin-bottom: 5px;
            line-height: 1.3;
        }

        .company-name {
            font-weight: bold;
        }

        .company-cnpj {
            font-weight: normal;
        }

        .emission-date {
            font-size: 11px;
            text-align: right;
            margin-top: 5px;
            font-weight: bold;
        }

        /* Informações do Colaborador */
        .employee-info-section {
            border: 1px solid #333;
            border-top: none;
            padding: 10px;
            margin-bottom: 15px;
            background: white;
        }

        .employee-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            flex-wrap: wrap;
        }

        .employee-row:last-child {
            margin-bottom: 0;
        }

        .info-group {
            display: flex;
            align-items: center;
            margin-right: 20px;
            margin-bottom: 5px;
        }

        .info-group .label {
            font-weight: bold;
            margin-right: 5px;
            font-size: 11px;
        }

        .info-group .value {
            font-size: 11px;
        }

        /* Estilos para impressão - Cabeçalho */
        @media print {
            .professional-header {
                border: 2px solid #000 !important;
                padding: 10px !important;
                margin-bottom: 5px !important;
                page-break-inside: avoid !important;
            }

            .header-title-section h1 {
                font-size: 14pt !important;
                margin: 0 0 5px 0 !important;
            }

            .company-info {
                font-size: 9pt !important;
                margin-bottom: 3px !important;
            }

            .emission-date {
                font-size: 8pt !important;
                margin-top: 3px !important;
            }

            .employee-info-section {
                border: 1px solid #000 !important;
                border-top: none !important;
                padding: 8px !important;
                margin-bottom: 10px !important;
                page-break-inside: avoid !important;
            }

            .employee-row {
                margin-bottom: 5px !important;
            }

            .info-group .label,
            .info-group .value {
                font-size: 8pt !important;
            }
        }

        /* ========================================
           NOVAS COLUNAS E RESUMO SIMPLES
           ======================================== */
        .extra-cell,
        .desc-cell {
            text-align: center;
            font-weight: bold;
            color: #007bff;
        }

        .desc-cell {
            color: #dc3545;
        }

        .summary-footer {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            flex-wrap: nowrap;
            gap: 15px;
            align-items: center;
        }

        .summary-item {
            text-align: center;
            margin: 0;
            flex: 1;
            min-width: 150px;
        }

        .summary-item strong {
            font-size: 11px;
            font-weight: 600;
            color: #333;
            display: block;
            margin-bottom: 4px;
        }

        .summary-item span {
            font-size: 13px;
            font-weight: 500;
            display: block;
        }

        .summary-item strong {
            display: block;
            margin-bottom: 5px;
            color: #333;
        }

        .summary-item span {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
        }

        /* Exibição elegante em horas e minutos */
        .hours-minutes-display {
            font-size: 18px;
            color: #28a745;
            font-weight: bold;
            text-align: center;
        }

        /* ========================================
           INDICADORES DE VALIDAÇÃO
           ======================================== */
        .calculated-value {
            position: relative;
            transition: all 0.3s ease;
        }

        .calculated-value.validated {
            color: #28a745 !important;
        }

        .calculated-value.error {
            color: #dc3545 !important;
            font-weight: bold;
        }

        .calculated-value.warning {
            color: #ffc107 !important;
        }

        .audit-indicator {
            position: absolute;
            top: -5px;
            right: -10px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }

        .audit-indicator.error {
            background: #dc3545;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .signature-section {
            border-top: 1px solid #333;
            padding: 15px;
            text-align: center;
        }

        .signature-text {
            font-size: 11px;
            margin-bottom: 20px;
        }

        .signature-line-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .signature-line {
            width: 300px;
            height: 1px;
            border-bottom: 1px solid #333;
            margin-bottom: 5px;
        }

        .signature-name {
            font-size: 10px;
            font-weight: bold;
        }

        /* Estilos para impressão - Resumo */
        @media print {
            .summary-footer {
                border: 1px solid #000 !important;
                margin-top: 10px !important;
                padding: 10px !important;
                page-break-inside: avoid !important;
            }

            .summary-row {
                margin-bottom: 10px !important;
                display: flex !important;
                justify-content: space-between !important;
                flex-wrap: nowrap !important;
                gap: 10px !important;
                align-items: center !important;
            }

            .summary-item {
                flex: 1 !important;
                min-width: 100px !important;
                margin: 0 !important;
                text-align: center !important;
            }

            .summary-item strong {
                font-size: 8pt !important;
                font-weight: 600 !important;
                margin-bottom: 2px !important;
                display: block !important;
            }

            .summary-item span {
                font-size: 9pt !important;
                font-weight: 500 !important;
                display: block !important;
            }

            .signature-section {
                border-top: 1px solid #000 !important;
                padding: 10px !important;
            }

            .signature-text {
                font-size: 8pt !important;
                margin-bottom: 15px !important;
            }

            .signature-line {
                border-bottom: 1px solid #000 !important;
            }

            .signature-name {
                font-size: 7pt !important;
            }

            .extra-cell,
            .desc-cell {
                font-size: 8pt !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Botões de ação (apenas na tela) -->
        <div class="header-buttons" style="display: block;">
            <button onclick="window.print()" class="btn btn-print">
                📄 Imprimir
            </button>
            <a href="{{ url_for('ponto_admin.detalhes_funcionario', funcionario_id=funcionario.id) }}" class="btn btn-back">
                ← Voltar
            </a>
        </div>

        <!-- Cabeçalho Profissional do Espelho de Ponto -->
        <div class="professional-header">
            <div class="header-title-section">
                <h1>Espelho de Ponto</h1>
                <div class="company-info">
                    <span class="company-name">
                        Empresa: {{ funcionario.empresa_nome or funcionario.empresa_fantasia or 'MSV ENGENHARIA' }}
                    </span>
                    <span class="company-cnpj">
                        - {{ funcionario.empresa_cnpj or '18.214.095/0001-01' }} - CNPJ: {{ funcionario.empresa_cnpj or '18.214.095/0001-01' }}
                    </span>
                </div>
                <div class="emission-date">
                    Emitido em: {{ data_atual.strftime('%d/%m/%Y às %H:%M') if data_atual else '27/05/2025 às 09:10' }}
                </div>
            </div>
        </div>

        <!-- Informações do Colaborador -->
        <div class="employee-info-section">
            <div class="employee-row">
                <div class="info-group">
                    <span class="label">Colaborador:</span>
                    <span class="value">{{ funcionario.nome_completo or 'ALINILSON RODRIGUES' }}</span>
                </div>
                <div class="info-group">
                    <span class="label">Data de Admissão:</span>
                    <span class="value">{{ funcionario.data_admissao.strftime('%d/%m/%Y') if funcionario.data_admissao else '26/03/2025' }}</span>
                </div>
            </div>
            <div class="employee-row">
                <div class="info-group">
                    <span class="label">CPF:</span>
                    <span class="value">{{ funcionario.cpf or '967.302.002-78' }}</span>
                </div>
                <div class="info-group">
                    <span class="label">PIS:</span>
                    <span class="value">{{ funcionario.pis_pasep or '161.58118.17-7' }}</span>
                </div>
                <div class="info-group">
                    <span class="label">Cargo:</span>
                    <span class="value">{{ funcionario.cargo or 'SERVENTE DE OBRAS' }}</span>
                </div>
                <div class="info-group">
                    <span class="label">Setor:</span>
                    <span class="value">{{ funcionario.setor or funcionario.setor_obra or 'OPERACIONAL' }}</span>
                </div>
            </div>
        </div>

        <!-- Seção de registros -->
        <div class="records-section">
            <div class="records-header">
                <div class="records-icon">📋</div>
                <div class="records-title">Registros de Ponto</div>
            </div>

            {% if registros %}
            <table class="records-table">
                <thead>
                    <tr>
                        <th>Data</th>
                        <th>Dia</th>
                        <th>Entrada</th>
                        <th>Saída Almoço</th>
                        <th>Retorno</th>
                        <th>Saída</th>
                        <th>Horas</th>
                        <th>Extra</th>
                        <th>Desc.</th>
                        <th>Justificativa</th>
                    </tr>
                </thead>
                <tbody>
                    {% for registro in registros %}
                    <tr>
                        <td class="date-cell">
                            {% if registro.data_registro %}
                                {{ registro.data_registro.strftime('%d/%m/%Y') }}
                            {% elif registro.data %}
                                {{ registro.data.strftime('%d/%m/%Y') }}
                            {% else %}
                                N/A
                            {% endif %}
                        </td>
                        <td class="day-cell">{{ registro.dia_semana or 'N/A' }}</td>
                        <td>{{ registro.entrada.strftime('%H:%M') if registro.entrada else '-' }}</td>
                        <td>{{ registro.saida_almoco.strftime('%H:%M') if registro.saida_almoco else '-' }}</td>
                        <td>{{ registro.retorno_almoco.strftime('%H:%M') if registro.retorno_almoco else '-' }}</td>
                        <td>{{ registro.saida.strftime('%H:%M') if registro.saida else '-' }}</td>
                        <td class="hours-cell">{{ registro.horas_trabalhadas or '-' }}</td>
                        <td class="extra-cell">{{ registro.horas_extras or '00:00' }}</td>
                        <td class="desc-cell">{{ registro.descontos or '00:00' }}</td>
                        <td>
                            {% if registro.status_justificativa == 'abonado' %}
                                <span class="badge badge-abonado">Abonado</span>
                            {% elif registro.status_justificativa == 'reprovado' %}
                                <span class="badge badge-reprovado">Reprovado</span>
                            {% elif registro.status_justificativa == 'pendente' %}
                                <span class="badge badge-pendente">Pendente</span>
                            {% else %}
                                <span class="badge badge-vazio">- -</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Resumo Dinâmico e Robusto -->
            <div class="summary-footer">
                <div class="summary-row">
                    <div class="summary-item">
                        <strong>Total de Horas:</strong>
                        <span id="total-horas" class="calculated-value hours-minutes-display">
                            {% set ns = namespace(total_segundos=0) %}
                            {% for registro in registros %}
                                {% if registro.horas_trabalhadas and ':' in registro.horas_trabalhadas %}
                                    {% set partes = registro.horas_trabalhadas.split(':') %}
                                    {% set horas = partes[0]|int %}
                                    {% set minutos = partes[1]|int %}
                                    {% set segundos_registro = (horas * 3600) + (minutos * 60) %}
                                    {% set ns.total_segundos = ns.total_segundos + segundos_registro %}
                                {% elif registro.horas_trabalhadas and 'h' in registro.horas_trabalhadas %}
                                    {% set horas_num = registro.horas_trabalhadas.replace('h', '')|float %}
                                    {% set segundos_registro = (horas_num * 3600)|round|int %}
                                    {% set ns.total_segundos = ns.total_segundos + segundos_registro %}
                                {% endif %}
                            {% endfor %}
                            {% set horas_totais = (ns.total_segundos // 3600)|int %}
                            {% set minutos_totais = ((ns.total_segundos % 3600) // 60)|int %}
                            {{ horas_totais }}h {{ "%02d"|format(minutos_totais) }}min
                        </span>
                    </div>
                    <div class="summary-item">
                        <strong>Total Extras:</strong>
                        <span id="total-extras" class="calculated-value">
                            {% set ns_extras = namespace(total=0.0) %}
                            {% for registro in registros %}
                                {% if registro.horas_extras and registro.horas_extras != '00:00' %}
                                    {% set extras_str = registro.horas_extras|string %}
                                    {% if ':' in extras_str %}
                                        {% set partes = extras_str.split(':') %}
                                        {% set horas = partes[0]|int %}
                                        {% set minutos = partes[1]|int %}
                                        {% set extras_decimal = horas + (minutos / 60.0) %}
                                        {% set ns_extras.total = ns_extras.total + extras_decimal %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                            {% set extras_horas = ns_extras.total|int %}
                            {% set extras_mins = ((ns_extras.total - extras_horas) * 60)|int %}
                            {% if ns_extras.total > 0 %}{{ extras_horas }}h {{ "%02d"|format(extras_mins) }}min{% else %}0h 00min{% endif %}
                        </span>
                    </div>
                    <div class="summary-item">
                        <strong>Banco de Horas:</strong>
                        <span id="banco-horas" class="calculated-value">
                            {% set ns_banco = namespace(total=0.0) %}
                            {% for registro in registros %}
                                {% if registro.banco_horas and registro.banco_horas != '00:00' %}
                                    {% set banco_str = registro.banco_horas|string %}
                                    {% if ':' in banco_str %}
                                        {% set partes = banco_str.split(':') %}
                                        {% set horas = partes[0]|int %}
                                        {% set minutos = partes[1]|int %}
                                        {% set banco_decimal = horas + (minutos / 60.0) %}
                                        {% set ns_banco.total = ns_banco.total + banco_decimal %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                            {% set banco_horas = ns_banco.total|int %}
                            {% set banco_mins = ((ns_banco.total - banco_horas) * 60)|int %}
                            {% if ns_banco.total != 0 %}{{ banco_horas }}h {{ "%02d"|format(banco_mins) }}min{% else %}0h 00min{% endif %}
                        </span>
                    </div>
                    <div class="summary-item">
                        <strong>Total em Atrasos:</strong>
                        <span id="total-atrasos" class="calculated-value">
                            {% set ns_atrasos = namespace(total=0.0) %}
                            {% for registro in registros %}
                                {% if registro.descontos and registro.descontos != '00:00' %}
                                    {% set atrasos_str = registro.descontos|string %}
                                    {% if ':' in atrasos_str %}
                                        {% set partes = atrasos_str.split(':') %}
                                        {% set horas = partes[0]|int %}
                                        {% set minutos = partes[1]|int %}
                                        {% set atrasos_decimal = horas + (minutos / 60.0) %}
                                        {% set ns_atrasos.total = ns_atrasos.total + atrasos_decimal %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                            {% set atrasos_horas = ns_atrasos.total|int %}
                            {% set atrasos_mins = ((ns_atrasos.total - atrasos_horas) * 60)|int %}
                            {% if ns_atrasos.total > 0 %}{{ atrasos_horas }}h {{ "%02d"|format(atrasos_mins) }}min{% else %}0h 00min{% endif %}
                        </span>
                    </div>
                </div>

                <!-- Linha de Assinatura -->
                <div class="signature-section">
                    <div class="signature-text">
                        Concordo com as informações acima registradas
                    </div>
                    <div class="signature-line-container">
                        <div class="signature-line"></div>
                        <div class="signature-name">({{ funcionario.nome_completo or 'Funcionário' }})</div>
                    </div>
                </div>
            </div>

            {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">📋</div>
                <h3>Nenhum registro encontrado</h3>
                <p>Não há registros de ponto para o período selecionado.</p>
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        // ========================================
        // SISTEMA ROBUSTO DE CÁLCULO DE HORAS
        // Validação cruzada e auditoria em tempo real
        // ========================================

        // Função para imprimir
        function imprimirPonto() {
            window.print();
        }

        // Função robusta para calcular horas trabalhadas
        function calcularHorasRobusta(entrada, saidaAlmoco, retorno, saida) {
            try {
                let totalHoras = 0;
                let detalhes = [];

                // Período manhã
                if (entrada && saidaAlmoco) {
                    const entradaTime = new Date(`2000-01-01T${entrada}`);
                    const saidaAlmocoTime = new Date(`2000-01-01T${saidaAlmoco}`);
                    const horasManha = (saidaAlmocoTime - entradaTime) / (1000 * 60 * 60);
                    if (horasManha > 0) {
                        totalHoras += horasManha;
                        detalhes.push(`Manhã: ${horasManha.toFixed(2)}h`);
                    }
                }

                // Período tarde
                if (retorno && saida) {
                    const retornoTime = new Date(`2000-01-01T${retorno}`);
                    const saidaTime = new Date(`2000-01-01T${saida}`);
                    const horasTarde = (saidaTime - retornoTime) / (1000 * 60 * 60);
                    if (horasTarde > 0) {
                        totalHoras += horasTarde;
                        detalhes.push(`Tarde: ${horasTarde.toFixed(2)}h`);
                    }
                }

                return {
                    total: Math.round(totalHoras * 10) / 10,
                    detalhes: detalhes,
                    valido: totalHoras > 0
                };

            } catch (error) {
                console.error('Erro no cálculo de horas:', error);
                return { total: 0, detalhes: ['Erro no cálculo'], valido: false };
            }
        }

        // Validação cruzada dos totais
        function validarTotais() {
            try {
                const linhas = document.querySelectorAll('tbody tr');
                let totalCalculadoJS = 0;
                let registrosValidos = 0;

                console.log('🔍 AUDITORIA DE CÁLCULOS:');
                console.log('========================');

                linhas.forEach((linha, index) => {
                    const colunas = linha.querySelectorAll('td');
                    if (colunas.length >= 9) {
                        const entrada = colunas[2].textContent.trim();
                        const saidaAlmoco = colunas[3].textContent.trim();
                        const retorno = colunas[4].textContent.trim();
                        const saida = colunas[5].textContent.trim();
                        const horasExibidas = colunas[6].textContent.trim();

                        // Calcular horas com JavaScript
                        const calculo = calcularHorasRobusta(
                            entrada !== '-' ? entrada : null,
                            saidaAlmoco !== '-' ? saidaAlmoco : null,
                            retorno !== '-' ? retorno : null,
                            saida !== '-' ? saida : null
                        );

                        if (calculo.valido) {
                            totalCalculadoJS += calculo.total;
                            registrosValidos++;

                            console.log(`📅 Registro ${index + 1}: ${calculo.total}h (${calculo.detalhes.join(', ')})`);

                            // Verificar divergência
                            const horasExibidasNum = parseFloat(horasExibidas.replace('h', ''));
                            if (Math.abs(calculo.total - horasExibidasNum) > 0.1) {
                                console.warn(`⚠️ DIVERGÊNCIA no registro ${index + 1}: Calculado ${calculo.total}h vs Exibido ${horasExibidasNum}h`);
                            }
                        }
                    }
                });

                // Comparar com total exibido
                const totalExibido = document.getElementById('total-horas');
                if (totalExibido) {
                    const totalExibidoNum = parseFloat(totalExibido.textContent.replace('h', ''));
                    console.log(`📊 TOTAL: Calculado ${totalCalculadoJS.toFixed(1)}h vs Exibido ${totalExibidoNum}h`);

                    if (Math.abs(totalCalculadoJS - totalExibidoNum) > 0.5) {
                        console.error('🚨 DIVERGÊNCIA CRÍTICA nos totais!');
                        totalExibido.style.color = '#dc3545';
                        totalExibido.title = `Divergência! Calculado: ${totalCalculadoJS.toFixed(1)}h`;
                    } else {
                        console.log('✅ Totais validados!');
                        totalExibido.style.color = '#28a745';
                    }
                }

            } catch (error) {
                console.error('Erro na validação:', error);
            }
        }

        // Executar validação quando carregar
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Sistema de auditoria iniciado');
            setTimeout(validarTotais, 500);
        });
    </script>
</body>
</html>
