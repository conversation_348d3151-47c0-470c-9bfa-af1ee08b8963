<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Captura de Biometria</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style-leitor-biometrico.css') }}">
</head>
<body>
    <div class="biometria-modal">
        <div class="biometria-content">
            <h2>Captura de Biometria</h2>
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    {% for message in messages %}
                        <div class="success-message">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            <div class="finger-selection">
                <button id="dedo1" class="finger-btn">Dedo 1</button>
                <button id="dedo2" class="finger-btn">Dedo 2</button>
            </div>
            <div class="biometria-display">
                <div class="digital-preview">
                    <img src="{{ url_for('static', filename='impressao_digital_icon.png') }}" alt="Impressão Digital">
                    <p>Qualidade: <span id="qualidade">0%</span></p>
                </div>
            </div>
            <div class="biometria-controls">
                <button id="limpar" class="control-btn">Limpar</button>
                <button id="capturar" class="control-btn">Capturar</button>
                <button id="salvar" class="control-btn">Salvar Biometria</button>
                <button id="fechar" class="control-btn">Fechar</button>
            </div>
        </div>
    </div>
    <script src="{{ url_for('static', filename='biometria-service.js') }}"></script>
    <script>
        document.getElementById('fechar').addEventListener('click', function() {
            // Se for uma modal, feche a modal
            document.querySelector('.biometria-modal').style.display = 'none';
            // Se for uma página independente, redirecione para a página anterior ou inicial
            window.location.href = '{{ url_for("index") }}';
        });
    </script>
</body>
</html>
