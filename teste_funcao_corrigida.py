#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TESTE DA FUNÇÃO CORRIGIDA
============================

Testa a função arredondar_horas_correto diretamente.
"""

def arredondar_horas_correto(horas_decimal):
    """
    Converte horas decimais para horas:minutos truncando segundos.

    Args:
        horas_decimal (float): Horas em formato decimal

    Returns:
        float: Horas convertidas para horas:minutos (truncando segundos)
    """
    if horas_decimal <= 0:
        return 0.0

    # Converter para segundos totais
    segundos_totais = horas_decimal * 3600

    # Extrair horas e minutos (truncar segundos)
    horas = int(segundos_totais // 3600)
    minutos = int((segundos_totais % 3600) // 60)

    # Reconverter para decimal
    return round(horas + (minutos / 60.0), 4)

def testar_funcao():
    """
    Testa a função com o caso problemático.
    """
    print("🧪 TESTE DA FUNÇÃO CORRIGIDA")
    print("=" * 40)
    
    # Caso problemático: 1h 05min 44seg
    horas_problematica = 1.0955555555555556
    
    print(f"📊 Entrada: {horas_problematica:.6f}h")
    
    # Função antiga (round)
    resultado_antigo = round(horas_problematica, 2)
    print(f"❌ Função antiga: {resultado_antigo}h")
    
    # Função nova (corrigida)
    resultado_novo = arredondar_horas_correto(horas_problematica)
    print(f"✅ Função nova: {resultado_novo}h")
    
    # Conversão para horas:minutos
    def converter_para_hm(horas_dec):
        h = int(horas_dec)
        m = int((horas_dec - h) * 60)
        return f"{h}h {m:02d}min"
    
    print(f"\n🕐 Conversão para horas:minutos:")
    print(f"   Função antiga: {converter_para_hm(resultado_antigo)}")
    print(f"   Função nova: {converter_para_hm(resultado_novo)}")
    
    # Resultado esperado
    print(f"   Esperado: 1h 05min")
    
    if converter_para_hm(resultado_novo) == "1h 05min":
        print(f"\n🎉 FUNÇÃO CORRIGIDA FUNCIONA!")
    else:
        print(f"\n❌ FUNÇÃO AINDA TEM PROBLEMA!")

if __name__ == "__main__":
    testar_funcao()
