#!/usr/bin/env python3
"""
Script para verificar e criar o usuário 'status' no banco de dados
RLPONTO-WEB v1.0
"""

import pymysql
from utils.config import Config

def verificar_criar_usuario_status():
    """Verifica se o usuário status existe e cria se necessário"""
    try:
        # Conectar ao banco
        conn = pymysql.connect(
            host='************',
            user='cavalcrod',
            password='200381',
            database='controle_ponto',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        print("🔗 Conectado ao banco de dados...")
        
        with conn.cursor() as cursor:
            # Verificar se usuário existe
            cursor.execute('SELECT * FROM usuarios WHERE usuario = %s', ('status',))
            user = cursor.fetchone()
            
            if user:
                print('✅ Usuário status já existe:')
                print(f'   ID: {user["id"]}')
                print(f'   Usuário: {user["usuario"]}')
                print(f'   Nome: {user["nome_completo"]}')
                print(f'   Ativo: {user["ativo"]}')
                print(f'   Nível: {user["nivel_acesso"]}')
            else:
                print('❌ Usuário status não encontrado. Criando...')
                
                # Inserir usuário
                cursor.execute("""
                    INSERT INTO usuarios (usuario, senha, nome_completo, email, ativo, nivel_acesso, data_criacao) 
                    VALUES (%s, %s, %s, %s, %s, %s, NOW())
                """, ('status', '12345678', 'Usuário Status do Sistema', '<EMAIL>', 1, 'usuario'))
                
                conn.commit()
                print('✅ Usuário status criado com sucesso!')
                
                # Verificar novamente
                cursor.execute('SELECT * FROM usuarios WHERE usuario = %s', ('status',))
                user = cursor.fetchone()
                if user:
                    print(f'   ID: {user["id"]}')
                    print(f'   Usuário: {user["usuario"]}')
                    print(f'   Nome: {user["nome_completo"]}')
            
            # Verificar/criar permissões
            user_id = user['id'] if user else cursor.lastrowid
            cursor.execute('SELECT * FROM permissoes WHERE usuario_id = %s', (user_id,))
            permissao = cursor.fetchone()
            
            if not permissao:
                print('📝 Criando permissões para usuário status...')
                cursor.execute("""
                    INSERT INTO permissoes (usuario_id, nivel_acesso) 
                    VALUES (%s, %s)
                """, (user_id, 'usuario'))
                conn.commit()
                print('✅ Permissões criadas!')
            else:
                print('✅ Permissões já existem')
        
        conn.close()
        print('\n🎉 Verificação concluída com sucesso!')
        print('📋 Credenciais do usuário status:')
        print('   Usuário: status')
        print('   Senha: 12345678')
        
    except Exception as e:
        print(f'❌ Erro: {e}')
        return False
    
    return True

if __name__ == '__main__':
    verificar_criar_usuario_status()