{% extends "base.html" %}

{% block content %}
<!-- Professional Dashboard Container -->
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">{{ titulo }}</h1>
                        <p class="text-sm text-gray-600 mt-1">Dashboard de controle de ponto • {{ periodo }}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                        <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        Sistema Ativo
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 border border-gray-200">
                        Atualizado hoje
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- KPI Cards -->
        <section class="mb-8">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                
                <!-- Total Registros -->
                <div class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="flex items-center text-sm font-medium text-green-600">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                                </svg>
                                +12.3%
                            </div>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900">{{ "{:,}".format(stats.total_registros) }}</p>
                            <p class="text-sm text-gray-600 mt-1">Total de Registros</p>
                        </div>
                    </div>
                </div>

                <!-- Funcionários Ativos -->
                <div class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                                </svg>
                            </div>
                            <div class="flex items-center text-sm font-medium text-green-600">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                                </svg>
                                +5.2%
                            </div>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900">{{ "{:,}".format(stats.funcionarios_ativos) }}</p>
                            <p class="text-sm text-gray-600 mt-1">Funcionários Ativos</p>
                        </div>
                    </div>
                </div>

                <!-- Registros Biométricos -->
                <div class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6.625 2.655A9 9 0 0119 11a1 1 0 11-2 0 7 7 0 00-9.625-6.492 1 1 0 11-.75-1.853zM4.662 4.959A1 1 0 014.75 6.37 6.97 6.97 0 003 11a1 1 0 11-2 0 8.97 8.97 0 012.25-5.953 1 1 0 011.412-.088z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-700">
                                {{ stats.percentual_biometrico }}%
                            </div>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900">{{ "{:,}".format(stats.registros_biometricos) }}</p>
                            <p class="text-sm text-gray-600 mt-1">Registros Biométricos</p>
                        </div>
                    </div>
                </div>

                <!-- Registros Manuais -->
                <div class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                                </svg>
                            </div>
                            <div class="flex items-center text-sm font-medium text-red-600">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                                </svg>
                                -2.1%
                            </div>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900">{{ "{:,}".format(stats.registros_manuais) }}</p>
                            <p class="text-sm text-gray-600 mt-1">Registros Manuais</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Charts Section -->
        <section class="mb-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                
                <!-- Registros Diários Chart -->
                <div class="lg:col-span-2 bg-white rounded-lg border border-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Registros por Período</h3>
                                <p class="text-sm text-gray-600 mt-1">Últimos 7 dias de atividade</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                <span class="text-xs text-gray-600">Registros diários</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="chart-container" style="height: 300px;">
                            <canvas id="chartRegistrosDiarios"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Distribuição de Métodos -->
                <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900">Distribuição de Métodos</h3>
                        <p class="text-sm text-gray-600 mt-1">Biométrico vs Manual</p>
                    </div>
                    <div class="p-6">
                        <div class="chart-container flex justify-center" style="height: 250px;">
                            <canvas id="chartMetodos"></canvas>
                        </div>
                        <div class="mt-6 space-y-3">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                    <span class="text-sm font-medium text-gray-700">Biométrico</span>
                                </div>
                                <span class="text-sm font-semibold text-gray-900">{{ stats.percentual_biometrico }}%</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                                    <span class="text-sm font-medium text-gray-700">Manual</span>
                                </div>
                                <span class="text-sm font-semibold text-gray-900">{{ 100 - stats.percentual_biometrico }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Pontualidade Chart -->
        <section class="mb-8">
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Análise de Pontualidade</h3>
                            <p class="text-sm text-gray-600 mt-1">Controle de atrasos por período</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                            <span class="text-xs text-gray-600">Atrasos por dia</span>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="chartPontualidade"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- Action Center -->
        <section>
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-900">Centro de Ações</h3>
                    <p class="text-sm text-gray-600 mt-1">Acesso rápido às principais funcionalidades</p>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <a href="/relatorios/pontos" class="group block p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-900 group-hover:text-blue-900">Relatórios</p>
                                    <p class="text-xs text-gray-600">Ver detalhados</p>
                                </div>
                            </div>
                        </a>
                        
                        <a href="/registro-ponto/manual" class="group block p-4 border-2 border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-all duration-200">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-900 group-hover:text-green-900">Ponto Manual</p>
                                    <p class="text-xs text-gray-600">Registrar agora</p>
                                </div>
                            </div>
                        </a>
                        
                        <a href="/registro-ponto/biometrico" class="group block p-4 border-2 border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-all duration-200">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0h-2m8-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-900 group-hover:text-purple-900">Biométrico</p>
                                    <p class="text-xs text-gray-600">Capturar digital</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>
</div>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    // Extract data from Flask template safely
    var chartsData = {
        labels: {{ graficos.labels_dias | tojson }},
        registrosDiarios: {{ graficos.dados_registros_diarios | tojson }},
        pontualidade: {{ graficos.dados_pontualidade | tojson }},
        biometricos: {{ stats.registros_biometricos }},
        manuais: {{ stats.registros_manuais }}
    };

    // Set global Chart.js defaults
    Chart.defaults.font.family = 'system-ui, -apple-system, sans-serif';
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#6B7280';

    // Common chart options
    var baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                border: {
                    display: false
                },
                grid: {
                    color: 'rgba(229, 231, 235, 0.8)'
                },
                ticks: {
                    color: '#6B7280',
                    font: {
                        size: 11
                    }
                }
            },
            x: {
                border: {
                    display: false
                },
                grid: {
                    display: false
                },
                ticks: {
                    color: '#6B7280',
                    font: {
                        size: 11
                    }
                }
            }
        }
    };

    // Initialize Bar Chart - Registros Diários
    var ctxBar = document.getElementById('chartRegistrosDiarios');
    if (ctxBar) {
        new Chart(ctxBar, {
            type: 'bar',
            data: {
                labels: chartsData.labels,
                datasets: [{
                    label: 'Registros',
                    data: chartsData.registrosDiarios,
                    backgroundColor: '#3B82F6',
                    borderColor: '#2563EB',
                    borderWidth: 0,
                    borderRadius: 4,
                    borderSkipped: false
                }]
            },
            options: baseOptions
        });
    }

    // Initialize Doughnut Chart - Métodos
    var ctxDoughnut = document.getElementById('chartMetodos');
    if (ctxDoughnut) {
        new Chart(ctxDoughnut, {
            type: 'doughnut',
            data: {
                labels: ['Biométrico', 'Manual'],
                datasets: [{
                    data: [chartsData.biometricos, chartsData.manuais],
                    backgroundColor: ['#8B5CF6', '#F59E0B'],
                    borderColor: ['#ffffff', '#ffffff'],
                    borderWidth: 3,
                    hoverOffset: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    // Initialize Line Chart - Pontualidade
    var ctxLine = document.getElementById('chartPontualidade');
    if (ctxLine) {
        new Chart(ctxLine, {
            type: 'line',
            data: {
                labels: chartsData.labels,
                datasets: [{
                    label: 'Atrasos',
                    data: chartsData.pontualidade,
                    fill: true,
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderColor: '#EF4444',
                    borderWidth: 2,
                    tension: 0.4,
                    pointBackgroundColor: '#EF4444',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: baseOptions
        });
    }
});
</script>

<style>
/* Professional styling */
.chart-container {
    position: relative;
}

.chart-container canvas {
    max-width: 100%;
    height: auto;
}

/* Smooth transitions */
.transition-shadow {
    transition: box-shadow 0.15s ease-in-out;
}

.transition-colors {
    transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out;
}

.transition-all {
    transition: all 0.15s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-4 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem;
    }
    
    .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .py-4 {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
    }
}

/* Print styles */
@media print {
    .shadow-sm,
    .shadow-md {
        box-shadow: none !important;
    }
    
    .border {
        border: 1px solid #e5e7eb !important;
    }
}
</style>
{% endblock %} 