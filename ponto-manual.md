# Sistema de Ponto Manual - RLPONTO-WEB

## 📋 Visão Geral

O sistema de ponto manual permite que administradores registrem pontos para funcionários quando o registro biométrico não está disponível ou em situações 
especiais. Este documento detalha todo o fluxo, validações e regras de negócio.

## 🎯 Funcionalidades Principais

### 1. Interface de Seleção de Funcionários
- **Localização**: `/registro-ponto/manual`
- **Template**: `templates/registro_ponto/manual.html`
- **Funcionalidades**:
  - Pesquisa de funcionários por nome, CPF ou matrícula
  - Grid responsivo com cards dos funcionários
  - Exibição de foto, nome, cargo e empresa
  - Filtro em tempo real

### 2. Modal de Registro de Ponto
- **Abertura**: Clique no botão "Registrar Ponto" do funcionário
- **Componentes**:
  - Dados do funcionário selecionado
  - Horários configurados da jornada de trabalho
  - Tipos de registro disponíveis (baseados no horário atual)
  - Campo de observações (opcional)
  - Indicador de hora atual

## 🔧 Fluxo Técnico

### 1. Carregamento da Página
```javascript
// Arquivo: templates/registro_ponto/manual.html
// Função: carregarFuncionarios()
function carregarFuncionarios() {
    fetch('/registro-ponto/api/funcionarios')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                funcionarios = data.funcionarios;
                renderizarFuncionarios(funcionarios);
            } else {
                console.error('Erro ao carregar funcionários:', data.message);
                funcionariosGrid.innerHTML = '<div class="alert alert-danger">Erro ao carregar funcionários</div>';
            }
        })
        .catch(error => {
            console.error('Erro na requisição:', error);
            funcionariosGrid.innerHTML = '<div class="alert alert-danger">Erro de conexão</div>';
        });
}
```

**Passos**:
1. Requisição GET para `/registro-ponto/api/funcionarios`
2. Renderização dos cards de funcionários
3. Configuração do filtro de pesquisa
4. Inicialização dos event listeners

### 2. Seleção de Funcionário
```javascript
// Função: abrirModalRegistro(funcionarioId)
function abrirModalRegistro(funcionarioId) {
    funcionarioSelecionado = funcionarioId;

    // Forçar visibilidade do modal antes de abrir
    modalElement.style.display = 'block';

    // Buscar dados do funcionário e horários
    Promise.all([
        buscarDadosFuncionario(funcionarioId),
        buscarHorariosFuncionario(funcionarioId)
    ]).then(([funcionario, horarios]) => {
        preencherModalFuncionario(funcionario);
        // Armazenar os registros já feitos hoje
        registrosExistentes = horarios.registros_existentes || [];
        todosTipos = horarios.todos_tipos || [];
        preencherTiposRegistro(horarios.tipos_disponiveis || [], horarios.todos_tipos || [], horarios.hora_atual);
        registroModal.show();

        // Habilitar botão de confirmação inicialmente
        if (btnConfirmar) {
            btnConfirmar.disabled = false;
            btnConfirmar.innerHTML = '<i class="fas fa-save"></i> Registrar Ponto';
        }
    }).catch(error => {
        console.error('Erro ao carregar dados:', error);
        alert('Erro ao carregar dados do funcionário: ' + error.message);
    });
}
```

**Passos**:
1. Busca dados do funcionário: `buscarDadosFuncionario(funcionarioId)`
2. Busca horários configurados: `buscarHorariosFuncionario(funcionarioId)`
3. Preenche modal com dados do funcionário
4. Determina tipos de registro disponíveis baseado no horário atual
5. Exibe modal de registro

### 3. Validação de Horários
```python
# Arquivo: app_registro_ponto.py
# Função: validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario)
def validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario):
    """
    Valida se o tipo de registro solicitado é permitido no horário atual.

    Args:
        tipo_registro (str): Tipo de registro (entrada_manha, saida_almoco, etc.)
        horarios_funcionario (dict): Horários configurados do funcionário

    Returns:
        dict: {
            'permitido': bool,
            'mensagem': str,
            'horario_liberado': bool,
            'status': str  # 'Pontual' ou 'Atrasado'
        }
    """
    try:
        # Obter hora atual
        hora_atual = obter_hora_atual()
        hora_atual_str = hora_atual.strftime('%H:%M')
        hora_atual_obj = hora_atual.time()

        # Obter horários configurados
        entrada_manha = string_para_time(horarios_funcionario.get('entrada_manha'))
        saida_almoco = string_para_time(horarios_funcionario.get('saida_almoco'))
        entrada_tarde = string_para_time(horarios_funcionario.get('entrada_tarde'))
        saida = string_para_time(horarios_funcionario.get('saida'))
        tolerancia = int(horarios_funcionario.get('tolerancia_minutos', 10))

        # Determinar período atual do dia
        periodo_atual = "indefinido"

        if entrada_manha and saida_almoco:
            # Período da manhã (entre entrada_manha e saida_almoco)
            if entrada_manha <= hora_atual_obj <= saida_almoco:
                periodo_atual = "manha"

        if saida_almoco and entrada_tarde:
            # Período de almoço (entre saida_almoco e entrada_tarde)
            if saida_almoco <= hora_atual_obj <= entrada_tarde:
                periodo_atual = "almoco"

        if entrada_tarde and saida:
            # Período da tarde (entre entrada_tarde e saida)
            if entrada_tarde <= hora_atual_obj <= saida:
                periodo_atual = "tarde"

        # Se estamos depois do horário de saída
        if saida and hora_atual_obj > saida:
            periodo_atual = "apos_expediente"

        # Se estamos antes do horário de entrada
        if entrada_manha and hora_atual_obj < entrada_manha:
            periodo_atual = "antes_expediente"

        # Inicializar status padrão como "pontual"
        status_registro = "Pontual"

        # Validar cada tipo de registro com base no período atual
        if tipo_registro == 'entrada_manha':
            # Entrada manhã é permitida a partir do horário de entrada menos tolerância
            if entrada_manha is None:
                return {'permitido': False, 'mensagem': 'Horário de entrada não configurado', 'horario_liberado': False, 'status': None}

            # Verificar se o registro será considerado "Atrasado" ou "Pontual"
            hora_limite_pontualidade = datetime.combine(date.today(), entrada_manha)
            hora_limite_pontualidade = (hora_limite_pontualidade + timedelta(minutes=tolerancia)).time()

            # Se estiver após o limite de tolerância, marcar como "Atrasado"
            if hora_atual_obj > hora_limite_pontualidade:
                status_registro = "Atrasado"
            else:
                status_registro = "Pontual"

            # Definir janela de tempo permitida para registro
            hora_min = datetime.combine(date.today(), entrada_manha)
            hora_min = (hora_min - timedelta(minutes=tolerancia)).time()

            limite_atraso = 60  # 1 hora de limite para registrar entrada atrasada
            hora_max = datetime.combine(date.today(), entrada_manha)
            hora_max = (hora_max + timedelta(minutes=limite_atraso)).time()

            # Entrada manhã é mais apropriada no início do dia
            if periodo_atual in ["antes_expediente", "manha"]:
                # Se for antes do horário programado, não permitir o registro
                if hora_atual_obj < hora_min:
                    return {
                        'permitido': False,
                        'mensagem': f"Entrada ainda não liberada. Disponível a partir de {hora_min.strftime('%H:%M')}",
                        'horario_liberado': False,
                        'status': None
                    }
                # Verificar se passou do limite máximo permitido
                elif hora_atual_obj > hora_max:
                    return {
                        'permitido': False,
                        'mensagem': f"Horário de entrada expirado. O limite para registro era {hora_max.strftime('%H:%M')}",
                        'horario_liberado': False,
                        'status': None
                    }
                else:
                    return {
                        'permitido': True,
                        'mensagem': f"Entrada manhã registrada com sucesso ({status_registro})",
                        'horario_liberado': True,
                        'status': status_registro
                    }
            else:
                # Fora do período ideal, mas ainda permitir com aviso
                return {
                    'permitido': True,
                    'mensagem': f"Entrada manhã registrada fora do período ideal ({status_registro})",
                    'horario_liberado': True,
                    'status': status_registro
                }

        # ... (continua com outros tipos de registro)

    except Exception as e:
        logger.error(f"Erro na validação de horário: {e}")
        return {'permitido': False, 'mensagem': f'Erro interno: {str(e)}', 'horario_liberado': False, 'status': None}
```

**Lógica de Validação**:

#### Períodos do Dia:
- **Antes do Expediente**: Antes da entrada da manhã
- **Manhã**: Entre entrada da manhã e saída para almoço
- **Almoço**: Entre saída para almoço e retorno
- **Tarde**: Entre retorno do almoço e saída
- **Após Expediente**: Após horário de saída

#### Tipos de Registro Permitidos:

**Entrada Manhã (`entrada_manha`)**:
- **Permitido**: Antes do expediente, manhã, almoço
- **Tolerância**: +10 minutos (configurável)
- **Status**: 
  - "Pontual": Dentro do horário + tolerância
  - "Atrasado": Após tolerância

**Saída Almoço (`saida_almoco`)**:
- **Permitido**: Durante período da manhã
- **Tolerância**: +10 minutos após horário configurado
- **Status**: Sempre "Pontual"

**Retorno Almoço (`entrada_tarde`)**:
- **Permitido**: Durante período de almoço e tarde
- **Tolerância**: +10 minutos após horário configurado
- **Status**:
  - "Pontual": Dentro do horário + tolerância
  - "Atrasado": Após tolerância

**Saída (`saida`)**:
- **Permitido**: Durante tarde e após expediente
- **Tolerância**: -10 minutos antes / +120 minutos depois
- **Status**: Sempre "Pontual"

### 4. Registro no Banco de Dados
```python
# Função: registrar_ponto_no_banco()
def registrar_ponto_no_banco(funcionario_id, tipo_registro, metodo_registro, observacoes=None, qualidade_biometria=None, status_pontualidade=None):
    """
    Registra um ponto no banco de dados.

    Args:
        funcionario_id (int): ID do funcionário
        tipo_registro (str): Tipo de registro (entrada_manha, saida_almoco, etc.)
        metodo_registro (str): Método de registro (biometrico, manual)
        observacoes (str, optional): Observações adicionais
        qualidade_biometria (int, optional): Qualidade da biometria (0-100)
        status_pontualidade (str, optional): Status da pontualidade (Pontual, Atrasado)

    Returns:
        dict: Resultado da operação com success, message e dados do registro
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Obter data e hora atuais
        agora = obter_hora_atual()
        data_registro = agora.date()
        hora_registro = agora.time()

        # Obter ID do usuário logado
        usuario_id = session.get('user_id')

        # Aplicar validações das novas regras se disponíveis
        if NOVAS_REGRAS_ATIVAS and not status_pontualidade:
            try:
                hora_atual = datetime.now()

                # Validar entrada da manhã com novas regras
                if tipo_registro == 'entrada_manha':
                    validacao = validar_entrada_manha_nova_regra(funcionario_id, hora_atual)
                    if validacao['status'] == 'atrasado':
                        status_pontualidade = 'Atrasado'
                        if not observacoes:
                            observacoes = f"Atraso de {validacao['minutos_atraso']} minutos"
                    elif validacao['status'] == 'pontual':
                        status_pontualidade = 'Pontual'
                    elif validacao['status'] == 'ausente_manha':
                        status_pontualidade = 'Atrasado'
                        if not observacoes:
                            observacoes = "Registro após período da manhã"

                # Validar saída com novas regras
                elif tipo_registro == 'saida':
                    validacao = validar_saida_expediente_nova_regra(funcionario_id, hora_atual)
                    if validacao['status'] == 'saida_antecipada':
                        status_pontualidade = 'Atrasado'  # Saída antecipada como irregularidade
                        if not observacoes:
                            observacoes = f"Saída antecipada em {validacao['minutos_antecipacao']} minutos"
                    else:
                        status_pontualidade = 'Pontual'

                # Para outros tipos, manter lógica padrão
                else:
                    status_pontualidade = status_pontualidade or 'Pontual'

            except Exception as e:
                logger.warning(f"Erro ao aplicar novas regras: {e}")
                status_pontualidade = status_pontualidade or 'Pontual'
        else:
            status_pontualidade = status_pontualidade or 'Pontual'

        # Inserir registro no banco
        query = """
            INSERT INTO registros_ponto
            (funcionario_id, data_registro, hora_registro, tipo_registro, metodo_registro,
             observacoes, qualidade_biometria, status_pontualidade, usuario_registro_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        cursor.execute(query, (
            funcionario_id, data_registro, hora_registro, tipo_registro, metodo_registro,
            observacoes, qualidade_biometria, status_pontualidade, usuario_id
        ))

        conn.commit()
        registro_id = cursor.lastrowid

        logger.info(f"Ponto registrado: ID={registro_id}, Funcionário={funcionario_id}, "
                   f"Tipo={tipo_registro}, Método={metodo_registro}, Status={status_pontualidade}")

        conn.close()

        return {
            'success': True,
            'message': 'Ponto registrado com sucesso',
            'registro_id': registro_id,
            'data_registro': data_registro.strftime('%Y-%m-%d'),
            'hora_registro': hora_registro.strftime('%H:%M:%S'),
            'status_pontualidade': status_pontualidade
        }

    except Exception as e:
        logger.error(f"Erro ao registrar ponto: {e}")
        return {
            'success': False,
            'message': f'Erro ao registrar ponto: {str(e)}'
        }
```

**Campos Salvos**:
- `funcionario_id`: ID do funcionário
- `data_registro`: Data atual
- `hora_registro`: Hora atual
- `tipo_registro`: Tipo do ponto (entrada_manha, saida_almoco, etc.)
- `metodo_registro`: "manual"
- `observacoes`: Observações do usuário
- `status_pontualidade`: "Pontual" ou "Atrasado"
- `usuario_registro_id`: ID do usuário que fez o registro

## 📊 APIs Utilizadas

### 1. Listar Funcionários
- **Endpoint**: `GET /registro-ponto/api/funcionarios`
- **Resposta**: Lista de funcionários ativos
- **Campos**: id, nome_completo, cargo, empresa_nome, foto_3x4

### 2. Obter Horários do Funcionário
- **Endpoint**: `GET /registro-ponto/api/obter-horarios/{funcionario_id}`
- **Resposta**: Horários configurados e tipos disponíveis
- **Campos**: 
  - `horarios`: entrada_manha, saida_almoco, entrada_tarde, saida, tolerancia_minutos
  - `tipos_disponiveis`: Array com tipos permitidos no momento
  - `todos_tipos`: Array com todos os tipos possíveis
  - `registros_existentes`: Registros já feitos no dia

### 3. Registrar Ponto Manual
- **Endpoint**: `POST /registro-ponto/api/registrar-manual`
- **Parâmetros**:
  - `funcionario_id`: ID do funcionário
  - `tipo_registro`: Tipo do registro
  - `observacoes`: Observações (opcional)
- **Resposta**: Sucesso/erro e detalhes do registro

## 🛡️ Validações e Regras de Negócio

### 1. Validações de Entrada
- Funcionário deve existir e estar ativo
- Tipo de registro deve ser válido
- Horário atual deve permitir o tipo de registro
- Não pode haver registro duplicado no mesmo dia

### 2. Regras de Tolerância
- **Entrada**: +10 minutos após horário configurado
- **Saída Almoço**: +10 minutos após horário configurado  
- **Retorno Almoço**: +10 minutos após horário configurado
- **Saída**: -10 minutos antes até +120 minutos depois

### 3. Status de Pontualidade
- **Pontual**: Registro dentro do horário + tolerância
- **Atrasado**: Registro após tolerância permitida

### 4. Prevenção de Duplicatas
- Sistema verifica se já existe registro do mesmo tipo no dia
- Bloqueia registro duplicado com mensagem de erro

## 🎨 Interface do Usuário

### 1. Página Principal
- **Layout**: Grid responsivo de funcionários
- **Pesquisa**: Filtro em tempo real
- **Cards**: Foto, nome, cargo, empresa, botão de ação

### 2. Modal de Registro
- **Cabeçalho**: Dados do funcionário selecionado
- **Corpo**: 
  - Indicador de hora atual
  - Tipos de registro disponíveis (radio buttons)
  - Campo de observações
- **Rodapé**: Botões Cancelar e Registrar

### 3. Feedback Visual
- **Loading**: Overlay durante processamento
- **Sucesso**: Mensagem verde com confirmação
- **Erro**: Mensagem vermelha com detalhes
- **Estados**: Botões desabilitados durante processamento

## 🔄 Fluxo Completo de Uso

1. **Acesso**: Usuário acessa `/registro-ponto/manual`
2. **Pesquisa**: Localiza funcionário usando filtro
3. **Seleção**: Clica em "Registrar Ponto" no card do funcionário
4. **Modal**: Sistema abre modal com dados e opções disponíveis
5. **Escolha**: Usuário seleciona tipo de registro apropriado
6. **Observações**: Adiciona observações se necessário
7. **Confirmação**: Clica em "Registrar Ponto"
8. **Validação**: Sistema valida horário e regras
9. **Registro**: Salva no banco com status de pontualidade
10. **Feedback**: Exibe resultado para o usuário
11. **Atualização**: Recarrega página para mostrar novos registros

## 📁 Arquivos Principais

- **Backend**: `app_registro_ponto.py`
- **Frontend**: `templates/registro_ponto/manual.html`
- **Validações**: `utils/helpers.py`
- **Banco**: Tabela `registros_ponto`

## 🚨 Tratamento de Erros

### Erros Comuns:
- **Funcionário não encontrado**: Retorna erro 404
- **Horário não permitido**: Retorna erro 400 com mensagem específica
- **Registro duplicado**: Retorna erro 400 com aviso
- **Dados incompletos**: Retorna erro 400 com validação

### Logs:
- Todos os registros são logados com detalhes
- Erros são capturados e registrados para debug
- Status de pontualidade é documentado

## 🔍 Detalhes Técnicos das Validações

### Função `validar_tipo_registro_por_horario()` - Código Completo

```python
def validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario):
    """
    Valida se o tipo de registro é permitido no horário atual.

    Retorna:
    {
        'permitido': bool,
        'mensagem': str,
        'horario_liberado': bool,
        'status': str  # 'Pontual' ou 'Atrasado'
    }
    """
    try:
        # Obter hora atual
        hora_atual = obter_hora_atual()
        hora_atual_str = hora_atual.strftime('%H:%M')
        hora_atual_obj = hora_atual.time()

        # Obter horários configurados
        entrada_manha = string_para_time(horarios_funcionario.get('entrada_manha'))
        saida_almoco = string_para_time(horarios_funcionario.get('saida_almoco'))
        entrada_tarde = string_para_time(horarios_funcionario.get('entrada_tarde'))
        saida = string_para_time(horarios_funcionario.get('saida'))
        tolerancia = int(horarios_funcionario.get('tolerancia_minutos', 10))

        # Determinar período atual do dia
        periodo_atual = "indefinido"

        if entrada_manha and saida_almoco:
            if entrada_manha <= hora_atual_obj <= saida_almoco:
                periodo_atual = "manha"

        if saida_almoco and entrada_tarde:
            if saida_almoco <= hora_atual_obj <= entrada_tarde:
                periodo_atual = "almoco"

        if entrada_tarde and saida:
            if entrada_tarde <= hora_atual_obj <= saida:
                periodo_atual = "tarde"

        if saida and hora_atual_obj > saida:
            periodo_atual = "apos_expediente"

        if entrada_manha and hora_atual_obj < entrada_manha:
            periodo_atual = "antes_expediente"

        # Inicializar status padrão como "pontual"
        status_registro = "Pontual"

        # Validação específica para cada tipo de registro
        if tipo_registro == 'entrada_manha':
            if entrada_manha is None:
                return {'permitido': False, 'mensagem': 'Horário de entrada não configurado', 'horario_liberado': False, 'status': None}

            # Calcular status de pontualidade
            hora_limite_pontualidade = datetime.combine(date.today(), entrada_manha)
            hora_limite_pontualidade = (hora_limite_pontualidade + timedelta(minutes=tolerancia)).time()

            if hora_atual_obj > hora_limite_pontualidade:
                status_registro = "Atrasado"
            else:
                status_registro = "Pontual"

            # Definir janela de tempo permitida
            hora_min = datetime.combine(date.today(), entrada_manha)
            hora_min = (hora_min - timedelta(minutes=tolerancia)).time()

            limite_atraso = 60  # 1 hora de limite
            hora_max = datetime.combine(date.today(), entrada_manha)
            hora_max = (hora_max + timedelta(minutes=limite_atraso)).time()

            if periodo_atual in ["antes_expediente", "manha"]:
                if hora_atual_obj < hora_min:
                    return {
                        'permitido': False,
                        'mensagem': f"Entrada ainda não liberada. Disponível a partir de {hora_min.strftime('%H:%M')}",
                        'horario_liberado': False,
                        'status': None
                    }
                elif hora_atual_obj > hora_max:
                    return {
                        'permitido': False,
                        'mensagem': f"Horário de entrada expirado. O limite para registro era {hora_max.strftime('%H:%M')}",
                        'horario_liberado': False,
                        'status': None
                    }
                else:
                    return {
                        'permitido': True,
                        'mensagem': f"Entrada manhã registrada com sucesso ({status_registro})",
                        'horario_liberado': True,
                        'status': status_registro
                    }

        elif tipo_registro == 'entrada_tarde':
            if entrada_tarde is None:
                return {'permitido': False, 'mensagem': 'Horário de retorno de almoço não configurado', 'horario_liberado': False, 'status': None}

            # Calcular status de pontualidade
            hora_limite_pontualidade = datetime.combine(date.today(), entrada_tarde)
            hora_limite_pontualidade = (hora_limite_pontualidade + timedelta(minutes=tolerancia)).time()

            if hora_atual_obj > hora_limite_pontualidade:
                status_registro = "Atrasado"
            else:
                status_registro = "Pontual"

            return {
                'permitido': True,
                'mensagem': f"Retorno de almoço registrado com sucesso ({status_registro})",
                'horario_liberado': True,
                'status': status_registro
            }

        elif tipo_registro == 'saida':
            if saida is None:
                return {'permitido': False, 'mensagem': 'Horário de saída não configurado', 'horario_liberado': False, 'status': None}

            # Saída sempre pontual (saída antes é considerada pontual)
            status_registro = "Pontual"

            # Definir janela de tempo permitida
            hora_min = datetime.combine(date.today(), saida)
            hora_min = (hora_min - timedelta(minutes=tolerancia)).time()

            limite_atraso = 120  # 2 horas de limite
            hora_max = datetime.combine(date.today(), saida)
            hora_max = (hora_max + timedelta(minutes=limite_atraso)).time()

            if periodo_atual in ["tarde", "apos_expediente"]:
                if hora_atual_obj > hora_max:
                    return {
                        'permitido': False,
                        'mensagem': f"Horário de saída expirado. O limite para registro era {hora_max.strftime('%H:%M')}",
                        'horario_liberado': False,
                        'status': None
                    }
                else:
                    return {
                        'permitido': True,
                        'mensagem': f"Saída registrada com sucesso ({status_registro})",
                        'horario_liberado': True,
                        'status': status_registro
                    }
            else:
                return {
                    'permitido': False,
                    'mensagem': f"Saída ainda não liberada. Disponível a partir de {hora_min.strftime('%H:%M')}",
                    'horario_liberado': False,
                    'status': None
                }

        else:
            return {'permitido': False, 'mensagem': f'Tipo de registro não reconhecido: {tipo_registro}', 'horario_liberado': False, 'status': None}

    except Exception as e:
        logger.error(f"Erro na validação de horário: {e}")
        return {'permitido': False, 'mensagem': f'Erro interno: {str(e)}', 'horario_liberado': False, 'status': None}
```

### Função JavaScript para Buscar Horários

```javascript
function buscarHorariosFuncionario(funcionarioId) {
    return fetch(`/registro-ponto/api/obter-horarios/${funcionarioId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Armazenar dados completos incluindo ausências em variável global
                window.horariosFuncionario = data;
                return data;
            } else {
                throw new Error(data.message || 'Erro ao obter horários');
            }
        })
        .catch(error => {
            console.warn('Erro ao buscar horários, usando padrão:', error);
            const horaAtual = new Date().toTimeString().substring(0, 5);

            // Dados padrão em caso de erro
            const dadosPadrao = {
                success: true,
                horarios: {
                    entrada_manha: '08:00',
                    saida_almoco: '12:00',
                    entrada_tarde: '13:00',
                    saida: '17:00',
                    tolerancia_minutos: 10
                },
                tipos_disponiveis: [
                    {value: 'entrada_manha', text: 'Entrada Manhã (08:00)', tolerancia: ''},
                    {value: 'saida_almoco', text: 'Saída Almoço (12:00)', tolerancia: ''},
                    {value: 'entrada_tarde', text: 'Retorno Almoço (13:00)', tolerancia: ''},
                    {value: 'saida', text: 'Saída (17:00)', tolerancia: ''}
                ],
                todos_tipos: [
                    {value: 'entrada_manha', text: 'Entrada Manhã (08:00)', tolerancia: ''},
                    {value: 'saida_almoco', text: 'Saída Almoço (12:00)', tolerancia: ''},
                    {value: 'entrada_tarde', text: 'Retorno Almoço (13:00)', tolerancia: ''},
                    {value: 'saida', text: 'Saída (17:00)', tolerancia: ''}
                ],
                registros_existentes: [],
                hora_atual: horaAtual
            };

            // Armazenar dados padrão na variável global
            window.horariosFuncionario = dadosPadrao;
            return dadosPadrao;
        });
}
```

### Função JavaScript para Registrar Ponto

```javascript
function registrarPonto() {
    // Obter tipo de registro selecionado
    const tipoRegistro = document.querySelector('input[name="tipo_registro"]:checked');
    if (!tipoRegistro) {
        alert('Por favor, selecione um tipo de registro');
        return;
    }

    // Obter observações
    const observacoes = document.getElementById('observacoes').value.trim();

    // Desabilitar o botão durante o envio
    btnConfirmar.disabled = true;
    btnConfirmar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';

    // Mostrar overlay de loading
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }

    // Usar FormData em vez de JSON
    const formData = new FormData();
    formData.append('funcionario_id', funcionarioSelecionado);
    formData.append('tipo_registro', tipoRegistro.value);
    formData.append('observacoes', observacoes);

    // Enviar requisição para API
    fetch('/registro-ponto/api/registrar-manual', {
        method: 'POST',
        body: formData // Enviar FormData em vez de JSON
    })
    .then(response => response.json())
    .then(data => {
        // Ocultar loading
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }

        if (data.success) {
            // Sucesso - mostrar mensagem e fechar modal
            const resultadoDiv = document.getElementById('resultado-registro');
            if (resultadoDiv) {
                resultadoDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        ${data.message}
                        <br><small>Horário: ${data.hora_registro || 'N/A'} | Status: ${data.status_pontualidade || 'N/A'}</small>
                    </div>
                `;
            }

            // Fechar modal após 2 segundos
            setTimeout(() => {
                registroModal.hide();
                // Recarregar página para mostrar novos registros
                location.reload();
            }, 2000);

        } else {
            // Erro - mostrar mensagem de erro
            const resultadoDiv = document.getElementById('resultado-registro');
            if (resultadoDiv) {
                resultadoDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${data.message}
                    </div>
                `;
            }

            // Reabilitar botão
            btnConfirmar.disabled = false;
            btnConfirmar.innerHTML = '<i class="fas fa-save"></i> Registrar Ponto';
        }
    })
    .catch(error => {
        console.error('Erro na requisição:', error);

        // Ocultar loading
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }

        // Mostrar erro
        const resultadoDiv = document.getElementById('resultado-registro');
        if (resultadoDiv) {
            resultadoDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Erro de conexão: ${error.message}
                </div>
            `;
        }

        // Reabilitar botão
        btnConfirmar.disabled = false;
        btnConfirmar.innerHTML = '<i class="fas fa-save"></i> Registrar Ponto';
    });
}
```

### Lógica de Períodos

**Determinação do Período Atual**:
```python
# Manhã: entrada_manha <= hora_atual <= saida_almoco
if entrada_manha <= hora_atual_obj <= saida_almoco:
    periodo_atual = "manha"

# Almoço: saida_almoco <= hora_atual <= entrada_tarde
if saida_almoco <= hora_atual_obj <= entrada_tarde:
    periodo_atual = "almoco"

# Tarde: entrada_tarde <= hora_atual <= saida
if entrada_tarde <= hora_atual_obj <= saida:
    periodo_atual = "tarde"
```

### Cálculo de Status de Pontualidade

**Para Entrada Manhã**:
```python
# Horário limite = entrada_manha + tolerancia_minutos
hora_limite = datetime.combine(date.today(), entrada_manha)
hora_limite = (hora_limite + timedelta(minutes=tolerancia)).time()

if hora_atual_obj > hora_limite:
    status = "Atrasado"
else:
    status = "Pontual"
```

**Para Retorno do Almoço**:
```python
# Similar à entrada manhã
hora_limite = datetime.combine(date.today(), entrada_tarde)
hora_limite = (hora_limite + timedelta(minutes=tolerancia)).time()

if hora_atual_obj > hora_limite:
    status = "Atrasado"
else:
    status = "Pontual"
```

### Validação de Duplicatas

```python
def validar_duplicata_registro(funcionario_id, tipo_registro, data_atual):
    """
    Verifica se já existe registro do mesmo tipo no dia.
    Também valida a sequência correta de registros.

    Retorna:
    {
        'existe': bool,
        'mensagem': str,
        'registro_existente': dict ou None
    }
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Verificar se já existe registro do mesmo tipo no dia
        query = """
            SELECT id, hora_registro, observacoes
            FROM registros_ponto
            WHERE funcionario_id = %s AND data_registro = %s AND tipo_registro = %s
        """
        cursor.execute(query, (funcionario_id, data_atual, tipo_registro))
        registro_existente = cursor.fetchone()

        if registro_existente:
            conn.close()
            return {
                'existe': True,
                'mensagem': f'Já existe um registro de {tipo_registro.replace("_", " ").title()} para hoje às {registro_existente[1]}',
                'registro_existente': {
                    'id': registro_existente[0],
                    'hora_registro': str(registro_existente[1]),
                    'observacoes': registro_existente[2]
                }
            }

        # Verificar sequência correta de registros
        query_todos_registros = """
            SELECT tipo_registro, hora_registro
            FROM registros_ponto
            WHERE funcionario_id = %s AND data_registro = %s
            ORDER BY hora_registro
        """
        cursor.execute(query_todos_registros, (funcionario_id, data_atual))
        registros_do_dia = cursor.fetchall()

        # Sequência correta esperada
        sequencia_correta = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']

        if registros_do_dia:
            # Verificar qual é a posição do tipo de registro na sequência
            if tipo_registro in sequencia_correta:
                indice_atual = sequencia_correta.index(tipo_registro)

                # Se não for o primeiro registro da sequência, verificar se o anterior foi registrado
                if indice_atual > 0:
                    tipo_anterior = sequencia_correta[indice_atual - 1]
                    tipo_anterior_registrado = False

                    for registro in registros_do_dia:
                        if registro[0] == tipo_anterior:  # registro[0] é tipo_registro
                            tipo_anterior_registrado = True
                            break

                    if not tipo_anterior_registrado:
                        tipo_anterior_formatado = tipo_anterior.replace('_', ' ').title()
                        conn.close()
                        return {
                            'existe': True,
                            'mensagem': f"Não é possível registrar {tipo_registro.replace('_', ' ').title()} sem antes registrar {tipo_anterior_formatado}",
                            'registro_existente': None
                        }

                # Verificar se há registros posteriores na sequência que já foram feitos
                if indice_atual < len(sequencia_correta) - 1:
                    for i in range(indice_atual + 1, len(sequencia_correta)):
                        tipo_posterior = sequencia_correta[i]

                        for registro in registros_do_dia:
                            if registro[0] == tipo_posterior:  # registro[0] é tipo_registro
                                tipo_posterior_formatado = tipo_posterior.replace('_', ' ').title()
                                conn.close()
                                return {
                                    'existe': True,
                                    'mensagem': f"Não é possível registrar {tipo_registro.replace('_', ' ').title()} pois já existe um registro posterior ({tipo_posterior_formatado}) hoje",
                                    'registro_existente': None
                                }

        conn.close()
        # Nenhuma duplicata ou problema de sequência encontrado
        return {'existe': False, 'mensagem': '', 'registro_existente': None}

    except Exception as e:
        logger.error(f"Erro ao validar duplicata: {e}")
        return {
            'existe': True,
            'mensagem': f'Erro ao verificar registros existentes: {str(e)}',
            'registro_existente': None
        }
```

## 📋 Estrutura do Banco de Dados

### Tabela `registros_ponto`

```sql
CREATE TABLE registros_ponto (
    id INT PRIMARY KEY AUTO_INCREMENT,
    funcionario_id INT NOT NULL,
    data_registro DATE NOT NULL,
    hora_registro TIME NOT NULL,
    tipo_registro ENUM('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida'),
    metodo_registro ENUM('biometrico', 'manual'),
    observacoes TEXT,
    status_pontualidade ENUM('Pontual', 'Atrasado'),
    qualidade_biometria INT,
    usuario_registro_id INT,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id),
    FOREIGN KEY (usuario_registro_id) REFERENCES usuarios(id),

    UNIQUE KEY unique_registro_dia (funcionario_id, data_registro, tipo_registro)
);
```

### Índices Importantes

```sql
-- Índice para consultas por funcionário e data
CREATE INDEX idx_funcionario_data ON registros_ponto(funcionario_id, data_registro);

-- Índice para consultas por tipo de registro
CREATE INDEX idx_tipo_registro ON registros_ponto(tipo_registro);

-- Índice para consultas por método
CREATE INDEX idx_metodo_registro ON registros_ponto(metodo_registro);
```

## 🎯 Casos de Uso Específicos

### 1. Funcionário Chegou Atrasado
- **Cenário**: Funcionário deveria entrar às 08:00, chegou às 08:30
- **Tolerância**: 10 minutos (até 08:10 = Pontual)
- **Resultado**: Status "Atrasado", observação automática com minutos de atraso

### 2. Registro Fora do Horário Padrão
- **Cenário**: Tentativa de registrar entrada às 15:00
- **Validação**: Sistema bloqueia - não é período de entrada
- **Resultado**: Erro com mensagem explicativa

### 3. Registro Duplicado
- **Cenário**: Tentar registrar entrada manhã duas vezes no mesmo dia
- **Validação**: Sistema detecta duplicata
- **Resultado**: Erro informando registro já existente

### 4. Funcionário em Horário Especial
- **Cenário**: Funcionário com jornada diferenciada
- **Sistema**: Busca horários específicos da jornada do funcionário
- **Validação**: Aplica regras baseadas na jornada individual

## 🔧 Configurações do Sistema

### Tolerâncias Padrão
```python
TOLERANCIA_ENTRADA = 10  # minutos
TOLERANCIA_SAIDA_ALMOCO = 10  # minutos
TOLERANCIA_RETORNO_ALMOCO = 10  # minutos
TOLERANCIA_SAIDA_ANTES = 10  # minutos antes
TOLERANCIA_SAIDA_DEPOIS = 120  # minutos depois
```

### Fuso Horário
```python
# Sistema configurado para fuso de Manaus (UTC-4)
FUSO_HORARIO = 'America/Manaus'
```

### Tipos de Registro Válidos
```python
TIPOS_REGISTRO = [
    'entrada_manha',
    'saida_almoco',
    'entrada_tarde',
    'saida'
]
```

---

**Desenvolvido por**: AiNexus Tecnologia
**Autor**: Richardson Rodrigues - Full Stack Developer
**Data**: 09/07/2025
**Versão**: 1.0
