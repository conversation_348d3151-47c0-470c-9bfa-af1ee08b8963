# 📊 LÓGICA DOS PERÍODOS NO PONTO MANUAL

**Data:** 11/07/2025  
**Sistema:** RLPONTO-WEB - Classificação Inteligente de Períodos  

## 🕐 COMO O SISTEMA DETERMINA OS PERÍODOS

### 📋 **HORÁRIOS BASE (Exemplo)**
```
Entrada Manhã:  08:00
Saída Almoço:   12:00  
Entrada Tarde:  13:00
Saída Final:    17:00
Tolerância:     10 minutos
```

### 🔍 **LÓGICA DE CLASSIFICAÇÃO DOS PERÍODOS**

#### **1. ANTES DO EXPEDIENTE**
```python
if hora_atual < entrada_manha:
    periodo_atual = "antes_expediente"
```
**Exemplo:** 07:30 → "antes_expediente"
- **Hor<PERSON>rio:** Antes de 08:00
- **Ação:** Permite entrada antecipada (até 1h antes)

#### **2. PERÍODO DA MANHÃ**
```python
if entrada_manha <= hora_atual <= saida_almoco:
    periodo_atual = "manha"
```
**Exemplo:** 08:24 → "manha"
- **Hor<PERSON>rio:** 08:00 até 12:00
- **Ação:** Permite entrada_manha (mesmo atrasada) ou saida_almoco

#### **3. PERÍODO DE ALMOÇO**
```python
if saida_almoco < hora_atual < entrada_tarde:
    periodo_atual = "almoco"
```
**Exemplo:** 12:30 → "almoco"
- **Horário:** 12:01 até 12:59
- **Ação:** Período de intervalo (não permite registros)

#### **4. PERÍODO DA TARDE**
```python
if entrada_tarde <= hora_atual <= saida:
    periodo_atual = "tarde"
```
**Exemplo:** 14:00 → "tarde"
- **Horário:** 13:00 até 17:00
- **Ação:** Permite entrada_tarde ou saida

#### **5. APÓS EXPEDIENTE**
```python
if hora_atual > saida:
    periodo_atual = "apos_expediente"
```
**Exemplo:** 18:00 → "apos_expediente"
- **Horário:** Após 17:00
- **Ação:** Permite registros extras ou noturnos

## 🎯 **EXEMPLOS PRÁTICOS**

### **Cenário 1: Funcionário Atrasado (08:24)**
```
Horários configurados: 08:00 - 12:00 - 13:00 - 17:00
Hora atual: 08:24
Registros existentes: [] (nenhum)

ANÁLISE:
✅ 08:24 está entre 08:00 e 12:00 → período = "manha"
✅ É a primeira batida (numero_batida = 1)
✅ Período "manha" + primeira batida → tipo = "entrada_manha"

RESULTADO: Permite entrada_manha (atrasada)
```

### **Cenário 2: Saída para Almoço (12:15)**
```
Horários configurados: 08:00 - 12:00 - 13:00 - 17:00
Hora atual: 12:15
Registros existentes: [entrada_manha: 08:24]

ANÁLISE:
✅ 12:15 está entre 12:01 e 12:59 → período = "almoco"
✅ É a segunda batida (numero_batida = 2)
✅ Período "almoco" + segunda batida → tipo = "saida_almoco"

RESULTADO: Permite saida_almoco
```

### **Cenário 3: Entrada Muito Atrasada (14:00)**
```
Horários configurados: 08:00 - 12:00 - 13:00 - 17:00
Hora atual: 14:00
Registros existentes: [] (nenhum)

ANÁLISE:
✅ 14:00 está entre 13:00 e 17:00 → período = "tarde"
✅ É a primeira batida (numero_batida = 1)
✅ Período "tarde" + primeira batida → tipo = "entrada_tarde"

RESULTADO: Permite entrada_tarde (não entrada_manha)
```

### **Cenário 4: Retorno do Almoço (13:30)**
```
Horários configurados: 08:00 - 12:00 - 13:00 - 17:00
Hora atual: 13:30
Registros existentes: [entrada_manha: 08:24, saida_almoco: 12:15]

ANÁLISE:
✅ 13:30 está entre 13:00 e 17:00 → período = "tarde"
✅ É a terceira batida (numero_batida = 3)
✅ Período "tarde" + terceira batida → tipo = "entrada_tarde"

RESULTADO: Permite entrada_tarde
```

## 🔧 **REGRAS ESPECIAIS**

### **1. Primeira Batida do Dia**
```python
if numero_batida == 1:
    if periodo_atual in ["antes_expediente", "manha"]:
        return 'entrada_manha'  # Entrada normal ou atrasada
    elif periodo_atual == "tarde":
        return 'entrada_tarde'  # Entrada muito atrasada
    elif periodo_atual == "apos_expediente":
        return 'entrada_manha'  # Entrada noturna/extra
```

### **2. Segunda Batida do Dia**
```python
if numero_batida == 2:
    if periodo_atual == "manha":
        return 'saida_almoco'   # Saída para almoço
    elif periodo_atual == "tarde":
        return 'saida'          # Saída direta (sem almoço)
    elif periodo_atual == "apos_expediente":
        return 'saida'          # Saída final
```

### **3. Validações Adicionais**
- **Entrada antecipada:** Máximo 1h antes do horário
- **Entrada atrasada:** Máximo 1h após o horário
- **Intervalo mínimo:** 1h entre saida_almoco e entrada_tarde
- **Sequência lógica:** Respeita ordem das batidas

## 📊 **TABELA DE DECISÃO**

| Hora Atual | Período | 1ª Batida | 2ª Batida | 3ª Batida | 4ª Batida |
|------------|---------|-----------|-----------|-----------|-----------|
| 07:30 | antes_expediente | entrada_manha | - | - | - |
| 08:24 | manha | entrada_manha | saida_almoco | - | - |
| 10:30 | manha | entrada_manha | saida_almoco | - | - |
| 12:30 | almoco | entrada_manha* | saida_almoco | entrada_tarde | - |
| 14:00 | tarde | entrada_tarde | saida | entrada_tarde | saida |
| 18:00 | apos_expediente | entrada_manha | saida | saida | saida |

*\* Se for primeira batida em período de almoço, classifica como entrada_manha atrasada*

## 🚨 **CASOS ESPECIAIS**

### **Funcionário com Batida Existente às 08:24**
```
Situação: Funcionário já tem entrada_manha às 08:00
Tentativa: Nova batida às 08:24

ANÁLISE:
❌ entrada_manha já registrada
✅ 08:24 está no período "manha"
✅ É a segunda batida (numero_batida = 2)
✅ Período "manha" + segunda batida → tipo = "saida_almoco"

RESULTADO: Oferece saida_almoco (correto)
```

### **Funcionário sem Batidas às 08:24**
```
Situação: Funcionário não tem nenhuma batida hoje
Tentativa: Nova batida às 08:24

ANÁLISE:
✅ Nenhum registro existente
✅ 08:24 está no período "manha"
✅ É a primeira batida (numero_batida = 1)
✅ Período "manha" + primeira batida → tipo = "entrada_manha"

RESULTADO: Oferece entrada_manha (correto)
```

## 🔍 **LOGS DE DEBUGGING**

O sistema gera logs detalhados para debugging:

```
[CLASSIFICAÇÃO INTELIGENTE] Funcionário: 1, Batida: #1, Hora: 08:24:00, Período: manha
[API HORÁRIOS] Funcionário: 1, Registros: 0, Próximo tipo: entrada_manha
```

### **Como Interpretar os Logs:**
- **Funcionário:** ID do funcionário
- **Batida:** Número sequencial da batida do dia
- **Hora:** Horário atual da tentativa
- **Período:** Período determinado pela lógica
- **Registros:** Quantidade de registros já existentes
- **Próximo tipo:** Tipo de registro determinado

## 🎯 **CONCLUSÃO**

A lógica se baseia em **dois fatores principais:**

1. **Horário atual** vs. **horários configurados** → Determina o período
2. **Número de batidas existentes** → Determina a sequência

**Resultado:** Classificação inteligente que considera o contexto real, evitando erros como confundir entrada atrasada com início de intervalo.
