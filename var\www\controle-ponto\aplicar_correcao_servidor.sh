#!/bin/bash
# ========================================
# SCRIPT DE CORREÇÃO PARA SERVIDOR REMOTO
# Data: 05/06/2025
# Problema: Página registro manual não mostra funcionários
# ========================================

set -e  # Parar se algum comando falhar

echo "========================================================================"
echo "🔧 APLICANDO CORREÇÃO NO SERVIDOR REMOTO - PÁGINA REGISTRO MANUAL"
echo "========================================================================"

# Variáveis
PROJETO_DIR="/var/www/controle-ponto"
BACKUP_DIR="/var/backups/controle-ponto"
DATA_BACKUP=$(date +%Y%m%d_%H%M%S)
ARQUIVO_BLUEPRINT="$PROJETO_DIR/app_registro_ponto.py"

# Verificar se estamos no diretório correto
if [ ! -f "$ARQUIVO_BLUEPRINT" ]; then
    echo "❌ Erro: Arquivo app_registro_ponto.py não encontrado em $PROJETO_DIR"
    echo "   Por favor, execute este script no servidor correto"
    exit 1
fi

echo "📁 Diretório do projeto: $PROJETO_DIR"
echo "📁 Diretório de backup: $BACKUP_DIR"

# Criar diretório de backup se não existir
mkdir -p "$BACKUP_DIR"

# 1. BACKUP DOS ARQUIVOS
echo ""
echo "📦 Fazendo backup dos arquivos..."
cp "$ARQUIVO_BLUEPRINT" "$BACKUP_DIR/app_registro_ponto.py.backup_$DATA_BACKUP"
echo "✅ Backup salvo: app_registro_ponto.py.backup_$DATA_BACKUP"

# 2. EXECUTAR SQL DE DIAGNÓSTICO
echo ""
echo "🗄️ Executando diagnóstico SQL..."
if [ -f "correcao_funcionarios.sql" ]; then
    mysql -u root -p controle_ponto < correcao_funcionarios.sql
    echo "✅ SQL de diagnóstico executado"
else
    echo "⚠️  Arquivo correcao_funcionarios.sql não encontrado - executar manualmente"
fi

# 3. APLICAR CORREÇÃO NO CÓDIGO
echo ""
echo "🔧 Aplicando correção no código Python..."

# Criar arquivo temporário com a correção
cat > "/tmp/correcao_query.py" << 'EOF'
import re
import sys

def corrigir_query_funcionarios(arquivo):
    """Corrige a query SQL na função pagina_registro_manual"""
    
    with open(arquivo, 'r', encoding='utf-8') as f:
        conteudo = f.read()
    
    # Padrão para encontrar a query problemática
    padrao_original = r'WHERE f\.ativo = TRUE'
    
    # Substituição
    query_corrigida = 'WHERE (f.ativo = 1 OR f.status_cadastro = \'Ativo\')'
    
    # Fazer a substituição
    conteudo_corrigido = re.sub(padrao_original, query_corrigida, conteudo)
    
    # Verificar se houve alteração
    if conteudo != conteudo_corrigido:
        print("✅ Query corrigida encontrada e substituída")
        
        # Salvar arquivo corrigido
        with open(arquivo, 'w', encoding='utf-8') as f:
            f.write(conteudo_corrigido)
        return True
    else:
        print("⚠️  Query original não encontrada - pode já estar corrigida")
        return False

if __name__ == "__main__":
    arquivo = sys.argv[1] if len(sys.argv) > 1 else "app_registro_ponto.py"
    corrigir_query_funcionarios(arquivo)
EOF

# Executar correção
python3 /tmp/correcao_query.py "$ARQUIVO_BLUEPRINT"

# 4. ADICIONAR ROTA DE TESTE (se não existir)
echo ""
echo "🧪 Adicionando rota de teste..."

if ! grep -q "manual-teste" "$ARQUIVO_BLUEPRINT"; then
    cat >> "$ARQUIVO_BLUEPRINT" << 'EOF'

# ✅ ROTA TEMPORÁRIA PARA TESTAR CORREÇÃO
@registro_ponto_bp.route('/manual-teste')
@require_login
def pagina_registro_manual_teste():
    """Versão de teste da página manual com query corrigida"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Query corrigida
        cursor.execute("""
            SELECT 
                f.id,
                f.nome_completo,
                COALESCE(f.cpf, 'N/A') as cpf,
                COALESCE(f.matricula_empresa, 'N/A') as matricula_empresa,
                COALESCE(f.cargo, 'N/A') as cargo,
                COALESCE(f.setor, f.setor_obra, 'N/A') as setor,
                f.foto_3x4,
                'Empresa Padrão' AS empresa,
                'Horário Padrão' AS nome_horario
            FROM funcionarios f
            WHERE (f.ativo = 1 OR f.status_cadastro = 'Ativo')
            ORDER BY f.nome_completo
        """)
        
        funcionarios_raw = cursor.fetchall()
        conn.close()
        
        funcionarios = []
        for f in funcionarios_raw:
            funcionario = {
                'id': f[0],
                'nome_completo': f[1],
                'cpf': f[2],
                'cpf_exibicao': f[2][:3] + '.***.***-' + f[2][-2:] if f[2] != 'N/A' else 'N/A',
                'matricula_empresa': f[3],
                'cargo': f[4],
                'setor': f[5],
                'foto_url': '/static/images/funcionario_sem_foto.svg',
                'empresa': f[6],
                'horario_trabalho': f[7]
            }
            funcionarios.append(funcionario)
        
        from datetime import datetime
        context = {
            'titulo': 'Registro Manual - TESTE CORRIGIDO',
            'funcionarios': funcionarios,
            'total_funcionarios': len(funcionarios),
            'data_atual': datetime.now().strftime('%d/%m/%Y'),
            'hora_atual': datetime.now().strftime('%H:%M'),
            'nivel_acesso': 'admin'
        }
        
        return render_template('registro_ponto/manual.html', **context)
        
    except Exception as e:
        import traceback
        return f"<h1>Erro no teste: {str(e)}</h1><pre>{traceback.format_exc()}</pre>"
EOF
    echo "✅ Rota de teste adicionada"
else
    echo "⚠️  Rota de teste já existe"
fi

# 5. DETECTAR E REINICIAR SERVIDOR WEB
echo ""
echo "🔄 Reiniciando servidor web..."

if systemctl is-active --quiet apache2; then
    echo "🔄 Reiniciando Apache..."
    systemctl restart apache2
    echo "✅ Apache reiniciado"
elif systemctl is-active --quiet nginx; then
    echo "🔄 Reiniciando Nginx..."
    systemctl restart nginx
    echo "✅ Nginx reiniciado"
else
    echo "⚠️  Servidor web não detectado automaticamente"
    echo "   Execute manualmente: sudo systemctl restart apache2"
    echo "   Ou: sudo systemctl restart nginx"
fi

# 6. INSTRUÇÕES FINAIS
echo ""
echo "========================================================================"
echo "🎉 CORREÇÃO APLICADA COM SUCESSO!"
echo "========================================================================"
echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo "   1. 🧪 Teste a rota temporária: http://************/registro-ponto/manual-teste"
echo "   2. ✅ Se funcionar, teste a original: http://************/registro-ponto/manual"
echo "   3. 🗑️  Após confirmar, remova a rota de teste do arquivo"
echo ""
echo "📁 BACKUP SALVO EM: $BACKUP_DIR/app_registro_ponto.py.backup_$DATA_BACKUP"
echo ""
echo "⚠️  EM CASO DE PROBLEMA:"
echo "   Restaure o backup: cp $BACKUP_DIR/app_registro_ponto.py.backup_$DATA_BACKUP $ARQUIVO_BLUEPRINT"
echo ""

# Cleanup
rm -f /tmp/correcao_query.py

echo "�� Script concluído!" 