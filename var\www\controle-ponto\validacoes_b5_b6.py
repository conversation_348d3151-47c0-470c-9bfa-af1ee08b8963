"""
RLPONTO-WEB v2.0 - Validações B5/B6 (Horas Extras)
Sistema de validação para batidas de hora extra
Data: 11/07/2025
"""

import logging
from datetime import datetime, timedelta
from utils.database import get_db_connection
from pymysql.cursors import DictCursor

logger = logging.getLogger(__name__)

def validar_b5_inicio_extra(funcionario_id, horario_batida):
    """
    Valida se B5 (início de hora extra) pode ser registrado
    
    Args:
        funcionario_id (int): ID do funcionário
        horario_batida (datetime): Hor<PERSON><PERSON> da batida
    
    Returns:
        dict: Resultado da validação
    """
    try:
        # 1. Verificar se B1, B2, B3, B4 estão completos
        batidas_dia = obter_batidas_do_dia(funcionario_id, horario_batida.date())
        tipos_registrados = [b['tipo_registro'] for b in batidas_dia]
        
        # Pré-requisitos obrigatórios
        requisitos = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']
        
        for req in requisitos:
            if req not in tipos_registrados:
                return {
                    'permitido': False,
                    'mensagem': f'B5 não permitido: falta {req}. Complete a jornada primeiro.',
                    'codigo_erro': 'JORNADA_INCOMPLETA'
                }
        
        # 2. Verificar tolerância de saída
        ultima_saida = None
        for batida in batidas_dia:
            if batida['tipo_registro'] == 'saida':
                ultima_saida = batida
                break
        
        if not ultima_saida:
            return {
                'permitido': False,
                'mensagem': 'B5 não permitido: saída (B4) não encontrada.',
                'codigo_erro': 'SAIDA_NAO_ENCONTRADA'
            }
        
        # 3. Obter jornada e calcular tolerância
        jornada = obter_jornada_funcionario(funcionario_id)
        if not jornada:
            return {
                'permitido': False,
                'mensagem': 'Funcionário não possui jornada configurada.',
                'codigo_erro': 'JORNADA_NAO_CONFIGURADA'
            }
        
        # Calcular horário limite (saída + 10min tolerância)
        horario_saida_previsto = datetime.combine(
            horario_batida.date(),
            jornada['seg_qui_saida'] if horario_batida.weekday() < 4 else jornada['sexta_saida']
        )
        tolerancia_saida = horario_saida_previsto + timedelta(minutes=10)
        
        if horario_batida <= tolerancia_saida:
            return {
                'permitido': False,
                'mensagem': f'B5 só permitido após {tolerancia_saida.strftime("%H:%M")}. Ainda dentro da tolerância de saída.',
                'codigo_erro': 'DENTRO_TOLERANCIA'
            }
        
        # 4. Verificar se já existe B5 hoje
        if 'inicio_extra' in tipos_registrados:
            return {
                'permitido': False,
                'mensagem': 'B5 já foi registrado hoje.',
                'codigo_erro': 'B5_JA_REGISTRADO'
            }
        
        return {
            'permitido': True,
            'tipo_registro': 'inicio_extra',
            'status': 'HORA_EXTRA_INICIADA',
            'requer_aprovacao': True,
            'mensagem': 'B5 (início de hora extra) autorizado.'
        }
        
    except Exception as e:
        logger.error(f"Erro ao validar B5: {e}")
        return {
            'permitido': False,
            'mensagem': 'Erro interno na validação B5.',
            'codigo_erro': 'ERRO_INTERNO'
        }

def validar_b6_fim_extra(funcionario_id, horario_batida):
    """
    Valida se B6 (fim de hora extra) pode ser registrado
    
    Args:
        funcionario_id (int): ID do funcionário
        horario_batida (datetime): Horário da batida
    
    Returns:
        dict: Resultado da validação
    """
    try:
        # 1. Verificar se B5 foi iniciado
        batidas_dia = obter_batidas_do_dia(funcionario_id, horario_batida.date())
        tipos_registrados = [b['tipo_registro'] for b in batidas_dia]
        
        if 'inicio_extra' not in tipos_registrados:
            return {
                'permitido': False,
                'mensagem': 'B6 inválido: B5 (início de hora extra) não foi registrado.',
                'codigo_erro': 'B5_NAO_REGISTRADO'
            }
        
        # 2. Verificar se B6 já foi registrado
        if 'fim_extra' in tipos_registrados:
            return {
                'permitido': False,
                'mensagem': 'B6 já foi registrado hoje.',
                'codigo_erro': 'B6_JA_REGISTRADO'
            }
        
        # 3. Obter horário do B5
        inicio_extra = None
        for batida in batidas_dia:
            if batida['tipo_registro'] == 'inicio_extra':
                inicio_extra = batida
                break
        
        if not inicio_extra:
            return {
                'permitido': False,
                'mensagem': 'B6 inválido: registro B5 não encontrado.',
                'codigo_erro': 'B5_NAO_ENCONTRADO'
            }
        
        # 4. Calcular duração da hora extra
        duracao_extra = horario_batida - inicio_extra['data_hora']
        duracao_minutos = int(duracao_extra.total_seconds() / 60)
        
        # Validar duração mínima (15 minutos)
        if duracao_minutos < 15:
            return {
                'permitido': False,
                'mensagem': f'Duração mínima de hora extra: 15 minutos. Atual: {duracao_minutos}min.',
                'codigo_erro': 'DURACAO_INSUFICIENTE'
            }
        
        return {
            'permitido': True,
            'tipo_registro': 'fim_extra',
            'duracao_extra_minutos': duracao_minutos,
            'status': 'HORA_EXTRA_FINALIZADA',
            'requer_aprovacao': True,
            'mensagem': f'B6 (fim de hora extra) autorizado. Duração: {duracao_minutos}min.'
        }
        
    except Exception as e:
        logger.error(f"Erro ao validar B6: {e}")
        return {
            'permitido': False,
            'mensagem': 'Erro interno na validação B6.',
            'codigo_erro': 'ERRO_INTERNO'
        }

def obter_batidas_do_dia(funcionario_id, data_referencia):
    """
    Obtém todas as batidas do funcionário em uma data específica
    
    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date): Data de referência
    
    Returns:
        list: Lista de batidas do dia
    """
    try:
        query = """
            SELECT id, tipo_registro, data_hora, metodo_registro, status_pontualidade
            FROM registros_ponto
            WHERE funcionario_id = %s 
            AND DATE(data_hora) = %s
            ORDER BY data_hora ASC
        """
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        cursor.execute(query, (funcionario_id, data_referencia))
        result = cursor.fetchall()
        cursor.close()
        conn.close()
        return result if result else []
        
    except Exception as e:
        logger.error(f"Erro ao obter batidas do dia: {e}")
        return []

def obter_jornada_funcionario(funcionario_id):
    """
    Obtém a jornada de trabalho do funcionário
    
    Args:
        funcionario_id (int): ID do funcionário
    
    Returns:
        dict: Dados da jornada ou None
    """
    try:
        query = """
            SELECT 
                jt.seg_qui_entrada,
                jt.seg_qui_saida,
                jt.sexta_entrada,
                jt.sexta_saida,
                jt.intervalo_inicio,
                jt.intervalo_fim,
                jt.tolerancia_entrada_minutos
            FROM funcionarios f
            INNER JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
            WHERE f.id = %s AND jt.ativa = 1
        """
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        cursor.execute(query, (funcionario_id,))
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        return result if result else None
        
    except Exception as e:
        logger.error(f"Erro ao obter jornada do funcionário: {e}")
        return None

def verificar_dia_especial(data_referencia):
    """
    Verifica se a data é sábado, domingo ou feriado
    
    Args:
        data_referencia (date): Data a verificar
    
    Returns:
        dict: Informações sobre o dia
    """
    try:
        dia_semana = data_referencia.weekday()  # 0=Segunda, 6=Domingo
        
        # Verificar feriado
        query = """
            SELECT nome_feriado, tipo
            FROM feriados
            WHERE data_feriado = %s AND ativo = TRUE
        """
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        cursor.execute(query, (data_referencia,))
        feriado = cursor.fetchall()
        cursor.close()
        conn.close()
        
        if feriado:
            return {
                'eh_especial': True,
                'tipo': 'FERIADO',
                'nome': feriado[0]['nome_feriado'],
                'porcentagem_disponivel': True
            }
        elif dia_semana == 5:  # Sábado
            return {
                'eh_especial': True,
                'tipo': 'SABADO',
                'nome': 'Sábado',
                'porcentagem_disponivel': True
            }
        elif dia_semana == 6:  # Domingo
            return {
                'eh_especial': True,
                'tipo': 'DOMINGO',
                'nome': 'Domingo',
                'porcentagem_disponivel': True
            }
        else:
            return {
                'eh_especial': False,
                'tipo': 'NORMAL',
                'nome': 'Dia útil',
                'porcentagem_disponivel': False
            }
            
    except Exception as e:
        logger.error(f"Erro ao verificar dia especial: {e}")
        return {
            'eh_especial': False,
            'tipo': 'NORMAL',
            'nome': 'Dia útil',
            'porcentagem_disponivel': False
        }
