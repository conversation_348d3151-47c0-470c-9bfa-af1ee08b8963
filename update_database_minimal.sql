-- RLPONTO-WEB v2.0 - <PERSON><PERSON><PERSON>MA SEM ERROS
-- Compatível com QUALQUER MySQL - SEM foreign keys
-- Data: 06/01/2025

USE controle_ponto;

-- <PERSON><PERSON><PERSON> tabelas básicas (SEM foreign keys para evitar erros)
CREATE TABLE IF NOT EXISTS logs_biometria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    similarity_score DECIMAL(5,4) DEFAULT 0.0000,
    device_info TEXT,
    status VARCHAR(20) DEFAULT 'failed',
    ip_address VARCHAR(45),
    user_agent TEXT
);

CREATE TABLE IF NOT EXISTS logs_seguranca (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo_evento VARCHAR(50) NOT NULL,
    funcionario_id INT,
    detalhes TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    nivel_risco VARCHAR(10) DEFAULT 'baixo'
);

CREATE TABLE IF NOT EXISTS configuracoes_sistema (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chave VARCHAR(100) UNIQUE NOT NULL,
    valor TEXT,
    descricao TEXT,
    tipo VARCHAR(20) DEFAULT 'string',
    categoria VARCHAR(50) DEFAULT 'geral',
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    atualizado_em DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Inserir configurações (se não existirem)
INSERT IGNORE INTO configuracoes_sistema (chave, valor, descricao, tipo, categoria) VALUES
('biometric_threshold', '0.7', 'Limite biométrico', 'string', 'biometria'),
('morning_start', '07:00', 'Início manhã', 'string', 'horarios'),
('morning_end', '09:30', 'Fim manhã', 'string', 'horarios'),
('evening_start', '17:00', 'Início tarde', 'string', 'horarios'),
('evening_end', '19:00', 'Fim tarde', 'string', 'horarios');

-- Log de sucesso
INSERT INTO logs_seguranca (tipo_evento, detalhes, nivel_risco) VALUES 
('database_update', 'Atualização v2.0 concluída', 'baixo');

SELECT 'RLPONTO-WEB v2.0 - Banco atualizado com SUCESSO!' as resultado; 