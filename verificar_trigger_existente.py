#!/usr/bin/env python3
"""
Verificar: Por que o trigger existente não está funcionando?
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def verificar_trigger_existente():
    """Verificar trigger existente"""
    
    print("🔍 VERIFICAÇÃO: TRIGGER EXISTENTE")
    print("=" * 50)
    
    db = DatabaseManager()
    
    # 1. Verificar detalhes do trigger
    print("\n📋 PASSO 1: Verificando detalhes do trigger...")
    
    try:
        trigger_details = db.execute_query("""
            SHOW CREATE TRIGGER tr_atualizar_jornadas_funcionarios
        """, fetch_one=True)
        
        if trigger_details:
            print("✅ Trigger encontrado:")
            print("=" * 50)
            print(trigger_details['SQL Original Statement'])
            print("=" * 50)
        else:
            print("❌ Trigger não encontrado")
    
    except Exception as e:
        print(f"❌ Erro ao verificar trigger: {e}")
    
    # 2. Testar manualmente a lógica que deveria estar no trigger
    print("\n📋 PASSO 2: Testando lógica manual...")
    
    # Buscar empresa AiNexus
    empresa = db.execute_query("""
        SELECT id, nome_fantasia FROM empresas 
        WHERE nome_fantasia LIKE 'AiNexus%' OR nome_fantasia LIKE '%AiNexus%'
        LIMIT 1
    """, fetch_one=True)
    
    if empresa:
        print(f"🏢 Empresa encontrada: {empresa['nome_fantasia']} (ID: {empresa['id']})")
        
        # Buscar jornada padrão
        jornada = db.execute_query("""
            SELECT id, nome_jornada, seg_qui_entrada, seg_qui_saida, tolerancia_entrada_minutos
            FROM jornadas_trabalho 
            WHERE empresa_id = %s AND padrao = 1 AND ativa = 1
        """, (empresa['id'],), fetch_one=True)
        
        if jornada:
            print(f"📋 Jornada padrão: {jornada['nome_jornada']} (ID: {jornada['id']})")
            print(f"   • Entrada: {jornada['seg_qui_entrada']}")
            print(f"   • Saída: {jornada['seg_qui_saida']}")
            print(f"   • Tolerância: {jornada['tolerancia_entrada_minutos']} min")
            
            # Buscar funcionários da empresa
            funcionarios = db.execute_query("""
                SELECT f.id, f.nome_completo, f.jornada_trabalho_id,
                       jt.seg_qui_entrada as func_entrada,
                       jt.tolerancia_entrada_minutos as func_tolerancia
                FROM funcionarios f
                LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
                WHERE f.empresa_id = %s AND f.ativo = 1
            """, (empresa['id'],))
            
            if funcionarios:
                print(f"\n👥 Funcionários da empresa:")
                
                for func in funcionarios:
                    print(f"   👤 {func['nome_completo']} (ID: {func['id']})")
                    print(f"      • Jornada atual: {func['jornada_trabalho_id']}")
                    print(f"      • Entrada atual: {func['func_entrada']}")
                    print(f"      • Tolerância atual: {func['func_tolerancia']}")
                    
                    # Verificar se precisa atualizar
                    if (func['jornada_trabalho_id'] != jornada['id'] or
                        str(func['func_entrada']) != str(jornada['seg_qui_entrada']) or
                        func['func_tolerancia'] != jornada['tolerancia_entrada_minutos']):
                        
                        print(f"      ❌ PRECISA ATUALIZAR!")
                        
                        # Aplicar atualização manual (simulando o que o trigger deveria fazer)
                        print(f"      🔧 Aplicando atualização...")
                        
                        try:
                            db.execute_query("""
                                UPDATE funcionarios 
                                SET jornada_trabalho_id = %s,
                                    usa_horario_empresa = TRUE
                                WHERE id = %s
                            """, (jornada['id'], func['id']), fetch_all=False)
                            
                            print(f"      ✅ Funcionário atualizado!")
                        
                        except Exception as e:
                            print(f"      ❌ Erro ao atualizar: {e}")
                    else:
                        print(f"      ✅ Já está atualizado")
            
            # Verificar alocações
            print(f"\n📋 Verificando alocações para esta empresa...")
            
            alocacoes = db.execute_query("""
                SELECT fa.id, fa.funcionario_id, f.nome_completo,
                       fa.jornada_trabalho_id as jornada_alocacao
                FROM funcionario_alocacoes fa
                INNER JOIN funcionarios f ON fa.funcionario_id = f.id
                WHERE fa.empresa_cliente_id = %s AND fa.ativo = 1
            """, (empresa['id'],))
            
            if alocacoes:
                print(f"👥 Alocações encontradas:")
                
                for aloc in alocacoes:
                    print(f"   👤 {aloc['nome_completo']} (Alocação ID: {aloc['id']})")
                    print(f"      • Jornada alocação: {aloc['jornada_alocacao']}")
                    
                    if aloc['jornada_alocacao'] != jornada['id']:
                        print(f"      ❌ ALOCAÇÃO PRECISA ATUALIZAR!")
                        
                        try:
                            db.execute_query("""
                                UPDATE funcionario_alocacoes 
                                SET jornada_trabalho_id = %s
                                WHERE id = %s
                            """, (jornada['id'], aloc['id']), fetch_all=False)
                            
                            print(f"      ✅ Alocação atualizada!")
                        
                        except Exception as e:
                            print(f"      ❌ Erro ao atualizar alocação: {e}")
                    else:
                        print(f"      ✅ Alocação já atualizada")
            else:
                print("📊 Nenhuma alocação encontrada")
    
    print(f"\n🎯 RESULTADO:")
    print(f"✅ Aplicação manual da lógica de herança concluída!")
    print(f"💡 Se o trigger existisse e funcionasse, isso seria automático!")

if __name__ == "__main__":
    verificar_trigger_existente()
