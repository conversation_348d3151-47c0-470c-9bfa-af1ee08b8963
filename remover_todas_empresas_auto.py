#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para Remo<PERSON> as Empresas Automaticamente - RLPONTO-WEB
------------------------------------------------------------------
<PERSON><PERSON><PERSON> todas as empresas do banco de dados sem solicitar confirmação.
"""

import pymysql
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.config import Config
from utils.database import DatabaseManager

def listar_empresas():
    """
    Lista todas as empresas cadastradas
    """
    try:
        empresas = DatabaseManager.fetch_all("""
        SELECT id, razao_social, cnpj, empresa_teste, 
               (SELECT COUNT(*) FROM funcionarios WHERE empresa_id = empresas.id) as total_funcionarios
        FROM empresas
        ORDER BY id
        """)
        
        print("\n===== EMPRESAS CADASTRADAS =====")
        print(f"{'ID':<5} | {'RAZÃO SOCIAL':<40} | {'CNPJ':<20} | {'TESTE':<5} | {'FUNC.':<5}")
        print("-" * 85)
        
        for empresa in empresas:
            print(f"{empresa['id']:<5} | {empresa['razao_social'][:38]:<40} | {empresa['cnpj']:<20} | {'Sim' if empresa['empresa_teste'] else 'Não':<5} | {empresa['total_funcionarios']:<5}")
        
        print("-" * 85)
        print(f"Total: {len(empresas)} empresas\n")
        
        return empresas
    except Exception as e:
        print(f"❌ Erro ao listar empresas: {e}")
        return []

def remover_funcionarios():
    """
    Remove todos os funcionários do banco de dados
    """
    try:
        # Verificar se há funcionários
        count = DatabaseManager.fetch_one("SELECT COUNT(*) as total FROM funcionarios")
        total = count['total'] if count else 0
        
        if total > 0:
            print(f"⚠️ Removendo {total} funcionários...")
            
            # Remover registros de ponto
            DatabaseManager.execute_query("DELETE FROM registros_ponto")
            print("✅ Registros de ponto removidos")
            
            # Remover EPIs
            try:
                DatabaseManager.execute_query("DELETE FROM epis")
                print("✅ EPIs removidos")
            except:
                print("ℹ️ Tabela EPIs não existe ou está vazia")
            
            # Remover funcionários
            DatabaseManager.execute_query("DELETE FROM funcionarios")
            print(f"✅ {total} funcionários removidos")
        else:
            print("✅ Não há funcionários para remover")
            
        return True
    except Exception as e:
        print(f"❌ Erro ao remover funcionários: {e}")
        return False

def remover_jornadas():
    """
    Remove todas as jornadas de trabalho
    """
    try:
        # Verificar se há jornadas
        count = DatabaseManager.fetch_one("SELECT COUNT(*) as total FROM jornadas_trabalho")
        total = count['total'] if count else 0
        
        if total > 0:
            print(f"⚠️ Removendo {total} jornadas de trabalho...")
            DatabaseManager.execute_query("DELETE FROM jornadas_trabalho")
            print(f"✅ {total} jornadas removidas")
        else:
            print("✅ Não há jornadas para remover")
            
        return True
    except Exception as e:
        print(f"❌ Erro ao remover jornadas: {e}")
        return False

def remover_empresas_auto():
    """
    Remove todas as empresas do banco de dados automaticamente
    """
    try:
        # Verificar se há empresas
        empresas = listar_empresas()
        
        if not empresas:
            print("✅ Não há empresas para remover")
            return True
            
        print(f"⚠️ REMOVENDO AUTOMATICAMENTE TODAS AS {len(empresas)} EMPRESAS...")
            
        # Remover funcionários primeiro (dependência)
        if not remover_funcionarios():
            print("❌ Falha ao remover funcionários")
            return False
            
        # Remover jornadas (dependência)
        if not remover_jornadas():
            print("❌ Falha ao remover jornadas")
            return False
            
        # Agora remover as empresas
        print(f"⚠️ Removendo {len(empresas)} empresas...")
        DatabaseManager.execute_query("DELETE FROM empresas")
        print(f"✅ {len(empresas)} empresas removidas")
        
        # Resetar auto_increment
        DatabaseManager.execute_query("ALTER TABLE empresas AUTO_INCREMENT = 1")
        print("✅ Auto_increment resetado para 1")
        
        # Verificar se todas foram removidas
        count = DatabaseManager.fetch_one("SELECT COUNT(*) as total FROM empresas")
        if count and count['total'] == 0:
            print("\n✅ TODAS AS EMPRESAS FORAM REMOVIDAS COM SUCESSO!")
            print("🔄 O sistema está pronto para começar do zero")
            return True
        else:
            print(f"⚠️ Ainda existem {count['total']} empresas no banco")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao remover empresas: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚫 REMOÇÃO AUTOMÁTICA DE TODAS AS EMPRESAS - RLPONTO-WEB")
    print("=" * 60)
    print("\n⚠️ ATENÇÃO: Esta operação removerá TODAS as empresas e dados relacionados!")
    print("⚠️ Esta ação NÃO PODE ser desfeita!\n")
    print("⚠️ EXECUTANDO EM MODO AUTOMÁTICO (sem confirmação)...\n")
    
    try:
        remover_empresas_auto()
    except KeyboardInterrupt:
        print("\n\n❌ Operação cancelada pelo usuário")
    except Exception as e:
        print(f"\n\n❌ Erro inesperado: {e}")
    
    print("\n" + "=" * 60) 