#!/usr/bin/env python3
"""
Teste específico para reproduzir o bug das 48 horas
"""
import sys
import os
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager

def testar_bug_48h():
    print("🐛 TESTE DO BUG: 44h → 48h")
    print("=" * 60)
    
    try:
        funcionario_id = 35
        db = DatabaseManager()
        
        print(f"\n1. 📊 RESETAR PARA 44H")
        # Garantir que está em 44h
        db.execute_query("""
            UPDATE funcionarios 
            SET horas_semanais_obrigatorias = 44.00 
            WHERE id = %s
        """, (funcionario_id,))
        
        verificacao = db.execute_query("""
            SELECT horas_semanais_obrigatorias 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,), fetch_one=True)
        
        print(f"   ✅ Estado inicial: {verificacao['horas_semanais_obrigatorias']}h")
        
        print(f"\n2. 🔍 VERIFICAR JORNADA DO FUNCIONÁRIO")
        # Verificar se há jornada configurada que pode estar causando o problema
        jornada = db.execute_query("""
            SELECT 
                f.id,
                f.nome_completo,
                f.horario_trabalho_id,
                ht.nome_horario,
                ht.entrada_manha,
                ht.saida_almoco,
                ht.entrada_tarde,
                ht.saida,
                -- Calcular horas da jornada
                CASE 
                    WHEN ht.entrada_manha IS NOT NULL AND ht.saida IS NOT NULL THEN
                        TIMESTAMPDIFF(MINUTE, ht.entrada_manha, ht.saida) / 60.0 - 1.0
                    ELSE NULL
                END as horas_jornada_calculada
            FROM funcionarios f
            LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
            WHERE f.id = %s
        """, (funcionario_id,), fetch_one=True)
        
        if jornada:
            print(f"   📋 Jornada: {jornada['nome_horario'] or 'Não definida'}")
            if jornada['entrada_manha'] and jornada['saida']:
                print(f"   🕐 Horários: {jornada['entrada_manha']} - {jornada['saida']}")
                print(f"   📊 Horas calculadas da jornada: {jornada['horas_jornada_calculada']}h")
                
                # ✅ AQUI PODE ESTAR O PROBLEMA!
                if jornada['horas_jornada_calculada'] and abs(float(jornada['horas_jornada_calculada']) - 48.0) < 0.1:
                    print(f"   🚨 PROBLEMA ENCONTRADO: Jornada calcula {jornada['horas_jornada_calculada']}h!")
                    print(f"   🚨 Isso pode estar sobrescrevendo as horas semanais!")
        
        print(f"\n3. 🔍 VERIFICAR EMPRESAS_CONFIG")
        # Verificar se há configuração de empresa que pode estar interferindo
        empresa_config = db.execute_query("""
            SELECT 
                ec.*,
                -- Calcular horas baseado na configuração da empresa
                CASE 
                    WHEN ec.jornada_segunda_entrada IS NOT NULL AND ec.jornada_segunda_saida IS NOT NULL THEN
                        TIMESTAMPDIFF(MINUTE, ec.jornada_segunda_entrada, ec.jornada_segunda_saida) / 60.0 - 1.0
                    ELSE NULL
                END as horas_empresa_calculada
            FROM funcionarios f
            LEFT JOIN empresas_config ec ON f.empresa_id = ec.empresa_id
            WHERE f.id = %s
        """, (funcionario_id,), fetch_one=True)
        
        if empresa_config and empresa_config['horas_empresa_calculada']:
            print(f"   🏢 Empresa config: {empresa_config['horas_empresa_calculada']}h calculadas")
            if abs(float(empresa_config['horas_empresa_calculada']) - 48.0) < 0.1:
                print(f"   🚨 PROBLEMA: Empresa config calcula 48h!")
        
        print(f"\n4. 🧪 SIMULAR EDIÇÃO VIA INTERFACE")
        # Simular exatamente o que acontece quando você edita via interface
        
        # Primeiro, vamos simular o carregamento para edição
        print("   📝 Simulando carregamento para edição...")
        from utils.database import FuncionarioQueries
        
        funcionario_dados = FuncionarioQueries.get_with_epis(funcionario_id)
        if funcionario_dados:
            print(f"   📊 Dados carregados: {funcionario_dados.get('horas_semanais_obrigatorias')}h")
        
        # Agora simular o salvamento
        print("   💾 Simulando salvamento de 44h...")
        
        # Usar a mesma lógica do app_funcionarios.py
        from app_funcionarios import _safe_decimal_with_comma_fix
        
        valor_form = "44.00"  # Simular valor do formulário
        valor_processado = _safe_decimal_with_comma_fix(valor_form)
        
        print(f"   🔧 Valor processado: {valor_processado}")
        
        # Salvar usando UPDATE direto (como faz a aplicação)
        db.execute_query("""
            UPDATE funcionarios 
            SET horas_semanais_obrigatorias = %s 
            WHERE id = %s
        """, (valor_processado, funcionario_id))
        
        # Verificar imediatamente após o salvamento
        verificacao_pos = db.execute_query("""
            SELECT horas_semanais_obrigatorias 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,), fetch_one=True)
        
        print(f"   ✅ Após salvamento: {verificacao_pos['horas_semanais_obrigatorias']}h")
        
        # Aguardar um pouco e verificar novamente (caso haja trigger assíncrono)
        import time
        time.sleep(2)
        
        verificacao_final = db.execute_query("""
            SELECT horas_semanais_obrigatorias 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,), fetch_one=True)
        
        print(f"   🔍 Após 2 segundos: {verificacao_final['horas_semanais_obrigatorias']}h")
        
        if verificacao_final['horas_semanais_obrigatorias'] != 44.00:
            print(f"   🚨 BUG REPRODUZIDO! Valor mudou de 44h para {verificacao_final['horas_semanais_obrigatorias']}h")
        else:
            print(f"   ✅ Valor mantido correto em 44h")
        
        print("\n" + "=" * 60)
        print("✅ TESTE CONCLUÍDO")
        
    except Exception as e:
        print(f"❌ ERRO GERAL NO TESTE: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    testar_bug_48h()
