/**
 * Cliente JavaScript Universal para Biometria - RLPONTO-WEB v2.0
 * ==============================================================
 * 
 * Sistema universal que detecta e utiliza qualquer leitor biométrico
 * com drivers instalados no Windows, removendo dependência de hardware específico.
 * 
 * Características:
 * ✓ Auto-detecção de dispositivos biométricos
 * ✓ Suporte a múltiplos fabricantes
 * ✓ Interface unificada independente de marca
 * ✓ Sistema de configuração de dispositivos
 * ✓ Zero referências a hardware específico
 * 
 * Autor: Richardson Rodrigues - AiNexus Tecnologia
 * Sistema: RLPONTO-WEB v1.0
 * Data: Junho 2025
 */

class UniversalBiometricClient {
    constructor() {
        // SEMPRE conecta ao serviço universal local
        this.serviceUrl = 'http://localhost:5001';
        this.isCapturing = false;
        this.callbacks = {};
        this.templates = {
            dedo1: null,
            dedo2: null
        };
        this.qualidades = {
            dedo1: 0,
            dedo2: 0
        };
        this.availableDevices = [];
        this.preferredDevice = null;
        
        console.log('🔐 Universal Biometric Client v2.0 iniciado');
        console.log('🌐 Serviço: http://localhost:5001');
        console.log('🎯 Modo: Universal (qualquer leitor biométrico)');
    }

    /**
     * Testa conexão com o serviço universal
     */
    async testarConexao() {
        try {
            const response = await this.makeRequest('/test', {
                method: 'GET',
                timeout: 5000
            });
            
            const data = await response.json();
            console.log('✅ Serviço Universal conectado:', data);
            
            return data.status === 'ok';
            
        } catch (error) {
            console.error('❌ Serviço Universal falhou:', error.message);
            
            if (error.message.includes('fetch')) {
                throw new Error(`❌ Serviço Biométrico Universal não está rodando!\n\n🔧 SOLUÇÕES:\n1. Execute: python universal_biometric_service.py\n2. Verifique se a porta 5001 está disponível\n3. Execute: curl http://localhost:5001/test\n4. Verifique logs em: logs/universal_biometric.log\n\n📍 O serviço deve rodar localmente para acessar os drivers de hardware!`);
            }
            
            throw new Error(`Serviço Universal: ${error.message}`);
        }
    }

    /**
     * Descobre todos os dispositivos biométricos disponíveis
     */
    async descobrirDispositivos() {
        try {
            console.log('🔍 Descobrindo dispositivos biométricos...');
            
            const response = await this.makeRequest('/devices/discover', {
                method: 'POST',
                body: JSON.stringify({})
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.availableDevices = data.devices;
                console.log(`✅ Descobertos ${data.count} dispositivos:`, data.devices);
                
                // Log detalhado dos dispositivos encontrados
                data.devices.forEach((device, index) => {
                    console.log(`  ${index + 1}. ${device.name} (${device.manufacturer}) - ${device.status}`);
                });
                
                return data.devices;
            } else {
                throw new Error(data.error || 'Falha na descoberta de dispositivos');
            }
            
        } catch (error) {
            console.error('Erro na descoberta:', error);
            throw error;
        }
    }

    /**
     * Registra um dispositivo para uso permanente
     */
    async registrarDispositivo(deviceId, deviceInfo, alias = null) {
        try {
            console.log(`📝 Registrando dispositivo: ${deviceInfo.name}`);
            
            const response = await this.makeRequest('/devices/register', {
                method: 'POST',
                body: JSON.stringify({
                    device_id: deviceId,
                    device_info: deviceInfo,
                    alias: alias
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                console.log('✅ Dispositivo registrado com sucesso');
                return true;
            } else {
                console.error('❌ Falha no registro:', data.error);
                return false;
            }
            
        } catch (error) {
            console.error('Erro no registro:', error);
            return false;
        }
    }

    /**
     * Obtém lista de dispositivos registrados
     */
    async obterDispositivosRegistrados() {
        try {
            const response = await this.makeRequest('/devices/registered', {
                method: 'GET'
            });
            
            const data = await response.json();
            return data.success ? data.devices : {};
            
        } catch (error) {
            console.error('Erro ao obter dispositivos registrados:', error);
            return {};
        }
    }

    /**
     * Captura impressão digital usando dispositivo disponível
     */
    async capturarDigital(deviceId = null) {
        if (this.isCapturing) {
            throw new Error('Captura já em andamento');
        }

        this.isCapturing = true;
        
        try {
            console.log('🔄 Iniciando captura biométrica universal...');
            
            // Se não especificou dispositivo, descobre automaticamente
            if (!deviceId && this.availableDevices.length === 0) {
                await this.descobrirDispositivos();
            }
            
            const response = await this.makeRequest('/capture', {
                method: 'POST',
                body: JSON.stringify({
                    device_id: deviceId
                })
            });
            
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'Falha na captura biométrica');
            }
            
            console.log('✅ Captura bem-sucedida:', {
                device: data.device_name,
                manufacturer: data.manufacturer,
                quality: data.quality
            });
            
            // Verifica qualidade mínima
            if (data.quality < 60) {
                console.warn('⚠️ Qualidade baixa detectada:', data.quality);
            }
            
            return data;
            
        } catch (error) {
            console.error('❌ Erro na captura:', error);
            throw error;
        } finally {
            this.isCapturing = false;
        }
    }

    /**
     * Realiza requisição HTTP para o serviço
     */
    async makeRequest(endpoint, options = {}) {
        const url = `${this.serviceUrl}${endpoint}`;
        console.log('🔄 Request:', url);
        
        try {
            const response = await fetch(url, {
                ...options,
                mode: 'cors',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });
            
            console.log('✅ Response:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`Serviço retornou ${response.status}: ${response.statusText}`);
            }
            
            return response;
            
        } catch (error) {
            console.error('❌ Request falhou:', error.message);
            throw error;
        }
    }

    /**
     * Salva template de impressão digital
     */
    salvarTemplate(numeroImpressao, template, qualidade) {
        const numeroStr = numeroImpressao.toString();
        this.templates[`dedo${numeroStr}`] = template;
        this.qualidades[`dedo${numeroStr}`] = qualidade;
        
        console.log(`💾 Template salvo para dedo ${numeroStr}:`, {
            qualidade: qualidade,
            template_length: template.length
        });
    }

    /**
     * Obtém templates salvos
     */
    getTemplates() {
        return {
            dedo1: this.templates.dedo1,
            dedo2: this.templates.dedo2
        };
    }

    /**
     * Limpa templates salvos
     */
    limparTemplates() {
        this.templates = { dedo1: null, dedo2: null };
        this.qualidades = { dedo1: 0, dedo2: 0 };
        console.log('🗑️ Templates limpos');
    }

    /**
     * Sistema de eventos
     */
    on(evento, callback) {
        if (!this.callbacks[evento]) {
            this.callbacks[evento] = [];
        }
        this.callbacks[evento].push(callback);
    }

    emit(evento, dados) {
        if (this.callbacks[evento]) {
            this.callbacks[evento].forEach(callback => {
                try {
                    callback(dados);
                } catch (error) {
                    console.error(`Erro no callback do evento ${evento}:`, error);
                }
            });
        }
    }

    /**
     * Diagnóstico do sistema biométrico
     */
    async diagnosticarSistema() {
        console.log('🔧 Iniciando diagnóstico do sistema biométrico...');
        
        const diagnostico = {
            servico_status: false,
            dispositivos_encontrados: 0,
            dispositivos_registrados: 0,
            wbf_disponivel: false,
            erros: [],
            recomendacoes: []
        };
        
        try {
            // Testa conexão com serviço
            await this.testarConexao();
            diagnostico.servico_status = true;
            
        } catch (error) {
            diagnostico.erros.push(`Serviço Universal: ${error.message}`);
            diagnostico.recomendacoes.push('Iniciar serviço universal_biometric_service.py');
        }
        
        try {
            // Testa descoberta de dispositivos
            const devices = await this.descobrirDispositivos();
            diagnostico.dispositivos_encontrados = devices.length;
            
            if (devices.length === 0) {
                diagnostico.erros.push('Nenhum dispositivo biométrico encontrado');
                diagnostico.recomendacoes.push('Verificar se leitor está conectado e drivers instalados');
            }
            
        } catch (error) {
            diagnostico.erros.push(`Descoberta de dispositivos: ${error.message}`);
        }
        
        try {
            // Verifica dispositivos registrados
            const registered = await this.obterDispositivosRegistrados();
            diagnostico.dispositivos_registrados = Object.keys(registered).length;
            
        } catch (error) {
            diagnostico.erros.push(`Dispositivos registrados: ${error.message}`);
        }
        
        // Log do diagnóstico
        console.log('📊 Diagnóstico completo:', diagnostico);
        
        return diagnostico;
    }

    /**
     * Obtém status completo do sistema
     */
    async obterStatus() {
        try {
            const response = await this.makeRequest('/status', {
                method: 'GET'
            });
            
            return await response.json();
            
        } catch (error) {
            console.error('Erro ao obter status:', error);
            return null;
        }
    }
}

class ModalBiometriaUniversal {
    /**
     * Modal visual para captura biométrica universal
     * Substitui completamente a interface específica anterior
     */
    constructor() {
        this.biometricClient = new UniversalBiometricClient();
        this.currentStep = 0;
        this.capturedFingerprints = [];
        
        console.log('🎨 Modal Biométrico Universal iniciado');
    }

    init() {
        // Inicialização do modal será feita aqui
        console.log('🚀 Inicializando interface universal...');
    }

    async abrir() {
        console.log('📱 Abrindo modal biométrico universal...');
        
        // Busca ou cria modal
        let modal = document.getElementById('modalBiometriaUniversal');
        if (!modal) {
            modal = this.criarModal();
            document.body.appendChild(modal);
        }
        
        // Reseta estado
        this.resetarEstados();
        
        // Descobre dispositivos disponíveis
        try {
            await this.descobrirEExibirDispositivos();
        } catch (error) {
            this.atualizarStatus('Erro ao descobrir dispositivos: ' + error.message, 'error');
        }
        
        // Exibe modal
        modal.style.display = 'block';
        modal.classList.add('show');
    }

    criarModal() {
        const modal = document.createElement('div');
        modal.id = 'modalBiometriaUniversal';
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-fingerprint"></i>
                            Cadastro Biométrico Universal
                        </h5>
                        <button type="button" class="btn-close" onclick="fecharModalBiometriaUniversal()"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Status do sistema -->
                        <div class="alert alert-info" id="statusSistema">
                            <i class="fas fa-info-circle"></i>
                            Inicializando sistema biométrico universal...
                        </div>
                        
                        <!-- Seleção de dispositivo -->
                        <div class="card mb-3" id="selecaoDispositivo">
                            <div class="card-header">
                                <h6><i class="fas fa-search"></i> Dispositivos Biométricos Detectados</h6>
                            </div>
                            <div class="card-body" id="listaDispositivos">
                                <div class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    Descobrindo dispositivos...
                                </div>
                            </div>
                        </div>
                        
                        <!-- Interface de captura -->
                        <div class="card" id="interfaceCaptura" style="display: none;">
                            <div class="card-header">
                                <h6><i class="fas fa-hand-paper"></i> Captura de Impressões Digitais</h6>
                            </div>
                            <div class="card-body text-center">
                                <div id="instrucoes">
                                    <p>Coloque o dedo no leitor biométrico e aguarde...</p>
                                </div>
                                
                                <div id="resultadoCaptura" class="mt-3">
                                    <!-- Resultados aparecerão aqui -->
                                </div>
                                
                                <div class="mt-3">
                                    <button class="btn btn-primary" id="btnCapturar" onclick="iniciarCapturaUniversal()">
                                        <i class="fas fa-fingerprint"></i>
                                        Capturar Impressão Digital
                                    </button>
                                    <button class="btn btn-secondary ms-2" onclick="diagnosticarSistemaUniversal()">
                                        <i class="fas fa-stethoscope"></i>
                                        Diagnóstico
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="fecharModalBiometriaUniversal()">
                            Cancelar
                        </button>
                        <button type="button" class="btn btn-success" id="btnSalvar" onclick="salvarBiometriaUniversal()" disabled>
                            <i class="fas fa-save"></i>
                            Salvar Biometria
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        return modal;
    }

    async descobrirEExibirDispositivos() {
        try {
            const devices = await this.biometricClient.descobrirDispositivos();
            const container = document.getElementById('listaDispositivos');
            
            if (devices.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Nenhum dispositivo biométrico encontrado.
                        <br><small>Verifique se o leitor está conectado e os drivers instalados.</small>
                    </div>
                `;
                return;
            }
            
            let html = '';
            devices.forEach((device, index) => {
                const statusIcon = this.getStatusIcon(device.status);
                const isSimulator = device.status === 'simulation';
                
                html += `
                    <div class="device-item card mb-2 ${index === 0 ? 'border-primary' : ''}" 
                         onclick="selecionarDispositivo('${device.id}')">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${device.name}</strong>
                                    <br><small class="text-muted">${device.manufacturer}</small>
                                </div>
                                <div class="text-end">
                                    ${statusIcon}
                                    <br><small class="${isSimulator ? 'text-warning' : 'text-success'}">${device.status}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
            // Auto-seleciona primeiro dispositivo
            if (devices.length > 0) {
                this.selecionarDispositivo(devices[0].id);
            }
            
        } catch (error) {
            document.getElementById('listaDispositivos').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i>
                    Erro na descoberta: ${error.message}
                </div>
            `;
        }
    }

    selecionarDispositivo(deviceId) {
        // Remove seleção anterior
        document.querySelectorAll('.device-item').forEach(item => {
            item.classList.remove('border-primary');
        });
        
        // Adiciona seleção atual
        event.target.closest('.device-item').classList.add('border-primary');
        
        // Armazena dispositivo selecionado
        this.selectedDeviceId = deviceId;
        
        // Exibe interface de captura
        document.getElementById('interfaceCaptura').style.display = 'block';
        
        console.log('✅ Dispositivo selecionado:', deviceId);
    }

    async iniciarCaptura() {
        if (!this.selectedDeviceId) {
            this.atualizarStatus('Selecione um dispositivo primeiro', 'warning');
            return;
        }
        
        const btnCapturar = document.getElementById('btnCapturar');
        btnCapturar.disabled = true;
        btnCapturar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Capturando...';
        
        try {
            this.atualizarStatus('Posicione o dedo no leitor e aguarde...', 'info');
            
            const result = await this.biometricClient.capturarDigital(this.selectedDeviceId);
            
            if (result.success) {
                this.processarCapturaComSucesso(result);
            } else {
                throw new Error(result.error);
            }
            
        } catch (error) {
            this.atualizarStatus('Erro na captura: ' + error.message, 'error');
            console.error('Erro na captura:', error);
        } finally {
            btnCapturar.disabled = false;
            btnCapturar.innerHTML = '<i class="fas fa-fingerprint"></i> Capturar Impressão Digital';
        }
    }

    processarCapturaComSucesso(result) {
        this.capturedFingerprints.push(result);
        
        const container = document.getElementById('resultadoCaptura');
        container.innerHTML = `
            <div class="alert alert-success">
                <h6><i class="fas fa-check-circle"></i> Captura Bem-Sucedida!</h6>
                <p><strong>Dispositivo:</strong> ${result.device_name}</p>
                <p><strong>Fabricante:</strong> ${result.manufacturer}</p>
                <p><strong>Qualidade:</strong> ${result.quality}%</p>
                <p><strong>Timestamp:</strong> ${new Date(result.timestamp).toLocaleString()}</p>
            </div>
        `;
        
        // Habilita botão salvar
        document.getElementById('btnSalvar').disabled = false;
        
        this.atualizarStatus('Impressão digital capturada com sucesso!', 'success');
        
        console.log('✅ Captura processada:', result);
    }

    getStatusIcon(status) {
        const icons = {
            'ready': '<i class="fas fa-check-circle text-success"></i>',
            'detected': '<i class="fas fa-eye text-info"></i>',
            'simulation': '<i class="fas fa-flask text-warning"></i>',
            'error': '<i class="fas fa-exclamation-circle text-danger"></i>'
        };
        
        return icons[status] || '<i class="fas fa-question-circle text-muted"></i>';
    }

    atualizarStatus(mensagem, tipo = 'info') {
        const statusElement = document.getElementById('statusSistema');
        const iconMap = {
            'info': 'fa-info-circle',
            'success': 'fa-check-circle',
            'warning': 'fa-exclamation-triangle',
            'error': 'fa-times-circle'
        };
        
        statusElement.className = `alert alert-${tipo}`;
        statusElement.innerHTML = `
            <i class="fas ${iconMap[tipo]}"></i>
            ${mensagem}
        `;
    }

    resetarEstados() {
        this.currentStep = 0;
        this.capturedFingerprints = [];
        this.selectedDeviceId = null;
        
        // Reset UI
        document.getElementById('interfaceCaptura').style.display = 'none';
        document.getElementById('btnSalvar').disabled = true;
    }

    fechar() {
        const modal = document.getElementById('modalBiometriaUniversal');
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('show');
        }
    }

    async diagnosticar() {
        this.atualizarStatus('Executando diagnóstico do sistema...', 'info');
        
        try {
            const diagnostico = await this.biometricClient.diagnosticarSistema();
            this.exibirResultadoDiagnostico(diagnostico);
        } catch (error) {
            this.atualizarStatus('Erro no diagnóstico: ' + error.message, 'error');
        }
    }

    exibirResultadoDiagnostico(diagnostico) {
        let html = '<div class="alert alert-info"><h6>📊 Diagnóstico do Sistema Biométrico</h6>';
        
        html += `<p><strong>Serviço:</strong> ${diagnostico.servico_status ? '✅ Online' : '❌ Offline'}</p>`;
        html += `<p><strong>Dispositivos Encontrados:</strong> ${diagnostico.dispositivos_encontrados}</p>`;
        html += `<p><strong>Dispositivos Registrados:</strong> ${diagnostico.dispositivos_registrados}</p>`;
        
        if (diagnostico.erros.length > 0) {
            html += '<h6>⚠️ Problemas Encontrados:</h6><ul>';
            diagnostico.erros.forEach(erro => {
                html += `<li>${erro}</li>`;
            });
            html += '</ul>';
        }
        
        if (diagnostico.recomendacoes.length > 0) {
            html += '<h6>💡 Recomendações:</h6><ul>';
            diagnostico.recomendacoes.forEach(rec => {
                html += `<li>${rec}</li>`;
            });
            html += '</ul>';
        }
        
        html += '</div>';
        
        document.getElementById('resultadoCaptura').innerHTML = html;
    }
}

// Instância global
const modalBiometriaUniversal = new ModalBiometriaUniversal();

// Funções globais para compatibilidade
function abrirModalBiometriaUniversal() {
    modalBiometriaUniversal.abrir();
}

function fecharModalBiometriaUniversal() {
    modalBiometriaUniversal.fechar();
}

function iniciarCapturaUniversal() {
    modalBiometriaUniversal.iniciarCaptura();
}

function selecionarDispositivo(deviceId) {
    modalBiometriaUniversal.selecionarDispositivo(deviceId);
}

function diagnosticarSistemaUniversal() {
    modalBiometriaUniversal.diagnosticar();
}

function salvarBiometriaUniversal() {
    // Implementar salvamento no backend
    console.log('💾 Salvando biometria...', modalBiometriaUniversal.capturedFingerprints);
    
    // Por enquanto, apenas fecha o modal
    fecharModalBiometriaUniversal();
}

// Log de inicialização
console.log('🎯 Universal Biometric Client v2.0 carregado');
console.log('📡 Pronto para detectar qualquer leitor biométrico');
console.log('🚫 Zero dependência de hardware específico'); 