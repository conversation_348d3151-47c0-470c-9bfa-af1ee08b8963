#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para executar limpeza e correção do banco de dados
ATENÇÃO: Este script remove TODOS os dados dos funcionários!
"""

import pymysql
import sys

def conectar_mysql():
    """Conecta com o servidor MySQL usando as credenciais do usuário criado"""
    try:
        connection = pymysql.connect(
            host='************',
            user='controle_user',
            password='controle123',
            database='controle_ponto',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        print("✓ Conectado ao MySQL servidor ************ como controle_user")
        return connection
        
    except pymysql.Error as e:
        print(f"✗ Erro ao conectar com MySQL: {e}")
        print("💡 Certifique-se de que executou o script de permissões primeiro!")
        return None

def verificar_coluna_existe(cursor, tabela, coluna):
    """Verifica se uma coluna existe na tabela"""
    try:
        cursor.execute(f"SHOW COLUMNS FROM {tabela} LIKE '{coluna}'")
        return cursor.fetchone() is not None
    except:
        return False

def executar_limpeza_banco(connection):
    """Executa a limpeza e correção da estrutura do banco"""
    
    print("⚠️  ATENÇÃO: Este processo vai REMOVER TODOS os dados dos funcionários!")
    confirmacao = input("Digite 'CONFIRMO' para prosseguir: ")
    
    if confirmacao != 'CONFIRMO':
        print("❌ Operação cancelada pelo usuário.")
        return False
    
    try:
        with connection.cursor() as cursor:
            print("[1/15] Desabilitando verificação de chaves estrangeiras...")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0;")
            print("✓ Comando executado com sucesso")
            
            print("[2/15] Removendo tabela epis...")
            cursor.execute("DROP TABLE IF EXISTS epis;")
            print("✓ Comando executado com sucesso")
            
            print("[3/15] Removendo tabela registros_ponto...")
            cursor.execute("DROP TABLE IF EXISTS registros_ponto;")
            print("✓ Comando executado com sucesso")
            
            print("[4/15] Removendo dados dos funcionários...")
            cursor.execute("DELETE FROM funcionarios;")
            print("✓ Comando executado com sucesso")
            
            # Remover colunas problemáticas (verificando se existem primeiro)
            colunas_remover = ['jornada_entrada', 'jornada_saida', 'assinatura']
            
            for i, coluna in enumerate(colunas_remover, 5):
                print(f"[{i}/15] Verificando e removendo coluna '{coluna}'...")
                if verificar_coluna_existe(cursor, 'funcionarios', coluna):
                    cursor.execute(f"ALTER TABLE funcionarios DROP COLUMN {coluna};")
                    print(f"✓ Coluna '{coluna}' removida com sucesso")
                else:
                    print(f"✓ Coluna '{coluna}' não existe (já foi removida)")
            
            print("[8/15] Adicionando colunas de jornada...")
            
            # Verificar e adicionar colunas uma por uma
            colunas_adicionar = [
                'jornada_seg_qui_entrada',
                'jornada_seg_qui_saida', 
                'jornada_sex_entrada',
                'jornada_sex_saida',
                'jornada_intervalo_entrada',
                'jornada_intervalo_saida'
            ]
            
            for coluna in colunas_adicionar:
                if not verificar_coluna_existe(cursor, 'funcionarios', coluna):
                    cursor.execute(f"ALTER TABLE funcionarios ADD COLUMN {coluna} TIME DEFAULT NULL;")
                    print(f"✓ Coluna '{coluna}' adicionada com sucesso")
                else:
                    print(f"✓ Coluna '{coluna}' já existe")
            
            print("[9/15] Recriando tabela registros_ponto...")
            cursor.execute("""CREATE TABLE registros_ponto (
                 id int NOT NULL AUTO_INCREMENT,
                 funcionario_id int DEFAULT NULL,
                 data_hora datetime DEFAULT CURRENT_TIMESTAMP,
                 sincronizado tinyint DEFAULT 0,
                 PRIMARY KEY (id),
                 FOREIGN KEY (funcionario_id) REFERENCES funcionarios (id) ON DELETE CASCADE
               ) ENGINE = INNODB,
               CHARACTER SET utf8mb4,
               COLLATE utf8mb4_0900_ai_ci;""")
            print("✓ Comando executado com sucesso")
            
            print("[10/15] Recriando tabela epis...")
            cursor.execute("""CREATE TABLE epis (
                 id int NOT NULL AUTO_INCREMENT,
                 funcionario_id int NOT NULL,
                 epi_nome varchar(255) NOT NULL,
                 epi_ca varchar(50) DEFAULT NULL,
                 epi_data_entrega date DEFAULT NULL,
                 epi_data_validade date DEFAULT NULL,
                 epi_observacoes text DEFAULT NULL,
                 PRIMARY KEY (id),
                 FOREIGN KEY (funcionario_id) REFERENCES funcionarios (id) ON DELETE CASCADE
               ) ENGINE = INNODB,
               CHARACTER SET utf8mb4,
               COLLATE utf8mb4_0900_ai_ci;""")
            print("✓ Comando executado com sucesso")
            
            print("[11/15] Reabilitando verificação de chaves estrangeiras...")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1;")
            print("✓ Comando executado com sucesso")
            
            print("[12/15] Resetando auto_increment da tabela funcionarios...")
            cursor.execute("ALTER TABLE funcionarios AUTO_INCREMENT = 1;")
            print("✓ Comando executado com sucesso")
            
            print("[13/15] Resetando auto_increment da tabela registros_ponto...")
            cursor.execute("ALTER TABLE registros_ponto AUTO_INCREMENT = 1;")
            print("✓ Comando executado com sucesso")
            
            print("[14/15] Resetando auto_increment da tabela epis...")
            cursor.execute("ALTER TABLE epis AUTO_INCREMENT = 1;")
            print("✓ Comando executado com sucesso")
                
        connection.commit()
        print("\n✓ Limpeza e correção do banco concluída com sucesso!")
        
        # Verificar estrutura final
        print("\n📋 Verificando estrutura das tabelas:")
        
        tabelas = ['funcionarios', 'epis', 'registros_ponto']
        for tabela in tabelas:
            with connection.cursor() as cursor:
                cursor.execute(f"DESCRIBE {tabela}")
                colunas = cursor.fetchall()
                print(f"\n🔧 Tabela '{tabela}':")
                for coluna in colunas:
                    print(f"   - {coluna['Field']} ({coluna['Type']})")
                
                # Contar registros
                cursor.execute(f"SELECT COUNT(*) as total FROM {tabela}")
                resultado = cursor.fetchone()
                print(f"   📊 Total de registros: {resultado['total']}")
                
        return True
        
    except pymysql.Error as e:
        print(f"✗ Erro ao executar limpeza: {e}")
        return False

def main():
    """Função principal"""
    print("🧹 Limpeza e correção do banco de dados - Controle de Ponto")
    print("=" * 60)
    
    # Conectar ao MySQL
    connection = conectar_mysql()
    if not connection:
        print("❌ Falha na conexão. Execute primeiro o script de permissões.")
        return False
    
    try:
        # Executar limpeza
        sucesso = executar_limpeza_banco(connection)
        
        if sucesso:
            print("\n🎉 Limpeza e correção concluída com sucesso!")
            print("\n✅ O sistema está pronto para uso!")
            print("\n🔄 Próximos passos:")
            print("   1. Testar a aplicação Python")
            print("   2. Cadastrar novos funcionários")
            print("   3. Verificar funcionalidades de EPI")
            return True
        else:
            print("\n❌ Falha na limpeza e correção.")
            return False
            
    finally:
        connection.close()
        print("\n🔌 Conexão MySQL fechada.")

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1) 