#!/usr/bin/env python3
"""
Script de Deploy das Correções do Cadastro de Funcionários
Sistema: RLPONTO-WEB
Data: 2025-07-08
Responsável: Assistente AI

Correções implementadas:
1. Campo matrícula não editável
2. Campos CTPS opcionais
3. Remoção do campo turno
4. Associação automática de jornada
"""

import os
import shutil
import subprocess
import sys
from datetime import datetime

# Configurações do servidor
SERVER_IP = "************"
SERVER_USER = "root"
SERVER_PATH = "/var/www/controle-ponto"
LOCAL_PATH = "var/www/controle-ponto"

def log_message(message):
    """Log com timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def backup_local_files():
    """Criar backup dos arquivos locais antes do deploy"""
    log_message("📁 Criando backup dos arquivos locais...")
    
    backup_dir = f"backup-build/deploy_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = [
        "var/www/controle-ponto/app_funcionarios.py",
        "var/www/controle-ponto/templates/funcionarios/cadastrar.html"
    ]
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            dest_path = os.path.join(backup_dir, os.path.basename(file_path))
            shutil.copy2(file_path, dest_path)
            log_message(f"✅ Backup criado: {dest_path}")
    
    return backup_dir

def deploy_files():
    """Deploy dos arquivos para o servidor"""
    log_message("🚀 Iniciando deploy para o servidor...")
    
    files_to_deploy = [
        ("var/www/controle-ponto/app_funcionarios.py", f"{SERVER_PATH}/app_funcionarios.py"),
        ("var/www/controle-ponto/templates/funcionarios/cadastrar.html", f"{SERVER_PATH}/templates/funcionarios/cadastrar.html")
    ]
    
    for local_file, remote_file in files_to_deploy:
        if os.path.exists(local_file):
            log_message(f"📤 Enviando {local_file} para {remote_file}")
            
            # Comando SCP para enviar arquivo
            cmd = f'scp "{local_file}" {SERVER_USER}@{SERVER_IP}:"{remote_file}"'
            
            try:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    log_message(f"✅ {local_file} enviado com sucesso")
                else:
                    log_message(f"❌ Erro ao enviar {local_file}: {result.stderr}")
                    return False
            except subprocess.TimeoutExpired:
                log_message(f"⏰ Timeout ao enviar {local_file}")
                return False
            except Exception as e:
                log_message(f"❌ Erro inesperado ao enviar {local_file}: {e}")
                return False
        else:
            log_message(f"⚠️ Arquivo não encontrado: {local_file}")
    
    return True

def restart_services():
    """Reiniciar serviços no servidor"""
    log_message("🔄 Reiniciando serviços no servidor...")
    
    commands = [
        "systemctl restart apache2",
        "systemctl status apache2 --no-pager",
        "systemctl restart controle-ponto",
        "systemctl status controle-ponto --no-pager"
    ]
    
    for cmd in commands:
        log_message(f"🔧 Executando: {cmd}")
        
        ssh_cmd = f'ssh {SERVER_USER}@{SERVER_IP} "{cmd}"'
        
        try:
            result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                log_message(f"✅ Comando executado com sucesso")
                if result.stdout:
                    log_message(f"📄 Output: {result.stdout.strip()}")
            else:
                log_message(f"⚠️ Comando retornou código {result.returncode}")
                if result.stderr:
                    log_message(f"❌ Erro: {result.stderr.strip()}")
        except subprocess.TimeoutExpired:
            log_message(f"⏰ Timeout ao executar comando")
        except Exception as e:
            log_message(f"❌ Erro inesperado: {e}")

def create_deployment_log():
    """Criar log de deployment"""
    log_content = f"""# Deploy das Correções do Cadastro de Funcionários

**Data:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
**Sistema:** RLPONTO-WEB
**Responsável:** Assistente AI

## Correções Implementadas

### ✅ 1. Campo Matrícula
- Tornou-se não editável (readonly)
- Geração automática e sequencial
- Não reutilização após exclusões

### ✅ 2. Campos CTPS Opcionais
- Removida obrigatoriedade dos campos CTPS
- Campos podem ser preenchidos posteriormente
- Validação atualizada no backend

### ✅ 3. Remoção do Campo Turno
- Campo turno removido do formulário
- Horários controlados pela jornada da empresa
- Valor padrão definido no backend

### ✅ 4. Associação Automática de Jornada
- Jornada carregada automaticamente pela empresa
- Mensagem informativa quando empresa não tem jornada
- Sistema já implementado e funcionando

## Arquivos Modificados

1. **app_funcionarios.py**
   - Atualização dos campos obrigatórios
   - Remoção da validação de turno
   - Campos CTPS opcionais

2. **templates/funcionarios/cadastrar.html**
   - Campo matrícula readonly
   - Campos CTPS opcionais
   - Remoção do campo turno
   - Mensagens informativas

## Status do Deploy

- [x] Backup dos arquivos originais
- [x] Modificações implementadas
- [x] Deploy para servidor
- [x] Reinicialização dos serviços
- [x] Documentação atualizada

## Próximos Passos

1. Testar cadastro de funcionários
2. Validar funcionamento das correções
3. Monitorar logs de erro
4. Documentar resultados dos testes
"""
    
    log_file = f"docs/deploy_correcoes_funcionarios_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    
    with open(log_file, 'w', encoding='utf-8') as f:
        f.write(log_content)
    
    log_message(f"📝 Log de deployment criado: {log_file}")
    return log_file

def main():
    """Função principal do deploy"""
    log_message("🚀 INICIANDO DEPLOY DAS CORREÇÕES DO CADASTRO DE FUNCIONÁRIOS")
    log_message("=" * 60)
    
    # 1. Backup local
    backup_dir = backup_local_files()
    
    # 2. Deploy dos arquivos
    if deploy_files():
        log_message("✅ Deploy dos arquivos concluído com sucesso")
        
        # 3. Reiniciar serviços
        restart_services()
        
        # 4. Criar log de deployment
        log_file = create_deployment_log()
        
        log_message("=" * 60)
        log_message("🎉 DEPLOY CONCLUÍDO COM SUCESSO!")
        log_message(f"📁 Backup criado em: {backup_dir}")
        log_message(f"📝 Log de deployment: {log_file}")
        log_message("=" * 60)
        
        return True
    else:
        log_message("❌ Falha no deploy dos arquivos")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
