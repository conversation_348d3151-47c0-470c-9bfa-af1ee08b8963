#!/bin/bash
# Script para aplicar correção de permissões das fotos no servidor remoto
# Sistema: RLPONTO-WEB v1.0
# Data: 08/06/2025
# Autor: <PERSON> / AiNexus Tecnologia

# Configurações
SERVIDOR="************"
USUARIO="root"
PROJETO_PATH="/var/www/controle-ponto"

echo "🔧 APLICAÇÃO DE CORREÇÃO - PERMISSÕES DAS FOTOS"
echo "==============================================="
echo "Servidor: $SERVIDOR"
echo "Projeto: $PROJETO_PATH"
echo ""

# Função para executar comandos remotos
executar_remoto() {
    local comando="$1"
    local descricao="$2"
    
    echo "📡 $descricao..."
    ssh $USUARIO@$SERVIDOR "$comando"
    
    if [ $? -eq 0 ]; then
        echo "✅ $descricao - SUCESSO"
    else
        echo "❌ $descricao - ERRO"
        return 1
    fi
    echo ""
}

# Função para copiar arquivo para o servidor
copiar_arquivo() {
    local arquivo_local="$1"
    local arquivo_remoto="$2"
    local descricao="$3"
    
    echo "📤 $descricao..."
    scp "$arquivo_local" $USUARIO@$SERVIDOR:"$arquivo_remoto"
    
    if [ $? -eq 0 ]; then
        echo "✅ $descricao - SUCESSO"
    else
        echo "❌ $descricao - ERRO"
        return 1
    fi
    echo ""
}

# Verificar conexão
echo "🔍 Verificando conexão com o servidor..."
ssh -o ConnectTimeout=10 $USUARIO@$SERVIDOR "echo 'Conexão OK'" >/dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "❌ Erro de conexão com o servidor $SERVIDOR"
    echo "Verifique:"
    echo "- Se o servidor está ligado"
    echo "- Se as credenciais SSH estão corretas"
    echo "- Se a rede está funcionando"
    exit 1
fi

echo "✅ Conexão com o servidor estabelecida"
echo ""

# Etapa 1: Copiar o script de correção
echo "📋 ETAPA 1: Copiando script de correção..."
copiar_arquivo "corrigir_permissoes_fotos.py" "$PROJETO_PATH/corrigir_permissoes_fotos.py" "Copiando script Python"

# Etapa 2: Dar permissão de execução
executar_remoto "chmod +x $PROJETO_PATH/corrigir_permissoes_fotos.py" "Dando permissão de execução"

# Etapa 3: Verificar estrutura atual
echo "📋 ETAPA 2: Verificando estrutura atual..."
executar_remoto "ls -la $PROJETO_PATH/static/ 2>/dev/null || echo 'Diretório static não existe'" "Listando diretório static"

# Etapa 4: Executar script de correção
echo "📋 ETAPA 3: Executando correção de permissões..."
executar_remoto "cd $PROJETO_PATH && python3 corrigir_permissoes_fotos.py" "Executando script de correção"

# Etapa 5: Verificar resultado
echo "📋 ETAPA 4: Verificando resultado..."
executar_remoto "ls -la $PROJETO_PATH/static/fotos_funcionarios/" "Verificando diretório de fotos"

# Etapa 6: Testar escrita
echo "📋 ETAPA 5: Testando escrita no diretório..."
executar_remoto "echo 'teste' > $PROJETO_PATH/static/fotos_funcionarios/teste_escrita.txt && rm $PROJETO_PATH/static/fotos_funcionarios/teste_escrita.txt" "Testando escrita"

# Etapa 7: Reiniciar Apache
echo "📋 ETAPA 6: Reiniciando serviços..."
executar_remoto "systemctl restart apache2" "Reiniciando Apache"
executar_remoto "systemctl status apache2 --no-pager -l" "Verificando status do Apache"

# Etapa 8: Limpeza
echo "📋 ETAPA 7: Limpeza..."
executar_remoto "rm -f $PROJETO_PATH/corrigir_permissoes_fotos.py" "Removendo script temporário"

echo ""
echo "🎯 CORREÇÃO CONCLUÍDA!"
echo "======================"
echo ""
echo "✅ Próximos passos:"
echo "1. Acesse o sistema: http://$SERVIDOR"
echo "2. Vá em Funcionários > Editar um funcionário"
echo "3. Teste o upload de uma foto"
echo "4. Verifique se a foto aparece corretamente"
echo ""
echo "📞 Em caso de problemas:"
echo "- Verifique os logs: tail -f /var/log/apache2/error.log"
echo "- Contate o suporte técnico"
echo ""
echo "📊 RELATÓRIO FINAL:"
echo "- Script executado com sucesso"
echo "- Permissões corrigidas"
echo "- Apache reiniciado"
echo "- Sistema pronto para uso" 