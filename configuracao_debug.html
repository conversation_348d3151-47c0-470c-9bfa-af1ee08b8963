<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurar <PERSON>ários - Controle <PERSON></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/static/css/relatorios.css" rel="stylesheet">
    
    <link rel="stylesheet" href="/static/style-cadastrar.css">
    
<style>
    .config-container {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .config-header {
        border-bottom: 2px solid #4fbdba;
        padding-bottom: 15px;
        margin-bottom: 25px;
    }
    
    .config-header h2 {
        color: #495057;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
    }
    
    .section {
        margin-bottom: 40px;
    }
    
    .section-title {
        color: #495057;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .form-container {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin-bottom: 30px;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 5px;
        font-size: 14px;
    }
    
    .form-group input,
    .form-group select {
        padding: 10px 12px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        background: white;
    }
    
    .form-group input:focus,
    .form-group select:focus {
        outline: none;
        border-color: #4fbdba;
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    }
    
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-primary {
        background: #4fbdba;
        color: white;
    }
    
    .btn-primary:hover {
        background: #3da8a6;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 189, 186, 0.3);
    }
    
    .btn-warning {
        background: #ffc107;
        color: #1a2634;
    }
    
    .btn-warning:hover {
        background: #e0a800;
    }
    
    .btn-danger {
        background: #dc3545;
        color: white;
    }
    
    .btn-danger:hover {
        background: #b52a37;
    }
    
    .users-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .users-table table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .users-table th,
    .users-table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }
    
    .users-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
        font-size: 14px;
    }
    
    .users-table tbody tr:hover {
        background: #f8f9fa;
    }
    
    .user-actions {
        display: flex;
        gap: 8px;
        align-items: center;
    }
    
    .level-select {
        background: white;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 5px 8px;
        font-size: 12px;
        min-width: 100px;
    }
    
    .level-select:focus {
        outline: none;
        border-color: #4fbdba;
    }
    
    .admin-badge {
        background: #dc3545;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    /* Modal */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .modal-content {
        background: white;
        margin: 10% auto;
        padding: 30px;
        border-radius: 12px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }
    
    .modal-header {
        border-bottom: 2px solid #4fbdba;
        padding-bottom: 15px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-header h3 {
        margin: 0;
        color: #495057;
        font-size: 20px;
    }
    
    .close {
        color: #6c757d;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        border: none;
        background: none;
    }
    
    .close:hover {
        color: #495057;
    }
    
    .modal-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }
    
    @media (max-width: 768px) {
        .form-grid {
            grid-template-columns: 1fr;
        }
        
        .user-actions {
            flex-direction: column;
            gap: 5px;
        }
        
        .config-container {
            padding: 20px;
        }
    }
</style>

    <style>
        /* 🎨 MODERN SIDEBAR DESIGN - Inspirado no MCP @21st-dev/magic */
        
        :root {
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 70px;
            --primary-color: #4fbdba;
            --sidebar-bg: #ffffff;
            --sidebar-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --sidebar-border: #e5e7eb;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --hover-bg: #f3f4f6;
            --active-bg: #e0f2f1;
            --active-text: #00695c;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        [data-theme="dark"] {
            --sidebar-bg: #1e293b;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --hover-bg: #334155;
            --active-bg: #0f172a;
            --sidebar-border: #334155;
        }

        /* Reset básico */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9fafb;
            color: var(--text-primary);
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        /* 🎯 SIDEBAR MODERNA */
        .modern-sidebar {
            width: var(--sidebar-width);
            background: var(--sidebar-bg);
            border-right: 1px solid var(--sidebar-border);
            box-shadow: var(--sidebar-shadow);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: var(--transition);
            display: flex;
            flex-direction: column;
        }

        .modern-sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        /* Header da Sidebar */
        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid var(--sidebar-border);
            display: flex;
            align-items: center;
            gap: 12px;
            min-height: 80px;
        }

        .sidebar-logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), #26a69a);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            transition: var(--transition);
        }

        .collapsed .sidebar-title {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        /* Navigation */
        .sidebar-nav {
            flex: 1;
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 32px;
        }

        .nav-section-title {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0 20px 12px;
            transition: var(--transition);
        }

        .collapsed .nav-section-title {
            opacity: 0;
            height: 0;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        .nav-items {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            margin-bottom: 4px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px 20px;
            color: var(--text-primary);
            text-decoration: none;
            border-radius: 0 24px 24px 0;
            margin-right: 20px;
            position: relative;
            transition: var(--transition);
            font-weight: 500;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
        }

        .nav-link:hover {
            background: var(--hover-bg);
            color: var(--text-primary);
            transform: translateX(4px);
        }

        .nav-link.active {
            background: var(--active-bg);
            color: var(--active-text);
            box-shadow: 0 2px 8px rgba(79, 189, 186, 0.2);
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-color);
            border-radius: 0 4px 4px 0;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            font-size: 18px;
        }

        .nav-text {
            font-size: 14px;
            transition: var(--transition);
        }

        .collapsed .nav-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        /* Sidebar Footer */
        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid var(--sidebar-border);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 12px;
            background: var(--hover-bg);
            transition: var(--transition);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #26a69a);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            flex-shrink: 0;
        }

        .user-info {
            transition: var(--transition);
        }

        .collapsed .user-info {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .user-role {
            font-size: 12px;
            color: var(--text-secondary);
            margin: 0;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            transition: var(--transition);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .collapsed ~ .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        .content-header {
            background: white;
            border-bottom: 1px solid var(--sidebar-border);
            padding: 16px 32px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: var(--transition);
        }

        .sidebar-toggle:hover {
            background: var(--hover-bg);
            color: var(--text-primary);
        }

        .content-body {
            flex: 1;
            padding: 32px;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .modern-sidebar {
                transform: translateX(-100%);
                width: var(--sidebar-width);
            }
            
            .modern-sidebar.mobile-open {
                transform: translateX(0);
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
                opacity: 0;
                visibility: hidden;
                transition: var(--transition);
            }
            
            .sidebar-overlay.show {
                opacity: 1;
                visibility: visible;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .collapsed ~ .main-content {
                margin-left: 0;
            }
            
            .content-header {
                padding: 16px 20px;
            }
            
            .content-body {
                padding: 20px;
            }
        }

        /* Estilos existentes mantidos para compatibilidade */
        .breadcrumbs {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .breadcrumbs a {
            color: #4fbdba;
            text-decoration: none;
        }
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        .breadcrumbs span {
            color: #6c757d;
            margin: 0 5px;
        }
        
        /* Flash messages */
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            font-weight: 500;
            border: 1px solid transparent;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .flash-warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }
        .flash-info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }

        /* Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .nav-link:hover .nav-icon {
            animation: slideIn 0.3s ease;
        }

        /* Badge para notificações */
        .nav-badge {
            background: #ef4444;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
            position: absolute;
            top: 8px;
            right: 16px;
        }

        /* Scroll customizado */
        .modern-sidebar::-webkit-scrollbar {
            width: 4px;
        }
        
        .modern-sidebar::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .modern-sidebar::-webkit-scrollbar-thumb {
            background: var(--sidebar-border);
            border-radius: 4px;
        }
        
        .modern-sidebar::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        /* Compatibility with existing styles */
        .table-responsive, .data-table, .action-buttons, .btn-sm, .btn-view, .btn-edit, .btn-delete,
        .pagination, .badge, .filters, .filters-row, .filter-group {
            /* Mantém estilos existentes para tabelas e outros componentes */
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 🎨 SIDEBAR MODERNA -->
        <aside class="modern-sidebar" id="sidebar">
            <!-- Header da Sidebar -->
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-fingerprint"></i>
                </div>
                <div class="sidebar-title">
                    <div>RLPONTO-WEB</div>
                    <div style="font-size: 10px; color: var(--text-secondary); font-weight: 400;">v1.0</div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="sidebar-nav">
                <!-- Menu Principal -->
                <div class="nav-section">
                    <div class="nav-section-title">Principal</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/funcionarios'">
                                <div class="nav-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <span class="nav-text">Funcionários</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/funcionarios/cadastrar'">
                                <div class="nav-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <span class="nav-text">Novo Funcionário</span>
                            </button>
                        </li>
                    </ul>
                </div>

                <!-- Registro de Ponto -->
                <div class="nav-section">
                    <div class="nav-section-title">Ponto</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/registro-ponto/biometrico'">
                                <div class="nav-icon">
                                    <i class="fas fa-fingerprint"></i>
                                </div>
                                <span class="nav-text">Ponto Biométrico</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/registro-ponto/manual'">
                                <div class="nav-icon">
                                    <i class="fas fa-edit"></i>
                                </div>
                                <span class="nav-text">Ponto Manual</span>
                            </button>
                        </li>
                    </ul>
                </div>

                <!-- Relatórios -->
                <div class="nav-section">
                    <div class="nav-section-title">Relatórios</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/relatorios/pontos'">
                                <div class="nav-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <span class="nav-text">Relatórios de Ponto</span>
                            </button>
                        </li>
                                                 <li class="nav-item">
                             <button class="nav-link" onclick="window.location.href='/relatorios/estatisticas'">
                                 <div class="nav-icon">
                                     <i class="fas fa-chart-bar"></i>
                                 </div>
                                 <span class="nav-text">Estatísticas</span>
                             </button>
                         </li>
                    </ul>
                </div>

                <!-- Configurações (apenas admin) -->
                
                <div class="nav-section">
                    <div class="nav-section-title">Administração</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/empresa-principal'">
                                <div class="nav-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <span class="nav-text">Empresa Principal</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/configuracoes'">
                                <div class="nav-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <span class="nav-text">Configurações</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" onclick="window.location.href='/configurar_usuarios'">
                                <div class="nav-icon">
                                    <i class="fas fa-users-cog"></i>
                                </div>
                                <span class="nav-text">Gerenciar Usuários</span>
                            </button>
                        </li>
                    </ul>
                </div>
                
            </nav>

            <!-- Footer da Sidebar -->
            <div class="sidebar-footer">
                <div class="user-profile">
                    <div class="user-avatar">
                        A
                    </div>
                    <div class="user-info">
                        <p class="user-name">admin</p>
                        <p class="user-role">Admin</p>
                    </div>
                </div>
                <button class="nav-link" onclick="window.location.href='/logout'" style="margin-top: 12px; color: #ef4444;">
                    <div class="nav-icon">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <span class="nav-text">Sair</span>
                </button>
            </div>
        </aside>

        <!-- Overlay para mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Conteúdo Principal -->
        <main class="main-content">
            <!-- Header do Conteúdo -->
            <header class="content-header">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div style="display: flex; align-items: center; gap: 16px;">
                    <div style="font-size: 18px; font-weight: 600;">
                        Sistema de Controle de Ponto
                    </div>
                </div>
            </header>

            <!-- Corpo do Conteúdo -->
            <div class="content-body">
                <!-- Breadcrumbs -->
                

                <!-- Mensagens Flash -->
                
                    
                

                <!-- Conteúdo principal -->
                
<div class="config-container">
    <div class="config-header">
        <h2>⚙️ Configurar Usuários</h2>
    </div>
    
    <!-- Adicionar Usuário -->
    <div class="section">
        <h3 class="section-title">
            👤 Adicionar Novo Usuário
        </h3>
        
        <div class="form-container">
            <form id="adicionar-usuario-form" method="POST" action="/adicionar_usuario">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="usuario">Nome de Usuário</label>
                        <input type="text" id="usuario" name="usuario" required placeholder="Digite o nome de usuário">
                    </div>
                    
                    <div class="form-group">
                        <label for="senha">Senha</label>
                        <input type="password" id="senha" name="senha" required placeholder="Mínimo 8 caracteres">
                    </div>
                    
                    <div class="form-group">
                        <label for="nivel_acesso">Nível de Acesso</label>
                        <select id="nivel_acesso" name="nivel_acesso" required>
                            <option value="usuario">Usuário</option>
                            <option value="admin">Administrador</option>
                        </select>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    ✅ Adicionar Usuário
                </button>
            </form>
        </div>
    </div>
    
    <!-- Lista de Usuários -->
    <div class="section">
        <h3 class="section-title">
            👥 Usuários Cadastrados
        </h3>
        
        <div class="users-table">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Usuário</th>
                        <th>Nível de Acesso</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    
                    <tr>
                        <td>2</td>
                        <td>
                            <strong>teste</strong>
                            
                        </td>
                        <td>
                            
                                <select class="level-select" 
                                        onchange="alterarNivel(2, this.value)"
                                        data-user-id="2">
                                    <option value="admin" >Administrador</option>
                                    <option value="usuario" selected>Usuário</option>
                                </select>
                            
                        </td>
                        <td>
                            <div class="user-actions">
                                <button class="btn btn-warning btn-sm" 
                                        onclick="abrirModalSenha(2)" 
                                        type="button">
                                    🔐 Trocar Senha
                                </button>
                                
                                
                                <button class="btn btn-danger btn-sm" 
                                        onclick="confirmarExclusaoUsuario(2, "teste")" 
                                        type="button">
                                    🗑️ Excluir
                                </button>
                                
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td>3</td>
                        <td>
                            <strong>status</strong>
                            
                        </td>
                        <td>
                            
                                <select class="level-select" 
                                        onchange="alterarNivel(3, this.value)"
                                        data-user-id="3">
                                    <option value="admin" >Administrador</option>
                                    <option value="usuario" selected>Usuário</option>
                                </select>
                            
                        </td>
                        <td>
                            <div class="user-actions">
                                <button class="btn btn-warning btn-sm" 
                                        onclick="abrirModalSenha(3)" 
                                        type="button">
                                    🔐 Trocar Senha
                                </button>
                                
                                
                                <button class="btn btn-danger btn-sm" 
                                        onclick="confirmarExclusaoUsuario(3, "status")" 
                                        type="button">
                                    🗑️ Excluir
                                </button>
                                
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td>5</td>
                        <td>
                            <strong>cavalcrod</strong>
                            
                        </td>
                        <td>
                            
                                <select class="level-select" 
                                        onchange="alterarNivel(5, this.value)"
                                        data-user-id="5">
                                    <option value="admin" >Administrador</option>
                                    <option value="usuario" selected>Usuário</option>
                                </select>
                            
                        </td>
                        <td>
                            <div class="user-actions">
                                <button class="btn btn-warning btn-sm" 
                                        onclick="abrirModalSenha(5)" 
                                        type="button">
                                    🔐 Trocar Senha
                                </button>
                                
                                
                                <button class="btn btn-danger btn-sm" 
                                        onclick="confirmarExclusaoUsuario(5, "cavalcrod")" 
                                        type="button">
                                    🗑️ Excluir
                                </button>
                                
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td>6</td>
                        <td>
                            <strong> cavalcrod </strong>
                            
                        </td>
                        <td>
                            
                                <select class="level-select" 
                                        onchange="alterarNivel(6, this.value)"
                                        data-user-id="6">
                                    <option value="admin" >Administrador</option>
                                    <option value="usuario" selected>Usuário</option>
                                </select>
                            
                        </td>
                        <td>
                            <div class="user-actions">
                                <button class="btn btn-warning btn-sm" 
                                        onclick="abrirModalSenha(6)" 
                                        type="button">
                                    🔐 Trocar Senha
                                </button>
                                
                                
                                <button class="btn btn-danger btn-sm" 
                                        onclick="confirmarExclusaoUsuario(6, " cavalcrod ")" 
                                        type="button">
                                    🗑️ Excluir
                                </button>
                                
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td>7</td>
                        <td>
                            <strong>suelen</strong>
                            
                        </td>
                        <td>
                            
                                <select class="level-select" 
                                        onchange="alterarNivel(7, this.value)"
                                        data-user-id="7">
                                    <option value="admin" >Administrador</option>
                                    <option value="usuario" selected>Usuário</option>
                                </select>
                            
                        </td>
                        <td>
                            <div class="user-actions">
                                <button class="btn btn-warning btn-sm" 
                                        onclick="abrirModalSenha(7)" 
                                        type="button">
                                    🔐 Trocar Senha
                                </button>
                                
                                
                                <button class="btn btn-danger btn-sm" 
                                        onclick="confirmarExclusaoUsuario(7, "suelen")" 
                                        type="button">
                                    🗑️ Excluir
                                </button>
                                
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td>9</td>
                        <td>
                            <strong>admin</strong>
                            
                                <span class="admin-badge">Admin Padrão</span>
                            
                        </td>
                        <td>
                            
                                <span class="badge badge-danger">Administrador</span>
                            
                        </td>
                        <td>
                            <div class="user-actions">
                                <button class="btn btn-warning btn-sm" 
                                        onclick="abrirModalSenha(9)" 
                                        type="button">
                                    🔐 Trocar Senha
                                </button>
                                
                                
                            </div>
                        </td>
                    </tr>
                    
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal de alteração de senha -->
<div id="modalSenha" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>🔐 Alterar Senha</h3>
            <button class="close" onclick="fecharModalSenha()">&times;</button>
        </div>
        
        <form id="formAlterarSenha">
            <input type="hidden" id="usuarioIdSenha" name="usuarioIdSenha">
            
            <div class="form-group">
                <label for="novaSenha">Nova Senha</label>
                <input type="password" id="novaSenha" name="novaSenha" required placeholder="Mínimo 8 caracteres">
            </div>
            
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="fecharModalSenha()">
                    Cancelar
                </button>
                <button type="submit" class="btn btn-primary">
                    💾 Salvar Senha
                </button>
            </div>
        </form>
    </div>
</div>

                
                <!-- Rodapé com informações do sistema -->
                <footer class="system-footer">
                    <div class="footer-content">
                        <div class="footer-line">RLPONTO-WEB v1.0 • Sistema de Controle de Ponto Biométrico Empresarial</div>
                        <div class="footer-line">© 2025 AiNexus Tecnologia. Todos os direitos reservados.</div>
                    </div>
                </footer>
            </div>
        </main>
    </div>
    
    <!-- Modal de confirmação -->
    <div id="confirmModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; max-width: 400px; width: 90%;">
            <h3 style="margin-top: 0; color: #dc3545;">Confirmar Exclusão</h3>
            <p id="confirmMessage">Tem certeza que deseja excluir este funcionário?</p>
            <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                <button onclick="closeConfirmModal()" style="padding: 8px 16px; border: 1px solid #ced4da; background: white; border-radius: 4px; cursor: pointer;">Cancelar</button>
                <button id="confirmDelete" style="padding: 8px 16px; border: none; background: #dc3545; color: white; border-radius: 4px; cursor: pointer;">Excluir</button>
            </div>
        </div>
    </div>
    
    
<script>
// Abrir modal de senha
function abrirModalSenha(userId) {
    document.getElementById('usuarioIdSenha').value = userId;
    document.getElementById('modalSenha').style.display = 'block';
    document.getElementById('novaSenha').focus();
}

// Fechar modal de senha
function fecharModalSenha() {
    document.getElementById('modalSenha').style.display = 'none';
    document.getElementById('formAlterarSenha').reset();
}

// Alterar nível de acesso
function alterarNivel(userId, novoNivel) {
    fetch('/alterar_nivel', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${userId}&nivel=${novoNivel}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Erro: ' + data.message);
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erro ao alterar nível de acesso');
        location.reload();
    });
}

// Confirmar exclusão de usuário
function confirmarExclusaoUsuario(userId, userName) {
    const message = `Tem certeza que deseja excluir o usuário "${userName}"? Esta ação não poderá ser desfeita.`;
    
    showConfirmModal(message, function() {
        fetch('/excluir_usuario', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `id=${userId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erro ao excluir usuário');
        });
    });
}

// Form de alteração de senha
document.getElementById('formAlterarSenha').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const userId = formData.get('usuarioIdSenha');
    const novaSenha = formData.get('novaSenha');
    
    if (novaSenha.length < 8) {
        alert('A senha deve ter pelo menos 8 caracteres');
        return;
    }
    
    fetch('/trocar_senha', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${userId}&senha=${encodeURIComponent(novaSenha)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            fecharModalSenha();
            alert('Senha alterada com sucesso');
            if (data.kick_user) {
                alert('Você alterou sua própria senha e será deslogado');
                window.location.href = '/logout';
            }
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erro ao alterar senha');
    });
});

// Fechar modal clicando fora
document.getElementById('modalSenha').addEventListener('click', function(e) {
    if (e.target === this) {
        fecharModalSenha();
    }
});

// Validação em tempo real
document.getElementById('novaSenha').addEventListener('input', function(e) {
    const senha = e.target.value;
    const isValid = senha.length >= 8;
    
    if (!isValid && senha.length > 0) {
        e.target.style.borderColor = '#dc3545';
    } else {
        e.target.style.borderColor = '#ced4da';
    }
});
</script>

    
    <script>
        // 🚀 SIDEBAR CONTROLS
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        const mainContent = document.querySelector('.main-content');

        let isMobile = window.innerWidth <= 768;
        let isCollapsed = false;

        // Toggle sidebar
        function toggleSidebar() {
            if (isMobile) {
                sidebar.classList.toggle('mobile-open');
                sidebarOverlay.classList.toggle('show');
            } else {
                isCollapsed = !isCollapsed;
                sidebar.classList.toggle('collapsed', isCollapsed);
                
                // Salvar preferência
                localStorage.setItem('sidebarCollapsed', isCollapsed);
            }
        }

        // Event listeners
        sidebarToggle.addEventListener('click', toggleSidebar);
        sidebarOverlay.addEventListener('click', toggleSidebar);

        // Responsive handling
        window.addEventListener('resize', () => {
            const wasMobile = isMobile;
            isMobile = window.innerWidth <= 768;
            
            if (wasMobile !== isMobile) {
                // Reset sidebar state when switching between mobile/desktop
                sidebar.classList.remove('mobile-open', 'collapsed');
                sidebarOverlay.classList.remove('show');
                
                if (!isMobile) {
                    // Restore collapsed state on desktop
                    isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                    sidebar.classList.toggle('collapsed', isCollapsed);
                }
            }
        });

        // Restore sidebar state on load
        document.addEventListener('DOMContentLoaded', () => {
            if (!isMobile) {
                isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                sidebar.classList.toggle('collapsed', isCollapsed);
            }
            
            // Highlight active nav item
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                const onclick = link.getAttribute('onclick');
                if (onclick && onclick.includes(currentPath)) {
                    link.classList.add('active');
                }
            });
        });

        // Função para modal de confirmação (mantida para compatibilidade)
        function showConfirmModal(message, onConfirm) {
            document.getElementById('confirmMessage').textContent = message;
            document.getElementById('confirmModal').style.display = 'block';
            
            document.getElementById('confirmDelete').onclick = function() {
                closeConfirmModal();
                onConfirm();
            };
        }
        
        function closeConfirmModal() {
            document.getElementById('confirmModal').style.display = 'none';
        }
        
        // Fechar modal clicando fora
        document.getElementById('confirmModal').onclick = function(e) {
            if (e.target === this) {
                closeConfirmModal();
            }
        };
        
        // Função para exclusão com confirmação
        function confirmarExclusao(funcionarioId, funcionarioNome) {
            const message = `Tem certeza que deseja excluir o funcionário "${funcionarioNome}"? Esta ação não poderá ser desfeita.`;
            
            showConfirmModal(message, function() {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/funcionarios/${funcionarioId}/apagar`;
                document.body.appendChild(form);
                form.submit();
            });
        }
        
        // Auto-hide para mensagens flash
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 300);
                }, 5000);
            });
        });
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>