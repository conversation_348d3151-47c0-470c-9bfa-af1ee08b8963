#!/usr/bin/env python3
"""
Teste para verificar o que está acontecendo no salvamento de horas semanais
"""
import sys
import os
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager

def testar_salvamento_horas():
    print("🧪 TESTE DE SALVAMENTO DE HORAS SEMANAIS")
    print("=" * 60)
    
    try:
        funcionario_id = 35
        db = DatabaseManager()
        
        print(f"\n1. 📊 ESTADO ATUAL NO BANCO")
        atual = db.execute_query("""
            SELECT id, nome_completo, horas_semanais_obrigatorias 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,), fetch_one=True)
        
        if atual:
            print(f"   ✅ {atual['nome_completo']}: {atual['horas_semanais_obrigatorias']}h")
        
        print(f"\n2. 🔄 TESTE DE ATUALIZAÇÃO DIRETA")
        # Atualizar para 44h
        print("   Atualizando para 44.00h...")
        db.execute_query("""
            UPDATE funcionarios 
            SET horas_semanais_obrigatorias = 44.00 
            WHERE id = %s
        """, (funcionario_id,))
        
        # Verificar se salvou
        verificacao1 = db.execute_query("""
            SELECT horas_semanais_obrigatorias 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,), fetch_one=True)
        
        print(f"   ✅ Após update direto: {verificacao1['horas_semanais_obrigatorias']}h")
        
        print(f"\n3. 🔍 VERIFICAR SE HÁ TRIGGERS OU PROCEDURES")
        # Verificar se há triggers na tabela funcionarios
        triggers = db.execute_query("""
            SELECT TRIGGER_NAME, EVENT_MANIPULATION, ACTION_TIMING 
            FROM information_schema.TRIGGERS 
            WHERE EVENT_OBJECT_TABLE = 'funcionarios' 
            AND EVENT_OBJECT_SCHEMA = 'controle_ponto'
        """)
        
        if triggers:
            print(f"   ⚠️ TRIGGERS ENCONTRADOS:")
            for trigger in triggers:
                print(f"      - {trigger['TRIGGER_NAME']}: {trigger['ACTION_TIMING']} {trigger['EVENT_MANIPULATION']}")
        else:
            print(f"   ✅ Nenhum trigger encontrado")
        
        print(f"\n4. 🔍 VERIFICAR ESTRUTURA DA TABELA")
        estrutura = db.execute_query("DESCRIBE funcionarios")
        campo_horas = [campo for campo in estrutura if 'hora' in campo['Field'].lower()]
        
        print(f"   📋 Campos relacionados a horas:")
        for campo in campo_horas:
            print(f"      - {campo['Field']}: {campo['Type']} (Default: {campo['Default']})")
        
        print(f"\n5. 🧪 SIMULAR SALVAMENTO VIA APLICAÇÃO")
        # Vamos simular o que acontece quando salvamos via aplicação
        from app_funcionarios import _safe_decimal_with_comma_fix
        
        # Testar a função de conversão
        valor_teste = "44.00"
        valor_convertido = _safe_decimal_with_comma_fix(valor_teste)
        print(f"   🔧 _safe_decimal_with_comma_fix('44.00') = {valor_convertido}")
        
        # Atualizar usando a mesma lógica da aplicação
        print("   Atualizando usando lógica da aplicação...")
        db.execute_query("""
            UPDATE funcionarios 
            SET horas_semanais_obrigatorias = %s 
            WHERE id = %s
        """, (valor_convertido, funcionario_id))
        
        # Verificar resultado
        verificacao2 = db.execute_query("""
            SELECT horas_semanais_obrigatorias 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,), fetch_one=True)
        
        print(f"   ✅ Após update via aplicação: {verificacao2['horas_semanais_obrigatorias']}h")
        
        print(f"\n6. 🔍 VERIFICAR LOGS DE ALTERAÇÃO")
        # Verificar se há algum log de alteração
        try:
            logs = db.execute_query("""
                SELECT * FROM funcionarios_log 
                WHERE funcionario_id = %s 
                ORDER BY data_alteracao DESC 
                LIMIT 5
            """, (funcionario_id,))
            
            if logs:
                print(f"   📝 Últimas alterações:")
                for log in logs:
                    print(f"      - {log.get('data_alteracao')}: {log.get('campo_alterado')} = {log.get('valor_novo')}")
            else:
                print(f"   ℹ️ Nenhum log de alteração encontrado")
        except:
            print(f"   ℹ️ Tabela de logs não existe")
        
        print("\n" + "=" * 60)
        print("✅ TESTE CONCLUÍDO")
        
    except Exception as e:
        print(f"❌ ERRO GERAL NO TESTE: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    testar_salvamento_horas()
