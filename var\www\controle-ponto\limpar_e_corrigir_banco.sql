-- Script para limpar e corrigir estrutura do banco - Controle de Ponto
-- ATENÇÃO: Este script remove TODOS os dados dos funcionários!
-- Execute apenas se tiver certeza que pode perder os dados atuais

USE controle_ponto;

-- 1. <PERSON>azer backup da estrutura atual (opcional - descomente se necessário)
-- CREATE TABLE funcionarios_backup AS SELECT * FROM funcionarios;

-- 2. Desabilitar verificação de chaves estrangeiras temporariamente
SET FOREIGN_KEY_CHECKS = 0;

-- 3. <PERSON>par todas as tabelas relacionadas na ordem correta
DROP TABLE IF EXISTS epis;
DROP TABLE IF EXISTS registros_ponto;

-- 4. Remover todos os dados da tabela funcionários (mas manter estrutura)
DELETE FROM funcionarios;

-- 5. Tentar remover as colunas problemáticas agora que não há dados
ALTER TABLE funcionarios DROP COLUMN IF EXISTS jornada_entrada;
ALTER TABLE funcionarios DROP COLUMN IF EXISTS jornada_saida;
ALTER TABLE funcionarios DROP COLUMN IF EXISTS assinatura;

-- 6. Verificar se as colunas necessárias existem, senão adicionar
ALTER TABLE funcionarios 
ADD COLUMN IF NOT EXISTS jornada_seg_qui_entrada TIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS jornada_seg_qui_saida TIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS jornada_sex_entrada TIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS jornada_sex_saida TIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS jornada_intervalo_entrada TIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS jornada_intervalo_saida TIME DEFAULT NULL;

-- 7. Recriar tabela de registros de ponto
CREATE TABLE IF NOT EXISTS registros_ponto (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int DEFAULT NULL,
  data_hora datetime DEFAULT CURRENT_TIMESTAMP,
  sincronizado tinyint DEFAULT 0,
  PRIMARY KEY (id),
  FOREIGN KEY (funcionario_id) REFERENCES funcionarios (id) ON DELETE CASCADE
) ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci;

-- 8. Recriar tabela de EPIs com chave estrangeira correta
CREATE TABLE IF NOT EXISTS epis (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int NOT NULL,
  epi_nome varchar(255) NOT NULL,
  epi_ca varchar(50) DEFAULT NULL,
  epi_data_entrega date DEFAULT NULL,
  epi_data_validade date DEFAULT NULL,
  epi_observacoes text DEFAULT NULL,
  PRIMARY KEY (id),
  FOREIGN KEY (funcionario_id) REFERENCES funcionarios (id) ON DELETE CASCADE
) ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci;

-- 9. Reabilitar verificação de chaves estrangeiras
SET FOREIGN_KEY_CHECKS = 1;

-- 10. Resetar auto_increment para começar do 1
ALTER TABLE funcionarios AUTO_INCREMENT = 1;
ALTER TABLE registros_ponto AUTO_INCREMENT = 1;
ALTER TABLE epis AUTO_INCREMENT = 1;

-- 11. Verificar estrutura final
DESCRIBE funcionarios;
DESCRIBE epis;
DESCRIBE registros_ponto;

-- 12. Mostrar contagem de registros (deve ser 0)
SELECT 'funcionarios' as tabela, COUNT(*) as registros FROM funcionarios
UNION ALL
SELECT 'epis' as tabela, COUNT(*) as registros FROM epis
UNION ALL
SELECT 'registros_ponto' as tabela, COUNT(*) as registros FROM registros_ponto; 