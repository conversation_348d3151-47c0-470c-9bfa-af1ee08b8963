"""
Serviço de Integração com APIs Externas e MCP Servers
Responsável pela comunicação com serviços externos
"""
import requests
import json
import asyncio
import websockets
from typing import Dict, Any, Optional, List
from datetime import datetime
from ..config.api_config import api_manager, APIConfig, MCPServerConfig

class ExternalAPIService:
    """Serviço para comunicação com APIs externas"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'AgenteAiNexus/1.0'
        })
    
    async def call_api(self, api_name: str, endpoint: str, method: str = 'GET', 
                       data: Dict[str, Any] = None, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Chama uma API externa configurada"""
        try:
            api_config = api_manager.get_api(api_name)
            if not api_config or not api_config.enabled:
                return {
                    'success': False,
                    'error': f'API {api_name} não encontrada ou desabilitada'
                }
            
            # Usar a URL como está configurada (já validada na criação)
            base_url = api_config.base_url.rstrip('/')
            endpoint = endpoint.lstrip('/')
            
            if endpoint:
                url = f"{base_url}/{endpoint}"
            else:
                url = base_url
            
            headers = api_config.headers.copy()
            
            # Adicionar autenticação
            if api_config.api_key:
                if 'Authorization' not in headers:
                    headers['Authorization'] = f"Bearer {api_config.api_key}"
            
            # Fazer requisição
            response = self.session.request(
                method=method.upper(),
                url=url,
                headers=headers,
                json=data if method.upper() in ['POST', 'PUT', 'PATCH'] else None,
                params=params,
                timeout=api_config.timeout
            )
            
            response.raise_for_status()
            
            return {
                'success': True,
                'data': response.json() if response.content else None,
                'status_code': response.status_code,
                'url_called': url,
                'timestamp': datetime.now().isoformat()
            }
            
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f'Erro na requisição: {str(e)}',
                'url_attempted': url if 'url' in locals() else 'URL não construída',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Erro inesperado: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def call_openai(self, prompt: str, model: str = "gpt-3.5-turbo") -> Dict[str, Any]:
        """Integração específica com OpenAI"""
        openai_config = api_manager.get_api('openai')
        if not openai_config or not openai_config.enabled:
            return {
                'success': False,
                'error': 'OpenAI não configurado ou desabilitado'
            }
        
        try:
            response = self.session.post(
                f"{openai_config.base_url}/chat/completions",
                headers={
                    'Authorization': f"Bearer {openai_config.api_key}",
                    'Content-Type': 'application/json'
                },
                json={
                    'model': model,
                    'messages': [{'role': 'user', 'content': prompt}],
                    'max_tokens': 1000
                },
                timeout=openai_config.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            return {
                'success': True,
                'response': result['choices'][0]['message']['content'],
                'usage': result.get('usage', {}),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Erro OpenAI: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }

class MCPService:
    """Serviço para comunicação com MCP Servers"""
    
    def __init__(self):
        self.active_connections: Dict[str, Any] = {}
    
    async def connect_mcp_server(self, server_name: str) -> Dict[str, Any]:
        """Conecta a um MCP Server"""
        try:
            mcp_config = api_manager.get_mcp_server(server_name)
            if not mcp_config or not mcp_config.enabled:
                return {
                    'success': False,
                    'error': f'MCP Server {server_name} não encontrado ou desabilitado'
                }
            
            if mcp_config.transport == 'websocket':
                return await self._connect_websocket(server_name, mcp_config)
            elif mcp_config.transport == 'http':
                return await self._connect_http(server_name, mcp_config)
            else:
                return {
                    'success': False,
                    'error': f'Protocolo {mcp_config.transport} não suportado'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Erro ao conectar MCP Server: {str(e)}'
            }
    
    async def _connect_http(self, server_name: str, config: MCPServerConfig) -> Dict[str, Any]:
        """Conecta via HTTP"""
        try:
            # Usar a URL como está configurada
            server_url = config.server_url.rstrip('/')
            
            # Construir URL de health check
            health_url = f"{server_url}/health"
            
            # Teste de conectividade
            async with asyncio.timeout(getattr(config, 'timeout', 30)):
                response = requests.get(
                    health_url,
                    headers={'Authorization': f"Bearer {config.auth_token}"},
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.active_connections[server_name] = {
                        'type': 'http',
                        'config': config,
                        'connected_at': datetime.now().isoformat()
                    }
                    
                    return {
                        'success': True,
                        'server_name': server_name,
                        'capabilities': config.capabilities,
                        'connected_at': datetime.now().isoformat(),
                        'health_url': health_url
                    }
                else:
                    return {
                        'success': False,
                        'error': f'Server respondeu com status {response.status_code}',
                        'health_url': health_url
                    }
                    
        except Exception as e:
            return {
                'success': False,
                'error': f'Erro HTTP: {str(e)}',
                'health_url': health_url if 'health_url' in locals() else 'URL não construída'
            }
    
    async def _connect_websocket(self, server_name: str, config: MCPServerConfig) -> Dict[str, Any]:
        """Conecta via WebSocket"""
        try:
            # Headers de autenticação
            headers = {
                'Authorization': f"Bearer {config.auth_token}"
            }
            
            # Tentar conectar
            websocket = await websockets.connect(
                config.server_url,
                extra_headers=headers,
                timeout=30
            )
            
            self.active_connections[server_name] = {
                'type': 'websocket',
                'websocket': websocket,
                'config': config,
                'connected_at': datetime.now().isoformat()
            }
            
            return {
                'success': True,
                'server_name': server_name,
                'capabilities': config.capabilities,
                'connected_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Erro WebSocket: {str(e)}'
            }
    
    async def call_mcp_tool(self, server_name: str, tool_name: str, 
                           arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """Chama uma ferramenta em um MCP Server"""
        try:
            if server_name not in self.active_connections:
                connect_result = await self.connect_mcp_server(server_name)
                if not connect_result['success']:
                    return connect_result
            
            connection = self.active_connections[server_name]
            config = connection['config']
            
            if connection['type'] == 'http':
                return await self._call_http_tool(config, tool_name, arguments)
            elif connection['type'] == 'websocket':
                return await self._call_websocket_tool(connection['websocket'], tool_name, arguments)
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Erro ao chamar ferramenta MCP: {str(e)}'
            }
    
    async def _call_http_tool(self, config: MCPServerConfig, tool_name: str, 
                             arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Chama ferramenta via HTTP"""
        try:
            response = requests.post(
                f"{config.server_url}/tools/{tool_name}",
                headers={
                    'Authorization': f"Bearer {config.auth_token}",
                    'Content-Type': 'application/json'
                },
                json={'arguments': arguments or {}},
                timeout=30
            )
            
            response.raise_for_status()
            
            return {
                'success': True,
                'result': response.json(),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Erro HTTP tool: {str(e)}'
            }
    
    async def _call_websocket_tool(self, websocket, tool_name: str, 
                                  arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Chama ferramenta via WebSocket"""
        try:
            message = {
                'type': 'tool_call',
                'tool_name': tool_name,
                'arguments': arguments or {},
                'id': f"call_{datetime.now().timestamp()}"
            }
            
            await websocket.send(json.dumps(message))
            response = await websocket.recv()
            result = json.loads(response)
            
            return {
                'success': True,
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Erro WebSocket tool: {str(e)}'
            }
    
    def list_active_connections(self) -> Dict[str, Dict[str, Any]]:
        """Lista conexões ativas com MCP Servers"""
        return {
            name: {
                'type': conn['type'],
                'server_url': conn['config'].server_url,
                'capabilities': conn['config'].capabilities,
                'connected_at': conn['connected_at']
            }
            for name, conn in self.active_connections.items()
        }
    
    async def disconnect_all(self):
        """Desconecta de todos os MCP Servers"""
        for name, connection in self.active_connections.items():
            if connection['type'] == 'websocket' and 'websocket' in connection:
                await connection['websocket'].close()
        
        self.active_connections.clear()

# Instâncias globais dos serviços
external_api_service = ExternalAPIService()
mcp_service = MCPService() 