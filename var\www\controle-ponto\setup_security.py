#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de Configuração de Segurança - RLPONTO-WEB
-------------------------------------------------

Este script orienta a configuração inicial segura do sistema,
criando o arquivo .env com credenciais apropriadas.

EXECUTE ESTE SCRIPT ANTES DE COLOCAR O SISTEMA EM PRODUÇÃO!
"""

import os
import secrets
import sys
from getpass import getpass

def print_banner():
    """Exibe banner do script"""
    print("=" * 60)
    print("🔒 RLPONTO-WEB - CONFIGURAÇÃO DE SEGURANÇA")
    print("=" * 60)
    print()
    print("Este script irá configurar credenciais seguras para o sistema.")
    print("⚠️  IMPORTANTE: Execute em ambiente seguro!")
    print()

def generate_secret_key():
    """Gera chave secreta segura"""
    return secrets.token_hex(32)

def get_database_config():
    """Coleta configurações do banco de dados"""
    print("📊 CONFIGURAÇÃO DO BANCO DE DADOS")
    print("-" * 40)
    
    db_host = input("Host do banco (localhost): ").strip() or "localhost"
    db_user = input("Usuário do banco (controle_user): ").strip() or "controle_user"
    
    while True:
        db_password = getpass("Senha do banco (obrigatória): ").strip()
        if db_password:
            db_password_confirm = getpass("Confirme a senha: ").strip()
            if db_password == db_password_confirm:
                break
            else:
                print("❌ Senhas não coincidem. Tente novamente.")
        else:
            print("❌ Senha é obrigatória!")
    
    db_name = input("Nome do banco (controle_ponto): ").strip() or "controle_ponto"
    
    return {
        'DB_HOST': db_host,
        'DB_USER': db_user, 
        'DB_PASSWORD': db_password,
        'DB_NAME': db_name
    }

def get_server_config():
    """Coleta configurações do servidor"""
    print("\n🌐 CONFIGURAÇÃO DO SERVIDOR")
    print("-" * 40)
    
    flask_host = input("Host Flask (0.0.0.0): ").strip() or "0.0.0.0"
    flask_port = input("Porta Flask (5000): ").strip() or "5000"
    
    # Validação da porta
    try:
        flask_port = int(flask_port)
        if not (1024 <= flask_port <= 65535):
            print("⚠️ Porta deve estar entre 1024 e 65535. Usando 5000.")
            flask_port = 5000
    except ValueError:
        print("⚠️ Porta inválida. Usando 5000.")
        flask_port = 5000
    
    debug_input = input("Habilitar debug? (N/y): ").strip().lower()
    flask_debug = debug_input in ['y', 'yes', 's', 'sim']
    
    if flask_debug:
        print("⚠️ ATENÇÃO: Debug habilitado! Use apenas em desenvolvimento!")
    
    return {
        'FLASK_HOST': flask_host,
        'FLASK_PORT': str(flask_port),
        'FLASK_DEBUG': str(flask_debug).lower()
    }

def create_env_file(config):
    """Cria arquivo .env com as configurações"""
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    
    if os.path.exists(env_path):
        response = input(f"\n⚠️ Arquivo .env já existe. Sobrescrever? (y/N): ").strip().lower()
        if response not in ['y', 'yes', 's', 'sim']:
            print("❌ Operação cancelada.")
            return False
    
    try:
        with open(env_path, 'w') as f:
            f.write("# ===============================================\n")
            f.write("# RLPONTO-WEB - Configurações de Produção\n")
            f.write(f"# Gerado em: {os.popen('date').read().strip()}\n")
            f.write("# ===============================================\n")
            f.write("# ⚠️ NUNCA COMMITAR ESTE ARQUIVO!\n\n")
            
            f.write("# 🔒 SEGURANÇA CRÍTICA\n")
            f.write(f"SECRET_KEY={config['SECRET_KEY']}\n")
            f.write(f"FLASK_DEBUG={config['FLASK_DEBUG']}\n\n")
            
            f.write("# 🗄️ BANCO DE DADOS\n")
            f.write(f"DB_HOST={config['DB_HOST']}\n")
            f.write(f"DB_USER={config['DB_USER']}\n")
            f.write(f"DB_PASSWORD={config['DB_PASSWORD']}\n")
            f.write(f"DB_NAME={config['DB_NAME']}\n\n")
            
            f.write("# 🌐 SERVIDOR\n")
            f.write(f"FLASK_HOST={config['FLASK_HOST']}\n")
            f.write(f"FLASK_PORT={config['FLASK_PORT']}\n\n")
            
            f.write("# 📊 LOGS\n")
            f.write("LOG_LEVEL=INFO\n")
            f.write("LOG_DIR=/var/log/controle-ponto\n\n")
            
            f.write("# 🔧 CONFIGURAÇÕES ADICIONAIS\n")
            f.write("SESSION_LIFETIME=86400\n")
            f.write("UPLOAD_FOLDER=static/uploads\n")
            f.write("MAX_CONTENT_LENGTH=16777216\n")
        
        print(f"✅ Arquivo .env criado com sucesso: {env_path}")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar arquivo .env: {e}")
        return False

def show_security_checklist():
    """Exibe checklist de segurança"""
    print("\n🛡️ CHECKLIST DE SEGURANÇA")
    print("-" * 40)
    print("✅ SECRET_KEY gerada automaticamente")
    print("✅ Credenciais do banco configuradas")
    print("✅ Arquivo .env criado")
    print()
    print("📋 PRÓXIMOS PASSOS:")
    print("1. 🔒 Proteja o arquivo .env (chmod 600 .env)")
    print("2. 🚫 Adicione .env ao .gitignore")
    print("3. 🔐 Configure firewall do banco de dados")
    print("4. 📊 Configure backup automático")
    print("5. 🌐 Configure HTTPS (recomendado)")
    print()

def main():
    """Função principal"""
    print_banner()
    
    try:
        # Gera chave secreta automaticamente
        secret_key = generate_secret_key()
        print(f"🔐 Chave secreta gerada: {secret_key[:16]}... (64 caracteres)")
        
        # Coleta configurações
        db_config = get_database_config()
        server_config = get_server_config()
        
        # Combina todas as configurações
        config = {
            'SECRET_KEY': secret_key,
            **db_config,
            **server_config
        }
        
        # Cria arquivo .env
        if create_env_file(config):
            show_security_checklist()
            
            print("\n🎉 CONFIGURAÇÃO CONCLUÍDA!")
            print("O sistema agora está configurado com credenciais seguras.")
            print()
            print("Para iniciar o sistema, execute:")
            print("  python app.py")
            print()
        else:
            print("\n❌ Falha na configuração. Tente novamente.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n❌ Configuração cancelada pelo usuário.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main() 