-- Consultar registros recentes para verificar atrasos
SELECT 
    f.nome_completo,
    DATE(rp.data_hora) as data,
    rp.tipo_registro,
    TIME(rp.data_hora) as hora,
    CASE 
        WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'ATRASO'
        ELSE 'PONTUAL'
    END as status_atraso
FROM registros_ponto rp 
JOIN funcionarios f ON rp.funcionario_id = f.id 
WHERE DATE(rp.data_hora) >= '2025-07-14' 
AND rp.tipo_registro = 'entrada_manha'
ORDER BY rp.data_hora;
