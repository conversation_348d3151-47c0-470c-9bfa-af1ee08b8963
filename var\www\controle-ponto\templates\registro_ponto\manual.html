{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    /* ========================================
       RLPONTO-WEB VISUAL IDENTITY - v2.0
       SEGUINDO ESPECIFICAÇÕES DO LAYOUT-RLPONTO.MD
       ======================================== */

    :root {
        /* Cores primárias - Sistema RLPONTO-WEB */
        --primary-color: #4fbdba;
        --primary-hover: #26a69a;
        --primary-light: #80cbc4;
        --primary-dark: #00695c;

        /* Backgrounds modernos */
        --background-color: #f8fafc;
        --card-background: #ffffff;
        --hover-bg: #f1f5f9;
        --sidebar-bg: #ffffff;

        /* Textos com melhor contraste */
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --text-muted: #94a3b8;
        --text-white: #ffffff;

        /* <PERSON><PERSON><PERSON> suaves */
        --border-color: #e2e8f0;
        --border-light: #f1f5f9;
        --border-dark: #cbd5e1;

        /* Estados visuais */
        --success-color: #10b981;
        --success-bg: #dcfce7;
        --success-text: #166534;

        --warning-color: #f59e0b;
        --warning-bg: #fef3c7;
        --warning-text: #92400e;

        --danger-color: #ef4444;
        --danger-bg: #fee2e2;
        --danger-text: #dc2626;

        --info-color: #3b82f6;
        --info-bg: #dbeafe;
        --info-text: #1e40af;              /* Texto info */

        /* Tamanhos de fonte */
        --font-size-xs: 0.75rem;      /* 12px - Textos muito pequenos */
        --font-size-sm: 0.875rem;     /* 14px - Textos pequenos */
        --font-size-base: 1rem;       /* 16px - Texto base */
        --font-size-lg: 1.125rem;     /* 18px - Textos grandes */
        --font-size-xl: 1.25rem;      /* 20px - Subtítulos */
        --font-size-2xl: 1.5rem;      /* 24px - Títulos */
        --font-size-3xl: 1.875rem;    /* 30px - Títulos principais */

        /* Espaçamentos */
        --spacing-xs: 0.25rem;    /* 4px */
        --spacing-sm: 0.5rem;     /* 8px */
        --spacing-md: 1rem;       /* 16px */
        --spacing-lg: 1.5rem;     /* 24px */
        --spacing-xl: 2rem;       /* 32px */
        --spacing-2xl: 3rem;      /* 48px */

        /* Border radius */
        --radius-sm: 6px;
        --radius-md: 8px;
        --radius-lg: 12px;
    }

    /* ========================================
       GLOBAL STYLES - PADRÃO RLPONTO-WEB
       ======================================== */

    body {
        background-color: var(--background-color);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        color: var(--text-primary);
        line-height: 1.5;
    }

    .main-container {
        background-color: var(--background-color);
        min-height: 100vh;
        padding: 2rem;
        max-width: 1200px;
        margin: 0 auto;
    }

    /* ========================================
       HEADER PADRÃO - SEGUINDO VISUAL.MD
       ======================================== */

    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        border-radius: var(--radius-lg);
        padding: 2rem;
        margin-bottom: 2rem;
        color: var(--text-white);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 60%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        transform: rotate(15deg);
    }

    .page-header h1 {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        position: relative;
        z-index: 2;
    }

    .page-header p {
        font-size: var(--font-size-lg);
        margin: 0;
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }

    /* ========================================
       SEÇÃO DE PESQUISA - PADRÃO RLPONTO-WEB
       ======================================== */
    .pesquisa-section {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .pesquisa-section h5 {
        color: var(--text-primary);
        font-size: var(--font-size-lg);
        font-weight: 600;
        margin-bottom: var(--spacing-md);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .pesquisa-input {
        position: relative;
        max-width: 100%;
    }

    .pesquisa-input input {
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: 0.75rem 1rem 0.75rem 3rem;
        background: var(--card-background);
        color: var(--text-primary);
        font-size: var(--font-size-base);
        transition: all 0.3s ease;
        width: 100%;
        box-sizing: border-box;
    }

    .pesquisa-input input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
        outline: none;
    }

    .pesquisa-input i {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
        z-index: 5;
    }

    /* ========================================
       GRID DE FUNCIONÁRIOS - PADRÃO RLPONTO-WEB
       ======================================== */
    .funcionarios-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: var(--spacing-lg);
        margin-top: var(--spacing-lg);
    }

    .funcionario-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .funcionario-card:hover {
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .funcionario-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-md);
    }

    .funcionario-foto {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-md);
        object-fit: cover;
        margin-right: var(--spacing-md);
        border: 2px solid var(--border-color);
        background: linear-gradient(45deg, var(--text-secondary), var(--text-muted));
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-white);
        font-size: 1.5rem;
        flex-shrink: 0;
    }

    .funcionario-foto img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: calc(var(--radius-md) - 2px);
    }

    .funcionario-info h5 {
        margin: 0 0 var(--spacing-xs) 0;
        color: var(--text-primary);
        font-size: var(--font-size-lg);
        font-weight: 600;
    }

    .funcionario-info .subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        margin: 2px 0;
    }

    .funcionario-detalhes {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-md);
    }

    .detalhe-item {
        display: flex;
        align-items: center;
        color: var(--text-secondary);
    }

    .detalhe-item i {
        width: 16px;
        margin-right: var(--spacing-sm);
        color: var(--primary-color);
    }

    .funcionario-actions {
        display: flex;
        justify-content: center;
        margin-top: var(--spacing-md);
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-light);
    }

    .btn-registrar-funcionario {
        background: var(--primary-color);
        border: 1px solid var(--primary-color);
        border-radius: var(--radius-lg);
        padding: 0.75rem 1.5rem;
        color: var(--text-white);
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: var(--font-size-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .btn-registrar-funcionario:hover {
        background: var(--primary-hover);
        border-color: var(--primary-hover);
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        color: var(--text-white);
        text-decoration: none;
    }

    /* ========================================
       ESTADOS VAZIOS - PADRÃO RLPONTO-WEB
       ======================================== */
    .no-results {
        text-align: center;
        padding: var(--spacing-2xl);
        color: var(--text-secondary);
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        margin-top: var(--spacing-lg);
    }

    .no-results i {
        font-size: 3rem;
        margin-bottom: var(--spacing-lg);
        color: var(--text-muted);
    }

    .no-results h4 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    /* ========================================
       MODAIS - PADRÃO RLPONTO-WEB
       ======================================== */

    /* ✅ MODAL FORÇADO A FICAR OCULTO INICIALMENTE */
    #registroModal {
        display: none !important;
    }

    #registroModal.show {
        display: block !important;
    }

    .modal-dialog {
        max-width: 600px;
        margin: 30px auto;
    }

    .modal-content {
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-color);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        max-height: 90vh;
        overflow-y: auto;
        background: var(--card-background);
    }

    .modal-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        color: var(--text-white);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        border-bottom: none;
        padding: var(--spacing-lg);
    }

    .modal-header h5 {
        margin: 0;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .modal-body {
        padding: var(--spacing-xl);
        max-height: 60vh;
        overflow-y: auto;
    }

    /* ========================================
       HEADER DO FUNCIONÁRIO NO MODAL
       ======================================== */
    .funcionario-modal-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-lg);
        padding: var(--spacing-lg);
        background: var(--hover-bg);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-light);
    }

    .funcionario-modal-foto {
        width: 80px;
        height: 80px;
        border-radius: var(--radius-lg);
        object-fit: cover;
        margin-right: var(--spacing-lg);
        border: 3px solid var(--primary-color);
        background: linear-gradient(45deg, var(--text-secondary), var(--text-muted));
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-white);
        font-size: 2rem;
        flex-shrink: 0;
    }

    .funcionario-modal-foto img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: calc(var(--radius-lg) - 3px);
    }

    .funcionario-modal-info h4 {
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
        font-weight: 600;
    }

    .funcionario-modal-info .text-muted {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    /* ========================================
       FORMULÁRIOS - PADRÃO RLPONTO-WEB
       ======================================== */

    .form-label {
        font-weight: 500;
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        border: 1px solid var(--border-color);
        border-radius: var(--radius-sm);
        padding: 0.5rem 0.75rem;
        background: var(--card-background);
        color: var(--text-primary);
        font-size: var(--font-size-base);
        transition: all 0.3s ease;
        width: 100%;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
        outline: none;
    }

    /* ========================================
       TIPOS DE REGISTRO - REDESENHADO
       ======================================== */
    .tipos-registro-modal {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
        margin-top: var(--spacing-md);
    }

    .tipo-registro-opcao {
        position: relative;
    }

    .tipo-registro-opcao input[type="radio"] {
        display: none;
    }

    .tipo-registro-opcao label {
        display: block;
        padding: var(--spacing-md);
        border: 2px solid var(--border-color);
        border-radius: var(--radius-lg);
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: var(--card-background);
        position: relative;
        overflow: hidden;
    }

    .tipo-registro-opcao label:hover {
        border-color: var(--primary-color);
        background: rgba(79, 189, 186, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .tipo-registro-opcao input[type="radio"]:checked + label {
        border-color: var(--primary-color);
        background: var(--primary-color);
        color: var(--text-white);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .tipo-registro-opcao i {
        font-size: 1.5rem;
        margin-bottom: var(--spacing-sm);
        display: block;
    }

    .horario-info {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
        margin-top: var(--spacing-sm);
    }

    .horario-info .badge {
        font-size: var(--font-size-xs);
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        display: inline-block;
    }

    .badge.bg-primary {
        background-color: var(--info-color) !important;
        color: var(--text-white);
    }

    .badge.bg-success {
        background-color: var(--success-color) !important;
        color: var(--text-white);
    }

    /* ========================================
       BOTÕES DO MODAL - PADRÃO RLPONTO-WEB
       ======================================== */

    .modal-footer {
        border-top: 1px solid var(--border-light);
        padding: var(--spacing-lg);
        display: flex;
        justify-content: flex-end;
        gap: var(--spacing-md);
    }

    .btn-secondary {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        color: var(--text-secondary);
        border-radius: var(--radius-md);
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: var(--hover-bg);
        border-color: var(--border-dark);
        color: var(--text-primary);
    }

    .btn-primary {
        background: var(--primary-color);
        border: 1px solid var(--primary-color);
        color: var(--text-white);
        border-radius: var(--radius-md);
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: var(--primary-hover);
        border-color: var(--primary-hover);
    }

    .btn-primary:disabled {
        background: var(--text-muted);
        border-color: var(--text-muted);
        cursor: not-allowed;
    }

    /* ========================================
       RESPONSIVIDADE - MOBILE FIRST
       ======================================== */

    @media (max-width: 767px) {
        .main-container {
            padding: 1rem;
        }

        .page-header {
            padding: 1.5rem;
            text-align: center;
        }

        .page-header h1 {
            font-size: var(--font-size-2xl);
        }

        .funcionarios-grid {
            grid-template-columns: 1fr;
        }

        .funcionario-detalhes {
            grid-template-columns: 1fr;
        }

        .tipos-registro-modal {
            grid-template-columns: 1fr;
        }

        .modal-dialog {
            margin: 1rem;
            max-width: calc(100% - 2rem);
        }
    }

    @media (min-width: 768px) and (max-width: 1023px) {
        .main-container {
            padding: 1.5rem;
        }

        .funcionarios-grid {
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        }
    }


    /* ========================================
       INDICADOR DE HORA ATUAL
       ======================================== */
    .hora-atual-indicador {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: var(--success-bg);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
        border: 1px solid var(--success-color);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .hora-atual-indicador i {
        color: var(--success-color);
        font-size: var(--font-size-lg);
    }

    .hora-atual-indicador strong {
        color: var(--success-text);
        font-weight: 600;
    }

    /* ========================================
       LOADING E OVERLAYS
       ======================================== */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-content {
        background: var(--card-background);
        padding: var(--spacing-xl);
        border-radius: var(--radius-lg);
        text-align: center;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .spinner-border {
        color: var(--primary-color);
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Header da Página -->
    <div class="page-header">
        <h1>
            <i class="fas fa-edit me-3"></i>
            {{ titulo }}
        </h1>
        <p>Registre pontos manualmente para funcionários</p>
    </div>

    <!-- Pesquisa -->
    <div class="pesquisa-section">
        <h5><i class="fas fa-search"></i> Buscar Funcionário</h5>
        <div class="pesquisa-input">
            <i class="fas fa-search"></i>
            <input type="text" id="pesquisa-funcionario" class="form-control"
                   placeholder="Digite nome, CPF, matrícula ou setor...">
        </div>
    </div>

    <!-- Grid de Funcionários -->
    <div class="funcionarios-grid" id="funcionarios-grid">
        {% for funcionario in funcionarios %}
        <div class="funcionario-card" data-nome="{{ funcionario.nome_completo|lower }}"
             data-cpf="{{ funcionario.cpf or '' }}"
             data-matricula="{{ funcionario.matricula or '' }}"
             data-setor="{{ funcionario.setor|lower if funcionario.setor else '' }}">

            <div class="funcionario-header">
                <div class="funcionario-foto">
                    {% if funcionario.foto %}
                        <img src="data:image/jpeg;base64,{{ funcionario.foto }}" alt="Foto">
                    {% else %}
                        <i class="fas fa-user"></i>
                    {% endif %}
                </div>
                <div class="funcionario-info">
                    <h5>{{ funcionario.nome_completo }}</h5>
                    <div class="subtitle">{{ funcionario.cargo or 'Cargo não informado' }}</div>
                    <div class="subtitle">{{ funcionario.empresa_nome or 'Empresa não informada' }}</div>
                </div>
            </div>

            <div class="funcionario-detalhes">
                <div class="detalhe-item">
                    <i class="fas fa-id-card"></i>
                    <span>{{ funcionario.matricula or 'N/A' }}</span>
                </div>
                <div class="detalhe-item">
                    <i class="fas fa-building"></i>
                    <span>{{ funcionario.setor or 'N/A' }}</span>
                </div>
                <div class="detalhe-item">
                    <i class="fas fa-phone"></i>
                    <span>{{ funcionario.telefone or 'N/A' }}</span>
                </div>
                <div class="detalhe-item">
                    <i class="fas fa-envelope"></i>
                    <span>{{ funcionario.email or 'N/A' }}</span>
                </div>
            </div>

            <div class="funcionario-actions">
                <button class="btn-registrar-funcionario" data-funcionario-id="{{ funcionario.id }}">
                    <i class="fas fa-clock"></i>
                    Registrar Ponto
                </button>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Estado Vazio -->
    <div class="no-results" id="no-results" style="display: none;">
        <i class="fas fa-search"></i>
        <h4>Nenhum funcionário encontrado</h4>
        <p>Tente ajustar os termos de busca para encontrar funcionários.</p>
    </div>
</div>

    /* Ajuste para dispositivos móveis */
    @media (max-width: 768px) {
        .funcionario-detalhes {
            grid-template-columns: 1fr;
        }

        .funcionarios-grid {
            grid-template-columns: 1fr;
        }

        .footer-info-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .modal-dialog {
            margin: 10px;
            max-width: calc(100% - 20px);
        }

        .modal-body {
            padding: 20px;
        }

        .tipos-registro-modal {
            grid-template-columns: 1fr;
        }
    }

    /* Destaque para setor do funcionário */
    .detalhe-item.setor-item {
        font-weight: 500;
        color: #495057;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Header da Página -->
    <div class="page-header">
        <h1>
            <i class="fas fa-edit me-3"></i>
            {{ titulo }}
        </h1>
        <p>Registre pontos manualmente para funcionários</p>
    </div>

    <!-- Pesquisa -->
    <div class="pesquisa-section">
        <h5><i class="fas fa-search"></i> Buscar Funcionário</h5>
        <div class="pesquisa-input">
            <i class="fas fa-search"></i>
            <input type="text" id="pesquisa-funcionario" class="form-control"
                   placeholder="Digite nome, CPF, matrícula ou setor...">
        </div>
    </div>

    <!-- Grid de Funcionários -->
    <div class="funcionarios-grid" id="funcionarios-grid">
        {% for funcionario in funcionarios %}
        <div class="funcionario-card"
             data-nome="{{ funcionario.nome_completo|lower }}"
             data-cpf="{{ funcionario.cpf or '' }}"
             data-matricula="{{ funcionario.matricula or '' }}"
             data-setor="{{ funcionario.setor|lower if funcionario.setor else '' }}"
             data-search="{{ (funcionario.nome_completo|lower + ' ' + (funcionario.cpf or '') + ' ' + (funcionario.matricula or '') + ' ' + (funcionario.setor|lower if funcionario.setor else '')) }}">

            <div class="funcionario-header">
                <div class="funcionario-foto">
                    {% if funcionario.foto %}
                        <img src="data:image/jpeg;base64,{{ funcionario.foto }}" alt="Foto">
                    {% else %}
                        <i class="fas fa-user"></i>
                    {% endif %}
                </div>
                <div class="funcionario-info">
                    <h5>{{ funcionario.nome_completo }}</h5>
                    <div class="subtitle">{{ funcionario.cargo or 'Cargo não informado' }}</div>
                    <div class="subtitle">{{ funcionario.empresa_nome or 'Empresa não informada' }}</div>
                </div>
            </div>

            <div class="funcionario-detalhes">
                <div class="detalhe-item">
                    <i class="fas fa-id-card"></i>
                    <span>{{ funcionario.matricula or 'N/A' }}</span>
                </div>
                <div class="detalhe-item">
                    <i class="fas fa-building"></i>
                    <span id="setor-{{ funcionario.id }}">{{ funcionario.setor or 'N/A' }}</span>
                </div>
                <div class="detalhe-item">
                    <i class="fas fa-phone"></i>
                    <span>{{ funcionario.telefone or 'N/A' }}</span>
                </div>
                <div class="detalhe-item">
                    <i class="fas fa-envelope"></i>
                    <span>{{ funcionario.email or 'N/A' }}</span>
                </div>
            </div>

            <div class="funcionario-actions">
                <button class="btn-registrar-funcionario" data-funcionario-id="{{ funcionario.id }}">
                    <i class="fas fa-clock"></i>
                    Registrar Ponto
                </button>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Estado Vazio -->
    <div class="no-results" id="no-results" style="display: none;">
        <i class="fas fa-search"></i>
        <h4>Nenhum funcionário encontrado</h4>
        <p>Tente ajustar os termos de busca para encontrar funcionários.</p>
    </div>
</div>

<!-- Modal de Registro -->
<div class="modal fade" id="registroModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> Registrar Ponto Manual
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Informações do Funcionário -->
                <div class="funcionario-modal-header" id="funcionario-modal-info">
                    <!-- Será preenchido via JavaScript -->
                </div>

                <!-- Formulário de Registro -->
                <form id="form-registro-manual">
                    <input type="hidden" id="funcionario-id" name="funcionario_id">

                    <!-- Tipos de Registro -->
                    <div class="mb-3">
                        <div class="hora-atual-indicador">
                            <i class="fas fa-clock"></i>
                            <div>Horário atual: <strong id="hora-atual-modal">{{ hora_atual }}</strong></div>
                        </div>

                        <label class="form-label"><strong>Tipo de Registro:</strong></label>
                        <div class="tipos-registro-modal" id="tipos-registro">
                            <!-- Será preenchido via JavaScript -->
                        </div>
                    </div>

                    <!-- Observações -->
                    <div class="mb-3">
                        <label for="observacoes" class="form-label">Observações (opcional):</label>
                        <textarea class="form-control" id="observacoes" name="observacoes" rows="3"
                                  placeholder="Digite observações sobre este registro..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="btn-confirmar-registro">
                    <i class="fas fa-save"></i> Registrar Ponto
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loading-overlay">
    <div class="loading-content">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Carregando...</span>
        </div>
        <div class="mt-2">Processando registro...</div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elementos da interface
    const pesquisaInput = document.getElementById('pesquisa-funcionario');
    const funcionariosGrid = document.getElementById('funcionarios-grid');
    const noResults = document.getElementById('no-results');
    const modalElement = document.getElementById('registroModal');
    const registroModal = new bootstrap.Modal(modalElement);
    const formRegistro = document.getElementById('form-registro-manual');
    const btnConfirmar = document.getElementById('btn-confirmar-registro');
    const loadingOverlay = document.getElementById('loading-overlay');

    let funcionarioSelecionado = null;
    let todosTipos = [];
    let registrosExistentes = [];

    // Atualizar hora atual
    setInterval(function() {
        const agora = new Date();
        const horaFormatada = agora.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
        });
        const horaElement = document.getElementById('hora-atual-modal');
        if (horaElement) {
            horaElement.textContent = horaFormatada;
        }
    }, 1000);

    // Pesquisa de funcionários
    if (pesquisaInput) {
        pesquisaInput.addEventListener('input', function() {
            const termo = this.value.toLowerCase().trim();
            const cards = funcionariosGrid.querySelectorAll('.funcionario-card');
            let resultados = 0;

            cards.forEach(card => {
                const searchData = card.dataset.search;
                const matches = searchData.includes(termo);

                card.style.display = matches ? 'block' : 'none';
                if (matches) resultados++;
            });

            // Mostrar/ocultar mensagem de "não encontrado"
            if (noResults) {
                noResults.style.display = resultados === 0 ? 'block' : 'none';
                funcionariosGrid.style.display = resultados === 0 ? 'none' : 'grid';
            }
        });
    }

    // Event listener para botões de registrar
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-registrar-funcionario') ||
            e.target.closest('.btn-registrar-funcionario')) {
            const btn = e.target.classList.contains('btn-registrar-funcionario') ?
                        e.target : e.target.closest('.btn-registrar-funcionario');
            const funcionarioId = btn.getAttribute('data-funcionario-id');
            if (funcionarioId) {
                abrirModalRegistro(funcionarioId);
            }
        }
    });

    // Função para abrir modal
    function abrirModalRegistro(funcionarioId) {
        funcionarioSelecionado = funcionarioId;

        // Buscar dados do funcionário e horários
        Promise.all([
            buscarDadosFuncionario(funcionarioId),
            buscarHorariosFuncionario(funcionarioId)
        ]).then(([funcionario, horarios]) => {
            preencherModalFuncionario(funcionario);
            registrosExistentes = horarios.registros_existentes || [];
            todosTipos = horarios.todos_tipos || [];
            preencherTiposRegistro(horarios.tipos_disponiveis || [], horarios.todos_tipos || [], horarios.hora_atual);
            registroModal.show();

            if (btnConfirmar) {
                btnConfirmar.disabled = false;
                btnConfirmar.innerHTML = '<i class="fas fa-save"></i> Registrar Ponto';
            }
        }).catch(error => {
            console.error('Erro ao carregar dados:', error);
            alert('Erro ao carregar dados do funcionário');
        });
    }

    function buscarDadosFuncionario(funcionarioId) {
        const card = document.querySelector(`[data-funcionario-id="${funcionarioId}"]`);
        if (card) {
            const fotoElement = card.querySelector('.funcionario-foto img');
            const foto = fotoElement ? fotoElement.src : '';
            const nome = card.querySelector('.funcionario-info h5')?.textContent || 'Nome não informado';
            const cargo = card.querySelector('.funcionario-info .subtitle')?.textContent || 'Cargo não informado';

            const setorElement = document.getElementById(`setor-${funcionarioId}`);
            const setor = setorElement ? setorElement.textContent.trim() : '';

            return Promise.resolve({
                id: funcionarioId,
                nome_completo: nome,
                cargo: cargo,
                setor: setor,
                foto_url: foto
            });
        }
        return Promise.reject('Funcionário não encontrado');
    }

    function buscarHorariosFuncionario(funcionarioId) {
        return fetch(`/registro-ponto/api/horarios-funcionario/${funcionarioId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    return data;
                } else {
                    throw new Error(data.message || 'Erro ao buscar horários');
                }
            })
            .catch(error => {
                console.error('Erro ao buscar horários:', error);
                const dadosPadrao = {
                    tipos_disponiveis: [
                        { tipo: 'entrada', nome: 'Entrada', icone: 'fa-sign-in-alt' },
                        { tipo: 'intervalo', nome: 'Saída para Intervalo', icone: 'fa-pause' },
                        { tipo: 'retorno', nome: 'Retorno do Intervalo', icone: 'fa-play' },
                        { tipo: 'saida', nome: 'Saída', icone: 'fa-sign-out-alt' }
                    ],
                    todos_tipos: [
                        { tipo: 'entrada', nome: 'Entrada', icone: 'fa-sign-in-alt' },
                        { tipo: 'intervalo', nome: 'Saída para Intervalo', icone: 'fa-pause' },
                        { tipo: 'retorno', nome: 'Retorno do Intervalo', icone: 'fa-play' },
                        { tipo: 'saida', nome: 'Saída', icone: 'fa-sign-out-alt' }
                    ],
                    registros_existentes: [],
                    hora_atual: new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
                };

                window.horariosFuncionario = dadosPadrao;
                return dadosPadrao;
            });
    }

    function preencherModalFuncionario(funcionario) {
        const modalInfo = document.getElementById('funcionario-modal-info');
        if (modalInfo) {
            let fotoHtml = '';
            if (funcionario.foto_url && funcionario.foto_url !== '/static/images/funcionario_sem_foto.svg') {
                fotoHtml = `
                    <div class="funcionario-modal-foto">
                        <img src="${funcionario.foto_url}" alt="Foto"
                             onload="this.style.display='block'"
                             onerror="this.style.display='none'; this.parentElement.innerHTML='<i class=\'fas fa-user\'></i>';">
                    </div>`;
            } else {
                fotoHtml = `
                    <div class="funcionario-modal-foto">
                        <i class="fas fa-user"></i>
                    </div>`;
            }

            modalInfo.innerHTML = `
                ${fotoHtml}
                <div class="funcionario-modal-info">
                    <h4>${funcionario.nome_completo}</h4>
                    <div class="text-muted">${funcionario.cargo}</div>
                    <div class="text-muted">${funcionario.setor || 'Setor não informado'}</div>
                </div>
            `;
        }

        // Preencher campo hidden
        const funcionarioIdInput = document.getElementById('funcionario-id');
        if (funcionarioIdInput) {
            funcionarioIdInput.value = funcionario.id;
        }
    }

    function preencherTiposRegistro(tiposDisponiveis, todosTipos, horaAtual) {
        const container = document.getElementById('tipos-registro');
        if (!container) return;

        container.innerHTML = '';

        if (tiposDisponiveis.length === 0) {
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Nenhum tipo de registro disponível no momento.
                </div>
            `;
            return;
        }

        tiposDisponiveis.forEach((tipo, index) => {
            const tipoHtml = `
                <div class="tipo-registro-opcao">
                    <input type="radio" id="tipo-${tipo.tipo}" name="tipo_registro" value="${tipo.tipo}" ${index === 0 ? 'checked' : ''}>
                    <label for="tipo-${tipo.tipo}">
                        <i class="fas ${tipo.icone}"></i>
                        <div><strong>${tipo.nome}</strong></div>
                        ${tipo.horario_previsto ? `<div class="horario-info">
                            <span class="badge bg-primary">${tipo.horario_previsto}</span>
                        </div>` : ''}
                    </label>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', tipoHtml);
        });
    }

    // Event listener para confirmar registro
    if (btnConfirmar) {
        btnConfirmar.addEventListener('click', confirmarRegistro);
    }

    function confirmarRegistro() {
        if (!funcionarioSelecionado) {
            alert('Nenhum funcionário selecionado!');
            return;
        }

        const tipoRegistroElement = document.querySelector('input[name="tipo_registro"]:checked');
        if (!tipoRegistroElement) {
            alert('Selecione o tipo de registro!');
            return;
        }

        const tipoRegistro = tipoRegistroElement.value;
        const observacoes = document.getElementById('observacoes').value.trim();

        btnConfirmar.disabled = true;
        btnConfirmar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';

        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }

        const formData = new FormData();
        formData.append('funcionario_id', funcionarioSelecionado);
        formData.append('tipo_registro', tipoRegistro);
        formData.append('observacoes', observacoes);

        fetch('/registro-ponto/api/registrar-manual', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarResultado('sucesso', data.message, data);
                setTimeout(() => {
                    registroModal.hide();
                    limparModal();
                    location.reload();
                }, 2000);
            } else {
                mostrarResultado('erro', data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            mostrarResultado('erro', 'Erro de comunicação: ' + error.message);
        })
        .finally(() => {
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
            btnConfirmar.disabled = false;
            btnConfirmar.innerHTML = '<i class="fas fa-save"></i> Registrar Ponto';
        });
    }

    function mostrarResultado(tipo, mensagem, dados = null) {
        const alertClass = tipo === 'sucesso' ? 'alert-success' : 'alert-danger';
        const icone = tipo === 'sucesso' ? 'fa-check-circle' : 'fa-exclamation-triangle';

        let detalhesHtml = '';
        if (dados && dados.detalhes) {
            detalhesHtml = `
                <div class="mt-2">
                    <strong>Detalhes do registro:</strong><br>
                    <small>${dados.detalhes}</small>
                </div>
            `;
        }

        const resultadoHtml = `
            <div class="alert ${alertClass} mt-3">
                <i class="fas ${icone}"></i>
                ${mensagem}
                ${detalhesHtml}
            </div>
        `;

        const modalBody = document.querySelector('#registroModal .modal-body');
        const alertExistente = modalBody.querySelector('.alert');
        if (alertExistente) {
            alertExistente.remove();
        }

        modalBody.insertAdjacentHTML('beforeend', resultadoHtml);
    }

    function limparModal() {
        const form = document.getElementById('form-registro-manual');
        if (form) {
            form.reset();
        }

        const modalBody = document.querySelector('#registroModal .modal-body');
        const alert = modalBody.querySelector('.alert');
        if (alert) {
            alert.remove();
        }

        const observacoes = document.getElementById('observacoes');
        if (observacoes) {
            observacoes.value = '';
        }

        funcionarioSelecionado = null;
    }
});
</script>
{% endblock %}