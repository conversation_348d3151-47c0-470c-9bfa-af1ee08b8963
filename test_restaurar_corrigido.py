#!/usr/bin/env python3
"""
Teste da função de restaurar corrigida
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from utils.database import DatabaseManager, FuncionarioQueries

def test_restaurar_funcionario():
    """Testa a função de restaurar funcionário corrigida"""
    print("🔍 TESTE: FUNÇÃO RESTAURAR FUNCIONÁRIO CORRIGIDA")
    print("=" * 60)
    
    try:
        # 1. Verificar funcionários desligados
        print("📋 1. VERIFICANDO FUNCIONÁRIOS DESLIGADOS:")
        db = DatabaseManager()
        funcionarios_desligados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa, data_desligamento
            FROM funcionarios_desligados 
            ORDER BY data_desligamento DESC
            LIMIT 3
        """)
        
        if funcionarios_desligados:
            print(f"   ✅ {len(funcionarios_desligados)} funcionários desligados encontrados:")
            for func in funcionarios_desligados:
                print(f"      - {func['nome_completo']} (ID: {func['funcionario_id_original']}, Matrícula: {func['matricula_empresa']})")
        else:
            print("   ❌ Nenhum funcionário desligado encontrado")
            return False
        
        # 2. Testar restauração do Richardson (que estava duplicado)
        richardson = None
        for func in funcionarios_desligados:
            if 'RICHARDSON' in func['nome_completo']:
                richardson = func
                break
        
        if not richardson:
            print("   ⚠️ Richardson não encontrado na lista de desligados")
            # Usar primeiro funcionário da lista
            richardson = funcionarios_desligados[0]
        
        print(f"\n📋 2. TESTANDO RESTAURAÇÃO DO FUNCIONÁRIO {richardson['nome_completo']}:")
        print(f"   ID Original: {richardson['funcionario_id_original']}")
        print(f"   Matrícula: {richardson['matricula_empresa']}")
        
        # Verificar se existe funcionário inativo com a mesma matrícula
        funcionario_inativo = db.execute_query("""
            SELECT id, ativo, status_cadastro 
            FROM funcionarios 
            WHERE matricula_empresa = %s
        """, (richardson['matricula_empresa'],))
        
        if funcionario_inativo:
            func_inativo = funcionario_inativo[0]
            print(f"   📋 Funcionário existente encontrado:")
            print(f"      ID: {func_inativo['id']}, Ativo: {func_inativo['ativo']}, Status: {func_inativo['status_cadastro']}")
        
        # Testar a função de restauração
        resultado = FuncionarioQueries.restaurar_funcionario(richardson['funcionario_id_original'])
        
        print(f"\n   📊 RESULTADO DA RESTAURAÇÃO:")
        print(f"   Success: {resultado['success']}")
        print(f"   Message: {resultado['message']}")
        
        if resultado['success']:
            print("   ✅ SUCESSO: Função de restauração funcionou!")
            
            # Verificar se funcionário foi restaurado corretamente
            funcionario_restaurado = db.execute_query("""
                SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
                FROM funcionarios 
                WHERE nome_completo = %s AND ativo = TRUE
            """, (richardson['nome_completo'],))
            
            if funcionario_restaurado:
                func_rest = funcionario_restaurado[0]
                print(f"   ✅ Funcionário restaurado encontrado:")
                print(f"      ID: {func_rest['id']}, Status: {func_rest['status_cadastro']}, Ativo: {func_rest['ativo']}")
                
                # Verificar se foi removido da tabela de desligados
                ainda_desligado = db.execute_query("""
                    SELECT COUNT(*) as total
                    FROM funcionarios_desligados 
                    WHERE funcionario_id_original = %s
                """, (richardson['funcionario_id_original'],))
                
                if ainda_desligado[0]['total'] == 0:
                    print(f"   ✅ Funcionário removido da tabela de desligados")
                else:
                    print(f"   ⚠️ Funcionário ainda está na tabela de desligados")
                
                return True
            else:
                print("   ❌ Funcionário não encontrado na tabela principal após restauração")
                return False
                
        else:
            print(f"   ❌ FALHA: {resultado['message']}")
            return False
        
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_via_interface():
    """Testa a restauração via interface web"""
    print("\n📋 3. TESTANDO VIA INTERFACE WEB:")
    
    try:
        # Configurar sessão
        session = requests.Session()
        retry_strategy = Retry(total=3, backoff_factor=1, status_forcelist=[429, 500, 502, 503, 504])
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        base_url = "http://************:5000"
        
        # Login
        login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=True)
        
        if login_response.status_code != 200:
            print("   ❌ Falha no login")
            return False
        
        print("   ✅ Login realizado")
        
        # Verificar se há funcionários desligados para testar
        db = DatabaseManager()
        funcionarios_desligados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa
            FROM funcionarios_desligados 
            ORDER BY data_desligamento DESC
            LIMIT 1
        """)
        
        if not funcionarios_desligados:
            print("   ⚠️ Não há funcionários desligados para testar via interface")
            return True
        
        funcionario_teste = funcionarios_desligados[0]
        funcionario_id = funcionario_teste['funcionario_id_original']
        
        print(f"   Testando restauração via interface do funcionário: {funcionario_teste['nome_completo']}")
        
        # Fazer requisição de restauração
        restaurar_response = session.post(f"{base_url}/funcionarios/restaurar/{funcionario_id}")
        
        print(f"   Status Code: {restaurar_response.status_code}")
        
        if restaurar_response.status_code == 200:
            print("   ✅ Requisição HTTP bem-sucedida")
            return True
        else:
            print(f"   ❌ Falha na requisição HTTP: {restaurar_response.status_code}")
            print(f"   Response: {restaurar_response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ ERRO: {e}")
        return False

if __name__ == "__main__":
    print("🎯 TESTE COMPLETO: FUNÇÃO RESTAURAR FUNCIONÁRIO CORRIGIDA")
    print("=" * 70)
    
    # Teste 1: Função direta
    teste1_ok = test_restaurar_funcionario()
    
    # Teste 2: Interface web
    teste2_ok = test_via_interface()
    
    print(f"\n📊 RESULTADOS:")
    print(f"   Função direta: {'✅ OK' if teste1_ok else '❌ FALHA'}")
    print(f"   Interface web: {'✅ OK' if teste2_ok else '❌ FALHA'}")
    
    if teste1_ok and teste2_ok:
        print("\n🎉 SUCESSO TOTAL!")
        print("✅ Função de restaurar funcionário corrigida e funcionando")
        print("✅ Interface web funcionando corretamente")
        print("✅ Problema de duplicação resolvido")
    else:
        print("\n❌ AINDA HÁ PROBLEMAS!")
        print("❌ Função de restaurar precisa de mais correções")
