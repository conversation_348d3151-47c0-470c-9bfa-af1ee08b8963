{% extends "base.html" %}

{% block title %}{{ funcionario.nome_completo }} - Detal<PERSON> de Ponto{% endblock %}

{% block extra_css %}
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <style>
        /* ========================================
           RLPONTO-WEB VISUAL IDENTITY - v1.0
           SEGUINDO ESPECIFICAÇÕES DO VISUAL.MD
           ======================================== */

        :root {
            /* Cor primária - Verde-azulado (igual sidebar) */
            --primary-color: #4fbdba;
            --primary-hover: #26a69a;
            --primary-light: #80cbc4;
            --primary-dark: #00695c;

            /* Backgrounds */
            --background-color: #f9fafb;        /* Background principal */
            --card-background: #ffffff;         /* Fundo de cards */
            --hover-bg: #f3f4f6;               /* Hover states */
            --sidebar-bg: #ffffff;             /* Sidebar background */

            /* Textos */
            --text-primary: #1f2937;           /* Texto principal (preto) */
            --text-secondary: #6b7280;         /* Texto secundário (cinza médio) */
            --text-muted: #9ca3af;             /* Texto desabilitado */
            --text-white: #ffffff;             /* Texto branco */

            /* Bordas e divisores */
            --border-color: #e5e7eb;           /* Bordas padrão */
            --border-light: #f3f4f6;           /* Bordas claras */
            --border-dark: #d1d5db;            /* Bordas escuras */

            /* Estados */
            --success-color: #10b981;          /* Verde sucesso */
            --success-bg: #dcfce7;             /* Background sucesso */
            --success-text: #166534;           /* Texto sucesso */

            --warning-color: #f59e0b;          /* Amarelo aviso */
            --warning-bg: #fef3c7;             /* Background aviso */
            --warning-text: #92400e;           /* Texto aviso */

            --danger-color: #ef4444;           /* Vermelho erro */
            --danger-bg: #fee2e2;              /* Background erro */
            --danger-text: #dc2626;            /* Texto erro */

            --info-color: #3b82f6;             /* Azul informação */
            --info-bg: #dbeafe;                /* Background info */
            --info-text: #1e40af;              /* Texto info */

            /* Tamanhos de fonte */
            --font-size-xs: 0.75rem;      /* 12px - Textos muito pequenos */
            --font-size-sm: 0.875rem;     /* 14px - Textos pequenos */
            --font-size-base: 1rem;       /* 16px - Texto base */
            --font-size-lg: 1.125rem;     /* 18px - Textos grandes */
            --font-size-xl: 1.25rem;      /* 20px - Subtítulos */
            --font-size-2xl: 1.5rem;      /* 24px - Títulos */
            --font-size-3xl: 1.875rem;    /* 30px - Títulos principais */

            /* Espaçamentos */
            --spacing-xs: 0.25rem;    /* 4px */
            --spacing-sm: 0.5rem;     /* 8px */
            --spacing-md: 1rem;       /* 16px */
            --spacing-lg: 1.5rem;     /* 24px */
            --spacing-xl: 2rem;       /* 32px */
            --spacing-2xl: 3rem;      /* 48px */

            /* Border radius */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
        }

        /* ========================================
           GLOBAL STYLES - PADRÃO RLPONTO-WEB
           ======================================== */

        body {
            background-color: var(--background-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-primary);
            line-height: 1.5;
        }

        .main-container {
            background-color: var(--background-color);
            min-height: 100vh;
            padding: 2rem;
        }

        /* ========================================
           HEADER PADRÃO - SEGUINDO VISUAL.MD
           ======================================== */

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            border-radius: var(--radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            color: var(--text-white);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 60%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: rotate(15deg);
        }

        .page-header h1 {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            position: relative;
            z-index: 2;
        }

        .page-header p {
            font-size: var(--font-size-lg);
            margin: 0;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        /* ========================================
           CARDS PADRÃO - SEGUINDO VISUAL.MD
           ======================================== */

        .info-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            margin-bottom: var(--spacing-lg);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            height: 180px;
            display: flex;
            flex-direction: column;
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border-color: var(--border-dark);
        }

        .info-card h5 {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding-bottom: var(--spacing-xs);
            border-bottom: 1px solid var(--border-light);
        }

        .info-card h5 i {
            color: var(--primary-color);
            font-size: 1.25rem;
        }

        /* ========================================
           LAYOUT DE INFORMAÇÕES MODERNIZADO
           ======================================== */
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem 2rem;
            flex: 1;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .info-label {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 0;
        }

        .info-value {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
            line-height: 1.2;
        }

        .status-item {
            grid-column: 1 / -1;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-active i {
            color: #22c55e;
            font-size: 0.5rem;
        }

        .status-inactive {
            background-color: #fef2f2;
            color: #991b1b;
        }

        .status-inactive i {
            color: #ef4444;
            font-size: 0.5rem;
        }

        /* ========================================
           RESUMO MODERNO EM GRADE - INSPIRADO SHADCN
           ======================================== */
        .modern-summary-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            overflow: hidden;
        }

        .summary-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.25rem;
            border-bottom: 1px solid #f3f4f6;
            background: #fafafa;
            font-weight: 600;
            font-size: 0.875rem;
            color: #374151;
        }

        .summary-header i {
            color: #6366f1;
            font-size: 1rem;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            padding: 0.75rem;
        }

        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 0.5rem;
            text-align: center;
            border-radius: 8px;
            margin: 0.125rem;
            background: #fafafa;
            border: 1px solid #f3f4f6;
            transition: all 0.2s ease;
        }

        .summary-item:hover {
            background: #f9fafb;
            border-color: #e5e7eb;
            transform: translateY(-1px);
        }

        .summary-item.span-full {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border: none;
        }

        .summary-item.span-full:hover {
            background: linear-gradient(135deg, #5b5bd6 0%, #7c3aed 100%);
        }

        .summary-value {
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .summary-label {
            font-size: 0.625rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            opacity: 0.8;
            line-height: 1;
        }

        /* Cores por tipo */
        .summary-item.primary .summary-value { color: #6366f1; }
        .summary-item.success .summary-value { color: #10b981; }
        .summary-item.warning .summary-value { color: #f59e0b; }
        .summary-item.danger .summary-value { color: #ef4444; }
        .summary-item.info .summary-value { color: #06b6d4; }
        .summary-item.secondary .summary-value { color: #6b7280; }
        .summary-item.accent .summary-value { color: #8b5cf6; }

        .summary-item.span-full .summary-value,
        .summary-item.span-full .summary-label {
            color: white;
        }

        /* ========================================
           ALINHAMENTO DOS CARDS DE INFORMAÇÕES
           ======================================== */
        .row.mb-4 {
            display: flex;
            align-items: stretch;
        }

        .row.mb-4 > [class*="col-"] {
            display: flex;
            flex-direction: column;
        }

        .row.mb-4 .info-card {
            flex: 1;
            height: 100%;
            margin-bottom: 0;
            display: flex;
            flex-direction: column;
        }

        .row.mb-4 .info-card > div:last-child {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* Alinhamento específico para o card de resumo */
        .info-card .text-center {
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 100%;
        }

        /* ========================================
           TABELA PADRÃO - SEGUINDO VISUAL.MD
           ======================================== */

        .registros-table {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            margin-bottom: var(--spacing-xl);
        }

        .registros-table .card-header {
            background: var(--hover-bg);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-lg);
        }

        .registros-table .card-header h5 {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        /* ========================================
           TABELA - ESTILOS PADRÃO RLPONTO-WEB
           ======================================== */

        .table {
            margin: 0;
        }

        .table th {
            background-color: var(--hover-bg);
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: var(--font-size-xs);
            letter-spacing: 0.5px;
            padding: var(--spacing-md);
            color: var(--text-secondary);
        }

        .table td {
            border: none;
            padding: var(--spacing-md);
            vertical-align: middle;
            border-bottom: 1px solid var(--border-light);
        }

        .table tbody tr:hover {
            background-color: var(--hover-bg);
        }

        /* ========================================
           HORÁRIOS - CÉLULAS INTERATIVAS
           ======================================== */

        .horario-cell {
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: var(--radius-sm);
            padding: 0.25rem 0.5rem;
        }

        .horario-cell:hover {
            background-color: var(--hover-bg);
        }

        .horario-editavel {
            background-color: var(--warning-bg);
            border: 1px dashed var(--warning-color);
        }

        /* ========================================
           BADGES DE STATUS - PADRÃO RLPONTO-WEB
           ======================================== */

        .badge-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: var(--font-size-xs);
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-success {
            background: var(--success-bg);
            color: var(--success-text);
            border: 1px solid var(--success-color);
        }

        .status-warning {
            background: var(--warning-bg);
            color: var(--warning-text);
            border: 1px solid var(--warning-color);
        }

        .status-danger {
            background: var(--danger-bg);
            color: var(--danger-text);
            border: 1px solid var(--danger-color);
        }

        .status-info {
            background: var(--info-bg);
            color: var(--info-text);
            border: 1px solid var(--info-color);
        }

        /* ========================================
           MODAIS - PADRÃO RLPONTO-WEB
           ======================================== */

        .modal-content {
            border-radius: var(--radius-lg);
            border: none;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: var(--text-white);
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
            padding: var(--spacing-lg);
        }

        .modal-title {
            font-weight: 600;
            font-size: var(--font-size-xl);
            margin: 0;
        }

        .modal-body {
            padding: var(--spacing-lg);
        }

        .modal-footer {
            padding: var(--spacing-lg);
            border-top: 1px solid var(--border-color);
        }

        /* ========================================
           CORREÇÕES ESPECÍFICAS PARA MODAL
           ======================================== */
        .modal-xl {
            max-width: 90%;
        }

        .modal-body .info-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            height: auto !important;
            display: block !important;
            flex-direction: initial !important;
        }

        .modal-body .info-card h5 {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .modal-body .badge-status {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .modal-body .status-info {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .modal-body .status-success {
            background-color: #dcfce7;
            color: #166534;
        }

        .modal-body .status-warning {
            background-color: #fef3c7;
            color: #92400e;
        }

        .modal-body .btn-primary {
            background: var(--primary-color);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-sm);
            font-weight: 500;
            transition: all 0.2s;
        }

        .modal-body .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }

        .modal-body .btn-secondary {
            background: #6b7280;
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-sm);
            font-weight: 500;
            transition: all 0.2s;
        }

        .modal-body .btn-secondary:hover {
            background: #4b5563;
        }

        /* ========================================
           CORREÇÕES ESPECÍFICAS PARA ELEMENTOS DO MODAL
           ======================================== */

        /* Resetar estilos conflitantes no modal */
        .modal-body .row.mb-4 {
            display: block !important;
            align-items: initial !important;
        }

        .modal-body .row.mb-4 > [class*="col-"] {
            display: block !important;
            flex-direction: initial !important;
        }

        .modal-body .row.mb-4 .info-card {
            height: auto !important;
            flex: none !important;
            margin-bottom: 1.5rem !important;
            display: block !important;
            flex-direction: initial !important;
        }

        .modal-body .row.mb-4 .info-card > div:last-child {
            flex: none !important;
            display: block !important;
            flex-direction: initial !important;
            justify-content: initial !important;
        }

        .modal-body .info-card .text-center {
            display: block !important;
            flex-direction: initial !important;
            justify-content: initial !important;
            height: auto !important;
        }

        /* Grid de horários no modal */
        .modal-body .row.text-center {
            margin-bottom: 1rem;
        }

        .modal-body .row.text-center .col-md-2,
        .modal-body .row.text-center .col-md-4 {
            margin-bottom: 1rem;
        }

        .modal-body .p-3.border.rounded {
            padding: 1rem !important;
            border: 1px solid var(--border-color) !important;
            border-radius: var(--radius-sm) !important;
            background: var(--card-background) !important;
        }

        /* Campos de formulário no modal */
        .modal-body .form-control {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            padding: 0.75rem;
            font-size: 0.875rem;
            background: var(--card-background);
            color: var(--text-primary);
        }

        .modal-body .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(79, 189, 186, 0.25);
        }

        .modal-body .form-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        /* Área de justificativa */
        .modal-body .position-relative {
            position: relative !important;
        }

        .modal-body .position-absolute {
            position: absolute !important;
        }

        /* Badges no modal */
        .modal-body .d-flex.gap-2 {
            gap: 0.5rem !important;
        }

        .modal-body .d-flex.align-items-center.justify-content-between {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            margin-bottom: 1rem !important;
        }

        /* Correções específicas para o modal de edição */
        #modalEdicaoRegistro .modal-body {
            padding: 2rem;
        }

        #modalEdicaoRegistro .info-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            height: auto !important;
            display: block !important;
        }

        #modalEdicaoRegistro .info-card h5 {
            color: #1f2937;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        #modalEdicaoRegistro .row {
            margin-bottom: 1rem;
        }

        #modalEdicaoRegistro .col-md-2,
        #modalEdicaoRegistro .col-md-3,
        #modalEdicaoRegistro .col-md-4,
        #modalEdicaoRegistro .col-md-6 {
            margin-bottom: 1rem;
        }

        #modalEdicaoRegistro .p-3.border.rounded {
            padding: 1rem !important;
            border: 1px solid #e5e7eb !important;
            border-radius: 6px !important;
            background: #f9fafb !important;
            text-align: center;
        }

        #modalEdicaoRegistro .fw-bold.h4,
        #modalEdicaoRegistro .fw-bold.h3 {
            margin: 0;
            color: #1f2937;
        }

        #modalEdicaoRegistro .text-muted.small {
            color: #6b7280 !important;
            font-size: 0.75rem;
        }

        /* ========================================
           GRID DE HORÁRIOS COMPACTO NO MODAL
           ======================================== */

        #modalEdicaoRegistro .horarios-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        #modalEdicaoRegistro .horario-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            transition: all 0.2s ease;
        }

        #modalEdicaoRegistro .horario-item:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }

        #modalEdicaoRegistro .horario-item.total-item {
            background: linear-gradient(135deg, #4fbdba 0%, #3b9b98 100%);
            color: white;
            border-color: #4fbdba;
        }

        #modalEdicaoRegistro .horario-label {
            font-size: 0.75rem;
            font-weight: 500;
            color: #64748b;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        #modalEdicaoRegistro .total-item .horario-label {
            color: rgba(255, 255, 255, 0.9);
        }

        #modalEdicaoRegistro .horario-valor {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            line-height: 1.2;
        }

        #modalEdicaoRegistro .total-item .horario-valor {
            color: white;
            font-size: 1.75rem;
        }

        #modalEdicaoRegistro .horario-sublabel {
            font-size: 0.7rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 0.25rem;
            font-weight: 400;
        }

        /* Grid responsivo para horários */
        @media (max-width: 768px) {
            #modalEdicaoRegistro .horarios-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.75rem;
            }

            #modalEdicaoRegistro .horario-item {
                padding: 0.75rem;
            }

            #modalEdicaoRegistro .horario-valor {
                font-size: 1.25rem;
            }

            #modalEdicaoRegistro .total-item .horario-valor {
                font-size: 1.5rem;
            }
        }

        /* ========================================
           GRID DE EDIÇÃO DE HORÁRIOS COMPACTO
           ======================================== */

        #modalEdicaoRegistro .edicao-horarios-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        #modalEdicaoRegistro .edicao-horario-item {
            display: flex;
            flex-direction: column;
        }

        #modalEdicaoRegistro .edicao-horario-item .form-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        #modalEdicaoRegistro .edicao-horario-item .form-control {
            text-align: center;
            font-weight: 600;
            font-size: 1rem;
            padding: 0.75rem 0.5rem;
        }

        @media (max-width: 768px) {
            #modalEdicaoRegistro .edicao-horarios-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.75rem;
            }
        }



        /* ========================================
           FORMULÁRIOS - PADRÃO RLPONTO-WEB
           ======================================== */

        .form-control {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            padding: 0.5rem 0.75rem;
            background: var(--card-background);
            color: var(--text-primary);
            font-size: var(--font-size-base);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
            outline: none;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            margin-bottom: 0.5rem;
            display: block;
        }

        /* ========================================
           TIMELINE - LOGS DE ATIVIDADES
           ======================================== */

        .logs-timeline {
            max-height: 400px;
            overflow-y: auto;
        }

        .timeline-item {
            border-left: 2px solid var(--border-color);
            padding-left: 1rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--primary-color);
        }

        /* ========================================
           UPLOAD AREA - DRAG & DROP
           ======================================== */

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-md);
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background-color: rgba(79, 189, 186, 0.05);
        }

        .upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(79, 189, 186, 0.1);
        }

        /* ========================================
           BOTÕES - PADRÃO RLPONTO-WEB
           ======================================== */

        .btn-primary {
            background: var(--primary-color);
            border: 1px solid var(--primary-color);
            color: var(--text-white);
            border-radius: var(--radius-md);
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: var(--radius-md);
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: var(--hover-bg);
            border-color: var(--border-dark);
        }

        .btn-light {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: var(--radius-md);
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-light:hover {
            background: var(--hover-bg);
            border-color: var(--border-dark);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
        }

        /* ========================================
           RESPONSIVIDADE - MOBILE FIRST
           ======================================== */

        @media (max-width: 767px) {
            .main-container {
                padding: 1rem;
            }

            .page-header {
                padding: 1.5rem;
                text-align: center;
            }

            .page-header h1 {
                font-size: var(--font-size-2xl);
            }

            .info-card {
                margin-bottom: 1rem;
            }

            .table th,
            .table td {
                padding: 0.5rem;
                font-size: var(--font-size-sm);
            }

            /* Em mobile, desabilitar alinhamento flexível para empilhamento vertical */
            .row.mb-4 {
                display: block;
            }

            .row.mb-4 > [class*="col-"] {
                display: block;
            }

            .row.mb-4 .info-card {
                height: auto;
                margin-bottom: var(--spacing-lg);
            }

            /* Layout responsivo para informações */
            .info-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .status-item {
                grid-column: 1;
            }

            .summary-grid {
                gap: 0.25rem;
                padding: 0.5rem;
            }

            .summary-item {
                padding: 0.5rem 0.25rem;
                margin: 0.0625rem;
            }

            .summary-value {
                font-size: 1.25rem;
            }

            .summary-label {
                font-size: 0.5rem;
            }

            .summary-header {
                padding: 0.75rem 1rem;
                font-size: 0.8rem;
            }
        }

        @media (min-width: 768px) and (max-width: 1023px) {
            .main-container {
                padding: 1.5rem;
            }
        }

        /* ========================================
           FINALIZAÇÕES - PADRÃO RLPONTO-WEB
           ======================================== */

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Garantir que os ícones tenham a cor primária */
        .fa-user, .fa-clock, .fa-building, .fa-chart-bar {
            color: var(--primary-color);
        }

        /* Melhorar legibilidade dos textos */
        .text-muted {
            color: var(--text-muted) !important;
        }

        .text-primary {
            color: var(--text-primary) !important;
        }

        .text-secondary {
            color: var(--text-secondary) !important;
        }

        .btn-light:hover {
            background: rgba(255, 255, 255, 0.3);
            color: var(--text-white);
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            border: 1px solid var(--primary-color);
            color: var(--text-white);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            color: var(--text-white);
        }

        .btn-sm {
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-xs);
        }

        /* ========================================
           BADGES MODERNOS
           ======================================== */
        .badge {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-success {
            background: var(--success-bg);
            color: var(--success-text);
            border: 1px solid var(--success-color);
        }

        .badge-warning {
            background: var(--warning-bg);
            color: var(--warning-text);
            border: 1px solid var(--warning-color);
        }

        .badge-danger {
            background: var(--danger-bg);
            color: var(--danger-text);
            border: 1px solid var(--danger-color);
        }

        .badge-info {
            background: var(--info-bg);
            color: var(--info-text);
            border: 1px solid var(--info-color);
        }

        /* ========================================
           HISTÓRICO DE ATIVIDADES PROFISSIONAL
           ======================================== */
        .historico-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            margin-top: 24px;
        }

        .historico-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f3f4f6;
        }

        .historico-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .historico-counter {
            background: #f3f4f6;
            color: #6b7280;
            font-size: 12px;
            font-weight: 500;
            padding: 4px 8px;
            border-radius: 12px;
            margin-left: auto;
        }

        .activity-timeline {
            max-height: 450px;
            overflow-y: auto;
            padding-right: 8px;
        }

        .activity-item {
            display: flex;
            gap: 16px;
            padding: 16px 0;
            border-bottom: 1px solid #f9fafb;
            position: relative;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
        }

        .activity-icon.justificativa { background: #dbeafe; color: #1d4ed8; }
        .activity-icon.empresa { background: #fef3c7; color: #d97706; }
        .activity-icon.jornada { background: #d1fae5; color: #059669; }
        .activity-icon.sistema { background: #f3e8ff; color: #7c3aed; }
        .activity-icon.aprovacao { background: #dcfce7; color: #16a34a; }
        .activity-icon.reprovacao { background: #fee2e2; color: #dc2626; }

        .activity-content {
            flex: 1;
            min-width: 0;
        }

        .activity-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .activity-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
            line-height: 1.4;
        }

        .activity-time {
            font-size: 12px;
            color: #6b7280;
            white-space: nowrap;
            margin-left: 12px;
        }

        .activity-description {
            font-size: 13px;
            color: #4b5563;
            line-height: 1.5;
            margin: 0 0 8px 0;
            word-wrap: break-word;
        }

        .activity-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 12px;
            color: #6b7280;
        }

        .activity-user {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .activity-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-aprovado { background: #dcfce7; color: #166534; }
        .status-reprovado { background: #fee2e2; color: #991b1b; }
        .status-pendente { background: #fef3c7; color: #92400e; }

        .activity-timeline::-webkit-scrollbar {
            width: 6px;
        }

        .activity-timeline::-webkit-scrollbar-track {
            background: #f9fafb;
            border-radius: 3px;
        }

        .activity-timeline::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
        }

        .activity-timeline::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        .empty-state {
            text-align: center;
            padding: 48px 24px;
            color: #6b7280;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state-text {
            font-size: 14px;
            margin: 0;
        }

        /* ========================================
           RESPONSIVIDADE APRIMORADA
           ======================================== */
        @media (max-width: 1200px) {
            .container-fluid {
                padding: var(--spacing-lg);
            }

            .display-6 {
                font-size: 2rem;
            }
        }

        @media (max-width: 992px) {
            .page-header {
                padding: var(--spacing-lg);
            }

            .page-header h1 {
                font-size: var(--font-size-2xl);
            }

            .info-card {
                margin-bottom: var(--spacing-md);
            }
        }

        @media (max-width: 768px) {
            .container-fluid {
                padding: var(--spacing-md);
            }

            .page-header {
                text-align: center;
                padding: var(--spacing-md);
            }

            .page-header .row {
                flex-direction: column;
                gap: var(--spacing-md);
            }

            .page-header .col-md-4 {
                text-align: center;
            }

            .btn {
                font-size: var(--font-size-sm);
                padding: var(--spacing-sm) var(--spacing-md);
            }

            .table-responsive {
                font-size: var(--font-size-sm);
            }

            .display-6 {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 576px) {
            .page-header h1 {
                font-size: var(--font-size-xl);
            }

            .info-card {
                padding: var(--spacing-md);
            }

            .table th,
            .table td {
                padding: var(--spacing-sm);
                font-size: var(--font-size-xs);
            }

            .btn {
                width: 100%;
                margin-bottom: var(--spacing-sm);
            }

            .btn:last-child {
                margin-bottom: 0;
            }
        }

        /* ========================================
           ANIMAÇÕES E TRANSIÇÕES
           ======================================== */
        .info-card,
        .registros-table,
        .historico-card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .table tbody tr {
            transition: background-color 0.2s ease;
        }

        .horario-cell {
            transition: all 0.2s ease;
        }

        .badge {
            transition: all 0.2s ease;
        }

        /* ========================================
           CONTROLES DA TABELA MODERNIZADOS
           ======================================== */
        .table-controls-container {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
        }

        /* Seção de Filtro de Período */
        .date-filter-section {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .date-filter-header {
            display: flex;
            align-items: center;
            color: var(--text-primary);
            font-weight: 600;
            font-size: var(--font-size-base);
        }

        .filter-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .date-range-inputs {
            display: flex;
            align-items: end;
            gap: var(--spacing-md);
            flex-wrap: wrap;
        }

        .date-input-group {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            min-width: 140px;
        }

        .date-input-label {
            font-size: var(--font-size-xs);
            color: var(--text-muted);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .date-input {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 0.5rem 0.75rem;
            background: var(--card-background);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            transition: all 0.3s ease;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .date-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
            outline: none;
            transform: translateY(-1px);
        }

        .date-separator {
            color: var(--primary-color);
            font-size: 1rem;
            opacity: 0.7;
            margin-bottom: 0.5rem;
        }

        /* Botões de Período Rápido */
        .quick-period-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .quick-btn {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            border-radius: var(--radius-sm);
            padding: 0.375rem 0.75rem;
            font-size: var(--font-size-xs);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .quick-btn:hover,
        .quick-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--text-white);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px -1px rgba(79, 189, 186, 0.3);
        }

        /* Seção de Botões de Ação */
        .action-buttons-section {
            display: flex;
            gap: var(--spacing-sm);
            flex-wrap: wrap;
            justify-content: flex-end;
        }

        .btn-action {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            font-weight: 500;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            white-space: nowrap;
            min-width: fit-content;
        }

        .btn-print {
            background: var(--primary-color);
            color: var(--text-white);
            box-shadow: 0 2px 4px -1px rgba(79, 189, 186, 0.3);
        }

        .btn-print:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(79, 189, 186, 0.4);
        }

        .btn-export {
            background: var(--success-color);
            color: var(--text-white);
            box-shadow: 0 2px 4px -1px rgba(16, 185, 129, 0.3);
        }

        .btn-export:hover {
            background: #059669;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.4);
        }

        .btn-filter {
            background: var(--info-color);
            color: var(--text-white);
            box-shadow: 0 2px 4px -1px rgba(59, 130, 246, 0.3);
        }

        .btn-filter:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.4);
        }

        /* Responsividade dos Controles Modernos */
        @media (max-width: 768px) {
            .table-controls-container {
                padding: var(--spacing-md);
                gap: var(--spacing-md);
            }

            .date-range-inputs {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-sm);
            }

            .date-input-group {
                min-width: 100%;
            }

            .date-separator {
                transform: rotate(90deg);
                align-self: center;
                margin: 0.25rem 0;
            }

            .quick-period-buttons {
                justify-content: center;
            }

            .action-buttons-section {
                justify-content: center;
                flex-direction: column;
            }

            .btn-action {
                width: 100%;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .quick-period-buttons {
                flex-direction: column;
            }

            .quick-btn {
                width: 100%;
                text-align: center;
            }
        }

        /* ========================================
           MELHORIAS DE ACESSIBILIDADE
           ======================================== */
        .btn:focus,
        .form-control:focus,
        .date-input:focus,
        .quick-btn:focus,
        .btn-action:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .table th {
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* ========================================
           ESTILOS PARA STATUS DE JUSTIFICATIVA
           ======================================== */
        .justificativa-status-cell {
            text-align: center;
            vertical-align: middle;
        }

        .justificativa-abonado {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background-color: rgba(34, 197, 94, 0.08); /* Verde bem clarinho */
            color: #166534;
            border: 1px solid rgba(34, 197, 94, 0.15);
        }

        .justificativa-reprovado {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background-color: rgba(239, 68, 68, 0.08); /* Vermelho bem clarinho */
            color: #991b1b;
            border: 1px solid rgba(239, 68, 68, 0.15);
        }

        .justificativa-pendente {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background-color: rgba(251, 191, 36, 0.08); /* Amarelo bem clarinho */
            color: #92400e;
            border: 1px solid rgba(251, 191, 36, 0.15);
        }

        .justificativa-vazio {
            color: var(--text-muted);
            font-size: 0.875rem;
            font-style: italic;
        }
    </style>
{% endblock %}

{% block content %}
    <!-- Container Principal Moderno -->
    <div class="container-fluid">
        <!-- Header Moderno - Inspirado Shadcn UI -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-user me-3"></i>
                        {{ funcionario.nome_completo }}
                    </h1>
                    <p class="mb-0 opacity-90">
                        {{ funcionario.cargo }} - {{ funcionario.empresa_nome }}
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light" onclick="window.history.back()" title="Voltar para página anterior">
                        <i class="fas fa-arrow-left me-2"></i>Voltar
                    </button>
                </div>
            </div>
        </div>

        <!-- Cards de Informações Modernos -->
        <div class="row mb-4">
            <!-- Informações Pessoais -->
            <div class="col-lg-8">
                <div class="info-card">
                    <h5>
                        <i class="fas fa-user-circle text-primary"></i>
                        Informações Pessoais
                    </h5>
                    <div class="info-grid">
                        <div class="info-item">
                            <label class="info-label">CPF</label>
                            <div class="info-value">{{ funcionario.cpf or '711.256.042-04' }}</div>
                        </div>

                        <div class="info-item">
                            <label class="info-label">Data de Admissão</label>
                            <div class="info-value">
                                {% if funcionario.data_admissao %}
                                    {{ funcionario.data_admissao.strftime('%d/%m/%Y') }}
                                {% else %}
                                    01/01/2024
                                {% endif %}
                            </div>
                        </div>

                        <div class="info-item">
                            <label class="info-label">Setor</label>
                            <div class="info-value">{{ funcionario.setor or 'Administrativo' }}</div>
                        </div>

                        <div class="info-item">
                            <label class="info-label">Jornada de Trabalho</label>
                            <div class="info-value">{{ funcionario.nome_jornada or 'Não definida' }}</div>
                        </div>

                        <div class="info-item status-item">
                            <label class="info-label">Status</label>
                            <div class="info-value">
                                {% if funcionario.status_cadastro == 'Ativo' %}
                                    <span class="status-badge status-active">
                                        <i class="fas fa-circle"></i>ATIVO
                                    </span>
                                {% else %}
                                    <span class="status-badge status-inactive">
                                        <i class="fas fa-circle"></i>{{ funcionario.status_cadastro }}
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resumo Estatístico Moderno -->
            <div class="col-lg-4">
                <div class="modern-summary-card">
                    <div class="summary-header">
                        <i class="fas fa-chart-line"></i>
                        <span>Resumo do Período</span>
                    </div>

                    <div class="summary-grid">
                        <!-- Linha 1: Registros e Dias -->
                        <div class="summary-item primary">
                            <div class="summary-value">
                                {% if registros and registros[0].total_batidas_periodo %}
                                    {{ registros[0].total_batidas_periodo }}
                                {% else %}
                                    {{ registros|length }}
                                {% endif %}
                            </div>
                            <div class="summary-label">Registros</div>
                        </div>

                        <div class="summary-item secondary">
                            <div class="summary-value">
                                {% set presentes = registros|selectattr('status_presenca', 'equalto', 'PRESENTE')|list|length %}
                                {{ presentes }}
                            </div>
                            <div class="summary-label">Dias Presentes</div>
                        </div>

                        <!-- Linha 2: Horas Normais e Extras -->
                        <div class="summary-item success">
                            <div class="summary-value">
                                {% if registros and registros[0].total_horas_normais_periodo is defined %}
                                    {% set horas_decimais = registros[0].total_horas_normais_periodo %}
                                    {% set segundos_totais = (horas_decimais * 3600)|round|int %}
                                    {% set horas = (segundos_totais // 3600)|int %}
                                    {% set minutos = ((segundos_totais % 3600) // 60)|int %}
                                    {{ horas }}:{{ "%02d"|format(minutos) }}
                                {% else %}
                                    {% set ns = namespace(total_segundos=0) %}
                                    {% for registro in registros %}
                                        {% if registro.horas_trabalhadas and ':' in registro.horas_trabalhadas %}
                                            {% set partes = registro.horas_trabalhadas.split(':') %}
                                            {% set horas = partes[0]|int %}
                                            {% set minutos = partes[1]|int %}
                                            {% set segundos_registro = (horas * 3600) + (minutos * 60) %}
                                            {% set ns.total_segundos = ns.total_segundos + segundos_registro %}
                                        {% elif registro.total_horas_decimal %}
                                            {% set segundos_registro = (registro.total_horas_decimal * 3600)|round|int %}
                                            {% set ns.total_segundos = ns.total_segundos + segundos_registro %}
                                        {% elif registro.total_horas_dia %}
                                            {% set segundos_registro = (registro.total_horas_dia * 3600)|round|int %}
                                            {% set ns.total_segundos = ns.total_segundos + segundos_registro %}
                                        {% endif %}
                                    {% endfor %}
                                    {% set horas_totais = (ns.total_segundos // 3600)|int %}
                                    {% set minutos_totais = ((ns.total_segundos % 3600) // 60)|int %}
                                    {{ horas_totais }}:{{ "%02d"|format(minutos_totais) }}
                                {% endif %}
                            </div>
                            <div class="summary-label">Horas Normais</div>
                        </div>

                        <div class="summary-item warning">
                            <div class="summary-value">
                                {% if registros and registros[0].total_horas_extras_periodo is defined %}
                                    {% set horas_decimais = registros[0].total_horas_extras_periodo %}
                                    {% if horas_decimais > 0 %}
                                        {% set segundos_totais = (horas_decimais * 3600)|round|int %}
                                        {% set horas = (segundos_totais // 3600)|int %}
                                        {% set minutos = ((segundos_totais % 3600) // 60)|int %}
                                        {{ horas }}:{{ "%02d"|format(minutos) }}
                                    {% else %}
                                        00:00
                                    {% endif %}
                                {% else %}
                                    00:00
                                {% endif %}
                            </div>
                            <div class="summary-label">Horas Extras</div>
                        </div>

                        <!-- Linha 3: Horas Negativas e Limite Diário -->
                        <div class="summary-item danger">
                            <div class="summary-value">
                                {% if registros and registros[0].total_horas_negativas_periodo is defined %}
                                    {% set horas_decimais = registros[0].total_horas_negativas_periodo %}
                                    {% if horas_decimais > 0 %}
                                        {% set segundos_totais = (horas_decimais * 3600)|round|int %}
                                        {% set horas = (segundos_totais // 3600)|int %}
                                        {% set minutos = ((segundos_totais % 3600) // 60)|int %}
                                        -{{ horas }}:{{ "%02d"|format(minutos) }}
                                    {% else %}
                                        00:00
                                    {% endif %}
                                {% else %}
                                    00:00
                                {% endif %}
                            </div>
                            <div class="summary-label">Horas Negativas</div>
                        </div>

                        <div class="summary-item info">
                            <div class="summary-value">9h</div>
                            <div class="summary-label">Limite Diário</div>
                        </div>

                        <!-- Linha 4: Horas Semanais (span completo) -->
                        <div class="summary-item accent span-full">
                            <div class="summary-value">
                                {{ "%.0f"|format(funcionario.horas_semanais_obrigatorias or 44) }}h
                            </div>
                            <div class="summary-label">Meta Semanal</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>





        <!-- Tabela de Registros Moderna -->
        <div class="registros-table">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock me-2"></i>
                        <h5 class="mb-0">Registros de Ponto</h5>
                    </div>

                    <!-- Controles da Tabela Modernizados -->
                    <div class="table-controls-container">
                        <!-- Filtro de Período -->
                        <div class="date-filter-section">
                            <div class="date-filter-header">
                                <i class="fas fa-calendar-alt me-2"></i>
                                <span class="filter-label">Período:</span>
                            </div>
                            <div class="date-range-inputs">
                                <div class="date-input-group">
                                    <label class="date-input-label">De:</label>
                                    <input type="date"
                                           class="form-control date-input"
                                           id="dataInicio"
                                           value="{{ (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d') }}">
                                </div>
                                <div class="date-separator">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                                <div class="date-input-group">
                                    <label class="date-input-label">Até:</label>
                                    <input type="date"
                                           class="form-control date-input"
                                           id="dataFim"
                                           value="{{ datetime.now().strftime('%Y-%m-%d') }}">
                                </div>
                            </div>
                            <!-- Botões de Período Rápido -->
                            <div class="quick-period-buttons">
                                <button type="button" class="quick-btn" onclick="setPeriodoRapido('hoje')">Hoje</button>
                                <button type="button" class="quick-btn" onclick="setPeriodoRapido('semana')">7 dias</button>
                                <button type="button" class="quick-btn" onclick="setPeriodoRapido('mes')">30 dias</button>
                                <button type="button" class="quick-btn" onclick="setPeriodoRapido('todos')">Todos</button>
                            </div>
                        </div>

                        <!-- Botões de Ação -->
                        <div class="action-buttons-section">
                            <button class="btn-action btn-print" onclick="imprimirPonto()" title="Imprimir registros filtrados">
                                <i class="fas fa-print"></i>
                                <span>Imprimir Ponto</span>
                            </button>
                            <button class="btn-action btn-export" onclick="exportarExcel()" title="Exportar para Excel">
                                <i class="fas fa-file-excel"></i>
                                <span>Excel</span>
                            </button>
                            <button class="btn-action btn-filter" onclick="aplicarFiltro()" title="Aplicar filtro de período">
                                <i class="fas fa-filter"></i>
                                <span>Filtrar</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover" id="registrosTable">
                    <thead>
                        <tr>
                            <th>Data</th>
                            <th>Dia</th>
                            <th>Cliente/Obra</th>
                            <th>Entrada</th>
                            <th>Saída Almoço</th>
                            <th>Retorno Almoço</th>
                            <th>Saída</th>
                            <th>Início Extra</th>
                            <th>Fim Extra</th>
                            <th>Horas Negativas</th>
                            <th>Total Horas</th>
                            <th>Status</th>
                            <th>Justificativa</th>
                            <th>Anexos</th>
                            <th>Editar</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for registro in registros %}
                        <tr data-registro-id="{{ registro.id }}">
                            <td>{{ registro.data.strftime('%d/%m/%Y') }}</td>
                            <td>{{ registro.dia_semana }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if registro.status_trabalho == 'ALOCADO' %}
                                        <span class="badge bg-primary me-2">Cliente</span>
                                        <div>
                                            <div class="fw-semibold">{{ registro.cliente_nome }}</div>
                                            {% if registro.cliente_fantasia and registro.cliente_fantasia != registro.cliente_nome %}
                                                <small class="text-muted">{{ registro.cliente_fantasia }}</small>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <span class="badge bg-secondary me-2">Sede</span>
                                        <span>Empresa Principal</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'entrada', '{{ registro.entrada or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.entrada.strftime('%H:%M') if registro.entrada else '-' }}
                                </span>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'saida_almoco', '{{ registro.saida_almoco or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.saida_almoco.strftime('%H:%M') if registro.saida_almoco else '-' }}
                                </span>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'retorno_almoco', '{{ registro.retorno_almoco or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.retorno_almoco.strftime('%H:%M') if registro.retorno_almoco else '-' }}
                                </span>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'saida', '{{ registro.saida or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.saida.strftime('%H:%M') if registro.saida else '-' }}
                                </span>
                            </td>
                            <td>
                                <span class="horario-cell {% if registro.inicio_extra %}text-warning{% else %}text-muted{% endif %}"
                                      onclick="editarHorario({{ registro.id }}, 'inicio_extra', '{{ registro.inicio_extra or '' }}')"
                                      title="Clique para editar - Início da Hora Extra (B5)">
                                    {{ registro.inicio_extra.strftime('%H:%M') if registro.inicio_extra else '-' }}
                                </span>
                            </td>
                            <td>
                                <span class="horario-cell {% if registro.fim_extra %}text-warning{% else %}text-muted{% endif %}"
                                      onclick="editarHorario({{ registro.id }}, 'fim_extra', '{{ registro.fim_extra or '' }}')"
                                      title="Clique para editar - Fim da Hora Extra (B6)">
                                    {{ registro.fim_extra.strftime('%H:%M') if registro.fim_extra else '-' }}
                                </span>
                            </td>
                            <td>
                                {% if registro.horas_negativas and registro.horas_negativas > 0 %}
                                    {% set horas_decimais = registro.horas_negativas %}
                                    {% set segundos_totais = (horas_decimais * 3600)|round|int %}
                                    {% set horas = (segundos_totais // 3600)|int %}
                                    {% set minutos = ((segundos_totais % 3600) // 60)|int %}
                                    <span class="badge bg-danger">-{{ horas }}:{{ "%02d"|format(minutos) }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if registro.horas_trabalhadas and ':' in registro.horas_trabalhadas %}
                                    <strong class="text-primary">{{ registro.horas_trabalhadas }}</strong>
                                {% elif registro.total_horas_dia %}
                                    {% set horas_decimais = registro.total_horas_dia %}
                                    {% set segundos_totais = (horas_decimais * 3600)|round|int %}
                                    {% set horas = (segundos_totais // 3600)|int %}
                                    {% set minutos = ((segundos_totais % 3600) // 60)|int %}
                                    <strong class="text-primary">{{ horas }}:{{ "%02d"|format(minutos) }}</strong>
                                {% else %}
                                    <strong class="text-primary">00:00</strong>
                                {% endif %}
                            </td>
                            <td>
                                {% if registro.entrada %}
                                    <span class="badge bg-success">Presente</span>
                                {% else %}
                                    <span class="badge bg-danger">Falta</span>
                                {% endif %}
                                {% if registro.justificativa %}
                                    <span class="badge bg-info ms-1" title="{{ registro.justificativa }}">Justificado</span>
                                {% endif %}
                                {% if registro.editado_por %}
                                    <span class="badge bg-warning ms-1">Editado</span>
                                {% endif %}
                            </td>
                            <td class="justificativa-status-cell">
                                {% if registro.status_justificativa == 'abonado' %}
                                    <span class="justificativa-abonado">Abonado</span>
                                {% elif registro.status_justificativa == 'reprovado' %}
                                    <span class="justificativa-reprovado">Reprovado</span>
                                {% elif registro.status_justificativa == 'pendente' %}
                                    <span class="justificativa-pendente">Pendente</span>
                                {% else %}
                                    <span class="justificativa-vazio">- -</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-secondary"
                                        onclick="visualizarAnexos({{ registro.id }})"
                                        title="Ver documentos anexados">
                                    <i class="fas fa-paperclip me-1"></i>
                                    <span class="anexos-count" id="count-{{ registro.id }}">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-primary"
                                        onclick="abrirModalEdicao('{{ registro.data.strftime('%Y-%m-%d') }}', {{ registro.id }})"
                                        title="Editar registro e justificativas">
                                    <i class="fas fa-edit me-1"></i>
                                    Editar
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Histórico de Atividades Profissional -->
        <div class="historico-card">
            <div class="historico-header">
                <h5 class="historico-title">
                    <i class="fas fa-history"></i>
                    Histórico de Atividades
                </h5>
                {% if logs %}
                <span class="historico-counter">{{ logs|length }} registro{{ 's' if logs|length != 1 else '' }}</span>
                {% endif %}
            </div>

            <div class="activity-timeline">
                {% if logs %}
                    {% for log in logs %}
                    <div class="activity-item">
                        <!-- Ícone da atividade -->
                        <div class="activity-icon {% if 'justificativa' in log.acao.lower() %}justificativa{% elif 'empresa' in log.acao.lower() or 'mudou' in log.acao.lower() %}empresa{% elif 'jornada' in log.acao.lower() %}jornada{% elif 'aprovad' in log.acao.lower() %}aprovacao{% elif 'reprovad' in log.acao.lower() %}reprovacao{% else %}sistema{% endif %}">
                            {% if 'justificativa' in log.acao.lower() %}JU{% elif 'empresa' in log.acao.lower() or 'mudou' in log.acao.lower() %}EM{% elif 'jornada' in log.acao.lower() %}JO{% elif 'aprovad' in log.acao.lower() %}✓{% elif 'reprovad' in log.acao.lower() %}✗{% else %}SI{% endif %}
                        </div>

                        <!-- Conteúdo da atividade -->
                        <div class="activity-content">
                            <div class="activity-header">
                                <h6 class="activity-title">
                                    {% set clean_title = log.acao.replace('_', ' ').title() %}
                                    {% if 'justificativa aprovada' in log.acao.lower() %}Justificativa Aprovada
                                    {% elif 'justificativa reprovada' in log.acao.lower() %}Justificativa Reprovada
                                    {% elif 'empresa mudou jornada' in log.acao.lower() %}Alteração de Jornada
                                    {% elif 'mudou jornada' in log.acao.lower() %}Alteração de Jornada
                                    {% elif 'justificativa' in log.acao.lower() %}Nova Justificativa
                                    {% else %}{{ clean_title }}{% endif %}
                                </h6>
                                <span class="activity-time">
                                    <i class="fas fa-clock"></i>
                                    {{ log.data_acao.strftime('%d/%m/%Y às %H:%M') }}
                                </span>
                            </div>

                            {% if log.detalhes %}
                            <p class="activity-description">
                                {% set clean_details = log.detalhes|string %}
                                {% if clean_details|length > 200 %}
                                    {{ clean_details[:200] }}...
                                {% else %}
                                    {{ clean_details }}
                                {% endif %}
                            </p>
                            {% endif %}

                            <div class="activity-meta">
                                <div class="activity-user">
                                    <i class="fas fa-user"></i>
                                    <span>{{ log.usuario_nome or 'Sistema Automático' }}</span>
                                </div>

                                {% if log.status_aprovacao %}
                                <span class="activity-status status-{{ log.status_aprovacao }}">
                                    {{ log.status_aprovacao.title() }}
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <p class="empty-state-text">Nenhuma atividade registrada ainda</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Histórico de Alocações -->
        <div class="card mb-4 mt-5">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    Histórico de Alocações em Clientes/Obras
                </h5>
            </div>
            <div class="card-body">
                {% if historico_alocacoes %}
                    <div class="row">
                        {% for alocacao in historico_alocacoes %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-start border-primary border-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="card-title mb-1">{{ alocacao.cliente_nome }}</h6>
                                            <small class="text-muted">{{ alocacao.cliente_fantasia }}</small>
                                        </div>
                                        <span class="badge {{ 'bg-success' if alocacao.ativo else 'bg-secondary' }}">
                                            {{ 'Ativo' if alocacao.ativo else 'Finalizado' }}
                                        </span>
                                    </div>
                                    <hr class="my-2">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="h6 text-primary mb-0">{{ alocacao.dias_trabalhados or 0 }}</div>
                                            <small class="text-muted">Dias</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h6 text-success mb-0">{{ "%.1f"|format(alocacao.total_horas_periodo or 0) }}h</div>
                                            <small class="text-muted">Horas</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h6 text-warning mb-0">{{ alocacao.faltas or 0 }}</div>
                                            <small class="text-muted">Faltas</small>
                                        </div>
                                    </div>
                                    <hr class="my-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ alocacao.data_inicio.strftime('%d/%m/%Y') }} -
                                        {{ alocacao.data_fim.strftime('%d/%m/%Y') if alocacao.data_fim else 'Atual' }}
                                    </small>
                                    {% if alocacao.nome_jornada %}
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ alocacao.nome_jornada }}
                                        ({{ alocacao.seg_qui_entrada.strftime('%H:%M') if alocacao.seg_qui_entrada else '' }} -
                                         {{ alocacao.seg_qui_saida.strftime('%H:%M') if alocacao.seg_qui_saida else '' }})
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p>Nenhuma alocação encontrada para este funcionário.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div> <!-- Fechamento do container-fluid -->
    </div>

    <!-- Modal Editar Horário -->
    <div class="modal fade" id="modalEditarHorario" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        Editar Horário
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="formEditarHorario">
                        <input type="hidden" id="registroId">
                        <input type="hidden" id="campoHorario">
                        
                        <div class="mb-3">
                            <label class="form-label">Novo Horário</label>
                            <input type="time" class="form-control" id="novoHorario" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Justificativa (obrigatória)</label>
                            <textarea class="form-control" id="justificativa" rows="3" 
                                      placeholder="Descreva o motivo da alteração..." required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="salvarEdicao()">
                        <i class="fas fa-save me-2"></i>Salvar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Edição de Registro de Ponto - MODERNIZADO RLPONTO-WEB -->
    <div class="modal fade" id="modalEdicaoRegistro" tabindex="-1" aria-labelledby="modalEdicaoRegistroLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <!-- Header Modernizado -->
                <div class="modal-header">
                    <h5 class="modal-title" id="modalEdicaoRegistroLabel">
                        <i class="fas fa-edit me-2"></i>
                        Editar Registro de Ponto
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <form id="formEdicaoRegistro">
                        <input type="hidden" id="registroData" name="data_registro">
                        <input type="hidden" id="funcionarioId" name="funcionario_id" value="{{ funcionario.id }}">

                        <!-- Card de Informações do Funcionário -->
                        <div class="info-card mb-4">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user me-3"></i>
                                        <div>
                                            <div class="text-muted small">Funcionário</div>
                                            <div class="fw-semibold">{{ funcionario.nome_completo }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-id-badge me-3"></i>
                                        <div>
                                            <div class="text-muted small">Matrícula</div>
                                            <div class="fw-semibold">{{ funcionario.matricula_empresa or '001' }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-calendar me-3"></i>
                                        <div>
                                            <div class="text-muted small">Data</div>
                                            <div class="fw-semibold" id="dataRegistroDisplay">11/07/2025</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Card de Registro de Ponto Atual -->
                        <div class="info-card mb-4">
                            <h5>
                                <i class="fas fa-clock"></i>
                                Registro de Ponto
                            </h5>

                            <!-- Badges de Status -->
                            <div class="d-flex align-items-center justify-content-between mb-4">
                                <div class="d-flex gap-2">
                                    <span id="diaSemanaBadge" class="badge-status status-info">QUI</span>
                                    <span id="statusBadge" class="badge-status status-success">PRESENTE</span>
                                </div>
                                <div>
                                    <span id="empresaBadge" class="badge-status status-info">EMPRESA PRINCIPAL</span>
                                </div>
                            </div>

                            <!-- Horários em Grid Compacto -->
                            <div class="horarios-grid" id="registroAtualDisplay">
                                <div class="horario-item">
                                    <div class="horario-label">Entrada</div>
                                    <div class="horario-valor" id="entradaDisplay">08:54</div>
                                </div>
                                <div class="horario-item">
                                    <div class="horario-label">Intervalo</div>
                                    <div class="horario-valor" id="saidaAlmocoDisplay">10:06</div>
                                </div>
                                <div class="horario-item">
                                    <div class="horario-label">Retorno</div>
                                    <div class="horario-valor" id="retornoAlmocoDisplay">11:23</div>
                                </div>
                                <div class="horario-item">
                                    <div class="horario-label">Saída</div>
                                    <div class="horario-valor" id="saidaDisplay">12:13</div>
                                </div>
                                <div class="horario-item total-item">
                                    <div class="horario-label">Total</div>
                                    <div class="horario-valor total-valor" id="totalHorasDisplay">8.0h</div>
                                    <div class="horario-sublabel" id="horasTrabalhadasDisplay">Trabalhadas: 2h 2min</div>
                                </div>
                            </div>

                            <!-- Área de Justificativa -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-3">
                                    <span id="justificadoBadge" class="badge-status status-warning me-3">SAÍDA ANTECIPADA</span>
                                    <span class="fw-semibold">Justificativa:</span>
                                </div>
                                <div class="border rounded p-3 position-relative" style="min-height: 80px; background-color: var(--card-background);">
                                    <div class="text-muted" id="areaJustificativa">
                                        Aqui aparece a justificativa dada no momento da batida do ponto antecipado.
                                    </div>
                                    <!-- Botão posicionado dentro da área de justificativa -->
                                    <div class="position-absolute" style="bottom: 15px; right: 15px;">
                                        <button type="button" class="btn btn-primary btn-sm" id="btnEnviarDocumento" style="padding: 0.5rem 1rem; font-size: 0.875rem;">
                                            <i class="fas fa-upload me-2"></i>
                                            Enviar Documentos
                                        </button>
                                    </div>

                                    <!-- Lista de documentos anexados -->
                                    <div id="listaDocumentosAnexados" class="mt-3" style="display: none;">
                                        <h6 class="text-muted mb-2">
                                            <i class="fas fa-paperclip me-1"></i>
                                            Documentos Anexados:
                                        </h6>
                                        <div id="documentosContainer" class="d-flex flex-wrap gap-2">
                                            <!-- Documentos serão inseridos aqui via JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Card de Edição de Horários -->
                        <div class="info-card mb-4">
                            <h5>
                                <i class="fas fa-edit"></i>
                                Editar Horários
                            </h5>

                            <!-- Grid de Edição de Horários Compacto -->
                            <div class="edicao-horarios-grid">
                                <div class="edicao-horario-item">
                                    <label for="entrada" class="form-label">Entrada</label>
                                    <input type="time" class="form-control" id="entrada" name="entrada">
                                </div>
                                <div class="edicao-horario-item">
                                    <label for="saida_almoco" class="form-label">Intervalo</label>
                                    <input type="time" class="form-control" id="saida_almoco" name="saida_almoco">
                                </div>
                                <div class="edicao-horario-item">
                                    <label for="retorno_almoco" class="form-label">Retorno</label>
                                    <input type="time" class="form-control" id="retorno_almoco" name="retorno_almoco">
                                </div>
                                <div class="edicao-horario-item">
                                    <label for="saida" class="form-label">Saída</label>
                                    <input type="time" class="form-control" id="saida" name="saida">
                                </div>
                            </div>

                            <!-- Configurações -->
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="metodo_registro" class="form-label">Método de Registro</label>
                                    <select class="form-control" id="metodo_registro" name="metodo_registro">
                                        <option value="biometrico">Biométrico</option>
                                        <option value="manual">Manual</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="status_pontualidade" class="form-label">Status Pontualidade</label>
                                    <select class="form-control" id="status_pontualidade" name="status_pontualidade">
                                        <option value="Pontual">Pontual</option>
                                        <option value="Atrasado">Atrasado</option>
                                        <option value="Saída Antecipada">Saída Antecipada</option>
                                    </select>
                                </div>
                            </div>

                            <!-- ========================================
                                 ÁREA DE ABONO DE FALTAS E ATRASOS - RH
                                 ======================================== -->
                            <div id="areaAprovacaoRH" class="card border-0 shadow-sm mt-4" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); display: none;">
                                <div class="card-header bg-transparent border-0 pb-0">
                                    <h6 class="card-title mb-0 d-flex align-items-center" style="color: #1e293b; font-weight: 600;">
                                        <i class="fas fa-user-check me-2" style="color: #4fbdba;"></i>
                                        Aprovação de Justificativas (RH)
                                    </h6>
                                    <p class="text-muted small mb-0 mt-1">Área destinada ao setor de Recursos Humanos para aprovação ou reprovação de justificativas</p>
                                </div>
                                <div class="card-body pt-3">
                                    <!-- Status Atual da Justificativa -->
                                    <div class="alert alert-light border-0 mb-3" style="background: rgba(79, 189, 186, 0.1);">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-info-circle me-2" style="color: #4fbdba;"></i>
                                            <div>
                                                <strong>Status Atual:</strong>
                                                <span id="statusJustificativaAtual" class="badge bg-warning ms-1">Pendente</span>
                                                <br>
                                                <small class="text-muted">
                                                    <span id="infoAprovacao">Aguardando análise do RH</span>
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Área de Decisão do RH -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label fw-semibold" style="color: #374151;">
                                                <i class="fas fa-clipboard-check me-1"></i>
                                                Decisão do RH
                                            </label>
                                            <select class="form-control" id="decisao_rh" name="decisao_rh" style="border: 2px solid #e5e7eb;">
                                                <option value="">Selecione uma decisão...</option>
                                                <option value="aprovado">✅ Aprovar Justificativa</option>
                                                <option value="rejeitado">❌ Reprovar Justificativa</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label fw-semibold" style="color: #374151;">
                                                <i class="fas fa-user-tie me-1"></i>
                                                Responsável pela Decisão
                                            </label>
                                            <input type="text" class="form-control" id="responsavel_decisao"
                                                   value="{{ session.usuario }}" readonly
                                                   style="background: #f9fafb; border: 2px solid #e5e7eb;">
                                        </div>
                                    </div>

                                    <!-- Observações do RH -->
                                    <div class="mt-3">
                                        <label class="form-label fw-semibold" style="color: #374151;">
                                            <i class="fas fa-comment-alt me-1"></i>
                                            Observações do RH
                                        </label>
                                        <textarea class="form-control" id="observacoes_rh" name="observacoes_rh"
                                                  rows="3" placeholder="Digite as observações sobre a decisão tomada..."
                                                  style="border: 2px solid #e5e7eb; resize: vertical;"></textarea>
                                        <small class="text-muted">
                                            <i class="fas fa-lightbulb me-1"></i>
                                            Descreva o motivo da aprovação ou reprovação da justificativa
                                        </small>
                                    </div>

                                    <!-- Botões de Ação Rápida -->
                                    <div class="mt-4 d-flex gap-2 flex-wrap">
                                        <button type="button" class="btn btn-success btn-sm" onclick="aplicarDecisaoRapida('aprovado')"
                                                style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); border: none;">
                                            <i class="fas fa-check me-1"></i>
                                            Aprovar Rapidamente
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="aplicarDecisaoRapida('rejeitado')"
                                                style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); border: none;">
                                            <i class="fas fa-times me-1"></i>
                                            Reprovar Rapidamente
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="limparDecisaoRH()">
                                            <i class="fas fa-undo me-1"></i>
                                            Limpar Decisão
                                        </button>
                                    </div>

                                    <!-- Histórico de Aprovações (se existir) -->
                                    <div id="historicoAprovacoes" class="mt-4" style="display: none;">
                                        <hr style="border-color: #e5e7eb;">
                                        <h6 class="text-muted mb-2">
                                            <i class="fas fa-history me-1"></i>
                                            Histórico de Aprovações
                                        </h6>
                                        <div id="listaHistoricoAprovacoes" class="small">
                                            <!-- Histórico será carregado via JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- ========================================
                                 ÁREA INFORMATIVA - SEM JUSTIFICATIVAS
                                 ======================================== -->
                            <div id="areaSemJustificativas" class="card border-0 shadow-sm mt-4" style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);">
                                <div class="card-body text-center py-4">
                                    <div class="mb-3">
                                        <i class="fas fa-check-circle fa-3x" style="color: #22c55e;"></i>
                                    </div>
                                    <h6 class="card-title mb-2" style="color: #166534; font-weight: 600;">
                                        Nenhuma Justificativa Pendente
                                    </h6>
                                    <p class="text-muted small mb-0">
                                        Este registro não possui justificativas aguardando análise do RH.
                                        <br>
                                        <small>A área de aprovação aparecerá automaticamente quando houver justificativas para analisar.</small>
                                    </p>
                                </div>
                            </div>
                        </div>




                    </form>
                </div>

                <!-- Footer Modernizado -->
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn-primary" onclick="salvarRegistro()" id="btnSalvarRegistro">
                        <i class="fas fa-save me-2"></i>Salvar Alterações
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Upload de Documentos - RLPONTO-WEB -->
    <div class="modal fade" id="modalUploadDocumento" tabindex="-1" aria-labelledby="modalUploadDocumentoLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Header Modernizado -->
                <div class="modal-header">
                    <h5 class="modal-title" id="modalUploadDocumentoLabel">
                        <i class="fas fa-upload me-2"></i>
                        Enviar Documento Comprobatório
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <!-- Informações do registro -->
                    <div class="alert alert-info mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>Registro:</strong> <span id="infoDataRegistro">-</span><br>
                                <strong>Funcionário:</strong> {{ funcionario.nome }}<br>
                                <strong>Tipo:</strong> <span id="infoTipoJustificativa">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- Formulário de upload -->
                    <form id="formUploadDocumento" enctype="multipart/form-data">
                        <input type="hidden" id="uploadRegistroId" name="registro_id">
                        <input type="hidden" id="uploadFuncionarioId" name="funcionario_id" value="{{ funcionario.id }}">

                        <!-- Seleção de arquivo -->
                        <div class="mb-4">
                            <label for="documentoFile" class="form-label">
                                <i class="fas fa-file me-1"></i>
                                Selecionar Documento
                            </label>
                            <input type="file" class="form-control" id="documentoFile" name="documento"
                                   accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" required>
                            <div class="form-text">
                                <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                Formatos aceitos: PDF, JPG, PNG, DOC, DOCX (máximo 5MB)
                            </div>
                        </div>

                        <!-- Tipo de documento -->
                        <div class="mb-4">
                            <label for="tipoDocumento" class="form-label">
                                <i class="fas fa-tag me-1"></i>
                                Tipo de Documento
                            </label>
                            <select class="form-select" id="tipoDocumento" name="tipo_documento" required>
                                <option value="">Selecione o tipo...</option>
                                <option value="ATESTADO_MEDICO">Atestado Médico</option>
                                <option value="JUSTIFICATIVA">Justificativa</option>
                                <option value="COMPROVANTE">Comprovante</option>
                                <option value="OUTROS">Outros</option>
                            </select>
                        </div>

                        <!-- Descrição -->
                        <div class="mb-4">
                            <label for="descricaoDocumento" class="form-label">
                                <i class="fas fa-comment me-1"></i>
                                Descrição (opcional)
                            </label>
                            <textarea class="form-control" id="descricaoDocumento" name="descricao" rows="3"
                                      placeholder="Descreva brevemente o documento (ex: Atestado médico referente à ausência do dia...)"></textarea>
                        </div>

                        <!-- Preview do arquivo selecionado -->
                        <div id="previewDocumento" class="mb-3" style="display: none;">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-eye me-1"></i>
                                        Arquivo Selecionado
                                    </h6>
                                    <div id="infoArquivo" class="text-muted">
                                        <!-- Informações do arquivo serão inseridas aqui -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="btnConfirmarUpload">
                        <i class="fas fa-upload me-2"></i>
                        <span id="textoBtnUpload">Enviar Documento</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Visualização de Anexos - RLPONTO-WEB -->
    <div class="modal fade" id="modalVisualizarAnexos" tabindex="-1" aria-labelledby="modalVisualizarAnexosLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Header Modernizado -->
                <div class="modal-header">
                    <h5 class="modal-title" id="modalVisualizarAnexosLabel">
                        <i class="fas fa-paperclip me-2"></i>
                        Documentos Anexados
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <!-- Informações do registro -->
                    <div class="alert alert-info mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>Data:</strong> <span id="anexosDataRegistro">-</span><br>
                                <strong>Funcionário:</strong> {{ funcionario.nome }}<br>
                                <strong>Total de documentos:</strong> <span id="anexosTotalDocumentos">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Lista de documentos -->
                    <div id="listaAnexosModal">
                        <div class="text-center text-muted py-4" id="semAnexosMsg">
                            <i class="fas fa-folder-open fa-3x mb-3"></i>
                            <p>Nenhum documento anexado a este registro.</p>
                        </div>

                        <div id="documentosAnexados" style="display: none;">
                            <!-- Documentos serão inseridos aqui via JavaScript -->
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Fechar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="abrirUploadParaRegistro()">
                        <i class="fas fa-plus me-2"></i>Adicionar Documento
                    </button>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

<!-- ========================================
     MODAL DE CONFIRMAÇÃO DE DECISÃO RH
     ======================================== -->
<div class="modal fade" id="modalConfirmacaoDecisao" tabindex="-1" aria-labelledby="modalConfirmacaoDecisaoLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="modalConfirmacaoDecisaoLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Confirmar Decisão Final
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Atenção:</strong> Esta decisão será <strong>FINAL</strong> e não poderá ser alterada posteriormente.
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">📋 Dados da Justificativa</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Funcionário:</strong></td>
                                <td id="confirmacao-funcionario">-</td>
                            </tr>
                            <tr>
                                <td><strong>Data:</strong></td>
                                <td id="confirmacao-data">-</td>
                            </tr>
                            <tr>
                                <td><strong>Tipo:</strong></td>
                                <td id="confirmacao-tipo">-</td>
                            </tr>
                            <tr>
                                <td><strong>Status Atual:</strong></td>
                                <td id="confirmacao-status-atual">-</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">⚖️ Nova Decisão</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Decisão:</strong></td>
                                <td id="confirmacao-nova-decisao" class="fw-bold">-</td>
                            </tr>
                            <tr>
                                <td><strong>Observações:</strong></td>
                                <td id="confirmacao-observacoes">-</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="mt-3">
                    <h6 class="fw-bold">📝 Motivo da Justificativa:</h6>
                    <div class="border rounded p-2 bg-light">
                        <small id="confirmacao-motivo">-</small>
                    </div>
                </div>

                <div class="mt-4 p-3 border rounded" style="background-color: #fff3cd;">
                    <h6 class="text-warning mb-2">
                        <i class="fas fa-lock me-2"></i>
                        Consequências desta Decisão:
                    </h6>
                    <ul class="mb-0 small">
                        <li>A decisão será <strong>permanente</strong> e não poderá ser alterada</li>
                        <li>O funcionário será notificado automaticamente</li>
                        <li>A decisão será registrada no histórico com data e responsável</li>
                        <li id="consequencia-banco-horas">O banco de horas será atualizado conforme a decisão</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    Cancelar
                </button>
                <button type="button" class="btn btn-success" id="btnConfirmarDecisao">
                    <i class="fas fa-check me-2"></i>
                    Confirmar Decisão Final
                </button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // Variáveis globais
        let registroAtual = null;
        let justificativaAtual = null;

        // ========================================
        // FUNÇÃO DE NAVEGAÇÃO
        // ========================================

        // ✅ SOLUÇÃO DEFINITIVA: Usar window.history.back() diretamente no botão

        // ✅ Função gerarRelatorio removida - botão "Relatório" eliminado do sistema

        // ✅ CORREÇÃO: Função para carregar dados do registro atual automaticamente
        function carregarRegistroAtual() {
            console.log('=== CARREGANDO REGISTRO ATUAL ===');

            // Obter data atual no formato YYYY-MM-DD
            const hoje = new Date();
            const dataAtual = hoje.toISOString().split('T')[0];

            console.log('Data atual:', dataAtual);

            // Buscar dados do registro de hoje
            const funcionarioId = {{ funcionario.id }};
            const url = `/ponto-admin/api/registro-detalhes/${funcionarioId}/${dataAtual}`;

            console.log('Buscando registro atual em:', url);

            fetch(url, {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(data => {
                console.log('✅ Dados do registro atual recebidos:', data);

                if (data && data.success && data.registro) {
                    // Atualizar display com dados reais
                    preencherDisplayRegistro(data.registro);

                    // Preencher campos de edição também
                    preencherCamposEdicao(data.registro);

                    // Preencher justificativa se existir
                    if (data.justificativa) {
                        preencherJustificativa(data.justificativa);
                    }
                } else {
                    console.log('ℹ️ Nenhum registro encontrado para hoje');

                    // ✅ CORREÇÃO: Limpar campos quando não há dados
                    const registroVazio = {
                        entrada: null,
                        saida_almoco: null,
                        retorno_almoco: null,
                        saida: null,
                        metodo_registro: null,
                        status_pontualidade: 'Pontual'  // ✅ CORREÇÃO: Pontual para ocultar badge
                    };

                    // Atualizar display com valores vazios
                    preencherDisplayRegistro(registroVazio);

                    // Limpar campos de edição
                    preencherCamposEdicao(registroVazio);
                }
            })
            .catch(error => {
                console.warn('⚠️ Erro ao carregar registro atual:', error);
                // Manter valores padrão --:-- em caso de erro
            });
        }

        // Inicializar DataTable quando jQuery estiver disponível
        document.addEventListener('DOMContentLoaded', function() {
            // ✅ CORREÇÃO: Carregar registro atual automaticamente
            carregarRegistroAtual();

            // Aguardar jQuery carregar
            function initDataTable() {
                if (typeof $ !== 'undefined' && typeof $.fn.DataTable !== 'undefined') {
                    $('#registrosTable').DataTable({
                        language: {
                            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/pt-BR.json'
                        },
                        order: [[0, 'desc']], // Ordenar por data decrescente
                        pageLength: 10,
                        responsive: true,
                        autoWidth: false, // ✅ NOVO: Desabilitar largura automática
                        columnDefs: [
                            // ✅ NOVO: Configuração explícita das colunas
                            { targets: 0, title: "Data", width: "8%" },
                            { targets: 1, title: "Dia", width: "6%" },
                            { targets: 2, title: "Cliente/Obra", width: "12%" },
                            { targets: 3, title: "Entrada", width: "7%" },
                            { targets: 4, title: "Saída Almoço", width: "7%" },
                            { targets: 5, title: "Retorno Almoço", width: "7%" },
                            { targets: 6, title: "Saída", width: "7%" },
                            { targets: 7, title: "Início Extra", width: "7%" },
                            { targets: 8, title: "Fim Extra", width: "7%" },
                            { targets: 9, title: "Horas Negativas", width: "8%" },
                            { targets: 10, title: "Total Horas", width: "7%" },
                            { targets: 11, title: "Status", width: "8%" },
                            { targets: 12, title: "Justificativa", width: "8%" },
                            { targets: 13, title: "Anexos", width: "6%" },
                            { targets: 14, title: "Editar", width: "6%" }
                        ],
                        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                             '<"row"<"col-sm-12"tr>>' +
                             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                        initComplete: function() {
                            // Ajustar alinhamento do texto "Exibir"
                            $('.dataTables_length').css('margin-left', '1rem');
                        }
                    });
                } else {
                    // Tentar novamente em 100ms
                    setTimeout(initDataTable, 100);
                }
            }

            initDataTable();
        });

        // ✅ CORREÇÃO: Função para abrir modal de edição com sincronização forçada
        function abrirModalEdicao(data, registroId) {
            console.log('=== ABRINDO MODAL DE EDIÇÃO ===');
            console.log('Data:', data);
            console.log('Registro ID:', registroId);

            registroAtual = registroId;

            // Definir data no modal
            document.getElementById('registroData').value = data;
            document.getElementById('dataRegistroDisplay').textContent = formatarData(data);

            console.log('Dados definidos no modal, iniciando busca...');

            // ✅ CORREÇÃO: Forçar sincronização com dados atuais do display ANTES de abrir modal
            sincronizarCamposComDisplay();

            // Abrir modal primeiro
            const modal = new bootstrap.Modal(document.getElementById('modalEdicaoRegistro'));
            modal.show();

            // ✅ INICIALIZAR: Ocultar área de aprovação por padrão até carregar dados
            mostrarAreaAprovacaoRH(false);

            // Carregar documentos anexados para este registro
            carregarDocumentosAnexados(registroId);

            // ✅ NOVO: Carregar dados da justificativa para área de RH
            carregarDadosJustificativa({{ funcionario.id }}, data);

            // Aguardar o modal estar completamente aberto antes de buscar dados
            setTimeout(() => {
                console.log('Modal aberto, buscando dados...');
                buscarDadosRegistro(data);
            }, 500);

            console.log('Modal sendo aberto...');
        }

        // ✅ NOVA FUNÇÃO: Sincronizar campos de edição com o display atual
        function sincronizarCamposComDisplay() {
            console.log('=== SINCRONIZANDO CAMPOS COM DISPLAY ===');

            // Obter valores atuais do display
            const entradaDisplay = document.getElementById('entradaDisplay').textContent;
            const saidaAlmocoDisplay = document.getElementById('saidaAlmocoDisplay').textContent;
            const retornoAlmocoDisplay = document.getElementById('retornoAlmocoDisplay').textContent;
            const saidaDisplay = document.getElementById('saidaDisplay').textContent;

            // Criar objeto de registro baseado no display
            const registroAtualDisplay = {
                entrada: entradaDisplay !== '--:--' ? entradaDisplay : null,
                saida_almoco: saidaAlmocoDisplay !== '--:--' ? saidaAlmocoDisplay : null,
                retorno_almoco: retornoAlmocoDisplay !== '--:--' ? retornoAlmocoDisplay : null,
                saida: saidaDisplay !== '--:--' ? saidaDisplay : null,
                metodo_registro: 'Manual',
                status_pontualidade: 'Pontual'
            };

            console.log('Dados do display para sincronização:', registroAtualDisplay);

            // Preencher campos de edição com dados do display
            preencherCamposEdicao(registroAtualDisplay);

            console.log('✅ Sincronização concluída');
        }

        // Função para buscar dados do registro
        function buscarDadosRegistro(data) {
            console.log('=== INICIANDO BUSCA DE DADOS ===');

            const funcionarioId = {{ funcionario.id }};
            const url = `/ponto-admin/api/registro-detalhes/${funcionarioId}/${data}`;

            console.log('Funcionário ID:', funcionarioId);
            console.log('Data:', data);
            console.log('URL da API:', url);

            // Fazer requisição para buscar dados reais da API
            console.log('🔄 Fazendo requisição para:', url);

            // Fazer requisição para buscar dados reais
            fetch(url, {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => {
                    console.log('=== RESPOSTA RECEBIDA ===');
                    console.log('Status:', response.status);
                    console.log('Status Text:', response.statusText);
                    console.log('Content-Type:', response.headers.get('content-type'));

                    if (response.status === 200) {
                        // Verificar se é JSON ou HTML (página de login)
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            return response.json();
                        } else {
                            throw new Error('Resposta não é JSON - possível redirecionamento para login');
                        }
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                })
                .then(data => {
                    console.log('=== DADOS RECEBIDOS ===');
                    console.log('Dados completos:', data);

                    if (data && data.success) {
                        console.log('✅ API retornou sucesso! Dados recebidos:', data);
                        console.log('📝 Registro:', data.registro);
                        console.log('📄 Justificativa:', data.justificativa);
                        preencherFormulario(data.registro, data.justificativa);
                    } else {
                        console.error('❌ API retornou erro:', data.message || 'Erro desconhecido');
                        console.error('📊 Dados completos da resposta:', data);
                        alert('Erro ao carregar dados do registro: ' + (data.message || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    console.error('=== ERRO NA REQUISIÇÃO ===');
                    console.error('Erro:', error);

                    // Verificar se é problema de autenticação
                    if (error.message.includes('login') || error.message.includes('JSON')) {
                        console.error('❌ PROBLEMA DE AUTENTICAÇÃO DETECTADO');
                        alert('Sessão expirada. Por favor, faça login novamente.');
                        window.location.reload();
                    } else {
                        alert('Erro ao carregar dados do registro: ' + error.message);
                    }
                });
        }

        // Função para preencher formulário
        function preencherFormulario(registro, justificativa) {
            console.log('=== PREENCHENDO FORMULÁRIO ===');
            console.log('Registro recebido:', registro);
            console.log('Justificativa recebida:', justificativa);

            if (!registro) {
                console.error('❌ Registro é null ou undefined!');
                return;
            }

            // Preencher display do registro atual
            preencherDisplayRegistro(registro);

            // Preencher campos de edição
            preencherCamposEdicao(registro);

            // Preencher justificativa se existir
            if (justificativa) {
                preencherJustificativa(justificativa);
            }
        }

        // Função para preencher o display do registro atual
        function preencherDisplayRegistro(registro) {
            console.log('=== PREENCHENDO DISPLAY DO REGISTRO ===');

            // Atualizar horários no display (formato simples)
            document.getElementById('entradaDisplay').textContent = registro.entrada || '--:--';
            document.getElementById('saidaAlmocoDisplay').textContent = registro.saida_almoco || '--:--';
            document.getElementById('retornoAlmocoDisplay').textContent = registro.retorno_almoco || '--:--';
            document.getElementById('saidaDisplay').textContent = registro.saida || '--:--';

            // Calcular horas trabalhadas baseado na jornada real
            const horasTrabalhadas = calcularHorasTrabalhadas(registro);
            const totalHorasCalculadas = calcularTotalHorasJornada(registro);
            document.getElementById('totalHorasDisplay').textContent = totalHorasCalculadas;
            document.getElementById('horasTrabalhadasDisplay').textContent = `Trabalhadas: ${horasTrabalhadas}`;

            // Atualizar status
            const statusBadge = document.getElementById('statusBadge');
            statusBadge.textContent = 'PRESENTE';
            statusBadge.className = 'badge bg-success ms-2 px-3 py-2';

            // ✅ CORREÇÃO: Atualizar badge de justificativa sempre (mostrar ou ocultar)
            const justificadoBadge = document.getElementById('justificadoBadge');
            if (registro.status_pontualidade && registro.status_pontualidade !== 'Pontual') {
                // Mostrar badge com status de irregularidade
                justificadoBadge.style.display = 'inline-block';
                justificadoBadge.textContent = registro.status_pontualidade.toUpperCase();
                justificadoBadge.className = 'badge bg-warning px-3 py-2 me-3';
                console.log('✅ Badge atualizado:', registro.status_pontualidade);
            } else {
                // ✅ CORREÇÃO: Ocultar badge quando status é Pontual
                justificadoBadge.style.display = 'none';
                console.log('✅ Badge ocultado - status Pontual');
            }
        }

        // Função para calcular horas trabalhadas
        function calcularTotalHorasJornada(registro) {
            /**
             * Calcula total de horas baseado nos registros reais
             * CORRIGIDO: Trata registros incompletos adequadamente
             */
            try {
                if (!registro.entrada) {
                    return '00:00';
                }

                let totalSegundos = 0;
                let observacoes = [];

                // Período manhã (sempre calcula se tem entrada e saída do almoço)
                if (registro.entrada && registro.saida_almoco) {
                    const entrada = new Date(`2000-01-01T${registro.entrada}`);
                    const saidaAlmoco = new Date(`2000-01-01T${registro.saida_almoco}`);
                    const segundosManha = Math.max(0, (saidaAlmoco - entrada) / 1000);
                    totalSegundos += segundosManha;
                    const horasManha = segundosManha / 3600;
                    observacoes.push(`Manhã: ${Math.floor(horasManha)}:${String(Math.floor((horasManha % 1) * 60)).padStart(2, '0')}`);
                }

                // Período tarde (só calcula se tem RETORNO do almoço E SAÍDA)
                if (registro.retorno_almoco && registro.saida) {
                    const retornoAlmoco = new Date(`2000-01-01T${registro.retorno_almoco}`);
                    const saida = new Date(`2000-01-01T${registro.saida}`);
                    const segundosTarde = Math.max(0, (saida - retornoAlmoco) / 1000);
                    totalSegundos += segundosTarde;
                    const horasTarde = segundosTarde / 3600;
                    observacoes.push(`Tarde: ${Math.floor(horasTarde)}:${String(Math.floor((horasTarde % 1) * 60)).padStart(2, '0')}`);
                }

                // CORREÇÃO: Detectar registros incompletos
                let statusRegistro = '';
                if (registro.entrada && registro.saida_almoco && !registro.retorno_almoco) {
                    statusRegistro = ' (só manhã)';
                } else if (registro.entrada && registro.saida_almoco && registro.retorno_almoco && !registro.saida) {
                    statusRegistro = ' (sem saída)';
                } else if (registro.entrada && !registro.saida_almoco) {
                    statusRegistro = ' (incompleto)';
                }

                // Horas extras (só se tem início e fim)
                if (registro.inicio_extra && registro.fim_extra) {
                    const inicioExtra = new Date(`2000-01-01T${registro.inicio_extra}`);
                    const fimExtra = new Date(`2000-01-01T${registro.fim_extra}`);
                    const segundosExtras = Math.max(0, (fimExtra - inicioExtra) / 1000);
                    totalSegundos += segundosExtras;
                    const horasExtras = segundosExtras / 3600;
                    observacoes.push(`Extra: ${Math.floor(horasExtras)}:${String(Math.floor((horasExtras % 1) * 60)).padStart(2, '0')}`);
                }

                // Converter segundos para HH:MM
                const horasFinais = Math.floor(totalSegundos / 3600);
                const minutosFinais = Math.floor((totalSegundos % 3600) / 60);
                const tempoFormatado = `${horasFinais}:${String(minutosFinais).padStart(2, '0')}`;

                // Log para debug
                if (observacoes.length > 0) {
                    console.log(`Cálculo detalhado: ${observacoes.join(' + ')} = ${tempoFormatado}${statusRegistro}`);
                }

                return `${tempoFormatado}${statusRegistro}`;

            } catch (error) {
                console.error('Erro ao calcular total de horas:', error);
                return '00:00';
            }
        }

        function calcularHorasTrabalhadas(registro) {
            try {
                if (!registro.entrada) return '00:00';

                let totalMinutos = 0;

                // Cenário 1: Tem entrada e saída do almoço (B1 e B2)
                // Calcular período trabalhado antes do intervalo
                if (registro.entrada && registro.saida_almoco) {
                    const entrada = new Date(`2000-01-01 ${registro.entrada}`);
                    const saidaAlmoco = new Date(`2000-01-01 ${registro.saida_almoco}`);
                    const minutosAntes = (saidaAlmoco - entrada) / (1000 * 60);
                    totalMinutos += minutosAntes;
                    console.log(`Período antes do intervalo: ${minutosAntes} minutos`);
                }

                // Cenário 2: Tem retorno do almoço e saída (B3 e B4)
                // Calcular período trabalhado após o intervalo
                if (registro.retorno_almoco && registro.saida) {
                    const retornoAlmoco = new Date(`2000-01-01 ${registro.retorno_almoco}`);
                    const saida = new Date(`2000-01-01 ${registro.saida}`);
                    const minutosDepois = (saida - retornoAlmoco) / (1000 * 60);
                    totalMinutos += minutosDepois;
                    console.log(`Período após o intervalo: ${minutosDepois} minutos`);
                }

                // Cenário 3: Só tem entrada e saída (sem intervalo)
                // Calcular período trabalhado direto
                if (registro.entrada && registro.saida && !registro.saida_almoco && !registro.retorno_almoco) {
                    const entrada = new Date(`2000-01-01 ${registro.entrada}`);
                    const saida = new Date(`2000-01-01 ${registro.saida}`);
                    totalMinutos = (saida - entrada) / (1000 * 60);
                    console.log(`Período direto (sem intervalo): ${totalMinutos} minutos`);
                }

                // Cenário 4: Só tem entrada (ainda trabalhando ou saiu sem bater)
                // Neste caso, não podemos calcular horas trabalhadas
                if (registro.entrada && !registro.saida && !registro.saida_almoco) {
                    console.log('Só tem entrada, não é possível calcular');
                    return '0h 0min';
                }

                console.log(`Total de minutos trabalhados: ${totalMinutos}`);

                // Converter para formato HH:MM
                const horas = Math.floor(totalMinutos / 60);
                const minutos = Math.round(totalMinutos % 60);

                return `${String(horas).padStart(2, '0')}:${String(minutos).padStart(2, '0')}`;
            } catch (error) {
                console.error('Erro ao calcular horas:', error);
                return '00:00';
            }
        }

        // ✅ CORREÇÃO: Função para preencher campos de edição com limpeza
        function preencherCamposEdicao(registro) {
            console.log('=== PREENCHENDO CAMPOS DE EDIÇÃO ===');
            console.log('Registro recebido para edição:', registro);

            // ✅ CORREÇÃO: SEMPRE limpar e preencher campos (não apenas se existir)
            // Isso garante que campos antigos sejam removidos quando não há dados

            // Preencher campos de horário (sempre definir, mesmo que vazio)
            document.getElementById('entrada').value = registro.entrada || '';
            document.getElementById('saida_almoco').value = registro.saida_almoco || '';
            document.getElementById('retorno_almoco').value = registro.retorno_almoco || '';
            document.getElementById('saida').value = registro.saida || '';

            // Preencher outros campos (sempre definir, mesmo que vazio)
            document.getElementById('metodo_registro').value = registro.metodo_registro || 'Manual';
            document.getElementById('status_pontualidade').value = registro.status_pontualidade || 'Pontual';

            console.log('✅ Campos de edição preenchidos e sincronizados');
            console.log('Entrada:', document.getElementById('entrada').value);
            console.log('Saída Almoço:', document.getElementById('saida_almoco').value);
            console.log('Retorno Almoço:', document.getElementById('retorno_almoco').value);
            console.log('Saída:', document.getElementById('saida').value);
        }

        // Função para preencher justificativa
        function preencherJustificativa(justificativa) {
            console.log('=== PREENCHENDO JUSTIFICATIVA ===');
            console.log('📄 Justificativa recebida:', justificativa);

            // ✅ CORREÇÃO: Definir justificativaAtual
            justificativaAtual = justificativa;
            console.log('✅ justificativaAtual definida:', justificativaAtual);

            const areaJustificativa = document.getElementById('areaJustificativa');

            if (justificativa && justificativa.motivo) {
                // Preencher área de justificativa no layout principal
                areaJustificativa.textContent = justificativa.motivo;
                areaJustificativa.className = 'text-dark';
            } else {
                // Manter texto padrão se não houver justificativa
                areaJustificativa.textContent = 'Aqui aparece a justificativa dada no momento da batida do ponto antecipado.';
                areaJustificativa.className = 'text-muted';
            }

            console.log('✅ Justificativa preenchida');
        }








        // Função para salvar registro
        function salvarRegistro() {
            console.log('💾 Iniciando salvamento do registro...');

            // ✅ VERIFICAR SE HÁ DECISÃO DO RH QUE PRECISA DE VALIDAÇÃO
            const decisaoRH = document.getElementById('decisao_rh').value;

            if (decisaoRH && decisaoRH !== '' && decisaoRH !== 'pendente') {
                console.log('⚖️ Decisão do RH detectada, validando antes de salvar...');
                validarDecisaoRH();
                return; // Parar aqui, a validação continuará o processo
            }

            // ✅ CONTINUAR COM SALVAMENTO NORMAL
            console.log('💾 Salvamento normal (sem decisão do RH)...');
            executarSalvamento();
        }

        /**
         * Executar o salvamento efetivo
         */
        function executarSalvamento() {
            console.log('💾 Iniciando salvamento...');

            const form = document.getElementById('formEdicaoRegistro');
            if (!form) {
                console.error('❌ Formulário não encontrado!');
                alert('Erro: Formulário não encontrado!');
                return;
            }

            console.log('✅ Formulário encontrado:', form);
            const formData = new FormData(form);

            // Adicionar dados adicionais
            formData.append('registro_id', registroAtual);
            if (justificativaAtual) {
                formData.append('justificativa_id', justificativaAtual.id);
            }

            // ✅ NOVO: Adicionar dados de aprovação do RH
            const decisaoRH = document.getElementById('decisao_rh').value;
            const observacoesRH = document.getElementById('observacoes_rh').value;
            const responsavelDecisao = document.getElementById('responsavel_decisao').value;

            console.log('📊 Dados de aprovação:', { decisaoRH, observacoesRH, responsavelDecisao });

            if (decisaoRH) {
                formData.append('decisao_rh', decisaoRH);
                formData.append('observacoes_rh', observacoesRH);
                formData.append('responsavel_decisao', responsavelDecisao);
                formData.append('data_decisao', new Date().toISOString());
            }

            // Mostrar loading no botão "Salvar Alterações"
            const btnSalvar = document.querySelector('button[onclick="salvarRegistro()"]');
            let textoOriginal = '';
            if (btnSalvar) {
                textoOriginal = btnSalvar.innerHTML;
                btnSalvar.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Salvando...';
                btnSalvar.disabled = true;
            }

            console.log('🌐 Enviando dados para servidor...');

            fetch('/ponto-admin/salvar-registro', {
                method: 'POST',
                credentials: 'same-origin',
                body: formData
            })
            .then(response => {
                console.log('📊 Resposta do salvamento:', response.status);
                console.log('📊 Response OK:', response.ok);
                console.log('📊 Content-Type:', response.headers.get('content-type'));

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return response.json();
            })
            .then(data => {
                console.log('📊 Dados de resposta:', data);

                if (data.success) {
                    console.log('✅ Salvamento bem-sucedido');
                    alert('Registro salvo com sucesso!');

                    // ✅ CORREÇÃO: Aguardar um pouco antes de recarregar para garantir persistência
                    setTimeout(() => {
                        location.reload(); // Recarregar página para mostrar alterações
                    }, 500);
                } else {
                    console.log('❌ Erro no salvamento:', data.message);
                    alert('Erro ao salvar: ' + data.message);
                }
            })
            .catch(error => {
                console.error('❌ ERRO NO SALVAMENTO:', error);
                console.error('❌ Stack trace:', error.stack);
                alert('Erro de comunicação com o servidor');
            })
            .finally(() => {
                if (btnSalvar) {
                    btnSalvar.innerHTML = textoOriginal;
                    btnSalvar.disabled = false;
                }
            });
        }

        // Função auxiliar para formatar data
        function formatarData(data) {
            const [ano, mes, dia] = data.split('-');
            return `${dia}/${mes}/${ano}`;
        }

        // ========================================
        // SISTEMA DE UPLOAD DE DOCUMENTOS
        // ========================================

        // Função para abrir modal de upload de documento
        function enviarDocumento(event) {
            // Prevenir comportamento padrão do botão
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            if (!registroAtual) {
                alert('Nenhum registro selecionado para anexar documento.');
                return;
            }

            // Preencher informações do modal
            const dataRegistro = document.getElementById('registroData').value;
            const tipoJustificativa = document.getElementById('justificadoBadge').textContent || 'Justificativa';

            document.getElementById('infoDataRegistro').textContent = formatarData(dataRegistro);
            document.getElementById('infoTipoJustificativa').textContent = tipoJustificativa;
            document.getElementById('uploadRegistroId').value = registroAtual;

            // Limpar formulário
            document.getElementById('formUploadDocumento').reset();
            document.getElementById('uploadRegistroId').value = registroAtual;
            document.getElementById('uploadFuncionarioId').value = {{ funcionario.id }};
            document.getElementById('previewDocumento').style.display = 'none';

            // Abrir modal de upload
            const modalUpload = new bootstrap.Modal(document.getElementById('modalUploadDocumento'));
            modalUpload.show();
        }

        // Preview do arquivo selecionado
        function configurarPreviewArquivo() {
            const fileInput = document.getElementById('documentoFile');
            const previewDiv = document.getElementById('previewDocumento');
            const infoDiv = document.getElementById('infoArquivo');

            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // Verificar tamanho (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('Arquivo muito grande. Máximo 5MB permitido.');
                        fileInput.value = '';
                        previewDiv.style.display = 'none';
                        return;
                    }

                    // Verificar tipo
                    const tiposPermitidos = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg',
                                           'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
                    if (!tiposPermitidos.includes(file.type)) {
                        alert('Tipo de arquivo não permitido. Use PDF, JPG, PNG, DOC ou DOCX.');
                        fileInput.value = '';
                        previewDiv.style.display = 'none';
                        return;
                    }

                    // Mostrar preview
                    const tamanhoMB = (file.size / 1024 / 1024).toFixed(2);
                    const icone = obterIconeArquivo(file.type);

                    infoDiv.innerHTML = `
                        <div class="d-flex align-items-center">
                            <i class="${icone} me-2 text-primary" style="font-size: 1.5rem;"></i>
                            <div>
                                <strong>${file.name}</strong><br>
                                <small>Tamanho: ${tamanhoMB} MB | Tipo: ${file.type}</small>
                            </div>
                        </div>
                    `;
                    previewDiv.style.display = 'block';
                } else {
                    previewDiv.style.display = 'none';
                }
            });
        }

        // Obter ícone baseado no tipo de arquivo
        function obterIconeArquivo(mimeType) {
            if (mimeType.includes('pdf')) return 'fas fa-file-pdf';
            if (mimeType.includes('image')) return 'fas fa-file-image';
            if (mimeType.includes('word') || mimeType.includes('document')) return 'fas fa-file-word';
            return 'fas fa-file';
        }

        // Processar upload do documento
        function processarUploadDocumento() {
            const form = document.getElementById('formUploadDocumento');
            const formData = new FormData(form);
            const btnUpload = document.getElementById('btnConfirmarUpload');
            const textoBtn = document.getElementById('textoBtnUpload');

            // Validar campos obrigatórios
            const arquivo = document.getElementById('documentoFile').files[0];
            const tipoDocumento = document.getElementById('tipoDocumento').value;

            if (!arquivo) {
                alert('Por favor, selecione um arquivo.');
                return;
            }

            if (!tipoDocumento) {
                alert('Por favor, selecione o tipo de documento.');
                return;
            }

            // Mostrar loading
            btnUpload.disabled = true;
            textoBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';

            // Enviar via AJAX
            fetch('{{ url_for("ponto_admin.upload_documento") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Sucesso
                    alert('Documento enviado com sucesso!');

                    // Fechar modal de upload
                    const modalUpload = bootstrap.Modal.getInstance(document.getElementById('modalUploadDocumento'));
                    modalUpload.hide();

                    // Atualizar lista de documentos no modal de edição
                    carregarDocumentosAnexados(registroAtual);

                    // Atualizar contador na tabela
                    atualizarContadorAnexos(registroAtual);

                } else {
                    alert('Erro ao enviar documento: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro interno ao enviar documento.');
            })
            .finally(() => {
                // Restaurar botão
                btnUpload.disabled = false;
                textoBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Enviar Documento';
            });
        }

        // Carregar documentos anexados para um registro
        function carregarDocumentosAnexados(registroId) {
            if (!registroId) return;

            fetch(`{{ url_for("ponto_admin.listar_documentos_registro") }}?registro_id=${registroId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    exibirDocumentosAnexados(data.documentos);
                } else {
                    console.error('Erro ao carregar documentos:', data.message);
                }
            })
            .catch(error => {
                console.error('Erro ao carregar documentos:', error);
            });
        }

        // Exibir documentos anexados na interface
        function exibirDocumentosAnexados(documentos) {
            const listaDiv = document.getElementById('listaDocumentosAnexados');
            const containerDiv = document.getElementById('documentosContainer');

            if (!documentos || documentos.length === 0) {
                listaDiv.style.display = 'none';
                return;
            }

            // Limpar container
            containerDiv.innerHTML = '';

            // Adicionar cada documento
            documentos.forEach(doc => {
                const docElement = criarElementoDocumento(doc);
                containerDiv.appendChild(docElement);
            });

            // Mostrar lista
            listaDiv.style.display = 'block';
        }

        // Criar elemento visual para um documento
        function criarElementoDocumento(documento) {
            const div = document.createElement('div');
            div.className = 'badge bg-light text-dark border d-flex align-items-center p-2';
            div.style.fontSize = '0.8rem';

            const icone = obterIconeArquivo(documento.mime_type || '');
            const tipoTexto = documento.tipo_documento || 'OUTROS';
            const nomeArquivo = documento.nome_original || documento.nome_arquivo;

            div.innerHTML = `
                <i class="${icone} me-2 text-primary"></i>
                <div class="me-2">
                    <div class="fw-bold">${nomeArquivo}</div>
                    <small class="text-muted">${tipoTexto}</small>
                </div>
                <button class="btn btn-sm btn-outline-danger ms-1" onclick="removerDocumento(${documento.id})" title="Remover documento">
                    <i class="fas fa-times"></i>
                </button>
            `;

            return div;
        }

        // Remover documento
        function removerDocumento(documentoId) {
            if (!confirm('Tem certeza que deseja remover este documento?')) {
                return;
            }

            fetch(`{{ url_for("ponto_admin.remover_documento") }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ documento_id: documentoId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Recarregar lista de documentos
                    carregarDocumentosAnexados(registroAtual);
                } else {
                    alert('Erro ao remover documento: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro interno ao remover documento.');
            });
        }

        // ========================================
        // SISTEMA DE VISUALIZAÇÃO DE ANEXOS NA TABELA
        // ========================================

        // Variável global para armazenar o registro atual sendo visualizado
        let registroAnexosAtual = null;

        // Função para visualizar anexos de um registro
        function visualizarAnexos(registroId) {
            registroAnexosAtual = registroId;

            // Buscar informações do registro
            const linha = document.querySelector(`tr[data-registro-id="${registroId}"]`);
            const dataRegistro = linha ? linha.querySelector('td:first-child').textContent : '-';

            // Preencher informações do modal
            document.getElementById('anexosDataRegistro').textContent = dataRegistro;

            // Carregar documentos
            carregarDocumentosParaVisualizacao(registroId);

            // Abrir modal
            const modal = new bootstrap.Modal(document.getElementById('modalVisualizarAnexos'));
            modal.show();
        }

        // Carregar documentos para visualização
        function carregarDocumentosParaVisualizacao(registroId) {
            fetch(`{{ url_for("ponto_admin.listar_documentos_registro") }}?registro_id=${registroId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    exibirDocumentosNoModal(data.documentos);
                } else {
                    console.error('Erro ao carregar documentos:', data.message);
                    exibirDocumentosNoModal([]);
                }
            })
            .catch(error => {
                console.error('Erro ao carregar documentos:', error);
                exibirDocumentosNoModal([]);
            });
        }

        // Exibir documentos no modal de visualização
        function exibirDocumentosNoModal(documentos) {
            const semAnexosMsg = document.getElementById('semAnexosMsg');
            const documentosContainer = document.getElementById('documentosAnexados');
            const totalSpan = document.getElementById('anexosTotalDocumentos');

            // Atualizar contador
            totalSpan.textContent = documentos.length;

            if (!documentos || documentos.length === 0) {
                semAnexosMsg.style.display = 'block';
                documentosContainer.style.display = 'none';
                return;
            }

            // Ocultar mensagem de sem anexos
            semAnexosMsg.style.display = 'none';
            documentosContainer.style.display = 'block';

            // Limpar container
            documentosContainer.innerHTML = '';

            // Adicionar cada documento
            documentos.forEach(doc => {
                const docCard = criarCardDocumento(doc);
                documentosContainer.appendChild(docCard);
            });
        }

        // Criar card visual para um documento
        function criarCardDocumento(documento) {
            const card = document.createElement('div');
            card.className = 'card mb-3';

            const icone = obterIconeArquivo(documento.mime_type || '');
            const tipoTexto = documento.tipo_documento || 'OUTROS';
            const nomeArquivo = documento.nome_original || documento.nome_arquivo;
            const dataUpload = documento.data_upload ? new Date(documento.data_upload).toLocaleString('pt-BR') : '-';
            const tamanhoMB = documento.tamanho_arquivo ? (documento.tamanho_arquivo / 1024 / 1024).toFixed(2) : '0';

            card.innerHTML = `
                <div class="card-body">
                    <div class="d-flex align-items-start">
                        <div class="me-3">
                            <i class="${icone} fa-2x text-primary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1">${nomeArquivo}</h6>
                            <div class="mb-2">
                                <span class="badge bg-secondary me-2">${tipoTexto}</span>
                                <small class="text-muted">${tamanhoMB} MB</small>
                            </div>
                            ${documento.descricao ? `<p class="card-text text-muted small mb-2">${documento.descricao}</p>` : ''}
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Enviado em: ${dataUpload}
                            </small>
                        </div>
                        <div class="ms-3">
                            <div class="btn-group-vertical" role="group">
                                <button class="btn btn-sm btn-outline-primary" onclick="baixarDocumento('${documento.nome_arquivo}')" title="Baixar documento">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="removerDocumentoModal(${documento.id})" title="Remover documento">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return card;
        }

        // Baixar documento
        function baixarDocumento(nomeArquivo) {
            const url = `{{ url_for("ponto_admin.baixar_documento") }}?arquivo=${encodeURIComponent(nomeArquivo)}`;

            // Criar link temporário para download
            const link = document.createElement('a');
            link.href = url;
            link.download = ''; // Força download
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Remover documento do modal
        function removerDocumentoModal(documentoId) {
            if (!confirm('Tem certeza que deseja remover este documento?')) {
                return;
            }

            fetch(`{{ url_for("ponto_admin.remover_documento") }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ documento_id: documentoId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Recarregar lista de documentos no modal
                    carregarDocumentosParaVisualizacao(registroAnexosAtual);
                    // Atualizar contador na tabela
                    atualizarContadorAnexos(registroAnexosAtual);
                } else {
                    alert('Erro ao remover documento: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro interno ao remover documento.');
            });
        }

        // Abrir upload para o registro atual sendo visualizado
        function abrirUploadParaRegistro() {
            if (registroAnexosAtual) {
                // Fechar modal de visualização
                const modalVisualizacao = bootstrap.Modal.getInstance(document.getElementById('modalVisualizarAnexos'));
                modalVisualizacao.hide();

                // Definir registro atual e abrir modal de upload
                registroAtual = registroAnexosAtual;
                enviarDocumento();
            }
        }

        // Atualizar contador de anexos na tabela
        function atualizarContadorAnexos(registroId) {
            fetch(`{{ url_for("ponto_admin.listar_documentos_registro") }}?registro_id=${registroId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const countElement = document.getElementById(`count-${registroId}`);
                    if (countElement) {
                        const count = data.documentos.length;
                        countElement.textContent = count;

                        // Atualizar estilo do botão baseado na quantidade
                        const button = countElement.closest('button');
                        if (count > 0) {
                            button.className = 'btn btn-sm btn-success';
                            button.title = `${count} documento(s) anexado(s)`;
                        } else {
                            button.className = 'btn btn-sm btn-outline-secondary';
                            button.title = 'Nenhum documento anexado';
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Erro ao atualizar contador:', error);
            });
        }

        // Carregar contadores de anexos para todos os registros
        function carregarTodosContadores() {
            const registros = document.querySelectorAll('[data-registro-id]');
            registros.forEach(linha => {
                const registroId = linha.getAttribute('data-registro-id');
                if (registroId) {
                    atualizarContadorAnexos(registroId);
                }
            });
        }

        // Adicionar eventos ao carregar a página
        document.addEventListener('DOMContentLoaded', function() {
            const btnEnviarDocumento = document.getElementById('btnEnviarDocumento');
            if (btnEnviarDocumento) {
                btnEnviarDocumento.addEventListener('click', function(event) {
                    enviarDocumento(event);
                });
            }

            const btnConfirmarUpload = document.getElementById('btnConfirmarUpload');
            if (btnConfirmarUpload) {
                btnConfirmarUpload.addEventListener('click', processarUploadDocumento);
            }

            // Event listener removido - usando link direto

            // Configurar preview de arquivo
            configurarPreviewArquivo();

            // Carregar contadores de anexos
            setTimeout(carregarTodosContadores, 1000);

            // ========================================
            // NOVAS FUNCIONALIDADES - CONTROLES MODERNOS
            // ========================================

            // Configurar controles modernos
            configurarControlesModernos();
        });

        // ========================================
        // CONFIGURAÇÃO DOS CONTROLES MODERNOS
        // ========================================
        function configurarControlesModernos() {
            // Configurar eventos dos inputs de data
            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');

            if (dataInicio && dataFim) {
                dataInicio.addEventListener('change', validarPeriodo);
                dataFim.addEventListener('change', validarPeriodo);
            }

            // REMOVIDO: Aplicação automática do filtro para evitar loops
            // O filtro agora é aplicado apenas manualmente via botão "Filtrar"
        }

        // ========================================
        // FUNÇÕES DE PERÍODO RÁPIDO
        // ========================================
        function setPeriodoRapido(periodo) {
            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');

            // ✅ CORREÇÃO: Função para obter data local sem problemas de timezone
            function getDataLocal(data) {
                const ano = data.getFullYear();
                const mes = String(data.getMonth() + 1).padStart(2, '0');
                const dia = String(data.getDate()).padStart(2, '0');
                return `${ano}-${mes}-${dia}`;
            }

            const hoje = new Date();
            let inicio, fim;

            switch(periodo) {
                case 'hoje':
                    inicio = fim = getDataLocal(hoje);
                    break;

                case 'semana':
                    // Últimos 7 dias
                    const semanaAtras = new Date(hoje);
                    semanaAtras.setDate(hoje.getDate() - 7);
                    inicio = getDataLocal(semanaAtras);
                    fim = getDataLocal(hoje);
                    break;

                case 'mes':
                    // Últimos 30 dias
                    const mesAtras = new Date(hoje);
                    mesAtras.setDate(hoje.getDate() - 30);
                    inicio = getDataLocal(mesAtras);
                    fim = getDataLocal(hoje);
                    break;

                case 'todos':
                    // Período amplo para mostrar todos os registros
                    const anoAtras = new Date(hoje);
                    anoAtras.setFullYear(hoje.getFullYear() - 1);
                    inicio = getDataLocal(anoAtras);
                    fim = getDataLocal(hoje);
                    break;
            }

            if (dataInicio && dataFim) {
                dataInicio.value = inicio;
                dataFim.value = fim;

                // Atualizar botões ativos
                const buttons = document.querySelectorAll('.quick-btn');
                buttons.forEach(btn => btn.classList.remove('active'));
                event.target.classList.add('active');

                // Aplicar filtro automaticamente
                setTimeout(aplicarFiltro, 100);
            }
        }

        // ========================================
        // VALIDAÇÃO DE PERÍODO
        // ========================================
        function validarPeriodo() {
            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');

            if (dataInicio && dataFim && dataInicio.value && dataFim.value) {
                const inicio = new Date(dataInicio.value);
                const fim = new Date(dataFim.value);

                if (inicio > fim) {
                    // Trocar as datas se início for maior que fim
                    const temp = dataInicio.value;
                    dataInicio.value = dataFim.value;
                    dataFim.value = temp;

                    // Mostrar feedback visual
                    mostrarFeedback('Datas ajustadas automaticamente', 'info');
                }
            }
        }

        // ========================================
        // APLICAR FILTRO DE PERÍODO - VERSÃO BACKEND
        // ========================================
        let filtroEmAndamento = false; // Proteção contra múltiplas chamadas

        function aplicarFiltro() {
            // Proteção contra múltiplas chamadas simultâneas
            if (filtroEmAndamento) {
                console.log('Filtro já em andamento, ignorando nova chamada');
                return;
            }

            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');

            if (!dataInicio || !dataFim) return;

            const inicio = dataInicio.value;
            const fim = dataFim.value;

            // Se não há datas selecionadas, não fazer nada
            if (!inicio || !fim) {
                mostrarFeedback('Selecione as datas de início e fim', 'warning');
                return;
            }

            // Marcar como em andamento
            filtroEmAndamento = true;

            // Mostrar loading
            mostrarLoading();

            // Buscar dados do backend com filtro de data
            const funcionarioId = {{ funcionario.id }};
            const url = `{{ url_for("ponto_admin.api_registros_funcionario", funcionario_id=0) }}`.replace('0', funcionarioId);

            fetch(`${url}?data_inicio=${inicio}&data_fim=${fim}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Recarregar a tabela com os novos dados
                        recarregarTabelaRegistros(data.registros);

                        // Feedback visual no botão
                        const btnFiltrar = document.querySelector('.btn-filter');
                        if (btnFiltrar) {
                            btnFiltrar.innerHTML = '<i class="fas fa-check"></i><span>Filtrado!</span>';
                            btnFiltrar.style.background = '#10b981';

                            setTimeout(() => {
                                btnFiltrar.innerHTML = '<i class="fas fa-filter me-1"></i>Filtrar';
                                btnFiltrar.style.background = '';
                            }, 1500);
                        }

                        mostrarFeedback(`Filtro aplicado: ${data.registros.length} registros encontrados`, 'success');
                    } else {
                        mostrarFeedback('Erro ao aplicar filtro: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erro ao aplicar filtro:', error);
                    mostrarFeedback('Erro ao aplicar filtro', 'error');
                })
                .finally(() => {
                    // Sempre limpar o estado
                    esconderLoading();
                    filtroEmAndamento = false;
                });
        }

        // ========================================
        // RECARREGAR TABELA COM NOVOS DADOS
        // ========================================
        function recarregarTabelaRegistros(registros) {
            const tbody = document.querySelector('#registrosTable tbody');
            if (!tbody) return;

            // Limpar tabela atual
            tbody.innerHTML = '';

            // Se não há registros, mostrar mensagem
            if (!registros || registros.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="12" class="text-center text-muted py-4">
                            <i class="fas fa-calendar-times fa-2x mb-2"></i><br>
                            Nenhum registro encontrado no período selecionado
                        </td>
                    </tr>
                `;
                return;
            }

            // Adicionar cada registro
            registros.forEach(registro => {
                const row = criarLinhaRegistro(registro);
                tbody.appendChild(row);
            });

            // Atualizar contador
            atualizarContadorFiltro(registros.length, registros.length);

            // ✅ CORREÇÃO: Reaplicar event listeners e contadores após filtro
            setTimeout(() => {
                carregarTodosContadores(); // Recarregar contadores de anexos
            }, 100);
        }

        // ========================================
        // CRIAR LINHA DA TABELA
        // ========================================
        function criarLinhaRegistro(registro) {
            const tr = document.createElement('tr');
            tr.setAttribute('data-registro-id', registro.id || '');

            // Converter data ISO para formato brasileiro (sem problemas de timezone)
            let dataFormatada, dataISO;
            if (registro.data) {
                // Se a data está no formato ISO (YYYY-MM-DD), converter diretamente
                if (typeof registro.data === 'string' && registro.data.match(/^\d{4}-\d{2}-\d{2}/)) {
                    const [ano, mes, dia] = registro.data.split('T')[0].split('-');
                    dataFormatada = `${dia}/${mes}/${ano}`;
                    dataISO = `${ano}-${mes}-${dia}`; // Manter formato ISO para funções
                } else {
                    // Fallback para conversão normal
                    const data = new Date(registro.data);
                    dataFormatada = data.toLocaleDateString('pt-BR');
                    dataISO = data.toISOString().split('T')[0];
                }
            } else {
                dataFormatada = '-';
                dataISO = '';
            }

            // Formatar horários
            const formatarHora = (hora) => {
                if (!hora) return '-';
                if (typeof hora === 'string' && hora.includes(':')) return hora.substring(0, 5);
                return hora;
            };

            // ✅ NOVO: Função para gerar HTML do status de justificativa
            const getStatusJustificativaHTML = (status) => {
                if (!status) {
                    return '<span class="justificativa-vazio">- -</span>';
                }

                if (status === 'abonado') {
                    return '<span class="justificativa-abonado">Abonado</span>';
                } else if (status === 'reprovado') {
                    return '<span class="justificativa-reprovado">Reprovado</span>';
                } else if (status === 'pendente') {
                    return '<span class="justificativa-pendente">Pendente</span>';
                }

                return '<span class="justificativa-vazio">- -</span>';
            };

            // Status de presença
            const statusPresenca = registro.entrada ?
                '<span class="badge bg-success">Presente</span>' :
                '<span class="badge bg-danger">Falta</span>';

            // Justificativa
            const justificativa = registro.justificativa ?
                `<span class="badge bg-info ms-1" title="${registro.justificativa}">Justificado</span>` : '';

            // Horas negativas
            const horasNegativas = registro.horas_negativas && registro.horas_negativas > 0 ? (() => {
                const segundosTotais = Math.round(registro.horas_negativas * 3600);
                const horas = Math.floor(segundosTotais / 3600);
                const minutos = Math.floor((segundosTotais % 3600) / 60);
                return `<span class="badge bg-danger">-${horas}:${String(minutos).padStart(2, '0')}</span>`;
            })() : '<span class="text-muted">-</span>';

            // Determinar badge e informações do cliente baseado no status de trabalho
            let badgeCliente, nomeCliente, fantasiaCliente = '';

            if (registro.status_trabalho === 'ALOCADO') {
                badgeCliente = '<span class="badge bg-primary me-2">Cliente</span>';
                nomeCliente = registro.cliente_nome || 'Cliente';
                if (registro.cliente_fantasia && registro.cliente_fantasia !== registro.cliente_nome) {
                    fantasiaCliente = `<small class="text-muted">${registro.cliente_fantasia}</small>`;
                }
            } else {
                badgeCliente = '<span class="badge bg-secondary me-2">Sede</span>';
                nomeCliente = 'Empresa Principal';
                fantasiaCliente = '';
            }

            tr.innerHTML = `
                <td>${dataFormatada}</td>
                <td>${registro.dia_semana || '-'}</td>
                <td>
                    <div class="d-flex align-items-center">
                        ${badgeCliente}
                        <div>
                            <div class="fw-semibold">${nomeCliente}</div>
                            ${fantasiaCliente}
                        </div>
                    </div>
                </td>
                <td><span class="horario-cell" data-tipo="entrada">${formatarHora(registro.entrada)}</span></td>
                <td><span class="horario-cell" data-tipo="saida_almoco">${formatarHora(registro.saida_almoco)}</span></td>
                <td><span class="horario-cell" data-tipo="retorno_almoco">${formatarHora(registro.retorno_almoco)}</span></td>
                <td><span class="horario-cell" data-tipo="saida">${formatarHora(registro.saida)}</span></td>
                <td><span class="horario-cell" data-tipo="inicio_extra">${formatarHora(registro.inicio_extra)}</span></td>
                <td><span class="horario-cell" data-tipo="fim_extra">${formatarHora(registro.fim_extra)}</span></td>
                <td>${horasNegativas}</td>
                <td><strong class="text-primary">${registro.horas_trabalhadas || '00:00'}</strong></td>
                <td>
                    ${statusPresenca}
                    ${justificativa}
                </td>
                <td class="justificativa-status-cell">
                    ${getStatusJustificativaHTML(registro.status_justificativa)}
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="visualizarAnexos(${registro.id || 0})" title="Ver anexos">
                        <i class="fas fa-paperclip"></i>
                        <span id="count-${registro.id || 0}">0</span>
                    </button>
                </td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="abrirModalEdicao('${dataISO}', ${registro.id || 0})" title="Editar registro e justificativas">
                        <i class="fas fa-edit me-1"></i>
                        Editar
                    </button>
                </td>
            `;

            return tr;
        }

        // Função auxiliar para converter data brasileira
        function parseDataBrasileira(dataStr) {
            // Formato esperado: DD/MM/YYYY
            const partes = dataStr.split('/');
            if (partes.length === 3) {
                return new Date(partes[2], partes[1] - 1, partes[0]);
            }
            return new Date(dataStr);
        }

        // Atualizar contador de filtro
        function atualizarContadorFiltro(visible, total) {
            const infoElement = document.querySelector('.dataTables_info');
            if (infoElement) {
                if (visible === total) {
                    infoElement.textContent = `Mostrando ${total} registros`;
                } else {
                    infoElement.textContent = `Mostrando ${visible} de ${total} registros (filtrado por período)`;
                }
            }
        }

        // ========================================
        // FUNÇÕES DE LOADING E FEEDBACK
        // ========================================
        function mostrarLoading() {
            const btnFiltrar = document.querySelector('.btn-filter');
            if (btnFiltrar && !btnFiltrar.disabled) {
                btnFiltrar.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Carregando...';
                btnFiltrar.disabled = true;
                btnFiltrar.style.pointerEvents = 'none'; // Prevenir cliques múltiplos
            }
        }

        function esconderLoading() {
            const btnFiltrar = document.querySelector('.btn-filter');
            if (btnFiltrar) {
                btnFiltrar.innerHTML = '<i class="fas fa-filter me-1"></i>Filtrar';
                btnFiltrar.disabled = false;
                btnFiltrar.style.pointerEvents = 'auto';
            }
        }

        // Função para mostrar feedback
        function mostrarFeedback(mensagem, tipo = 'success') {
            // Criar toast de feedback
            const toast = document.createElement('div');
            toast.className = `alert alert-${tipo === 'error' ? 'danger' : tipo} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${mensagem}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(toast);

            // Remover automaticamente após 5 segundos
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 5000);
        }

        // ========================================
        // FUNÇÕES DE ABONO DE FALTAS E ATRASOS - RH
        // ========================================

        /**
         * Aplicar decisão rápida (aprovar/reprovar)
         */
        function aplicarDecisaoRapida(decisao) {
            const selectDecisao = document.getElementById('decisao_rh');
            const observacoesRH = document.getElementById('observacoes_rh');

            // Definir decisão
            selectDecisao.value = decisao;

            // Sugerir texto padrão baseado na decisão
            if (decisao === 'aprovado') {
                observacoesRH.value = 'Justificativa aprovada pelo RH. Falta/atraso abonado conforme documentação apresentada.';
                atualizarStatusJustificativa('aprovado', 'Aprovado pelo RH');
            } else if (decisao === 'rejeitado') {
                observacoesRH.value = 'Justificativa reprovada pelo RH. Documentação insuficiente ou motivo não aceito.';
                atualizarStatusJustificativa('rejeitado', 'Reprovado pelo RH');
            }

            // Focar no campo de observações para permitir edição
            observacoesRH.focus();
            observacoesRH.select();
        }

        /**
         * Limpar decisão do RH
         */
        function limparDecisaoRH() {
            document.getElementById('decisao_rh').value = '';
            document.getElementById('observacoes_rh').value = '';
            atualizarStatusJustificativa('pendente', 'Aguardando análise do RH');
        }

        /**
         * Atualizar status visual da justificativa
         */
        function atualizarStatusJustificativa(status, info) {
            const statusElement = document.getElementById('statusJustificativaAtual');
            const infoElement = document.getElementById('infoAprovacao');

            // Remover classes anteriores
            statusElement.className = 'badge ms-1';

            // Aplicar nova classe e texto baseado no status
            switch(status) {
                case 'aprovado':
                    statusElement.classList.add('bg-success');
                    statusElement.textContent = 'Aprovado';
                    break;
                case 'rejeitado':
                    statusElement.classList.add('bg-danger');
                    statusElement.textContent = 'Reprovado';
                    break;
                default:
                    statusElement.classList.add('bg-warning');
                    statusElement.textContent = 'Pendente';
            }

            infoElement.textContent = info;
        }

        /**
         * Carregar dados da justificativa no modal
         */
        function carregarDadosJustificativa(funcionarioId, dataRegistro) {
            // Esta função será chamada quando o modal for aberto
            // para carregar os dados existentes da justificativa

            // ✅ CORREÇÃO: Definir registroAtual com data_registro para uso posterior
            registroAtual = {
                ...registroAtual,
                data_registro: dataRegistro,
                funcionario_id: funcionarioId
            };

            console.log('🔍 registroAtual definido:', registroAtual);

            fetch(`/ponto-admin/api/justificativa-detalhes/${funcionarioId}/${dataRegistro}`)
                .then(response => response.json())
                .then(data => {
                    console.log('🔍 Resposta da API justificativa-detalhes:', data);
                    console.log('🔍 data.success:', data.success);
                    console.log('🔍 data.justificativa:', data.justificativa);
                    console.log('🔍 data.tem_documentos_anexados:', data.tem_documentos_anexados);

                    if (data.success && data.justificativa) {
                        const justificativa = data.justificativa;

                        // ✅ MOSTRAR área de aprovação do RH
                        mostrarAreaAprovacaoRH(true);

                        // Preencher campos
                        document.getElementById('decisao_rh').value = justificativa.status_aprovacao || '';
                        document.getElementById('observacoes_rh').value = justificativa.observacoes_aprovador || '';

                        // Atualizar status visual
                        let statusTexto = 'Pendente';
                        let infoTexto = 'Aguardando análise do RH';

                        // ✅ NOVO: Verificar se é justificativa baseada em documentos anexados
                        if (justificativa.origem === 'documentos_anexados') {
                            statusTexto = 'Pendente';
                            infoTexto = `📎 Documentos anexados detectados: ${justificativa.documentos_anexados?.length || 0} arquivo(s) - Aguardando análise do RH`;
                        } else if (justificativa.status_aprovacao === 'aprovada') {
                            statusTexto = '✅ Aprovada';
                            infoTexto = `Aprovada por ${justificativa.aprovador_nome || 'RH'} em ${justificativa.data_aprovacao || ''} - DECISÃO FINAL`;

                            // ✅ CORREÇÃO: Ocultar área de aprovação quando já aprovada
                            console.log('🎯 Justificativa já aprovada - ocultando área de aprovação');
                            mostrarAreaAprovacaoRH(false);

                        } else if (justificativa.status_aprovacao === 'reprovada') {
                            statusTexto = '❌ Reprovada';
                            infoTexto = `Reprovada por ${justificativa.aprovador_nome || 'RH'} em ${justificativa.data_aprovacao || ''} - DECISÃO FINAL`;

                            // ✅ CORREÇÃO: Ocultar área de aprovação quando já reprovada
                            console.log('🎯 Justificativa já reprovada - ocultando área de aprovação');
                            mostrarAreaAprovacaoRH(false);
                        }

                        atualizarStatusJustificativa(justificativa.status_aprovacao || 'pendente', infoTexto);

                        // ✅ NOVO: Exibir informações sobre documentos anexados se existirem
                        if (data.documentos_anexados && data.documentos_anexados.total > 0) {
                            exibirInformacoesDocumentosAnexados(data.documentos_anexados);
                        }

                        // Carregar histórico se existir
                        if (data.historico && data.historico.length > 0) {
                            carregarHistoricoAprovacoes(data.historico);
                        }
                    } else {
                        // ✅ OCULTAR área de aprovação do RH quando não há justificativas
                        mostrarAreaAprovacaoRH(false);
                    }
                })
                .catch(error => {
                    console.error('Erro ao carregar dados da justificativa:', error);
                    // ✅ OCULTAR área de aprovação do RH em caso de erro
                    mostrarAreaAprovacaoRH(false);
                });
        }

        /**
         * Validar decisão antes de salvar
         */
        function validarDecisaoRH() {
            const funcionarioId = {{ funcionario.id }};
            const dataRegistro = registroAtual?.data_registro || new Date().toISOString().split('T')[0];
            const decisaoRH = document.getElementById('decisao_rh').value;
            const observacoesRH = document.getElementById('observacoes_rh').value;

            console.log('🔍 Validando decisão RH:', { funcionarioId, dataRegistro, decisaoRH });
            console.log('🔍 registroAtual completo:', registroAtual);
            console.log('🔍 dataRegistro final:', dataRegistro);

            if (!decisaoRH || decisaoRH === '') {
                alert('Por favor, selecione uma decisão (Aprovar ou Reprovar)');
                return;
            }

            // Chamar API de validação
            fetch('/ponto-admin/api/validar-decisao-justificativa', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    funcionario_id: funcionarioId,
                    data_registro: dataRegistro,
                    decisao_rh: decisaoRH,
                    observacoes_rh: observacoesRH
                })
            })
            .then(response => {
                console.log('📊 Resposta recebida:', response);
                console.log('📊 Status da resposta:', response.status);
                console.log('📊 Headers da resposta:', response.headers);

                if (response.status === 401) {
                    console.log('❌ Erro 401 - Sessão expirada');
                    alert('❌ Sessão expirada. Faça login novamente.');
                    window.location.href = '/login';
                    return;
                }

                if (!response.ok) {
                    console.log('❌ Resposta não OK:', response.status, response.statusText);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                console.log('✅ Resposta OK, fazendo parse JSON...');
                return response.json();
            })
            .then(data => {
                console.log('✅ JSON parseado com sucesso');
                console.log('📊 Resposta da validação:', data);

                if (!data) {
                    console.log('❌ Data é null ou undefined');
                    alert('❌ Resposta inválida do servidor');
                    return;
                }

                if (!data.success) {
                    console.log('❌ Success = false:', data.message);
                    alert(`❌ ${data.message}`);
                    return;
                }

                console.log('✅ Validação bem-sucedida');

                if (data.requer_confirmacao) {
                    console.log('📋 Requer confirmação, mostrando modal...');
                    // Mostrar modal de confirmação
                    mostrarModalConfirmacao(data.confirmacao);
                } else {
                    console.log('💾 Salvando diretamente...');
                    // Salvar diretamente
                    salvarRegistro();
                }
            })
            .catch(error => {
                console.error('❌ ERRO CAPTURADO:', error);
                console.error('❌ Tipo do erro:', typeof error);
                console.error('❌ Stack trace:', error.stack);
                alert('Erro ao validar decisão. Tente novamente.');
            });
        }

        /**
         * Mostrar modal de confirmação
         */
        function mostrarModalConfirmacao(dadosConfirmacao) {
            console.log('📋 Mostrando modal de confirmação:', dadosConfirmacao);

            // Função auxiliar para definir texto com segurança
            function setTextSafely(elementId, text) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = text;
                    console.log(`✅ ${elementId}: ${text}`);
                    return true;
                } else {
                    console.error(`❌ Elemento não encontrado: ${elementId}`);
                    return false;
                }
            }

            // Aguardar um momento para garantir que o DOM esteja pronto
            setTimeout(function() {
                console.log('🔄 Iniciando preenchimento do modal...');

                // Preencher dados no modal com verificação de segurança
                setTextSafely('confirmacao-funcionario', dadosConfirmacao.funcionario_nome);
                setTextSafely('confirmacao-data', dadosConfirmacao.data_registro);
                setTextSafely('confirmacao-tipo', dadosConfirmacao.tipo_justificativa);
                setTextSafely('confirmacao-status-atual', dadosConfirmacao.status_atual);
                setTextSafely('confirmacao-nova-decisao', dadosConfirmacao.nova_decisao);
                setTextSafely('confirmacao-observacoes', dadosConfirmacao.observacoes || 'Nenhuma observação');
                setTextSafely('confirmacao-motivo', dadosConfirmacao.motivo);

                // Configurar cor da decisão com verificação segura
                const elementoDecisao = document.getElementById('confirmacao-nova-decisao');
                if (elementoDecisao) {
                    if (dadosConfirmacao.nova_decisao === 'aprovada') {
                        elementoDecisao.className = 'fw-bold text-success';
                        elementoDecisao.textContent = '✅ APROVADA';
                    } else if (dadosConfirmacao.nova_decisao === 'reprovada') {
                        elementoDecisao.className = 'fw-bold text-danger';
                        elementoDecisao.textContent = '❌ REPROVADA';
                    }
                    console.log('✅ Cor da decisão configurada');
                } else {
                    console.error('❌ Elemento confirmacao-nova-decisao não encontrado');
                }

                // Configurar botão de confirmação com verificação segura
                const btnConfirmar = document.getElementById('btnConfirmarDecisao');
                if (btnConfirmar) {
                    btnConfirmar.onclick = function() {
                        confirmarDecisaoFinal();
                    };
                    console.log('✅ Botão de confirmação configurado');
                } else {
                    console.error('❌ Botão btnConfirmarDecisao não encontrado');
                }

                // Mostrar modal com verificação segura
                const modalElement = document.getElementById('modalConfirmacaoDecisao');
                if (modalElement) {
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                    console.log('✅ Modal de confirmação exibido');
                } else {
                    console.error('❌ Modal modalConfirmacaoDecisao não encontrado');
                    // Fallback: usar alert se modal não existir
                    const confirmacao = confirm(`Confirmar ${dadosConfirmacao.nova_decisao.toUpperCase()} da justificativa?\n\nFuncionário: ${dadosConfirmacao.funcionario_nome}\nData: ${dadosConfirmacao.data_registro}\nMotivo: ${dadosConfirmacao.motivo}`);
                    if (confirmacao) {
                        confirmarDecisaoFinal();
                    }
                }
            }, 100); // Aguardar 100ms para garantir que o DOM esteja pronto
        }

        /**
         * Confirmar decisão final
         */
        function confirmarDecisaoFinal() {
            console.log('✅ Confirmando decisão final...');

            // Fechar modal de confirmação com verificação segura
            const modalElement = document.getElementById('modalConfirmacaoDecisao');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                    console.log('✅ Modal fechado');
                } else {
                    console.error('❌ Instância do modal não encontrada');
                }
            } else {
                console.error('❌ Elemento do modal não encontrado');
            }

            // Executar salvamento direto
            executarSalvamento();
        }

        /**
         * Controlar visibilidade da área de aprovação do RH
         */
        function mostrarAreaAprovacaoRH(mostrar) {
            const areaAprovacao = document.getElementById('areaAprovacaoRH');
            const areaSemJustificativas = document.getElementById('areaSemJustificativas');

            if (mostrar) {
                // Mostrar área de aprovação e ocultar área informativa
                areaAprovacao.style.display = 'block';
                areaSemJustificativas.style.display = 'none';
                console.log('✅ Área de aprovação do RH exibida - há justificativas para analisar');
            } else {
                // Ocultar área de aprovação e mostrar área informativa
                areaAprovacao.style.display = 'none';
                areaSemJustificativas.style.display = 'block';
                console.log('ℹ️ Área de aprovação do RH ocultada - sem justificativas pendentes');
            }
        }

        /**
         * Exibir informações sobre documentos anexados na área de aprovação
         */
        function exibirInformacoesDocumentosAnexados(documentosInfo) {
            // Verificar se já existe uma área de informações de documentos
            let areaDocumentos = document.getElementById('areaDocumentosAnexadosInfo');

            if (!areaDocumentos) {
                // Criar área de informações sobre documentos anexados
                const areaAprovacao = document.getElementById('areaAprovacaoRH');
                const cardBody = areaAprovacao.querySelector('.card-body');

                areaDocumentos = document.createElement('div');
                areaDocumentos.id = 'areaDocumentosAnexadosInfo';
                areaDocumentos.className = 'alert alert-info border-0 mb-3';
                areaDocumentos.style.background = 'rgba(59, 130, 246, 0.1)';

                // Inserir após o status atual
                const statusAlert = cardBody.querySelector('.alert.alert-light');
                statusAlert.parentNode.insertBefore(areaDocumentos, statusAlert.nextSibling);
            }

            // Criar lista de documentos
            let listaDocumentos = '';
            if (documentosInfo.documentos && documentosInfo.documentos.length > 0) {
                listaDocumentos = '<ul class="list-unstyled mb-0 mt-2">';
                documentosInfo.documentos.forEach(doc => {
                    const dataUpload = new Date(doc.data_upload).toLocaleDateString('pt-BR');
                    listaDocumentos += `
                        <li class="small text-muted">
                            <i class="fas fa-paperclip me-1"></i>
                            <strong>${doc.nome_original}</strong>
                            ${doc.descricao ? `- ${doc.descricao}` : ''}
                            <span class="text-muted">(${dataUpload})</span>
                        </li>
                    `;
                });
                listaDocumentos += '</ul>';
            }

            areaDocumentos.innerHTML = `
                <div class="d-flex align-items-start">
                    <i class="fas fa-paperclip me-2 mt-1" style="color: #3b82f6;"></i>
                    <div class="flex-grow-1">
                        <strong style="color: #1e40af;">Documentos Anexados Detectados</strong>
                        <br>
                        <small class="text-muted">
                            ${documentosInfo.total} documento(s) anexado(s) aos registros de ponto desta data.
                            Estes documentos podem conter justificativas que precisam de análise.
                        </small>
                        ${listaDocumentos}
                    </div>
                </div>
            `;

            console.log(`📎 Exibindo informações de ${documentosInfo.total} documentos anexados`);
        }

        /**
         * Carregar histórico de aprovações
         */
        function carregarHistoricoAprovacoes(historico) {
            const historicoDiv = document.getElementById('historicoAprovacoes');
            const listaDiv = document.getElementById('listaHistoricoAprovacoes');

            if (historico && historico.length > 0) {
                let html = '';
                historico.forEach(item => {
                    const statusClass = item.status === 'aprovado' ? 'text-success' : 'text-danger';
                    html += `
                        <div class="border-start border-3 ps-3 mb-2" style="border-color: ${item.status === 'aprovado' ? '#10b981' : '#ef4444'} !important;">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong class="${statusClass}">${item.status === 'aprovado' ? 'Aprovado' : 'Reprovado'}</strong>
                                    <span class="text-muted">por ${item.aprovador}</span>
                                </div>
                                <small class="text-muted">${item.data}</small>
                            </div>
                            ${item.observacoes ? `<div class="text-muted small mt-1">${item.observacoes}</div>` : ''}
                        </div>
                    `;
                });

                listaDiv.innerHTML = html;
                historicoDiv.style.display = 'block';
            } else {
                historicoDiv.style.display = 'none';
            }
        }

        // ========================================
        // FUNÇÃO DE IMPRESSÃO MODERNIZADA
        // ========================================
        function imprimirPonto() {
            const funcionarioNome = '{{ funcionario.nome_completo }}';
            const funcionarioId = '{{ funcionario.id }}';
            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');

            // Feedback visual
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Preparando...</span>';
            btn.disabled = true;

            // Construir URL de impressão com filtros
            let printUrl = `/ponto-admin/funcionario/${funcionarioId}/imprimir`;

            // Adicionar parâmetros de filtro se existirem
            const params = new URLSearchParams();
            if (dataInicio && dataInicio.value) params.append('data_inicio', dataInicio.value);
            if (dataFim && dataFim.value) params.append('data_fim', dataFim.value);

            if (params.toString()) {
                printUrl += '?' + params.toString();
            }

            console.log('🖨️ Abrindo página de impressão para:', funcionarioNome, 'URL:', printUrl);

            // Abrir em nova janela otimizada para impressão
            const printWindow = window.open(printUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

            // Restaurar botão após abrir a janela
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 1000);

            // Focar na nova janela se foi aberta com sucesso
            if (printWindow) {
                printWindow.focus();
            } else {
                alert('Por favor, permita pop-ups para este site para abrir a página de impressão.');
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        // ========================================
        // FUNÇÃO DE EXPORTAÇÃO EXCEL MODERNIZADA
        // ========================================
        function exportarExcel() {
            const funcionarioNome = '{{ funcionario.nome_completo }}';
            const funcionarioId = '{{ funcionario.id }}';
            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');

            // Feedback visual
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Gerando...</span>';
            btn.disabled = true;

            // Construir URL de exportação com filtros
            let exportUrl = `/ponto-admin/funcionario/${funcionarioId}/excel`;

            // Adicionar parâmetros de filtro se existirem
            const params = new URLSearchParams();
            if (dataInicio && dataInicio.value) params.append('data_inicio', dataInicio.value);
            if (dataFim && dataFim.value) params.append('data_fim', dataFim.value);

            if (params.toString()) {
                exportUrl += '?' + params.toString();
            }

            console.log('📊 Exportando Excel para:', funcionarioNome, 'URL:', exportUrl);

            // Download direto
            window.open(exportUrl, '_blank');

            // Restaurar botão após delay
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 2000);
        }

        // ========================================
        // MELHORIAS NO DATATABLES
        // ========================================

        // Sobrescrever configuração do DataTable para incluir pesquisa personalizada
        document.addEventListener('DOMContentLoaded', function() {
            // Aguardar DataTable ser inicializado
            setTimeout(() => {
                const dataTable = $('#registrosTable').DataTable();
                if (dataTable) {
                    // Desabilitar pesquisa padrão do DataTable
                    $('.dataTables_filter').hide();

                    // Conectar nossa pesquisa personalizada
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.addEventListener('input', function() {
                            dataTable.search(this.value).draw();
                        });
                    }
                }
            }, 1000);
        });

        // ========================================
        // FUNÇÕES DE FORMATAÇÃO DO HISTÓRICO
        // ========================================

        /**
         * Determina o tipo de atividade baseado na ação
         */
        window.get_activity_type = function(acao) {
            const acaoLower = acao.toLowerCase();

            if (acaoLower.includes('justificativa')) return 'justificativa';
            if (acaoLower.includes('empresa') || acaoLower.includes('mudou')) return 'empresa';
            if (acaoLower.includes('jornada')) return 'jornada';
            if (acaoLower.includes('aprovad')) return 'aprovacao';
            if (acaoLower.includes('reprovad')) return 'reprovacao';

            return 'sistema';
        };

        /**
         * Retorna o ícone apropriado para cada tipo de atividade
         */
        window.get_activity_icon = function(acao) {
            const acaoLower = acao.toLowerCase();

            if (acaoLower.includes('justificativa')) return 'JU';
            if (acaoLower.includes('empresa') || acaoLower.includes('mudou')) return 'EM';
            if (acaoLower.includes('jornada')) return 'JO';
            if (acaoLower.includes('aprovad')) return '✓';
            if (acaoLower.includes('reprovad')) return '✗';

            return 'SI';
        };

        /**
         * Formata o título da atividade de forma mais legível
         */
        window.format_activity_title = function(acao) {
            // Remove prefixos técnicos e formata
            let title = acao.replace(/^\[.*?\]\s*/, ''); // Remove [CATEGORIA:NIVEL]
            title = title.replace(/_/g, ' '); // Substitui _ por espaços

            // Mapeamento de títulos específicos
            const titleMap = {
                'justificativa aprovada': 'Justificativa Aprovada',
                'justificativa reprovada': 'Justificativa Reprovada',
                'empresa mudou jornada': 'Alteração de Jornada',
                'mudou jornada': 'Alteração de Jornada',
                'justificativa': 'Nova Justificativa'
            };

            const titleLower = title.toLowerCase();
            return titleMap[titleLower] || title.split(' ').map(word =>
                word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
            ).join(' ');
        };

        /**
         * Formata a descrição da atividade de forma mais limpa
         */
        window.format_activity_description = function(detalhes) {
            if (!detalhes) return '';

            // Remove informações técnicas desnecessárias
            let description = detalhes;

            // Remove IDs técnicos
            description = description.replace(/\b(ID|id):\s*\d+/g, '');

            // Remove informações redundantes
            description = description.replace(/DETALHES DA OCORRÊNCIA:\s*/i, '');
            description = description.replace(/INFORMAÇÕES DA EMPRESA:\s*/i, '');

            // Limpa múltiplos espaços e quebras de linha
            description = description.replace(/\s+/g, ' ').trim();

            // Limita o tamanho da descrição
            if (description.length > 200) {
                description = description.substring(0, 200) + '...';
            }

            return description;
        };

    </script>
{% endblock %}
