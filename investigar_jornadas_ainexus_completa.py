#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para investigar todas as jornadas da AiNexus e corrigir discrepância
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def investigar_jornadas_ainexus():
    """Investigar todas as jornadas da AiNexus"""
    print("🔍 INVESTIGANDO TODAS AS JORNADAS DA AINEXUS")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Buscar TODAS as jornadas da AiNexus
        print("\n1. Buscando TODAS as jornadas da AiNexus...")
        sql_todas_jornadas = """
        SELECT 
            jt.id, jt.nome_jornada, jt.tipo_jornada,
            jt.seg_qui_entrada, jt.seg_qui_saida,
            jt.sexta_entrada, jt.sexta_saida,
            jt.intervalo_inicio, jt.intervalo_fim,
            jt.tolerancia_entrada_minutos,
            jt.padrao, jt.ativa, jt.data_cadastro, jt.data_atualizacao,
            e.razao_social as empresa_nome, e.id as empresa_id
        FROM jornadas_trabalho jt
        JOIN empresas e ON jt.empresa_id = e.id
        WHERE e.razao_social LIKE '%%AiNexus%%'
        ORDER BY jt.data_cadastro DESC, jt.id DESC
        """
        
        jornadas_ainexus = db.execute_query(sql_todas_jornadas)
        
        print(f"📊 TODAS as jornadas da AiNexus: {len(jornadas_ainexus)}")
        for i, jornada in enumerate(jornadas_ainexus):
            padrao = "PADRÃO" if jornada['padrao'] else "NORMAL"
            ativa = "ATIVA" if jornada['ativa'] else "INATIVA"
            print(f"\n   {i+1}. ID {jornada['id']}: {jornada['nome_jornada']} ({padrao}, {ativa})")
            print(f"      Empresa ID: {jornada['empresa_id']}")
            print(f"      Tipo: {jornada['tipo_jornada']}")
            print(f"      Seg-Qui: {jornada['seg_qui_entrada']} às {jornada['seg_qui_saida']}")
            print(f"      Sexta: {jornada['sexta_entrada']} às {jornada['sexta_saida']}")
            print(f"      Intervalo: {jornada['intervalo_inicio']} às {jornada['intervalo_fim']}")
            print(f"      Tolerância: {jornada['tolerancia_entrada_minutos']} minutos")
            print(f"      Cadastro: {jornada['data_cadastro']}")
            print(f"      Atualização: {jornada['data_atualizacao']}")
        
        # 2. Verificar qual jornada o Richardson está usando
        print("\n2. Verificando qual jornada o Richardson está usando...")
        sql_richardson = """
        SELECT 
            f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id,
            e.razao_social as empresa_nome
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        WHERE f.id = 1
        """
        
        richardson = db.execute_query(sql_richardson, fetch_one=True)
        
        if richardson:
            print(f"📋 Richardson:")
            print(f"   Empresa: {richardson['empresa_nome']} (ID: {richardson['empresa_id']})")
            print(f"   Jornada Trabalho ID: {richardson['jornada_trabalho_id']}")
            
            # Encontrar qual jornada ele está usando
            jornada_richardson = None
            for jornada in jornadas_ainexus:
                if jornada['id'] == richardson['jornada_trabalho_id']:
                    jornada_richardson = jornada
                    break
            
            if jornada_richardson:
                print(f"   📋 Usando jornada: {jornada_richardson['nome_jornada']}")
                print(f"      Seg-Qui: {jornada_richardson['seg_qui_entrada']} às {jornada_richardson['seg_qui_saida']}")
                print(f"      Tolerância: {jornada_richardson['tolerancia_entrada_minutos']} minutos")
                print(f"      Padrão: {jornada_richardson['padrao']}")
                print(f"      Ativa: {jornada_richardson['ativa']}")
            else:
                print(f"   ❌ Jornada ID {richardson['jornada_trabalho_id']} não encontrada!")
        
        # 3. Identificar qual deveria ser a jornada padrão atual
        print("\n3. Identificando jornada padrão atual da AiNexus...")
        jornada_padrao_atual = None
        for jornada in jornadas_ainexus:
            if jornada['padrao'] and jornada['ativa']:
                jornada_padrao_atual = jornada
                break
        
        if jornada_padrao_atual:
            print(f"📋 Jornada padrão atual:")
            print(f"   ID: {jornada_padrao_atual['id']}")
            print(f"   Nome: {jornada_padrao_atual['nome_jornada']}")
            print(f"   Seg-Qui: {jornada_padrao_atual['seg_qui_entrada']} às {jornada_padrao_atual['seg_qui_saida']}")
            print(f"   Sexta: {jornada_padrao_atual['sexta_entrada']} às {jornada_padrao_atual['sexta_saida']}")
            print(f"   Intervalo: {jornada_padrao_atual['intervalo_inicio']} às {jornada_padrao_atual['intervalo_fim']}")
            print(f"   Tolerância: {jornada_padrao_atual['tolerancia_entrada_minutos']} minutos")
        else:
            print(f"❌ Nenhuma jornada padrão ativa encontrada!")
        
        # 4. Comparar e identificar discrepância
        print("\n4. Comparando jornadas...")
        if jornada_richardson and jornada_padrao_atual:
            if jornada_richardson['id'] != jornada_padrao_atual['id']:
                print(f"⚠️ DISCREPÂNCIA ENCONTRADA!")
                print(f"   Richardson usa jornada ID {jornada_richardson['id']}: {jornada_richardson['nome_jornada']}")
                print(f"   Deveria usar jornada ID {jornada_padrao_atual['id']}: {jornada_padrao_atual['nome_jornada']}")
                
                print(f"\n   📊 COMPARAÇÃO:")
                print(f"   Richardson (atual):")
                print(f"     Seg-Qui: {jornada_richardson['seg_qui_entrada']} às {jornada_richardson['seg_qui_saida']}")
                print(f"     Tolerância: {jornada_richardson['tolerancia_entrada_minutos']} min")
                print(f"   Deveria ser (padrão atual):")
                print(f"     Seg-Qui: {jornada_padrao_atual['seg_qui_entrada']} às {jornada_padrao_atual['seg_qui_saida']}")
                print(f"     Tolerância: {jornada_padrao_atual['tolerancia_entrada_minutos']} min")
                
                # 5. Corrigir Richardson
                print(f"\n5. Corrigindo Richardson...")
                sql_update = """
                UPDATE funcionarios 
                SET jornada_trabalho_id = %s
                WHERE id = 1
                """
                
                result = db.execute_query(sql_update, (jornada_padrao_atual['id'],), fetch_all=False)
                
                if result is not None:
                    print(f"✅ Richardson corrigido!")
                    print(f"   Jornada atualizada: ID {jornada_richardson['id']} → ID {jornada_padrao_atual['id']}")
                    print(f"   Nome: {jornada_padrao_atual['nome_jornada']}")
                else:
                    print(f"❌ Erro ao corrigir Richardson")
                    return False
            else:
                print(f"✅ Richardson já está usando a jornada padrão correta")
        
        # 6. Verificar resultado final
        print(f"\n6. Verificando resultado final...")
        from utils.database import FuncionarioQueries
        
        funcionario_final = FuncionarioQueries.get_with_epis(1)
        
        if funcionario_final:
            print(f"📋 Richardson após correção:")
            print(f"   Jornada: {funcionario_final.get('nome_jornada')}")
            print(f"   Seg-Qui: {funcionario_final.get('jornada_seg_qui_entrada')} às {funcionario_final.get('jornada_seg_qui_saida')}")
            print(f"   Sexta: {funcionario_final.get('jornada_sex_entrada')} às {funcionario_final.get('jornada_sex_saida')}")
            print(f"   Intervalo: {funcionario_final.get('jornada_intervalo_entrada')} às {funcionario_final.get('jornada_intervalo_saida')}")
            print(f"   Tolerância: {funcionario_final.get('tolerancia_entrada_minutos')} minutos")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a investigação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    investigar_jornadas_ainexus()
