{"mcpServers": {"github": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@smithery-ai/github", "--key", "e68990f7-63a3-463a-9b83-736d2a5d4314", "--profile", "right-deer-A3RiHp"]}, "browser-tools": {"command": "cmd", "args": ["/c", "npx", "-y", "@agentdeskai/browser-tools-mcp@1.2.0"], "enabled": true}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": "6000"}}, "@21st-dev/magic": {"command": "cmd", "args": ["/c", "npx", "-y", "@21st-dev/magic@latest", "API_KEY=\"7bac1d77a079e8e3d511939c81bd0d248e8a7b56c1310eb13cecca0c1a948ef1\""]}}}