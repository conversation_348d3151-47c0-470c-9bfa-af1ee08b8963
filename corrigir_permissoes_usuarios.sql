-- =====================================================
-- CORREÇÃO DO BUG - ALTERAÇÃO DE NÍVEL DE USUÁRIO
-- RLPONTO-WEB - Sistema de Controle de Ponto Biométrico
-- Data: 07/07/2025
-- =====================================================

-- 1. CRIAR BACKUP DAS TABELAS
CREATE TABLE usuarios_backup_20250707 AS SELECT * FROM usuarios;
CREATE TABLE permissoes_backup_20250707 AS SELECT * FROM permissoes;

-- 2. VERIFICAR USUÁRIOS SEM PERMISSÃO
SELECT 
    u.id,
    u.usuario,
    'SEM PERMISSÃO' as status
FROM usuarios u 
LEFT JOIN permissoes p ON u.id = p.usuario_id 
WHERE p.usuario_id IS NULL;

-- 3. ADICIONAR PERMISSÕES PARA USUÁRIOS SEM REGISTRO
-- Inserir permissão para usuário cavalcrod (ID 5) como admin
INSERT INTO permissoes (usuario_id, nivel_acesso, data_atribuicao) 
SELECT 5, 'admin', NOW()
WHERE NOT EXISTS (SELECT 1 FROM permissoes WHERE usuario_id = 5);

-- Inserir permissões padrão para outros usuários sem permissão
INSERT INTO permissoes (usuario_id, nivel_acesso, data_atribuicao)
SELECT 
    u.id, 
    CASE 
        WHEN LOWER(TRIM(u.usuario)) IN ('admin', 'cavalcrod') THEN 'admin'
        ELSE 'usuario'
    END as nivel_acesso,
    NOW()
FROM usuarios u 
LEFT JOIN permissoes p ON u.id = p.usuario_id 
WHERE p.usuario_id IS NULL;

-- 4. LIMPAR USUÁRIOS DUPLICADOS (com espaços)
-- Primeiro, identificar usuários com espaços
SELECT 
    id, 
    CONCAT('"', usuario, '"') as usuario_com_aspas,
    CONCAT('"', TRIM(usuario), '"') as usuario_limpo,
    LENGTH(usuario) as tamanho_original,
    LENGTH(TRIM(usuario)) as tamanho_limpo
FROM usuarios 
WHERE usuario != TRIM(usuario);

-- Remover usuários duplicados com espaços (se existirem)
-- CUIDADO: Execute apenas se houver duplicatas confirmadas
-- DELETE p FROM permissoes p 
-- JOIN usuarios u ON p.usuario_id = u.id 
-- WHERE u.usuario != TRIM(u.usuario) 
-- AND EXISTS (
--     SELECT 1 FROM usuarios u2 
--     WHERE TRIM(u2.usuario) = TRIM(u.usuario) 
--     AND u2.id != u.id 
--     AND u2.id < u.id
-- );

-- DELETE FROM usuarios 
-- WHERE usuario != TRIM(usuario) 
-- AND EXISTS (
--     SELECT 1 FROM usuarios u2 
--     WHERE TRIM(u2.usuario) = TRIM(usuario) 
--     AND u2.id != id 
--     AND u2.id < id
-- );

-- 5. VERIFICAR RESULTADO FINAL
SELECT 
    u.id,
    u.usuario,
    p.nivel_acesso,
    p.data_atribuicao,
    CASE 
        WHEN p.nivel_acesso IS NOT NULL THEN '✅ TEM PERMISSÃO'
        ELSE '❌ SEM PERMISSÃO'
    END as status
FROM usuarios u 
LEFT JOIN permissoes p ON u.id = p.usuario_id 
ORDER BY u.id;

-- 6. VERIFICAR SE AINDA HÁ USUÁRIOS SEM PERMISSÃO
SELECT 
    COUNT(*) as usuarios_sem_permissao
FROM usuarios u 
LEFT JOIN permissoes p ON u.id = p.usuario_id 
WHERE p.usuario_id IS NULL;

-- 7. TESTAR FUNCIONALIDADE DE UPDATE
-- Simular o que a aplicação faz
SELECT 
    'TESTE: Simulando UPDATE de permissão' as acao,
    u.id,
    u.usuario,
    p.nivel_acesso as nivel_atual
FROM usuarios u 
JOIN permissoes p ON u.id = p.usuario_id 
WHERE u.usuario = 'teste'
LIMIT 1;

-- Comentário: Para testar, você pode executar:
-- UPDATE permissoes SET nivel_acesso = 'admin' WHERE usuario_id = (SELECT id FROM usuarios WHERE usuario = 'teste' LIMIT 1);
-- E depois verificar se foi alterado:
-- SELECT nivel_acesso FROM permissoes WHERE usuario_id = (SELECT id FROM usuarios WHERE usuario = 'teste' LIMIT 1);

-- =====================================================
-- INSTRUÇÕES DE EXECUÇÃO:
-- 1. Execute as seções 1-3 para corrigir o problema
-- 2. Execute a seção 5 para verificar o resultado
-- 3. Execute a seção 6 para confirmar que não há mais usuários sem permissão
-- 4. Teste a funcionalidade na interface web
-- =====================================================
