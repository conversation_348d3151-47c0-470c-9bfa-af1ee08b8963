#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Sistema de Controle de Jornada com Banco de Horas Personalizado
Sistema: RLPONTO-WEB v1.0
Data: 25/06/2025
Desenvolvido por: AiNexus Tecnologia - Richardson Rodrigues

Implementa as novas regras de controle de jornada baseadas no documento controle-jornada.md
"""

import logging
from datetime import datetime, date, time, timedelta
from typing import Dict, List, Optional, Tuple
from utils.database import get_db_connection
from pymysql.cursors import DictCursor

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ControleJornada:
    """
    Classe principal para controle de jornada com banco de horas personalizado
    """
    
    def __init__(self):
        self.conn = None
        self.cursor = None
    
    def __enter__(self):
        self.conn = get_db_connection()
        self.cursor = self.conn.cursor(DictCursor)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.conn:
            self.conn.close()
    
    def obter_configuracao_funcionario(self, funcionario_id: int) -> Dict:
        """
        Obtém as configurações de jornada personalizadas do funcionário
        
        Args:
            funcionario_id (int): ID do funcionário
            
        Returns:
            Dict: Configurações de jornada do funcionário
        """
        try:
            self.cursor.execute("""
                SELECT 
                    id,
                    nome_completo,
                    inicio_expediente,
                    horario_saida_seg_qui,
                    horario_saida_sexta,
                    periodo_almoco_inicio,
                    periodo_almoco_fim,
                    duracao_minima_almoco,
                    tolerancia_entrada,
                    permite_banco_horas_positivo
                FROM funcionarios 
                WHERE id = %s AND status_cadastro = 'Ativo'
            """, (funcionario_id,))
            
            resultado = self.cursor.fetchone()
            
            if not resultado:
                raise ValueError(f"Funcionário {funcionario_id} não encontrado ou inativo")
            
            # Converter para dicionário com valores padrão se necessário
            config = {
                'funcionario_id': resultado['id'],
                'nome_completo': resultado['nome_completo'],
                'inicio_expediente': resultado['inicio_expediente'] or time(7, 0),
                'horario_saida_seg_qui': resultado['horario_saida_seg_qui'] or time(17, 0),
                'horario_saida_sexta': resultado['horario_saida_sexta'] or time(16, 30),
                'periodo_almoco_inicio': resultado['periodo_almoco_inicio'] or time(11, 0),
                'periodo_almoco_fim': resultado['periodo_almoco_fim'] or time(14, 0),
                'duracao_minima_almoco': resultado['duracao_minima_almoco'] or 60,
                'tolerancia_entrada': resultado['tolerancia_entrada'] or 15,
                'permite_banco_horas_positivo': resultado['permite_banco_horas_positivo'] or False
            }
            
            logger.info(f"Configuração obtida para funcionário {funcionario_id}: {config}")
            return config
            
        except Exception as e:
            logger.error(f"Erro ao obter configuração do funcionário {funcionario_id}: {e}")
            raise
    
    def validar_entrada_manha(self, funcionario_id: int, hora_registro: datetime) -> Dict:
        """
        Valida entrada da manhã com as novas regras
        
        Args:
            funcionario_id (int): ID do funcionário
            hora_registro (datetime): Horário do registro
            
        Returns:
            Dict: Resultado da validação com status e detalhes
        """
        try:
            config = self.obter_configuracao_funcionario(funcionario_id)
            
            # Obter apenas a hora do registro
            hora_atual = hora_registro.time()
            data_atual = hora_registro.date()
            
            # Verificar se é domingo (0 = segunda, 6 = domingo)
            dia_semana = hora_registro.weekday()
            if dia_semana == 6:  # Domingo
                return {
                    'permitido': False,
                    'status': 'domingo',
                    'mensagem': 'Registro em domingo requer aprovação especial',
                    'requer_aprovacao': True,
                    'minutos_atraso': 0
                }
            
            # Horários de referência
            inicio_expediente = config['inicio_expediente']
            periodo_almoco_inicio = config['periodo_almoco_inicio']
            tolerancia_minutos = config['tolerancia_entrada']
            
            # Calcular horário limite com tolerância
            inicio_com_tolerancia = (datetime.combine(data_atual, inicio_expediente) + 
                                   timedelta(minutes=tolerancia_minutos)).time()
            
            # Verificar se está dentro do período válido para entrada da manhã
            if hora_atual > periodo_almoco_inicio:
                return {
                    'permitido': False,
                    'status': 'ausente_manha',
                    'mensagem': f'Registro após {periodo_almoco_inicio.strftime("%H:%M")} é considerado ausência na manhã',
                    'requer_aprovacao': True,
                    'minutos_atraso': self._calcular_minutos_diferenca(inicio_expediente, hora_atual)
                }
            
            # Calcular atraso
            if hora_atual <= inicio_com_tolerancia:
                # Pontual (dentro da tolerância)
                return {
                    'permitido': True,
                    'status': 'pontual',
                    'mensagem': 'Entrada registrada dentro da tolerância',
                    'requer_aprovacao': False,
                    'minutos_atraso': 0
                }
            else:
                # Atrasado
                minutos_atraso = self._calcular_minutos_diferenca(inicio_com_tolerancia, hora_atual)
                return {
                    'permitido': True,
                    'status': 'atrasado',
                    'mensagem': f'Entrada com atraso de {minutos_atraso} minutos',
                    'requer_aprovacao': False,
                    'minutos_atraso': minutos_atraso
                }
                
        except Exception as e:
            logger.error(f"Erro ao validar entrada da manhã: {e}")
            return {
                'permitido': False,
                'status': 'erro',
                'mensagem': f'Erro na validação: {str(e)}',
                'requer_aprovacao': False,
                'minutos_atraso': 0
            }
    
    def validar_intervalo_almoco(self, funcionario_id: int, saida_almoco: datetime, 
                                entrada_tarde: datetime) -> Dict:
        """
        Valida intervalo de almoço com duração mínima obrigatória
        
        Args:
            funcionario_id (int): ID do funcionário
            saida_almoco (datetime): Horário de saída para almoço
            entrada_tarde (datetime): Horário de entrada da tarde
            
        Returns:
            Dict: Resultado da validação do intervalo
        """
        try:
            config = self.obter_configuracao_funcionario(funcionario_id)
            
            # Calcular duração do intervalo
            duracao_intervalo = entrada_tarde - saida_almoco
            minutos_intervalo = int(duracao_intervalo.total_seconds() / 60)
            duracao_minima = config['duracao_minima_almoco']
            
            # Verificar se está dentro do período permitido
            periodo_inicio = config['periodo_almoco_inicio']
            periodo_fim = config['periodo_almoco_fim']
            
            hora_saida = saida_almoco.time()
            hora_entrada = entrada_tarde.time()
            
            if not (periodo_inicio <= hora_saida <= periodo_fim):
                return {
                    'valido': False,
                    'status': 'fora_periodo',
                    'mensagem': f'Saída para almoço fora do período permitido ({periodo_inicio}-{periodo_fim})',
                    'minutos_excesso': 0,
                    'minutos_computados': duracao_minima
                }
            
            if minutos_intervalo < duracao_minima:
                # Intervalo menor que o mínimo - complementar automaticamente
                return {
                    'valido': True,
                    'status': 'complementado',
                    'mensagem': f'Intervalo de {minutos_intervalo}min complementado para {duracao_minima}min',
                    'minutos_excesso': 0,
                    'minutos_computados': duracao_minima
                }
            else:
                # Intervalo maior que o mínimo - calcular excesso
                excesso = minutos_intervalo - duracao_minima
                return {
                    'valido': True,
                    'status': 'excesso' if excesso > 0 else 'normal',
                    'mensagem': f'Intervalo de {minutos_intervalo}min ({excesso}min de excesso)' if excesso > 0 
                               else f'Intervalo normal de {minutos_intervalo}min',
                    'minutos_excesso': excesso,
                    'minutos_computados': minutos_intervalo
                }
                
        except Exception as e:
            logger.error(f"Erro ao validar intervalo de almoço: {e}")
            return {
                'valido': False,
                'status': 'erro',
                'mensagem': f'Erro na validação: {str(e)}',
                'minutos_excesso': 0,
                'minutos_computados': 60
            }
    
    def validar_saida_expediente(self, funcionario_id: int, hora_saida: datetime) -> Dict:
        """
        Valida saída do expediente considerando dia da semana
        
        Args:
            funcionario_id (int): ID do funcionário
            hora_saida (datetime): Horário de saída
            
        Returns:
            Dict: Resultado da validação da saída
        """
        try:
            config = self.obter_configuracao_funcionario(funcionario_id)
            
            # Determinar horário de saída baseado no dia da semana
            dia_semana = hora_saida.weekday()  # 0 = segunda, 4 = sexta, 6 = domingo
            
            if dia_semana == 4:  # Sexta-feira
                horario_saida_esperado = config['horario_saida_sexta']
            else:  # Segunda a quinta
                horario_saida_esperado = config['horario_saida_seg_qui']
            
            hora_atual = hora_saida.time()
            
            if hora_atual < horario_saida_esperado:
                # Saída antecipada
                minutos_antecipacao = self._calcular_minutos_diferenca(hora_atual, horario_saida_esperado)
                return {
                    'permitido': False,
                    'status': 'saida_antecipada',
                    'mensagem': f'Saída antecipada em {minutos_antecipacao} minutos - requer aprovação',
                    'requer_aprovacao': True,
                    'minutos_antecipacao': minutos_antecipacao,
                    'horario_esperado': horario_saida_esperado.strftime('%H:%M')
                }
            else:
                # Saída normal ou após horário
                minutos_extras = self._calcular_minutos_diferenca(horario_saida_esperado, hora_atual)
                permite_banco_positivo = config['permite_banco_horas_positivo']
                
                return {
                    'permitido': True,
                    'status': 'normal' if minutos_extras == 0 else 'horas_extras',
                    'mensagem': f'Saída normal' if minutos_extras == 0 
                               else f'Saída com {minutos_extras} minutos extras',
                    'requer_aprovacao': False,
                    'minutos_extras': minutos_extras,
                    'gera_credito': permite_banco_positivo and minutos_extras > 0
                }
                
        except Exception as e:
            logger.error(f"Erro ao validar saída do expediente: {e}")
            return {
                'permitido': False,
                'status': 'erro',
                'mensagem': f'Erro na validação: {str(e)}',
                'requer_aprovacao': False,
                'minutos_extras': 0
            }
    
    def _calcular_minutos_diferenca(self, hora1: time, hora2: time) -> int:
        """
        Calcula diferença em minutos entre dois horários
        
        Args:
            hora1 (time): Primeiro horário
            hora2 (time): Segundo horário
            
        Returns:
            int: Diferença em minutos (sempre positiva)
        """
        dt1 = datetime.combine(date.today(), hora1)
        dt2 = datetime.combine(date.today(), hora2)
        diferenca = abs((dt2 - dt1).total_seconds() / 60)
        return int(diferenca)

# Funções auxiliares para compatibilidade com o sistema existente
def validar_entrada_manha_nova_regra(funcionario_id: int, hora_registro: datetime) -> Dict:
    """Função auxiliar para validação de entrada da manhã"""
    with ControleJornada() as controle:
        return controle.validar_entrada_manha(funcionario_id, hora_registro)

def validar_intervalo_almoco_nova_regra(funcionario_id: int, saida_almoco: datetime, 
                                       entrada_tarde: datetime) -> Dict:
    """Função auxiliar para validação de intervalo de almoço"""
    with ControleJornada() as controle:
        return controle.validar_intervalo_almoco(funcionario_id, saida_almoco, entrada_tarde)

def validar_saida_expediente_nova_regra(funcionario_id: int, hora_saida: datetime) -> Dict:
    """Função auxiliar para validação de saída do expediente"""
    with ControleJornada() as controle:
        return controle.validar_saida_expediente(funcionario_id, hora_saida)

class BancoHoras:
    """
    Classe para gerenciamento do banco de horas
    """

    def __init__(self):
        self.conn = None
        self.cursor = None

    def __enter__(self):
        self.conn = get_db_connection()
        self.cursor = self.conn.cursor(DictCursor)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.conn:
            self.conn.close()

    def calcular_banco_horas_dia(self, funcionario_id: int, data_referencia: date) -> Dict:
        """
        Calcula o banco de horas para um dia específico

        Args:
            funcionario_id (int): ID do funcionário
            data_referencia (date): Data para cálculo

        Returns:
            Dict: Resultado do cálculo do banco de horas
        """
        try:
            # Obter registros do dia
            registros = self._obter_registros_dia(funcionario_id, data_referencia)

            if not registros:
                return self._criar_registro_ausencia_total(funcionario_id, data_referencia)

            # Analisar registros
            entrada_manha = None
            saida_almoco = None
            entrada_tarde = None
            saida_final = None

            for registro in registros:
                tipo = registro['tipo_registro']
                if tipo == 'entrada_manha':
                    entrada_manha = registro
                elif tipo == 'saida_almoco':
                    saida_almoco = registro
                elif tipo == 'entrada_tarde':
                    entrada_tarde = registro
                elif tipo == 'saida':
                    saida_final = registro

            # Calcular componentes do banco de horas
            resultado = {
                'funcionario_id': funcionario_id,
                'data_referencia': data_referencia,
                'atraso_entrada_minutos': 0,
                'excesso_almoco_minutos': 0,
                'saida_antecipada_minutos': 0,
                'horas_extras_minutos': 0,
                'saldo_devedor_minutos': 0,
                'saldo_credor_minutos': 0,
                'saldo_liquido_minutos': 0,
                'status_dia': 'incompleto',
                'observacoes': []
            }

            # Validar entrada da manhã
            if entrada_manha:
                with ControleJornada() as controle:
                    validacao_entrada = controle.validar_entrada_manha(
                        funcionario_id, entrada_manha['data_hora']
                    )

                    if validacao_entrada['status'] == 'atrasado':
                        resultado['atraso_entrada_minutos'] = validacao_entrada['minutos_atraso']
                        resultado['observacoes'].append(f"Atraso na entrada: {validacao_entrada['minutos_atraso']}min")
                    elif validacao_entrada['status'] == 'ausente_manha':
                        resultado['status_dia'] = 'ausente_parcial'
                        resultado['observacoes'].append("Ausência no período da manhã")
            else:
                resultado['observacoes'].append("Sem registro de entrada da manhã")

            # Validar intervalo de almoço
            if saida_almoco and entrada_tarde:
                with ControleJornada() as controle:
                    validacao_almoco = controle.validar_intervalo_almoco(
                        funcionario_id, saida_almoco['data_hora'], entrada_tarde['data_hora']
                    )

                    if validacao_almoco['status'] == 'excesso':
                        resultado['excesso_almoco_minutos'] = validacao_almoco['minutos_excesso']
                        resultado['observacoes'].append(f"Excesso no almoço: {validacao_almoco['minutos_excesso']}min")

            # Validar saída do expediente
            if saida_final:
                with ControleJornada() as controle:
                    validacao_saida = controle.validar_saida_expediente(
                        funcionario_id, saida_final['data_hora']
                    )

                    if validacao_saida['status'] == 'saida_antecipada':
                        resultado['saida_antecipada_minutos'] = validacao_saida['minutos_antecipacao']
                        resultado['observacoes'].append(f"Saída antecipada: {validacao_saida['minutos_antecipacao']}min")
                    elif validacao_saida['status'] == 'horas_extras':
                        if validacao_saida['gera_credito']:
                            resultado['horas_extras_minutos'] = validacao_saida['minutos_extras']
                            resultado['observacoes'].append(f"Horas extras: {validacao_saida['minutos_extras']}min")
            else:
                resultado['observacoes'].append("Sem registro de saída")

            # Calcular saldos
            total_devedor = (resultado['atraso_entrada_minutos'] +
                           resultado['excesso_almoco_minutos'] +
                           resultado['saida_antecipada_minutos'])

            total_credor = resultado['horas_extras_minutos']

            resultado['saldo_devedor_minutos'] = total_devedor
            resultado['saldo_credor_minutos'] = total_credor
            resultado['saldo_liquido_minutos'] = total_credor - total_devedor

            # Determinar status do dia
            if entrada_manha and saida_almoco and entrada_tarde and saida_final:
                resultado['status_dia'] = 'completo'
            elif not entrada_manha and not saida_almoco and not entrada_tarde and not saida_final:
                resultado['status_dia'] = 'ausente_total'
            else:
                resultado['status_dia'] = 'incompleto'

            resultado['observacoes'] = '; '.join(resultado['observacoes']) if resultado['observacoes'] else None

            return resultado

        except Exception as e:
            logger.error(f"Erro ao calcular banco de horas: {e}")
            raise

    def salvar_banco_horas(self, dados_banco: Dict) -> bool:
        """
        Salva ou atualiza registro de banco de horas

        Args:
            dados_banco (Dict): Dados do banco de horas

        Returns:
            bool: True se salvou com sucesso
        """
        try:
            self.cursor.execute("""
                INSERT INTO banco_horas (
                    funcionario_id, data_referencia, atraso_entrada_minutos,
                    excesso_almoco_minutos, saida_antecipada_minutos, horas_extras_minutos,
                    saldo_devedor_minutos, saldo_credor_minutos, saldo_liquido_minutos,
                    status_dia, observacoes
                ) VALUES (
                    %(funcionario_id)s, %(data_referencia)s, %(atraso_entrada_minutos)s,
                    %(excesso_almoco_minutos)s, %(saida_antecipada_minutos)s, %(horas_extras_minutos)s,
                    %(saldo_devedor_minutos)s, %(saldo_credor_minutos)s, %(saldo_liquido_minutos)s,
                    %(status_dia)s, %(observacoes)s
                ) ON DUPLICATE KEY UPDATE
                    atraso_entrada_minutos = VALUES(atraso_entrada_minutos),
                    excesso_almoco_minutos = VALUES(excesso_almoco_minutos),
                    saida_antecipada_minutos = VALUES(saida_antecipada_minutos),
                    horas_extras_minutos = VALUES(horas_extras_minutos),
                    saldo_devedor_minutos = VALUES(saldo_devedor_minutos),
                    saldo_credor_minutos = VALUES(saldo_credor_minutos),
                    saldo_liquido_minutos = VALUES(saldo_liquido_minutos),
                    status_dia = VALUES(status_dia),
                    observacoes = VALUES(observacoes),
                    atualizado_em = CURRENT_TIMESTAMP
            """, dados_banco)

            self.conn.commit()
            logger.info(f"Banco de horas salvo para funcionário {dados_banco['funcionario_id']} em {dados_banco['data_referencia']}")
            return True

        except Exception as e:
            logger.error(f"Erro ao salvar banco de horas: {e}")
            self.conn.rollback()
            return False

    def _obter_registros_dia(self, funcionario_id: int, data_referencia: date) -> List[Dict]:
        """Obtém registros de ponto do dia"""
        self.cursor.execute("""
            SELECT id, funcionario_id, tipo_registro, data_hora, metodo_registro,
                   observacoes, status_pontualidade
            FROM registros_ponto
            WHERE funcionario_id = %s AND DATE(data_hora) = %s
            ORDER BY data_hora
        """, (funcionario_id, data_referencia))

        return list(self.cursor.fetchall())

    def _criar_registro_ausencia_total(self, funcionario_id: int, data_referencia: date) -> Dict:
        """Cria registro para ausência total"""
        return {
            'funcionario_id': funcionario_id,
            'data_referencia': data_referencia,
            'atraso_entrada_minutos': 0,
            'excesso_almoco_minutos': 0,
            'saida_antecipada_minutos': 0,
            'horas_extras_minutos': 0,
            'saldo_devedor_minutos': 480,  # 8 horas de ausência
            'saldo_credor_minutos': 0,
            'saldo_liquido_minutos': -480,
            'status_dia': 'ausente_total',
            'observacoes': 'Ausência total - sem registros de ponto'
        }

# Função auxiliar para processar banco de horas
def processar_banco_horas_dia(funcionario_id: int, data_referencia: date) -> bool:
    """Processa banco de horas para um dia específico"""
    try:
        with BancoHoras() as banco:
            dados = banco.calcular_banco_horas_dia(funcionario_id, data_referencia)
            return banco.salvar_banco_horas(dados)
    except Exception as e:
        logger.error(f"Erro ao processar banco de horas: {e}")
        return False
