/**
 * Cliente para Biometria Universal - RLPONTO-WEB
 * Sistema compatível com qualquer dispositivo biométrico
 */
class UniversalBiometricClient {
    constructor() {
        this.apiBase = '/configuracoes';
        this.availableDevices = [];
        this.selectedDevice = null;
    }
    
    /**
     * Faz uma requisição HTTP
     */
    async makeRequest(endpoint, options = {}) {
        try {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            };
            
            const fullOptions = {...defaultOptions, ...options};
            const response = await fetch(`${this.apiBase}${endpoint}`, fullOptions);
            
            if (!response.ok) {
                throw new Error(`Erro HTTP: ${response.status}`);
            }
            
            return response;
        } catch (error) {
            console.error(`Erro na requisição para ${endpoint}:`, error);
            throw error;
        }
    }

    /**
     * Descobre todos os dispositivos biométricos disponíveis
     */
    async descobrirDispositivos() {
        try {
            console.log('🔍 Descobrindo dispositivos biométricos...');
            
            // Usar a API de detecção atualizada
            const response = await this.makeRequest('/api/detectar-dispositivos');
            const data = await response.json();
            
            if (data.success) {
                // Mapear dispositivos para o formato esperado pela interface
                this.availableDevices = data.dispositivos.map(device => {
                    return {
                        id: device.instance_id,
                        name: device.friendly_name,
                        manufacturer: device.manufacturer || 'Desconhecido',
                        status: device.status,              // Status real do dispositivo (OK, Disconnected, Error)
                        isConnected: device.status === 'OK', // Flag explícita de conexão
                        supported: device.supported || false,
                        detection_method: device.detection_method || 'API',
                        meta: device // Manter dados originais do dispositivo para referência
                    };
                });
                
                console.log(`✅ Descobertos ${this.availableDevices.length} dispositivos:`, this.availableDevices);
                
                // Log detalhado dos dispositivos encontrados
                this.availableDevices.forEach((device, index) => {
                    console.log(`  ${index + 1}. ${device.name} (${device.manufacturer}) - Status: ${device.status}`);
                });
                
                return this.availableDevices;
            } else {
                throw new Error(data.error || 'Falha na descoberta de dispositivos');
            }
            
        } catch (error) {
            console.error('Erro na descoberta:', error);
            throw error;
        }
    }
    
    /**
     * Testa a comunicação com um dispositivo específico
     */
    async testarDispositivo(deviceId) {
        try {
            console.log(`🧪 Testando dispositivo: ${deviceId}`);
            
            // Codificar duas vezes para garantir que caracteres especiais como & sejam tratados corretamente
            const encodedId = encodeURIComponent(deviceId).replace(/\(/g, '%28').replace(/\)/g, '%29');
            console.log(`🔄 ID codificado para URL: ${encodedId}`);
            
            const response = await this.makeRequest(`/api/testar-dispositivo-biometrico`, {
                method: 'POST',
                body: JSON.stringify({
                    instance_id: deviceId
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log(`✅ Teste concluído:`, result);
                
                // Verificar se o dispositivo está realmente conectado
                const deviceStatus = {
                    id: deviceId,
                    conectado: result.conectado,
                    status: result.status,
                    message: result.message,
                    qualidade: result.qualidade_sinal
                };
                
                return {
                    success: true,
                    deviceStatus
                };
            } else {
                throw new Error(result.error || 'Falha no teste do dispositivo');
            }
            
        } catch (error) {
            console.error(`Erro no teste do dispositivo ${deviceId}:`, error);
            return {
                success: false,
                error: error.message,
                deviceStatus: {
                    id: deviceId,
                    conectado: false,
                    status: 'Error',
                    message: error.message
                }
            };
        }
    }
    
    /**
     * Captura impressão digital
     */
    async capturarDigital(deviceId) {
        try {
            console.log(`🖐️ Capturando digital no dispositivo: ${deviceId}`);
            
            // Se o dispositivo estiver desconectado, retornar erro
            const device = this.availableDevices.find(d => d.id === deviceId);
            if (device && device.status !== 'OK') {
                throw new Error(`Dispositivo desconectado ou indisponível (Status: ${device.status})`);
            }
            
            // Simular captura (será substituído por chamada real à API)
            // Aqui devemos implementar a chamada real à API de captura
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            return {
                success: true,
                template: 'TEMPLATE_SIMULADA',
                quality: 85,
                message: 'Impressão digital capturada com sucesso'
            };
            
        } catch (error) {
            console.error(`Erro na captura digital:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }
} 