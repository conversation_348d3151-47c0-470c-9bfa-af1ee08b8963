#!/usr/bin/env python3
"""
Testar se as correções críticas funcionaram
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def testar_correcoes():
    """Testar correções de jornada e EPIs"""
    try:
        from utils.database import DatabaseManager
        from app_funcionarios import _processar_dados_funcionario
        import logging
        
        logging.basicConfig(level=logging.INFO)
        
        print("🔧 TESTANDO CORREÇÕES CRÍTICAS")
        print("=" * 60)
        
        # Buscar funcionário da Ainexus
        funcionario = DatabaseManager.execute_query("""
            SELECT f.id, f.nome_completo, f.empresa_id,
                   f.jornada_seg_qui_entrada, f.jornada_seg_qui_saida,
                   e.nome_fantasia as empresa_nome
            FROM funcionarios f
            JOIN empresas e ON f.empresa_id = e.id
            WHERE e.nome_fantasia LIKE %s
            LIMIT 1
        """, ('%Ainexus%',), fetch_one=True)
        
        if not funcionario:
            print("❌ Funcionário da Ainexus não encontrado")
            return False
            
        func_id = funcionario['id']
        empresa_id = funcionario['empresa_id']
        
        print(f"👤 Funcionário: {funcionario['nome_completo']} (ID: {func_id})")
        print(f"🏢 Empresa: {funcionario['empresa_nome']} (ID: {empresa_id})")
        print(f"📅 Jornada atual: {funcionario['jornada_seg_qui_entrada']} - {funcionario['jornada_seg_qui_saida']}")
        
        # TESTE 1: Verificar se jornada NÃO é sobrescrita em edição
        print("\n🔧 TESTE 1: CORREÇÃO DA JORNADA EM EDIÇÃO")
        print("-" * 50)
        
        # Simular dados de edição (sem horario_trabalho_id)
        dados_edicao = {
            'nome_completo': funcionario['nome_completo'],
            'empresa_id': empresa_id,
            'cpf': '12345678901',
            'cargo': 'Desenvolvedor',
            'data_admissao': '2025-01-01',
            'status_cadastro': 'ativo'
            # NOTA: Não incluir horario_trabalho_id (como vem do formulário)
        }
        
        print("📋 Dados de entrada (simulando edição):")
        for key, value in dados_edicao.items():
            print(f"   {key}: {value}")
        
        # Processar dados COM funcionario_id (modo edição)
        print(f"\n🔍 Processando dados com funcionario_id={func_id} (EDIÇÃO)...")
        dados_processados = _processar_dados_funcionario(dados_edicao, funcionario_id=func_id)
        
        # Verificar se jornada foi preservada
        if 'jornada_seg_qui_entrada' in dados_processados:
            print(f"📅 Jornada processada: {dados_processados.get('jornada_seg_qui_entrada')} - {dados_processados.get('jornada_seg_qui_saida')}")
            
            # Verificar se manteve a jornada original
            if (str(dados_processados.get('jornada_seg_qui_entrada')) == str(funcionario['jornada_seg_qui_entrada']) and
                str(dados_processados.get('jornada_seg_qui_saida')) == str(funcionario['jornada_seg_qui_saida'])):
                print("✅ SUCESSO: Jornada foi PRESERVADA durante edição!")
                teste1_ok = True
            else:
                print("❌ FALHA: Jornada foi ALTERADA durante edição!")
                teste1_ok = False
        else:
            print("✅ SUCESSO: Jornada não foi modificada (preservada)!")
            teste1_ok = True
        
        # TESTE 2: Verificar se jornada É aplicada em novo cadastro
        print("\n🔧 TESTE 2: APLICAÇÃO DE JORNADA EM NOVO CADASTRO")
        print("-" * 50)
        
        # Simular dados de novo cadastro
        dados_novo = {
            'nome_completo': 'Teste Novo Funcionário',
            'empresa_id': empresa_id,
            'cpf': '98765432100',
            'cargo': 'Analista',
            'data_admissao': '2025-01-01',
            'status_cadastro': 'ativo'
        }
        
        print("📋 Dados de entrada (simulando novo cadastro):")
        for key, value in dados_novo.items():
            print(f"   {key}: {value}")
        
        # Processar dados SEM funcionario_id (modo novo)
        print(f"\n🔍 Processando dados sem funcionario_id (NOVO CADASTRO)...")
        dados_novo_processados = _processar_dados_funcionario(dados_novo, funcionario_id=None)
        
        # Verificar se jornada foi aplicada
        if dados_novo_processados.get('horario_trabalho_id'):
            print(f"⏰ Jornada aplicada: ID {dados_novo_processados.get('horario_trabalho_id')}")
            print("✅ SUCESSO: Jornada foi aplicada automaticamente em novo cadastro!")
            teste2_ok = True
        else:
            print("❌ FALHA: Jornada não foi aplicada em novo cadastro!")
            teste2_ok = False
        
        # TESTE 3: Simular adição de EPI
        print("\n🔧 TESTE 3: TESTE DE PROCESSAMENTO DE EPIs")
        print("-" * 50)
        
        # Simular dados com EPIs
        dados_com_epi = dados_edicao.copy()
        dados_com_epi['epis'] = [
            {
                'epi_nome': 'Capacete de Segurança Teste',
                'epi_ca': '12345',
                'epi_data_entrega': '2025-01-01',
                'epi_data_validade': '2026-01-01',
                'epi_observacoes': 'EPI de teste'
            }
        ]
        
        print("🦺 Dados de EPI para teste:")
        for epi in dados_com_epi['epis']:
            print(f"   • {epi['epi_nome']} (CA: {epi['epi_ca']})")
        
        # Processar dados com EPIs
        dados_epi_processados = _processar_dados_funcionario(dados_com_epi, funcionario_id=func_id)
        
        if dados_epi_processados.get('epis'):
            print(f"✅ SUCESSO: {len(dados_epi_processados['epis'])} EPIs processados!")
            teste3_ok = True
        else:
            print("❌ FALHA: EPIs não foram processados!")
            teste3_ok = False
        
        # RESULTADO FINAL
        print("\n" + "=" * 60)
        print("📊 RESULTADO DOS TESTES:")
        print(f"   🔧 Teste 1 - Preservar jornada em edição: {'✅ PASSOU' if teste1_ok else '❌ FALHOU'}")
        print(f"   🔧 Teste 2 - Aplicar jornada em novo cadastro: {'✅ PASSOU' if teste2_ok else '❌ FALHOU'}")
        print(f"   🔧 Teste 3 - Processar EPIs: {'✅ PASSOU' if teste3_ok else '❌ FALHOU'}")
        
        if teste1_ok and teste2_ok and teste3_ok:
            print("\n🎉 TODAS AS CORREÇÕES FUNCIONARAM!")
            print("✅ Sistema está funcionando corretamente")
            return True
        else:
            print("\n❌ ALGUMAS CORREÇÕES FALHARAM!")
            print("🔍 Verifique os logs acima para detalhes")
            return False
        
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    testar_correcoes()
