@echo off
:: =====================================================================
:: DESINSTALADOR BRIDGE ZK4500 - RLPONTO-WEB v1.0
:: Remove completamente o bridge biométrico do Windows
:: Desenvolvido por: <PERSON>ues - AiNexus Tecnologia
:: =====================================================================

setlocal enabledelayedexpansion

:: Definir variáveis
set BRIDGE_NAME=RLPonto-BridgeZK4500
set BRIDGE_DIR=C:\RLPonto-Bridge

:: Verificar se existe sistema de logging no diretório de instalação
if exist "%BRIDGE_DIR%\bridge_logger.bat" (
    call "%BRIDGE_DIR%\bridge_logger.bat" :START_SECTION "DESINSTALACAO_BRIDGE_ZK4500"
    call "%BRIDGE_DIR%\bridge_logger.bat" :SYSTEM_INFO
    set LOGGING_ENABLED=1
) else (
    set LOGGING_ENABLED=0
)

echo.
echo ========================================================
echo   DESINSTALADOR BRIDGE ZK4500 - RLPONTO-WEB v1.0
echo   AiNexus Tecnologia - Sistema Biometrico Empresarial
echo ========================================================
echo.

:: Verificar se está executando como administrador
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Executando como Administrador
    if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :LOG "Desinstalacao iniciada como Administrador"
) else (
    echo [ERRO] Este desinstalador precisa ser executado como Administrador!
    echo        Clique com botao direito e selecione "Executar como administrador"
    if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :ERROR "Tentativa de desinstalacao sem permissoes de administrador"
    pause
    exit /b 1
)

echo.
echo [AVISO] Este processo removera COMPLETAMENTE o Bridge ZK4500:
echo         - Servico do Windows: %BRIDGE_NAME%
echo         - Diretorio: %BRIDGE_DIR%
echo         - Todos os arquivos relacionados
echo         - Logs de instalacao e operacao
echo.
set /p CONFIRM="Deseja continuar? (S/N): "
if /i "%CONFIRM%" neq "S" (
    echo [CANCELADO] Desinstalacao cancelada pelo usuario
    if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :LOG "Desinstalacao cancelada pelo usuario"
    pause
    exit /b 0
)

if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :LOG "Usuario confirmou desinstalacao"

echo.
echo [1/5] Parando servico do Windows...
sc stop "%BRIDGE_NAME%" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Servico parado com sucesso
    if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :INSTALL_LOG "SERVICE_STOP" "SUCCESS" "%BRIDGE_NAME%"
) else (
    echo [INFO] Servico ja estava parado ou nao existe
    if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :LOG "Servico ja parado ou inexistente"
)

:: Aguardar alguns segundos para garantir que o serviço parou
timeout /t 3 >nul

echo.
echo [2/5] Removendo servico do Windows...
sc delete "%BRIDGE_NAME%" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Servico removido do registro do Windows
    if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :INSTALL_LOG "SERVICE_DELETE" "SUCCESS" "%BRIDGE_NAME%"
) else (
    echo [INFO] Servico nao estava registrado ou ja foi removido
    if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :LOG "Servico nao estava registrado"
)

echo.
echo [3/5] Finalizando processos Python relacionados...
tasklist | findstr /i "python.*biometric" >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Encontrados processos Python relacionados - finalizando...
    if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :LOG "Finalizando processos Python relacionados"
    
    :: Finalizar especificamente processos do bridge
    for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo table /nh 2^>nul') do (
        wmic process where "processid=%%i and commandline like '%%biometric%%'" delete >nul 2>&1
    )
    
    taskkill /f /im python.exe /fi "WINDOWTITLE eq *biometric*" >nul 2>&1
    echo [OK] Processos Python finalizados
    if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :INSTALL_LOG "KILL_PROCESSES" "SUCCESS" "Processos Python finalizados"
) else (
    echo [OK] Nenhum processo Python relacionado encontrado
    if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :LOG "Nenhum processo Python encontrado"
)

echo.
echo [4/5] Removendo diretorio de instalacao...
if exist "%BRIDGE_DIR%" (
    echo [INFO] Removendo: %BRIDGE_DIR%
    
    :: Fazer backup dos logs antes de remover (opcional)
    if exist "%BRIDGE_DIR%\logs\" (
        if not exist "%TEMP%\RLPONTO_BACKUP_LOGS\" mkdir "%TEMP%\RLPONTO_BACKUP_LOGS\" >nul 2>&1
        copy "%BRIDGE_DIR%\logs\*.*" "%TEMP%\RLPONTO_BACKUP_LOGS\" >nul 2>&1
        echo [INFO] Backup de logs salvo em: %TEMP%\RLPONTO_BACKUP_LOGS\
        if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :LOG "Backup de logs criado em %TEMP%\RLPONTO_BACKUP_LOGS\"
    )
    
    if %LOGGING_ENABLED%==1 call "%BRIDGE_DIR%\bridge_logger.bat" :LOG "Iniciando remocao do diretorio %BRIDGE_DIR%"
    
    :: Remover atributos de somente leitura antes de deletar
    attrib -r "%BRIDGE_DIR%\*.*" /s >nul 2>&1
    
    rmdir /s /q "%BRIDGE_DIR%" >nul 2>&1
    if %errorLevel% == 0 (
        echo [OK] Diretorio removido com sucesso
        :: Log final salvo no backup temporário
        if exist "%TEMP%\RLPONTO_BACKUP_LOGS\bridge_operations.log" (
            echo [%date% %time%] INSTALL: DIRECTORY_REMOVE - SUCCESS - %BRIDGE_DIR% >> "%TEMP%\RLPONTO_BACKUP_LOGS\bridge_operations.log"
        )
    ) else (
        echo [AVISO] Erro ao remover diretorio - pode estar em uso
        echo         Tente reiniciar o computador e executar novamente
        :: Tentar listar o que ainda está sendo usado
        echo [INFO] Tentando identificar arquivos em uso...
        dir "%BRIDGE_DIR%" >nul 2>&1
        if %errorLevel% == 0 (
            echo [INFO] Diretorio ainda contem arquivos - alguns podem estar em uso
        )
    )
) else (
    echo [INFO] Diretorio nao existe: %BRIDGE_DIR%
)

echo.
echo [5/5] Limpando registros temporarios...
:: Limpar registros de eventos relacionados
echo [INFO] Limpando registros do sistema...
for /f "tokens=*" %%i in ('reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\EventLog\Application" /s /k 2^>nul ^| findstr /i "RLPonto" 2^>nul') do (
    reg delete "%%i" /f >nul 2>&1
)

:: Limpar possíveis entradas de firewall
netsh advfirewall firewall delete rule name="RLPONTO Bridge" >nul 2>&1
netsh advfirewall firewall delete rule name="Python Bridge ZK4500" >nul 2>&1

echo [OK] Limpeza de registros concluida

echo.
echo =======================================================
echo   DESINSTALACAO CONCLUIDA COM SUCESSO!
echo =======================================================
echo.
echo O Bridge ZK4500 foi completamente removido do sistema:
echo   - Servico do Windows: REMOVIDO
echo   - Diretorio C:\RLPonto-Bridge: REMOVIDO
echo   - Processos relacionados: FINALIZADOS
echo   - Registros do sistema: LIMPOS
echo.
if exist "%TEMP%\RLPONTO_BACKUP_LOGS\" (
    echo   - Backup de logs mantido em: %TEMP%\RLPONTO_BACKUP_LOGS\
    echo.
)
echo Para reinstalar o bridge, execute novamente o instalador.bat
echo.

:: Verificar se ainda existe algum vestígio
echo Verificando remocao completa...
set CLEANUP_ISSUES=0

sc query "%BRIDGE_NAME%" >nul 2>&1
if %errorLevel% == 0 (
    echo [AVISO] Servico ainda existe no sistema - pode ser necessario reiniciar
    set CLEANUP_ISSUES=1
) else (
    echo [OK] Servico completamente removido
)

if exist "%BRIDGE_DIR%" (
    echo [AVISO] Diretorio ainda existe - pode ser necessario reiniciar
    set CLEANUP_ISSUES=1
) else (
    echo [OK] Diretorio completamente removido
)

netstat -an | findstr :8080 >nul 2>&1
if %errorLevel% == 0 (
    echo [AVISO] Porta 8080 ainda em uso - pode ser necessario reiniciar
    set CLEANUP_ISSUES=1
) else (
    echo [OK] Porta 8080 liberada
)

if %CLEANUP_ISSUES% == 1 (
    echo.
    echo [RECOMENDACAO] Alguns componentes podem ainda estar em memoria.
    echo                Reinicie o computador para limpeza completa.
    echo.
) else (
    echo.
    echo [OK] Remocao 100%% completa - sistema limpo!
    echo.
)

:: Log final da desinstalação
if exist "%TEMP%\RLPONTO_BACKUP_LOGS\bridge_operations.log" (
    echo [%date% %time%] ======================================== >> "%TEMP%\RLPONTO_BACKUP_LOGS\bridge_operations.log"
    echo [%date% %time%] FINALIZADO: DESINSTALACAO_BRIDGE_ZK4500 - SUCCESS >> "%TEMP%\RLPONTO_BACKUP_LOGS\bridge_operations.log"
    echo [%date% %time%] ======================================== >> "%TEMP%\RLPONTO_BACKUP_LOGS\bridge_operations.log"
)

echo Pressione qualquer tecla para finalizar...
pause >nul 