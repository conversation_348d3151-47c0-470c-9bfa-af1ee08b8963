#!/usr/bin/env python3
"""
Teste da API de validação de decisão de justificativa
Sistema: RLPONTO-WEB
Data: 16/07/2025
"""

import requests
import json

def test_validacao_api():
    """Testar a API de validação de decisão"""
    
    url = "http://10.19.208.31/ponto-admin/api/validar-decisao-justificativa"
    
    # Dados de teste
    data = {
        "funcionario_id": 35,
        "data_registro": "2025-07-14",
        "decisao_rh": "aprovado",
        "observacoes_rh": "Teste de validação"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("🧪 Testando API de validação...")
    print(f"URL: {url}")
    print(f"Dados: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, json=data, headers=headers)
        
        print(f"\n📊 Status Code: {response.status_code}")
        print(f"📊 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print(f"✅ Resposta JSON:")
                print(json.dumps(json_data, indent=2, ensure_ascii=False))
            except json.JSONDecodeError as e:
                print(f"❌ Erro ao decodificar JSON: {e}")
                print(f"Resposta raw: {response.text[:500]}")
        else:
            print(f"❌ Erro HTTP: {response.status_code}")
            print(f"Resposta: {response.text[:500]}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Erro de conexão: {e}")

if __name__ == "__main__":
    test_validacao_api()
