#!/bin/bash

# Script para corrigir permissões do RLPONTO-WEB no servidor de produção
# Autor: AiNexus Tecnologia
# Data: 10/06/2025
# Uso: ./fix_permissions.sh

echo "=== CORREÇÃO DE PERMISSÕES - RLPONTO-WEB ==="
echo "Executando no servidor: ************"
echo "Diretório: /var/www/controle-ponto"
echo "=========================================="

# Navegar para o diretório do projeto
cd /var/www/controle-ponto

# Criar diretório de fotos caso não exista
echo "1. Criando diretório fotos_funcionarios..."
mkdir -p static/fotos_funcionarios

# Definir proprietário correto (www-data para servidores Apache/Nginx)
echo "2. Definindo proprietário www-data..."
chown -R www-data:www-data .

# Aplicar permissões para diretórios (755)
echo "3. Aplicando permissões para diretórios (755)..."
find . -type d -exec chmod 755 {} \;

# Aplicar permissões para arquivos (644)
echo "4. Aplicando permissões para arquivos (644)..."
find . -type f -exec chmod 644 {} \;

# Tornar arquivos Python executáveis
echo "5. Tornando arquivos Python executáveis..."
chmod +x *.py

# Permissões especiais para diretórios críticos
echo "6. Aplicando permissões especiais..."
chmod 755 static/fotos_funcionarios
chmod 755 static/
chmod 755 templates/
chmod 755 utils/

# Verificar resultado
echo "=========================================="
echo "✅ PERMISSÕES APLICADAS COM SUCESSO!"
echo "=========================================="
echo "Verificando diretório fotos_funcionarios:"
ls -la static/fotos_funcionarios/
echo "=========================================="
echo "Status do projeto:"
ls -la | head -10
echo "=========================================="
echo "🚀 O sistema está pronto para uso!" 