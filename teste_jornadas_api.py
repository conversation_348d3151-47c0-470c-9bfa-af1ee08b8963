#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar a API de jornadas disponíveis
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def testar_jornadas_api():
    """Testar a API de jornadas disponíveis"""
    print("🔍 TESTANDO API DE JORNADAS DISPONÍVEIS")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar estrutura da tabela jornadas_trabalho
        print("\n1. Verificando estrutura da tabela...")
        sql_estrutura = "DESCRIBE jornadas_trabalho"
        estrutura = db.execute_query(sql_estrutura)
        
        print("📋 Campos da tabela jornadas_trabalho:")
        for campo in estrutura:
            print(f"   - {campo['Field']}: {campo['Type']}")
        
        # 2. Verificar jornadas existentes
        print("\n2. Verificando jornadas existentes...")
        sql_jornadas = """
        SELECT 
            j.id, 
            j.nome_jornada, 
            j.empresa_id,
            j.ativa,
            j.padrao,
            e.razao_social as empresa_nome
        FROM jornadas_trabalho j
        JOIN empresas e ON j.empresa_id = e.id
        ORDER BY e.razao_social, j.padrao DESC
        """
        
        jornadas = db.execute_query(sql_jornadas)
        
        print(f"📊 Total de jornadas encontradas: {len(jornadas)}")
        for jornada in jornadas:
            status = "ATIVA" if jornada['ativa'] else "INATIVA"
            padrao = "PADRÃO" if jornada['padrao'] else "NORMAL"
            print(f"   - ID {jornada['id']}: {jornada['nome_jornada']} ({jornada['empresa_nome']}) - {status}, {padrao}")
        
        # 3. Testar a query da API
        print("\n3. Testando query da API...")
        sql_api = """
        SELECT 
            j.id, 
            j.nome_jornada as nome, 
            j.descricao,
            j.tipo_jornada,
            j.categoria_funcionario,
            j.seg_qui_entrada, 
            j.seg_qui_saida,
            j.sexta_entrada,
            j.sexta_saida,
            j.intervalo_inicio,
            j.intervalo_fim,
            j.ativa,
            j.padrao,
            e.razao_social as empresa_nome,
            CONCAT(
                TIME_FORMAT(j.seg_qui_entrada, '%%H:%%i'),
                ' às ',
                TIME_FORMAT(j.seg_qui_saida, '%%H:%%i')
            ) as carga_horaria
        FROM jornadas_trabalho j
        JOIN empresas e ON j.empresa_id = e.id
        WHERE j.ativa = TRUE
        ORDER BY e.razao_social, j.padrao DESC, j.nome_jornada
        """
        
        jornadas_api = db.execute_query(sql_api)
        
        print(f"✅ Query da API executada com sucesso!")
        print(f"📊 Jornadas ativas encontradas: {len(jornadas_api)}")
        
        for jornada in jornadas_api:
            print(f"   - {jornada['nome']} ({jornada['empresa_nome']}) - {jornada['carga_horaria']}")
        
        # 4. Simular resposta JSON
        print("\n4. Simulando resposta JSON...")
        resposta = {
            'success': True,
            'jornadas': []
        }
        
        for jornada in jornadas_api:
            jornada_json = {}
            for key, value in jornada.items():
                if hasattr(value, 'isoformat'):  # datetime/date
                    jornada_json[key] = value.isoformat()
                else:
                    jornada_json[key] = value
            resposta['jornadas'].append(jornada_json)
        
        print("📄 Resposta JSON simulada:")
        print(json.dumps(resposta, indent=2, ensure_ascii=False))
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    testar_jornadas_api()
