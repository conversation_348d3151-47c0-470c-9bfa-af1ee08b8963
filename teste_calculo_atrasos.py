#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 TESTE ESPECÍFICO PARA CÁLCULO DE ATRASOS
===========================================

Testa se os atrasos estão sendo calculados corretamente no relatório.
"""

import requests
import logging
from datetime import datetime
from bs4 import BeautifulSoup

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger('teste_atrasos')

def testar_calculo_atrasos():
    """
    Testa o cálculo de atrasos no relatório de impressão.
    """
    print("🔍 TESTE DE CÁLCULO DE ATRASOS")
    print("=" * 50)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 50)
    
    try:
        # Configurações
        base_url = "http://10.19.208.31"
        funcionario_id = 44  # Suelen (tem atrasos reais)
        
        # Fazer login
        session = requests.Session()
        
        logger.info("🔐 Fazendo login...")
        login_data = {
            'username': 'admin',
            'password': '@Ric6109'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code != 200:
            logger.error(f"❌ Erro no login: {login_response.status_code}")
            return False
        
        logger.info("✅ Login realizado com sucesso")
        
        # Acessar página de impressão
        logger.info("📄 Acessando página de impressão...")
        
        url_impressao = f"{base_url}/ponto-admin/funcionario/{funcionario_id}/imprimir?data_inicio=2025-07-15&data_fim=2025-07-17"
        
        response = session.get(url_impressao)
        
        if response.status_code != 200:
            logger.error(f"❌ Erro ao acessar página: {response.status_code}")
            return False
        
        logger.info("✅ Página de impressão carregada")
        
        # Analisar HTML
        soup = BeautifulSoup(response.text, 'html.parser')

        # Debug: Salvar HTML para análise
        with open('debug_html.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        logger.info("📄 HTML salvo em debug_html.html para análise")

        # Verificar se há dados na tabela
        tabela = soup.find('table', class_='records-table')
        if not tabela:
            # Tentar encontrar qualquer tabela
            todas_tabelas = soup.find_all('table')
            logger.error(f"❌ Tabela 'records-table' não encontrada. Tabelas encontradas: {len(todas_tabelas)}")

            # Verificar se há erro na página
            if "erro" in response.text.lower() or "error" in response.text.lower():
                logger.error("❌ Página contém erro")

            # Verificar se há redirecionamento
            if "login" in response.text.lower():
                logger.error("❌ Página redirecionou para login")

            return False
        
        # Analisar registros
        linhas = tabela.find('tbody').find_all('tr')
        logger.info(f"📊 Encontradas {len(linhas)} linhas de registros")
        
        atrasos_encontrados = []
        
        for i, linha in enumerate(linhas):
            colunas = linha.find_all('td')
            if len(colunas) >= 9:
                data = colunas[0].get_text(strip=True)
                entrada = colunas[2].get_text(strip=True)
                desc_cell = colunas[8].get_text(strip=True)  # Coluna "Desc."
                
                logger.info(f"📅 Linha {i+1}: {data} - Entrada: {entrada} - Desc: {desc_cell}")
                
                if desc_cell and desc_cell != '00:00' and desc_cell != '-':
                    atrasos_encontrados.append({
                        'data': data,
                        'entrada': entrada,
                        'desconto': desc_cell
                    })
        
        # Verificar totais no rodapé
        total_atrasos_element = soup.find('span', id='total-atrasos')
        if total_atrasos_element:
            total_atrasos = total_atrasos_element.get_text(strip=True)
            logger.info(f"📊 Total de atrasos no rodapé: '{total_atrasos}'")
        else:
            total_atrasos = "Não encontrado"
            logger.warning("⚠️ Total de atrasos não encontrado no rodapé")
        
        # Resultados
        print(f"\n📊 RESULTADOS DO TESTE:")
        print(f"   Registros analisados: {len(linhas)}")
        print(f"   Atrasos encontrados: {len(atrasos_encontrados)}")
        print(f"   Total no rodapé: {total_atrasos}")
        
        if atrasos_encontrados:
            print(f"\n📋 DETALHES DOS ATRASOS:")
            for atraso in atrasos_encontrados:
                print(f"   {atraso['data']}: Entrada {atraso['entrada']} - Desconto {atraso['desconto']}")
        else:
            print(f"\n⚠️ NENHUM ATRASO DETECTADO")
            print(f"   Isso pode indicar:")
            print(f"   - Funcionário não teve atrasos")
            print(f"   - Cálculo de atrasos não está funcionando")
            print(f"   - Tolerância está cobrindo os atrasos")
        
        # Verificar se há entrada às 07:37 (que deveria ser atraso)
        entrada_0737_encontrada = False
        for linha in linhas:
            colunas = linha.find_all('td')
            if len(colunas) >= 3:
                entrada = colunas[2].get_text(strip=True)
                if entrada == "07:37":
                    entrada_0737_encontrada = True
                    desc = colunas[8].get_text(strip=True) if len(colunas) >= 9 else "N/A"
                    print(f"\n🔍 ANÁLISE ESPECÍFICA - Entrada 07:37:")
                    print(f"   Desconto calculado: {desc}")
                    if desc == "00:00" or desc == "-":
                        print(f"   ❌ PROBLEMA: Entrada 07:37 deveria ter atraso!")
                        print(f"   (Considerando horário padrão 08:00 + tolerância)")
                    else:
                        print(f"   ✅ Atraso calculado corretamente")
                    break
        
        if not entrada_0737_encontrada:
            print(f"\n⚠️ Entrada 07:37 não encontrada nos dados")
        
        return len(atrasos_encontrados) > 0 or total_atrasos != "0h 00min"
        
    except Exception as e:
        logger.error(f"❌ Erro durante o teste: {e}")
        return False

def main():
    sucesso = testar_calculo_atrasos()
    
    print(f"\n" + "=" * 50)
    print(f"📊 RESULTADO FINAL DO TESTE")
    print(f"=" * 50)
    
    if sucesso:
        print(f"✅ Cálculo de atrasos: FUNCIONANDO")
        print(f"   Atrasos foram detectados e calculados")
    else:
        print(f"❌ Cálculo de atrasos: NÃO FUNCIONANDO")
        print(f"   Nenhum atraso foi detectado ou calculado")
    
    print(f"\n💡 PRÓXIMOS PASSOS:")
    if not sucesso:
        print(f"   1. Verificar se a função calcular_atraso_entrada está sendo chamada")
        print(f"   2. Verificar se os horários esperados estão configurados")
        print(f"   3. Verificar se a tolerância está sendo aplicada corretamente")
        print(f"   4. Verificar logs do servidor para erros")
    else:
        print(f"   1. Verificar se os valores estão corretos")
        print(f"   2. Testar com outros funcionários")
        print(f"   3. Validar cálculos manualmente")

if __name__ == "__main__":
    main()
