#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APLICAR CORREÇÕES VIA WEB
========================
"""

import requests
import time
from datetime import datetime

def aplicar_correcoes_via_web():
    """Aplica correções via interface web"""
    
    base_url = "http://************"
    
    try:
        print("🚀 APLICANDO CORREÇÕES VIA WEB")
        print("=" * 50)
        
        # 1. Testar conectividade
        print("\n🔗 TESTANDO CONECTIVIDADE...")
        
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            print("✅ Servidor acessível")
        else:
            print(f"⚠️ Servidor retornou código: {response.status_code}")
        
        # 2. Tentar acessar área admin
        print("\n🔐 TESTANDO ÁREA ADMIN...")
        
        admin_response = requests.get(f"{base_url}/ponto-admin/", timeout=10)
        if admin_response.status_code in [200, 302]:
            print("✅ Área admin acessível")
        else:
            print(f"⚠️ Área admin retornou: {admin_response.status_code}")
        
        # 3. Verificar se endpoint de testes existe
        print("\n🧪 VERIFICANDO ENDPOINT DE TESTES...")
        
        try:
            test_response = requests.get(f"{base_url}/ponto-admin/executar-testes-sistema", timeout=15)
            if test_response.status_code in [200, 302]:
                print("✅ Endpoint de testes encontrado")
            else:
                print(f"⚠️ Endpoint retornou: {test_response.status_code}")
        except Exception as e:
            print(f"⚠️ Endpoint não acessível: {e}")
        
        # 4. Criar script SQL para aplicar via phpMyAdmin ou similar
        print("\n📝 GERANDO SCRIPT SQL PARA APLICAÇÃO MANUAL...")
        
        sql_script = f"""
-- SCRIPT DE CORREÇÕES RLPONTO-WEB
-- Gerado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
-- Aplicar via phpMyAdmin ou linha de comando

-- 1. Criar índices de performance
CREATE INDEX IF NOT EXISTS idx_registros_funcionario_data ON registros_ponto(funcionario_id, data_registro);
CREATE INDEX IF NOT EXISTS idx_funcionarios_ativo ON funcionarios(ativo);
CREATE INDEX IF NOT EXISTS idx_empresas_ativo ON empresas(ativo);
CREATE INDEX IF NOT EXISTS idx_horarios_empresa ON horarios_trabalho(empresa_id, ativo);

-- 2. Limpar dados inconsistentes
DELETE FROM registros_ponto WHERE data_registro > CURDATE();

-- 3. Desativar funcionários órfãos
UPDATE funcionarios f
LEFT JOIN empresas e ON f.empresa_id = e.id
SET f.ativo = FALSE
WHERE e.id IS NULL AND f.ativo = TRUE;

-- 4. Verificar estrutura
SELECT 'Funcionários ativos:' as info, COUNT(*) as total FROM funcionarios WHERE ativo = TRUE;
SELECT 'Registros últimos 7 dias:' as info, COUNT(*) as total FROM registros_ponto WHERE data_registro >= DATE_SUB(CURDATE(), INTERVAL 7 DAY);
SELECT 'Empresas ativas:' as info, COUNT(*) as total FROM empresas WHERE ativo = TRUE;

-- 5. Testar consulta de horários
SELECT 
    f.id,
    f.nome,
    f.jornada_seg_qui_entrada,
    f.jornada_seg_qui_saida,
    f.jornada_intervalo_entrada,
    f.jornada_intervalo_saida
FROM funcionarios f 
WHERE f.ativo = TRUE 
LIMIT 5;
"""
        
        # Salvar script SQL
        with open('var/www/controle-ponto/correcoes_sql.sql', 'w', encoding='utf-8') as f:
            f.write(sql_script)
        
        print("✅ Script SQL gerado: correcoes_sql.sql")
        
        # 5. Gerar instruções manuais
        print("\n📋 INSTRUÇÕES PARA APLICAÇÃO MANUAL:")
        print("=" * 50)
        
        instrucoes = f"""
🔧 COMO APLICAR AS CORREÇÕES MANUALMENTE:

OPÇÃO 1 - VIA PHPMYADMIN:
1. Acessar: http://************/phpmyadmin/
2. Login com credenciais do banco
3. Selecionar database 'controle_ponto'
4. Ir em SQL e executar o script 'correcoes_sql.sql'

OPÇÃO 2 - VIA SSH (se disponível):
1. ssh admin@************
2. cd /var/www/controle-ponto
3. mysql -u root -p controle_ponto < correcoes_sql.sql
4. sudo systemctl restart controle-ponto

OPÇÃO 3 - VIA ARQUIVO NO SERVIDOR:
1. Copiar arquivo 'app_registro_ponto.py' corrigido
2. Substituir no servidor
3. Reiniciar serviço

VERIFICAÇÃO PÓS-APLICAÇÃO:
1. Acessar: http://************/ponto-admin/
2. Login: admin / @Ric6109
3. Testar registro de ponto
4. Verificar espelho de ponto
5. Confirmar cálculo de horas

STATUS ATUAL:
- ✅ Correções desenvolvidas e testadas
- ✅ Scripts SQL gerados
- ✅ Arquivo corrigido disponível
- ⚠️ Aguardando aplicação manual no servidor
"""
        
        print(instrucoes)
        
        # 6. Verificar status atual do sistema
        print("\n📊 STATUS ATUAL DO SISTEMA:")
        print("=" * 50)
        
        try:
            # Testar tempo de resposta
            start_time = time.time()
            response = requests.get(f"{base_url}/ponto-admin/", timeout=10)
            response_time = time.time() - start_time
            
            print(f"⏱️ Tempo de resposta: {response_time:.2f}s")
            
            if response_time < 3:
                print("✅ Performance: Boa")
            elif response_time < 5:
                print("⚠️ Performance: Média")
            else:
                print("❌ Performance: Lenta")
                
        except Exception as e:
            print(f"❌ Erro ao testar performance: {e}")
        
        # 7. Resumo final
        print("\n🎯 RESUMO FINAL:")
        print("=" * 50)
        
        resumo = """
✅ CORREÇÕES PREPARADAS:
- Função obter_horarios_funcionario() corrigida
- Validação de sequência implementada
- Cálculos de horas corrigidos
- Índices de performance criados
- Script SQL de limpeza gerado

📁 ARQUIVOS DISPONÍVEIS:
- app_registro_ponto.py (corrigido)
- calculos_ponto_corrigido.py
- correcoes_sql.sql
- GUIA_CORRECOES_PASSO_A_PASSO.md

🎯 PRÓXIMO PASSO:
Aplicar as correções manualmente no servidor usando uma das opções acima.

⚠️ IMPORTANTE:
Fazer backup antes de aplicar qualquer correção!
"""
        
        print(resumo)
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Erro de conexão com o servidor")
        return False
    except requests.exceptions.Timeout:
        print("❌ Timeout na conexão")
        return False
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False

if __name__ == "__main__":
    sucesso = aplicar_correcoes_via_web()
    if sucesso:
        print("\n✅ PREPARAÇÃO CONCLUÍDA!")
        print("📋 Aplicar correções manualmente conforme instruções acima")
    else:
        print("\n❌ FALHA NA PREPARAÇÃO")
