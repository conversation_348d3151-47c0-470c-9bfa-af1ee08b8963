class ModalBiometriaUniversal {
    constructor() {
        this.biometricClient = new UniversalBiometricClient();
        this.selectedDeviceId = null;
        this.capturedTemplate = null;
        this.capturaAtiva = false;
    }

    async descobrirEExibirDispositivos() {
        try {
            const devices = await this.biometricClient.descobrirDispositivos();
            const container = document.getElementById('listaDispositivos');
            
            if (devices.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Nenhum dispositivo biométrico encontrado.
                        <br><small>Verifique se o leitor está conectado e os drivers instalados.</small>
                    </div>
                `;
                return;
            }
            
            let html = '';
            devices.forEach((device, index) => {
                const isConnected = device.status === 'OK';
                const statusIcon = isConnected ? 
                    '<i class="fas fa-check-circle text-success"></i>' : 
                    '<i class="fas fa-exclamation-circle text-danger"></i>';
                
                const statusClass = isConnected ? 'text-success' : 'text-danger';
                const statusText = isConnected ? 'Conectado' : 'Desconectado';
                const isSimulator = device.status === 'simulation';
                
                html += `
                    <div class="device-item card mb-2 ${index === 0 ? 'border-primary' : ''}" 
                         onclick="selecionarDispositivo('${device.id}')">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${device.name}</strong>
                                    <br><small class="text-muted">${device.manufacturer}</small>
                                </div>
                                <div class="text-end">
                                    ${statusIcon}
                                    <br><small class="${isSimulator ? 'text-warning' : statusClass}">${statusText}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
            // Verificar se existe pelo menos um dispositivo conectado (status = OK)
            const connectedDevice = devices.find(device => device.status === 'OK');
            
            if (connectedDevice) {
                // Se tem dispositivo conectado, mostrar interface normal
                this.mostrarInterface();
            } else {
                // Se não tem dispositivo conectado, mostrar alerta
                container.innerHTML += `
                    <div class="alert alert-danger mt-3">
                        <i class="fas fa-plug"></i>
                        <strong>Dispositivo Desconectado</strong>
                        <br>O leitor biométrico está fisicamente desconectado.
                        <br><small>Por favor, conecte o dispositivo e clique em "Atualizar".</small>
                    </div>
                    <div style="margin-top: 15px;">
                        <button class="btn btn-primary" onclick="atualizarListaDispositivos()">
                            <i class="fas fa-sync"></i> Atualizar
                        </button>
                        <button class="btn btn-secondary" onclick="testeSemDispositivo()">
                            <i class="fas fa-cog"></i> Diagnóstico
                        </button>
                    </div>
                `;
            }
            
        } catch (error) {
            document.getElementById('listaDispositivos').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Serviço Biométrico Offline</strong>
                    <br>Modo autônomo ativado - sistema funcionando localmente.
                    <br><small>Para detecção automática, inicie: <code>python biometric_service_simple.py</code></small>
                </div>
                <div style="margin-top: 15px;">
                    <button style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;" onclick="atualizarListaDispositivos()">
                        <i class="fas fa-redo"></i> Tentar Novamente
                    </button>
                    <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;" onclick="ativarModoDemo()">
                        <i class="fas fa-play"></i> Continuar Mesmo Assim
                    </button>
                </div>
            `;
            
            // Mostrar interface de captura mesmo com erro
            this.mostrarInterfaceDemo();
        }
    }
    
    // Métodos auxiliares
    mostrarInterface() {
        // Mostrar interface de captura para dispositivo real conectado
        document.getElementById('interfaceCaptura').style.display = 'block';
    }
    
    mostrarInterfaceDemo() {
        // Mostrar interface de captura em modo demonstração
        document.getElementById('interfaceCaptura').style.display = 'block';
        document.getElementById('instrucoes').innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Modo Demonstração</strong>
                <br>O sistema está funcionando em modo simulado.
                <br>Algumas funcionalidades podem estar limitadas.
            </div>
        `;
    }
    
    resetarEstados() {
        this.selectedDeviceId = null;
        this.capturedTemplate = null;
        this.capturaAtiva = false;
        document.getElementById('btnSalvar').disabled = true;
    }
    
    atualizarStatus(message, type = 'info') {
        const statusElement = document.getElementById('statusSistema');
        if (!statusElement) return;
        
        let icon = 'info-circle';
        let alertClass = 'alert-info';
        
        switch (type) {
            case 'success':
                icon = 'check-circle';
                alertClass = 'alert-success';
                break;
            case 'warning':
                icon = 'exclamation-triangle';
                alertClass = 'alert-warning';
                break;
            case 'error':
                icon = 'times-circle';
                alertClass = 'alert-danger';
                break;
        }
        
        statusElement.className = `alert ${alertClass}`;
        statusElement.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
    }
}

// Função global para selecionar dispositivo
function selecionarDispositivo(deviceId) {
    console.log('🔍 Selecionando dispositivo:', deviceId);
    
    // Verificar se a janela modal existe
    const modal = document.getElementById('modalBiometriaUniversal');
    if (!modal || !window.modalBiometriaUniversal) {
        console.error('❌ Erro: Modal biométrico não inicializado!');
        return;
    }
    
    // Definir dispositivo selecionado
    window.modalBiometriaUniversal.selectedDeviceId = deviceId;
    
    // Atualizar UI para mostrar dispositivo selecionado
    document.querySelectorAll('.device-item').forEach(item => {
        item.classList.remove('border-primary');
        item.classList.remove('bg-light');
    });
    
    const selectedItem = document.querySelector(`.device-item[onclick*="${deviceId}"]`);
    if (selectedItem) {
        selectedItem.classList.add('border-primary');
        selectedItem.classList.add('bg-light');
    }
    
    // Atualizar status
    window.modalBiometriaUniversal.atualizarStatus('Dispositivo selecionado. Pronto para capturar.', 'info');
}

// Função global para testar um dispositivo
async function testarDispositivoBiometrico(deviceId) {
    console.log('🧪 Testando dispositivo:', deviceId);
    
    // Mostrar modal de carregamento enquanto testa
    const loadingModal = document.createElement('div');
    loadingModal.classList.add('modal-testing');
    loadingModal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    `;
    
    loadingModal.innerHTML = `
        <div style="background: #1e293b; color: white; border-radius: 10px; padding: 20px; text-align: center; max-width: 400px; width: 90%;">
            <div style="font-size: 48px; margin-bottom: 20px;">🔄</div>
            <h4>Testando Dispositivo</h4>
            <p>Aguarde enquanto verificamos a comunicação...</p>
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
        </div>
    `;
    
    document.body.appendChild(loadingModal);
    
    try {
        // Verificar se temos acesso ao cliente biométrico
        let client = window.biometricClient;
        if (!client && window.modalBiometriaUniversal) {
            client = window.modalBiometriaUniversal.biometricClient;
        }
        
        if (!client) {
            throw new Error('Cliente biométrico não inicializado');
        }
        
        // Executar teste
        const result = await client.testarDispositivo(deviceId);
        
        // Remover modal de carregamento
        loadingModal.remove();
        
        // Exibir resultado do teste
        if (result.success && result.deviceStatus.conectado) {
            // Teste bem-sucedido - dispositivo conectado
            exibirModalResultado('✅ Teste Bem-sucedido', 
                `O dispositivo ${result.deviceStatus.id} está conectado e pronto para uso.`,
                'success');
        } else {
            // Teste falhou - dispositivo não conectado ou erro
            const mensagemErro = result.error || result.deviceStatus.message || 'Dispositivo não encontrado ou desconectado';
            exibirModalResultado('❌ Teste Falhou', mensagemErro, 'error');
        }
        
    } catch (error) {
        console.error('Erro ao testar dispositivo:', error);
        
        // Remover modal de carregamento
        loadingModal.remove();
        
        // Exibir erro
        exibirModalResultado('❌ Teste Falhou', 
            'Erro ao testar dispositivo: ' + (error.message || 'Erro desconhecido'),
            'error');
    }
}

// Função para exibir modal com resultado do teste
function exibirModalResultado(titulo, mensagem, tipo) {
    const modal = document.createElement('div');
    modal.classList.add('modal-resultado');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    `;
    
    const bgColor = tipo === 'success' ? '#10b981' : '#ef4444';
    const iconClass = tipo === 'success' ? 'check-circle' : 'times-circle';
    
    modal.innerHTML = `
        <div style="background: #1e293b; color: white; border-radius: 10px; padding: 30px; text-align: center; max-width: 400px; width: 90%;">
            <div style="font-size: 64px; margin-bottom: 20px; color: ${bgColor};">
                <i class="fas fa-${iconClass}"></i>
            </div>
            <h3 style="margin-bottom: 15px;">${titulo}</h3>
            <p>${mensagem}</p>
            <button onclick="this.parentNode.parentNode.remove()" class="btn btn-primary mt-3">
                Fechar
            </button>
        </div>
    `;
    
    document.body.appendChild(modal);
} 