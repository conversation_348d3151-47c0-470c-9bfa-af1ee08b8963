{% extends "base.html" %}

{% block title %}{{ titulo }} - RLPONTO-WEB{% endblock %}

{% block extra_css %}
<style>
    .config-biometric {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .biometric-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        padding: 30px;
        margin-bottom: 30px;
        animation: slideInUp 0.6s ease-out;
    }

    .biometric-header {
        border-bottom: 2px solid #f8f9fa;
        padding-bottom: 20px;
        margin-bottom: 30px;
    }

    .biometric-header h2 {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
    }

    .service-status {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        border-left: 5px solid;
    }

    .service-status.online {
        background: #d4edda;
        border-color: #28a745;
        color: #155724;
    }

    .service-status.offline {
        background: #f8d7da;
        border-color: #dc3545;
        color: #721c24;
    }

    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    .status-indicator.online {
        background: #28a745;
    }

    .status-indicator.offline {
        background: #dc3545;
    }

    .biometric-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 25px;
        border: 1px solid #e9ecef;
    }

    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 20px;
        font-size: 1.2rem;
    }

    .action-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .action-card:hover {
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .action-card .icon {
        font-size: 2rem;
        color: #667eea;
        margin-bottom: 15px;
    }

    .device-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        position: relative;
    }

    .device-card.registered {
        border-color: #28a745;
        background: #f8fff9;
    }

    .device-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .device-status.registered {
        background: #d4edda;
        color: #155724;
    }

    .device-status.available {
        background: #fff3cd;
        color: #856404;
    }

    .btn-biometric {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-biometric:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-danger-biometric {
        background: linear-gradient(45deg, #dc3545, #c82333);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
    }

    .btn-danger-biometric:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(220, 53, 69, 0.4);
        color: white;
    }

    .settings-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }

    .devices-list {
        max-height: 400px;
        overflow-y: auto;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
</style>
{% endblock %}

{% block content %}
<div class="config-biometric">
    <div class="container">
        <!-- Header -->
        <div class="biometric-container">
            <div class="biometric-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2><i class="fas fa-fingerprint me-3"></i>{{ titulo }}</h2>
                        <p class="text-muted mb-0">Gerenciamento e configuração de leitores biométricos universais</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="/configuracoes" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Voltar
                        </a>
                    </div>
                </div>
            </div>

            <!-- Status do Serviço -->
            <div class="service-status {% if service_running %}online{% else %}offline{% endif %}" id="serviceStatus">
                <div class="status-indicator {% if service_running %}online{% else %}offline{% endif %}"></div>
                <div>
                    <strong>Serviço Biométrico Universal:</strong>
                    <span id="serviceStatusText">
                        {% if service_running %}
                            Online e funcionando
                        {% else %}
                            Offline - necessário iniciar
                        {% endif %}
                    </span>
                </div>
                {% if not service_running %}
                <button class="btn btn-sm btn-biometric ms-auto" onclick="startBiometricService()">
                    <i class="fas fa-play me-2"></i>Iniciar Serviço
                </button>
                {% endif %}
            </div>
            
            <!-- Status Bridge Local (NOVO) -->
            <div class="service-status offline" id="bridge-status-container">
                <div class="status-indicator offline" id="bridge-status-indicator"></div>
                <div>
                    <strong>Bridge Biométrico Local:</strong>
                    <span id="bridge-status-text">Verificando...</span>
                </div>
                <button class="btn btn-sm btn-outline-secondary ms-auto" id="check-bridge-status">
                    <i class="fas fa-sync-alt me-2"></i>Verificar
                </button>
            </div>
        </div>

        <!-- Descoberta de Dispositivos -->
        <div class="biometric-container">
            <div class="biometric-section">
                <h3 class="section-title">
                    <i class="fas fa-search me-2"></i>Descoberta de Dispositivos
                </h3>
                
                <div class="row">
                    <div class="col-md-8">
                        <p class="text-muted">
                            Escaneie automaticamente por leitores biométricos conectados ao sistema.
                            Última descoberta: <strong>{{ last_discovery }}</strong>
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-biometric" onclick="discoverDevices()" id="btnDiscover">
                            <i class="fas fa-search me-2"></i>Descobrir Dispositivos
                        </button>
                    </div>
                </div>

                <div class="loading-spinner" id="discoveryLoading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="mt-2">Escaneando dispositivos...</p>
                </div>

                <div class="devices-list" id="devicesList">
                    <!-- Dispositivos descobertos aparecerão aqui -->
                </div>
            </div>
        </div>

        <!-- Dispositivos Registrados -->
        <div class="biometric-container">
            <div class="biometric-section">
                <h3 class="section-title">
                    <i class="fas fa-list me-2"></i>Dispositivos Registrados
                    <span class="badge bg-primary ms-2">{{ registered_devices|length }}</span>
                </h3>

                {% if registered_devices %}
                <div class="row">
                    {% for device_id, device_data in registered_devices.items() %}
                    <div class="col-md-6 mb-3">
                        <div class="device-card registered">
                            <div class="device-status registered">Registrado</div>
                            <h5>{{ device_data.info.name }}</h5>
                            <p class="text-muted mb-2">
                                <strong>Fabricante:</strong> {{ device_data.info.manufacturer }}<br>
                                <strong>Tipo:</strong> {{ device_data.info.type }}<br>
                                <strong>ID:</strong> {{ device_id[:16] }}...
                            </p>
                            <small class="text-muted">
                                Registrado em: {{ device_data.registered_at[:19] }}
                            </small>
                            <div class="mt-3">
                                <button class="btn btn-sm btn-biometric me-2" onclick="testDevice('{{ device_id }}')">
                                    <i class="fas fa-vial me-1"></i>Testar
                                </button>
                                <button class="btn btn-sm btn-danger-biometric" onclick="removeDevice('{{ device_id }}')">
                                    <i class="fas fa-trash me-1"></i>Remover
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>Nenhum dispositivo registrado ainda.<br>Use a descoberta de dispositivos para encontrar e registrar leitores biométricos.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Configurações Avançadas -->
        <div class="biometric-container">
            <div class="biometric-section">
                <h3 class="section-title">
                    <i class="fas fa-cogs me-2"></i>Configurações Avançadas
                </h3>

                <form id="settingsForm" onsubmit="saveSettings(event)">
                    <div class="settings-grid">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Configurações Gerais</h6>
                                
                                <div class="mb-3">
                                    <label class="form-label">Serviço Habilitado</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="serviceEnabled" 
                                               {% if settings.service_enabled %}checked{% endif %}>
                                        <label class="form-check-label" for="serviceEnabled">
                                            Habilitar serviço biométrico
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Auto-descoberta</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoDiscovery" 
                                               {% if settings.auto_discovery %}checked{% endif %}>
                                        <label class="form-check-label" for="autoDiscovery">
                                            Descoberta automática de dispositivos
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Configurações de Captura</h6>
                                
                                <div class="mb-3">
                                    <label for="sensitivityLevel" class="form-label">Nível de Sensibilidade</label>
                                    <select class="form-select" id="sensitivityLevel">
                                        <option value="low" {% if settings.sensitivity_level == 'low' %}selected{% endif %}>Baixa</option>
                                        <option value="medium" {% if settings.sensitivity_level == 'medium' %}selected{% endif %}>Média</option>
                                        <option value="high" {% if settings.sensitivity_level == 'high' %}selected{% endif %}>Alta</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="timeoutSeconds" class="form-label">Timeout (segundos)</label>
                                    <input type="number" class="form-control" id="timeoutSeconds" 
                                           value="{{ settings.timeout_seconds }}" min="10" max="60">
                                </div>

                                <div class="mb-3">
                                    <label for="maxAttempts" class="form-label">Máximo de Tentativas</label>
                                    <input type="number" class="form-control" id="maxAttempts" 
                                           value="{{ settings.max_attempts }}" min="1" max="10">
                                </div>

                                <div class="mb-3">
                                    <label for="qualityThreshold" class="form-label">Limite de Qualidade (%)</label>
                                    <input type="number" class="form-control" id="qualityThreshold" 
                                           value="{{ settings.quality_threshold }}" min="30" max="100">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-biometric btn-lg">
                            <i class="fas fa-save me-2"></i>Salvar Configurações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Teste -->
<div class="modal fade" id="testModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Teste de Dispositivo Biométrico</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="testModalBody">
                <!-- Conteúdo do teste -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Scripts originais -->
<script src="{{ url_for('static', filename='js/biometria-producao.js') }}"></script>

<!-- Novo Script para Bridge Direct -->
<script src="{{ url_for('static', filename='js/biometria-bridge-direct.js') }}"></script>

<script>
// Iniciar bridge biométrica com debug ativado
const biometricBridge = new BiometricBridge({
    debug: true
});

// Event listener para mudanças de status do bridge
biometricBridge.addEventListener('bridge-status-change', updateBridgeStatusUI);

// Atualiza UI de acordo com status do bridge
function updateBridgeStatusUI(status) {
    const container = document.getElementById('bridge-status-container');
    const indicator = document.getElementById('bridge-status-indicator');
    const text = document.getElementById('bridge-status-text');
    
    if (status.available) {
        container.className = 'service-status online';
        indicator.className = 'status-indicator online';
        text.innerText = `Online - Versão ${status.version || 'N/A'}`;
    } else {
        container.className = 'service-status offline';
        indicator.className = 'status-indicator offline';
        text.innerText = `Offline - ${status.message || 'Serviço indisponível'}`;
    }
}

// Configurar botão para verificar status do bridge
document.getElementById('check-bridge-status').addEventListener('click', async () => {
    try {
        document.getElementById('bridge-status-text').innerText = 'Verificando...';
        await biometricBridge.checkBridgeAvailability();
    } catch (error) {
        console.error('Erro ao verificar status do bridge:', error);
    }
});

// Função modificada para detectar dispositivos diretamente via bridge local
async function discoverDevices() {
    const btn = document.getElementById('btnDiscover');
    const loading = document.getElementById('discoveryLoading');
    const devicesList = document.getElementById('devicesList');
    
    try {
        btn.disabled = true;
        loading.style.display = 'block';
        devicesList.innerHTML = '';
        
        // Usar bridge local diretamente em vez do backend
        const devices = await biometricBridge.detectDevices();
        
        displayDevices(devices);
        showSuccess('Descoberta concluída com sucesso!');
    } catch (error) {
        showError('Erro na descoberta: ' + error.message);
    } finally {
        btn.disabled = false;
        loading.style.display = 'none';
    }
}

// Função modificada para testar dispositivo diretamente via bridge
async function testDevice(deviceId) {
    const modal = new bootstrap.Modal(document.getElementById('testModal'));
    const modalBody = document.getElementById('testModalBody');
    
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status"></div>
            <p>Testando dispositivo biométrico...</p>
            <small class="text-muted">Posicione seu dedo no leitor</small>
        </div>
    `;
    
    modal.show();
    
    try {
        // Usar bridge local diretamente em vez do backend
        const result = await biometricBridge.testDevice(deviceId);
        
        if (result.success) {
            modalBody.innerHTML = `
                <div class="text-center text-success">
                    <i class="fas fa-check-circle fa-3x mb-3"></i>
                    <h5>Teste Concluído com Sucesso!</h5>
                    <p class="text-muted">O dispositivo está funcionando corretamente.</p>
                </div>
            `;
        } else {
            modalBody.innerHTML = `
                <div class="text-center text-danger">
                    <i class="fas fa-times-circle fa-3x mb-3"></i>
                    <h5>Falha no Teste</h5>
                    <p class="text-muted">${result.message}</p>
                </div>
            `;
        }
    } catch (error) {
        modalBody.innerHTML = `
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h5>Erro no Teste</h5>
                <p class="text-muted">${error.message}</p>
            </div>
        `;
    }
}

// Funções de gerenciamento biométrico

async function startBiometricService() {
    try {
        showLoading('Iniciando serviço biométrico...');
        
        const response = await fetch('/configuracoes/biometria/api/service/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(result.message);
            updateServiceStatus(true);
        } else {
            showError(result.message);
        }
    } catch (error) {
        showError('Erro ao iniciar serviço: ' + error.message);
    } finally {
        hideLoading();
    }
}

function displayDevices(devices) {
    const devicesList = document.getElementById('devicesList');
    
    if (devices.length === 0) {
        devicesList.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-search fa-2x mb-3"></i>
                <p>Nenhum dispositivo biométrico encontrado.</p>
                <small>Verifique se os leitores estão conectados e com drivers instalados.</small>
            </div>
        `;
        return;
    }
    
    devicesList.innerHTML = devices.map(device => `
        <div class="device-card">
            <div class="device-status available">Disponível</div>
            <h5>${device.name}</h5>
            <p class="text-muted mb-2">
                <strong>Fabricante:</strong> ${device.manufacturer}<br>
                <strong>Tipo:</strong> ${device.type}<br>
                <strong>Driver:</strong> ${device.driver}<br>
                <strong>ID:</strong> ${device.id}
            </p>
            <div class="mt-3">
                <button class="btn btn-sm btn-biometric me-2" onclick="registerDevice('${device.id}', ${JSON.stringify(device).replace(/"/g, '&quot;')})">
                    <i class="fas fa-plus me-1"></i>Registrar
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="testDevice('${device.id}')">
                    <i class="fas fa-vial me-1"></i>Testar
                </button>
            </div>
        </div>
    `).join('');
}

async function registerDevice(deviceId, deviceInfo) {
    try {
        showLoading('Registrando dispositivo...');
        
        const response = await fetch('/configuracoes/biometria/api/devices/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                device_id: deviceId,
                device_info: deviceInfo
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(result.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showError(result.message);
        }
    } catch (error) {
        showError('Erro ao registrar dispositivo: ' + error.message);
    } finally {
        hideLoading();
    }
}

async function removeDevice(deviceId) {
    if (!confirm('Tem certeza que deseja remover este dispositivo?')) {
        return;
    }
    
    try {
        showLoading('Removendo dispositivo...');
        
        const response = await fetch('/configuracoes/biometria/api/devices/remove', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                device_id: deviceId
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(result.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showError(result.message);
        }
    } catch (error) {
        showError('Erro ao remover dispositivo: ' + error.message);
    } finally {
        hideLoading();
    }
}

async function saveSettings(event) {
    event.preventDefault();
    
    const settings = {
        service_enabled: document.getElementById('serviceEnabled').checked,
        auto_discovery: document.getElementById('autoDiscovery').checked,
        sensitivity_level: document.getElementById('sensitivityLevel').value,
        timeout_seconds: parseInt(document.getElementById('timeoutSeconds').value),
        max_attempts: parseInt(document.getElementById('maxAttempts').value),
        quality_threshold: parseInt(document.getElementById('qualityThreshold').value)
    };
    
    try {
        showLoading('Salvando configurações...');
        
        const response = await fetch('/configuracoes/biometria/api/settings/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(result.message);
        } else {
            showError(result.message);
        }
    } catch (error) {
        showError('Erro ao salvar configurações: ' + error.message);
    } finally {
        hideLoading();
    }
}

function updateServiceStatus(isOnline) {
    const statusDiv = document.getElementById('serviceStatus');
    const statusText = document.getElementById('serviceStatusText');
    
    if (isOnline) {
        statusDiv.className = 'service-status online';
        statusText.textContent = 'Online e funcionando';
    } else {
        statusDiv.className = 'service-status offline';
        statusText.textContent = 'Offline - necessário iniciar';
    }
}

// Utilitários de notificação
function showSuccess(message) {
    alert('✅ ' + message);
}

function showError(message) {
    alert('❌ ' + message);
}

function showLoading(message) {
    console.log('Loading: ' + message);
}

function hideLoading() {
    console.log('Loading hidden');
}

// Verificar status do serviço ao carregar a página
document.addEventListener('DOMContentLoaded', function() {
    // Verificar status do serviço a cada 10 segundos
    setInterval(async () => {
        try {
            const response = await fetch('/configuracoes/biometria/api/service/status');
            const result = await response.json();
            updateServiceStatus(result.running);
        } catch (error) {
            console.error('Erro ao verificar status:', error);
        }
    }, 10000);
});
</script>
{% endblock %} 