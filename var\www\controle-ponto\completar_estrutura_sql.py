#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para completar a estrutura SQL das views de relatórios
"""

import pymysql
from utils.config import Config

def criar_views_relatorios():
    """
    Cria as views necessárias para relatórios.
    """
    try:
        config = Config.get_database_url()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        print("🔧 Criando views de relatórios...")
        
        # View de relatórios de pontos
        view_relatorio_sql = """
        CREATE OR REPLACE VIEW vw_relatorio_pontos AS
        SELECT 
            rp.id,
            rp.funcionario_id,
            f.nome_completo,
            f.matricula_empresa,
            f.cpf,
            rp.data_hora,
            DATE(rp.data_hora) as data_registro,
            TIME(rp.data_hora) as hora_registro,
            rp.tipo_registro,
            CASE rp.tipo_registro
                WHEN 'entrada_manha' THEN 'Entrada Manhã'
                WHEN 'saida_almoco' THEN 'Saída Almoço'
                WHEN 'entrada_tarde' THEN 'Entrada Tarde'
                WHEN 'saida' THEN 'Saída'
            END AS tipo_descricao,
            rp.metodo_registro,
            CASE 
                WHEN rp.metodo_registro = 'biometrico' THEN 'Biométrico'
                ELSE 'Manual'
            END AS metodo_descricao,
            COALESCE(f.setor_obra, f.setor, 'Não informado') as setor,
            f.cargo,
            COALESCE(f.empresa, 'Não informado') as empresa,
            rp.qualidade_biometria,
            rp.observacoes,
            rp.ip_origem,
            rp.criado_em,
            rp.criado_por,
            CASE 
                WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atraso'
                WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atraso'
                ELSE 'Pontual'
            END AS status_pontualidade
        FROM registros_ponto rp
        INNER JOIN funcionarios f ON rp.funcionario_id = f.id
        WHERE f.status_cadastro = 'Ativo' OR f.ativo = 1
        ORDER BY rp.data_hora DESC
        """
        
        cursor.execute(view_relatorio_sql)
        print("✅ View vw_relatorio_pontos criada")
        
        # View de estatísticas
        view_estatisticas_sql = """
        CREATE OR REPLACE VIEW vw_estatisticas_pontos AS
        SELECT 
            DATE(rp.data_hora) as data_registro,
            COUNT(*) as total_registros,
            SUM(CASE WHEN rp.metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
            SUM(CASE WHEN rp.metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais,
            SUM(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN 1 ELSE 0 END) as entradas_manha,
            SUM(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN 1 ELSE 0 END) as saidas_almoco,
            SUM(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN 1 ELSE 0 END) as entradas_tarde,
            SUM(CASE WHEN rp.tipo_registro = 'saida' THEN 1 ELSE 0 END) as saidas,
            SUM(CASE 
                WHEN (rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00') OR
                     (rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00')
                THEN 1 ELSE 0 
            END) as atrasos,
            COUNT(DISTINCT rp.funcionario_id) as funcionarios_registraram
        FROM registros_ponto rp
        INNER JOIN funcionarios f ON rp.funcionario_id = f.id
        WHERE f.status_cadastro = 'Ativo' OR f.ativo = 1
        GROUP BY DATE(rp.data_hora)
        ORDER BY data_registro DESC
        """
        
        cursor.execute(view_estatisticas_sql)
        print("✅ View vw_estatisticas_pontos criada")
        
        conn.commit()
        
        # Verificar estrutura final
        print("\n📋 Verificando estrutura final...")
        
        # Listar tabelas
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"📊 Tabelas: {len(tables)}")
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
            count = cursor.fetchone()[0]
            print(f"  - {table[0]}: {count} registros")
        
        # Listar views
        cursor.execute("SHOW FULL TABLES WHERE Table_Type = 'VIEW'")
        views = cursor.fetchall()
        print(f"📈 Views: {len(views)}")
        for view in views:
            print(f"  - {view[0]}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Completando estrutura SQL do sistema...")
    if criar_views_relatorios():
        print("\n✅ Estrutura SQL completa!")
    else:
        print("\n❌ Falha ao completar estrutura.") 