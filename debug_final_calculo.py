#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 DEBUG FINAL DO CÁLCULO
=========================

Vamos descobrir exatamente onde está o problema.
"""

from datetime import datetime, time

def debug_calculo_completo():
    """
    Debug completo do cálculo.
    """
    print("🔍 DEBUG FINAL DO CÁLCULO")
    print("=" * 50)
    
    # Dados exatos do banco
    entrada = time(8, 0, 0)      # 08:00:00
    saida_almoco = time(9, 5, 0) # 09:05:00
    
    print(f"📅 Dados do banco:")
    print(f"   Entrada: {entrada}")
    print(f"   Saída Almoço: {saida_almoco}")
    
    # Calcular como o sistema deveria fazer
    hoje = datetime.now().date()
    dt_entrada = datetime.combine(hoje, entrada)
    dt_saida_almoco = datetime.combine(hoje, saida_almoco)
    
    print(f"\n🕐 Conversão para datetime:")
    print(f"   Entrada: {dt_entrada}")
    print(f"   Saída Almoço: {dt_saida_almoco}")
    
    # Diferença
    diferenca = dt_saida_almoco - dt_entrada
    print(f"\n⏱️  Diferença:")
    print(f"   Timedelta: {diferenca}")
    print(f"   Total segundos: {diferenca.total_seconds()}")
    
    # Conversão para horas decimal
    horas_decimal = diferenca.total_seconds() / 3600
    print(f"\n🧮 Conversão para horas decimal:")
    print(f"   Horas decimal: {horas_decimal}")
    print(f"   Horas decimal (6 casas): {horas_decimal:.6f}")
    
    # Função arredondar_horas_correto (nova)
    horas_corrigidas = round(horas_decimal, 6)
    print(f"   Função corrigida: {horas_corrigidas}")
    
    # Template - conversão via segundos (nova lógica CORRIGIDA)
    print(f"\n🔧 TEMPLATE - LÓGICA NOVA CORRIGIDA (via segundos com round):")
    total_decimal = horas_corrigidas
    segundos_totais = round(total_decimal * 3600)
    horas_inteiras = int(segundos_totais // 3600)
    minutos_inteiros = int((segundos_totais % 3600) // 60)
    
    print(f"   Total decimal: {total_decimal}")
    print(f"   Segundos totais: {segundos_totais}")
    print(f"   Horas inteiras: {horas_inteiras}")
    print(f"   Minutos inteiros: {minutos_inteiros}")
    print(f"   Resultado: {horas_inteiras}h {minutos_inteiros:02d}min")
    
    # Template - lógica antiga (para comparação)
    print(f"\n❌ TEMPLATE - LÓGICA ANTIGA:")
    horas_inteiras_old = int(total_decimal)
    minutos_decimal_old = (total_decimal - horas_inteiras_old) * 60
    minutos_old = int(minutos_decimal_old)
    print(f"   Horas inteiras: {horas_inteiras_old}")
    print(f"   Minutos decimal: {minutos_decimal_old}")
    print(f"   Minutos: {minutos_old}")
    print(f"   Resultado: {horas_inteiras_old}h {minutos_old:02d}min")
    
    # Verificação
    print(f"\n🎯 VERIFICAÇÃO:")
    print(f"   Esperado: 1h 05min")
    print(f"   Lógica nova: {horas_inteiras}h {minutos_inteiros:02d}min")
    print(f"   Lógica antiga: {horas_inteiras_old}h {minutos_old:02d}min")
    
    if f"{horas_inteiras}h {minutos_inteiros:02d}min" == "1h 05min":
        print(f"   ✅ LÓGICA NOVA CORRETA!")
        return True
    else:
        print(f"   ❌ LÓGICA NOVA AINDA INCORRETA!")
        return False

def simular_backend_completo():
    """
    Simula o backend completo.
    """
    print(f"\n🔧 SIMULAÇÃO BACKEND COMPLETO")
    print("=" * 50)
    
    # Simular dados como vem do banco
    registros_dia = [
        {'data_hora': datetime(2025, 7, 17, 8, 0, 0), 'tipo_registro': 'entrada_manha'},
        {'data_hora': datetime(2025, 7, 17, 9, 5, 0), 'tipo_registro': 'saida_almoco'}
    ]
    
    print(f"📊 Registros do dia:")
    for reg in registros_dia:
        print(f"   {reg['tipo_registro']}: {reg['data_hora'].time()}")
    
    # Simular função calcular_horas_trabalhadas
    entrada_manha = None
    saida_almoco = None
    entrada_tarde = None
    saida = None
    
    for registro in registros_dia:
        if registro['tipo_registro'] == 'entrada_manha':
            entrada_manha = registro['data_hora'].time()
        elif registro['tipo_registro'] == 'saida_almoco':
            saida_almoco = registro['data_hora'].time()
        elif registro['tipo_registro'] == 'entrada_tarde':
            entrada_tarde = registro['data_hora'].time()
        elif registro['tipo_registro'] == 'saida':
            saida = registro['data_hora'].time()
    
    print(f"\n🔧 Horários extraídos:")
    print(f"   entrada_manha: {entrada_manha}")
    print(f"   saida_almoco: {saida_almoco}")
    print(f"   entrada_tarde: {entrada_tarde}")
    print(f"   saida: {saida}")
    
    # Calcular horas (lógica do sistema)
    total_horas = 0.0
    
    if entrada_manha and saida_almoco:
        hoje = datetime.now().date()
        dt_entrada = datetime.combine(hoje, entrada_manha)
        dt_saida_almoco = datetime.combine(hoje, saida_almoco)
        
        periodo_manha = (dt_saida_almoco - dt_entrada).total_seconds() / 3600
        total_horas += periodo_manha
        
        print(f"\n🧮 Cálculo período manhã:")
        print(f"   Período manhã: {periodo_manha:.6f}h")
        print(f"   Total horas: {total_horas:.6f}h")
    
    # Aplicar função arredondar_horas_correto
    horas_final = round(total_horas, 6)
    print(f"   Horas final (arredondadas): {horas_final:.6f}h")
    
    return horas_final

def main():
    debug_calculo_completo()
    horas_backend = simular_backend_completo()
    
    print(f"\n🎯 RESULTADO FINAL:")
    print(f"   Backend retorna: {horas_backend:.6f}h")
    
    # Simular template CORRIGIDO
    segundos_totais = round(horas_backend * 3600)
    horas_inteiras = int(segundos_totais // 3600)
    minutos_inteiros = int((segundos_totais % 3600) // 60)
    
    resultado_template = f"{horas_inteiras}h {minutos_inteiros:02d}min"
    print(f"   Template mostra: {resultado_template}")
    
    if resultado_template == "1h 05min":
        print(f"   🎉 TUDO CORRETO!")
    else:
        print(f"   ❌ AINDA HÁ PROBLEMA!")

if __name__ == "__main__":
    main()
