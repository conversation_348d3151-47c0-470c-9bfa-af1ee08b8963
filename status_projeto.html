<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Status do Projeto RLPONTO-WEB</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .status-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .progress-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(102, 126, 234, 0.3);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: 300;
            margin: 0;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .milestone {
            padding: 15px;
            border-left: 4px solid #28a745;
            margin: 10px 0;
            background: rgba(40, 167, 69, 0.1);
            border-radius: 0 8px 8px 0;
        }
        
        .milestone.completed {
            border-left-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }
        
        .milestone.in-progress {
            border-left-color: #007bff;
            background: rgba(0, 123, 255, 0.1);
        }
        
        .milestone.pending {
            border-left-color: #6c757d;
            background: rgba(108, 117, 125, 0.1);
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="text-center mb-3">
            <i class="fas fa-chart-line text-primary"></i>
            Status do Projeto RLPONTO-WEB v1.0
        </h1>
        <div class="text-center mb-5">
            <p class="text-muted mb-1">Sistema de Controle de Ponto Biométrico Empresarial</p>
            <p class="text-muted"><strong>Release:</strong> 09/01/2025 • <strong>Status:</strong> <span class="badge bg-success">PRODUÇÃO</span></p>
        </div>
        
        <!-- Cards de Status -->
        <div class="row">
            <div class="col-md-3">
                <div class="status-card text-center">
                    <i class="fas fa-calendar-alt text-primary mb-3" style="font-size: 2em;"></i>
                    <h3 class="stat-number">75%</h3>
                    <div class="stat-label">Progresso Total</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="status-card text-center">
                    <i class="fas fa-clock text-success mb-3" style="font-size: 2em;"></i>
                    <h3 class="stat-number">45</h3>
                    <div class="stat-label">Dias Decorridos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="status-card text-center">
                    <i class="fas fa-hourglass-half text-warning mb-3" style="font-size: 2em;"></i>
                    <h3 class="stat-number">15</h3>
                    <div class="stat-label">Dias Restantes</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="status-card text-center">
                    <i class="fas fa-check-circle text-success mb-3" style="font-size: 2em;"></i>
                    <h3 class="stat-number">18/24</h3>
                    <div class="stat-label">Funcionalidades Concluídas</div>
                </div>
            </div>
        </div>
        
        <!-- Gráfico de Progresso -->
        <div class="chart-container">
            <canvas id="progressChart"></canvas>
        </div>
        
        <!-- Informações do Projeto -->
        <div class="row">
            <div class="col-md-6">
                <div class="progress-card">
                    <h4 class="mb-4">Informações do Projeto</h4>
                    <div class="mb-3">
                        <strong>Sistema:</strong> RLPONTO-WEB v1.0
                    </div>
                    <div class="mb-3">
                        <strong>Empresa:</strong> AiNexus Tecnologia
                    </div>
                    <div class="mb-3">
                        <strong>Data de Release:</strong> 09/01/2025
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong> <span class="badge bg-success">PRODUÇÃO</span>
                    </div>
                    <div class="progress mt-4" style="height: 10px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="status-card">
                    <h4 class="mb-4">Marcos do Projeto</h4>
                    <div class="milestone completed">
                        <h5><i class="fas fa-check-circle text-success"></i> Análise de Requisitos</h5>
                        <small class="text-muted">Concluído em 23/05/2025</small>
                    </div>
                    <div class="milestone completed">
                        <h5><i class="fas fa-check-circle text-success"></i> Desenvolvimento do Backend</h5>
                        <small class="text-muted">Concluído em 25/05/2025</small>
                    </div>
                    <div class="milestone in-progress">
                        <h5><i class="fas fa-spinner fa-spin text-primary"></i> Desenvolvimento do Frontend</h5>
                        <small class="text-muted">Em andamento - 80% concluído</small>
                    </div>
                    <div class="milestone pending">
                        <h5><i class="fas fa-clock text-muted"></i> Testes e Homologação</h5>
                        <small class="text-muted">Previsto para 10/07/2025</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Dados do progresso do projeto
        const progressData = {
            labels: ['21/05', '26/05', '31/05', '05/06', '10/06', '15/06', '20/06', '25/06', '30/06', '05/07'],
            actual: [0, 15, 25, 35, 45, 60, 75, null, null, null],
            planned: [0, 10, 20, 30, 40, 50, 60, 70, 85, 100]
        };

        // Configurar o gráfico
        const ctx = document.getElementById('progressChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: progressData.labels,
                datasets: [
                    {
                        label: 'Progresso Real',
                        data: progressData.actual,
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    },
                    {
                        label: 'Progresso Planejado',
                        data: progressData.planned,
                        borderColor: '#007bff',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        fill: false,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Progresso do Projeto',
                        font: {
                            size: 16
                        }
                    },
                    legend: {
                        position: 'bottom'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html> 