#!/usr/bin/env python3
"""
Simulador <PERSON> para testes
Simula as respostas do ZKAgent quando o hardware não está disponível
"""

from flask import Flask, jsonify, request
import random
import time
import base64

app = Flask(__name__)

@app.route('/test', methods=['GET'])
def test():
    """Simula teste de conexão"""
    return jsonify({"status": "ok"})

@app.route('/list-devices', methods=['GET'])
def list_devices():
    """Simula listagem de dispositivos"""
    return jsonify({"devices": 1})

@app.route('/capture', methods=['POST'])
def capture():
    """Simula captura de digital"""
    # Simula tempo de captura
    time.sleep(2)
    
    # Gera template simulado
    template = base64.b64encode(f"template_sim_{int(time.time())}".encode()).decode()
    quality = random.randint(70, 95)
    
    return jsonify({
        "success": True,
        "template": template,
        "quality": quality,
        "timestamp": time.time()
    })

@app.route('/identify', methods=['POST'])
def identify():
    """Simula identificação"""
    return jsonify({
        "success": False,
        "message": "Nenhuma digital cadastrada encontrada"
    })

@app.route('/status', methods=['GET'])
def status():
    """Status do simulador"""
    return jsonify({
        "service": "ZKAgent Simulador",
        "version": "1.0",
        "devices": 1,
        "status": "online"
    })

if __name__ == '__main__':
    print("🔐 ZKAgent Simulador iniciando na porta 5001...")
    print("Este é um simulador para testes sem hardware ZK4500")
    print("Para parar: Ctrl+C")
    app.run(host='0.0.0.0', port=5001, debug=False) 