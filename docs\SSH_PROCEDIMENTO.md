# Procedimento de Configuração SSH sem Senha - RLPONTO-WEB

**Data de Criação:** 07/07/2025
**Versão:** 1.0
**Sistema:** RLPONTO-WEB v1.0
**Servidor:** ************
**Objetivo:** Configurar acesso SSH sem senha para facilitar operações de deploy e manutenção

---

## 📋 Índice

1. [Visão Geral](#visão-geral)
2. [Pré-requisitos](#pré-requisitos)
3. [Procedimento de Configuração](#procedimento-de-configuração)
4. [Verificação e Testes](#verificação-e-testes)
5. [<PERSON><PERSON> Di<PERSON>](#uso-diário)
6. [Troubleshooting](#troubleshooting)
7. [Segurança](#segurança)

---

## 🎯 Visão Geral

Este documento descreve o procedimento completo para configurar autenticação SSH baseada em chaves públicas/privadas, eliminando a necessidade de inserir senha a cada conexão com o servidor RLPONTO-WEB.

### Benefícios:
- ✅ Acesso rápido e automatizado ao servidor
- ✅ Maior segurança que autenticação por senha
- ✅ Facilita scripts de deploy automatizado
- ✅ Reduz tempo de operações de manutenção

---

## 🔧 Pré-requisitos

### Cliente (Máquina Local):
- Windows 10/11 com OpenSSH Client instalado
- PowerShell 5.0 ou superior
- Acesso à internet para conexão com o servidor

### Servidor:
- **IP:** ************
- **Usuário:** root
- **Senha:** @Ric6109
- OpenSSH Server configurado e ativo

---

## 🛠️ Procedimento de Configuração

### Passo 1: Criar Diretório SSH (se não existir)
```powershell
mkdir -p $env:USERPROFILE\.ssh
```

### Passo 2: Gerar Par de Chaves SSH
```powershell
ssh-keygen -t rsa -b 4096 -f "$env:USERPROFILE\.ssh\id_rsa_rlponto" -N '""'
```

**Resultado esperado:**
- Chave privada: `C:\Users\<USER>\.ssh\id_rsa_rlponto`
- Chave pública: `C:\Users\<USER>\.ssh\id_rsa_rlponto.pub`

### Passo 3: Visualizar Chave Pública
```powershell
Get-Content "$env:USERPROFILE\.ssh\id_rsa_rlponto.pub"
```

### Passo 4: Configurar Servidor - Criar Diretório SSH
```powershell
ssh root@************ "mkdir -p ~/.ssh && chmod 700 ~/.ssh"
```
*Inserir senha: @Ric6109*

### Passo 5: Copiar Chave Pública para o Servidor
```powershell
ssh root@************ "echo 'CONTEUDO_DA_CHAVE_PUBLICA' >> ~/.ssh/authorized_keys"
```
*Substituir CONTEUDO_DA_CHAVE_PUBLICA pelo conteúdo real da chave*

### Passo 6: Configurar Permissões no Servidor
```powershell
ssh root@************ "chmod 600 ~/.ssh/authorized_keys"
```

### Passo 7: Criar Arquivo de Configuração SSH Local
```powershell
@"
Host rlponto-server
    HostName ************
    User root
    IdentityFile ~/.ssh/id_rsa_rlponto
    IdentitiesOnly yes
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
"@ | Out-File -FilePath "$env:USERPROFILE\.ssh\config" -Encoding UTF8
```

---

## ✅ Verificação e Testes

### Teste 1: Conexão com Chave Específica
```powershell
ssh -i "$env:USERPROFILE\.ssh\id_rsa_rlponto" root@************ "echo 'SSH sem senha funcionando!' && hostname"
```

### Teste 2: Conexão com Alias Configurado
```powershell
ssh rlponto-server "echo 'Conexão SSH com alias funcionando!' && date"
```

### Resultado Esperado:
- Conexão estabelecida sem solicitar senha
- Mensagens de confirmação exibidas
- Hostname: RLPONTO
- Usuário: root

---

## 🚀 Uso Diário

### Conexão Simples:
```powershell
ssh rlponto-server
```

### Executar Comando Remoto:
```powershell
ssh rlponto-server "comando_aqui"
```

### Exemplos Práticos:
```powershell
# Verificar status do serviço
ssh rlponto-server "systemctl status apache2"

# Verificar logs
ssh rlponto-server "tail -f /var/log/apache2/error.log"

# Deploy de arquivos
scp arquivo.py rlponto-server:/var/www/controle-ponto/

# Sincronizar diretório
rsync -avz --delete ./var/www/controle-ponto/ rlponto-server:/var/www/controle-ponto/
```

---

## 🔍 Troubleshooting

### Problema: Ainda solicita senha
**Solução:**
1. Verificar permissões do arquivo authorized_keys:
   ```bash
   ssh rlponto-server "ls -la ~/.ssh/authorized_keys"
   ```
2. Verificar se a chave foi adicionada corretamente:
   ```bash
   ssh rlponto-server "cat ~/.ssh/authorized_keys"
   ```

### Problema: "Permission denied (publickey)"
**Solução:**
1. Verificar se a chave privada existe:
   ```powershell
   Test-Path "$env:USERPROFILE\.ssh\id_rsa_rlponto"
   ```
2. Verificar configuração SSH:
   ```powershell
   Get-Content "$env:USERPROFILE\.ssh\config"
   ```

### Problema: "Host key verification failed"
**Solução:**
1. Limpar known_hosts:
   ```powershell
   ssh-keygen -R ************
   ```
2. Conectar novamente e aceitar a nova chave

---

## 🔒 Segurança

### Boas Práticas Implementadas:
- ✅ Chave RSA de 4096 bits (alta segurança)
- ✅ Chave privada sem passphrase (para automação)
- ✅ Permissões restritivas (700 para .ssh, 600 para authorized_keys)
- ✅ IdentitiesOnly yes (usa apenas a chave especificada)

### Recomendações Adicionais:
- 🔄 Rotacionar chaves a cada 6 meses
- 📝 Manter backup seguro das chaves privadas
- 🚫 Nunca compartilhar chaves privadas
- 📊 Monitorar logs de acesso SSH regularmente

### Localização dos Arquivos:
- **Chave Privada:** `C:\Users\<USER>\.ssh\id_rsa_rlponto`
- **Chave Pública:** `C:\Users\<USER>\.ssh\id_rsa_rlponto.pub`
- **Configuração:** `C:\Users\<USER>\.ssh\config`
- **Servidor:** `/root/.ssh/authorized_keys`

---

## 📝 Notas Importantes

1. **Backup:** Sempre fazer backup das chaves antes de modificações
2. **Acesso:** Manter acesso alternativo (senha) até confirmar funcionamento
3. **Documentação:** Atualizar este documento em caso de mudanças
4. **Monitoramento:** Verificar logs de acesso regularmente

---

**✅ CONFIGURAÇÃO CONCLUÍDA COM SUCESSO EM:** 07/07/2025 10:39
**🔧 RESPONSÁVEL:** Richardson Rodrigues
**🏢 EMPRESA:** AiNexus Tecnologia
**🎯 SISTEMA:** RLPONTO-WEB v1.0