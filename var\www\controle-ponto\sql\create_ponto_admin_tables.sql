-- ================================================================
-- TABELAS PARA SISTEMA DE ADMINISTRAÇÃO DE PONTO
-- Sistema: RLPONTO-WEB v1.0
-- Data: 08/07/2025
-- Autor: <PERSON> - AiNexus Tecnologia
-- ================================================================

-- Tabela para logs de atividades do sistema
CREATE TABLE IF NOT EXISTS logs_atividades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT NOT NULL,
    acao VARCHAR(100) NOT NULL,
    detalhes TEXT,
    usuario_id INT,
    usuario_nome VARCHAR(255),
    registro_ponto_id INT NULL,
    data_acao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    
    INDEX idx_funcionario_id (funcionario_id),
    INDEX idx_usuario_id (usuario_id),
    INDEX idx_data_acao (data_acao),
    INDEX idx_acao (acao),
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
    FOREIGN KEY (registro_ponto_id) REFERENCES registro_ponto(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela para documentos comprobatórios de ponto
CREATE TABLE IF NOT EXISTS documentos_ponto (
    id INT AUTO_INCREMENT PRIMARY KEY,
    registro_ponto_id INT NOT NULL,
    funcionario_id INT NOT NULL,
    nome_arquivo VARCHAR(255) NOT NULL,
    nome_original VARCHAR(255),
    descricao TEXT,
    tipo_documento ENUM('ATESTADO_MEDICO', 'JUSTIFICATIVA', 'COMPROVANTE', 'OUTROS') DEFAULT 'OUTROS',
    tamanho_arquivo INT,
    mime_type VARCHAR(100),
    usuario_upload_id INT,
    data_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ativo BOOLEAN DEFAULT TRUE,
    
    INDEX idx_registro_ponto_id (registro_ponto_id),
    INDEX idx_funcionario_id (funcionario_id),
    INDEX idx_data_upload (data_upload),
    INDEX idx_usuario_upload (usuario_upload_id),
    
    FOREIGN KEY (registro_ponto_id) REFERENCES registro_ponto(id) ON DELETE CASCADE,
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Adicionar campos de auditoria na tabela registro_ponto se não existirem
ALTER TABLE registro_ponto
ADD COLUMN IF NOT EXISTS justificativa TEXT,
ADD COLUMN IF NOT EXISTS editado_por INT,
ADD COLUMN IF NOT EXISTS data_edicao TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS empresa_cliente_id INT NULL COMMENT 'Cliente onde o funcionário estava alocado no momento do ponto',
ADD COLUMN IF NOT EXISTS alocacao_id INT NULL COMMENT 'ID da alocação ativa no momento do registro';

-- Adicionar índices para melhor performance
ALTER TABLE registro_ponto
ADD INDEX IF NOT EXISTS idx_funcionario_data (funcionario_id, data),
ADD INDEX IF NOT EXISTS idx_data_mes (data),
ADD INDEX IF NOT EXISTS idx_editado_por (editado_por),
ADD INDEX IF NOT EXISTS idx_empresa_cliente_id (empresa_cliente_id),
ADD INDEX IF NOT EXISTS idx_alocacao_id (alocacao_id);

-- Tabela para histórico completo de alocações (para rastreamento temporal)
CREATE TABLE IF NOT EXISTS historico_alocacoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT NOT NULL,
    empresa_cliente_id INT NOT NULL,
    alocacao_id INT NULL,
    data_inicio DATE NOT NULL,
    data_fim DATE NULL,
    motivo_mudanca VARCHAR(255),
    observacoes TEXT,
    usuario_responsavel_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_funcionario_id (funcionario_id),
    INDEX idx_empresa_cliente_id (empresa_cliente_id),
    INDEX idx_periodo (data_inicio, data_fim),
    INDEX idx_data_inicio (data_inicio),

    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
    FOREIGN KEY (empresa_cliente_id) REFERENCES empresas(id) ON DELETE CASCADE,
    FOREIGN KEY (alocacao_id) REFERENCES funcionario_alocacoes(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela para configurações do sistema de ponto
CREATE TABLE IF NOT EXISTS configuracoes_ponto (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chave VARCHAR(100) NOT NULL UNIQUE,
    valor TEXT,
    descricao TEXT,
    tipo ENUM('STRING', 'INTEGER', 'BOOLEAN', 'JSON') DEFAULT 'STRING',
    categoria VARCHAR(50) DEFAULT 'GERAL',
    editavel BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_chave (chave),
    INDEX idx_categoria (categoria)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inserir configurações padrão
INSERT IGNORE INTO configuracoes_ponto (chave, valor, descricao, tipo, categoria) VALUES
('tolerancia_entrada_minutos', '15', 'Tolerância para entrada em minutos', 'INTEGER', 'HORARIOS'),
('tolerancia_saida_minutos', '15', 'Tolerância para saída em minutos', 'INTEGER', 'HORARIOS'),
('permitir_edicao_ponto', 'true', 'Permitir edição de registros de ponto', 'BOOLEAN', 'PERMISSOES'),
('dias_edicao_permitidos', '7', 'Dias retroativos permitidos para edição', 'INTEGER', 'PERMISSOES'),
('obrigar_justificativa_edicao', 'true', 'Obrigar justificativa ao editar ponto', 'BOOLEAN', 'PERMISSOES'),
('formatos_documento_permitidos', '["pdf","jpg","jpeg","png","doc","docx"]', 'Formatos de documento permitidos', 'JSON', 'DOCUMENTOS'),
('tamanho_max_documento_mb', '10', 'Tamanho máximo de documento em MB', 'INTEGER', 'DOCUMENTOS');

-- Tabela para templates de justificativas
CREATE TABLE IF NOT EXISTS templates_justificativas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    texto TEXT NOT NULL,
    categoria ENUM('ATRASO', 'FALTA', 'SAIDA_ANTECIPADA', 'OUTROS') DEFAULT 'OUTROS',
    ativo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_categoria (categoria),
    INDEX idx_ativo (ativo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inserir templates padrão de justificativas
INSERT IGNORE INTO templates_justificativas (nome, texto, categoria) VALUES
('Atestado Médico', 'Falta justificada por atestado médico anexado.', 'FALTA'),
('Consulta Médica', 'Atraso devido a consulta médica agendada.', 'ATRASO'),
('Problema de Transporte', 'Atraso devido a problemas no transporte público.', 'ATRASO'),
('Emergência Familiar', 'Ausência devido a emergência familiar.', 'FALTA'),
('Problema Pessoal', 'Saída antecipada por motivo pessoal autorizado.', 'SAIDA_ANTECIPADA'),
('Trânsito Intenso', 'Atraso devido ao trânsito intenso na cidade.', 'ATRASO');

-- View para relatórios consolidados com histórico de alocações
CREATE OR REPLACE VIEW vw_resumo_ponto_funcionarios AS
SELECT
    f.id as funcionario_id,
    f.nome_completo,
    f.cargo,
    f.setor,
    e.razao_social as empresa_nome,
    ec.razao_social as cliente_nome,
    DATE_FORMAT(rp.data, '%Y-%m') as mes_ano,
    COUNT(rp.id) as total_registros,
    COUNT(CASE WHEN rp.entrada IS NOT NULL THEN 1 END) as dias_trabalhados,
    COUNT(CASE WHEN rp.entrada IS NULL THEN 1 END) as faltas,
    COUNT(CASE WHEN rp.justificativa IS NOT NULL THEN 1 END) as registros_justificados,
    COUNT(CASE WHEN rp.editado_por IS NOT NULL THEN 1 END) as registros_editados,
    AVG(CASE
        WHEN rp.entrada IS NOT NULL AND rp.saida IS NOT NULL
        THEN TIME_TO_SEC(TIMEDIFF(rp.saida, rp.entrada)) / 3600
    END) as media_horas_dia,
    SUM(CASE
        WHEN rp.entrada IS NOT NULL AND rp.saida IS NOT NULL
        THEN TIME_TO_SEC(TIMEDIFF(rp.saida, rp.entrada)) / 3600
        ELSE 0
    END) as total_horas_mes
FROM funcionarios f
LEFT JOIN empresas e ON f.empresa_id = e.id
LEFT JOIN registro_ponto rp ON f.id = rp.funcionario_id
LEFT JOIN empresas ec ON rp.empresa_cliente_id = ec.id
WHERE f.status_cadastro = 'Ativo'
GROUP BY f.id, f.nome_completo, f.cargo, f.setor, e.razao_social, ec.razao_social, DATE_FORMAT(rp.data, '%Y-%m');

-- View para histórico completo de ponto com alocações
CREATE OR REPLACE VIEW vw_historico_ponto_completo AS
SELECT
    rp.*,
    f.nome_completo as funcionario_nome,
    f.cargo,
    f.setor,
    e_funcionario.razao_social as empresa_funcionario,
    e_cliente.razao_social as cliente_nome,
    e_cliente.nome_fantasia as cliente_fantasia,
    ha.data_inicio as alocacao_inicio,
    ha.data_fim as alocacao_fim,
    CASE
        WHEN DAYOFWEEK(rp.data) = 1 THEN 'Domingo'
        WHEN DAYOFWEEK(rp.data) = 2 THEN 'Segunda'
        WHEN DAYOFWEEK(rp.data) = 3 THEN 'Terça'
        WHEN DAYOFWEEK(rp.data) = 4 THEN 'Quarta'
        WHEN DAYOFWEEK(rp.data) = 5 THEN 'Quinta'
        WHEN DAYOFWEEK(rp.data) = 6 THEN 'Sexta'
        WHEN DAYOFWEEK(rp.data) = 7 THEN 'Sábado'
    END as dia_semana,
    CASE
        WHEN rp.entrada IS NOT NULL AND rp.saida IS NOT NULL
        THEN TIME_TO_SEC(TIMEDIFF(rp.saida, rp.entrada)) / 3600
        ELSE 0
    END as total_horas_dia
FROM registro_ponto rp
JOIN funcionarios f ON rp.funcionario_id = f.id
LEFT JOIN empresas e_funcionario ON f.empresa_id = e_funcionario.id
LEFT JOIN empresas e_cliente ON rp.empresa_cliente_id = e_cliente.id
LEFT JOIN historico_alocacoes ha ON rp.funcionario_id = ha.funcionario_id
    AND rp.empresa_cliente_id = ha.empresa_cliente_id
    AND rp.data BETWEEN ha.data_inicio AND COALESCE(ha.data_fim, CURDATE());

-- Comentários das tabelas
ALTER TABLE logs_atividades COMMENT = 'Logs de todas as atividades realizadas no sistema de ponto';
ALTER TABLE documentos_ponto COMMENT = 'Documentos comprobatórios anexados aos registros de ponto';
ALTER TABLE configuracoes_ponto COMMENT = 'Configurações gerais do sistema de administração de ponto';
ALTER TABLE templates_justificativas COMMENT = 'Templates pré-definidos para justificativas de ponto';
