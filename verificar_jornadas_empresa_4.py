#!/usr/bin/env python3
import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

db = DatabaseManager()

print("=== TODAS AS JORNADAS DA EMPRESA MSV ENGENHARIA (ID 4) ===")

jornadas = db.execute_query("""
    SELECT id, nome_jornada, padrao, ativa,
           seg_qui_entrada, seg_qui_saida,
           sexta_entrada, sexta_saida,
           intervalo_inicio, intervalo_fim,
           tolerancia_entrada_minutos
    FROM jornadas_trabalho 
    WHERE empresa_id = 4
    ORDER BY padrao DESC, ativa DESC, id
""")

if jornadas:
    for jornada in jornadas:
        status = []
        if jornada['padrao']:
            status.append("PADRÃO")
        if jornada['ativa']:
            status.append("ATIVA")
        else:
            status.append("INATIVA")
        
        print(f"\n📋 JORNADA ID {jornada['id']}: {jornada['nome_jornada']} [{', '.join(status)}]")
        print(f"   • Seg-Qui: {jornada['seg_qui_entrada']} às {jornada['seg_qui_saida']}")
        print(f"   • Sexta: {jornada['sexta_entrada']} às {jornada['sexta_saida']}")
        print(f"   • Intervalo: {jornada['intervalo_inicio']} às {jornada['intervalo_fim']}")
        print(f"   • Tolerância: {jornada['tolerancia_entrada_minutos']} min")
        
        if jornada['padrao'] and jornada['ativa']:
            print("   🎯 ESTA É A JORNADA PADRÃO ATIVA")
else:
    print("❌ Nenhuma jornada encontrada para a empresa 4!")

print("\n=== VERIFICANDO SE EXISTE JORNADA COM HORÁRIOS 07:30 ===")

jornada_0730 = db.execute_query("""
    SELECT id, nome_jornada, padrao, ativa,
           seg_qui_entrada, seg_qui_saida,
           sexta_entrada, sexta_saida
    FROM jornadas_trabalho 
    WHERE empresa_id = 4 
    AND seg_qui_entrada = '07:30:00'
    AND seg_qui_saida = '17:30:00'
""")

if jornada_0730:
    print("✅ ENCONTRADA jornada com horários 07:30-17:30:")
    for jornada in jornada_0730:
        print(f"   • ID: {jornada['id']} - {jornada['nome_jornada']}")
        print(f"   • Padrão: {jornada['padrao']} | Ativa: {jornada['ativa']}")
        
        if not jornada['padrao']:
            print(f"\n🔧 CORRIGINDO: Definindo jornada {jornada['id']} como padrão...")
            
            # Remover padrão de outras jornadas
            db.execute_query("""
                UPDATE jornadas_trabalho 
                SET padrao = FALSE 
                WHERE empresa_id = 4
            """, fetch_all=False)
            
            # Definir esta como padrão
            db.execute_query("""
                UPDATE jornadas_trabalho 
                SET padrao = TRUE, ativa = TRUE
                WHERE id = %s
            """, (jornada['id'],), fetch_all=False)
            
            print(f"✅ Jornada {jornada['id']} definida como padrão!")
            
            # Aplicar a todos os funcionários da empresa
            print(f"🔄 Aplicando aos funcionários da empresa...")
            
            funcionarios_atualizados = db.execute_query("""
                UPDATE funcionarios 
                SET jornada_trabalho_id = %s, usa_horario_empresa = TRUE
                WHERE empresa_id = 4 AND ativo = 1
            """, (jornada['id'],), fetch_all=False)
            
            print(f"✅ Funcionários atualizados!")
else:
    print("❌ NÃO ENCONTRADA jornada com horários 07:30-17:30")
    print("💡 Você precisa criar ou corrigir a jornada na interface web")
