{% extends "base.html" %}

{% block title %}{{ titulo }} - <PERSON><PERSON>{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.css">
<style>
    /* ========================================
       SISTEMA DE CONTROLE DE PONTO - DESIGN MODERNO
       Seguindo a filosofia RLPONTO-WEB: Segurança, Robustez, Escalabilidade
       ======================================== */
    
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --background-light: #f8fafc;
        --background-white: #ffffff;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --border-radius: 0.75rem;
    }

    body {
        background-color: var(--background-light);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    /* ========================================
       CARDS DE ESTATÍSTICAS
       ======================================== */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: var(--background-white);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        transition: all 0.2s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .stat-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }

    .stat-icon.success { background: #dcfce7; color: var(--success-color); }
    .stat-icon.warning { background: #fef3c7; color: var(--warning-color); }
    .stat-icon.danger { background: #fee2e2; color: var(--danger-color); }
    .stat-icon.primary { background: #dbeafe; color: var(--primary-color); }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
    }

    /* ========================================
       FILTROS MODERNOS
       ======================================== */
    .filters-container {
        background: var(--background-white);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .form-control {
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        background: var(--background-white);
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
    }

    .btn-search {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        height: fit-content;
    }

    .btn-search:hover {
        background: #1d4ed8;
        transform: translateY(-1px);
    }

    .btn-export {
        background: var(--success-color);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 500;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-export:hover {
        background: #059669;
        color: white;
        text-decoration: none;
    }

    /* ========================================
       TABELA MODERNA
       ======================================== */
    .table-container {
        background: var(--background-white);
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .table-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .table-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .modern-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin: 0;
    }

    .modern-table thead th {
        background: #f8fafc;
        padding: 1rem;
        font-weight: 600;
        font-size: 0.875rem;
        color: var(--text-primary);
        text-align: left;
        border-bottom: 1px solid var(--border-color);
        white-space: nowrap;
    }

    .modern-table tbody td {
        padding: 1rem;
        font-size: 0.875rem;
        color: var(--text-primary);
        border-bottom: 1px solid var(--border-color);
        vertical-align: middle;
    }

    .modern-table tbody tr:hover {
        background: #f8fafc;
    }

    .modern-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* ========================================
       BADGES E STATUS
       ======================================== */
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .status-presente { background: #dcfce7; color: #15803d; }
    .status-atrasado { background: #fef3c7; color: #d97706; }
    .status-ausente { background: #fee2e2; color: #dc2626; }
    .status-completo { background: #dbeafe; color: #2563eb; }

    /* ========================================
       RESPONSIVIDADE
       ======================================== */
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .filter-grid {
            grid-template-columns: 1fr;
        }
        
        .table-header {
            flex-direction: column;
            align-items: stretch;
        }
        
        .modern-table {
            font-size: 0.8rem;
        }
        
        .modern-table thead th,
        .modern-table tbody td {
            padding: 0.5rem;
        }
    }

    /* ========================================
       LOADING E ESTADOS
       ======================================== */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .loading-spinner {
        background: white;
        padding: 2rem;
        border-radius: var(--border-radius);
        text-align: center;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--text-secondary);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    /* Utilities */
    .d-flex { display: flex; }
    .align-items-center { align-items: center; }
    .gap-2 { gap: 0.5rem; }
    .me-2 { margin-right: 0.5rem; }
    .fw-medium { font-weight: 500; }
    .text-muted { color: var(--text-secondary); }
    .rounded-circle { border-radius: 50%; }
    .visually-hidden { 
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        white-space: nowrap !important;
        border: 0 !important;
    }
    .mt-2 { margin-top: 0.5rem; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- ========================================
         CARDS DE ESTATÍSTICAS
         ======================================== -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon success">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-number" id="totalPresentes">{{ estatisticas.presentes or 0 }}</div>
            <div class="stat-label">Funcionários Presentes</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-number" id="totalAtrasos">{{ estatisticas.atrasados or 0 }}</div>
            <div class="stat-label">Chegadas Atrasadas</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon danger">
                <i class="fas fa-user-times"></i>
            </div>
            <div class="stat-number" id="totalAusentes">{{ estatisticas.ausentes or 0 }}</div>
            <div class="stat-label">Funcionários Ausentes</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon primary">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div class="stat-number" id="totalRegistros">{{ registros|length or 0 }}</div>
            <div class="stat-label">Registros de Ponto</div>
        </div>
    </div>

    <!-- ========================================
         FILTROS DE BUSCA
         ======================================== -->
    <div class="filters-container">
        <form id="filtrosForm" method="POST" action="{{ url_for('relatorios.pagina_relatorio_pontos') }}">
            <div class="filter-grid">
                <div class="form-group">
                    <label for="funcionario_id" class="form-label">Funcionário</label>
                    <select class="form-control" id="funcionario_id" name="funcionario_id">
                        <option value="">Todos os funcionários</option>
                        {% for f in funcionarios %}
                        <option value="{{ f.id }}" {% if request.form.funcionario_id == f.id|string %}selected{% endif %}>
                            {{ f.nome_completo }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="setor" class="form-label">Setor</label>
                    <select class="form-control" id="setor" name="setor">
                        <option value="">Todos os setores</option>
                        {% for setor in funcionarios|map(attribute='setor')|unique|list %}
                        {% if setor %}
                        <option value="{{ setor }}" {% if request.form.setor == setor %}selected{% endif %}>
                            {{ setor }}
                        </option>
                        {% endif %}
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="data_inicio" class="form-label">Data Início</label>
                    <input type="date" class="form-control" id="data_inicio" name="data_inicio" 
                           value="{{ request.form.data_inicio or data_atual }}">
                </div>
                
                <div class="form-group">
                    <label for="data_fim" class="form-label">Data Fim</label>
                    <input type="date" class="form-control" id="data_fim" name="data_fim" 
                           value="{{ request.form.data_fim or data_atual }}">
                </div>
                
                <div class="form-group">
                    <label for="tipo_registro" class="form-label">Tipo de Registro</label>
                    <select class="form-control" id="tipo_registro" name="tipo_registro">
                        <option value="">Todos os tipos</option>
                        <option value="entrada" {% if request.form.tipo_registro == 'entrada' %}selected{% endif %}>Entrada</option>
                        <option value="saida" {% if request.form.tipo_registro == 'saida' %}selected{% endif %}>Saída</option>
                        <option value="entrada_almoco" {% if request.form.tipo_registro == 'entrada_almoco' %}selected{% endif %}>Entrada Almoço</option>
                        <option value="saida_almoco" {% if request.form.tipo_registro == 'saida_almoco' %}selected{% endif %}>Saída Almoço</option>
                    </select>
                </div>
                
                <button type="submit" class="btn-search">
                    <i class="fas fa-search"></i>
                    Buscar Registros
                </button>
            </div>
        </form>
    </div>

    <!-- ========================================
         TABELA DE REGISTROS
         ======================================== -->
    <div class="table-container">
        <div class="table-header">
            <h3 class="table-title">Registros de Ponto</h3>
            <div class="d-flex gap-2">
                {% if registros %}
                <a href="{{ url_for('relatorios.pontos_csv') }}?{{ request.query_string.decode() }}" 
                   class="btn-export">
                    <i class="fas fa-download"></i>
                    Exportar CSV
                </a>
                {% endif %}
            </div>
        </div>
        
        {% if registros %}
        <div class="table-responsive">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>Data</th>
                        <th>Funcionário</th>
                        <th>Setor</th>
                        <th>Horário</th>
                        <th>Tipo</th>
                        <th>Status</th>
                        <th>Observações</th>
                    </tr>
                </thead>
                <tbody>
                    {% for registro in registros %}
                    <tr>
                        <td>{{ registro.data_formatada }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if registro.foto_funcionario %}
                                <img src="data:image/jpeg;base64,{{ registro.foto_funcionario }}" 
                                     class="rounded-circle me-2" width="32" height="32" alt="Foto">
                                {% endif %}
                                <div>
                                    <div class="fw-medium">{{ registro.nome_funcionario }}</div>
                                </div>
                            </div>
                        </td>
                        <td>{{ registro.setor_funcionario or '-' }}</td>
                        <td>
                            <span class="fw-medium">{{ registro.horario_formatado }}</span>
                        </td>
                        <td>
                            <span class="status-badge 
                                {% if registro.tipo_registro == 'entrada' %}status-presente
                                {% elif registro.tipo_registro == 'saida' %}status-completo
                                {% elif 'almoco' in registro.tipo_registro %}status-warning
                                {% else %}status-primary{% endif %}">
                                {{ registro.tipo_registro_formatado }}
                            </span>
                        </td>
                        <td>
                            {% if registro.status_pontualidade %}
                            <span class="status-badge 
                                {% if registro.status_pontualidade == 'Pontual' %}status-presente
                                {% elif registro.status_pontualidade == 'Atrasado' %}status-atrasado
                                {% else %}status-ausente{% endif %}">
                                {{ registro.status_pontualidade }}
                            </span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if registro.observacoes %}
                            <span class="text-muted">{{ registro.observacoes }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-search"></i>
            <h4>Nenhum registro encontrado</h4>
            <p>Nenhum registro de ponto foi encontrado com os filtros selecionados.</p>
            <p class="text-muted">Tente ajustar os filtros de busca para ver os resultados.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Carregando...</span>
        </div>
        <p class="mt-2">Processando registros...</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// ========================================
// CONTROLE DE PONTO - JAVASCRIPT MODERNO
// Seguindo a filosofia RLPONTO-WEB
// ========================================

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('filtrosForm');
    const loadingOverlay = document.getElementById('loadingOverlay');
    
    // Configurar data atual como padrão
    const hoje = new Date().toISOString().split('T')[0];
    const dataInicio = document.getElementById('data_inicio');
    const dataFim = document.getElementById('data_fim');
    
    if (!dataInicio.value) dataInicio.value = hoje;
    if (!dataFim.value) dataFim.value = hoje;
    
    // Loading nos formulários
    if (form) {
        form.addEventListener('submit', function() {
            loadingOverlay.style.display = 'flex';
        });
    }
    
    // Auto-submit quando alterar filtros (opcional)
    const autoSubmitFields = ['funcionario_id', 'setor', 'tipo_registro'];
    autoSubmitFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('change', function() {
                // Opcional: auto-submit após pequeno delay
                // setTimeout(() => form.submit(), 300);
            });
        }
    });
    
    // Validação de datas
    dataInicio.addEventListener('change', function() {
        if (dataFim.value && dataInicio.value > dataFim.value) {
            dataFim.value = dataInicio.value;
        }
    });
    
    dataFim.addEventListener('change', function() {
        if (dataInicio.value && dataFim.value < dataInicio.value) {
            dataInicio.value = dataFim.value;
        }
    });
    
    console.log('Sistema RLPONTO-WEB carregado com sucesso!');
});

// Função para atualizar estatísticas em tempo real (se necessário)
function atualizarEstatisticas() {
    // Implementar se necessário chamadas AJAX para atualizar stats
    console.log('Atualizando estatísticas...');
}

// Exportação de dados
function exportarDados(formato) {
    const params = new URLSearchParams(new FormData(document.getElementById('filtrosForm')));
    window.open(`{{ url_for('relatorios.pontos_csv') }}?${params.toString()}`, '_blank');
}
</script>
{% endblock %} 