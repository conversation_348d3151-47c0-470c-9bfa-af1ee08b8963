#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para corrigir campos individuais dos funcionários
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def corrigir_campos_individuais():
    """Corrigir campos individuais dos funcionários"""
    print("🔧 CORRIGINDO CAMPOS INDIVIDUAIS DOS FUNCIONÁRIOS")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar situação atual
        print("\n1. Verificando situação atual...")
        sql_funcionarios = """
        SELECT 
            f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id,
            f.turno, f.tolerancia_ponto,
            e.razao_social as empresa_nome
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        WHERE f.status_cadastro = 'Ativo'
        ORDER BY f.id
        """
        
        funcionarios = db.execute_query(sql_funcionarios)
        
        print(f"📊 Funcionários ativos: {len(funcionarios)}")
        for func in funcionarios:
            print(f"   - ID {func['id']}: {func['nome_completo']} ({func['empresa_nome']})")
            print(f"     Empresa ID: {func['empresa_id']}")
            print(f"     Jornada ID: {func['jornada_trabalho_id']}")
            print(f"     Turno Individual: {func['turno']}")
            print(f"     Tolerância Individual: {func['tolerancia_ponto']}")
        
        # 2. Verificar jornadas das empresas
        print("\n2. Verificando jornadas das empresas...")
        sql_jornadas_empresas = """
        SELECT 
            e.id as empresa_id, e.razao_social,
            jt.id as jornada_id, jt.nome_jornada
        FROM empresas e
        LEFT JOIN jornadas_trabalho jt ON e.id = jt.empresa_id AND jt.padrao = TRUE AND jt.ativa = TRUE
        WHERE e.ativa = TRUE
        ORDER BY e.razao_social
        """
        
        jornadas_empresas = db.execute_query(sql_jornadas_empresas)
        jornadas_map = {}
        
        for item in jornadas_empresas:
            empresa_id = item['empresa_id']
            jornadas_map[empresa_id] = item
            status = f"Jornada ID {item['jornada_id']}: {item['nome_jornada']}" if item['jornada_id'] else "SEM JORNADA"
            print(f"   - {item['razao_social']} (ID {empresa_id}): {status}")
        
        # 3. Corrigir cada funcionário
        print("\n3. Corrigindo funcionários...")
        
        for func in funcionarios:
            funcionario_id = func['id']
            nome = func['nome_completo']
            empresa_id = func['empresa_id']
            empresa_nome = func['empresa_nome']
            
            print(f"\n   🔧 Corrigindo {nome} (ID {funcionario_id})...")
            
            # Verificar se empresa tem jornada
            jornada_empresa = jornadas_map.get(empresa_id)
            
            if jornada_empresa and jornada_empresa['jornada_id']:
                # Empresa TEM jornada - usar jornada da empresa e limpar campos individuais
                jornada_correta_id = jornada_empresa['jornada_id']
                
                print(f"      ✅ Empresa {empresa_nome} tem jornada ID {jornada_correta_id}")
                print(f"      🧹 Limpando campos individuais e aplicando jornada da empresa")
                
                sql_update = """
                UPDATE funcionarios 
                SET jornada_trabalho_id = %s,
                    turno = NULL,
                    tolerancia_ponto = NULL
                WHERE id = %s
                """
                
                result = db.execute_query(sql_update, (jornada_correta_id, funcionario_id), fetch_all=False)
                
                if result is not None:
                    print(f"      ✅ Corrigido: Jornada ID {jornada_correta_id}, campos individuais limpos")
                else:
                    print(f"      ❌ Erro ao corrigir")
                    
            else:
                # Empresa NÃO tem jornada - manter campos individuais mas limpar jornada_trabalho_id incorreta
                print(f"      ⚠️ Empresa {empresa_nome} não tem jornada")
                
                # Verificar se jornada_trabalho_id aponta para outra empresa
                if func['jornada_trabalho_id']:
                    # Verificar se a jornada pertence à empresa correta
                    sql_verificar_jornada = """
                    SELECT empresa_id FROM jornadas_trabalho WHERE id = %s
                    """
                    jornada_info = db.execute_query(sql_verificar_jornada, (func['jornada_trabalho_id'],), fetch_one=True)
                    
                    if jornada_info and jornada_info['empresa_id'] != empresa_id:
                        print(f"      🔧 Jornada ID {func['jornada_trabalho_id']} pertence à empresa {jornada_info['empresa_id']}, não à empresa {empresa_id}")
                        print(f"      🧹 Removendo jornada incorreta, mantendo campos individuais")
                        
                        sql_update = """
                        UPDATE funcionarios 
                        SET jornada_trabalho_id = NULL
                        WHERE id = %s
                        """
                        
                        result = db.execute_query(sql_update, (funcionario_id,), fetch_all=False)
                        
                        if result is not None:
                            print(f"      ✅ Jornada incorreta removida, campos individuais mantidos")
                        else:
                            print(f"      ❌ Erro ao corrigir")
                    else:
                        print(f"      ✅ Jornada ID {func['jornada_trabalho_id']} está correta para esta empresa")
                else:
                    print(f"      ✅ Sem jornada definida, mantendo campos individuais")
        
        # 4. Verificar resultado final
        print(f"\n4. Verificando resultado final...")
        funcionarios_atualizados = db.execute_query(sql_funcionarios)
        
        print(f"📋 Status final dos funcionários:")
        for func in funcionarios_atualizados:
            empresa_id = func['empresa_id']
            jornada_empresa = jornadas_map.get(empresa_id)
            
            print(f"   - {func['nome_completo']} ({func['empresa_nome']}):")
            print(f"     Jornada ID: {func['jornada_trabalho_id']}")
            print(f"     Turno Individual: {func['turno']}")
            print(f"     Tolerância Individual: {func['tolerancia_ponto']}")
            
            if jornada_empresa and jornada_empresa['jornada_id']:
                if func['jornada_trabalho_id'] == jornada_empresa['jornada_id'] and not func['turno'] and not func['tolerancia_ponto']:
                    print(f"     ✅ CORRETO - Usando jornada da empresa")
                else:
                    print(f"     ⚠️ VERIFICAR - Pode ter problema")
            else:
                if not func['jornada_trabalho_id'] and func['turno'] and func['tolerancia_ponto']:
                    print(f"     ✅ CORRETO - Usando campos individuais (empresa sem jornada)")
                else:
                    print(f"     ⚠️ VERIFICAR - Pode ter problema")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a correção: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    corrigir_campos_individuais()
