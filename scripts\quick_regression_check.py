#!/usr/bin/env python3
"""
VERIFICAÇÃO RÁPIDA DE REGRESSÃO - RLPONTO-WEB

Script que executa em 10-15 segundos para detectar problemas
antes que eles quebrem o sistema em produção.

Uso: python scripts/quick_regression_check.py
"""

import sys
import os
import shutil
from datetime import datetime
from pathlib import Path

def create_backup():
    """Criar backup rápido dos arquivos principais"""
    print("📁 Criando backup de segurança...")
    
    timestamp = datetime.now().strftime("%H%M%S")
    backup_dir = Path("../../backup-build")
    backup_dir.mkdir(exist_ok=True)
    
    # Arquivos críticos para backup
    critical_files = [
        "app.py",
        "app_funcionarios.py", 
        "app_configuracoes.py",
        "utils/database.py"
    ]
    
    backed_up = []
    for file_path in critical_files:
        if Path(file_path).exists():
            backup_name = f"{Path(file_path).stem}_pre_check_{timestamp}{Path(file_path).suffix}"
            backup_path = backup_dir / backup_name
            shutil.copy2(file_path, backup_path)
            backed_up.append(backup_name)
    
    if backed_up:
        print(f"   ✅ Backup criado: {len(backed_up)} arquivos")
    return backed_up

def test_module_loading():
    """Testar se todos os módulos carregam corretamente"""
    print("🧪 Testando carregamento de módulos...")
    
    modules_to_test = [
        ("app", "Aplicação principal"),
        ("app_funcionarios", "Módulo de funcionários"),
        ("app_configuracoes", "Módulo de configurações"), 
        ("app_relatorios", "Módulo de relatórios"),
        ("utils.database", "Utilitários de banco")
    ]
    
    failed_modules = []
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"   ✅ {description}")
        except ImportError as e:
            print(f"   ❌ {description}: {e}")
            failed_modules.append((module_name, str(e)))
        except Exception as e:
            print(f"   ⚠️ {description}: {e}")
            failed_modules.append((module_name, str(e)))
    
    return failed_modules

def test_blueprint_registration():
    """Testar se blueprints estão registrados corretamente"""
    print("🔗 Testando registro de blueprints...")
    
    try:
        import app
        
        # Verificar blueprints registrados
        blueprint_names = [bp.name for bp in app.app.blueprints.values()]
        required_blueprints = ['funcionarios', 'configuracoes', 'relatorios', 'status']
        
        missing_blueprints = []
        for bp_name in required_blueprints:
            if bp_name in blueprint_names:
                print(f"   ✅ Blueprint '{bp_name}' registrado")
            else:
                print(f"   ❌ Blueprint '{bp_name}' AUSENTE")
                missing_blueprints.append(bp_name)
        
        return missing_blueprints
        
    except Exception as e:
        print(f"   ❌ Erro ao verificar blueprints: {e}")
        return [f"Erro: {e}"]

def test_database_connectivity():
    """Testar conectividade básica com banco de dados"""
    print("🗄️ Testando conectividade do banco...")
    
    try:
        from utils.database import get_db_connection
        
        # Tentar conexão
        conn = get_db_connection()
        if conn and hasattr(conn, 'open') and conn.open:
            print("   ✅ Conexão de banco funcionando")
            conn.close()
            return True
        else:
            print("   ❌ Conexão de banco falhou")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro de banco: {e}")
        return False

def test_cross_module_imports():
    """Testar se módulos podem importar entre si sem conflito"""
    print("🔄 Testando importações cross-module...")
    
    test_cases = [
        ("app_funcionarios", "utils.database"),
        ("app_configuracoes", "utils.database"), 
        ("app_relatorios", "utils.database"),
        ("app", "app_funcionarios"),
        ("app", "app_configuracoes")
    ]
    
    failed_imports = []
    
    for module1, module2 in test_cases:
        try:
            # Limpar imports anteriores
            if module1 in sys.modules:
                del sys.modules[module1]
            if module2 in sys.modules:
                del sys.modules[module2]
            
            # Testar import sequencial
            __import__(module1)
            __import__(module2)
            print(f"   ✅ {module1} → {module2}")
            
        except Exception as e:
            print(f"   ❌ {module1} → {module2}: {e}")
            failed_imports.append((module1, module2, str(e)))
    
    return failed_imports

def run_quick_regression_check():
    """Executar verificação completa de regressão"""
    print("🛡️ VERIFICAÇÃO RÁPIDA DE REGRESSÃO")
    print("=" * 50)
    print(f"⏰ Início: {datetime.now().strftime('%H:%M:%S')}")
    print()
    
    # Contador de problemas
    issues_found = 0
    
    # 1. Criar backup
    try:
        create_backup()
    except Exception as e:
        print(f"   ⚠️ Backup falhou: {e}")
        issues_found += 1
    
    print()
    
    # 2. Testar carregamento de módulos
    failed_modules = test_module_loading()
    if failed_modules:
        issues_found += len(failed_modules)
        print(f"\n❌ Módulos com problemas: {len(failed_modules)}")
        for module, error in failed_modules:
            print(f"   - {module}: {error}")
    
    print()
    
    # 3. Testar blueprints
    missing_blueprints = test_blueprint_registration()
    if missing_blueprints:
        issues_found += len(missing_blueprints)
        print(f"\n❌ Blueprints ausentes: {missing_blueprints}")
    
    print()
    
    # 4. Testar banco
    db_ok = test_database_connectivity()
    if not db_ok:
        issues_found += 1
    
    print()
    
    # 5. Testar importações cruzadas
    failed_imports = test_cross_module_imports()
    if failed_imports:
        issues_found += len(failed_imports)
        print(f"\n❌ Importações problemáticas: {len(failed_imports)}")
        for mod1, mod2, error in failed_imports:
            print(f"   - {mod1} → {mod2}: {error}")
    
    print()
    print("=" * 50)
    print(f"⏰ Fim: {datetime.now().strftime('%H:%M:%S')}")
    
    # Resultado final
    if issues_found == 0:
        print("✅ TODOS OS TESTES PASSARAM - Sistema sem regressões detectadas")
        return True
    else:
        print(f"❌ {issues_found} PROBLEMAS DETECTADOS")
        print("⚠️ RECOMENDAÇÃO: Corrija os problemas antes de fazer commit")
        return False

if __name__ == "__main__":
    # Mudar para diretório correto
    script_dir = Path(__file__).parent
    os.chdir(script_dir.parent)
    
    # Executar verificação
    success = run_quick_regression_check()
    
    # Exit code
    sys.exit(0 if success else 1) 