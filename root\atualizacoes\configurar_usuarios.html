<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurar Usuários</title>
    <link rel="stylesheet" href="/static/style-config-usuarios.css">
    <script src="/static/configurar_usuarios.js"></script>
    <style>
        .modal {
            display: none; /* Garantir que o modal esteja oculto por padrão */
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            background-color: #1a2b3c;
            padding: 20px;
            border-radius: 5px;
            width: 300px;
            color: white;
        }
        .fechar-modal {
            float: right;
            cursor: pointer;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .btn-salvar-senha, .btn-fechar-modal {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Configurar Usuários</h1>
        <a href="{{ url_for('index') }}" class="btn-voltar">Voltar</a>

        {% if error_message %}
        <div class="mensagem-erro">
            {{ error_message }}
        </div>
        {% endif %}

        {% if message %}
        <div class="mensagem-sucesso">
            {{ message }}
        </div>
        {% endif %}

        <h2>Adicionar Novo Usuário</h2>
        <form id="adicionar-usuario-form">
            <div class="form-group">
                <label for="usuario">Usuário</label>
                <input type="text" id="usuario" name="usuario" required>
            </div>
            <div class="form-group">
                <label for="senha">Senha</label>
                <input type="password" id="senha" name="senha" required>
            </div>
            <div class="form-group">
                <label for="nivel_acesso">Nível de Acesso</label>
                <select id="nivel_acesso" name="nivel_acesso" required>
                    <option value="usuario">Usuário</option>
                    <option value="admin">Admin</option>
                </select>
            </div>
            <button type="submit" class="btn-adicionar">Adicionar</button>
        </form>

        <h2>Usuários Cadastrados</h2>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Usuário</th>
                    <th>Nível de Acesso</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                {% for user in usuarios %}
                <tr>
                    <td>{{ user.id }}</td>
                    <td>{{ user.usuario }}</td>
                    <td>
                        {% if user.usuario == admin_default_username %}
                            Admin
                        {% else %}
                            <form class="alterar-nivel-form" data-id="{{ user.id }}">
                                <select class="nivel-acesso-select" name="nivel">
                                    <option value="admin" {% if user.nivel_acesso == 'admin' %}selected{% endif %}>Admin</option>
                                    <option value="usuario" {% if user.nivel_acesso == 'usuario' %}selected{% endif %}>Usuário</option>
                                </select>
                                <button type="submit" class="btn-alterar-nivel">Alterar</button>
                            </form>
                        {% endif %}
                    </td>
                    <td>
                        <a href="#" class="abrir-modal-trocar-senha" data-id="{{ user.id }}">Trocar Senha</a>
                        {% if user.usuario != admin_default_username %}
                            <form class="excluir-usuario-form" data-id="{{ user.id }}" style="display:inline;">
                                <button type="submit" class="btn-excluir">Excluir</button>
                            </form>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div id="modal-trocar-senha" class="modal">
        <div class="modal-content">
            <span class="fechar-modal">×</span>
            <h2>Trocar Senha</h2>
            <form id="form-trocar-senha" class="trocar-senha-form">
                <div class="form-group">
                    <label for="nova-senha">Nova Senha</label>
                    <input type="password" class="nova-senha" name="nova_senha" required>
                </div>
                <button type="submit" class="btn-salvar-senha">Salvar Senha</button>
                <button type="button" class="btn-fechar-modal fechar-modal">Fechar</button>
            </form>
        </div>
    </div>
</body>
</html>