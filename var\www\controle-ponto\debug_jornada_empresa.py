#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug para verificar problema com jornada de empresa
"""

import sys
sys.path.append('/var/www/controle-ponto')

def debug_jornada_empresa():
    """Debug do problema de jornada de empresa"""
    try:
        from utils.database import DatabaseManager
        
        print("🔍 DEBUG JORNADA EMPRESA")
        print("=" * 40)
        
        # 1. Buscar empresas com configurações
        print("\n1. EMPRESAS COM CONFIGURAÇÕES:")
        print("-" * 30)
        
        empresas_config = DatabaseManager.execute_query("""
            SELECT 
                e.id, e.razao_social, e.nome_fantasia,
                ec.jornada_segunda_entrada, ec.jornada_segunda_saida_almoco,
                ec.jornada_segunda_entrada_almoco, ec.jornada_segunda_saida
            FROM empresas e
            LEFT JOIN empresas_config ec ON e.id = ec.empresa_id
            WHERE e.ativa = 1
            ORDER BY e.razao_social
            LIMIT 5
        """, fetch_all=True)
        
        for empresa in empresas_config:
            print(f"📋 {empresa['razao_social']}")
            print(f"   ID: {empresa['id']}")
            print(f"   Entrada: {empresa['jornada_segunda_entrada']} (tipo: {type(empresa['jornada_segunda_entrada'])})")
            print(f"   Saída Almoço: {empresa['jornada_segunda_saida_almoco']}")
            print(f"   Entrada Almoço: {empresa['jornada_segunda_entrada_almoco']}")
            print(f"   Saída: {empresa['jornada_segunda_saida']}")
            print()
        
        # 2. Testar conversão problemática
        print("\n2. TESTANDO CONVERSÕES:")
        print("-" * 30)
        
        test_values = [None, '', '08:00:00', '08:00', 'invalid', 0]
        
        for value in test_values:
            try:
                # Simular a lógica atual
                if value:
                    result = str(value)[:5]
                else:
                    result = '08:00'
                print(f"   Valor: {repr(value)} -> Resultado: '{result}'")
            except Exception as e:
                print(f"   Valor: {repr(value)} -> ERRO: {e}")
        
        # 3. Buscar empresa específica para teste
        print("\n3. TESTE COM EMPRESA ESPECÍFICA:")
        print("-" * 30)
        
        empresa_teste = DatabaseManager.execute_query("""
            SELECT id, razao_social FROM empresas WHERE ativa = 1 LIMIT 1
        """, fetch_one=True)
        
        if empresa_teste:
            empresa_id = empresa_teste['id']
            print(f"📋 Testando empresa: {empresa_teste['razao_social']} (ID: {empresa_id})")
            
            # Buscar configuração atual
            config = DatabaseManager.execute_query("""
                SELECT jornada_segunda_entrada, jornada_segunda_saida_almoco,
                       jornada_segunda_entrada_almoco, jornada_segunda_saida
                FROM empresas_config
                WHERE empresa_id = %s
            """, (empresa_id,), fetch_one=True)
            
            if config:
                print("   Configuração encontrada:")
                for key, value in config.items():
                    print(f"     {key}: {repr(value)} (tipo: {type(value)})")
            else:
                print("   ❌ Nenhuma configuração encontrada")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    debug_jornada_empresa()
