# Estrutura Atual do Projeto RLPONTO-WEB

```
c:\Users\<USER>\Documents\RLPONTO-WEB
├── adicao_funcionalidades_relatorios.markdown
├── adicionar_campos_pagamento.sql
├── adicionar_horas_obrigatorias.sql
├── adicionar_indices_performance.sql
├── ANALISE_SISTEMA_AUSENCIAS_FUNCIONARIOS.md
├── aplicacao_configuracoes.log
├── aplicar_correcao_configuracoes_definitiva.py
├── aplicar_correcao_fotos_servidor.sh
├── aplicar_correcoes_configuracoes_sistema.py
├── app.py
├── app_output.log
├── atualizacao_controle_jornada.sql
├── atualizacao_v1-0_rev0001.markdown
├── backup-build/
│   ├── app-original.py
│   ├── app.py.backup_20250109_rate_limiter_fix
│   ├── ...
├── bin/ (vazio)
├── biometria_detector_linux.py
├── biometrico.html
├── bridge-zk4500/
│   ├── analisar.bat
│   ├── biometric_bridge_service.py
│   ├── ...
├── bridge_temp.py
├── capturar_erro_admin.py
├── check_cavalcrod_user.py
├── check_endpoints.py
├── comandos_servidor.sh
├── controle_ponto.sql
├── controle_ponto_bkp08062025.sql
├── cookies.txt
├── ...
├── db/
│   └── controle_ponto.sql
├── debug_configuracoes.py
├── ...
├── deploy.bat
├── deploy.ps1
├── DEPLOYMENT_V2_GUIDE.md
├── deploy_config_fix.py
├── DEPLOY_CORRECAO_AUSENTES_CONCLUIDO.md
├── deploy_ssh.py
├── docs/
│   ├── 01-epi_implantacao_mcp.md
│   ├── ...
│   └── TECNOLOGIAS_RLPONTO.markdown
├── ...
├── lixo/
│   ├── ALTERNACAO_SIMULADOR_ZKAGENT.md
│   ├── ...
├── MCP_VISUAL.md
├── ...
├── requirements.txt
├── resolucao-problema-relatorio.markdown
├── resultado_verificacao.log
├── RLPONTO-WEB-TRAE.code-workspace
├── RLPONTO-WEB.code-workspace
├── root/
│   ├── atualizacoes/
│   │   ├── app.py
│   │   ├── ...
│   ├── atualizador.sh
│   ├── ...
├── scripts/
│   ├── deploy_fix_permissions.bat
│   ├── ...
├── simulacao/
│   ├── CHANGELOG-DESENVOLVIMENTO.md
│   ├── ...
├── static/
│   └── js/
│       ├── biometria-bridge-direct.js
│       ├── ...
├── templates/
│   └── configuracoes/
│       └── index.html
├── tests/
│   └── regression/
│       └── config_funcionario_test.py
├── utils/
│   ├── audit_logger.py
│   ├── ...
├── var/
│   └── www/
│       └── controle-ponto/
│           ├── .env
│           ├── .gitignore
│           ├── adicionar_biometria_mysql.py
│           ├── ...
│           ├── app/
│           │   ├── api/
│           │   │   ├── external_routes.py
│           │   │   └── __init__.py
│           │   ├── config/
│           │   │   ├── api_config.py
│           │   │   └── __init__.py
│           │   ├── services/
│           │   │   ├── external_api_service.py
│           │   │   └── __init__.py
│           │   └── __init__.py
│           ├── logs/
│           │   ├── app.log
│           │   ├── ...
│           ├── scripts/
│           │   └── README.md
│           ├── sql/
│           │   ├── configuracoes_mcp.sql
│           │   └── melhorias_views.sql
│           ├── static/
│           │   ├── biometria-service.js
│           │   ├── ...
│           │   ├── css/
│           │   │   ├── modal-biometria-fix.css
│           │   │   └── relatorios.css
│           │   ├── fotos_funcionarios/
│           │   │   ├── funcionario_1_20250617T074603.jpg
│           │   │   ├── ...
│           │   ├── images/
│           │   │   ├── avatars/
│           │   │   │   ├── funcionario_feminino_1.jpg
│           │   │   │   ├── ...
│           │   │   ├── fingerprint-placeholder.png
│           │   │   └── funcionario_sem_foto.svg
│           │   ├── js/
│           │   │   ├── biometria-unified.js
│           │   │   ├── ...
│           │   ├── style-cadastrar.css
│           │   ├── ...
│           ├── templates/
│           │   ├── base.html
│           │   ├── ...
│           │   ├── configuracoes/
│           │   │   ├── biometria.html
│           │   │   ├── ...
│           │   │   └── index_professional.html
│           │   ├── empresas/
│           │   │   ├── cadastrar.html
│           │   │   ├── ...
│           │   ├── epis/
│           │   │   ├── adicionar.html
│           │   │   └── listar.html
│           │   ├── funcionarios/
│           │   │   ├── cadastrar.html
│           │   │   ├── ...
│           │   ├── horarios_trabalho/
│           │   │   └── index.html
│           │   ├── quality_control/
│           │   │   └── dashboard.html
│           │   ├── registro_ponto/
│           │   │   ├── biometrico.html
│           │   │   └── manual.html
│           │   ├── relatorios/
│           │   │   ├── debug.html
│           │   │   ├── estatisticas.html
│           │   │   └── pontos.html
│           │   ├── status/
│           │   │   └── dashboard.html
│           ├── tests/
│           │   └── regression/ (vazio)
│           ├── utils/
│           │   ├── auth.py
│           │   ├── ...
│           │   └── __pycache__/
│           └── ...
└── ...
```

*Observação: Estrutura detalhada, incluindo subpastas e arquivos principais. Algumas pastas e arquivos foram resumidos com reticências para facilitar a leitura, mas a estrutura principal está completa.*
