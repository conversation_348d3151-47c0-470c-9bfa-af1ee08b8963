#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para Criar Empresa de Teste - RLPONTO-WEB
------------------------------------------------
Cria uma nova empresa de teste para começar do zero.
"""

from utils.database import DatabaseManager

def criar_empresa_teste():
    """
    Cria uma nova empresa de teste
    """
    print("=" * 60)
    print("🏢 CRIAÇÃO DE EMPRESA DE TESTE - RLPONTO-WEB")
    print("=" * 60)
    
    try:
        # Verificar se já existe uma empresa com o CNPJ
        cnpj = '99.999.999/9999-99'
        empresas = DatabaseManager.execute_query(f"SELECT id FROM empresas WHERE cnpj = '{cnpj}'")
        
        if empresas:
            print(f"❌ Já existe uma empresa com o CNPJ {cnpj}")
            return
            
        # Criar nova empresa
        query = """
        INSERT INTO empresas 
        (razao_social, nome_fantasia, cnpj, telefone, email, ativa, empresa_teste) 
        VALUES 
        ('Empresa Teste Limpa', 'Teste Limpa', '99.999.999/9999-99', '(11) 9999-9999', '<EMAIL>', 1, 1)
        """
        
        DatabaseManager.execute_query(query)
        
        # Verificar se foi criada
        nova_empresa = DatabaseManager.execute_query("SELECT id, razao_social, cnpj, empresa_teste FROM empresas WHERE cnpj = '99.999.999/9999-99'")
        
        if nova_empresa:
            print(f"✅ Empresa de teste criada com sucesso!")
            print(f"ID: {nova_empresa[0]['id']}")
            print(f"Razão Social: {nova_empresa[0]['razao_social']}")
            print(f"CNPJ: {nova_empresa[0]['cnpj']}")
            print(f"Empresa de Teste: {'Sim' if nova_empresa[0]['empresa_teste'] == 1 else 'Não'}")
        else:
            print("❌ Falha ao criar empresa de teste")
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        
if __name__ == "__main__":
    criar_empresa_teste() 