#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
<PERSON>ó<PERSON>lo de Funcionários - Controle de Ponto
-------------------------------------------

Este módulo contém todas as rotas e funcionalidades relacionadas
ao gerenciamento de funcionários do sistema.
"""

import json
import logging
import os
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, Response, current_app
from werkzeug.security import generate_password_hash
from datetime import timedelta

# Imports locais
from utils.database import FuncionarioQueries, DatabaseManager
from utils.auth import require_login, require_admin, get_current_user, get_template_context
from utils.helpers import (
    FormValidator, parse_biometria_data, format_date, format_cpf, 
    format_telefone, generate_breadcrumbs, safe_int, gerar_nome_foto_funcionario
)

# Configuração do logger
logger = logging.getLogger('controle-ponto.funcionarios')

def get_empresas_para_funcionario():
    """
    Busca todas as empresas disponíveis para associação com funcionários.
    A empresa principal aparece primeiro na lista.

    Returns:
        list: Lista de empresas ordenada (empresa principal primeiro)
    """
    try:
        logger.info("🔍 [DEBUG] Iniciando busca de empresas para funcionário")

        empresas = DatabaseManager.execute_query("""
            SELECT id, razao_social, nome_fantasia, empresa_principal
            FROM empresas
            WHERE ativa = TRUE
            ORDER BY empresa_principal DESC, razao_social ASC
        """)

        logger.info(f"🔍 [DEBUG] Query executada, resultado: {empresas}")

        if empresas:
            logger.info(f"✅ {len(empresas)} empresas encontradas para seleção")
            # Log detalhado de cada empresa
            for empresa in empresas:
                principal_flag = "⭐ PRINCIPAL" if empresa['empresa_principal'] else ""
                logger.info(f"   - ID {empresa['id']}: {empresa['nome_fantasia']} {principal_flag}")

            # Log da empresa principal
            empresa_principal = next((e for e in empresas if e['empresa_principal']), None)
            if empresa_principal:
                logger.info(f"⭐ Empresa principal: {empresa_principal['nome_fantasia']}")
        else:
            logger.warning("⚠️ Nenhuma empresa encontrada no sistema")

        return empresas or []

    except Exception as e:
        logger.error(f"❌ Erro ao buscar empresas: {e}")
        import traceback
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        return []

def get_jornada_padrao_empresa(empresa_id):
    """
    Busca a jornada padrão de uma empresa específica.

    Args:
        empresa_id (int): ID da empresa

    Returns:
        dict: Dados da jornada padrão da empresa ou None se não encontrada
    """
    try:
        if not empresa_id:
            return None

        # Buscar jornada padrão configurada diretamente na empresa
        jornada = DatabaseManager.execute_query("""
            SELECT
                'Jornada Padrão da Empresa' as nome_horario,
                ec.jornada_segunda_entrada as entrada_manha,
                ec.jornada_segunda_saida_almoco as saida_almoco,
                ec.jornada_segunda_entrada_almoco as entrada_tarde,
                ec.jornada_segunda_saida as saida,
                ec.tolerancia_empresa_minutos as tolerancia_minutos,
                ec.jornada_sexta_entrada,
                ec.jornada_sexta_saida_almoco,
                ec.jornada_sexta_entrada_almoco,
                ec.jornada_sexta_saida,
                ec.intervalo_obrigatorio
            FROM empresas_config ec
            WHERE ec.empresa_id = %s
        """, (empresa_id,))

        # Se não há configuração padrão, buscar a primeira jornada ativa da empresa
        if not jornada:
            jornada = DatabaseManager.execute_query("""
                SELECT ht.id, ht.nome_horario, ht.entrada_manha, ht.saida_almoco,
                       ht.entrada_tarde, ht.saida, ht.tolerancia_minutos
                FROM horarios_trabalho ht
                WHERE ht.empresa_id = %s AND ht.ativo = 1
                ORDER BY ht.id ASC
                LIMIT 1
            """, (empresa_id,))

        if jornada:
            logger.info(f"✅ Jornada encontrada para empresa {empresa_id}: {jornada[0]['nome_horario']}")

            # ✅ CORREÇÃO CRÍTICA: Remover campo 'id' para evitar conflito com dados do funcionário
            resultado = jornada[0].copy()
            if 'id' in resultado:
                jornada_id = resultado.pop('id')  # Remove e guarda o ID
                resultado['horario_trabalho_id'] = jornada_id  # Renomeia para evitar conflito
                logger.info(f"🔧 CORREÇÃO: Campo 'id' removido e renomeado para 'horario_trabalho_id': {jornada_id}")

            return resultado
        else:
            logger.warning(f"⚠️ Nenhuma jornada encontrada para empresa {empresa_id}")
            return None

    except Exception as e:
        logger.error(f"❌ Erro ao buscar jornada da empresa {empresa_id}: {e}")
        return None

def _buscar_jornada_empresa_funcionario(empresa_id):
    """
    Busca a jornada da empresa para exibição no formulário de funcionário.

    Args:
        empresa_id (int): ID da empresa

    Returns:
        dict: Dados da jornada formatados para exibição ou None
    """
    try:
        sql = """
        SELECT
            jt.id,
            jt.nome_jornada,
            jt.seg_qui_entrada,
            jt.seg_qui_saida,
            jt.sexta_entrada,
            jt.sexta_saida,
            jt.intervalo_inicio,
            jt.intervalo_fim,
            jt.tolerancia_entrada_minutos,
            jt.padrao,
            e.razao_social as empresa_nome
        FROM jornadas_trabalho jt
        JOIN empresas e ON jt.empresa_id = e.id
        WHERE jt.empresa_id = %s AND jt.padrao = 1 AND jt.ativa = 1
        LIMIT 1
        """

        jornada = DatabaseManager.execute_query(sql, (empresa_id,))

        if jornada:
            resultado = jornada[0]
            logger.info(f"📋 Jornada da empresa {empresa_id} carregada para exibição: {resultado['nome_jornada']}")
            return resultado
        else:
            logger.warning(f"⚠️ Empresa {empresa_id} não possui jornada padrão para exibição")
            return None

    except Exception as e:
        logger.error(f"❌ Erro ao buscar jornada da empresa {empresa_id} para exibição: {e}")
        return None

def get_empresa_principal_id():
    """
    Busca o ID da empresa principal no sistema.

    Returns:
        int: ID da empresa principal ou None se não encontrada
    """
    try:
        db = DatabaseManager()
        result = db.execute_query(
            "SELECT id FROM empresas WHERE empresa_principal = TRUE AND ativa = TRUE LIMIT 1"
        )
        if result and len(result) > 0:
            empresa_id = result[0]['id']
            logger.info(f"✅ Empresa principal encontrada: ID {empresa_id}")
            return empresa_id
        else:
            logger.warning("⚠️ Nenhuma empresa principal encontrada no sistema")
            return None
    except Exception as e:
        logger.error(f"❌ Erro ao buscar empresa principal: {e}")
        return None

def get_empresa_principal_info():
    """
    Busca informações completas da empresa principal.

    Returns:
        dict: Dados da empresa principal ou None se não encontrada
    """
    try:
        db = DatabaseManager()
        result = db.execute_query(
            "SELECT id, razao_social, nome_fantasia FROM empresas WHERE empresa_principal = TRUE AND ativa = TRUE LIMIT 1"
        )
        if result and len(result) > 0:
            return result[0]
        else:
            return None
    except Exception as e:
        logger.error(f"❌ Erro ao buscar informações da empresa principal: {e}")
        return None

# Criação do Blueprint
funcionarios_bp = Blueprint('funcionarios', __name__, url_prefix='/funcionarios')

# Constantes de validação
VALID_SEXO = ['M', 'F', 'Outro']
VALID_ESTADO_CIVIL = ['Solteiro', 'Casado', 'Divorciado', 'Viuvo']
VALID_TIPO_CONTRATO = ['CLT', 'PJ', 'Estagio', 'Temporario']
VALID_NIVEL_ACESSO = ['Funcionario', 'Supervisao', 'Gerencia']
VALID_TURNO = ['Diurno', 'Noturno', 'Misto']
VALID_STATUS = ['Ativo', 'Inativo']

REQUIRED_FIELDS = [
    'nome_completo', 'cpf', 'rg', 'data_nascimento', 'sexo', 'estado_civil', 'nacionalidade',
    'pis_pasep', 'endereco_cep', 'endereco_estado', 'telefone1',
    'cargo', 'setor_obra', 'matricula_empresa', 'data_admissao', 'tipo_contrato',
    'nivel_acesso', 'tolerancia_ponto', 'status_cadastro',
    'empresa_id'  # ✅ NOVO: Empresa obrigatória
    # ✅ REMOVIDO: ctps_numero, ctps_serie_uf (agora opcionais)
    # ✅ REMOVIDO: turno (controlado pela jornada da empresa)
]

# Campos opcionais com valores padrão
OPTIONAL_FIELDS_WITH_DEFAULTS = {
    'horas_trabalho_obrigatorias': 8.00  # Padrão 8 horas por dia
}

# Campos opcionais de biometria
BIOMETRIA_FIELDS = [
    'digital_dedo1', 'digital_dedo2', 'qualidade_dedo1', 'qualidade_dedo2'
]

# Campos de pagamento/remuneração
PAGAMENTO_FIELDS = [
    'salario_base', 'tipo_pagamento', 'valor_hora', 'valor_hora_extra', 
    'percentual_hora_extra', 'vale_transporte', 'vale_alimentacao', 
    'outros_beneficios', 'desconto_inss', 'desconto_irrf', 
    'observacoes_pagamento', 'data_ultima_alteracao_salario'
]

def get_fotos_dir():
    return os.path.join(current_app.root_path, 'static', 'fotos_funcionarios')

def _safe_decimal(value):
    """
    Converte valor para decimal de forma segura.
    
    Args:
        value (str): Valor a ser convertido
        
    Returns:
        float or None: Valor convertido ou None se inválido
    """
    if not value or not str(value).strip():
        return None
    
    try:
        return float(str(value).strip())
    except (ValueError, TypeError):
        return None

def _validar_campos_pagamento(data, validator):
    """
    Valida campos específicos de pagamento.
    
    Args:
        data (dict): Dados do formulário
        validator (FormValidator): Instância do validador
    """
    # Validar tipo de pagamento
    tipos_validos = ['Mensal', 'Quinzenal', 'Semanal', 'Diario', 'Por_Hora']
    tipo_pagamento = data.get('tipo_pagamento', '')
    if tipo_pagamento and tipo_pagamento not in tipos_validos:
        validator.add_error('tipo_pagamento', f"Tipo de pagamento deve ser um dos: {', '.join(tipos_validos)}")
    
    # Validar valores monetários
    salario_base = data.get('salario_base')
    if salario_base is not None and salario_base < 0:
        validator.add_error('salario_base', "Salário base não pode ser negativo")
    
    valor_hora = data.get('valor_hora')
    if valor_hora is not None and valor_hora < 0:
        validator.add_error('valor_hora', "Valor da hora não pode ser negativo")
    
    valor_hora_extra = data.get('valor_hora_extra')
    if valor_hora_extra is not None and valor_hora_extra < 0:
        validator.add_error('valor_hora_extra', "Valor da hora extra não pode ser negativo")
    
    percentual_hora_extra = data.get('percentual_hora_extra')
    if percentual_hora_extra is not None and (percentual_hora_extra < 0 or percentual_hora_extra > 200):
        validator.add_error('percentual_hora_extra', "Percentual de hora extra deve estar entre 0% e 200%")
    
    # Validar benefícios
    for campo in ['vale_transporte', 'vale_alimentacao', 'outros_beneficios']:
        valor = data.get(campo)
        if valor is not None and valor < 0:
            validator.add_error(campo, f"{campo.replace('_', ' ').title()} não pode ser negativo")
    
    # Validar consistência: se há salário base, deve ter valor hora
    if salario_base and salario_base > 0:
        if not valor_hora or valor_hora <= 0:
            # Calcular automaticamente se não foi fornecido
            data['valor_hora'] = round(salario_base / 220, 2)
            logger.info(f"💰 Valor hora calculado automaticamente: R$ {data['valor_hora']}")
        
        # Calcular hora extra se não foi fornecido
        if percentual_hora_extra and (not valor_hora_extra or valor_hora_extra <= 0):
            valor_hora_calculado = data.get('valor_hora', valor_hora)
            if valor_hora_calculado:
                data['valor_hora_extra'] = round(valor_hora_calculado * (1 + percentual_hora_extra / 100), 2)
                logger.info(f"💰 Valor hora extra calculado automaticamente: R$ {data['valor_hora_extra']}")
    
    logger.info(f"💰 Validação de pagamento concluída - Salário: R$ {salario_base or 0}, Hora: R$ {valor_hora or 0}")

@funcionarios_bp.route('/')
@require_login
def index():
    """
    Lista funcionários com paginação e filtros.
    """
    try:
        # Parâmetros de paginação e filtros
        page = safe_int(request.args.get('page', 1), 1)
        per_page = safe_int(request.args.get('per_page', 10), 10)
        search = request.args.get('search', '').strip()
        status = request.args.get('status', '').strip()
        
        # Busca funcionários com paginação
        result = FuncionarioQueries.get_all(
            page=page,
            per_page=per_page,
            search=search if search else None,
            status=status if status else None
        )
        
        # Contexto do template
        context = get_template_context()
        context.update({
            'funcionarios': result['data'],
            'pagination': result['pagination'],
            'search': search,
            'status': status,
            'breadcrumbs': generate_breadcrumbs('funcionarios'),
            'format_cpf': format_cpf,
            'format_date': format_date
        })
        
        return render_template('funcionarios/index.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao listar funcionários: {e}")
        error_msg = str(e)
        
        # Mensagens específicas baseadas no tipo de erro
        if "Banco de dados indisponível" in error_msg:
            flash("Sistema temporariamente indisponível. Verifique se o MySQL está rodando.", "error")
        elif "Erro de autenticação" in error_msg:
            flash("Erro de configuração do banco. Contate o administrador.", "error")
        elif "não encontrada" in error_msg:
            flash("Base de dados não encontrada. Execute o script de instalação.", "error")
        else:
            flash("Erro ao carregar lista de funcionários", "error")
            
        return redirect(url_for('index'))

@funcionarios_bp.route('/<int:funcionario_id>')
@require_login
def detalhes(funcionario_id):
    """
    Exibe detalhes completos de um funcionário.
    """
    try:
        funcionario = FuncionarioQueries.get_with_epis(funcionario_id)
        
        if not funcionario:
            flash("Funcionário não encontrado", "error")
            return redirect(url_for('funcionarios.index'))
        
        # Contexto do template
        context = get_template_context()
        context.update({
            'funcionario': funcionario,
            'breadcrumbs': generate_breadcrumbs('funcionario_detalhes', funcionario_id=funcionario_id),
            'format_cpf': format_cpf,
            'format_telefone': format_telefone,
            'format_date': format_date
        })
        
        return render_template('funcionarios/detalhes.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar funcionário {funcionario_id}: {e}")
        error_msg = str(e)
        
        # Mensagens específicas baseadas no tipo de erro
        if "Banco de dados indisponível" in error_msg:
            flash("Sistema temporariamente indisponível. Tente novamente em alguns instantes.", "error")
        elif "Erro de autenticação" in error_msg:
            flash("Erro de configuração do sistema. Contate o administrador.", "error")
        elif "não encontrada" in error_msg:
            flash("Base de dados não configurada. Contate o administrador.", "error")
        else:
            flash("Erro ao carregar dados do funcionário", "error")
            
        return redirect(url_for('funcionarios.index'))

@funcionarios_bp.route('/cadastrar', methods=['GET', 'POST'])
@require_login
def cadastrar():
    """
    Cadastra novo funcionário.
    """
    if request.method == 'POST':
        logger.info("🔄 [DEBUG] POST recebido para cadastro de funcionário")
        try:
            return _processar_formulario_funcionario()
        except Exception as e:
            logger.error(f"❌ [DEBUG] Erro na função _processar_formulario_funcionario: {e}")
            import traceback
            logger.error(f"❌ [DEBUG] Traceback: {traceback.format_exc()}")
            flash(f"Erro no processamento: {str(e)}", "error")
            return redirect(url_for('funcionarios.cadastrar'))
    
    # GET: Exibir formulário
    try:
        logger.info("🔄 [CADASTRO] Carregando formulário de cadastro de funcionário")

        # Teste simples de empresas
        try:
            empresas = DatabaseManager.execute_query("""
                SELECT id, razao_social, nome_fantasia, empresa_principal
                FROM empresas
                WHERE ativa = TRUE
                ORDER BY empresa_principal DESC, razao_social ASC
            """)
            logger.info(f"🔍 [CADASTRO] Query direta - Empresas encontradas: {len(empresas) if empresas else 0}")
            if empresas:
                for emp in empresas[:3]:  # Log apenas as 3 primeiras
                    logger.info(f"   - {emp['nome_fantasia']} (ID: {emp['id']})")
        except Exception as emp_error:
            logger.error(f"❌ [CADASTRO] Erro ao buscar empresas: {emp_error}")
            empresas = []

        try:
            proxima_matricula = FuncionarioQueries.get_next_matricula()
            logger.info(f"🔍 [CADASTRO] Próxima matrícula: {proxima_matricula}")
        except Exception as mat_error:
            logger.error(f"❌ [CADASTRO] Erro ao buscar matrícula: {mat_error}")
            proxima_matricula = "0001"

        try:
            context = get_template_context()
            logger.info(f"🔍 [CADASTRO] Template context obtido com sucesso")
        except Exception as ctx_error:
            logger.error(f"❌ [CADASTRO] Erro no template context: {ctx_error}")
            context = {}

        context.update({
            'data': {},
            'proxima_matricula': proxima_matricula,
            'empresas': empresas,
            'breadcrumbs': generate_breadcrumbs('cadastrar'),
            'modo_edicao': False
        })

        logger.info(f"🔍 [CADASTRO] Context preparado com {len(context)} itens")
        return render_template('funcionarios/cadastrar.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao exibir formulário de cadastro: {e}")
        flash("Erro ao carregar formulário", "error")
        return redirect(url_for('funcionarios.index'))

@funcionarios_bp.route('/<int:funcionario_id>/editar', methods=['GET', 'POST'])
@require_admin
def editar(funcionario_id):
    """
    Edita funcionário existente.
    """
    if request.method == 'POST':
        return _processar_formulario_funcionario(funcionario_id)
    
    # GET: Exibir formulário preenchido
    try:
        # ✅ CORREÇÃO CRÍTICA: Usar get_with_epis() para obter dados da jornada da empresa
        funcionario = FuncionarioQueries.get_with_epis(funcionario_id)
        
        if not funcionario:
            flash("Funcionário não encontrado", "error")
            return redirect(url_for('funcionarios.index'))
        
        # Conversão dos campos de horário para string 'HH:MM' se não forem None
        for campo in [
            'jornada_seg_qui_entrada', 'jornada_seg_qui_saida',
            'jornada_sex_entrada', 'jornada_sex_saida',
            'jornada_intervalo_entrada', 'jornada_intervalo_saida']:
            valor = funcionario.get(campo)
            if valor is not None and hasattr(valor, 'strftime'):
                funcionario[campo] = valor.strftime('%H:%M')
            elif isinstance(valor, timedelta):
                total_seconds = int(valor.total_seconds())
                horas = total_seconds // 3600
                minutos = (total_seconds % 3600) // 60
                funcionario[campo] = f"{horas:02d}:{minutos:02d}"
            elif isinstance(valor, str) and valor:
                partes = valor.split(':')
                if len(partes) >= 2:
                    funcionario[campo] = f"{int(partes[0]):02d}:{int(partes[1]):02d}"
                else:
                    funcionario[campo] = valor
            elif valor is None:
                funcionario[campo] = ''
        
        # 🦺 Buscar EPIs do funcionário para exibir no formulário
        funcionario['epis'] = _buscar_epis_funcionario(funcionario_id)

        # 🕐 Buscar jornada da empresa do funcionário para exibição
        jornada_empresa = None
        if funcionario.get('empresa_id'):
            jornada_empresa = _buscar_jornada_empresa_funcionario(funcionario['empresa_id'])

        empresas = get_empresas_para_funcionario()

        context = get_template_context()
        context.update({
            'data': funcionario,
            'funcionario_id': funcionario_id,
            'empresas': empresas,
            'jornada_empresa': jornada_empresa,
            'breadcrumbs': generate_breadcrumbs('funcionario_editar', funcionario_id=funcionario_id),
            'modo_edicao': True
        })

        return render_template('funcionarios/cadastrar.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar funcionário para edição {funcionario_id}: {e}")
        flash("Erro ao carregar dados para edição", "error")
        return redirect(url_for('funcionarios.index'))

@funcionarios_bp.route('/<int:funcionario_id>/apagar', methods=['POST'])
@require_admin
def apagar(funcionario_id):
    """
    Exclui funcionário e dados relacionados.
    """
    try:
        funcionario = FuncionarioQueries.get_by_id(funcionario_id)
        
        if not funcionario:
            flash("Funcionário não encontrado", "error")
            return redirect(url_for('funcionarios.index'))
        
        # Executa exclusão
        success = FuncionarioQueries.delete_funcionario(funcionario_id)
        
        if success:
            flash(f"Funcionário {funcionario['nome_completo']} excluído com sucesso", "success")
            logger.info(f"Funcionário {funcionario_id} excluído por {get_current_user()['usuario']}")
        else:
            flash("Erro ao excluir funcionário", "error")
            
        return redirect(url_for('funcionarios.index'))
        
    except Exception as e:
        logger.error(f"Erro ao excluir funcionário {funcionario_id}: {e}")
        flash("Erro ao excluir funcionário", "error")
        return redirect(url_for('funcionarios.index'))

@funcionarios_bp.route('/<int:funcionario_id>/foto')
@require_login
def foto_funcionario(funcionario_id):
    """
    Serve a foto do funcionário ou a imagem padrão.
    """
    try:
        funcionario = FuncionarioQueries.get_by_id(funcionario_id)

        if funcionario and funcionario.get('foto_3x4'):
            foto_path = funcionario['foto_3x4']

            # Se o campo contém um caminho de arquivo (novo sistema)
            if isinstance(foto_path, str) and foto_path.startswith('fotos_funcionarios/'):
                # Redirecionar para arquivo estático
                return redirect(url_for('static', filename=foto_path))

            # Se o campo contém dados binários (sistema antigo)
            elif isinstance(foto_path, (bytes, bytearray)):
                return Response(foto_path, mimetype='image/jpeg')

            # Se é string mas não é caminho válido, tentar como arquivo estático
            elif isinstance(foto_path, str):
                return redirect(url_for('static', filename=foto_path))

        # Fallback para imagem padrão
        return redirect(url_for('static', filename='images/funcionario_sem_foto.svg'))

    except Exception as e:
        logger.error(f"Erro ao carregar foto do funcionário {funcionario_id}: {e}")
        return redirect(url_for('static', filename='images/funcionario_sem_foto.svg'))

@funcionarios_bp.route('/api/<int:funcionario_id>', methods=['GET'])
@require_login
def api_get_funcionario(funcionario_id):
    """
    API para obter dados de um funcionário.
    """
    try:
        funcionario = FuncionarioQueries.get_by_id(funcionario_id)
        
        if not funcionario:
            return jsonify({'error': 'Funcionário não encontrado'}), 404
        
        # Remove dados sensíveis da resposta
        funcionario.pop('digital_dedo1', None)
        funcionario.pop('digital_dedo2', None)
        funcionario.pop('foto_3x4', None)
        funcionario.pop('assinatura', None)
        
        return jsonify(funcionario)
        
    except Exception as e:
        logger.error(f"Erro na API get funcionário {funcionario_id}: {e}")
        return jsonify({'error': 'Erro interno do servidor'}), 500

def _processar_formulario_funcionario(funcionario_id=None):
    """
    Processa formulário de cadastro/edição de funcionário.
    
    Args:
        funcionario_id (int, optional): ID do funcionário para edição
        
    Returns:
        Response: Redirect ou template com erros
    """
    try:
        # 🔍 DEBUG: Log todos os dados do formulário recebidos
        logger.error(f"🔍 [DEBUG FORM] request.form.keys(): {list(request.form.keys())}")
        campos_id = [k for k in request.form.keys() if 'id' in k.lower()]
        logger.error(f"🔍 [DEBUG FORM] Campos com 'id': {campos_id}")
        for campo in campos_id:
            valor = request.form.get(campo, '')
            logger.error(f"🔍 [DEBUG FORM] {campo} = '{valor}' (vazio: {not valor})")

        # ✅ VERIFICAÇÃO CRÍTICA: Verificar se há campo 'id' isolado
        if 'id' in request.form:
            valor_id_isolado = request.form.get('id', '')
            logger.error(f"🚨 [CRÍTICO] Campo 'id' isolado encontrado no formulário: '{valor_id_isolado}'")

        # Coleta dados do formulário
        form_data = _extrair_dados_formulario()

        # ✅ CORREÇÃO CRÍTICA: Garantir que 'id' NUNCA esteja nos dados principais
        if 'id' in form_data:
            logger.error(f"🚨 [CORREÇÃO CRÍTICA] Campo 'id' encontrado nos dados extraídos: '{form_data['id']}' - REMOVENDO!")
            form_data.pop('id', None)
            logger.info(f"✅ [CORREÇÃO] Campo 'id' removido dos dados principais")
        else:
            logger.info(f"✅ [VERIFICAÇÃO] Campo 'id' NÃO está nos dados extraídos")

        # ✅ CORREÇÃO: Verificar e remover TODOS os campos de EPIs dos dados principais
        campos_epi_proibidos = ['id', 'epi_nome', 'epi_ca', 'epi_data_entrega', 'epi_data_validade', 'epi_observacoes']
        campos_removidos = []
        for campo in campos_epi_proibidos:
            if campo in form_data:
                form_data.pop(campo, None)
                campos_removidos.append(campo)

        if campos_removidos:
            logger.warning(f"🚨 [CORREÇÃO] Removidos campos de EPIs dos dados principais: {campos_removidos}")

        # 🛡️ ADICIONA funcionario_id aos dados para validação de segurança
        if funcionario_id:
            form_data['funcionario_id'] = funcionario_id

        # Validação
        validator = FormValidator()
        _validar_dados_funcionario(form_data, validator)
        
        if validator.has_errors():
            # 🔍 DEBUG TEMPORÁRIO: Log detalhado dos erros
            errors_dict = validator.get_errors()
            logger.error(f"🔍 [DEBUG ERRO ID] Erros encontrados na validação:")
            for field, field_errors in errors_dict.items():
                logger.error(f"🔍 [DEBUG ERRO ID] Campo '{field}': {field_errors}")
                if field == 'id':
                    logger.error(f"🔍 [DEBUG ERRO ID] ERRO 'id' CONFIRMADO! Valor nos dados: {form_data.get('id', 'NÃO ENCONTRADO')}")
                    logger.error(f"🔍 [DEBUG ERRO ID] Valor no request.form: {request.form.get('id', 'NÃO ENCONTRADO')}")
                    # ✅ CORREÇÃO CRÍTICA: Remover erro de 'id' se existir
                    logger.error(f"🚨 [CORREÇÃO CRÍTICA] Removendo erro do campo 'id' - este campo não deveria ser validado!")
                    errors_dict.pop('id', None)

            empresas = get_empresas_para_funcionario()
            context = get_template_context()

            # ✅ VERIFICAÇÃO FINAL: Garantir que não há erro de 'id'
            if 'id' in errors_dict:
                logger.error(f"🚨 [CORREÇÃO FINAL] Erro de 'id' ainda presente - REMOVENDO FORÇADAMENTE!")
                errors_dict.pop('id', None)

            # ✅ CORREÇÃO: Converter dicionário de erros para lista
            errors_list = []
            for field, field_errors in errors_dict.items():
                # ✅ PROTEÇÃO ADICIONAL: Pular campo 'id' se ainda estiver presente
                if field == 'id':
                    logger.warning(f"🚨 [PROTEÇÃO] Pulando erro do campo 'id' na conversão para lista")
                    continue
                for error_msg in field_errors:
                    field_name = field.replace('_', ' ').title()
                    errors_list.append(f"{field_name}: {error_msg}")

            logger.error(f"🔍 [DEBUG ERRO ID] Erros convertidos para lista: {errors_list}")

            # ✅ VERIFICAÇÃO FINAL: Se não há erros após limpeza, continuar processamento
            if not errors_list:
                logger.info(f"✅ [CORREÇÃO] Todos os erros foram removidos (eram apenas de 'id') - continuando processamento")
                # Pular para o processamento normal (não retornar aqui)
            else:
                # Há erros reais, mostrar formulário com erros
                empresas = get_empresas_para_funcionario()
                context = get_template_context()

                context.update({
                    'errors': errors_list,  # ✅ CORRIGIDO: Lista em vez de dicionário
                    'data': form_data,
                    'proxima_matricula': FuncionarioQueries.get_next_matricula() if not funcionario_id else None,
                    'funcionario_id': funcionario_id,
                    'empresas': empresas,
                    'modo_edicao': funcionario_id is not None,
                    'breadcrumbs': generate_breadcrumbs(
                        'funcionario_editar' if funcionario_id else 'cadastrar',
                        funcionario_id=funcionario_id
                    )
                })
                return render_template('funcionarios/cadastrar.html', **context)
        
        # Processamento de dados
        try:
            logger.info(f"🔄 [PROCESSAMENTO] Iniciando processamento de dados do funcionário")
            processed_data = _processar_dados_funcionario(form_data, funcionario_id=funcionario_id)
            logger.info(f"✅ [PROCESSAMENTO] Dados processados com sucesso")
        except Exception as e:
            logger.error(f"❌ [PROCESSAMENTO] Erro ao processar dados: {e}")
            flash("Erro ao processar dados do funcionário", "error")
            return redirect(url_for('funcionarios.index'))

        if funcionario_id:
            # Atualização
            logger.info(f"🔄 [ATUALIZAÇÃO] Atualizando funcionário {funcionario_id}")
            success = _atualizar_funcionario(funcionario_id, processed_data)
            if success:
                flash("Funcionário atualizado com sucesso", "success")
                return redirect(url_for('funcionarios.detalhes', funcionario_id=funcionario_id))
        else:
            # Criação
            logger.info(f"🔄 [CRIAÇÃO] Criando novo funcionário")
            try:
                funcionario_id = _criar_funcionario(processed_data)
            except Exception as e:
                logger.error(f"❌ [CRIAÇÃO] Erro ao criar funcionário: {e}")
                flash(f"Erro ao cadastrar funcionário: {str(e)}", "error")
                return redirect(url_for('funcionarios.index'))

            if funcionario_id:
                # Mensagem de sucesso básica
                flash("Funcionário cadastrado com sucesso", "success")

                # ⚠️ NOVO: Verificar se precisa avisar sobre jornada
                if processed_data.get('_empresa_sem_jornada'):
                    empresa_nome = processed_data.get('_empresa_nome', 'selecionada')
                    aviso_jornada = (
                        f"⚠️ ATENÇÃO: A empresa '{empresa_nome}' não possui jornadas de trabalho cadastradas. "
                        f"Para o funcionamento completo do sistema, é necessário cadastrar pelo menos uma "
                        f"jornada para esta empresa. Acesse Empresas → Jornadas de Trabalho para configurar."
                    )
                    flash(aviso_jornada, "warning")
                    logger.warning(f"⚠️ Funcionário {funcionario_id} cadastrado em empresa sem jornada: {empresa_nome}")

                return redirect(url_for('funcionarios.detalhes', funcionario_id=funcionario_id))
        
        # Se chegou aqui, houve erro
        flash("Erro ao processar dados do funcionário", "error")
        return redirect(url_for('funcionarios.index'))
        
    except Exception as e:
        # Trata erros de validação específicos de segurança
        msg_erro = str(e)
        if "ACESSO NEGADO" in msg_erro or "administradores" in msg_erro.lower():
            flash("🚫 Acesso negado: Somente administradores podem editar biometria existente", "error")
        elif hasattr(e, 'args') and e.args and isinstance(e.args[0], dict):
            # Se o erro for um dict de erros de campo
            erros = e.args[0]
            msg_erro = "; ".join([f"{campo}: {', '.join(msgs)}" for campo, msgs in erros.items()])
            flash(f"Erro de validação: {msg_erro}", "error")
        else:
            flash(f"Erro ao processar formulário: {msg_erro}", "error")
            
        logger.error(f"Erro ao processar formulário: {msg_erro}")
        return redirect(url_for('funcionarios.index'))

def _extrair_dados_formulario():
    """
    Extrai e organiza dados do formulário.

    Returns:
        dict: Dados organizados do formulário
    """
    # ✅ CORREÇÃO CRÍTICA: Filtrar campos de EPIs ANTES de processar dados principais
    logger.info(f"🔍 [DEBUG FORM] Iniciando extração de dados do formulário")
    logger.info(f"🔍 [DEBUG FORM] Total de campos no request.form: {len(request.form)}")

    # Identificar e filtrar campos de EPIs
    campos_epi_encontrados = []
    campos_id_epi = []
    for key in request.form.keys():
        if key.startswith('epis[') and '][' in key:
            campos_epi_encontrados.append(key)
            if key.endswith('][id]'):
                campos_id_epi.append(key)

    if campos_epi_encontrados:
        logger.info(f"🦺 [EPIs] Encontrados {len(campos_epi_encontrados)} campos de EPIs: {campos_epi_encontrados[:5]}...")

    if campos_id_epi:
        logger.info(f"🆔 [EPIs ID] Encontrados {len(campos_id_epi)} campos de ID de EPIs: {campos_id_epi}")

    # ✅ CORREÇÃO CRÍTICA: Verificar se há campo 'id' isolado no formulário
    if 'id' in request.form:
        valor_id = request.form.get('id', '').strip()
        logger.error(f"🚨 [CORREÇÃO CRÍTICA] Campo 'id' isolado encontrado no formulário: '{valor_id}' - SERÁ IGNORADO!")

    dados_principais = {
        # Dados pessoais
        'nome_completo': request.form.get('nome_completo', '').strip(),
        'cpf': request.form.get('cpf', '').strip(),
        'rg': request.form.get('rg', '').strip(),
        'data_nascimento': request.form.get('data_nascimento', '').strip(),
        'sexo': request.form.get('sexo', '').strip(),
        'estado_civil': request.form.get('estado_civil', '').strip(),
        'nacionalidade': request.form.get('nacionalidade', '').strip(),

        # Documentos trabalhistas
        'ctps_numero': request.form.get('ctps_numero', '').strip() or None,  # ✅ OPCIONAL
        'ctps_serie_uf': request.form.get('ctps_serie_uf', '').strip() or None,  # ✅ OPCIONAL
        'pis_pasep': request.form.get('pis_pasep', '').strip(),

        # Endereço
        'endereco_rua': request.form.get('endereco_rua', '').strip(),
        'endereco_bairro': request.form.get('endereco_bairro', '').strip(),
        'endereco_cidade': request.form.get('endereco_cidade', '').strip(),
        'endereco_cep': request.form.get('endereco_cep', '').strip(),
        'endereco_estado': request.form.get('endereco_estado', '').strip(),

        # Contato
        'telefone1': request.form.get('telefone1', '').strip(),
        'telefone2': request.form.get('telefone2', '').strip(),
        'email': request.form.get('email', '').strip(),

        # Dados profissionais
        'empresa_id': request.form.get('empresa_id', '').strip(),  # ✅ NOVO: Campo empresa obrigatório
        'cargo': request.form.get('cargo', '').strip(),
        'setor_obra': request.form.get('setor_obra', '').strip(),
        'matricula_empresa': request.form.get('matricula_empresa', '').strip(),
        'data_admissao': request.form.get('data_admissao', '').strip(),
        'tipo_contrato': request.form.get('tipo_contrato', '').strip(),
        'status_cadastro': request.form.get('status_cadastro', '').strip(),

        # Jornada de trabalho
        'jornada_seg_qui_entrada': request.form.get('jornada_seg_qui_entrada', '').strip(),
        'jornada_seg_qui_saida': request.form.get('jornada_seg_qui_saida', '').strip(),
        'jornada_sex_entrada': request.form.get('jornada_sex_entrada', '').strip(),
        'jornada_sex_saida': request.form.get('jornada_sex_saida', '').strip(),
        'jornada_intervalo_entrada': request.form.get('jornada_intervalo_entrada', '').strip(),
        'jornada_intervalo_saida': request.form.get('jornada_intervalo_saida', '').strip(),

        # Configurações
        'nivel_acesso': request.form.get('nivel_acesso', '').strip(),
        'turno': 'Diurno',  # ✅ VALOR PADRÃO: Campo controlado pela jornada da empresa
        'tolerancia_ponto': safe_int(request.form.get('tolerancia_ponto', 5), 5),
        'banco_horas': 1 if request.form.get('banco_horas') == 'on' else 0,
        'hora_extra': 1 if request.form.get('hora_extra') == 'on' else 0,

        # Biometria e foto
        'digital_dedo1': request.form.get('digital_dedo1', '').strip(),
        'digital_dedo2': request.form.get('digital_dedo2', '').strip(),
        'qualidade_dedo1': safe_int(request.form.get('qualidade_dedo1', 0), 0),
        'qualidade_dedo2': safe_int(request.form.get('qualidade_dedo2', 0), 0),
        'foto_3x4': request.files.get('foto_3x4'),

        # Pagamento e remuneração
        'salario_base': _safe_decimal(request.form.get('salario_base', '')),
        'tipo_pagamento': request.form.get('tipo_pagamento', 'Mensal').strip(),
        'valor_hora': _safe_decimal(request.form.get('valor_hora', '')),
        'valor_hora_extra': _safe_decimal(request.form.get('valor_hora_extra', '')),
        'percentual_hora_extra': _safe_decimal(request.form.get('percentual_hora_extra', '50.00')),
        'vale_transporte': _safe_decimal(request.form.get('vale_transporte', '')),
        'vale_alimentacao': _safe_decimal(request.form.get('vale_alimentacao', '')),
        'outros_beneficios': _safe_decimal(request.form.get('outros_beneficios', '')),
        'desconto_inss': 1 if request.form.get('desconto_inss') == 'on' else 0,
        'desconto_irrf': 1 if request.form.get('desconto_irrf') == 'on' else 0,
        'observacoes_pagamento': request.form.get('observacoes_pagamento', '').strip(),

        # 🦺 EPIs - Integração com formulário de funcionários
        'epis': _extrair_dados_epis()
    }

    # ✅ CORREÇÃO CRÍTICA: Garantir que campos de EPIs e 'id' NUNCA sejam incluídos nos dados principais
    campos_proibidos = ['id', 'epi_nome', 'epi_ca', 'epi_data_entrega', 'epi_data_validade', 'epi_observacoes']
    campos_removidos = []

    # ✅ PROTEÇÃO ADICIONAL: Verificar se algum campo de EPI vazou para os dados principais
    for key in list(dados_principais.keys()):
        if key.startswith('epis[') or key in campos_proibidos:
            logger.warning(f"⚠️ CORREÇÃO: Removendo campo proibido '{key}' dos dados principais do funcionário")
            dados_principais.pop(key, None)
            campos_removidos.append(key)

    if campos_removidos:
        logger.info(f"✅ CORREÇÃO: Removidos {len(campos_removidos)} campos proibidos: {campos_removidos}")

    # ✅ VERIFICAÇÃO FINAL: Garantir que 'id' não está nos dados
    if 'id' in dados_principais:
        logger.error(f"🚨 ERRO CRÍTICO: Campo 'id' ainda está nos dados principais após limpeza!")
        dados_principais.pop('id', None)
    else:
        logger.info(f"✅ VERIFICAÇÃO: Campo 'id' NÃO está nos dados principais - OK!")

    logger.info(f"✅ [DEBUG FORM] Extração concluída com {len(dados_principais)} campos principais")
    return dados_principais

def _extrair_dados_epis(form_data=None):
    """
    Extrai dados de EPIs do formulário.

    Args:
        form_data (dict, optional): Dados do formulário. Se None, usa request.form

    Returns:
        list: Lista de EPIs com dados validados
    """
    epis = []

    # Usar form_data se fornecido, caso contrário usar request.form
    if form_data is None:
        form_data = request.form

    # ✅ CORREÇÃO CRÍTICA: Log para debug
    logger.info(f"🦺 [EPIs] Iniciando extração de dados de EPIs")

    # Buscar todos os campos com padrão epis[index][campo]
    for key in form_data.keys():
        if key.startswith('epis[') and '][' in key:
            # ✅ CORREÇÃO: Log campos de EPIs encontrados
            logger.info(f"🦺 [EPIs] Campo encontrado: {key}")

            # Extrair índice e campo (ex: epis[0][epi_nome] -> índice=0, campo=epi_nome)
            try:
                start = key.find('[') + 1
                end = key.find(']')
                index = int(key[start:end])

                campo_start = key.find('][') + 2
                campo_end = key.rfind(']')
                campo = key[campo_start:campo_end]

                # ✅ CORREÇÃO CRÍTICA: Processar campos 'id' de EPIs de forma segura
                if campo == 'id':
                    valor = form_data.get(key, '').strip()
                    if not valor or valor == '' or valor == 'new-':
                        logger.info(f"🦺 [EPIs] Ignorando campo 'id' vazio/novo: {key} = '{valor}'")
                        continue
                    logger.info(f"🦺 [EPIs] Campo 'id' de EPI válido encontrado: {key} = {valor}")
                    # ✅ IMPORTANTE: Este 'id' é específico do EPI, não do funcionário!

                # Garantir que existe EPI no índice
                while len(epis) <= index:
                    epis.append({})

                # ✅ CORREÇÃO CRÍTICA: Adicionar valor ao EPI (não aos dados principais!)
                valor = form_data.get(key, '').strip()
                epis[index][campo] = valor

                # ✅ CORREÇÃO CRÍTICA: Garantir que campos de EPIs NUNCA sejam adicionados aos dados principais
                logger.debug(f"🦺 [EPIs] Campo '{campo}' adicionado ao EPI {index}, NÃO aos dados principais")

            except (ValueError, IndexError):
                logger.warning(f"Campo EPI inválido: {key}")
                continue
    
    # Filtrar EPIs válidos (que têm pelo menos um nome)
    epis_validos = []
    for epi in epis:
        if epi.get('epi_nome'):
            epi_data = {
                'epi_nome': epi.get('epi_nome', '').strip(),
                'epi_ca': epi.get('epi_ca', '').strip(),
                'epi_data_entrega': epi.get('epi_data_entrega', '').strip(),
                'epi_data_validade': epi.get('epi_data_validade', '').strip(),
                'epi_observacoes': epi.get('epi_observacoes', '').strip()
            }

            # ✅ CORREÇÃO: Incluir ID apenas se não estiver vazio (para edição)
            epi_id = epi.get('id', '').strip()
            if epi_id:
                epi_data['id'] = epi_id

            epis_validos.append(epi_data)
    
    logger.info(f"[EPIs FORMULÁRIO] Extraídos {len(epis_validos)} EPIs válidos")

    # Debug: Mostrar EPIs extraídos
    for i, epi in enumerate(epis_validos):
        logger.debug(f"[EPIs FORMULÁRIO] EPI {i+1} extraído: {epi}")

    return epis_validos

def _validar_dados_funcionario(data, validator):
    """
    Valida dados do funcionário.

    Args:
        data (dict): Dados a serem validados
        validator (FormValidator): Instância do validador
    """
    # ✅ CORREÇÃO CRÍTICA: Filtrar campos que não devem ser validados como campos do funcionário
    campos_excluir = ['id', 'epi_nome', 'epi_ca', 'epi_data_entrega', 'epi_data_validade', 'epi_observacoes', 'epis']

    # ✅ CORREÇÃO: Verificar e remover campos indevidamente incluídos nos dados principais
    campos_removidos = []
    for campo_excluir in campos_excluir:
        if campo_excluir in data:
            logger.warning(f"⚠️ CORREÇÃO: Campo '{campo_excluir}' encontrado nos dados principais - removendo")
            data.pop(campo_excluir, None)
            campos_removidos.append(campo_excluir)

    if campos_removidos:
        logger.info(f"✅ CORREÇÃO: Removidos {len(campos_removidos)} campos de EPIs dos dados principais: {campos_removidos}")

    # ✅ CORREÇÃO CRÍTICA: Validar apenas campos que realmente pertencem ao funcionário
    logger.info(f"🔍 DEBUG: Iniciando validação de {len(REQUIRED_FIELDS)} campos obrigatórios")
    for field in REQUIRED_FIELDS:
        # ✅ CORREÇÃO CRÍTICA: NUNCA validar campo 'id' como obrigatório
        if field == 'id':
            logger.warning(f"🚨 CORREÇÃO: Campo 'id' encontrado em REQUIRED_FIELDS - IGNORANDO!")
            continue

        valor = data.get(field)
        logger.info(f"🔍 DEBUG: Campo obrigatório '{field}' = '{valor}' (tipo: {type(valor)})")
        validator.validate_required(valor, field)

    # ✅ NOVO: Aplicar valores padrão para campos opcionais
    for field, default_value in OPTIONAL_FIELDS_WITH_DEFAULTS.items():
        if not data.get(field):
            data[field] = default_value
            logger.info(f"✅ Campo {field} definido com valor padrão: {default_value}")
    
    # Validações específicas
    validator.validate_cpf(data.get('cpf'))
    validator.validate_email(data.get('email'))
    validator.validate_choice(data.get('sexo'), VALID_SEXO, 'sexo')
    validator.validate_choice(data.get('estado_civil'), VALID_ESTADO_CIVIL, 'estado_civil')
    validator.validate_choice(data.get('tipo_contrato'), VALID_TIPO_CONTRATO, 'tipo_contrato')
    validator.validate_choice(data.get('nivel_acesso'), VALID_NIVEL_ACESSO, 'nivel_acesso')
    # ✅ REMOVIDO: Validação de turno (controlado pela jornada da empresa)
    validator.validate_choice(data.get('status_cadastro'), VALID_STATUS, 'status_cadastro')
    validator.validate_date(data.get('data_nascimento'), 'data_nascimento')
    validator.validate_date(data.get('data_admissao'), 'data_admissao')
    
    # Validações de pagamento
    _validar_campos_pagamento(data, validator)

    # ✅ NOVO: Validação de empresa (flexível)
    empresa_id = data.get('empresa_id')
    logger.info(f"🔍 DEBUG: empresa_id recebido = '{empresa_id}' (tipo: {type(empresa_id)})")

    # Verificar se empresa_id é válido (não vazio, não None, não string vazia)
    if empresa_id and str(empresa_id).strip():
        try:
            empresa_id_int = int(empresa_id)

            # 1. Verificar se a empresa existe e está ativa
            empresa_dados = DatabaseManager.execute_query(
                "SELECT id, nome_fantasia, razao_social FROM empresas WHERE id = %s AND ativa = TRUE",
                (empresa_id_int,)
            )

            if not empresa_dados:
                validator.add_error('empresa_id', 'Empresa selecionada não existe ou está inativa')
            else:
                empresa = empresa_dados[0]

                # 2. ⚠️ VERIFICAÇÃO DE JORNADA (não bloqueia, apenas registra)
                jornadas_empresa = DatabaseManager.execute_query(
                    "SELECT id, nome_horario FROM horarios_trabalho WHERE empresa_id = %s AND ativo = 1",
                    (empresa_id_int,)
                )

                if not jornadas_empresa:
                    # ⚠️ AVISO: Empresa sem jornada (não bloqueia o cadastro)
                    logger.warning(f"⚠️ AVISO: Empresa {empresa_id_int} ({empresa['nome_fantasia']}) sem jornadas cadastradas")
                    # Armazenar informação para exibir aviso após o cadastro
                    data['_empresa_sem_jornada'] = True
                    data['_empresa_nome'] = empresa['nome_fantasia']
                else:
                    logger.info(f"✅ Empresa {empresa_id_int} possui {len(jornadas_empresa)} jornada(s) cadastrada(s)")
                    data['_empresa_sem_jornada'] = False

        except (ValueError, TypeError):
            validator.add_error('empresa_id', 'ID da empresa deve ser um número válido')
    else:
        # empresa_id está vazio, None ou string vazia
        if empresa_id == '' or empresa_id is None:
            validator.add_error('empresa_id', 'Empresa é obrigatória')
        else:
            logger.warning(f"⚠️ empresa_id inválido: '{empresa_id}' (tipo: {type(empresa_id)})")

    # 🛡️ VALIDAÇÃO CRÍTICA DE SEGURANÇA BIOMÉTRICA - CORREÇÃO DEFINITIVA
    current_user = get_current_user()
    is_admin = current_user.get('is_admin', False)
    funcionario_id = data.get('funcionario_id')
    
    # 🔧 CORREÇÃO DEFINITIVA: Distinguir entre dados novos (JSON) e dados existentes (templates binários)
    nova_biometria_dedo1 = data.get('digital_dedo1', '').strip()
    nova_biometria_dedo2 = data.get('digital_dedo2', '').strip()
    
    logger.info(f"[VALIDAÇÃO BIOMETRIA] Funcionário ID: {funcionario_id}")
    logger.info(f"[VALIDAÇÃO BIOMETRIA] Dedo1 recebido: {bool(nova_biometria_dedo1)} - Tamanho: {len(nova_biometria_dedo1) if nova_biometria_dedo1 else 0}")
    logger.info(f"[VALIDAÇÃO BIOMETRIA] Dedo2 recebido: {bool(nova_biometria_dedo2)} - Tamanho: {len(nova_biometria_dedo2) if nova_biometria_dedo2 else 0}")
    logger.info(f"[VALIDAÇÃO BIOMETRIA] É admin: {is_admin}")
    
    # 🚨 CORREÇÃO CRÍTICA: NUNCA validar biometria em edições
    # O problema é que campos hidden vêm preenchidos com dados do banco (templates binários)
    # Validação deve ser APENAS para dados JSON novos do modal de captura
    
    def eh_dado_biometrico_novo(biometria_data):
        """
        Verifica se o dado é uma nova captura biométrica (JSON) ou dado existente (template binário).
        
        Returns:
            bool: True se for nova captura (JSON), False se for dado existente ou vazio
        """
        if not biometria_data or not biometria_data.strip():
            return False
        
        # Tenta parsear como JSON - se conseguir, é nova captura
        try:
            parsed = json.loads(biometria_data)
            if isinstance(parsed, dict) and 'template' in parsed:
                logger.info(f"[VALIDAÇÃO BIOMETRIA] Detectado JSON válido (nova captura): {biometria_data[:50]}...")
                return True
        except (json.JSONDecodeError, TypeError):
            pass
        
        # Se não é JSON válido, é template binário do banco (dados existentes)
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Detectado template binário (dados existentes): {len(biometria_data)} bytes")
        return False
    
    # ✅ VALIDAÇÃO APENAS PARA NOVAS CAPTURAS (JSON)
    if eh_dado_biometrico_novo(nova_biometria_dedo1):
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Validando NOVA captura Dedo1...")
        if not parse_biometria_data(nova_biometria_dedo1):
            validator.add_error('digital_dedo1', "Formato inválido para nova biometria do dedo 1")
            logger.error(f"[VALIDAÇÃO BIOMETRIA] Nova captura Dedo1 inválida: {nova_biometria_dedo1[:50]}...")
    else:
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Dedo1 - Sem nova captura (vazio ou dados existentes)")
    
    if eh_dado_biometrico_novo(nova_biometria_dedo2):
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Validando NOVA captura Dedo2...")
        if not parse_biometria_data(nova_biometria_dedo2):
            validator.add_error('digital_dedo2', "Formato inválido para nova biometria do dedo 2")
            logger.error(f"[VALIDAÇÃO BIOMETRIA] Nova captura Dedo2 inválida: {nova_biometria_dedo2[:50]}...")
    else:
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Dedo2 - Sem nova captura (vazio ou dados existentes)")
    
    # 🚨 PROTEÇÃO CONTRA EDIÇÃO NÃO AUTORIZADA DE BIOMETRIA
    # ✅ CORREÇÃO: Só verifica se há tentativa real de nova captura por usuário não-admin
    if funcionario_id and not is_admin:
        try:
            # Verifica se há biometria existente no banco
            funcionario_atual = FuncionarioQueries.get_by_id(funcionario_id)
            if funcionario_atual:
                biometria_existente_dedo1 = funcionario_atual.get('digital_dedo1')
                biometria_existente_dedo2 = funcionario_atual.get('digital_dedo2')
                
                logger.info(f"[VALIDAÇÃO BIOMETRIA] Biometria existente - Dedo1: {bool(biometria_existente_dedo1)}, Dedo2: {bool(biometria_existente_dedo2)}")
                
                # ✅ PROTEÇÃO: Só bloqueia se há NOVA CAPTURA sendo enviada por usuário não-admin
                if biometria_existente_dedo1 and eh_dado_biometrico_novo(nova_biometria_dedo1):
                    logger.warning(f"[SEGURANÇA] Usuário não-admin {current_user.get('usuario')} tentou capturar nova biometria dedo1 para funcionário {funcionario_id}")
                    validator.add_error('digital_dedo1', "🚫 ACESSO NEGADO: Somente administradores podem alterar biometria existente")
                
                if biometria_existente_dedo2 and eh_dado_biometrico_novo(nova_biometria_dedo2):
                    logger.warning(f"[SEGURANÇA] Usuário não-admin {current_user.get('usuario')} tentou capturar nova biometria dedo2 para funcionário {funcionario_id}")
                    validator.add_error('digital_dedo2', "🚫 ACESSO NEGADO: Somente administradores podem alterar biometria existente")
                        
        except Exception as e:
            logger.error(f"[SEGURANÇA] Erro ao validar permissões de biometria: {e}")
            # Em caso de erro, bloquear apenas se há NOVA CAPTURA sendo enviada
            if not is_admin and (eh_dado_biometrico_novo(nova_biometria_dedo1) or eh_dado_biometrico_novo(nova_biometria_dedo2)):
                validator.add_error('biometria', "Erro de segurança: não foi possível validar permissões para nova captura biométrica")
    else:
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Saltando verificação de permissões - Admin: {is_admin}, Novo cadastro: {not funcionario_id}")
    
    logger.info(f"[VALIDAÇÃO BIOMETRIA] Validação concluída - Erros encontrados: {validator.has_errors()}")

def _processar_dados_funcionario(data, funcionario_id=None):
    """
    Processa e converte dados do funcionário para inserção no banco.

    Args:
        data (dict): Dados brutos do formulário
        funcionario_id (int, optional): ID do funcionário para edição

    Returns:
        dict: Dados processados
    """
    try:
        logger.info(f"🔄 [PROCESSAMENTO] Iniciando processamento de dados do funcionário")
        logger.info(f"🔍 [DEBUG] Dados recebidos: {len(data)} campos")
        logger.info(f"🔍 [DEBUG] Funcionário ID: {funcionario_id}")

        # ✅ VALIDAÇÃO INICIAL: Verificar se dados básicos estão presentes
        if not data:
            raise ValueError("Dados do formulário estão vazios")

        # ✅ LÓGICA SIMPLIFICADA: Jornada sempre controlada pela empresa
        empresa_id = data.get('empresa_id')
        logger.info(f"🔍 [DEBUG] Empresa ID: {empresa_id}")

        if not funcionario_id:
            # 🆕 NOVO CADASTRO: Aplicar jornada da empresa automaticamente
            if empresa_id:
                logger.info(f"🕐 [NOVO CADASTRO] Aplicando jornada da empresa {empresa_id}")

                try:
                    # Buscar jornada padrão da empresa
                    jornada_empresa = DatabaseManager.execute_query("""
                        SELECT id FROM jornadas_trabalho
                        WHERE empresa_id = %s AND padrao = 1 AND ativa = 1
                        LIMIT 1
                    """, (empresa_id,))

                    if jornada_empresa:
                        data['horario_trabalho_id'] = jornada_empresa[0]['id']  # ✅ CORREÇÃO: Campo correto
                        data['usa_horario_empresa'] = True
                        logger.info(f"✅ [NOVO CADASTRO] Jornada da empresa aplicada: ID {jornada_empresa[0]['id']}")
                    else:
                        # Empresa sem jornada - usar fallback
                        data['horario_trabalho_id'] = None  # ✅ CORREÇÃO: Permitir NULL
                        data['usa_horario_empresa'] = False
                        logger.warning(f"⚠️ [NOVO CADASTRO] Empresa {empresa_id} sem jornada - usando fallback")
                except Exception as db_error:
                    logger.error(f"❌ [ERRO DB] Erro ao buscar jornada da empresa {empresa_id}: {db_error}")
                    data['horario_trabalho_id'] = None
                    data['usa_horario_empresa'] = False
            else:
                # Sem empresa - usar fallback
                data['horario_trabalho_id'] = None  # ✅ CORREÇÃO: Permitir NULL
                data['usa_horario_empresa'] = False
                logger.warning("⚠️ [NOVO CADASTRO] Sem empresa - usando jornada fallback")
        else:
            # ✏️ EDIÇÃO: Preservar apenas referência à jornada existente
            logger.info(f"🔄 [EDIÇÃO] Processando edição do funcionário {funcionario_id}")
            try:
                # ✅ CORREÇÃO: Usar get_with_epis() para dados completos de jornada
                funcionario_atual = FuncionarioQueries.get_with_epis(funcionario_id)
                if funcionario_atual and funcionario_atual.get('horario_trabalho_id'):
                    data['horario_trabalho_id'] = funcionario_atual['horario_trabalho_id']  # ✅ CORREÇÃO: Campo correto
                    data['usa_horario_empresa'] = funcionario_atual.get('usa_horario_empresa', True)
                    logger.info(f"🔒 [EDIÇÃO] Jornada preservada: ID {data['horario_trabalho_id']}")
                else:
                    logger.warning(f"⚠️ [EDIÇÃO] Funcionário {funcionario_id} sem jornada - usando fallback")
                    data['horario_trabalho_id'] = None  # ✅ CORREÇÃO: Permitir NULL
                    data['usa_horario_empresa'] = False
            except Exception as edit_error:
                logger.error(f"❌ [ERRO EDIÇÃO] Erro ao buscar dados do funcionário {funcionario_id}: {edit_error}")
                data['horario_trabalho_id'] = None
                data['usa_horario_empresa'] = False

        # 🛡️ PRESERVAÇÃO CRÍTICA DE BIOMETRIA - LÓGICA CORRIGIDA
        digital_dedo1_final = None
        digital_dedo2_final = None

        nova_biometria_dedo1 = data.get('digital_dedo1', '').strip()
        nova_biometria_dedo2 = data.get('digital_dedo2', '').strip()

        if funcionario_id:
            # É EDIÇÃO - buscar biometria atual primeiro para preservar
            try:
                funcionario_atual = FuncionarioQueries.get_by_id(funcionario_id)
                if funcionario_atual:
                    digital_dedo1_existente = funcionario_atual.get('digital_dedo1')
                    digital_dedo2_existente = funcionario_atual.get('digital_dedo2')

                    logger.info(f"[BIOMETRIA] Edição - Dados existentes - Dedo1: {'✅' if digital_dedo1_existente else '❌'}, Dedo2: {'✅' if digital_dedo2_existente else '❌'}")

                    # DEDO 1: Se há NOVA CAPTURA (JSON), processar; senão, preservar existente
                    if eh_dado_biometrico_novo(nova_biometria_dedo1):
                        # Nova captura detectada - processar JSON
                        biometria_data = parse_biometria_data(nova_biometria_dedo1)
                        if biometria_data and biometria_data.get('template'):
                            digital_dedo1_final = biometria_data.get('template')
                            logger.info(f"[BIOMETRIA] NOVA captura Dedo1 processada ✅")
                        else:
                            # Nova captura inválida - preservar existente
                            digital_dedo1_final = digital_dedo1_existente
                        logger.warning(f"[BIOMETRIA] Nova captura Dedo1 inválida - preservando existente")
                else:
                    # Não é nova captura (dados existentes ou vazio) - preservar existente
                    digital_dedo1_final = digital_dedo1_existente
                    logger.info(f"[BIOMETRIA] Dedo1 - Preservando dados existentes")
                
                # DEDO 2: Mesma lógica
                if eh_dado_biometrico_novo(nova_biometria_dedo2):
                    # Nova captura detectada - processar JSON
                    biometria_data = parse_biometria_data(nova_biometria_dedo2)
                    if biometria_data and biometria_data.get('template'):
                        digital_dedo2_final = biometria_data.get('template')
                        logger.info(f"[BIOMETRIA] NOVA captura Dedo2 processada ✅")
                    else:
                        # Nova captura inválida - preservar existente
                        digital_dedo2_final = digital_dedo2_existente
                        logger.warning(f"[BIOMETRIA] Nova captura Dedo2 inválida - preservando existente")
                else:
                    # Não é nova captura (dados existentes ou vazio) - preservar existente
                    digital_dedo2_final = digital_dedo2_existente
                    logger.info(f"[BIOMETRIA] Dedo2 - Preservando dados existentes")

            except Exception as e:
                logger.error(f"[BIOMETRIA] Erro ao buscar biometria existente: {e}")
                # Em caso de erro, tentar processar nova biometria se houver
                if eh_dado_biometrico_novo(nova_biometria_dedo1):
                    biometria_data = parse_biometria_data(nova_biometria_dedo1)
                    if biometria_data:
                        digital_dedo1_final = biometria_data.get('template')

                if eh_dado_biometrico_novo(nova_biometria_dedo2):
                    biometria_data = parse_biometria_data(nova_biometria_dedo2)
                    if biometria_data:
                        digital_dedo2_final = biometria_data.get('template')
        else:
            # É CADASTRO NOVO - processar apenas se houver dados JSON válidos
            if eh_dado_biometrico_novo(nova_biometria_dedo1):
                biometria_data = parse_biometria_data(nova_biometria_dedo1)
                if biometria_data:
                    digital_dedo1_final = biometria_data.get('template')
                    logger.info(f"[BIOMETRIA] Novo cadastro - Dedo1 processado ✅")

            if eh_dado_biometrico_novo(nova_biometria_dedo2):
                biometria_data = parse_biometria_data(nova_biometria_dedo2)
                if biometria_data:
                    digital_dedo2_final = biometria_data.get('template')
                    logger.info(f"[BIOMETRIA] Novo cadastro - Dedo2 processado ✅")

        # 🔧 Processa foto preservando a existente em edições
        foto_3x4_result = None

        # Se é edição E não há nova foto, preservar foto existente
        if funcionario_id and not (data.get('foto_3x4') and hasattr(data['foto_3x4'], 'filename') and data['foto_3x4'].filename):
            try:
                funcionario_atual = FuncionarioQueries.get_by_id(funcionario_id)
                if funcionario_atual and funcionario_atual.get('foto_3x4'):
                    foto_3x4_result = funcionario_atual['foto_3x4']
                    logger.info(f"[FOTO] Preservando foto existente")
            except Exception as e:
                logger.error(f"[FOTO] Erro ao buscar foto existente: {e}")

        # Se há nova foto sendo enviada, processa upload
        elif data.get('foto_3x4') and hasattr(data['foto_3x4'], 'filename') and data['foto_3x4'].filename:
            logger.info(f"[FOTO] Nova foto detectada: {data['foto_3x4'].filename}")
            file = data['foto_3x4']

            if funcionario_id:
                fotos_dir = get_fotos_dir()
                os.makedirs(fotos_dir, exist_ok=True)
                filename = gerar_nome_foto_funcionario(funcionario_id, file.filename)

                if filename:
                    file_path = os.path.join(fotos_dir, filename)
                    try:
                        file.save(file_path)
                        foto_3x4_result = f'fotos_funcionarios/{filename}'
                        logger.info(f"[FOTO] Foto de edição salva: {foto_3x4_result}")
                    except Exception as ex:
                        logger.error(f"[FOTO] Erro ao salvar foto de edição: {ex}")

        # 🛡️ LOG FINAL DE SEGURANÇA
        logger.info(f"[BIOMETRIA] Resultado final - Dedo1: {'✅' if digital_dedo1_final else '❌'}, Dedo2: {'✅' if digital_dedo2_final else '❌'}")
        logger.info(f"[FOTO] Resultado final: {foto_3x4_result or 'NENHUMA'}")

        # Mapeia campos para compatibilidade com banco
        resultado = {
            **data,
            'digital_dedo1': digital_dedo1_final,  # 🛡️ PROTEGIDO contra perda
            'digital_dedo2': digital_dedo2_final,  # 🛡️ PROTEGIDO contra perda
            'foto_3x4': foto_3x4_result,
            'endereco_rua': data.get('endereco_rua') or None,
            'endereco_bairro': data.get('endereco_bairro') or None,
            'endereco_cidade': data.get('endereco_cidade') or None,
            'telefone2': data.get('telefone2') or None,
            'email': data.get('email') or None
        }

        logger.info(f"✅ [PROCESSAMENTO] Dados processados com sucesso - {len(resultado)} campos")
        return resultado

    except Exception as e:
        logger.error(f"❌ [PROCESSAMENTO] Erro crítico no processamento de dados: {e}")
        logger.error(f"❌ [PROCESSAMENTO] Tipo do erro: {type(e).__name__}")
        import traceback
        logger.error(f"❌ [PROCESSAMENTO] Traceback: {traceback.format_exc()}")
        raise e  # Re-raise para que seja capturado pela função chamadora

def eh_dado_biometrico_novo(biometria_data):
    """
    Verifica se o dado é uma nova captura biométrica (JSON) ou dado existente (template binário).
    """
    if not biometria_data or not biometria_data.strip():
        return False

    try:
        parsed = json.loads(biometria_data)
        if isinstance(parsed, dict) and 'template' in parsed:
            return True
    except (json.JSONDecodeError, TypeError):
        pass

    return False

def _criar_funcionario(data):
    """
    Cria novo funcionário no banco de dados.
    
    Args:
        data (dict): Dados processados do funcionário
        
    Returns:
        int: ID do funcionário criado ou None em caso de erro
    """
    try:
        # ✅ CORREÇÃO CRÍTICA: Query corrigida com campos e parâmetros alinhados
        sql = """
        INSERT INTO funcionarios (
            nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
            ctps_numero, ctps_serie_uf, pis_pasep,
            endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
            telefone1, telefone2, email,
            cargo, setor_obra, matricula_empresa, data_admissao, tipo_contrato,
            nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, status_cadastro,
            horas_trabalho_obrigatorias, usa_horario_empresa, horario_trabalho_id, empresa_id,
            digital_dedo1, digital_dedo2, foto_3x4
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s
        )
        """

        # ✅ CORREÇÃO CRÍTICA: Parâmetros alinhados com campos da query
        params = (
            data['nome_completo'], data['cpf'], data['rg'], data['data_nascimento'],
            data['sexo'], data['estado_civil'], data['nacionalidade'],
            data['ctps_numero'], data['ctps_serie_uf'], data['pis_pasep'],
            data['endereco_rua'], data['endereco_bairro'], data['endereco_cidade'],
            data['endereco_cep'], data['endereco_estado'],
            data['telefone1'], data['telefone2'], data['email'],
            data['cargo'], data['setor_obra'], data['matricula_empresa'],
            data['data_admissao'], data['tipo_contrato'],
            data['nivel_acesso'], data['turno'], data['tolerancia_ponto'],
            data['banco_horas'], data['hora_extra'], data['status_cadastro'],
            data.get('horas_trabalho_obrigatorias', 8.00),  # Padrão 8 horas
            data.get('usa_horario_empresa', True),  # Usar horário da empresa por padrão
            data.get('horario_trabalho_id'),  # ✅ CORREÇÃO: Pode ser None
            data['empresa_id'],  # ✅ NOVO: Empresa selecionada no formulário
            data.get('digital_dedo1'), data.get('digital_dedo2'), data.get('foto_3x4')
        )

        # 🔍 DEBUG: Log dos parâmetros para diagnóstico
        logger.info(f"🔍 [DEBUG] Criando funcionário com {len(params)} parâmetros")
        logger.info(f"🔍 [DEBUG] Empresa ID: {data['empresa_id']}")
        logger.info(f"🔍 [DEBUG] Horário trabalho ID: {data.get('horario_trabalho_id')}")
        
        try:
            funcionario_id = DatabaseManager.execute_query(sql, params, fetch_all=False)
            logger.info(f"✅ [CRIAÇÃO] Funcionário criado com ID: {funcionario_id}")
        except Exception as sql_error:
            logger.error(f"❌ [CRIAÇÃO] ERRO SQL ao criar funcionário: {sql_error}")
            logger.error(f"❌ [CRIAÇÃO] SQL: {sql}")
            logger.error(f"❌ [CRIAÇÃO] Parâmetros ({len(params)}): {params}")
            raise sql_error

        # 🦺 Processar EPIs após criação do funcionário
        if funcionario_id and data.get('epis'):
            logger.info(f"[CRIAÇÃO] Processando {len(data['epis'])} EPIs para funcionário {funcionario_id}")
            try:
                _processar_epis_funcionario(funcionario_id, data['epis'])
                logger.info(f"[CRIAÇÃO] EPIs processados com sucesso para funcionário {funcionario_id}")
            except Exception as e:
                logger.error(f"[CRIAÇÃO] ERRO ao processar EPIs para funcionário {funcionario_id}: {e}")
                # Não falhar o cadastro por causa dos EPIs, apenas logar o erro
        
        # 🔧 CORREÇÃO: Verificar se foto foi processada corretamente
        logger.info(f"[CRIAÇÃO] Funcionário {funcionario_id} criado por {get_current_user()['usuario']}")
        logger.info(f"[CRIAÇÃO] Foto salva: {data.get('foto_3x4', 'NENHUMA')}")
        
        # 🔧 CORREÇÃO: Se há arquivo de foto no formulário mas não foi processado
        # isso acontece quando o ID não estava disponível no momento do processamento
        if funcionario_id and not data.get('foto_3x4'):
            # Buscar arquivo original do formulário
            foto_file = None
            # Recuperar da request atual se ainda disponível
            if hasattr(request, 'files') and request.files.get('foto_3x4'):
                foto_file = request.files.get('foto_3x4')
                if foto_file and foto_file.filename:
                    logger.info(f"[CRIAÇÃO] Processando foto não salva anteriormente: {foto_file.filename}")
                    
                    fotos_dir = get_fotos_dir()
                    os.makedirs(fotos_dir, exist_ok=True)
                    filename = gerar_nome_foto_funcionario(funcionario_id, foto_file.filename)
                    
                    if filename:
                        file_path = os.path.join(fotos_dir, filename)
                        try:
                            foto_file.save(file_path)
                            logger.info(f"[CRIAÇÃO] Foto salva com sucesso: {file_path}")
                            
                            # Atualizar campo foto_3x4 no banco
                            foto_path = f'fotos_funcionarios/{filename}'
                            update_sql = "UPDATE funcionarios SET foto_3x4=%s WHERE id=%s"
                            DatabaseManager.execute_query(update_sql, (foto_path, funcionario_id), fetch_all=False)
                            
                            logger.info(f"[CRIAÇÃO] Campo foto_3x4 atualizado no banco: {foto_path}")
                            
                        except Exception as ex:
                            logger.error(f"[CRIAÇÃO] Erro ao salvar foto pós-criação: {ex}")
                    else:
                        logger.error('[CRIAÇÃO] Nome do arquivo de foto está vazio! Upload abortado.')
        
        return funcionario_id
        
    except Exception as e:
        logger.error(f"Erro ao criar funcionário: {e}")
        return None

def _atualizar_funcionario(funcionario_id, data):
    """
    Atualiza funcionário existente.
    
    Args:
        funcionario_id (int): ID do funcionário
        data (dict): Dados processados
        
    Returns:
        bool: True se sucesso, False caso contrário
    """
    try:
        # 🚨 CORREÇÃO CRÍTICA: Query de atualização SEM campos de jornada individual
        # Os campos de jornada individual são gerenciados pela tabela jornadas_trabalho
        # e não devem ser atualizados diretamente durante edição de funcionário
        sql = """
        UPDATE funcionarios SET
            nome_completo=%s, cpf=%s, rg=%s, data_nascimento=%s, sexo=%s, estado_civil=%s, nacionalidade=%s,
            ctps_numero=%s, ctps_serie_uf=%s, pis_pasep=%s,
            endereco_rua=%s, endereco_bairro=%s, endereco_cidade=%s, endereco_cep=%s, endereco_estado=%s,
            telefone1=%s, telefone2=%s, email=%s,
            cargo=%s, setor_obra=%s, matricula_empresa=%s, data_admissao=%s, tipo_contrato=%s,
            nivel_acesso=%s, turno=%s, tolerancia_ponto=%s, banco_horas=%s, hora_extra=%s, status_cadastro=%s,
            empresa_id=%s, digital_dedo1=%s, digital_dedo2=%s, foto_3x4=%s
        WHERE id=%s
        """

        params = (
            data['nome_completo'], data['cpf'], data['rg'], data['data_nascimento'],
            data['sexo'], data['estado_civil'], data['nacionalidade'],
            data['ctps_numero'], data['ctps_serie_uf'], data['pis_pasep'],
            data['endereco_rua'], data['endereco_bairro'], data['endereco_cidade'],
            data['endereco_cep'], data['endereco_estado'],
            data['telefone1'], data['telefone2'], data['email'],
            data['cargo'], data['setor_obra'], data['matricula_empresa'],
            data['data_admissao'], data['tipo_contrato'],
            data['nivel_acesso'], data['turno'], data['tolerancia_ponto'],
            data['banco_horas'], data['hora_extra'], data['status_cadastro'],
            data['empresa_id'], data['digital_dedo1'], data['digital_dedo2'], data['foto_3x4'],
            funcionario_id
        )

        logger.info(f"🔒 [EDIÇÃO] Atualizando funcionário {funcionario_id} SEM modificar campos de jornada individual")
        
        DatabaseManager.execute_query(sql, params, fetch_all=False)

        # 🚨 CORREÇÃO CRÍTICA: Atualizar horario_trabalho_id separadamente se necessário
        if data.get('horario_trabalho_id'):
            sql_jornada = "UPDATE funcionarios SET horario_trabalho_id = %s WHERE id = %s"
            DatabaseManager.execute_query(sql_jornada, (data['horario_trabalho_id'], funcionario_id), fetch_all=False)
            logger.info(f"🔒 [EDIÇÃO] horario_trabalho_id atualizado para: {data['horario_trabalho_id']}")

        # 🦺 Processar EPIs após atualização do funcionário
        if data.get('epis') is not None:  # Usar 'is not None' para permitir lista vazia
            logger.info(f"[EDIÇÃO] Processando {len(data['epis'])} EPIs para funcionário {funcionario_id}")
            try:
                _processar_epis_funcionario(funcionario_id, data['epis'])
                logger.info(f"[EDIÇÃO] EPIs processados com sucesso para funcionário {funcionario_id}")
            except Exception as e:
                logger.error(f"[EDIÇÃO] ERRO ao processar EPIs para funcionário {funcionario_id}: {e}")
                # Não falhar a edição por causa dos EPIs, apenas logar o erro
        
        logger.info(f"Funcionário {funcionario_id} atualizado por {get_current_user()['usuario']}")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao atualizar funcionário {funcionario_id}: {e}")
        return False

# Registro de filtros personalizados para templates
@funcionarios_bp.app_template_filter('format_cpf')
def format_cpf_filter(cpf):
    return format_cpf(cpf)

@funcionarios_bp.app_template_filter('format_telefone')
def format_telefone_filter(telefone):
    return format_telefone(telefone)

@funcionarios_bp.app_template_filter('format_date')
def format_date_filter(date_obj):
    return format_date(date_obj)

# 🦺 Funções de Integração EPI com Funcionários

def _processar_epis_funcionario(funcionario_id, epis_data):
    """
    Processa EPIs do funcionário (criação/edição).
    
    Args:
        funcionario_id (int): ID do funcionário
        epis_data (list): Lista de EPIs do formulário
    """
    try:
        if not epis_data:
            logger.info(f"[EPIs] Nenhum EPI para processar - Funcionário {funcionario_id}")
            return
        
        logger.info(f"[EPIs] Processando {len(epis_data)} EPIs para funcionário {funcionario_id}")

        # Debug: Mostrar dados recebidos
        for i, epi in enumerate(epis_data):
            logger.debug(f"[EPIs] EPI {i}: {epi}")

        # Buscar EPIs existentes para comparação (em modo edição)
        epis_existentes = DatabaseManager.execute_query(
            "SELECT id FROM epis WHERE funcionario_id = %s",
            (funcionario_id,)
        )
        ids_existentes = {str(epi['id']) for epi in epis_existentes}
        ids_mantidos = set()

        logger.info(f"[EPIs] EPIs existentes no banco: {len(ids_existentes)} - IDs: {ids_existentes}")

        for i, epi in enumerate(epis_data):
            epi_id = epi.get('id', '').strip()
            epi_nome = epi.get('epi_nome', '').strip()

            logger.info(f"[EPIs] Processando EPI {i+1}: Nome='{epi_nome}', ID='{epi_id}'")

            if epi_id and epi_id in ids_existentes:
                # Atualizar EPI existente
                logger.info(f"[EPIs] Atualizando EPI existente {epi_id}")
                _atualizar_epi(epi_id, epi)
                ids_mantidos.add(epi_id)
                logger.info(f"[EPIs] ✅ Atualizado EPI {epi_id}: {epi_nome}")
            else:
                # Criar novo EPI
                logger.info(f"[EPIs] Criando novo EPI: {epi_nome}")
                novo_id = _criar_epi(funcionario_id, epi)
                logger.info(f"[EPIs] ✅ Criado novo EPI {novo_id}: {epi_nome}")
        
        # Remover EPIs que não estão mais no formulário (foram removidos)
        ids_para_remover = ids_existentes - ids_mantidos
        logger.info(f"[EPIs] EPIs para remover: {len(ids_para_remover)} - IDs: {ids_para_remover}")

        for epi_id in ids_para_remover:
            logger.info(f"[EPIs] Removendo EPI {epi_id}")
            _remover_epi(epi_id)
            logger.info(f"[EPIs] ✅ Removido EPI {epi_id}")

        logger.info(f"[EPIs] ✅ Processamento concluído - Funcionário {funcionario_id}")

    except Exception as e:
        logger.error(f"[EPIs] ❌ Erro ao processar EPIs do funcionário {funcionario_id}: {e}")
        import traceback
        logger.error(f"[EPIs] Traceback: {traceback.format_exc()}")
        raise

def _criar_epi(funcionario_id, epi_data):
    """
    Cria novo EPI no banco.
    
    Args:
        funcionario_id (int): ID do funcionário
        epi_data (dict): Dados do EPI
        
    Returns:
        int: ID do EPI criado
    """
    sql = """
    INSERT INTO epis (funcionario_id, epi_nome, epi_ca, epi_data_entrega, epi_data_validade, epi_observacoes)
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    
    params = (
        funcionario_id,
        epi_data.get('epi_nome'),
        epi_data.get('epi_ca') or None,
        epi_data.get('epi_data_entrega') or None,
        epi_data.get('epi_data_validade') or None,
        epi_data.get('epi_observacoes') or None
    )
    
    return DatabaseManager.execute_query(sql, params, fetch_all=False)

def _atualizar_epi(epi_id, epi_data):
    """
    Atualiza EPI existente.
    
    Args:
        epi_id (str): ID do EPI
        epi_data (dict): Novos dados do EPI
    """
    sql = """
    UPDATE epis 
    SET epi_nome = %s, epi_ca = %s, epi_data_entrega = %s, 
        epi_data_validade = %s, epi_observacoes = %s
    WHERE id = %s
    """
    
    params = (
        epi_data.get('epi_nome'),
        epi_data.get('epi_ca') or None,
        epi_data.get('epi_data_entrega') or None,
        epi_data.get('epi_data_validade') or None,
        epi_data.get('epi_observacoes') or None,
        int(epi_id)
    )
    
    DatabaseManager.execute_query(sql, params, fetch_all=False)

def _remover_epi(epi_id):
    """
    Remove EPI do banco.
    
    Args:
        epi_id (str): ID do EPI
    """
    DatabaseManager.execute_query(
        "DELETE FROM epis WHERE id = %s",
        (int(epi_id),),
        fetch_all=False
    )

def _buscar_epis_funcionario(funcionario_id):
    """
    Busca EPIs de um funcionário.
    
    Args:
        funcionario_id (int): ID do funcionário
        
    Returns:
        list: Lista de EPIs
    """
    try:
        epis = DatabaseManager.execute_query(
            "SELECT * FROM epis WHERE funcionario_id = %s ORDER BY epi_data_entrega DESC",
            (funcionario_id,)
        )
        return epis or []
    except Exception as e:
        logger.error(f"Erro ao buscar EPIs do funcionário {funcionario_id}: {e}")
        return [] 