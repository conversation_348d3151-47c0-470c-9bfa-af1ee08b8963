{% extends "base.html" %}

{% block title %}Nova Jornada - {{ empresa.razao_social }}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 900px;
        margin: 0 auto;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 30px;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
    }
    
    .page-header h1 {
        margin: 0 0 10px 0;
        font-size: 2em;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: #f8f9fa;
    }
    
    .section-title {
        margin: 0 0 20px 0;
        color: #2c3e50;
        font-size: 1.2em;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
    
    .form-grid-3 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group.full-width {
        grid-column: 1 / -1;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #2c3e50;
    }
    
    .form-group.required label::after {
        content: " *";
        color: #e74c3c;
    }
    
    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s ease;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }
    
    .horario-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .horario-group input {
        flex: 1;
    }
    
    .horario-separator {
        color: #7f8c8d;
        font-weight: bold;
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 10px;
    }
    
    .checkbox-group input[type="checkbox"] {
        width: auto;
        margin: 0;
    }
    
    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }
    
    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-primary {
        background: #3498db;
        color: white;
    }
    
    .btn-primary:hover {
        background: #2980b9;
    }
    
    .btn-secondary {
        background: #95a5a6;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #7f8c8d;
        color: white;
    }
    
    .alert {
        padding: 12px 15px;
        border-radius: 6px;
        margin-bottom: 20px;
    }
    
    .alert-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
    
    .form-text {
        font-size: 0.85em;
        color: #6c757d;
        margin-top: 5px;
    }
    
    .exemplo-jornada {
        background: #e8f4fd;
        border: 1px solid #bee5eb;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .exemplo-jornada h4 {
        margin: 0 0 10px 0;
        color: #0c5460;
    }
    
    .exemplo-item {
        margin-bottom: 5px;
        color: #0c5460;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-clock"></i> Nova Jornada de Trabalho</h1>
    <p>Empresa: <strong>{{ empresa.razao_social }}</strong></p>
</div>

<div class="form-container">
    <div class="exemplo-jornada">
        <h4><i class="fas fa-lightbulb"></i> Exemplos de Jornadas</h4>
        <div class="exemplo-item"><strong>Diurno 01 - Profissionais:</strong> Segunda a Quinta: 07:00-17:00 | Sexta: 07:00-16:00</div>
        <div class="exemplo-item"><strong>Diurno 02 - Serventes:</strong> Segunda a Sábado: 07:00-15:20</div>
        <div class="exemplo-item"><strong>Noturno - Serventes:</strong> Segunda a Sábado: 19:00-05:20</div>
    </div>
    
    <form method="POST">
        <!-- Identificação -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-tag"></i> Identificação da Jornada
            </h3>
            
            <div class="form-grid">
                <div class="form-group required">
                    <label for="nome_jornada">Nome da Jornada</label>
                    <input type="text" 
                           id="nome_jornada" 
                           name="nome_jornada" 
                           class="form-control"
                           value="{{ dados.nome_jornada if dados else '' }}"
                           required
                           placeholder="Ex: Diurno 01 - Profissionais">
                    <div class="form-text">Nome identificador da jornada</div>
                </div>
                
                <div class="form-group required">
                    <label for="tipo_jornada">Tipo de Jornada</label>
                    <select id="tipo_jornada" name="tipo_jornada" class="form-control" required>
                        <option value="Diurno" {{ 'selected' if dados and dados.tipo_jornada == 'Diurno' else '' }}>Diurno</option>
                        <option value="Noturno" {{ 'selected' if dados and dados.tipo_jornada == 'Noturno' else '' }}>Noturno</option>
                        <option value="Misto" {{ 'selected' if dados and dados.tipo_jornada == 'Misto' else '' }}>Misto</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="categoria_funcionario">Categoria de Funcionário</label>
                <input type="text" 
                       id="categoria_funcionario" 
                       name="categoria_funcionario" 
                       class="form-control"
                       value="{{ dados.categoria_funcionario if dados else '' }}"
                       placeholder="Ex: Profissionais, Serventes, Administrativo">
                <div class="form-text">Tipo de funcionário que usa esta jornada</div>
            </div>
        </div>
        
        <!-- Horários Principais -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-clock"></i> Horários de Trabalho
            </h3>
            
            <div class="form-grid">
                <div class="form-group required">
                    <label>Segunda a Quinta-feira</label>
                    <div class="horario-group">
                        <input type="time" 
                               name="seg_qui_entrada" 
                               class="form-control"
                               value="{{ dados.seg_qui_entrada if dados else '07:00' }}"
                               required>
                        <span class="horario-separator">às</span>
                        <input type="time" 
                               name="seg_qui_saida" 
                               class="form-control"
                               value="{{ dados.seg_qui_saida if dados else '17:00' }}"
                               required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Sexta-feira (se diferente)</label>
                    <div class="horario-group">
                        <input type="time" 
                               name="sexta_entrada" 
                               class="form-control"
                               value="{{ dados.sexta_entrada if dados else '' }}">
                        <span class="horario-separator">às</span>
                        <input type="time" 
                               name="sexta_saida" 
                               class="form-control"
                               value="{{ dados.sexta_saida if dados else '' }}">
                    </div>
                    <div class="form-text">Deixe em branco para usar o mesmo horário de segunda a quinta</div>
                </div>
            </div>
            
            <div class="form-group">
                <label>Sábado (opcional)</label>
                <div class="horario-group">
                    <input type="time" 
                           name="sabado_entrada" 
                           class="form-control"
                           value="{{ dados.sabado_entrada if dados else '' }}">
                    <span class="horario-separator">às</span>
                    <input type="time" 
                           name="sabado_saida" 
                           class="form-control"
                           value="{{ dados.sabado_saida if dados else '' }}">
                </div>
                <div class="form-text">Preencha apenas se a jornada inclui sábado</div>
            </div>
        </div>
        
        <!-- Intervalo -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-coffee"></i> Intervalo/Almoço
            </h3>
            
            <div class="form-grid">
                <div class="form-group">
                    <label>Início do Intervalo</label>
                    <input type="time" 
                           name="intervalo_inicio" 
                           class="form-control"
                           value="{{ dados.intervalo_inicio if dados else '11:45' }}">
                </div>
                
                <div class="form-group">
                    <label>Fim do Intervalo</label>
                    <input type="time" 
                           name="intervalo_fim" 
                           class="form-control"
                           value="{{ dados.intervalo_fim if dados else '13:00' }}">
                </div>
            </div>
        </div>
        
        <!-- Configurações -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-cog"></i> Configurações
            </h3>
            
            <div class="form-group">
                <label for="tolerancia_entrada_minutos">Tolerância para Entrada (minutos)</label>
                <input type="number" 
                       id="tolerancia_entrada_minutos" 
                       name="tolerancia_entrada_minutos" 
                       class="form-control"
                       value="{{ dados.tolerancia_entrada_minutos if dados else '15' }}"
                       min="0" 
                       max="60">
                <div class="form-text">Tolerância em minutos para entrada</div>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" 
                       id="ativa" 
                       name="ativa" 
                       {{ 'checked' if not dados or dados.ativa else '' }}>
                <label for="ativa">Jornada ativa</label>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" 
                       id="padrao" 
                       name="padrao" 
                       {{ 'checked' if dados and dados.padrao else '' }}>
                <label for="padrao">Definir como jornada padrão da empresa</label>
            </div>
        </div>
        
        <!-- Ações -->
        <div class="form-actions">
            <a href="{{ url_for('empresas.detalhes', id=empresa.id) }}" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancelar
            </a>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Cadastrar Jornada
            </button>
        </div>
    </form>
</div>
{% endblock %}
