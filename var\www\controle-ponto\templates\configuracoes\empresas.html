{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    .empresas-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .header-card {
        background: linear-gradient(135deg, #6f42c1, #5a32a3);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(111,66,193,0.3);
    }
    
    .header-card h2 {
        margin: 0 0 10px 0;
        font-size: 2rem;
    }
    
    .header-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;
    }
    
    .btn-nova-empresa {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        border-radius: 25px;
        padding: 12px 25px;
        color: white;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-nova-empresa:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40,167,69,0.3);
        color: white;
    }
    
    .empresas-list {
        background: white;
        border-radius: 15px;
        padding: 0;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .empresa-item {
        border-bottom: 1px solid #e9ecef;
        padding: 20px;
        transition: all 0.3s ease;
    }
    
    .empresa-item:last-child {
        border-bottom: none;
    }
    
    .empresa-item:hover {
        background: #f8f9fa;
    }
    
    .empresa-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
        margin-bottom: 10px;
    }
    
    .empresa-info h5 {
        margin: 0 0 5px 0;
        color: #495057;
        font-size: 1.2rem;
    }
    
    .empresa-info .subtitle {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 2px 0;
    }
    
    .empresa-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-ativa {
        background: #d4edda;
        color: #155724;
    }
    
    .status-inativa {
        background: #f8d7da;
        color: #721c24;
    }
    
    .empresa-detalhes {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 15px 0;
    }
    
    .detalhe-item {
        display: flex;
        align-items: center;
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .detalhe-item i {
        width: 20px;
        margin-right: 10px;
        color: #6f42c1;
    }
    
    .empresa-acoes {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-top: 15px;
    }
    
    .btn-acao {
        padding: 6px 12px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        text-decoration: none;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .btn-editar {
        background: #ffc107;
        color: #1a2634;
    }
    
    .btn-editar:hover {
        background: #e0a800;
        color: #1a2634;
    }
    
    .btn-excluir {
        background: #dc3545;
        color: white;
    }
    
    .btn-excluir:hover {
        background: #b52a37;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }
    
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        color: #6f42c1;
        display: block;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    /* Estilo para o spinner de carregamento */
    .spinner-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.3);
        z-index: 9998;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .spinner-border {
        width: 3rem;
        height: 3rem;
        border: 0.25em solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border .75s linear infinite;
    }
    
    @keyframes spinner-border {
        to { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="empresas-container">
    <!-- Header -->
    <div class="header-card">
        <h2><i class="fas fa-building"></i> {{ titulo }}</h2>
        <div class="header-actions">
            <div class="stats-card">
                <span class="stats-number">{{ total_empresas }}</span>
                <div class="stats-label">Empresas Cadastradas</div>
            </div>
            <a href="{{ url_for('configuracoes.nova_empresa') }}" class="btn-nova-empresa">
                <i class="fas fa-plus"></i> Nova Empresa
            </a>
        </div>
    </div>

    <!-- Lista de Empresas -->
    {% if empresas %}
    <div class="empresas-list">
        {% for empresa in empresas %}
        <div class="empresa-item">
            <div class="empresa-header">
                <div class="empresa-info">
                    <h5>{{ empresa.razao_social }}</h5>
                    {% if empresa.nome_fantasia %}
                    <div class="subtitle">{{ empresa.nome_fantasia }}</div>
                    {% endif %}
                    <div class="subtitle">CNPJ: {{ empresa.cnpj }}</div>
                </div>
                <div class="empresa-status {{ 'status-ativa' if empresa.ativa else 'status-inativa' }}">
                    {{ 'Ativa' if empresa.ativa else 'Inativa' }}
                </div>
            </div>
            
            <div class="empresa-detalhes">
                {% if empresa.telefone %}
                <div class="detalhe-item">
                    <i class="fas fa-phone"></i>
                    <span>{{ empresa.telefone }}</span>
                </div>
                {% endif %}
                
                {% if empresa.email %}
                <div class="detalhe-item">
                    <i class="fas fa-envelope"></i>
                    <span>{{ empresa.email }}</span>
                </div>
                {% endif %}
                
                <div class="detalhe-item">
                    <i class="fas fa-users"></i>
                    <span>{{ empresa.total_funcionarios }} funcionário(s)</span>
                </div>
                
                <div class="detalhe-item">
                    <i class="fas fa-calendar"></i>
                    <span>Cadastrada em {{ empresa.data_cadastro }}</span>
                </div>
            </div>
            
            <div class="empresa-acoes">
                <a href="{{ url_for('configuracoes.editar_empresa', empresa_id=empresa.id) }}" 
                   class="btn-acao btn-editar">
                    <i class="fas fa-edit"></i> Editar
                </a>
                
                {% if empresa.total_funcionarios == 0 %}
                <button type="button" 
                        class="btn-acao btn-excluir"
                        data-empresa-id="{{ empresa.id }}"
                        data-empresa-nome="{{ empresa.razao_social }}"
                        onclick="confirmarExclusaoEmpresa({{ empresa.id }}, '{{ empresa.razao_social }}')">
                    <i class="fas fa-trash"></i> Excluir
                </button>
                {% else %}
                <button type="button" 
                        class="btn-acao btn-excluir"
                        style="opacity: 0.5; cursor: not-allowed;"
                        title="Não é possível excluir empresa com funcionários ativos"
                        disabled>
                    <i class="fas fa-trash"></i> Excluir
                </button>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="empresas-list">
        <div class="empty-state">
            <i class="fas fa-building"></i>
            <h5>Nenhuma empresa cadastrada</h5>
            <p>Comece cadastrando a primeira empresa do sistema</p>
            <a href="{{ url_for('configuracoes.nova_empresa') }}" class="btn-nova-empresa">
                <i class="fas fa-plus"></i> Cadastrar Primeira Empresa
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<!-- Incluir script de tratamento de erros -->
<script src="{{ url_for('static', filename='js/configuracoes_error_handler.js') }}"></script>

<script>
// Função de confirmação de exclusão com tratamento de erros aprimorado
function confirmarExclusaoEmpresa(empresaId, empresaNome) {
    const message = `Tem certeza que deseja excluir a empresa "${empresaNome}"? Esta ação não poderá ser desfeita.`;

    if (confirm(message)) {
        // Exibir indicador de carregamento
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'spinner-overlay';
        
        const spinner = document.createElement('div');
        spinner.className = 'spinner-border text-light';
        spinner.setAttribute('role', 'status');
        
        const spinnerText = document.createElement('span');
        spinnerText.className = 'sr-only';
        spinnerText.textContent = 'Carregando...';
        
        spinner.appendChild(spinnerText);
        loadingOverlay.appendChild(spinner);
        document.body.appendChild(loadingOverlay);
        
        // Obter CSRF token se disponível
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const csrfValue = csrfToken ? csrfToken.getAttribute('content') : '';
        
        // Fazer requisição AJAX em vez de submeter formulário
        fetch(`/configuracoes/empresas/${empresaId}/excluir`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfValue
            },
            body: JSON.stringify({ is_ajax: true })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Erro ao excluir empresa');
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Sucesso - recarregar a página ou atualizar a lista
                window.location.reload();
            } else {
                // Erro com mensagem do servidor
                exibirErroAmigavel(data.error || 'Erro ao excluir empresa');
            }
        })
        .catch(error => {
            // Erro de rede ou outro erro
            console.error('Erro ao excluir empresa:', error);
            exibirErroAmigavel(`Erro ao excluir empresa: ${error.message}`);
        })
        .finally(() => {
            // Remover overlay de carregamento
            document.body.removeChild(loadingOverlay);
        });
    }
}
</script>
{% endblock %} 