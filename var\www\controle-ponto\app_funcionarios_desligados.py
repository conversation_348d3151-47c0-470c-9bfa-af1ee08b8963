"""
Blueprint para gerenciamento de funcionários desligados.
Área administrativa profissional para controle de funcionários desligados.

Autor: <PERSON> - Full Stack Developer
Data: 2025-07-13
"""

import logging
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, make_response
from utils.auth import require_admin, get_current_user
from utils.database import DatabaseManager

# Configuração do logger
logger = logging.getLogger('controle-ponto.funcionarios_desligados')

# Criação do Blueprint
funcionarios_desligados_bp = Blueprint('funcionarios_desligados', __name__, url_prefix='/funcionarios-desligados')

@funcionarios_desligados_bp.route('/')
@require_admin
def index():
    """
    Página principal de funcionários desligados com filtros e estatísticas.
    """
    try:
        # Obter filtros da URL
        motivo_filtro = request.args.get('motivo', '')
        periodo_filtro = request.args.get('periodo', '')
        busca_filtro = request.args.get('busca', '')

        # Construir query base
        query = """
        SELECT
            fd.*,
            u.usuario as usuario_responsavel
        FROM funcionarios_desligados fd
        LEFT JOIN usuarios u ON fd.usuario_responsavel_desligamento = u.id
        WHERE 1=1
        """
        params = []

        # Aplicar filtros
        if motivo_filtro:
            query += " AND fd.motivo_desligamento = %s"
            params.append(motivo_filtro)

        if periodo_filtro:
            dias = int(periodo_filtro)
            data_limite = datetime.now() - timedelta(days=dias)
            query += " AND fd.data_desligamento >= %s"
            params.append(data_limite)

        if busca_filtro:
            query += " AND (fd.nome_completo LIKE %s OR fd.matricula_empresa LIKE %s)"
            params.extend([f'%{busca_filtro}%', f'%{busca_filtro}%'])

        query += " ORDER BY fd.data_desligamento DESC"

        # Executar query principal
        funcionarios_desligados = DatabaseManager.execute_query(query, params)

        # Calcular estatísticas
        # Total de desligados
        total_desligados_result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM funcionarios_desligados")
        total_desligados = total_desligados_result[0]['total'] if total_desligados_result else 0

        # Desligados este mês
        primeiro_dia_mes = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        desligados_mes_result = DatabaseManager.execute_query(
            "SELECT COUNT(*) as total FROM funcionarios_desligados WHERE data_desligamento >= %s",
            (primeiro_dia_mes,)
        )
        desligados_mes = desligados_mes_result[0]['total'] if desligados_mes_result else 0

        # Total de demissões
        total_demissoes_result = DatabaseManager.execute_query(
            "SELECT COUNT(*) as total FROM funcionarios_desligados WHERE motivo_desligamento IN ('Demissao_sem_justa_causa', 'Demissao_com_justa_causa')"
        )
        total_demissoes = total_demissoes_result[0]['total'] if total_demissoes_result else 0

        # Total de pedidos de demissão
        total_pedidos_result = DatabaseManager.execute_query(
            "SELECT COUNT(*) as total FROM funcionarios_desligados WHERE motivo_desligamento = 'Pedido_demissao'"
        )
        total_pedidos = total_pedidos_result[0]['total'] if total_pedidos_result else 0

        context = {
            'funcionarios_desligados': funcionarios_desligados or [],
            'total_desligados': total_desligados,
            'desligados_mes': desligados_mes,
            'total_demissoes': total_demissoes,
            'total_pedidos': total_pedidos,
            'filtros': {
                'motivo': motivo_filtro,
                'periodo': periodo_filtro,
                'busca': busca_filtro
            }
        }

        return render_template('funcionarios_desligados/index.html', **context)

    except Exception as e:
        logger.error(f"Erro ao carregar funcionários desligados: {e}")
        flash("Erro ao carregar dados dos funcionários desligados", "error")
        return redirect(url_for('index'))

@funcionarios_desligados_bp.route('/<int:funcionario_id_original>/detalhes')
@require_admin
def detalhes(funcionario_id_original):
    """
    Página de detalhes de um funcionário desligado específico.
    """
    try:
        # Buscar dados do funcionário desligado
        funcionario_result = DatabaseManager.execute_query("""
            SELECT
                fd.*,
                u.usuario as usuario_responsavel
            FROM funcionarios_desligados fd
            LEFT JOIN usuarios u ON fd.usuario_responsavel_desligamento = u.id
            WHERE fd.funcionario_id_original = %s
        """, (funcionario_id_original,))

        if not funcionario_result:
            flash("Funcionário desligado não encontrado", "error")
            return redirect(url_for('funcionarios_desligados.index'))

        funcionario = funcionario_result[0]

        # Buscar estatísticas dos dados relacionados
        total_pontos_result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM registros_ponto WHERE funcionario_id = %s", (funcionario_id_original,))
        total_pontos = total_pontos_result[0]['total'] if total_pontos_result else 0

        total_epis_result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM epis WHERE funcionario_id = %s", (funcionario_id_original,))
        total_epis = total_epis_result[0]['total'] if total_epis_result else 0

        total_banco_horas_result = DatabaseManager.execute_query("SELECT COUNT(*) as total FROM banco_horas WHERE funcionario_id = %s", (funcionario_id_original,))
        total_banco_horas = total_banco_horas_result[0]['total'] if total_banco_horas_result else 0

        # Calcular tempo na empresa
        tempo_empresa = None
        if funcionario['data_admissao'] and funcionario['data_desligamento']:
            delta = funcionario['data_desligamento'].date() - funcionario['data_admissao']
            anos = delta.days // 365
            meses = (delta.days % 365) // 30
            if anos > 0:
                tempo_empresa = f"{anos} ano(s) e {meses} mês(es)"
            else:
                tempo_empresa = f"{meses} mês(es)"

        # Buscar últimos registros de ponto
        registros_ponto = DatabaseManager.execute_query("""
            SELECT * FROM registros_ponto
            WHERE funcionario_id = %s
            ORDER BY data_hora DESC
            LIMIT 10
        """, (funcionario_id_original,))

        context = {
            'funcionario': funcionario,
            'estatisticas': {
                'total_pontos': total_pontos,
                'total_epis': total_epis,
                'total_banco_horas': total_banco_horas,
                'tempo_empresa': tempo_empresa
            },
            'registros_ponto': registros_ponto or []
        }

        return render_template('funcionarios_desligados/detalhes.html', **context)

    except Exception as e:
        logger.error(f"Erro ao carregar detalhes do funcionário desligado {funcionario_id_original}: {e}")
        flash("Erro ao carregar detalhes do funcionário", "error")
        return redirect(url_for('funcionarios_desligados.index'))

@funcionarios_desligados_bp.route('/api/estatisticas')
@require_admin
def api_estatisticas():
    """
    API para obter estatísticas dos funcionários desligados.
    """
    try:
        # Estatísticas por motivo
        por_motivo = DatabaseManager.execute_query("""
            SELECT
                motivo_desligamento,
                COUNT(*) as total
            FROM funcionarios_desligados
            GROUP BY motivo_desligamento
            ORDER BY total DESC
        """)

        # Estatísticas por mês (últimos 12 meses)
        por_mes = DatabaseManager.execute_query("""
            SELECT
                DATE_FORMAT(data_desligamento, '%Y-%m') as mes,
                COUNT(*) as total
            FROM funcionarios_desligados
            WHERE data_desligamento >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY DATE_FORMAT(data_desligamento, '%Y-%m')
            ORDER BY mes
        """)

        return jsonify({
            'success': True,
            'data': {
                'por_motivo': por_motivo or [],
                'por_mes': por_mes or []
            }
        })

    except Exception as e:
        logger.error(f"Erro ao obter estatísticas: {e}")
        return jsonify({
            'success': False,
            'message': 'Erro ao obter estatísticas'
        }), 500

@funcionarios_desligados_bp.route('/exportar')
@require_admin
def exportar():
    """
    Exportar lista de funcionários desligados para CSV.
    """
    try:
        import csv
        from io import StringIO

        # Obter filtros
        motivo_filtro = request.args.get('motivo', '')
        periodo_filtro = request.args.get('periodo', '')
        busca_filtro = request.args.get('busca', '')

        # Construir query (mesma lógica do index)
        query = """
        SELECT
            fd.nome_completo,
            fd.cpf,
            fd.matricula_empresa,
            fd.cargo,
            fd.setor,
            fd.data_admissao,
            fd.data_desligamento,
            fd.motivo_desligamento,
            fd.observacoes_desligamento,
            u.usuario as usuario_responsavel
        FROM funcionarios_desligados fd
        LEFT JOIN usuarios u ON fd.usuario_responsavel_desligamento = u.id
        WHERE 1=1
        """
        params = []

        if motivo_filtro:
            query += " AND fd.motivo_desligamento = %s"
            params.append(motivo_filtro)

        if periodo_filtro:
            dias = int(periodo_filtro)
            data_limite = datetime.now() - timedelta(days=dias)
            query += " AND fd.data_desligamento >= %s"
            params.append(data_limite)

        if busca_filtro:
            query += " AND (fd.nome_completo LIKE %s OR fd.matricula_empresa LIKE %s)"
            params.extend([f'%{busca_filtro}%', f'%{busca_filtro}%'])

        query += " ORDER BY fd.data_desligamento DESC"

        funcionarios = DatabaseManager.execute_query(query, params)

        # Criar CSV
        output = StringIO()
        writer = csv.writer(output)

        # Cabeçalho
        writer.writerow([
            'Nome Completo', 'CPF', 'Matrícula', 'Cargo', 'Setor',
            'Data Admissão', 'Data Desligamento', 'Motivo',
            'Observações', 'Responsável'
        ])

        # Dados
        if funcionarios:
            for funcionario in funcionarios:
                writer.writerow([
                    funcionario['nome_completo'],
                    funcionario['cpf'],
                    funcionario['matricula_empresa'],
                    funcionario['cargo'],
                    funcionario['setor'] or '',
                    funcionario['data_admissao'].strftime('%d/%m/%Y') if funcionario['data_admissao'] else '',
                    funcionario['data_desligamento'].strftime('%d/%m/%Y') if funcionario['data_desligamento'] else '',
                    funcionario['motivo_desligamento'].replace('_', ' ').title(),
                    funcionario['observacoes_desligamento'] or '',
                    funcionario['usuario_responsavel'] or 'Sistema'
                ])

        # Preparar resposta
        output.seek(0)
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=funcionarios_desligados_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

        return response

    except Exception as e:
        logger.error(f"Erro ao exportar funcionários desligados: {e}")
        flash("Erro ao exportar dados", "error")
        return redirect(url_for('funcionarios_desligados.index'))
