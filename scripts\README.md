# 🔧 Scripts de Configuração e Manutenção

## 📋 **Scripts Disponíveis**

### **🗄️ executar_configuracao_mysql.py**
**Função:** Configurar permissões e usuários no MySQL para o sistema de controle de ponto.

**Uso:**
```bash
python executar_configuracao_mysql.py
```

**O que faz:**
- Cria usuário `controle_user` no MySQL
- Configura permissões para IPs específicos e rede local
- Testa conectividade com o banco
- Aplica as configurações automaticamente

**Pré-requisitos:**
- MySQL em execução no servidor ************
- Usuário root com acesso ao MySQL

---

### **🧹 executar_limpeza_banco.py**
**Função:** Limpar e reestruturar o banco de dados, removendo colunas problemáticas.

**Uso:**
```bash
python executar_limpeza_banco.py
```

**O que faz:**
- Remove colunas duplicadas ou problemáticas
- Reestrutura tabelas funcionarios, epis e registros_ponto
- Cria relacionamentos corretos com chaves estrangeiras
- Valida a integridade final do banco

**⚠️ Atenção:** Este script modifica a estrutura do banco permanentemente.

---

### **📊 controle_ponto.sql**
**Função:** Script SQL base com a estrutura completa do banco de dados.

**Uso:**
```sql
mysql -u root -p < controle_ponto.sql
```

**O que contém:**
- Estrutura completa das tabelas
- Relacionamentos e chaves estrangeiras
- Dados de exemplo para testes
- Configurações de charset e collation

---

## 🚀 **Ordem de Execução Recomendada**

Para configurar o sistema do zero:

1. **Primeiro:** `controle_ponto.sql`
   ```bash
   mysql -u root -p < scripts/controle_ponto.sql
   ```

2. **Segundo:** `executar_configuracao_mysql.py`
   ```bash
   python scripts/executar_configuracao_mysql.py
   ```

3. **Se necessário:** `executar_limpeza_banco.py`
   ```bash
   python scripts/executar_limpeza_banco.py
   ```

---

## 🔧 **Configurações Necessárias**

### **Parâmetros de Conexão:**
- **Servidor MySQL:** ************
- **Porta:** 3306
- **Database:** controle_ponto
- **Usuário:** controle_user
- **Senha:** controle123

### **Estrutura Final do Banco:**
- **funcionarios:** 40 colunas (dados completos)
- **epis:** 7 colunas (relacionamento com funcionários)
- **registros_ponto:** 4 colunas (histórico de marcações)

---

## 📝 **Logs e Monitoramento**

Todos os scripts geram logs detalhados de suas operações:
- Conexões realizadas
- Comandos SQL executados
- Erros encontrados
- Status de conclusão

Consulte a pasta `docs/` para documentação adicional sobre troubleshooting.

---

**🎉 Scripts prontos para configuração automática do sistema!** 