#!/usr/bin/env python3
"""
Script para debugar exatamente quais dados estão sendo enviados pelo formulário real.
"""

import sys
import os
import traceback

# Adicionar o diretório do projeto ao path
sys.path.append('/var/www/controle-ponto')

try:
    from app_funcionarios import _extrair_dados_formulario, _validar_dados_funcionario
    from utils.helpers import FormValidator
    from flask import Flask, request
    
    print("✅ Imports realizados com sucesso")
    
    # Criar uma aplicação Flask de teste
    app = Flask(__name__)
    
    def interceptar_dados_formulario():
        """
        Intercepta e analisa todos os dados que estão sendo enviados pelo formulário.
        """
        print("\n🔍 INTERCEPTANDO DADOS DO FORMULÁRIO REAL")
        print("=" * 60)
        
        # Adicionar um interceptador temporário na função _extrair_dados_formulario
        # para capturar exatamente o que está sendo enviado
        
        # Simular dados que podem estar sendo enviados pelo template
        form_data_simulado = {
            'nome_completo': 'RICHARDSON CARDOSO RODRIGUES',
            'cpf': '711.256.042-04',
            'empresa_id': '11',
            # Possíveis campos problemáticos que podem estar sendo enviados
            'epis[0][id]': '',  # Campo vazio que pode estar causando problema
            'epis[0][epi_nome]': '',
            'epis[1][id]': '',  # Outro campo vazio
            'epis[1][epi_nome]': '',
            # Ou até mesmo um campo 'id' direto
            'id': '',  # ❌ Este pode ser o problema!
        }
        
        print("📋 Dados simulados que podem estar sendo enviados:")
        for key, value in form_data_simulado.items():
            print(f"  {key}: '{value}'")
        
        with app.test_request_context('/', method='POST', data=form_data_simulado):
            try:
                # Primeiro, vamos ver o que está no request.form
                print(f"\n📋 request.form.keys(): {list(request.form.keys())}")
                
                # Verificar se há campos 'id' diretos
                campos_id = [k for k in request.form.keys() if 'id' in k.lower()]
                print(f"📋 Campos com 'id': {campos_id}")
                
                # Extrair dados
                dados_extraidos = _extrair_dados_formulario()
                
                print(f"\n📊 Dados extraídos: {len(dados_extraidos)} campos")
                
                # Verificar especificamente o campo 'id'
                if 'id' in dados_extraidos:
                    print(f"❌ PROBLEMA CONFIRMADO: Campo 'id' = '{dados_extraidos['id']}'")
                    print(f"   Tipo: {type(dados_extraidos['id'])}")
                    print(f"   Vazio: {not dados_extraidos['id']}")
                else:
                    print("✅ Campo 'id' NÃO está nos dados extraídos")
                
                # Verificar EPIs
                epis = dados_extraidos.get('epis', [])
                print(f"\n🦺 EPIs extraídos: {len(epis)}")
                for i, epi in enumerate(epis):
                    print(f"  EPI {i}: {epi}")
                    if 'id' in epi and not epi['id']:
                        print(f"    ⚠️ EPI {i} tem campo 'id' vazio: '{epi['id']}'")
                
                return dados_extraidos
                
            except Exception as e:
                print(f"❌ ERRO: {e}")
                print(f"Traceback: {traceback.format_exc()}")
                return None
    
    def testar_validacao_com_id_vazio():
        """
        Testa validação com campo 'id' vazio que pode estar causando o problema.
        """
        print("\n🔍 TESTANDO VALIDAÇÃO COM CAMPO 'id' VAZIO")
        print("=" * 60)
        
        # Dados que incluem um campo 'id' vazio (que pode estar vindo do template)
        dados_com_id_vazio = {
            'nome_completo': 'João da Silva',
            'cpf': '123.456.789-00',
            'empresa_id': '11',
            'id': '',  # ❌ Campo 'id' vazio que pode estar causando problema
            'epis': []
        }
        
        print("📋 Dados com campo 'id' vazio:")
        for key, value in dados_com_id_vazio.items():
            print(f"  {key}: '{value}'")
        
        validator = FormValidator()
        
        try:
            _validar_dados_funcionario(dados_com_id_vazio, validator)
            
            if validator.has_errors():
                print("❌ ERROS ENCONTRADOS:")
                errors_dict = validator.get_errors()
                
                for field, field_errors in errors_dict.items():
                    print(f"  {field}: {field_errors}")
                    
                    if field == 'id':
                        print(f"    🎯 ERRO 'id' CONFIRMADO!")
                        print(f"    Valor: '{dados_com_id_vazio.get('id', 'NÃO ENCONTRADO')}'")
                        print(f"    Tipo: {type(dados_com_id_vazio.get('id', None))}")
                        return True  # Problema reproduzido
            else:
                print("✅ Validação passou sem erros")
                return False
                
        except Exception as e:
            print(f"❌ ERRO durante validação: {e}")
            return False
    
    def verificar_campos_obrigatorios():
        """
        Verifica se 'id' está na lista de campos obrigatórios.
        """
        print("\n📋 VERIFICANDO CAMPOS OBRIGATÓRIOS")
        print("=" * 50)
        
        from app_funcionarios import REQUIRED_FIELDS
        
        print(f"📊 Total de campos obrigatórios: {len(REQUIRED_FIELDS)}")
        print("📋 Campos obrigatórios:")
        for i, field in enumerate(REQUIRED_FIELDS, 1):
            print(f"  {i:2d}. {field}")
            if field == 'id':
                print(f"      ❌ PROBLEMA: 'id' está na lista de obrigatórios!")
        
        if 'id' in REQUIRED_FIELDS:
            print(f"\n❌ CAUSA RAIZ: Campo 'id' está na lista REQUIRED_FIELDS!")
            print(f"   Posição: {REQUIRED_FIELDS.index('id') + 1}")
            return True
        else:
            print(f"\n✅ Campo 'id' NÃO está na lista de obrigatórios")
            return False
    
    if __name__ == "__main__":
        print("🐛 DEBUG: FORMULÁRIO REAL - ERRO 'id'")
        print("=" * 60)
        
        # Verificar campos obrigatórios
        id_obrigatorio = verificar_campos_obrigatorios()
        
        # Interceptar dados do formulário
        dados = interceptar_dados_formulario()
        
        # Testar validação com ID vazio
        erro_reproduzido = testar_validacao_com_id_vazio()
        
        print("\n" + "=" * 60)
        print("📊 DIAGNÓSTICO FINAL")
        print("=" * 60)
        
        if id_obrigatorio:
            print("🎯 CAUSA RAIZ ENCONTRADA!")
            print("❌ Campo 'id' está na lista REQUIRED_FIELDS")
            print("🔧 SOLUÇÃO: Remover 'id' da lista de campos obrigatórios")
        elif erro_reproduzido:
            print("🎯 PROBLEMA REPRODUZIDO!")
            print("❌ Campo 'id' vazio está causando erro de validação")
            print("🔧 SOLUÇÃO: Filtrar campo 'id' vazio antes da validação")
        else:
            print("🤔 Problema não reproduzido nos testes")
            print("🔍 Pode ser um problema específico do template ou dados reais")

except ImportError as e:
    print(f"❌ Erro de import: {e}")
except Exception as e:
    print(f"❌ Erro inesperado: {e}")
    print(f"Traceback: {traceback.format_exc()}")
