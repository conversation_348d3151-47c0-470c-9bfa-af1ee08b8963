#!/usr/bin/env python3
"""
Script para debugar exatamente o que acontece durante a edição de funcionário
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def simular_edicao_richardson():
    """Simular o que acontece quando editamos o Richardson"""
    print("🔍 SIMULANDO EDIÇÃO DO RICHARDSON")
    print("=" * 60)
    
    try:
        # 1. Buscar estado ANTES da edição
        print("📋 ESTADO ANTES DA EDIÇÃO:")
        funcionario_antes = DatabaseManager.execute_query(
            "SELECT id, nome_completo, horario_trabalho_id, jornada_seg_qui_entrada, jornada_seg_qui_saida, jornada_sex_entrada, jornada_sex_saida, jornada_intervalo_entrada, jornada_intervalo_saida FROM funcionarios WHERE id = 1"
        )[0]
        
        print(f"   horario_trabalho_id: {funcionario_antes['horario_trabalho_id']}")
        print(f"   jornada_seg_qui_entrada: {funcionario_antes['jornada_seg_qui_entrada']}")
        print(f"   jornada_seg_qui_saida: {funcionario_antes['jornada_seg_qui_saida']}")
        print(f"   jornada_sex_entrada: {funcionario_antes['jornada_sex_entrada']}")
        print(f"   jornada_sex_saida: {funcionario_antes['jornada_sex_saida']}")
        print(f"   jornada_intervalo_entrada: {funcionario_antes['jornada_intervalo_entrada']}")
        print(f"   jornada_intervalo_saida: {funcionario_antes['jornada_intervalo_saida']}")
        
        # 2. Simular dados que vêm do formulário (sem campos de jornada)
        print(f"\n📝 SIMULANDO DADOS DO FORMULÁRIO (apenas EPIs alterados):")
        dados_formulario = {
            'nome_completo': 'RICHARDSON CARDOSO RODRIGUES',
            'cpf': '123.456.789-00',
            'rg': '1234567',
            'data_nascimento': '1990-01-01',
            'sexo': 'M',
            'estado_civil': 'SOLTEIRO',
            'nacionalidade': 'BRASILEIRO',
            'ctps_numero': '1234567',
            'ctps_serie_uf': '001/AC',
            'pis_pasep': '123.45678.90-1',
            'endereco_rua': 'RUA TESTE',
            'endereco_bairro': 'BAIRRO TESTE',
            'endereco_cidade': 'MANAUS',
            'endereco_cep': '69000-000',
            'endereco_estado': 'AM',
            'telefone1': '(92) 99999-9999',
            'telefone2': '',
            'email': '<EMAIL>',
            'cargo': 'DESENVOLVEDOR',
            'setor_obra': 'TI',
            'matricula_empresa': '001',
            'data_admissao': '2024-01-01',
            'tipo_contrato': 'CLT',
            'nivel_acesso': 'FUNCIONARIO',
            'turno': 'DIURNO',
            'tolerancia_ponto': 15,
            'banco_horas': 1,
            'hora_extra': 1,
            'status_cadastro': 'ATIVO',
            'empresa_id': 11,
            'digital_dedo1': '',
            'digital_dedo2': '',
            'foto_3x4': None,
            # NOTA: Campos de jornada NÃO estão sendo enviados do formulário
        }
        
        print("   ✅ Dados do formulário preparados (SEM campos de jornada)")
        
        # 3. Simular o processamento que deveria preservar a jornada
        print(f"\n🔧 SIMULANDO PROCESSAMENTO (_processar_dados_funcionario):")
        
        # Buscar funcionário atual para preservar jornada (como no código)
        funcionario_atual = funcionario_antes
        if funcionario_atual and funcionario_atual.get('horario_trabalho_id'):
            # Preservar jornada existente
            dados_formulario['horario_trabalho_id'] = funcionario_atual['horario_trabalho_id']
            dados_formulario['jornada_seg_qui_entrada'] = funcionario_atual.get('jornada_seg_qui_entrada')
            dados_formulario['jornada_seg_qui_saida'] = funcionario_atual.get('jornada_seg_qui_saida')
            dados_formulario['jornada_sex_entrada'] = funcionario_atual.get('jornada_sex_entrada')
            dados_formulario['jornada_sex_saida'] = funcionario_atual.get('jornada_sex_saida')
            dados_formulario['jornada_intervalo_entrada'] = funcionario_atual.get('jornada_intervalo_entrada')
            dados_formulario['jornada_intervalo_saida'] = funcionario_atual.get('jornada_intervalo_saida')
            
            print(f"   ✅ Jornada preservada nos dados:")
            print(f"      horario_trabalho_id: {dados_formulario['horario_trabalho_id']}")
            print(f"      jornada_seg_qui_entrada: {dados_formulario['jornada_seg_qui_entrada']}")
            print(f"      jornada_seg_qui_saida: {dados_formulario['jornada_seg_qui_saida']}")
        
        # 4. Simular o SQL de UPDATE (como no código atual)
        print(f"\n💾 SIMULANDO SQL DE UPDATE:")
        sql_atual = """
        UPDATE funcionarios SET
            nome_completo=%s, cpf=%s, rg=%s, data_nascimento=%s, sexo=%s, estado_civil=%s, nacionalidade=%s,
            ctps_numero=%s, ctps_serie_uf=%s, pis_pasep=%s,
            endereco_rua=%s, endereco_bairro=%s, endereco_cidade=%s, endereco_cep=%s, endereco_estado=%s,
            telefone1=%s, telefone2=%s, email=%s,
            cargo=%s, setor_obra=%s, matricula_empresa=%s, data_admissao=%s, tipo_contrato=%s,
            jornada_seg_qui_entrada=%s, jornada_seg_qui_saida=%s, jornada_sex_entrada=%s, jornada_sex_saida=%s,
            jornada_intervalo_entrada=%s, jornada_intervalo_saida=%s,
            nivel_acesso=%s, turno=%s, tolerancia_ponto=%s, banco_horas=%s, hora_extra=%s, status_cadastro=%s,
            empresa_id=%s, digital_dedo1=%s, digital_dedo2=%s, foto_3x4=%s
        WHERE id=%s
        """
        
        print("   ⚠️ PROBLEMA: SQL atualiza campos de jornada mesmo quando preservados!")
        print("   📋 Campos que serão atualizados:")
        print(f"      jornada_seg_qui_entrada = {dados_formulario.get('jornada_seg_qui_entrada')}")
        print(f"      jornada_seg_qui_saida = {dados_formulario.get('jornada_seg_qui_saida')}")
        print(f"      jornada_sex_entrada = {dados_formulario.get('jornada_sex_entrada')}")
        print(f"      jornada_sex_saida = {dados_formulario.get('jornada_sex_saida')}")
        print(f"      jornada_intervalo_entrada = {dados_formulario.get('jornada_intervalo_entrada')}")
        print(f"      jornada_intervalo_saida = {dados_formulario.get('jornada_intervalo_saida')}")
        
        # 5. Mostrar a solução
        print(f"\n💡 SOLUÇÃO:")
        print("   ❌ PROBLEMA: SQL sempre atualiza campos de jornada")
        print("   ✅ CORREÇÃO: Remover campos de jornada do SQL de UPDATE")
        print("   📝 ALTERNATIVA: Atualizar horario_trabalho_id separadamente se necessário")
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simular_edicao_richardson()
