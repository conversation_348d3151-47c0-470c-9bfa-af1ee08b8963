"""
RLPONTO-WEB v1.0 - Blueprint de Status do Sistema

Módulo responsável pelo status profissional do projeto, acompanhamento de progresso,
arquivo trabalhados e timeline de desenvolvimento.

Desenvolvido por: AiNexus Tecnologia
Autor: <PERSON> - Full Stack Developer
© 2025 AiNexus Tecnologia. Todos os direitos reservados.
"""

import os
import logging
import json
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from utils.auth import get_current_user
from utils.database import get_db_connection
from utils.config import Config
import glob

# Configurar logger
logger = logging.getLogger('controle-ponto.status')

# Criar Blueprint
status_bp = Blueprint('status', __name__, url_prefix='/status')

# 📋 MARCOS AUTOMÁTICOS DO PROJETO - Sistema de Tracking Inteligente
PROJECT_MILESTONES = {
    'infrastructure': {
        'name': 'Infraestrutura Base',
        'files': ['app.py', 'utils/database.py', 'utils/config.py', 'requirements.txt'],
        'weight': 2
    },
    'authentication': {
        'name': 'Sistema de Autenticação',
        'files': ['utils/auth.py', 'templates/login.html'],
        'weight': 2
    },
    'employees': {
        'name': 'Gestão de Funcionários',
        'files': ['app_funcionarios.py', 'templates/funcionarios/'],
        'weight': 3
    },
    'biometrics': {
        'name': 'Sistema Biométrico',
        'files': ['static/js/biometria-zkagent.js', 'templates/biometria/'],
        'weight': 4
    },
    'time_tracking': {
        'name': 'Controle de Ponto',
        'files': ['app_ponto.py', 'templates/ponto/'],
        'weight': 3
    },
    'reports': {
        'name': 'Relatórios',
        'files': ['app_relatorios.py', 'templates/relatorios/', 'static/css/relatorios.css'],
        'weight': 3
    },
    'epis': {
        'name': 'Gestão de EPIs',
        'files': ['app_epis.py', 'templates/epis/'],
        'weight': 2
    },
    'dashboard': {
        'name': 'Dashboard Administrativo',
        'files': ['templates/dashboard.html', 'static/css/'],
        'weight': 2
    },
    'status_system': {
        'name': 'Sistema de Status',
        'files': ['app_status.py', 'templates/status/'],
        'weight': 1
    },
    'security': {
        'name': 'Segurança e Validações',
        'files': ['utils/validators.py', 'utils/security.py'],
        'weight': 2
    },
    'testing': {
        'name': 'Testes Automatizados',
        'files': ['tests/', 'pytest.ini'],
        'weight': 1
    },
    'documentation': {
        'name': 'Documentação',
        'files': ['README.md', 'docs/', '*.md'],
        'weight': 1
    }
}

def calculate_automatic_progress():
    """Calcula automaticamente o progresso baseado nos marcos implementados"""
    try:
        total_weight = sum(milestone['weight'] for milestone in PROJECT_MILESTONES.values())
        completed_weight = 0
        milestone_status = {}
        
        for milestone_id, milestone in PROJECT_MILESTONES.items():
            completed = check_milestone_completion(milestone)
            milestone_status[milestone_id] = {
                'name': milestone['name'],
                'completed': completed,
                'weight': milestone['weight'],
                'completion_date': get_milestone_completion_date(milestone) if completed else None
            }
            
            if completed:
                completed_weight += milestone['weight']
        
        progress_percentage = int((completed_weight / total_weight) * 100)
        completed_milestones = sum(1 for status in milestone_status.values() if status['completed'])
        total_milestones = len(PROJECT_MILESTONES)
        
        return {
            'progress_percentage': progress_percentage,
            'completed_milestones': completed_milestones,
            'total_milestones': total_milestones,
            'milestone_details': milestone_status,
            'total_weight': total_weight,
            'completed_weight': completed_weight
        }
        
    except Exception as e:
        logger.error(f"Erro ao calcular progresso automático: {e}")
        # Fallback para valores padrão
        return {
            'progress_percentage': 75,
            'completed_milestones': 18,
            'total_milestones': 24,
            'milestone_details': {},
            'total_weight': 26,
            'completed_weight': 20
        }

def check_milestone_completion(milestone):
    """Verifica se um marco foi completado baseado na existência dos arquivos"""
    try:
        for file_pattern in milestone['files']:
            if file_pattern.endswith('/'):
                # É um diretório
                if not os.path.exists(file_pattern):
                    return False
            elif '*' in file_pattern:
                # É um padrão glob
                if not glob.glob(file_pattern):
                    return False
            else:
                # É um arquivo específico
                if not os.path.exists(file_pattern):
                    return False
        return True
    except Exception:
        return False

def get_milestone_completion_date(milestone):
    """Obtém a data de conclusão do marco baseada na data de modificação mais recente"""
    try:
        latest_date = None
        for file_pattern in milestone['files']:
            if file_pattern.endswith('/'):
                # Diretório - pegar arquivo mais recente
                if os.path.exists(file_pattern):
                    for root, dirs, files in os.walk(file_pattern):
                        for file in files:
                            file_path = os.path.join(root, file)
                            mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                            if latest_date is None or mtime > latest_date:
                                latest_date = mtime
            elif '*' in file_pattern:
                # Padrão glob
                for file_path in glob.glob(file_pattern):
                    mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if latest_date is None or mtime > latest_date:
                        latest_date = mtime
            else:
                # Arquivo específico
                if os.path.exists(file_pattern):
                    mtime = datetime.fromtimestamp(os.path.getmtime(file_pattern))
                    if latest_date is None or mtime > latest_date:
                        latest_date = mtime
        
        return latest_date
    except Exception:
        return None

def auto_detect_project_status():
    """Detecta automaticamente o status do projeto baseado no progresso"""
    progress_data = calculate_automatic_progress()
    progress = progress_data['progress_percentage']
    
    if progress >= 95:
        return 'PRODUÇÃO'
    elif progress >= 80:
        return 'HOMOLOGAÇÃO'
    elif progress >= 11:
        return 'DESENVOLVIMENTO'
    else:
        return 'PLANEJAMENTO'

def log_milestone_completion(milestone_id, milestone_name):
    """Registra automaticamente a conclusão de um marco"""
    try:
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'type': 'milestone_completed',
            'milestone_id': milestone_id,
            'milestone_name': milestone_name,
            'auto_detected': True
        }
        
        # Salvar no log de marcos (JSON)
        milestones_log_file = 'milestones_log.json'
        
        if os.path.exists(milestones_log_file):
            with open(milestones_log_file, 'r', encoding='utf-8') as f:
                logs = json.load(f)
        else:
            logs = []
        
        # Verificar se já foi registrado
        if not any(log['milestone_id'] == milestone_id for log in logs):
            logs.append(log_entry)
            
            with open(milestones_log_file, 'w', encoding='utf-8') as f:
                json.dump(logs, f, indent=2, ensure_ascii=False)
            
            logger.info(f"[MARCO CONCLUÍDO] {milestone_name} - Auto-detectado")
            
    except Exception as e:
        logger.error(f"Erro ao registrar marco: {e}")

def get_milestone_activities():
    """Obtém atividades de marcos concluídos"""
    activities = []
    try:
        milestones_log_file = 'milestones_log.json'
        if os.path.exists(milestones_log_file):
            with open(milestones_log_file, 'r', encoding='utf-8') as f:
                logs = json.load(f)
            
            for log in logs[-10:]:  # Últimos 10 marcos
                activities.append({
                    'timestamp': log['timestamp'],
                    'level': 'SUCCESS',
                    'message': f"Marco '{log['milestone_name']}' concluído automaticamente",
                    'type': 'milestone'
                })
    except Exception:
        pass
    
    return activities

def require_status_user():
    """Decorator para verificar se o usuário tem acesso ao status"""
    def decorator(f):
        def wrapper(*args, **kwargs):
            if 'usuario' not in session:
                return redirect(url_for('login'))
            
            # Apenas o usuário "status" pode acessar essas páginas
            if session.get('usuario') != 'status':
                return redirect(url_for('index'))
            
            return f(*args, **kwargs)
        wrapper.__name__ = f.__name__
        return wrapper
    return decorator

def get_file_timeline():
    """Obtém timeline dos arquivos trabalhados no projeto"""
    timeline = []
    
    # Diretórios para análise
    project_dirs = [
        'templates',
        'static',
        'utils',
        '.'
    ]
    
    try:
        for directory in project_dirs:
            if os.path.exists(directory):
                for root, dirs, files in os.walk(directory):
                    # Ignorar diretórios de backup e cache
                    dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'node_modules', 'backup-build']]
                    
                    for file in files:
                        if file.endswith(('.py', '.html', '.css', '.js', '.md', '.sql')):
                            file_path = os.path.join(root, file)
                            try:
                                stat = os.stat(file_path)
                                timeline.append({
                                    'arquivo': file_path.replace('\\', '/'),
                                    'nome': file,
                                    'tipo': get_file_type(file),
                                    'tamanho': stat.st_size,
                                    'modificado': datetime.fromtimestamp(stat.st_mtime),
                                    'criado': datetime.fromtimestamp(stat.st_ctime)
                                })
                            except:
                                continue
        
        # Ordenar por data de modificação
        timeline.sort(key=lambda x: x['modificado'], reverse=True)
        return timeline[:50]  # Últimos 50 arquivos
        
    except Exception as e:
        logger.error(f"Erro ao obter timeline: {e}")
        return []

def get_last_project_update():
    """Obtém a data/hora da última modificação real no projeto"""
    try:
        timeline = get_file_timeline()
        if timeline:
            # Retorna a data do arquivo mais recentemente modificado
            return timeline[0]['modificado']
        else:
            return datetime.now()
    except Exception as e:
        logger.error(f"Erro ao obter última atualização: {e}")
        return datetime.now()

def get_file_type(filename):
    """Determina o tipo do arquivo baseado na extensão"""
    ext = filename.split('.')[-1].lower()
    types = {
        'py': 'Python',
        'html': 'Template',
        'css': 'Estilo',
        'js': 'JavaScript', 
        'md': 'Documentação',
        'sql': 'Banco de Dados'
    }
    return types.get(ext, 'Outro')

def get_project_stats():
    """Obtém estatísticas do projeto"""
    stats = {
        'arquivos_python': 0,
        'arquivos_template': 0,
        'arquivos_css': 0,
        'arquivos_js': 0,
        'arquivos_docs': 0,
        'arquivos_sql': 0,
        'linhas_codigo': 0,
        'tamanho_total': 0
    }
    
    try:
        for root, dirs, files in os.walk('.'):
            dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'node_modules']]
            
            for file in files:
                file_path = os.path.join(root, file)
                ext = file.split('.')[-1].lower()
                
                try:
                    size = os.path.getsize(file_path)
                    stats['tamanho_total'] += size
                    
                    if ext == 'py':
                        stats['arquivos_python'] += 1
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            stats['linhas_codigo'] += len(f.readlines())
                    elif ext == 'html':
                        stats['arquivos_template'] += 1
                    elif ext == 'css':
                        stats['arquivos_css'] += 1
                    elif ext == 'js':
                        stats['arquivos_js'] += 1
                    elif ext in ['md', 'txt']:
                        stats['arquivos_docs'] += 1
                    elif ext == 'sql':
                        stats['arquivos_sql'] += 1
                except:
                    continue
        
        return stats
    except Exception as e:
        logger.error(f"Erro ao obter estatísticas: {e}")
        return stats

def get_database_stats():
    """Obtém estatísticas do banco de dados"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Estatísticas das tabelas principais
            cursor.execute("""
                SELECT 
                    (SELECT COUNT(*) FROM funcionarios) as funcionarios,
                    (SELECT COUNT(*) FROM registros_ponto) as registros_ponto,
                    (SELECT COUNT(*) FROM usuarios) as usuarios,
                    (SELECT COUNT(*) FROM epis) as epis
            """)
            stats = cursor.fetchone()
            
            # Tamanho do banco
            cursor.execute("""
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = 'controle_ponto'
            """)
            size_result = cursor.fetchone()
            stats['tamanho_mb'] = size_result['size_mb'] if size_result else 0
            
            return stats
    except Exception as e:
        logger.error(f"Erro ao obter estatísticas do banco: {e}")
        return {}
    finally:
        if 'conn' in locals():
            conn.close()

def get_recent_activities():
    """Obtém atividades recentes do sistema"""
    activities = []
    
    try:
        # Ler logs do sistema
        log_file = os.path.join(Config.LOG_DIR, 'app.log')
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()[-20:]  # Últimas 20 linhas
                
                for line in lines:
                    if any(keyword in line for keyword in ['INFO', 'ERROR', 'WARNING']):
                        parts = line.split(' - ', 3)
                        if len(parts) >= 3:
                            activities.append({
                                'timestamp': parts[0],
                                'level': parts[2],
                                'message': parts[3].strip() if len(parts) > 3 else parts[2],
                                'type': 'log'
                            })
        
        # Adicionar atividades do backup log
        backup_log = 'backup-build/backup-log.md'
        if os.path.exists(backup_log):
            activities.append({
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'level': 'INFO',
                'message': 'Backup log atualizado com correções recentes',
                'type': 'backup'
            })
        
        return activities[-10:]  # Últimas 10 atividades
        
    except Exception as e:
        logger.error(f"Erro ao obter atividades recentes: {e}")
        return []

@status_bp.route('/')
@require_status_user()
def dashboard():
    """Dashboard principal do status do sistema com tracking automático"""
    try:
        # Obter dados para o dashboard
        timeline = get_file_timeline()
        project_stats = get_project_stats()
        db_stats = get_database_stats()
        
        # 🤖 SISTEMA AUTOMÁTICO DE TRACKING
        auto_progress = calculate_automatic_progress()
        
        # Detectar marcos recém-concluídos e registrar automaticamente
        for milestone_id, details in auto_progress['milestone_details'].items():
            if details['completed']:
                log_milestone_completion(milestone_id, details['name'])
        
        # Combinar atividades automáticas e manuais
        manual_activities = get_recent_activities()
        milestone_activities = get_milestone_activities()
        all_activities = milestone_activities + manual_activities
        all_activities.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # Calcular dias decorridos e restantes
        from datetime import datetime, date
        inicio = date(2025, 5, 21)
        termino_previsto = date(2025, 7, 5)
        hoje = date.today()
        
        total_dias = (termino_previsto - inicio).days
        dias_decorridos = (hoje - inicio).days
        dias_restantes = (termino_previsto - hoje).days
        
        # Status automático baseado no progresso
        status_automatico = auto_detect_project_status()
        
        # Obter última atualização real do projeto
        ultima_atualizacao_real = get_last_project_update()
        
        # Dados do projeto com tracking automático
        project_data = {
            'nome': 'RLPONTO-WEB v1.0',
            'empresa': 'AiNexus Tecnologia',
            'desenvolvedor': 'Richardson Rodrigues',
            'inicio': '21/05/2025',
            'termino_previsto': '05/07/2025',
            'status': status_automatico,
            'progresso': auto_progress['progress_percentage'],
            'marcos_concluidos': auto_progress['completed_milestones'],
            'marcos_total': auto_progress['total_milestones'],
            'total_dias': total_dias,
            'dias_decorridos': dias_decorridos,
            'dias_restantes': dias_restantes,
            'progresso_tempo': round((dias_decorridos / total_dias) * 100) if total_dias > 0 else 0,
            'milestone_details': auto_progress['milestone_details'],
            'auto_tracking': True,
            'ultima_atualizacao': ultima_atualizacao_real
        }
        
        return render_template('status/dashboard.html',
                             project_data=project_data,
                             timeline=timeline,
                             project_stats=project_stats,
                             db_stats=db_stats,
                             activities=all_activities[:15])  # Top 15 atividades
                             
    except Exception as e:
        logger.error(f"Erro no dashboard de status: {e}")
        return render_template('erro.html', 
                             titulo="Erro no sistema de status",
                             mensagem=f"Erro interno: {str(e)}")

@status_bp.route('/refresh', methods=['POST'])
@require_status_user()
def refresh_data():
    """API para atualizar dados do dashboard"""
    try:
        timeline = get_file_timeline()
        project_stats = get_project_stats()
        db_stats = get_database_stats()
        activities = get_recent_activities()
        
        # Obter última modificação REAL do projeto
        ultima_modificacao = get_last_project_update()
        
        return jsonify({
            'success': True,
            'data': {
                'timeline': timeline[:10],  # Top 10 arquivos recentes
                'project_stats': project_stats,
                'db_stats': db_stats,
                'activities': activities,
                'last_real_update': ultima_modificacao.strftime('%d/%m/%Y %H:%M:%S'),
                'dashboard_refreshed_at': datetime.now().strftime('%H:%M:%S')
            }
        })
        
    except Exception as e:
        logger.error(f"Erro ao refresh dados: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@status_bp.route('/milestones')
@require_status_user()
def milestones():
    """API para obter detalhes dos marcos do projeto"""
    try:
        auto_progress = calculate_automatic_progress()
        return jsonify({
            'success': True,
            'milestones': auto_progress['milestone_details'],
            'progress': auto_progress['progress_percentage'],
            'completed': auto_progress['completed_milestones'],
            'total': auto_progress['total_milestones']
        })
    except Exception as e:
        logger.error(f"Erro ao obter marcos: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@status_bp.route('/force-milestone-check', methods=['POST'])
@require_status_user()
def force_milestone_check():
    """Força verificação manual dos marcos"""
    try:
        auto_progress = calculate_automatic_progress()
        
        # Forçar log de todos os marcos concluídos
        for milestone_id, details in auto_progress['milestone_details'].items():
            if details['completed']:
                log_milestone_completion(milestone_id, details['name'])
        
        return jsonify({
            'success': True,
            'message': 'Verificação de marcos concluída',
            'progress': auto_progress
        })
    except Exception as e:
        logger.error(f"Erro na verificação forçada: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@status_bp.route('/logout')
@require_status_user()
def logout():
    """Logout específico para usuário status"""
    session.clear()
    return redirect(url_for('login'))
