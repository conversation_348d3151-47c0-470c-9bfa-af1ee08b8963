#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
from pymysql.cursors import DictCursor

print("=== DEBUG DADOS FUNCIONÁRIOS ===")

# Conectar ao banco
conn = pymysql.connect(
    host='************',
    user='cavalcrod', 
    password='200381',
    db='controle_ponto'
)

cursor = conn.cursor(DictCursor)

# Buscar dados completos dos funcionários
cursor.execute("""
    SELECT 
        f.id, f.nome_completo, f.cpf, f.matricula_empresa, f.cargo, f.setor, 
        f.foto_3x4, f.empresa_id, f.horario_trabalho_id,
        e.nome_fantasia AS empresa_nome,
        ht.nome_horario
    FROM funcionarios f
    LEFT JOIN empresas e ON f.empresa_id = e.id
    LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
    WHERE f.ativo = TRUE
    ORDER BY f.nome_completo
    LIMIT 5
""")

funcionarios = cursor.fetchall()

print(f"Total funcionários encontrados: {len(funcionarios)}")
print()

for i, f in enumerate(funcionarios, 1):
    print(f"--- FUNCIONÁRIO {i} ---")
    print(f"ID: {f['id']}")
    print(f"Nome: {f['nome_completo']}")
    print(f"CPF: {f['cpf']}")
    print(f"Matrícula: {f['matricula_empresa']}")
    print(f"Cargo: {f['cargo']}")
    print(f"Setor: {f['setor']}")
    print(f"Foto: {f['foto_3x4']}")
    print(f"Empresa ID: {f['empresa_id']}")
    print(f"Empresa Nome: {f['empresa_nome']}")
    print(f"Horário Trabalho ID: {f['horario_trabalho_id']}")
    print(f"Nome Horário: {f['nome_horario']}")
    print()

# Verificar tabela empresas
cursor.execute("SELECT COUNT(*) as count FROM empresas")
empresas_count = cursor.fetchone()
print(f"Total empresas cadastradas: {empresas_count['count']}")

if empresas_count['count'] > 0:
    cursor.execute("SELECT id, nome_fantasia, nome_corporativo FROM empresas LIMIT 3")
    empresas = cursor.fetchall()
    print("Empresas:")
    for emp in empresas:
        print(f"  - ID {emp['id']}: {emp['nome_fantasia']} ({emp['nome_corporativo']})")

# Verificar tabela horarios_trabalho
cursor.execute("SELECT COUNT(*) as count FROM horarios_trabalho")
horarios_count = cursor.fetchone()
print(f"\nTotal horários de trabalho: {horarios_count['count']}")

if horarios_count['count'] > 0:
    cursor.execute("SELECT id, nome_horario FROM horarios_trabalho LIMIT 3")
    horarios = cursor.fetchall()
    print("Horários:")
    for hor in horarios:
        print(f"  - ID {hor['id']}: {hor['nome_horario']}")

conn.close()
print("\n=== FIM DEBUG ===") 