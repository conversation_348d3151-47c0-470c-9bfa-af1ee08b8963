-- ========================================
-- ATUALIZAÇÃO BANCO DE DADOS - REGISTRO DE PONTO
-- Data: 08/01/2025
-- Descrição: Adiciona funcionalidades de registro de ponto e configurações
-- ========================================

USE controle_ponto;

-- ========================================
-- 1. ATUALIZAR TABELA REGISTROS_PONTO
-- ========================================

-- Adicionar campos necessários para o sistema de registro
ALTER TABLE registros_ponto 
MODIFY COLUMN tipo_registro ENUM('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida', 'entrada', 'saida_antiga', 'intervalo_inicio', 'intervalo_fim') NOT NULL DEFAULT 'entrada_manha';

ALTER TABLE registros_ponto 
ADD COLUMN IF NOT EXISTS metodo_registro ENUM('biometrico', 'manual') NOT NULL DEFAULT 'biometrico' AFTER qualidade_biometria;

ALTER TABLE registros_ponto 
ADD COLUMN IF NOT EXISTS ip_origem VARCHAR(45) NULL COMMENT 'IP de origem do registro' AFTER observacoes;

ALTER TABLE registros_ponto 
ADD COLUMN IF NOT EXISTS criado_por INT NULL COMMENT 'ID do usuário que fez o registro manual' AFTER ip_origem;

ALTER TABLE registros_ponto 
ADD COLUMN IF NOT EXISTS criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER criado_por;

-- Adicionar chaves estrangeiras
ALTER TABLE registros_ponto 
ADD CONSTRAINT fk_registros_ponto_criado_por 
FOREIGN KEY (criado_por) REFERENCES usuarios(id) ON DELETE SET NULL;

-- ========================================
-- 2. CRIAR TABELA EMPRESAS
-- ========================================

CREATE TABLE IF NOT EXISTS empresas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    razao_social VARCHAR(200) NOT NULL,
    nome_fantasia VARCHAR(200) NULL,
    cnpj VARCHAR(18) UNIQUE NOT NULL,
    inscricao_estadual VARCHAR(20) NULL,
    inscricao_municipal VARCHAR(20) NULL,
    endereco_rua VARCHAR(150) NULL,
    endereco_numero VARCHAR(10) NULL,
    endereco_complemento VARCHAR(50) NULL,
    endereco_bairro VARCHAR(50) NULL,
    endereco_cidade VARCHAR(50) NULL,
    endereco_estado VARCHAR(2) NULL,
    endereco_cep VARCHAR(10) NULL,
    telefone VARCHAR(15) NULL,
    email VARCHAR(100) NULL,
    website VARCHAR(100) NULL,
    ativa BOOLEAN DEFAULT TRUE,
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci 
COMMENT='Cadastro de empresas do sistema';

-- Índices para tabela empresas
CREATE INDEX idx_empresas_cnpj ON empresas(cnpj);
CREATE INDEX idx_empresas_ativa ON empresas(ativa);

-- ========================================
-- 3. CRIAR TABELA HORARIOS_TRABALHO
-- ========================================

CREATE TABLE IF NOT EXISTS horarios_trabalho (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_id INT NOT NULL,
    nome_horario VARCHAR(100) NOT NULL COMMENT 'Ex: Administrativo, Produção, etc.',
    entrada_manha TIME NOT NULL DEFAULT '08:00:00',
    saida_almoco TIME NULL DEFAULT '12:00:00',
    entrada_tarde TIME NULL DEFAULT '13:00:00',
    saida TIME NOT NULL DEFAULT '17:00:00',
    tolerancia_minutos INT NOT NULL DEFAULT 10,
    ativo BOOLEAN DEFAULT TRUE,
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (empresa_id) REFERENCES empresas(id) ON DELETE CASCADE
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci 
COMMENT='Configurações de horários de trabalho por empresa';

-- Índices para tabela horarios_trabalho
CREATE INDEX idx_horarios_empresa ON horarios_trabalho(empresa_id);
CREATE INDEX idx_horarios_ativo ON horarios_trabalho(ativo);

-- ========================================
-- 4. ATUALIZAR TABELA FUNCIONARIOS
-- ========================================

-- Adicionar relacionamento com empresa e horário
ALTER TABLE funcionarios 
ADD COLUMN IF NOT EXISTS empresa_id INT NULL AFTER id;

ALTER TABLE funcionarios 
ADD COLUMN IF NOT EXISTS horario_trabalho_id INT NULL AFTER empresa_id;

-- Renomear campos para padronização
ALTER TABLE funcionarios 
CHANGE COLUMN setor_obra setor VARCHAR(50) NULL;

ALTER TABLE funcionarios 
CHANGE COLUMN status_cadastro ativo BOOLEAN DEFAULT TRUE;

-- Adicionar chaves estrangeiras
ALTER TABLE funcionarios 
ADD CONSTRAINT fk_funcionarios_empresa 
FOREIGN KEY (empresa_id) REFERENCES empresas(id) ON DELETE SET NULL;

ALTER TABLE funcionarios 
ADD CONSTRAINT fk_funcionarios_horario 
FOREIGN KEY (horario_trabalho_id) REFERENCES horarios_trabalho(id) ON DELETE SET NULL;

-- ========================================
-- 5. ATUALIZAR TABELA USUARIOS
-- ========================================

-- Adicionar campos para melhor controle de usuários
ALTER TABLE usuarios 
ADD COLUMN IF NOT EXISTS nome_completo VARCHAR(100) NULL AFTER usuario;

ALTER TABLE usuarios 
ADD COLUMN IF NOT EXISTS email VARCHAR(100) NULL AFTER nome_completo;

ALTER TABLE usuarios 
ADD COLUMN IF NOT EXISTS ativo BOOLEAN DEFAULT TRUE AFTER email;

ALTER TABLE usuarios 
ADD COLUMN IF NOT EXISTS ultimo_login TIMESTAMP NULL AFTER ativo;

ALTER TABLE usuarios 
ADD COLUMN IF NOT EXISTS data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER ultimo_login;

-- Atualizar tabela de permissões
ALTER TABLE permissoes 
MODIFY COLUMN nivel_acesso ENUM('admin', 'usuario', 'supervisor') DEFAULT 'usuario';

-- ========================================
-- 6. CRIAR VIEWS OTIMIZADAS PARA RELATÓRIOS
-- ========================================

-- View para relatórios de ponto
CREATE OR REPLACE VIEW vw_relatorio_pontos AS
SELECT 
    rp.id,
    rp.funcionario_id,
    f.nome_completo,
    f.matricula_empresa,
    f.cpf,
    CONCAT(SUBSTRING(f.cpf, 1, 3), '.***.**', SUBSTRING(f.cpf, -2)) AS cpf_exibicao,
    rp.data_hora,
    DATE(rp.data_hora) AS data_registro,
    TIME(rp.data_hora) AS hora_registro,
    rp.tipo_registro,
    CASE 
        WHEN rp.tipo_registro = 'entrada_manha' THEN 'Entrada Manhã'
        WHEN rp.tipo_registro = 'saida_almoco' THEN 'Saída Almoço'
        WHEN rp.tipo_registro = 'entrada_tarde' THEN 'Entrada Tarde'
        WHEN rp.tipo_registro = 'saida' THEN 'Saída'
        ELSE rp.tipo_registro
    END AS tipo_descricao,
    rp.metodo_registro,
    CASE 
        WHEN rp.metodo_registro = 'biometrico' THEN 'Biométrico'
        WHEN rp.metodo_registro = 'manual' THEN CONCAT('Manual (', COALESCE(u.usuario, 'Sistema'), ')')
        ELSE rp.metodo_registro
    END AS metodo_descricao,
    f.setor,
    f.cargo,
    rp.qualidade_biometria,
    rp.observacoes,
    rp.ip_origem,
    u.usuario AS criado_por_usuario,
    rp.criado_em,
    e.nome_fantasia AS empresa,
    ht.nome_horario AS horario_trabalho,
    CASE 
        WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > ADDTIME(COALESCE(ht.entrada_manha, '08:00:00'), SEC_TO_TIME(COALESCE(ht.tolerancia_minutos, 10) * 60)) THEN 'Atraso'
        WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > ADDTIME(COALESCE(ht.entrada_tarde, '13:00:00'), SEC_TO_TIME(COALESCE(ht.tolerancia_minutos, 10) * 60)) THEN 'Atraso'
        ELSE 'Pontual'
    END AS status_pontualidade
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
LEFT JOIN usuarios u ON rp.criado_por = u.id
LEFT JOIN empresas e ON f.empresa_id = e.id
LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
WHERE f.ativo = TRUE
ORDER BY rp.data_hora DESC;

-- View para estatísticas de pontos
CREATE OR REPLACE VIEW vw_estatisticas_pontos AS
SELECT 
    DATE(rp.data_hora) AS data_registro,
    COUNT(*) AS total_registros,
    COUNT(DISTINCT rp.funcionario_id) AS funcionarios_distintos,
    SUM(CASE WHEN rp.metodo_registro = 'biometrico' THEN 1 ELSE 0 END) AS registros_biometricos,
    SUM(CASE WHEN rp.metodo_registro = 'manual' THEN 1 ELSE 0 END) AS registros_manuais,
    SUM(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN 1 ELSE 0 END) AS entradas_manha,
    SUM(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN 1 ELSE 0 END) AS saidas_almoco,
    SUM(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN 1 ELSE 0 END) AS entradas_tarde,
    SUM(CASE WHEN rp.tipo_registro = 'saida' THEN 1 ELSE 0 END) AS saidas,
    SUM(CASE 
        WHEN (rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > ADDTIME(COALESCE(ht.entrada_manha, '08:00:00'), SEC_TO_TIME(COALESCE(ht.tolerancia_minutos, 10) * 60))) OR
             (rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > ADDTIME(COALESCE(ht.entrada_tarde, '13:00:00'), SEC_TO_TIME(COALESCE(ht.tolerancia_minutos, 10) * 60)))
        THEN 1 ELSE 0 
    END) AS atrasos
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
WHERE f.ativo = TRUE
GROUP BY DATE(rp.data_hora)
ORDER BY data_registro DESC;

-- ========================================
-- 7. INSERIR DADOS PADRÃO
-- ========================================

-- Inserir empresa padrão se não existir
INSERT IGNORE INTO empresas (id, razao_social, nome_fantasia, cnpj, ativa) 
VALUES (1, 'Empresa Padrão Ltda', 'Empresa Padrão', '00.000.000/0000-00', TRUE);

-- Inserir horário padrão se não existir
INSERT IGNORE INTO horarios_trabalho (id, empresa_id, nome_horario, entrada_manha, saida_almoco, entrada_tarde, saida, tolerancia_minutos) 
VALUES (1, 1, 'Horário Administrativo', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10);

-- Atualizar funcionários existentes para usar empresa e horário padrão
UPDATE funcionarios 
SET empresa_id = 1, horario_trabalho_id = 1 
WHERE empresa_id IS NULL OR horario_trabalho_id IS NULL;

-- ========================================
-- 8. CRIAR ÍNDICES OTIMIZADOS
-- ========================================

-- Índices para melhor performance nos relatórios
CREATE INDEX IF NOT EXISTS idx_registros_ponto_data_tipo ON registros_ponto(data_hora, tipo_registro);
CREATE INDEX IF NOT EXISTS idx_registros_ponto_metodo ON registros_ponto(metodo_registro);
CREATE INDEX IF NOT EXISTS idx_funcionarios_empresa ON funcionarios(empresa_id);
CREATE INDEX IF NOT EXISTS idx_funcionarios_horario ON funcionarios(horario_trabalho_id);

-- ========================================
-- 9. ATUALIZAR ESTATÍSTICAS DO BANCO
-- ========================================

-- Recalcular estatísticas das tabelas
ANALYZE TABLE funcionarios, registros_ponto, empresas, horarios_trabalho, usuarios, permissoes;

-- ========================================
-- SCRIPT CONCLUÍDO
-- ======================================== 