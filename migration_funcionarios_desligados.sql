-- =====================================================
-- MIGRAÇÃO: SISTEMA DE FUNCIONÁRIOS DESLIGADOS
-- Data: 13/07/2025
-- Objetivo: Implementar soft delete profissional
-- =====================================================

-- 1. CRIAR TABELA DE FUNCIONÁRIOS DESLIGADOS
CREATE TABLE funcionarios_desligados (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    
    -- Dados do funcionário original (cópia completa)
    funcionario_id_original INT UNSIGNED NOT NULL,
    nome_completo VARCHAR(100) NOT NULL,
    cpf VARCHAR(14) NOT NULL,
    rg VARCHAR(20) NOT NULL,
    data_nascimento DATE NOT NULL,
    sexo ENUM('M', 'F', 'Outro') NOT NULL,
    estado_civil ENUM('Solteiro', 'Casado', 'Divorciado', 'Viuvo') NOT NULL,
    nacionalidade VARCHAR(50) NOT NULL DEFAULT 'Brasileiro',
    
    -- Documentos trabalhistas
    ctps_numero VARCHAR(20) NOT NULL,
    ctps_serie_uf VARCHAR(20) NOT NULL,
    pis_pasep VARCHAR(20) NOT NULL,
    
    -- Endereço
    endereco_rua VARCHAR(100) DEFAULT NULL,
    endereco_bairro VARCHAR(50) DEFAULT NULL,
    endereco_cidade VARCHAR(50) DEFAULT NULL,
    endereco_cep VARCHAR(10) NOT NULL,
    endereco_estado VARCHAR(2) NOT NULL,
    
    -- Contatos
    telefone1 VARCHAR(15) NOT NULL,
    telefone2 VARCHAR(15) DEFAULT NULL,
    email VARCHAR(100) DEFAULT NULL,
    
    -- Dados profissionais
    cargo VARCHAR(100) NOT NULL,
    setor VARCHAR(100) DEFAULT NULL,
    setor_obra VARCHAR(100) DEFAULT NULL,
    matricula_empresa VARCHAR(10) NOT NULL,
    data_admissao DATE NOT NULL,
    tipo_contrato ENUM('CLT', 'Terceirizado', 'Temporario', 'Estagiario') NOT NULL,
    
    -- Configurações de trabalho
    nivel_acesso ENUM('funcionario', 'supervisor', 'admin') DEFAULT 'funcionario',
    turno ENUM('Manha', 'Tarde', 'Noite', 'Integral') NOT NULL,
    tolerancia_ponto INT DEFAULT 10,
    banco_horas BOOLEAN DEFAULT TRUE,
    hora_extra BOOLEAN DEFAULT TRUE,
    status_cadastro ENUM('Ativo', 'Inativo', 'Pendente') DEFAULT 'Ativo',
    horas_trabalho_obrigatorias DECIMAL(4,2) DEFAULT 8.00,
    
    -- Relacionamentos
    empresa_id INT UNSIGNED DEFAULT NULL,
    jornada_trabalho_id INT UNSIGNED DEFAULT NULL,
    horario_trabalho_id INT UNSIGNED DEFAULT NULL,
    
    -- Dados biométricos (preservados para auditoria)
    digital_dedo1 LONGBLOB DEFAULT NULL,
    digital_dedo2 LONGBLOB DEFAULT NULL,
    foto_3x4 LONGBLOB DEFAULT NULL,
    
    -- Dados do desligamento
    data_desligamento TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    motivo_desligamento ENUM(
        'Demissao_sem_justa_causa',
        'Demissao_com_justa_causa', 
        'Pedido_demissao',
        'Termino_contrato',
        'Aposentadoria',
        'Falecimento',
        'Abandono_emprego',
        'Acordo_mutuo',
        'Outros'
    ) NOT NULL,
    observacoes_desligamento TEXT DEFAULT NULL,
    usuario_responsavel_desligamento INT UNSIGNED DEFAULT NULL,
    
    -- Dados de auditoria
    data_criacao_original TIMESTAMP DEFAULT NULL,
    data_ultima_atualizacao TIMESTAMP DEFAULT NULL,
    
    -- Índices para performance
    INDEX idx_funcionario_original (funcionario_id_original),
    INDEX idx_matricula (matricula_empresa),
    INDEX idx_cpf (cpf),
    INDEX idx_data_desligamento (data_desligamento),
    INDEX idx_motivo (motivo_desligamento),
    INDEX idx_empresa (empresa_id),
    
    -- Constraints
    UNIQUE KEY uk_funcionario_original (funcionario_id_original),
    UNIQUE KEY uk_matricula_desligado (matricula_empresa)
    
) ENGINE=InnoDB 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT = 'Histórico de funcionários desligados - Auditoria e Compliance';

-- 2. CRIAR TABELA DE LOG DE DESLIGAMENTOS
CREATE TABLE log_desligamentos (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    funcionario_id_original INT UNSIGNED NOT NULL,
    nome_funcionario VARCHAR(100) NOT NULL,
    matricula VARCHAR(10) NOT NULL,
    motivo_desligamento VARCHAR(50) NOT NULL,
    data_desligamento TIMESTAMP NOT NULL,
    usuario_responsavel INT UNSIGNED NOT NULL,
    ip_origem VARCHAR(45) DEFAULT NULL,
    observacoes TEXT DEFAULT NULL,
    data_log TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_funcionario (funcionario_id_original),
    INDEX idx_data_desligamento (data_desligamento),
    INDEX idx_usuario (usuario_responsavel),
    INDEX idx_matricula (matricula)
    
) ENGINE=InnoDB 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT = 'Log de auditoria de desligamentos de funcionários';

-- 3. CRIAR VIEW PARA CONSULTAS UNIFICADAS
CREATE VIEW vw_todas_matriculas AS
SELECT 
    matricula_empresa,
    'ATIVO' as status,
    nome_completo,
    data_admissao as data_referencia,
    'funcionarios' as origem
FROM funcionarios
WHERE status_cadastro = 'Ativo'

UNION ALL

SELECT 
    matricula_empresa,
    'DESLIGADO' as status,
    nome_completo,
    data_desligamento as data_referencia,
    'funcionarios_desligados' as origem
FROM funcionarios_desligados

ORDER BY matricula_empresa;

-- 4. COMENTÁRIOS E DOCUMENTAÇÃO
ALTER TABLE funcionarios_desligados 
COMMENT = 'Tabela de histórico de funcionários desligados. Mantém registro completo para auditoria, compliance trabalhista e prevenção de reutilização de matrículas. NUNCA deletar registros desta tabela.';

ALTER TABLE log_desligamentos 
COMMENT = 'Log de auditoria de todos os desligamentos realizados no sistema. Rastreabilidade completa para compliance e auditoria interna.';
