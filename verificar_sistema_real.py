#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 VERIFICAÇÃO DO SISTEMA REAL - RLPONTO-WEB
===========================================

Sistema: RLPONTO-WEB v1.0
Data: 18/07/2025
Objetivo: Verificar o que REALMENTE existe no sistema

VERIFICAÇÕES:
- ✅ Status do servidor
- ✅ Funcionários reais cadastrados
- ✅ Registros de ponto existentes
- ✅ Estrutura do banco de dados
- ✅ APIs funcionando
"""

import requests
import sys
from datetime import datetime, date
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('verificar_real')

class VerificadorSistemaReal:
    """
    Classe para verificar o sistema real.
    """
    
    def __init__(self, base_url="http://10.19.208.31:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def verificar_servidor_online(self):
        """
        Verifica se o servidor está online e respondendo.
        """
        try:
            response = self.session.get(self.base_url, timeout=10)
            
            if response.status_code in [200, 302]:  # 302 = redirect para login
                logger.info("✅ Servidor ONLINE e respondendo")
                return True
            else:
                logger.error(f"❌ Servidor retornou código: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Servidor OFFLINE ou inacessível: {e}")
            return False
    
    def fazer_login(self, usuario="admin", senha="@Ric6109"):
        """
        Tenta fazer login no sistema real.
        """
        try:
            login_data = {
                'usuario': usuario,
                'senha': senha
            }
            
            response = self.session.post(f"{self.base_url}/login", data=login_data)
            
            if response.status_code == 200 and 'dashboard' in response.url:
                logger.info("✅ Login realizado com sucesso")
                return True
            else:
                logger.error(f"❌ Falha no login: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro no login: {e}")
            return False
    
    def verificar_funcionarios_existentes(self):
        """
        Verifica quantos funcionários realmente existem.
        """
        try:
            response = self.session.get(f"{self.base_url}/funcionarios/api/listar")
            
            if response.status_code == 200:
                funcionarios = response.json()
                
                logger.info(f"✅ Encontrados {len(funcionarios)} funcionários REAIS:")
                
                for i, func in enumerate(funcionarios[:10], 1):  # Mostrar apenas 10
                    nome = func.get('nome_completo', 'N/A')
                    status = func.get('status', 'N/A')
                    logger.info(f"   {i}. {nome} - Status: {status}")
                
                if len(funcionarios) > 10:
                    logger.info(f"   ... e mais {len(funcionarios) - 10} funcionários")
                
                return len(funcionarios)
            else:
                logger.error(f"❌ Erro ao obter funcionários: {response.status_code}")
                return 0
                
        except Exception as e:
            logger.error(f"❌ Erro ao verificar funcionários: {e}")
            return 0
    
    def verificar_registros_ponto_hoje(self):
        """
        Verifica se há registros de ponto hoje.
        """
        try:
            # Tentar obter registros via API (se existir endpoint)
            hoje = date.today().strftime('%Y-%m-%d')
            
            # Primeiro, vamos tentar obter funcionários e depois seus registros
            response = self.session.get(f"{self.base_url}/funcionarios/api/listar")
            
            if response.status_code == 200:
                funcionarios = response.json()
                
                if funcionarios:
                    # Pegar primeiro funcionário para testar
                    primeiro_func = funcionarios[0]
                    func_id = primeiro_func.get('id')
                    
                    # Tentar obter registros do funcionário
                    response_registros = self.session.get(
                        f"{self.base_url}/ponto-admin/api/funcionario/{func_id}/registros",
                        params={'data': hoje}
                    )
                    
                    if response_registros.status_code == 200:
                        registros = response_registros.json()
                        logger.info(f"✅ Encontrados {len(registros)} registros hoje para {primeiro_func.get('nome_completo')}")
                        return len(registros)
                    else:
                        logger.info("ℹ️  Nenhum registro de ponto encontrado hoje")
                        return 0
                else:
                    logger.info("ℹ️  Nenhum funcionário encontrado")
                    return 0
            else:
                logger.error(f"❌ Erro ao verificar registros: {response.status_code}")
                return 0
                
        except Exception as e:
            logger.error(f"❌ Erro ao verificar registros de ponto: {e}")
            return 0
    
    def verificar_apis_disponiveis(self):
        """
        Verifica quais APIs estão disponíveis.
        """
        apis_teste = [
            '/funcionarios/api/listar',
            '/registro-ponto/api/registrar-manual',
            '/ponto-admin/api/funcionarios',
            '/relatorios/api/espelho-ponto'
        ]
        
        apis_funcionando = 0
        
        logger.info("🔍 Verificando APIs disponíveis:")
        
        for api in apis_teste:
            try:
                response = self.session.get(f"{self.base_url}{api}")
                
                if response.status_code in [200, 400, 405]:  # 400/405 = API existe mas precisa de parâmetros
                    logger.info(f"   ✅ {api} - Disponível")
                    apis_funcionando += 1
                else:
                    logger.info(f"   ❌ {api} - Não disponível ({response.status_code})")
                    
            except Exception as e:
                logger.info(f"   ❌ {api} - Erro: {e}")
        
        return apis_funcionando
    
    def executar_verificacao_completa(self):
        """
        Executa verificação completa do sistema real.
        """
        logger.info("🔍 VERIFICANDO SISTEMA REAL RLPONTO-WEB")
        logger.info("=" * 50)
        
        resultados = {}
        
        # 1. Verificar servidor
        resultados['servidor_online'] = self.verificar_servidor_online()
        
        if not resultados['servidor_online']:
            logger.error("❌ Servidor offline. Não é possível continuar verificação.")
            return resultados
        
        # 2. Fazer login
        resultados['login_ok'] = self.fazer_login()
        
        if not resultados['login_ok']:
            logger.error("❌ Login falhou. Verificação limitada.")
            return resultados
        
        # 3. Verificar funcionários
        resultados['num_funcionarios'] = self.verificar_funcionarios_existentes()
        
        # 4. Verificar registros de hoje
        resultados['registros_hoje'] = self.verificar_registros_ponto_hoje()
        
        # 5. Verificar APIs
        resultados['apis_funcionando'] = self.verificar_apis_disponiveis()
        
        # 6. Relatório final
        self.gerar_relatorio_verificacao(resultados)
        
        return resultados
    
    def gerar_relatorio_verificacao(self, resultados):
        """
        Gera relatório da verificação.
        """
        logger.info("\n" + "=" * 50)
        logger.info("📊 RELATÓRIO DE VERIFICAÇÃO DO SISTEMA REAL")
        logger.info("=" * 50)
        
        logger.info(f"🌐 Servidor online: {'✅ Sim' if resultados['servidor_online'] else '❌ Não'}")
        logger.info(f"🔐 Login funcionando: {'✅ Sim' if resultados['login_ok'] else '❌ Não'}")
        logger.info(f"👥 Funcionários cadastrados: {resultados.get('num_funcionarios', 0)}")
        logger.info(f"📝 Registros hoje: {resultados.get('registros_hoje', 0)}")
        logger.info(f"🔌 APIs funcionando: {resultados.get('apis_funcionando', 0)}")
        
        # Status geral
        if (resultados['servidor_online'] and resultados['login_ok'] and 
            resultados.get('num_funcionarios', 0) > 0):
            logger.info("\n🎉 SISTEMA REAL FUNCIONANDO!")
            logger.info("✅ Pronto para testes reais")
        else:
            logger.info("\n⚠️ SISTEMA COM LIMITAÇÕES")
            logger.info("❌ Verificar configurações antes de testes reais")

def main():
    """
    Função principal.
    """
    print("🔍 VERIFICAÇÃO DO SISTEMA REAL RLPONTO-WEB")
    print("=" * 50)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 50)
    
    verificador = VerificadorSistemaReal()
    resultados = verificador.executar_verificacao_completa()
    
    return resultados.get('servidor_online', False)

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
