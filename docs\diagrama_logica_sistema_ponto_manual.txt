```
+------------------------------------+
| 1. Usu<PERSON><PERSON> acessa /registro-ponto  |
+------------------------------------+
                |
                v
+------------------------------------+
| 2. Se<PERSON>ciona funcionário           |
+------------------------------------+
                |
                v
+------------------------------------+
| 3. <PERSON><PERSON><PERSON> chama GET /api/obter-horarios/{funcionario_id} |
+------------------------------------+
                |
                v
+------------------------------------+
| 4. <PERSON><PERSON><PERSON> fun<PERSON> ativo?      |
+------------------------------------+
                |
    +-----------+-----------+
    |                       |
    v                       v
+------------------------------------+    +------------------------------------+
| 5. Retornar erro: Funcionário inativo | | 6. Obter jornada do funcionário    |
|                                       | | (ex.: 08:00-18:00, intervalo 13:00-14:00) |
+------------------------------------+    +------------------------------------+
                                                         |
                                                         v
+------------------------------------+    +------------------------------------+
| 7. Jornada configurada?            |<---| 8. Obter registros do dia          |
+------------------------------------+    +------------------------------------+
                |                                     |
                v                                     v
+------------------------------------+    +------------------------------------+
| 9. Retornar erro: Jornada não      |    | 10. Consultar tabela dia_dados     |
|    configurada                     |    |     (ex.: Manhã 06:00-11:00)      |
+------------------------------------+    +------------------------------------+
                                                         |
                                                         v
+------------------------------------+    +------------------------------------+
| 11. Mapear período para tipo       |<---| 12. Hora atual está em algum período? |
|    (ex.: Manhã → entrada_manha)    |    +------------------------------------+
+------------------------------------+                    |
                |                                     |
                v                                     v
+------------------------------------+    +------------------------------------+
| 13. Tipo já registrado no dia?     |    | 14. Usar lógica de sequência (B1-B6) |
+------------------------------------+    |    (se período indefinido)         |
                |                        +------------------------------------+
                v                                     |
+------------------------------------+                 |
| 15. Retornar tipo sugerido         |-----------------+
|    (ou próximo tipo na sequência)  |
+------------------------------------+
                |
                v
+------------------------------------+
| 16. Verificar batidas disponíveis  |
|    (máximo 6: B1-B4 regulares, B5-B6 extras) |
+------------------------------------+
                |
                v
+------------------------------------+
| 17. Retornar JSON com tipos disponíveis |
+------------------------------------+
                |
                v
+------------------------------------+
| 18. Usuário seleciona tipo e envia |
|     POST /api/registrar-manual     |
+------------------------------------+
                |
                v
+------------------------------------+
| 19. Validar parâmetros (funcionário, |
|     tipo, horário atual)           |
+------------------------------------+
                |
                v
+------------------------------------+
| 20. Verificar período na tabela    |
|     dia_dados (ex.: Manhã 06:00-11:00) |
+------------------------------------+
                |
                v
+------------------------------------+
| 21. Verificar jornada do funcionário |
|    (ex.: entrada às 08:00)         |
+------------------------------------+
                |
                v
+------------------------------------+
| 22. Batida antecipada? (ex.: antes |
|     de 08:00, mas após 06:00)      |
+------------------------------------+
                |
    +-----------+-----------+
    |                       |
    v                       v
+------------------------------------+    +------------------------------------+
| 23. Ajustar horário para início da |    | 24. Verificar tolerância (15 min)  |
|     jornada (ex.: 07:00 → 08:00)   |    |    (ex.: 08:00-08:15 = Pontual)    |
+------------------------------------+    +------------------------------------+
                |                                     |
                v                                     v
+------------------------------------+    +------------------------------------+
| 25. Batida atrasada ou saída       |<---| 26. Saída antecipada? (ex.: antes  |
|     antecipada? (ex.: após 08:15   |    |     de 18:00)                      |
|     ou antes de 18:00)             |    +------------------------------------+
+------------------------------------+                    |
                |                                     |
                v                                     |
+------------------------------------+                 |
| 27. Oferecer opção de justificativa |-----------------+
+------------------------------------+
                |
    +-----------+-----------+
    |                       |
    v                       v
+------------------------------------+    +------------------------------------+
| 28. Funcionário preenche           |    | 29. Funcionário não justifica      |
|     justificativa?                 |    +------------------------------------+
+------------------------------------+                    |
                |                                     |
                v                                     v
+------------------------------------+    +------------------------------------+
| 30. Salvar justificativa no campo  |    | 31. Marcar como Atrasado           |
|     justificativas                 |    +------------------------------------+
+------------------------------------+                    |
                |                                     |
                +-------------------------------------+
                |
                v
+------------------------------------+
| 32. Calcular status de pontualidade|
|    (Pontual/Atrasado)              |
+------------------------------------+
                |
                v
+------------------------------------+
| 33. Registrar ponto no banco       |
|    (ajustado para jornada, se necessário) |
+------------------------------------+
                |
                v
+------------------------------------+
| 34. Calcular horas trabalhadas     |
|    (dentro de 08:00-18:00, exceto  |
|     intervalo 13:00-14:00)         |
+------------------------------------+
                |
                v
+------------------------------------+
| 35. Retornar resultado (JSON)      |
+------------------------------------+
```