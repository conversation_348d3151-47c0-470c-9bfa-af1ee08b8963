#!/usr/bin/env python3
"""
Debug da estrutura da tabela justificativas_ponto
"""

import subprocess
import sys
from datetime import datetime

def log(message, level="INFO"):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def run_sql_command(sql_command, description):
    """Executa comando SQL no servidor"""
    try:
        log(f"Executando: {description}")
        
        # Comando SSH para executar SQL
        ssh_command = [
            "ssh", "root@************",
            f"mysql -u root -p@Ric6109 controle_ponto -e \"{sql_command}\""
        ]
        
        result = subprocess.run(
            ssh_command,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            log(f"✅ {description} - Sucesso")
            print("=" * 60)
            print(result.stdout)
            print("=" * 60)
            return True, result.stdout
        else:
            log(f"❌ {description} - Erro: {result.stderr}", "ERROR")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        log(f"⏰ {description} - Timeout", "ERROR")
        return False, "Timeout"
    except Exception as e:
        log(f"❌ {description} - Exceção: {e}", "ERROR")
        return False, str(e)

def main():
    log("=== DEBUG TABELA JUSTIFICATIVAS ===")
    
    # 1. Verificar estrutura da tabela
    sql_estrutura = "DESCRIBE justificativas_ponto;"
    run_sql_command(sql_estrutura, "Verificando estrutura da tabela justificativas_ponto")
    
    # 2. Verificar se existem registros
    sql_count = "SELECT COUNT(*) as total FROM justificativas_ponto;"
    run_sql_command(sql_count, "Contando registros na tabela")
    
    # 3. Verificar registros do funcionário 35
    sql_funcionario = """
    SELECT 
        id, funcionario_id, data_registro, tipo_justificativa, 
        motivo, status_aprovacao, criado_em 
    FROM justificativas_ponto 
    WHERE funcionario_id = 35 
    ORDER BY criado_em DESC 
    LIMIT 5;
    """
    run_sql_command(sql_funcionario, "Verificando registros do funcionário 35")
    
    # 4. Verificar documentos anexados para o funcionário 35
    sql_documentos = """
    SELECT 
        dp.id, dp.registro_ponto_id, dp.nome_original, dp.tipo_documento,
        rp.funcionario_id, DATE(rp.data_hora) as data_registro
    FROM documentos_ponto dp
    JOIN registros_ponto rp ON dp.registro_ponto_id = rp.id
    WHERE rp.funcionario_id = 35
    AND dp.ativo = 1
    ORDER BY dp.data_upload DESC
    LIMIT 5;
    """
    run_sql_command(sql_documentos, "Verificando documentos anexados do funcionário 35")
    
    # 5. Verificar registros de ponto do funcionário 35 na data específica
    sql_registros = """
    SELECT 
        id, funcionario_id, DATE(data_hora) as data_registro, 
        TIME(data_hora) as hora, tipo_registro
    FROM registros_ponto 
    WHERE funcionario_id = 35 
    AND DATE(data_hora) = '2025-07-14'
    ORDER BY data_hora ASC;
    """
    run_sql_command(sql_registros, "Verificando registros de ponto do funcionário 35 em 2025-07-14")
    
    # 6. Verificar se há justificativas para a data específica
    sql_justificativas_data = """
    SELECT 
        id, funcionario_id, data_registro, tipo_justificativa, 
        motivo, status_aprovacao, criado_em 
    FROM justificativas_ponto 
    WHERE funcionario_id = 35 
    AND data_registro = '2025-07-14';
    """
    run_sql_command(sql_justificativas_data, "Verificando justificativas do funcionário 35 em 2025-07-14")

if __name__ == "__main__":
    main()
