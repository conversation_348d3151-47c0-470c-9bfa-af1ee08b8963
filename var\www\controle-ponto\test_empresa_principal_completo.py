#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste completo da funcionalidade de empresa principal
"""

import requests
import sys
import json

# Configurações
BASE_URL = "http://************"
LOGIN_URL = f"{BASE_URL}/login"
EMPRESA_PRINCIPAL_URL = f"{BASE_URL}/empresa-principal"
DEFINIR_PRINCIPAL_URL = f"{BASE_URL}/empresa-principal/definir-principal"

def test_empresa_principal():
    """Teste completo da funcionalidade"""
    
    # Criar sessão para manter cookies
    session = requests.Session()
    
    print("🔍 Testando funcionalidade de empresa principal...")
    
    # 1. Fazer login
    print("\n1. Fazendo login...")
    login_data = {
        'usuario': 'admin',
        'senha': 'admin123'
    }
    
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    print(f"   Status do login: {response.status_code}")
    
    if response.status_code not in [200, 302]:
        print("❌ Falha no login")
        return False
    
    # 2. Acessar página de empresa principal
    print("\n2. Acessando página de empresa principal...")
    response = session.get(EMPRESA_PRINCIPAL_URL)
    print(f"   Status da página: {response.status_code}")
    
    if response.status_code != 200:
        print("❌ Falha ao acessar página de empresa principal")
        return False
    
    # Verificar se a página contém o conteúdo esperado
    content = response.text
    print(f"   Tamanho da resposta: {len(content)} caracteres")

    # Debug: mostrar parte do conteúdo
    if len(content) > 500:
        print(f"   Início da página: {content[:500]}...")
    else:
        print(f"   Conteúdo completo: {content}")

    if "Empresas Cadastradas" in content or "empresa-principal" in content.lower():
        print("✅ Página carregada corretamente")
    else:
        print("❌ Página não contém o conteúdo esperado")
        # Não retornar False ainda, vamos continuar para ver mais detalhes
    
    # 3. Verificar se há empresas para selecionar
    if "Renovar Construcao Civil Ltda" in content:
        print("✅ Empresa encontrada na página")
    else:
        print("❌ Empresa não encontrada na página")
        return False
    
    # 4. Tentar definir empresa como principal
    print("\n3. Definindo empresa como principal...")
    definir_data = {
        'empresa_id': '4'  # ID da empresa Renovar Construcao Civil Ltda
    }
    
    response = session.post(DEFINIR_PRINCIPAL_URL, data=definir_data, allow_redirects=False)
    print(f"   Status da definição: {response.status_code}")
    
    if response.status_code in [200, 302]:
        print("✅ Empresa definida como principal com sucesso")
    else:
        print("❌ Falha ao definir empresa como principal")
        return False
    
    # 5. Verificar se a empresa foi definida como principal
    print("\n4. Verificando se empresa foi definida como principal...")
    response = session.get(EMPRESA_PRINCIPAL_URL)
    
    if response.status_code == 200:
        content = response.text
        # Se a empresa foi definida como principal, a página deve mostrar o dashboard
        if "Dashboard da Empresa Principal" in content or "Renovar Construcao Civil Ltda" in content:
            print("✅ Empresa principal definida com sucesso!")
            return True
        else:
            print("⚠️ Página ainda mostra seleção de empresa")
            return False
    else:
        print("❌ Falha ao verificar status")
        return False

if __name__ == "__main__":
    try:
        success = test_empresa_principal()
        if success:
            print("\n🎉 Teste completo: SUCESSO!")
            sys.exit(0)
        else:
            print("\n❌ Teste completo: FALHA!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Erro durante o teste: {e}")
        sys.exit(1)
