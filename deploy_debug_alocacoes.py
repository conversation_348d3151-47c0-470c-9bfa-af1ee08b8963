#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import paramiko
import os
import sys

def deploy_debug_alocacoes():
    """Deploy das correções de debug para o servidor"""
    
    # Configurações do servidor
    hostname = "************"
    username = "root"
    password = "@Ric6109"
    
    # Arquivos para fazer upload
    arquivos_locais = [
        "var/www/controle-ponto/app_empresa_principal.py"
    ]
    
    try:
        print("🚀 INICIANDO DEPLOY DE DEBUG PARA ALOCAÇÕES")
        print("=" * 50)
        
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        # Configurar SFTP
        sftp = ssh.open_sftp()
        
        print(f"✅ Conectado ao servidor {hostname}")
        
        # Upload dos arquivos
        for arquivo_local in arquivos_locais:
            if os.path.exists(arquivo_local):
                arquivo_remoto = f"/var/www/controle-ponto/{os.path.basename(arquivo_local)}"
                
                print(f"📤 Enviando {arquivo_local} -> {arquivo_remoto}")
                sftp.put(arquivo_local, arquivo_remoto)
                print(f"   ✅ Arquivo enviado com sucesso")
            else:
                print(f"   ❌ Arquivo local não encontrado: {arquivo_local}")
        
        # Reiniciar o serviço
        print(f"\n🔄 Reiniciando serviço Flask...")
        stdin, stdout, stderr = ssh.exec_command("systemctl restart controle-ponto")
        
        # Aguardar um pouco
        import time
        time.sleep(3)
        
        # Verificar status
        stdin, stdout, stderr = ssh.exec_command("systemctl status controle-ponto --no-pager")
        status_output = stdout.read().decode()
        
        if "active (running)" in status_output:
            print(f"   ✅ Serviço reiniciado com sucesso")
        else:
            print(f"   ⚠️ Status do serviço:")
            print(f"   {status_output}")
        
        # Fechar conexões
        sftp.close()
        ssh.close()
        
        print(f"\n✅ DEPLOY CONCLUÍDO!")
        print(f"🔍 Agora teste novamente a página de alocações")
        print(f"📋 Os logs detalhados aparecerão no arquivo de log do servidor")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no deploy: {e}")
        return False

if __name__ == "__main__":
    deploy_debug_alocacoes()
