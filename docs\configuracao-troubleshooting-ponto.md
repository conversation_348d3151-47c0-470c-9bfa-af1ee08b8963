# 🔧 CONFIGURAÇÃO E TROUBLESHOOTING - SISTEMA DE PONTO

**Data:** 12/07/2025  
**Versão:** 2.0 - Sistema Anti-Malandragem + Lógica Justa  

---

## 📋 **CONFIGURAÇÃO INICIAL**

### **1. Configurar Jornada do Funcionário**

#### **Via SQL:**
```sql
-- Configurar jornada oficial
UPDATE funcionarios 
SET 
    jornada_seg_qui_entrada = '09:00:00',
    jornada_seg_qui_saida = '18:00:00',
    jornada_intervalo_entrada = '12:00:00',
    jornada_intervalo_saida = '13:00:00'
WHERE id = 26;

-- Verificar configuração
SELECT 
    id, 
    nome_completo, 
    jornada_seg_qui_entrada, 
    jornada_seg_qui_saida, 
    jornada_intervalo_entrada, 
    jornada_intervalo_saida 
FROM funcionarios 
WHERE id = 26;
```

#### **Via Interface (Futuro):**
- Acessar cadastro do funcionário
- Configurar horários oficiais
- Salvar alterações

### **2. Verificar Configuração do Sistema**

#### **Arquivos Importantes:**
```bash
/var/www/controle-ponto/app_ponto_admin.py    # Lógica principal
/var/www/controle-ponto/utils/database.py     # Conexão banco
/var/www/controle-ponto/templates/ponto_admin/ # Interface
```

#### **Serviço:**
```bash
# Status do serviço
sudo systemctl status controle-ponto

# Reiniciar serviço
sudo systemctl restart controle-ponto

# Ver logs em tempo real
sudo journalctl -u controle-ponto -f
```

---

## 🚨 **TROUBLESHOOTING COMUM**

### **Problema 1: Dados não aparecem na interface**

#### **Sintomas:**
- Interface mostra "No data available in table"
- Registros existem no banco mas não aparecem

#### **Diagnóstico:**
```bash
# Verificar logs de erro
sudo journalctl -u controle-ponto | grep ERROR

# Testar função diretamente
cd /var/www/controle-ponto
python3 -c "
from app_ponto_admin import get_registros_ponto_funcionario
registros = get_registros_ponto_funcionario(26)
print(f'Registros encontrados: {len(registros)}')
"
```

#### **Soluções:**
1. **Reiniciar serviço:**
   ```bash
   sudo systemctl restart controle-ponto
   ```

2. **Verificar conexão banco:**
   ```bash
   mysql -u root -p'rlponto2024!' controle_ponto -e "SELECT COUNT(*) FROM registros_ponto WHERE funcionario_id = 26;"
   ```

3. **Limpar cache navegador:**
   - Ctrl+F5 (hard refresh)
   - Ou aguardar sistema anti-cache funcionar

### **Problema 2: Cálculos incorretos**

#### **Sintomas:**
- Horas calculadas não batem com expectativa
- Malandragems não sendo bloqueadas
- Trabalho parcial não reconhecido

#### **Diagnóstico:**
```bash
# Verificar jornada do funcionário
mysql -u root -p'rlponto2024!' controle_ponto -e "
SELECT id, nome_completo, jornada_seg_qui_entrada, jornada_seg_qui_saida 
FROM funcionarios WHERE id = 26;
"

# Verificar logs de cálculo
sudo journalctl -u controle-ponto | grep "INICIANDO get_registros"
```

#### **Soluções:**
1. **Configurar jornada oficial:**
   ```sql
   UPDATE funcionarios SET 
   jornada_seg_qui_entrada = '09:00:00',
   jornada_seg_qui_saida = '18:00:00'
   WHERE id = 26;
   ```

2. **Verificar sequência de batidas:**
   ```sql
   SELECT 
       DATE(data_hora) as data,
       tipo_registro,
       TIME(data_hora) as hora
   FROM registros_ponto 
   WHERE funcionario_id = 26 
   ORDER BY data_hora;
   ```

### **Problema 3: Sistema muito rigoroso**

#### **Sintomas:**
- Funcionários reclamando de cálculos injustos
- Trabalho real não sendo reconhecido

#### **Diagnóstico:**
```python
# Testar caso específico
from app_ponto_admin import get_registros_ponto_funcionario
registros = get_registros_ponto_funcionario(26)
for r in registros:
    if '2025-07-11' in str(r.get('data')):
        print(f"Horas normais: {r.get('horas_normais')}")
        print(f"Alertas: {r.get('alertas_ajustes_jornada')}")
```

#### **Soluções:**
1. **Verificar lógica justa ativa:**
   - Função `aplicar_regra_rigorosa_com_jornada()` deve reconhecer trabalho parcial

2. **Ajustar tolerâncias se necessário:**
   ```python
   # Em app_ponto_admin.py, ajustar lógica se muito rigorosa
   ```

### **Problema 4: Performance lenta**

#### **Sintomas:**
- Interface demora para carregar
- Timeout em requisições

#### **Diagnóstico:**
```bash
# Verificar uso de recursos
top
htop

# Verificar logs de performance
sudo journalctl -u controle-ponto | grep "Timestamp"
```

#### **Soluções:**
1. **Otimizar queries:**
   ```sql
   -- Adicionar índices se necessário
   CREATE INDEX idx_registros_funcionario_data 
   ON registros_ponto(funcionario_id, data_hora);
   ```

2. **Limitar período de busca:**
   ```python
   # Buscar apenas últimos 30 dias por padrão
   data_inicio = datetime.now() - timedelta(days=30)
   ```

---

## 🔍 **COMANDOS DE DEBUG**

### **Verificar Estado do Sistema:**
```bash
# Status geral
sudo systemctl status controle-ponto

# Logs recentes
sudo journalctl -u controle-ponto -n 50

# Logs em tempo real
sudo journalctl -u controle-ponto -f

# Verificar processo
ps aux | grep python3
```

### **Testar Funções Específicas:**
```bash
cd /var/www/controle-ponto

# Testar busca de registros
python3 -c "
from app_ponto_admin import get_registros_ponto_funcionario
print('Testando função principal...')
registros = get_registros_ponto_funcionario(26)
print(f'Registros: {len(registros)}')
for r in registros[:3]:
    print(f'{r.get(\"data\")}: {r.get(\"horas_normais\")}h')
"

# Testar conexão banco
python3 -c "
from utils.database import DatabaseManager
print('Testando conexão banco...')
db = DatabaseManager()
conn = db.get_connection()
print('Conexão OK!')
conn.close()
"
```

### **Verificar Dados no Banco:**
```sql
-- Verificar registros recentes
SELECT 
    DATE(data_hora) as data,
    tipo_registro,
    TIME(data_hora) as hora
FROM registros_ponto 
WHERE funcionario_id = 26 
AND data_hora >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY data_hora;

-- Verificar jornada configurada
SELECT 
    id, nome_completo,
    jornada_seg_qui_entrada,
    jornada_seg_qui_saida
FROM funcionarios 
WHERE id = 26;

-- Contar registros por dia
SELECT 
    DATE(data_hora) as data,
    COUNT(*) as total_batidas
FROM registros_ponto 
WHERE funcionario_id = 26
GROUP BY DATE(data_hora)
ORDER BY data DESC;
```

---

## 📊 **MONITORAMENTO CONTÍNUO**

### **Alertas Importantes:**
```bash
# Verificar erros críticos
sudo journalctl -u controle-ponto | grep "ERROR" | tail -10

# Verificar warnings
sudo journalctl -u controle-ponto | grep "WARNING" | tail -10

# Verificar se regra rigorosa está ativa
sudo journalctl -u controle-ponto | grep "Regra rigorosa" | tail -5
```

### **Métricas de Saúde:**
```python
# Script de monitoramento (executar periodicamente)
def verificar_saude_sistema():
    try:
        from app_ponto_admin import get_registros_ponto_funcionario
        
        # Testar função principal
        registros = get_registros_ponto_funcionario(26)
        
        if len(registros) > 0:
            print("✅ Sistema funcionando")
            return True
        else:
            print("⚠️ Sistema sem dados")
            return False
            
    except Exception as e:
        print(f"❌ Sistema com erro: {e}")
        return False
```

---

## 🔄 **PROCEDIMENTOS DE MANUTENÇÃO**

### **Backup Antes de Alterações:**
```bash
# Backup do código
cp -r /var/www/controle-ponto /backup/controle-ponto-$(date +%Y%m%d)

# Backup do banco
mysqldump -u root -p'rlponto2024!' controle_ponto > /backup/controle_ponto_$(date +%Y%m%d).sql
```

### **Deploy de Atualizações:**
```bash
# 1. Parar serviço
sudo systemctl stop controle-ponto

# 2. Fazer backup
cp app_ponto_admin.py app_ponto_admin.py.backup

# 3. Aplicar alterações
# (copiar novos arquivos)

# 4. Reiniciar serviço
sudo systemctl start controle-ponto

# 5. Verificar funcionamento
sudo systemctl status controle-ponto
```

### **Rollback em Caso de Problema:**
```bash
# 1. Parar serviço
sudo systemctl stop controle-ponto

# 2. Restaurar backup
cp app_ponto_admin.py.backup app_ponto_admin.py

# 3. Reiniciar serviço
sudo systemctl start controle-ponto

# 4. Verificar se voltou ao normal
sudo journalctl -u controle-ponto -n 20
```

---

## 📞 **CONTATOS DE SUPORTE**

**Sistema implementado por:** Augment Agent  
**Data de implementação:** 12/07/2025  
**Versão atual:** 2.0 - Anti-Malandragem + Lógica Justa  

**Para suporte:**
1. Consultar esta documentação
2. Verificar logs do sistema
3. Executar testes de validação
4. Contatar desenvolvedor se necessário

---

**⚠️ IMPORTANTE:** Sempre fazer backup antes de alterações no sistema de produção!
