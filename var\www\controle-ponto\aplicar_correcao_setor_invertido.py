#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para aplicar a correção da view vw_relatorio_pontos para priorizar o campo setor_obra.
"""

import os
import logging
import pymysql
from datetime import datetime

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Dados de conexão com o banco de dados de produção
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

def aplicar_correcao_invertido():
    """
    Aplica o script SQL corrigido que prioriza o campo setor_obra na view vw_relatorio_pontos.
    """
    try:
        # Conectar ao banco de dados
        logger.info(f"Conectando ao servidor de produção {DB_CONFIG['host']}...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        logger.info("✓ Conectado ao banco de dados")
        
        # Primeiro forçar a remoção da view existente
        logger.info("Removendo view existente...")
        try:
            cursor.execute("DROP VIEW IF EXISTS vw_relatorio_pontos")
            conn.commit()
            logger.info("✓ View removida com sucesso")
        except Exception as e:
            logger.error(f"Erro ao remover view: {str(e)}")
            # Continuar mesmo se der erro
        
        # Definição da view corrigida - priorizar setor_obra
        create_view_sql = """
        CREATE VIEW vw_relatorio_pontos AS
        SELECT 
            rp.id,
            rp.funcionario_id,
            f.nome_completo,
            f.matricula_empresa,
            f.cpf,
            rp.data_hora,
            DATE(rp.data_hora) as data_registro,
            TIME(rp.data_hora) as hora_registro,
            rp.tipo_registro,
            CASE rp.tipo_registro
                WHEN 'entrada_manha' THEN 'Entrada Manhã'
                WHEN 'saida_almoco' THEN 'Saída Almoço'
                WHEN 'entrada_tarde' THEN 'Entrada Tarde'
                WHEN 'saida' THEN 'Saída'
            END AS tipo_descricao,
            rp.metodo_registro,
            CASE 
                WHEN rp.metodo_registro = 'biometrico' THEN 'Biométrico'
                ELSE 'Manual'
            END AS metodo_descricao,
            -- Correção da ordem: primeiro verificar setor_obra (campo Setor/Obra na interface), depois setor
            -- Isso garante que o valor correto cadastrado no formulário seja exibido
            COALESCE(f.setor_obra, f.setor, 'Não informado') as setor,
            f.cargo,
            COALESCE(f.empresa, 'Não informado') as empresa,
            rp.qualidade_biometria,
            rp.observacoes,
            rp.ip_origem,
            rp.criado_em,
            u.usuario as criado_por_usuario,
            CASE 
                WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atraso'
                WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atraso'
                ELSE 'Pontual'
            END AS status_pontualidade
        FROM registros_ponto rp
        INNER JOIN funcionarios f ON rp.funcionario_id = f.id
        LEFT JOIN usuarios u ON rp.criado_por = u.id
        WHERE f.status_cadastro = 'Ativo'
        ORDER BY rp.data_hora DESC
        """
        
        # Criar a view com a prioridade invertida
        logger.info("Criando view com prioridade corrigida...")
        cursor.execute(create_view_sql)
        conn.commit()
        logger.info("✓ View criada com sucesso")
        
        # Verificar a view
        cursor.execute("SHOW CREATE VIEW vw_relatorio_pontos")
        view_def = cursor.fetchone()
        
        if view_def:
            logger.info("✅ View vw_relatorio_pontos confirmada")
        else:
            logger.warning("⚠️ View não foi encontrada após a execução do script")
        
        # Verificar o resultado da correção - buscar o funcionário Richardson
        cursor.execute("""
            SELECT id, nome_completo, setor 
            FROM vw_relatorio_pontos 
            WHERE nome_completo LIKE '%RICHARDSON%'
            LIMIT 1
        """)
        
        registro = cursor.fetchone()
        
        if registro and len(registro) >= 3:
            logger.info(f"\n✅ VERIFICAÇÃO: Funcionário {registro[1]} agora tem setor = {registro[2]}")
            
            # Verificar se o campo setor_obra está sendo priorizado corretamente
            cursor.execute("""
                SELECT 
                    f.id, 
                    f.nome_completo, 
                    f.setor,
                    f.setor_obra,
                    v.setor as setor_na_view
                FROM funcionarios f
                JOIN vw_relatorio_pontos v ON f.id = v.funcionario_id
                WHERE f.nome_completo LIKE '%RICHARDSON%'
                LIMIT 1
            """)
            
            dados_completos = cursor.fetchone()
            
            if dados_completos:
                setor = dados_completos[2] or 'NULL'
                setor_obra = dados_completos[3] or 'NULL' 
                setor_na_view = dados_completos[4] or 'NULL'
                
                logger.info(f"\n==== DADOS DO FUNCIONÁRIO ====")
                logger.info(f"Campo setor: {setor}")
                logger.info(f"Campo setor_obra: {setor_obra}")
                logger.info(f"Valor exibido na view: {setor_na_view}")
                
                if setor_obra != 'NULL' and setor_na_view == setor_obra:
                    logger.info("✅ CORRETO! O campo setor_obra está sendo priorizado na view.")
                elif setor != 'NULL' and setor_na_view == setor and setor_obra == 'NULL':
                    logger.info("✅ CORRETO! Como não há setor_obra, está usando setor.")
                else:
                    logger.error("❌ A priorização dos campos não está funcionando corretamente.")
        
        # Fechar a conexão
        cursor.close()
        conn.close()
        logger.info("✓ Conexão com o banco de dados fechada")
        
        return True
    except Exception as e:
        logger.error(f"❌ Erro ao aplicar correção: {str(e)}")
        logger.error("Detalhes:", exc_info=True)
        return False

if __name__ == "__main__":
    logger.info("=== INÍCIO DO SCRIPT DE CORREÇÃO DA VIEW vw_relatorio_pontos ===")
    logger.info(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    
    # Aplicar a correção invertendo a prioridade dos campos
    sucesso = aplicar_correcao_invertido()
    
    if sucesso:
        logger.info("✅ CORREÇÃO APLICADA COM SUCESSO!")
        logger.info("A view vw_relatorio_pontos foi atualizada para priorizar o campo setor_obra.")
    else:
        logger.error("❌ FALHA AO APLICAR CORREÇÃO!")
        
    logger.info("=== FIM DO SCRIPT DE CORREÇÃO ===") 