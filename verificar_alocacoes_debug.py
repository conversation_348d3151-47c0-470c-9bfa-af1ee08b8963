#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'var', 'www', 'controle-ponto'))

from utils.database import DatabaseManager
import traceback

def verificar_tabelas_alocacao():
    """Verificar se as tabelas necessárias existem e sua estrutura"""
    print("🔍 VERIFICANDO TABELAS PARA ALOCAÇÕES")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar se a tabela funcionario_alocacoes existe
        print("\n1. Verificando tabela funcionario_alocacoes...")
        try:
            sql_check = "SHOW TABLES LIKE 'funcionario_alocacoes'"
            result = db.execute_query(sql_check)
            
            if result:
                print("   ✅ Tabela funcionario_alocacoes EXISTE")
                
                # Verificar estrutura
                sql_desc = "DESCRIBE funcionario_alocacoes"
                estrutura = db.execute_query(sql_desc)
                
                print("   📋 Estrutura da tabela:")
                for campo in estrutura:
                    print(f"      - {campo['Field']}: {campo['Type']} (Null: {campo['Null']})")
                    
            else:
                print("   ❌ Tabela funcionario_alocacoes NÃO EXISTE")
                return False
                
        except Exception as e:
            print(f"   ❌ Erro ao verificar tabela: {e}")
            return False
        
        # 2. Verificar outras tabelas necessárias
        tabelas_necessarias = ['funcionarios', 'empresas', 'jornadas_trabalho', 'empresa_clientes']
        
        print(f"\n2. Verificando outras tabelas necessárias...")
        for tabela in tabelas_necessarias:
            try:
                sql_check = f"SHOW TABLES LIKE '{tabela}'"
                result = db.execute_query(sql_check)
                
                if result:
                    print(f"   ✅ {tabela}: EXISTE")
                else:
                    print(f"   ❌ {tabela}: NÃO EXISTE")
                    
            except Exception as e:
                print(f"   ❌ Erro ao verificar {tabela}: {e}")
        
        # 3. Testar a consulta que está falhando
        print(f"\n3. Testando consulta de alocações...")
        try:
            sql_test = """
            SELECT fa.*,
                   f.nome_completo as nome, f.cargo, f.cpf, f.telefone1 as telefone,
                   e.razao_social, e.nome_fantasia, e.cnpj,
                   jt.nome_jornada as jornada_nome, NULL as carga_horaria,
                   ec.nome_contrato, ec.codigo_contrato
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            JOIN empresas e ON fa.empresa_cliente_id = e.id
            JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
            LEFT JOIN empresa_clientes ec ON fa.contrato_id = ec.id
            ORDER BY fa.created_at DESC
            LIMIT 5
            """
            
            result = db.execute_query(sql_test)
            print(f"   ✅ Consulta executada com sucesso!")
            print(f"   📊 Registros encontrados: {len(result) if result else 0}")
            
            if result:
                print("   📋 Primeiro registro:")
                primeiro = result[0]
                for key, value in primeiro.items():
                    print(f"      - {key}: {value}")
            
        except Exception as e:
            print(f"   ❌ Erro na consulta: {e}")
            print(f"   📋 Traceback: {traceback.format_exc()}")
            
            # Tentar consulta mais simples
            print(f"\n   🔄 Tentando consulta mais simples...")
            try:
                sql_simples = "SELECT * FROM funcionario_alocacoes LIMIT 1"
                result_simples = db.execute_query(sql_simples)
                print(f"   ✅ Consulta simples funcionou! Registros: {len(result_simples) if result_simples else 0}")
                
                if result_simples:
                    print("   📋 Campos disponíveis:")
                    for key in result_simples[0].keys():
                        print(f"      - {key}")
                        
            except Exception as e2:
                print(f"   ❌ Erro na consulta simples: {e2}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    verificar_tabelas_alocacao()
