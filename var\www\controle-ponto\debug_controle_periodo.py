#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de debug para controle de período
Sistema: RLPONTO-WEB v1.0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Testa todas as importações do módulo"""
    print("🔍 TESTANDO IMPORTAÇÕES...")
    
    try:
        from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
        print("✅ Flask imports OK")
    except Exception as e:
        print(f"❌ Flask imports: {e}")
        return False
    
    try:
        from datetime import datetime, date, timedelta
        print("✅ datetime imports OK")
    except Exception as e:
        print(f"❌ datetime imports: {e}")
        return False
    
    try:
        from utils.auth import require_login, require_admin
        print("✅ utils.auth imports OK")
    except Exception as e:
        print(f"❌ utils.auth imports: {e}")
        return False
    
    try:
        from utils.database import get_db_connection
        print("✅ utils.database imports OK")
    except Exception as e:
        print(f"❌ utils.database imports: {e}")
        return False
    
    try:
        from pymysql.cursors import DictCursor
        print("✅ pymysql imports OK")
    except Exception as e:
        print(f"❌ pymysql imports: {e}")
        return False
    
    try:
        import logging
        print("✅ logging imports OK")
    except Exception as e:
        print(f"❌ logging imports: {e}")
        return False
    
    try:
        from functools import wraps
        print("✅ functools imports OK")
    except Exception as e:
        print(f"❌ functools imports: {e}")
        return False
    
    return True

def test_database_connection():
    """Testa conexão com banco de dados"""
    print("\n🔍 TESTANDO CONEXÃO COM BANCO...")
    
    try:
        from utils.database import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        print("✅ Conexão com banco OK")
        return True
    except Exception as e:
        print(f"❌ Conexão com banco: {e}")
        return False

def test_functions():
    """Testa as funções principais do módulo"""
    print("\n🔍 TESTANDO FUNÇÕES PRINCIPAIS...")
    
    try:
        # Simular importação das funções
        sys.path.append('/var/www/controle-ponto')
        
        # Testar função de cálculo de período
        from datetime import date
        hoje = date.today()
        
        # Calcular período 21-20
        if hoje.day >= 21:
            inicio = date(hoje.year, hoje.month, 21)
            if hoje.month == 12:
                fim = date(hoje.year + 1, 1, 20)
            else:
                fim = date(hoje.year, hoje.month + 1, 20)
        else:
            if hoje.month == 1:
                inicio = date(hoje.year - 1, 12, 21)
            else:
                inicio = date(hoje.year, hoje.month - 1, 21)
            fim = date(hoje.year, hoje.month, 20)
        
        print(f"✅ Cálculo de período OK: {inicio} a {fim}")
        return True
        
    except Exception as e:
        print(f"❌ Funções principais: {e}")
        return False

def test_module_import():
    """Testa importação do módulo completo"""
    print("\n🔍 TESTANDO IMPORTAÇÃO DO MÓDULO...")
    
    try:
        from app_controle_periodo import controle_periodo_bp
        print("✅ Módulo app_controle_periodo importado OK")
        print(f"   Blueprint name: {controle_periodo_bp.name}")
        print(f"   URL prefix: {controle_periodo_bp.url_prefix}")
        return True
    except Exception as e:
        print(f"❌ Importação do módulo: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal de diagnóstico"""
    print("=" * 60)
    print("🔧 DIAGNÓSTICO DO CONTROLE DE PERÍODO")
    print("=" * 60)
    
    success = True
    
    # Teste 1: Importações
    if not test_imports():
        success = False
    
    # Teste 2: Banco de dados
    if not test_database_connection():
        success = False
    
    # Teste 3: Funções
    if not test_functions():
        success = False
    
    # Teste 4: Módulo completo
    if not test_module_import():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ TODOS OS TESTES PASSARAM!")
        print("O problema pode estar na sessão ou autenticação.")
    else:
        print("❌ ALGUNS TESTES FALHARAM!")
        print("Verifique os erros acima para identificar o problema.")
    print("=" * 60)

if __name__ == "__main__":
    main()
