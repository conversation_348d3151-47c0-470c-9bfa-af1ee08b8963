#!/usr/bin/env python3
"""
Script para configurar jornadas padrão nas empresas
Sistema RLPONTO-WEB
Data: 05/07/2025
"""

import sys
import os

# Adicionar o caminho do projeto
sys.path.append('/var/www/controle-ponto')

try:
    from utils.database import DatabaseManager
    print("✅ Módulo DatabaseManager importado com sucesso")
except ImportError as e:
    print(f"❌ Erro ao importar DatabaseManager: {e}")
    print("Tentando importação alternativa...")
    
    # Importação alternativa usando mysql.connector diretamente
    import mysql.connector
    
    def conectar_db():
        return mysql.connector.connect(
            host='************',
            user='cavalcrod',
            password='200381',
            database='controle_ponto'
        )
    
    class DatabaseManager:
        @staticmethod
        def execute_query(sql, params=None, fetch_one=False, fetch_all=True):
            conn = conectar_db()
            cursor = conn.cursor(dictionary=True)
            cursor.execute(sql, params or ())
            if fetch_one:
                result = cursor.fetchone()
            elif fetch_all:
                result = cursor.fetchall()
            else:
                result = cursor.rowcount
            cursor.close()
            conn.close()
            return result

def configurar_jornadas_empresas():
    """Configurar jornadas padrão para todas as empresas"""
    try:
        print("🚀 Iniciando configuração de jornadas para empresas...")
        
        # 1. VERIFICAR EMPRESAS EXISTENTES
        print("\n📋 1. Verificando empresas existentes...")
        sql_empresas = """
        SELECT id, razao_social, nome_fantasia 
        FROM empresas 
        WHERE ativa = TRUE
        ORDER BY razao_social
        """
        empresas = DatabaseManager.execute_query(sql_empresas)
        
        print(f"   ✅ Encontradas {len(empresas)} empresas ativas")
        for empresa in empresas:
            print(f"   - ID {empresa['id']}: {empresa['razao_social']}")
        
        # 2. VERIFICAR JORNADAS EXISTENTES
        print("\n🔍 2. Verificando jornadas existentes...")
        sql_jornadas = """
        SELECT j.id, j.empresa_id, j.nome_jornada, j.padrao, e.razao_social
        FROM jornadas_trabalho j
        JOIN empresas e ON j.empresa_id = e.id
        WHERE j.ativa = TRUE
        ORDER BY j.empresa_id, j.padrao DESC
        """
        jornadas_existentes = DatabaseManager.execute_query(sql_jornadas)
        
        print(f"   ✅ Encontradas {len(jornadas_existentes)} jornadas ativas")
        
        # Agrupar por empresa
        jornadas_por_empresa = {}
        for jornada in jornadas_existentes:
            emp_id = jornada['empresa_id']
            if emp_id not in jornadas_por_empresa:
                jornadas_por_empresa[emp_id] = []
            jornadas_por_empresa[emp_id].append(jornada)
        
        # 3. RENOMEAR JORNADAS PADRÃO PARA "PRIMEIRO TURNO"
        print("\n🔄 3. Renomeando jornadas padrão para 'Primeiro Turno'...")
        sql_renomear = """
        UPDATE jornadas_trabalho 
        SET nome_jornada = 'Primeiro Turno'
        WHERE padrao = TRUE AND nome_jornada != 'Primeiro Turno'
        """
        resultado_renomear = DatabaseManager.execute_query(sql_renomear, fetch_all=False)
        print(f"   ✅ {resultado_renomear} jornadas renomeadas para 'Primeiro Turno'")
        
        # 4. CRIAR "SEGUNDO TURNO" PARA EMPRESAS QUE NÃO TÊM
        print("\n➕ 4. Criando 'Segundo Turno' para empresas...")
        
        for empresa in empresas:
            empresa_id = empresa['id']
            
            # Verificar se já existe segundo turno
            sql_verificar_segundo = """
            SELECT id FROM jornadas_trabalho 
            WHERE empresa_id = %s AND nome_jornada = 'Segundo Turno' AND ativa = TRUE
            """
            segundo_existente = DatabaseManager.execute_query(sql_verificar_segundo, (empresa_id,))
            
            if segundo_existente:
                print(f"   ⚠️  Empresa {empresa['razao_social']} já tem 'Segundo Turno' - pulando")
                continue
            
            # Criar segundo turno
            sql_criar_segundo = """
            INSERT INTO jornadas_trabalho (
                empresa_id, nome_jornada, descricao, tipo_jornada, categoria_funcionario,
                seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
                sabado_entrada, sabado_saida, intervalo_inicio, intervalo_fim,
                intervalo_duracao_minutos, tolerancia_entrada_minutos, 
                ativa, padrao, ordem_exibicao, cadastrado_por
            ) VALUES (
                %s, 'Segundo Turno', 
                'Jornada do segundo turno - horário vespertino/noturno',
                'Diurno', 'Geral',
                '14:00:00', '22:00:00', '14:00:00', '21:00:00',
                NULL, NULL, '18:00:00', '19:00:00',
                60, 15, TRUE, FALSE, 2, 'sistema'
            )
            """
            
            resultado = DatabaseManager.execute_query(sql_criar_segundo, (empresa_id,), fetch_all=False)
            if resultado:
                print(f"   ✅ Criado 'Segundo Turno' para {empresa['razao_social']}")
            else:
                print(f"   ❌ Erro ao criar 'Segundo Turno' para {empresa['razao_social']}")
        
        # 5. GARANTIR QUE EMPRESAS SEM JORNADA TENHAM UMA PADRÃO
        print("\n🏗️ 5. Criando jornada padrão para empresas sem jornadas...")
        
        for empresa in empresas:
            empresa_id = empresa['id']
            
            # Verificar se empresa tem pelo menos uma jornada
            sql_verificar_jornadas = """
            SELECT COUNT(*) as total FROM jornadas_trabalho 
            WHERE empresa_id = %s AND ativa = TRUE
            """
            result = DatabaseManager.execute_query(sql_verificar_jornadas, (empresa_id,), fetch_one=True)
            total_jornadas = result['total'] if result else 0
            
            if total_jornadas == 0:
                print(f"   🆕 Criando jornada padrão para {empresa['razao_social']}")
                
                sql_criar_padrao = """
                INSERT INTO jornadas_trabalho (
                    empresa_id, nome_jornada, descricao, tipo_jornada, categoria_funcionario,
                    seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
                    intervalo_inicio, intervalo_fim, intervalo_duracao_minutos,
                    tolerancia_entrada_minutos, ativa, padrao, ordem_exibicao, cadastrado_por
                ) VALUES (
                    %s, 'Primeiro Turno', 
                    'Jornada padrão da empresa - horário comercial',
                    'Diurno', 'Geral',
                    '08:00:00', '17:00:00', '08:00:00', '16:00:00',
                    '12:00:00', '13:00:00', 60, 15, TRUE, TRUE, 1, 'sistema'
                )
                """
                
                resultado = DatabaseManager.execute_query(sql_criar_padrao, (empresa_id,), fetch_all=False)
                if resultado:
                    print(f"   ✅ Jornada padrão criada para {empresa['razao_social']}")
                else:
                    print(f"   ❌ Erro ao criar jornada padrão para {empresa['razao_social']}")
        
        # 6. VERIFICAR RESULTADO FINAL
        print("\n📊 6. Verificando resultado final...")
        sql_final = """
        SELECT 
            e.id, e.razao_social,
            COUNT(j.id) as total_jornadas,
            SUM(CASE WHEN j.padrao = TRUE THEN 1 ELSE 0 END) as jornadas_padrao
        FROM empresas e
        LEFT JOIN jornadas_trabalho j ON e.id = j.empresa_id AND j.ativa = TRUE
        WHERE e.ativa = TRUE
        GROUP BY e.id, e.razao_social
        ORDER BY e.razao_social
        """
        resultado_final = DatabaseManager.execute_query(sql_final)
        
        print(f"   ✅ Configuração concluída para {len(resultado_final)} empresas:")
        for empresa in resultado_final:
            print(f"      - {empresa['razao_social']}: {empresa['total_jornadas']} jornadas ({empresa['jornadas_padrao']} padrão)")
        
        print("\n🎉 Configuração de jornadas concluída com sucesso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro na configuração: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    configurar_jornadas_empresas()
