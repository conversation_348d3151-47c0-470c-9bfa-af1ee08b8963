# Correção do Erro "Erro ao processar dados do funcionário"

**Data:** 2025-07-08  
**Sistema:** RLPONTO-WEB v1.0  
**Problema:** Erro ao cadastrar funcionários  
**Status:** ✅ CORRIGIDO

---

## 🔍 Análise do Problema

### Problema Identificado
O sistema apresentava erro "Erro ao processar dados do funcionário" ao tentar cadastrar novos funcionários.

### Causa Raiz Encontrada
**INCONSISTÊNCIA NAS TABELAS DE JORNADA:**

O sistema possui duas tabelas para jornadas:
1. `horarios_trabalho` (tabela antiga/simples)
2. `jornadas_trabalho` (tabela nova/completa)

E dois campos na tabela `funcionarios`:
1. `horario_trabalho_id` → referencia `horarios_trabalho`
2. `jornada_trabalho_id` → referencia `jornadas_trabalho`

### Inconsistência no Código
O código estava **misturando as duas tabelas**:

```python
# ❌ ERRO: Buscava em horarios_trabalho
jornadas_empresa = DatabaseManager.execute_query(
    "SELECT id, nome_horario FROM horarios_trabalho WHERE empresa_id = %s AND ativo = 1",
    (empresa_id_int,)
)

# ❌ ERRO: Mas salvava em jornadas_trabalho
jornada_empresa = DatabaseManager.execute_query("""
    SELECT id FROM jornadas_trabalho
    WHERE empresa_id = %s AND padrao = 1 AND ativa = 1
    LIMIT 1
""", (empresa_id,))

# ❌ ERRO: E usava campo horario_trabalho_id
data['horario_trabalho_id'] = jornada_empresa[0]['id']
```

---

## ✅ Correção Implementada

### 1. Padronização para `jornadas_trabalho`
Alterado todo o código para usar **exclusivamente** a tabela `jornadas_trabalho`:

```python
# ✅ CORRETO: Busca em jornadas_trabalho
jornadas_empresa = DatabaseManager.execute_query(
    "SELECT id, nome_jornada FROM jornadas_trabalho WHERE empresa_id = %s AND ativa = 1",
    (empresa_id_int,)
)

# ✅ CORRETO: Salva em jornadas_trabalho
jornada_empresa = DatabaseManager.execute_query("""
    SELECT id FROM jornadas_trabalho
    WHERE empresa_id = %s AND padrao = 1 AND ativa = 1
    LIMIT 1
""", (empresa_id,))

# ✅ CORRETO: Usa campo jornada_trabalho_id
data['jornada_trabalho_id'] = jornada_empresa[0]['id']
```

### 2. Campos Corrigidos
- **Validação:** `horarios_trabalho` → `jornadas_trabalho`
- **Processamento:** `horario_trabalho_id` → `jornada_trabalho_id`
- **Inserção:** `horario_trabalho_id` → `jornada_trabalho_id`
- **Edição:** `horario_trabalho_id` → `jornada_trabalho_id`

### 3. Arquivos Modificados
- `var/www/controle-ponto/app_funcionarios.py`
  - Linhas 1113-1115: Query de validação
  - Linhas 1262-1270: Processamento de novo cadastro
  - Linhas 1275-1279: Fallback sem empresa
  - Linhas 1284-1293: Processamento de edição
  - Linhas 1294-1297: Fallback de erro
  - Linha 1469: Query de inserção
  - Linha 1497: Parâmetro de inserção

---

## 🧪 Verificação da Correção

### Estado do Banco de Dados
```sql
-- Tabelas existentes
horarios_trabalho (5 registros)
jornadas_trabalho (2 registros) ✅ USANDO ESTA

-- Empresas ativas
4  - Msv Engenharia e Construcao
7  - XYZ Engenharia  
9  - GHI Consultoria
10 - JKL Tech
11 - AiNexus Tecnologia

-- Jornadas disponíveis
1 - Jornada Padrão AiNexus (empresa_id: 11, padrao: 1)
2 - Primeiro Turno (empresa_id: 4, padrao: 1)
```

### Deploy Realizado
- ✅ Arquivo `app_funcionarios.py` enviado via SCP
- ✅ Serviço `controle-ponto` reiniciado
- ✅ Sistema online em http://10.19.208.31:5000

---

## 🎯 Resultado Esperado

Após a correção, o sistema deve:

1. **Cadastrar funcionários sem erro**
2. **Associar jornadas automaticamente** baseado na empresa
3. **Exibir mensagem informativa** quando empresa não tem jornada
4. **Usar fallback seguro** para empresas sem jornada

---

## 📋 Teste Recomendado

### Cenário 1: Empresa com Jornada
- Selecionar empresa "AiNexus Tecnologia" (ID: 11)
- Verificar carregamento da "Jornada Padrão AiNexus"
- Cadastrar funcionário
- ✅ Deve funcionar sem erro

### Cenário 2: Empresa sem Jornada
- Selecionar empresa "XYZ Engenharia" (ID: 7)
- Verificar mensagem informativa
- Cadastrar funcionário
- ✅ Deve funcionar com fallback

### Cenário 3: Validação de Campos
- Campo matrícula: readonly ✅
- Campos CTPS: opcionais ✅
- Campo turno: removido ✅

---

## 🔧 Detalhes Técnicos

### Estrutura da Tabela `jornadas_trabalho`
- **Campos principais:** id, empresa_id, nome_jornada, ativa, padrao
- **Horários:** seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida
- **Intervalos:** intervalo_inicio, intervalo_fim, intervalo_duracao_minutos
- **Tolerâncias:** tolerancia_entrada_minutos, tolerancia_saida_minutos

### Relacionamento Correto
```
empresas (id) → jornadas_trabalho (empresa_id)
jornadas_trabalho (id) → funcionarios (jornada_trabalho_id)
```

---

## 📝 Observações

1. **Tabela `horarios_trabalho`:** Mantida para compatibilidade, mas não usada no novo código
2. **Campo `horario_trabalho_id`:** Mantido na tabela funcionarios, mas não usado
3. **Migração:** Futuramente pode ser necessário migrar dados antigos

---

**✅ CORREÇÃO CONCLUÍDA COM SUCESSO**

**Responsável:** Assistente AI  
**Data de Correção:** 2025-07-08  
**Horário:** 10:15  
**Status:** Pronto para teste
