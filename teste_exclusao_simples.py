#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simples para testar a funcionalidade de exclusão de funcionários
Usa SQL direto para evitar problemas de contexto Flask
"""

import sys
import os
import traceback
from datetime import datetime

# Adicionar o diretório do projeto ao path
sys.path.insert(0, '/var/www/controle-ponto')

try:
    from utils.database import DatabaseManager, FuncionarioQueries
    print("✅ Módulos importados com sucesso")
except ImportError as e:
    print(f"❌ Erro ao importar: {e}")
    sys.exit(1)

def criar_funcionario_teste_sql():
    """Cria um funcionário de teste usando SQL direto"""
    timestamp = datetime.now().strftime("%H%M%S")
    
    sql = """
    INSERT INTO funcionarios (
        nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
        ctps_numero, ctps_serie_uf, pis_pasep,
        endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
        telefone1, telefone2, email,
        cargo, setor_obra, matricula_empresa, data_admissao, tipo_contrato,
        nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, status_cadastro,
        horas_semanais_obrigatorias, empresa_id, jornada_trabalho_id,
        digital_dedo1, digital_dedo2, foto_3x4
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s,
        %s, %s, %s, %s, %s,
        %s, %s, %s,
        %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s,
        %s, %s, %s,
        %s, %s, %s
    )
    """
    
    params = (
        'FUNCIONÁRIO TESTE EXCLUSÃO', f'888.777.666-{timestamp[-2:]}', '88.777.666-5',
        '1990-01-01', 'M', 'Solteiro', 'Brasileira',
        '8888888', '888/MG', '888.77666.77-8',
        'RUA TESTE EXCLUSÃO, 888', 'CENTRO', 'BELO HORIZONTE', '30000-000', 'MG',
        '(31) 88888-8888', '(31) 7777-8888', '<EMAIL>',
        'ANALISTA DE TESTE', 'DESENVOLVIMENTO', f'EXCL{timestamp}',
        '2025-01-01', 'CLT',
        'Funcionario', 'Diurno', 5, True, True, 'Ativo',
        44.0, 11, 1,
        None, None, None
    )
    
    try:
        funcionario_id = DatabaseManager.execute_query(sql, params, fetch_all=False)
        if funcionario_id:
            print(f"✅ Funcionário de teste criado com ID: {funcionario_id}")
            return funcionario_id
        else:
            print("❌ Falha ao criar funcionário de teste")
            return None
    except Exception as e:
        print(f"❌ Erro ao criar funcionário de teste: {e}")
        return None

def testar_exclusao_funcionario(funcionario_id):
    """Testa a exclusão/desligamento de funcionário"""
    try:
        print(f"\n🔍 Testando exclusão do funcionário ID: {funcionario_id}")
        
        # Verificar se funcionário existe antes da exclusão
        funcionario_antes = FuncionarioQueries.get_by_id(funcionario_id)
        if not funcionario_antes:
            print("❌ Funcionário não encontrado antes da exclusão")
            return False
            
        print(f"📊 Funcionário encontrado: {funcionario_antes['nome_completo']}")
        
        # 🛡️ PROTEÇÃO: Só excluir funcionários com "TESTE" no nome
        nome = funcionario_antes['nome_completo'].upper()
        if 'TESTE' not in nome:
            print(f"🛡️ PROTEÇÃO ATIVADA: Funcionário '{funcionario_antes['nome_completo']}' não contém 'TESTE' no nome")
            print("❌ Exclusão cancelada por segurança - apenas funcionários de teste podem ser excluídos")
            return False
        
        print("✅ Funcionário de teste identificado - prosseguindo com exclusão")
        
        # Executar exclusão (que na verdade é desligamento)
        print("🔄 Executando delete_funcionario...")
        sucesso = FuncionarioQueries.delete_funcionario(funcionario_id)
        print(f"🔍 Resultado da exclusão: {sucesso} (tipo: {type(sucesso)})")

        if sucesso:
            print("✅ Exclusão/desligamento executado com sucesso")

            # Verificar se funcionário foi marcado como inativo
            funcionario_depois = FuncionarioQueries.get_by_id(funcionario_id)
            if funcionario_depois:
                status = funcionario_depois.get('status_cadastro', 'N/A')
                ativo = funcionario_depois.get('ativo', True)
                print(f"📊 Status após desligamento: {status}, Ativo: {ativo}")

                if status == 'Inativo' and not ativo:
                    print("✅ Funcionário marcado como inativo na tabela principal")

                    # Verificar se está na tabela de desligados
                    desligado = DatabaseManager.execute_query(
                        "SELECT * FROM funcionarios_desligados WHERE funcionario_id_original = %s",
                        (funcionario_id,),
                        fetch_one=True
                    )

                    if desligado:
                        print("✅ Funcionário encontrado na tabela de desligados")
                        print(f"   Motivo: {desligado.get('motivo_desligamento', 'N/A')}")
                        print(f"   Data: {desligado.get('data_desligamento', 'N/A')}")
                        return True
                    else:
                        print("⚠️ Funcionário não encontrado na tabela de desligados")
                        return False
                else:
                    print(f"⚠️ Funcionário não foi marcado como inativo corretamente (Status: {status}, Ativo: {ativo})")
                    return False
            else:
                print("⚠️ Funcionário não encontrado após desligamento")
                return False
        else:
            print("❌ Falha na exclusão/desligamento")
            return False
            
    except Exception as e:
        print(f"❌ Erro durante teste de exclusão: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Função principal"""
    print("🧪 TESTE SIMPLES DE EXCLUSÃO DE FUNCIONÁRIOS")
    print("=" * 60)
    print(f"⏰ Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Teste de conexão
    try:
        result = DatabaseManager.execute_query("SELECT 1 as test", fetch_one=True)
        if result and result.get('test') == 1:
            print("✅ Conexão com banco funcionando")
        else:
            print("❌ Conexão com banco falhou")
            return
    except Exception as e:
        print(f"❌ ERRO de conexão: {e}")
        return
    
    # Criar funcionário de teste
    funcionario_id = criar_funcionario_teste_sql()
    if not funcionario_id:
        print("❌ Não foi possível criar funcionário de teste")
        return
    
    # Testar exclusão
    if testar_exclusao_funcionario(funcionario_id):
        print("\n🎉 TESTE DE EXCLUSÃO PASSOU!")
        print("✅ A funcionalidade de exclusão está funcionando corretamente")
        print("✅ Funcionário foi desligado e movido para tabela de histórico")
        print("✅ A proteção de segurança está ativa (apenas funcionários com 'TESTE' podem ser excluídos)")
    else:
        print("\n❌ TESTE DE EXCLUSÃO FALHOU!")
        print("❌ Há um problema com a funcionalidade de exclusão")
        
        # Limpar funcionário de teste se ainda existir
        try:
            DatabaseManager.execute_query(
                "DELETE FROM funcionarios WHERE id = %s", 
                (funcionario_id,), 
                fetch_all=False
            )
            print(f"🧹 Funcionário de teste removido (ID: {funcionario_id})")
        except Exception as cleanup_error:
            print(f"⚠️ Erro ao limpar teste: {cleanup_error}")

if __name__ == "__main__":
    main()
