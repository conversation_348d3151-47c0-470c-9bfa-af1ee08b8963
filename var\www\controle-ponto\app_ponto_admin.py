# -*- coding: utf-8 -*-
"""
Sistema de Administração de Ponto - RLPONTO-WEB
Módulo para gerenciamento completo de registros de ponto

Desenvolvido por: AiNexus Tecnologia
Autor: <PERSON> - Full Stack Developer
Data: 08/07/2025
Versão: 1.0
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, session, send_file
from werkzeug.utils import secure_filename
import logging
import os
import traceback
from datetime import datetime, date, time, timedelta
from decimal import Decimal
import json
import uuid
from utils.database import DatabaseManager
from utils.auth import require_admin, require_login, login_required
import uuid

# Configuração do logger
logger = logging.getLogger('controle-ponto.ponto_admin')

# Criar blueprint
ponto_admin_bp = Blueprint('ponto_admin', __name__, url_prefix='/ponto-admin')

# ========================================
# ROTAS PRINCIPAIS
# ========================================

@ponto_admin_bp.route('/executar-teste-funcionarios')
@login_required
def executar_teste_funcionarios():
    """Executa script de criação de funcionários de teste"""
    try:
        import subprocess
        import os

        # Caminho do script
        script_path = '/var/www/controle-ponto/criar_funcionarios_teste_completo.py'

        # Executar script
        result = subprocess.run(['python3', script_path],
                              capture_output=True,
                              text=True,
                              cwd='/var/www/controle-ponto')

        output = result.stdout
        errors = result.stderr

        return f"""
        <html>
        <head><title>Teste de Funcionários</title></head>
        <body>
        <h1>Resultado da Criação de Funcionários de Teste</h1>
        <h2>Output:</h2>
        <pre>{output}</pre>
        <h2>Erros:</h2>
        <pre>{errors}</pre>
        <p><a href="/ponto-admin/">Voltar</a></p>
        </body>
        </html>
        """

    except Exception as e:
        return f"Erro: {str(e)}"

@ponto_admin_bp.route('/executar-testes-sistema')
@login_required
def executar_testes_sistema():
    """Executa testes completos do sistema"""
    try:
        import subprocess
        import os

        # Caminho do script
        script_path = '/var/www/controle-ponto/executar_testes_dados_existentes.py'

        # Executar script
        result = subprocess.run(['python3', script_path],
                              capture_output=True,
                              text=True,
                              cwd='/var/www/controle-ponto')

        output = result.stdout
        errors = result.stderr

        # Tentar ler arquivo de relatório gerado
        relatorio_json = ""
        try:
            import glob
            arquivos_relatorio = glob.glob('/var/www/controle-ponto/teste_dados_existentes_*.json')
            if arquivos_relatorio:
                arquivo_mais_recente = max(arquivos_relatorio, key=os.path.getctime)
                with open(arquivo_mais_recente, 'r', encoding='utf-8') as f:
                    import json
                    dados = json.load(f)
                    relatorio_json = json.dumps(dados, indent=2, ensure_ascii=False)
        except:
            relatorio_json = "Erro ao carregar relatório JSON"

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Relatório de Testes do Sistema</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .section {{ margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }}
                .critico {{ border-left: 5px solid #dc3545; background: #fff5f5; }}
                .medio {{ border-left: 5px solid #ffc107; background: #fffbf0; }}
                .baixo {{ border-left: 5px solid #28a745; background: #f8fff8; }}
                pre {{ background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }}
                .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }}
                .stat-card {{ background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }}
                h1 {{ color: #007bff; }}
                h2 {{ color: #495057; }}
                .nav {{ margin-bottom: 20px; }}
                .nav a {{ margin-right: 15px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }}
                .nav a:hover {{ background: #0056b3; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="nav">
                    <a href="/ponto-admin/">🏠 Voltar ao Admin</a>
                    <a href="/ponto-admin/executar-teste-funcionarios">👥 Criar Funcionários Teste</a>
                    <a href="/ponto-admin/executar-testes-sistema">🔄 Executar Novamente</a>
                </div>

                <h1>🧪 Relatório Completo de Testes do Sistema</h1>

                <div class="section">
                    <h2>📊 Output da Execução</h2>
                    <pre>{output}</pre>
                </div>

                {f'<div class="section critico"><h2>❌ Erros</h2><pre>{errors}</pre></div>' if errors else ''}

                <div class="section">
                    <h2>📋 Relatório Detalhado (JSON)</h2>
                    <pre>{relatorio_json}</pre>
                </div>

                <div class="section">
                    <h2>🔗 Links Úteis</h2>
                    <ul>
                        <li><a href="/ponto-admin/">Painel Administrativo</a></li>
                        <li><a href="/relatorios/">Relatórios do Sistema</a></li>
                        <li><a href="/funcionarios/">Gestão de Funcionários</a></li>
                    </ul>
                </div>
            </div>
        </body>
        </html>
        """

    except Exception as e:
        return f"""
        <html>
        <head><title>Erro nos Testes</title></head>
        <body>
        <h1>❌ Erro ao Executar Testes</h1>
        <p><strong>Erro:</strong> {str(e)}</p>
        <p><a href="/ponto-admin/">Voltar</a></p>
        </body>
        </html>
        """

# Configurações de upload
UPLOAD_FOLDER = '/var/www/controle-ponto/uploads/documentos_ponto'
ALLOWED_EXTENSIONS = {'pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'}

def allowed_file(filename):
    """Verificar se o arquivo é permitido"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_folder():
    """Garantir que a pasta de upload existe"""
    if not os.path.exists(UPLOAD_FOLDER):
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# ================================================================
# ROTAS PRINCIPAIS
# ================================================================

@ponto_admin_bp.route('/')
@require_login
def index():
    """Página principal de administração de ponto"""
    try:
        # Limpar mensagens flash antigas que podem estar causando problemas
        session.pop('_flashes', None)

        # Buscar estatísticas gerais
        stats = get_estatisticas_ponto()

        # Buscar funcionários com resumo de ponto
        funcionarios = get_funcionarios_resumo_ponto()

        return render_template('ponto_admin/index.html',
                             stats=stats,
                             funcionarios=funcionarios)
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Erro na página principal de ponto admin: {e}")
        logger.error(f"Detalhes do erro: {error_details}")
        flash(f'Erro ao carregar dados de ponto: {str(e)}', 'error')
        return redirect('/')

@ponto_admin_bp.route('/funcionario/<int:funcionario_id>')
@require_login
def detalhes_funcionario(funcionario_id):
    """Detalhes completos de ponto de um funcionário com histórico de alocações"""
    try:
        # Buscar dados do funcionário diretamente do banco
        db = DatabaseManager()

        sql = """
        SELECT
            f.*,
            e.razao_social as empresa_nome
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        WHERE f.id = %s
        """

        result = db.execute_query(sql, (funcionario_id,))
        funcionario = result[0] if result else None

        if not funcionario:
            flash('Funcionário não encontrado', 'error')
            return redirect(url_for('ponto_admin.index'))

        # FORÇAR RECÁLCULO COMPLETO DOS DADOS
        logger.info(f"🔄 FORÇANDO recálculo para funcionário {funcionario_id}")

        # Buscar dados reais do funcionário (sempre recalculado)
        registros = get_registros_ponto_funcionario(funcionario_id)
        historico_alocacoes = get_historico_alocacoes_funcionario(funcionario_id)
        logs = get_logs_atividades_funcionario(funcionario_id)

        # Log detalhado dos dados
        logger.info(f"📊 Registros obtidos: {len(registros)}")
        for i, reg in enumerate(registros):
            if '2025-07-11' in str(reg.get('data', '')):
                logger.info(f"🎯 CASO 11/07 - Registro {i}: {reg.get('horas_normais')}h normais, {reg.get('horas_negativas')}h negativas")

        # Importar datetime e timedelta para o template
        from datetime import datetime, timedelta

        # FORÇAR DADOS ATUALIZADOS - SEM CACHE
        from datetime import datetime
        timestamp_atual = int(datetime.now().timestamp())

        # Log para debug
        logger.info(f"🔄 Renderizando detalhes funcionário {funcionario_id} - Timestamp: {timestamp_atual}")
        logger.info(f"📊 Total registros: {len(registros)}")

        # Verificar se dados estão atualizados
        for registro in registros:
            if '2025-07-11' in str(registro.get('data', '')):
                logger.info(f"🎯 Caso 11/07: {registro.get('horas_normais')}h normais, {registro.get('horas_negativas')}h negativas")

        # Renderizar template com dados atualizados
        response = render_template('ponto_admin/detalhes_funcionario.html',
                                 funcionario=funcionario,
                                 registros=registros,
                                 historico_alocacoes=historico_alocacoes,
                                 logs=logs,
                                 datetime=datetime,
                                 timedelta=timedelta,
                                 timestamp=timestamp_atual,
                                 cache_buster=timestamp_atual)

        # HEADERS ANTI-CACHE ROBUSTOS
        from flask import make_response
        resp = make_response(response)
        resp.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0'
        resp.headers['Pragma'] = 'no-cache'
        resp.headers['Expires'] = '0'
        resp.headers['Last-Modified'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S GMT')
        resp.headers['ETag'] = f'"{timestamp_atual}"'

        return resp
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()

        logger.error(f"ERRO ao buscar detalhes do funcionário {funcionario_id}: {e}")
        logger.error(f"Detalhes completos do erro: {error_details}")
        flash('Erro ao carregar detalhes do funcionário', 'error')
        return redirect(url_for('ponto_admin.index'))

# ================================================================
# API ENDPOINTS
# ================================================================

@ponto_admin_bp.route('/api/funcionarios')
@require_admin
def api_funcionarios():
    """API: Lista de funcionários com dados de ponto"""
    try:
        funcionarios = get_funcionarios_resumo_ponto()
        
        # Converter tipos não serializáveis
        for funcionario in funcionarios:
            for key, value in funcionario.items():
                if isinstance(value, (date, datetime, time)):
                    funcionario[key] = value.isoformat() if hasattr(value, 'isoformat') else str(value)
                elif isinstance(value, Decimal):
                    funcionario[key] = float(value)
        
        return jsonify({'success': True, 'funcionarios': funcionarios})
    except Exception as e:
        logger.error(f"Erro na API funcionários: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@ponto_admin_bp.route('/api/funcionario/<int:funcionario_id>/registros')
@require_login
def api_registros_funcionario(funcionario_id):
    """API: Registros de ponto de um funcionário"""
    try:
        # Parâmetros de filtro
        data_inicio = request.args.get('data_inicio')
        data_fim = request.args.get('data_fim')
        
        registros = get_registros_ponto_funcionario(funcionario_id, data_inicio, data_fim)
        
        # Converter tipos não serializáveis
        for registro in registros:
            for key, value in registro.items():
                if isinstance(value, (date, datetime, time)):
                    registro[key] = value.isoformat() if hasattr(value, 'isoformat') else str(value)
                elif isinstance(value, Decimal):
                    registro[key] = float(value)
        
        return jsonify({'success': True, 'registros': registros})
    except Exception as e:
        logger.error(f"Erro na API registros funcionário {funcionario_id}: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@ponto_admin_bp.route('/api/editar-registro', methods=['POST'])
@require_admin
def api_editar_registro():
    """API: Editar registro de ponto"""
    try:
        data = request.get_json()
        registro_id = data.get('registro_id')
        campo = data.get('campo')
        valor = data.get('valor')
        justificativa = data.get('justificativa', '')
        
        if not all([registro_id, campo, valor]):
            return jsonify({'success': False, 'message': 'Dados incompletos'})
        
        # Validar campo permitido
        campos_permitidos = ['entrada', 'saida_almoco', 'retorno_almoco', 'saida']
        if campo not in campos_permitidos:
            return jsonify({'success': False, 'message': 'Campo não permitido'})
        
        # Editar registro
        sucesso = editar_registro_ponto(registro_id, campo, valor, justificativa)
        
        if sucesso:
            # Registrar log de atividade
            registrar_log_atividade(
                funcionario_id=data.get('funcionario_id'),
                acao='EDICAO_PONTO',
                detalhes=f"Campo {campo} alterado para {valor}. Justificativa: {justificativa}",
                usuario_id=session.get('user_id')
            )
            
            return jsonify({'success': True, 'message': 'Registro editado com sucesso'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao editar registro'})
            
    except Exception as e:
        logger.error(f"Erro ao editar registro: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

# ================================================================
# FUNÇÕES AUXILIARES
# ================================================================

def get_estatisticas_ponto():
    """Obter estatísticas gerais de ponto"""
    try:
        db = DatabaseManager()
        
        # Estatísticas do mês atual
        sql = """
        SELECT
            COUNT(DISTINCT rp.funcionario_id) as funcionarios_com_ponto,
            COUNT(rp.id) as total_registros,
            COUNT(CASE WHEN rp.tipo_registro = 'entrada_manha' AND rp.data_registro IS NULL THEN 1 END) as faltas,
            COUNT(CASE WHEN rp.observacoes IS NOT NULL AND rp.observacoes != '' AND rp.observacoes != '2' THEN 1 END) as registros_justificados
        FROM registros_ponto rp
        WHERE MONTH(rp.data_registro) = MONTH(CURRENT_DATE())
        AND YEAR(rp.data_registro) = YEAR(CURRENT_DATE())
        """
        
        result = db.execute_query(sql)
        return result[0] if result else {}
        
    except Exception as e:
        logger.error(f"Erro ao obter estatísticas: {e}")
        return {}

def get_funcionarios_resumo_ponto():
    """Obter lista de funcionários com resumo de ponto incluindo histórico de alocações"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT
            f.id,
            f.nome_completo,
            f.cargo,
            f.setor,
            f.status_cadastro,
            e.razao_social as empresa_nome,
            COUNT(rp.id) as registros_mes,
            COUNT(CASE WHEN rp.tipo_registro = 'entrada_manha' AND rp.data_registro IS NULL THEN 1 END) as faltas_mes,
            COUNT(CASE WHEN rp.observacoes IS NOT NULL AND rp.observacoes != '' AND rp.observacoes != '2' THEN 1 END) as justificativas_mes,
            MAX(rp.data_registro) as ultimo_ponto,
            0 as clientes_trabalhados,
            '' as clientes_recentes
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN registros_ponto rp ON f.id = rp.funcionario_id
            AND MONTH(rp.data_registro) = MONTH(CURRENT_DATE())
            AND YEAR(rp.data_registro) = YEAR(CURRENT_DATE())
        WHERE f.status_cadastro = 'Ativo'
        GROUP BY f.id, f.nome_completo, f.cargo, f.setor, f.status_cadastro, e.razao_social
        ORDER BY f.nome_completo
        """

        return db.execute_query(sql)

    except Exception as e:
        logger.error(f"Erro ao obter funcionários: {e}")
        return []

def get_funcionario_detalhes(funcionario_id):
    """Obter detalhes completos de um funcionário"""
    try:
        db = DatabaseManager()
        
        sql = """
        SELECT
            f.*,
            e.razao_social as empresa_nome,
            e.cnpj,
            jt.nome_jornada,
            jt.seg_qui_entrada,
            jt.seg_qui_saida,
            jt.sexta_entrada,
            jt.sexta_saida,
            jt.intervalo_inicio,
            jt.intervalo_fim
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.id = %s
        """
        
        result = db.execute_query(sql, (funcionario_id,))
        return result[0] if result else None
        
    except Exception as e:
        logger.error(f"Erro ao obter detalhes do funcionário {funcionario_id}: {e}")
        return None

def get_registros_ponto_funcionario(funcionario_id, data_inicio=None, data_fim=None):
    """
    Obter registros de ponto de um funcionário agrupados por dia
    VERSÃO ROBUSTA: Sempre recalcula, sem cache
    """
    logger.info(f"🔄 INICIANDO get_registros_ponto_funcionario({funcionario_id})")

    try:
        db = DatabaseManager()

        # Definir período padrão (últimos 60 dias para garantir histórico completo)
        if not data_inicio:
            data_inicio = (datetime.now() - timedelta(days=60)).date()
        else:
            # Converter string para date se necessário
            if isinstance(data_inicio, str):
                try:
                    data_inicio = datetime.strptime(data_inicio, '%Y-%m-%d').date()
                except ValueError:
                    logger.error(f"Formato de data_inicio inválido: {data_inicio}")
                    data_inicio = (datetime.now() - timedelta(days=60)).date()

        if not data_fim:
            data_fim = datetime.now().date()
        else:
            # Converter string para date se necessário
            if isinstance(data_fim, str):
                try:
                    data_fim = datetime.strptime(data_fim, '%Y-%m-%d').date()
                except ValueError:
                    logger.error(f"Formato de data_fim inválido: {data_fim}")
                    data_fim = datetime.now().date()

        logger.info(f"🗓️ Período de filtro: {data_inicio} até {data_fim}")

        # Buscar alocação ativa do funcionário para determinar status de trabalho
        sql_alocacao = """
        SELECT
            fa.empresa_cliente_id,
            e.razao_social as cliente_nome,
            e.nome_fantasia as cliente_fantasia
        FROM funcionario_alocacoes fa
        LEFT JOIN empresas e ON fa.empresa_cliente_id = e.id
        WHERE fa.funcionario_id = %s
        AND fa.ativo = TRUE
        AND (fa.data_fim IS NULL OR fa.data_fim >= CURDATE())
        ORDER BY fa.id DESC
        LIMIT 1
        """

        alocacao_ativa = db.execute_query(sql_alocacao, (funcionario_id,))

        # Definir status de trabalho e informações do cliente
        if alocacao_ativa:
            status_trabalho = 'ALOCADO'
            cliente_nome = alocacao_ativa[0]['cliente_nome'] or 'Cliente'
            cliente_fantasia = alocacao_ativa[0]['cliente_fantasia'] or 'Cliente'
            logger.info(f"🎯 Funcionário {funcionario_id} ALOCADO para: {cliente_nome}")
        else:
            status_trabalho = 'EMPRESA_PRINCIPAL'
            cliente_nome = 'Empresa Principal'
            cliente_fantasia = 'Sede'
            logger.info(f"🏢 Funcionário {funcionario_id} trabalhando na EMPRESA PRINCIPAL")

        # Buscar todos os registros primeiro
        sql_registros = """
        SELECT
            DATE(data_hora) as data_trabalho,
            tipo_registro,
            TIME(data_hora) as hora_registro,
            observacoes,
            id,
            status_justificativa
        FROM registros_ponto
        WHERE funcionario_id = %s
        AND DATE(data_hora) BETWEEN %s AND %s
        ORDER BY data_hora ASC
        """

        registros_raw = db.execute_query(sql_registros, (funcionario_id, data_inicio, data_fim))

        if not registros_raw:
            logger.info(f"❌ Nenhum registro encontrado para funcionário {funcionario_id}")
            return []

        logger.info(f"📊 Encontrados {len(registros_raw)} registros brutos")

        # Log detalhado dos registros encontrados
        for i, reg in enumerate(registros_raw):
            logger.info(f"🔍 Registro {i+1}: {reg['data_trabalho']} - {reg['tipo_registro']} - {reg['hora_registro']}")

        # Agrupar registros por data
        registros_agrupados = {}
        for registro in registros_raw:
            data = registro['data_trabalho']
            if data not in registros_agrupados:
                registros_agrupados[data] = {
                    'data': data,
                    'dia_semana': ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo'][data.weekday()],
                    'entrada': None,
                    'saida_almoco': None,
                    'retorno_almoco': None,
                    'saida': None,
                    'inicio_extra': None,  # B5
                    'fim_extra': None,     # B6
                    'justificativa': None,
                    'status_justificativa': None,  # ✅ NOVO: Status da justificativa (abonado/reprovado)
                    'status_trabalho': status_trabalho,  # Usar status determinado pela alocação
                    'cliente_nome': cliente_nome,        # Usar nome do cliente ou empresa principal
                    'cliente_fantasia': cliente_fantasia, # Usar fantasia do cliente ou 'Sede'
                    'total_horas_dia': 8.0,
                    'id': registro['id']
                }

            # Mapear tipos de registro para campos
            # Converter timedelta para datetime.time se necessário
            hora = registro['hora_registro']
            if isinstance(hora, timedelta):
                # Converter timedelta para time
                total_seconds = int(hora.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                seconds = total_seconds % 60
                hora = time(hours, minutes, seconds)

            if registro['tipo_registro'] == 'entrada_manha':
                registros_agrupados[data]['entrada'] = hora
            elif registro['tipo_registro'] == 'saida_almoco':
                registros_agrupados[data]['saida_almoco'] = hora
            elif registro['tipo_registro'] == 'entrada_tarde':
                registros_agrupados[data]['retorno_almoco'] = hora
            elif registro['tipo_registro'] == 'saida':
                registros_agrupados[data]['saida'] = hora
            elif registro['tipo_registro'] == 'inicio_extra':
                registros_agrupados[data]['inicio_extra'] = hora  # B5
            elif registro['tipo_registro'] == 'fim_extra':
                registros_agrupados[data]['fim_extra'] = hora     # B6

            # ✅ NOVO: Processar status de justificativa do registro
            if registro['status_justificativa']:
                # Se qualquer registro do dia tem status, aplicar ao dia todo
                registros_agrupados[data]['status_justificativa'] = registro['status_justificativa']

            # ✅ CORREÇÃO: Não incluir observações como justificativas
            # Observações são comentários informativos, não justificativas reais
            # Justificativas reais vêm da tabela justificativas_ponto

        # ✅ NOVO: Buscar justificativas reais da tabela justificativas_ponto
        sql_justificativas = """
        SELECT
            data_registro,
            motivo,
            status_aprovacao
        FROM justificativas_ponto
        WHERE funcionario_id = %s
        AND data_registro BETWEEN %s AND %s
        AND status_aprovacao = 'aprovado'
        """

        justificativas_reais = db.execute_query(sql_justificativas, (funcionario_id, data_inicio, data_fim))

        # Mapear justificativas por data
        justificativas_por_data = {}
        if justificativas_reais:
            for just in justificativas_reais:
                data_just = just['data_registro']
                justificativas_por_data[data_just] = just['motivo']

        # Aplicar justificativas reais aos registros agrupados
        for data, registro in registros_agrupados.items():
            if data in justificativas_por_data:
                registro['justificativa'] = justificativas_por_data[data]

        # Calcular horas trabalhadas para cada dia e definir status de presença
        total_horas_normais_periodo = 0.0
        total_horas_extras_periodo = 0.0
        total_horas_negativas_periodo = 0.0

        for registro in registros_agrupados.values():
            # Calcular horas separadamente (com regra rigorosa se tiver funcionario_id)
            horas_normais, horas_extras, horas_negativas = calcular_horas_separadas_dia(registro, funcionario_id)
            horas_totais = horas_normais + horas_extras

            # Armazenar no registro
            registro['horas_normais'] = horas_normais
            registro['horas_extras'] = horas_extras
            registro['horas_negativas'] = horas_negativas
            registro['total_horas_dia'] = horas_totais
            registro['total_horas_decimal'] = horas_totais  # Para compatibilidade com template
            # ✅ MODIFICADO: Usar formato HH:MM em vez de decimal
            from utils.helpers import decimal_para_hhmm

            registro['horas_trabalhadas'] = decimal_para_hhmm(horas_totais) if horas_totais > 0 else '00:00'

            # ✅ CORREÇÃO: Horas negativas = Total de atrasos
            def calcular_total_atrasos(registro):
                """
                Calcula total de atrasos baseado na jornada REAL configurada do funcionário.

                REGRA CORRETA:
                1. Busca jornada real do funcionário (entrada/saída - intervalo)
                2. Compara com horas trabalhadas
                3. Deficit = horas negativas = atrasos
                """
                try:
                    # ✅ CORREÇÃO: Buscar jornada REAL do funcionário
                    jornada_esperada = calcular_horas_esperadas_jornada_real(funcionario_id)

                    if jornada_esperada is None or jornada_esperada == 0:
                        logger.error(f"❌ Não foi possível calcular atrasos - jornada não configurada para funcionário {funcionario_id}")
                        return 0.0

                    # Horas realmente trabalhadas (apenas horas normais, não extras)
                    horas_trabalhadas = horas_normais

                    # Se trabalhou menos que o esperado = horas negativas (atrasos)
                    if horas_trabalhadas < jornada_esperada:
                        deficit = jornada_esperada - horas_trabalhadas
                        logger.info(f"📊 Cálculo de atrasos:")
                        logger.info(f"   Jornada esperada: {jornada_esperada:.2f}h")
                        logger.info(f"   Horas trabalhadas: {horas_trabalhadas:.2f}h")
                        logger.info(f"   Deficit (atrasos): {deficit:.2f}h")
                        return deficit

                    # Se trabalhou igual ou mais = sem atrasos
                    return 0.0

                except Exception as e:
                    logger.error(f"Erro ao calcular total de atrasos: {e}")
                    return 0.0

            # Calcular total de atrasos (horas negativas)
            atraso_horas = calcular_total_atrasos(registro)

            # Campos esperados pelo template de impressão
            registro['horas_extras'] = decimal_para_hhmm(horas_extras)  # Formato HH:MM
            registro['descontos'] = decimal_para_hhmm(atraso_horas)  # Atrasos reais em formato HH:MM
            registro['banco_horas'] = "00:00"  # Por enquanto zerado, pode ser implementado depois

            # Adicionar alertas informativos (não afeta cálculos)
            registro['alertas_irregularidades'] = detectar_irregularidades_informativo(registro)

            # Somar para totais do período
            total_horas_normais_periodo += horas_normais
            total_horas_extras_periodo += horas_extras
            total_horas_negativas_periodo += horas_negativas

            # Definir status de presença baseado na existência de entrada
            if registro.get('entrada'):
                registro['status_presenca'] = 'PRESENTE'
            else:
                registro['status_presenca'] = 'FALTA'

            # Definir status de completude do registro
            entrada = registro.get('entrada')
            saida_almoco = registro.get('saida_almoco')
            retorno_almoco = registro.get('retorno_almoco')
            saida = registro.get('saida')

            # Lógica para determinar se o registro está completo
            if entrada and saida_almoco and retorno_almoco and saida:
                # Jornada completa com intervalo (B1, B2, B3, B4)
                registro['status_texto'] = 'Completo'
                registro['status_classe'] = 'completo'
            elif entrada and saida and not saida_almoco and not retorno_almoco:
                # Jornada sem intervalo (B1, B4) - também considerado completo
                registro['status_texto'] = 'Completo'
                registro['status_classe'] = 'completo'
            elif entrada and not saida:
                # Registro incompleto - tem entrada mas não tem saída final
                registro['status_texto'] = 'Incompleto'
                registro['status_classe'] = 'incompleto'
            elif not entrada:
                # Sem entrada - falta
                registro['status_texto'] = 'Falta'
                registro['status_classe'] = 'falta'
            else:
                # Outros casos incompletos
                registro['status_texto'] = 'Incompleto'
                registro['status_classe'] = 'incompleto'

        # Converter para lista e ordenar por data decrescente
        resultado = list(registros_agrupados.values())
        resultado.sort(key=lambda x: x['data'], reverse=True)

        # Adicionar informações sobre totais para o template
        total_batidas = len(registros_raw)
        for registro in resultado:
            registro['total_batidas_periodo'] = total_batidas
            registro['total_horas_normais_periodo'] = round(total_horas_normais_periodo, 1)
            registro['total_horas_extras_periodo'] = round(total_horas_extras_periodo, 1)
            registro['total_horas_negativas_periodo'] = round(total_horas_negativas_periodo, 1)

        return resultado

    except Exception as e:
        logger.error(f"Erro ao obter registros do funcionário {funcionario_id}: {e}")
        return []





def obter_jornada_funcionario_real(funcionario_id):
    """
    Obtém a jornada real do funcionário usando sistema de herança dinâmica

    Args:
        funcionario_id (int): ID do funcionário

    Returns:
        dict: Dados da jornada com horas obrigatórias ou None
    """
    try:
        from utils.database import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()

        # ✅ CORREÇÃO: Usar sistema de herança dinâmica igual ao app_registro_ponto.py
        cursor.execute("""
            SELECT
                f.nome_completo,
                f.empresa_id,
                f.horas_semanais_obrigatorias,
                e.razao_social as empresa_nome,
                -- ✅ PRIORIDADE 1: Jornada da alocação ativa (se existir)
                -- ✅ PRIORIDADE 2: Configuração da empresa (empresas_config)
                -- ✅ PRIORIDADE 3: Jornada específica do funcionário (fallback)
                COALESCE(
                    ht_alocacao.entrada_manha,
                    TIME(ec_config.jornada_segunda_entrada),
                    ht_funcionario.entrada_manha,
                    '08:00'
                ) as entrada_oficial,
                COALESCE(
                    ht_alocacao.saida_almoco,
                    TIME(ec_config.jornada_segunda_saida_almoco),
                    ht_funcionario.saida_almoco,
                    '12:00'
                ) as almoco_inicio,
                COALESCE(
                    ht_alocacao.entrada_tarde,
                    TIME(ec_config.jornada_segunda_entrada_almoco),
                    ht_funcionario.entrada_tarde,
                    '13:00'
                ) as almoco_fim,
                COALESCE(
                    ht_alocacao.saida,
                    TIME(ec_config.jornada_segunda_saida),
                    ht_funcionario.saida,
                    '17:00'
                ) as saida_oficial,
                -- ✅ NOVO: Campos específicos para sexta-feira
                COALESCE(
                    ht_alocacao.sexta_entrada,
                    TIME(ec_config.jornada_sexta_entrada),
                    ht_funcionario.sexta_entrada,
                    ht_alocacao.entrada_manha,
                    TIME(ec_config.jornada_segunda_entrada),
                    ht_funcionario.entrada_manha,
                    '08:00'
                ) as sexta_entrada,
                COALESCE(
                    ht_alocacao.sexta_saida,
                    TIME(ec_config.jornada_sexta_saida),
                    ht_funcionario.sexta_saida,
                    ht_alocacao.saida,
                    TIME(ec_config.jornada_segunda_saida),
                    ht_funcionario.saida,
                    '17:00'
                ) as sexta_saida
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            LEFT JOIN empresas_config ec_config ON f.empresa_id = ec_config.empresa_id
            LEFT JOIN horarios_trabalho ht_funcionario ON f.horario_trabalho_id = ht_funcionario.id
            LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id
                AND fa.ativo = TRUE
                AND (fa.data_fim IS NULL OR fa.data_fim >= CURDATE())
            LEFT JOIN horarios_trabalho ht_alocacao ON fa.horario_trabalho_id = ht_alocacao.id
            WHERE f.id = %s
            LIMIT 1
        """, (funcionario_id,))

        result = cursor.fetchone()
        cursor.close()
        conn.close()

        if result:
            return {
                'entrada_oficial': result[4],  # entrada_oficial
                'saida_oficial': result[7],    # saida_oficial
                'almoco_inicio': result[5],    # almoco_inicio
                'almoco_fim': result[6],       # almoco_fim
                'sexta_entrada': result[8],    # sexta_entrada
                'sexta_saida': result[9],      # sexta_saida
                'horas_semanais_obrigatorias': float(result[2]) if result[2] else 44.0,  # horas_semanais_obrigatorias
                'funcionario_nome': result[0],
                'empresa_nome': result[3]
            }
        return None

    except Exception as e:
        logger.error(f"Erro ao obter jornada do funcionário {funcionario_id}: {e}")
        return None

def aplicar_regra_rigorosa_com_jornada(registro, funcionario_id=None):
    """
    REGRA RIGOROSA: Usa jornada oficial do funcionário

    Args:
        registro (dict): Registro do dia
        funcionario_id (int): ID do funcionário

    Returns:
        tuple: (horas_normais_limitadas, alertas_ajustes)
    """
    alertas_ajustes = []

    try:
        # Tentar obter jornada oficial do funcionário
        jornada = None
        if funcionario_id:
            jornada = obter_jornada_funcionario_real(funcionario_id)

        # ✅ CORREÇÃO: Sempre deve ter jornada (sistema de herança garante isso)
        if jornada:
            entrada_oficial = jornada['entrada_oficial']
            saida_oficial = jornada['saida_oficial']
            almoco_inicio = jornada['almoco_inicio']
            almoco_fim = jornada['almoco_fim']
            logger.info(f"📋 Jornada obtida: {entrada_oficial}-{saida_oficial} (almoço: {almoco_inicio}-{almoco_fim})")
        else:
            # ❌ ERRO: Sistema de herança falhou - usar padrão emergencial
            logger.error(f"❌ ERRO CRÍTICO: Funcionário {funcionario_id} sem jornada configurada!")
            entrada_oficial = datetime.strptime('08:00:00', '%H:%M:%S').time()
            saida_oficial = datetime.strptime('17:00:00', '%H:%M:%S').time()
            almoco_inicio = datetime.strptime('12:00:00', '%H:%M:%S').time()
            almoco_fim = datetime.strptime('13:00:00', '%H:%M:%S').time()

        # Converter para datetime para cálculos
        def time_to_datetime(time_obj):
            if isinstance(time_obj, str):
                time_obj = datetime.strptime(time_obj, '%H:%M:%S').time()
            return datetime.combine(datetime.today(), time_obj)

        entrada_oficial_dt = time_to_datetime(entrada_oficial)
        saida_oficial_dt = time_to_datetime(saida_oficial)
        almoco_inicio_dt = time_to_datetime(almoco_inicio)
        almoco_fim_dt = time_to_datetime(almoco_fim)

        # Horários registrados
        entrada_real = None
        saida_real = None

        if registro.get('entrada'):
            entrada_real = time_to_datetime(registro['entrada'])
        if registro.get('saida'):
            saida_real = time_to_datetime(registro['saida'])

        # NOVA LÓGICA JUSTA: Calcular trabalho parcial mesmo com registro incompleto
        if not entrada_real:
            return 0.0, ['⚠️ Sem entrada registrada']

        # 🔧 CORREÇÃO: Lógica para registros incompletos
        if not saida_real:
            # Para registros incompletos, calcular apenas os períodos válidos
            if registro.get('saida_almoco') and not registro.get('retorno_almoco'):
                # Cenário 1: Só trabalhou manhã (entrada + saída_almoco)
                saida_real = time_to_datetime(registro['saida_almoco'])
                alertas_ajustes.append('⚠️ Registro incompleto: calculando apenas período manhã')
            elif registro.get('retorno_almoco') and not registro.get('saida'):
                # Cenário 2: Trabalhou manhã + voltou do almoço mas não registrou saída
                # ERRO ANTERIOR: Não assumir saída oficial, calcular só até onde registrou
                # Neste caso, só calcular manhã (entrada até saída_almoco)
                if registro.get('saida_almoco'):
                    saida_real = time_to_datetime(registro['saida_almoco'])
                    alertas_ajustes.append('⚠️ Sem saída registrada: calculando apenas manhã')
                else:
                    saida_real = almoco_inicio
                    alertas_ajustes.append('⚠️ Registro muito incompleto: calculando até almoço')
            else:
                # Cenário 3: Só tem entrada
                saida_real = almoco_inicio
                alertas_ajustes.append('⚠️ Registro muito incompleto: calculando até almoço')

        # 🔧 CORREÇÃO: NÃO APLICAR LIMITAÇÕES ARTIFICIAIS
        # O funcionário deve receber crédito pelas horas realmente trabalhadas
        # As limitações de jornada são apenas para calcular horas extras vs normais

        # Usar horários reais registrados (sem limitação artificial)
        entrada_valida = entrada_real
        saida_valida = saida_real

        # Apenas informar se está fora da jornada oficial (sem limitar)
        if entrada_real < entrada_oficial_dt:
            minutos_antecipacao = (entrada_oficial_dt - entrada_real).total_seconds() / 60
            alertas_ajustes.append(f'ℹ️ Entrada antecipada: {entrada_real.strftime("%H:%M")} (antes de {entrada_oficial_dt.strftime("%H:%M")})')

        if saida_real > saida_oficial_dt:
            minutos_excesso = (saida_real - saida_oficial_dt).total_seconds() / 60
            alertas_ajustes.append(f'ℹ️ Saída após horário: {saida_real.strftime("%H:%M")} (depois de {saida_oficial_dt.strftime("%H:%M")})')

        # 🔧 CORREÇÃO: Calcular horas reais trabalhadas (sem limitação artificial)
        horas_trabalhadas = 0.0

        # Período manhã: usar horários reais registrados
        if registro.get('entrada') and registro.get('saida_almoco'):
            entrada_manha = time_to_datetime(registro['entrada'])
            saida_manha = time_to_datetime(registro['saida_almoco'])
            periodo_manha = (saida_manha - entrada_manha).total_seconds() / 3600
            if periodo_manha > 0:
                horas_trabalhadas += periodo_manha
                alertas_ajustes.append(f'✅ Manhã: {entrada_manha.strftime("%H:%M")}-{saida_manha.strftime("%H:%M")} = {periodo_manha:.2f}h')

        # Período tarde: usar horários reais registrados
        if registro.get('retorno_almoco') and registro.get('saida'):
            retorno_tarde = time_to_datetime(registro['retorno_almoco'])
            saida_tarde = time_to_datetime(registro['saida'])
            periodo_tarde = (saida_tarde - retorno_tarde).total_seconds() / 3600
            if periodo_tarde > 0:
                horas_trabalhadas += periodo_tarde
                alertas_ajustes.append(f'✅ Tarde: {retorno_tarde.strftime("%H:%M")}-{saida_tarde.strftime("%H:%M")} = {periodo_tarde:.2f}h')

        # Verificar se trabalhou apenas manhã
        if not registro.get('retorno_almoco') or not registro.get('saida'):
            alertas_ajustes.append('📋 Trabalhou apenas período manhã')

        return round(horas_trabalhadas, 2), alertas_ajustes

    except Exception as e:
        logger.error(f"Erro na regra rigorosa: {e}")
        return 0.0, ['❌ Erro no cálculo']

def calcular_horas_separadas_dia(registro, funcionario_id=None):
    """
    Calcula as horas normais, extras e negativas separadamente

    REGRAS DE CÁLCULO ROBUSTAS:
    1. Cálculo baseado no início (B1) e fim da jornada (B4)
    2. B1 e B2 = período manhã (contar como horas trabalhadas)
    3. B3 e B4 = período tarde (contar como horas trabalhadas)
    4. Horas negativas = jornada esperada - horas trabalhadas

    Args:
        registro (dict): Registro do dia com todos os horários
        funcionario_id (int): ID do funcionário para aplicar regra rigorosa

    Returns:
        tuple: (horas_normais, horas_extras, horas_negativas)
    """
    try:
        horas_normais = 0.0
        horas_extras = 0.0
        alertas_ajustes = []

        # 🎯 APLICAR NOVO CÁLCULO ROBUSTO
        logger.info(f"🔄 Iniciando cálculo robusto para registro...")

        # Calcular horas trabalhadas usando regras robustas
        horas_normais = calcular_horas_trabalhadas_robusto(registro)

        # Calcular horas extras usando regras robustas
        horas_extras = calcular_horas_extras_robusto(registro)

        # Armazenar alertas de ajustes no registro
        if alertas_ajustes:
            registro['alertas_ajustes_jornada'] = alertas_ajustes

        # ✅ NOVO: Calcular déficit baseado em horas semanais do funcionário
        # IMPORTANTE: Horas extras (B5-B6) NÃO compensam déficit da jornada normal
        horas_diarias_esperadas = 8.8  # Padrão se não conseguir obter do funcionário

        # ✅ FLEXÍVEL: Obter horas semanais do cadastro do funcionário
        if funcionario_id:
            jornada_info = obter_jornada_funcionario_real(funcionario_id)
            if jornada_info and jornada_info.get('horas_semanais_obrigatorias'):
                horas_semanais = jornada_info['horas_semanais_obrigatorias']
                # Calcular horas diárias esperadas (distribuição: 5 dias úteis)
                horas_diarias_esperadas = horas_semanais / 5.0
                logger.info(f"📊 Funcionário {funcionario_id}: {horas_semanais}h semanais = {horas_diarias_esperadas:.2f}h/dia")

        horas_negativas = 0.0

        # 🎯 CORREÇÃO: Calcular horas esperadas baseado na jornada REAL do funcionário
        horas_diarias_esperadas_real = calcular_horas_esperadas_jornada_real(funcionario_id)

        if horas_diarias_esperadas_real is None:
            logger.error(f"❌ Não foi possível calcular horas negativas - jornada não configurada")
            horas_diarias_esperadas_real = 0.0

        # 🎯 CORREÇÃO SIMPLIFICADA: Se não conseguiu calcular jornada real, usar cálculo direto
        if horas_diarias_esperadas_real is None or horas_diarias_esperadas_real == 0:
            # Buscar jornada diretamente da tabela jornadas_trabalho
            try:
                db = DatabaseManager()
                query_simples = """
                SELECT
                    jt.seg_qui_entrada,
                    jt.seg_qui_saida,
                    f.nome_completo
                FROM funcionarios f
                LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
                WHERE f.id = %s
                """
                resultado = db.execute_query(query_simples, (funcionario_id,))

                if resultado and resultado[0]['seg_qui_entrada'] and resultado[0]['seg_qui_saida']:
                    entrada = resultado[0]['seg_qui_entrada']
                    saida = resultado[0]['seg_qui_saida']
                    nome = resultado[0]['nome_completo']

                    # Converter timedelta para time se necessário
                    if hasattr(entrada, 'total_seconds'):
                        entrada = (datetime.min + entrada).time()
                    if hasattr(saida, 'total_seconds'):
                        saida = (datetime.min + saida).time()

                    # Calcular horas da jornada
                    entrada_dt = datetime.combine(datetime.today(), entrada)
                    saida_dt = datetime.combine(datetime.today(), saida)

                    if saida_dt <= entrada_dt:
                        saida_dt += timedelta(days=1)

                    total_jornada_horas = (saida_dt - entrada_dt).total_seconds() / 3600
                    horas_diarias_esperadas_real = total_jornada_horas - 1.0  # Subtrair 1h de almoço

                    logger.info(f"✅ Jornada calculada diretamente para {nome}:")
                    logger.info(f"   Entrada: {entrada}, Saída: {saida}")
                    logger.info(f"   Horas esperadas: {horas_diarias_esperadas_real:.2f}h")
                else:
                    logger.error(f"❌ Não foi possível obter jornada para funcionário {funcionario_id}")
                    horas_diarias_esperadas_real = 0.0

            except Exception as e:
                logger.error(f"❌ Erro no cálculo simplificado: {e}")
                horas_diarias_esperadas_real = 0.0

        # Déficit é calculado SOMENTE nas horas normais (B1-B4)
        # Horas extras são um bônus separado e não compensam a jornada obrigatória
        if horas_diarias_esperadas_real > 0 and horas_normais < horas_diarias_esperadas_real:
            horas_negativas = horas_diarias_esperadas_real - horas_normais
            logger.info(f"📊 Horas negativas calculadas:")
            logger.info(f"   Esperadas: {horas_diarias_esperadas_real:.2f}h")
            logger.info(f"   Trabalhadas: {horas_normais:.2f}h")
            logger.info(f"   Deficit: {horas_negativas:.2f}h")

        return round(horas_normais, 2), round(horas_extras, 2), round(horas_negativas, 2)

    except Exception as e:
        logger.error(f"Erro ao calcular horas separadas: {e}")
        return 0.0, 0.0, 0.0

def calcular_horas_semanais_funcionario(funcionario_id, data_referencia):
    """
    ✅ FLEXÍVEL: Calcula horas semanais baseado no cadastro individual do funcionário
    Cada funcionário pode ter carga horária diferente (30h, 40h, 44h, etc.)

    Args:
        funcionario_id (int): ID do funcionário
        data_referencia (date): Data de referência para a semana

    Returns:
        dict: Informações sobre a jornada semanal personalizada
    """
    try:
        from datetime import timedelta

        # Encontrar segunda-feira da semana
        dias_desde_segunda = data_referencia.weekday()
        segunda_feira = data_referencia - timedelta(days=dias_desde_segunda)

        # ✅ OBTER HORAS SEMANAIS DO CADASTRO DO FUNCIONÁRIO
        jornada_info = obter_jornada_funcionario_real(funcionario_id)
        if not jornada_info:
            logger.error(f"Não foi possível obter jornada para funcionário {funcionario_id}")
            return None

        # ✅ FLEXÍVEL: Usar horas semanais do cadastro (não hardcoded)
        horas_semanais_obrigatorias = jornada_info.get('horas_semanais_obrigatorias', 44.0)
        dias_uteis = 5  # Segunda a sexta

        # ✅ DISTRIBUIÇÃO PROPORCIONAL: Baseada nas horas semanais do funcionário
        horas_por_dia = horas_semanais_obrigatorias / dias_uteis

        resultado = {
            'horas_semanais_obrigatorias': horas_semanais_obrigatorias,
            'horas_por_dia': round(horas_por_dia, 2),
            'jornada_info': jornada_info,
            'semana_referencia': {
                'segunda': segunda_feira,
                'sexta': segunda_feira + timedelta(days=4)
            },
            'distribuicao': {
                'segunda_a_sexta': f"{horas_por_dia:.2f}h/dia",
                'total_semanal': f"{horas_semanais_obrigatorias:.2f}h"
            }
        }

        logger.info(f"📊 Funcionário {funcionario_id}: {horas_semanais_obrigatorias}h semanais = {horas_por_dia:.2f}h/dia")
        return resultado

    except Exception as e:
        logger.error(f"Erro ao calcular jornada semanal do funcionário: {e}")
        return None

def calcular_horas_esperadas_por_dia(registro, funcionario_id):
    """
    Calcula quantas horas são esperadas por dia baseado na jornada REAL do funcionário

    Args:
        registro (dict): Registro do dia com data
        funcionario_id (int): ID do funcionário

    Returns:
        float: Horas esperadas para o dia baseado na jornada configurada
    """
    try:
        # Obter jornada real do funcionário
        jornada = obter_jornada_funcionario_real(funcionario_id)
        if not jornada:
            logger.error(f"❌ ERRO CRÍTICO: Jornada não encontrada para funcionário {funcionario_id}")
            logger.error(f"❌ FUNCIONÁRIO DEVE TER JORNADA CONFIGURADA - Não é possível calcular horas esperadas!")
            return 0.0  # ❌ REMOVIDO FALLBACK DE 8H FIXAS

        # Determinar dia da semana
        data_registro = registro.get('data')
        if isinstance(data_registro, str):
            from datetime import datetime
            data_registro = datetime.strptime(data_registro, '%Y-%m-%d').date()

        dia_semana = data_registro.weekday()  # 0=Segunda, 4=Sexta

        # Calcular horas baseado no dia da semana e jornada REAL
        if dia_semana <= 3:  # Segunda a Quinta (0,1,2,3)
            entrada = jornada['entrada_oficial']
            saida = jornada['saida_oficial']
        elif dia_semana == 4:  # Sexta (4)
            # 🎯 USAR JORNADA ESPECÍFICA DE SEXTA-FEIRA
            entrada = jornada['sexta_entrada'] or jornada['entrada_oficial']
            saida = jornada['sexta_saida'] or jornada['saida_oficial']
        else:
            return 0.0  # Fim de semana

        # Calcular horas da jornada
        from datetime import datetime, timedelta

        if isinstance(entrada, str):
            entrada = datetime.strptime(entrada, '%H:%M:%S').time()
        if isinstance(saida, str):
            saida = datetime.strptime(saida, '%H:%M:%S').time()

        entrada_dt = datetime.combine(datetime.today(), entrada)
        saida_dt = datetime.combine(datetime.today(), saida)

        # Se saída é no dia seguinte
        if saida_dt <= entrada_dt:
            saida_dt += timedelta(days=1)

        # Calcular total de horas da jornada
        total_jornada_segundos = (saida_dt - entrada_dt).total_seconds()
        total_jornada_horas = total_jornada_segundos / 3600

        # Subtrair intervalo obrigatório (1h padrão, mas pode ser configurado)
        intervalo_horas = 1.0  # Padrão, mas deveria vir da configuração
        horas_esperadas = total_jornada_horas - intervalo_horas

        logger.info(f"📊 Cálculo de horas esperadas para funcionário {funcionario_id}:")
        logger.info(f"   Entrada: {entrada}")
        logger.info(f"   Saída: {saida}")
        logger.info(f"   Total jornada: {total_jornada_horas:.2f}h")
        logger.info(f"   Intervalo: {intervalo_horas}h")
        logger.info(f"   Horas esperadas: {horas_esperadas:.2f}h")

        return max(0.0, round(horas_esperadas, 2))

    except Exception as e:
        logger.error(f"❌ ERRO CRÍTICO: Não foi possível calcular horas esperadas para funcionário {funcionario_id}: {e}")
        logger.error(f"❌ SISTEMA NÃO PODE USAR FALLBACK FIXO - Jornada deve ser configurada!")
        return 0.0  # ❌ REMOVIDO FALLBACK DE 8H FIXAS

def calcular_horas_esperadas_jornada_real(funcionario_id):
    """
    Calcula horas esperadas baseado na jornada REAL configurada do funcionário.

    REGRA CORRETA:
    1. Busca horário de entrada e saída da jornada do funcionário
    2. Calcula diferença entre entrada e saída
    3. Subtrai 1 hora de intervalo obrigatório
    4. Resultado = horas que o funcionário deve cumprir por dia

    Args:
        funcionario_id (int): ID do funcionário

    Returns:
        float: Horas esperadas por dia (ex: 8.0 para jornada 09:00-18:00)
    """
    try:
        db = DatabaseManager()

        # Query simplificada sem tabela alocacoes_funcionarios (que não existe)
        query = """
        SELECT
            f.nome_completo,
            f.empresa_id,
            -- Horário de entrada (prioridade: empresa > funcionário > jornada)
            COALESCE(
                TIME(ec_config.jornada_segunda_entrada),
                ht_funcionario.entrada_manha,
                jt.seg_qui_entrada
            ) as entrada_jornada,
            -- Horário de saída (prioridade: empresa > funcionário > jornada)
            COALESCE(
                TIME(ec_config.jornada_segunda_saida),
                ht_funcionario.saida,
                jt.seg_qui_saida
            ) as saida_jornada,
            -- Intervalo obrigatório
            COALESCE(
                ec_config.intervalo_obrigatorio,
                1.0
            ) as intervalo_horas,
            -- Informações para debug
            jt.nome_jornada,
            e.razao_social as empresa_nome
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        LEFT JOIN empresas_config ec_config ON f.empresa_id = ec_config.empresa_id
        LEFT JOIN horarios_trabalho ht_funcionario ON f.horario_trabalho_id = ht_funcionario.id
        WHERE f.id = %s
        """

        resultado = db.execute_query(query, (funcionario_id,))

        if not resultado:
            logger.error(f"❌ Funcionário {funcionario_id} não encontrado no banco de dados")
            return None

        dados = resultado[0]
        nome_funcionario = dados['nome_completo']
        entrada_jornada = dados['entrada_jornada']
        saida_jornada = dados['saida_jornada']
        intervalo_horas = dados['intervalo_horas'] or 1.0
        nome_jornada = dados['nome_jornada']
        empresa_nome = dados['empresa_nome']

        # Validar se tem horários configurados
        if not entrada_jornada or not saida_jornada:
            logger.error(f"❌ ERRO DE CONFIGURAÇÃO:")
            logger.error(f"   Funcionário: {nome_funcionario}")
            logger.error(f"   Empresa: {empresa_nome}")
            logger.error(f"   Jornada: {nome_jornada}")
            logger.error(f"   Entrada: {entrada_jornada}")
            logger.error(f"   Saída: {saida_jornada}")
            logger.error(f"❌ FUNCIONÁRIO DEVE TER JORNADA COMPLETA CONFIGURADA!")
            return None

        # Converter para datetime para calcular diferença
        from datetime import datetime, timedelta

        # Converter timedelta para time se necessário
        if hasattr(entrada_jornada, 'total_seconds'):
            # É timedelta, converter para time
            entrada_jornada = (datetime.min + entrada_jornada).time()
        if hasattr(saida_jornada, 'total_seconds'):
            # É timedelta, converter para time
            saida_jornada = (datetime.min + saida_jornada).time()

        entrada_dt = datetime.combine(datetime.today(), entrada_jornada)
        saida_dt = datetime.combine(datetime.today(), saida_jornada)

        # Se saída é no dia seguinte (jornada noturna)
        if saida_dt <= entrada_dt:
            saida_dt += timedelta(days=1)

        # Calcular total de horas da jornada
        total_jornada_segundos = (saida_dt - entrada_dt).total_seconds()
        total_jornada_horas = total_jornada_segundos / 3600

        # Subtrair intervalo obrigatório
        horas_esperadas = total_jornada_horas - intervalo_horas

        logger.info(f"✅ Jornada calculada para {nome_funcionario}:")
        logger.info(f"   📍 Empresa: {empresa_nome}")
        logger.info(f"   📋 Jornada: {nome_jornada}")
        logger.info(f"   🕐 Entrada: {entrada_jornada}")
        logger.info(f"   🕕 Saída: {saida_jornada}")
        logger.info(f"   ⏱️ Total jornada: {total_jornada_horas:.2f}h")
        logger.info(f"   ☕ Intervalo: {intervalo_horas}h")
        logger.info(f"   ✅ Horas esperadas: {horas_esperadas:.2f}h")

        return max(0.0, round(horas_esperadas, 2))

    except Exception as e:
        logger.error(f"❌ Erro ao calcular jornada real do funcionário {funcionario_id}: {e}")
        return None

def calcular_horas_trabalhadas_robusto(registro):
    """
    Calcula horas trabalhadas seguindo as regras robustas definidas:

    REGRA 1: Cálculo baseado no início (B1) e fim da jornada (B4)
    REGRA 2: B1 e B2 = período manhã (contar como horas trabalhadas)
    REGRA 3: B3 e B4 = período tarde (contar como horas trabalhadas)

    Args:
        registro (dict): Registro do dia com horários

    Returns:
        float: Total de horas trabalhadas
    """
    try:
        total_horas = 0.0
        detalhes = []

        # Extrair horários do registro
        entrada = registro.get('entrada')  # B1
        saida_almoco = registro.get('saida_almoco')  # B2
        retorno_almoco = registro.get('retorno_almoco')  # B3
        saida = registro.get('saida')  # B4

        logger.info(f"🔍 Analisando registro:")
        logger.info(f"   B1 (Entrada): {entrada}")
        logger.info(f"   B2 (Saída Almoço): {saida_almoco}")
        logger.info(f"   B3 (Retorno Almoço): {retorno_almoco}")
        logger.info(f"   B4 (Saída): {saida}")

        # PERÍODO MANHÃ: B1 e B2 (se ambos existem)
        if entrada and saida_almoco:
            # Converter para datetime para calcular diferença
            entrada_dt = datetime.combine(datetime.today(), entrada)
            saida_almoco_dt = datetime.combine(datetime.today(), saida_almoco)

            # Calcular horas do período manhã
            if saida_almoco_dt > entrada_dt:
                horas_manha = (saida_almoco_dt - entrada_dt).total_seconds() / 3600
                total_horas += horas_manha
                detalhes.append(f"Manhã (B1-B2): {horas_manha:.2f}h")
                logger.info(f"   ✅ Período manhã: {entrada} - {saida_almoco} = {horas_manha:.2f}h")
            else:
                logger.warning(f"   ⚠️ Horário de saída almoço anterior à entrada")

        # PERÍODO TARDE: B3 e B4 (se ambos existem)
        if retorno_almoco and saida:
            # Converter para datetime para calcular diferença
            retorno_dt = datetime.combine(datetime.today(), retorno_almoco)
            saida_dt = datetime.combine(datetime.today(), saida)

            # Calcular horas do período tarde
            if saida_dt > retorno_dt:
                horas_tarde = (saida_dt - retorno_dt).total_seconds() / 3600
                total_horas += horas_tarde
                detalhes.append(f"Tarde (B3-B4): {horas_tarde:.2f}h")
                logger.info(f"   ✅ Período tarde: {retorno_almoco} - {saida} = {horas_tarde:.2f}h")
            else:
                logger.warning(f"   ⚠️ Horário de saída anterior ao retorno")

        # CASOS ESPECIAIS
        if not entrada:
            logger.warning(f"   ❌ Sem entrada (B1) - 0 horas trabalhadas")
        elif entrada and not saida_almoco and not saida:
            logger.warning(f"   ⚠️ Apenas entrada (B1) - 0 horas trabalhadas")
        elif entrada and saida_almoco and not retorno_almoco and not saida:
            logger.info(f"   ⚠️ Apenas período manhã - sem retorno/saída")

        logger.info(f"   📊 Total calculado: {total_horas:.2f}h")
        if detalhes:
            logger.info(f"   📋 Detalhes: {' + '.join(detalhes)}")

        return round(total_horas, 2)

    except Exception as e:
        logger.error(f"❌ Erro no cálculo robusto: {e}")
        return 0.0

def calcular_horas_extras_robusto(registro):
    """
    Calcula horas extras (B5 e B6) de forma robusta.

    Args:
        registro (dict): Registro do dia com horários

    Returns:
        float: Total de horas extras
    """
    try:
        inicio_extra = registro.get('inicio_extra')  # B5
        fim_extra = registro.get('fim_extra')  # B6

        if inicio_extra and fim_extra:
            inicio_dt = datetime.combine(datetime.today(), inicio_extra)
            fim_dt = datetime.combine(datetime.today(), fim_extra)

            if fim_dt > inicio_dt:
                horas_extras = (fim_dt - inicio_dt).total_seconds() / 3600
                logger.info(f"   ✅ Horas extras (B5-B6): {inicio_extra} - {fim_extra} = {horas_extras:.2f}h")
                return round(horas_extras, 2)
            else:
                logger.warning(f"   ⚠️ Fim extra anterior ao início extra")

        return 0.0

    except Exception as e:
        logger.error(f"❌ Erro no cálculo de horas extras: {e}")
        return 0.0

def calcular_horas_normais_fallback(registro):
    """
    Cálculo normal de horas (fallback quando não tem jornada oficial)
    """
    horas_normais = 0.0

    # Período manhã
    if registro.get('entrada') and registro.get('saida_almoco'):
        entrada = registro['entrada']
        saida_almoco = registro['saida_almoco']

        if isinstance(entrada, str):
            entrada = datetime.strptime(entrada, '%H:%M:%S').time()
        if isinstance(saida_almoco, str):
            saida_almoco = datetime.strptime(saida_almoco, '%H:%M:%S').time()

        periodo_manha = datetime.combine(datetime.today(), saida_almoco) - datetime.combine(datetime.today(), entrada)
        horas_normais += periodo_manha.total_seconds() / 3600

    # Período tarde
    if registro.get('retorno_almoco') and registro.get('saida'):
        retorno_almoco = registro['retorno_almoco']
        saida = registro['saida']

        if isinstance(retorno_almoco, str):
            retorno_almoco = datetime.strptime(retorno_almoco, '%H:%M:%S').time()
        if isinstance(saida, str):
            saida = datetime.strptime(saida, '%H:%M:%S').time()

        periodo_tarde = datetime.combine(datetime.today(), saida) - datetime.combine(datetime.today(), retorno_almoco)
        horas_normais += periodo_tarde.total_seconds() / 3600

    return horas_normais

def calcular_horas_extras(registro):
    """
    Cálculo de horas extras (B5 e B6)
    """
    horas_extras = 0.0

    if registro.get('inicio_extra') and registro.get('fim_extra'):
        inicio_extra = registro['inicio_extra']
        fim_extra = registro['fim_extra']

        if isinstance(inicio_extra, str):
            inicio_extra = datetime.strptime(inicio_extra, '%H:%M:%S').time()
        if isinstance(fim_extra, str):
            fim_extra = datetime.strptime(fim_extra, '%H:%M:%S').time()

        periodo_extra = datetime.combine(datetime.today(), fim_extra) - datetime.combine(datetime.today(), inicio_extra)
        horas_extras += periodo_extra.total_seconds() / 3600

    return horas_extras

def detectar_irregularidades_informativo(registro):
    """
    FUNÇÃO APENAS INFORMATIVA - Detecta possíveis irregularidades
    NÃO AFETA CÁLCULOS - Apenas gera alertas para análise posterior

    Args:
        registro (dict): Registro do dia

    Returns:
        list: Lista de alertas informativos
    """
    alertas = []

    try:
        # Detectar entrada muito antecipada (antes das 7h)
        if registro.get('entrada'):
            entrada_str = str(registro['entrada'])
            if any(h in entrada_str for h in ['06:', '07:']):
                alertas.append('⚠️ Entrada antecipada')

        # Detectar almoço muito longo (mais de 2h)
        if registro.get('saida_almoco') and registro.get('retorno_almoco'):
            try:
                saida_almoco = registro['saida_almoco']
                retorno_almoco = registro['retorno_almoco']

                if isinstance(saida_almoco, str):
                    saida_almoco = datetime.strptime(saida_almoco, '%H:%M:%S').time()
                if isinstance(retorno_almoco, str):
                    retorno_almoco = datetime.strptime(retorno_almoco, '%H:%M:%S').time()

                saida_dt = datetime.combine(datetime.today(), saida_almoco)
                retorno_dt = datetime.combine(datetime.today(), retorno_almoco)
                duracao = (retorno_dt - saida_dt).total_seconds() / 60

                if duracao > 120:  # Mais de 2h
                    alertas.append(f'🍽️ Almoço longo ({int(duracao)}min)')
            except:
                pass

        # Detectar saída muito tardia (depois das 19h)
        if registro.get('saida'):
            saida_str = str(registro['saida'])
            if any(h in saida_str for h in ['19:', '20:', '21:', '22:']):
                alertas.append('🌙 Saída tardia')

        return alertas

    except Exception as e:
        logger.error(f"Erro na detecção de irregularidades: {e}")
        return []

def calcular_horas_trabalhadas_dia(registro):
    """
    Calcula as horas trabalhadas em um dia baseado nos registros de ponto

    Args:
        registro (dict): Registro do dia com entrada, saida_almoco, retorno_almoco, saida

    Returns:
        float: Horas trabalhadas no formato decimal
    """
    try:
        total_horas = 0.0

        # Verificar se tem entrada
        if not registro.get('entrada'):
            return 0.0

        entrada = registro['entrada']
        saida_almoco = registro.get('saida_almoco')
        retorno_almoco = registro.get('retorno_almoco')
        saida = registro.get('saida')

        # Converter time para datetime para cálculos
        from datetime import datetime, time

        def time_to_datetime(t):
            if isinstance(t, time):
                return datetime.combine(datetime.today(), t)
            return t

        entrada_dt = time_to_datetime(entrada)

        # Cenário 1: Jornada completa com intervalo (B1, B2, B3, B4)
        if entrada and saida_almoco and retorno_almoco and saida:
            saida_almoco_dt = time_to_datetime(saida_almoco)
            retorno_almoco_dt = time_to_datetime(retorno_almoco)
            saida_dt = time_to_datetime(saida)

            # Período manhã: entrada até saída para almoço
            periodo_manha = (saida_almoco_dt - entrada_dt).total_seconds() / 3600

            # Período tarde: retorno do almoço até saída
            periodo_tarde = (saida_dt - retorno_almoco_dt).total_seconds() / 3600

            total_horas = periodo_manha + periodo_tarde

        # Cenário 2: Jornada sem intervalo (B1, B4)
        elif entrada and saida and not saida_almoco and not retorno_almoco:
            saida_dt = time_to_datetime(saida)
            total_horas = (saida_dt - entrada_dt).total_seconds() / 3600

        # Cenário 3: Jornada incompleta - só entrada
        elif entrada and not saida:
            # Não podemos calcular sem saída
            total_horas = 0.0

        # Cenário 4: Outras combinações incompletas
        else:
            total_horas = 0.0

        # Garantir que não seja negativo
        return max(0.0, round(total_horas, 2))

    except Exception as e:
        logger.error(f"Erro ao calcular horas trabalhadas: {e}")
        return 0.0

def get_historico_alocacoes_funcionario(funcionario_id):
    """Obter histórico de alocações de um funcionário"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT
            fa.id,
            fa.data_inicio,
            fa.data_fim,
            fa.ativo,
            e.razao_social as cliente_nome,
            e.nome_fantasia as cliente_fantasia,
            fa.observacoes
        FROM funcionario_alocacoes fa
        LEFT JOIN empresas e ON fa.empresa_cliente_id = e.id
        WHERE fa.funcionario_id = %s
        ORDER BY fa.data_inicio DESC
        """

        return db.execute_query(sql, (funcionario_id,))

    except Exception as e:
        logger.error(f"Erro ao obter histórico de alocações do funcionário {funcionario_id}: {e}")
        return []

def get_historico_alocacoes_funcionario(funcionario_id):
    """Obter histórico completo de alocações de um funcionário"""
    try:
        # Retornar lista vazia por enquanto para evitar erros
        return []
    except Exception as e:
        logger.error(f"Erro ao obter histórico de alocações do funcionário {funcionario_id}: {e}")
        return []

def editar_registro_ponto(registro_id, campo, valor, justificativa):
    """Editar um registro de ponto"""
    try:
        db = DatabaseManager()

        # Validar e converter valor
        if valor and valor != '':
            try:
                # Converter para time se for horário
                if ':' in str(valor):
                    valor = datetime.strptime(str(valor), '%H:%M').time()
            except ValueError:
                logger.error(f"Formato de hora inválido: {valor}")
                return False
        else:
            valor = None

        # Atualizar registro
        sql = f"""
        UPDATE registros_ponto
        SET {campo} = %s,
            justificativa = CASE
                WHEN justificativa IS NULL OR justificativa = '' THEN %s
                ELSE CONCAT(justificativa, ' | ', %s)
            END,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """

        db.execute_query(sql, (valor, justificativa, justificativa, registro_id), fetch_all=False)
        return True

    except Exception as e:
        logger.error(f"Erro ao editar registro {registro_id}: {e}")
        return False

def get_logs_atividades_funcionario(funcionario_id, limite=50):
    """Obter logs de atividades de um funcionário"""
    try:
        db = DatabaseManager()

        # Buscar histórico do funcionário
        sql = """
        SELECT
            hf.tipo_evento as acao,
            hf.detalhes,
            hf.data_evento as data_acao,
            hf.status_aprovacao,
            u.nome_completo as usuario_nome
        FROM historico_funcionario hf
        LEFT JOIN usuarios u ON hf.aprovado_por = u.id
        WHERE hf.funcionario_id = %s
        ORDER BY hf.data_evento DESC
        LIMIT %s
        """

        result = db.execute_query(sql, (funcionario_id, limite))

        logger.info(f"📊 Logs encontrados para funcionário {funcionario_id}: {len(result) if result else 0}")

        return result if result else []

    except Exception as e:
        logger.error(f"Erro ao obter logs do funcionário {funcionario_id}: {e}")
        return []

def registrar_log_atividade(funcionario_id, acao, detalhes, usuario_id, registro_ponto_id=None):
    """Registrar log de atividade"""
    try:
        db = DatabaseManager()

        # Buscar nome do usuário
        sql_usuario = "SELECT nome FROM usuarios WHERE id = %s"
        usuario_result = db.execute_query(sql_usuario, (usuario_id,))
        usuario_nome = usuario_result[0]['nome'] if usuario_result else 'Sistema'

        sql = """
        INSERT INTO logs_atividades
        (funcionario_id, acao, detalhes, usuario_id, usuario_nome, registro_ponto_id, data_acao)
        VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
        """

        db.execute_query(sql, (funcionario_id, acao, detalhes, usuario_id, usuario_nome, registro_ponto_id), fetch_all=False)
        return True

    except Exception as e:
        logger.error(f"Erro ao registrar log: {e}")
        return False

# ================================================================
# ROTAS DE UPLOAD E DOCUMENTOS
# ================================================================

@ponto_admin_bp.route('/upload-documento', methods=['POST'])
@require_admin
def upload_documento():
    """Upload de documento comprobatório"""
    try:
        ensure_upload_folder()

        if 'documento' not in request.files:
            return jsonify({'success': False, 'message': 'Nenhum arquivo selecionado'})

        file = request.files['documento']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'Nenhum arquivo selecionado'})

        if file and allowed_file(file.filename):
            # Gerar nome único
            filename = secure_filename(file.filename)
            unique_filename = f"{uuid.uuid4()}_{filename}"
            filepath = os.path.join(UPLOAD_FOLDER, unique_filename)

            file.save(filepath)

            # Salvar referência no banco
            registro_id = request.form.get('registro_id')
            funcionario_id = request.form.get('funcionario_id')
            descricao = request.form.get('descricao', '')
            tipo_documento = request.form.get('tipo_documento', 'OUTROS')

            sucesso = salvar_documento_banco(registro_id, funcionario_id, unique_filename, filename, descricao, tipo_documento, file.content_type, file.content_length)

            if sucesso:
                # Registrar log
                registrar_log_atividade(
                    funcionario_id=funcionario_id,
                    acao='UPLOAD_DOCUMENTO',
                    detalhes=f"Documento enviado: {filename}. Descrição: {descricao}",
                    usuario_id=session.get('user_id'),
                    registro_ponto_id=registro_id
                )

                return jsonify({'success': True, 'message': 'Documento enviado com sucesso', 'filename': unique_filename})
            else:
                # Remover arquivo se falhou ao salvar no banco
                os.remove(filepath)
                return jsonify({'success': False, 'message': 'Erro ao salvar documento no banco'})
        else:
            return jsonify({'success': False, 'message': 'Tipo de arquivo não permitido'})

    except Exception as e:
        logger.error(f"Erro no upload de documento: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

def salvar_documento_banco(registro_id, funcionario_id, filename, nome_original, descricao, tipo_documento, mime_type, tamanho):
    """Salvar referência do documento no banco"""
    try:
        db = DatabaseManager()

        sql = """
        INSERT INTO documentos_ponto
        (registro_ponto_id, funcionario_id, nome_arquivo, nome_original, descricao,
         tipo_documento, tamanho_arquivo, mime_type, usuario_upload_id, data_upload)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
        """

        db.execute_query(sql, (registro_id, funcionario_id, filename, nome_original, descricao,
                              tipo_documento, tamanho, mime_type, session.get('user_id')), fetch_all=False)
        return True

    except Exception as e:
        logger.error(f"Erro ao salvar documento no banco: {e}")
        return False

@ponto_admin_bp.route('/listar-documentos-registro', methods=['GET'])
@require_login
def listar_documentos_registro():
    """Listar documentos anexados a um registro de ponto"""
    try:
        registro_id = request.args.get('registro_id')

        if not registro_id:
            return jsonify({'success': False, 'message': 'ID do registro não fornecido'})

        db = DatabaseManager()

        sql = """
        SELECT id, nome_arquivo, nome_original, descricao, tipo_documento,
               tamanho_arquivo, mime_type, data_upload, usuario_upload_id
        FROM documentos_ponto
        WHERE registro_ponto_id = %s AND ativo = TRUE
        ORDER BY data_upload DESC
        """

        documentos = db.execute_query(sql, (registro_id,))

        # Converter para formato JSON serializável
        documentos_json = []
        for doc in documentos:
            documentos_json.append({
                'id': doc['id'],
                'nome_arquivo': doc['nome_arquivo'],
                'nome_original': doc['nome_original'],
                'descricao': doc['descricao'],
                'tipo_documento': doc['tipo_documento'],
                'tamanho_arquivo': doc['tamanho_arquivo'],
                'mime_type': doc['mime_type'],
                'data_upload': doc['data_upload'].isoformat() if doc['data_upload'] else None,
                'usuario_upload_id': doc['usuario_upload_id']
            })

        return jsonify({'success': True, 'documentos': documentos_json})

    except Exception as e:
        logger.error(f"Erro ao listar documentos: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@ponto_admin_bp.route('/remover-documento', methods=['POST'])
@require_admin
def remover_documento():
    """Remover documento anexado"""
    try:
        data = request.get_json()
        documento_id = data.get('documento_id')

        if not documento_id:
            return jsonify({'success': False, 'message': 'ID do documento não fornecido'})

        db = DatabaseManager()

        # Buscar informações do documento antes de remover
        sql_info = "SELECT nome_arquivo, funcionario_id FROM documentos_ponto WHERE id = %s"
        doc_info = db.execute_query(sql_info, (documento_id,), fetch_all=False)

        if not doc_info:
            return jsonify({'success': False, 'message': 'Documento não encontrado'})

        # Marcar como inativo (soft delete)
        sql_remove = "UPDATE documentos_ponto SET ativo = FALSE WHERE id = %s"
        db.execute_query(sql_remove, (documento_id,), fetch_all=False)

        # Tentar remover arquivo físico
        try:
            filepath = os.path.join(UPLOAD_FOLDER, doc_info['nome_arquivo'])
            if os.path.exists(filepath):
                os.remove(filepath)
        except Exception as e:
            logger.warning(f"Erro ao remover arquivo físico: {e}")

        # Registrar log
        registrar_log_atividade(
            funcionario_id=doc_info['funcionario_id'],
            acao='REMOVER_DOCUMENTO',
            detalhes=f"Documento removido: {doc_info['nome_arquivo']}",
            usuario_id=session.get('user_id')
        )

        return jsonify({'success': True, 'message': 'Documento removido com sucesso'})

    except Exception as e:
        logger.error(f"Erro ao remover documento: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@ponto_admin_bp.route('/baixar-documento', methods=['GET'])
@require_login
def baixar_documento():
    """Baixar documento anexado"""
    try:
        nome_arquivo = request.args.get('arquivo')
        logger.info(f"Tentativa de download do arquivo: {nome_arquivo}")

        if not nome_arquivo:
            logger.error("Nome do arquivo não fornecido")
            return jsonify({'success': False, 'message': 'Nome do arquivo não fornecido'}), 400

        # Verificar se o arquivo existe no banco
        db = DatabaseManager()
        sql = "SELECT nome_original, mime_type FROM documentos_ponto WHERE nome_arquivo = %s AND ativo = TRUE"
        logger.info(f"Executando SQL: {sql} com parâmetro: {nome_arquivo}")
        doc_info = db.execute_query(sql, (nome_arquivo,), fetch_one=True)
        logger.info(f"Resultado da consulta: {doc_info}")

        if not doc_info:
            logger.error(f"Documento não encontrado no banco: {nome_arquivo}")
            return jsonify({'success': False, 'message': 'Documento não encontrado'}), 404

        # Caminho do arquivo
        filepath = os.path.join(UPLOAD_FOLDER, nome_arquivo)
        logger.info(f"Caminho do arquivo: {filepath}")
        logger.info(f"UPLOAD_FOLDER: {UPLOAD_FOLDER}")

        if not os.path.exists(filepath):
            logger.error(f"Arquivo físico não encontrado: {filepath}")
            return jsonify({'success': False, 'message': 'Arquivo físico não encontrado'}), 404

        # Retornar arquivo para download
        nome_original = doc_info['nome_original'] or nome_arquivo
        mime_type = doc_info['mime_type'] or 'application/octet-stream'
        logger.info(f"Iniciando download: {nome_original} ({mime_type})")

        return send_file(
            filepath,
            as_attachment=True,
            download_name=nome_original,
            mimetype=mime_type
        )

    except Exception as e:
        logger.error(f"Erro ao baixar documento: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'}), 500

# ================================================================
# ROTAS DE RELATÓRIOS - REMOVIDAS
# ================================================================
# ✅ Rota /relatorio-funcionario removida - funcionalidade eliminada do sistema

@ponto_admin_bp.route('/funcionario/<int:funcionario_id>/imprimir')
@require_login
def imprimir_ponto_funcionario(funcionario_id):
    """Página de impressão de ponto de um funcionário"""
    try:
        from datetime import datetime
        import traceback

        logger.info(f"🖨️ IMPRESSÃO: Iniciando impressão para funcionário {funcionario_id}")

        # Parâmetros de filtro
        data_inicio = request.args.get('data_inicio')
        data_fim = request.args.get('data_fim')
        logger.info(f"🖨️ IMPRESSÃO: Filtros - data_inicio={data_inicio}, data_fim={data_fim}")

        # Buscar funcionário diretamente do banco para evitar problemas
        try:
            db = DatabaseManager()
            sql = """
            SELECT
                f.*,
                e.razao_social as empresa_nome,
                e.nome_fantasia as empresa_fantasia,
                e.cnpj as empresa_cnpj
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            WHERE f.id = %s
            """
            result = db.execute_query(sql, (funcionario_id,))
            funcionario = result[0] if result else None
            logger.info(f"🖨️ IMPRESSÃO: Funcionário encontrado: {funcionario is not None}")
        except Exception as e:
            logger.error(f"🖨️ IMPRESSÃO: Erro ao buscar funcionário: {e}")
            funcionario = None

        if not funcionario:
            logger.error(f"🖨️ IMPRESSÃO: Funcionário {funcionario_id} não encontrado")
            # Criar funcionário fictício para não quebrar a impressão
            funcionario = {
                'id': funcionario_id,
                'nome_completo': f'Funcionário ID {funcionario_id}',
                'empresa_nome': 'Empresa não encontrada',
                'cnpj': 'N/A'
            }

        # Buscar registros de forma simplificada
        try:
            registros = get_registros_ponto_funcionario(funcionario_id, data_inicio, data_fim)
            if not registros:
                registros = []
            logger.info(f"🖨️ IMPRESSÃO: {len(registros)} registros encontrados")
        except Exception as e:
            logger.error(f"🖨️ IMPRESSÃO: Erro ao buscar registros: {e}")
            registros = []

        # Usar template específico para impressão
        logger.info(f"🖨️ IMPRESSÃO: Renderizando template de impressão")
        return render_template('ponto_admin/imprimir_ponto.html',
                             funcionario=funcionario,
                             registros=registros,
                             data_inicio=data_inicio,
                             data_fim=data_fim,
                             data_atual=datetime.now())
    except Exception as e:
        logger.error(f"🖨️ IMPRESSÃO: Erro crítico ao gerar página de impressão do funcionário {funcionario_id}: {e}")
        logger.error(f"🖨️ IMPRESSÃO: Traceback: {traceback.format_exc()}")

        # Em caso de erro crítico, retornar página de erro simples ao invés de redirect
        return f"""
        <!DOCTYPE html>
        <html>
        <head><title>Erro na Impressão</title></head>
        <body>
            <h1>Erro ao Gerar Página de Impressão</h1>
            <p>Funcionário ID: {funcionario_id}</p>
            <p>Erro: {str(e)}</p>
            <a href="/ponto-admin/">Voltar</a>
        </body>
        </html>
        """, 500

# ================================================================
# ROTAS PARA SISTEMA DE JUSTIFICATIVAS
# ================================================================

@ponto_admin_bp.route('/api/registro-detalhes/<int:funcionario_id>/<data_registro>')
@require_login
def api_registro_detalhes(funcionario_id, data_registro):
    """API: Buscar detalhes completos de um registro de ponto"""
    try:
        logger.info(f"🔍 API chamada: funcionario_id={funcionario_id}, data_registro={data_registro}")
        db = DatabaseManager()
        logger.info("✅ DatabaseManager inicializado")

        # Buscar registros do dia
        sql_registros = """
        SELECT
            id,
            tipo_registro,
            TIME(data_hora) as hora_registro,
            metodo_registro,
            status_pontualidade,
            observacoes
        FROM registros_ponto
        WHERE funcionario_id = %s
        AND DATE(data_hora) = %s
        ORDER BY data_hora ASC
        """

        logger.info(f"📊 Executando query: {sql_registros}")
        logger.info(f"📊 Parâmetros: funcionario_id={funcionario_id}, data_registro={data_registro}")
        registros_raw = db.execute_query(sql_registros, (funcionario_id, data_registro))
        logger.info(f"📊 Registros encontrados: {len(registros_raw) if registros_raw else 0}")
        logger.info(f"📊 Dados brutos: {registros_raw}")

        # Agrupar registros
        registro = {
            'entrada': None,
            'saida_almoco': None,
            'retorno_almoco': None,
            'saida': None,
            'metodo_registro': 'manual',
            'status_pontualidade': 'Pontual'
        }

        # ✅ CORREÇÃO: Variáveis para controlar status específico por tipo
        tem_saida = False
        status_saida = None
        status_geral = 'Pontual'
        justificativa_observacoes = None  # ✅ NOVO: Para capturar justificativa das observações

        for reg in registros_raw:
            hora = reg['hora_registro']
            if isinstance(hora, timedelta):
                total_seconds = int(hora.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                hora = f"{hours:02d}:{minutes:02d}"
            else:
                hora = str(hora)[:5]  # HH:MM

            if reg['tipo_registro'] == 'entrada_manha':
                registro['entrada'] = hora
            elif reg['tipo_registro'] == 'saida_almoco':
                registro['saida_almoco'] = hora
            elif reg['tipo_registro'] == 'entrada_tarde':
                registro['retorno_almoco'] = hora
            elif reg['tipo_registro'] == 'saida':
                registro['saida'] = hora
                tem_saida = True
                # ✅ CORREÇÃO: Só considerar "Saída Antecipada" se for registro de SAÍDA
                if reg['status_pontualidade'] == 'Saída Antecipada':
                    status_saida = 'Saída Antecipada'
                    # ✅ NOVO: Extrair justificativa das observações
                    if reg['observacoes'] and 'SAÍDA ANTECIPADA -' in reg['observacoes']:
                        justificativa_observacoes = reg['observacoes'].replace('SAÍDA ANTECIPADA - ', '').strip()

            # ✅ NOVO: Detectar justificativas de atraso nas observações
            if reg['observacoes'] and 'ATRASO JUSTIFICADO' in reg['observacoes']:
                if ' - ' in reg['observacoes']:
                    justificativa_observacoes = reg['observacoes'].split(' - ', 1)[1].strip()
                else:
                    # Fallback se não houver " - "
                    justificativa_observacoes = reg['observacoes'].replace('ATRASO JUSTIFICADO', '').strip()
                    # Remover informação de minutos se presente
                    import re
                    justificativa_observacoes = re.sub(r'\(\d+min\)', '', justificativa_observacoes).strip()

                logger.info(f"🔍 Detectada justificativa de atraso nas observações: {justificativa_observacoes}")

            # Pegar dados do último registro (exceto status_pontualidade)
            if reg['metodo_registro']:
                registro['metodo_registro'] = reg['metodo_registro']

            # ✅ CORREÇÃO: Acumular status de irregularidades (exceto saída antecipada sem saída)
            if reg['status_pontualidade'] and reg['status_pontualidade'] != 'Pontual':
                if reg['tipo_registro'] != 'saida' or reg['status_pontualidade'] != 'Saída Antecipada':
                    # Para outros tipos de irregularidade (atraso, etc.)
                    if reg['status_pontualidade'] == 'Atrasado':
                        status_geral = 'Atrasado'

        # ✅ CORREÇÃO: Determinar status final baseado na lógica correta
        if tem_saida and status_saida == 'Saída Antecipada':
            registro['status_pontualidade'] = 'Saída Antecipada'
        elif status_geral != 'Pontual':
            registro['status_pontualidade'] = status_geral
        else:
            registro['status_pontualidade'] = 'Pontual'

        # Buscar justificativa se existir
        sql_justificativa = """
        SELECT
            id,
            tipo_justificativa,
            motivo,
            descricao_funcionario,
            documento_nome,
            documento_caminho,
            status_aprovacao,
            observacoes_aprovador,
            data_aprovacao
        FROM justificativas_ponto
        WHERE funcionario_id = %s
        AND data_registro = %s
        ORDER BY criado_em DESC
        LIMIT 1
        """

        justificativa_result = db.execute_query(sql_justificativa, (funcionario_id, data_registro))
        justificativa = justificativa_result[0] if justificativa_result else None

        # ✅ NOVO: Se não há justificativa na tabela, mas há nas observações, criar justificativa temporária
        if not justificativa and justificativa_observacoes:
            # Determinar tipo de justificativa baseado no status ou observações
            tipo_justificativa = 'saida_antecipada'  # padrão

            # Verificar se é justificativa de atraso
            for reg in registros:
                if reg['observacoes'] and 'ATRASO JUSTIFICADO' in reg['observacoes']:
                    tipo_justificativa = 'atraso'
                    break
                elif reg['status_pontualidade'] == 'Atraso Justificado':
                    tipo_justificativa = 'atraso'
                    break

            justificativa = {
                'id': None,
                'tipo_justificativa': tipo_justificativa,
                'motivo': justificativa_observacoes,
                'descricao_funcionario': None,
                'documento_nome': None,
                'documento_caminho': None,
                'status_aprovacao': 'pendente',
                'observacoes_aprovador': None,
                'data_aprovacao': None
            }
            logger.info(f"✅ Justificativa extraída das observações: {tipo_justificativa} - {justificativa_observacoes}")

        # Converter data_aprovacao se existir
        if justificativa and justificativa.get('data_aprovacao'):
            justificativa['data_aprovacao'] = justificativa['data_aprovacao'].strftime('%Y-%m-%dT%H:%M')

        logger.info(f"✅ Retornando dados: registro={registro}, justificativa={justificativa}")
        return jsonify({
            'success': True,
            'registro': registro,
            'justificativa': justificativa
        })

    except Exception as e:
        import traceback
        logger.error(f"❌ Erro ao buscar detalhes do registro: {e}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'Erro interno: {str(e)}'})

@ponto_admin_bp.route('/salvar-registro', methods=['POST'])
@require_login
def salvar_registro():
    """Salvar alterações no registro de ponto e justificativas"""
    try:
        db = DatabaseManager()

        # Dados do formulário
        funcionario_id = request.form.get('funcionario_id')
        data_registro = request.form.get('data_registro')

        # Horários
        entrada = request.form.get('entrada')
        saida_almoco = request.form.get('saida_almoco')
        retorno_almoco = request.form.get('retorno_almoco')
        saida = request.form.get('saida')

        # Outros dados
        metodo_registro = request.form.get('metodo_registro', 'manual')
        status_pontualidade = request.form.get('status_pontualidade', 'Pontual')

        # Dados da justificativa
        tipo_justificativa = request.form.get('tipo_justificativa')
        motivo = request.form.get('motivo')
        descricao_funcionario = request.form.get('descricao_funcionario')
        status_aprovacao = request.form.get('status_aprovacao', 'pendente')
        observacoes_aprovador = request.form.get('observacoes_aprovador')
        data_aprovacao = request.form.get('data_aprovacao')

        # ✅ NOVO: Dados de aprovação do RH
        decisao_rh = request.form.get('decisao_rh')
        observacoes_rh = request.form.get('observacoes_rh')
        responsavel_decisao = request.form.get('responsavel_decisao')
        data_decisao = request.form.get('data_decisao')
        justificativa_id = request.form.get('justificativa_id')

        # Se há decisão do RH, sobrescrever dados de aprovação
        if decisao_rh:
            status_aprovacao = decisao_rh
            observacoes_aprovador = observacoes_rh
            data_aprovacao = data_decisao

            # ✅ NOVO: Processar justificativa aprovada e criar log no histórico
            if decisao_rh == 'aprovado':
                logger.info(f"🎯 Processando justificativa aprovada: ID={justificativa_id}")
                processar_justificativa_aprovada(
                    funcionario_id=funcionario_id,
                    data_registro=data_registro,
                    tipo_justificativa=tipo_justificativa,
                    motivo=motivo,
                    responsavel_decisao=responsavel_decisao,
                    observacoes_rh=observacoes_rh,
                    justificativa_id=justificativa_id
                )

        # Processar upload de documento
        documento_info = None
        if 'documento' in request.files:
            file = request.files['documento']
            if file and file.filename and allowed_file(file.filename):
                documento_info = processar_upload_documento(file, funcionario_id)

        # Atualizar/criar registros de ponto
        sucesso_registros = atualizar_registros_ponto(
            funcionario_id, data_registro, entrada, saida_almoco,
            retorno_almoco, saida, metodo_registro, status_pontualidade
        )

        # Salvar justificativa se fornecida
        logger.info(f"🔍 Verificando se deve salvar justificativa: tipo={tipo_justificativa}, motivo={motivo}, decisao_rh={decisao_rh}")

        if tipo_justificativa or motivo or decisao_rh:
            # ✅ CORREÇÃO: Se só há decisão RH, buscar dados da justificativa existente
            if decisao_rh and not tipo_justificativa and not motivo:
                logger.info(f"🔍 Buscando dados da justificativa existente para aprovação")
                db_temp = DatabaseManager()
                sql_existing = """
                SELECT tipo_justificativa, motivo, descricao_funcionario
                FROM justificativas_ponto
                WHERE funcionario_id = %s AND data_registro = %s
                """
                existing_data = db_temp.execute_query(sql_existing, (funcionario_id, data_registro))
                if existing_data:
                    tipo_justificativa = existing_data[0]['tipo_justificativa']
                    motivo = existing_data[0]['motivo']
                    if not descricao_funcionario:
                        descricao_funcionario = existing_data[0]['descricao_funcionario']
                    logger.info(f"✅ Dados obtidos: tipo={tipo_justificativa}, motivo={motivo}")

            logger.info(f"✅ Salvando justificativa com status: {status_aprovacao}")
            sucesso_justificativa = salvar_justificativa(
                funcionario_id, data_registro, tipo_justificativa, motivo,
                descricao_funcionario, status_aprovacao, observacoes_aprovador,
                data_aprovacao, documento_info
            )
        else:
            # ✅ NOVO: Verificar se há justificativa nas observações que precisa ser migrada
            sucesso_justificativa = verificar_e_migrar_justificativa_observacoes(
                funcionario_id, data_registro
            )

        if sucesso_registros and sucesso_justificativa:
            # Registrar log de atividade
            registrar_log_atividade(
                funcionario_id=funcionario_id,
                acao='EDICAO_REGISTRO_COMPLETA',
                detalhes=f"Registro de {data_registro} editado completamente",
                usuario_id=session.get('user_id')
            )

            return jsonify({'success': True, 'message': 'Registro salvo com sucesso'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao salvar algumas informações'})

    except Exception as e:
        logger.error(f"Erro ao salvar registro: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

def processar_upload_documento(file, funcionario_id):
    """Processar upload de documento"""
    try:
        # Criar diretório se não existir
        upload_dir = UPLOAD_FOLDER
        os.makedirs(upload_dir, exist_ok=True)

        # Gerar nome único
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        filepath = os.path.join(upload_dir, unique_filename)

        # Salvar arquivo
        file.save(filepath)

        return {
            'nome_original': filename,
            'nome_arquivo': unique_filename,
            'caminho': filepath,
            'tipo': file.content_type,
            'tamanho': os.path.getsize(filepath)
        }

    except Exception as e:
        logger.error(f"Erro no upload de documento: {e}")
        return None

def atualizar_registros_ponto(funcionario_id, data_registro, entrada, saida_almoco, retorno_almoco, saida, metodo_registro, status_pontualidade):
    """Atualizar registros de ponto de um dia"""
    try:
        db = DatabaseManager()

        # Mapear horários para tipos de registro
        horarios = {
            'entrada_manha': entrada,
            'saida_almoco': saida_almoco,
            'entrada_tarde': retorno_almoco,
            'saida': saida
        }

        for tipo_registro, horario in horarios.items():
            if horario:  # Se horário foi fornecido
                # Verificar se registro já existe
                sql_check = """
                SELECT id FROM registros_ponto
                WHERE funcionario_id = %s
                AND DATE(data_hora) = %s
                AND tipo_registro = %s
                """

                existing = db.execute_query(sql_check, (funcionario_id, data_registro, tipo_registro))

                # Criar datetime completo
                data_hora = f"{data_registro} {horario}:00"

                if existing:
                    # Atualizar registro existente
                    sql_update = """
                    UPDATE registros_ponto
                    SET data_hora = %s,
                        metodo_registro = %s,
                        status_pontualidade = %s,
                        atualizado_em = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    db.execute_query(sql_update, (data_hora, metodo_registro, status_pontualidade, existing[0]['id']), fetch_all=False)
                else:
                    # Criar novo registro
                    sql_insert = """
                    INSERT INTO registros_ponto
                    (funcionario_id, data_hora, tipo_registro, metodo_registro, status_pontualidade, criado_em)
                    VALUES (%s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                    """
                    db.execute_query(sql_insert, (funcionario_id, data_hora, tipo_registro, metodo_registro, status_pontualidade), fetch_all=False)

        return True

    except Exception as e:
        logger.error(f"Erro ao atualizar registros de ponto: {e}")
        return False

def salvar_justificativa(funcionario_id, data_registro, tipo_justificativa, motivo, descricao_funcionario, status_aprovacao, observacoes_aprovador, data_aprovacao, documento_info):
    """Salvar ou atualizar justificativa com processamento de banco de horas"""
    try:
        logger.info(f"🔄 INICIANDO salvar_justificativa: funcionario_id={funcionario_id}, data={data_registro}, status={status_aprovacao}")

        # ✅ NORMALIZAR: Converter 'aprovado' -> 'aprovada' e 'rejeitado' -> 'reprovada'
        if status_aprovacao == 'aprovado':
            status_aprovacao = 'aprovada'
            logger.info(f"🔄 Status normalizado: aprovado -> aprovada")
        elif status_aprovacao == 'rejeitado':
            status_aprovacao = 'reprovada'
            logger.info(f"🔄 Status normalizado: rejeitado -> reprovada")

        db = DatabaseManager()

        # Verificar se justificativa já existe
        sql_check = """
        SELECT id, status_aprovacao FROM justificativas_ponto
        WHERE funcionario_id = %s AND data_registro = %s
        """

        existing = db.execute_query(sql_check, (funcionario_id, data_registro))

        # ✅ NOVO: Detectar mudança no status de aprovação
        status_anterior = existing[0]['status_aprovacao'] if existing else None
        mudou_status = status_anterior != status_aprovacao and status_aprovacao in ['aprovada', 'reprovada']

        logger.info(f"🔄 Status anterior: {status_anterior}, novo: {status_aprovacao}, mudou: {mudou_status}")

        # ✅ VALIDAR TRANSIÇÃO DE STATUS
        if existing and status_anterior:
            validacao = validar_transicao_status_justificativa(
                status_anterior, status_aprovacao, existing[0]['id']
            )

            if not validacao['valido']:
                logger.error(f"❌ Transição inválida: {validacao['motivo']}")
                return {
                    'success': False,
                    'message': validacao['motivo'],
                    'codigo': validacao['codigo']
                }

        # Preparar dados do documento
        documento_nome = documento_info['nome_original'] if documento_info else None
        documento_caminho = documento_info['caminho'] if documento_info else None
        documento_tipo = documento_info['tipo'] if documento_info else None
        documento_tamanho = documento_info['tamanho'] if documento_info else None

        # Preparar data de aprovação
        data_aprovacao_formatted = None
        if data_aprovacao:
            try:
                data_aprovacao_formatted = datetime.strptime(data_aprovacao, '%Y-%m-%dT%H:%M').strftime('%Y-%m-%d %H:%M:%S')
            except:
                data_aprovacao_formatted = None

        if existing:
            # Atualizar justificativa existente
            sql_update = """
            UPDATE justificativas_ponto
            SET tipo_justificativa = %s,
                motivo = %s,
                descricao_funcionario = %s,
                status_aprovacao = %s,
                observacoes_aprovador = %s,
                data_aprovacao = %s,
                aprovado_por = %s,
                atualizado_em = CURRENT_TIMESTAMP
            """

            params = [tipo_justificativa, motivo, descricao_funcionario, status_aprovacao,
                     observacoes_aprovador, data_aprovacao_formatted, session.get('user_id')]

            # Adicionar campos de documento se fornecido
            if documento_info:
                sql_update += """,
                    documento_nome = %s,
                    documento_caminho = %s,
                    documento_tipo = %s,
                    documento_tamanho = %s
                """
                params.extend([documento_nome, documento_caminho, documento_tipo, documento_tamanho])

            sql_update += " WHERE id = %s"
            params.append(existing[0]['id'])

            db.execute_query(sql_update, params, fetch_all=False)
        else:
            # Criar nova justificativa
            sql_insert = """
            INSERT INTO justificativas_ponto
            (funcionario_id, data_registro, tipo_justificativa, motivo, descricao_funcionario,
             status_aprovacao, observacoes_aprovador, data_aprovacao, aprovado_por,
             documento_nome, documento_caminho, documento_tipo, documento_tamanho,
             criado_por, criado_em)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
            """

            db.execute_query(sql_insert, (
                funcionario_id, data_registro, tipo_justificativa, motivo, descricao_funcionario,
                status_aprovacao, observacoes_aprovador, data_aprovacao_formatted, session.get('user_id'),
                documento_nome, documento_caminho, documento_tipo, documento_tamanho,
                session.get('user_id')
            ), fetch_all=False)

        # ✅ NOVO: Processar impacto no banco de horas se status mudou para aprovada/reprovada
        if mudou_status:
            logger.info(f"🔄 Processando impacto no banco de horas: {status_aprovacao}")
            try:
                from app_banco_horas import processar_impacto_banco_horas_direto
                resultado = processar_impacto_banco_horas_direto(
                    funcionario_id, data_registro, tipo_justificativa, status_aprovacao
                )
                logger.info(f"✅ Resultado do processamento: {resultado}")
            except Exception as e:
                logger.error(f"❌ Erro ao processar banco de horas: {e}")

        # ✅ NOVO: Atualizar status_justificativa nos registros de ponto
        if status_aprovacao in ['aprovada', 'reprovada']:
            try:
                status_registro = 'abonado' if status_aprovacao == 'aprovada' else 'reprovado'

                sql_update_registros = """
                UPDATE registros_ponto
                SET status_justificativa = %s
                WHERE funcionario_id = %s AND DATE(data_hora) = %s
                """

                db.execute_query(sql_update_registros, (status_registro, funcionario_id, data_registro), fetch_all=False)
                logger.info(f"✅ Registros de ponto atualizados com status: {status_registro}")

            except Exception as e:
                logger.error(f"❌ Erro ao atualizar status nos registros de ponto: {e}")

        return True

    except Exception as e:
        logger.error(f"Erro ao salvar justificativa: {e}")
        return False

def verificar_e_migrar_justificativa_observacoes(funcionario_id, data_registro):
    """
    ✅ CORREÇÃO COMPLETA: Verifica e migra justificativas das observações para tabela
    """
    try:
        logger.info(f"🔍 INICIANDO migração de justificativas para funcionário {funcionario_id} na data {data_registro}")

        db = DatabaseManager()

        # ✅ BUSCAR TODOS OS REGISTROS DO DIA E FILTRAR NO PYTHON
        sql_buscar = """
        SELECT id, tipo_registro, observacoes, status_pontualidade
        FROM registros_ponto
        WHERE funcionario_id = %s
        AND DATE(data_hora) = %s
        AND observacoes IS NOT NULL
        """

        logger.info(f"🔍 Executando busca de registros com observações...")
        todos_registros = db.execute_query(sql_buscar, (funcionario_id, data_registro))

        # Filtrar registros com justificativas no Python
        registros = []
        if todos_registros:
            for reg in todos_registros:
                obs = reg.get('observacoes', '') or ''
                if 'SAÍDA ANTECIPADA' in obs or 'ATRASO JUSTIFICADO' in obs:
                    registros.append(reg)

        logger.info(f"📊 Total de registros: {len(todos_registros) if todos_registros else 0}, Com justificativas: {len(registros)}")

        if not registros:
            logger.info("ℹ️ Nenhum registro encontrado com justificativas nas observações")
            return True

        logger.info(f"✅ Encontrados {len(registros)} registros com justificativas: {registros}")

        # ✅ PROCESSAR CADA REGISTRO
        for registro in registros:
            observacoes = registro.get('observacoes', '')
            logger.info(f"🔍 Processando observação: '{observacoes}'")

            justificativa_texto = None
            tipo_justificativa = None

            # ✅ DETECTAR SAÍDA ANTECIPADA
            if 'SAÍDA ANTECIPADA' in observacoes:
                if ' - ' in observacoes:
                    justificativa_texto = observacoes.split(' - ', 1)[1].strip()
                else:
                    justificativa_texto = observacoes.replace('SAÍDA ANTECIPADA', '').strip()
                tipo_justificativa = 'saida_antecipada'
                logger.info(f"✅ Detectada SAÍDA ANTECIPADA: '{justificativa_texto}'")

            # ✅ DETECTAR ATRASO JUSTIFICADO
            elif 'ATRASO JUSTIFICADO' in observacoes:
                if ' - ' in observacoes:
                    justificativa_texto = observacoes.split(' - ', 1)[1].strip()
                else:
                    justificativa_texto = observacoes.replace('ATRASO JUSTIFICADO', '').strip()
                    # Remover informação de minutos se presente
                    import re
                    justificativa_texto = re.sub(r'\(\d+min\)', '', justificativa_texto).strip()

                tipo_justificativa = 'atraso'
                logger.info(f"✅ Detectado ATRASO JUSTIFICADO: '{justificativa_texto}'")

            # ✅ CRIAR JUSTIFICATIVA SE ENCONTRADA
            if justificativa_texto and tipo_justificativa and justificativa_texto.strip():
                logger.info(f"🔍 Verificando se justificativa já existe...")

                # Verificar se já existe
                sql_check = """
                SELECT id FROM justificativas_ponto
                WHERE funcionario_id = %s AND data_registro = %s AND tipo_justificativa = %s
                """

                existing = db.execute_query(sql_check, (funcionario_id, data_registro, tipo_justificativa))

                if not existing:
                    logger.info(f"📝 Criando nova justificativa: {tipo_justificativa} - '{justificativa_texto}'")

                    # Criar justificativa incluindo registro_ponto_id
                    sql_insert = """
                    INSERT INTO justificativas_ponto
                    (registro_ponto_id, funcionario_id, data_registro, tipo_justificativa, motivo,
                     status_aprovacao, criado_por, criado_em)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                    """

                    db.execute_query(sql_insert, (
                        registro['id'], funcionario_id, data_registro, tipo_justificativa, justificativa_texto,
                        'pendente', None  # criado_por pode ser NULL
                    ), fetch_all=False)

                    logger.info(f"✅ SUCESSO: Justificativa criada - {tipo_justificativa}: '{justificativa_texto}'")
                else:
                    logger.info(f"ℹ️ Justificativa já existe: {tipo_justificativa}")
            else:
                logger.info(f"⚠️ Justificativa vazia ou inválida: '{justificativa_texto}'")

        return True

    except Exception as e:
        import traceback
        logger.error(f"❌ ERRO na migração de justificativas: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return True  # Não falhar o processo principal

@ponto_admin_bp.route('/api/historico-aprovacoes/<int:justificativa_id>')
@require_login
def api_historico_aprovacoes(justificativa_id):
    """API: Buscar histórico de aprovações de uma justificativa"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT
            ha.campo_alterado,
            ha.valor_anterior,
            ha.valor_novo,
            ha.motivo_alteracao,
            ha.data_alteracao,
            u.nome_completo as aprovador
        FROM historico_alteracoes_ponto ha
        LEFT JOIN usuarios u ON ha.alterado_por = u.id
        WHERE ha.justificativa_id = %s
        ORDER BY ha.data_alteracao DESC
        """

        historico = db.execute_query(sql, (justificativa_id,))

        # Formatar dados
        historico_formatado = []
        for item in historico:
            historico_formatado.append({
                'status': f"{item['campo_alterado']}: {item['valor_anterior']} → {item['valor_novo']}",
                'data': item['data_alteracao'].strftime('%d/%m/%Y %H:%M'),
                'aprovador': item['aprovador'] or 'Sistema',
                'observacoes': item['motivo_alteracao']
            })

        return jsonify({
            'success': True,
            'historico': historico_formatado
        })

    except Exception as e:
        logger.error(f"Erro ao buscar histórico de aprovações: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@ponto_admin_bp.route('/download-documento/<int:justificativa_id>')
@require_login
def download_documento(justificativa_id):
    """Download de documento de justificativa"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT documento_nome, documento_caminho
        FROM justificativas_ponto
        WHERE id = %s
        """

        result = db.execute_query(sql, (justificativa_id,))

        if not result or not result[0]['documento_caminho']:
            flash('Documento não encontrado', 'error')
            return redirect(request.referrer or url_for('ponto_admin.index'))

        documento = result[0]

        if os.path.exists(documento['documento_caminho']):
            return send_file(
                documento['documento_caminho'],
                as_attachment=True,
                download_name=documento['documento_nome']
            )
        else:
            flash('Arquivo não encontrado no servidor', 'error')
            return redirect(request.referrer or url_for('ponto_admin.index'))

    except Exception as e:
        logger.error(f"Erro no download de documento: {e}")
        flash('Erro ao baixar documento', 'error')
        return redirect(request.referrer or url_for('ponto_admin.index'))

@ponto_admin_bp.route('/api/justificativa-detalhes/<int:funcionario_id>/<data_registro>')
@require_login
def api_justificativa_detalhes(funcionario_id, data_registro):
    """API: Buscar detalhes da justificativa para área de RH"""
    try:
        logger.info(f"🔍 API chamada: funcionario_id={funcionario_id}, data_registro={data_registro}")
        db = DatabaseManager()

        # Buscar justificativa atual
        sql_justificativa = """
        SELECT
            jp.id,
            jp.tipo_justificativa,
            jp.motivo,
            jp.descricao_funcionario,
            jp.status_aprovacao,
            jp.observacoes_aprovador,
            jp.data_aprovacao,
            jp.aprovado_por,
            u.nome_completo as aprovador_nome,
            jp.documento_nome,
            jp.documento_caminho,
            jp.criado_em
        FROM justificativas_ponto jp
        LEFT JOIN usuarios u ON jp.aprovado_por = u.id
        WHERE jp.funcionario_id = %s
        AND jp.data_registro = %s
        ORDER BY jp.criado_em DESC
        LIMIT 1
        """

        justificativa_result = db.execute_query(sql_justificativa, (funcionario_id, data_registro))
        justificativa = justificativa_result[0] if justificativa_result else None

        # ✅ NOVO: Verificar se há documentos anexados nos registros do dia
        logger.info(f"🔍 Chamando verificar_documentos_anexados_por_data para funcionário {funcionario_id} na data {data_registro}")
        documentos_anexados = verificar_documentos_anexados_por_data(funcionario_id, data_registro)
        logger.info(f"📊 Resultado da verificação de documentos: {documentos_anexados}")

        # ✅ NOVO: Se não há justificativa, verificar se há justificativas nas observações para migrar
        if not justificativa:
            logger.info(f"🔍 Não há justificativa na tabela, verificando observações para migração...")
            verificar_e_migrar_justificativa_observacoes(funcionario_id, data_registro)

            # Buscar novamente após migração
            justificativa_result = db.execute_query(sql_justificativa, (funcionario_id, data_registro))
            justificativa = justificativa_result[0] if justificativa_result else None

            if justificativa:
                logger.info(f"✅ Justificativa encontrada após migração: {justificativa['tipo_justificativa']} - {justificativa['motivo']}")
            else:
                logger.info(f"ℹ️ Nenhuma justificativa encontrada nas observações para migrar")

        # ✅ FORÇAR CRIAÇÃO: Se há documentos anexados, SEMPRE criar justificativa
        if documentos_anexados['total'] > 0:
            logger.info(f"📝 FORÇANDO criação de justificativa para {documentos_anexados['total']} documentos anexados")

            # Se já existe justificativa, usar ela; senão criar nova
            if not justificativa:
                logger.info("📝 Criando nova justificativa no banco de dados")

                # Criar justificativa no banco de dados
                motivo_justificativa = f'Documentos anexados: {documentos_anexados["total"]} arquivo(s) - {documentos_anexados["descricao"]}'

                sql_insert_justificativa = """
                INSERT INTO justificativas_ponto
                (funcionario_id, data_registro, tipo_justificativa, motivo, descricao_funcionario,
                 status_aprovacao, criado_por, criado_em)
                VALUES (%s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                """

                # Inserir no banco
                user_id = session.get('user_id')
                if not user_id:
                    # Buscar ID do usuário admin como fallback
                    sql_admin = "SELECT id FROM usuarios WHERE usuario = 'admin' LIMIT 1"
                    admin_result = db.execute_query(sql_admin)
                    user_id = admin_result[0]['id'] if admin_result else None

                logger.info(f"🔍 Inserindo justificativa com user_id: {user_id}")

                try:
                    result = db.execute_query(sql_insert_justificativa, (
                        funcionario_id,
                        data_registro,
                        'outros',
                        motivo_justificativa,
                        'Justificativa baseada em documentos anexados aos registros de ponto',
                        'pendente',
                        user_id
                    ), fetch_all=False)

                    logger.info(f"✅ Inserção executada com sucesso")

                    # Buscar a justificativa recém-criada
                    sql_buscar_nova = """
                    SELECT
                        id, tipo_justificativa, motivo, descricao_funcionario,
                        status_aprovacao, observacoes_aprovador, data_aprovacao,
                        aprovado_por, criado_em
                    FROM justificativas_ponto
                    WHERE funcionario_id = %s AND data_registro = %s
                    ORDER BY criado_em DESC LIMIT 1
                    """

                    justificativa_result = db.execute_query(sql_buscar_nova, (funcionario_id, data_registro))

                    if justificativa_result:
                        justificativa = justificativa_result[0]
                        logger.info(f"✅ Justificativa criada com ID {justificativa['id']}")
                    else:
                        logger.error("❌ Erro: Não foi possível recuperar a justificativa recém-criada")
                        # Criar justificativa temporária como fallback
                        justificativa = {
                            'id': 'temp_' + str(funcionario_id) + '_' + data_registro,
                            'tipo_justificativa': 'outros',
                            'motivo': motivo_justificativa,
                            'descricao_funcionario': 'Justificativa baseada em documentos anexados aos registros de ponto',
                            'status_aprovacao': 'pendente',
                            'observacoes_aprovador': None,
                            'data_aprovacao': None,
                            'aprovado_por': None,
                            'criado_em': None
                        }

                except Exception as insert_error:
                    logger.error(f"❌ Erro na inserção da justificativa: {insert_error}")
                    # Criar justificativa temporária em caso de erro
                    justificativa = {
                        'id': 'temp_' + str(funcionario_id) + '_' + data_registro,
                        'tipo_justificativa': 'outros',
                        'motivo': f'Documentos anexados: {documentos_anexados["total"]} arquivo(s)',
                        'descricao_funcionario': 'Justificativa baseada em documentos anexados aos registros de ponto',
                        'status_aprovacao': 'pendente',
                        'observacoes_aprovador': None,
                        'data_aprovacao': None,
                        'aprovado_por': None,
                        'criado_em': None
                    }

            # Adicionar informações sobre documentos anexados
            if justificativa:
                justificativa['origem'] = 'documentos_anexados'
                justificativa['documentos_anexados'] = documentos_anexados['documentos']
                justificativa['aprovador_nome'] = None

        # Buscar histórico de aprovações se existir justificativa
        historico = []
        if justificativa and justificativa.get('id'):
            sql_historico = """
            SELECT
                ha.campo_alterado,
                ha.valor_anterior,
                ha.valor_novo,
                ha.motivo_alteracao,
                ha.data_alteracao,
                u.nome_completo as aprovador
            FROM historico_alteracoes_ponto ha
            LEFT JOIN usuarios u ON ha.alterado_por = u.id
            WHERE ha.justificativa_id = %s
            AND ha.campo_alterado = 'status_aprovacao'
            ORDER BY ha.data_alteracao DESC
            """

            historico_result = db.execute_query(sql_historico, (justificativa['id'],))

            for item in historico_result:
                historico.append({
                    'status': item['valor_novo'],
                    'aprovador': item['aprovador'],
                    'data': item['data_alteracao'].strftime('%d/%m/%Y %H:%M') if item['data_alteracao'] else '',
                    'observacoes': item['motivo_alteracao']
                })

        # Converter datas para string se necessário
        if justificativa:
            for key, value in justificativa.items():
                if hasattr(value, 'strftime'):
                    justificativa[key] = value.strftime('%d/%m/%Y %H:%M')

        return jsonify({
            'success': True,
            'justificativa': justificativa,
            'historico': historico,
            'documentos_anexados': documentos_anexados if documentos_anexados['total'] > 0 else None,
            'tem_documentos_anexados': documentos_anexados['total'] > 0
        })

    except Exception as e:
        logger.error(f"❌ Erro ao buscar detalhes da justificativa: {e}")
        logger.error(f"❌ Tipo do erro: {type(e).__name__}")
        logger.error(f"❌ Detalhes do erro: {str(e)}")
        import traceback
        logger.error(f"❌ Stack trace: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'Erro interno: {str(e)}'})

def validar_transicao_status_justificativa(status_atual, novo_status, justificativa_id=None):
    """
    Validar se a transição de status é permitida

    Regras:
    - pendente -> aprovada/reprovada: ✅ Permitido
    - aprovada -> qualquer outro: ❌ Não permitido (decisão final)
    - reprovada -> qualquer outro: ❌ Não permitido (decisão final)
    - em_analise -> aprovada/reprovada/pendente: ✅ Permitido
    """
    try:
        logger.info(f"🔍 Validando transição: {status_atual} -> {novo_status} (ID: {justificativa_id})")

        # Estados finais não podem ser alterados
        if status_atual in ['aprovada', 'reprovada']:
            return {
                'valido': False,
                'motivo': f'Justificativa já foi {status_atual}. Decisões finais não podem ser alteradas.',
                'codigo': 'DECISAO_FINAL'
            }

        # Transições válidas
        transicoes_validas = {
            'pendente': ['aprovada', 'reprovada', 'em_analise'],
            'em_analise': ['aprovada', 'reprovada', 'pendente']
        }

        if status_atual not in transicoes_validas:
            return {
                'valido': False,
                'motivo': f'Status atual "{status_atual}" não é reconhecido.',
                'codigo': 'STATUS_INVALIDO'
            }

        if novo_status not in transicoes_validas[status_atual]:
            return {
                'valido': False,
                'motivo': f'Transição de "{status_atual}" para "{novo_status}" não é permitida.',
                'codigo': 'TRANSICAO_INVALIDA'
            }

        logger.info(f"✅ Transição válida: {status_atual} -> {novo_status}")
        return {
            'valido': True,
            'motivo': 'Transição permitida',
            'codigo': 'OK'
        }

    except Exception as e:
        logger.error(f"❌ Erro ao validar transição: {e}")
        return {
            'valido': False,
            'motivo': 'Erro interno na validação',
            'codigo': 'ERRO_INTERNO'
        }

@ponto_admin_bp.route('/api/test-documentos/<int:funcionario_id>/<data_registro>')
@require_login
def test_documentos_anexados(funcionario_id, data_registro):
    """Rota de teste para verificar documentos anexados"""
    try:
        logger.info(f"🧪 TESTE: Verificando documentos para funcionário {funcionario_id} na data {data_registro}")

        # Teste direto da função
        resultado = verificar_documentos_anexados_por_data(funcionario_id, data_registro)

        return jsonify({
            'success': True,
            'funcionario_id': funcionario_id,
            'data_registro': data_registro,
            'resultado': resultado
        })

    except Exception as e:
        logger.error(f"❌ Erro no teste de documentos: {e}")
        return jsonify({'success': False, 'error': str(e)})

@ponto_admin_bp.route('/api/validar-decisao-justificativa', methods=['POST'])
@require_login
def validar_decisao_justificativa():
    """API: Validar decisão antes de salvar (com confirmação)"""
    try:
        data = request.get_json()
        funcionario_id = data.get('funcionario_id')
        data_registro = data.get('data_registro')
        nova_decisao = data.get('decisao_rh')  # 'aprovada' ou 'reprovada'
        observacoes = data.get('observacoes_rh', '')

        # ✅ NORMALIZAR: Converter 'aprovado' -> 'aprovada' e 'rejeitado' -> 'reprovada'
        if nova_decisao == 'aprovado':
            nova_decisao = 'aprovada'
        elif nova_decisao == 'rejeitado':
            nova_decisao = 'reprovada'

        logger.info(f"🔍 Validando decisão: funcionário {funcionario_id}, data {data_registro}, decisão: {nova_decisao}")
        logger.info(f"🔍 Dados recebidos - funcionario_id: {funcionario_id} (tipo: {type(funcionario_id)})")
        logger.info(f"🔍 Dados recebidos - data_registro: {data_registro} (tipo: {type(data_registro)})")
        logger.info(f"🔍 Dados recebidos - nova_decisao: {nova_decisao} (tipo: {type(nova_decisao)})")

        if not all([funcionario_id, data_registro, nova_decisao]):
            logger.error(f"❌ Dados obrigatórios faltando: funcionario_id={funcionario_id}, data_registro={data_registro}, nova_decisao={nova_decisao}")
            return jsonify({
                'success': False,
                'message': 'Dados obrigatórios não fornecidos'
            })

        db = DatabaseManager()

        # Buscar justificativa atual (incluindo as criadas automaticamente por documentos)
        sql_justificativa = """
        SELECT
            id,
            status_aprovacao,
            motivo,
            tipo_justificativa,
            aprovado_por,
            data_aprovacao,
            descricao_funcionario
        FROM justificativas_ponto
        WHERE funcionario_id = %s AND data_registro = %s
        ORDER BY criado_em DESC LIMIT 1
        """

        logger.info(f"🔍 Executando query SQL: {sql_justificativa}")
        logger.info(f"🔍 Parâmetros: funcionario_id={funcionario_id}, data_registro={data_registro}")

        justificativa_result = db.execute_query(sql_justificativa, (funcionario_id, data_registro))

        logger.info(f"🔍 Resultado da query: {justificativa_result}")
        logger.info(f"🔍 Número de resultados: {len(justificativa_result) if justificativa_result else 0}")

        if not justificativa_result:
            # ✅ NOVO: Verificar se há documentos anexados e criar justificativa automaticamente
            logger.info(f"🔍 Não há justificativa, verificando documentos anexados...")
            documentos_anexados = verificar_documentos_anexados_por_data(funcionario_id, data_registro)
            logger.info(f"📊 Resultado da verificação de documentos: {documentos_anexados}")

            if documentos_anexados['total'] > 0:
                logger.info(f"📝 CRIANDO justificativa automaticamente para {documentos_anexados['total']} documentos anexados")

                # Criar justificativa no banco de dados
                motivo_justificativa = f'Documentos anexados: {documentos_anexados["total"]} arquivo(s) - {documentos_anexados["descricao"]}'

                # Buscar um registro de ponto da data para usar como referência
                sql_registro_ref = """
                SELECT id FROM registros_ponto
                WHERE funcionario_id = %s AND DATE(data_hora) = %s
                ORDER BY data_hora ASC LIMIT 1
                """

                registro_ref_result = db.execute_query(sql_registro_ref, (funcionario_id, data_registro))
                registro_ponto_id = registro_ref_result[0]['id'] if registro_ref_result else None

                if not registro_ponto_id:
                    logger.error(f"❌ Nenhum registro de ponto encontrado para a data {data_registro}")
                    return jsonify({
                        'success': False,
                        'message': 'Nenhum registro de ponto encontrado para criar justificativa'
                    })

                sql_insert_justificativa = """
                INSERT INTO justificativas_ponto
                (registro_ponto_id, funcionario_id, data_registro, tipo_justificativa, motivo, descricao_funcionario,
                 status_aprovacao, criado_por, criado_em)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                """

                # Inserir no banco
                user_id = session.get('user_id')
                if not user_id:
                    # Buscar ID do usuário admin como fallback
                    sql_admin = "SELECT id FROM usuarios WHERE usuario = 'admin' LIMIT 1"
                    admin_result = db.execute_query(sql_admin)
                    user_id = admin_result[0]['id'] if admin_result else None

                logger.info(f"🔍 Inserindo justificativa com user_id: {user_id}, registro_ponto_id: {registro_ponto_id}")

                try:
                    result = db.execute_query(sql_insert_justificativa, (
                        registro_ponto_id,
                        funcionario_id,
                        data_registro,
                        'outros',
                        motivo_justificativa,
                        'Justificativa baseada em documentos anexados aos registros de ponto',
                        'pendente',
                        user_id
                    ), fetch_all=False)

                    logger.info(f"✅ Justificativa criada automaticamente com sucesso")

                    # Buscar a justificativa recém-criada
                    justificativa_result = db.execute_query(sql_justificativa, (funcionario_id, data_registro))

                except Exception as insert_error:
                    logger.error(f"❌ Erro ao criar justificativa automaticamente: {insert_error}")
                    return jsonify({
                        'success': False,
                        'message': f'Erro ao criar justificativa automaticamente: {str(insert_error)}'
                    })

            if not justificativa_result:
                return jsonify({
                    'success': False,
                    'message': 'Nenhuma justificativa encontrada para validar'
                })

        justificativa = justificativa_result[0]
        status_atual = justificativa['status_aprovacao']
        tipo_justificativa = justificativa['tipo_justificativa']

        logger.info(f"✅ Justificativa encontrada: ID {justificativa['id']}, tipo: {tipo_justificativa}, status: {status_atual}")

        # Mensagem específica para justificativas baseadas em documentos
        if tipo_justificativa == 'outros':
            logger.info(f"📎 Validando justificativa baseada em documentos anexados")

        # Validar transição
        validacao = validar_transicao_status_justificativa(
            status_atual, nova_decisao, justificativa['id']
        )

        if not validacao['valido']:
            return jsonify({
                'success': False,
                'message': validacao['motivo'],
                'codigo': validacao['codigo'],
                'pode_alterar': False
            })

        # Buscar nome do funcionário para confirmação
        try:
            sql_funcionario = "SELECT nome_completo FROM funcionarios WHERE id = %s"
            funcionario_result = db.execute_query(sql_funcionario, (funcionario_id,))
            nome_funcionario = funcionario_result[0]['nome_completo'] if funcionario_result else f"ID {funcionario_id}"
            logger.info(f"✅ Nome do funcionário encontrado: {nome_funcionario}")
        except Exception as e:
            logger.error(f"❌ Erro ao buscar nome do funcionário: {e}")
            nome_funcionario = f"Funcionário ID {funcionario_id}"

        # Preparar dados para confirmação
        confirmacao_data = {
            'funcionario_nome': nome_funcionario,
            'data_registro': data_registro,
            'tipo_justificativa': justificativa['tipo_justificativa'],
            'motivo': justificativa['motivo'],
            'status_atual': status_atual,
            'nova_decisao': nova_decisao,
            'observacoes': observacoes,
            'decisao_final': nova_decisao in ['aprovada', 'reprovada'],
            'pode_reverter': False  # Decisões são finais
        }

        response_data = {
            'success': True,
            'validacao': validacao,
            'confirmacao': confirmacao_data,
            'pode_alterar': True,
            'requer_confirmacao': True
        }

        logger.info(f"✅ Retornando resposta de validação: {response_data}")
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"❌ Erro ao validar decisão: {e}")
        return jsonify({
            'success': False,
            'message': 'Erro interno na validação'
        })

def verificar_documentos_anexados_por_data(funcionario_id, data_registro):
    """Verificar se há documentos anexados nos registros de ponto de uma data específica"""
    try:
        logger.info(f"🔍 Verificando documentos anexados para funcionário {funcionario_id} na data {data_registro}")
        db = DatabaseManager()

        # Buscar registros de ponto da data específica
        sql_registros = """
        SELECT id, data_hora FROM registros_ponto
        WHERE funcionario_id = %s
        AND DATE(data_hora) = %s
        ORDER BY data_hora ASC
        """

        logger.info(f"📊 Executando query para buscar registros: {sql_registros}")
        logger.info(f"📊 Parâmetros: funcionario_id={funcionario_id}, data_registro={data_registro}")

        registros = db.execute_query(sql_registros, (funcionario_id, data_registro))
        logger.info(f"📊 Registros encontrados: {len(registros) if registros else 0}")

        if registros:
            logger.info(f"📊 Detalhes dos registros: {registros}")

        if not registros:
            logger.info("❌ Nenhum registro encontrado para a data")
            return {'total': 0, 'documentos': [], 'descricao': ''}

        # Buscar documentos anexados para todos os registros da data
        registro_ids = [str(r['id']) for r in registros]
        logger.info(f"📊 IDs dos registros encontrados: {registro_ids}")

        placeholders = ','.join(['%s'] * len(registro_ids))

        sql_documentos = f"""
        SELECT
            dp.id,
            dp.registro_ponto_id,
            dp.nome_original,
            dp.descricao,
            dp.tipo_documento,
            dp.data_upload,
            rp.data_hora as data_registro
        FROM documentos_ponto dp
        JOIN registros_ponto rp ON dp.registro_ponto_id = rp.id
        WHERE dp.registro_ponto_id IN ({placeholders})
        AND dp.ativo = 1
        ORDER BY dp.data_upload DESC
        """

        logger.info(f"📊 Executando query para buscar documentos: {sql_documentos}")
        logger.info(f"📊 Parâmetros: {registro_ids}")

        documentos = db.execute_query(sql_documentos, registro_ids)
        logger.info(f"📊 Documentos encontrados: {len(documentos) if documentos else 0}")

        # Criar descrição dos documentos
        descricao_docs = []
        if documentos:
            for doc in documentos:
                descricao_docs.append(f"{doc['nome_original']} ({doc['tipo_documento'] or 'Documento'})")

        resultado = {
            'total': len(documentos) if documentos else 0,
            'documentos': documentos if documentos else [],
            'descricao': ', '.join(descricao_docs[:3]) + ('...' if len(descricao_docs) > 3 else '')
        }

        logger.info(f"✅ Resultado da verificação de documentos: {resultado['total']} documentos encontrados")
        return resultado

    except Exception as e:
        logger.error(f"Erro ao verificar documentos anexados: {e}")
        return {'total': 0, 'documentos': [], 'descricao': ''}

def processar_justificativa_aprovada(funcionario_id, data_registro, tipo_justificativa, motivo,
                                   responsavel_decisao, observacoes_rh, justificativa_id):
    """
    Processa justificativa aprovada e cria log detalhado no histórico do funcionário
    identificando o período específico da jornada onde ocorreu a justificativa
    """
    try:
        logger.info(f"🎯 INICIANDO processar_justificativa_aprovada: funcionario_id={funcionario_id}, justificativa_id={justificativa_id}")
        db = DatabaseManager()

        # 1. Buscar dados do funcionário e empresa
        sql_funcionario = """
        SELECT
            f.id,
            f.nome_completo,
            f.empresa_id,
            f.matricula_empresa,
            e.nome_fantasia as empresa_nome,
            e.cnpj as empresa_cnpj
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        WHERE f.id = %s
        """

        funcionario_result = db.execute_query(sql_funcionario, (funcionario_id,))
        if not funcionario_result:
            logger.error(f"Funcionário {funcionario_id} não encontrado")
            return

        funcionario = funcionario_result[0]

        # 2. Buscar registros de ponto do dia para identificar período específico
        sql_registros = """
        SELECT
            tipo_registro,
            TIME(data_hora) as hora_registro,
            data_hora,
            status_pontualidade
        FROM registros_ponto
        WHERE funcionario_id = %s
        AND DATE(data_hora) = %s
        ORDER BY data_hora ASC
        """

        registros_result = db.execute_query(sql_registros, (funcionario_id, data_registro))
        registros_dia = registros_result if registros_result else []

        # 3. Buscar jornada de trabalho do funcionário
        sql_jornada = """
        SELECT
            jt.nome_jornada,
            jt.seg_qui_entrada,
            jt.intervalo_inicio as seg_qui_saida_almoco,
            jt.intervalo_fim as seg_qui_entrada_almoco,
            jt.seg_qui_saida,
            jt.sexta_entrada,
            jt.intervalo_inicio as sexta_saida_almoco,
            jt.intervalo_fim as sexta_entrada_almoco,
            jt.sexta_saida,
            jt.tolerancia_entrada_minutos as tolerancia_entrada,
            jt.tolerancia_saida_minutos as tolerancia_saida
        FROM funcionarios f
        LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.id = %s
        """

        jornada_result = db.execute_query(sql_jornada, (funcionario_id,))
        jornada = jornada_result[0] if jornada_result else None

        # 4. Buscar documento da justificativa
        sql_documento = """
        SELECT documento_nome, documento_caminho
        FROM justificativas_ponto
        WHERE id = %s
        """

        documento_result = db.execute_query(sql_documento, (justificativa_id,))
        documento = documento_result[0] if documento_result else None

        # 5. Analisar período específico da justificativa
        periodo_info = analisar_periodo_justificativa(
            registros_dia, jornada, data_registro, tipo_justificativa, motivo
        )

        # 6. Criar log detalhado no histórico do funcionário
        criar_log_justificativa_aprovada(
            funcionario=funcionario,
            periodo_info=periodo_info,
            tipo_justificativa=tipo_justificativa,
            motivo=motivo,
            documento=documento,
            responsavel_decisao=responsavel_decisao,
            observacoes_rh=observacoes_rh,
            data_registro=data_registro
        )

        logger.info(f"✅ Justificativa processada e log criado para funcionário {funcionario_id}")

    except Exception as e:
        logger.error(f"Erro ao processar justificativa aprovada: {e}")

def analisar_periodo_justificativa(registros_dia, jornada, data_registro, tipo_justificativa, motivo):
    """
    Analisa os registros do dia e identifica o período específico onde ocorreu a justificativa
    """
    try:
        from datetime import datetime, time

        # Converter data_registro para objeto date
        if isinstance(data_registro, str):
            data_obj = datetime.strptime(data_registro, '%Y-%m-%d').date()
        else:
            data_obj = data_registro

        # Determinar se é sexta-feira (weekday 4) ou seg-qui (0-3)
        dia_semana = data_obj.weekday()
        is_sexta = dia_semana == 4

        # Definir horários esperados baseado na jornada
        if jornada and is_sexta:
            entrada_esperada = jornada['sexta_entrada']
            saida_almoco_esperada = jornada['sexta_saida_almoco']
            entrada_almoco_esperada = jornada['sexta_entrada_almoco']
            saida_esperada = jornada['sexta_saida']
        elif jornada:
            entrada_esperada = jornada['seg_qui_entrada']
            saida_almoco_esperada = jornada['seg_qui_saida_almoco']
            entrada_almoco_esperada = jornada['seg_qui_entrada_almoco']
            saida_esperada = jornada['seg_qui_saida']
        else:
            # Horários padrão se não há jornada configurada
            entrada_esperada = time(8, 0)
            saida_almoco_esperada = time(12, 0)
            entrada_almoco_esperada = time(13, 0)
            saida_esperada = time(17, 0)

        # Organizar registros por tipo
        registros_organizados = {}
        for registro in registros_dia:
            registros_organizados[registro['tipo_registro']] = registro

        # Analisar cada período e identificar problemas
        periodos_analisados = []

        # PERÍODO MANHÃ (Entrada)
        if 'entrada_manha' in registros_organizados:
            entrada_real = registros_organizados['entrada_manha']['hora_registro']
            status_pontualidade = registros_organizados['entrada_manha']['status_pontualidade']

            if status_pontualidade == 'Atrasado':
                atraso_minutos = calcular_diferenca_minutos(entrada_esperada, entrada_real)
                periodos_analisados.append({
                    'periodo': 'ENTRADA_MANHÃ',
                    'horario_esperado': entrada_esperada.strftime('%H:%M'),
                    'horario_real': entrada_real.strftime('%H:%M'),
                    'problema': 'ATRASO',
                    'minutos_diferenca': atraso_minutos,
                    'descricao': f'Atraso de {atraso_minutos} minutos na entrada da manhã'
                })
        else:
            # Falta na entrada da manhã
            periodos_analisados.append({
                'periodo': 'ENTRADA_MANHÃ',
                'horario_esperado': entrada_esperada.strftime('%H:%M'),
                'horario_real': 'NÃO REGISTRADO',
                'problema': 'FALTA',
                'minutos_diferenca': 0,
                'descricao': 'Ausência no período da manhã'
            })

        # PERÍODO ALMOÇO (Saída e Retorno)
        if 'saida_almoco' in registros_organizados:
            saida_almoco_real = registros_organizados['saida_almoco']['hora_registro']

            # Verificar se saiu muito cedo ou muito tarde para o almoço
            diferenca_saida = calcular_diferenca_minutos(saida_almoco_esperada, saida_almoco_real)
            if abs(diferenca_saida) > 15:  # Tolerância de 15 minutos
                periodos_analisados.append({
                    'periodo': 'SAÍDA_ALMOÇO',
                    'horario_esperado': saida_almoco_esperada.strftime('%H:%M'),
                    'horario_real': saida_almoco_real.strftime('%H:%M'),
                    'problema': 'HORÁRIO_IRREGULAR',
                    'minutos_diferenca': diferenca_saida,
                    'descricao': f'Saída para almoço {diferenca_saida} minutos {"após" if diferenca_saida > 0 else "antes"} do previsto'
                })

        if 'entrada_tarde' in registros_organizados:
            entrada_tarde_real = registros_organizados['entrada_tarde']['hora_registro']

            # Verificar atraso no retorno do almoço
            atraso_retorno = calcular_diferenca_minutos(entrada_almoco_esperada, entrada_tarde_real)
            if atraso_retorno > 10:  # Tolerância de 10 minutos
                periodos_analisados.append({
                    'periodo': 'RETORNO_ALMOÇO',
                    'horario_esperado': entrada_almoco_esperada.strftime('%H:%M'),
                    'horario_real': entrada_tarde_real.strftime('%H:%M'),
                    'problema': 'ATRASO',
                    'minutos_diferenca': atraso_retorno,
                    'descricao': f'Atraso de {atraso_retorno} minutos no retorno do almoço'
                })

        # PERÍODO TARDE (Saída)
        if 'saida' in registros_organizados:
            saida_real = registros_organizados['saida']['hora_registro']

            # Verificar saída antecipada
            diferenca_saida = calcular_diferenca_minutos(saida_real, saida_esperada)
            if diferenca_saida > 15:  # Saiu mais de 15 min antes
                periodos_analisados.append({
                    'periodo': 'SAÍDA_TARDE',
                    'horario_esperado': saida_esperada.strftime('%H:%M'),
                    'horario_real': saida_real.strftime('%H:%M'),
                    'problema': 'SAÍDA_ANTECIPADA',
                    'minutos_diferenca': diferenca_saida,
                    'descricao': f'Saída antecipada em {diferenca_saida} minutos'
                })
        else:
            # Falta na saída (não registrou saída)
            periodos_analisados.append({
                'periodo': 'SAÍDA_TARDE',
                'horario_esperado': saida_esperada.strftime('%H:%M'),
                'horario_real': 'NÃO REGISTRADO',
                'problema': 'FALTA',
                'minutos_diferenca': 0,
                'descricao': 'Não registrou saída do expediente'
            })

        # Identificar período principal da justificativa baseado no tipo e motivo
        periodo_principal = identificar_periodo_principal_justificativa(
            tipo_justificativa, motivo, periodos_analisados
        )

        return {
            'periodo_principal': periodo_principal,
            'periodos_analisados': periodos_analisados,
            'jornada_nome': jornada['nome_jornada'] if jornada else 'Jornada Padrão',
            'dia_semana': 'Sexta-feira' if is_sexta else 'Segunda a Quinta',
            'total_registros': len(registros_dia),
            'registros_completos': len(registros_dia) == 4
        }

    except Exception as e:
        logger.error(f"Erro ao analisar período da justificativa: {e}")
        return {
            'periodo_principal': 'PERÍODO_INDETERMINADO',
            'periodos_analisados': [],
            'jornada_nome': 'Erro na análise',
            'dia_semana': 'Indeterminado',
            'total_registros': 0,
            'registros_completos': False
        }

def calcular_diferenca_minutos(horario1, horario2):
    """
    Calcula diferença em minutos entre dois horários
    """
    try:
        from datetime import datetime, time

        # Converter para datetime se necessário
        if isinstance(horario1, time):
            dt1 = datetime.combine(datetime.today(), horario1)
        else:
            dt1 = datetime.combine(datetime.today(), horario1)

        if isinstance(horario2, time):
            dt2 = datetime.combine(datetime.today(), horario2)
        else:
            dt2 = datetime.combine(datetime.today(), horario2)

        # Calcular diferença em minutos
        diferenca = (dt2 - dt1).total_seconds() / 60
        return int(diferenca)

    except Exception as e:
        logger.error(f"Erro ao calcular diferença de minutos: {e}")
        return 0

def identificar_periodo_principal_justificativa(tipo_justificativa, motivo, periodos_analisados):
    """
    Identifica o período principal da justificativa baseado no tipo, motivo e análise dos registros
    """
    try:
        # Mapear tipos de justificativa para períodos
        mapeamento_tipos = {
            'ATRASO': ['ENTRADA_MANHÃ', 'RETORNO_ALMOÇO'],
            'FALTA': ['ENTRADA_MANHÃ', 'SAÍDA_TARDE'],
            'SAIDA_ANTECIPADA': ['SAÍDA_TARDE'],
            'AUSENCIA_PARCIAL': ['ENTRADA_MANHÃ', 'SAÍDA_TARDE'],
            'ATESTADO_MEDICO': ['ENTRADA_MANHÃ', 'SAÍDA_TARDE', 'RETORNO_ALMOÇO'],
            'COMPROMISSO_MEDICO': ['ENTRADA_MANHÃ', 'SAÍDA_TARDE', 'RETORNO_ALMOÇO'],
            'EMERGENCIA_FAMILIAR': ['ENTRADA_MANHÃ', 'SAÍDA_TARDE', 'RETORNO_ALMOÇO'],
            'PROBLEMA_TRANSPORTE': ['ENTRADA_MANHÃ', 'RETORNO_ALMOÇO']
        }

        # Buscar períodos com problemas identificados
        periodos_com_problema = [p for p in periodos_analisados if p['problema'] in ['ATRASO', 'FALTA', 'SAÍDA_ANTECIPADA']]

        # Se há períodos com problema, priorizar o mais relevante
        if periodos_com_problema:
            # Prioridade: FALTA > ATRASO > SAÍDA_ANTECIPADA
            for prioridade in ['FALTA', 'ATRASO', 'SAÍDA_ANTECIPADA']:
                for periodo in periodos_com_problema:
                    if periodo['problema'] == prioridade:
                        return periodo['periodo']

        # Se não há problemas identificados, usar mapeamento por tipo
        periodos_possiveis = mapeamento_tipos.get(tipo_justificativa, ['PERÍODO_GERAL'])

        # Retornar primeiro período possível
        return periodos_possiveis[0] if periodos_possiveis else 'PERÍODO_GERAL'

    except Exception as e:
        logger.error(f"Erro ao identificar período principal: {e}")
        return 'PERÍODO_INDETERMINADO'

def criar_log_justificativa_aprovada(funcionario, periodo_info, tipo_justificativa, motivo,
                                   documento, responsavel_decisao, observacoes_rh, data_registro):
    """
    Cria log detalhado no histórico do funcionário quando justificativa é aprovada
    """
    try:
        db = DatabaseManager()

        # Construir detalhes do log
        periodo_principal = periodo_info['periodo_principal']
        jornada_nome = periodo_info['jornada_nome']
        dia_semana = periodo_info['dia_semana']

        # Encontrar detalhes do período principal
        detalhes_periodo = None
        for periodo in periodo_info['periodos_analisados']:
            if periodo['periodo'] == periodo_principal:
                detalhes_periodo = periodo
                break

        # Construir descrição detalhada
        descricao_base = f"JUSTIFICATIVA APROVADA - {tipo_justificativa}"

        if detalhes_periodo:
            descricao_detalhada = f"""
{descricao_base}

📋 DETALHES DA OCORRÊNCIA:
• Período: {periodo_principal}
• Problema: {detalhes_periodo['problema']}
• Horário Esperado: {detalhes_periodo['horario_esperado']}
• Horário Real: {detalhes_periodo['horario_real']}
• Descrição: {detalhes_periodo['descricao']}

🏢 INFORMAÇÕES DA EMPRESA:
• Empresa: {funcionario['empresa_nome']}
• CNPJ: {funcionario['empresa_cnpj']}
• Matrícula: {funcionario['matricula_empresa']}

⏰ JORNADA DE TRABALHO:
• Jornada: {jornada_nome}
• Dia da Semana: {dia_semana}
• Registros Completos: {'Sim' if periodo_info['registros_completos'] else 'Não'}

📄 JUSTIFICATIVA:
• Motivo: {motivo}
• Documento: {documento['documento_nome'] if documento else 'Nenhum documento anexado'}

✅ APROVAÇÃO:
• Aprovado por: {responsavel_decisao}
• Observações do RH: {observacoes_rh}
• Data da Aprovação: {datetime.now().strftime('%d/%m/%Y %H:%M')}
            """.strip()
        else:
            descricao_detalhada = f"""
{descricao_base}

📋 DETALHES DA OCORRÊNCIA:
• Período: {periodo_principal}
• Tipo: {tipo_justificativa}
• Motivo: {motivo}

🏢 INFORMAÇÕES DA EMPRESA:
• Empresa: {funcionario['empresa_nome']}
• CNPJ: {funcionario['empresa_cnpj']}
• Matrícula: {funcionario['matricula_empresa']}

⏰ JORNADA DE TRABALHO:
• Jornada: {jornada_nome}
• Dia da Semana: {dia_semana}

📄 JUSTIFICATIVA:
• Documento: {documento['documento_nome'] if documento else 'Nenhum documento anexado'}

✅ APROVAÇÃO:
• Aprovado por: {responsavel_decisao}
• Observações do RH: {observacoes_rh}
• Data da Aprovação: {datetime.now().strftime('%d/%m/%Y %H:%M')}
            """.strip()

        # Determinar tipo de evento baseado no período e problema
        tipo_evento = mapear_tipo_evento_historico(periodo_principal, tipo_justificativa)

        # Buscar ID do usuário aprovador
        sql_usuario = "SELECT id FROM usuarios WHERE nome_completo = %s LIMIT 1"
        usuario_result = db.execute_query(sql_usuario, (responsavel_decisao,))
        aprovado_por_id = usuario_result[0]['id'] if usuario_result else None

        # Inserir no histórico do funcionário
        sql_historico = """
        INSERT INTO historico_funcionario (
            funcionario_id, tipo_evento, data_evento, data_referencia,
            detalhes, status_aprovacao, aprovado_por, observacoes
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """

        params_historico = (
            funcionario['id'],
            tipo_evento,
            datetime.now(),
            data_registro,
            descricao_detalhada,
            'APROVADO',
            aprovado_por_id,
            f"Justificativa aprovada pelo RH - Período: {periodo_principal}"
        )

        historico_id = db.execute_query(sql_historico, params_historico, fetch_all=False)

        logger.info(f"✅ Log de justificativa criado no histórico: ID {historico_id}")

        return historico_id

    except Exception as e:
        logger.error(f"Erro ao criar log de justificativa aprovada: {e}")
        return None

def corrigir_justificativa_aprovada_sem_historico(justificativa_id):
    """
    Corrige justificativas que foram aprovadas mas não têm histórico criado
    """
    try:
        logger.info(f"🔧 Corrigindo justificativa {justificativa_id} sem histórico")

        db = DatabaseManager()

        # Buscar dados da justificativa
        sql_justificativa = """
        SELECT j.*, f.nome_completo, f.empresa_id
        FROM justificativas_ponto j
        JOIN funcionarios f ON j.funcionario_id = f.id
        WHERE j.id = %s AND j.status_aprovacao = 'aprovada'
        """

        result = db.execute_query(sql_justificativa, (justificativa_id,))
        if not result:
            logger.warning(f"Justificativa {justificativa_id} não encontrada ou não aprovada")
            return False

        justificativa = result[0]

        # Verificar se já existe histórico
        sql_check = """
        SELECT COUNT(*) as count FROM historico_funcionario
        WHERE funcionario_id = %s AND data_referencia = %s
        AND tipo_evento LIKE %s
        """

        check_result = db.execute_query(sql_check, (justificativa['funcionario_id'], justificativa['data_registro'], '%JUSTIFICATIVA%'))
        if check_result and check_result[0]['count'] > 0:
            logger.info(f"Histórico já existe para justificativa {justificativa_id}")
            return True

        # Criar histórico retroativo
        historico_id = processar_justificativa_aprovada(
            funcionario_id=justificativa['funcionario_id'],
            data_registro=justificativa['data_registro'],
            tipo_justificativa=justificativa['tipo_justificativa'],
            motivo=justificativa['motivo'],
            responsavel_decisao='RH',
            observacoes_rh=justificativa['observacoes_aprovador'] or 'Justificativa aprovada pelo RH',
            justificativa_id=justificativa_id
        )

        if historico_id:
            logger.info(f"✅ Histórico criado retroativamente: ID {historico_id}")
            return True
        else:
            logger.error(f"❌ Falha ao criar histórico retroativo")
            return False

    except Exception as e:
        logger.error(f"Erro ao corrigir justificativa {justificativa_id}: {e}")
        return False

@ponto_admin_bp.route('/api/corrigir-justificativa/<int:justificativa_id>')
@require_login
def api_corrigir_justificativa(justificativa_id):
    """API temporária para corrigir justificativas sem histórico"""
    try:
        sucesso = corrigir_justificativa_aprovada_sem_historico(justificativa_id)

        if sucesso:
            return jsonify({
                'success': True,
                'message': f'Justificativa {justificativa_id} corrigida com sucesso'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'Falha ao corrigir justificativa {justificativa_id}'
            })

    except Exception as e:
        logger.error(f"Erro na API de correção: {e}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

def mapear_tipo_evento_historico(periodo_principal, tipo_justificativa):
    """
    Mapeia período e tipo de justificativa para tipo de evento do histórico
    """
    try:
        # Mapeamento específico por período
        mapeamento_periodo = {
            'ENTRADA_MANHÃ': 'ATRASO_JUSTIFICADO_APROVADO',
            'RETORNO_ALMOÇO': 'ATRASO_RETORNO_JUSTIFICADO_APROVADO',
            'SAÍDA_TARDE': 'SAIDA_ANTECIPADA_JUSTIFICADA_APROVADA',
            'SAÍDA_ALMOÇO': 'HORARIO_ALMOCO_JUSTIFICADO_APROVADO'
        }

        # Mapeamento por tipo de justificativa
        mapeamento_tipo = {
            'ATESTADO_MEDICO': 'ATESTADO_MEDICO_APROVADO',
            'FALTA': 'FALTA_JUSTIFICADA_APROVADA',
            'AUSENCIA_PARCIAL': 'AUSENCIA_JUSTIFICADA_APROVADA',
            'EMERGENCIA_FAMILIAR': 'EMERGENCIA_JUSTIFICADA_APROVADA',
            'COMPROMISSO_MEDICO': 'COMPROMISSO_MEDICO_APROVADO'
        }

        # Priorizar mapeamento por tipo se disponível
        if tipo_justificativa in mapeamento_tipo:
            return mapeamento_tipo[tipo_justificativa]

        # Senão, usar mapeamento por período
        if periodo_principal in mapeamento_periodo:
            return mapeamento_periodo[periodo_principal]

        # Fallback genérico
        return 'JUSTIFICATIVA_APROVADA'

    except Exception as e:
        logger.error(f"Erro ao mapear tipo de evento: {e}")
        return 'JUSTIFICATIVA_APROVADA'
