/**
 * RLPONTO-WEB - Comunicação Direta com Bridge Biométrico Local
 * Este arquivo implementa comunicação direta com a bridge biométrica local,
 * removendo a dependência do backend para detecção de dispositivos.
 * 
 * <AUTHOR> Assistant for Richardson Rodrigues - AiNexus Tecnologia
 * @version 1.0
 * @date 2025-06-xx
 */

class BiometricBridge {
    constructor(options = {}) {
        // Configuração do bridge
        this.config = {
            bridgeUrl: options.bridgeUrl || 'http://localhost:8080',
            timeout: options.timeout || 10000,
            debug: options.debug || false
        };

        // Estado do bridge
        this.state = {
            isAvailable: false,
            lastCheck: null,
            lastError: null
        };

        // Event listeners
        this.eventListeners = {
            'bridge-status-change': [],
            'device-detected': [],
            'error': [],
            'test-complete': []
        };

        // Inicialização
        this.initialize();
    }

    /**
     * Inicializa o bridge e verifica disponibilidade
     */
    async initialize() {
        try {
            this.log('Inicializando BiometricBridge...');
            await this.checkBridgeAvailability();
        } catch (error) {
            this.log('Erro na inicialização:', error);
            this.dispatchEvent('error', {
                code: 'INIT_ERROR',
                message: 'Erro ao inicializar bridge: ' + error.message
            });
        }
    }

    /**
     * Verifica disponibilidade do bridge local
     */
    async checkBridgeAvailability() {
        try {
            this.log('Verificando disponibilidade do bridge...');
            
            const response = await this.fetchWithTimeout(`${this.config.bridgeUrl}/api/bridge-status`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.log('Bridge disponível:', data);
                
                const wasAvailable = this.state.isAvailable;
                this.state.isAvailable = true;
                this.state.lastCheck = new Date();
                this.state.lastError = null;
                
                // Notifica mudança de status se necessário
                if (!wasAvailable) {
                    this.dispatchEvent('bridge-status-change', {
                        available: true,
                        status: data.status,
                        version: data.bridge_version,
                        message: data.message
                    });
                }
                
                return true;
            } else {
                throw new Error('Bridge não está respondendo corretamente');
            }
        } catch (error) {
            this.log('Erro ao verificar bridge:', error);
            
            const wasAvailable = this.state.isAvailable;
            this.state.isAvailable = false;
            this.state.lastCheck = new Date();
            this.state.lastError = error.message;
            
            // Notifica mudança de status se necessário
            if (wasAvailable) {
                this.dispatchEvent('bridge-status-change', {
                    available: false,
                    message: error.message
                });
            }
            
            return false;
        }
    }

    /**
     * Detecta dispositivos biométricos via bridge local
     */
    async detectDevices() {
        try {
            this.log('Iniciando detecção de dispositivos...');
            
            // Verificar disponibilidade do bridge
            if (!this.state.isAvailable) {
                await this.checkBridgeAvailability();
                if (!this.state.isAvailable) {
                    throw new Error('Bridge biométrico não está disponível');
                }
            }
            
            // Chamar API de detecção do bridge local
            const response = await this.fetchWithTimeout(`${this.config.bridgeUrl}/api/detect-biometric-devices`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.log('Dispositivos detectados:', data);
                
                if (data.success) {
                    // Formatar dispositivos para o padrão da UI
                    const formattedDevices = this.formatDevices(data.dispositivos || []);
                    
                    // Notificar detecção
                    this.dispatchEvent('device-detected', formattedDevices);
                    
                    return formattedDevices;
                } else {
                    throw new Error(data.message || 'Falha na detecção');
                }
            } else {
                throw new Error('Falha na comunicação com bridge local');
            }
        } catch (error) {
            this.log('Erro na detecção:', error);
            this.dispatchEvent('error', {
                code: 'DETECTION_ERROR',
                message: error.message
            });
            throw error;
        }
    }

    /**
     * Testa comunicação com dispositivo específico
     */
    async testDevice(deviceId) {
        try {
            this.log(`Testando dispositivo: ${deviceId}`);
            
            // Verificar disponibilidade do bridge
            if (!this.state.isAvailable) {
                await this.checkBridgeAvailability();
                if (!this.state.isAvailable) {
                    throw new Error('Bridge biométrico não está disponível');
                }
            }
            
            // Chamar API de teste do bridge local
            const response = await this.fetchWithTimeout(`${this.config.bridgeUrl}/api/test-device/${deviceId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.log('Resultado do teste:', data);
                
                // Notificar resultado do teste
                this.dispatchEvent('test-complete', {
                    success: data.success,
                    message: data.message,
                    result: data.result
                });
                
                return data;
            } else {
                throw new Error('Falha na comunicação com bridge local');
            }
        } catch (error) {
            this.log('Erro no teste:', error);
            this.dispatchEvent('error', {
                code: 'TEST_ERROR',
                message: error.message
            });
            throw error;
        }
    }

    /**
     * Formata dispositivos detectados para o formato esperado pela UI
     */
    formatDevices(bridgeDevices) {
        return bridgeDevices.map(device => {
            return {
                id: device.instance_id || device.device_id || generateDeviceId(device),
                name: device.friendly_name || 'Dispositivo Biométrico',
                manufacturer: device.manufacturer || 'Desconhecido',
                type: device.device_type || 'fingerprint',
                driver: device.driver || 'Bridge Local',
                status: device.status || 'OK',
                bridge_info: {
                    detection_method: device.detection_method || 'bridge_local',
                    vendor_id: device.vendor_id || '',
                    product_id: device.product_id || '',
                    supported: device.supported || false
                }
            };
        });
    }

    /**
     * Gera ID para dispositivo caso não tenha
     */
    generateDeviceId(device) {
        const vid = device.vendor_id || 'UNKNOWN';
        const pid = device.product_id || 'UNKNOWN';
        const timestamp = Date.now().toString(36);
        return `BIOMETRIC_${vid}_${pid}_${timestamp}`;
    }

    /**
     * Fetch com timeout
     */
    fetchWithTimeout(url, options = {}) {
        const timeout = options.timeout || this.config.timeout;
        
        return Promise.race([
            fetch(url, options),
            new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Timeout')), timeout)
            )
        ]);
    }

    /**
     * Registra event listener
     */
    addEventListener(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].push(callback);
        }
    }

    /**
     * Remove event listener
     */
    removeEventListener(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event] = this.eventListeners[event].filter(
                cb => cb !== callback
            );
        }
    }

    /**
     * Dispara evento
     */
    dispatchEvent(event, data) {
        if (this.eventListeners[event]) {
            for (const callback of this.eventListeners[event]) {
                callback(data);
            }
        }
    }

    /**
     * Função de log
     */
    log(...args) {
        if (this.config.debug) {
            console.log('[BiometricBridge]', ...args);
        }
    }
}

// Exportar para uso global
window.BiometricBridge = BiometricBridge;