#!/usr/bin/env python3
"""
Teste da restauração com Kalebe
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager, FuncionarioQueries

def test_restaurar_kalebe():
    """Testa a restauração do Kalebe"""
    print("🔍 TESTE: RESTAURAÇÃO DO KALEBE")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar se Kalebe está na tabela de desligados
        print("📋 1. VERIFICANDO KALEBE NA TABELA DE DESLIGADOS:")
        kalebe_desligados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa, data_desligamento
            FROM funcionarios_desligados 
            WHERE nome_completo LIKE %s
        """, ('%KALEBE%',))
        
        if not kalebe_desligados:
            print("   ❌ Kalebe não encontrado na tabela de desligados")
            return False
        
        kalebe = kalebe_desligados[0]
        print(f"   ✅ Kalebe encontrado:")
        print(f"      ID Original: {kalebe['funcionario_id_original']}")
        print(f"      Matrícula: {kalebe['matricula_empresa']}")
        print(f"      Data Desligamento: {kalebe['data_desligamento']}")
        
        # 2. Verificar se existe na tabela principal
        print(f"\n📋 2. VERIFICANDO KALEBE NA TABELA PRINCIPAL:")
        kalebe_principal = db.execute_query("""
            SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
            FROM funcionarios 
            WHERE matricula_empresa = %s
        """, (kalebe['matricula_empresa'],))
        
        if kalebe_principal:
            func = kalebe_principal[0]
            status_ativo = "ATIVO" if func['ativo'] else "INATIVO"
            print(f"   ✅ Kalebe encontrado na tabela principal:")
            print(f"      ID: {func['id']}, Status: {func['status_cadastro']}, Ativo: {status_ativo}")
        else:
            print("   ❌ Kalebe não encontrado na tabela principal")
        
        # 3. Testar restauração
        print(f"\n📋 3. TESTANDO RESTAURAÇÃO:")
        resultado = FuncionarioQueries.restaurar_funcionario(kalebe['funcionario_id_original'])
        
        print(f"   Success: {resultado['success']}")
        print(f"   Message: {resultado['message']}")
        
        if resultado['success']:
            print("   ✅ SUCESSO: Função de restauração funcionou!")
            
            # 4. Verificar resultado
            print(f"\n📋 4. VERIFICANDO RESULTADO:")
            
            # Verificar na tabela principal
            kalebe_restaurado = db.execute_query("""
                SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
                FROM funcionarios 
                WHERE matricula_empresa = %s
            """, (kalebe['matricula_empresa'],))
            
            if kalebe_restaurado:
                func = kalebe_restaurado[0]
                status_ativo = "ATIVO" if func['ativo'] else "INATIVO"
                print(f"   ✅ Kalebe na tabela principal:")
                print(f"      ID: {func['id']}, Status: {func['status_cadastro']}, Ativo: {status_ativo}")
                
                if func['ativo'] and func['status_cadastro'] == 'Ativo':
                    print("   ✅ PERFEITO: Kalebe está ativo!")
                else:
                    print("   ❌ PROBLEMA: Kalebe não está ativo")
                    return False
            else:
                print("   ❌ Kalebe não encontrado na tabela principal")
                return False
            
            # Verificar se foi removido da tabela de desligados
            kalebe_ainda_desligado = db.execute_query("""
                SELECT COUNT(*) as total
                FROM funcionarios_desligados 
                WHERE funcionario_id_original = %s
            """, (kalebe['funcionario_id_original'],))
            
            if kalebe_ainda_desligado[0]['total'] == 0:
                print("   ✅ Kalebe removido da tabela de desligados")
            else:
                print("   ⚠️ Kalebe ainda está na tabela de desligados")
            
            # 5. Verificar se aparece na lista de funcionários ativos
            print(f"\n📋 5. VERIFICANDO SE APARECE NA LISTA DE FUNCIONÁRIOS ATIVOS:")
            funcionarios_ativos = db.execute_query("""
                SELECT id, nome_completo, matricula_empresa
                FROM funcionarios 
                WHERE ativo = TRUE
                ORDER BY nome_completo
            """)
            
            kalebe_na_lista = False
            if funcionarios_ativos:
                print(f"   ✅ {len(funcionarios_ativos)} funcionários ativos encontrados:")
                for func in funcionarios_ativos:
                    print(f"      - {func['nome_completo']} (Matrícula: {func['matricula_empresa']})")
                    if 'KALEBE' in func['nome_completo']:
                        kalebe_na_lista = True
            
            if kalebe_na_lista:
                print("   ✅ PERFEITO: Kalebe aparece na lista de funcionários ativos!")
            else:
                print("   ❌ PROBLEMA: Kalebe não aparece na lista de funcionários ativos")
                return False
            
            return True
        else:
            print(f"   ❌ FALHA: {resultado['message']}")
            return False
        
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🎯 TESTE ESPECÍFICO: RESTAURAÇÃO DO KALEBE")
    print("=" * 60)
    
    sucesso = test_restaurar_kalebe()
    
    if sucesso:
        print("\n🎉 SUCESSO TOTAL!")
        print("✅ Função de restaurar funcionário funcionando perfeitamente")
        print("✅ Kalebe restaurado com sucesso")
        print("✅ Funcionário aparece na lista de ativos")
    else:
        print("\n❌ FALHA!")
        print("❌ Função de restaurar ainda tem problemas")
