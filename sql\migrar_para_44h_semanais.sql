-- =====================================================
-- MIGRAÇÃO: SISTEMA DE 44 HORAS SEMANAIS
-- =====================================================
-- Data: 15/07/2025
-- Objetivo: Alterar sistema de horas diárias para horas semanais

USE controle_ponto;

-- =====================================================
-- 1. BACKUP DA CONFIGURAÇÃO ATUAL
-- =====================================================

-- Criar tabela de backup
CREATE TABLE IF NOT EXISTS backup_horas_diarias_20250715 (
    id INT,
    nome_completo VARCHAR(100),
    horas_trabalho_obrigatorias_antigas DECIMAL(4,2),
    data_backup TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Fazer backup dos dados atuais
INSERT INTO backup_horas_diarias_20250715 (id, nome_completo, horas_trabalho_obrigatorias_antigas)
SELECT id, nome_completo, horas_trabalho_obrigatorias
FROM funcionarios
WHERE horas_trabalho_obrigatorias IS NOT NULL;

SELECT CONCAT('✅ Backup criado com ', COUNT(*), ' registros') as status 
FROM backup_horas_diarias_20250715;

-- =====================================================
-- 2. ALTERAR ESTRUTURA DO CAMPO
-- =====================================================

-- Renomear campo atual para preservar dados
ALTER TABLE funcionarios 
CHANGE COLUMN horas_trabalho_obrigatorias horas_trabalho_obrigatorias_old DECIMAL(4,2);

-- Adicionar novo campo para horas semanais (FLEXÍVEL - cada funcionário define)
ALTER TABLE funcionarios
ADD COLUMN horas_semanais_obrigatorias DECIMAL(5,2) DEFAULT 44.00
COMMENT 'Horas de trabalho obrigatórias por semana definidas no contrato do funcionário (ex: 44.00 padrão CLT, 30.00 meio período, 40.00 empresa específica)';

-- =====================================================
-- 3. MIGRAR DADOS EXISTENTES
-- =====================================================

-- ✅ CONVERSÃO INTELIGENTE: Converter horas diárias para horas semanais
-- Lógica: horas_diarias * 5 dias = horas_semanais (padrão)
-- FLEXÍVEL: Cada funcionário mantém sua carga horária proporcional
UPDATE funcionarios
SET horas_semanais_obrigatorias = CASE
    WHEN horas_trabalho_obrigatorias_old IS NOT NULL THEN
        -- Converter proporcionalmente: 8h/dia = 40h/semana, 6h/dia = 30h/semana, etc.
        ROUND(horas_trabalho_obrigatorias_old * 5, 2)
    ELSE 44.00  -- Sugestão padrão CLT (não obrigatório)
END;

-- =====================================================
-- 4. VALIDAR MIGRAÇÃO
-- =====================================================

-- Verificar resultados da migração
SELECT 
    'Migração de Horas Diárias → Semanais' as tipo,
    COUNT(*) as total_funcionarios,
    MIN(horas_semanais_obrigatorias) as min_horas_semanais,
    MAX(horas_semanais_obrigatorias) as max_horas_semanais,
    AVG(horas_semanais_obrigatorias) as media_horas_semanais
FROM funcionarios
WHERE horas_semanais_obrigatorias IS NOT NULL;

-- Mostrar alguns exemplos da conversão
SELECT 
    nome_completo,
    horas_trabalho_obrigatorias_old as horas_diarias_antigas,
    horas_semanais_obrigatorias as horas_semanais_novas,
    CONCAT(
        ROUND(horas_semanais_obrigatorias / 5, 2), 'h/dia (Seg-Qui) + ',
        ROUND(horas_semanais_obrigatorias - (ROUND(horas_semanais_obrigatorias / 5, 2) * 4), 2), 'h (Sex)'
    ) as distribuicao_semanal
FROM funcionarios
WHERE horas_trabalho_obrigatorias_old IS NOT NULL
LIMIT 5;

-- =====================================================
-- 5. CRIAR ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índice para consultas por horas semanais
CREATE INDEX idx_funcionarios_horas_semanais ON funcionarios(horas_semanais_obrigatorias);

-- =====================================================
-- 6. ATUALIZAR COMENTÁRIOS DA TABELA
-- =====================================================

ALTER TABLE funcionarios 
MODIFY COLUMN horas_trabalho_obrigatorias_old DECIMAL(4,2) 
COMMENT 'OBSOLETO: Horas diárias antigas - usar horas_semanais_obrigatorias';

-- =====================================================
-- 7. VERIFICAÇÃO FINAL
-- =====================================================

SELECT 
    '✅ MIGRAÇÃO CONCLUÍDA COM SUCESSO!' as status,
    CONCAT('Total de funcionários migrados: ', COUNT(*)) as resultado
FROM funcionarios
WHERE horas_semanais_obrigatorias IS NOT NULL;

-- Mostrar estrutura atualizada
DESCRIBE funcionarios;
