#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para verificar o comportamento da função excluir_empresa
Data: 03/07/2025
"""

import requests
import json
import sys
import time

def main():
    try:
        # Criar uma empresa de teste
        from utils.database import get_db_connection
        import datetime
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Criar uma empresa de teste
        empresa_nome = f"Empresa Teste Exclusão {datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        cursor.execute("""
            INSERT INTO empresas (razao_social, nome_fantasia, cnpj, telefone, email, ativa, data_cadastro)
            VALUES (%s, %s, %s, %s, %s, TRUE, NOW())
        """, (
            empresa_nome,
            f"Teste Exclusão {datetime.datetime.now().strftime('%H%M%S')}",
            "00.000.000/0001-00", 
            "(11) 1234-5678", 
            "<EMAIL>"
        ))
        
        conn.commit()
        
        # Verificar se a empresa foi criada
        cursor.execute("SELECT id, razao_social FROM empresas WHERE razao_social = %s", (empresa_nome,))
        empresa = cursor.fetchone()
        
        if not empresa:
            print("Falha ao criar empresa de teste.")
            conn.close()
            sys.exit(1)
            
        empresa_id = empresa['id'] if isinstance(empresa, dict) else empresa[0]
        print(f"Empresa de teste criada com sucesso! ID: {empresa_id}, Nome: {empresa_nome}")
        
        # Fazer login e excluir a empresa
        username = "admin"
        password = "@Ric6109"
        
        session = requests.Session()
        
        print("Fazendo login...")
        login_url = "http://10.19.208.31/login"
        login_data = {
            "usuario": username,
            "senha": password
        }
        
        login_response = session.post(login_url, data=login_data)
        
        if "Controle de Ponto" in login_response.text and "Faça login" in login_response.text:
            print("❌ Falha no login! Verifique as credenciais.")
            sys.exit(1)
        else:
            print("✅ Login bem-sucedido!")
        
        # Excluir a empresa
        print(f"Enviando requisição para excluir a empresa ID {empresa_id}...")
        url = f"http://10.19.208.31/configuracoes/empresas/{empresa_id}/excluir"
        
        # Cabeçalhos
        headers = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # Dados
        data = {
            'is_ajax': True
        }
        
        # Fazer a requisição
        response = session.post(url, headers=headers, data=json.dumps(data))
        
        # Verificar a resposta
        print(f"Status code: {response.status_code}")
        print(f"Resposta: {response.text}")
        
        # Aguardar um pouco para garantir que a operação seja concluída
        print("Aguardando 2 segundos para garantir que a operação seja concluída...")
        time.sleep(2)
        
        # Verificar se a empresa foi realmente excluída
        cursor.execute("SELECT id, razao_social, ativa FROM empresas WHERE id = %s", (empresa_id,))
        empresa_depois = cursor.fetchone()
        
        if empresa_depois:
            ativa_depois = empresa_depois['ativa'] if isinstance(empresa_depois, dict) else empresa_depois[2]
            print(f"Empresa depois da exclusão - ID: {empresa_id}, Ativa: {ativa_depois}")
            
            if not ativa_depois:
                print(f"✅ Empresa ID {empresa_id} foi marcada como inativa com sucesso!")
            else:
                print(f"❌ Empresa ID {empresa_id} ainda está ativa no banco de dados!")
                
                # Tentar atualizar diretamente
                print(f"\nTentando atualizar diretamente a coluna 'ativa' da empresa ID {empresa_id}...")
                cursor.execute("UPDATE empresas SET ativa = FALSE WHERE id = %s", (empresa_id,))
                conn.commit()
                
                # Verificar se a atualização funcionou
                cursor.execute("SELECT id, razao_social, ativa FROM empresas WHERE id = %s", (empresa_id,))
                empresa = cursor.fetchone()
                
                if empresa:
                    ativa = empresa['ativa'] if isinstance(empresa, dict) else empresa[2]
                    print(f"Empresa após atualização direta - ID: {empresa_id}, Ativa: {ativa}")
                    
                    if not ativa:
                        print(f"✅ Atualização direta funcionou! A empresa ID {empresa_id} foi marcada como inativa.")
                    else:
                        print(f"❌ Atualização direta falhou! A empresa ID {empresa_id} ainda está ativa.")
        else:
            print(f"❌ Empresa ID {empresa_id} não encontrada após a exclusão!")
            
        conn.close()
        
    except Exception as e:
        print(f"Erro: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 