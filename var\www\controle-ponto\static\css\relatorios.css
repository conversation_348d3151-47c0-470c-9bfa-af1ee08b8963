/* Estilos para a página de relatórios */

/* Card de Filtros */
.filtros-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
    position: relative;
}

.filtros-card .row {
    margin: 0;
}

.filtros-card h5 {
    color: white;
    margin-bottom: 20px;
    font-weight: 600;
}

.filtros-card .form-label {
    color: white;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 0.9rem;
    display: block;
}

.form-control-white {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
    color: #2c3e50;
    border-radius: 8px;
    height: 38px;
    padding: 8px 12px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    width: 100%;
}

.form-control-white:focus {
    background: white;
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.filtros-card select.form-control-white {
    height: 38px !important;
    padding-right: 24px;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: calc(100% - 8px) center;
}

.filtros-card .col-md-1,
.filtros-card .col-md-2,
.filtros-card .col-md-3 {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    height: 80px;
}

/* Botões */
.btn-filtrar {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    height: 38px;
    width: 38px;
    border-radius: 8px;
    color: white;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-filtrar:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4);
    color: white;
}

.btn-exportar {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-exportar:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
    color: white;
}

/* Cards de Estatísticas */
.stats-row {
    margin-bottom: 25px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 4px solid;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.total {
    border-left-color: #007bff;
}

.stat-card.biometrico {
    border-left-color: #28a745;
}

.stat-card.manual {
    border-left-color: #ffc107;
}

.stat-card.funcionarios {
    border-left-color: #17a2b8;
}

.stat-card h6 {
    color: #495057;
    margin-bottom: 10px;
    font-weight: 600;
}

.stat-card h3 {
    color: #2c3e50;
    margin: 0;
    font-weight: 700;
}

/* Tabela de Resultados */
.resultados-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-custom {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table-custom thead {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    color: white;
}

.table-custom thead th {
    border: none;
    padding: 15px 12px;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85em;
    letter-spacing: 0.5px;
}

.table-custom tbody tr {
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.table-custom tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.001);
}

.table-custom tbody td {
    padding: 12px;
    vertical-align: middle;
    border: none;
}

/* Badges */
.badge-tipo {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-entrada-manha {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.badge-saida-almoco {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
}

.badge-entrada-tarde {
    background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
    color: white;
}

.badge-saida {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.badge-metodo {
    padding: 4px 8px;
    border-radius: 10px;
    font-size: 0.75em;
    font-weight: 500;
}

.badge-biometrico {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.badge-manual {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Gráficos */
.chart-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Abas */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border: none;
    color: #495057;
    font-weight: 600;
    padding: 12px 20px;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border: none;
    color: #007bff;
}

.nav-tabs .nav-link.active {
    border: none;
    color: #007bff;
    position: relative;
}

.nav-tabs .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #007bff;
}

/* Cards de Resumo */
.resumo-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.resumo-card h6 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.resumo-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.resumo-info:last-child {
    border-bottom: none;
}

.resumo-label {
    color: #6c757d;
    font-size: 0.9em;
}

.resumo-valor {
    font-weight: 600;
    color: #212529;
}

/* Barras de Progresso */
.progress {
    height: 8px;
    border-radius: 4px;
    margin-top: 5px;
}

.progress-bar-pontualidade {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.progress-bar-atrasos {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

/* Responsividade */
@media (max-width: 768px) {
    .filtros-card {
        padding: 15px;
    }
    
    .search-button {
        position: static;
        width: 100%;
        height: 38px;
        border-radius: 8px;
        margin-top: 15px;
    }
    
    .filtros-card .col-md-1,
    .filtros-card .col-md-2,
    .filtros-card .col-md-3 {
        height: auto;
        margin-bottom: 15px;
    }
    
    .btn-filtrar {
        width: 100%;
        margin-top: 8px;
    }

    .stat-card {
        margin-bottom: 15px;
    }

    .table-custom thead th {
        font-size: 0.75em;
        padding: 10px 8px;
    }

    .table-custom tbody td {
        font-size: 0.85em;
        padding: 8px;
    }

    .badge-tipo {
        font-size: 0.7em;
        padding: 4px 8px;
    }
} 