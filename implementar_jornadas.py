#!/usr/bin/env python3
"""
Implementação de Jornadas Dinâmicas
Sistema RLPONTO-WEB
Data: 05/07/2025
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import traceback

def implementar_jornadas_dinamicas():
    """Implementar sistema de jornadas dinâmicas"""
    try:
        print("🚀 Iniciando implementação de jornadas dinâmicas...")
        
        db = DatabaseManager()
        
        # 1. VERIFICAR JORNADAS EXISTENTES
        print("\n📋 1. Verificando jornadas existentes...")
        sql_verificar = """
        SELECT id, empresa_id, nome_jornada, tipo_jornada, padrao, ativa 
        FROM jornadas_trabalho 
        ORDER BY empresa_id, padrao DESC
        """
        jornadas_existentes = db.execute_query(sql_verificar)
        
        print(f"   ✅ Encontradas {len(jornadas_existentes)} jornadas existentes")
        for jornada in jornadas_existentes:
            status = "PADRÃO" if jornada['padrao'] else "NORMAL"
            ativa = "ATIVA" if jornada['ativa'] else "INATIVA"
            print(f"   - ID {jornada['id']}: {jornada['nome_jornada']} ({status}, {ativa})")
        
        # 2. RENOMEAR JORNADA ATUAL PARA "PRIMEIRO TURNO"
        print("\n🔄 2. Renomeando jornadas padrão para 'Primeiro Turno'...")
        sql_renomear = """
        UPDATE jornadas_trabalho 
        SET nome_jornada = 'Primeiro Turno'
        WHERE padrao = TRUE
        """
        resultado_renomear = db.execute_update(sql_renomear)
        print(f"   ✅ {resultado_renomear} jornadas renomeadas para 'Primeiro Turno'")
        
        # 3. VERIFICAR EMPRESAS QUE PRECISAM DO SEGUNDO TURNO
        print("\n🏢 3. Verificando empresas para criar 'Segundo Turno'...")
        sql_empresas = """
        SELECT DISTINCT empresa_id 
        FROM jornadas_trabalho 
        WHERE padrao = TRUE
        """
        empresas_com_padrao = db.execute_query(sql_empresas)
        print(f"   ✅ Encontradas {len(empresas_com_padrao)} empresas com jornada padrão")
        
        # 4. CRIAR "SEGUNDO TURNO" PARA CADA EMPRESA
        print("\n➕ 4. Criando 'Segundo Turno' para cada empresa...")
        
        for empresa in empresas_com_padrao:
            empresa_id = empresa['empresa_id']
            
            # Verificar se já existe segundo turno
            sql_verificar_segundo = """
            SELECT id FROM jornadas_trabalho 
            WHERE empresa_id = %s AND nome_jornada = 'Segundo Turno'
            """
            segundo_existente = db.execute_query(sql_verificar_segundo, (empresa_id,))
            
            if segundo_existente:
                print(f"   ⚠️  Empresa {empresa_id} já tem 'Segundo Turno' - pulando")
                continue
            
            # Criar segundo turno
            sql_criar_segundo = """
            INSERT INTO jornadas_trabalho (
                empresa_id, nome_jornada, descricao, tipo_jornada, categoria_funcionario,
                seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
                sabado_entrada, sabado_saida, intervalo_inicio, intervalo_fim,
                intervalo_duracao_minutos, tolerancia_entrada_minutos, 
                ativa, padrao, ordem_exibicao, cadastrado_por
            ) VALUES (
                %s, 'Segundo Turno', 
                'Jornada do segundo turno - horário vespertino/noturno',
                'Diurno', 'Geral',
                '14:00:00', '22:00:00', '14:00:00', '21:00:00',
                NULL, NULL, '18:00:00', '19:00:00',
                60, 15, TRUE, FALSE, 2, 'sistema'
            )
            """
            
            resultado = db.execute_update(sql_criar_segundo, (empresa_id,))
            if resultado:
                print(f"   ✅ Criado 'Segundo Turno' para empresa {empresa_id}")
            else:
                print(f"   ❌ Erro ao criar 'Segundo Turno' para empresa {empresa_id}")
        
        # 5. VERIFICAR RESULTADO FINAL
        print("\n📊 5. Verificando resultado final...")
        sql_final = """
        SELECT 
            id, empresa_id, nome_jornada, tipo_jornada, 
            seg_qui_entrada, seg_qui_saida, padrao, ativa
        FROM jornadas_trabalho 
        ORDER BY empresa_id, padrao DESC, nome_jornada
        """
        jornadas_finais = db.execute_query(sql_final)
        
        print(f"   ✅ Total de jornadas após implementação: {len(jornadas_finais)}")
        
        # Agrupar por empresa
        empresas_jornadas = {}
        for jornada in jornadas_finais:
            emp_id = jornada['empresa_id']
            if emp_id not in empresas_jornadas:
                empresas_jornadas[emp_id] = []
            empresas_jornadas[emp_id].append(jornada)
        
        for emp_id, jornadas in empresas_jornadas.items():
            print(f"\n   🏢 Empresa {emp_id}:")
            for jornada in jornadas:
                status = "PADRÃO" if jornada['padrao'] else "NORMAL"
                horario = f"{jornada['seg_qui_entrada']}-{jornada['seg_qui_saida']}"
                print(f"      - {jornada['nome_jornada']} ({status}) - {horario}")
        
        print("\n🎉 Implementação de jornadas dinâmicas concluída com sucesso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro na implementação: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    implementar_jornadas_dinamicas()
