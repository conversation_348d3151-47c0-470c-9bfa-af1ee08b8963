{% extends "base.html" %}

{% block content %}
<!-- Dashboard Container -->
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ titulo }}</h1>
                    <p class="mt-1 text-sm text-gray-500">Dashboard de controle de ponto - {{ periodo }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Sistema Ativo
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Stats Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total de Registros -->
            <div class="bg-white rounded-lg shadow border p-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-500 truncate">Total de Registros</p>
                        <p class="mt-1 text-3xl font-semibold text-gray-900">{{ "{:,}".format(stats.total_registros) }}</p>
                    </div>
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-gray-500">Registros no sistema</span>
                </div>
            </div>

            <!-- Funcionários Ativos -->
            <div class="bg-white rounded-lg shadow border p-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-500 truncate">Funcionários Ativos</p>
                        <p class="mt-1 text-3xl font-semibold text-gray-900">{{ "{:,}".format(stats.funcionarios_ativos) }}</p>
                    </div>
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-gray-500">Usuários cadastrados</span>
                </div>
            </div>

            <!-- Registros Biométricos -->
            <div class="bg-white rounded-lg shadow border p-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-500 truncate">Registros Biométricos</p>
                        <p class="mt-1 text-3xl font-semibold text-gray-900">{{ "{:,}".format(stats.registros_biometricos) }}</p>
                    </div>
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.625 2.655A9 9 0 0119 11a1 1 0 11-2 0 7 7 0 00-9.625-6.492 1 1 0 11-.75-1.853zM4.662 4.959A1 1 0 014.75 6.37 6.97 6.97 0 003 11a1 1 0 11-2 0 8.97 8.97 0 012.25-5.953 1 1 0 011.412-.088z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm font-medium text-green-600">{{ stats.percentual_biometrico }}%</span>
                    <span class="text-sm text-gray-500"> do total</span>
                </div>
            </div>

            <!-- Registros Manuais -->
            <div class="bg-white rounded-lg shadow border p-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-500 truncate">Registros Manuais</p>
                        <p class="mt-1 text-3xl font-semibold text-gray-900">{{ "{:,}".format(stats.registros_manuais) }}</p>
                    </div>
                    <div class="w-8 h-8 bg-amber-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-gray-500">Inserções manuais</span>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Registros por Dia -->
            <div class="bg-white rounded-lg shadow border">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">Registros por Dia</h3>
                    <p class="text-sm text-gray-500">Últimos 7 dias</p>
                </div>
                <div class="p-6">
                    <div style="height: 300px;">
                        <canvas id="chartRegistrosDiarios"></canvas>
                    </div>
                </div>
            </div>

            <!-- Distribuição por Método -->
            <div class="bg-white rounded-lg shadow border">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">Métodos de Registro</h3>
                    <p class="text-sm text-gray-500">Biométrico vs Manual</p>
                </div>
                <div class="p-6 flex justify-center">
                    <div style="width: 250px; height: 250px;">
                        <canvas id="chartMetodos"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pontualidade Chart Full Width -->
        <div class="bg-white rounded-lg shadow border mb-8">
            <div class="px-6 py-4 border-b">
                <h3 class="text-lg font-medium text-gray-900">Análise de Pontualidade</h3>
                <p class="text-sm text-gray-500">Controle de atrasos por dia</p>
            </div>
            <div class="p-6">
                <div style="height: 300px;">
                    <canvas id="chartPontualidade"></canvas>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-4 justify-center">
            <a href="/relatorios/pontos" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                <svg class="-ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                Ver Relatórios Detalhados
            </a>
            <a href="/registro-ponto/manual" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                <svg class="-ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                Registrar Ponto Manual
            </a>
            <a href="/registro-ponto/biometrico" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                <svg class="-ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0h-2m8-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                Registrar Ponto Biométrico
            </a>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configurações dos dados
    var labelsData = {{ graficos.labels_dias | tojson }};
    var registrosDiariosData = {{ graficos.dados_registros_diarios | tojson }};
    var pontualidadeData = {{ graficos.dados_pontualidade | tojson }};
    var biometricosData = {{ stats.registros_biometricos }};
    var manuaisData = {{ stats.registros_manuais }};

    // Configurações base dos gráficos
    var chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: '#f3f4f6'
                },
                ticks: {
                    color: '#6b7280'
                }
            },
            x: {
                grid: {
                    color: '#f3f4f6'
                },
                ticks: {
                    color: '#6b7280'
                }
            }
        }
    };

    // Gráfico de Barras - Registros Diários
    var ctx1 = document.getElementById('chartRegistrosDiarios').getContext('2d');
    new Chart(ctx1, {
        type: 'bar',
        data: {
            labels: labelsData,
            datasets: [{
                label: 'Registros',
                data: registrosDiariosData,
                backgroundColor: '#3b82f6',
                borderColor: '#2563eb',
                borderWidth: 1,
                borderRadius: 4
            }]
        },
        options: chartOptions
    });

    // Gráfico de Pizza - Métodos
    var ctx2 = document.getElementById('chartMetodos').getContext('2d');
    new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: ['Biométrico', 'Manual'],
            datasets: [{
                data: [biometricosData, manuaisData],
                backgroundColor: ['#8b5cf6', '#f59e0b'],
                borderColor: ['#7c3aed', '#d97706'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        color: '#374151'
                    }
                }
            }
        }
    });

    // Gráfico de Linha - Pontualidade
    var ctx3 = document.getElementById('chartPontualidade').getContext('2d');
    new Chart(ctx3, {
        type: 'line',
        data: {
            labels: labelsData,
            datasets: [{
                label: 'Atrasos',
                data: pontualidadeData,
                fill: true,
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderColor: '#ef4444',
                tension: 0.4,
                pointBackgroundColor: '#ef4444',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5
            }]
        },
        options: chartOptions
    });
});
</script>

<style>
/* Estilos adicionais para consistência */
.transition-colors {
    transition: background-color 0.2s ease, color 0.2s ease;
}

/* Responsividade adicional */
@media (max-width: 640px) {
    .grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-4 {
        grid-template-columns: 1fr;
    }
    
    .flex.flex-wrap {
        flex-direction: column;
        align-items: stretch;
    }
    
    .flex.flex-wrap > a {
        margin-bottom: 0.5rem;
    }
}
</style>
{% endblock %} 