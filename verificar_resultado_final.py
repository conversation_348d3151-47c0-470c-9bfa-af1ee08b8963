#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar o resultado final após correção da jornada
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def verificar_resultado_final():
    """Verificar resultado final"""
    print("🔍 VERIFICANDO RESULTADO FINAL")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar jornada da AiNexus após correção
        print("\n1. Verificando jornada da AiNexus após correção...")
        sql_jornada = """
        SELECT 
            jt.id, jt.nome_jornada, jt.tipo_jornada,
            jt.seg_qui_entrada, jt.seg_qui_saida,
            jt.sexta_entrada, jt.sexta_saida,
            jt.intervalo_inicio, jt.intervalo_fim,
            jt.tolerancia_entrada_minutos,
            jt.data_atualizacao
        FROM jornadas_trabalho jt
        JOIN empresas e ON jt.empresa_id = e.id
        WHERE e.razao_social LIKE '%%AiNexus%%' AND jt.padrao = TRUE AND jt.ativa = TRUE
        """
        
        jornada = db.execute_query(sql_jornada, fetch_one=True)
        
        if jornada:
            print(f"📋 Jornada da AiNexus (corrigida):")
            print(f"   ID: {jornada['id']}")
            print(f"   Nome: {jornada['nome_jornada']}")
            print(f"   Seg-Qui: {jornada['seg_qui_entrada']} às {jornada['seg_qui_saida']}")
            print(f"   Sexta: {jornada['sexta_entrada']} às {jornada['sexta_saida']}")
            print(f"   Intervalo: {jornada['intervalo_inicio']} às {jornada['intervalo_fim']}")
            print(f"   Tolerância: {jornada['tolerancia_entrada_minutos']} minutos")
            print(f"   Atualização: {jornada['data_atualizacao']}")
        
        # 2. Verificar como Richardson verá agora
        print(f"\n2. Verificando como Richardson verá a jornada agora...")
        from utils.database import FuncionarioQueries
        
        funcionario = FuncionarioQueries.get_with_epis(1)
        
        if funcionario:
            print(f"📋 Richardson (após correção da jornada):")
            print(f"   Nome: {funcionario.get('nome_completo')}")
            print(f"   Jornada: {funcionario.get('nome_jornada')}")
            print(f"   Seg-Qui: {funcionario.get('jornada_seg_qui_entrada')} às {funcionario.get('jornada_seg_qui_saida')}")
            print(f"   Sexta: {funcionario.get('jornada_sex_entrada')} às {funcionario.get('jornada_sex_saida')}")
            print(f"   Intervalo: {funcionario.get('jornada_intervalo_entrada')} às {funcionario.get('jornada_intervalo_saida')}")
            print(f"   Tipo: {funcionario.get('tipo_jornada')}")
            print(f"   Tolerância: {funcionario.get('tolerancia_entrada_minutos')} minutos")
        
        # 3. Comparar com valores esperados
        print(f"\n3. Comparando com valores esperados...")
        valores_esperados = {
            'seg_qui_entrada': '09:00:00',
            'seg_qui_saida': '18:00:00',
            'sexta_entrada': '09:00:00', 
            'sexta_saida': '18:00:00',
            'intervalo_inicio': '13:00:00',
            'intervalo_fim': '14:00:00',
            'tolerancia': 5
        }
        
        if funcionario:
            print(f"📊 COMPARAÇÃO:")
            print(f"   Seg-Qui Entrada: {funcionario.get('jornada_seg_qui_entrada')} (esperado: {valores_esperados['seg_qui_entrada']})")
            print(f"   Seg-Qui Saída: {funcionario.get('jornada_seg_qui_saida')} (esperado: {valores_esperados['seg_qui_saida']})")
            print(f"   Sexta Entrada: {funcionario.get('jornada_sex_entrada')} (esperado: {valores_esperados['sexta_entrada']})")
            print(f"   Sexta Saída: {funcionario.get('jornada_sex_saida')} (esperado: {valores_esperados['sexta_saida']})")
            print(f"   Intervalo Início: {funcionario.get('jornada_intervalo_entrada')} (esperado: {valores_esperados['intervalo_inicio']})")
            print(f"   Intervalo Fim: {funcionario.get('jornada_intervalo_saida')} (esperado: {valores_esperados['intervalo_fim']})")
            print(f"   Tolerância: {funcionario.get('tolerancia_entrada_minutos')} (esperado: {valores_esperados['tolerancia']})")
            
            # Verificar se está correto
            correto = (
                str(funcionario.get('jornada_seg_qui_entrada')) == valores_esperados['seg_qui_entrada'] and
                str(funcionario.get('jornada_seg_qui_saida')) == valores_esperados['seg_qui_saida'] and
                str(funcionario.get('jornada_sex_entrada')) == valores_esperados['sexta_entrada'] and
                str(funcionario.get('jornada_sex_saida')) == valores_esperados['sexta_saida'] and
                str(funcionario.get('jornada_intervalo_entrada')) == valores_esperados['intervalo_inicio'] and
                str(funcionario.get('jornada_intervalo_saida')) == valores_esperados['intervalo_fim'] and
                funcionario.get('tolerancia_entrada_minutos') == valores_esperados['tolerancia']
            )
            
            if correto:
                print(f"\n✅ SUCESSO! Richardson agora herda corretamente a jornada da AiNexus!")
                print(f"   📋 Valores corretos:")
                print(f"   Seg-Qui: 09:00 às 18:00")
                print(f"   Sexta: 09:00 às 18:00")
                print(f"   Intervalo: 13:00 às 14:00")
                print(f"   Tolerância: 5 minutos")
            else:
                print(f"\n⚠️ Ainda há discrepâncias nos valores")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a verificação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    verificar_resultado_final()
