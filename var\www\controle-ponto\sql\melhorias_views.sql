-- <PERSON><PERSON><PERSON> nas views de relatórios
-- Data: 2025-01-08

-- View para estatísticas detalhadas de ponto por setor
CREATE OR REPLACE VIEW vw_estatisticas_ponto_setor AS
SELECT 
    f.setor,
    DATE(rp.data_hora) as data_registro,
    COUNT(*) as total_registros,
    COUNT(DISTINCT f.id) as funcionarios_unicos,
    SUM(CASE WHEN rp.metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
    SUM(CASE WHEN rp.metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais,
    SUM(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN 1 ELSE 0 END) as entradas_manha,
    SUM(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN 1 ELSE 0 END) as said<PERSON>_al<PERSON><PERSON>,
    SUM(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN 1 ELSE 0 END) as entradas_tarde,
    SUM(CASE WHEN rp.tipo_registro = 'saida' THEN 1 ELSE 0 END) as saidas,
    SUM(CASE 
        WHEN rp.tipo_registro = 'entrada_manha' 
        AND TIME(rp.data_hora) > ADDTIME(f.jornada_seg_qui_entrada, SEC_TO_TIME(f.tolerancia_ponto * 60))
        THEN 1 
        ELSE 0 
    END) as atrasos_manha,
    SUM(CASE 
        WHEN rp.tipo_registro = 'entrada_tarde' 
        AND TIME(rp.data_hora) > ADDTIME(f.jornada_intervalo_saida, SEC_TO_TIME(f.tolerancia_ponto * 60))
        THEN 1 
        ELSE 0 
    END) as atrasos_tarde
FROM 
    registros_ponto rp
    INNER JOIN funcionarios f ON rp.funcionario_id = f.id
GROUP BY 
    f.setor, 
    DATE(rp.data_hora);

-- View para análise de horas trabalhadas
CREATE OR REPLACE VIEW vw_horas_trabalhadas AS
WITH registros_diarios AS (
    SELECT 
        f.id as funcionario_id,
        f.nome_completo,
        f.setor,
        f.cargo,
        DATE(rp.data_hora) as data_registro,
        MAX(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN rp.data_hora END) as entrada_manha,
        MAX(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN rp.data_hora END) as saida_almoco,
        MAX(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN rp.data_hora END) as entrada_tarde,
        MAX(CASE WHEN rp.tipo_registro = 'saida' THEN rp.data_hora END) as saida
    FROM 
        registros_ponto rp
        INNER JOIN funcionarios f ON rp.funcionario_id = f.id
    GROUP BY 
        f.id,
        f.nome_completo,
        f.setor,
        f.cargo,
        DATE(rp.data_hora)
)
SELECT 
    rd.*,
    TIMEDIFF(rd.saida_almoco, rd.entrada_manha) as periodo_manha,
    TIMEDIFF(rd.saida, rd.entrada_tarde) as periodo_tarde,
    TIMEDIFF(
        ADDTIME(
            TIMEDIFF(rd.saida_almoco, rd.entrada_manha),
            TIMEDIFF(rd.saida, rd.entrada_tarde)
        ),
        '00:00:00'
    ) as total_horas_trabalhadas,
    CASE 
        WHEN rd.entrada_manha IS NULL OR rd.saida_almoco IS NULL 
        OR rd.entrada_tarde IS NULL OR rd.saida IS NULL 
        THEN 'Incompleto'
        ELSE 'Completo'
    END as status_dia
FROM 
    registros_diarios rd;

-- View para análise de pontualidade
CREATE OR REPLACE VIEW vw_analise_pontualidade AS
SELECT 
    f.id as funcionario_id,
    f.nome_completo,
    f.setor,
    f.cargo,
    DATE_FORMAT(rp.data_hora, '%Y-%m') as mes_ano,
    COUNT(DISTINCT DATE(rp.data_hora)) as dias_trabalhados,
    SUM(CASE 
        WHEN rp.tipo_registro = 'entrada_manha' 
        AND TIME(rp.data_hora) <= ADDTIME(f.jornada_seg_qui_entrada, SEC_TO_TIME(f.tolerancia_ponto * 60))
        THEN 1 
        ELSE 0 
    END) as entradas_pontuais_manha,
    SUM(CASE 
        WHEN rp.tipo_registro = 'entrada_manha' 
        AND TIME(rp.data_hora) > ADDTIME(f.jornada_seg_qui_entrada, SEC_TO_TIME(f.tolerancia_ponto * 60))
        THEN 1 
        ELSE 0 
    END) as atrasos_manha,
    SUM(CASE 
        WHEN rp.tipo_registro = 'entrada_tarde' 
        AND TIME(rp.data_hora) <= ADDTIME(f.jornada_intervalo_saida, SEC_TO_TIME(f.tolerancia_ponto * 60))
        THEN 1 
        ELSE 0 
    END) as entradas_pontuais_tarde,
    SUM(CASE 
        WHEN rp.tipo_registro = 'entrada_tarde' 
        AND TIME(rp.data_hora) > ADDTIME(f.jornada_intervalo_saida, SEC_TO_TIME(f.tolerancia_ponto * 60))
        THEN 1 
        ELSE 0 
    END) as atrasos_tarde,
    ROUND(
        (SUM(CASE 
            WHEN rp.tipo_registro IN ('entrada_manha', 'entrada_tarde')
            AND TIME(rp.data_hora) <= ADDTIME(
                CASE 
                    WHEN rp.tipo_registro = 'entrada_manha' THEN f.jornada_seg_qui_entrada
                    ELSE f.jornada_intervalo_saida
                END, 
                SEC_TO_TIME(f.tolerancia_ponto * 60)
            )
            THEN 1 
            ELSE 0 
        END) * 100.0) / 
        NULLIF(SUM(CASE WHEN rp.tipo_registro IN ('entrada_manha', 'entrada_tarde') THEN 1 ELSE 0 END), 0),
        2
    ) as percentual_pontualidade
FROM 
    registros_ponto rp
    INNER JOIN funcionarios f ON rp.funcionario_id = f.id
GROUP BY 
    f.id,
    f.nome_completo,
    f.setor,
    f.cargo,
    DATE_FORMAT(rp.data_hora, '%Y-%m'); 