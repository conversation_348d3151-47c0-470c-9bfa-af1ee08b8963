#!/usr/bin/env python3
"""
Script para verificar status de alocação dos funcionários
Identifica se funcionários estão apenas cadastrados ou também alocados
"""

import sys
import os
sys.path.append('var/www/controle-ponto')

from utils.database import DatabaseManager
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verificar_alocacoes():
    """Verifica status de alocação dos funcionários"""
    print("🔍 VERIFICAÇÃO DE ALOCAÇÕES DE FUNCIONÁRIOS")
    print("=" * 60)
    
    try:
        # 1. Funcionários cadastrados vs alocados
        print("\n📊 RESUMO GERAL:")
        print("-" * 40)
        
        # Total de funcionários
        total_funcionarios = DatabaseManager.execute_query(
            "SELECT COUNT(*) as total FROM funcionarios WHERE status_cadastro = 'Ativo'"
        )[0]['total']
        print(f"👥 Total de funcionários ativos: {total_funcionarios}")
        
        # Funcionários alocados
        funcionarios_alocados = DatabaseManager.execute_query("""
            SELECT COUNT(DISTINCT fa.funcionario_id) as total
            FROM funcionario_alocacoes fa
            WHERE fa.ativo = TRUE
        """)[0]['total']
        print(f"✅ Funcionários alocados: {funcionarios_alocados}")
        
        # Funcionários não alocados
        nao_alocados = total_funcionarios - funcionarios_alocados
        print(f"⚠️ Funcionários NÃO alocados: {nao_alocados}")
        
        # 2. Detalhes dos funcionários
        print(f"\n📋 DETALHES DOS FUNCIONÁRIOS:")
        print("-" * 40)
        
        funcionarios_detalhes = DatabaseManager.execute_query("""
            SELECT 
                f.id,
                f.nome_completo,
                f.empresa_id,
                e.razao_social as empresa_nome,
                f.jornada_trabalho_id,
                jt.nome_jornada as jornada_funcionario,
                
                -- Dados de alocação
                fa.id as alocacao_id,
                fa.empresa_cliente_id,
                ec.razao_social as cliente_nome,
                fa.jornada_trabalho_id as jornada_alocacao_id,
                jt_alocacao.nome_jornada as jornada_alocacao,
                
                -- Status
                CASE 
                    WHEN fa.id IS NOT NULL THEN 'ALOCADO'
                    ELSE 'APENAS CADASTRADO'
                END as status_alocacao
                
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
            
            -- Alocação ativa
            LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id AND fa.ativo = TRUE
            LEFT JOIN empresas ec ON fa.empresa_cliente_id = ec.id
            LEFT JOIN jornadas_trabalho jt_alocacao ON fa.jornada_trabalho_id = jt_alocacao.id
            
            WHERE f.status_cadastro = 'Ativo'
            ORDER BY f.nome_completo
        """)
        
        for func in funcionarios_detalhes:
            print(f"\n👤 {func['nome_completo']} (ID: {func['id']})")
            print(f"   🏢 Empresa: {func['empresa_nome'] or 'N/A'}")
            print(f"   📊 Status: {func['status_alocacao']}")
            
            if func['status_alocacao'] == 'ALOCADO':
                print(f"   🎯 Cliente: {func['cliente_nome']}")
                print(f"   📅 Jornada da alocação: {func['jornada_alocacao'] or 'N/A'}")
            else:
                print(f"   📅 Jornada do funcionário: {func['jornada_funcionario'] or 'N/A'}")
        
        # 3. Problemas identificados
        print(f"\n⚠️ PROBLEMAS IDENTIFICADOS:")
        print("-" * 40)
        
        # Funcionários sem jornada
        sem_jornada = DatabaseManager.execute_query("""
            SELECT f.id, f.nome_completo
            FROM funcionarios f
            LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id AND fa.ativo = TRUE
            WHERE f.status_cadastro = 'Ativo'
            AND f.jornada_trabalho_id IS NULL
            AND fa.jornada_trabalho_id IS NULL
        """)
        
        if sem_jornada:
            print(f"❌ {len(sem_jornada)} funcionários sem jornada:")
            for func in sem_jornada:
                print(f"   - {func['nome_completo']} (ID: {func['id']})")
        else:
            print("✅ Todos os funcionários têm jornada definida")
        
        # 4. Recomendações
        print(f"\n💡 RECOMENDAÇÕES:")
        print("-" * 40)
        
        if nao_alocados > 0:
            print(f"📌 {nao_alocados} funcionários estão apenas cadastrados na empresa")
            print("   Para que apareça a jornada correta, eles precisam ser ALOCADOS")
            print("   Acesse: Empresa Principal > Alocações > Alocar Funcionário")
        
        if sem_jornada:
            print(f"📌 {len(sem_jornada)} funcionários sem jornada definida")
            print("   Configure jornadas nas empresas ou aloque os funcionários")
        
        print(f"\n🎯 CONCLUSÃO:")
        print("-" * 40)
        print("✅ Funcionários ALOCADOS: Jornada vem da alocação")
        print("⚠️ Funcionários NÃO alocados: Jornada vem da empresa (se configurada)")
        print("❌ Sem jornada: Não aparece horários na visualização")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    sucesso = verificar_alocacoes()
    if sucesso:
        print("\n🎉 VERIFICAÇÃO CONCLUÍDA COM SUCESSO")
        sys.exit(0)
    else:
        print("\n💥 ERRO NA VERIFICAÇÃO")
        sys.exit(1)
