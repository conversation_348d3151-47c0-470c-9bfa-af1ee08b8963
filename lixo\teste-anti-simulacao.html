<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Anti-Simulação - Sistema Biométrico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="var/www/controle-ponto/static/style.css">
    <style>
        body {
            background: linear-gradient(135deg, #0f0f23, #1a1a2e);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid;
        }
        .test-result.success {
            background: rgba(40, 167, 69, 0.2);
            border-color: #28a745;
            color: #28a745;
        }
        .test-result.error {
            background: rgba(220, 53, 69, 0.2);
            border-color: #dc3545;
            color: #dc3545;
        }
        .test-result.warning {
            background: rgba(255, 193, 7, 0.2);
            border-color: #ffc107;
            color: #ffc107;
        }
        code {
            background: rgba(0,0,0,0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .btn-test {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #e91e63);
        }
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #ff9800);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-5">
            <h1 class="display-4">🛡️ Sistema Anti-Simulação</h1>
            <p class="lead">Demonstração do sistema que <strong>NUNCA aceita dados simulados</strong></p>
        </div>

        <!-- Teste 1: Simulação de Template Simulado -->
        <div class="test-card">
            <h3>🚨 Teste 1: Simulação de Template com "MODO_COMPATIVEL"</h3>
            <p>Este teste simula o que acontece quando o ZKAgent retorna dados simulados.</p>
            
            <button class="btn btn-test btn-danger" onclick="testarSimulacaoCompativel()">
                Simular Captura com "MODO_COMPATIVEL"
            </button>
            
            <div id="resultado-teste1" class="test-result" style="display: none;"></div>
        </div>

        <!-- Teste 2: Template Suspeito -->
        <div class="test-card">
            <h3>⚠️ Teste 2: Template Suspeito (muito pequeno)</h3>
            <p>Este teste simula um template suspeito com características não-biométricas.</p>
            
            <button class="btn btn-test btn-warning" onclick="testarTemplateSuspeito()">
                Simular Template Suspeito
            </button>
            
            <div id="resultado-teste2" class="test-result" style="display: none;"></div>
        </div>

        <!-- Teste 3: Template Real (válido) -->
        <div class="test-card">
            <h3>✅ Teste 3: Template Real Válido</h3>
            <p>Este teste simula um template biométrico real que seria aceito pelo sistema.</p>
            
            <button class="btn btn-test" onclick="testarTemplateReal()">
                Simular Template Real Válido
            </button>
            
            <div id="resultado-teste3" class="test-result" style="display: none;"></div>
        </div>

        <!-- Status do Sistema -->
        <div class="test-card">
            <h3>📊 Status do Sistema Anti-Simulação</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>🔒 Proteções Ativas:</h5>
                    <ul>
                        <li>✅ Detecção de "MODO_COMPATIVEL"</li>
                        <li>✅ Detecção de "SIMULACAO"</li>
                        <li>✅ Detecção de "ZKAgent_Professional"</li>
                        <li>✅ Verificação de tamanho mínimo</li>
                        <li>✅ Validação de formato binário</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>🎯 Funcionalidades:</h5>
                    <ul>
                        <li>🚨 Alerta visual no modal</li>
                        <li>📋 Mensagens específicas por tipo</li>
                        <li>🔄 Instruções de correção</li>
                        <li>📊 Diagnóstico automático</li>
                        <li>🛡️ Rejeição total de simulação</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="var/www/controle-ponto/static/js/biometria-zkagent.js"></script>
    <script>
        // Simula a classe ZKAgentBiometria para testes
        const zkAgentTeste = new ZKAgentBiometria();

        function testarSimulacaoCompativel() {
            const resultadoDiv = document.getElementById('resultado-teste1');
            resultadoDiv.style.display = 'block';
            resultadoDiv.className = 'test-result';
            resultadoDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testando detecção de simulação...';

            // Simula resposta do ZKAgent com template simulado
            const templateSimulado = btoa('MODO_COMPATIVEL:ZKAgent_Professional_v4.0:Instale_ZKFinger_SDK_para_captura_real');
            
            setTimeout(() => {
                try {
                    // Testa a verificação de simulação
                    const decoded = atob(templateSimulado);
                    const indicadoresSimulacao = [
                        'MODO_COMPATIVEL', 'SIMULACAO', 'ZKAgent_Professional'
                    ];
                    
                    const isSimulacao = indicadoresSimulacao.some(indicador => 
                        decoded.toLowerCase().includes(indicador.toLowerCase())
                    );
                    
                    if (isSimulacao) {
                        resultadoDiv.className = 'test-result error';
                        resultadoDiv.innerHTML = `
                            <h5>🚨 SIMULAÇÃO DETECTADA E REJEITADA!</h5>
                            <p><strong>Template decodificado:</strong></p>
                            <code>${decoded}</code>
                            <p class="mt-3"><strong>✅ Sistema funcionando corretamente:</strong></p>
                            <ul>
                                <li>❌ Template simulado foi rejeitado</li>
                                <li>🔍 Indicador "MODO_COMPATIVEL" detectado</li>
                                <li>🛡️ Sistema se recusa a aceitar dados simulados</li>
                                <li>📢 Alerta visual seria mostrado no modal</li>
                            </ul>
                        `;
                    }
                } catch (error) {
                    resultadoDiv.className = 'test-result error';
                    resultadoDiv.innerHTML = `<h5>❌ Erro no teste:</h5><p>${error.message}</p>`;
                }
            }, 1500);
        }

        function testarTemplateSuspeito() {
            const resultadoDiv = document.getElementById('resultado-teste2');
            resultadoDiv.style.display = 'block';
            resultadoDiv.className = 'test-result';
            resultadoDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testando template suspeito...';

            // Template muito pequeno e com caracteres suspeitos
            const templateSuspeito = btoa('fake:123');
            
            setTimeout(() => {
                try {
                    const decoded = atob(templateSuspeito);
                    
                    // Verifica características suspeitas
                    const isSuspeito = decoded.length < 100 || decoded.includes(':');
                    
                    if (isSuspeito) {
                        resultadoDiv.className = 'test-result warning';
                        resultadoDiv.innerHTML = `
                            <h5>⚠️ TEMPLATE SUSPEITO DETECTADO!</h5>
                            <p><strong>Template decodificado:</strong></p>
                            <code>${decoded}</code>
                            <p class="mt-3"><strong>✅ Verificações do sistema:</strong></p>
                            <ul>
                                <li>📏 Tamanho: ${decoded.length} bytes (< 100 = suspeito)</li>
                                <li>🔍 Contém ":" (indicador de texto, não binário)</li>
                                <li>❌ Não parece ser biometria real</li>
                                <li>🛡️ Sistema rejeitaria por segurança</li>
                            </ul>
                        `;
                    }
                } catch (error) {
                    resultadoDiv.className = 'test-result error';
                    resultadoDiv.innerHTML = `<h5>❌ Erro no teste:</h5><p>${error.message}</p>`;
                }
            }, 1500);
        }

        function testarTemplateReal() {
            const resultadoDiv = document.getElementById('resultado-teste3');
            resultadoDiv.style.display = 'block';
            resultadoDiv.className = 'test-result';
            resultadoDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testando template real...';

            // Simula um template binário real (Base64 de dados binários)
            const templateReal = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';
            
            setTimeout(() => {
                try {
                    const decoded = atob(templateReal);
                    
                    // Verifica se parece ser template real
                    const isTemplateReal = decoded.length >= 100 && !decoded.includes(':') && 
                                          !decoded.includes('MODO_COMPATIVEL') && 
                                          !decoded.includes('SIMULACAO');
                    
                    if (isTemplateReal) {
                        resultadoDiv.className = 'test-result success';
                        resultadoDiv.innerHTML = `
                            <h5>✅ TEMPLATE REAL VÁLIDO!</h5>
                            <p><strong>Características do template:</strong></p>
                            <ul>
                                <li>📏 Tamanho: ${decoded.length} bytes (✅ >= 100)</li>
                                <li>🔍 Formato binário (sem caracteres de texto)</li>
                                <li>🛡️ Não contém indicadores de simulação</li>
                                <li>✅ Sistema aceitaria este template</li>
                            </ul>
                            <p class="mt-3"><strong>🎯 Este é o tipo de dado que o sistema aceita:</strong></p>
                            <ul>
                                <li>Templates reais do hardware ZK4500</li>
                                <li>Dados binários sem indicadores de simulação</li>
                                <li>Tamanho adequado para biometria</li>
                            </ul>
                        `;
                    } else {
                        resultadoDiv.className = 'test-result warning';
                        resultadoDiv.innerHTML = `
                            <h5>⚠️ Template não passou na validação</h5>
                            <p>O template não atendeu aos critérios de segurança.</p>
                        `;
                    }
                } catch (error) {
                    resultadoDiv.className = 'test-result error';
                    resultadoDiv.innerHTML = `<h5>❌ Erro no teste:</h5><p>${error.message}</p>`;
                }
            }, 1500);
        }

        // Log de inicialização
        console.log('🛡️ Sistema Anti-Simulação carregado');
        console.log('🔒 Proteções ativas contra templates simulados');
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 