#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar adição manual de EPI e verificar se aparece
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json
from datetime import date

def testar_adicao_epi():
    """Testar adição manual de EPI"""
    print("🔍 TESTANDO ADIÇÃO MANUAL DE EPI")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar estrutura da tabela epis
        print("\n1. Verificando estrutura da tabela epis...")
        sql_desc = "DESCRIBE epis"
        campos = db.execute_query(sql_desc)
        
        print(f"📋 Campos da tabela epis:")
        for campo in campos:
            print(f"   - {campo['Field']}: {campo['Type']} ({campo['Null']}, {campo['Key']})")
        
        # 2. Verificar EPIs existentes
        print(f"\n2. Verificando EPIs existentes...")
        sql_count = "SELECT COUNT(*) as total FROM epis"
        total = db.execute_query(sql_count, fetch_one=True)
        print(f"📊 Total de EPIs na tabela: {total['total']}")
        
        if total['total'] > 0:
            sql_epis = "SELECT * FROM epis LIMIT 5"
            epis_existentes = db.execute_query(sql_epis)
            print(f"📋 Primeiros EPIs:")
            for epi in epis_existentes:
                print(f"   - ID {epi['id']}: {epi['epi_nome']} (Funcionário: {epi['funcionario_id']})")
        
        # 3. Adicionar EPI de teste para Richardson
        print(f"\n3. Adicionando EPI de teste para Richardson...")
        
        sql_inserir = """
        INSERT INTO epis (
            funcionario_id, 
            epi_nome, 
            epi_ca, 
            epi_data_entrega, 
            epi_data_validade, 
            epi_observacoes, 
            status_epi,
            criado_em,
            atualizado_em
        ) VALUES (
            1, 
            'Capacete de Segurança TESTE', 
            '12345', 
            CURDATE(), 
            DATE_ADD(CURDATE(), INTERVAL 1 YEAR), 
            'EPI de teste adicionado via script', 
            'entregue',
            NOW(),
            NOW()
        )
        """
        
        result = db.execute_query(sql_inserir, fetch_all=False)
        
        if result is not None:
            print(f"   ✅ EPI de teste adicionado com sucesso!")
            
            # Verificar se foi inserido
            sql_verificar = """
            SELECT * FROM epis WHERE funcionario_id = 1 ORDER BY id DESC LIMIT 1
            """
            
            epi_inserido = db.execute_query(sql_verificar, fetch_one=True)
            
            if epi_inserido:
                print(f"   📋 EPI inserido:")
                print(f"      ID: {epi_inserido['id']}")
                print(f"      Nome: {epi_inserido['epi_nome']}")
                print(f"      CA: {epi_inserido['epi_ca']}")
                print(f"      Status: {epi_inserido['status_epi']}")
                print(f"      Data entrega: {epi_inserido['epi_data_entrega']}")
        else:
            print(f"   ❌ Erro ao adicionar EPI de teste")
            return False
        
        # 4. Testar função get_with_epis
        print(f"\n4. Testando função get_with_epis após adição...")
        from utils.database import FuncionarioQueries
        
        funcionario_com_epis = FuncionarioQueries.get_with_epis(1)
        
        if funcionario_com_epis:
            print(f"📋 Resultado da função get_with_epis:")
            print(f"   Nome: {funcionario_com_epis.get('nome_completo')}")
            
            epis = funcionario_com_epis.get('epis', [])
            print(f"   EPIs: {len(epis)} encontrados")
            
            if epis:
                for i, epi in enumerate(epis):
                    print(f"      {i+1}. {epi.get('epi_nome')} (CA: {epi.get('epi_ca')})")
                    print(f"         Status: {epi.get('status_epi')}")
                    print(f"         Data: {epi.get('epi_data_entrega')}")
                print(f"   ✅ EPIs aparecem na função get_with_epis!")
            else:
                print(f"   ❌ EPIs ainda não aparecem na função get_with_epis")
        
        # 5. Verificar query direta
        print(f"\n5. Verificando query direta...")
        sql_direto = "SELECT * FROM epis WHERE funcionario_id = 1"
        epis_direto = db.execute_query(sql_direto)
        
        print(f"📊 Query direta - EPIs do Richardson: {len(epis_direto)}")
        for epi in epis_direto:
            print(f"   - {epi['epi_nome']} (Status: {epi['status_epi']})")
        
        # 6. Testar template
        print(f"\n6. Simulando dados para o template...")
        
        template_data = {
            'funcionario': funcionario_com_epis,
            'epis': funcionario_com_epis.get('epis', []) if funcionario_com_epis else []
        }
        
        print(f"📋 Dados que o template receberia:")
        print(f"   funcionario.epis: {len(template_data['funcionario'].get('epis', []))} EPIs")
        print(f"   epis (variável): {len(template_data['epis'])} EPIs")
        
        if template_data['epis']:
            print(f"   📋 EPIs para exibição:")
            for epi in template_data['epis']:
                print(f"      - {epi.get('epi_nome')} (CA: {epi.get('epi_ca')})")
        
        # 7. Limpar EPI de teste
        print(f"\n7. Limpando EPI de teste...")
        sql_limpar = "DELETE FROM epis WHERE funcionario_id = 1 AND epi_nome LIKE '%TESTE%'"
        db.execute_query(sql_limpar, fetch_all=False)
        print(f"   🧹 EPI de teste removido")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    testar_adicao_epi()
