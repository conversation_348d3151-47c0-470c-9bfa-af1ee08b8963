# Script para configurar acesso SSH ao servidor RLPONTO-WEB
# Servidor: ************
# Usuário: cavalcrod

Write-Host "=== CONFIGURAÇÃO DE ACESSO SSH PARA RLPONTO-WEB ===" -ForegroundColor Green
Write-Host ""

# Verificar se a chave SSH existe
$sshKeyPath = "$env:USERPROFILE\.ssh\id_rsa.pub"
if (-not (Test-Path $sshKeyPath)) {
    Write-Host "❌ Chave SSH não encontrada em $sshKeyPath" -ForegroundColor Red
    Write-Host "Execute primeiro: ssh-keygen -t rsa -b 4096 -f `"$env:USERPROFILE\.ssh\id_rsa`" -N '`"`"' -C `"rlponto-web-access`"" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Chave SSH encontrada: $sshKeyPath" -ForegroundColor Green

# Exibir a chave pública
Write-Host ""
Write-Host "📋 CHAVE PÚBLICA SSH (copie esta chave):" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Gray
Get-Content $sshKeyPath
Write-Host "=" * 80 -ForegroundColor Gray
Write-Host ""

# Instruções para adicionar a chave ao servidor
Write-Host "📝 INSTRUÇÕES PARA CONFIGURAR NO SERVIDOR:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Conecte-se ao servidor via SSH:" -ForegroundColor White
Write-Host "   ssh cavalcrod@************" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Crie o diretório .ssh (se não existir):" -ForegroundColor White
Write-Host "   mkdir -p ~/.ssh" -ForegroundColor Cyan
Write-Host "   chmod 700 ~/.ssh" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. Adicione a chave pública ao arquivo authorized_keys:" -ForegroundColor White
Write-Host "   echo 'COLE_A_CHAVE_PUBLICA_AQUI' >> ~/.ssh/authorized_keys" -ForegroundColor Cyan
Write-Host "   chmod 600 ~/.ssh/authorized_keys" -ForegroundColor Cyan
Write-Host ""
Write-Host "4. Teste a conexão sem senha:" -ForegroundColor White
Write-Host "   ssh cavalcrod@************" -ForegroundColor Cyan
Write-Host ""

# Opção para copiar automaticamente (se ssh-copy-id estiver disponível)
Write-Host "🚀 OPÇÃO AUTOMÁTICA (se disponível):" -ForegroundColor Magenta
Write-Host "   ssh-copy-id cavalcrod@************" -ForegroundColor Cyan
Write-Host ""

# Teste de conectividade
Write-Host "🔍 TESTANDO CONECTIVIDADE COM O SERVIDOR..." -ForegroundColor Blue
$testResult = Test-NetConnection -ComputerName "************" -Port 22 -WarningAction SilentlyContinue

if ($testResult.TcpTestSucceeded) {
    Write-Host "✅ Servidor acessível na porta 22 (SSH)" -ForegroundColor Green
} else {
    Write-Host "❌ Servidor não acessível na porta 22" -ForegroundColor Red
    Write-Host "   Verifique se o servidor está ligado e se a porta SSH está aberta" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📊 INFORMAÇÕES DO PROJETO RLPONTO-WEB:" -ForegroundColor Green
Write-Host "   Servidor: ************" -ForegroundColor White
Write-Host "   Usuário SSH: cavalcrod" -ForegroundColor White
Write-Host "   Usuário MySQL: cavalcrod" -ForegroundColor White
Write-Host "   Porta MySQL: 3306" -ForegroundColor White
Write-Host "   Banco de Dados: controle_ponto" -ForegroundColor White
Write-Host ""
Write-Host "🔐 Após configurar a chave SSH, você poderá acessar o servidor sem senha!" -ForegroundColor Green
