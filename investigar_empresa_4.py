#!/usr/bin/env python3
import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

db = DatabaseManager()

print("=== INVESTIGAÇÃO EMPRESA MSV ENGENHARIA (ID 4) ===")

print("\n1. DADOS DA EMPRESA:")
empresa = db.execute_query("SELECT * FROM empresas WHERE id = 4", fetch_one=True)
if empresa:
    print(f"   • ID: {empresa['id']}")
    print(f"   • Razão Social: {empresa['razao_social']}")
    print(f"   • Nome Fantasia: {empresa['nome_fantasia']}")
    print(f"   • Ativa: {empresa['ativa']}")
else:
    print("   ❌ Empresa não encontrada!")

print("\n2. JORNADA PADRÃO DA EMPRESA:")
jornada_empresa = db.execute_query("""
    SELECT id, nome_jornada, padrao, ativa,
           seg_qui_entrada, seg_qui_saida,
           sexta_entrada, sexta_saida,
           intervalo_inicio, intervalo_fim,
           tolerancia_entrada_minutos
    FROM jornadas_trabalho 
    WHERE empresa_id = 4 AND padrao = 1 AND ativa = 1
""")

if jornada_empresa:
    for jornada in jornada_empresa:
        print(f"   • ID: {jornada['id']}")
        print(f"   • Nome: {jornada['nome_jornada']}")
        print(f"   • Seg-Qui: {jornada['seg_qui_entrada']} às {jornada['seg_qui_saida']}")
        print(f"   • Sexta: {jornada['sexta_entrada']} às {jornada['sexta_saida']}")
        print(f"   • Intervalo: {jornada['intervalo_inicio']} às {jornada['intervalo_fim']}")
        print(f"   • Tolerância: {jornada['tolerancia_entrada_minutos']} min")
else:
    print("   ❌ Nenhuma jornada padrão encontrada!")

print("\n3. FUNCIONÁRIOS DA EMPRESA:")
funcionarios = db.execute_query("""
    SELECT f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id, f.usa_horario_empresa,
           jt.nome_jornada, jt.seg_qui_entrada, jt.seg_qui_saida, jt.sexta_entrada, jt.sexta_saida
    FROM funcionarios f
    LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
    WHERE f.empresa_id = 4 AND f.ativo = 1
""")

if funcionarios:
    for func in funcionarios:
        print(f"\n   👤 {func['nome_completo']} (ID: {func['id']})")
        print(f"      • Empresa ID: {func['empresa_id']}")
        print(f"      • Jornada ID: {func['jornada_trabalho_id']}")
        print(f"      • Usa horário empresa: {func['usa_horario_empresa']}")
        if func['nome_jornada']:
            print(f"      • Jornada atual: {func['nome_jornada']}")
            print(f"      • Seg-Qui: {func['seg_qui_entrada']} às {func['seg_qui_saida']}")
            print(f"      • Sexta: {func['sexta_entrada']} às {func['sexta_saida']}")
        else:
            print("      ❌ SEM JORNADA DEFINIDA!")
else:
    print("   ❌ Nenhum funcionário encontrado!")

print("\n4. COMPARAÇÃO:")
if jornada_empresa and funcionarios:
    jornada_correta = jornada_empresa[0]
    print(f"   JORNADA DA EMPRESA: {jornada_correta['seg_qui_entrada']}-{jornada_correta['seg_qui_saida']} / {jornada_correta['sexta_entrada']}-{jornada_correta['sexta_saida']}")
    
    for func in funcionarios:
        if func['nome_jornada']:
            print(f"   FUNCIONÁRIO {func['nome_completo']}: {func['seg_qui_entrada']}-{func['seg_qui_saida']} / {func['sexta_entrada']}-{func['sexta_saida']}")
            
            # Verificar se está correto
            if (func['seg_qui_entrada'] == jornada_correta['seg_qui_entrada'] and 
                func['seg_qui_saida'] == jornada_correta['seg_qui_saida'] and
                func['sexta_entrada'] == jornada_correta['sexta_entrada'] and
                func['sexta_saida'] == jornada_correta['sexta_saida']):
                print(f"      ✅ JORNADA CORRETA")
            else:
                print(f"      ❌ JORNADA INCORRETA - DEVERIA SER IGUAL À DA EMPRESA!")
                
                # Aplicar correção
                print(f"      🔧 APLICANDO CORREÇÃO...")
                try:
                    db.execute_query("""
                        UPDATE funcionarios 
                        SET jornada_trabalho_id = %s,
                            usa_horario_empresa = TRUE
                        WHERE id = %s
                    """, (jornada_correta['id'], func['id']), fetch_all=False)
                    print(f"      ✅ CORREÇÃO APLICADA!")
                except Exception as e:
                    print(f"      ❌ ERRO NA CORREÇÃO: {e}")

print("\n=== VERIFICAÇÃO FINAL ===")
funcionarios_final = db.execute_query("""
    SELECT f.id, f.nome_completo, f.jornada_trabalho_id,
           jt.nome_jornada, jt.seg_qui_entrada, jt.seg_qui_saida, jt.sexta_entrada, jt.sexta_saida
    FROM funcionarios f
    LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
    WHERE f.empresa_id = 4 AND f.ativo = 1
""")

for func in funcionarios_final:
    print(f"👤 {func['nome_completo']}: {func['seg_qui_entrada']}-{func['seg_qui_saida']} / {func['sexta_entrada']}-{func['sexta_saida']}")
