#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Deploy das Melhorias do Relatório de Impressão
==============================================

Script para aplicar as melhorias no template de impressão de ponto
no servidor de produção de forma segura e controlada.
"""

import os
import sys
import subprocess
import datetime
from pathlib import Path

def run_ssh_command(command):
    """Executa comando SSH no servidor"""
    ssh_command = [
        'ssh', 
        'user@************',
        '-o', 'StrictHostKeyChecking=no',
        command
    ]
    
    try:
        result = subprocess.run(
            ssh_command, 
            capture_output=True, 
            text=True, 
            timeout=30
        )
        return result.stdout, result.stderr, result.returncode
    except subprocess.TimeoutExpired:
        return "", "Timeout na execução do comando", 1
    except Exception as e:
        return "", f"Erro na execução: {e}", 1

def upload_file(local_path, remote_path):
    """Faz upload de arquivo via SCP"""
    scp_command = [
        'scp',
        '-o', 'StrictHostKeyChecking=no',
        local_path,
        f'user@************:{remote_path}'
    ]
    
    try:
        result = subprocess.run(
            scp_command,
            capture_output=True,
            text=True,
            timeout=60
        )
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Erro no upload: {e}")
        return False

def main():
    print("🚀 DEPLOY DAS MELHORIAS DO RELATÓRIO DE IMPRESSÃO")
    print("=" * 60)
    
    # 1. Verificar se o arquivo local existe
    local_template = "var/www/controle-ponto/templates/ponto_admin/imprimir_ponto.html"
    if not os.path.exists(local_template):
        print(f"❌ Arquivo local não encontrado: {local_template}")
        return False
    
    print(f"✅ Arquivo local encontrado: {local_template}")
    
    # 2. Verificar conectividade com o servidor
    print("\n🔍 Verificando conectividade com o servidor...")
    stdout, stderr, returncode = run_ssh_command("echo 'Conexão OK'")
    
    if returncode != 0:
        print(f"❌ Erro de conectividade: {stderr}")
        print("💡 Certifique-se de que:")
        print("   - SSH está configurado")
        print("   - Servidor está acessível")
        print("   - Credenciais estão corretas")
        return False
    
    print("✅ Conectividade OK")
    
    # 3. Fazer backup no servidor
    print("\n💾 Criando backup no servidor...")
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_command = f"""
    cd /var/www/controle-ponto/templates/ponto_admin
    cp imprimir_ponto.html imprimir_ponto.html.backup.deploy.{timestamp}
    echo "Backup criado: imprimir_ponto.html.backup.deploy.{timestamp}"
    """
    
    stdout, stderr, returncode = run_ssh_command(backup_command)
    if returncode != 0:
        print(f"❌ Erro ao criar backup: {stderr}")
        return False
    
    print(f"✅ Backup criado no servidor: {stdout.strip()}")
    
    # 4. Upload do arquivo atualizado
    print("\n📤 Fazendo upload do arquivo atualizado...")
    remote_path = "/var/www/controle-ponto/templates/ponto_admin/imprimir_ponto.html"
    
    if not upload_file(local_template, remote_path):
        print("❌ Erro no upload do arquivo")
        return False
    
    print("✅ Upload concluído com sucesso")
    
    # 5. Verificar se o arquivo foi atualizado
    print("\n🔍 Verificando se o arquivo foi atualizado...")
    verify_command = """
    cd /var/www/controle-ponto/templates/ponto_admin
    ls -la imprimir_ponto.html
    head -10 imprimir_ponto.html | grep -E "(Espelho de Ponto|professional-header)"
    """
    
    stdout, stderr, returncode = run_ssh_command(verify_command)
    if "Espelho de Ponto" in stdout or "professional-header" in stdout:
        print("✅ Arquivo atualizado corretamente no servidor")
    else:
        print("⚠️ Arquivo pode não ter sido atualizado corretamente")
        print(f"Saída: {stdout}")
    
    # 6. Reiniciar serviço
    print("\n🔄 Reiniciando serviço controle-ponto...")
    restart_command = """
    sudo systemctl restart controle-ponto
    sleep 3
    sudo systemctl status controle-ponto --no-pager -l
    """
    
    stdout, stderr, returncode = run_ssh_command(restart_command)
    if "active (running)" in stdout:
        print("✅ Serviço reiniciado com sucesso")
    else:
        print("⚠️ Possível problema no reinício do serviço")
        print(f"Status: {stdout}")
    
    # 7. Teste básico
    print("\n🧪 Testando acesso básico...")
    test_command = "curl -I http://localhost:5000/ponto-admin/ 2>/dev/null | head -1"
    stdout, stderr, returncode = run_ssh_command(test_command)
    
    if "200 OK" in stdout:
        print("✅ Serviço respondendo corretamente")
    else:
        print("⚠️ Serviço pode não estar respondendo")
        print(f"Resposta: {stdout}")
    
    print("\n" + "=" * 60)
    print("🎉 DEPLOY CONCLUÍDO!")
    print("=" * 60)
    print("\n📋 PRÓXIMOS PASSOS:")
    print("1. Acesse: http://************/ponto-admin/")
    print("2. Vá para detalhes de um funcionário")
    print("3. Clique em 'Imprimir Ponto'")
    print("4. Verifique se o novo layout está funcionando")
    print("\n🔄 ROLLBACK (se necessário):")
    print(f"   ssh user@************")
    print(f"   cd /var/www/controle-ponto/templates/ponto_admin")
    print(f"   cp imprimir_ponto.html.backup.deploy.{timestamp} imprimir_ponto.html")
    print(f"   sudo systemctl restart controle-ponto")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
