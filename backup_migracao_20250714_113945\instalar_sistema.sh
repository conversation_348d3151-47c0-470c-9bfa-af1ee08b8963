#!/bin/bash
# Script de instalação automática do sistema RLPONTO-WEB
# Gerado em: 2025-07-14 11:39:49.969277

set -e  # Parar em caso de erro

echo "🚀 INSTALAÇÃO DO SISTEMA RLPONTO-WEB"
echo "===================================="

# Verificar se é root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Este script deve ser executado como root"
    exit 1
fi

# Atualizar sistema
echo "📦 Atualizando sistema..."
apt update && apt upgrade -y

# Instalar dependências
echo "📦 Instalando dependências..."
apt install -y python3 python3-pip python3-venv mysql-server nginx supervisor curl wget unzip

# Configurar MySQL
echo "🗄️ Configurando MySQL..."
systemctl start mysql
systemctl enable mysql

# Configurar senha root do MySQL se necessário
mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '@Ric6109';" || true
mysql -u root -p@Ric6109 -e "CREATE DATABASE IF NOT EXISTS controle_ponto CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -u root -p@Ric6109 -e "CREATE USER IF NOT EXISTS 'controle_ponto'@'localhost' IDENTIFIED BY '@Ric6109';"
mysql -u root -p@Ric6109 -e "GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_ponto'@'localhost';"
mysql -u root -p@Ric6109 -e "FLUSH PRIVILEGES;"

# Criar diretório do sistema
echo "📁 Criando estrutura de diretórios..."
mkdir -p /var/www
cd /var/www

# Extrair arquivos do sistema
echo "📁 Extraindo arquivos do sistema..."
tar -xzf /root/sistema_completo_20250714_113945.tar.gz

# Configurar permissões
echo "🔧 Configurando permissões..."
chown -R www-data:www-data /var/www/controle-ponto
chmod -R 755 /var/www/controle-ponto

# Instalar dependências Python
echo "🐍 Instalando dependências Python..."
cd /var/www/controle-ponto
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r /root/requirements.txt

# Restaurar banco de dados
echo "🗄️ Restaurando banco de dados..."
mysql -u root -p@Ric6109 controle_ponto < /root/controle_ponto_backup_20250714_113945.sql

# Configurar Nginx
echo "🌐 Configurando Nginx..."
cat > /etc/nginx/sites-available/controle-ponto << 'EOF'
server {
    listen 80;
    server_name _;
    client_max_body_size 100M;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    location /static {
        alias /var/www/controle-ponto/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Ativar site
ln -sf /etc/nginx/sites-available/controle-ponto /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default
nginx -t && systemctl restart nginx
systemctl enable nginx

# Configurar Supervisor
echo "⚙️ Configurando Supervisor..."
cat > /etc/supervisor/conf.d/controle-ponto.conf << 'EOF'
[program:controle-ponto]
command=/var/www/controle-ponto/venv/bin/python app.py
directory=/var/www/controle-ponto
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/controle-ponto.log
environment=FLASK_ENV=production,SECRET_KEY=sua_chave_secreta_aqui
EOF

# Iniciar serviços
echo "🚀 Iniciando serviços..."
systemctl enable supervisor
systemctl restart supervisor
supervisorctl reread
supervisorctl update
supervisorctl start controle-ponto

# Aguardar inicialização
echo "⏳ Aguardando inicialização..."
sleep 10

# Verificar status
echo "✅ Verificando instalação..."
curl -s -o /dev/null -w "Status HTTP: %{http_code}\n" http://localhost/ || echo "Serviço ainda inicializando..."

echo ""
echo "🎉 INSTALAÇÃO CONCLUÍDA!"
echo "========================"
echo "✅ Sistema instalado em: /var/www/controle-ponto"
echo "✅ Banco de dados: controle_ponto"
echo "✅ Nginx configurado na porta 80"
echo "✅ Supervisor gerenciando o processo"
echo ""
echo "🌐 Acesse: http://$(hostname -I | awk '{print $1}')"
echo "👤 Login: admin"
echo "🔑 Senha: @Ric6109"
echo ""
echo "🔧 Comandos úteis:"
echo "   supervisorctl status"
echo "   tail -f /var/log/controle-ponto.log"
echo "   systemctl status nginx"
