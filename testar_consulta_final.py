#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste Final da Consulta de Alocações
====================================

Script para testar a consulta corrigida de alocações.

Data: 07/07/2025
"""

import pymysql

# Configurações do banco
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def testar_consulta_final():
    """Testa a consulta final corrigida"""
    print("🧪 TESTE FINAL DA CONSULTA DE ALOCAÇÕES")
    print("=" * 60)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        with conn.cursor() as cursor:
            
            # Verificar se existe carga_horaria_semanal
            cursor.execute("DESCRIBE jornadas_trabalho")
            campos = cursor.fetchall()
            
            tem_carga_semanal = any(campo['Field'] == 'carga_horaria_semanal' for campo in campos)
            
            if tem_carga_semanal:
                print("✅ Campo carga_horaria_semanal existe")
                carga_field = "jt.carga_horaria_semanal"
            else:
                print("⚠️ Campo carga_horaria_semanal não existe, usando alternativa")
                # Verificar outros campos possíveis
                campos_carga = [c['Field'] for c in campos if 'carga' in c['Field'].lower()]
                print(f"   Campos de carga disponíveis: {campos_carga}")
                
                # Usar um campo calculado ou NULL
                carga_field = "NULL"
            
            # Consulta final corrigida
            sql_final = f"""
            SELECT fa.*, 
                   f.nome_completo as nome, f.cargo, f.cpf, f.telefone1 as telefone,
                   e.razao_social, e.nome_fantasia, e.cnpj,
                   jt.nome_jornada as jornada_nome, {carga_field} as carga_horaria,
                   ec.nome_contrato, ec.codigo_contrato
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            JOIN empresas e ON fa.empresa_cliente_id = e.id
            JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
            LEFT JOIN empresa_clientes ec ON fa.contrato_id = ec.id
            ORDER BY fa.created_at DESC
            """
            
            print(f"\n🔍 Executando consulta final...")
            cursor.execute(sql_final)
            resultados = cursor.fetchall()
            
            print(f"✅ Consulta executada com sucesso!")
            print(f"📊 Resultados: {len(resultados)} alocações encontradas")
            
            if resultados:
                print(f"\n📋 Primeira alocação:")
                primeiro = resultados[0]
                print(f"   ID: {primeiro['id']}")
                print(f"   Funcionário: {primeiro['nome']}")
                print(f"   Cliente: {primeiro['razao_social']}")
                print(f"   Jornada: {primeiro['jornada_nome']}")
                print(f"   Carga Horária: {primeiro['carga_horaria']}")
                print(f"   Status: {'Ativo' if primeiro['ativo'] else 'Inativo'}")
            
            return sql_final
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return None
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    sql_final = testar_consulta_final()
    
    if sql_final:
        print(f"\n🎉 CONSULTA FINAL FUNCIONANDO!")
        print("✅ Pronta para ser aplicada no código")
    else:
        print(f"\n❌ CONSULTA AINDA COM PROBLEMAS")
