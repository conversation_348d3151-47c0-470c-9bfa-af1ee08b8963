#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste Específico do Conteúdo de Configurações
============================================
"""

import requests

def test_config_content():
    """Testa o conteúdo específico da página de configurações"""
    
    base_url = "http://10.19.208.31:5000"
    session = requests.Session()
    
    # Login
    login_data = {
        'usuario': 'admin',
        'senha': '@Ric6109'
    }
    
    print("🔐 Fazendo login...")
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code == 200 or login_response.status_code == 302:
        print("✅ Login realizado com sucesso")
        
        # Obter página de configurações
        print("\n🔍 Carregando página de configurações...")
        config_response = session.get(f"{base_url}/configuracoes")
        
        if config_response.status_code == 200:
            content = config_response.text
            
            print("\n📋 VERIFICANDO CONTEÚDO ESPECÍFICO:")
            
            # Verificações específicas
            checks = [
                ("Tab Geral", 'id="geral"' in content),
                ("Tab Empresas", 'id="empresas"' in content),
                ("Tab Usuários", 'id="usuarios"' in content),
                ("Tab Sistema", 'id="sistema"' in content),
                ("Biometria ATIVA", "🔥 Configuração Biométrica ATIVA" in content),
                ("Link Biometria", "/configuracoes/biometria" in content),
                ("Bootstrap JS", "bootstrap" in content),
                ("Action Cards", "action-card" in content),
                ("Botão Configurar", "Configurar Biometria" in content),
                ("Seção Sistema", "Configurações do Sistema" in content)
            ]
            
            all_ok = True
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"{status} {check_name}")
                if not result:
                    all_ok = False
            
            if all_ok:
                print("\n🎉 TODAS AS VERIFICAÇÕES PASSARAM!")
                print("✅ Página de configurações totalmente funcional!")
            else:
                print("\n⚠️ Algumas verificações falharam")
                
            # Mostrar parte do HTML para debug
            print("\n📄 TRECHO DO HTML (primeiros 1000 chars):")
            print("-" * 50)
            print(content[:1000])
            print("-" * 50)
                
        else:
            print(f"❌ Erro ao carregar página: {config_response.status_code}")
    else:
        print(f"❌ Erro no login: {login_response.status_code}")

if __name__ == "__main__":
    test_config_content() 