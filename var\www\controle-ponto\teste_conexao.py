#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste simples de conexão com banco de dados
"""

try:
    print("Testando imports...")
    from utils.database import get_db_connection
    print("✅ Import OK")
    
    print("Testando conexão...")
    conn = get_db_connection()
    print("✅ Conexão OK")
    
    cursor = conn.cursor()
    print("✅ Cursor OK")
    
    print("Testando consulta empresas...")
    cursor.execute('SELECT COUNT(*) as total FROM empresas')
    result = cursor.fetchone()
    count = result['total']
    print(f"✅ Total empresas: {count}")
    
    print("Testando consulta funcionários...")
    cursor.execute('SELECT COUNT(*) as total FROM funcionarios')
    result = cursor.fetchone()
    count = result['total']
    print(f"✅ Total funcionários: {count}")
    
    conn.close()
    print("✅ Teste completo com sucesso!")

except Exception as e:
    print(f"❌ Erro: {str(e)}")
    import traceback
    traceback.print_exc() 