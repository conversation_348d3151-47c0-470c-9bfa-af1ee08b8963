#!/usr/bin/env python3
"""
Script para debugar qual template está sendo usado
"""

import paramiko
import time

def debug_template():
    """Debug do template sendo usado"""
    
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    print("🔍 DEBUG DO TEMPLATE - RLPONTO-WEB")
    print("=" * 60)
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password, timeout=30)
        
        print("✅ Conectado ao servidor")
        
        # Comandos de debug
        commands = [
            # Verificar se o arquivo foi modificado
            ("Verificar modificação do template", "ls -la /var/www/controle-ponto/templates/funcionarios/index.html"),
            
            # Ver as primeiras linhas do arquivo
            ("Primeiras linhas do template", "head -20 /var/www/controle-ponto/templates/funcionarios/index.html"),
            
            # Verificar se há cache do Flask
            ("Verificar cache Python", "find /var/www/controle-ponto -name '*.pyc' -o -name '__pycache__'"),
            
            # Limpar cache Python
            ("Limpar cache Python", "find /var/www/controle-ponto -name '*.pyc' -delete && find /var/www/controle-ponto -name '__pycache__' -type d -exec rm -rf {} + 2>/dev/null || true"),
            
            # Verificar processo Flask
            ("Verificar processo Flask", "ps aux | grep python | grep app.py"),
            
            # Verificar logs do Flask
            ("Logs recentes do Flask", "tail -20 /var/www/controle-ponto/flask.log"),
            
            # Forçar restart completo
            ("Parar Flask", "pkill -9 -f 'python.*app.py' || true"),
            
            # Aguardar
            ("Aguardar", "sleep 3"),
            
            # Iniciar Flask novamente
            ("Iniciar Flask", "cd /var/www/controle-ponto && nohup python3 app.py > flask.log 2>&1 &"),
            
            # Aguardar inicialização
            ("Aguardar inicialização", "sleep 5"),
            
            # Verificar se iniciou
            ("Verificar inicialização", "ps aux | grep python | grep app.py | grep -v grep"),
            
            # Testar resposta
            ("Testar resposta", "curl -s -I http://localhost:5000/funcionarios/ | head -5"),
            
            # Verificar se há nginx cache
            ("Verificar nginx", "nginx -t && systemctl status nginx --no-pager -l"),
        ]
        
        for description, command in commands:
            print(f"\n📋 {description}...")
            print(f"💻 {command}")
            
            try:
                stdin, stdout, stderr = ssh.exec_command(command, timeout=30)
                output = stdout.read().decode().strip()
                error = stderr.read().decode().strip()
                
                if output:
                    print(f"✅ {output}")
                if error:
                    print(f"⚠️ {error}")
                    
            except Exception as e:
                print(f"❌ Erro: {e}")
        
        ssh.close()
        
        print("\n" + "="*60)
        print("🎯 AÇÕES RECOMENDADAS:")
        print("1. Limpe o cache do navegador (Ctrl+F5)")
        print("2. Tente acessar em modo incógnito")
        print("3. Verifique se há proxy/CDN")
        print("4. Acesse: http://************/funcionarios/")
        
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    debug_template()
