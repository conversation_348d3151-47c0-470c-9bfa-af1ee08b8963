#!/usr/bin/env python3
"""
Verificação final da restauração
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager

def verificar_estado_final():
    """Verificar o estado final após a restauração"""
    print("🔍 VERIFICAÇÃO FINAL: ESTADO APÓS RESTAURAÇÃO")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar Richardson na tabela principal
        print("📋 1. RICHARDSON NA TABELA PRINCIPAL:")
        richardson_principal = db.execute_query("""
            SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
            FROM funcionarios 
            WHERE nome_completo LIKE %s
        """, ('%RICHARDSON%',))
        
        if richardson_principal:
            print(f"   ✅ {len(richardson_principal)} registros encontrados:")
            for func in richardson_principal:
                status_ativo = "ATIVO" if func['ativo'] else "INATIVO"
                print(f"      - ID: {func['id']}, Status: {func['status_cadastro']}, Ativo: {status_ativo}")
                print(f"        Matrícula: {func['matricula_empresa']}")
        else:
            print("   ❌ Richardson não encontrado na tabela principal")
        
        # 2. Verificar Richardson na tabela de desligados
        print("\n📋 2. RICHARDSON NA TABELA DE DESLIGADOS:")
        richardson_desligados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa, data_desligamento
            FROM funcionarios_desligados 
            WHERE nome_completo LIKE %s
        """, ('%RICHARDSON%',))
        
        if richardson_desligados:
            print(f"   ✅ {len(richardson_desligados)} registros encontrados:")
            for func in richardson_desligados:
                print(f"      - ID Original: {func['funcionario_id_original']}, Matrícula: {func['matricula_empresa']}")
                print(f"        Data Desligamento: {func['data_desligamento']}")
        else:
            print("   ✅ Richardson não encontrado na tabela de desligados (correto após restauração)")
        
        # 3. Verificar todos os funcionários ativos
        print("\n📋 3. TODOS OS FUNCIONÁRIOS ATIVOS:")
        funcionarios_ativos = db.execute_query("""
            SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
            FROM funcionarios 
            WHERE ativo = TRUE
            ORDER BY nome_completo
        """)
        
        if funcionarios_ativos:
            print(f"   ✅ {len(funcionarios_ativos)} funcionários ativos encontrados:")
            for func in funcionarios_ativos:
                print(f"      - {func['nome_completo']} (ID: {func['id']}, Matrícula: {func['matricula_empresa']})")
        else:
            print("   ❌ Nenhum funcionário ativo encontrado")
        
        # 4. Verificar funcionários com matrícula 001
        print("\n📋 4. FUNCIONÁRIOS COM MATRÍCULA 001:")
        funcionarios_001 = db.execute_query("""
            SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
            FROM funcionarios 
            WHERE matricula_empresa = '001'
        """)
        
        if funcionarios_001:
            print(f"   ✅ {len(funcionarios_001)} funcionários com matrícula 001:")
            for func in funcionarios_001:
                status_ativo = "ATIVO" if func['ativo'] else "INATIVO"
                print(f"      - {func['nome_completo']} (ID: {func['id']}, Status: {func['status_cadastro']}, Ativo: {status_ativo})")
        else:
            print("   ❌ Nenhum funcionário com matrícula 001")
        
        # 5. Análise final
        print("\n📋 5. ANÁLISE FINAL:")
        
        if richardson_principal:
            func = richardson_principal[0]
            if func['ativo'] and func['status_cadastro'] == 'Ativo':
                print("   ✅ SUCESSO: Richardson restaurado corretamente!")
                print("   ✅ Funcionário ativo na tabela principal")
                if not richardson_desligados:
                    print("   ✅ Funcionário removido da tabela de desligados")
                else:
                    print("   ⚠️ Funcionário ainda na tabela de desligados")
                return True
            else:
                print("   ⚠️ Richardson encontrado mas não está ativo")
                print(f"   Status: {func['status_cadastro']}, Ativo: {func['ativo']}")
                return False
        else:
            print("   ❌ FALHA: Richardson não encontrado na tabela principal")
            return False
        
    except Exception as e:
        print(f"\n❌ ERRO NA VERIFICAÇÃO: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🎯 VERIFICAÇÃO FINAL: ESTADO DO SISTEMA APÓS RESTAURAÇÃO")
    print("=" * 70)
    
    sucesso = verificar_estado_final()
    
    if sucesso:
        print("\n🎉 RESTAURAÇÃO BEM-SUCEDIDA!")
        print("✅ Funcionário Richardson restaurado corretamente")
        print("✅ Sistema funcionando como esperado")
    else:
        print("\n❌ PROBLEMAS NA RESTAURAÇÃO!")
        print("❌ Verificar logs e estado do sistema")
