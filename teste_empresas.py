#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para verificar empresas sem funcionários ativos
Data: 03/07/2025
"""

from utils.database import get_db_connection

def main():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Buscar empresas sem funcionários ativos
        cursor.execute("""
            SELECT id, razao_social, ativa 
            FROM empresas 
            WHERE ativa = TRUE 
            AND id NOT IN (
                SELECT DISTINCT empresa_id 
                FROM funcionarios 
                WHERE ativo = TRUE
            )
            LIMIT 5
        """)
        
        empresas = cursor.fetchall()
        
        print("Empresas sem funcionários ativos:")
        for empresa in empresas:
            print(f"ID: {empresa['id']}, Razão Social: {empresa['razao_social']}, Ativa: {empresa['ativa']}")
        
        # Buscar empresas com funcionários ativos
        cursor.execute("""
            SELECT e.id, e.razao_social, COUNT(f.id) as total_funcionarios
            FROM empresas e
            JOIN funcionarios f ON e.id = f.empresa_id
            WHERE e.ativa = TRUE AND f.ativo = TRUE
            GROUP BY e.id, e.razao_social
            LIMIT 5
        """)
        
        empresas_com_funcionarios = cursor.fetchall()
        
        print("\nEmpresas com funcionários ativos:")
        for empresa in empresas_com_funcionarios:
            print(f"ID: {empresa['id']}, Razão Social: {empresa['razao_social']}, Total Funcionários: {empresa['total_funcionarios']}")
        
        conn.close()
        
    except Exception as e:
        print(f"Erro: {str(e)}")

if __name__ == "__main__":
    main() 