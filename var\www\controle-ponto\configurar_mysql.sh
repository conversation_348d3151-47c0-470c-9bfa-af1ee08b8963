#!/bin/bash

# Script para configurar MySQL - Sistema Controle de Ponto
# Execute este script no servidor MySQL (************)

echo "🔧 Configurando permissões MySQL para Sistema Controle de Ponto..."
echo "=========================================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para executar comandos MySQL
execute_mysql() {
    echo -e "${BLUE}Executando: $1${NC}"
    mysql -u root -e "$1" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Sucesso${NC}"
    else
        echo -e "${RED}❌ Erro ao executar comando${NC}"
        return 1
    fi
}

echo -e "${YELLOW}1. Verificando se database existe...${NC}"
DB_EXISTS=$(mysql -u root -e "SHOW DATABASES LIKE 'controle_ponto';" 2>/dev/null | grep controle_ponto)
if [ -z "$DB_EXISTS" ]; then
    echo -e "${YELLOW}Database não existe. Criando...${NC}"
    execute_mysql "CREATE DATABASE controle_ponto CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
else
    echo -e "${GREEN}✅ Database 'controle_ponto' já existe${NC}"
fi

echo -e "${YELLOW}2. Criando usuários MySQL...${NC}"
execute_mysql "CREATE USER IF NOT EXISTS 'controle_user'@'***********' IDENTIFIED BY 'controle123';"
execute_mysql "CREATE USER IF NOT EXISTS 'controle_user'@'10.19.208.%' IDENTIFIED BY 'controle123';"
execute_mysql "CREATE USER IF NOT EXISTS 'controle_user'@'localhost' IDENTIFIED BY 'controle123';"

echo -e "${YELLOW}3. Concedendo permissões...${NC}"
execute_mysql "GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'***********';"
execute_mysql "GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'10.19.208.%';"
execute_mysql "GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'localhost';"

echo -e "${YELLOW}4. Atualizando privilégios...${NC}"
execute_mysql "FLUSH PRIVILEGES;"

echo -e "${YELLOW}5. Verificando configuração...${NC}"
echo -e "${BLUE}Usuários criados:${NC}"
mysql -u root -e "SELECT User, Host FROM mysql.user WHERE User = 'controle_user';" 2>/dev/null

echo -e "${BLUE}Privilégios do usuário:${NC}"
mysql -u root -e "SHOW GRANTS FOR 'controle_user'@'***********';" 2>/dev/null

echo -e "${YELLOW}6. Testando conexão...${NC}"
mysql -h localhost -u controle_user -pcontrole123 controle_ponto -e "SELECT 'Conexão bem-sucedida!' AS status;" 2>/dev/null
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Teste de conexão local bem-sucedido!${NC}"
else
    echo -e "${RED}❌ Falha no teste de conexão local${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Configuração MySQL concluída!${NC}"
echo -e "${BLUE}Agora você pode testar a conexão do cliente em ***********${NC}"
echo ""
echo -e "${YELLOW}Para testar do cliente, execute:${NC}"
echo -e "${BLUE}python -c \"from utils.database import get_db_connection; conn = get_db_connection(); print('SUCESSO!'); conn.close()\"${NC}" 