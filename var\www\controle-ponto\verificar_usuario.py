#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar usuários e seus níveis de acesso
Sistema: RLPONTO-WEB v1.0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database import get_db_connection
import pymysql

def verificar_usuarios():
    """Verifica todos os usuários e seus níveis de acesso"""
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # Buscar todos os usuários
        cursor.execute("""
            SELECT id, usuario, nome_completo, email, nivel_acesso, ativo, data_criacao
            FROM usuarios 
            ORDER BY id
        """)
        
        usuarios = cursor.fetchall()
        
        print("=" * 80)
        print("📋 LISTA DE USUÁRIOS NO SISTEMA")
        print("=" * 80)
        
        if not usuarios:
            print("❌ Nenhum usuário encontrado!")
        else:
            for user in usuarios:
                status = "✅ ATIVO" if user['ativo'] else "❌ INATIVO"
                print(f"""
ID: {user['id']}
👤 Usuário: {user['usuario']}
📝 Nome: {user['nome_completo']}
📧 Email: {user['email']}
🔐 Nível: {user['nivel_acesso']}
📊 Status: {status}
📅 Criado: {user['data_criacao']}
{'-' * 50}""")
        
        # Verificar especificamente o usuário admin
        cursor.execute("SELECT * FROM usuarios WHERE usuario = 'admin'")
        admin_user = cursor.fetchone()
        
        print("\n" + "=" * 80)
        print("🔍 VERIFICAÇÃO ESPECÍFICA DO USUÁRIO 'admin'")
        print("=" * 80)
        
        if admin_user:
            print(f"✅ Usuário 'admin' encontrado!")
            print(f"   🔐 Nível atual: '{admin_user['nivel_acesso']}'")
            print(f"   📊 Status: {'ATIVO' if admin_user['ativo'] else 'INATIVO'}")
            
            # Verificar se precisa atualizar
            if admin_user['nivel_acesso'] != 'administrador':
                print(f"\n⚠️ ATENÇÃO: Nível atual é '{admin_user['nivel_acesso']}', mas deveria ser 'administrador'")
                
                resposta = input("\n🔧 Deseja atualizar o nível para 'administrador'? (s/n): ")
                if resposta.lower() in ['s', 'sim', 'y', 'yes']:
                    cursor.execute("""
                        UPDATE usuarios 
                        SET nivel_acesso = 'administrador' 
                        WHERE usuario = 'admin'
                    """)
                    conn.commit()
                    print("✅ Nível atualizado para 'administrador' com sucesso!")
                else:
                    print("❌ Atualização cancelada.")
            else:
                print("✅ Nível de acesso está correto!")
                
        else:
            print("❌ Usuário 'admin' NÃO encontrado!")
            print("\n🔧 Criando usuário admin...")
            
            from werkzeug.security import generate_password_hash
            senha_hash = generate_password_hash("@Ric6109")
            
            cursor.execute("""
                INSERT INTO usuarios (usuario, senha, nome_completo, email, nivel_acesso, ativo, data_criacao)
                VALUES ('admin', %s, 'Administrador do Sistema', '<EMAIL>', 'administrador', 1, NOW())
            """, (senha_hash,))
            conn.commit()
            print("✅ Usuário 'admin' criado com sucesso!")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Erro ao verificar usuários: {e}")

if __name__ == "__main__":
    verificar_usuarios()
