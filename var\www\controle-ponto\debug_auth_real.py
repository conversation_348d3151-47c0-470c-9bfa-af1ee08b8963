#!/usr/bin/env python3
import paramiko

def debug_auth_real():
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🔍 DEBUG REAL - PROBLEMA DE AUTENTICAÇÃO")
        print("=" * 50)
        
        # 1. Verificar se o usuário admin existe
        print("1. Verificando usuário admin no banco...")
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u root -p@Ric6109 controle_ponto -e "
        SELECT id, username, email, is_admin, ativo 
        FROM usuarios 
        WHERE username = 'admin' OR email = 'admin';"
        ''')
        
        admin_check = stdout.read().decode()
        error = stderr.read().decode()
        
        if error:
            print(f"Erro no banco: {error}")
        else:
            print("Resultado da consulta:")
            print(admin_check)
        
        # 2. Verificar estrutura da tabela usuarios
        print("\n2. Verificando estrutura da tabela usuarios...")
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u root -p@Ric6109 controle_ponto -e "DESCRIBE usuarios;"
        ''')
        
        table_structure = stdout.read().decode()
        print("Estrutura da tabela usuarios:")
        print(table_structure)
        
        # 3. Verificar se há usuários cadastrados
        print("\n3. Verificando todos os usuários...")
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u root -p@Ric6109 controle_ponto -e "
        SELECT id, username, email, is_admin, ativo 
        FROM usuarios 
        LIMIT 5;"
        ''')
        
        all_users = stdout.read().decode()
        print("Usuários cadastrados:")
        print(all_users)
        
        # 4. Verificar função de autenticação
        print("\n4. Verificando função de autenticação...")
        stdin, stdout, stderr = ssh.exec_command('grep -A 10 -B 5 "def.*login" /var/www/controle-ponto/app.py')
        login_function = stdout.read().decode()
        print("Função de login encontrada:")
        print(login_function)
        
        # 5. Verificar decorador require_admin
        print("\n5. Verificando decorador require_admin...")
        stdin, stdout, stderr = ssh.exec_command('grep -A 15 "def require_admin" /var/www/controle-ponto/utils/auth.py')
        require_admin = stdout.read().decode()
        print("Decorador require_admin:")
        print(require_admin)
        
        # 6. Verificar logs de autenticação
        print("\n6. Verificando logs de autenticação...")
        stdin, stdout, stderr = ssh.exec_command('journalctl -u controle-ponto -n 20 --no-pager | grep -i "login\|auth\|admin"')
        auth_logs = stdout.read().decode()
        print("Logs de autenticação:")
        print(auth_logs)
        
        # 7. Criar usuário admin se não existir
        print("\n7. Criando/atualizando usuário admin...")
        create_admin = '''
        mysql -u root -p@Ric6109 controle_ponto -e "
        INSERT INTO usuarios (username, email, password_hash, is_admin, ativo) 
        VALUES ('admin', '<EMAIL>', SHA2('@Ric6109', 256), 1, 1)
        ON DUPLICATE KEY UPDATE 
        password_hash = SHA2('@Ric6109', 256),
        is_admin = 1,
        ativo = 1;"
        '''
        
        stdin, stdout, stderr = ssh.exec_command(create_admin)
        create_result = stdout.read().decode()
        create_error = stderr.read().decode()
        
        if create_error:
            print(f"Erro ao criar admin: {create_error}")
        else:
            print("✅ Usuário admin criado/atualizado com sucesso")
        
        # 8. Verificar novamente
        print("\n8. Verificando usuário admin após criação...")
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u root -p@Ric6109 controle_ponto -e "
        SELECT id, username, email, is_admin, ativo 
        FROM usuarios 
        WHERE username = 'admin';"
        ''')
        
        final_check = stdout.read().decode()
        print("Usuário admin final:")
        print(final_check)
        
        # 9. Testar hash da senha
        print("\n9. Testando hash da senha...")
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u root -p@Ric6109 controle_ponto -e "
        SELECT username, password_hash, SHA2('@Ric6109', 256) as senha_teste
        FROM usuarios 
        WHERE username = 'admin';"
        ''')
        
        hash_check = stdout.read().decode()
        print("Verificação de hash:")
        print(hash_check)
        
        ssh.close()
        
        print("\n" + "=" * 50)
        print("📋 RESUMO DO DEBUG")
        print("=" * 50)
        print("✅ Usuário admin verificado/criado")
        print("✅ Hash da senha verificado")
        print("🔄 Agora teste fazer login novamente")
        
    except Exception as e:
        print(f"❌ Erro durante o debug: {e}")

if __name__ == "__main__":
    debug_auth_real()
