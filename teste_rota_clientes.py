#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar a rota /empresa-principal/clientes com autenticação
"""

import requests
import json

def testar_rota_clientes():
    """Testa a rota de clientes com autenticação"""
    
    base_url = "http://************:5000"
    session = requests.Session()
    
    print("=== TESTE DA ROTA /empresa-principal/clientes ===")
    
    # 1. Fazer login
    print("1. Fazendo login...")
    login_data = {
        'usuario': 'admin',
        'senha': '@Ric6109'
    }
    
    login_response = session.post(f"{base_url}/login", data=login_data)
    print(f"Status do login: {login_response.status_code}")
    
    if login_response.status_code == 200:
        print("✅ Login realizado com sucesso")
    else:
        print(f"❌ Erro no login: {login_response.status_code}")
        print(f"Resposta: {login_response.text[:500]}")
        return
    
    # 2. Testar rota principal da empresa principal
    print("\n2. Testando rota principal...")
    dashboard_response = session.get(f"{base_url}/empresa-principal/")
    print(f"Status dashboard: {dashboard_response.status_code}")
    
    if dashboard_response.status_code == 200:
        print("✅ Dashboard da empresa principal acessível")
    else:
        print(f"❌ Erro no dashboard: {dashboard_response.status_code}")
        print(f"Resposta: {dashboard_response.text[:500]}")
    
    # 2.5. Testar rota de teste direto
    print("\n2.5. Testando rota de teste direto...")
    teste_direto_response = session.get(f"{base_url}/teste-clientes-direto")
    print(f"Status teste direto: {teste_direto_response.status_code}")
    if teste_direto_response.status_code == 200:
        print("✅ Rota direta funcionando")
    else:
        print(f"❌ Rota direta com problema: {teste_direto_response.status_code}")

    # 3. Testar rota de clientes
    print("\n3. Testando rota de clientes...")
    clientes_response = session.get(f"{base_url}/empresa-principal/clientes")
    print(f"Status clientes: {clientes_response.status_code}")
    
    if clientes_response.status_code == 200:
        print("✅ Rota de clientes acessível")
        print("Conteúdo da página:")
        # Salvar HTML completo para análise
        with open('/tmp/pagina_clientes_completa.html', 'w', encoding='utf-8') as f:
            f.write(clientes_response.text)
        print("HTML completo salvo em /tmp/pagina_clientes_completa.html")

        # Verificar se há conteúdo específico
        if "Erro ao carregar lista de clientes" in clientes_response.text:
            print("❌ Erro encontrado na página: 'Erro ao carregar lista de clientes'")
        elif "Gerenciar Clientes" in clientes_response.text:
            print("✅ Página carregou corretamente")
        elif "Nenhum cliente cadastrado" in clientes_response.text:
            print("✅ Página carregou - mostrando estado vazio")
        elif "Internal Server Error" in clientes_response.text:
            print("❌ Erro interno do servidor")
        else:
            print("⚠️ Página carregou mas conteúdo inesperado")

        # Verificar se há erros específicos
        if "error" in clientes_response.text.lower():
            print("❌ Palavra 'error' encontrada na página")
        if "exception" in clientes_response.text.lower():
            print("❌ Palavra 'exception' encontrada na página")
        if "traceback" in clientes_response.text.lower():
            print("❌ Palavra 'traceback' encontrada na página")
        if "alert-danger" in clientes_response.text.lower():
            print("❌ Alert de erro encontrado na página")
        if "flash" in clientes_response.text.lower() and "error" in clientes_response.text.lower():
            print("❌ Flash message de erro encontrada")
    else:
        print(f"❌ Erro na rota de clientes: {clientes_response.status_code}")
        print(f"Resposta: {clientes_response.text[:500]}")
    
    # 4. Testar rota de teste do blueprint
    print("\n4. Testando rota de teste...")
    teste_response = session.get(f"{base_url}/empresa-principal/teste")
    print(f"Status teste: {teste_response.status_code}")
    
    if teste_response.status_code == 200:
        try:
            teste_data = teste_response.json()
            print(f"✅ Rota de teste funcionando: {teste_data}")
        except:
            print("✅ Rota de teste respondeu mas não é JSON")
    else:
        print(f"❌ Erro na rota de teste: {teste_response.status_code}")

if __name__ == '__main__':
    testar_rota_clientes()
