<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>rocar <PERSON>ha - Controle <PERSON>o</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .password-wrapper {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            margin: 20px;
        }
        
        .password-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .password-header h1 {
            color: #495057;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .password-header .warning-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .security-notice {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .security-notice strong {
            display: block;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: #f8f9fa;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4fbdba;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
        }
        
        .password-requirements {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            margin-top: 10px;
            font-size: 13px;
        }
        
        .requirement {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            color: #6c757d;
        }
        
        .requirement.valid {
            color: #28a745;
        }
        
        .requirement-icon {
            margin-right: 8px;
            font-weight: bold;
        }
        
        .btn-update {
            width: 100%;
            background: #4fbdba;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 20px;
        }
        
        .btn-update:hover:not(:disabled) {
            background: #3da8a6;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(79, 189, 186, 0.3);
        }
        
        .btn-update:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 12px 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-size: 14px;
        }
        
        .system-info {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 12px;
        }
        
        @media (max-width: 480px) {
            .password-wrapper {
                margin: 10px;
                padding: 30px 20px;
            }
        }
        
        /* Animação de entrada */
        .password-wrapper {
            animation: slideUp 0.3s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="password-wrapper">
        <div class="password-header">
            <span class="warning-icon">⚠️</span>
            <h1>Atualização de Senha Necessária</h1>
        </div>
        
        <div class="security-notice">
            <strong>🔒 Atenção de Segurança</strong>
            Por motivos de segurança, sua senha atual está em formato inseguro e precisa ser atualizada. 
            Por favor, defina uma nova senha forte com pelo menos 8 caracteres.
        </div>
        
        <form method="POST" id="passwordForm">
            <div class="form-group">
                <label for="nova_senha">Nova Senha</label>
                <input type="password" 
                       id="nova_senha" 
                       name="nova_senha" 
                       required 
                       placeholder="Digite sua nova senha"
                       minlength="8">
                
                <div class="password-requirements">
                    <div class="requirement" id="req-length">
                        <span class="requirement-icon">×</span>
                        Pelo menos 8 caracteres
                    </div>
                    <div class="requirement" id="req-letter">
                        <span class="requirement-icon">×</span>
                        Pelo menos uma letra
                    </div>
                    <div class="requirement" id="req-number">
                        <span class="requirement-icon">×</span>
                        Pelo menos um número
                    </div>
                </div>
            </div>
            
            <button type="submit" class="btn-update" id="submitBtn" disabled>
                🔐 Atualizar Senha
            </button>
            
            {% if erro %}
            <div class="error-message">
                {{ erro }}
            </div>
            {% endif %}
        </form>
        
        <div class="system-info">
            Esta é uma medida de segurança obrigatória<br>
            Sistema de Controle de Ponto v2.0
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('nova_senha');
            const submitBtn = document.getElementById('submitBtn');
            const requirements = {
                length: document.getElementById('req-length'),
                letter: document.getElementById('req-letter'),
                number: document.getElementById('req-number')
            };
            
            // Foco automático no campo senha
            passwordInput.focus();
            
            // Validação em tempo real
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                let validCount = 0;
                
                // Validar comprimento
                if (password.length >= 8) {
                    requirements.length.classList.add('valid');
                    requirements.length.querySelector('.requirement-icon').textContent = '✓';
                    validCount++;
                } else {
                    requirements.length.classList.remove('valid');
                    requirements.length.querySelector('.requirement-icon').textContent = '×';
                }
                
                // Validar letra
                if (/[a-zA-Z]/.test(password)) {
                    requirements.letter.classList.add('valid');
                    requirements.letter.querySelector('.requirement-icon').textContent = '✓';
                    validCount++;
                } else {
                    requirements.letter.classList.remove('valid');
                    requirements.letter.querySelector('.requirement-icon').textContent = '×';
                }
                
                // Validar número
                if (/[0-9]/.test(password)) {
                    requirements.number.classList.add('valid');
                    requirements.number.querySelector('.requirement-icon').textContent = '✓';
                    validCount++;
                } else {
                    requirements.number.classList.remove('valid');
                    requirements.number.querySelector('.requirement-icon').textContent = '×';
                }
                
                // Habilitar/desabilitar botão
                submitBtn.disabled = validCount < 3;
            });
            
            // Submissão do formulário
            document.getElementById('passwordForm').addEventListener('submit', function(e) {
                const password = passwordInput.value;
                
                if (password.length < 8) {
                    e.preventDefault();
                    alert('A senha deve ter pelo menos 8 caracteres');
                    return;
                }
                
                if (!/[a-zA-Z]/.test(password)) {
                    e.preventDefault();
                    alert('A senha deve conter pelo menos uma letra');
                    return;
                }
                
                if (!/[0-9]/.test(password)) {
                    e.preventDefault();
                    alert('A senha deve conter pelo menos um número');
                    return;
                }
                
                // Desabilitar botão durante envio
                submitBtn.disabled = true;
                submitBtn.textContent = '⏳ Atualizando...';
            });
        });
    </script>
</body>
</html>