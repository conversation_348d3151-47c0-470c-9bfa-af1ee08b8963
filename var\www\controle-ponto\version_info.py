"""
RLPONTO-WEB - Sistema de Controle de Ponto Biométrico
Informações de Versão e Créditos

Desenvolvido por: AiNexus Tecnologia
Autor: <PERSON> - Full Stack Developer
"""

# Informações do Sistema
SYSTEM_INFO = {
    "name": "RLPONTO",
    "version": "Beta",
    "release_date": "2025-01-09",
    "description": "Sistema de Controle de Ponto",
    "build": "1.0.0-release"
}

# Informações da Empresa Desenvolvedora
COMPANY_INFO = {
    "name": "AiNexus Tecnologia",
    "developer": "<PERSON>",
    "role": "Full Stack Developer",
    "copyright": "© 2025 AiNexus Tecnologia. Todos os direitos reservados.",
    "contact": "<EMAIL>"
}

# Informações Técnicas
TECHNICAL_INFO = {
    "framework": "Flask 2.3.3",
    "database": "MySQL 8.0",
    "biometric_device": "ZK4500",
    "platform": "Windows/Linux",
    "python_version": "3.8+",
    "license": "Proprietary - AiNexus Tecnologia"
}

# Módulos e Funcionalidades
MODULES = {
    "authentication": "Sistema de autenticação e autorização",
    "employees": "Gestão completa de funcionários",
    "biometrics": "Integração biométrica ZK4500",
    "time_tracking": "Registro e controle de ponto",
    "reports": "Relatórios avançados de frequência",
    "configurations": "Configurações do sistema",
    "schedules": "Gestão de horários de trabalho",
    "epis": "Controle de EPIs"
}

# Changelog Principal
CHANGELOG = [
    {
        "version": "1.0",
        "date": "2025-01-09",
        "type": "release",
        "changes": [
            "✅ Sistema completo de controle de ponto biométrico",
            "✅ Interface moderna e responsiva",
            "✅ Integração ZK4500 com ZKAgent Professional",
            "✅ Relatórios avançados de frequência",
            "✅ Sistema de autenticação robusto",
            "✅ Gestão completa de funcionários",
            "✅ Configurações flexíveis de horários",
            "✅ Controle de EPIs integrado",
            "✅ Backup automático e segurança LGPD",
            "✅ Arquitetura modular e escalável"
        ]
    }
]

def get_version():
    """Retorna a versão atual do sistema"""
    return SYSTEM_INFO["version"]

def get_build_info():
    """Retorna informações completas de build"""
    return {
        "version": SYSTEM_INFO["version"],
        "build": SYSTEM_INFO["build"],
        "release_date": SYSTEM_INFO["release_date"],
        "developer": f"{COMPANY_INFO['name']} - {COMPANY_INFO['developer']}"
    }

def get_copyright():
    """Retorna informações de copyright"""
    return COMPANY_INFO["copyright"]

def get_system_banner():
    """Retorna banner completo do sistema para logs"""
    return f"""
╔══════════════════════════════════════════════════════════════╗
║      {SYSTEM_INFO['name']} v{SYSTEM_INFO['version']}         ║
║              {SYSTEM_INFO['description']}                    ║
║                                                              ║
║  Desenvolvido por: {COMPANY_INFO['name']}                    ║
║  Autor: {COMPANY_INFO['developer']} - {COMPANY_INFO['role']} ║
║  Data de Release: {SYSTEM_INFO['release_date']}              ║
║                                                              ║
║              {COMPANY_INFO['copyright']}                     ║
╚══════════════════════════════════════════════════════════════╝
"""



def get_footer_info():
    """Retorna informações para rodapé da aplicação web"""
    return {
        "system_name": SYSTEM_INFO["name"],
        "version": SYSTEM_INFO["version"],
        "description": SYSTEM_INFO["description"],
        "company": COMPANY_INFO["name"],
        "copyright": COMPANY_INFO["copyright"]
    }

def get_interface_info():
    """Retorna informações simplificadas para interface (sem nome do desenvolvedor)"""
    return {
        "system_full": f"{SYSTEM_INFO['name']} v{SYSTEM_INFO['version']} • {SYSTEM_INFO['description']}",
        "copyright_line": COMPANY_INFO["copyright"]
    }