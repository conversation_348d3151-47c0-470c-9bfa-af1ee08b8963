#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 TESTE MANUAL DE ATRASOS
==========================

Verifica se os atrasos estão sendo calculados corretamente
analisando os dados diretamente do banco.
"""

import pymysql
from datetime import datetime, time, timedelta

def conectar_banco():
    """Conecta ao banco de dados"""
    return pymysql.connect(
        host='************',
        user='cavalcrod',
        password='200381',
        database='controle_ponto',
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )

def testar_atrasos_banco():
    """
    Testa cálculo de atrasos diretamente no banco
    """
    print("🔍 TESTE MANUAL DE ATRASOS - ANÁLISE DO BANCO")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    try:
        conn = conectar_banco()
        cursor = conn.cursor()
        
        # Buscar registros do funcionário 35 (Richardson) do dia 17/07/2025
        print("📊 Buscando registros do funcionário 35 (Richardson)...")
        
        cursor.execute("""
            SELECT 
                DATE(data_hora) as data_registro,
                tipo_registro,
                TIME(data_hora) as hora_registro,
                data_hora
            FROM registros_ponto 
            WHERE funcionario_id = 35 
            AND DATE(data_hora) = '2025-07-17'
            ORDER BY data_hora
        """)
        
        registros = cursor.fetchall()
        
        if not registros:
            print("❌ Nenhum registro encontrado para o funcionário 35 em 17/07/2025")
            return False
        
        print(f"✅ Encontrados {len(registros)} registros:")
        
        # Analisar cada registro
        entrada_manha = None
        
        for registro in registros:
            data = registro['data_registro']
            tipo = registro['tipo_registro']
            hora = registro['hora_registro']
            
            print(f"   📅 {data} - {tipo}: {hora}")
            
            if tipo == 'entrada_manha':
                entrada_manha = hora
        
        # Calcular atraso se houver entrada da manhã
        if entrada_manha:
            print(f"\n🔍 ANÁLISE DE ATRASO:")
            print(f"   Entrada registrada: {entrada_manha}")
            
            # Horário padrão: 08:00 + 10 min tolerância = 08:10
            horario_limite = time(8, 10)
            print(f"   Horário limite (08:00 + 10min): {horario_limite}")
            
            if entrada_manha > horario_limite:
                # Calcular atraso
                dt_real = datetime.combine(datetime.today(), entrada_manha)
                dt_limite = datetime.combine(datetime.today(), horario_limite)
                atraso_segundos = (dt_real - dt_limite).total_seconds()
                atraso_minutos = int(atraso_segundos / 60)
                atraso_horas = atraso_segundos / 3600
                
                print(f"   ⚠️ ATRASO DETECTADO:")
                print(f"      Atraso em minutos: {atraso_minutos}")
                print(f"      Atraso em horas decimais: {atraso_horas:.4f}")
                print(f"      Formato HH:MM: {int(atraso_horas):02d}:{int((atraso_horas % 1) * 60):02d}")
                
                return True
            else:
                print(f"   ✅ SEM ATRASO: Entrada dentro da tolerância")
                return False
        else:
            print(f"   ❌ Nenhuma entrada da manhã encontrada")
            return False
        
    except Exception as e:
        print(f"❌ Erro ao conectar ao banco: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def testar_calculo_sistema():
    """
    Simula o cálculo que o sistema deveria fazer
    """
    print(f"\n🧮 SIMULAÇÃO DO CÁLCULO DO SISTEMA:")
    print("=" * 50)
    
    # Dados do teste
    entrada_real = time(7, 37)  # 07:37 (do registro)
    horario_esperado = time(8, 0)  # 08:00 (padrão)
    tolerancia = 10  # 10 minutos
    
    print(f"📊 Dados do teste:")
    print(f"   Entrada real: {entrada_real}")
    print(f"   Horário esperado: {horario_esperado}")
    print(f"   Tolerância: {tolerancia} minutos")
    
    # Calcular horário limite
    horario_limite = datetime.combine(datetime.today(), horario_esperado) + timedelta(minutes=tolerancia)
    horario_limite = horario_limite.time()
    
    print(f"   Horário limite: {horario_limite}")
    
    # Verificar se há atraso
    if entrada_real > horario_limite:
        # Calcular atraso
        dt_real = datetime.combine(datetime.today(), entrada_real)
        dt_limite = datetime.combine(datetime.today(), horario_limite)
        atraso_segundos = (dt_real - dt_limite).total_seconds()
        atraso_minutos = int(atraso_segundos / 60)
        atraso_horas = atraso_segundos / 3600
        
        print(f"\n⚠️ RESULTADO - ATRASO:")
        print(f"   Atraso em segundos: {atraso_segundos}")
        print(f"   Atraso em minutos: {atraso_minutos}")
        print(f"   Atraso em horas decimais: {atraso_horas:.4f}")
        print(f"   Formato HH:MM: {int(atraso_horas):02d}:{int((atraso_horas % 1) * 60):02d}")
        
        return True
    else:
        print(f"\n✅ RESULTADO - SEM ATRASO")
        print(f"   Entrada {entrada_real} está antes do limite {horario_limite}")
        return False

def main():
    # Teste 1: Análise do banco
    atraso_banco = testar_atrasos_banco()
    
    # Teste 2: Simulação do cálculo
    atraso_simulado = testar_calculo_sistema()
    
    # Resultado final
    print(f"\n" + "=" * 60)
    print(f"📊 RESULTADO FINAL DOS TESTES")
    print(f"=" * 60)
    print(f"✅ Atraso detectado no banco: {'SIM' if atraso_banco else 'NÃO'}")
    print(f"✅ Atraso na simulação: {'SIM' if atraso_simulado else 'NÃO'}")
    
    if atraso_banco or atraso_simulado:
        print(f"\n🎯 CONCLUSÃO:")
        print(f"   Há atraso que deveria ser calculado pelo sistema")
        print(f"   Se não aparece no relatório, há problema na implementação")
    else:
        print(f"\n🎯 CONCLUSÃO:")
        print(f"   Não há atraso para ser calculado")
        print(f"   Sistema está correto em não mostrar atrasos")

if __name__ == "__main__":
    main()
