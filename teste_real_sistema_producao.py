#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 TESTE REAL NO SISTEMA DE PRODUÇÃO - RLPONTO-WEB
=================================================

Sistema: RLPONTO-WEB v1.0
Data: 18/07/2025
Objetivo: Teste REAL com dados REAIS no sistema em produção

DIFERENÇAS DO TESTE ANTERIOR:
- ✅ Conecta ao banco MySQL real
- ✅ Usa funcionários reais existentes
- ✅ Registra batidas via API real
- ✅ Verifica dados persistidos
- ✅ Testa interface web real
- ✅ Valida com dados do servidor
"""

import sys
import os
import requests
import pymysql
from datetime import datetime, time, date, timedelta
import logging
import json

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('teste_real')

class TesteRealSistemaProducao:
    """
    Classe para teste REAL no sistema de produção.
    """
    
    def __init__(self, base_url="http://************:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.conexao_db = None
        self.funcionarios_reais = []
        self.registros_inseridos = []
        
        logger.info(f"🔥 Iniciando teste REAL no sistema: {base_url}")

    def conectar_banco_real(self):
        """
        Conecta ao banco MySQL REAL do sistema.
        """
        try:
            # Conectar ao banco real (ajustar credenciais conforme necessário)
            self.conexao_db = pymysql.connect(
                host='************',  # IP do servidor real
                user='root',          # Usuário do banco
                password='',          # Senha do banco (configurar)
                database='controle_ponto',
                charset='utf8mb4'
            )
            
            logger.info("✅ Conectado ao banco MySQL REAL")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao conectar banco real: {e}")
            logger.info("💡 Verifique se o banco está acessível e as credenciais estão corretas")
            return False

    def fazer_login_real(self, usuario="admin", senha="@Ric6109"):
        """
        Faz login REAL no sistema via interface web.
        """
        try:
            # Fazer login real na interface
            login_data = {
                'usuario': usuario,
                'senha': senha
            }
            
            response = self.session.post(f"{self.base_url}/login", data=login_data)
            
            if response.status_code == 200:
                logger.info("✅ Login REAL realizado com sucesso")
                return True
            else:
                logger.error(f"❌ Falha no login real: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro no login real: {e}")
            return False

    def obter_funcionarios_reais(self):
        """
        Obtém funcionários REAIS do banco de dados.
        """
        if not self.conexao_db:
            logger.error("❌ Sem conexão com banco real")
            return False
        
        try:
            cursor = self.conexao_db.cursor(pymysql.cursors.DictCursor)
            
            # Buscar funcionários ativos REAIS
            sql = """
            SELECT id, nome_completo, matricula, empresa_id, status, 
                   horas_trabalho_obrigatorias
            FROM funcionarios 
            WHERE status = 'ativo' 
            LIMIT 5
            """
            
            cursor.execute(sql)
            funcionarios = cursor.fetchall()
            
            self.funcionarios_reais = funcionarios
            
            logger.info(f"✅ Obtidos {len(funcionarios)} funcionários REAIS:")
            for func in funcionarios:
                logger.info(f"   - {func['nome_completo']} (ID: {func['id']})")
            
            return len(funcionarios) > 0
            
        except Exception as e:
            logger.error(f"❌ Erro ao obter funcionários reais: {e}")
            return False

    def registrar_ponto_real(self, funcionario_id, tipo_registro):
        """
        Registra ponto REAL via API do sistema.
        """
        try:
            dados_registro = {
                'funcionario_id': funcionario_id,
                'tipo_registro': tipo_registro,
                'observacoes': f'TESTE REAL - {datetime.now().strftime("%H:%M:%S")}'
            }
            
            response = self.session.post(
                f"{self.base_url}/registro-ponto/api/registrar-manual",
                data=dados_registro
            )
            
            if response.status_code == 200:
                resultado = response.json()
                if resultado.get('success'):
                    logger.info(f"✅ Ponto REAL registrado: {tipo_registro} para funcionário {funcionario_id}")
                    
                    # Armazenar para limpeza posterior
                    self.registros_inseridos.append({
                        'funcionario_id': funcionario_id,
                        'tipo': tipo_registro,
                        'timestamp': datetime.now()
                    })
                    
                    return True
                else:
                    logger.error(f"❌ Falha no registro real: {resultado.get('message')}")
                    return False
            else:
                logger.error(f"❌ Erro HTTP no registro real: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao registrar ponto real: {e}")
            return False

    def verificar_dados_persistidos(self, funcionario_id):
        """
        Verifica se os dados foram REALMENTE persistidos no banco.
        """
        if not self.conexao_db:
            return False
        
        try:
            cursor = self.conexao_db.cursor(pymysql.cursors.DictCursor)
            
            # Buscar registros inseridos hoje
            sql = """
            SELECT id, funcionario_id, tipo_registro, hora_registro, observacoes
            FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND DATE(data_registro) = CURDATE()
            AND observacoes LIKE 'TESTE REAL%'
            ORDER BY hora_registro DESC
            LIMIT 10
            """
            
            cursor.execute(sql, (funcionario_id,))
            registros = cursor.fetchall()
            
            logger.info(f"✅ Encontrados {len(registros)} registros REAIS no banco:")
            for reg in registros:
                logger.info(f"   - {reg['tipo_registro']} às {reg['hora_registro']} (ID: {reg['id']})")
            
            return len(registros) > 0
            
        except Exception as e:
            logger.error(f"❌ Erro ao verificar dados persistidos: {e}")
            return False

    def testar_calculos_reais(self, funcionario_id):
        """
        Testa cálculos com dados REAIS do funcionário.
        """
        try:
            # Obter registros do funcionário via API real
            response = self.session.get(
                f"{self.base_url}/ponto-admin/api/funcionario/{funcionario_id}/registros",
                params={'data': date.today().strftime('%Y-%m-%d')}
            )
            
            if response.status_code == 200:
                registros = response.json()
                logger.info(f"✅ Obtidos {len(registros)} registros reais para cálculo")
                
                # Aqui você pode implementar validação dos cálculos
                # usando os dados reais retornados pela API
                
                return True
            else:
                logger.error(f"❌ Erro ao obter registros para cálculo: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao testar cálculos reais: {e}")
            return False

    def limpar_dados_teste(self):
        """
        Limpa dados de teste inseridos no sistema real.
        """
        if not self.conexao_db:
            return
        
        try:
            cursor = self.conexao_db.cursor()
            
            # Deletar registros de teste inseridos
            sql = """
            DELETE FROM registros_ponto 
            WHERE observacoes LIKE 'TESTE REAL%' 
            AND DATE(data_registro) = CURDATE()
            """
            
            cursor.execute(sql)
            registros_deletados = cursor.rowcount
            
            self.conexao_db.commit()
            
            logger.info(f"🧹 Removidos {registros_deletados} registros de teste do sistema real")
            
        except Exception as e:
            logger.error(f"❌ Erro ao limpar dados de teste: {e}")

    def executar_teste_real_completo(self):
        """
        Executa teste REAL completo no sistema de produção.
        """
        logger.info("🔥 INICIANDO TESTE REAL NO SISTEMA DE PRODUÇÃO")
        logger.info("=" * 70)
        
        # 1. Conectar ao banco real
        if not self.conectar_banco_real():
            logger.error("❌ Não foi possível conectar ao banco real. Abortando.")
            return False
        
        # 2. Fazer login real
        if not self.fazer_login_real():
            logger.error("❌ Não foi possível fazer login real. Abortando.")
            return False
        
        # 3. Obter funcionários reais
        if not self.obter_funcionarios_reais():
            logger.error("❌ Não foi possível obter funcionários reais. Abortando.")
            return False
        
        # 4. Testar com primeiro funcionário real
        funcionario_teste = self.funcionarios_reais[0]
        funcionario_id = funcionario_teste['id']
        funcionario_nome = funcionario_teste['nome_completo']
        
        logger.info(f"🎯 Testando com funcionário REAL: {funcionario_nome} (ID: {funcionario_id})")
        
        # 5. Registrar batidas reais
        batidas_teste = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']
        
        sucesso_registros = 0
        for batida in batidas_teste:
            if self.registrar_ponto_real(funcionario_id, batida):
                sucesso_registros += 1
            
            # Aguardar um pouco entre registros
            import time
            time.sleep(1)
        
        # 6. Verificar persistência real
        dados_persistidos = self.verificar_dados_persistidos(funcionario_id)
        
        # 7. Testar cálculos reais
        calculos_ok = self.testar_calculos_reais(funcionario_id)
        
        # 8. Relatório do teste real
        logger.info("\n" + "=" * 70)
        logger.info("📊 RELATÓRIO DO TESTE REAL")
        logger.info("=" * 70)
        logger.info(f"✅ Funcionário testado: {funcionario_nome}")
        logger.info(f"✅ Batidas registradas: {sucesso_registros}/{len(batidas_teste)}")
        logger.info(f"✅ Dados persistidos: {'Sim' if dados_persistidos else 'Não'}")
        logger.info(f"✅ Cálculos funcionando: {'Sim' if calculos_ok else 'Não'}")
        
        # 9. Perguntar sobre limpeza
        resposta = input("\n🗑️  Deseja limpar os dados de teste do sistema real? (s/N): ")
        if resposta.lower() == 's':
            self.limpar_dados_teste()
        
        # 10. Resultado final
        teste_sucesso = (sucesso_registros > 0 and dados_persistidos)
        
        if teste_sucesso:
            logger.info("\n🎉 TESTE REAL CONCLUÍDO COM SUCESSO!")
            logger.info("✅ Sistema funcionando corretamente em produção")
        else:
            logger.info("\n⚠️ TESTE REAL APRESENTOU PROBLEMAS!")
            logger.info("❌ Verificar configurações do sistema")
        
        return teste_sucesso

def main():
    """
    Função principal para executar teste real.
    """
    print("🔥 TESTE REAL NO SISTEMA DE PRODUÇÃO RLPONTO-WEB")
    print("=" * 60)
    print("⚠️  ATENÇÃO: Este teste irá inserir dados REAIS no sistema!")
    print("=" * 60)
    
    confirmacao = input("Deseja continuar com o teste REAL? (s/N): ")
    if confirmacao.lower() != 's':
        print("❌ Teste cancelado pelo usuário.")
        return False
    
    # Executar teste real
    teste = TesteRealSistemaProducao()
    sucesso = teste.executar_teste_real_completo()
    
    return sucesso

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
