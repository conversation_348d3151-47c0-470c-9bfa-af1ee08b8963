#!/usr/bin/env python3
"""
Script para verificar usuário cavalcrod
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import get_db_connection
import hashlib

def check_user():
    conn = get_db_connection()
    cursor = conn.cursor()
    
    print("🔍 Verificando usuário cavalcrod...")
    
    cursor.execute("SELECT id, usuario, nivel, senha FROM usuarios WHERE usuario = %s", ('cavalcrod',))
    user = cursor.fetchone()
    
    if user:
        print(f"✅ Usuário encontrado: {user}")
        print(f"ID: {user['id']}")
        print(f"Usuário: {user['usuario']}")
        print(f"Nível: {user['nivel']}")
        print(f"Senha (hash): {user['senha'][:20]}...")
        
        # Testar senha
        senha_teste = "@Ric6109"
        senha_hash = hashlib.md5(senha_teste.encode()).hexdigest()
        print(f"Hash da senha teste: {senha_hash}")
        print(f"Senhas coincidem: {senha_hash == user['senha']}")
        
    else:
        print("❌ Usuário cavalcrod não encontrado!")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    check_user() 