#!/usr/bin/env python3
"""
Correção: Jornada MSV Engenharia
Corrige a jornada padrão da empresa MSV Engenharia para os horários corretos
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def corrigir_jornada_msv():
    """Corrige jornada da MSV Engenharia"""
    
    print("🔧 CORREÇÃO: JORNADA MSV ENGENHARIA (ID 4)")
    print("=" * 60)
    
    db = DatabaseManager()
    
    # Horários corretos conforme mostrado na interface
    horarios_corretos = {
        'seg_qui_entrada': '07:30:00',
        'seg_qui_saida': '17:30:00',
        'sexta_entrada': '07:30:00', 
        'sexta_saida': '16:30:00',
        'intervalo_inicio': '12:00:00',
        'intervalo_fim': '13:00:00',
        'tolerancia': 15
    }
    
    print("📋 PASSO 1: Verificando jornada atual...")
    
    jornada_atual = db.execute_query("""
        SELECT id, nome_jornada, seg_qui_entrada, seg_qui_saida,
               sexta_entrada, sexta_saida, intervalo_inicio, intervalo_fim
        FROM jornadas_trabalho 
        WHERE empresa_id = 4 AND padrao = 1 AND ativa = 1
    """, fetch_one=True)
    
    if jornada_atual:
        print(f"✅ Jornada atual: {jornada_atual['nome_jornada']} (ID: {jornada_atual['id']})")
        print(f"   • Seg-Qui: {jornada_atual['seg_qui_entrada']} às {jornada_atual['seg_qui_saida']}")
        print(f"   • Sexta: {jornada_atual['sexta_entrada']} às {jornada_atual['sexta_saida']}")
        
        # Verificar se já está correto
        if (str(jornada_atual['seg_qui_entrada']) == horarios_corretos['seg_qui_entrada'] and
            str(jornada_atual['seg_qui_saida']) == horarios_corretos['seg_qui_saida']):
            print("✅ Jornada já está com os horários corretos!")
            return
        
        print("❌ Jornada com horários incorretos - corrigindo...")
        
        # Corrigir jornada existente
        db.execute_query("""
            UPDATE jornadas_trabalho 
            SET seg_qui_entrada = %s,
                seg_qui_saida = %s,
                sexta_entrada = %s,
                sexta_saida = %s,
                intervalo_inicio = %s,
                intervalo_fim = %s,
                tolerancia_entrada_minutos = %s
            WHERE id = %s
        """, (
            horarios_corretos['seg_qui_entrada'],
            horarios_corretos['seg_qui_saida'],
            horarios_corretos['sexta_entrada'],
            horarios_corretos['sexta_saida'],
            horarios_corretos['intervalo_inicio'],
            horarios_corretos['intervalo_fim'],
            horarios_corretos['tolerancia'],
            jornada_atual['id']
        ), fetch_all=False)
        
        print(f"✅ Jornada {jornada_atual['id']} corrigida!")
        jornada_id = jornada_atual['id']
        
    else:
        print("❌ Nenhuma jornada padrão encontrada - criando nova...")
        
        # Criar nova jornada
        db.execute_query("""
            INSERT INTO jornadas_trabalho (
                empresa_id, nome_jornada, tipo_jornada,
                seg_qui_entrada, seg_qui_saida,
                sexta_entrada, sexta_saida,
                intervalo_inicio, intervalo_fim,
                tolerancia_entrada_minutos,
                padrao, ativa
            ) VALUES (
                4, 'Horário Padrão MSV', 'NORMAL',
                %s, %s, %s, %s, %s, %s, %s, TRUE, TRUE
            )
        """, (
            horarios_corretos['seg_qui_entrada'],
            horarios_corretos['seg_qui_saida'],
            horarios_corretos['sexta_entrada'],
            horarios_corretos['sexta_saida'],
            horarios_corretos['intervalo_inicio'],
            horarios_corretos['intervalo_fim'],
            horarios_corretos['tolerancia']
        ), fetch_all=False)
        
        # Buscar ID da jornada criada
        nova_jornada = db.execute_query("""
            SELECT id FROM jornadas_trabalho 
            WHERE empresa_id = 4 AND nome_jornada = 'Horário Padrão MSV'
            ORDER BY id DESC LIMIT 1
        """, fetch_one=True)
        
        jornada_id = nova_jornada['id']
        print(f"✅ Nova jornada criada com ID: {jornada_id}")
    
    print("\n📋 PASSO 2: Aplicando jornada a todos os funcionários...")
    
    # Buscar funcionários da empresa
    funcionarios = db.execute_query("""
        SELECT id, nome_completo FROM funcionarios 
        WHERE empresa_id = 4 AND ativo = 1
    """)
    
    if funcionarios:
        print(f"📊 Encontrados {len(funcionarios)} funcionários")
        
        # Aplicar jornada a todos
        db.execute_query("""
            UPDATE funcionarios 
            SET jornada_trabalho_id = %s,
                usa_horario_empresa = TRUE,
                data_atualizacao_jornada = CURRENT_TIMESTAMP,
                jornada_alterada_por = 1
            WHERE empresa_id = 4 AND ativo = 1
        """, (jornada_id,), fetch_all=False)
        
        print("✅ Jornada aplicada a todos os funcionários!")
        
        # Registrar no histórico
        for func in funcionarios:
            db.execute_query("""
                INSERT INTO historico_funcionario 
                (funcionario_id, tipo_evento, descricao, usuario_responsavel, data_evento)
                VALUES (%s, 'JORNADA_CORRIGIDA', %s, 1, CURRENT_TIMESTAMP)
            """, (
                func['id'], 
                f"Correção automática: Aplicada jornada padrão MSV (07:30-17:30)"
            ), fetch_all=False)
        
        print("✅ Histórico registrado!")
    
    print("\n📋 PASSO 3: Verificação final...")
    
    # Verificar resultado
    verificacao = db.execute_query("""
        SELECT f.id, f.nome_completo,
               jt.nome_jornada, jt.seg_qui_entrada, jt.seg_qui_saida,
               jt.sexta_entrada, jt.sexta_saida
        FROM funcionarios f
        INNER JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.empresa_id = 4 AND f.ativo = 1
        LIMIT 3
    """)
    
    if verificacao:
        print("✅ VERIFICAÇÃO - Funcionários com jornada corrigida:")
        for func in verificacao:
            print(f"   👤 {func['nome_completo']}")
            print(f"      • Jornada: {func['nome_jornada']}")
            print(f"      • Seg-Qui: {func['seg_qui_entrada']} às {func['seg_qui_saida']}")
            print(f"      • Sexta: {func['sexta_entrada']} às {func['sexta_saida']}")
    
    print(f"\n🎯 CORREÇÃO CONCLUÍDA!")
    print(f"✅ Jornada MSV Engenharia corrigida para 07:30-17:30")
    print(f"✅ Todos os funcionários atualizados")
    print(f"✅ Sistema de herança funcionando corretamente")

if __name__ == "__main__":
    corrigir_jornada_msv()
