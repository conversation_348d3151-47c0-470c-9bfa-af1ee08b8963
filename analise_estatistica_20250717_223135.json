{"metadata": {"data_analise": "2025-07-17T22:31:35.953285", "arquivo_origem": {"data_teste": "2025-07-17T22:31:18.904625", "sistema": "RLPONTO-WEB v1.0", "tipo_teste": "Robusto 30 Funcionários", "total_funcionarios": 30, "total_batidas": 660}}, "estatisticas_avancadas": {"horas_trabalhadas": {"media": 166.21666666666667, "mediana": 168.03500000000003, "desvio_padrao": 8.78302372461366, "minimo": 145.25, "maximo": 180.5, "amplitude": 35.25}, "banco_horas": {"media": -8.886, "mediana": -7.805, "desvio_padrao": 8.892968008488504, "minimo": -31.02, "maximo": 9.29, "positivos": 2, "negativos": 28, "zeros": 0}, "horas_extras": {"media": 3.25, "mediana": 1.5, "total": 97.5, "funcionarios_com_extras": 29}, "erros": {"media_erros_por_funcionario": 1.6, "total_erros": 48, "funcionarios_sem_erro": 15, "funcionarios_com_erro": 15, "max_erros_funcionario": 9}, "comparacao_tipos": {"corretos": {"quantidade": 15, "horas_media": 170.91666666666666, "horas_total": 2563.75, "erros_media": 0, "erros_total": 0, "extras_media": 0.7333333333333333, "extras_total": 11.0}, "problematicos": {"quantidade": 15, "horas_media": 161.51666666666668, "horas_total": 2422.75, "erros_media": 3.2, "erros_total": 48, "extras_media": 5.766666666666667, "extras_total": 86.5}}, "padroes_erro": {"tipos_erro_frequencia": {"Saída deve ser depois da entrada da tarde": 18, "Entrada da tarde deve ser depois da saída para almoço": 30, "Intervalo de almoço deve ser de pelo menos 30 minutos": 30}, "funcionarios_problema": [{"nome": "João27", "tipo": "problematico", "erros": 9, "horas": 176.0}, {"nome": "João29", "tipo": "problematico", "erros": 7, "horas": 167.5}, {"nome": "João<PERSON>", "tipo": "problematico", "erros": 4, "horas": 155.75}, {"nome": "João25", "tipo": "problematico", "erros": 4, "horas": 159.25}, {"nome": "João<PERSON>", "tipo": "problematico", "erros": 3, "horas": 166.25}, {"nome": "João20", "tipo": "problematico", "erros": 3, "horas": 172.5}, {"nome": "João23", "tipo": "problematico", "erros": 3, "horas": 180.5}, {"nome": "João<PERSON>", "tipo": "problematico", "erros": 3, "horas": 145.25}, {"nome": "João18", "tipo": "problematico", "erros": 2, "horas": 158.5}, {"nome": "João<PERSON>", "tipo": "problematico", "erros": 2, "horas": 147.5}, {"nome": "João22", "tipo": "problematico", "erros": 2, "horas": 162.75}, {"nome": "João24", "tipo": "problematico", "erros": 2, "horas": 163.0}, {"nome": "João28", "tipo": "problematico", "erros": 2, "horas": 150.5}, {"nome": "João21", "tipo": "problematico", "erros": 1, "horas": 156.0}, {"nome": "João30", "tipo": "problematico", "erros": 1, "horas": 161.5}], "taxa_erro_geral": 50.0}, "performance": {"taxa_sucesso_batidas": 92.72727272727272, "taxa_erro_batidas": 7.2727272727272725, "media_batidas_por_funcionario": 22.0, "robustez_sistema": "Alta"}}}