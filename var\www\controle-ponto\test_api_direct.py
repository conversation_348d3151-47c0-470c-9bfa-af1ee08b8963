# TESTE DE API DIRETA - SEM AUTENTICAÇÃO
from flask import Flask, jsonify
import socket

app = Flask(__name__)

@app.route('/test-api/ping')
def test_ping():
    return jsonify({
        'status': 'success',
        'message': 'API de Teste Funcionando!',
        'server': socket.gethostname(),
        'port': 5001
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=False) 