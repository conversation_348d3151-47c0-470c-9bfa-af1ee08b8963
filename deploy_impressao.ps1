# Deploy das Melhorias do Relatório de Impressão
# ===============================================

Write-Host "DEPLOY DAS MELHORIAS DO RELATÓRIO DE IMPRESSÃO" -ForegroundColor Green
Write-Host "============================================================"

# Verificar se o arquivo local existe
$localTemplate = "var\www\controle-ponto\templates\ponto_admin\imprimir_ponto.html"
if (-not (Test-Path $localTemplate)) {
    Write-Host "ERRO: Arquivo local não encontrado: $localTemplate" -ForegroundColor Red
    exit 1
}

Write-Host "OK: Arquivo local encontrado: $localTemplate" -ForegroundColor Green

# Criar timestamp para backup
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

Write-Host ""
Write-Host "INSTRUÇÕES PARA DEPLOY MANUAL:" -ForegroundColor Yellow
Write-Host "==============================="

Write-Host ""
Write-Host "1. Conectar ao servidor:" -ForegroundColor Cyan
Write-Host "   ssh user@************"
Write-Host "   Senha: @Ric6109"

Write-Host ""
Write-Host "2. Criar backup:" -ForegroundColor Cyan
Write-Host "   cd /var/www/controle-ponto/templates/ponto_admin"
Write-Host "   cp imprimir_ponto.html imprimir_ponto.html.backup.deploy.$timestamp"

Write-Host ""
Write-Host "3. Copiar arquivo atualizado:" -ForegroundColor Cyan
Write-Host "   # No Windows (outro terminal):"
Write-Host "   scp `"$localTemplate`" user@************:/var/www/controle-ponto/templates/ponto_admin/imprimir_ponto.html"

Write-Host ""
Write-Host "4. Reiniciar serviço:" -ForegroundColor Cyan
Write-Host "   sudo systemctl restart controle-ponto"
Write-Host "   sudo systemctl status controle-ponto"

Write-Host ""
Write-Host "5. Testar:" -ForegroundColor Cyan
Write-Host "   curl -I http://localhost:5000/ponto-admin/"

Write-Host ""
Write-Host "6. Validar no navegador:" -ForegroundColor Cyan
Write-Host "   http://************/ponto-admin/"

Write-Host ""
Write-Host "ROLLBACK (se necessário):" -ForegroundColor Yellow
Write-Host "   cp imprimir_ponto.html.backup.deploy.$timestamp imprimir_ponto.html"
Write-Host "   sudo systemctl restart controle-ponto"

Write-Host ""
Write-Host "RESUMO DAS MELHORIAS IMPLEMENTADAS:" -ForegroundColor Green
Write-Host "=================================="
Write-Host "OK: Novo cabeçalho profissional com informações da empresa"
Write-Host "OK: Informações do colaborador (CPF, PIS, cargo, setor, data admissão)"
Write-Host "OK: Nova estrutura de colunas (H.T., H.D., T.N., Extra, H.N., Extras 100%, Desc.)"
Write-Host "OK: Rodapé com totais gerais e resumos"
Write-Host "OK: Linha de assinatura do funcionário"
Write-Host "OK: Estilos otimizados para impressão"

Write-Host ""
Write-Host "PRÓXIMOS PASSOS APÓS DEPLOY:" -ForegroundColor Magenta
Write-Host "1. Acessar detalhes de um funcionário"
Write-Host "2. Clicar em Imprimir Ponto"
Write-Host "3. Verificar se o novo layout está correto"
Write-Host "4. Testar impressão em papel A4"

Write-Host ""
Write-Host "IMPORTANTE:" -ForegroundColor Red
Write-Host "- Backup foi criado automaticamente"
Write-Host "- Em caso de problemas, use o comando de rollback"
Write-Host "- Teste sempre antes de usar em produção"

Write-Host ""
Write-Host "Deploy preparado com sucesso!" -ForegroundColor Green
