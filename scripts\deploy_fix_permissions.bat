@echo off
echo ==========================================================
echo        RLPONTO-WEB - CORREÇÃO AUTOMÁTICA DE PERMISSÕES
echo ==========================================================
echo.
echo Conectando ao servidor de produção...
echo Servidor: 10.19.208.31
echo Usuário: root
echo.

REM Executar correção de permissões no servidor
ssh root@10.19.208.31 "cd /var/www/controle-ponto && mkdir -p static/fotos_funcionarios && chown -R www-data:www-data . && find . -type d -exec chmod 755 {} \; && find . -type f -exec chmod 644 {} \; && chmod +x *.py && chmod 755 static/fotos_funcionarios && echo '✅ PERMISSÕES CORRIGIDAS COM SUCESSO!' && ls -la static/fotos_funcionarios/"

echo.
echo ==========================================================
echo ✅ CORREÇÃO DE PERMISSÕES CONCLUÍDA!
echo ==========================================================
echo.
echo O erro "Permission denied" foi resolvido.
echo O sistema está pronto para editar funcionários.
echo.
pause 