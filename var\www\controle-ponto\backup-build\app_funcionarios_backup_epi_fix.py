#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
<PERSON>ó<PERSON>lo de Funcionários - Controle de Ponto
-------------------------------------------

Este módulo contém todas as rotas e funcionalidades relacionadas
ao gerenciamento de funcionários do sistema.
"""

import json
import logging
import os
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, Response, current_app
from werkzeug.security import generate_password_hash
from datetime import timedelta

# Imports locais
from utils.database import FuncionarioQueries, DatabaseManager
from utils.auth import require_login, require_admin, get_current_user, get_template_context
from utils.helpers import (
    FormValidator, parse_biometria_data, format_date, format_cpf, 
    format_telefone, generate_breadcrumbs, safe_int, gerar_nome_foto_funcionario
)

# Configuração do logger
logger = logging.getLogger('controle-ponto.funcionarios')

# Criação do Blueprint
funcionarios_bp = Blueprint('funcionarios', __name__, url_prefix='/funcionarios')

# Constantes de validação
VALID_SEXO = ['M', 'F', 'Outro']
VALID_ESTADO_CIVIL = ['Solteiro', 'Casado', 'Divorciado', 'Viuvo']
VALID_TIPO_CONTRATO = ['CLT', 'PJ', 'Estagio', 'Temporario']
VALID_NIVEL_ACESSO = ['Funcionario', 'Supervisao', 'Gerencia']
VALID_TURNO = ['Diurno', 'Noturno', 'Misto']
VALID_STATUS = ['Ativo', 'Inativo']

REQUIRED_FIELDS = [
    'nome_completo', 'cpf', 'rg', 'data_nascimento', 'sexo', 'estado_civil', 'nacionalidade',
    'ctps_numero', 'ctps_serie_uf', 'pis_pasep', 'endereco_cep', 'endereco_estado', 'telefone1',
    'cargo', 'setor_obra', 'matricula_empresa', 'data_admissao', 'tipo_contrato',
    'jornada_seg_qui_entrada', 'jornada_seg_qui_saida', 'jornada_sex_entrada', 'jornada_sex_saida',
    'jornada_intervalo_entrada', 'jornada_intervalo_saida',
    'nivel_acesso', 'turno', 'tolerancia_ponto', 'status_cadastro'
]

# Campos opcionais de biometria
BIOMETRIA_FIELDS = [
    'digital_dedo1', 'digital_dedo2', 'qualidade_dedo1', 'qualidade_dedo2'
]

# Campos de pagamento/remuneração
PAGAMENTO_FIELDS = [
    'salario_base', 'tipo_pagamento', 'valor_hora', 'valor_hora_extra', 
    'percentual_hora_extra', 'vale_transporte', 'vale_alimentacao', 
    'outros_beneficios', 'desconto_inss', 'desconto_irrf', 
    'observacoes_pagamento', 'data_ultima_alteracao_salario'
]

def get_fotos_dir():
    return os.path.join(current_app.root_path, 'static', 'fotos_funcionarios')

def _safe_decimal(value):
    """
    Converte valor para decimal de forma segura.
    
    Args:
        value (str): Valor a ser convertido
        
    Returns:
        float or None: Valor convertido ou None se inválido
    """
    if not value or not str(value).strip():
        return None
    
    try:
        return float(str(value).strip())
    except (ValueError, TypeError):
        return None

def _validar_campos_pagamento(data, validator):
    """
    Valida campos específicos de pagamento.
    
    Args:
        data (dict): Dados do formulário
        validator (FormValidator): Instância do validador
    """
    # Validar tipo de pagamento
    tipos_validos = ['Mensal', 'Quinzenal', 'Semanal', 'Diario', 'Por_Hora']
    tipo_pagamento = data.get('tipo_pagamento', '')
    if tipo_pagamento and tipo_pagamento not in tipos_validos:
        validator.add_error('tipo_pagamento', f"Tipo de pagamento deve ser um dos: {', '.join(tipos_validos)}")
    
    # Validar valores monetários
    salario_base = data.get('salario_base')
    if salario_base is not None and salario_base < 0:
        validator.add_error('salario_base', "Salário base não pode ser negativo")
    
    valor_hora = data.get('valor_hora')
    if valor_hora is not None and valor_hora < 0:
        validator.add_error('valor_hora', "Valor da hora não pode ser negativo")
    
    valor_hora_extra = data.get('valor_hora_extra')
    if valor_hora_extra is not None and valor_hora_extra < 0:
        validator.add_error('valor_hora_extra', "Valor da hora extra não pode ser negativo")
    
    percentual_hora_extra = data.get('percentual_hora_extra')
    if percentual_hora_extra is not None and (percentual_hora_extra < 0 or percentual_hora_extra > 200):
        validator.add_error('percentual_hora_extra', "Percentual de hora extra deve estar entre 0% e 200%")
    
    # Validar benefícios
    for campo in ['vale_transporte', 'vale_alimentacao', 'outros_beneficios']:
        valor = data.get(campo)
        if valor is not None and valor < 0:
            validator.add_error(campo, f"{campo.replace('_', ' ').title()} não pode ser negativo")
    
    # Validar consistência: se há salário base, deve ter valor hora
    if salario_base and salario_base > 0:
        if not valor_hora or valor_hora <= 0:
            # Calcular automaticamente se não foi fornecido
            data['valor_hora'] = round(salario_base / 220, 2)
            logger.info(f"💰 Valor hora calculado automaticamente: R$ {data['valor_hora']}")
        
        # Calcular hora extra se não foi fornecido
        if percentual_hora_extra and (not valor_hora_extra or valor_hora_extra <= 0):
            valor_hora_calculado = data.get('valor_hora', valor_hora)
            if valor_hora_calculado:
                data['valor_hora_extra'] = round(valor_hora_calculado * (1 + percentual_hora_extra / 100), 2)
                logger.info(f"💰 Valor hora extra calculado automaticamente: R$ {data['valor_hora_extra']}")
    
    logger.info(f"💰 Validação de pagamento concluída - Salário: R$ {salario_base or 0}, Hora: R$ {valor_hora or 0}")

@funcionarios_bp.route('/')
@require_login
def index():
    """
    Lista funcionários com paginação e filtros.
    """
    try:
        # Parâmetros de paginação e filtros
        page = safe_int(request.args.get('page', 1), 1)
        per_page = safe_int(request.args.get('per_page', 10), 10)
        search = request.args.get('search', '').strip()
        status = request.args.get('status', '').strip()
        
        # Busca funcionários com paginação
        result = FuncionarioQueries.get_all(
            page=page,
            per_page=per_page,
            search=search if search else None,
            status=status if status else None
        )
        
        # Contexto do template
        context = get_template_context()
        context.update({
            'funcionarios': result['data'],
            'pagination': result['pagination'],
            'search': search,
            'status': status,
            'breadcrumbs': generate_breadcrumbs('funcionarios'),
            'format_cpf': format_cpf,
            'format_date': format_date
        })
        
        return render_template('funcionarios/index.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao listar funcionários: {e}")
        error_msg = str(e)
        
        # Mensagens específicas baseadas no tipo de erro
        if "Banco de dados indisponível" in error_msg:
            flash("Sistema temporariamente indisponível. Verifique se o MySQL está rodando.", "error")
        elif "Erro de autenticação" in error_msg:
            flash("Erro de configuração do banco. Contate o administrador.", "error")
        elif "não encontrada" in error_msg:
            flash("Base de dados não encontrada. Execute o script de instalação.", "error")
        else:
            flash("Erro ao carregar lista de funcionários", "error")
            
        return redirect(url_for('index'))

@funcionarios_bp.route('/<int:funcionario_id>')
@require_login
def detalhes(funcionario_id):
    """
    Exibe detalhes completos de um funcionário.
    """
    try:
        funcionario = FuncionarioQueries.get_with_epis(funcionario_id)
        
        if not funcionario:
            flash("Funcionário não encontrado", "error")
            return redirect(url_for('funcionarios.index'))
        
        # Contexto do template
        context = get_template_context()
        context.update({
            'funcionario': funcionario,
            'breadcrumbs': generate_breadcrumbs('funcionario_detalhes', funcionario_id=funcionario_id),
            'format_cpf': format_cpf,
            'format_telefone': format_telefone,
            'format_date': format_date
        })
        
        return render_template('funcionarios/detalhes.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar funcionário {funcionario_id}: {e}")
        error_msg = str(e)
        
        # Mensagens específicas baseadas no tipo de erro
        if "Banco de dados indisponível" in error_msg:
            flash("Sistema temporariamente indisponível. Tente novamente em alguns instantes.", "error")
        elif "Erro de autenticação" in error_msg:
            flash("Erro de configuração do sistema. Contate o administrador.", "error")
        elif "não encontrada" in error_msg:
            flash("Base de dados não configurada. Contate o administrador.", "error")
        else:
            flash("Erro ao carregar dados do funcionário", "error")
            
        return redirect(url_for('funcionarios.index'))

@funcionarios_bp.route('/cadastrar', methods=['GET', 'POST'])
@require_login
def cadastrar():
    """
    Cadastra novo funcionário.
    """
    if request.method == 'POST':
        return _processar_formulario_funcionario()
    
    # GET: Exibir formulário
    try:
        proxima_matricula = FuncionarioQueries.get_next_matricula()
        
        context = get_template_context()
        context.update({
            'data': {},
            'proxima_matricula': proxima_matricula,
            'breadcrumbs': generate_breadcrumbs('cadastrar'),
            'modo_edicao': False
        })
        
        return render_template('funcionarios/cadastrar.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao exibir formulário de cadastro: {e}")
        flash("Erro ao carregar formulário", "error")
        return redirect(url_for('funcionarios.index'))

@funcionarios_bp.route('/<int:funcionario_id>/editar', methods=['GET', 'POST'])
@require_admin
def editar(funcionario_id):
    """
    Edita funcionário existente.
    """
    if request.method == 'POST':
        return _processar_formulario_funcionario(funcionario_id)
    
    # GET: Exibir formulário preenchido
    try:
        funcionario = FuncionarioQueries.get_by_id(funcionario_id)
        
        if not funcionario:
            flash("Funcionário não encontrado", "error")
            return redirect(url_for('funcionarios.index'))
        
        # Conversão dos campos de horário para string 'HH:MM' se não forem None
        for campo in [
            'jornada_seg_qui_entrada', 'jornada_seg_qui_saida',
            'jornada_sex_entrada', 'jornada_sex_saida',
            'jornada_intervalo_entrada', 'jornada_intervalo_saida']:
            valor = funcionario.get(campo)
            if valor is not None and hasattr(valor, 'strftime'):
                funcionario[campo] = valor.strftime('%H:%M')
            elif isinstance(valor, timedelta):
                total_seconds = int(valor.total_seconds())
                horas = total_seconds // 3600
                minutos = (total_seconds % 3600) // 60
                funcionario[campo] = f"{horas:02d}:{minutos:02d}"
            elif isinstance(valor, str) and valor:
                partes = valor.split(':')
                if len(partes) >= 2:
                    funcionario[campo] = f"{int(partes[0]):02d}:{int(partes[1]):02d}"
                else:
                    funcionario[campo] = valor
            elif valor is None:
                funcionario[campo] = ''
        
        # 🦺 Buscar EPIs do funcionário para exibir no formulário
        funcionario['epis'] = _buscar_epis_funcionario(funcionario_id)
        
        context = get_template_context()
        context.update({
            'data': funcionario,
            'funcionario_id': funcionario_id,
            'breadcrumbs': generate_breadcrumbs('funcionario_editar', funcionario_id=funcionario_id),
            'modo_edicao': True
        })
        
        return render_template('funcionarios/cadastrar.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar funcionário para edição {funcionario_id}: {e}")
        flash("Erro ao carregar dados para edição", "error")
        return redirect(url_for('funcionarios.index'))

@funcionarios_bp.route('/<int:funcionario_id>/apagar', methods=['POST'])
@require_admin
def apagar(funcionario_id):
    """
    Exclui funcionário e dados relacionados.
    """
    try:
        funcionario = FuncionarioQueries.get_by_id(funcionario_id)
        
        if not funcionario:
            flash("Funcionário não encontrado", "error")
            return redirect(url_for('funcionarios.index'))
        
        # Executa exclusão
        success = FuncionarioQueries.delete_funcionario(funcionario_id)
        
        if success:
            flash(f"Funcionário {funcionario['nome_completo']} excluído com sucesso", "success")
            logger.info(f"Funcionário {funcionario_id} excluído por {get_current_user()['usuario']}")
        else:
            flash("Erro ao excluir funcionário", "error")
            
        return redirect(url_for('funcionarios.index'))
        
    except Exception as e:
        logger.error(f"Erro ao excluir funcionário {funcionario_id}: {e}")
        flash("Erro ao excluir funcionário", "error")
        return redirect(url_for('funcionarios.index'))

@funcionarios_bp.route('/<int:funcionario_id>/foto')
@require_login
def foto_funcionario(funcionario_id):
    """
    Serve a foto do funcionário ou a imagem padrão.
    """
    try:
        funcionario = FuncionarioQueries.get_by_id(funcionario_id)
        
        if funcionario and funcionario.get('foto_3x4'):
            return Response(funcionario['foto_3x4'], mimetype='image/jpeg')
        else:
            # Redirecionar para imagem padrão
            return redirect(url_for('static', filename='images/funcionario_sem_foto.svg'))
            
    except Exception as e:
        logger.error(f"Erro ao carregar foto do funcionário {funcionario_id}: {e}")
        return redirect(url_for('static', filename='images/funcionario_sem_foto.svg'))

@funcionarios_bp.route('/api/<int:funcionario_id>', methods=['GET'])
@require_login
def api_get_funcionario(funcionario_id):
    """
    API para obter dados de um funcionário.
    """
    try:
        funcionario = FuncionarioQueries.get_by_id(funcionario_id)
        
        if not funcionario:
            return jsonify({'error': 'Funcionário não encontrado'}), 404
        
        # Remove dados sensíveis da resposta
        funcionario.pop('digital_dedo1', None)
        funcionario.pop('digital_dedo2', None)
        funcionario.pop('foto_3x4', None)
        funcionario.pop('assinatura', None)
        
        return jsonify(funcionario)
        
    except Exception as e:
        logger.error(f"Erro na API get funcionário {funcionario_id}: {e}")
        return jsonify({'error': 'Erro interno do servidor'}), 500

def _processar_formulario_funcionario(funcionario_id=None):
    """
    Processa formulário de cadastro/edição de funcionário.
    
    Args:
        funcionario_id (int, optional): ID do funcionário para edição
        
    Returns:
        Response: Redirect ou template com erros
    """
    try:
        # Coleta dados do formulário
        form_data = _extrair_dados_formulario()
        
        # 🛡️ ADICIONA funcionario_id aos dados para validação de segurança
        if funcionario_id:
            form_data['funcionario_id'] = funcionario_id
        
        # Validação
        validator = FormValidator()
        _validar_dados_funcionario(form_data, validator)
        
        if validator.has_errors():
            context = get_template_context()
            context.update({
                'errors': validator.get_errors(),
                'data': form_data,
                'proxima_matricula': FuncionarioQueries.get_next_matricula() if not funcionario_id else None,
                'funcionario_id': funcionario_id,
                'modo_edicao': funcionario_id is not None,
                'breadcrumbs': generate_breadcrumbs(
                    'funcionario_editar' if funcionario_id else 'cadastrar',
                    funcionario_id=funcionario_id
                )
            })
            return render_template('funcionarios/cadastrar.html', **context)
        
        # Processamento de dados
        processed_data = _processar_dados_funcionario(form_data, funcionario_id=funcionario_id)
        
        if funcionario_id:
            # Atualização
            success = _atualizar_funcionario(funcionario_id, processed_data)
            if success:
                flash("Funcionário atualizado com sucesso", "success")
                return redirect(url_for('funcionarios.detalhes', funcionario_id=funcionario_id))
        else:
            # Criação
            funcionario_id = _criar_funcionario(processed_data)
            if funcionario_id:
                flash("Funcionário cadastrado com sucesso", "success")
                return redirect(url_for('funcionarios.detalhes', funcionario_id=funcionario_id))
        
        # Se chegou aqui, houve erro
        flash("Erro ao processar dados do funcionário", "error")
        return redirect(url_for('funcionarios.index'))
        
    except Exception as e:
        # Trata erros de validação específicos de segurança
        msg_erro = str(e)
        if "ACESSO NEGADO" in msg_erro or "administradores" in msg_erro.lower():
            flash("🚫 Acesso negado: Somente administradores podem editar biometria existente", "error")
        elif hasattr(e, 'args') and e.args and isinstance(e.args[0], dict):
            # Se o erro for um dict de erros de campo
            erros = e.args[0]
            msg_erro = "; ".join([f"{campo}: {', '.join(msgs)}" for campo, msgs in erros.items()])
            flash(f"Erro de validação: {msg_erro}", "error")
        else:
            flash(f"Erro ao processar formulário: {msg_erro}", "error")
            
        logger.error(f"Erro ao processar formulário: {msg_erro}")
        return redirect(url_for('funcionarios.index'))

def _extrair_dados_formulario():
    """
    Extrai e organiza dados do formulário.
    
    Returns:
        dict: Dados organizados do formulário
    """
    return {
        # Dados pessoais
        'nome_completo': request.form.get('nome_completo', '').strip(),
        'cpf': request.form.get('cpf', '').strip(),
        'rg': request.form.get('rg', '').strip(),
        'data_nascimento': request.form.get('data_nascimento', '').strip(),
        'sexo': request.form.get('sexo', '').strip(),
        'estado_civil': request.form.get('estado_civil', '').strip(),
        'nacionalidade': request.form.get('nacionalidade', '').strip(),
        
        # Documentos trabalhistas
        'ctps_numero': request.form.get('ctps_numero', '').strip(),
        'ctps_serie_uf': request.form.get('ctps_serie_uf', '').strip(),
        'pis_pasep': request.form.get('pis_pasep', '').strip(),
        
        # Endereço
        'endereco_rua': request.form.get('endereco_rua', '').strip(),
        'endereco_bairro': request.form.get('endereco_bairro', '').strip(),
        'endereco_cidade': request.form.get('endereco_cidade', '').strip(),
        'endereco_cep': request.form.get('endereco_cep', '').strip(),
        'endereco_estado': request.form.get('endereco_estado', '').strip(),
        
        # Contato
        'telefone1': request.form.get('telefone1', '').strip(),
        'telefone2': request.form.get('telefone2', '').strip(),
        'email': request.form.get('email', '').strip(),
        
        # Dados profissionais
        'cargo': request.form.get('cargo', '').strip(),
        'setor_obra': request.form.get('setor_obra', '').strip(),
        'matricula_empresa': request.form.get('matricula_empresa', '').strip(),
        'data_admissao': request.form.get('data_admissao', '').strip(),
        'tipo_contrato': request.form.get('tipo_contrato', '').strip(),
        'status_cadastro': request.form.get('status_cadastro', '').strip(),
        
        # Jornada de trabalho
        'jornada_seg_qui_entrada': request.form.get('jornada_seg_qui_entrada', '').strip(),
        'jornada_seg_qui_saida': request.form.get('jornada_seg_qui_saida', '').strip(),
        'jornada_sex_entrada': request.form.get('jornada_sex_entrada', '').strip(),
        'jornada_sex_saida': request.form.get('jornada_sex_saida', '').strip(),
        'jornada_intervalo_entrada': request.form.get('jornada_intervalo_entrada', '').strip(),
        'jornada_intervalo_saida': request.form.get('jornada_intervalo_saida', '').strip(),
        
        # Configurações
        'nivel_acesso': request.form.get('nivel_acesso', '').strip(),
        'turno': request.form.get('turno', '').strip(),
        'tolerancia_ponto': safe_int(request.form.get('tolerancia_ponto', 5), 5),
        'banco_horas': 1 if request.form.get('banco_horas') == 'on' else 0,
        'hora_extra': 1 if request.form.get('hora_extra') == 'on' else 0,
        
        # Biometria e foto
        'digital_dedo1': request.form.get('digital_dedo1', '').strip(),
        'digital_dedo2': request.form.get('digital_dedo2', '').strip(),
        'qualidade_dedo1': safe_int(request.form.get('qualidade_dedo1', 0), 0),
        'qualidade_dedo2': safe_int(request.form.get('qualidade_dedo2', 0), 0),
        'foto_3x4': request.files.get('foto_3x4'),
        
        # Pagamento e remuneração
        'salario_base': _safe_decimal(request.form.get('salario_base', '')),
        'tipo_pagamento': request.form.get('tipo_pagamento', 'Mensal').strip(),
        'valor_hora': _safe_decimal(request.form.get('valor_hora', '')),
        'valor_hora_extra': _safe_decimal(request.form.get('valor_hora_extra', '')),
        'percentual_hora_extra': _safe_decimal(request.form.get('percentual_hora_extra', '50.00')),
        'vale_transporte': _safe_decimal(request.form.get('vale_transporte', '')),
        'vale_alimentacao': _safe_decimal(request.form.get('vale_alimentacao', '')),
        'outros_beneficios': _safe_decimal(request.form.get('outros_beneficios', '')),
        'desconto_inss': 1 if request.form.get('desconto_inss') == 'on' else 0,
        'desconto_irrf': 1 if request.form.get('desconto_irrf') == 'on' else 0,
        'observacoes_pagamento': request.form.get('observacoes_pagamento', '').strip(),
        
        # 🦺 EPIs - Integração com formulário de funcionários
        'epis': _extrair_dados_epis()
    }

def _extrair_dados_epis(form_data=None):
    """
    Extrai dados de EPIs do formulário.
    
    Args:
        form_data (dict, optional): Dados do formulário. Se None, usa request.form
    
    Returns:
        list: Lista de EPIs com dados validados
    """
    epis = []
    
    # Usar form_data se fornecido, caso contrário usar request.form
    if form_data is None:
        form_data = request.form
    
    # Buscar todos os campos com padrão epis[index][campo]
    for key in form_data.keys():
        if key.startswith('epis[') and '][' in key:
            # Extrair índice e campo (ex: epis[0][epi_nome] -> índice=0, campo=epi_nome)
            try:
                start = key.find('[') + 1
                end = key.find(']')
                index = int(key[start:end])
                
                campo_start = key.find('][') + 2
                campo_end = key.rfind(']')
                campo = key[campo_start:campo_end]
                
                # Garantir que existe EPI no índice
                while len(epis) <= index:
                    epis.append({})
                
                # Adicionar valor ao EPI
                valor = form_data.get(key, '').strip()
                epis[index][campo] = valor
                
            except (ValueError, IndexError):
                logger.warning(f"Campo EPI inválido: {key}")
                continue
    
    # Filtrar EPIs válidos (que têm pelo menos um nome)
    epis_validos = []
    for epi in epis:
        if epi.get('epi_nome'):
            epis_validos.append({
                'id': epi.get('id', ''),  # ID para edição
                'epi_nome': epi.get('epi_nome', '').strip(),
                'epi_ca': epi.get('epi_ca', '').strip(),
                'epi_data_entrega': epi.get('epi_data_entrega', '').strip(),
                'epi_data_validade': epi.get('epi_data_validade', '').strip(),
                'epi_observacoes': epi.get('epi_observacoes', '').strip()
            })
    
    logger.info(f"[EPIs FORMULÁRIO] Extraídos {len(epis_validos)} EPIs válidos")
    return epis_validos

def _validar_dados_funcionario(data, validator):
    """
    Valida dados do funcionário.
    
    Args:
        data (dict): Dados a serem validados
        validator (FormValidator): Instância do validador
    """
    # Campos obrigatórios
    for field in REQUIRED_FIELDS:
        validator.validate_required(data.get(field), field)
    
    # Validações específicas
    validator.validate_cpf(data.get('cpf'))
    validator.validate_email(data.get('email'))
    validator.validate_choice(data.get('sexo'), VALID_SEXO, 'sexo')
    validator.validate_choice(data.get('estado_civil'), VALID_ESTADO_CIVIL, 'estado_civil')
    validator.validate_choice(data.get('tipo_contrato'), VALID_TIPO_CONTRATO, 'tipo_contrato')
    validator.validate_choice(data.get('nivel_acesso'), VALID_NIVEL_ACESSO, 'nivel_acesso')
    validator.validate_choice(data.get('turno'), VALID_TURNO, 'turno')
    validator.validate_choice(data.get('status_cadastro'), VALID_STATUS, 'status_cadastro')
    validator.validate_date(data.get('data_nascimento'), 'data_nascimento')
    validator.validate_date(data.get('data_admissao'), 'data_admissao')
    
    # Validações de pagamento
    _validar_campos_pagamento(data, validator)
    
    # 🛡️ VALIDAÇÃO CRÍTICA DE SEGURANÇA BIOMÉTRICA - CORREÇÃO DEFINITIVA
    current_user = get_current_user()
    is_admin = current_user.get('is_admin', False)
    funcionario_id = data.get('funcionario_id')
    
    # 🔧 CORREÇÃO DEFINITIVA: Distinguir entre dados novos (JSON) e dados existentes (templates binários)
    nova_biometria_dedo1 = data.get('digital_dedo1', '').strip()
    nova_biometria_dedo2 = data.get('digital_dedo2', '').strip()
    
    logger.info(f"[VALIDAÇÃO BIOMETRIA] Funcionário ID: {funcionario_id}")
    logger.info(f"[VALIDAÇÃO BIOMETRIA] Dedo1 recebido: {bool(nova_biometria_dedo1)} - Tamanho: {len(nova_biometria_dedo1) if nova_biometria_dedo1 else 0}")
    logger.info(f"[VALIDAÇÃO BIOMETRIA] Dedo2 recebido: {bool(nova_biometria_dedo2)} - Tamanho: {len(nova_biometria_dedo2) if nova_biometria_dedo2 else 0}")
    logger.info(f"[VALIDAÇÃO BIOMETRIA] É admin: {is_admin}")
    
    # 🚨 CORREÇÃO CRÍTICA: NUNCA validar biometria em edições
    # O problema é que campos hidden vêm preenchidos com dados do banco (templates binários)
    # Validação deve ser APENAS para dados JSON novos do modal de captura
    
    def eh_dado_biometrico_novo(biometria_data):
        """
        Verifica se o dado é uma nova captura biométrica (JSON) ou dado existente (template binário).
        
        Returns:
            bool: True se for nova captura (JSON), False se for dado existente ou vazio
        """
        if not biometria_data or not biometria_data.strip():
            return False
        
        # Tenta parsear como JSON - se conseguir, é nova captura
        try:
            parsed = json.loads(biometria_data)
            if isinstance(parsed, dict) and 'template' in parsed:
                logger.info(f"[VALIDAÇÃO BIOMETRIA] Detectado JSON válido (nova captura): {biometria_data[:50]}...")
                return True
        except (json.JSONDecodeError, TypeError):
            pass
        
        # Se não é JSON válido, é template binário do banco (dados existentes)
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Detectado template binário (dados existentes): {len(biometria_data)} bytes")
        return False
    
    # ✅ VALIDAÇÃO APENAS PARA NOVAS CAPTURAS (JSON)
    if eh_dado_biometrico_novo(nova_biometria_dedo1):
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Validando NOVA captura Dedo1...")
        if not parse_biometria_data(nova_biometria_dedo1):
            validator.add_error('digital_dedo1', "Formato inválido para nova biometria do dedo 1")
            logger.error(f"[VALIDAÇÃO BIOMETRIA] Nova captura Dedo1 inválida: {nova_biometria_dedo1[:50]}...")
    else:
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Dedo1 - Sem nova captura (vazio ou dados existentes)")
    
    if eh_dado_biometrico_novo(nova_biometria_dedo2):
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Validando NOVA captura Dedo2...")
        if not parse_biometria_data(nova_biometria_dedo2):
            validator.add_error('digital_dedo2', "Formato inválido para nova biometria do dedo 2")
            logger.error(f"[VALIDAÇÃO BIOMETRIA] Nova captura Dedo2 inválida: {nova_biometria_dedo2[:50]}...")
    else:
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Dedo2 - Sem nova captura (vazio ou dados existentes)")
    
    # 🚨 PROTEÇÃO CONTRA EDIÇÃO NÃO AUTORIZADA DE BIOMETRIA
    # ✅ CORREÇÃO: Só verifica se há tentativa real de nova captura por usuário não-admin
    if funcionario_id and not is_admin:
        try:
            # Verifica se há biometria existente no banco
            funcionario_atual = FuncionarioQueries.get_by_id(funcionario_id)
            if funcionario_atual:
                biometria_existente_dedo1 = funcionario_atual.get('digital_dedo1')
                biometria_existente_dedo2 = funcionario_atual.get('digital_dedo2')
                
                logger.info(f"[VALIDAÇÃO BIOMETRIA] Biometria existente - Dedo1: {bool(biometria_existente_dedo1)}, Dedo2: {bool(biometria_existente_dedo2)}")
                
                # ✅ PROTEÇÃO: Só bloqueia se há NOVA CAPTURA sendo enviada por usuário não-admin
                if biometria_existente_dedo1 and eh_dado_biometrico_novo(nova_biometria_dedo1):
                    logger.warning(f"[SEGURANÇA] Usuário não-admin {current_user.get('usuario')} tentou capturar nova biometria dedo1 para funcionário {funcionario_id}")
                    validator.add_error('digital_dedo1', "🚫 ACESSO NEGADO: Somente administradores podem alterar biometria existente")
                
                if biometria_existente_dedo2 and eh_dado_biometrico_novo(nova_biometria_dedo2):
                    logger.warning(f"[SEGURANÇA] Usuário não-admin {current_user.get('usuario')} tentou capturar nova biometria dedo2 para funcionário {funcionario_id}")
                    validator.add_error('digital_dedo2', "🚫 ACESSO NEGADO: Somente administradores podem alterar biometria existente")
                        
        except Exception as e:
            logger.error(f"[SEGURANÇA] Erro ao validar permissões de biometria: {e}")
            # Em caso de erro, bloquear apenas se há NOVA CAPTURA sendo enviada
            if not is_admin and (eh_dado_biometrico_novo(nova_biometria_dedo1) or eh_dado_biometrico_novo(nova_biometria_dedo2)):
                validator.add_error('biometria', "Erro de segurança: não foi possível validar permissões para nova captura biométrica")
    else:
        logger.info(f"[VALIDAÇÃO BIOMETRIA] Saltando verificação de permissões - Admin: {is_admin}, Novo cadastro: {not funcionario_id}")
    
    logger.info(f"[VALIDAÇÃO BIOMETRIA] Validação concluída - Erros encontrados: {validator.has_errors()}")

def _processar_dados_funcionario(data, funcionario_id=None):
    """
    Processa e converte dados do funcionário para inserção no banco.
    
    Args:
        data (dict): Dados brutos do formulário
        funcionario_id (int, optional): ID do funcionário para edição
        
    Returns:
        dict: Dados processados
    """
    def eh_dado_biometrico_novo(biometria_data):
        """
        Verifica se o dado é uma nova captura biométrica (JSON) ou dado existente (template binário).
        """
        if not biometria_data or not biometria_data.strip():
            return False
        
        try:
            parsed = json.loads(biometria_data)
            if isinstance(parsed, dict) and 'template' in parsed:
                return True
        except (json.JSONDecodeError, TypeError):
            pass
        
        return False
    
    # 🛡️ PRESERVAÇÃO CRÍTICA DE BIOMETRIA - LÓGICA CORRIGIDA
    digital_dedo1_final = None
    digital_dedo2_final = None
    
    nova_biometria_dedo1 = data.get('digital_dedo1', '').strip()
    nova_biometria_dedo2 = data.get('digital_dedo2', '').strip()
    
    if funcionario_id:
        # É EDIÇÃO - buscar biometria atual primeiro para preservar
        try:
            funcionario_atual = FuncionarioQueries.get_by_id(funcionario_id)
            if funcionario_atual:
                digital_dedo1_existente = funcionario_atual.get('digital_dedo1')
                digital_dedo2_existente = funcionario_atual.get('digital_dedo2')
                
                logger.info(f"[BIOMETRIA] Edição - Dados existentes - Dedo1: {'✅' if digital_dedo1_existente else '❌'}, Dedo2: {'✅' if digital_dedo2_existente else '❌'}")
                
                # DEDO 1: Se há NOVA CAPTURA (JSON), processar; senão, preservar existente
                if eh_dado_biometrico_novo(nova_biometria_dedo1):
                    # Nova captura detectada - processar JSON
                    biometria_data = parse_biometria_data(nova_biometria_dedo1)
                    if biometria_data and biometria_data.get('template'):
                        digital_dedo1_final = biometria_data.get('template')
                        logger.info(f"[BIOMETRIA] NOVA captura Dedo1 processada ✅")
                    else:
                        # Nova captura inválida - preservar existente
                        digital_dedo1_final = digital_dedo1_existente
                        logger.warning(f"[BIOMETRIA] Nova captura Dedo1 inválida - preservando existente")
                else:
                    # Não é nova captura (dados existentes ou vazio) - preservar existente
                    digital_dedo1_final = digital_dedo1_existente
                    logger.info(f"[BIOMETRIA] Dedo1 - Preservando dados existentes")
                
                # DEDO 2: Mesma lógica
                if eh_dado_biometrico_novo(nova_biometria_dedo2):
                    # Nova captura detectada - processar JSON
                    biometria_data = parse_biometria_data(nova_biometria_dedo2)
                    if biometria_data and biometria_data.get('template'):
                        digital_dedo2_final = biometria_data.get('template')
                        logger.info(f"[BIOMETRIA] NOVA captura Dedo2 processada ✅")
                    else:
                        # Nova captura inválida - preservar existente
                        digital_dedo2_final = digital_dedo2_existente
                        logger.warning(f"[BIOMETRIA] Nova captura Dedo2 inválida - preservando existente")
                else:
                    # Não é nova captura (dados existentes ou vazio) - preservar existente
                    digital_dedo2_final = digital_dedo2_existente
                    logger.info(f"[BIOMETRIA] Dedo2 - Preservando dados existentes")
                    
        except Exception as e:
            logger.error(f"[BIOMETRIA] Erro ao buscar biometria existente: {e}")
            # Em caso de erro, tentar processar nova biometria se houver
            if eh_dado_biometrico_novo(nova_biometria_dedo1):
                biometria_data = parse_biometria_data(nova_biometria_dedo1)
                if biometria_data:
                    digital_dedo1_final = biometria_data.get('template')
            
            if eh_dado_biometrico_novo(nova_biometria_dedo2):
                biometria_data = parse_biometria_data(nova_biometria_dedo2)
                if biometria_data:
                    digital_dedo2_final = biometria_data.get('template')
    else:
        # É CADASTRO NOVO - processar apenas se houver dados JSON válidos
        if eh_dado_biometrico_novo(nova_biometria_dedo1):
            biometria_data = parse_biometria_data(nova_biometria_dedo1)
            if biometria_data:
                digital_dedo1_final = biometria_data.get('template')
                logger.info(f"[BIOMETRIA] Novo cadastro - Dedo1 processado ✅")
        
        if eh_dado_biometrico_novo(nova_biometria_dedo2):
            biometria_data = parse_biometria_data(nova_biometria_dedo2)
            if biometria_data:
                digital_dedo2_final = biometria_data.get('template')
                logger.info(f"[BIOMETRIA] Novo cadastro - Dedo2 processado ✅")

    # 🔧 Processa foto preservando a existente em edições
    foto_3x4_result = None
    
    # Se é edição E não há nova foto, preservar foto existente
    if funcionario_id and not (data.get('foto_3x4') and hasattr(data['foto_3x4'], 'filename') and data['foto_3x4'].filename):
        try:
            funcionario_atual = FuncionarioQueries.get_by_id(funcionario_id)
            if funcionario_atual and funcionario_atual.get('foto_3x4'):
                foto_3x4_result = funcionario_atual['foto_3x4']
                logger.info(f"[FOTO] Preservando foto existente")
        except Exception as e:
            logger.error(f"[FOTO] Erro ao buscar foto existente: {e}")
    
    # Se há nova foto sendo enviada, processa upload
    elif data.get('foto_3x4') and hasattr(data['foto_3x4'], 'filename') and data['foto_3x4'].filename:
        logger.info(f"[FOTO] Nova foto detectada: {data['foto_3x4'].filename}")
        file = data['foto_3x4']
        
        if funcionario_id:
            fotos_dir = get_fotos_dir()
            os.makedirs(fotos_dir, exist_ok=True)
            filename = gerar_nome_foto_funcionario(funcionario_id, file.filename)
            
            if filename:
                file_path = os.path.join(fotos_dir, filename)
                try:
                    file.save(file_path)
                    foto_3x4_result = f'fotos_funcionarios/{filename}'
                    logger.info(f"[FOTO] Foto de edição salva: {foto_3x4_result}")
                except Exception as ex:
                    logger.error(f"[FOTO] Erro ao salvar foto de edição: {ex}")

    # 🛡️ LOG FINAL DE SEGURANÇA
    logger.info(f"[BIOMETRIA] Resultado final - Dedo1: {'✅' if digital_dedo1_final else '❌'}, Dedo2: {'✅' if digital_dedo2_final else '❌'}")
    logger.info(f"[FOTO] Resultado final: {foto_3x4_result or 'NENHUMA'}")

    # Mapeia campos para compatibilidade com banco
    return {
        **data,
        'digital_dedo1': digital_dedo1_final,  # 🛡️ PROTEGIDO contra perda
        'digital_dedo2': digital_dedo2_final,  # 🛡️ PROTEGIDO contra perda
        'foto_3x4': foto_3x4_result,
        'endereco_rua': data['endereco_rua'] or None,
        'endereco_bairro': data['endereco_bairro'] or None,
        'endereco_cidade': data['endereco_cidade'] or None,
        'telefone2': data['telefone2'] or None,
        'email': data['email'] or None
    }

def _criar_funcionario(data):
    """
    Cria novo funcionário no banco de dados.
    
    Args:
        data (dict): Dados processados do funcionário
        
    Returns:
        int: ID do funcionário criado ou None em caso de erro
    """
    try:
        # Query de inserção (removidos campos redundantes jornada_entrada, jornada_saida e assinatura)
        sql = """
        INSERT INTO funcionarios (
            nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
            ctps_numero, ctps_serie_uf, pis_pasep,
            endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
            telefone1, telefone2, email,
            cargo, setor_obra, matricula_empresa, data_admissao, tipo_contrato,
            jornada_seg_qui_entrada, jornada_seg_qui_saida, jornada_sex_entrada, jornada_sex_saida, 
            jornada_intervalo_entrada, jornada_intervalo_saida,
            nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, status_cadastro,
            digital_dedo1, digital_dedo2, foto_3x4
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s,
            %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s,
            %s, %s, %s
        )
        """
        
        params = (
            data['nome_completo'], data['cpf'], data['rg'], data['data_nascimento'], 
            data['sexo'], data['estado_civil'], data['nacionalidade'],
            data['ctps_numero'], data['ctps_serie_uf'], data['pis_pasep'],
            data['endereco_rua'], data['endereco_bairro'], data['endereco_cidade'], 
            data['endereco_cep'], data['endereco_estado'],
            data['telefone1'], data['telefone2'], data['email'],
            data['cargo'], data['setor_obra'], data['matricula_empresa'], 
            data['data_admissao'], data['tipo_contrato'],
            data['jornada_seg_qui_entrada'], data['jornada_seg_qui_saida'], 
            data['jornada_sex_entrada'], data['jornada_sex_saida'],
            data['jornada_intervalo_entrada'], data['jornada_intervalo_saida'],
            data['nivel_acesso'], data['turno'], data['tolerancia_ponto'], 
            data['banco_horas'], data['hora_extra'], data['status_cadastro'],
            data['digital_dedo1'], data['digital_dedo2'], data['foto_3x4']
        )
        
        funcionario_id = DatabaseManager.execute_query(sql, params, fetch_all=False)
        
        # 🦺 Processar EPIs após criação do funcionário
        if funcionario_id and data.get('epis'):
            _processar_epis_funcionario(funcionario_id, data['epis'])
        
        # 🔧 CORREÇÃO: Verificar se foto foi processada corretamente
        logger.info(f"[CRIAÇÃO] Funcionário {funcionario_id} criado por {get_current_user()['usuario']}")
        logger.info(f"[CRIAÇÃO] Foto salva: {data.get('foto_3x4', 'NENHUMA')}")
        
        # 🔧 CORREÇÃO: Se há arquivo de foto no formulário mas não foi processado
        # isso acontece quando o ID não estava disponível no momento do processamento
        if funcionario_id and not data.get('foto_3x4'):
            # Buscar arquivo original do formulário
            foto_file = None
            # Recuperar da request atual se ainda disponível
            if hasattr(request, 'files') and request.files.get('foto_3x4'):
                foto_file = request.files.get('foto_3x4')
                if foto_file and foto_file.filename:
                    logger.info(f"[CRIAÇÃO] Processando foto não salva anteriormente: {foto_file.filename}")
                    
                    fotos_dir = get_fotos_dir()
                    os.makedirs(fotos_dir, exist_ok=True)
                    filename = gerar_nome_foto_funcionario(funcionario_id, foto_file.filename)
                    
                    if filename:
                        file_path = os.path.join(fotos_dir, filename)
                        try:
                            foto_file.save(file_path)
                            logger.info(f"[CRIAÇÃO] Foto salva com sucesso: {file_path}")
                            
                            # Atualizar campo foto_3x4 no banco
                            foto_path = f'fotos_funcionarios/{filename}'
                            update_sql = "UPDATE funcionarios SET foto_3x4=%s WHERE id=%s"
                            DatabaseManager.execute_query(update_sql, (foto_path, funcionario_id), fetch_all=False)
                            
                            logger.info(f"[CRIAÇÃO] Campo foto_3x4 atualizado no banco: {foto_path}")
                            
                        except Exception as ex:
                            logger.error(f"[CRIAÇÃO] Erro ao salvar foto pós-criação: {ex}")
                    else:
                        logger.error('[CRIAÇÃO] Nome do arquivo de foto está vazio! Upload abortado.')
        
        return funcionario_id
        
    except Exception as e:
        logger.error(f"Erro ao criar funcionário: {e}")
        return None

def _atualizar_funcionario(funcionario_id, data):
    """
    Atualiza funcionário existente.
    
    Args:
        funcionario_id (int): ID do funcionário
        data (dict): Dados processados
        
    Returns:
        bool: True se sucesso, False caso contrário
    """
    try:
        # Query de atualização (removidos campos redundantes)
        sql = """
        UPDATE funcionarios SET
            nome_completo=%s, cpf=%s, rg=%s, data_nascimento=%s, sexo=%s, estado_civil=%s, nacionalidade=%s,
            ctps_numero=%s, ctps_serie_uf=%s, pis_pasep=%s,
            endereco_rua=%s, endereco_bairro=%s, endereco_cidade=%s, endereco_cep=%s, endereco_estado=%s,
            telefone1=%s, telefone2=%s, email=%s,
            cargo=%s, setor_obra=%s, matricula_empresa=%s, data_admissao=%s, tipo_contrato=%s,
            jornada_seg_qui_entrada=%s, jornada_seg_qui_saida=%s, jornada_sex_entrada=%s, jornada_sex_saida=%s,
            jornada_intervalo_entrada=%s, jornada_intervalo_saida=%s,
            nivel_acesso=%s, turno=%s, tolerancia_ponto=%s, banco_horas=%s, hora_extra=%s, status_cadastro=%s,
            digital_dedo1=%s, digital_dedo2=%s, foto_3x4=%s
        WHERE id=%s
        """
        
        params = (
            data['nome_completo'], data['cpf'], data['rg'], data['data_nascimento'], 
            data['sexo'], data['estado_civil'], data['nacionalidade'],
            data['ctps_numero'], data['ctps_serie_uf'], data['pis_pasep'],
            data['endereco_rua'], data['endereco_bairro'], data['endereco_cidade'], 
            data['endereco_cep'], data['endereco_estado'],
            data['telefone1'], data['telefone2'], data['email'],
            data['cargo'], data['setor_obra'], data['matricula_empresa'], 
            data['data_admissao'], data['tipo_contrato'],
            data['jornada_seg_qui_entrada'], data['jornada_seg_qui_saida'], 
            data['jornada_sex_entrada'], data['jornada_sex_saida'],
            data['jornada_intervalo_entrada'], data['jornada_intervalo_saida'],
            data['nivel_acesso'], data['turno'], data['tolerancia_ponto'], 
            data['banco_horas'], data['hora_extra'], data['status_cadastro'],
            data['digital_dedo1'], data['digital_dedo2'], data['foto_3x4'],
            funcionario_id
        )
        
        DatabaseManager.execute_query(sql, params, fetch_all=False)
        
        # 🦺 Processar EPIs após atualização do funcionário
        if data.get('epis') is not None:  # Usar 'is not None' para permitir lista vazia
            _processar_epis_funcionario(funcionario_id, data['epis'])
        
        logger.info(f"Funcionário {funcionario_id} atualizado por {get_current_user()['usuario']}")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao atualizar funcionário {funcionario_id}: {e}")
        return False

# Registro de filtros personalizados para templates
@funcionarios_bp.app_template_filter('format_cpf')
def format_cpf_filter(cpf):
    return format_cpf(cpf)

@funcionarios_bp.app_template_filter('format_telefone')
def format_telefone_filter(telefone):
    return format_telefone(telefone)

@funcionarios_bp.app_template_filter('format_date')
def format_date_filter(date_obj):
    return format_date(date_obj)

# 🦺 Funções de Integração EPI com Funcionários

def _processar_epis_funcionario(funcionario_id, epis_data):
    """
    Processa EPIs do funcionário (criação/edição).
    
    Args:
        funcionario_id (int): ID do funcionário
        epis_data (list): Lista de EPIs do formulário
    """
    try:
        if not epis_data:
            logger.info(f"[EPIs] Nenhum EPI para processar - Funcionário {funcionario_id}")
            return
        
        logger.info(f"[EPIs] Processando {len(epis_data)} EPIs para funcionário {funcionario_id}")
        
        # Buscar EPIs existentes para comparação (em modo edição)
        epis_existentes = DatabaseManager.execute_query(
            "SELECT id FROM epis WHERE funcionario_id = %s",
            (funcionario_id,)
        )
        ids_existentes = {str(epi['id']) for epi in epis_existentes}
        ids_mantidos = set()
        
        for epi in epis_data:
            epi_id = epi.get('id', '').strip()
            
            if epi_id and epi_id in ids_existentes:
                # Atualizar EPI existente
                _atualizar_epi(epi_id, epi)
                ids_mantidos.add(epi_id)
                logger.info(f"[EPIs] Atualizado EPI {epi_id}: {epi.get('epi_nome')}")
            else:
                # Criar novo EPI
                novo_id = _criar_epi(funcionario_id, epi)
                logger.info(f"[EPIs] Criado novo EPI {novo_id}: {epi.get('epi_nome')}")
        
        # Remover EPIs que não estão mais no formulário (foram removidos)
        ids_para_remover = ids_existentes - ids_mantidos
        for epi_id in ids_para_remover:
            _remover_epi(epi_id)
            logger.info(f"[EPIs] Removido EPI {epi_id}")
        
        logger.info(f"[EPIs] Processamento concluído - Funcionário {funcionario_id}")
        
    except Exception as e:
        logger.error(f"[EPIs] Erro ao processar EPIs do funcionário {funcionario_id}: {e}")
        raise

def _criar_epi(funcionario_id, epi_data):
    """
    Cria novo EPI no banco.
    
    Args:
        funcionario_id (int): ID do funcionário
        epi_data (dict): Dados do EPI
        
    Returns:
        int: ID do EPI criado
    """
    sql = """
    INSERT INTO epis (funcionario_id, epi_nome, epi_ca, epi_data_entrega, epi_data_validade, epi_observacoes)
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    
    params = (
        funcionario_id,
        epi_data.get('epi_nome'),
        epi_data.get('epi_ca') or None,
        epi_data.get('epi_data_entrega') or None,
        epi_data.get('epi_data_validade') or None,
        epi_data.get('epi_observacoes') or None
    )
    
    return DatabaseManager.execute_query(sql, params, fetch_all=False)

def _atualizar_epi(epi_id, epi_data):
    """
    Atualiza EPI existente.
    
    Args:
        epi_id (str): ID do EPI
        epi_data (dict): Novos dados do EPI
    """
    sql = """
    UPDATE epis 
    SET epi_nome = %s, epi_ca = %s, epi_data_entrega = %s, 
        epi_data_validade = %s, epi_observacoes = %s
    WHERE id = %s
    """
    
    params = (
        epi_data.get('epi_nome'),
        epi_data.get('epi_ca') or None,
        epi_data.get('epi_data_entrega') or None,
        epi_data.get('epi_data_validade') or None,
        epi_data.get('epi_observacoes') or None,
        int(epi_id)
    )
    
    DatabaseManager.execute_query(sql, params, fetch_all=False)

def _remover_epi(epi_id):
    """
    Remove EPI do banco.
    
    Args:
        epi_id (str): ID do EPI
    """
    DatabaseManager.execute_query(
        "DELETE FROM epis WHERE id = %s",
        (int(epi_id),),
        fetch_all=False
    )

def _buscar_epis_funcionario(funcionario_id):
    """
    Busca EPIs de um funcionário.
    
    Args:
        funcionario_id (int): ID do funcionário
        
    Returns:
        list: Lista de EPIs
    """
    try:
        epis = DatabaseManager.execute_query(
            "SELECT * FROM epis WHERE funcionario_id = %s ORDER BY epi_data_entrega DESC",
            (funcionario_id,)
        )
        return epis or []
    except Exception as e:
        logger.error(f"Erro ao buscar EPIs do funcionário {funcionario_id}: {e}")
        return [] 