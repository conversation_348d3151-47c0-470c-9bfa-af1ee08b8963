#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DEPLOY DIRETO VIA SSH
====================
"""

import paramiko
import os

def deploy_direto():
    """Deploy direto via SSH"""
    hostname = '************'
    username = 'admin'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🚀 DEPLOY DIRETO - TESTES DO SISTEMA")
        print("✅ Conectado ao servidor!")
        
        # 1. Fazer backup do arquivo atual
        print("💾 Criando backup...")
        backup_cmd = "cp /var/www/controle-ponto/app_ponto_admin.py /var/www/controle-ponto/app_ponto_admin.py.backup.$(date +%Y%m%d_%H%M%S)"
        ssh.exec_command(backup_cmd)
        
        # 2. Upload do arquivo via SFTP
        print("📤 Enviando arquivo atualizado...")
        sftp = ssh.open_sftp()
        sftp.put('var/www/controle-ponto/app_ponto_admin.py', '/var/www/controle-ponto/app_ponto_admin.py')
        
        # 3. Upload do script de testes
        print("📤 Enviando script de testes...")
        sftp.put('var/www/controle-ponto/executar_testes_dados_existentes.py', '/var/www/controle-ponto/executar_testes_dados_existentes.py')
        
        sftp.close()
        
        # 4. Dar permissões de execução
        print("🔧 Configurando permissões...")
        chmod_cmd = "chmod +x /var/www/controle-ponto/executar_testes_dados_existentes.py"
        ssh.exec_command(chmod_cmd)
        
        # 5. Reiniciar serviço
        print("🔄 Reiniciando serviço...")
        restart_cmd = "sudo systemctl restart controle-ponto"
        stdin, stdout, stderr = ssh.exec_command(restart_cmd)
        
        # Aguardar reinicialização
        import time
        time.sleep(5)
        
        # 6. Verificar status
        print("📊 Verificando status...")
        status_cmd = "sudo systemctl status controle-ponto --no-pager -l"
        stdin, stdout, stderr = ssh.exec_command(status_cmd)
        status_output = stdout.read().decode('utf-8')
        
        if "active (running)" in status_output:
            print("✅ Serviço rodando corretamente!")
        else:
            print("⚠️ Possível problema com o serviço:")
            print(status_output)
        
        # 7. Testar endpoint
        print("🧪 Testando endpoint...")
        test_cmd = "curl -s -o /dev/null -w '%{http_code}' http://localhost:5000/ponto-admin/executar-testes-sistema"
        stdin, stdout, stderr = ssh.exec_command(test_cmd)
        http_code = stdout.read().decode('utf-8').strip()
        
        if http_code == "200":
            print("✅ Endpoint funcionando!")
        elif http_code == "302":
            print("🔄 Endpoint redirecionando (normal para login)")
        else:
            print(f"⚠️ Endpoint retornou código: {http_code}")
        
        print("\n🎯 DEPLOY CONCLUÍDO!")
        print("🌐 Acesse: http://************/ponto-admin/executar-testes-sistema")
        print("🔑 Faça login com: admin / @Ric6109")
        
    except Exception as e:
        print(f"❌ Erro durante deploy: {e}")
    finally:
        ssh.close()

if __name__ == "__main__":
    deploy_direto()
