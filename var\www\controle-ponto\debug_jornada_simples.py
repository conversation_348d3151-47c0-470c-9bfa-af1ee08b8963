#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug simples para verificar jornada do funcionário
"""

import sys
sys.path.append('/var/www/controle-ponto')

def debug_jornada():
    try:
        from utils.database import FuncionarioQueries, DatabaseManager
        
        print("🔍 DEBUG JORNADA FUNCIONÁRIO")
        print("=" * 40)
        
        # 1. Buscar um funcionário qualquer
        funcionario_teste = DatabaseManager.execute_query("""
            SELECT f.id, f.nome_completo, f.empresa_id, e.nome_fantasia
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            WHERE f.status_cadastro = 'Ativo'
            LIMIT 1
        """, fetch_one=True)
        
        if not funcionario_teste:
            print("❌ Nenhum funcionário encontrado")
            return
        
        funcionario_id = funcionario_teste['id']
        print(f"📋 Testando funcionário: {funcionario_teste['nome_completo']} (ID: {funcionario_id})")
        print(f"🏢 Empresa: {funcionario_teste['nome_fantasia']}")
        
        # 2. Testar função get_with_epis
        print(f"\n🔍 Chamando get_with_epis({funcionario_id})...")
        funcionario = FuncionarioQueries.get_with_epis(funcionario_id)
        
        if not funcionario:
            print("❌ Função retornou None")
            return
        
        print("✅ Função executou com sucesso")
        
        # 3. Verificar campos de jornada
        print("\n📅 CAMPOS DE JORNADA:")
        campos_jornada = [
            'jornada_seg_qui_entrada',
            'jornada_seg_qui_saida',
            'jornada_sex_entrada', 
            'jornada_sex_saida',
            'jornada_intervalo_entrada',
            'jornada_intervalo_saida'
        ]
        
        tem_jornada = False
        for campo in campos_jornada:
            valor = funcionario.get(campo)
            status = "✅" if valor else "❌"
            print(f"   {status} {campo}: {valor or 'None'}")
            if valor:
                tem_jornada = True
        
        # 4. Verificar campos de horário (fallback)
        print("\n⏰ CAMPOS DE HORÁRIO (fallback):")
        campos_horario = ['entrada_manha', 'saida', 'saida_almoco', 'entrada_tarde']
        tem_horario = False
        for campo in campos_horario:
            valor = funcionario.get(campo)
            status = "✅" if valor else "❌"
            print(f"   {status} {campo}: {valor or 'None'}")
            if valor:
                tem_horario = True
        
        # 5. Verificar EPIs
        epis = funcionario.get('epis', [])
        print(f"\n🦺 EPIs: {len(epis)} encontrados")
        
        # 6. Resultado final
        print(f"\n📊 RESULTADO:")
        if tem_jornada:
            print("   ✅ Dados de jornada encontrados")
        elif tem_horario:
            print("   ⚠️ Usando dados de horário como fallback")
        else:
            print("   ❌ Nenhum dado de jornada/horário encontrado")
        
        # 7. Verificar IDs de jornada/horário
        print(f"\n🔗 RELACIONAMENTOS:")
        print(f"   horario_trabalho_id: {funcionario.get('horario_trabalho_id')}")
        print(f"   jornada_trabalho_id: {funcionario.get('jornada_trabalho_id')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    debug_jornada()
