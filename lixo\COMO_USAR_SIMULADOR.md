# 🔬 Como Usar o Simulador Biométrico

**Localização:** `simulacao/`  
**Desenvolvido para:** Testes e desenvolvimento sem hardware ZK4500

---

## 🚀 **Execução Rápida**

### **Opção 1: Demonstração Automática (Recomendado)**
```bash
# Executa demonstração automática sem interação
python simulacao/demo-rapido.py
```

### **Opção 2: Simulador Completo Interativo**
```bash
# Executa simulador com menu interativo
python simulacao/simulador-biometrico.py

# OU clique duplo em:
simulacao/iniciar-simulador.bat
```

---

## 🎯 **Para que Serve**

- ✅ **Testar integração** do site sem hardware real
- ✅ **Demonstrar funcionamento** para clientes
- ✅ **Desenvolver lógica** de capturas biométricas
- ✅ **Validar templates** e fluxos de dados

---

## 👥 **Usuários Pré-cadastrados**

O simulador vem com 5 usuários de exemplo:
1. <PERSON> - Desenvolvedor
2. <PERSON> - Ana<PERSON>a
3. <PERSON> - <PERSON>
4. <PERSON> - Designer
5. <PERSON> - Suporte

---

## 📁 **Arquivos Gerados**

- **`usuarios_simulados.json`** - Base de dados simulada
- **`capturas_log.txt`** - Log de todas as capturas
- **`README.md`** - Documentação completa

---

## 🔧 **Integração com ZKAgent**

O simulador é **independente** do ZKAgent:

- **Produção:** Site → ZKAgent → Hardware ZK4500
- **Desenvolvimento:** Site → Simulador (dados fake)

---

**📖 Para documentação completa:** `simulacao/README.md` 