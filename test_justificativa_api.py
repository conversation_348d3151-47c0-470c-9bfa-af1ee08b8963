#!/usr/bin/env python3
"""
Teste direto da API de justificativas
"""

import requests
import json
from datetime import datetime

def log(message, level="INFO"):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def test_api():
    log("=== TESTE API JUSTIFICATIVAS ===")
    
    # Configurações
    base_url = "http://10.19.208.31"
    funcionario_id = 35  # ID do funcionário que tem documentos anexados
    data_registro = "2025-07-14"  # Data que tem documentos
    
    # Criar sessão para manter cookies
    session = requests.Session()
    
    try:
        # 1. Fazer login primeiro
        log("1. Fazendo login...")
        login_data = {
            'username': 'admin',
            'password': '@Ric6109'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code == 200:
            log("✅ Login realizado com sucesso")
        else:
            log(f"❌ Erro no login: {login_response.status_code}", "ERROR")
            return
        
        # 2. Testar API de detalhes da justificativa
        log(f"2. Testando API justificativa-detalhes para funcionário {funcionario_id} na data {data_registro}")
        
        api_url = f"{base_url}/ponto-admin/api/justificativa-detalhes/{funcionario_id}/{data_registro}"
        log(f"URL da API: {api_url}")
        
        response = session.get(api_url)
        
        log(f"Status Code: {response.status_code}")
        log(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                log("✅ Resposta da API:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                # Verificar se justificativa foi criada
                if data.get('success') and data.get('justificativa'):
                    log("✅ Justificativa encontrada!")
                    justificativa = data['justificativa']
                    log(f"ID: {justificativa.get('id')}")
                    log(f"Tipo: {justificativa.get('tipo_justificativa')}")
                    log(f"Status: {justificativa.get('status_aprovacao')}")
                    log(f"Motivo: {justificativa.get('motivo')}")
                else:
                    log("❌ Nenhuma justificativa encontrada")
                    
                # Verificar documentos anexados
                if data.get('tem_documentos_anexados'):
                    log("✅ Documentos anexados detectados!")
                    docs = data.get('documentos_anexados', {})
                    log(f"Total de documentos: {docs.get('total', 0)}")
                else:
                    log("❌ Nenhum documento anexado detectado")
                    
            except json.JSONDecodeError as e:
                log(f"❌ Erro ao decodificar JSON: {e}", "ERROR")
                log(f"Resposta raw: {response.text[:500]}")
        else:
            log(f"❌ Erro na API: {response.status_code}", "ERROR")
            log(f"Resposta: {response.text[:500]}")
        
        # 3. Testar API de validação
        log("3. Testando API de validação...")
        
        validation_url = f"{base_url}/ponto-admin/api/validar-decisao-justificativa"
        validation_data = {
            'funcionario_id': funcionario_id,
            'data_registro': data_registro,
            'decisao_rh': 'aprovada',
            'observacoes_rh': 'Teste de aprovação'
        }
        
        validation_response = session.post(
            validation_url,
            json=validation_data,
            headers={'Content-Type': 'application/json'}
        )
        
        log(f"Status Code validação: {validation_response.status_code}")
        
        if validation_response.status_code == 200:
            try:
                validation_data = validation_response.json()
                log("✅ Resposta da validação:")
                print(json.dumps(validation_data, indent=2, ensure_ascii=False))
            except json.JSONDecodeError as e:
                log(f"❌ Erro ao decodificar JSON da validação: {e}", "ERROR")
        else:
            log(f"❌ Erro na validação: {validation_response.status_code}", "ERROR")
            log(f"Resposta: {validation_response.text[:500]}")
            
    except Exception as e:
        log(f"❌ Erro geral: {e}", "ERROR")

if __name__ == "__main__":
    test_api()
