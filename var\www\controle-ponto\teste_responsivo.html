<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Responsivo - Modal Biométrico</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-btn {
            margin: 10px;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .screen-size {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            z-index: 10000;
        }
        .resolution-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .resolution-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .resolution-card h6 {
            margin: 0 0 5px 0;
            color: #007bff;
        }
        .resolution-card small {
            color: #666;
        }
        @media (max-width: 768px) {
            .test-container {
                margin: 10px;
                padding: 20px;
            }
            .test-btn {
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="screen-size" id="screenSize">
        Carregando...
    </div>

    <div class="test-container">
        <h2 class="text-center mb-4">
            <i class="fas fa-mobile-alt text-primary"></i>
            Teste de Responsividade - Modal Biométrico
        </h2>
        
        <div class="text-center">
            <button class="btn btn-primary test-btn" onclick="abrirModalBiometriaUniversal()">
                <i class="fas fa-fingerprint"></i>
                Abrir Modal Biométrico
            </button>
            
            <button class="btn btn-info test-btn" onclick="redimensionarTela('desktop')">
                <i class="fas fa-desktop"></i>
                Simular Desktop
            </button>
            
            <button class="btn btn-warning test-btn" onclick="redimensionarTela('tablet')">
                <i class="fas fa-tablet-alt"></i>
                Simular Tablet
            </button>
            
            <button class="btn btn-success test-btn" onclick="redimensionarTela('mobile')">
                <i class="fas fa-mobile"></i>
                Simular Mobile
            </button>
        </div>
        
        <div class="resolution-info">
            <div class="resolution-card">
                <h6><i class="fas fa-desktop"></i> Desktop</h6>
                <small>1200px+ • Modal centralizado com margens amplas</small>
            </div>
            <div class="resolution-card">
                <h6><i class="fas fa-tablet-alt"></i> Tablet</h6>
                <small>768px - 1199px • Modal reduzido com scroll interno</small>
            </div>
            <div class="resolution-card">
                <h6><i class="fas fa-mobile"></i> Mobile</h6>
                <small>Até 767px • Modal em tela cheia responsivo</small>
            </div>
        </div>
        
        <div class="mt-4">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> Testes de Responsividade:</h6>
                <ol>
                    <li>Abra o modal em diferentes tamanhos de tela</li>
                    <li>Verifique se todo conteúdo é acessível</li>
                    <li>Teste o scroll interno do modal</li>
                    <li>Confirme que botões funcionam em todos os tamanhos</li>
                    <li>Teste fechar modal clicando fora (área escura)</li>
                    <li>Teste fechar modal com tecla ESC</li>
                </ol>
            </div>
        </div>
        
        <div class="mt-4">
            <h6>Status do Teste:</h6>
            <div id="status-teste" style="background: #000; color: #0f0; padding: 10px; font-family: monospace; font-size: 12px; border-radius: 4px; min-height: 60px;">
                ✅ Sistema carregado e pronto para teste<br>
                📱 Redimensione a janela ou use os botões de simulação<br>
                🔍 Monitor de resolução ativo no canto superior direito
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/biometria-zkagent.js"></script>
    
    <script>
        // Monitor de resolução em tempo real
        function atualizarTamanhoTela() {
            const screenSize = document.getElementById('screenSize');
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            let tipo = '';
            if (width >= 1200) tipo = 'Desktop';
            else if (width >= 768) tipo = 'Tablet';
            else tipo = 'Mobile';
            
            screenSize.innerHTML = `
                ${width} x ${height}<br>
                ${tipo}
            `;
        }
        
        // Simulador de diferentes tamanhos de tela
        function redimensionarTela(tipo) {
            const status = document.getElementById('status-teste');
            
            switch(tipo) {
                case 'desktop':
                    window.resizeTo(1400, 900);
                    status.innerHTML += `<br>🖥️ Simulando Desktop (1400x900)`;
                    break;
                case 'tablet':
                    window.resizeTo(1024, 768);
                    status.innerHTML += `<br>📱 Simulando Tablet (1024x768)`;
                    break;
                case 'mobile':
                    window.resizeTo(375, 667);
                    status.innerHTML += `<br>📱 Simulando Mobile (375x667)`;
                    break;
            }
            
            setTimeout(atualizarTamanhoTela, 100);
        }
        
        // Eventos
        window.addEventListener('resize', atualizarTamanhoTela);
        window.addEventListener('load', atualizarTamanhoTela);
        
        // Interceptar logs
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const status = document.getElementById('status-teste');
            const message = args.join(' ');
            status.innerHTML += `<br>[${new Date().toLocaleTimeString()}] ${message}`;
            status.scrollTop = status.scrollHeight;
        };
        
        console.log('🧪 Teste de responsividade iniciado');
        console.log('📊 Monitor de resolução ativo');
        
        // Inicialização
        atualizarTamanhoTela();
    </script>
</body>
</html> 