#!/usr/bin/env python3
"""
Correção Global: Sistema de Herança Dinâmica de Jornadas
Aplica herança de jornadas para TODOS os funcionários de TODAS as empresas
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
from sistema_heranca_jornadas import SistemaHerancaJornadas
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def corrigir_heranca_global():
    """Corrige herança de jornadas para TODOS os funcionários"""
    
    print("🔧 CORREÇÃO GLOBAL: SISTEMA DE HERANÇA DINÂMICA DE JORNADAS")
    print("=" * 80)
    
    db = DatabaseManager()
    
    # 1. Buscar todas as empresas com jornadas padrão
    print("\n📋 PASSO 1: Identificando empresas com jornadas padrão...")
    empresas_com_jornada = db.execute_query("""
        SELECT DISTINCT e.id, e.razao_social, e.nome_fantasia,
               jt.id as jornada_id, jt.nome_jornada,
               jt.seg_qui_entrada, jt.seg_qui_saida,
               jt.sexta_entrada, jt.sexta_saida
        FROM empresas e
        INNER JOIN jornadas_trabalho jt ON e.id = jt.empresa_id
        WHERE e.ativa = 1 AND jt.padrao = 1 AND jt.ativa = 1
        ORDER BY e.id
    """)
    
    if not empresas_com_jornada:
        print("❌ Nenhuma empresa com jornada padrão encontrada!")
        return
    
    print(f"✅ Encontradas {len(empresas_com_jornada)} empresas com jornadas padrão:")
    for empresa in empresas_com_jornada:
        print(f"   • {empresa['razao_social']} (ID: {empresa['id']}) - Jornada: {empresa['nome_jornada']}")
    
    # 2. Para cada empresa, corrigir funcionários
    total_corrigidos = 0
    total_funcionarios = 0
    
    for empresa in empresas_com_jornada:
        print(f"\n📋 PASSO 2: Processando empresa {empresa['razao_social']} (ID: {empresa['id']})")
        
        # Buscar funcionários da empresa
        funcionarios = db.execute_query("""
            SELECT f.id, f.nome_completo, f.jornada_trabalho_id, f.usa_horario_empresa,
                   jt.nome_jornada as jornada_atual,
                   jt.seg_qui_entrada as atual_seg_entrada,
                   jt.seg_qui_saida as atual_seg_saida,
                   jt.sexta_entrada as atual_sex_entrada,
                   jt.sexta_saida as atual_sex_saida
            FROM funcionarios f
            LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
            WHERE f.empresa_id = %s AND f.ativo = 1
        """, (empresa['id'],))
        
        if not funcionarios:
            print(f"   ⚠️ Nenhum funcionário ativo encontrado")
            continue
        
        print(f"   📊 Encontrados {len(funcionarios)} funcionários")
        total_funcionarios += len(funcionarios)
        
        # Verificar e corrigir cada funcionário
        for func in funcionarios:
            precisa_corrigir = False
            motivo = ""
            
            # Verificar se precisa correção
            if func['jornada_trabalho_id'] is None:
                precisa_corrigir = True
                motivo = "SEM JORNADA"
            elif func['jornada_trabalho_id'] != empresa['jornada_id']:
                precisa_corrigir = True
                motivo = "JORNADA DIFERENTE DA EMPRESA"
            elif not func['usa_horario_empresa']:
                precisa_corrigir = True
                motivo = "NÃO USA HERANÇA DA EMPRESA"
            elif (func['atual_seg_entrada'] != empresa['seg_qui_entrada'] or
                  func['atual_seg_saida'] != empresa['seg_qui_saida'] or
                  func['atual_sex_entrada'] != empresa['sexta_entrada'] or
                  func['atual_sex_saida'] != empresa['sexta_saida']):
                precisa_corrigir = True
                motivo = "HORÁRIOS DIFERENTES"
            
            if precisa_corrigir:
                print(f"   🔧 Corrigindo {func['nome_completo']} - {motivo}")
                
                try:
                    # Aplicar correção
                    db.execute_query("""
                        UPDATE funcionarios 
                        SET jornada_trabalho_id = %s,
                            usa_horario_empresa = TRUE,
                            data_atualizacao_jornada = CURRENT_TIMESTAMP,
                            jornada_alterada_por = 1
                        WHERE id = %s
                    """, (empresa['jornada_id'], func['id']), fetch_all=False)
                    
                    # Registrar no histórico
                    db.execute_query("""
                        INSERT INTO historico_funcionario 
                        (funcionario_id, tipo_evento, descricao, usuario_responsavel, data_evento)
                        VALUES (%s, 'JORNADA_CORRIGIDA', %s, 1, CURRENT_TIMESTAMP)
                    """, (func['id'], f"Correção global: {motivo} - Aplicada jornada {empresa['nome_jornada']}"), fetch_all=False)
                    
                    print(f"      ✅ Corrigido com sucesso")
                    total_corrigidos += 1
                    
                except Exception as e:
                    print(f"      ❌ Erro na correção: {e}")
            else:
                print(f"   ✅ {func['nome_completo']} - JÁ CORRETO")
    
    # 3. Verificação final
    print(f"\n📋 PASSO 3: Verificação final...")
    print(f"   📊 Total de funcionários processados: {total_funcionarios}")
    print(f"   🔧 Total de funcionários corrigidos: {total_corrigidos}")
    
    # Executar verificação de consistência
    try:
        relatorio = SistemaHerancaJornadas.verificar_consistencia_jornadas()
        print(f"\n📊 RELATÓRIO DE CONSISTÊNCIA FINAL:")
        print(f"   • Funcionários sem jornada: {relatorio['funcionarios_sem_jornada']}")
        print(f"   • Funcionários com jornada inativa: {relatorio['funcionarios_jornada_inativa']}")
        print(f"   • Empresas sem jornada padrão: {relatorio['empresas_sem_jornada_padrao']}")
        print(f"   • Alocações inconsistentes: {relatorio['alocacoes_jornada_inconsistente']}")
        
        if relatorio['problemas']:
            print(f"   ⚠️ Problemas restantes:")
            for problema in relatorio['problemas']:
                print(f"      • {problema}")
        else:
            print(f"   ✅ SISTEMA TOTALMENTE CONSISTENTE!")
            
    except Exception as e:
        print(f"   ❌ Erro na verificação final: {e}")
    
    print(f"\n🎯 CORREÇÃO GLOBAL CONCLUÍDA!")
    print(f"   ✅ {total_corrigidos} funcionários corrigidos")
    print(f"   ✅ Sistema de herança dinâmica aplicado globalmente")
    
    return total_corrigidos

def main():
    """Função principal"""
    try:
        total_corrigidos = corrigir_heranca_global()
        
        if total_corrigidos > 0:
            print(f"\n🔄 RECOMENDAÇÃO: Reinicie o serviço Flask para garantir que as mudanças sejam refletidas:")
            print(f"   sudo systemctl restart controle-ponto")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante correção global: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
