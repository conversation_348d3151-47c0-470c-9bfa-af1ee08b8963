﻿--
-- Script was generated by Devart dbForge Studio for MySQL, Version **********
-- Product home page: http://www.devart.com/dbforge/mysql/studio
-- Script date 07/07/2025 15:15:50
-- Server version: 8.0.42
--

--
-- Disable foreign keys
--
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;

--
-- Set SQL mode
--
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;

--
-- Set character set the client will use to send SQL statements to the server
--
SET NAMES 'utf8';

DROP DATABASE IF EXISTS controle_ponto;

CREATE DATABASE IF NOT EXISTS controle_ponto
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

--
-- Set default database
--
USE controle_ponto;

--
-- Create table `empresas`
--
CREATE TABLE IF NOT EXISTS empresas (
  id int NOT NULL AUTO_INCREMENT,
  razao_social varchar(200) NOT NULL,
  nome_fantasia varchar(200) DEFAULT NULL,
  cnpj varchar(18) NOT NULL,
  telefone varchar(15) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  ativa tinyint(1) DEFAULT 1,
  empresa_teste tinyint(1) DEFAULT 0,
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  logotipo longblob DEFAULT NULL,
  logotipo_mime_type varchar(100) DEFAULT NULL,
  empresa_principal tinyint(1) DEFAULT 0 COMMENT 'Define se Ã© a empresa principal/proprietÃ¡ria do sistema',
  empresa_matriz_id int DEFAULT NULL COMMENT 'ID da empresa matriz (para filiais)',
  tipo_empresa enum ('principal', 'cliente', 'filial', 'independente') DEFAULT 'independente' COMMENT 'Tipo da empresa no sistema',
  configuracoes_cliente json DEFAULT NULL COMMENT 'ConfiguraÃ§Ãµes especÃ­ficas quando atua como cliente',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 14,
AVG_ROW_LENGTH = 2048,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `cnpj` on table `empresas`
--
ALTER TABLE empresas
ADD UNIQUE INDEX cnpj (cnpj);

--
-- Create index `idx_empresa_matriz` on table `empresas`
--
ALTER TABLE empresas
ADD INDEX idx_empresa_matriz (empresa_matriz_id);

--
-- Create index `idx_empresa_principal` on table `empresas`
--
ALTER TABLE empresas
ADD INDEX idx_empresa_principal (empresa_principal);

--
-- Create index `idx_tipo_empresa` on table `empresas`
--
ALTER TABLE empresas
ADD INDEX idx_tipo_empresa (tipo_empresa);

--
-- Create foreign key
--
ALTER TABLE empresas
ADD CONSTRAINT fk_empresa_matriz FOREIGN KEY (empresa_matriz_id)
REFERENCES empresas (id) ON DELETE SET NULL;

--
-- Create table `jornadas_trabalho`
--
CREATE TABLE IF NOT EXISTS jornadas_trabalho (
  id int NOT NULL AUTO_INCREMENT,
  empresa_id int NOT NULL COMMENT 'ID da empresa',
  nome_jornada varchar(100) NOT NULL COMMENT 'Nome da jornada (ex: Diurno 01 - Profissionais)',
  descricao text DEFAULT NULL COMMENT 'DescriÃ§Ã£o detalhada da jornada',
  codigo_jornada varchar(20) DEFAULT NULL COMMENT 'CÃ³digo interno da jornada',
  tipo_jornada enum ('Diurno', 'Noturno', 'Misto', 'Especial') DEFAULT 'Diurno' COMMENT 'Tipo da jornada',
  categoria_funcionario varchar(100) DEFAULT NULL COMMENT 'Categoria de funcionÃ¡rio (ex: Profissionais, Serventes)',
  seg_qui_entrada time NOT NULL DEFAULT '08:00:00' COMMENT 'Entrada segunda a quinta',
  seg_qui_saida time NOT NULL DEFAULT '17:00:00' COMMENT 'SaÃ­da segunda a quinta',
  sexta_entrada time DEFAULT NULL COMMENT 'Entrada sexta-feira (se diferente)',
  sexta_saida time DEFAULT NULL COMMENT 'SaÃ­da sexta-feira (se diferente)',
  sabado_entrada time DEFAULT NULL COMMENT 'Entrada sÃ¡bado',
  sabado_saida time DEFAULT NULL COMMENT 'SaÃ­da sÃ¡bado',
  domingo_entrada time DEFAULT NULL COMMENT 'Entrada domingo',
  domingo_saida time DEFAULT NULL COMMENT 'SaÃ­da domingo',
  intervalo_obrigatorio tinyint(1) DEFAULT 1 COMMENT 'Se tem intervalo obrigatÃ³rio',
  intervalo_inicio time DEFAULT '12:00:00' COMMENT 'InÃ­cio do intervalo',
  intervalo_fim time DEFAULT '13:00:00' COMMENT 'Fim do intervalo',
  intervalo_duracao_minutos int DEFAULT 60 COMMENT 'DuraÃ§Ã£o do intervalo em minutos',
  intervalo_flexivel tinyint(1) DEFAULT 0 COMMENT 'Se o horÃ¡rio do intervalo Ã© flexÃ­vel',
  intervalo_inicio_permitido time DEFAULT '11:00:00' COMMENT 'InÃ­cio mais cedo permitido para intervalo',
  intervalo_fim_permitido time DEFAULT '14:00:00' COMMENT 'Fim mais tarde permitido para intervalo',
  tolerancia_entrada_minutos int DEFAULT 15 COMMENT 'TolerÃ¢ncia para entrada em minutos',
  tolerancia_saida_minutos int DEFAULT 10 COMMENT 'TolerÃ¢ncia para saÃ­da em minutos',
  tolerancia_intervalo_minutos int DEFAULT 10 COMMENT 'TolerÃ¢ncia para intervalo em minutos',
  permite_banco_horas tinyint(1) DEFAULT 1 COMMENT 'Se permite banco de horas nesta jornada',
  limite_banco_horas_diario_minutos int DEFAULT 120 COMMENT 'Limite diÃ¡rio de banco de horas em minutos',
  permite_hora_extra tinyint(1) DEFAULT 1 COMMENT 'Se permite hora extra',
  limite_hora_extra_diaria_minutos int DEFAULT 120 COMMENT 'Limite diÃ¡rio de hora extra em minutos',
  aplica_segunda tinyint(1) DEFAULT 1 COMMENT 'Se aplica na segunda-feira',
  aplica_terca tinyint(1) DEFAULT 1 COMMENT 'Se aplica na terÃ§a-feira',
  aplica_quarta tinyint(1) DEFAULT 1 COMMENT 'Se aplica na quarta-feira',
  aplica_quinta tinyint(1) DEFAULT 1 COMMENT 'Se aplica na quinta-feira',
  aplica_sexta tinyint(1) DEFAULT 1 COMMENT 'Se aplica na sexta-feira',
  aplica_sabado tinyint(1) DEFAULT 0 COMMENT 'Se aplica no sÃ¡bado',
  aplica_domingo tinyint(1) DEFAULT 0 COMMENT 'Se aplica no domingo',
  ativa tinyint(1) DEFAULT 1 COMMENT 'Se a jornada estÃ¡ ativa',
  padrao tinyint(1) DEFAULT 0 COMMENT 'Se Ã© a jornada padrÃ£o da empresa',
  ordem_exibicao int DEFAULT 0 COMMENT 'Ordem de exibiÃ§Ã£o na lista',
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de cadastro',
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Data da Ãºltima atualizaÃ§Ã£o',
  cadastrado_por varchar(100) DEFAULT NULL COMMENT 'UsuÃ¡rio que cadastrou',
  atualizado_por varchar(100) DEFAULT NULL COMMENT 'UsuÃ¡rio que atualizou',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
COMMENT = 'Jornadas de trabalho por empresa',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_ativa` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD INDEX idx_ativa (ativa);

--
-- Create index `idx_categoria` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD INDEX idx_categoria (categoria_funcionario);

--
-- Create index `idx_empresa` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD INDEX idx_empresa (empresa_id);

--
-- Create index `idx_padrao` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD INDEX idx_padrao (padrao);

--
-- Create index `idx_tipo_jornada` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD INDEX idx_tipo_jornada (tipo_jornada);

--
-- Create index `uk_empresa_padrao` on table `jornadas_trabalho`
--
ALTER TABLE jornadas_trabalho
ADD UNIQUE INDEX uk_empresa_padrao (empresa_id, padrao);

--
-- Create foreign key
--
ALTER TABLE jornadas_trabalho
ADD CONSTRAINT jornadas_trabalho_ibfk_1 FOREIGN KEY (empresa_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create table `horarios_trabalho`
--
CREATE TABLE IF NOT EXISTS horarios_trabalho (
  id int NOT NULL AUTO_INCREMENT,
  empresa_id int NOT NULL,
  nome_horario varchar(100) NOT NULL,
  entrada_manha time NOT NULL DEFAULT '08:00:00',
  saida_almoco time DEFAULT '12:00:00',
  entrada_tarde time DEFAULT '13:00:00',
  saida time NOT NULL DEFAULT '17:00:00',
  tolerancia_minutos int NOT NULL DEFAULT 10,
  ativo tinyint(1) DEFAULT 1,
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 8,
AVG_ROW_LENGTH = 2340,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE horarios_trabalho
ADD CONSTRAINT horarios_trabalho_ibfk_1 FOREIGN KEY (empresa_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create table `empresas_config`
--
CREATE TABLE IF NOT EXISTS empresas_config (
  id int NOT NULL AUTO_INCREMENT,
  empresa_id int NOT NULL,
  jornada_padrao_id int DEFAULT NULL COMMENT 'Jornada padrÃ£o que funcionÃ¡rios herdam automaticamente',
  tolerancia_atraso int DEFAULT 10 COMMENT 'TolerÃ¢ncia para atraso em minutos',
  tolerancia_saida_antecipada int DEFAULT 10 COMMENT 'TolerÃ¢ncia para saÃ­da antecipada em minutos',
  permite_banco_horas tinyint(1) DEFAULT 1 COMMENT 'Se permite acÃºmulo de banco de horas',
  limite_banco_horas_mensal decimal(5, 2) DEFAULT 10.00 COMMENT 'Limite de horas extras por mÃªs',
  configuracoes_extras json DEFAULT NULL COMMENT 'ConfiguraÃ§Ãµes especÃ­ficas em JSON',
  data_criacao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  jornada_segunda_entrada time DEFAULT '08:00:00' COMMENT 'Entrada segunda-feira',
  jornada_segunda_saida_almoco time DEFAULT '12:00:00' COMMENT 'SaÃ­da almoÃ§o segunda-feira',
  jornada_segunda_entrada_almoco time DEFAULT '13:00:00' COMMENT 'Entrada almoÃ§o segunda-feira',
  jornada_segunda_saida time DEFAULT '17:00:00' COMMENT 'SaÃ­da segunda-feira',
  jornada_sexta_entrada time DEFAULT '08:00:00' COMMENT 'Entrada sexta-feira',
  jornada_sexta_saida_almoco time DEFAULT '12:00:00' COMMENT 'SaÃ­da almoÃ§o sexta-feira',
  jornada_sexta_entrada_almoco time DEFAULT '13:00:00' COMMENT 'Entrada almoÃ§o sexta-feira',
  jornada_sexta_saida time DEFAULT '16:30:00' COMMENT 'SaÃ­da sexta-feira',
  intervalo_obrigatorio tinyint(1) DEFAULT 1 COMMENT 'Se o intervalo de almoÃ§o Ã© obrigatÃ³rio',
  tolerancia_empresa_minutos int DEFAULT 15 COMMENT 'TolerÃ¢ncia da empresa em minutos',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 23,
AVG_ROW_LENGTH = 2048,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
COMMENT = 'ConfiguraÃ§Ãµes especÃ­ficas por empresa',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_empresas_config_empresa` on table `empresas_config`
--
ALTER TABLE empresas_config
ADD INDEX idx_empresas_config_empresa (empresa_id);

--
-- Create index `idx_empresas_config_jornada` on table `empresas_config`
--
ALTER TABLE empresas_config
ADD INDEX idx_empresas_config_jornada (jornada_padrao_id);

--
-- Create index `unique_empresa_config` on table `empresas_config`
--
ALTER TABLE empresas_config
ADD UNIQUE INDEX unique_empresa_config (empresa_id);

--
-- Create foreign key
--
ALTER TABLE empresas_config
ADD CONSTRAINT empresas_config_ibfk_1 FOREIGN KEY (empresa_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE empresas_config
ADD CONSTRAINT empresas_config_ibfk_2 FOREIGN KEY (jornada_padrao_id)
REFERENCES horarios_trabalho (id) ON DELETE SET NULL;

--
-- Create table `empresa_clientes`
--
CREATE TABLE IF NOT EXISTS empresa_clientes (
  id int NOT NULL AUTO_INCREMENT,
  empresa_principal_id int NOT NULL COMMENT 'ID da empresa principal',
  empresa_cliente_id int NOT NULL COMMENT 'ID da empresa que atua como cliente',
  nome_contrato varchar(200) DEFAULT NULL COMMENT 'Nome do contrato/projeto',
  codigo_contrato varchar(50) DEFAULT NULL COMMENT 'CÃ³digo Ãºnico do contrato',
  descricao_projeto text DEFAULT NULL COMMENT 'DescriÃ§Ã£o detalhada do projeto',
  data_inicio date NOT NULL COMMENT 'Data de inÃ­cio do contrato',
  data_fim date DEFAULT NULL COMMENT 'Data prevista de fim do contrato',
  valor_contrato decimal(15, 2) DEFAULT NULL COMMENT 'Valor total do contrato',
  status_contrato enum ('ativo', 'pausado', 'finalizado', 'cancelado') DEFAULT 'ativo',
  ativo tinyint(1) DEFAULT 1,
  observacoes text DEFAULT NULL,
  configuracoes_especiais json DEFAULT NULL COMMENT 'ConfiguraÃ§Ãµes especÃ­ficas do cliente',
  created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by int DEFAULT NULL COMMENT 'ID do usuÃ¡rio que criou',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_ativo` on table `empresa_clientes`
--
ALTER TABLE empresa_clientes
ADD INDEX idx_ativo (ativo);

--
-- Create index `idx_data_inicio` on table `empresa_clientes`
--
ALTER TABLE empresa_clientes
ADD INDEX idx_data_inicio (data_inicio);

--
-- Create index `idx_empresa_cliente` on table `empresa_clientes`
--
ALTER TABLE empresa_clientes
ADD INDEX idx_empresa_cliente (empresa_cliente_id);

--
-- Create index `idx_empresa_principal` on table `empresa_clientes`
--
ALTER TABLE empresa_clientes
ADD INDEX idx_empresa_principal (empresa_principal_id);

--
-- Create index `idx_status_contrato` on table `empresa_clientes`
--
ALTER TABLE empresa_clientes
ADD INDEX idx_status_contrato (status_contrato);

--
-- Create index `unique_cliente_principal` on table `empresa_clientes`
--
ALTER TABLE empresa_clientes
ADD UNIQUE INDEX unique_cliente_principal (empresa_principal_id, empresa_cliente_id);

--
-- Create foreign key
--
ALTER TABLE empresa_clientes
ADD CONSTRAINT empresa_clientes_ibfk_1 FOREIGN KEY (empresa_principal_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE empresa_clientes
ADD CONSTRAINT empresa_clientes_ibfk_2 FOREIGN KEY (empresa_cliente_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE empresa_clientes
ADD CONSTRAINT fk_empresa_cliente FOREIGN KEY (empresa_cliente_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE empresa_clientes
ADD CONSTRAINT fk_empresa_principal FOREIGN KEY (empresa_principal_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create table `clientes`
--
CREATE TABLE IF NOT EXISTS clientes (
  id int NOT NULL AUTO_INCREMENT,
  empresa_id int NOT NULL COMMENT 'ID da empresa proprietÃ¡ria do cliente',
  nome_cliente varchar(200) NOT NULL COMMENT 'Nome/RazÃ£o social do cliente',
  nome_fantasia varchar(200) DEFAULT NULL COMMENT 'Nome fantasia do cliente',
  cnpj_cpf varchar(18) DEFAULT NULL COMMENT 'CNPJ/CPF do cliente',
  codigo_cliente varchar(50) DEFAULT NULL COMMENT 'CÃ³digo interno do cliente',
  tipo_cliente enum ('Pessoa FÃ­sica', 'Pessoa JurÃ­dica', 'Obra', 'Projeto') DEFAULT 'Pessoa JurÃ­dica' COMMENT 'Tipo do cliente',
  endereco_completo text DEFAULT NULL COMMENT 'EndereÃ§o completo do cliente',
  telefone varchar(15) DEFAULT NULL COMMENT 'Telefone do cliente',
  email varchar(100) DEFAULT NULL COMMENT 'Email do cliente',
  contato_responsavel varchar(100) DEFAULT NULL COMMENT 'Nome do responsÃ¡vel/contato',
  observacoes text DEFAULT NULL COMMENT 'ObservaÃ§Ãµes sobre o cliente',
  ativo tinyint(1) DEFAULT 1 COMMENT 'Se o cliente estÃ¡ ativo',
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de cadastro',
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Data da Ãºltima atualizaÃ§Ã£o',
  cadastrado_por varchar(100) DEFAULT NULL COMMENT 'UsuÃ¡rio que cadastrou',
  atualizado_por varchar(100) DEFAULT NULL COMMENT 'UsuÃ¡rio que atualizou',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
COMMENT = 'Clientes das empresas para alocaÃ§Ã£o de funcionÃ¡rios',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_ativo` on table `clientes`
--
ALTER TABLE clientes
ADD INDEX idx_ativo (ativo);

--
-- Create index `idx_empresa_id` on table `clientes`
--
ALTER TABLE clientes
ADD INDEX idx_empresa_id (empresa_id);

--
-- Create index `idx_nome_cliente` on table `clientes`
--
ALTER TABLE clientes
ADD INDEX idx_nome_cliente (nome_cliente);

--
-- Create index `idx_tipo_cliente` on table `clientes`
--
ALTER TABLE clientes
ADD INDEX idx_tipo_cliente (tipo_cliente);

--
-- Create foreign key
--
ALTER TABLE clientes
ADD CONSTRAINT clientes_ibfk_1 FOREIGN KEY (empresa_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create table `funcionarios`
--
CREATE TABLE IF NOT EXISTS funcionarios (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  empresa_id int DEFAULT NULL,
  cliente_atual_id int DEFAULT NULL COMMENT 'ID do cliente atual do funcionÃ¡rio',
  horario_trabalho_id int DEFAULT NULL,
  nome_completo varchar(100) NOT NULL,
  cpf varchar(14) NOT NULL,
  rg varchar(20) NOT NULL,
  data_nascimento date NOT NULL,
  sexo enum ('M', 'F', 'Outro') NOT NULL,
  estado_civil enum ('Solteiro', 'Casado', 'Divorciado', 'Viuvo') NOT NULL,
  nacionalidade varchar(50) NOT NULL DEFAULT 'Brasileiro',
  ctps_numero varchar(20) NOT NULL,
  ctps_serie_uf varchar(20) NOT NULL,
  pis_pasep varchar(20) NOT NULL,
  endereco_rua varchar(100) DEFAULT NULL,
  endereco_bairro varchar(50) DEFAULT NULL,
  endereco_cidade varchar(50) DEFAULT NULL,
  endereco_cep varchar(10) NOT NULL,
  endereco_estado varchar(2) NOT NULL,
  telefone1 varchar(15) NOT NULL,
  telefone2 varchar(15) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  cargo varchar(50) NOT NULL,
  setor varchar(50) DEFAULT NULL,
  setor_obra varchar(50) NOT NULL,
  empresa varchar(100) DEFAULT 'Empresa Principal',
  matricula_empresa varchar(20) NOT NULL,
  data_admissao date NOT NULL,
  tipo_contrato enum ('CLT', 'PJ', 'Estagio', 'Temporario') NOT NULL,
  jornada_seg_qui_entrada time DEFAULT NULL,
  jornada_seg_qui_saida time DEFAULT NULL,
  jornada_sex_entrada time DEFAULT NULL,
  jornada_sex_saida time DEFAULT NULL,
  jornada_intervalo_entrada time DEFAULT NULL,
  jornada_intervalo_saida time DEFAULT NULL,
  digital_dedo1 longblob DEFAULT NULL COMMENT 'Template biométrico dedo 1',
  digital_dedo2 longblob DEFAULT NULL COMMENT 'Template biométrico dedo 2',
  foto_3x4 varchar(255) DEFAULT NULL COMMENT 'Caminho para foto 3x4',
  foto_url varchar(255) DEFAULT NULL COMMENT 'URL da foto para templates',
  nivel_acesso enum ('Funcionario', 'Supervisao', 'Gerencia') NOT NULL DEFAULT 'Funcionario',
  turno enum ('Diurno', 'Noturno', 'Misto') NOT NULL DEFAULT 'Diurno',
  tolerancia_ponto int UNSIGNED NOT NULL DEFAULT 5 COMMENT 'Tolerância em minutos',
  banco_horas tinyint(1) DEFAULT 0,
  hora_extra tinyint(1) DEFAULT 0,
  ativo tinyint(1) DEFAULT 1 COMMENT 'Compatibilidade com código existente',
  status_cadastro enum ('Ativo', 'Inativo') NOT NULL DEFAULT 'Ativo',
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  biometria_qualidade_1 tinyint UNSIGNED DEFAULT NULL COMMENT 'Qualidade da captura do dedo 1 (0-100)',
  biometria_qualidade_2 tinyint UNSIGNED DEFAULT NULL COMMENT 'Qualidade da captura do dedo 2 (0-100)',
  biometria_data_cadastro timestamp NULL DEFAULT NULL,
  horario_entrada_manha time GENERATED ALWAYS AS (`jornada_seg_qui_entrada`) VIRTUAL,
  horario_saida_almoco time GENERATED ALWAYS AS (`jornada_intervalo_entrada`) VIRTUAL,
  horario_entrada_tarde time GENERATED ALWAYS AS (`jornada_intervalo_saida`) VIRTUAL,
  horario_saida time GENERATED ALWAYS AS (`jornada_seg_qui_saida`) VIRTUAL,
  salario_base decimal(10, 2) DEFAULT NULL COMMENT 'Salário base do funcionário',
  tipo_pagamento enum ('Mensal', 'Quinzenal', 'Semanal', 'Diario', 'Por_Hora') DEFAULT 'Mensal' COMMENT 'Tipo/frequência de pagamento',
  valor_hora decimal(8, 2) DEFAULT NULL COMMENT 'Valor da hora de trabalho',
  valor_hora_extra decimal(8, 2) DEFAULT NULL COMMENT 'Valor da hora extra',
  percentual_hora_extra decimal(5, 2) DEFAULT 50.00 COMMENT 'Percentual adicional para hora extra',
  vale_transporte decimal(8, 2) DEFAULT NULL COMMENT 'Valor do vale transporte',
  vale_alimentacao decimal(8, 2) DEFAULT NULL COMMENT 'Valor do vale alimentação/refeição',
  outros_beneficios decimal(8, 2) DEFAULT NULL COMMENT 'Outros benefícios monetários',
  desconto_inss tinyint(1) DEFAULT 1 COMMENT 'Aplicar desconto de INSS',
  desconto_irrf tinyint(1) DEFAULT 1 COMMENT 'Aplicar desconto de IRRF',
  observacoes_pagamento text DEFAULT NULL COMMENT 'Observações sobre pagamento',
  data_ultima_alteracao_salario timestamp NULL DEFAULT NULL COMMENT 'Data da última alteração salarial',
  biometria_qualidade int DEFAULT 0,
  ultimo_login timestamp NULL DEFAULT NULL,
  tentativas_biometria int DEFAULT 0,
  status_biometria enum ('ativo', 'inativo', 'bloqueado') DEFAULT 'inativo',
  status enum ('ativo', 'inativo', 'suspenso') DEFAULT 'ativo',
  epi_obrigatorio_json json DEFAULT NULL,
  epi_treinamento_data date DEFAULT NULL,
  epi_responsavel varchar(100) DEFAULT NULL,
  epi_observacoes text DEFAULT NULL,
  epi_termo_assinado tinyint(1) DEFAULT 0,
  inicio_expediente time DEFAULT '07:00:00' COMMENT 'HorÃ¡rio programado de entrada',
  horario_saida_seg_qui time DEFAULT '17:00:00' COMMENT 'HorÃ¡rio de saÃ­da segunda a quinta',
  horario_saida_sexta time DEFAULT '16:30:00' COMMENT 'HorÃ¡rio de saÃ­da na sexta-feira',
  periodo_almoco_inicio time DEFAULT '11:00:00' COMMENT 'InÃ­cio da janela de almoÃ§o',
  periodo_almoco_fim time DEFAULT '14:00:00' COMMENT 'Fim da janela de almoÃ§o',
  duracao_minima_almoco int DEFAULT 60 COMMENT 'DuraÃ§Ã£o mÃ­nima do almoÃ§o em minutos',
  tolerancia_entrada int DEFAULT 15 COMMENT 'TolerÃ¢ncia para entrada em minutos',
  permite_banco_horas_positivo tinyint(1) DEFAULT 0 COMMENT 'Permite acumular horas extras como crÃ©dito',
  data_atualizacao_jornada timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  horas_trabalho_obrigatorias decimal(4, 2) DEFAULT 8.00,
  jornada_trabalho_id int DEFAULT NULL COMMENT 'ID da jornada especÃ­fica do funcionÃ¡rio',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 10,
AVG_ROW_LENGTH = 5461,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Funcionários com dados biométricos e jornada de trabalho',
ROW_FORMAT = DYNAMIC;

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_cpf_format CHECK (regexp_like(`cpf`, _utf8mb4 '^[0-9]{3}\\.[0-9]{3}\\.[0-9]{3}-[0-9]{2}$'));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_email_format CHECK ((`email` IS NULL) OR regexp_like(`email`, _utf8mb4 '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_percentual_range CHECK ((`percentual_hora_extra` IS NULL) OR (`percentual_hora_extra` BETWEEN 0 AND 200));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_qualidade_range_1 CHECK ((`biometria_qualidade_1` IS NULL) OR (`biometria_qualidade_1` BETWEEN 0 AND 100));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_qualidade_range_2 CHECK ((`biometria_qualidade_2` IS NULL) OR (`biometria_qualidade_2` BETWEEN 0 AND 100));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_salario_positivo CHECK ((`salario_base` IS NULL) OR (`salario_base` >= 0));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_tolerancia_range CHECK (`tolerancia_ponto` BETWEEN 0 AND 60);

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_valor_hora_positivo CHECK ((`valor_hora` IS NULL) OR (`valor_hora` >= 0));

--
-- Create index `cpf` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD UNIQUE INDEX cpf (cpf);

--
-- Create index `idx_cliente_atual` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_cliente_atual (cliente_atual_id);

--
-- Create index `idx_cpf` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_cpf (cpf);

--
-- Create index `idx_data_admissao` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_data_admissao (data_admissao);

--
-- Create index `idx_funcionario_jornada` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_funcionario_jornada (jornada_trabalho_id);

--
-- Create index `idx_funcionarios_biometria_status` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_funcionarios_biometria_status (status_biometria);

--
-- Create index `idx_matricula` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_matricula (matricula_empresa);

--
-- Create index `idx_nome` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_nome (nome_completo);

--
-- Create index `idx_salario_base` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_salario_base (salario_base);

--
-- Create index `idx_setor` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_setor (setor_obra);

--
-- Create index `idx_status` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_status (status_cadastro);

--
-- Create index `idx_tipo_pagamento` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_tipo_pagamento (tipo_pagamento);

--
-- Create index `matricula_empresa` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD UNIQUE INDEX matricula_empresa (matricula_empresa);

--
-- Create foreign key
--
ALTER TABLE funcionarios
ADD CONSTRAINT fk_funcionario_jornada FOREIGN KEY (jornada_trabalho_id)
REFERENCES jornadas_trabalho (id) ON DELETE SET NULL;

--
-- Create foreign key
--
ALTER TABLE funcionarios
ADD CONSTRAINT funcionarios_cliente_atual_fk FOREIGN KEY (cliente_atual_id)
REFERENCES clientes (id) ON DELETE SET NULL;

--
-- Create view `vw_funcionarios_biometria`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_funcionarios_biometria
AS
SELECT
  `f`.`id` AS `id`,
  `f`.`nome_completo` AS `nome_completo`,
  `f`.`cpf` AS `cpf`,
  `f`.`setor_obra` AS `setor`,
  `f`.`cargo` AS `cargo`,
  `f`.`matricula_empresa` AS `matricula_empresa`,
  `f`.`empresa` AS `empresa`,
  `f`.`status_cadastro` AS `status_cadastro`,
  (CASE WHEN ((`f`.`digital_dedo1` IS NOT NULL) OR
      (`f`.`digital_dedo2` IS NOT NULL)) THEN 'Configurado' ELSE 'Não Configurado' END) AS `status_biometria`,
  `f`.`biometria_qualidade_1` AS `biometria_qualidade_1`,
  `f`.`biometria_qualidade_2` AS `biometria_qualidade_2`,
  `f`.`foto_url` AS `foto_url`,
  `f`.`data_cadastro` AS `data_cadastro`,
  `f`.`data_atualizacao` AS `data_atualizacao`
FROM `funcionarios` `f`
WHERE (`f`.`status_cadastro` = 'Ativo')
ORDER BY `f`.`nome_completo`;

--
-- Create table `funcionario_cliente_alocacao`
--
CREATE TABLE IF NOT EXISTS funcionario_cliente_alocacao (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL COMMENT 'ID do funcionÃ¡rio',
  cliente_id int NOT NULL COMMENT 'ID do cliente',
  jornada_trabalho_id int DEFAULT NULL COMMENT 'ID da jornada especÃ­fica para este cliente',
  data_inicio date NOT NULL COMMENT 'Data de inÃ­cio da alocaÃ§Ã£o',
  data_fim date DEFAULT NULL COMMENT 'Data de fim da alocaÃ§Ã£o (NULL = indefinido)',
  observacoes text DEFAULT NULL COMMENT 'ObservaÃ§Ãµes sobre a alocaÃ§Ã£o',
  ativo tinyint(1) DEFAULT 1 COMMENT 'Se a alocaÃ§Ã£o estÃ¡ ativa',
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de cadastro da alocaÃ§Ã£o',
  cadastrado_por varchar(100) DEFAULT NULL COMMENT 'UsuÃ¡rio que cadastrou',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
COMMENT = 'AlocaÃ§Ã£o de funcionÃ¡rios para clientes com jornadas especÃ­ficas',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_ativo` on table `funcionario_cliente_alocacao`
--
ALTER TABLE funcionario_cliente_alocacao
ADD INDEX idx_ativo (ativo);

--
-- Create index `idx_cliente_id` on table `funcionario_cliente_alocacao`
--
ALTER TABLE funcionario_cliente_alocacao
ADD INDEX idx_cliente_id (cliente_id);

--
-- Create index `idx_data_inicio` on table `funcionario_cliente_alocacao`
--
ALTER TABLE funcionario_cliente_alocacao
ADD INDEX idx_data_inicio (data_inicio);

--
-- Create index `idx_funcionario_id` on table `funcionario_cliente_alocacao`
--
ALTER TABLE funcionario_cliente_alocacao
ADD INDEX idx_funcionario_id (funcionario_id);

--
-- Create index `idx_jornada_trabalho_id` on table `funcionario_cliente_alocacao`
--
ALTER TABLE funcionario_cliente_alocacao
ADD INDEX idx_jornada_trabalho_id (jornada_trabalho_id);

--
-- Create foreign key
--
ALTER TABLE funcionario_cliente_alocacao
ADD CONSTRAINT funcionario_cliente_alocacao_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE funcionario_cliente_alocacao
ADD CONSTRAINT funcionario_cliente_alocacao_ibfk_2 FOREIGN KEY (cliente_id)
REFERENCES clientes (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE funcionario_cliente_alocacao
ADD CONSTRAINT funcionario_cliente_alocacao_ibfk_3 FOREIGN KEY (jornada_trabalho_id)
REFERENCES jornadas_trabalho (id) ON DELETE SET NULL;

--
-- Create table `funcionario_alocacoes`
--
CREATE TABLE IF NOT EXISTS funcionario_alocacoes (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL COMMENT 'ID do funcionÃ¡rio alocado',
  empresa_cliente_id int NOT NULL COMMENT 'ID da empresa cliente',
  contrato_id int DEFAULT NULL COMMENT 'ID do contrato especÃ­fico (referÃªncia a empresa_clientes)',
  jornada_trabalho_id int NOT NULL COMMENT 'ID da jornada de trabalho herdada',
  cargo_no_cliente varchar(100) DEFAULT NULL COMMENT 'Cargo especÃ­fico no cliente',
  data_inicio date NOT NULL COMMENT 'Data de inÃ­cio da alocaÃ§Ã£o',
  data_fim date DEFAULT NULL COMMENT 'Data de fim da alocaÃ§Ã£o',
  percentual_alocacao decimal(5, 2) DEFAULT 100.00 COMMENT 'Percentual de tempo alocado (0-100%)',
  valor_hora decimal(10, 2) DEFAULT NULL COMMENT 'Valor da hora para este cliente',
  ativo tinyint(1) DEFAULT 1,
  observacoes text DEFAULT NULL,
  configuracoes_alocacao json DEFAULT NULL COMMENT 'ConfiguraÃ§Ãµes especÃ­ficas da alocaÃ§Ã£o',
  created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by int DEFAULT NULL COMMENT 'ID do usuÃ¡rio que criou',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_ativo` on table `funcionario_alocacoes`
--
ALTER TABLE funcionario_alocacoes
ADD INDEX idx_ativo (ativo);

--
-- Create index `idx_contrato` on table `funcionario_alocacoes`
--
ALTER TABLE funcionario_alocacoes
ADD INDEX idx_contrato (contrato_id);

--
-- Create index `idx_data_inicio` on table `funcionario_alocacoes`
--
ALTER TABLE funcionario_alocacoes
ADD INDEX idx_data_inicio (data_inicio);

--
-- Create index `idx_empresa_cliente` on table `funcionario_alocacoes`
--
ALTER TABLE funcionario_alocacoes
ADD INDEX idx_empresa_cliente (empresa_cliente_id);

--
-- Create index `idx_funcionario` on table `funcionario_alocacoes`
--
ALTER TABLE funcionario_alocacoes
ADD INDEX idx_funcionario (funcionario_id);

--
-- Create index `idx_jornada` on table `funcionario_alocacoes`
--
ALTER TABLE funcionario_alocacoes
ADD INDEX idx_jornada (jornada_trabalho_id);

--
-- Create index `idx_periodo` on table `funcionario_alocacoes`
--
ALTER TABLE funcionario_alocacoes
ADD INDEX idx_periodo (data_inicio, data_fim);

--
-- Create foreign key
--
ALTER TABLE funcionario_alocacoes
ADD CONSTRAINT funcionario_alocacoes_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE funcionario_alocacoes
ADD CONSTRAINT funcionario_alocacoes_ibfk_2 FOREIGN KEY (empresa_cliente_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE funcionario_alocacoes
ADD CONSTRAINT funcionario_alocacoes_ibfk_3 FOREIGN KEY (contrato_id)
REFERENCES empresa_clientes (id) ON DELETE SET NULL;

--
-- Create foreign key
--
ALTER TABLE funcionario_alocacoes
ADD CONSTRAINT funcionario_alocacoes_ibfk_4 FOREIGN KEY (jornada_trabalho_id)
REFERENCES jornadas_trabalho (id);

--
-- Create view `vw_funcionarios_alocados`
--
CREATE
DEFINER = 'root'@'localhost'
VIEW vw_funcionarios_alocados
AS
SELECT
  `fa`.`id` AS `alocacao_id`,
  `fa`.`funcionario_id` AS `funcionario_id`,
  `f`.`nome_completo` AS `funcionario_nome`,
  `f`.`cpf` AS `funcionario_cpf`,
  `fa`.`empresa_cliente_id` AS `empresa_cliente_id`,
  `e`.`razao_social` AS `cliente_nome`,
  `fa`.`contrato_id` AS `contrato_id`,
  `ec`.`nome_contrato` AS `nome_contrato`,
  `fa`.`jornada_trabalho_id` AS `jornada_trabalho_id`,
  `jt`.`nome_jornada` AS `jornada_nome`,
  `jt`.`seg_qui_entrada` AS `horario_entrada`,
  `jt`.`seg_qui_saida` AS `horario_saida`,
  `fa`.`cargo_no_cliente` AS `cargo_no_cliente`,
  `fa`.`data_inicio` AS `data_inicio`,
  `fa`.`data_fim` AS `data_fim`,
  `fa`.`percentual_alocacao` AS `percentual_alocacao`,
  `fa`.`valor_hora` AS `valor_hora`,
  `fa`.`ativo` AS `ativo`
FROM ((((`funcionario_alocacoes` `fa`
  JOIN `funcionarios` `f`
    ON ((`fa`.`funcionario_id` = `f`.`id`)))
  JOIN `empresas` `e`
    ON ((`fa`.`empresa_cliente_id` = `e`.`id`)))
  LEFT JOIN `empresa_clientes` `ec`
    ON ((`fa`.`contrato_id` = `ec`.`id`)))
  JOIN `jornadas_trabalho` `jt`
    ON ((`fa`.`jornada_trabalho_id` = `jt`.`id`)))
WHERE (`fa`.`ativo` = TRUE);

--
-- Create view `vw_empresa_principal`
--
CREATE
DEFINER = 'root'@'localhost'
VIEW vw_empresa_principal
AS
SELECT
  `e`.`id` AS `id`,
  `e`.`razao_social` AS `razao_social`,
  `e`.`nome_fantasia` AS `nome_fantasia`,
  `e`.`cnpj` AS `cnpj`,
  `e`.`telefone` AS `telefone`,
  `e`.`email` AS `email`,
  `e`.`ativa` AS `ativa`,
  `e`.`data_cadastro` AS `data_cadastro`,
  COUNT(DISTINCT `ec`.`id`) AS `total_clientes`,
  COUNT(DISTINCT `fa`.`funcionario_id`) AS `total_funcionarios_alocados`,
  COUNT(DISTINCT (CASE WHEN (`ec`.`status_contrato` = 'ativo') THEN `ec`.`id` END)) AS `clientes_ativos`
FROM ((`empresas` `e`
  LEFT JOIN `empresa_clientes` `ec`
    ON ((`e`.`id` = `ec`.`empresa_principal_id`)))
  LEFT JOIN `funcionario_alocacoes` `fa`
    ON (((`ec`.`empresa_cliente_id` = `fa`.`empresa_cliente_id`)
    AND (`fa`.`ativo` = TRUE))))
WHERE (`e`.`empresa_principal` = TRUE)
GROUP BY `e`.`id`,
         `e`.`razao_social`,
         `e`.`nome_fantasia`,
         `e`.`cnpj`,
         `e`.`telefone`,
         `e`.`email`,
         `e`.`ativa`,
         `e`.`data_cadastro`;

--
-- Create view `vw_clientes_detalhados`
--
CREATE
DEFINER = 'root'@'localhost'
VIEW vw_clientes_detalhados
AS
SELECT
  `ec`.`id` AS `contrato_id`,
  `ec`.`empresa_principal_id` AS `empresa_principal_id`,
  `ep`.`razao_social` AS `empresa_principal_nome`,
  `ec`.`empresa_cliente_id` AS `empresa_cliente_id`,
  `ecl`.`razao_social` AS `cliente_razao_social`,
  `ecl`.`nome_fantasia` AS `cliente_nome_fantasia`,
  `ecl`.`cnpj` AS `cliente_cnpj`,
  `ec`.`nome_contrato` AS `nome_contrato`,
  `ec`.`codigo_contrato` AS `codigo_contrato`,
  `ec`.`data_inicio` AS `data_inicio`,
  `ec`.`data_fim` AS `data_fim`,
  `ec`.`valor_contrato` AS `valor_contrato`,
  `ec`.`status_contrato` AS `status_contrato`,
  `ec`.`ativo` AS `ativo`,
  COUNT(DISTINCT `fa`.`funcionario_id`) AS `funcionarios_alocados`,
  COUNT(DISTINCT `jt`.`id`) AS `jornadas_disponiveis`
FROM ((((`empresa_clientes` `ec`
  JOIN `empresas` `ep`
    ON ((`ec`.`empresa_principal_id` = `ep`.`id`)))
  JOIN `empresas` `ecl`
    ON ((`ec`.`empresa_cliente_id` = `ecl`.`id`)))
  LEFT JOIN `funcionario_alocacoes` `fa`
    ON (((`ec`.`empresa_cliente_id` = `fa`.`empresa_cliente_id`)
    AND (`fa`.`ativo` = TRUE))))
  LEFT JOIN `jornadas_trabalho` `jt`
    ON (((`ecl`.`id` = `jt`.`empresa_id`)
    AND (`jt`.`ativa` = TRUE))))
GROUP BY `ec`.`id`,
         `ec`.`empresa_principal_id`,
         `ep`.`razao_social`,
         `ec`.`empresa_cliente_id`,
         `ecl`.`razao_social`,
         `ecl`.`nome_fantasia`,
         `ecl`.`cnpj`,
         `ec`.`nome_contrato`,
         `ec`.`codigo_contrato`,
         `ec`.`data_inicio`,
         `ec`.`data_fim`,
         `ec`.`valor_contrato`,
         `ec`.`status_contrato`,
         `ec`.`ativo`;

--
-- Create table `historico_alocacoes`
--
CREATE TABLE IF NOT EXISTS historico_alocacoes (
  id int NOT NULL AUTO_INCREMENT,
  alocacao_id int NOT NULL COMMENT 'ID da alocaÃ§Ã£o original',
  funcionario_id int UNSIGNED NOT NULL,
  empresa_cliente_id int NOT NULL,
  acao enum ('criada', 'modificada', 'finalizada', 'cancelada') NOT NULL,
  dados_anteriores json DEFAULT NULL COMMENT 'Dados antes da modificaÃ§Ã£o',
  dados_novos json DEFAULT NULL COMMENT 'Dados apÃ³s a modificaÃ§Ã£o',
  motivo text DEFAULT NULL COMMENT 'Motivo da alteraÃ§Ã£o',
  created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  created_by int DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_acao` on table `historico_alocacoes`
--
ALTER TABLE historico_alocacoes
ADD INDEX idx_acao (acao);

--
-- Create index `idx_alocacao` on table `historico_alocacoes`
--
ALTER TABLE historico_alocacoes
ADD INDEX idx_alocacao (alocacao_id);

--
-- Create index `idx_data` on table `historico_alocacoes`
--
ALTER TABLE historico_alocacoes
ADD INDEX idx_data (created_at);

--
-- Create index `idx_funcionario` on table `historico_alocacoes`
--
ALTER TABLE historico_alocacoes
ADD INDEX idx_funcionario (funcionario_id);

--
-- Create foreign key
--
ALTER TABLE historico_alocacoes
ADD CONSTRAINT historico_alocacoes_ibfk_1 FOREIGN KEY (alocacao_id)
REFERENCES funcionario_alocacoes (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE historico_alocacoes
ADD CONSTRAINT historico_alocacoes_ibfk_2 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE historico_alocacoes
ADD CONSTRAINT historico_alocacoes_ibfk_3 FOREIGN KEY (empresa_cliente_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create table `epis`
--
CREATE TABLE IF NOT EXISTS epis (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL,
  epi_nome varchar(255) NOT NULL,
  epi_ca varchar(50) DEFAULT NULL,
  epi_data_entrega date DEFAULT NULL,
  epi_data_validade date DEFAULT NULL,
  epi_observacoes text DEFAULT NULL,
  status_epi enum ('entregue', 'vencido', 'devolvido') DEFAULT 'entregue',
  criado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  atualizado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 6,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Controle de EPIs dos funcionários',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_data_validade` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_data_validade (epi_data_validade);

--
-- Create index `idx_funcionario_epi` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_funcionario_epi (funcionario_id);

--
-- Create index `idx_status_epi` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_status_epi (status_epi);

--
-- Create foreign key
--
ALTER TABLE epis
ADD CONSTRAINT epis_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create table `banco_horas`
--
CREATE TABLE IF NOT EXISTS banco_horas (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL,
  data_referencia date NOT NULL,
  atraso_entrada_minutos int DEFAULT 0,
  excesso_almoco_minutos int DEFAULT 0,
  saida_antecipada_minutos int DEFAULT 0,
  horas_extras_minutos int DEFAULT 0,
  saldo_devedor_minutos int DEFAULT 0,
  saldo_credor_minutos int DEFAULT 0,
  saldo_liquido_minutos int DEFAULT 0,
  status_dia enum ('completo', 'incompleto', 'ausente_parcial', 'ausente_total', 'feriado', 'folga') DEFAULT 'incompleto',
  observacoes text DEFAULT NULL,
  justificativa text DEFAULT NULL,
  aprovado_por int DEFAULT NULL,
  data_aprovacao timestamp NULL DEFAULT NULL,
  criado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  atualizado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 4,
AVG_ROW_LENGTH = 5461,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_data_referencia` on table `banco_horas`
--
ALTER TABLE banco_horas
ADD INDEX idx_data_referencia (data_referencia);

--
-- Create index `idx_funcionario` on table `banco_horas`
--
ALTER TABLE banco_horas
ADD INDEX idx_funcionario (funcionario_id);

--
-- Create index `idx_status` on table `banco_horas`
--
ALTER TABLE banco_horas
ADD INDEX idx_status (status_dia);

--
-- Create index `uk_funcionario_data` on table `banco_horas`
--
ALTER TABLE banco_horas
ADD UNIQUE INDEX uk_funcionario_data (funcionario_id, data_referencia);

--
-- Create foreign key
--
ALTER TABLE banco_horas
ADD CONSTRAINT banco_horas_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE;

--
-- Create table `usuarios`
--
CREATE TABLE IF NOT EXISTS usuarios (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  usuario varchar(50) NOT NULL,
  nome_completo varchar(100) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  ativo tinyint(1) DEFAULT 1,
  ultimo_login timestamp NULL DEFAULT NULL,
  senha varchar(255) NOT NULL,
  nivel_acesso enum ('usuario', 'admin') DEFAULT 'usuario',
  data_criacao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  data_ultimo_acesso timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 10,
AVG_ROW_LENGTH = 2730,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Usuários do sistema de controle de ponto',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_usuario` on table `usuarios`
--
ALTER TABLE usuarios
ADD INDEX idx_usuario (usuario);

--
-- Create index `usuario` on table `usuarios`
--
ALTER TABLE usuarios
ADD UNIQUE INDEX usuario (usuario);

--
-- Create table `registros_ponto`
--
CREATE TABLE IF NOT EXISTS registros_ponto (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL,
  tipo_registro enum ('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida') NOT NULL,
  data_hora timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  data_registro date GENERATED ALWAYS AS (CAST(`data_hora` AS date)) STORED COMMENT 'Data extraída para índices',
  metodo_registro enum ('biometrico', 'manual') NOT NULL,
  criado_por int UNSIGNED DEFAULT NULL COMMENT 'ID do usuário que fez registro manual',
  template_biometrico longblob DEFAULT NULL COMMENT 'Template biométrico usado',
  digital_capturada longblob DEFAULT NULL COMMENT 'Compatibilidade com código antigo',
  qualidade_biometria tinyint UNSIGNED DEFAULT NULL COMMENT 'Qualidade 0-100',
  observacoes text DEFAULT NULL,
  ip_origem varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  sincronizado tinyint(1) DEFAULT 0 COMMENT 'Compatibilidade',
  criado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  atualizado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  biometria_verificada tinyint(1) DEFAULT 0,
  device_hash varchar(64) DEFAULT NULL,
  security_score int DEFAULT 100,
  ip_address varchar(45) DEFAULT NULL,
  status_pontualidade enum ('Pontual', 'Atrasado') DEFAULT NULL COMMENT 'Status de pontualidade do registro',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 4,
AVG_ROW_LENGTH = 5461,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Registros de ponto biométrico e manual - RLPONTO-WEB v1.2',
ROW_FORMAT = DYNAMIC;

--
-- Create check constraint
--
ALTER TABLE registros_ponto
ADD CONSTRAINT chk_qualidade_biometria CHECK ((`qualidade_biometria` IS NULL) OR (`qualidade_biometria` BETWEEN 0 AND 100));

--
-- Create index `idx_criado_por` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_criado_por (criado_por);

--
-- Create index `idx_data_hora` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_data_hora (data_hora);

--
-- Create index `idx_data_registro` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_data_registro (data_registro);

--
-- Create index `idx_funcionario_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_funcionario_data (funcionario_id, data_hora);

--
-- Create index `idx_funcionario_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_funcionario_tipo_data (funcionario_id, tipo_registro, data_hora);

--
-- Create index `idx_metodo` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_metodo (metodo_registro);

--
-- Create index `idx_registros_ponto_biometria` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_registros_ponto_biometria (biometria_verificada);

--
-- Create index `idx_registros_ponto_data_funcionario` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_registros_ponto_data_funcionario (data_hora, funcionario_id);

--
-- Create index `idx_registros_ponto_metodo` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_registros_ponto_metodo (metodo_registro);

--
-- Create index `idx_status_pontualidade` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_status_pontualidade (status_pontualidade);

--
-- Create index `idx_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_tipo_data (tipo_registro, data_hora);

--
-- Create index `uk_funcionario_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD UNIQUE INDEX uk_funcionario_tipo_data (funcionario_id, tipo_registro, data_registro);

--
-- Create foreign key
--
ALTER TABLE registros_ponto
ADD CONSTRAINT registros_ponto_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create foreign key
--
ALTER TABLE registros_ponto
ADD CONSTRAINT registros_ponto_ibfk_2 FOREIGN KEY (criado_por)
REFERENCES usuarios (id) ON DELETE SET NULL ON UPDATE CASCADE;

DELIMITER $$

--
-- Create function `VerificarIntegridadeBiometrica`
--
CREATE
DEFINER = 'cavalcrod'@'%'
FUNCTION IF NOT EXISTS VerificarIntegridadeBiometrica (p_funcionario_id int UNSIGNED)
RETURNS json
DETERMINISTIC
READS SQL DATA
BEGIN
  DECLARE resultado json;
  DECLARE tem_dedo1 boolean DEFAULT FALSE;
  DECLARE tem_dedo2 boolean DEFAULT FALSE;
  DECLARE total_registros int DEFAULT 0;
  DECLARE nome_funcionario varchar(100) DEFAULT '';

  -- Verificar se funcionário existe
  SELECT
    (digital_dedo1 IS NOT NULL) AS dedo1,
    (digital_dedo2 IS NOT NULL) AS dedo2,
    nome_completo INTO tem_dedo1, tem_dedo2, nome_funcionario
  FROM funcionarios
  WHERE id = p_funcionario_id
  AND status_cadastro = 'Ativo'
  LIMIT 1;

  -- Se funcionário não existe, retornar erro
  IF nome_funcionario = '' THEN
    SET resultado = JSON_OBJECT('erro', TRUE,
    'mensagem', 'Funcionário não encontrado ou inativo');
    RETURN resultado;
  END IF;

  SELECT
    COUNT(*) INTO total_registros
  FROM registros_ponto
  WHERE funcionario_id = p_funcionario_id;

  SET resultado = JSON_OBJECT('funcionario_id', p_funcionario_id,
  'nome_funcionario', nome_funcionario,
  'tem_biometria_dedo1', tem_dedo1,
  'tem_biometria_dedo2', tem_dedo2,
  'total_registros_ponto', total_registros,
  'status_biometrico', CASE WHEN tem_dedo1 OR
      tem_dedo2 THEN 'configurado' ELSE 'nao_configurado' END,
  'erro', FALSE);

  RETURN resultado;
END
$$

DELIMITER ;

--
-- Create view `vw_relatorio_pontos`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_relatorio_pontos
AS
SELECT
  COALESCE(`rp`.`id`, CONCAT('AUS_', `f`.`id`, '_', CURDATE())) AS `id`,
  `f`.`id` AS `funcionario_id`,
  `f`.`nome_completo` AS `nome_completo`,
  `f`.`matricula_empresa` AS `matricula_empresa`,
  `f`.`cpf` AS `cpf`,
  `rp`.`data_hora` AS `data_hora`,
  COALESCE(CAST(`rp`.`data_hora` AS date), CURDATE()) AS `data_registro`,
  COALESCE(CAST(`rp`.`data_hora` AS time), '00:00:00') AS `hora_registro`,
  COALESCE(`rp`.`tipo_registro`, 'ausente') AS `tipo_registro`,
  (CASE WHEN (`rp`.`tipo_registro` IS NULL) THEN 'Ausente' WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN 'Entrada ManhÃ£' WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN 'SaÃ­da AlmoÃ§o' WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN 'Entrada Tarde' WHEN (`rp`.`tipo_registro` = 'saida') THEN 'SaÃ­da' ELSE `rp`.`tipo_registro` END) AS `tipo_descricao`,
  COALESCE(`rp`.`metodo_registro`, 'nao_registrado') AS `metodo_registro`,
  (CASE WHEN (`rp`.`metodo_registro` IS NULL) THEN 'NÃ£o Registrado' WHEN (`rp`.`metodo_registro` = 'biometrico') THEN 'BiomÃ©trico' WHEN (`rp`.`metodo_registro` = 'manual') THEN 'Manual' ELSE `rp`.`metodo_registro` END) AS `metodo_descricao`,
  COALESCE(`f`.`setor`, `f`.`setor_obra`, 'NÃ£o informado') AS `setor`,
  `f`.`cargo` AS `cargo`,
  COALESCE(`f`.`empresa`, 'NÃ£o informado') AS `empresa`,
  `rp`.`qualidade_biometria` AS `qualidade_biometria`,
  (CASE WHEN (`rp`.`id` IS NULL) THEN 'FuncionÃ¡rio ausente - sem registros de ponto' ELSE `rp`.`observacoes` END) AS `observacoes`,
  `rp`.`ip_origem` AS `ip_origem`,
  COALESCE(`rp`.`criado_em`, `f`.`data_cadastro`) AS `criado_em`,
  `u`.`usuario` AS `criado_por_usuario`,
  (CASE WHEN (`rp`.`id` IS NULL) THEN 'Ausente' WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > '08:10:00')) THEN 'Atraso' WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > '13:10:00')) THEN 'Atraso' ELSE 'Pontual' END) AS `status_pontualidade`,
  (CASE WHEN (`rp`.`id` IS NULL) THEN 1 ELSE 0 END) AS `is_ausente`
FROM ((`funcionarios` `f`
  LEFT JOIN `registros_ponto` `rp`
    ON ((`f`.`id` = `rp`.`funcionario_id`)))
  LEFT JOIN `usuarios` `u`
    ON ((`rp`.`criado_por` = `u`.`id`)))
WHERE (`f`.`status_cadastro` = 'Ativo')
ORDER BY `f`.`nome_completo`, `rp`.`data_hora` DESC;

--
-- Create view `vw_horas_trabalhadas`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_horas_trabalhadas
AS
WITH `registros_diarios`
AS
(SELECT
    `f`.`id` AS `funcionario_id`,
    `f`.`nome_completo` AS `nome_completo`,
    `f`.`setor` AS `setor`,
    `f`.`cargo` AS `cargo`,
    CAST(`rp`.`data_hora` AS date) AS `data_registro`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN `rp`.`data_hora` END)) AS `entrada_manha`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN `rp`.`data_hora` END)) AS `saida_almoco`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN `rp`.`data_hora` END)) AS `entrada_tarde`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN `rp`.`data_hora` END)) AS `saida`
  FROM (`registros_ponto` `rp`
    JOIN `funcionarios` `f`
      ON ((`rp`.`funcionario_id` = `f`.`id`)))
  WHERE (`f`.`ativo` = 1)
  GROUP BY `f`.`id`,
           `f`.`nome_completo`,
           `f`.`setor`,
           `f`.`cargo`,
           CAST(`rp`.`data_hora` AS date))
SELECT
  `rd`.`funcionario_id` AS `funcionario_id`,
  `rd`.`nome_completo` AS `nome_completo`,
  `rd`.`setor` AS `setor`,
  `rd`.`cargo` AS `cargo`,
  `rd`.`data_registro` AS `data_registro`,
  `rd`.`entrada_manha` AS `entrada_manha`,
  `rd`.`saida_almoco` AS `saida_almoco`,
  `rd`.`entrada_tarde` AS `entrada_tarde`,
  `rd`.`saida` AS `saida`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NOT NULL) AND
      (`rd`.`saida_almoco` IS NOT NULL)) THEN TIMEDIFF(`rd`.`saida_almoco`, `rd`.`entrada_manha`) ELSE NULL END) AS `periodo_manha`,
  (CASE WHEN ((`rd`.`entrada_tarde` IS NOT NULL) AND
      (`rd`.`saida` IS NOT NULL)) THEN TIMEDIFF(`rd`.`saida`, `rd`.`entrada_tarde`) ELSE NULL END) AS `periodo_tarde`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NOT NULL) AND
      (`rd`.`saida_almoco` IS NOT NULL) AND
      (`rd`.`entrada_tarde` IS NOT NULL) AND
      (`rd`.`saida` IS NOT NULL)) THEN ADDTIME(TIMEDIFF(`rd`.`saida_almoco`, `rd`.`entrada_manha`), TIMEDIFF(`rd`.`saida`, `rd`.`entrada_tarde`)) ELSE NULL END) AS `total_horas_trabalhadas`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NULL) OR
      (`rd`.`saida_almoco` IS NULL) OR
      (`rd`.`entrada_tarde` IS NULL) OR
      (`rd`.`saida` IS NULL)) THEN 'Incompleto' ELSE 'Completo' END) AS `status_dia`
FROM `registros_diarios` `rd`;

--
-- Create view `vw_estatisticas_sistema`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_sistema
AS
SELECT
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (`funcionarios`.`status_cadastro` = 'Ativo')) AS `funcionarios_ativos`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (`funcionarios`.`status_cadastro` = 'Inativo')) AS `funcionarios_inativos`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (((`funcionarios`.`digital_dedo1` IS NOT NULL)
    OR (`funcionarios`.`digital_dedo2` IS NOT NULL))
    AND (`funcionarios`.`status_cadastro` = 'Ativo'))) AS `funcionarios_com_biometria`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE ((`funcionarios`.`digital_dedo1` IS NULL)
    AND (`funcionarios`.`digital_dedo2` IS NULL)
    AND (`funcionarios`.`status_cadastro` = 'Ativo'))) AS `funcionarios_sem_biometria`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE (CAST(`registros_ponto`.`data_hora` AS date) = CURDATE())) AS `registros_hoje`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE (YEARWEEK(`registros_ponto`.`data_hora`, 1) = YEARWEEK(NOW(), 1))) AS `registros_semana_atual`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE ((YEAR(`registros_ponto`.`data_hora`) = YEAR(NOW()))
    AND (MONTH(`registros_ponto`.`data_hora`) = MONTH(NOW())))) AS `registros_mes_atual`;

--
-- Create view `vw_estatisticas_pontos`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_pontos
AS
SELECT
  CAST(`rp`.`data_hora` AS date) AS `data_registro`,
  COUNT(0) AS `total_registros`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'biometrico') THEN 1 ELSE 0 END)) AS `registros_biometricos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'manual') THEN 1 ELSE 0 END)) AS `registros_manuais`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN 1 ELSE 0 END)) AS `entradas_manha`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN 1 ELSE 0 END)) AS `saidas_almoco`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN 1 ELSE 0 END)) AS `entradas_tarde`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN 1 ELSE 0 END)) AS `saidas`,
  SUM((CASE WHEN (((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > '08:10:00')) OR
      ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > '13:10:00'))) THEN 1 ELSE 0 END)) AS `atrasos`,
  COUNT(DISTINCT `rp`.`funcionario_id`) AS `funcionarios_registraram`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
WHERE (`f`.`ativo` = 1)
GROUP BY CAST(`rp`.`data_hora` AS date)
ORDER BY `data_registro` DESC;

--
-- Create view `vw_estatisticas_ponto_setor`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_ponto_setor
AS
SELECT
  `f`.`setor` AS `setor`,
  CAST(`rp`.`data_hora` AS date) AS `data_registro`,
  COUNT(0) AS `total_registros`,
  COUNT(DISTINCT `f`.`id`) AS `funcionarios_unicos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'biometrico') THEN 1 ELSE 0 END)) AS `registros_biometricos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'manual') THEN 1 ELSE 0 END)) AS `registros_manuais`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN 1 ELSE 0 END)) AS `entradas_manha`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN 1 ELSE 0 END)) AS `saidas_almoco`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN 1 ELSE 0 END)) AS `entradas_tarde`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN 1 ELSE 0 END)) AS `saidas`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > ADDTIME(`f`.`jornada_seg_qui_entrada`, SEC_TO_TIME((`f`.`tolerancia_ponto` * 60))))) THEN 1 ELSE 0 END)) AS `atrasos_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > ADDTIME(`f`.`jornada_intervalo_saida`, SEC_TO_TIME((`f`.`tolerancia_ponto` * 60))))) THEN 1 ELSE 0 END)) AS `atrasos_tarde`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
GROUP BY `f`.`setor`,
         CAST(`rp`.`data_hora` AS date);

--
-- Create view `vw_analise_pontualidade`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_analise_pontualidade
AS
SELECT
  `f`.`id` AS `funcionario_id`,
  `f`.`nome_completo` AS `nome_completo`,
  `f`.`setor` AS `setor`,
  `f`.`cargo` AS `cargo`,
  DATE_FORMAT(`rp`.`data_hora`, '%Y-%m') AS `mes_ano`,
  COUNT(DISTINCT CAST(`rp`.`data_hora` AS date)) AS `dias_trabalhados`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) <= '08:10:00')) THEN 1 ELSE 0 END)) AS `entradas_pontuais_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > '08:10:00')) THEN 1 ELSE 0 END)) AS `atrasos_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) <= '13:10:00')) THEN 1 ELSE 0 END)) AS `entradas_pontuais_tarde`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > '13:10:00')) THEN 1 ELSE 0 END)) AS `atrasos_tarde`,
  ROUND(((SUM((CASE WHEN ((`rp`.`tipo_registro` IN ('entrada_manha', 'entrada_tarde')) AND
      (((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) <= '08:10:00')) OR
      ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) <= '13:10:00')))) THEN 1 ELSE 0 END)) * 100.0) / NULLIF(SUM((CASE WHEN (`rp`.`tipo_registro` IN ('entrada_manha', 'entrada_tarde')) THEN 1 ELSE 0 END)), 0)), 2) AS `percentual_pontualidade`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
WHERE (`f`.`ativo` = 1)
GROUP BY `f`.`id`,
         `f`.`nome_completo`,
         `f`.`setor`,
         `f`.`cargo`,
         DATE_FORMAT(`rp`.`data_hora`, '%Y-%m');

--
-- Create table `permissoes`
--
CREATE TABLE IF NOT EXISTS permissoes (
  usuario_id int UNSIGNED NOT NULL,
  nivel_acesso enum ('admin', 'usuario') DEFAULT 'usuario',
  data_atribuicao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (usuario_id)
)
ENGINE = INNODB,
AVG_ROW_LENGTH = 16384,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Permissões e níveis de acesso dos usuários',
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE permissoes
ADD CONSTRAINT permissoes_ibfk_1 FOREIGN KEY (usuario_id)
REFERENCES usuarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create table `logs_sistema`
--
CREATE TABLE IF NOT EXISTS logs_sistema (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  usuario_id int UNSIGNED DEFAULT NULL,
  acao varchar(100) NOT NULL,
  tabela_afetada varchar(50) DEFAULT NULL,
  registro_id int UNSIGNED DEFAULT NULL,
  detalhes json DEFAULT NULL,
  ip_origem varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  data_hora timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 93,
AVG_ROW_LENGTH = 1424,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Logs de auditoria para segurança do sistema',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_acao_data` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_acao_data (acao, data_hora);

--
-- Create index `idx_data_hora` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_data_hora (data_hora);

--
-- Create index `idx_usuario_data` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_usuario_data (usuario_id, data_hora);

--
-- Create foreign key
--
ALTER TABLE logs_sistema
ADD CONSTRAINT logs_sistema_ibfk_1 FOREIGN KEY (usuario_id)
REFERENCES usuarios (id) ON DELETE SET NULL ON UPDATE CASCADE;

DELIMITER $$

--
-- Create procedure `LimparLogsAntigos`
--
CREATE
DEFINER = 'cavalcrod'@'%'
PROCEDURE IF NOT EXISTS LimparLogsAntigos (IN dias_para_manter int UNSIGNED)
BEGIN
  DECLARE registros_removidos int DEFAULT 0;

  -- Validar parâmetro
  IF dias_para_manter < 7 THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Mínimo de 7 dias deve ser mantido nos logs';
  END IF;

  DELETE
    FROM logs_sistema
  WHERE data_hora < DATE_SUB(NOW(), INTERVAL dias_para_manter DAY);

  SET registros_removidos = ROW_COUNT();

  SELECT
    registros_removidos AS registros_removidos,
    dias_para_manter AS dias_mantidos,
    NOW() AS data_limpeza;

  -- Log da limpeza
  INSERT INTO logs_sistema (acao, tabela_afetada, detalhes, data_hora)
    VALUES ('LIMPEZA_LOGS', 'logs_sistema', JSON_OBJECT('registros_removidos', registros_removidos, 'dias_mantidos', dias_para_manter), NOW());
END
$$

DELIMITER ;

--
-- Create table `tentativas_biometria`
--
CREATE TABLE IF NOT EXISTS tentativas_biometria (
  id int NOT NULL AUTO_INCREMENT,
  template_hash varchar(64) DEFAULT NULL,
  timestamp datetime DEFAULT CURRENT_TIMESTAMP,
  device_vendor_id varchar(10) DEFAULT NULL,
  device_product_id varchar(10) DEFAULT NULL,
  success tinyint(1) DEFAULT 0,
  funcionario_id int DEFAULT NULL,
  ip_address varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_funcionario` on table `tentativas_biometria`
--
ALTER TABLE tentativas_biometria
ADD INDEX idx_funcionario (funcionario_id);

--
-- Create index `idx_success` on table `tentativas_biometria`
--
ALTER TABLE tentativas_biometria
ADD INDEX idx_success (success);

--
-- Create index `idx_timestamp` on table `tentativas_biometria`
--
ALTER TABLE tentativas_biometria
ADD INDEX idx_timestamp (timestamp);

--
-- Create table `logs_seguranca`
--
CREATE TABLE IF NOT EXISTS logs_seguranca (
  id int NOT NULL AUTO_INCREMENT,
  tipo_evento varchar(50) NOT NULL,
  funcionario_id int DEFAULT NULL,
  detalhes json DEFAULT NULL,
  timestamp datetime DEFAULT CURRENT_TIMESTAMP,
  ip_address varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  nivel_risco enum ('baixo', 'medio', 'alto') DEFAULT 'baixo',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_funcionario_timestamp` on table `logs_seguranca`
--
ALTER TABLE logs_seguranca
ADD INDEX idx_funcionario_timestamp (funcionario_id, timestamp);

--
-- Create index `idx_nivel_risco` on table `logs_seguranca`
--
ALTER TABLE logs_seguranca
ADD INDEX idx_nivel_risco (nivel_risco);

--
-- Create index `idx_tipo_timestamp` on table `logs_seguranca`
--
ALTER TABLE logs_seguranca
ADD INDEX idx_tipo_timestamp (tipo_evento, timestamp);

--
-- Create table `logs_biometria`
--
CREATE TABLE IF NOT EXISTS logs_biometria (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int DEFAULT NULL,
  timestamp datetime DEFAULT CURRENT_TIMESTAMP,
  similarity_score decimal(5, 4) DEFAULT 0.0000,
  device_info json DEFAULT NULL,
  status enum ('success', 'failed') DEFAULT 'failed',
  ip_address varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_funcionario_timestamp` on table `logs_biometria`
--
ALTER TABLE logs_biometria
ADD INDEX idx_funcionario_timestamp (funcionario_id, timestamp);

--
-- Create index `idx_status_timestamp` on table `logs_biometria`
--
ALTER TABLE logs_biometria
ADD INDEX idx_status_timestamp (status, timestamp);

DELIMITER $$

--
-- Create procedure `sp_cleanup_old_logs`
--
CREATE
DEFINER = 'cavalcrod'@'%'
PROCEDURE IF NOT EXISTS sp_cleanup_old_logs ()
BEGIN
  DECLARE EXIT HANDLER FOR SQLEXCEPTION
  BEGIN
    ROLLBACK;
    RESIGNAL;
  END;

  START TRANSACTION;

    DELETE
      FROM logs_biometria
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY);
    DELETE
      FROM logs_seguranca
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 180 DAY)
      AND nivel_risco != 'alto';
    DELETE
      FROM tentativas_biometria
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY);

  COMMIT;
END
$$

DELIMITER ;

--
-- Create view `vw_estatisticas_biometria`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_biometria
AS
SELECT
  CAST(`lb`.`timestamp` AS date) AS `data`,
  COUNT(0) AS `total_tentativas`,
  SUM((CASE WHEN (`lb`.`status` = 'success') THEN 1 ELSE 0 END)) AS `sucessos`,
  SUM((CASE WHEN (`lb`.`status` = 'failed') THEN 1 ELSE 0 END)) AS `falhas`,
  ROUND(AVG(`lb`.`similarity_score`), 4) AS `score_medio`,
  COUNT(DISTINCT `lb`.`funcionario_id`) AS `funcionarios_unicos`
FROM `logs_biometria` `lb`
WHERE (`lb`.`timestamp` >= (CURDATE() - INTERVAL 30 DAY))
GROUP BY CAST(`lb`.`timestamp` AS date)
ORDER BY `data` DESC;

--
-- Create table `log_exclusao_empresas`
--
CREATE TABLE IF NOT EXISTS log_exclusao_empresas (
  id int NOT NULL AUTO_INCREMENT,
  empresa_id int NOT NULL,
  razao_social varchar(200) NOT NULL,
  cnpj varchar(18) NOT NULL,
  usuario_id int NOT NULL,
  data_exclusao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  motivo text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `dispositivos_biometricos`
--
CREATE TABLE IF NOT EXISTS dispositivos_biometricos (
  id int NOT NULL AUTO_INCREMENT,
  nome_dispositivo varchar(100) NOT NULL,
  fabricante varchar(50) NOT NULL,
  device_id varchar(255) NOT NULL,
  vendor_id varchar(10) DEFAULT NULL,
  product_id varchar(10) DEFAULT NULL,
  serial_number varchar(100) DEFAULT NULL,
  versao_driver varchar(50) DEFAULT NULL,
  porta_usb varchar(20) DEFAULT NULL,
  status_dispositivo enum ('ativo', 'inativo', 'erro', 'manutencao') NOT NULL DEFAULT 'ativo',
  data_registro timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  data_ultima_deteccao timestamp NULL DEFAULT NULL,
  data_desinstalacao timestamp NULL DEFAULT NULL,
  configuracao_json json DEFAULT NULL,
  observacoes text DEFAULT NULL,
  ativo tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `uk_device_id` on table `dispositivos_biometricos`
--
ALTER TABLE dispositivos_biometricos
ADD UNIQUE INDEX uk_device_id (device_id);

--
-- Create table `configuracoes_sistema`
--
CREATE TABLE IF NOT EXISTS configuracoes_sistema (
  id int NOT NULL AUTO_INCREMENT,
  chave varchar(100) NOT NULL,
  valor text DEFAULT NULL,
  descricao text DEFAULT NULL,
  tipo enum ('string', 'integer', 'boolean', 'time', 'json') DEFAULT 'string',
  categoria varchar(50) DEFAULT 'geral',
  criado_em datetime DEFAULT CURRENT_TIMESTAMP,
  atualizado_em datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  editavel tinyint(1) DEFAULT 1,
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 38,
AVG_ROW_LENGTH = 528,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `chave` on table `configuracoes_sistema`
--
ALTER TABLE configuracoes_sistema
ADD UNIQUE INDEX chave (chave);

--
-- Create index `idx_categoria` on table `configuracoes_sistema`
--
ALTER TABLE configuracoes_sistema
ADD INDEX idx_categoria (categoria);

--
-- Create index `idx_chave` on table `configuracoes_sistema`
--
ALTER TABLE configuracoes_sistema
ADD INDEX idx_chave (chave);

--
-- Create table `cad_empresas`
--
CREATE TABLE IF NOT EXISTS cad_empresas (
  id int NOT NULL AUTO_INCREMENT,
  nome_empresa varchar(255) DEFAULT NULL,
  logotipo longblob DEFAULT NULL,
  logotipo_mime_type varchar(100) DEFAULT NULL,
  regras_especificas text DEFAULT NULL,
  tolerancia_atraso int DEFAULT 10,
  tolerancia_saida_antecipada int DEFAULT 10,
  jornada_trabalho_padrao time DEFAULT '08:00:00',
  intervalo_almoco_inicio time DEFAULT '12:00:00',
  intervalo_almoco_fim time DEFAULT '13:00:00',
  data_criacao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  ativa tinyint(1) DEFAULT 1,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `backup_jornada_funcionarios`
--
CREATE TABLE IF NOT EXISTS backup_jornada_funcionarios (
  id int UNSIGNED NOT NULL DEFAULT 0,
  nome_completo varchar(100) NOT NULL,
  jornada_seg_qui_entrada time DEFAULT NULL,
  jornada_seg_qui_saida time DEFAULT NULL,
  jornada_sex_entrada time DEFAULT NULL,
  jornada_sex_saida time DEFAULT NULL,
  jornada_intervalo_entrada time DEFAULT NULL,
  jornada_intervalo_saida time DEFAULT NULL,
  tolerancia_ponto int UNSIGNED NOT NULL DEFAULT 5 COMMENT 'Tolerância em minutos',
  inicio_expediente time DEFAULT '07:00:00' COMMENT 'HorÃ¡rio programado de entrada',
  horario_saida_seg_qui time DEFAULT '17:00:00' COMMENT 'HorÃ¡rio de saÃ­da segunda a quinta',
  horario_saida_sexta time DEFAULT '16:30:00' COMMENT 'HorÃ¡rio de saÃ­da na sexta-feira',
  periodo_almoco_inicio time DEFAULT '11:00:00' COMMENT 'InÃ­cio da janela de almoÃ§o',
  periodo_almoco_fim time DEFAULT '14:00:00' COMMENT 'Fim da janela de almoÃ§o',
  duracao_minima_almoco int DEFAULT 60 COMMENT 'DuraÃ§Ã£o mÃ­nima do almoÃ§o em minutos',
  tolerancia_entrada int DEFAULT 15 COMMENT 'TolerÃ¢ncia para entrada em minutos',
  permite_banco_horas_positivo tinyint(1) DEFAULT 0 COMMENT 'Permite acumular horas extras como crÃ©dito',
  data_backup datetime NOT NULL
)
ENGINE = INNODB,
AVG_ROW_LENGTH = 5461,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

-- 
-- Dumping data for table empresas
--
INSERT INTO empresas VALUES
(4, 'Renovar Construcao Civil Ltda', 'Msv Engenharia e Construcao', '18.214.095/0001-01', '92984383510', '<EMAIL>', 1, 0, '2025-07-03 11:32:08', NULL, NULL, 1, NULL, 'principal', NULL),
(6, 'Construtora ABC Ltda', 'ABC ConstruÃ§Ãµes', '12.345.678/0001-90', '(92) 3234-5678', '<EMAIL>', 1, 0, '2025-07-03 19:44:31', NULL, NULL, 0, NULL, 'independente', NULL),
(7, 'Engenharia XYZ S/A', 'XYZ Engenharia', '98.765.432/0001-10', '(92) 3876-5432', '<EMAIL>', 1, 0, '2025-07-03 19:44:31', NULL, NULL, 0, NULL, 'independente', NULL),
(8, 'ServiÃ§os TÃ©cnicos DEF Ltda', 'DEF ServiÃ§os', '11.222.333/0001-44', '(92) 3111-2222', '<EMAIL>', 1, 0, '2025-07-03 19:44:31', NULL, NULL, 0, NULL, 'independente', NULL),
(9, 'Consultoria GHI Ltda', 'GHI Consultoria', '55.666.777/0001-88', '(92) 3555-6666', '<EMAIL>', 1, 0, '2025-07-03 19:44:31', NULL, NULL, 0, NULL, 'independente', NULL),
(10, 'Tecnologia JKL S/A', 'JKL Tech', '99.888.777/0001-66', '(92) 3999-8888', '<EMAIL>', 1, 0, '2025-07-03 19:44:31', NULL, NULL, 0, NULL, 'independente', NULL),
(11, 'AiNexus Tecnologia', 'AiNexus Tecnologia', '48.061.684/0001-68', '92992455278', '<EMAIL>', 1, 0, '2025-07-04 09:32:35', x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image/png', 0, NULL, 'independente', NULL),
(13, 'Empresa Teste Sem Jornada Ltda', 'Teste Sem Jornada', '12.345.678/0001-99', NULL, NULL, 1, 0, '2025-07-04 11:12:02', NULL, NULL, 0, NULL, 'independente', NULL);

-- Table controle_ponto.clientes does not contain any data (it is empty)

-- 
-- Dumping data for table jornadas_trabalho
--
INSERT INTO jornadas_trabalho VALUES
(1, 11, 'Jornada Padrão AiNexus', NULL, NULL, 'Diurno', NULL, '09:00:00', '18:00:00', '09:00:00', '18:00:00', NULL, NULL, NULL, NULL, 1, '13:00:00', '14:00:00', 60, 0, '11:00:00', '14:00:00', 5, 10, 10, 1, 120, 1, 120, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, '2025-07-04 17:54:06', '2025-07-05 12:26:04', NULL, NULL);

-- 
-- Dumping data for table funcionarios
--
INSERT INTO funcionarios(id, empresa_id, cliente_atual_id, horario_trabalho_id, nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade, ctps_numero, ctps_serie_uf, pis_pasep, endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado, telefone1, telefone2, email, cargo, setor, setor_obra, empresa, matricula_empresa, data_admissao, tipo_contrato, jornada_seg_qui_entrada, jornada_seg_qui_saida, jornada_sex_entrada, jornada_sex_saida, jornada_intervalo_entrada, jornada_intervalo_saida, digital_dedo1, digital_dedo2, foto_3x4, foto_url, nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, ativo, status_cadastro, data_cadastro, data_atualizacao, biometria_qualidade_1, biometria_qualidade_2, biometria_data_cadastro, salario_base, tipo_pagamento, valor_hora, valor_hora_extra, percentual_hora_extra, vale_transporte, vale_alimentacao, outros_beneficios, desconto_inss, desconto_irrf, observacoes_pagamento, data_ultima_alteracao_salario, biometria_qualidade, ultimo_login, tentativas_biometria, status_biometria, status, epi_obrigatorio_json, epi_treinamento_data, epi_responsavel, epi_observacoes, epi_termo_assinado, inicio_expediente, horario_saida_seg_qui, horario_saida_sexta, periodo_almoco_inicio, periodo_almoco_fim, duracao_minima_almoco, tolerancia_entrada, permite_banco_horas_positivo, data_atualizacao_jornada, horas_trabalho_obrigatorias, jornada_trabalho_id) VALUES
(1, 11, NULL, NULL, 'RICHARDSON CARDOSO RODRIGUES', '711.256.042-04', '31.799.841', '1981-03-20', 'M', 'Casado', 'BRASILEIRO', '1234567', '001/AC', '123.45678.90-1', 'RUA GERMÂNIO', 'VILA DA PRATA', 'MANAUS', '69030-685', 'AM', '(92) 99245-5278', NULL, '<EMAIL>', 'DESENVOLVEDOR SENIOR', 'Administrativo', 'TI', 'Empresa Principal', '001', '2024-01-01', 'PJ', NULL, NULL, NULL, NULL, NULL, NULL, x'', x'', 'fotos_funcionarios/funcionario_1_20250704T203909.jpg', NULL, 'Funcionario', 'Diurno', 15, 1, 1, 1, 'Ativo', '2025-06-05 16:27:38', '2025-07-05 10:59:59', NULL, NULL, NULL, NULL, 'Mensal', NULL, NULL, 50.00, NULL, NULL, NULL, 1, 1, NULL, NULL, 0, NULL, 0, 'inativo', 'ativo', NULL, NULL, '', '', 0, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-07-05 10:59:59', 8.00, 1),
(8, 4, NULL, NULL, 'SUELEN OLIVEIRA DOS SANTOS', '702.965.122-73', '32.320.213', '2000-03-05', 'F', 'Solteiro', 'Brasileira', '9244846', '0050', '201.86947.50-4', 'RUA MARIA SEGADILHA, N 93', 'LÍRIO DO VALE', 'MANAUS', '69038-220', 'AM', '(92) 99332-4049', NULL, NULL, 'ANALISTA ADMINISTRATIVO', NULL, 'ADMINISTRATIVO', 'Empresa Principal', '0004', '2025-04-01', 'CLT', '07:30:00', '17:30:00', '07:30:00', '16:30:00', '12:00:00', '13:00:00', NULL, NULL, 'fotos_funcionarios/funcionario_8_20250617T095959.png', NULL, 'Supervisao', 'Diurno', 10, 1, 1, 1, 'Ativo', '2025-06-16 18:36:19', '2025-07-04 10:45:07', NULL, NULL, NULL, NULL, 'Mensal', NULL, NULL, 50.00, NULL, NULL, NULL, 1, 1, NULL, NULL, 0, NULL, 0, 'inativo', 'ativo', NULL, NULL, NULL, NULL, 0, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-07-04 10:45:07', 8.00, NULL),
(9, 4, NULL, NULL, 'TESTE 2 TURNO', '744.589.652-14', '12.354.785', '1980-01-01', 'F', 'Solteiro', 'Brasileira', '1234567', '0000000000', '000.00000.00-0', 'RUA 17', 'SANTO AGOSTINHO', 'MANAUS', '69036-850', 'AM', '(92) 99248-7456', NULL, NULL, 'SOLDADOR', NULL, 'OBRA NORTE', 'Empresa Principal', '0005', '2025-01-01', 'CLT', '07:00:00', '17:00:00', '07:00:00', '16:00:00', '12:00:00', '13:00:00', NULL, NULL, 'fotos_funcionarios/funcionario_9_20250618T091757.png', NULL, 'Funcionario', 'Noturno', 10, 1, 1, 1, 'Ativo', '2025-06-18 09:17:57', '2025-07-04 10:45:07', NULL, NULL, NULL, NULL, 'Mensal', NULL, NULL, 50.00, NULL, NULL, NULL, 1, 1, NULL, NULL, 0, NULL, 0, 'inativo', 'ativo', NULL, NULL, NULL, NULL, 0, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-07-04 10:45:07', 8.00, NULL);

-- 
-- Dumping data for table empresa_clientes
--
INSERT INTO empresa_clientes(id, empresa_principal_id, empresa_cliente_id, nome_contrato, codigo_contrato, descricao_projeto, data_inicio, data_fim, valor_contrato, status_contrato, ativo, observacoes, configuracoes_especiais, created_at, updated_at, created_by) VALUES
(1, 4, 11, NULL, NULL, NULL, '2025-01-01', NULL, NULL, 'ativo', 1, NULL, NULL, '2025-07-04 09:45:43', '2025-07-04 09:45:43', NULL);

-- 
-- Dumping data for table usuarios
--
INSERT INTO usuarios(id, usuario, nome_completo, email, ativo, ultimo_login, senha, nivel_acesso, data_criacao, data_ultimo_acesso) VALUES
(2, 'teste', NULL, NULL, 1, NULL, 'pbkdf2:sha256:600000$OmXNQh5XtS7BNSri$e174ced16e67d04869383d125a0665498d51b7bc82b6bbc0a6e00d20cb5bc2c0', 'usuario', '2025-06-06 10:56:33', NULL),
(3, 'status', 'Usuário Status do Sistema', '<EMAIL>', 1, NULL, 'pbkdf2:sha256:600000$x5KZTJwcBg7xA6HP$b084242370e60ff03f0e061dcb5c9f6170383dc014dd06fcab8e9ac1bed748d1', 'usuario', '2025-06-08 19:20:11', NULL),
(5, 'cavalcrod', 'Quality Control Manager', '<EMAIL>', 1, NULL, 'pbkdf2:sha256:600000$G5lErTBgGugN2iXS$fdbebe2c4fd4f939c7d9161136d74713e4e89a22d6898dfd87142cf97a5a3a54', 'usuario', '2025-06-10 12:49:17', NULL),
(6, ' cavalcrod ', NULL, NULL, 1, NULL, ' @Ric6109 ', 'admin', '2025-06-10 14:48:51', NULL),
(7, 'suelen', NULL, NULL, 1, NULL, 'pbkdf2:sha256:600000$ScRWhyIZ4d9sQrgJ$3be2cd9a857975a3f9e5fa90217dd98c76f6ee7535de9ede2b0d748854a47afb', 'admin', '2025-06-16 18:18:47', NULL),
(9, 'admin', 'Admin Sistema', '<EMAIL>', 1, NULL, 'pbkdf2:sha256:600000$aopQv5YR3yfljlF0$2f17fe06bf7c03d7fb57cacf69183814f0daa09359c69f9b013ce9cd7cd00787', 'admin', '2025-06-25 21:56:52', NULL);

-- 
-- Dumping data for table funcionario_alocacoes
--
INSERT INTO funcionario_alocacoes(id, funcionario_id, empresa_cliente_id, contrato_id, jornada_trabalho_id, cargo_no_cliente, data_inicio, data_fim, percentual_alocacao, valor_hora, ativo, observacoes, configuracoes_alocacao, created_at, updated_at, created_by) VALUES
(1, 9, 11, NULL, 1, 'soldador', '2025-07-05', '2025-08-04', 100.00, 50.00, 1, 'teste', NULL, '2025-07-05 10:53:37', '2025-07-05 10:53:37', NULL);

-- 
-- Dumping data for table horarios_trabalho
--
INSERT INTO horarios_trabalho(id, empresa_id, nome_horario, entrada_manha, saida_almoco, entrada_tarde, saida, tolerancia_minutos, ativo, data_cadastro) VALUES
(1, 4, 'Padrão Msv Engenharia e Construcao', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10, 1, '2025-07-04 11:04:24'),
(2, 6, 'Padrão ABC ConstruÃ§Ãµes', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10, 1, '2025-07-04 11:04:24'),
(3, 7, 'Padrão XYZ Engenharia', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10, 1, '2025-07-04 11:04:24'),
(4, 8, 'Padrão DEF ServiÃ§os', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10, 1, '2025-07-04 11:04:24'),
(5, 9, 'Padrão GHI Consultoria', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10, 1, '2025-07-04 11:04:24'),
(6, 10, 'Padrão JKL Tech', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10, 1, '2025-07-04 11:04:24'),
(7, 11, 'Padrão AiNexus Tecnologia', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10, 1, '2025-07-04 11:04:24');

-- Table controle_ponto.tentativas_biometria does not contain any data (it is empty)

-- 
-- Dumping data for table registros_ponto
--
INSERT INTO registros_ponto(id, funcionario_id, tipo_registro, data_hora, metodo_registro, criado_por, template_biometrico, digital_capturada, qualidade_biometria, observacoes, ip_origem, user_agent, sincronizado, criado_em, atualizado_em, biometria_verificada, device_hash, security_score, ip_address, status_pontualidade) VALUES
(1, 1, 'entrada_manha', '2025-07-04 08:52:36', 'manual', NULL, NULL, NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-04 08:52:36', '2025-07-04 08:52:36', 0, NULL, 100, NULL, 'Atrasado'),
(2, 8, 'entrada_tarde', '2025-07-07 13:05:05', 'manual', NULL, NULL, NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-07 13:05:05', '2025-07-07 13:05:05', 0, NULL, 100, NULL, 'Pontual'),
(3, 9, 'entrada_tarde', '2025-07-07 13:05:20', 'manual', NULL, NULL, NULL, NULL, '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '2025-07-07 13:05:20', '2025-07-07 13:05:20', 0, NULL, 100, NULL, 'Pontual');

-- 
-- Dumping data for table permissoes
--
INSERT INTO permissoes(usuario_id, nivel_acesso, data_atribuicao) VALUES
(2, 'usuario', '2025-06-06 10:56:33'),
(3, 'usuario', '2025-06-08 19:20:33'),
(5, 'usuario', '2025-07-07 10:59:23'),
(6, 'usuario', '2025-06-10 14:49:26'),
(7, 'admin', '2025-06-16 18:18:47'),
(9, 'admin', '2025-06-25 22:21:37');

-- Table controle_ponto.log_exclusao_empresas does not contain any data (it is empty)

-- 
-- Dumping data for table logs_sistema
--
INSERT INTO logs_sistema(id, usuario_id, acao, tabela_afetada, registro_id, detalhes, ip_origem, user_agent, data_hora) VALUES
(1, NULL, 'INICIALIZACAO_BANCO', 'sistema', NULL, '{"status": "sucesso", "versao": "1.2", "data_criacao": "2025-06-05 20:06:48.000000"}', NULL, NULL, '2025-06-05 16:06:48'),
(2, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 1, '{"data_hora": "2025-06-05 22:29:09.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:29:09'),
(3, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 1, '{"data_hora": "2025-06-05 08:05:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(4, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 2, '{"data_hora": "2025-06-05 17:10:00.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(5, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 3, '{"data_hora": "2025-06-05 08:05:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 2, "metodo_registro": "manual", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(6, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 4, '{"data_hora": "2025-06-05 17:10:00.000000", "tipo_registro": "saida", "funcionario_id": 2, "metodo_registro": "biometrico", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(7, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 5, '{"data_hora": "2025-06-04 07:58:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(8, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 6, '{"data_hora": "2025-06-04 07:58:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 2, "metodo_registro": "biometrico", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(9, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 7, '{"data_hora": "2025-06-06 01:20:04.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:20:04'),
(10, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 8, '{"data_hora": "2025-06-06 01:22:30.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:22:30'),
(11, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 9, '{"data_hora": "2025-06-06 01:23:22.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:23:22'),
(12, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 10, '{"data_hora": "2025-06-06 09:02:37.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 09:02:37'),
(13, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 11, '{"data_hora": "2025-06-06 14:13:20.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 3, "metodo_registro": "manual", "nome_funcionario": "Maria Oliveira Costa", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 14:13:20'),
(14, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 12, '{"data_hora": "2025-06-07 19:40:10.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-07 19:40:10'),
(15, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 13, '{"data_hora": "2025-06-08 18:00:48.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-08 18:00:48'),
(16, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 14, '{"data_hora": "2025-06-09 00:04:24.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": 85}', NULL, NULL, '2025-06-09 00:04:24'),
(17, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 15, '{"data_hora": "2025-06-09 00:04:47.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": 85}', NULL, NULL, '2025-06-09 00:04:47'),
(18, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 16, '{"data_hora": "2025-06-09 00:07:26.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": 85}', NULL, NULL, '2025-06-09 00:07:26'),
(19, NULL, 'CRIACAO_TABELA_CONFIGURACOES', 'configuracoes_sistema', NULL, '{"operacao": "correcao_critica", "responsavel": "Claude AI Assistant", "tabela_criada": "configuracoes_sistema", "documento_referencia": "Correção de Falhas do Sistema.markdown", "configuracoes_inseridas": 15}', NULL, NULL, '2025-06-09 16:29:24'),
(20, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T22:54:43.035102", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "c1595090815e454e", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 22:54:43'),
(21, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:11:56.757323", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "bccdffaffc286b31", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:11:57'),
(22, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:16:03.609032", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "b21d38e74d319904", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:16:04'),
(23, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:27:36.807910", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "0eb7dfb22da9dc81", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:27:37'),
(24, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:54:05.768789", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "eaa985a0b05e6d8c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:54:06'),
(25, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:55:03.681970", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/configuracoes/", "ip_origem": "***********", "session_id": "eaa985a0b05e6d8c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:55:04'),
(26, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:56:38.642329", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "35897cc7bc0b505a", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:56:39'),
(27, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:56:45.825609", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/funcionarios/", "ip_origem": "***********", "session_id": "35897cc7bc0b505a", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:56:46'),
(28, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:57:34.806182", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "35897cc7bc0b505a", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:57:35'),
(29, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T00:14:01.872003", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "d98a5642d8c60b0c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 00:14:02'),
(30, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T00:18:35.866717", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/funcionarios/", "ip_origem": "***********", "session_id": "d98a5642d8c60b0c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 00:18:36'),
(31, NULL, '[LOGIN:INFO] Tentativa de login: status', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T00:18:48.950638", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "d98a5642d8c60b0c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "status"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 00:18:49'),
(32, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T08:52:37.501327", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "8f96b1ea1d8f70f0", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 08:52:38'),
(33, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T08:53:41.277687", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/funcionarios/", "ip_origem": "************", "session_id": "8f96b1ea1d8f70f0", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 08:53:41'),
(34, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:00:25.913665", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "1cde18f92f10a7e0", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:00:26'),
(35, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:11:18.258902", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:11:18'),
(36, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:27:44.012921", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:27:44'),
(37, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:27:44.946649", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:27:45'),
(38, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:30:18.282472", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:30:18'),
(39, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:30:19.110784", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:30:19'),
(40, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:32:40.344872", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:32:40'),
(41, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:36:42.178963", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:36:42'),
(42, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:37:29.274996", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:37:29'),
(43, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:38:52.184760", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:38:52'),
(44, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:39:49.266399", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:39:49'),
(45, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:40:51.249143", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:40:51'),
(46, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:41:16.921365", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:41:17'),
(47, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:48:30.584236", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:48:31'),
(48, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:54:06.616742", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "595c40ab92027e1c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:54:07'),
(49, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:55:02.441801", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "595c40ab92027e1c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:55:02'),
(50, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:03:08.824593", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "7e2cb865c3a31b14", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:03:09'),
(51, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:06:51.146595", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "7e2cb865c3a31b14", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:06:51'),
(52, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:08:49.517407", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "de4a1d3357ef8065", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:08:50'),
(53, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:09:00.389321", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": "admin", "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "de4a1d3357ef8065", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:09:00'),
(54, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:11:23.115108", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "ec15c5627341d603", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:11:23'),
(55, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:11:45.553938", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "ec15c5627341d603", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:11:46'),
(56, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:18:50.672069", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "b803533a43808d88", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:18:51'),
(57, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:20:36.909630", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "60df3a52ab109440", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:20:37'),
(58, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:32:09.372911", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "9aa91e335bae0f50", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:32:09'),
(59, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 17, '{"data_hora": "2025-06-10 12:33:43.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-10 12:33:43'),
(60, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 18, '{"data_hora": "2025-06-10 14:43:21.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-10 14:43:21'),
(61, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 19, '{"data_hora": "2025-06-10 19:06:17.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-10 19:06:17'),
(62, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 20, '{"data_hora": "2025-06-11 11:16:19.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-11 11:16:19'),
(63, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 21, '{"data_hora": "2025-06-11 16:03:56.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-11 16:03:56'),
(64, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 22, '{"data_hora": "2025-06-13 11:34:04.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-13 11:34:04'),
(65, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 23, '{"data_hora": "2025-06-13 11:35:48.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-13 11:35:48'),
(66, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 24, '{"data_hora": "2025-06-13 12:36:36.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-13 12:36:36'),
(67, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 25, '{"data_hora": "2025-06-16 09:21:59.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 09:21:59'),
(68, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 26, '{"data_hora": "2025-06-16 10:17:33.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 10:17:33'),
(69, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 27, '{"data_hora": "2025-06-16 14:17:39.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 6, "metodo_registro": "manual", "nome_funcionario": "FUNCIONARIO TESTE", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 14:17:39'),
(70, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 28, '{"data_hora": "2025-06-16 14:18:55.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 14:18:55'),
(71, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 29, '{"data_hora": "2025-06-16 14:20:28.000000", "tipo_registro": "saida_almoco", "funcionario_id": 6, "metodo_registro": "manual", "nome_funcionario": "FUNCIONARIO TESTE", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 14:20:28'),
(72, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 30, '{"data_hora": "2025-06-16 14:20:43.000000", "tipo_registro": "entrada_manha", "funcionario_id": 6, "metodo_registro": "manual", "nome_funcionario": "FUNCIONARIO TESTE", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 14:20:43'),
(73, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 34, '{"data_hora": "2025-06-16 15:46:46.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 15:46:46'),
(74, NULL, 'CORRECAO', NULL, NULL, '{"autor": "sistema", "arquivo": "app_registro_ponto.py", "mensagem": "Foi corrigida a consulta SQL em app_registro_ponto.py para priorizar o campo setor_obra em vez de setor.", "data_correcao": "2025-06-16 21:07:46"}', NULL, NULL, '2025-06-16 21:07:46'),
(75, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 40, '{"data_hora": "2025-06-17 07:52:53.000000", "tipo_registro": "entrada_manha", "funcionario_id": 8, "metodo_registro": "manual", "nome_funcionario": "SUELEN OLIVEIRA DOS SANTOS", "qualidade_biometria": null}', NULL, NULL, '2025-06-17 07:52:53'),
(76, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 42, '{"data_hora": "2025-06-17 08:04:15.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-17 08:04:15'),
(77, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 43, '{"data_hora": "2025-06-17 13:00:54.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-17 13:00:54'),
(78, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 45, '{"data_hora": "2025-06-17 14:05:24.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-17 14:05:24'),
(79, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 47, '{"data_hora": "2025-06-17 18:06:19.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-17 18:06:19'),
(80, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 48, '{"data_hora": "2025-06-18 08:12:38.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-18 08:12:38'),
(81, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 49, '{"data_hora": "2025-06-18 08:28:44.000000", "tipo_registro": "entrada_manha", "funcionario_id": 8, "metodo_registro": "manual", "nome_funcionario": "SUELEN OLIVEIRA DOS SANTOS", "qualidade_biometria": null}', NULL, NULL, '2025-06-18 08:28:44'),
(82, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 50, '{"data_hora": "2025-06-18 13:02:16.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-18 13:02:16'),
(83, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 51, '{"data_hora": "2025-06-18 14:19:53.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-18 14:19:53'),
(84, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 53, '{"data_hora": "2025-06-18 19:07:35.000000", "tipo_registro": "saida", "funcionario_id": 8, "metodo_registro": "manual", "nome_funcionario": "SUELEN OLIVEIRA DOS SANTOS", "qualidade_biometria": null}', NULL, NULL, '2025-06-18 19:07:35'),
(85, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 54, '{"data_hora": "2025-06-19 07:42:27.000000", "tipo_registro": "entrada_manha", "funcionario_id": 8, "metodo_registro": "manual", "nome_funcionario": "SUELEN OLIVEIRA DOS SANTOS", "qualidade_biometria": null}', NULL, NULL, '2025-06-19 07:42:27'),
(86, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 55, '{"data_hora": "2025-06-19 08:56:42.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-19 08:56:42'),
(87, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 56, '{"data_hora": "2025-06-19 13:15:13.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-19 13:15:13'),
(88, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 1, '{"data_hora": "2025-07-04 08:52:36.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-07-04 08:52:36'),
(89, NULL, 'UPDATE_FUNCIONARIO', 'funcionarios', 1, '{"nome": "RICHARDSON CARDOSO RODRIGUES EDITADO", "mudancas": {"nome_alterado": true, "status_alterado": false, "biometria_dedo1_alterado": false, "biometria_dedo2_alterado": false}, "funcionario_id": 1}', NULL, NULL, '2025-07-04 20:21:09'),
(90, NULL, 'UPDATE_FUNCIONARIO', 'funcionarios', 1, '{"nome": "RICHARDSON CARDOSO RODRIGUES", "mudancas": {"nome_alterado": true, "status_alterado": false, "biometria_dedo1_alterado": false, "biometria_dedo2_alterado": false}, "funcionario_id": 1}', NULL, NULL, '2025-07-04 20:21:09'),
(91, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 2, '{"data_hora": "2025-07-07 13:05:05.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 8, "metodo_registro": "manual", "nome_funcionario": "SUELEN OLIVEIRA DOS SANTOS", "qualidade_biometria": null}', NULL, NULL, '2025-07-07 13:05:05'),
(92, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 3, '{"data_hora": "2025-07-07 13:05:20.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 9, "metodo_registro": "manual", "nome_funcionario": "TESTE 2 TURNO", "qualidade_biometria": null}', NULL, NULL, '2025-07-07 13:05:20');

-- 
-- Dumping data for table logs_seguranca
--
INSERT INTO logs_seguranca(id, tipo_evento, funcionario_id, detalhes, timestamp, ip_address, user_agent, nivel_risco) VALUES
(1, 'database_update', NULL, '{"version": "2.0", "features": ["WebUSB API direct hardware access", "ZK4500 biometric device support", "Real-time template comparison", "Automatic attendance type detection", "Enhanced security audit logs"], "description": "Sistema biométrico WebUSB integrado"}', '2025-06-09 01:40:29', NULL, NULL, 'baixo');

-- Table controle_ponto.logs_biometria does not contain any data (it is empty)

-- Table controle_ponto.historico_alocacoes does not contain any data (it is empty)

-- Table controle_ponto.funcionario_cliente_alocacao does not contain any data (it is empty)

-- Table controle_ponto.epis does not contain any data (it is empty)

-- 
-- Dumping data for table empresas_config
--
INSERT INTO empresas_config(id, empresa_id, jornada_padrao_id, tolerancia_atraso, tolerancia_saida_antecipada, permite_banco_horas, limite_banco_horas_mensal, configuracoes_extras, data_criacao, data_atualizacao, jornada_segunda_entrada, jornada_segunda_saida_almoco, jornada_segunda_entrada_almoco, jornada_segunda_saida, jornada_sexta_entrada, jornada_sexta_saida_almoco, jornada_sexta_entrada_almoco, jornada_sexta_saida, intervalo_obrigatorio, tolerancia_empresa_minutos) VALUES
(1, 4, 1, 10, 10, 1, 10.00, NULL, '2025-07-04 11:54:39', '2025-07-04 11:54:39', '08:00:00', '12:00:00', '13:00:00', '17:00:00', '08:00:00', '12:00:00', '13:00:00', '16:30:00', 1, 15),
(2, 6, 2, 10, 10, 1, 10.00, NULL, '2025-07-04 11:54:39', '2025-07-04 11:54:39', '08:00:00', '12:00:00', '13:00:00', '17:00:00', '08:00:00', '12:00:00', '13:00:00', '16:30:00', 1, 15),
(3, 7, 3, 10, 10, 1, 10.00, NULL, '2025-07-04 11:54:39', '2025-07-04 11:54:39', '08:00:00', '12:00:00', '13:00:00', '17:00:00', '08:00:00', '12:00:00', '13:00:00', '16:30:00', 1, 15),
(4, 8, 4, 10, 10, 1, 10.00, NULL, '2025-07-04 11:54:39', '2025-07-04 11:54:39', '08:00:00', '12:00:00', '13:00:00', '17:00:00', '08:00:00', '12:00:00', '13:00:00', '16:30:00', 1, 15),
(5, 9, 5, 10, 10, 1, 10.00, NULL, '2025-07-04 11:54:39', '2025-07-04 11:54:39', '08:00:00', '12:00:00', '13:00:00', '17:00:00', '08:00:00', '12:00:00', '13:00:00', '16:30:00', 1, 15),
(6, 10, 6, 10, 10, 1, 10.00, NULL, '2025-07-04 11:54:39', '2025-07-04 11:54:39', '08:00:00', '12:00:00', '13:00:00', '17:00:00', '08:00:00', '12:00:00', '13:00:00', '16:30:00', 1, 15),
(7, 11, 7, 10, 10, 1, 10.00, NULL, '2025-07-04 11:54:39', '2025-07-04 21:32:32', '09:00:00', '13:00:00', '14:00:00', '18:00:00', '09:00:00', '13:00:00', '14:00:00', '18:00:00', 1, 5),
(8, 13, NULL, 10, 10, 1, 10.00, NULL, '2025-07-04 11:54:39', '2025-07-04 11:54:39', '08:00:00', '12:00:00', '13:00:00', '17:00:00', '08:00:00', '12:00:00', '13:00:00', '16:30:00', 1, 15);

-- 
-- Dumping data for table dispositivos_biometricos
--
INSERT INTO dispositivos_biometricos(id, nome_dispositivo, fabricante, device_id, vendor_id, product_id, serial_number, versao_driver, porta_usb, status_dispositivo, data_registro, data_ultima_deteccao, data_desinstalacao, configuracao_json, observacoes, ativo) VALUES
(1, 'Dispositivo Teste RLPONTO', 'Genérico', 'test_device_001', NULL, NULL, NULL, NULL, NULL, 'ativo', '2025-06-11 11:39:21', NULL, NULL, NULL, 'Dispositivo de teste criado automaticamente durante instalação', 1);

-- 
-- Dumping data for table configuracoes_sistema
--
INSERT INTO configuracoes_sistema(id, chave, valor, descricao, tipo, categoria, criado_em, atualizado_em, editavel, data_atualizacao) VALUES
(1, 'biometric_threshold', '0.7', 'Limite mínimo de similaridade biométrica', 'string', 'biometria', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(2, 'security_enabled', 'true', 'Habilitar verificações de segurança', 'boolean', 'seguranca', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(3, 'max_failed_attempts', '5', 'Máximo de tentativas falhadas por hora', 'integer', 'seguranca', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(4, 'device_whitelist', '["1b55:4500"]', 'Lista de dispositivos autorizados', 'json', 'hardware', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(5, 'attendance_tolerance_minutes', '15', 'Tolerância em minutos para pontualidade', 'integer', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(6, 'morning_start', '07:00', 'Início do período matutino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(7, 'morning_end', '09:30', 'Fim do período matutino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(8, 'lunch_out_start', '11:30', 'Início da saída para almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(9, 'lunch_out_end', '13:30', 'Fim da saída para almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(10, 'lunch_return_start', '13:30', 'Início da volta do almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(11, 'lunch_return_end', '15:00', 'Fim da volta do almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(12, 'evening_start', '17:00', 'Início do período vespertino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(13, 'evening_end', '19:00', 'Fim do período vespertino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(14, 'tema_sistema', 'claro', 'Tema do sistema', 'string', 'interface', '2025-06-09 11:03:09', '2025-06-09 23:58:37', 1, '2025-06-09 23:58:37'),
(15, 'mostrar_fotos_funcionarios', 'true', 'Mostrar fotos', 'boolean', 'interface', '2025-06-09 11:03:09', '2025-06-09 23:58:36', 1, '2025-06-09 23:58:36'),
(16, 'registros_por_pagina', '50', 'Registros por página', 'integer', 'interface', '2025-06-09 11:03:09', '2025-06-09 23:58:36', 1, '2025-06-09 23:58:36'),
(17, 'versao_sistema', '1.0', 'Versão', 'string', 'tecnico', '2025-06-09 11:03:09', '2025-06-09 11:03:09', 0, '2025-06-09 23:25:05'),
(18, 'formato_data', 'dd/mm/yyyy', 'Formato de data', 'string', 'interface', '2025-06-09 12:43:07', '2025-06-09 23:58:36', 1, '2025-06-09 23:58:36'),
(20, 'fuso_horario', 'America/Manaus', 'Fuso horário padrão do sistema', 'string', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(21, 'timeout_sessao', '86400', 'Tempo limite de sessão em segundos', 'integer', 'seguranca', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(22, 'max_tentativas_login', '5', 'Máximo de tentativas de login', 'integer', 'seguranca', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(23, 'backup_automatico', 'true', 'Habilitar backup automático', 'boolean', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(24, 'horario_backup', '02:00:00', 'Horário para execução do backup automático', 'time', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(25, 'tolerancia_ponto_minutos', '15', 'Tolerância em minutos para registro de ponto', 'integer', 'ponto', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(26, 'email_notificacoes', 'true', 'Habilitar notificações por email', 'boolean', 'notificacoes', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(27, 'smtp_servidor', 'localhost', 'Servidor SMTP para envio de emails', 'string', 'email', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(28, 'smtp_porta', '587', 'Porta do servidor SMTP', 'integer', 'email', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(30, 'modo_debug', 'false', 'Modo de depuração ativo', 'boolean', 'desenvolvimento', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(31, 'cache_habilitado', 'true', 'Cache de consultas habilitado', 'boolean', 'performance', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(32, 'log_nivel', 'INFO', 'Nível de log do sistema', 'string', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(33, 'biometria_qualidade_minima', '60', 'Qualidade mínima exigida para biometria', 'integer', 'biometria', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(34, 'empresa_principal_id', '0', 'ID da empresa principal do sistema', 'integer', 'empresa', '2025-07-03 12:57:53', '2025-07-03 12:57:53', 1, '2025-07-03 12:57:53'),
(35, 'permitir_multiplas_alocacoes', 'true', 'Permitir funcionÃ¡rio em mÃºltiplos clientes simultaneamente', 'boolean', 'funcionarios', '2025-07-03 12:57:53', '2025-07-03 12:57:53', 1, '2025-07-03 12:57:53'),
(36, 'heranca_jornada_automatica', 'true', 'HeranÃ§a automÃ¡tica de jornada ao alocar funcionÃ¡rio', 'boolean', 'funcionarios', '2025-07-03 12:57:53', '2025-07-03 12:57:53', 1, '2025-07-03 12:57:53'),
(37, 'notificar_mudancas_alocacao', 'true', 'Notificar mudanÃ§as nas alocaÃ§Ãµes de funcionÃ¡rios', 'boolean', 'notificacoes', '2025-07-03 12:57:53', '2025-07-03 12:57:53', 1, '2025-07-03 12:57:53');

-- Table controle_ponto.cad_empresas does not contain any data (it is empty)

-- 
-- Dumping data for table banco_horas
--
INSERT INTO banco_horas(id, funcionario_id, data_referencia, atraso_entrada_minutos, excesso_almoco_minutos, saida_antecipada_minutos, horas_extras_minutos, saldo_devedor_minutos, saldo_credor_minutos, saldo_liquido_minutos, status_dia, observacoes, justificativa, aprovado_por, data_aprovacao, criado_em, atualizado_em) VALUES
(1, 1, '2025-07-04', 0, 0, 0, 0, 0, 0, 0, 'incompleto', 'Sem registro de saída', NULL, NULL, NULL, '2025-07-04 08:52:36', '2025-07-04 08:52:36'),
(2, 8, '2025-07-07', 0, 0, 0, 0, 0, 0, 0, 'incompleto', 'Sem registro de entrada da manhã; Sem registro de saída', NULL, NULL, NULL, '2025-07-07 13:05:05', '2025-07-07 13:05:05'),
(3, 9, '2025-07-07', 0, 0, 0, 0, 0, 0, 0, 'incompleto', 'Sem registro de entrada da manhã; Sem registro de saída', NULL, NULL, NULL, '2025-07-07 13:05:20', '2025-07-07 13:05:20');

-- 
-- Dumping data for table backup_jornada_funcionarios
--
INSERT INTO backup_jornada_funcionarios(id, nome_completo, jornada_seg_qui_entrada, jornada_seg_qui_saida, jornada_sex_entrada, jornada_sex_saida, jornada_intervalo_entrada, jornada_intervalo_saida, tolerancia_ponto, inicio_expediente, horario_saida_seg_qui, horario_saida_sexta, periodo_almoco_inicio, periodo_almoco_fim, duracao_minima_almoco, tolerancia_entrada, permite_banco_horas_positivo, data_backup) VALUES
(1, 'RICHARDSON CARDOSO RODRIGUES', '08:00:00', '18:00:00', '08:00:00', '18:00:00', '13:00:00', '14:00:00', 10, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-06-25 14:32:03'),
(8, 'SUELEN OLIVEIRA DOS SANTOS', '07:30:00', '17:30:00', '07:30:00', '16:30:00', '12:00:00', '13:00:00', 10, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-06-25 14:32:03'),
(9, 'TESTE 2 TURNO', '07:00:00', '17:00:00', '07:00:00', '16:00:00', '12:00:00', '13:00:00', 10, '07:00:00', '17:00:00', '16:30:00', '11:00:00', '14:00:00', 60, 15, 0, '2025-06-25 14:32:03');

--
-- Set default database
--
USE controle_ponto;

DELIMITER $$

--
-- Create trigger `tr_registros_ponto_audit_insert`
--
CREATE
DEFINER = 'cavalcrod'@'%'
TRIGGER IF NOT EXISTS tr_registros_ponto_audit_insert
AFTER INSERT
ON registros_ponto
FOR EACH ROW
BEGIN
  DECLARE nome_funcionario varchar(100);

  -- Buscar nome do funcionário
  SELECT
    nome_completo INTO nome_funcionario
  FROM funcionarios
  WHERE id = NEW.funcionario_id
  LIMIT 1;

  INSERT INTO logs_sistema (acao, tabela_afetada, registro_id, usuario_id, detalhes, data_hora)
    VALUES ('INSERT_REGISTRO_PONTO', 'registros_ponto', NEW.id, NEW.criado_por, JSON_OBJECT('funcionario_id', NEW.funcionario_id, 'nome_funcionario', COALESCE(nome_funcionario, 'N/A'), 'tipo_registro', NEW.tipo_registro, 'metodo_registro', NEW.metodo_registro, 'data_hora', NEW.data_hora, 'qualidade_biometria', NEW.qualidade_biometria), NOW());
END
$$

--
-- Create trigger `tr_historico_alocacao_update`
--
CREATE
DEFINER = 'root'@'localhost'
TRIGGER IF NOT EXISTS tr_historico_alocacao_update
AFTER UPDATE
ON funcionario_alocacoes
FOR EACH ROW
BEGIN
  INSERT INTO historico_alocacoes (alocacao_id, funcionario_id, empresa_cliente_id, acao,
  dados_anteriores, dados_novos, created_at)
    VALUES (NEW.id, NEW.funcionario_id, NEW.empresa_cliente_id, 'modificada', JSON_OBJECT('data_inicio', OLD.data_inicio, 'data_fim', OLD.data_fim, 'jornada_trabalho_id', OLD.jornada_trabalho_id, 'ativo', OLD.ativo), JSON_OBJECT('data_inicio', NEW.data_inicio, 'data_fim', NEW.data_fim, 'jornada_trabalho_id', NEW.jornada_trabalho_id, 'ativo', NEW.ativo), NOW());
END
$$

--
-- Create trigger `tr_funcionarios_audit_update`
--
CREATE
DEFINER = 'cavalcrod'@'%'
TRIGGER IF NOT EXISTS tr_funcionarios_audit_update
AFTER UPDATE
ON funcionarios
FOR EACH ROW
BEGIN
  -- Só registra se houve mudança significativa
  IF (OLD.nome_completo != NEW.nome_completo
    OR OLD.status_cadastro != NEW.status_cadastro
    OR (OLD.digital_dedo1 IS NULL) != (NEW.digital_dedo1 IS NULL)
    OR (OLD.digital_dedo2 IS NULL) != (NEW.digital_dedo2 IS NULL)) THEN

    INSERT INTO logs_sistema (acao, tabela_afetada, registro_id, detalhes, data_hora)
      VALUES ('UPDATE_FUNCIONARIO', 'funcionarios', NEW.id, JSON_OBJECT('funcionario_id', NEW.id, 'nome', NEW.nome_completo, 'mudancas', JSON_OBJECT('nome_alterado', OLD.nome_completo != NEW.nome_completo, 'status_alterado', OLD.status_cadastro != NEW.status_cadastro, 'biometria_dedo1_alterado', (OLD.digital_dedo1 IS NULL) != (NEW.digital_dedo1 IS NULL), 'biometria_dedo2_alterado', (OLD.digital_dedo2 IS NULL) != (NEW.digital_dedo2 IS NULL))), NOW());
  END IF;
END
$$

DELIMITER ;

--
-- Restore previous SQL mode
--
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;

--
-- Enable foreign keys
--
/*!40014 SET FOREIGN_KEY_CHECKS = @OLD_FOREIGN_KEY_CHECKS */;