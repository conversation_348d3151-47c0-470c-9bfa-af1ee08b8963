#!/usr/bin/env python3
"""
TESTE ANTI-REGRESSÃO: Configurações vs Funcionários

Este teste específico detecta se alterações em configurações
quebram a funcionalidade de funcionários.

Baseado no problema real enfrentado pelo usuário.
"""

import sys
import os
import unittest
from datetime import datetime

# Adicionar caminho do sistema
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestConfigFuncionarioIntegration(unittest.TestCase):
    """Testa se configurações e funcionários funcionam independentemente"""
    
    def setUp(self):
        """Preparar ambiente de teste"""
        print(f"\n🧪 Iniciando teste anti-regressão: {datetime.now()}")
        
    def test_modules_load_independently(self):
        """Teste 1: Módulos devem carregar independentemente"""
        print("📋 Teste 1: Carregamento independente de módulos")
        
        try:
            # Tentar importar app_configuracoes
            import app_configuracoes
            print("   ✅ app_configuracoes carregou")
            
            # Tentar importar app_funcionarios  
            import app_funcionarios
            print("   ✅ app_funcionarios carregou")
            
            # Tentar importar app principal
            import app
            print("   ✅ app principal carregou")
            
        except ImportError as e:
            self.fail(f"❌ Módulo não carregou: {e}")
        except Exception as e:
            self.fail(f"❌ Erro ao carregar módulos: {e}")
    
    def test_database_connections_independent(self):
        """Teste 2: Conexões de banco devem funcionar para ambos"""
        print("📋 Teste 2: Conexões de banco independentes")
        
        try:
            from utils.database import get_db_connection
            
            # Testar conexão para configurações
            conn1 = get_db_connection()
            self.assertTrue(conn1, "Conexão para configurações falhou")
            
            # Testar conexão para funcionários  
            conn2 = get_db_connection()
            self.assertTrue(conn2, "Conexão para funcionários falhou")
            
            print("   ✅ Conexões de banco funcionando")
            
        except Exception as e:
            self.fail(f"❌ Erro nas conexões: {e}")
    
    def test_cross_module_functionality(self):
        """Teste 3: Funcionalidades cross-module"""
        print("📋 Teste 3: Funcionalidades cruzadas")
        
        try:
            # Simular uso de configurações
            from utils.database import DatabaseManager
            
            # Tentar query em configurações
            config_query = "SELECT COUNT(*) as total FROM configuracoes_sistema"
            config_result = DatabaseManager.execute_query(config_query)
            self.assertIsNotNone(config_result, "Query de configurações falhou")
            
            # Tentar query em funcionários
            func_query = "SELECT COUNT(*) as total FROM funcionarios"
            func_result = DatabaseManager.execute_query(func_query)
            self.assertIsNotNone(func_result, "Query de funcionários falhou")
            
            print("   ✅ Queries cross-module funcionando")
            
        except Exception as e:
            self.fail(f"❌ Erro em funcionalidades cruzadas: {e}")
    
    def test_blueprints_registration(self):
        """Teste 4: Blueprints devem estar registrados corretamente"""
        print("📋 Teste 4: Registro de blueprints")
        
        try:
            import app
            
            # Verificar se blueprints estão registrados
            blueprint_names = [bp.name for bp in app.app.blueprints.values()]
            
            required_blueprints = ['funcionarios', 'configuracoes']
            for bp_name in required_blueprints:
                self.assertIn(bp_name, blueprint_names, 
                             f"Blueprint '{bp_name}' não registrado")
            
            print(f"   ✅ Blueprints registrados: {blueprint_names}")
            
        except Exception as e:
            self.fail(f"❌ Erro nos blueprints: {e}")

def run_regression_test():
    """Função principal para executar o teste"""
    print("🛡️ EXECUTANDO TESTE ANTI-REGRESSÃO")
    print("=" * 50)
    
    # Configurar teste
    unittest.TestLoader.testMethodPrefix = 'test_'
    suite = unittest.TestLoader().loadTestsFromTestCase(TestConfigFuncionarioIntegration)
    
    # Executar teste
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Resultado
    if result.wasSuccessful():
        print("\n✅ TODOS OS TESTES PASSARAM - Sistema sem regressões")
        return True
    else:
        print(f"\n❌ {len(result.failures)} FALHAS DETECTADAS")
        print(f"❌ {len(result.errors)} ERROS DETECTADOS")
        return False

if __name__ == "__main__":
    success = run_regression_test()
    sys.exit(0 if success else 1) 