# 🚨 PROBLEMA CRÍTICO - SISTEMA DE HORÁRIOS

**Data:** 11/07/2025  
**Prioridade:** CRÍTICA  
**Status:** IDENTIFICADO - REQUER CORREÇÃO IMEDIATA

## 📋 **RESUMO DO PROBLEMA**

O sistema está permitindo registros de ponto **fora de sequência e horário** devido a inconsistências na configuração e consulta de horários de trabalho.

## 🔍 **EVIDÊNCIAS**

### **Caso Específico:**
- **Funcionário:** TESTE 5
- **Data:** 11/07/2025
- **Registros:**
  - `08:59:57` - entrada_manha (Atrasado)
  - `14:47:33` - said<PERSON>_al<PERSON><PERSON> (Pontual) ❌ **SEM TER BATIDO ENTRADA_TARDE**
  - `20:30:40` - entrada_tarde (Atrasado) ❌ **FORA DE SEQUÊNCIA E HORÁRIO**

### **Hor<PERSON>rio Configurado no Cadastro:**
- **Segunda a Quinta:** 8:00-17:00
- **Sexta:** 8:00-16:30
- **Intervalo:** 12:00-13:00
- **Tolerância:** 15 minutos

## 🎯 **CAUSA RAIZ**

### **1. Inconsistência na Consulta de Horários:**

**Função Atual (INCORRETA):**
```sql
-- Busca na tabela horarios_trabalho via empresa
SELECT ht.entrada_manha, ht.saida_almoco, ht.entrada_tarde, ht.saida
FROM funcionarios f
LEFT JOIN empresas e ON f.empresa_id = e.id
LEFT JOIN horarios_trabalho ht ON e.id = ht.empresa_id
WHERE f.id = %s
```

**Deveria Buscar (CORRETO):**
```sql
-- Busca diretamente na tabela funcionarios
SELECT 
    jornada_seg_qui_entrada,
    jornada_seg_qui_saida,
    jornada_sex_entrada,
    jornada_sex_saida,
    jornada_intervalo_entrada,
    jornada_intervalo_saida,
    tolerancia_ponto
FROM funcionarios 
WHERE id = %s
```

### **2. Armazenamento Duplo:**
- Horários salvos na tabela `funcionarios` (campos jornada_*)
- Sistema tentando buscar na tabela `horarios_trabalho`
- Resultado: `NULL` retornado, validações ignoradas

## 🔧 **CORREÇÕES NECESSÁRIAS**

### **1. Corrigir Função `obter_horarios_funcionario()`**
- Buscar horários diretamente da tabela `funcionarios`
- Considerar diferença entre seg-qui e sexta
- Mapear campos corretamente

### **2. Implementar Validação de Sequência**
- Impedir registros fora de ordem
- Validar horários permitidos
- Bloquear registros muito tardios

### **3. Revisar Lógica de Classificação**
- Função `classificar_batida_inteligente()` 
- Validação por período do dia
- Controle de tolerância

## 📊 **IMPACTO**

### **Problemas Atuais:**
- ✅ Registros fora de sequência aceitos
- ✅ Horários inadequados permitidos
- ✅ Validações de horário não funcionando
- ✅ Relatórios de ponto incorretos

### **Riscos:**
- Dados de ponto inconsistentes
- Cálculos de horas incorretos
- Problemas trabalhistas
- Perda de controle de jornada

## 🚀 **PLANO DE AÇÃO**

### **Fase 1 - Correção Imediata:**
1. Corrigir função `obter_horarios_funcionario()`
2. Implementar validação de horários
3. Bloquear registros fora de sequência

### **Fase 2 - Validação:**
1. Testar com funcionários reais
2. Verificar todos os cenários
3. Validar cálculos de horas

### **Fase 3 - Documentação:**
1. Atualizar documentação técnica
2. Criar guia de configuração
3. Treinar usuários

## 📝 **OBSERVAÇÕES**

- **Urgente:** Sistema em produção com falha crítica
- **Prioridade:** Correção antes de novos registros
- **Teste:** Validar com dados reais antes do deploy

---
**Responsável:** Equipe de Desenvolvimento  
**Prazo:** Imediato  
**Próxima Revisão:** Após correção
