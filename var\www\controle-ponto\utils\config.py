#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Configurações Centralizadas - Controle de Ponto
-----------------------------------------------

Este módulo centraliza todas as configurações do sistema,
utilizando variáveis de ambiente para máxima segurança.
"""

import os
import secrets
import logging

logger = logging.getLogger('controle-ponto.config')

class Config:
    """
    Classe de configuração que carrega valores de variáveis de ambiente
    com fallbacks seguros para desenvolvimento.
    """
    
    # 🔒 SEGURANÇA CRÍTICA
    SECRET_KEY = os.getenv('SECRET_KEY')
    if not SECRET_KEY:
        # Gera chave aleatória se não estiver definida
        SECRET_KEY = secrets.token_hex(32)
        logger.warning("SECRET_KEY não definida! Usando chave temporária. Configure SECRET_KEY no ambiente de produção!")
    
    FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 🗄️ BANCO DE DADOS
    DB_HOST = os.getenv('DB_HOST', '************')  # Servidor MySQL correto
    DB_USER = os.getenv('DB_USER', 'cavalcrod')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '200381')
    
    # Validação das credenciais
    if not DB_PASSWORD or DB_PASSWORD == '200381':
        logger.info("✅ Usando credenciais configuradas para servidor MySQL remoto")
    
    DB_NAME = os.getenv('DB_NAME', 'controle_ponto')
    
    # 🌐 REDE E SERVIDOR
    FLASK_HOST = os.getenv('FLASK_HOST', '0.0.0.0')
    FLASK_PORT = int(os.getenv('FLASK_PORT', 5000))
    
    # 📊 LOGS E MONITORAMENTO
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_DIR = os.getenv('LOG_DIR', os.path.join(os.path.dirname(__file__), '..', 'logs'))
    LOG_FILE = os.getenv('LOG_FILE', os.path.join(os.path.dirname(__file__), '..', 'logs', 'app.log'))
    
    # 🎯 BIOMETRIA E HARDWARE
    ZK4500_HOST = os.getenv('ZK4500_HOST', 'localhost')
    ZK4500_PORT = int(os.getenv('ZK4500_PORT', 8080))
    ZK4500_TIMEOUT = int(os.getenv('ZK4500_TIMEOUT', 30))
    
    # 🔄 CACHE E PERFORMANCE
    CACHE_TYPE = os.getenv('CACHE_TYPE', 'simple')  # 'redis' para produção
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    
    # 📧 NOTIFICAÇÕES
    SMTP_SERVER = os.getenv('SMTP_SERVER', 'localhost')
    SMTP_PORT = int(os.getenv('SMTP_PORT', 587))
    SMTP_USERNAME = os.getenv('SMTP_USERNAME', '')
    SMTP_PASSWORD = os.getenv('SMTP_PASSWORD', '')
    
    # 🎨 INTERFACE
    ITEMS_PER_PAGE = int(os.getenv('ITEMS_PER_PAGE', 20))
    MAX_UPLOAD_SIZE = int(os.getenv('MAX_UPLOAD_SIZE', 5 * 1024 * 1024))  # 5MB
    MAX_CONTENT_LENGTH = int(os.getenv('MAX_CONTENT_LENGTH', 16 * 1024 * 1024))  # 16MB
    
    # 🔐 SEGURANÇA AVANÇADA
    SESSION_TIMEOUT = int(os.getenv('SESSION_TIMEOUT', 86400))  # 24 horas
    SESSION_LIFETIME = int(os.getenv('SESSION_LIFETIME', 86400))  # 24 horas (alias para compatibilidade)
    MAX_LOGIN_ATTEMPTS = int(os.getenv('MAX_LOGIN_ATTEMPTS', 5))
    REQUIRE_HTTPS = os.getenv('REQUIRE_HTTPS', 'True').lower() == 'true'
    
    @classmethod
    def validate_config(cls):
        """
        Valida configurações críticas e registra avisos de segurança.
        """
        warnings = []
        
        if cls.FLASK_DEBUG:
            warnings.append("🚨 FLASK_DEBUG está ativo! Desative em produção!")
        
        if cls.SECRET_KEY == secrets.token_hex(32):
            warnings.append("🚨 SECRET_KEY temporária em uso! Configure uma chave fixa!")
        
        if cls.DB_PASSWORD in ['controle123', 'controle_password', 'root', '']:
            warnings.append("🚨 Senha de banco padrão em uso! Altere imediatamente!")
        
        if not cls.REQUIRE_HTTPS:
            warnings.append("⚠️ HTTPS não é obrigatório! Considere ativar para produção!")
        
        for warning in warnings:
            logger.warning(warning)
        
        return len(warnings) == 0
    
    @classmethod
    def get_database_url(cls):
        """
        Constrói URL de conexão do banco de dados.
        
        Returns:
            dict: Configuração de conexão
        """
        return {
            'host': cls.DB_HOST,
            'user': cls.DB_USER,
            'password': cls.DB_PASSWORD,
            'database': cls.DB_NAME,
            'charset': 'utf8mb4',
            'port': 3306
        }
    
    @classmethod
    def get_db_config(cls):
        """
        Alias para get_database_url() para compatibilidade com database.py
        
        Returns:
            dict: Configuração de conexão do banco
        """
        return cls.get_database_url()
    
    @classmethod
    def log_config_status(cls):
        """
        Registra status das configurações (sem expor dados sensíveis).
        """
        logger.info("📋 Status das Configurações:")
        logger.info(f"  🔒 SECRET_KEY: {'✅ Configurada' if os.getenv('SECRET_KEY') else '⚠️ Temporária'}")
        logger.info(f"  🐛 DEBUG: {'⚠️ Habilitado' if cls.FLASK_DEBUG else '✅ Desabilitado'}")
        logger.info(f"  🗄️ DB_HOST: {cls.DB_HOST}")
        logger.info(f"  🗄️ DB_USER: {cls.DB_USER}")
        logger.info(f"  🗄️ DB_PASSWORD: {'✅ Configurada' if os.getenv('DB_PASSWORD') else '⚠️ Fallback'}")
        logger.info(f"  🌐 HOST:PORT: {cls.FLASK_HOST}:{cls.FLASK_PORT}")

# Instância global da configuração
config = Config()

# Validar configurações na importação
config.validate_config()

def load_dotenv_file():
    """
    Carrega arquivo .env se existir.
    """
    env_file = os.path.join(os.path.dirname(__file__), '..', '.env')
    if os.path.exists(env_file):
        try:
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ.setdefault(key.strip(), value.strip())
            logger.info("✅ Arquivo .env carregado com sucesso")
        except Exception as e:
            logger.error(f"❌ Erro ao carregar .env: {e}")
    else:
        logger.warning("⚠️ Arquivo .env não encontrado - usando variáveis de ambiente do sistema")

# Carrega configurações automaticamente
load_dotenv_file()

# Valida configurações na importação
try:
    Config.validate_config()
    Config.log_config_status()
except Exception as e:
    logger.error(f"❌ Erro na configuração: {e}")
    # Em desenvolvimento, continua mesmo com erro
    if os.getenv('FLASK_ENV') == 'production':
        raise 