document.addEventListener('DOMContentLoaded', function() {
    // Enviar formulário de adicionar usuário
    const adicionarForm = document.getElementById('adicionar-usuario-form');
    if (adicionarForm) {
        adicionarForm.addEventListener('submit', function(event) {
            event.preventDefault();

            const usuario = document.getElementById('usuario').value;
            const senha = document.getElementById('senha').value;
            const nivelAcesso = document.getElementById('nivel_acesso').value;

            fetch('/adicionar_usuario', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `usuario=${encodeURIComponent(usuario)}&senha=${encodeURIComponent(senha)}&nivel_acesso=${encodeURIComponent(nivelAcesso)}`
            })
            .then(response => response.text())
            .then(html => {
                // Atualizar a página com o novo conteúdo retornado
                document.open();
                document.write(html);
                document.close();
            })
            .catch(error => {
                console.error('Erro ao adicionar usuário:', error);
                alert('Erro ao adicionar usuário. Verifique o console para mais detalhes.');
            });
        });
    }

    // Enviar formulário de troca de senha
    const trocarSenhaForms = document.querySelectorAll('.trocar-senha-form');
    trocarSenhaForms.forEach(form => {
        form.addEventListener('submit', function(event) {
            event.preventDefault();

            const id = form.dataset.id;
            const novaSenha = form.querySelector('.nova-senha').value;

            fetch('/trocar_senha', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `id=${encodeURIComponent(id)}&senha=${encodeURIComponent(novaSenha)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    if (data.kick_user) {
                        window.location.href = '/logout';
                    } else {
                        window.location.reload();
                    }
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Erro ao trocar senha:', error);
                alert('Erro ao trocar senha. Verifique o console para mais detalhes.');
            });
        });
    });

    // Enviar formulário de alterar nível de acesso
    const alterarNivelForms = document.querySelectorAll('.alterar-nivel-form');
    alterarNivelForms.forEach(form => {
        form.addEventListener('submit', function(event) {
            event.preventDefault();

            const id = form.dataset.id;
            const nivel = form.querySelector('.nivel-acesso-select').value;

            fetch('/alterar_nivel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `id=${encodeURIComponent(id)}&nivel=${encodeURIComponent(nivel)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    window.location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Erro ao alterar nível de acesso:', error);
                alert('Erro ao alterar nível de acesso. Verifique o console para mais detalhes.');
            });
        });
    });

    // Enviar formulário de excluir usuário
    const excluirForms = document.querySelectorAll('.excluir-usuario-form');
    excluirForms.forEach(form => {
        form.addEventListener('submit', function(event) {
            event.preventDefault();

            if (!confirm('Tem certeza que deseja excluir este usuário?')) {
                return;
            }

            const id = form.dataset.id;

            fetch('/excluir_usuario', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `id=${encodeURIComponent(id)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    window.location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Erro ao excluir usuário:', error);
                alert('Erro ao excluir usuário. Verifique o console para mais detalhes.');
            });
        });
    });

    // Mostrar/esconder modal de troca de senha
    const modal = document.getElementById('modal-trocar-senha');
    const abrirModalLinks = document.querySelectorAll('.abrir-modal-trocar-senha');
    const fecharModalBtn = document.querySelector('.fechar-modal');

    abrirModalLinks.forEach(link => {
        link.addEventListener('click', function(event) {
            event.preventDefault();
            const id = this.dataset.id;
            const form = document.getElementById('form-trocar-senha');
            form.dataset.id = id;
            modal.style.display = 'block';
        });
    });

    if (fecharModalBtn) {
        fecharModalBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });
    }

    window.addEventListener('click', function(event) {
        if (event.target == modal) {
            modal.style.display = 'none';
        }
    });
});