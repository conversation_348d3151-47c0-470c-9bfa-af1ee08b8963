#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar uma rota de debug temporária
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from flask import Flask, jsonify
from app_empresa_principal import get_empresa_principal, get_clientes_da_empresa_principal, get_empresas_disponiveis_para_cliente

app = Flask(__name__)

@app.route('/debug-clientes')
def debug_clientes():
    """Debug da função de clientes"""
    try:
        result = {
            'status': 'iniciando',
            'steps': []
        }
        
        # Passo 1: Empresa principal
        result['steps'].append('1. Buscando empresa principal...')
        empresa_principal = get_empresa_principal()
        if empresa_principal:
            result['empresa_principal'] = {
                'id': empresa_principal['id'],
                'razao_social': empresa_principal['razao_social'],
                'total_clientes': empresa_principal.get('total_clientes', 0)
            }
            result['steps'].append('✅ Empresa principal encontrada')
        else:
            result['steps'].append('❌ Empresa principal não encontrada')
            return jsonify(result)
        
        # Passo 2: Clientes
        result['steps'].append('2. Buscando clientes...')
        clientes = get_clientes_da_empresa_principal()
        result['clientes_count'] = len(clientes) if clientes else 0
        result['steps'].append(f'✅ {result["clientes_count"]} clientes encontrados')
        
        # Passo 3: Empresas disponíveis
        result['steps'].append('3. Buscando empresas disponíveis...')
        empresas_disponiveis = get_empresas_disponiveis_para_cliente()
        result['empresas_disponiveis_count'] = len(empresas_disponiveis) if empresas_disponiveis else 0
        result['steps'].append(f'✅ {result["empresas_disponiveis_count"]} empresas disponíveis')
        
        if empresas_disponiveis:
            result['empresas_sample'] = [
                {
                    'id': emp['id'],
                    'razao_social': emp['razao_social'],
                    'ja_e_cliente': emp.get('ja_e_cliente', False)
                }
                for emp in empresas_disponiveis[:3]  # Primeiras 3
            ]
        
        # Passo 4: Simular template
        result['steps'].append('4. Simulando dados para template...')
        template_data = {
            'empresa_principal': empresa_principal,
            'clientes': clientes,
            'empresas_disponiveis': empresas_disponiveis
        }
        
        # Verificar se algum campo é None
        for key, value in template_data.items():
            if value is None:
                result['steps'].append(f'❌ {key} é None')
            else:
                result['steps'].append(f'✅ {key} OK')
        
        result['status'] = 'sucesso'
        return jsonify(result)
        
    except Exception as e:
        result['status'] = 'erro'
        result['erro'] = str(e)
        result['steps'].append(f'❌ Erro: {str(e)}')
        return jsonify(result)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
