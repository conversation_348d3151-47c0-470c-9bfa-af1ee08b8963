-- =================================================================
-- RLPONTO-WEB v2.0 - ATUALIZAÇÃO BANCO DE DADOS DEFINITIVA
-- Sistema Biométrico WebUSB - SEM ERROS DE SINTAXE
-- Data: 06/01/2025
-- =================================================================

USE controle_ponto;

SELECT 'Iniciando atualização RLPONTO-WEB v2.0...' as status;

-- ETAPA 1: ADICIONAR COLUNAS registros_ponto
ALTER TABLE registros_ponto ADD COLUMN biometria_verificada BOOLEAN DEFAULT FALSE;
ALTER TABLE registros_ponto ADD COLUMN device_hash VARCHAR(64);
ALTER TABLE registros_ponto ADD COLUMN user_agent TEXT;
ALTER TABLE registros_ponto ADD COLUMN security_score INT DEFAULT 100;
ALTER TABLE registros_ponto ADD COLUMN ip_address VARCHAR(45);

-- ETAPA 2: ADICIONAR COLUNAS funcionarios
ALTER TABLE funcionarios ADD COLUMN biometria_qualidade INT DEFAULT 0;
ALTER TABLE funcionarios ADD COLUMN biometria_data_cadastro TIMESTAMP NULL;
ALTER TABLE funcionarios ADD COLUMN ultimo_login TIMESTAMP NULL;
ALTER TABLE funcionarios ADD COLUMN tentativas_biometria INT DEFAULT 0;
ALTER TABLE funcionarios ADD COLUMN status_biometria ENUM('ativo', 'inativo', 'bloqueado') DEFAULT 'inativo';
ALTER TABLE funcionarios ADD COLUMN status ENUM('ativo', 'inativo', 'suspenso') DEFAULT 'ativo';

-- ETAPA 3: CRIAR TABELA logs_biometria
DROP TABLE IF EXISTS logs_biometria;
CREATE TABLE logs_biometria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    similarity_score DECIMAL(5,4) DEFAULT 0.0000,
    device_info JSON,
    status ENUM('success', 'failed') DEFAULT 'failed',
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_funcionario_timestamp (funcionario_id, timestamp),
    INDEX idx_status_timestamp (status, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ETAPA 4: CRIAR TABELA logs_seguranca
DROP TABLE IF EXISTS logs_seguranca;
CREATE TABLE logs_seguranca (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo_evento VARCHAR(50) NOT NULL,
    funcionario_id INT,
    detalhes JSON,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    nivel_risco ENUM('baixo', 'medio', 'alto') DEFAULT 'baixo',
    INDEX idx_tipo_timestamp (tipo_evento, timestamp),
    INDEX idx_funcionario_timestamp (funcionario_id, timestamp),
    INDEX idx_nivel_risco (nivel_risco)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ETAPA 5: CRIAR TABELA tentativas_biometria
DROP TABLE IF EXISTS tentativas_biometria;
CREATE TABLE tentativas_biometria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_hash VARCHAR(64),
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    device_vendor_id VARCHAR(10),
    device_product_id VARCHAR(10),
    success BOOLEAN DEFAULT FALSE,
    funcionario_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_timestamp (timestamp),
    INDEX idx_success (success),
    INDEX idx_funcionario (funcionario_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ETAPA 6: CRIAR TABELA configuracoes_sistema
DROP TABLE IF EXISTS configuracoes_sistema;
CREATE TABLE configuracoes_sistema (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chave VARCHAR(100) UNIQUE NOT NULL,
    valor TEXT,
    descricao TEXT,
    tipo ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    categoria VARCHAR(50) DEFAULT 'geral',
    criado_em DATETIME DEFAULT CURRENT_TIMESTAMP,
    atualizado_em DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_categoria (categoria),
    INDEX idx_chave (chave)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ETAPA 7: INSERIR CONFIGURAÇÕES PADRÃO
INSERT INTO configuracoes_sistema (chave, valor, descricao, tipo, categoria) VALUES
('biometric_threshold', '0.7', 'Limite mínimo de similaridade biométrica', 'string', 'biometria'),
('security_enabled', 'true', 'Habilitar verificações de segurança', 'boolean', 'seguranca'),
('max_failed_attempts', '5', 'Máximo de tentativas falhadas por hora', 'integer', 'seguranca'),
('device_whitelist', '["1b55:4500"]', 'Lista de dispositivos autorizados', 'json', 'hardware'),
('attendance_tolerance_minutes', '15', 'Tolerância em minutos para pontualidade', 'integer', 'horarios'),
('morning_start', '07:00', 'Início do período matutino', 'string', 'horarios'),
('morning_end', '09:30', 'Fim do período matutino', 'string', 'horarios'),
('lunch_out_start', '11:30', 'Início da saída para almoço', 'string', 'horarios'),
('lunch_out_end', '13:30', 'Fim da saída para almoço', 'string', 'horarios'),
('lunch_return_start', '13:30', 'Início da volta do almoço', 'string', 'horarios'),
('lunch_return_end', '15:00', 'Fim da volta do almoço', 'string', 'horarios'),
('evening_start', '17:00', 'Início do período vespertino', 'string', 'horarios'),
('evening_end', '19:00', 'Fim do período vespertino', 'string', 'horarios');

-- ETAPA 8: CRIAR ÍNDICES OTIMIZADOS
CREATE INDEX idx_registros_ponto_data_funcionario ON registros_ponto(data_hora, funcionario_id);
CREATE INDEX idx_registros_ponto_tipo_status ON registros_ponto(tipo, status);
CREATE INDEX idx_registros_ponto_biometria ON registros_ponto(biometria_verificada);
CREATE INDEX idx_funcionarios_biometria_status ON funcionarios(status_biometria);

-- ETAPA 9: CRIAR VIEWS PARA RELATÓRIOS
DROP VIEW IF EXISTS vw_registros_completos;
CREATE VIEW vw_registros_completos AS
SELECT 
    rp.id,
    rp.funcionario_id,
    f.nome as funcionario_nome,
    f.cpf as funcionario_cpf,
    rp.data_hora,
    rp.tipo,
    rp.status,
    COALESCE(rp.biometria_verificada, FALSE) as biometria_verificada,
    COALESCE(rp.security_score, 100) as security_score,
    CASE 
        WHEN rp.tipo = 'entrada' THEN 'Entrada'
        WHEN rp.tipo = 'saida_almoco' THEN 'Saída Almoço'
        WHEN rp.tipo = 'entrada_almoco' THEN 'Volta Almoço'
        WHEN rp.tipo = 'saida' THEN 'Saída'
        ELSE rp.tipo
    END as tipo_descricao,
    DATE(rp.data_hora) as data_registro,
    TIME(rp.data_hora) as hora_registro
FROM registros_ponto rp
JOIN funcionarios f ON rp.funcionario_id = f.id
WHERE COALESCE(f.status, 'ativo') = 'ativo'
ORDER BY rp.data_hora DESC;

DROP VIEW IF EXISTS vw_estatisticas_biometria;
CREATE VIEW vw_estatisticas_biometria AS
SELECT 
    DATE(lb.timestamp) as data,
    COUNT(*) as total_tentativas,
    SUM(CASE WHEN lb.status = 'success' THEN 1 ELSE 0 END) as sucessos,
    SUM(CASE WHEN lb.status = 'failed' THEN 1 ELSE 0 END) as falhas,
    ROUND(AVG(lb.similarity_score), 4) as score_medio,
    COUNT(DISTINCT lb.funcionario_id) as funcionarios_unicos
FROM logs_biometria lb
WHERE lb.timestamp >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(lb.timestamp)
ORDER BY data DESC;

-- ETAPA 10: CRIAR TRIGGERS (sintaxe limpa)
DELIMITER $$

DROP TRIGGER IF EXISTS tr_registros_ponto_security_log$$
CREATE TRIGGER tr_registros_ponto_security_log
AFTER INSERT ON registros_ponto
FOR EACH ROW
BEGIN
    INSERT INTO logs_seguranca (
        tipo_evento,
        funcionario_id,
        detalhes,
        timestamp,
        nivel_risco
    ) VALUES (
        'registro_ponto',
        NEW.funcionario_id,
        JSON_OBJECT(
            'registro_id', NEW.id,
            'tipo', NEW.tipo,
            'status', NEW.status,
            'biometria_verificada', COALESCE(NEW.biometria_verificada, FALSE),
            'security_score', COALESCE(NEW.security_score, 100)
        ),
        NEW.data_hora,
        CASE 
            WHEN COALESCE(NEW.security_score, 100) < 50 THEN 'alto'
            WHEN COALESCE(NEW.security_score, 100) < 80 THEN 'medio'
            ELSE 'baixo'
        END
    );
END$$

DELIMITER ;

-- ETAPA 11: STORED PROCEDURE PARA LIMPEZA
DELIMITER $$

DROP PROCEDURE IF EXISTS sp_cleanup_old_logs$$
CREATE PROCEDURE sp_cleanup_old_logs()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    DELETE FROM logs_biometria WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY);
    DELETE FROM logs_seguranca WHERE timestamp < DATE_SUB(NOW(), INTERVAL 180 DAY) AND nivel_risco != 'alto';
    DELETE FROM tentativas_biometria WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    COMMIT;
END$$

DELIMITER ;

-- ETAPA 12: ATUALIZAR REGISTROS EXISTENTES
UPDATE registros_ponto 
SET biometria_verificada = TRUE,
    security_score = 100
WHERE funcionario_id IN (
    SELECT id FROM funcionarios 
    WHERE template_biometrico IS NOT NULL 
    AND template_biometrico != ''
);

UPDATE funcionarios 
SET status_biometria = 'ativo',
    biometria_qualidade = 85,
    biometria_data_cadastro = NOW()
WHERE template_biometrico IS NOT NULL 
AND template_biometrico != '';

-- ETAPA 13: LOG FINAL
INSERT INTO logs_seguranca (
    tipo_evento,
    funcionario_id,
    detalhes,
    timestamp,
    nivel_risco
) VALUES (
    'database_update',
    NULL,
    JSON_OBJECT(
        'version', '2.0',
        'description', 'Sistema biométrico WebUSB integrado',
        'features', JSON_ARRAY(
            'WebUSB API direct hardware access',
            'ZK4500 biometric device support',
            'Real-time template comparison',
            'Automatic attendance type detection',
            'Enhanced security audit logs'
        )
    ),
    NOW(),
    'baixo'
);

-- VERIFICAÇÃO FINAL
SELECT 'RLPONTO-WEB v2.0 ATUALIZADO COM SUCESSO!' as status;
SELECT COUNT(*) as funcionarios_com_biometria FROM funcionarios WHERE template_biometrico IS NOT NULL;
SELECT COUNT(*) as novas_configuracoes FROM configuracoes_sistema;

COMMIT; 