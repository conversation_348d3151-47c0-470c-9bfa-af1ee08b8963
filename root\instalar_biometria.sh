#!/bin/bash

# Script de instalação do serviço de biometria para o sistema de Controle de Ponto
# Este script configura o serviço de biometria ZK4500 para funcionar em um container LXC no Proxmox

echo "🔧 Instalando serviço de biometria ZK4500..."

# Verifica se está rodando como root
if [ "$EUID" -ne 0 ]; then
  echo "❌ Este script precisa ser executado como root"
  exit 1
fi

# Diretórios
APP_DIR="/var/www/controle-ponto"
LOG_DIR="/var/log/controle-ponto"
SERVICE_DIR="/etc/systemd/system"

# Cria diretório de logs se não existir
mkdir -p "$LOG_DIR"
chmod 755 "$LOG_DIR"

# Instala dependências
echo "📦 Instalando dependências..."
apt-get update
apt-get install -y python3-pip python3-dev libusb-1.0-0-dev libudev-dev

# Instala pacotes Python necessários
echo "📦 Instalando pacotes Python..."
pip3 install websockets pyusb

# Copia o serviço de biometria para o diretório da aplicação
echo "📄 Copiando arquivos do serviço..."
cp biometria_service.py "$APP_DIR/"
chmod +x "$APP_DIR/biometria_service.py"

# Cria diretório para arquivos estáticos se não existir
mkdir -p "$APP_DIR/static"
cp static/biometria-service.js "$APP_DIR/static/"
cp static/images/fingerprint-placeholder.png "$APP_DIR/static/images/"
cp static/images/fingerprint-captured.png "$APP_DIR/static/images/"

# Configura o serviço systemd
echo "🔧 Configurando serviço systemd..."
cat > "$SERVICE_DIR/biometria.service" << EOF
[Unit]
Description=Serviço de Biometria ZK4500
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/python3 $APP_DIR/biometria_service.py
Restart=always
RestartSec=5
StandardOutput=append:$LOG_DIR/biometria.log
StandardError=append:$LOG_DIR/biometria.log

[Install]
WantedBy=multi-user.target
EOF

# Configura regras udev para acesso ao dispositivo USB
echo "🔧 Configurando regras udev para o leitor biométrico..."
cat > "/etc/udev/rules.d/99-zkfp.rules" << EOF
# ZKTeco Fingerprint Reader
SUBSYSTEM=="usb", ATTRS{idVendor}=="1b55", ATTRS{idProduct}=="0120", MODE="0666", GROUP="www-data"
EOF

# Recarrega regras udev
echo "🔄 Recarregando regras udev..."
udevadm control --reload-rules
udevadm trigger

# Configura permissões
echo "🔒 Configurando permissões..."
chown -R www-data:www-data "$APP_DIR"
chown -R www-data:www-data "$LOG_DIR"
chmod 644 "$SERVICE_DIR/biometria.service"

# Habilita e inicia o serviço
echo "🚀 Habilitando e iniciando o serviço..."
systemctl daemon-reload
systemctl enable biometria.service
systemctl start biometria.service

echo "✅ Instalação do serviço de biometria concluída!"
echo ""
echo "Para verificar o status do serviço:"
echo "  systemctl status biometria.service"
echo ""
echo "Para visualizar os logs:"
echo "  tail -f $LOG_DIR/biometria.log"
echo ""
echo "IMPORTANTE: Para que o leitor biométrico funcione em um container LXC no Proxmox,"
echo "você precisa configurar o passthrough USB no host Proxmox. Instruções:"
echo ""
echo "1. No host Proxmox, identifique o dispositivo USB:"
echo "   lsusb | grep -i zk"
echo ""
echo "2. Adicione a configuração de passthrough ao container LXC editando o arquivo de configuração:"
echo "   nano /etc/pve/lxc/SEU_CONTAINER_ID.conf"
echo ""
echo "3. Adicione as seguintes linhas (substituindo os IDs pelo seu dispositivo):"
echo "   lxc.cgroup2.devices.allow: c 189:* rwm"
echo "   lxc.mount.entry: /dev/bus/usb/001/003 dev/bus/usb/001/003 none bind,optional,create=file"
echo ""
echo "4. Reinicie o container:"
echo "   pct restart SEU_CONTAINER_ID"
echo ""
echo "5. Verifique se o dispositivo está disponível dentro do container:"
echo "   lsusb | grep -i zk"
