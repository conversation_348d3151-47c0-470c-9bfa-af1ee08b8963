from utils.database import DatabaseManager\n\nprint('Removendo empresas...')\nDatabaseManager.execute_query('SET FOREIGN_KEY_CHECKS = 0')\nDatabaseManager.execute_query('DELETE FROM epis')\nDatabaseManager.execute_query('DELETE FROM funcionarios')\nDatabaseManager.execute_query('DELETE FROM empresas')\nDatabaseManager.execute_query('ALTER TABLE empresas AUTO_INCREMENT = 1')\nDatabaseManager.execute_query('SET FOREIGN_KEY_CHECKS = 1')\nprint('Concluído!')
