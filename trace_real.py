import sys
import traceback

print("=== TRACE REAL CONFIGURAÇÕES ===")

try:
    # Teste 1: Imports
    print("1. Testando imports...")
    import app_configuracoes
    print("✅ app_configuracoes importado")
    
    from app import app
    print("✅ app principal importado")
    
    # Teste 2: Funções
    print("\n2. Testando funções...")
    configs = app_configuracoes.obter_configuracoes_por_categoria('sistema')
    print(f"✅ {len(configs)} configurações encontradas")
    
    # Teste 3: Rota
    print("\n3. Testando rota...")
    client = app.test_client()
    
    # Com admin simulado
    with client.session_transaction() as sess:
        sess['usuario'] = 'admin'
        sess['nivel_acesso'] = 'admin'
    
    response = client.get('/configuracoes/')
    print(f"Status: {response.status_code}")
    
    if response.status_code == 500:
        print("❌ ERRO 500 CONFIRMADO!")
        error_content = response.data.decode('utf-8', errors='ignore')
        print("Conteúdo do erro:")
        print(error_content[:1000])
    elif response.status_code == 200:
        print("✅ PÁGINA CARREGOU COM SUCESSO!")
    else:
        print(f"⚠️ Status: {response.status_code}")
        
except Exception as e:
    print(f"❌ ERRO: {e}")
    traceback.print_exc() 