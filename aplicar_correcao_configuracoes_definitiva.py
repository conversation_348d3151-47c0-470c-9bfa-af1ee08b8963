#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Aplicar Correção Definitiva - Tabela configuracoes_sistema
========================================================

Script para aplicar a correção definitiva da tabela configuracoes_sistema
no servidor de produção, seguindo as diretrizes do documento de correções.

Desenvolvido por: Claude AI Assistant
Data: 09/06/2025 
Projeto: RLPONTO-WEB v1.0
"""

import pymysql
import logging
from datetime import datetime
import sys
import os

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('aplicacao_configuracoes.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuração do banco (usando as credenciais do servidor de produção)
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4',
    'port': 3306
}

def conectar_banco():
    """
    Conecta ao banco de dados MySQL
    """
    try:
        conn = pymysql.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset=DB_CONFIG['charset'],
            port=DB_CONFIG['port'],
            cursorclass=pymysql.cursors.DictCursor
        )
        logger.info(f"✅ Conectado ao MySQL em {DB_CONFIG['host']}:{DB_CONFIG['port']}")
        return conn
    except Exception as e:
        logger.error(f"❌ Erro na conexão: {e}")
        return None

def verificar_tabela_existe(cursor):
    """
    Verifica se a tabela configuracoes_sistema já existe
    """
    try:
        cursor.execute("""
            SELECT COUNT(*) as count 
            FROM information_schema.tables 
            WHERE table_schema = %s 
            AND table_name = 'configuracoes_sistema'
        """, (DB_CONFIG['database'],))
        
        result = cursor.fetchone()
        return result['count'] > 0
    except Exception as e:
        logger.error(f"❌ Erro ao verificar tabela: {e}")
        return False

def criar_tabela_configuracoes(cursor):
    """
    Cria a tabela configuracoes_sistema
    """
    sql_create = """
    CREATE TABLE IF NOT EXISTS configuracoes_sistema (
      id int NOT NULL AUTO_INCREMENT,
      chave varchar(100) NOT NULL,
      valor text,
      tipo enum('string', 'integer', 'boolean', 'time', 'json') NOT NULL DEFAULT 'string',
      categoria varchar(50) DEFAULT 'geral',
      descricao varchar(255) DEFAULT NULL,
      editavel tinyint(1) DEFAULT 1,
      data_criacao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
      data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (id),
      UNIQUE KEY uk_configuracoes_chave (chave),
      KEY idx_configuracoes_categoria (categoria),
      KEY idx_configuracoes_editavel (editavel)
    )
    ENGINE = INNODB,
    AUTO_INCREMENT = 1,
    CHARACTER SET utf8mb4,
    COLLATE utf8mb4_0900_ai_ci,
    ROW_FORMAT = DYNAMIC
    """
    
    try:
        cursor.execute(sql_create)
        logger.info("✅ Tabela configuracoes_sistema criada com sucesso")
        return True
    except Exception as e:
        logger.error(f"❌ Erro ao criar tabela: {e}")
        return False

def inserir_configuracoes_iniciais(cursor):
    """
    Insere as configurações iniciais na tabela
    """
    configuracoes = [
        ('formato_data', 'dd/mm/yyyy', 'string', 'sistema', 'Formato de exibição de datas no sistema', 1),
        ('fuso_horario', 'America/Manaus', 'string', 'sistema', 'Fuso horário padrão do sistema', 1),
        ('timeout_sessao', '86400', 'integer', 'seguranca', 'Tempo limite de sessão em segundos', 1),
        ('max_tentativas_login', '5', 'integer', 'seguranca', 'Máximo de tentativas de login', 1),
        ('backup_automatico', 'true', 'boolean', 'sistema', 'Habilitar backup automático', 1),
        ('horario_backup', '02:00:00', 'time', 'sistema', 'Horário para execução do backup automático', 1),
        ('tolerancia_ponto_minutos', '15', 'integer', 'ponto', 'Tolerância em minutos para registro de ponto', 1),
        ('email_notificacoes', 'true', 'boolean', 'notificacoes', 'Habilitar notificações por email', 1),
        ('smtp_servidor', 'localhost', 'string', 'email', 'Servidor SMTP para envio de emails', 1),
        ('smtp_porta', '587', 'integer', 'email', 'Porta do servidor SMTP', 1),
        ('versao_sistema', '1.0', 'string', 'sistema', 'Versão atual do sistema', 0),
        ('modo_debug', 'false', 'boolean', 'desenvolvimento', 'Modo de depuração ativo', 1),
        ('cache_habilitado', 'true', 'boolean', 'performance', 'Cache de consultas habilitado', 1),
        ('log_nivel', 'INFO', 'string', 'sistema', 'Nível de log do sistema', 1),
        ('biometria_qualidade_minima', '60', 'integer', 'biometria', 'Qualidade mínima exigida para biometria', 1)
    ]
    
    sql_insert = """
    INSERT IGNORE INTO configuracoes_sistema(chave, valor, tipo, categoria, descricao, editavel) 
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    
    try:
        contador = 0
        for config in configuracoes:
            cursor.execute(sql_insert, config)
            if cursor.rowcount > 0:
                contador += 1
                logger.info(f"✅ Inserida configuração: {config[0]} = {config[1]}")
        
        logger.info(f"✅ {contador} configurações inseridas com sucesso")
        return True
    except Exception as e:
        logger.error(f"❌ Erro ao inserir configurações: {e}")
        return False

def verificar_configuracao_formato_data(cursor):
    """
    Verifica especificamente se a configuração formato_data existe
    """
    try:
        cursor.execute("""
            SELECT * FROM configuracoes_sistema 
            WHERE chave = 'formato_data'
        """)
        
        result = cursor.fetchone()
        if result:
            logger.info(f"✅ Configuração 'formato_data' encontrada: {result['valor']}")
            return True
        else:
            logger.warning("⚠️ Configuração 'formato_data' NÃO encontrada")
            return False
    except Exception as e:
        logger.error(f"❌ Erro ao verificar formato_data: {e}")
        return False

def registrar_log_sistema(cursor):
    """
    Registra a operação no log do sistema
    """
    try:
        cursor.execute("""
            INSERT INTO logs_sistema (acao, tabela_afetada, detalhes, data_hora)
            VALUES (%s, %s, %s, %s)
        """, (
            'CRIACAO_TABELA_CONFIGURACOES',
            'configuracoes_sistema',
            '{"operacao": "correcao_critica", "tabela_criada": "configuracoes_sistema", "configuracoes_inseridas": 15, "responsavel": "Claude AI Assistant", "documento_referencia": "Correção de Falhas do Sistema.markdown"}',
            datetime.now()
        ))
        
        logger.info("✅ Operação registrada no log do sistema")
        return True
    except Exception as e:
        logger.error(f"❌ Erro ao registrar log: {e}")
        return False

def main():
    """
    Função principal
    """
    logger.info("🚀 Iniciando aplicação da correção definitiva - configuracoes_sistema")
    logger.info("📋 Correção baseada no documento: 'Correção de Falhas do Sistema.markdown'")
    logger.info(f"🎯 Servidor de destino: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    
    # Conectar ao banco
    conn = conectar_banco()
    if not conn:
        logger.error("❌ Falha na conexão. Abortando operação.")
        return False
    
    try:
        with conn.cursor() as cursor:
            # Verificar se tabela já existe
            logger.info("🔍 Verificando se tabela configuracoes_sistema existe...")
            tabela_existe = verificar_tabela_existe(cursor)
            
            if tabela_existe:
                logger.info("ℹ️ Tabela configuracoes_sistema já existe")
            else:
                logger.info("⚠️ Tabela configuracoes_sistema NÃO existe - criando...")
                if not criar_tabela_configuracoes(cursor):
                    return False
            
            # Inserir configurações
            logger.info("📝 Inserindo configurações iniciais...")
            if not inserir_configuracoes_iniciais(cursor):
                return False
            
            # Verificar configuração crítica
            logger.info("🔍 Verificando configuração crítica 'formato_data'...")
            if not verificar_configuracao_formato_data(cursor):
                return False
            
            # Registrar no log
            logger.info("📋 Registrando operação no log do sistema...")
            if not registrar_log_sistema(cursor):
                return False
            
            # Commit das alterações
            conn.commit()
            logger.info("✅ Todas as alterações foram aplicadas com sucesso!")
            
            # Verificação final
            logger.info("🔍 Executando verificação final...")
            cursor.execute("SELECT COUNT(*) as total FROM configuracoes_sistema")
            total = cursor.fetchone()['total']
            logger.info(f"📊 Total de configurações na tabela: {total}")
            
            if total >= 15:
                logger.info("✅ CORREÇÃO CONCLUÍDA COM SUCESSO!")
                logger.info("🎯 Problema crítico resolvido: Erro 500 nas configurações")
                return True
            else:
                logger.warning(f"⚠️ Esperadas 15 configurações, encontradas {total}")
                return False
                
    except Exception as e:
        logger.error(f"❌ Erro durante a execução: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
        logger.info("🔌 Conexão com banco de dados fechada")

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("RLPONTO-WEB v1.0 - Correção Crítica configuracoes_sistema")
    logger.info("=" * 60)
    
    sucesso = main()
    
    logger.info("=" * 60)
    if sucesso:
        logger.info("🎉 CORREÇÃO APLICADA COM SUCESSO!")
        logger.info("✅ Erro 500 das configurações foi resolvido")
        logger.info("📋 Vide backup-build/backup-log.md para detalhes")
        sys.exit(0)
    else:
        logger.error("❌ FALHA NA APLICAÇÃO DA CORREÇÃO")
        logger.error("🔧 Verifique os logs e tente novamente")
        sys.exit(1) 