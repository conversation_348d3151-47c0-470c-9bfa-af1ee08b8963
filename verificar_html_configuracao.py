#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Verificação do HTML da Página de Configuração - RLPONTO-WEB
===========================================================

Script para verificar o HTML gerado pela página de configuração
e identificar por que os elementos de alteração não aparecem.

Data: 07/07/2025
"""

import requests
from bs4 import BeautifulSoup

# Configurações
BASE_URL = "http://************"
LOGIN_URL = f"{BASE_URL}/login"
CONFIGURAR_USUARIOS_URL = f"{BASE_URL}/configurar_usuarios"

def analisar_html_configuracao():
    """Analisa o HTML da página de configuração"""
    print("🔍 ANÁLISE DO HTML DA PÁGINA DE CONFIGURAÇÃO")
    print("=" * 60)
    
    # Criar sessão e fazer login
    session = requests.Session()
    
    # Login
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    
    if response.status_code not in [200, 302]:
        print(f"❌ Falha no login: {response.status_code}")
        return
    
    print("✅ Login realizado com sucesso")
    
    # Acessar página de configuração
    response = session.get(CONFIGURAR_USUARIOS_URL)
    
    if response.status_code != 200:
        print(f"❌ Falha ao acessar configuração: {response.status_code}")
        return
    
    print("✅ Página de configuração acessada")
    
    # Analisar HTML
    soup = BeautifulSoup(response.text, 'html.parser')
    
    print("\n📋 ANÁLISE DOS ELEMENTOS:")
    print("-" * 40)
    
    # Verificar tabela de usuários
    tabela = soup.find('table')
    if tabela:
        print("✅ Tabela de usuários encontrada")
        
        # Verificar linhas da tabela
        linhas = tabela.find_all('tr')
        print(f"   Total de linhas: {len(linhas)}")
        
        # Analisar cada linha (exceto cabeçalho)
        for i, linha in enumerate(linhas[1:], 1):  # Pular cabeçalho
            colunas = linha.find_all('td')
            if len(colunas) >= 3:
                user_id = colunas[0].get_text(strip=True)
                usuario = colunas[1].get_text(strip=True)
                nivel_col = colunas[2]
                
                print(f"\n   Linha {i}: ID={user_id}, Usuário={usuario}")
                
                # Verificar se há select de nível
                select_nivel = nivel_col.find('select', class_='level-select')
                if select_nivel:
                    print(f"      ✅ Select de nível encontrado")
                    print(f"      onchange: {select_nivel.get('onchange', 'N/A')}")
                    
                    # Verificar opções
                    opcoes = select_nivel.find_all('option')
                    for opcao in opcoes:
                        valor = opcao.get('value')
                        texto = opcao.get_text(strip=True)
                        selected = 'selected' if opcao.get('selected') else ''
                        print(f"         - {valor}: {texto} {selected}")
                else:
                    # Verificar se é admin padrão
                    badge = nivel_col.find('span', class_='badge')
                    if badge:
                        print(f"      ℹ️ Badge encontrado: {badge.get_text(strip=True)}")
                    else:
                        print(f"      ❌ Nenhum elemento de nível encontrado")
                        print(f"      HTML da coluna: {nivel_col}")
    else:
        print("❌ Tabela de usuários não encontrada")
    
    # Verificar JavaScript
    print("\n📋 VERIFICAÇÃO DO JAVASCRIPT:")
    print("-" * 40)
    
    scripts = soup.find_all('script')
    alterarNivel_encontrado = False
    
    for script in scripts:
        if script.string and 'alterarNivel' in script.string:
            alterarNivel_encontrado = True
            print("✅ Função alterarNivel encontrada no JavaScript")
            break
    
    if not alterarNivel_encontrado:
        print("❌ Função alterarNivel não encontrada no JavaScript")
    
    # Salvar HTML para análise
    with open('configuracao_debug.html', 'w', encoding='utf-8') as f:
        f.write(response.text)
    print("\n📁 HTML salvo em 'configuracao_debug.html' para análise detalhada")

if __name__ == "__main__":
    analisar_html_configuracao()
