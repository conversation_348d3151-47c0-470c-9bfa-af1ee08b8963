#!/usr/bin/env python3
"""
Debug simples para EPIs.
"""

def testar_logica_extracao():
    """
    Testa apenas a lógica de extração.
    """
    print("🔍 TESTE: Lógica de extração de EPIs")
    print("=" * 50)
    
    # Dados de teste
    form_data = {
        'epis[0][epi_nome]': 'Capacete de Segurança',
        'epis[0][epi_ca]': '12345',
        'epis[1][epi_nome]': 'Luvas de Proteção',
        'epis[1][epi_ca]': '67890',
    }
    
    epis = []
    
    print(f"📋 Dados de entrada:")
    for key, value in form_data.items():
        print(f"   {key}: {value}")
    
    print(f"\n🔄 Processando campos...")
    
    for key in form_data.keys():
        print(f"\n🔍 Processando chave: {key}")
        
        # Verificar condição
        condicao1 = key.startswith('epis[')
        condicao2 = '][' in key
        print(f"   startswith('epis['): {condicao1}")
        print(f"   '][' in key: {condicao2}")
        
        if condicao1 and condicao2:
            print(f"   ✅ Condição atendida - processando...")
            
            try:
                # Extrair índice
                start = key.find('[') + 1
                end = key.find(']')
                index_str = key[start:end]
                index = int(index_str)
                print(f"   📍 Índice extraído: '{index_str}' -> {index}")
                
                # Extrair campo
                campo_start = key.find('][') + 2
                campo_end = key.rfind(']')
                campo = key[campo_start:campo_end]
                print(f"   📝 Campo extraído: '{campo}'")
                
                # Garantir que existe EPI no índice
                while len(epis) <= index:
                    epis.append({})
                    print(f"   📦 Criado EPI vazio no índice {len(epis)-1}")
                
                # Adicionar valor
                valor = form_data.get(key, '').strip()
                epis[index][campo] = valor
                print(f"   ✅ Adicionado: epis[{index}][{campo}] = '{valor}'")
                
            except Exception as e:
                print(f"   ❌ Erro: {e}")
        else:
            print(f"   ❌ Condição não atendida - ignorando")
    
    print(f"\n📦 EPIs finais: {len(epis)}")
    for i, epi in enumerate(epis):
        print(f"   EPI {i}: {epi}")
    
    # Testar validação
    print(f"\n🔍 Testando validação...")
    epis_validos = []
    for i, epi in enumerate(epis):
        epi_nome = epi.get('epi_nome', '').strip()
        print(f"   EPI {i}: nome='{epi_nome}' (válido: {bool(epi_nome)})")
        
        if epi_nome:
            epis_validos.append(epi)
    
    print(f"\n✅ EPIs válidos: {len(epis_validos)}")
    return epis_validos

if __name__ == "__main__":
    testar_logica_extracao()
