#!/usr/bin/env python3
"""
Debug do erro na edição de funcionários
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def debug_edicao_funcionario():
    """Debug da edição de funcionário"""
    print("🔍 DEBUG: ERRO NA EDIÇÃO DE FUNCIONÁRIOS")
    print("=" * 60)
    
    try:
        # 1. Verificar se o funcionário existe
        print("📋 1. VERIFICANDO FUNCIONÁRIO RICHARDSON:")
        funcionario = DatabaseManager.execute_query(
            "SELECT * FROM funcionarios WHERE id = 1"
        )
        
        if funcionario:
            func = funcionario[0]
            print(f"   ✅ Funcionário encontrado: {func['nome_completo']}")
            print(f"   🏢 Empresa ID: {func['empresa_id']}")
            print(f"   📷 Foto: {func.get('foto_3x4', 'N/A')}")
        else:
            print("   ❌ Funcionário não encontrado")
            return
            
        # 2. Testar função de busca de jornada
        print(f"\n🕐 2. TESTANDO BUSCA DE JORNADA DA EMPRESA:")
        empresa_id = func['empresa_id']
        
        # Simular a função _buscar_jornada_empresa_funcionario
        sql = """
        SELECT 
            jt.id,
            jt.nome_jornada,
            jt.seg_qui_entrada,
            jt.seg_qui_saida,
            jt.sexta_entrada,
            jt.sexta_saida,
            jt.intervalo_inicio,
            jt.intervalo_fim,
            jt.tolerancia_entrada_minutos,
            jt.padrao,
            e.razao_social as empresa_nome
        FROM jornadas_trabalho jt
        JOIN empresas e ON jt.empresa_id = e.id
        WHERE jt.empresa_id = %s AND jt.padrao = 1 AND jt.ativa = 1
        LIMIT 1
        """
        
        jornada = DatabaseManager.execute_query(sql, (empresa_id,))
        
        if jornada:
            j = jornada[0]
            print(f"   ✅ Jornada encontrada: {j['nome_jornada']}")
            print(f"   🏢 Empresa: {j['empresa_nome']}")
            print(f"   ⏰ Horário: {j['seg_qui_entrada']} - {j['seg_qui_saida']}")
        else:
            print(f"   ❌ Nenhuma jornada encontrada para empresa {empresa_id}")
            
        # 3. Verificar EPIs
        print(f"\n🦺 3. VERIFICANDO EPIs:")
        epis = DatabaseManager.execute_query(
            "SELECT * FROM epis WHERE funcionario_id = 1"
        )
        print(f"   📋 EPIs encontrados: {len(epis) if epis else 0}")
        
        # 4. Verificar empresas disponíveis
        print(f"\n🏢 4. VERIFICANDO EMPRESAS DISPONÍVEIS:")
        empresas = DatabaseManager.execute_query(
            "SELECT id, razao_social FROM empresas WHERE ativa = 1"
        )
        print(f"   📋 Empresas ativas: {len(empresas) if empresas else 0}")
        
        # 5. Simular carregamento completo
        print(f"\n🔄 5. SIMULANDO CARREGAMENTO COMPLETO:")
        
        # Conversão de campos de horário (como no código original)
        campos_horario = ['jornada_seg_qui_entrada', 'jornada_seg_qui_saida', 
                         'jornada_sex_entrada', 'jornada_sex_saida',
                         'jornada_intervalo_entrada', 'jornada_intervalo_saida']
        
        for campo in campos_horario:
            valor = func.get(campo)
            if valor is not None:
                try:
                    # Tentar converter para string HH:MM
                    if hasattr(valor, 'strftime'):
                        func[campo] = valor.strftime('%H:%M')
                    else:
                        func[campo] = str(valor)
                    print(f"   ✅ {campo}: {func[campo]}")
                except Exception as e:
                    print(f"   ⚠️ Erro ao converter {campo}: {e}")
                    func[campo] = ''
            else:
                func[campo] = ''
                print(f"   ⚠️ {campo}: vazio")
                
        print(f"\n✅ SIMULAÇÃO CONCLUÍDA SEM ERROS")
        return True
        
    except Exception as e:
        print(f"❌ ERRO DURANTE DEBUG: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_foto_funcionario():
    """Debug específico da foto"""
    print(f"\n📷 DEBUG: FOTO DO FUNCIONÁRIO")
    print("=" * 40)
    
    try:
        # Verificar campo foto_3x4
        funcionario = DatabaseManager.execute_query(
            "SELECT id, nome_completo, foto_3x4 FROM funcionarios WHERE id = 1"
        )[0]
        
        foto = funcionario.get('foto_3x4')
        print(f"   👤 Funcionário: {funcionario['nome_completo']}")
        print(f"   📷 Campo foto_3x4: {type(foto)} - {len(foto) if foto else 0} bytes")
        
        if foto:
            print(f"   ✅ Foto existe no banco de dados")
            # Verificar se é base64 ou binário
            if isinstance(foto, str) and foto.startswith('data:image'):
                print(f"   📋 Formato: Base64 Data URL")
            elif isinstance(foto, (bytes, bytearray)):
                print(f"   📋 Formato: Dados binários")
            else:
                print(f"   ⚠️ Formato desconhecido: {type(foto)}")
        else:
            print(f"   ❌ Nenhuma foto encontrada")
            
    except Exception as e:
        print(f"❌ Erro ao verificar foto: {e}")

if __name__ == "__main__":
    sucesso = debug_edicao_funcionario()
    debug_foto_funcionario()
    print(f"\n{'✅ DEBUG CONCLUÍDO' if sucesso else '❌ DEBUG COM ERROS'}")
