# 🌐 Simulador Biométrico ZK4500 - HTTP Server

**Desenvolvido para AiNexus Tecnologia**  
**Autor:** <PERSON>  
**Versão:** 1.0-SIMULADOR-HTTP

---

## 🎯 **Objetivo**

Simular capturas biométricas para testar a integração do frontend com o ZKAgent **sem precisar do hardware real** ZK4500.

O simulador funciona como servidor HTTP na **mesma porta 5001** que o ZKAgent real, permitindo alternar entre desenvolvimento (simulado) e produção (real) facilmente.

---

## 🚀 **Como Usar - 2 Cliques!**

### **Iniciar Simulador**
```bash
# Duplo clique no arquivo:
simulacao/iniciar-simulador.bat
```

**O script faz tudo automaticamente:**
- ✅ Detecta se ZKAgent real está rodando
- ❓ Pergunta se quer parar ZKAgent e usar simulador
- 🔄 Libera porta 5001 automaticamente
- 🚀 Inicia simulador HTTP
- ✅ Frontend funciona normalmente!

---

## 📡 **Endpoints Compatíveis**

### **GET Endpoints**
- `/ping` - Verifica se está funcionando
- `/status` - Status detalhado do simulador
- `/list-devices` - Lista dispositivos simulados
- `/device-info` - Informações do dispositivo
- `/test` - Teste básico

### **POST Endpoints**
- `/capture` - **Principal** - Captura biometria simulada

---

## 🔄 **Como Funciona**

### **1. Frontend faz requisição** 
```javascript
// Frontend chama (igual ao ZKAgent real):
fetch('http://localhost:5001/capture', {method: 'POST'})
```

### **2. Simulador responde**
```json
{
  "success": true,
  "identified": true,
  "user": {"name": "João Silva", "role": "Desenvolvedor"},
  "quality": 87,
  "device": "ZK4500-SIMULADO",
  "simulationMode": true
}
```

### **3. Frontend detecta simulação**
- ✅ **Aceita** nosso simulador específico
- 🌐 Mostra interface "modo simulação"
- ❌ **Rejeita** outros simuladores maliciosos

---

## 📁 **Estrutura Limpa**

```
simulacao/
├── iniciar-simulador.bat     # ⭐ PRINCIPAL - Inicia tudo
├── simulador-servidor.py     # Servidor HTTP
├── usuarios_simulados.json   # Dados dos usuários
├── capturas_servidor_log.txt # Logs automáticos
└── README.md                 # Esta documentação
```

---

## 👥 **Usuários Pré-cadastrados**

O simulador vem com 5 usuários para testes:

1. **João Silva** - Desenvolvedor
2. **Maria Santos** - Analista  
3. **Pedro Costa** - Gerente
4. **Ana Oliveira** - Designer
5. **Carlos Lima** - Suporte

**70% das capturas** identificam usuários existentes  
**30% das capturas** retornam "não identificado"

---

## 🔧 **Recursos Avançados**

### **Liberação Automática de Porta**
- ✅ Detecta conflitos na porta 5001
- ✅ Para processos automaticamente  
- ✅ Libera porta mesmo se fechado pelo "X"
- ✅ Handlers para Ctrl+C, Ctrl+Break, etc.

### **Qualidade Realista**
- 🎯 Capturas: 75-95% qualidade
- ⚠️ Falhas ocasionais (15% das vezes)
- 📊 Logs detalhados de todas operações

### **Compatibilidade Total**
- ✅ Mesmos endpoints do ZKAgent
- ✅ Mesmos códigos de resposta
- ✅ Headers CORS configurados
- ✅ Frontend funciona sem alterações

---

## 🔄 **Alternando Simulador ↔ ZKAgent Real**

### **Para usar SIMULADOR:**
```bash
# Execute:
simulacao/iniciar-simulador.bat
```

### **Para usar ZKAgent REAL:**
```bash
# Feche o simulador (Ctrl+C ou X)
# Inicie o ZKAgent real normalmente
```

**Ambos usam a mesma porta 5001** - nunca funcionam juntos!

---

## 📊 **Logs e Monitoramento**

### **Logs em Tempo Real**
- 📡 Requisições: `GET /ping -> 200 OK`
- 📸 Capturas: `✅ Identificado: João Silva (87%)`
- ❌ Erros: `⚠️ Falha simulada na captura`

### **Arquivo de Log**
```
capturas_servidor_log.txt
[2025-06-05 08:45:12] CAPTURE: {"success": true, "user": "João Silva"}
```

---

## 🛠️ **Solução de Problemas**

### **Porta 5001 em uso**
```bash
# O script detecta automaticamente e oferece soluções
```

### **Python não encontrado**
```bash
# Instale Python 3.6+ de https://www.python.org/downloads/
```

### **Simulador não inicia**
```bash
# Verifique se está na pasta correta
# Execute: simulacao/iniciar-simulador.bat
```

---

## 📞 **Suporte**

**Desenvolvedor:** Richardson Rodrigues  
**Empresa:** AiNexus Tecnologia  
**GitHub:** https://github.com/cavalcrod200381/RLPONTO-WEB

---

## ✨ **Status do Projeto**

- ✅ **Simulador HTTP funcionando**
- ✅ **Frontend compatível 100%**
- ✅ **Detecção automática de conflitos**
- ✅ **Liberação automática de porta**
- ✅ **Interface simplificada (2 cliques)**

**🎉 Simulador pronto para desenvolvimento!** 