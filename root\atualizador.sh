#!/bin/bash

# Configuração do log
LOG_DIR="/var/log/controle-ponto"
LOG_FILE="$LOG_DIR/atualizacoes.log"
APP_LOG="$LOG_DIR/app.log"
BIOMETRIA_LOG="$LOG_DIR/biometria.log"
[ ! -d "$LOG_DIR" ] && mkdir -p "$LOG_DIR" && chown www-data:www-data "$LOG_DIR" && chmod 775 "$LOG_DIR"
[ ! -f "$LOG_FILE" ] && touch "$LOG_FILE" && chown root:root "$LOG_FILE" && chmod 664 "$LOG_FILE"
[ ! -f "$APP_LOG" ] && touch "$APP_LOG" && chown www-data:www-data "$APP_LOG" && chmod 664 "$APP_LOG"
[ ! -f "$BIOMETRIA_LOG" ] && touch "$BIOMETRIA_LOG" && chown www-data:www-data "$BIOMETRIA_LOG" && chmod 664 "$BIOMETRIA_LOG"
log() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $1" | tee -a "$LOG_FILE"
}

log "🚧 Iniciando atualizações do sistema de controle de ponto..."

# Definir diretórios
BASE_DIR="/var/www/controle-ponto"
UPDATE_DIR="/root/atualizacoes"

# Funções de atualização
atualizar_layout() {
    log "📄 Atualizando layout..."
    for file in style.css style-login.css style-cadastrar.css style-config-usuarios.css style-index.css style-leitor-biometrico.css index.html cadastrar.html login.html configurar_usuarios.html leitor_biometrico.html busca_cep.js biometria-service.js; do
        src="$UPDATE_DIR/$file"
        if [[ "$file" == *.css || "$file" == *.js ]]; then
            dest="$BASE_DIR/static/$file"
        else
            dest="$BASE_DIR/templates/$file"
        fi
        if [ -f "$src" ]; then
            cp "$src" "$dest" && chown www-data:www-data "$dest" && chmod 644 "$dest"
            [ $? -eq 0 ] && log "✅ $file atualizado com sucesso." || log "❌ Falha ao atualizar $file."
        else
            log "⚠️ $file não encontrado em $UPDATE_DIR."
        fi
    done
    log "✅ Atualização de layout concluída."
}

atualizar_base() {
    log "🗄️ Atualizando base de dados..."
    if [ -f "$UPDATE_DIR/atualizacao_db.sql" ]; then
        export MYSQL_PWD='controle_password'
        mysql -u controle_user controle_ponto < "$UPDATE_DIR/atualizacao_db.sql" 2>/tmp/mysql_error.log
        unset MYSQL_PWD
        if [ $? -eq 0 ]; then
            log "✅ Base de dados atualizada com $UPDATE_DIR/atualizacao_db.sql."
        else
            log "❌ Erro ao atualizar base de dados: $(cat /tmp/mysql_error.log)"
            exit 1
        fi
        rm -f /tmp/mysql_error.log
    else
        log "⚠️ Arquivo atualizacao_db.sql não encontrado em $UPDATE_DIR."
    fi
    log "✅ Atualização da base de dados concluída."
}

atualizar_app() {
    log "🔧 Atualizando aplicação principal..."
    for file in app.py biometria_service.py; do
        src="$UPDATE_DIR/$file"
        dest="$BASE_DIR/$file"
        if [ -f "$src" ]; then
            cp "$src" "$dest" && chown www-data:www-data "$dest" && chmod 644 "$dest"
            if [ $? -eq 0 ]; then
                log "✅ $file atualizado com sucesso."
            else
                log "❌ Falha ao copiar $file."
                exit 1
            fi
        else
            log "⚠️ Arquivo $file não encontrado em $UPDATE_DIR."
        fi
    done
    log "✅ Atualização da aplicação concluída."
}

configurar_logrotate() {
    log "🔍 Configurando rotação de logs..."
    ROTATE_DAYS=7
    LOGROTATE_FILE="/etc/logrotate.d/controle-ponto"
    cat > "$LOGROTATE_FILE" << EOL
/var/log/controle-ponto/*.log {
    daily
    rotate $ROTATE_DAYS
    compress
    missingok
    notifempty
    create 0664 www-data www-data
    postrotate
        systemctl restart controle-ponto >/dev/null 2>&1 || true
    endscript
}
EOL
    if [ $? -eq 0 ]; then
        chown root:root "$LOGROTATE_FILE"
        chmod 644 "$LOGROTATE_FILE"
        log "✅ Configuração do logrotate criada em $LOGROTATE_FILE com $ROTATE_DAYS dias de rotação."
    else
        log "❌ Falha ao criar configuração do logrotate."
        exit 1
    fi
    log "✅ Configuração de rotação de logs concluída."
}

configurar_ambiente() {
    log "⚙️ Configurando ambiente do sistema..."

    # Verificar e instalar dependências
    log "📦 Verificando e instalando dependências..."
    for pkg in flask pymysql websockets; do
        pip3 show "$pkg" >/dev/null 2>&1
        if [ $? -ne 0 ]; then
            log "📦 Instalando $pkg..."
            pip3 install "$pkg" 2>/tmp/pip_error.log
            if [ $? -eq 0 ]; then
                log "✅ $pkg instalado com sucesso."
            else
                log "❌ Falha ao instalar $pkg: $(cat /tmp/pip_error.log)"
                exit 1
            fi
        else
            log "✅ $pkg já está instalado."
        fi
    done
    log "⚠️ Aviso: O pacote pyzkfp2 não está disponível no PyPI. Baixe o SDK da ZKTeco e instale manualmente o pyzkfp2 seguindo as instruções do fornecedor."
    rm -f /tmp/pip_error.log

    # Ajustar permissões dos arquivos
    log "🔒 Ajustando permissões dos arquivos..."
    chown -R www-data:www-data "$BASE_DIR"
    chmod -R 644 "$BASE_DIR"/*.py
    chmod -R 755 "$BASE_DIR/static" "$BASE_DIR/templates"
    if [ $? -eq 0 ]; then
        log "✅ Permissões ajustadas com sucesso."
    else
        log "❌ Falha ao ajustar permissões."
        exit 1
    fi

    # Testar o app.py antes de iniciar o Flask
    log "🔍 Testando app.py antes de iniciar o Flask..."
    cd "$BASE_DIR" && sudo -u www-data python3 app.py &
    APP_PID=$!
    sleep 5
    if ss -tuln | grep -q 5000; then
        kill $APP_PID 2>/dev/null
        log "✅ app.py passou no teste de inicialização."
    else
        kill $APP_PID 2>/dev/null
        log "❌ Erro ao testar app.py: Flask não escutou na porta 5000"
        exit 1
    fi

    # Liberar a porta 5000, se necessário
    log "🔍 Verificando se a porta 5000 está em uso..."
    if ss -tuln | grep -q 5000; then
        log "⚠️ Porta 5000 em uso. Liberando..."
        fuser -k 5000/tcp >/dev/null 2>&1
        sleep 2
    fi
    log "✅ Porta 5000 liberada."

    # Configurar o serviço controle-ponto para usar o Flask diretamente
    log "🔧 Configurando serviço controle-ponto..."
    SERVICE_FILE="/etc/systemd/system/controle-ponto.service"
    cat > "$SERVICE_FILE" << EOL
[Unit]
Description=Controle de Ponto
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=$BASE_DIR
Environment="PATH=/usr/local/bin:/usr/bin:/bin"
ExecStart=/usr/bin/python3 $BASE_DIR/app.py
Restart=always
StartLimitInterval=60
StartLimitBurst=3

[Install]
WantedBy=multi-user.target
EOL
    if [ $? -eq 0 ]; then
        chown root:root "$SERVICE_FILE"
        chmod 644 "$SERVICE_FILE"
        systemctl daemon-reload
        log "✅ Serviço controle-ponto configurado para usar Flask diretamente."
    else
        log "❌ Falha ao configurar serviço controle-ponto."
        exit 1
    fi

    # Configurar o serviço biometria-service
    log "🔧 Configurando serviço biometria-service..."
    BIOMETRIA_SERVICE_FILE="/etc/systemd/system/biometria-service.service"
    cat > "$BIOMETRIA_SERVICE_FILE" << EOL
[Unit]
Description=Serviço de Biometria para Controle de Ponto
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=$BASE_DIR
Environment="PATH=/usr/local/bin:/usr/bin:/bin"
ExecStart=/usr/bin/python3 $BASE_DIR/biometria_service.py
Restart=always
StartLimitInterval=60
StartLimitBurst=3

[Install]
WantedBy=multi-user.target
EOL
    if [ $? -eq 0 ]; then
        chown root:root "$BIOMETRIA_SERVICE_FILE"
        chmod 644 "$BIOMETRIA_SERVICE_FILE"
        systemctl daemon-reload
        systemctl enable biometria-service
        systemctl start biometria-service
        log "✅ Serviço biometria-service configurado e iniciado."
    else
        log "❌ Falha ao configurar serviço biometria-service."
        exit 1
    fi

    # Verificar se o Flask está escutando na porta 5000
    log "🔍 Iniciando o Flask e verificando a porta 5000..."
    systemctl restart controle-ponto
    for i in {1..30}; do
        if ss -tuln | grep -q 5000; then
            log "✅ Flask está escutando na porta 5000."
            break
        fi
        sleep 1
    done
    if [ $i -eq 30 ]; then
        log "❌ Flask não está escutando na porta 5000. Verificando logs do serviço..."
        journalctl -u controle-ponto -b > /tmp/controle-ponto.log
        log "❌ Detalhes do erro (systemd): $(cat /tmp/controle-ponto.log)"
        log "❌ Detalhes do erro (app): $(cat $APP_LOG)"
        rm -f /tmp/controle-ponto.log
        exit 1
    fi

    # Verificar se o serviço de biometria está escutando na porta 8765
    log "🔍 Verificando se o serviço de biometria está escutando na porta 8765..."
    for i in {1..30}; do
        if ss -tuln | grep -q 8765; then
            log "✅ Serviço de biometria está escutando na porta 8765."
            break
        fi
        sleep 1
    done
    if [ $i -eq 30 ]; then
        log "❌ Serviço de biometria não está escutando na porta 8765. Verificando logs do serviço..."
        journalctl -u biometria-service -b > /tmp/biometria-service.log
        log "❌ Detalhes do erro (systemd): $(cat /tmp/biometria-service.log)"
        rm -f /tmp/biometria-service.log
        exit 1
    fi

    # Verificar e configurar o firewall
    log "🔥 Verificando firewall para as portas 5000 e 8765..."
    if command -v ufw >/dev/null; then
        for port in 5000 8765; do
            ufw status | grep $port >/dev/null 2>&1
            if [ $? -ne 0 ]; then
                log "⚠️ Porta $port não está liberada no firewall. Liberando..."
                ufw allow $port/tcp
                log "✅ Porta $port liberada no firewall."
            else
                log "✅ Porta $port já está liberada no firewall."
            fi
        done
    else
        log "⚠️ ufw não está instalado. Verifique manualmente se há outro firewall bloqueando as portas 5000 e 8765."
    fi

    # Configurar o Nginx
    log "🌐 Configurando Nginx..."
    NGINX_CONF="/etc/nginx/sites-available/controle-ponto"
    cat > "$NGINX_CONF" << EOL
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
        proxy_read_timeout 300;
    }
}
EOL
    if [ $? -eq 0 ]; then
        chown root:root "$NGINX_CONF"
        chmod 644 "$NGINX_CONF"
        ln -sf "$NGINX_CONF" /etc/nginx/sites-enabled/
        nginx -t 2>/tmp/nginx_error.log
        if [ $? -eq 0 ]; then
            systemctl reload nginx
            log "✅ Nginx configurado e recarregado com sucesso."
        else
            log "❌ Erro na configuração do Nginx: $(cat /tmp/nginx_error.log)"
            exit 1
        fi
        rm -f /tmp/nginx_error.log
    else
        log "❌ Falha ao configurar Nginx."
        exit 1
    fi

    # Reiniciar serviços
    log "🔄 Reiniciando serviços..."
    systemctl restart controle-ponto
    if [ $? -eq 0 ]; then
        log "✅ Serviço controle-ponto reiniciado com sucesso."
    else
        log "❌ Falha ao reiniciar serviço controle-ponto."
        exit 1
    fi
    systemctl restart biometria-service
    if [ $? -eq 0 ]; then
        log "✅ Serviço biometria-service reiniciado com sucesso."
    else
        log "❌ Falha ao reiniciar serviço biometria-service."
        exit 1
    fi

    log "✅ Configuração do ambiente concluída."
}

atualizar_tudo() {
    log "🔄 Aplicando todas as atualizações..."
    atualizar_layout
    atualizar_base
    atualizar_app
    configurar_logrotate
    configurar_ambiente
    log "✅ Todas as atualizações concluídas."
}

# Executar função passada como argumento
if [ "$1" ]; then
    case "$1" in
        atualizar_layout)
            atualizar_layout
            ;;
        atualizar_base)
            atualizar_base
            ;;
        atualizar_app)
            atualizar_app
            ;;
        configurar_ambiente)
            configurar_ambiente
            ;;
        atualizar_tudo)
            atualizar_tudo
            ;;
        *)
            log "⚠️ Opção inválida: $1"
            echo "Uso: $0 {atualizar_layout|atualizar_base|atualizar_app|configurar_ambiente|atualizar_tudo}"
            exit 1
            ;;
    esac
else
    log "⚠️ Nenhuma opção de atualização fornecida."
    echo "Uso: $0 {atualizar_layout|atualizar_base|atualizar_app|configurar_ambiente|atualizar_tudo}"
    exit 1
fi

log "🏁 Atualização finalizada."
