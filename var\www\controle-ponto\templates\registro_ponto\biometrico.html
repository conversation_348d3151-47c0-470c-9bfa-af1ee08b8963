{% extends "base.html" %}

{% block title %}Registro Biométrico{% endblock %}

{% block extra_css %}
<style>
    /* ===== VARIÁVEIS CSS ===== */
    :root {
        --primary-color: #4fbdba;
        --primary-hover: #26a69a;
        --success-color: #10b981;
        --success-bg: #dcfce7;
        --warning-color: #f59e0b;
        --warning-bg: #fef3c7;
        --danger-color: #ef4444;
        --danger-bg: #fee2e2;
        --background-color: #f9fafb;
        --card-background: #ffffff;
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --border-color: #e5e7eb;
        --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --transition: all 0.3s ease;
    }

    /* ===== LAYOUT PRINCIPAL ===== */
    .main-container {
        background-color: var(--background-color);
        min-height: 100vh;
        padding: 2rem;
    }

    .biometric-container {
        max-width: 1200px;
        margin: 0 auto;
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 2rem;
        align-items: start;
    }

    /* ===== ÁREA DO LEITOR ===== */
    .reader-area {
        background: var(--card-background);
        border-radius: 16px;
        padding: 2rem;
        text-align: center;
        box-shadow: var(--card-shadow);
        border: 1px solid var(--border-color);
        transition: var(--transition);
        cursor: pointer;
    }

    .reader-area:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
        border-color: var(--primary-color);
    }

    .reader-status {
        margin-bottom: 2rem;
    }

    .reader-icon {
        width: 120px;
        height: 120px;
        margin: 0 auto 1rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        transition: var(--transition);
        border: 3px solid var(--border-color);
    }

    .reader-icon.detecting {
        background: var(--warning-bg);
        color: var(--warning-color);
        border-color: var(--warning-color);
        animation: pulse 2s infinite;
    }

    .reader-icon.ready {
        background: var(--success-bg);
        color: var(--success-color);
        border-color: var(--success-color);
    }

    .reader-icon.error {
        background: var(--danger-bg);
        color: var(--danger-color);
        border-color: var(--danger-color);
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .status-text {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1rem;
    }

    .instructions {
        font-size: 0.9rem;
        color: var(--text-secondary);
        line-height: 1.5;
    }

    /* ===== ÁREA DO FUNCIONÁRIO ===== */
    .employee-area {
        background: var(--card-background);
        border-radius: 16px;
        padding: 2rem;
        box-shadow: var(--card-shadow);
        border: 1px solid var(--border-color);
        display: none;
    }

    .employee-area.show {
        display: block;
        animation: slideIn 0.3s ease;
    }

    @keyframes slideIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .employee-header {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--border-color);
    }

    .employee-photo {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid var(--primary-color);
        background: var(--background-color);
    }

    .employee-info h3 {
        margin: 0 0 0.5rem 0;
        color: var(--text-primary);
        font-size: 1.5rem;
    }

    .employee-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .detail-label {
        font-size: 0.8rem;
        color: var(--text-secondary);
        font-weight: 600;
        text-transform: uppercase;
    }

    .detail-value {
        font-size: 1rem;
        color: var(--text-primary);
        font-weight: 500;
    }

    /* ===== JORNADA DE TRABALHO ===== */
    .work-schedule {
        margin-bottom: 2rem;
    }

    .schedule-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .schedule-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .schedule-item {
        text-align: center;
        padding: 0.75rem;
        border-radius: 8px;
        border: 1px solid var(--border-color);
        background: var(--background-color);
    }

    .schedule-item.completed {
        background: var(--success-bg);
        border-color: var(--success-color);
        color: var(--success-color);
    }

    .schedule-item.next {
        background: var(--warning-bg);
        border-color: var(--warning-color);
        color: var(--warning-color);
        font-weight: 600;
    }

    .schedule-label {
        font-size: 0.7rem;
        text-transform: uppercase;
        margin-bottom: 0.25rem;
    }

    .schedule-time {
        font-size: 0.9rem;
        font-weight: 600;
    }

    /* ===== ÁREA DE CONFIRMAÇÃO ===== */
    .confirm-area {
        text-align: center;
        padding-top: 1rem;
        border-top: 2px solid var(--border-color);
    }

    .confirm-message {
        background: var(--warning-bg);
        color: var(--warning-color);
        padding: 1rem 2rem;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        border: 2px solid var(--warning-color);
        animation: pulse 2s infinite;
    }

    /* ===== RESPONSIVIDADE ===== */
    @media (max-width: 768px) {
        .main-container {
            padding: 1rem;
        }

        .biometric-container {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .employee-details {
            grid-template-columns: 1fr;
        }

        .schedule-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <div class="biometric-container">
        <!-- Área do Leitor Biométrico -->
        <div class="reader-area">
            <div class="reader-status">
                <div class="reader-icon detecting" id="reader-icon">
                    <i class="fas fa-fingerprint"></i>
                </div>
                <div class="status-text" id="status-text">
                    Detectando leitor biométrico...
                </div>
                <div class="instructions" id="instructions">
                    Aguarde enquanto o sistema detecta o leitor biométrico.
                </div>
            </div>
        </div>

        <!-- Área do Funcionário -->
        <div class="employee-area" id="employee-area">
            <div class="employee-header">
                <img src="/static/images/default-avatar.png" alt="Foto" class="employee-photo" id="employee-photo">
                <div class="employee-info">
                    <h3 id="employee-name">Nome do Funcionário</h3>
                    <p id="employee-role">Cargo</p>
                </div>
            </div>

            <div class="employee-details">
                <div class="detail-item">
                    <span class="detail-label">Matrícula</span>
                    <span class="detail-value" id="employee-id">000</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Empresa</span>
                    <span class="detail-value" id="employee-company">Empresa</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">CNPJ</span>
                    <span class="detail-value" id="employee-cnpj">00.000.000/0001-00</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Setor</span>
                    <span class="detail-value" id="employee-sector">Setor</span>
                </div>
            </div>

            <div class="work-schedule">
                <div class="schedule-title">
                    <i class="fas fa-clock"></i>
                    Jornada de Trabalho - Hoje
                </div>
                <div class="schedule-grid" id="schedule-grid">
                    <div class="schedule-item">
                        <div class="schedule-label">Entrada</div>
                        <div class="schedule-time">08:00</div>
                    </div>
                    <div class="schedule-item">
                        <div class="schedule-label">Saída Almoço</div>
                        <div class="schedule-time">12:00</div>
                    </div>
                    <div class="schedule-item">
                        <div class="schedule-label">Retorno</div>
                        <div class="schedule-time">13:00</div>
                    </div>
                    <div class="schedule-item">
                        <div class="schedule-label">Saída</div>
                        <div class="schedule-time">17:00</div>
                    </div>
                </div>
            </div>

            <div class="confirm-area">
                <div class="confirm-message">
                    <i class="fas fa-fingerprint"></i>
                    Posicione o dedo novamente para confirmar o registro
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elementos da interface
    const readerIcon = document.getElementById('reader-icon');
    const statusText = document.getElementById('status-text');
    const instructions = document.getElementById('instructions');
    const employeeArea = document.getElementById('employee-area');
    // Removido botão de confirmação - agora é automático

    // Estado da aplicação
    let readerDetected = false;
    let awaitingFingerprint = false;
    let employeeFound = null;
    let awaitingConfirmation = false;

    // Inicializar sistema
    initializeBiometricSystem();

    function initializeBiometricSystem() {
        updateStatus('detecting', 'Detectando leitor biométrico...', 'Aguarde enquanto o sistema detecta o leitor.');

        // INTEGRAÇÃO REAL COM LEITOR BIOMÉTRICO
        detectBiometricReader();
    }

    function detectBiometricReader() {
        // TODO: Integração real com ZKAgent ou Universal Biometric Service
        // Exemplo de integração:
        // if (typeof ZKAgent !== 'undefined') {
        //     ZKAgent.detectReader().then(reader => {
        //         if (reader) {
        //             readerDetected = true;
        //             updateStatus('ready', 'Leitor biométrico detectado', 'Posicione o dedo no leitor para identificação.');
        //             startListeningForFingerprint();
        //         } else {
        //             updateStatus('error', 'Leitor não encontrado', 'Verifique se o leitor está conectado.');
        //         }
        //     });
        // }

        updateStatus('error', 'Integração biométrica não configurada', 'Sistema aguardando integração com leitor real.');
    }

    function startListeningForFingerprint() {
        if (!readerDetected) return;

        awaitingFingerprint = true;

        // TODO: Integração real com eventos do leitor
        // Exemplo:
        // ZKAgent.onFingerDetected = function(biometricData) {
        //     if (awaitingFingerprint && !awaitingConfirmation) {
        //         processBiometricData(biometricData);
        //     }
        // };
    }

    function processBiometricData(biometricData) {
        if (!awaitingFingerprint) return;

        updateStatus('detecting', 'Lendo biometria...', 'Mantenha o dedo no leitor.');

        // Buscar funcionário pela biometria real
        searchEmployeeByBiometric(biometricData);
    }

    function searchEmployeeByBiometric(biometricData) {
        // Usar dados reais do leitor biométrico
        if (!biometricData || !biometricData.template) {
            showError('Dados biométricos inválidos');
            return;
        }

        // Buscar funcionário pela biometria real
        fetch('/registro-ponto/api/identificar-funcionario-biometria', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                template_biometrico: biometricData.template,
                qualidade_biometria: biometricData.quality || 0
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                employeeFound = data.funcionario;
                showEmployeeData(data.funcionario);
            } else {
                employeeNotFound();
            }
        })
        .catch(error => {
            showError('Erro de comunicação: ' + error.message);
        });
    }

    function employeeNotFound() {
        updateStatus('error', 'Funcionário não encontrado', 'Biometria não cadastrada no sistema.');

        // Voltar a escutar após 3 segundos
        setTimeout(() => {
            if (readerDetected) {
                updateStatus('ready', 'Leitor biométrico pronto', 'Posicione o dedo no leitor para identificação.');
                startListeningForFingerprint();
            }
        }, 3000);
    }

    function showEmployeeData(employee) {
        awaitingFingerprint = false;
        awaitingConfirmation = true;

        // Atualizar status
        updateStatus('ready', 'Funcionário identificado!', 'Verifique os dados e posicione o dedo novamente para confirmar o registro.');

        // Preencher dados do funcionário
        document.getElementById('employee-name').textContent = employee.nome_completo;
        document.getElementById('employee-role').textContent = employee.cargo;
        document.getElementById('employee-id').textContent = employee.matricula_empresa;
        document.getElementById('employee-company').textContent = employee.empresa_nome;
        document.getElementById('employee-cnpj').textContent = employee.cnpj;
        document.getElementById('employee-sector').textContent = employee.setor;
        document.getElementById('employee-photo').src = employee.foto_url;

        // Atualizar jornada e registros do dia
        updateWorkSchedule(employee);

        // Mostrar área do funcionário
        employeeArea.classList.add('show');

        // Aguardar segunda leitura para confirmação
        waitForConfirmationFingerprint();
    }

    function waitForConfirmationFingerprint() {
        // TODO: Integração real com segunda leitura do leitor
        // Exemplo:
        // ZKAgent.onFingerDetected = function(biometricData) {
        //     if (awaitingConfirmation) {
        //         processConfirmationFingerprint(biometricData);
        //     }
        // };

        // SISTEMA AGUARDANDO INTEGRAÇÃO REAL
        updateStatus('ready', 'Aguardando integração', 'Sistema precisa de integração com leitor real para confirmação.');
    }

    function processConfirmationFingerprint(biometricData) {
        if (!awaitingConfirmation) return;

        updateStatus('detecting', 'Confirmando registro...', 'Processando confirmação...');

        // Registrar ponto usando mesma lógica da batida manual
        registerEmployeePunch();
    }

    function updateWorkSchedule(employee) {
        const scheduleGrid = document.getElementById('schedule-grid');
        const scheduleItems = scheduleGrid.querySelectorAll('.schedule-item');

        // Mapear tipos de registro
        const scheduleTypes = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida'];
        const scheduleLabels = ['Entrada', 'Saída Almoço', 'Retorno', 'Saída'];
        const scheduleTimes = [
            employee.jornada.entrada,
            employee.jornada.saida_almoco,
            employee.jornada.retorno_almoco,
            employee.jornada.saida
        ];

        // Determinar próximo registro esperado
        let nextExpected = 0;
        employee.registros_hoje.forEach(registro => {
            const index = scheduleTypes.indexOf(registro.tipo);
            if (index >= nextExpected) {
                nextExpected = index + 1;
            }
        });

        // Atualizar visual dos itens
        scheduleItems.forEach((item, index) => {
            const label = item.querySelector('.schedule-label');
            const time = item.querySelector('.schedule-time');

            label.textContent = scheduleLabels[index];
            time.textContent = scheduleTimes[index];

            // Remover classes anteriores
            item.classList.remove('completed', 'next');

            // Aplicar estilo baseado no status
            if (index < nextExpected) {
                item.classList.add('completed');
                // Mostrar hora real do registro
                const registro = employee.registros_hoje.find(r => scheduleTypes.indexOf(r.tipo) === index);
                if (registro) {
                    time.textContent = registro.hora;
                }
            } else if (index === nextExpected) {
                item.classList.add('next');
            }
        });
    }

    function registerEmployeePunch() {
        if (!employeeFound) return;

        // Usar mesma API da batida manual - ela determina automaticamente o tipo
        const registroData = {
            funcionario_id: employeeFound.id
            // NÃO enviar tipo_registro - deixar a API determinar automaticamente
        };

        // Chamar API de registro manual (mesma lógica)
        fetch('/registro-ponto/api/registrar-manual', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(registroData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showRegistrationSuccess(data);
            } else {
                showRegistrationError(data.message);
            }
        })
        .catch(error => {
            showRegistrationError('Erro de comunicação: ' + error.message);
        });
    }

    function showRegistrationSuccess(data) {
        updateStatus('ready', 'Ponto registrado com sucesso!',
            `${data.tipo_registro_texto || 'Registro'} realizado às ${data.data_hora || 'agora'}.`);

        // Resetar sistema imediatamente (sem timeout)
        resetBiometricSystem();
    }

    function showRegistrationError(message) {
        updateStatus('error', 'Erro no registro', message);

        // Resetar sistema imediatamente (sem timeout)
        resetBiometricSystem();
    }

    function showError(message) {
        updateStatus('error', 'Erro', message);

        // Voltar a escutar imediatamente (sem timeout)
        if (readerDetected) {
            updateStatus('ready', 'Leitor biométrico pronto', 'Posicione o dedo no leitor para identificação.');
            startListeningForFingerprint();
        }
    }

    function resetBiometricSystem() {
        // Limpar estado
        awaitingFingerprint = false;
        employeeFound = null;
        awaitingConfirmation = false;

        // Ocultar área do funcionário
        employeeArea.classList.remove('show');

        // Voltar ao estado inicial - aguardando biometria
        if (readerDetected) {
            updateStatus('ready', 'Leitor biométrico pronto', 'Posicione o dedo no leitor para identificação.');
            startListeningForFingerprint();
        }
    }

    function updateStatus(type, text, instruction) {
        // Atualizar ícone
        readerIcon.className = `reader-icon ${type}`;

        // Atualizar textos
        statusText.textContent = text;
        instructions.textContent = instruction;
    }
});
</script>
{% endblock %}
