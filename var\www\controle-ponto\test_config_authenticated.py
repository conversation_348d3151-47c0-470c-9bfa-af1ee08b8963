#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste Autenticado da Página de Configurações
===========================================
"""

import requests
import sys

def test_authenticated_config():
    """Testa a página de configurações com autenticação"""
    
    base_url = "http://10.19.208.31:5000"
    
    # Criar sessão
    session = requests.Session()
    
    print("🔐 TESTANDO LOGIN...")
    
    # Fazer login
    login_data = {
        'usuario': 'admin',
        'senha': '@Ric6109'
    }
    
    try:
        # Login
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        print(f"✅ Login realizado - Status: {login_response.status_code}")
        
        if login_response.status_code == 302:
            print("✅ Login bem-sucedido (redirecionamento)")
        else:
            print(f"⚠️ Status inesperado: {login_response.status_code}")
            
        # Testar página de configurações
        print("\n🔍 TESTANDO PÁGINA DE CONFIGURAÇÕES...")
        config_response = session.get(f"{base_url}/configuracoes")
        
        print(f"Status: {config_response.status_code}")
        
        if config_response.status_code == 200:
            print("✅ Página de configurações carregada com sucesso!")
            
            # Verificar se tem o conteúdo esperado
            content = config_response.text
            
            checks = [
                ("Título da página", "Configurações do Sistema" in content),
                ("Botão de biometria", "/configuracoes/biometria" in content),
                ("Seção de sistema", "Sistema" in content),
                ("Cards de configuração", "config-card" in content)
            ]
            
            print("\n📋 VERIFICAÇÕES DE CONTEÚDO:")
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"{status} {check_name}")
                
            # Testar página de biometria
            print("\n🔍 TESTANDO PÁGINA DE BIOMETRIA...")
            biometric_response = session.get(f"{base_url}/configuracoes/biometria")
            
            print(f"Status: {biometric_response.status_code}")
            
            if biometric_response.status_code == 200:
                print("✅ Página de biometria carregada com sucesso!")
                
                bio_content = biometric_response.text
                bio_checks = [
                    ("Título biometria", "Configurações Biométricas" in bio_content),
                    ("Status do serviço", "service-status" in bio_content),
                    ("Configurações", "biometric-section" in bio_content)
                ]
                
                print("\n📋 VERIFICAÇÕES BIOMETRIA:")
                for check_name, result in bio_checks:
                    status = "✅" if result else "❌"
                    print(f"{status} {check_name}")
                    
            else:
                print(f"❌ Erro na página de biometria: {biometric_response.status_code}")
        
        else:
            print(f"❌ Erro na página de configurações: {config_response.status_code}")
            print("Conteúdo da resposta:")
            print(config_response.text[:500] + "..." if len(config_response.text) > 500 else config_response.text)
    
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_authenticated_config() 