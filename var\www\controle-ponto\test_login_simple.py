#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste simples de login
"""

import requests
import sys

def test_login():
    """Teste de login com diferentes senhas"""
    
    session = requests.Session()
    login_url = "http://localhost:5000/login"
    
    # Tentar diferentes senhas
    senhas = ['admin123', 'admin', '123456', 'password', 'Ainexus@2024']
    
    for senha in senhas:
        print(f"Tentando login com senha: {senha}")
        
        login_data = {
            'usuario': 'admin',
            'senha': senha
        }
        
        response = session.post(login_url, data=login_data, allow_redirects=False)
        print(f"  Status: {response.status_code}")
        
        if response.status_code == 302:
            location = response.headers.get('Location', '')
            print(f"  Redirecionamento para: {location}")
            if '/login' not in location:
                print(f"✅ Login bem-sucedido com senha: {senha}")
                return senha
        elif response.status_code == 200:
            if 'login' not in response.text.lower() or 'erro' not in response.text.lower():
                print(f"✅ Login bem-sucedido com senha: {senha}")
                return senha
        
        print("  ❌ Falha no login")
    
    print("❌ Nenhuma senha funcionou")
    return None

if __name__ == "__main__":
    senha_correta = test_login()
    if senha_correta:
        print(f"\n🎉 Senha correta encontrada: {senha_correta}")
    else:
        print("\n💥 Não foi possível fazer login")
