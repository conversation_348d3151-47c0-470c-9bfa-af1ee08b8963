#!/usr/bin/env python3
import sys
sys.path.insert(0, '/var/www/controle-ponto')

from werkzeug.security import generate_password_hash
from utils.database import DatabaseManager

print("=== CRIANDO HASH PARA SENHA DO CAVALCROD ===")

try:
    # Gerar hash para a senha 200381
    senha = "200381"
    hash_senha = generate_password_hash(senha)
    
    print(f"Senha original: {senha}")
    print(f"Hash gerado: {hash_senha}")
    
    # Atualizar no banco
    DatabaseManager.execute_query(
        "UPDATE usuarios SET senha = %s WHERE usuario = 'cavalcrod'",
        (hash_senha,)
    )
    
    print("✅ Hash atualizado no banco de dados")
    
    # Verificar
    user = DatabaseManager.execute_query(
        "SELECT senha FROM usuarios WHERE usuario = 'cavalcrod'"
    )
    
    if user:
        print(f"✅ Senha no banco: {user[0]['senha'][:50]}...")
    
except Exception as e:
    print(f"❌ ERRO: {e}")

print("\n=== FIM ===")
