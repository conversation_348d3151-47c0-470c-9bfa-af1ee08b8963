<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RLPONTO-WEB - Registro Biométrico</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom Styles -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .scan-animation {
            animation: scan 2s ease-in-out infinite;
        }
        
        @keyframes scan {
            0%, 100% { transform: translateY(0); opacity: 0.3; }
            50% { transform: translateY(-20px); opacity: 1; }
        }
        
        .pulse-ring {
            animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
        }
        
        @keyframes pulse-ring {
            0% { transform: scale(0.8); opacity: 1; }
            100% { transform: scale(2.4); opacity: 0; }
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
    
    <!-- Main Container -->
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            
            <!-- Header Card -->
            <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 mb-6 text-center border border-white/20">
                <div class="flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 mx-auto mb-6 shadow-2xl">
                    <i data-lucide="fingerprint" class="w-10 h-10 text-white"></i>
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">RLPONTO-WEB</h1>
                <p class="text-blue-200 text-sm">Sistema de Controle de Ponto Biométrico</p>
            </div>
            
            <!-- Status Card -->
            <div class="bg-white rounded-3xl shadow-2xl overflow-hidden">
                
                <!-- Connection Status Bar -->
                <div id="connection-status" class="h-2 bg-red-500 transition-all duration-500"></div>
                
                <!-- Main Content -->
                <div class="p-8">
                    
                    <!-- Device Connection Section -->
                    <div class="text-center mb-8">
                        <div class="relative mb-6">
                            <div id="pulse-ring" class="absolute inset-0 rounded-full bg-blue-500 pulse-ring opacity-0"></div>
                            <div id="device-icon" class="relative w-20 h-20 mx-auto rounded-full bg-gray-100 flex items-center justify-center transition-all duration-300">
                                <i data-lucide="usb" id="usb-icon" class="w-8 h-8 text-gray-400"></i>
                            </div>
                        </div>
                        
                        <h2 id="status-title" class="text-xl font-semibold text-gray-800 mb-2">Leitor Biométrico</h2>
                        <p id="status-message" class="text-gray-500 text-sm mb-6">Clique em conectar para iniciar</p>
                        
                        <!-- Connect Button -->
                        <button id="connect-reader" class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center space-x-3">
                            <i data-lucide="power" class="w-5 h-5"></i>
                            <span>Conectar Leitor</span>
                        </button>
                    </div>
                    
                    <!-- Biometric Scanner Section -->
                    <div id="scanner-section" class="hidden">
                        <div class="text-center mb-6">
                            <div class="relative w-32 h-32 mx-auto mb-4">
                                <div class="absolute inset-0 rounded-full bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center">
                                    <i data-lucide="fingerprint" class="w-16 h-16 text-white"></i>
                                </div>
                                <div id="scan-line" class="absolute left-1/2 transform -translate-x-1/2 w-24 h-1 bg-white/50 rounded-full scan-animation hidden"></div>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">Posicione seu dedo</h3>
                            <p class="text-gray-500 text-sm">Mantenha o dedo no sensor até ouvir o sinal</p>
                        </div>
                        
                        <!-- Scan Button -->
                        <button id="scan-biometric" class="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-medium py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center space-x-3">
                            <i data-lucide="scan" class="w-5 h-5"></i>
                            <span>Registrar Ponto</span>
                        </button>
                    </div>
                    
                </div>
            </div>
            
            <!-- Info Card -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 mt-6 border border-white/20">
                <div class="flex items-start space-x-3">
                    <i data-lucide="info" class="w-5 h-5 text-blue-300 mt-0.5 flex-shrink-0"></i>
                    <div>
                        <h4 class="text-white font-medium mb-1">Sistema Inteligente</h4>
                        <p class="text-blue-200 text-sm">O horário de entrada/saída é detectado automaticamente. Sua biometria deve estar cadastrada no sistema.</p>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
    
    <!-- Success Modal -->
    <div id="success-modal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50 hidden">
        <div class="bg-white rounded-3xl p-8 max-w-sm w-full transform transition-all duration-300">
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                    <i data-lucide="check" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">Ponto Registrado!</h3>
                <p id="success-details" class="text-gray-600 mb-6"></p>
                <button id="close-success" class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white font-medium py-3 px-6 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300">
                    OK
                </button>
            </div>
        </div>
    </div>
    
    <!-- Error Modal -->
    <div id="error-modal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50 hidden">
        <div class="bg-white rounded-3xl p-8 max-w-sm w-full transform transition-all duration-300">
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center">
                    <i data-lucide="x" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">Erro no Registro</h3>
                <p id="error-details" class="text-gray-600 mb-6">Biometria não encontrada</p>
                <button id="close-error" class="w-full bg-gradient-to-r from-red-500 to-red-600 text-white font-medium py-3 px-6 rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300">
                    Tentar Novamente
                </button>
            </div>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50 hidden">
        <div class="bg-white rounded-2xl p-8 text-center">
            <div class="w-12 h-12 mx-auto mb-4 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
            <p class="text-gray-600 font-medium">Processando...</p>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="static/js/biometria-producao.js"></script>
    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Initialize biometric system
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof BiometricSystem !== 'undefined') {
                window.biometricSystem = new BiometricSystem();
            }
        });
    </script>
    
</body>
</html>