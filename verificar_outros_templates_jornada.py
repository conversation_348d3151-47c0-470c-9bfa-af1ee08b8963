#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar se há outros templates com problemas similares
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def verificar_outros_templates():
    """Verificar se há outros funcionários e templates com problemas"""
    print("🔍 VERIFICANDO OUTROS FUNCIONÁRIOS E TEMPLATES")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar todos os funcionários ativos
        print("\n1. Verificando todos os funcionários ativos...")
        sql_funcionarios = """
        SELECT 
            f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id,
            f.turno, f.tolerancia_ponto,
            e.razao_social as empresa_nome
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        WHERE f.status_cadastro = 'Ativo'
        ORDER BY e.razao_social, f.nome_completo
        """
        
        funcionarios = db.execute_query(sql_funcionarios)
        
        print(f"📊 Funcionários ativos: {len(funcionarios)}")
        for func in funcionarios:
            print(f"   - ID {func['id']}: {func['nome_completo']} ({func['empresa_nome']})")
            print(f"     Jornada ID: {func['jornada_trabalho_id']}")
            print(f"     Turno Individual: {func['turno']}")
            print(f"     Tolerância Individual: {func['tolerancia_ponto']}")
        
        # 2. Testar get_with_epis para cada funcionário
        print("\n2. Testando get_with_epis para cada funcionário...")
        from utils.database import FuncionarioQueries
        
        funcionarios_com_problema = []
        
        for func in funcionarios:
            funcionario_id = func['id']
            nome = func['nome_completo']
            
            print(f"\n   📋 Funcionário {funcionario_id}: {nome}")
            
            funcionario_completo = FuncionarioQueries.get_with_epis(funcionario_id)
            
            if funcionario_completo:
                turno_individual = func['turno']
                tolerancia_individual = func['tolerancia_ponto']
                
                turno_jornada = funcionario_completo.get('tipo_jornada')
                tolerancia_jornada = funcionario_completo.get('tolerancia_entrada_minutos')
                
                print(f"      Turno Individual: {turno_individual}")
                print(f"      Turno da Jornada: {turno_jornada}")
                print(f"      Tolerância Individual: {tolerancia_individual}")
                print(f"      Tolerância da Jornada: {tolerancia_jornada}")
                
                # Verificar se há divergência
                divergencia = False
                if turno_individual and turno_individual != turno_jornada:
                    print(f"      ⚠️ DIVERGÊNCIA DE TURNO: Individual={turno_individual}, Jornada={turno_jornada}")
                    divergencia = True
                
                if tolerancia_individual and tolerancia_individual != tolerancia_jornada:
                    print(f"      ⚠️ DIVERGÊNCIA DE TOLERÂNCIA: Individual={tolerancia_individual}, Jornada={tolerancia_jornada}")
                    divergencia = True
                
                if divergencia:
                    funcionarios_com_problema.append({
                        'id': funcionario_id,
                        'nome': nome,
                        'empresa': func['empresa_nome'],
                        'turno_individual': turno_individual,
                        'turno_jornada': turno_jornada,
                        'tolerancia_individual': tolerancia_individual,
                        'tolerancia_jornada': tolerancia_jornada
                    })
                else:
                    print(f"      ✅ OK - Dados consistentes")
            else:
                print(f"      ❌ Erro ao carregar dados")
        
        # 3. Resumo dos problemas encontrados
        print(f"\n3. RESUMO DOS PROBLEMAS ENCONTRADOS:")
        if funcionarios_com_problema:
            print(f"   ⚠️ {len(funcionarios_com_problema)} funcionários com divergências:")
            for func in funcionarios_com_problema:
                print(f"      - {func['nome']} ({func['empresa']}):")
                if func['turno_individual'] != func['turno_jornada']:
                    print(f"        Turno: {func['turno_individual']} → {func['turno_jornada']}")
                if func['tolerancia_individual'] != func['tolerancia_jornada']:
                    print(f"        Tolerância: {func['tolerancia_individual']} → {func['tolerancia_jornada']}")
        else:
            print(f"   ✅ Nenhum funcionário com divergências encontrado")
        
        # 4. Verificar se há campos individuais que deveriam ser limpos
        print(f"\n4. Verificando campos individuais que deveriam ser limpos...")
        sql_campos_individuais = """
        SELECT 
            f.id, f.nome_completo, f.turno, f.tolerancia_ponto,
            e.razao_social as empresa_nome
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        WHERE f.status_cadastro = 'Ativo' 
        AND (f.turno IS NOT NULL OR f.tolerancia_ponto IS NOT NULL)
        ORDER BY e.razao_social, f.nome_completo
        """
        
        funcionarios_com_campos = db.execute_query(sql_campos_individuais)
        
        if funcionarios_com_campos:
            print(f"   ⚠️ {len(funcionarios_com_campos)} funcionários com campos individuais:")
            for func in funcionarios_com_campos:
                print(f"      - {func['nome_completo']} ({func['empresa_nome']}):")
                print(f"        Turno: {func['turno']}")
                print(f"        Tolerância: {func['tolerancia_ponto']}")
        else:
            print(f"   ✅ Nenhum funcionário com campos individuais encontrado")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a verificação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    verificar_outros_templates()
