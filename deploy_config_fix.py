#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de Deploy - Configurações Biométricas
===========================================

Script para garantir que as configurações biométricas sejam aplicadas
corretamente no servidor, incluindo restart do Flask e limpeza de cache.

Desenvolvido por: <PERSON> Rodrigues - AiNexus Tecnologia
Data: Junho 2025
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime
from pathlib import Path

def log_message(message):
    """Log com timestamp"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")

def main():
    log_message("🚀 Iniciando deploy das configurações biométricas...")
    
    # Definir caminhos
    local_base = Path("var/www/controle-ponto")
    server_base = Path("/var/www/controle-ponto")  # Caminho do servidor
    
    # Arquivos que precisam ser atualizados
    files_to_update = [
        "templates/configuracoes/index.html",
        "templates/configuracoes/biometria.html", 
        "app_biometric_config.py",
        "app_configuracoes.py",
        "app.py",
        "app_status.py"
    ]
    
    log_message("📋 Verificando arquivos locais...")
    
    # Verificar se todos os arquivos existem localmente
    for file_path in files_to_update:
        local_file = local_base / file_path
        if not local_file.exists():
            log_message(f"❌ ERRO: Arquivo não encontrado: {local_file}")
            return False
        else:
            log_message(f"✅ Arquivo encontrado: {file_path}")
    
    # Verificar se o botão de biometria está no arquivo
    config_template = local_base / "templates/configuracoes/index.html"
    with open(config_template, 'r', encoding='utf-8') as f:
        content = f.read()
        if 'fas fa-fingerprint' in content and 'Biometria' in content:
            log_message("✅ Botão de biometria confirmado no template local")
        else:
            log_message("❌ ERRO: Botão de biometria não encontrado no template!")
            return False
    
    log_message("🔧 Instruções para aplicar no servidor:")
    print("\n" + "="*60)
    print("COMANDOS PARA EXECUTAR NO SERVIDOR (IP: ************)")
    print("="*60)
    print()
    
    print("1. 🔄 Fazer backup dos arquivos atuais:")
    print("cd /var/www/controle-ponto")
    print("mkdir -p backup-pre-biometric")
    for file_path in files_to_update:
        print(f"cp {file_path} backup-pre-biometric/")
    print()
    
    print("2. 📁 Copiar novos arquivos (via SCP ou rsync):")
    for file_path in files_to_update:
        print(f"# Copiar {file_path}")
    print()
    
    print("3. 🔐 Verificar permissões:")
    print("chown -R www-data:www-data /var/www/controle-ponto")
    print("chmod -R 755 /var/www/controle-ponto")
    print()
    
    print("4. 🔄 Restart do serviço Flask:")
    print("systemctl restart flask-app")
    print("# OU")
    print("pkill -f 'python.*app.py'")
    print("cd /var/www/controle-ponto && python app.py &")
    print()
    
    print("5. 🧹 Limpar cache do navegador:")
    print("- Pressionar Ctrl+F5 na página")
    print("- Ou abrir DevTools (F12) e fazer 'Empty Cache and Hard Reload'")
    print()
    
    print("6. ✅ Verificar se funcionou:")
    print("- Acessar http://IP:5000/configuracoes")
    print("- Ir na aba 'Sistema'")
    print("- Verificar se aparece o card 'Biometria'")
    print()
    
    # Criar arquivo de verificação
    verification_file = "verify_biometric_config.py"
    log_message(f"📝 Criando arquivo de verificação: {verification_file}")
    
    verification_code = '''#!/usr/bin/env python3
# Verificação das configurações biométricas

import os
from pathlib import Path

def check_biometric_config():
    print("🔍 Verificando configurações biométricas...")
    
    # Verificar template
    template_file = Path("templates/configuracoes/index.html")
    if template_file.exists():
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'fas fa-fingerprint' in content and 'Biometria' in content:
                print("✅ Template de configurações: OK (botão biometria presente)")
            else:
                print("❌ Template de configurações: ERRO (botão biometria ausente)")
    else:
        print("❌ Template de configurações não encontrado")
    
    # Verificar blueprint biométrico
    biometric_file = Path("app_biometric_config.py")
    if biometric_file.exists():
        print("✅ Blueprint biométrico: OK")
    else:
        print("❌ Blueprint biométrico: ERRO (arquivo não encontrado)")
    
    # Verificar template biométrico
    biometric_template = Path("templates/configuracoes/biometria.html")
    if biometric_template.exists():
        print("✅ Template biométrico: OK")
    else:
        print("❌ Template biométrico: ERRO (arquivo não encontrado)")
    
    print("\\n🏁 Verificação concluída!")

if __name__ == "__main__":
    check_biometric_config()
'''
    
    with open(verification_file, 'w', encoding='utf-8') as f:
        f.write(verification_code)
    
    log_message(f"✅ Arquivo de verificação criado: {verification_file}")
    log_message("🎯 Execute este arquivo no servidor para verificar se tudo está correto")
    
    print("\n" + "="*60)
    print("📋 RESUMO DO PROBLEMA")
    print("="*60)
    print("• Os arquivos locais ESTÃO corretos (botão biometria presente)")
    print("• O problema é que o servidor não foi atualizado corretamente")
    print("• Siga as instruções acima para aplicar no servidor")
    print("• Use o arquivo 'verify_biometric_config.py' para verificar")
    print("="*60)
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Script concluído com sucesso!")
        print("📋 Siga as instruções acima para atualizar o servidor")
    else:
        print("\n❌ Erro no script!")
        sys.exit(1) 