#!/usr/bin/env python3
"""
Script para debugar problema relacionado a EPIs e empresa na edição de funcionários.
"""

import sys
import os
import traceback
import pymysql

# Adicionar o diretório do projeto ao path
sys.path.append('/var/www/controle-ponto')

try:
    from app_funcionarios import _buscar_epis_funcionario, get_empresas_para_funcionario, _extrair_dados_formulario, _validar_dados_funcionario
    from utils.helpers import FormValidator
    from flask import Flask, request
    
    print("✅ Imports realizados com sucesso")
    
    # Criar uma aplicação Flask de teste
    app = Flask(__name__)
    
    def verificar_epis_funcionario():
        """
        Verifica os EPIs do funcionário e se há campo 'id' problemático.
        """
        print("\n🦺 VERIFICANDO EPIs DO FUNCIONÁRIO")
        print("=" * 50)
        
        funcionario_id = 1
        epis = _buscar_epis_funcionario(funcionario_id)
        
        print(f"📊 EPIs encontrados: {len(epis)}")
        
        for i, epi in enumerate(epis):
            print(f"\n🦺 EPI {i+1}:")
            for campo, valor in epi.items():
                print(f"  {campo}: {valor}")
                
                # Verificar se há campo 'id' que pode causar problema
                if campo == 'id':
                    print(f"  ⚠️ CAMPO 'id' ENCONTRADO: {valor} (tipo: {type(valor)})")
        
        return epis
    
    def verificar_empresas_disponiveis():
        """
        Verifica as empresas disponíveis e se há problema na busca.
        """
        print("\n🏢 VERIFICANDO EMPRESAS DISPONÍVEIS")
        print("=" * 50)
        
        empresas = get_empresas_para_funcionario()
        
        print(f"📊 Empresas encontradas: {len(empresas)}")
        
        for i, empresa in enumerate(empresas):
            print(f"\n🏢 Empresa {i+1}:")
            for campo, valor in empresa.items():
                print(f"  {campo}: {valor}")
                
                # Verificar se há campo 'id' que pode causar problema
                if campo == 'id':
                    print(f"  ⚠️ CAMPO 'id' ENCONTRADO: {valor} (tipo: {type(valor)})")
        
        return empresas
    
    def simular_edicao_com_epis_reais():
        """
        Simula edição usando EPIs e empresa reais do funcionário.
        """
        print("\n🔍 SIMULANDO EDIÇÃO COM DADOS REAIS (EPIs + EMPRESA)")
        print("=" * 60)
        
        # Buscar dados reais
        funcionario_id = 1
        epis_reais = _buscar_epis_funcionario(funcionario_id)
        empresas_reais = get_empresas_para_funcionario()
        
        # Buscar dados do funcionário do banco
        try:
            connection = pymysql.connect(
                host='localhost',
                user='cavalcrod',
                password='200381',
                database='controle_ponto',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT * FROM funcionarios WHERE id = %s", (funcionario_id,))
                funcionario = cursor.fetchone()
                
                if not funcionario:
                    print("❌ Funcionário não encontrado")
                    return False
                
        except Exception as e:
            print(f"❌ Erro ao buscar funcionário: {e}")
            return False
        finally:
            if 'connection' in locals():
                connection.close()
        
        # Montar dados do formulário como seria enviado pelo navegador
        form_data = {}
        
        # Dados básicos do funcionário
        campos_basicos = [
            'nome_completo', 'cpf', 'rg', 'data_nascimento', 'sexo', 'estado_civil',
            'nacionalidade', 'ctps_numero', 'ctps_serie_uf', 'pis_pasep',
            'endereco_cep', 'endereco_estado', 'endereco_rua', 'endereco_bairro',
            'endereco_cidade', 'telefone1', 'telefone2', 'email', 'empresa_id',
            'cargo', 'setor_obra', 'matricula_empresa', 'data_admissao',
            'tipo_contrato', 'nivel_acesso', 'turno', 'tolerancia_ponto',
            'status_cadastro', 'salario_base', 'horas_trabalho_obrigatorias'
        ]
        
        for campo in campos_basicos:
            valor = funcionario.get(campo)
            if valor is not None:
                if hasattr(valor, 'strftime'):
                    form_data[campo] = valor.strftime('%Y-%m-%d')
                else:
                    form_data[campo] = str(valor)
            else:
                form_data[campo] = ''
        
        # ⚠️ ADICIONAR EPIs REAIS (aqui pode estar o problema!)
        print(f"🦺 Adicionando {len(epis_reais)} EPIs ao formulário...")
        for i, epi in enumerate(epis_reais):
            for campo, valor in epi.items():
                form_key = f"epis[{i}][{campo}]"
                form_data[form_key] = str(valor) if valor is not None else ''
                
                # Log campos problemáticos
                if campo == 'id':
                    print(f"  ⚠️ Adicionando EPI ID: {form_key} = {valor}")
        
        print(f"📋 Total de campos no formulário: {len(form_data)}")
        print("📋 Campos EPIs:")
        epi_fields = [k for k in form_data.keys() if k.startswith('epis[')]
        for field in epi_fields[:10]:  # Mostrar apenas os primeiros 10
            print(f"  {field}: {form_data[field]}")
        if len(epi_fields) > 10:
            print(f"  ... e mais {len(epi_fields) - 10} campos EPIs")
        
        # Simular processamento
        with app.test_request_context('/', method='POST', data=form_data):
            try:
                print("\n🔍 Extraindo dados do formulário...")
                dados_extraidos = _extrair_dados_formulario()
                
                print("✅ Dados extraídos com sucesso")
                print(f"📊 Campos extraídos: {len(dados_extraidos)}")
                
                # Verificar se 'id' está nos dados principais
                if 'id' in dados_extraidos:
                    print(f"\n❌ PROBLEMA ENCONTRADO!")
                    print(f"  Campo 'id' nos dados principais: {dados_extraidos['id']}")
                    print(f"  Tipo: {type(dados_extraidos['id'])}")
                    
                    # Investigar de onde veio
                    print(f"\n🔍 INVESTIGANDO ORIGEM DO CAMPO 'id':")
                    for key, value in form_data.items():
                        if 'id' in key.lower():
                            print(f"  Formulário: {key} = {value}")
                else:
                    print("\n✅ Campo 'id' NÃO está nos dados principais")
                
                # Verificar EPIs extraídos
                epis_extraidos = dados_extraidos.get('epis', [])
                print(f"\n🦺 EPIs extraídos: {len(epis_extraidos)}")
                for i, epi in enumerate(epis_extraidos[:3]):  # Mostrar apenas os primeiros 3
                    print(f"  EPI {i}: {epi}")
                
                print("\n🔍 Validando dados...")
                validator = FormValidator()
                dados_extraidos['funcionario_id'] = funcionario_id
                _validar_dados_funcionario(dados_extraidos, validator)
                
                if validator.has_errors():
                    print("❌ ERROS ENCONTRADOS:")
                    errors_dict = validator.get_errors()
                    
                    for field, field_errors in errors_dict.items():
                        print(f"  {field}: {field_errors}")
                        
                        # Verificar erro específico do 'id'
                        if field == 'id':
                            print(f"    🎯 ERRO 'id' CONFIRMADO!")
                            print(f"    Valor problemático: {dados_extraidos.get('id', 'NÃO ENCONTRADO')}")
                    
                    return False
                else:
                    print("✅ Validação passou sem erros!")
                    return True
                    
            except Exception as e:
                print(f"❌ ERRO durante processamento: {e}")
                print(f"Traceback: {traceback.format_exc()}")
                return False
    
    if __name__ == "__main__":
        print("🐛 DEBUG: PROBLEMA EPIs + EMPRESA NA EDIÇÃO")
        print("=" * 60)
        
        verificar_epis_funcionario()
        verificar_empresas_disponiveis()
        
        sucesso = simular_edicao_com_epis_reais()
        
        print("\n" + "=" * 60)
        print("📊 DIAGNÓSTICO FINAL")
        print("=" * 60)
        
        if sucesso:
            print("✅ SIMULAÇÃO PASSOU!")
            print("🤔 O problema pode estar em outro lugar")
        else:
            print("❌ PROBLEMA REPRODUZIDO!")
            print("🎯 Erro relacionado a EPIs ou empresa identificado")

except ImportError as e:
    print(f"❌ Erro de import: {e}")
except Exception as e:
    print(f"❌ Erro inesperado: {e}")
    print(f"Traceback: {traceback.format_exc()}")
