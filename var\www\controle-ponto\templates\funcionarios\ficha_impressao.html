<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes do Funcionário - {{ funcionario.nome_completo }}</title>
    <style>
        /* 🎨 PADRONIZADO COMO MODAL DE CLIENTE - Layout Limpo e Profissional */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 9pt;
            line-height: 1.2;
            color: #333;
            background: #fff;
            padding: 15px;
            max-width: 800px;
            margin: 0 auto;
        }

        /* ===== CABEÇALHO PRINCIPAL ===== */
        .header-section {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
        }

        .header-icon {
            width: 30px;
            height: 30px;
            background: #4fbdba;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
        }

        .header-content h1 {
            font-size: 12pt;
            font-weight: 600;
            color: #333;
            margin-bottom: 3px;
        }

        .header-subtitle {
            font-size: 8pt;
            color: #666;
        }
        }

        /* ===== SEÇÃO DO FUNCIONÁRIO ===== */
        .funcionario-section {
            margin-bottom: 10px;
        }

        .funcionario-header {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 8px;
            margin-bottom: 10px;
            border-radius: 3px;
        }

        .funcionario-nome {
            font-size: 10pt;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .funcionario-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6px;
            font-size: 8pt;
        }

        .funcionario-info .info-item {
            display: flex;
            justify-content: space-between;
        }

        .info-label {
            font-weight: 600;
            color: #555;
        }

        .info-value {
            color: #333;
        }

        /* ===== CARDS DE INFORMAÇÃO (COMO MODAL DE CLIENTE) ===== */
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 12px;
        }

        .info-card {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            padding: 8px 10px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .card-header-icon {
            color: #4fbdba;
            font-size: 12px;
        }

        .card-title {
            font-size: 9pt;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .card-body {
            padding: 10px;
        }

        .field-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 3px 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .field-row:last-child {
            border-bottom: none;
        }

        .field-label {
            font-weight: 500;
            color: #666;
            font-size: 8pt;
        }

        .field-value {
            color: #333;
            font-size: 8pt;
            text-align: right;
            max-width: 60%;
            word-wrap: break-word;
        }

        /* ===== STATUS BADGES ===== */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 9pt;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-ativo {
            background: #d4edda;
            color: #155724;
        }

        .status-inativo {
            background: #f8d7da;
            color: #721c24;
        }

        /* ===== SEÇÃO DE JORNADA ===== */
        .jornada-section {
            margin-top: 12px;
        }

        .jornada-card {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }

        .jornada-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 6px;
            padding: 10px;
        }

        .jornada-item {
            text-align: center;
            padding: 5px;
            background: #f8f9fa;
            border-radius: 3px;
        }

        .jornada-label {
            font-size: 7pt;
            color: #666;
            font-weight: 500;
            margin-bottom: 1px;
        }

        .jornada-value {
            font-size: 8pt;
            color: #333;
            font-weight: 600;
        }

        /* ===== SEÇÃO DE EPIs ===== */
        .epis-section {
            margin-top: 12px;
        }

        .epis-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 7pt;
            margin-top: 5px;
        }

        .epis-table th,
        .epis-table td {
            border: 1px solid #ddd;
            padding: 3px 5px;
            text-align: left;
        }

        .epis-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .epis-table td {
            color: #333;
        }

        /* ===== RODAPÉ ===== */
        .footer {
            margin-top: 15px;
            padding-top: 8px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 7pt;
            color: #666;
        }

        /* ===== ESTILOS DE IMPRESSÃO ===== */
        @media print {
            @page {
                size: A4;
                margin: 0.3cm;
            }

            body {
                padding: 0 !important;
                margin: 0 !important;
                font-size: 7pt !important;
                line-height: 1.0 !important;
            }

            .header-section {
                padding: 8px !important;
                margin-bottom: 8px !important;
            }

            .header-content h1 {
                font-size: 10pt !important;
            }

            .header-subtitle {
                font-size: 7pt !important;
            }

            .funcionario-header {
                padding: 6px !important;
                margin-bottom: 6px !important;
            }

            .funcionario-nome {
                font-size: 9pt !important;
            }

            .info-grid {
                gap: 8px !important;
                margin-bottom: 8px !important;
            }

            .card-header {
                padding: 6px 8px !important;
            }

            .card-title {
                font-size: 8pt !important;
            }

            .card-body {
                padding: 8px !important;
            }

            .field-row {
                padding: 2px 0 !important;
            }

            .field-label, .field-value {
                font-size: 7pt !important;
            }

            .jornada-grid {
                padding: 8px !important;
                gap: 4px !important;
            }

            .jornada-item {
                padding: 4px !important;
            }

            .jornada-label {
                font-size: 6pt !important;
            }

            .jornada-value {
                font-size: 7pt !important;
            }

            .empresa-header {
                padding-bottom: 5px;
                margin-bottom: 8px;
                border-bottom: 1px solid #333;
            }

            .empresa-nome {
                font-size: 12pt !important;
                margin-bottom: 2px;
            }

            .empresa-cnpj {
                font-size: 8pt !important;
                margin-bottom: 2px;
            }



            .data-geracao {
                font-size: 7pt !important;
                margin-top: 2px;
            }

            .funcionario-section {
                margin-bottom: 8px;
            }

            .funcionario-header {
                padding: 6px;
                margin-bottom: 8px;
            }

            .funcionario-nome {
                font-size: 10pt !important;
                margin-bottom: 3px;
            }

            .funcionario-info {
                gap: 5px;
                font-size: 7pt !important;
            }

            .info-grid {
                gap: 8px;
                margin-bottom: 8px;
            }

            .section-header {
                padding: 4px 6px;
                font-size: 8pt !important;
            }

            .section-content {
                padding: 5px;
            }

            .field-row {
                margin-bottom: 2px;
                font-size: 7pt !important;
            }

            .epis-section {
                margin-top: 8px;
            }

            .epis-table {
                font-size: 6pt !important;
                margin-top: 3px;
            }

            .epis-table th,
            .epis-table td {
                padding: 2px 3px;
            }

            .footer {
                margin-top: 10px;
                padding-top: 5px;
                font-size: 6pt !important;
            }

            /* Ocultar elementos desnecessários na impressão */
            .no-print {
                display: none !important;
            }

            /* Forçar quebra de página se necessário */
            .page-break {
                page-break-before: always;
            }
        }

        /* ===== BOTÕES DE AÇÃO (apenas tela) ===== */
        .action-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .btn-action {
            padding: 8px 15px;
            border: none;
            border-radius: 6px;
            font-size: 10pt;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-print {
            background: #4fbdba;
            color: white;
        }

        .btn-print:hover {
            background: #26a69a;
            transform: translateY(-1px);
        }

        .btn-back {
            background: #6c757d;
            color: white;
        }

        .btn-back:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        @media print {
            .action-buttons {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Botões de Ação (apenas na tela) -->
    <div class="action-buttons no-print">
        <button onclick="window.print()" class="btn-action btn-print">
            🖨️ Imprimir
        </button>
        <a href="{{ url_for('funcionarios.detalhes', funcionario_id=funcionario.id) }}" class="btn-action btn-back">
            ← Voltar
        </a>
    </div>

    <!-- Cabeçalho Principal (Como Modal de Cliente) -->
    <div class="header-section">
        <div class="header-icon">👤</div>
        <div class="header-content">
            <h1>Detalhes do Funcionário</h1>
            <div class="header-subtitle">
                {{ empresa.nome_fantasia or empresa.razao_social or 'EMPRESA' }}
                {% if empresa.cnpj %} • CNPJ: {{ empresa.cnpj }}{% endif %}
                • Gerado em: {{ data_geracao }}
            </div>
        </div>
    </div>

    <!-- Informações Principais do Funcionário -->
    <div class="funcionario-section">
        <div class="funcionario-header">
            <div class="funcionario-nome">{{ funcionario.nome_completo }}</div>
            <div class="funcionario-info">
                <div class="info-item">
                    <span class="info-label">Matrícula:</span>
                    <span class="info-value">{{ funcionario.matricula_empresa }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Status:</span>
                    <span class="info-value">
                        <span class="status-badge {% if funcionario.status_cadastro == 'Ativo' %}status-ativo{% else %}status-inativo{% endif %}">
                            {{ funcionario.status_cadastro }}
                        </span>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Cargo:</span>
                    <span class="info-value">{{ funcionario.cargo }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Setor:</span>
                    <span class="info-value">{{ funcionario.setor_obra }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Grid de Informações Detalhadas (Como Modal de Cliente) -->
    <div class="info-grid">
        <!-- Informações da Empresa -->
        <div class="info-card">
            <div class="card-header">
                <span class="card-header-icon">🏢</span>
                <h3 class="card-title">Informações da Empresa</h3>
            </div>
            <div class="card-body">
                <div class="field-row">
                    <span class="field-label">Razão Social:</span>
                    <span class="field-value">{{ funcionario.empresa_nome or 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Nome Fantasia:</span>
                    <span class="field-value">{{ funcionario.empresa_nome or 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">CNPJ:</span>
                    <span class="field-value">{{ empresa.cnpj or 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Telefone:</span>
                    <span class="field-value">{{ funcionario.telefone1 | format_telefone if funcionario.telefone1 else 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">E-mail:</span>
                    <span class="field-value">{{ funcionario.email or 'Não informado' }}</span>
                </div>
            </div>
        </div>

        <!-- Informações do Contrato -->
        <div class="info-card">
            <div class="card-header">
                <span class="card-header-icon">📋</span>
                <h3 class="card-title">Informações do Contrato</h3>
            </div>
            <div class="card-body">
                <div class="field-row">
                    <span class="field-label">Status:</span>
                    <span class="field-value">
                        <span class="status-badge {% if funcionario.status_cadastro == 'Ativo' %}status-ativo{% else %}status-inativo{% endif %}">
                            {{ funcionario.status_cadastro }}
                        </span>
                    </span>
                </div>
                <div class="field-row">
                    <span class="field-label">Data de Início:</span>
                    <span class="field-value">{{ funcionario.data_admissao | format_date }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Criado em:</span>
                    <span class="field-value">{{ funcionario.criado_em | format_datetime if funcionario.criado_em else 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Atualizado em:</span>
                    <span class="field-value">{{ funcionario.atualizado_em | format_datetime if funcionario.atualizado_em else 'Não informado' }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Dados Pessoais e Profissionais -->
    <div class="info-grid">
        <!-- Dados Pessoais -->
        <div class="info-card">
            <div class="card-header">
                <span class="card-header-icon">👤</span>
                <h3 class="card-title">Dados Pessoais</h3>
            </div>
            <div class="card-body">
                <div class="field-row">
                    <span class="field-label">Nome:</span>
                    <span class="field-value">{{ funcionario.nome_completo }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">CPF:</span>
                    <span class="field-value">{{ funcionario.cpf | format_cpf }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">RG:</span>
                    <span class="field-value">{{ funcionario.rg or 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Data Nascimento:</span>
                    <span class="field-value">{{ funcionario.data_nascimento | format_date }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Telefone:</span>
                    <span class="field-value">{{ funcionario.telefone1 | format_telefone if funcionario.telefone1 else 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">E-mail:</span>
                    <span class="field-value">{{ funcionario.email or 'Não informado' }}</span>
                </div>
            </div>
        </div>

        <!-- Dados Profissionais -->
        <div class="info-card">
            <div class="card-header">
                <span class="card-header-icon">💼</span>
                <h3 class="card-title">Dados Profissionais</h3>
            </div>
            <div class="card-body">
                <div class="field-row">
                    <span class="field-label">Cargo:</span>
                    <span class="field-value">{{ funcionario.cargo }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Setor:</span>
                    <span class="field-value">{{ funcionario.setor_obra }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Data Admissão:</span>
                    <span class="field-value">{{ funcionario.data_admissao | format_date }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">Tipo Contrato:</span>
                    <span class="field-value">{{ funcionario.tipo_contrato or 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">CTPS:</span>
                    <span class="field-value">{{ funcionario.ctps_numero or 'Não informado' }}</span>
                </div>
                <div class="field-row">
                    <span class="field-label">PIS/PASEP:</span>
                    <span class="field-value">{{ funcionario.pis_pasep or 'Não informado' }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Jornada de Trabalho -->
    {% if jornada %}
    <div class="jornada-section">
        <div class="jornada-card">
            <div class="card-header">
                <span class="card-header-icon">⏰</span>
                <h3 class="card-title">Jornada de Trabalho</h3>
            </div>
            <div class="jornada-grid">
                <div class="jornada-item">
                    <div class="jornada-label">Jornada:</div>
                    <div class="jornada-value">{{ jornada.nome_jornada or 'Jornada Padrão' }}</div>
                </div>
                <div class="jornada-item">
                    <div class="jornada-label">Seg-Qui:</div>
                    <div class="jornada-value">{{ jornada.seg_qui_entrada or '00:00' }} às {{ jornada.seg_qui_saida or '00:00' }}</div>
                </div>
                <div class="jornada-item">
                    <div class="jornada-label">Sexta:</div>
                    <div class="jornada-value">{{ jornada.sexta_entrada or '00:00' }} às {{ jornada.sexta_saida or '00:00' }}</div>
                </div>
                <div class="jornada-item">
                    <div class="jornada-label">Intervalo:</div>
                    <div class="jornada-value">{{ jornada.intervalo_inicio or '00:00' }} às {{ jornada.intervalo_fim or '00:00' }}</div>
                </div>
                <div class="jornada-item">
                    <div class="jornada-label">Tolerância:</div>
                    <div class="jornada-value">{{ jornada.tolerancia or '5' }} min</div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="jornada-section">
        <div class="jornada-card">
            <div class="card-header">
                <span class="card-header-icon">⏰</span>
                <h3 class="card-title">Jornada de Trabalho</h3>
            </div>
            <div class="card-body">
                <div class="field-row">
                    <span class="field-label">Status:</span>
                    <span class="field-value">Não configurada</span>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Observações e Assinatura -->
    <div class="info-grid">
        <!-- Observações (1 linha) -->
        <div class="info-card">
            <div class="card-header">
                <span class="card-header-icon">📝</span>
                <h3 class="card-title">Observações</h3>
            </div>
            <div class="card-body">
                <div style="height: 30px; border: 1px dashed #ddd; padding: 5px; font-size: 8pt; color: #999; background: #fafafa;">
                    Espaço para anotações manuais sobre o funcionário
                </div>
            </div>
        </div>

        <!-- Assinatura do Funcionário -->
        <div class="info-card">
            <div class="card-header">
                <span class="card-header-icon">✍️</span>
                <h3 class="card-title">Assinatura do Funcionário</h3>
            </div>
            <div class="card-body">
                <div style="height: 40px; border-bottom: 1px solid #333; margin-bottom: 5px;"></div>
                <div style="font-size: 7pt; color: #666; text-align: center;">
                    Data: ___/___/______
                </div>
            </div>
        </div>
    </div>

    <!-- EPIs (se houver) -->
    {% if funcionario.epis and funcionario.epis|length > 0 %}
    <div class="epis-section">
        <div class="section-header">Equipamentos de Proteção Individual (EPIs)</div>
        <table class="epis-table">
            <thead>
                <tr>
                    <th>Nome do EPI</th>
                    <th>CA</th>
                    <th>Data Entrega</th>
                    <th>Data Vencimento</th>
                </tr>
            </thead>
            <tbody>
                {% for epi in funcionario.epis %}
                <tr>
                    <td>{{ epi.epi_nome or 'Não informado' }}</td>
                    <td>{{ epi.epi_ca or 'N/A' }}</td>
                    <td>{{ epi.epi_data_entrega | format_date if epi.epi_data_entrega else 'N/A' }}</td>
                    <td>{{ epi.epi_data_vencimento | format_date if epi.epi_data_vencimento else 'N/A' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}

    <!-- Rodapé -->
    <div class="footer">
        <p>Sistema de Controle de Ponto - RLPONTO-WEB v1.0</p>
        <p>© 2025 AiNexus Tecnologia. Todos os direitos reservados.</p>
    </div>
</body>
</html>
