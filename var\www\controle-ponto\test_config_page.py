#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste de Diagnóstico da Página de Configurações
===============================================

Este script testa se a página de configurações está funcionando
corretamente e identifica problemas específicos.
"""

import sys
import os
import traceback
import requests

# Adicionar o diretório do projeto ao path
sys.path.insert(0, '/var/www/controle-ponto')
sys.path.insert(0, '.')

def test_imports():
    """Testa se todas as importações estão funcionando"""
    print("🔍 TESTANDO IMPORTAÇÕES...")
    
    try:
        from app_configuracoes import configuracoes_bp
        print("✅ app_configuracoes importado com sucesso")
        print(f"   - URL Prefix: {configuracoes_bp.url_prefix}")
        print(f"   - Nome: {configuracoes_bp.name}")
    except Exception as e:
        print(f"❌ Erro ao importar app_configuracoes: {e}")
        traceback.print_exc()
        return False
    
    try:
        from app_biometric_config import biometric_config_bp
        print("✅ app_biometric_config importado com sucesso")
        print(f"   - URL Prefix: {biometric_config_bp.url_prefix}")
        print(f"   - Nome: {biometric_config_bp.name}")
    except Exception as e:
        print(f"❌ Erro ao importar app_biometric_config: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_database_connection():
    """Testa a conexão com o banco de dados"""
    print("\n🔍 TESTANDO CONEXÃO COM BANCO...")
    
    try:
        from utils.database import get_db_connection
        conn = get_db_connection()
        
        with conn.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) as total FROM empresas")
            result = cursor.fetchone()
            print(f"✅ Conexão OK - {result['total']} empresas encontradas")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro na conexão com banco: {e}")
        traceback.print_exc()
        return False

def test_template_exists():
    """Verifica se os templates existem"""
    print("\n🔍 VERIFICANDO TEMPLATES...")
    
    templates = [
        'templates/configuracoes/index.html',
        'templates/configuracoes/biometria.html'
    ]
    
    all_ok = True
    for template in templates:
        if os.path.exists(template):
            print(f"✅ {template} existe")
        else:
            print(f"❌ {template} NÃO EXISTE")
            all_ok = False
    
    return all_ok

def test_flask_app():
    """Testa se a aplicação Flask está funcionando"""
    print("\n🔍 TESTANDO APLICAÇÃO FLASK...")
    
    try:
        from app import app
        print("✅ Aplicação Flask importada")
        
        # Registra blueprints se necessário
        try:
            from app_configuracoes import configuracoes_bp
            from app_biometric_config import biometric_config_bp
            
            # Verifica se já estão registrados
            registered_blueprints = [bp.name for bp in app.blueprints.values()]
            print(f"📋 Blueprints registrados: {registered_blueprints}")
            
            if 'configuracoes' not in registered_blueprints:
                app.register_blueprint(configuracoes_bp)
                print("✅ configuracoes_bp registrado")
            
            if 'biometric_config' not in registered_blueprints:
                app.register_blueprint(biometric_config_bp)
                print("✅ biometric_config_bp registrado")
            
        except Exception as e:
            print(f"⚠️ Blueprints já registrados ou erro: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na aplicação Flask: {e}")
        traceback.print_exc()
        return False

def test_web_access():
    """Testa acesso via HTTP"""
    print("\n🔍 TESTANDO ACESSO WEB...")
    
    # URLs para testar
    base_url = "http://10.19.208.31:5000"
    test_urls = [
        f"{base_url}/login",
        f"{base_url}/configuracoes",
        f"{base_url}/configuracoes/biometria"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5, allow_redirects=False)
            if response.status_code in [200, 302, 401, 403]:
                print(f"✅ {url} - Status: {response.status_code}")
            else:
                print(f"⚠️ {url} - Status: {response.status_code}")
        except Exception as e:
            print(f"❌ {url} - Erro: {e}")

def main():
    """Função principal de teste"""
    print("🚀 DIAGNÓSTICO COMPLETO DA PÁGINA DE CONFIGURAÇÕES")
    print("=" * 60)
    
    # Lista de testes
    tests = [
        ("Importações", test_imports),
        ("Banco de Dados", test_database_connection),
        ("Templates", test_template_exists),
        ("Aplicação Flask", test_flask_app),
        ("Acesso Web", test_web_access)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ ERRO CRÍTICO em {test_name}: {e}")
            results[test_name] = False
    
    # Resumo
    print("\n" + "=" * 60)
    print("📊 RESUMO DOS TESTES:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASSOU" if passed else "❌ FALHOU"
        print(f"{test_name:<20} : {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 TODOS OS TESTES PASSARAM! Sistema está OK!")
    else:
        print("🔧 ALGUNS TESTES FALHARAM! Verificar problemas acima.")
    print("=" * 60)

if __name__ == "__main__":
    main() 