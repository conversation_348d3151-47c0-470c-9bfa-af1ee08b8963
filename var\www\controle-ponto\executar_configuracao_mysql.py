#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para executar configuração MySQL remota
Execute este script para configurar permissões no servidor MySQL
"""

import pymysql
import sys

def conectar_mysql():
    """Conecta com o servidor MySQL"""
    try:
        # Conectar como root sem senha
        print("Conectando ao MySQL como root (sem senha)...")
        
        connection = pymysql.connect(
            host='************',
            user='root',
            password='',  # Senha vazia
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        print("✓ Conectado ao MySQL servidor ************")
        return connection
        
    except pymysql.Error as e:
        print(f"✗ Erro ao conectar com MySQL: {e}")
        return None

def executar_configuracao_permissoes(connection):
    """Executa os comandos de configuração de permissões"""
    
    comandos = [
        "CREATE USER IF NOT EXISTS 'controle_user'@'***********' IDENTIFIED BY 'controle123';",
        "CREATE USER IF NOT EXISTS 'controle_user'@'10.19.208.%' IDENTIFIED BY 'controle123';",
        "CREATE USER IF NOT EXISTS 'controle_user'@'localhost' IDENTIFIED BY 'controle123';",
        "GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'***********';",
        "GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'10.19.208.%';",
        "GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'localhost';",
        "FLUSH PRIVILEGES;"
    ]
    
    try:
        with connection.cursor() as cursor:
            for comando in comandos:
                print(f"Executando: {comando}")
                cursor.execute(comando)
                print("✓ Comando executado com sucesso")
                
        connection.commit()
        print("\n✓ Todas as permissões foram configuradas com sucesso!")
        
        # Verificar usuários criados
        with connection.cursor() as cursor:
            cursor.execute("SELECT User, Host FROM mysql.user WHERE User = 'controle_user'")
            usuarios = cursor.fetchall()
            
            print("\n📋 Usuários 'controle_user' criados:")
            for usuario in usuarios:
                print(f"   - {usuario['User']}@{usuario['Host']}")
                
        return True
        
    except pymysql.Error as e:
        print(f"✗ Erro ao executar configuração: {e}")
        return False

def main():
    """Função principal"""
    print("🔧 Configurando permissões MySQL para sistema de controle de ponto...")
    print("=" * 60)
    
    # Conectar ao MySQL
    connection = conectar_mysql()
    if not connection:
        print("❌ Falha na conexão. Verifique se o servidor MySQL está acessível.")
        return False
    
    try:
        # Executar configuração
        sucesso = executar_configuracao_permissoes(connection)
        
        if sucesso:
            print("\n🎉 Configuração de permissões concluída com sucesso!")
            print("\nPróximo passo: executar script de limpeza do banco")
            return True
        else:
            print("\n❌ Falha na configuração de permissões.")
            return False
            
    finally:
        connection.close()
        print("\n🔌 Conexão MySQL fechada.")

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1) 