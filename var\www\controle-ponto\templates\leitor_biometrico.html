<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title>Captura de Biometria</title>
  <link rel="stylesheet" href="{{ url_for('static','style-leitor-biometrico.css') }}">
  <script src="{{ url_for('static','biometria-service.js') }}" defer></script>
</head>
<body>
  <div class="biometria-modal">
    <div class="biometria-content">
      <div class="leitor-status">
        <span id="leitor-indicator" class="status-offline">●</span>
        <span id="leitor-text">Offline</span>
      </div>
      <h2>Captura de Biometria</h2>
      <div class="finger-selection">
        <button id="dedo1" class="finger-btn">Dedo 1</button>
        <button id="dedo2" class="finger-btn">Dedo 2</button>
      </div>
      <div class="biometria-display">
        <div class="digital-preview">
          <img src="{{ url_for('static','impressao_digital_icon.png') }}" alt="Impressão Digital">
          <p>Qualidade: <span id="qualidade">0%</span></p>
        </div>
      </div>
      <div class="biometria-controls">
        <button id="capturar" class="control-btn" disabled>Capturar</button>
        <button id="salvar" class="control-btn" disabled>Salvar Biometria</button>
        <button id="fechar" class="control-btn">Fechar</button>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const svc = new BiometriaService();
      const ind = document.getElementById('leitor-indicator');
      const txt = document.getElementById('leitor-text');
      const btnCapt = document.getElementById('capturar');
      const btnSal = document.getElementById('salvar');
      const qual = document.getElementById('qualidade');
      let currentTpl = null;

      svc.on('onConnect', () => {
        console.log("[Front] onConnect");
      });
      svc.on('onDisconnect', () => {
        console.log("[Front] onDisconnect");
      });
      svc.on('onReaderStatus', online => {
        console.log("[Front] onReaderStatus:", online);
        ind.classList.toggle('status-online', online);
        ind.classList.toggle('status-offline', !online);
        txt.textContent = online ? 'Online' : 'Offline';
        btnCapt.disabled = !online;
      });
      svc.on('onCapture', data => {
        console.log("[Front] onCapture:", data);
        qual.textContent = data.quality + '%';
        btnSal.disabled = !data.success;
        if (data.success) currentTpl = data.template;
      });

      // Botões
      btnCapt.onclick = () => svc.capturarDigital().catch(e => console.error("Falha na captura:", e));
      btnSal.onclick   = () => {
        console.log("[Front] Salvando template:", currentTpl);
        // faça fetch para seu endpoint Flask aqui...
      };
      document.getElementById('fechar').onclick = () => window.close();
    });
  </script>

  <style>
    .leitor-status { display:flex; align-items:center; margin-bottom:10px; }
    #leitor-indicator { font-size:1.5em; margin-right:8px; }
    .status-online { color:#28a745; }
    .status-offline { color:#dc3545; }
  </style>
</body>
</html>
