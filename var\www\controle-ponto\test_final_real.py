#!/usr/bin/env python3
import requests
from bs4 import BeautifulSoup

def test_final_real():
    print("🧪 TESTE FINAL REAL - COM USUÁRIO ADMIN CRIADO")
    print("=" * 60)
    
    # Criar sessão para manter cookies
    session = requests.Session()
    base_url = "http://10.19.208.31:5000"
    
    try:
        # 1. Acessar página de login
        print("1. Acessando página de login...")
        login_response = session.get(f"{base_url}/login")
        print(f"   Status: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print("   ❌ Falha ao acessar login")
            return
        
        # 2. Fazer login com credenciais corretas
        print("2. Fazendo login com admin/@Ric6109...")
        login_data = {
            'usuario': 'admin',  # Campo correto é 'usuario', não 'username'
            'senha': '@Ric6109'
        }
        
        login_post = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        print(f"   Status: {login_post.status_code}")
        print(f"   Headers: {dict(login_post.headers)}")
        
        # Verificar se houve redirecionamento (sucesso no login)
        if login_post.status_code == 302:
            print("   ✅ Login bem-sucedido (redirecionamento)")
            redirect_location = login_post.headers.get('Location', '')
            print(f"   Redirecionando para: {redirect_location}")
        else:
            print("   ❌ Falha no login")
            print(f"   Conteúdo: {login_post.text[:200]}...")
            return
        
        # 3. Seguir redirecionamento
        print("3. Seguindo redirecionamento...")
        if redirect_location.startswith('/'):
            redirect_url = f"{base_url}{redirect_location}"
        else:
            redirect_url = redirect_location
            
        redirect_response = session.get(redirect_url)
        print(f"   Status: {redirect_response.status_code}")
        
        # 4. Acessar página de configurações
        print("4. Acessando página de configurações...")
        config_response = session.get(f"{base_url}/configuracoes/")
        print(f"   Status: {config_response.status_code}")
        
        if config_response.status_code == 200:
            print("   ✅ Acesso às configurações bem-sucedido!")
            
            # 5. Analisar HTML da página de configurações
            print("5. Analisando HTML da página de configurações...")
            soup = BeautifulSoup(config_response.text, 'html.parser')
            
            # Verificar título da página
            title = soup.find('title')
            if title:
                print(f"   Título: {title.text}")
            
            # Verificar se existe a aba empresas
            empresas_tab = soup.find('button', {'id': 'empresas-tab'})
            if empresas_tab:
                print("   ✅ Aba Empresas encontrada no HTML")
                print(f"   Atributos: {empresas_tab.attrs}")
            else:
                print("   ❌ Aba Empresas NÃO encontrada no HTML")
                # Procurar por qualquer referência a empresas
                empresas_refs = soup.find_all(text=lambda text: text and 'empresa' in text.lower())
                print(f"   Referências a 'empresa' encontradas: {len(empresas_refs)}")
                for ref in empresas_refs[:3]:
                    print(f"     - {ref.strip()}")
            
            # Verificar se existe o conteúdo da aba empresas
            empresas_content = soup.find('div', {'id': 'empresas'})
            if empresas_content:
                print("   ✅ Conteúdo da aba Empresas encontrado")
                print(f"   Tamanho do conteúdo: {len(str(empresas_content))} caracteres")
            else:
                print("   ❌ Conteúdo da aba Empresas NÃO encontrado")
            
            # Verificar JavaScript Bootstrap
            scripts = soup.find_all('script')
            bootstrap_js_found = False
            for script in scripts:
                if script.string and 'bootstrap.Tab' in script.string:
                    bootstrap_js_found = True
                    print("   ✅ JavaScript Bootstrap encontrado")
                    break
            
            if not bootstrap_js_found:
                print("   ❌ JavaScript Bootstrap NÃO encontrado")
            
            # Salvar HTML para análise detalhada
            with open('debug_configuracoes_final.html', 'w', encoding='utf-8') as f:
                f.write(config_response.text)
            print("   📄 HTML salvo em debug_configuracoes_final.html")
            
        elif config_response.status_code == 302:
            print("   ❌ Ainda redirecionando (problema de autenticação)")
            print(f"   Location: {config_response.headers.get('Location', 'N/A')}")
        else:
            print(f"   ❌ Erro ao acessar configurações (código {config_response.status_code})")
        
        # 6. Testar acesso direto à página de empresas
        print("6. Testando acesso direto à página de empresas...")
        empresas_response = session.get(f"{base_url}/configuracoes/empresas")
        print(f"   Status: {empresas_response.status_code}")
        
        if empresas_response.status_code == 200:
            print("   ✅ Página de empresas acessível")
            with open('debug_empresas_final.html', 'w', encoding='utf-8') as f:
                f.write(empresas_response.text)
            print("   📄 HTML salvo em debug_empresas_final.html")
        else:
            print(f"   ❌ Página de empresas não acessível")
        
        # 7. Verificar cookies de sessão
        print("7. Verificando cookies de sessão...")
        cookies = session.cookies.get_dict()
        print(f"   Cookies: {cookies}")
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
    
    print("\n" + "=" * 60)
    print("📋 RESULTADO DO TESTE FINAL")
    print("=" * 60)

if __name__ == "__main__":
    test_final_real()
