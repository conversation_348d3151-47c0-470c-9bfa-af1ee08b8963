#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste das Funções de Alocações
==============================

Script para testar individualmente cada função usada na página de alocações
e identificar qual está causando a exceção.

Data: 07/07/2025
"""

import sys
import os
sys.path.append('var/www/controle-ponto')

def testar_get_empresa_principal():
    """Testa a função get_empresa_principal"""
    print("1️⃣ TESTANDO get_empresa_principal()...")
    
    try:
        from app_empresa_principal import get_empresa_principal
        resultado = get_empresa_principal()
        
        if resultado:
            print(f"   ✅ Empresa principal encontrada: {resultado.get('razao_social', 'N/A')}")
            return True
        else:
            print(f"   ⚠️ Nenhuma empresa principal definida")
            return False
            
    except Exception as e:
        print(f"   ❌ ERRO: {e}")
        return False

def testar_get_todas_alocacoes():
    """Testa a função get_todas_alocacoes"""
    print("2️⃣ TESTANDO get_todas_alocacoes()...")
    
    try:
        from app_empresa_principal import get_todas_alocacoes
        resultado = get_todas_alocacoes()
        
        if resultado is not None:
            print(f"   ✅ Alocações encontradas: {len(resultado)}")
            if resultado:
                print(f"   📋 Primeira alocação: {resultado[0].get('nome', 'N/A')}")
            return True
        else:
            print(f"   ⚠️ Nenhuma alocação encontrada")
            return True  # Não é erro, pode não ter alocações
            
    except Exception as e:
        print(f"   ❌ ERRO: {e}")
        import traceback
        print(f"   📋 Detalhes: {traceback.format_exc()}")
        return False

def testar_get_clientes_da_empresa_principal():
    """Testa a função get_clientes_da_empresa_principal"""
    print("3️⃣ TESTANDO get_clientes_da_empresa_principal()...")
    
    try:
        from app_empresa_principal import get_clientes_da_empresa_principal
        resultado = get_clientes_da_empresa_principal()
        
        if resultado is not None:
            print(f"   ✅ Clientes encontrados: {len(resultado)}")
            if resultado:
                print(f"   📋 Primeiro cliente: {resultado[0].get('razao_social', 'N/A')}")
            return True
        else:
            print(f"   ⚠️ Nenhum cliente encontrado")
            return True  # Não é erro, pode não ter clientes
            
    except Exception as e:
        print(f"   ❌ ERRO: {e}")
        import traceback
        print(f"   📋 Detalhes: {traceback.format_exc()}")
        return False

def testar_calculo_estatisticas():
    """Testa o cálculo de estatísticas"""
    print("4️⃣ TESTANDO cálculo de estatísticas...")
    
    try:
        from app_empresa_principal import get_todas_alocacoes
        alocacoes = get_todas_alocacoes()
        
        # Simular o cálculo que é feito na função alocacoes()
        stats = {
            'total_alocacoes': len(alocacoes) if alocacoes else 0,
            'alocacoes_ativas': len([a for a in alocacoes if a['ativo']]) if alocacoes else 0,
            'funcionarios_unicos': len(set([a['funcionario_id'] for a in alocacoes])) if alocacoes else 0
        }
        
        print(f"   ✅ Estatísticas calculadas:")
        print(f"      Total: {stats['total_alocacoes']}")
        print(f"      Ativas: {stats['alocacoes_ativas']}")
        print(f"      Funcionários únicos: {stats['funcionarios_unicos']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ ERRO: {e}")
        import traceback
        print(f"   📋 Detalhes: {traceback.format_exc()}")
        return False

def testar_template_context():
    """Testa a criação do contexto do template"""
    print("5️⃣ TESTANDO criação do contexto...")
    
    try:
        from app_empresa_principal import get_empresa_principal, get_todas_alocacoes, get_clientes_da_empresa_principal
        
        empresa_principal = get_empresa_principal()
        alocacoes = get_todas_alocacoes()
        clientes = get_clientes_da_empresa_principal()
        
        stats = {
            'total_alocacoes': len(alocacoes) if alocacoes else 0,
            'alocacoes_ativas': len([a for a in alocacoes if a['ativo']]) if alocacoes else 0,
            'funcionarios_unicos': len(set([a['funcionario_id'] for a in alocacoes])) if alocacoes else 0
        }
        
        context = {
            'titulo': 'Alocação de Funcionários',
            'empresa_principal': empresa_principal,
            'alocacoes': alocacoes,
            'clientes': clientes,
            'stats': stats
        }
        
        print(f"   ✅ Contexto criado com sucesso:")
        print(f"      Empresa principal: {'Sim' if empresa_principal else 'Não'}")
        print(f"      Alocações: {len(alocacoes) if alocacoes else 0}")
        print(f"      Clientes: {len(clientes) if clientes else 0}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ ERRO: {e}")
        import traceback
        print(f"   📋 Detalhes: {traceback.format_exc()}")
        return False

def main():
    """Função principal de teste"""
    print("🧪 TESTE DAS FUNÇÕES DE ALOCAÇÕES")
    print("=" * 60)
    
    testes = [
        testar_get_empresa_principal,
        testar_get_todas_alocacoes,
        testar_get_clientes_da_empresa_principal,
        testar_calculo_estatisticas,
        testar_template_context
    ]
    
    resultados = []
    
    for teste in testes:
        resultado = teste()
        resultados.append(resultado)
        print()
    
    print("📊 RESUMO DOS TESTES:")
    print("-" * 30)
    
    sucessos = sum(resultados)
    total = len(resultados)
    
    print(f"   Sucessos: {sucessos}/{total}")
    
    if sucessos == total:
        print(f"\n🎉 TODOS OS TESTES PASSARAM!")
        print("✅ O problema pode estar na importação ou configuração do Flask")
    else:
        print(f"\n❌ ALGUNS TESTES FALHARAM!")
        print("🔧 Corrigir os erros identificados acima")

if __name__ == "__main__":
    main()
