# Script de Deploy - Correção JavaScript Empresas
# Data: 03/07/2025
# Sistema: RLPONTO-WEB

param(
    [string]$ServerIP = "************",
    [string]$Username = "root",
    [string]$RemotePath = "/var/www/controle-ponto/templates/configuracoes/"
)

Write-Host "=== DEPLOY CORREÇÃO JAVASCRIPT EMPRESAS ===" -ForegroundColor Green
Write-Host "Servidor: $ServerIP" -ForegroundColor Cyan
Write-Host "Usuário: $Username" -ForegroundColor Cyan
Write-Host "Arquivo: empresa_form.html" -ForegroundColor Cyan
Write-Host ""

# Verificar se o arquivo local existe
$localFile = "var\www\controle-ponto\templates\configuracoes\empresa_form.html"
if (-not (Test-Path $localFile)) {
    Write-Host "❌ Arquivo local não encontrado: $localFile" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Arquivo local encontrado: $localFile" -ForegroundColor Green

# Criar backup no servidor antes do deploy
Write-Host ""
Write-Host "📋 INSTRUÇÕES PARA DEPLOY MANUAL:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Conecte-se ao servidor:" -ForegroundColor White
Write-Host "   ssh root@$ServerIP" -ForegroundColor Cyan
Write-Host "   Senha: @Ric6109" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Criar backup do arquivo atual:" -ForegroundColor White
Write-Host "   cd /var/www/controle-ponto/templates/configuracoes/" -ForegroundColor Cyan
Write-Host "   cp empresa_form.html empresa_form_backup_$(date +%Y%m%d_%H%M%S).html" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. Verificar se o serviço está rodando:" -ForegroundColor White
Write-Host "   systemctl status rlponto-web" -ForegroundColor Cyan
Write-Host "   # ou" -ForegroundColor Gray
Write-Host "   ps aux | grep python | grep app.py" -ForegroundColor Cyan
Write-Host ""
Write-Host "4. Copiar arquivo corrigido (do Windows para Linux):" -ForegroundColor White
Write-Host "   # Use WinSCP, FileZilla ou scp para copiar:" -ForegroundColor Gray
Write-Host "   # $localFile" -ForegroundColor Gray
Write-Host "   # para: $RemotePath" -ForegroundColor Gray
Write-Host ""
Write-Host "5. Verificar permissões:" -ForegroundColor White
Write-Host "   chown www-data:www-data empresa_form.html" -ForegroundColor Cyan
Write-Host "   chmod 644 empresa_form.html" -ForegroundColor Cyan
Write-Host ""
Write-Host "6. Reiniciar serviço (se necessário):" -ForegroundColor White
Write-Host "   systemctl restart rlponto-web" -ForegroundColor Cyan
Write-Host "   # ou" -ForegroundColor Gray
Write-Host "   pkill -f 'python.*app.py' && nohup python3 app.py &" -ForegroundColor Cyan
Write-Host ""

# Mostrar conteúdo das principais correções
Write-Host "📝 PRINCIPAIS CORREÇÕES APLICADAS:" -ForegroundColor Magenta
Write-Host ""
Write-Host "1. Proteção contra elementos nulos:" -ForegroundColor White
Write-Host "   if (!feedback) { console.error(...); return; }" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Correção de inconsistência de IDs:" -ForegroundColor White
Write-Host "   mostrarFeedback('razao_social', ...) // era 'razao-social'" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. ID do feedback corrigido:" -ForegroundColor White
Write-Host "   id='razao_social-feedback' // era 'razao-social-feedback'" -ForegroundColor Cyan
Write-Host ""

# Comandos de teste
Write-Host "🧪 COMANDOS DE TESTE APÓS DEPLOY:" -ForegroundColor Blue
Write-Host ""
Write-Host "1. Testar aplicação web:" -ForegroundColor White
Write-Host "   curl -I http://************:5000" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Verificar logs de erro:" -ForegroundColor White
Write-Host "   tail -f /var/log/rlponto-web/app.log" -ForegroundColor Cyan
Write-Host "   # ou" -ForegroundColor Gray
Write-Host "   journalctl -u rlponto-web -f" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. Testar página de empresas:" -ForegroundColor White
Write-Host "   curl http://************:5000/configuracoes/empresas" -ForegroundColor Cyan
Write-Host ""

Write-Host "📊 INFORMAÇÕES DO SISTEMA:" -ForegroundColor Green
Write-Host "   Servidor: $ServerIP" -ForegroundColor White
Write-Host "   Aplicação: Flask RLPONTO-WEB" -ForegroundColor White
Write-Host "   Arquivo: empresa_form.html" -ForegroundColor White
Write-Host "   Correção: Erro JavaScript classList" -ForegroundColor White
Write-Host ""
Write-Host "🎯 Após o deploy, teste editando uma empresa e salvando!" -ForegroundColor Green
