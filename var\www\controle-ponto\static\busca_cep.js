async function buscarCep(input) {
    const cep = input.value.replace(/\D/g, '');
    
    if (cep.length !== 8) {
        return;
    }
    
    try {
        const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
        const data = await response.json();
        
        if (data.erro) {
            throw new Error('CEP não encontrado');
        }
        
        document.getElementById('rua').value = data.logradouro;
        document.getElementById('bairro').value = data.bairro;
        document.getElementById('cidade').value = data.localidade;
        
        const estadoSelect = document.querySelector('select[name="endereco_estado"]');
        if (estadoSelect) {
            estadoSelect.value = data.uf;
        }
    } catch (error) {
        console.error('Erro ao buscar CEP:', error);
    }
}

function applyCepMask(input) {
    let value = input.value.replace(/\D/g, '');
    if (value.length > 8) {
        value = value.slice(0, 8);
    }
    if (value.length > 5) {
        value = value.slice(0, 5) + '-' + value.slice(5);
    }
    input.value = value;
} 