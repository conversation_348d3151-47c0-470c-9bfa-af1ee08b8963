#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧮 TESTE DIRETO DOS CÁLCULOS DE PONTO MANUAL
===========================================

Sistema: RLPONTO-WEB v1.0
Data: 18/07/2025
Objetivo: Testar diretamente as funções de cálculo sem depender da API

TESTES INCLUÍDOS:
1. Importação dos módulos de cálculo
2. Testes de cálculos básicos
3. Validação de cenários complexos
4. Verificação de edge cases
5. Performance dos cálculos
"""

import sys
import os
from datetime import datetime, time, date, timedelta
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('teste_calculos_direto')

# Adicionar o diretório do projeto ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'var', 'www', 'controle-ponto'))

def testar_importacoes():
    """
    Testa se conseguimos importar os módulos de cálculo.
    """
    logger.info("🔍 TESTE: Importação dos módulos")
    
    try:
        # Tentar importar o módulo de cálculos
        from calculos_ponto_corrigido import (
            calcular_horas_trabalhadas,
            validar_horarios_jornada,
            calcular_banco_horas,
            calcular_horas_extras_b5_b6
        )
        
        logger.info("✅ Módulos importados com sucesso")
        return True, {
            'calcular_horas_trabalhadas': calcular_horas_trabalhadas,
            'validar_horarios_jornada': validar_horarios_jornada,
            'calcular_banco_horas': calcular_banco_horas,
            'calcular_horas_extras_b5_b6': calcular_horas_extras_b5_b6
        }
        
    except ImportError as e:
        logger.error(f"❌ Erro na importação: {e}")
        return False, None

def testar_calculo_jornada_normal(funcoes):
    """
    Testa cálculo de jornada normal (8h).
    """
    logger.info("🔍 TESTE: Jornada normal 8h")
    
    try:
        calcular_horas = funcoes['calcular_horas_trabalhadas']
        
        # Jornada normal: 08:00-12:00 + 13:00-17:00 = 8h
        entrada = time(8, 0)
        saida_almoco = time(12, 0)
        entrada_tarde = time(13, 0)
        saida = time(17, 0)
        
        horas = calcular_horas(entrada, saida_almoco, entrada_tarde, saida)
        
        if horas == 8.0:
            logger.info(f"✅ Jornada normal: {horas}h (correto)")
            return True
        else:
            logger.error(f"❌ Jornada normal: {horas}h (esperado: 8.0h)")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False

def testar_calculo_com_atraso(funcoes):
    """
    Testa cálculo com atraso.
    """
    logger.info("🔍 TESTE: Jornada com atraso")
    
    try:
        calcular_horas = funcoes['calcular_horas_trabalhadas']
        
        # Atraso de 30min: 08:30-12:00 + 13:00-17:00 = 7.5h
        entrada = time(8, 30)
        saida_almoco = time(12, 0)
        entrada_tarde = time(13, 0)
        saida = time(17, 0)
        
        horas = calcular_horas(entrada, saida_almoco, entrada_tarde, saida)
        
        if horas == 7.5:
            logger.info(f"✅ Jornada com atraso: {horas}h (correto)")
            return True
        else:
            logger.error(f"❌ Jornada com atraso: {horas}h (esperado: 7.5h)")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False

def testar_jornada_sem_intervalo(funcoes):
    """
    Testa jornada sem intervalo (6h corridas).
    """
    logger.info("🔍 TESTE: Jornada sem intervalo")
    
    try:
        calcular_horas = funcoes['calcular_horas_trabalhadas']
        
        # Jornada corrida: 08:00-14:00 = 6h
        entrada = time(8, 0)
        saida = time(14, 0)
        
        horas = calcular_horas(entrada, None, None, saida)
        
        if horas == 6.0:
            logger.info(f"✅ Jornada sem intervalo: {horas}h (correto)")
            return True
        else:
            logger.error(f"❌ Jornada sem intervalo: {horas}h (esperado: 6.0h)")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False

def testar_validacao_horarios(funcoes):
    """
    Testa validação de sequência de horários.
    """
    logger.info("🔍 TESTE: Validação de horários")
    
    try:
        validar_horarios = funcoes['validar_horarios_jornada']
        
        # Teste 1: Sequência válida
        entrada = time(8, 0)
        saida_almoco = time(12, 0)
        entrada_tarde = time(13, 0)
        saida = time(17, 0)
        
        valido, erros = validar_horarios(entrada, saida_almoco, entrada_tarde, saida)
        
        if valido and len(erros) == 0:
            logger.info("✅ Validação sequência válida: OK")
        else:
            logger.error(f"❌ Validação sequência válida falhou: {erros}")
            return False
        
        # Teste 2: Sequência inválida (saída antes da entrada)
        entrada = time(8, 0)
        saida_almoco = time(12, 0)
        entrada_tarde = time(13, 0)
        saida = time(7, 0)  # ERRO: saída antes da entrada
        
        valido, erros = validar_horarios(entrada, saida_almoco, entrada_tarde, saida)
        
        if not valido and len(erros) > 0:
            logger.info(f"✅ Validação sequência inválida: {len(erros)} erros detectados")
            return True
        else:
            logger.error("❌ Validação não detectou sequência inválida")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False

def testar_banco_horas(funcoes):
    """
    Testa cálculo de banco de horas.
    """
    logger.info("🔍 TESTE: Banco de horas")
    
    try:
        calcular_banco = funcoes['calcular_banco_horas']
        
        # Teste 1: Crédito (trabalhou mais)
        resultado = calcular_banco(8.5, 8.0, 2.0)
        
        if (resultado['diferenca'] == 0.5 and 
            resultado['novo_saldo'] == 2.5 and 
            resultado['tipo'] == 'credito'):
            logger.info("✅ Banco de horas - Crédito: OK")
        else:
            logger.error(f"❌ Banco de horas - Crédito falhou: {resultado}")
            return False
        
        # Teste 2: Débito (trabalhou menos)
        resultado = calcular_banco(7.5, 8.0, 1.0)
        
        if (resultado['diferenca'] == -0.5 and 
            resultado['novo_saldo'] == 0.5 and 
            resultado['tipo'] == 'debito'):
            logger.info("✅ Banco de horas - Débito: OK")
            return True
        else:
            logger.error(f"❌ Banco de horas - Débito falhou: {resultado}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False

def testar_horas_extras_b5_b6(funcoes):
    """
    Testa cálculo de horas extras B5/B6.
    """
    logger.info("🔍 TESTE: Horas extras B5/B6")
    
    try:
        calcular_extras = funcoes['calcular_horas_extras_b5_b6']
        
        # Horas extras: 17:30-19:00 = 1.5h
        inicio = time(17, 30)
        fim = time(19, 0)
        
        horas_extras = calcular_extras(inicio, fim)
        
        if horas_extras == 1.5:
            logger.info(f"✅ Horas extras B5/B6: {horas_extras}h (correto)")
            return True
        else:
            logger.error(f"❌ Horas extras B5/B6: {horas_extras}h (esperado: 1.5h)")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False

def testar_casos_extremos(funcoes):
    """
    Testa casos extremos e edge cases.
    """
    logger.info("🔍 TESTE: Casos extremos")
    
    try:
        calcular_horas = funcoes['calcular_horas_trabalhadas']
        
        # Caso 1: Horários iguais (0h)
        entrada = time(8, 0)
        saida = time(8, 0)
        horas = calcular_horas(entrada, None, None, saida)
        
        if horas == 0.0:
            logger.info("✅ Caso extremo - Horários iguais: OK")
        else:
            logger.error(f"❌ Caso extremo - Horários iguais: {horas}h (esperado: 0.0h)")
            return False
        
        # Caso 2: Jornada muito longa (12h)
        entrada = time(6, 0)
        saida = time(18, 0)
        horas = calcular_horas(entrada, None, None, saida)
        
        if horas == 12.0:
            logger.info("✅ Caso extremo - Jornada longa: OK")
            return True
        else:
            logger.error(f"❌ Caso extremo - Jornada longa: {horas}h (esperado: 12.0h)")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False

def testar_performance(funcoes):
    """
    Testa performance dos cálculos.
    """
    logger.info("🔍 TESTE: Performance")
    
    try:
        import time as time_module
        calcular_horas = funcoes['calcular_horas_trabalhadas']
        
        # Executar 1000 cálculos
        inicio = time_module.time()
        
        for i in range(1000):
            entrada = time(8, 0)
            saida_almoco = time(12, 0)
            entrada_tarde = time(13, 0)
            saida = time(17, 0)
            calcular_horas(entrada, saida_almoco, entrada_tarde, saida)
        
        fim = time_module.time()
        tempo_total = fim - inicio
        
        if tempo_total < 1.0:
            logger.info(f"✅ Performance: 1000 cálculos em {tempo_total:.3f}s (OK)")
            return True
        else:
            logger.warning(f"⚠️ Performance: 1000 cálculos em {tempo_total:.3f}s (lento)")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False

def executar_todos_testes():
    """
    Executa todos os testes de cálculo direto.
    """
    print("🧮 INICIANDO TESTES DIRETOS DOS CÁLCULOS")
    print("=" * 50)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"Sistema: RLPONTO-WEB v1.0")
    print("=" * 50)
    
    # Teste de importação
    sucesso_import, funcoes = testar_importacoes()
    if not sucesso_import:
        print("\n❌ FALHA CRÍTICA: Não foi possível importar os módulos")
        return False
    
    # Lista de testes
    testes = [
        ("Jornada Normal", testar_calculo_jornada_normal),
        ("Jornada com Atraso", testar_calculo_com_atraso),
        ("Jornada sem Intervalo", testar_jornada_sem_intervalo),
        ("Validação de Horários", testar_validacao_horarios),
        ("Banco de Horas", testar_banco_horas),
        ("Horas Extras B5/B6", testar_horas_extras_b5_b6),
        ("Casos Extremos", testar_casos_extremos),
        ("Performance", testar_performance)
    ]
    
    # Executar testes
    resultados = []
    for nome, teste_func in testes:
        try:
            resultado = teste_func(funcoes)
            resultados.append(resultado)
            status = "✅ PASSOU" if resultado else "❌ FALHOU"
            logger.info(f"{nome}: {status}")
        except Exception as e:
            logger.error(f"{nome}: ❌ ERRO - {e}")
            resultados.append(False)
    
    # Relatório final
    print("\n" + "=" * 50)
    print("📊 RELATÓRIO FINAL DOS TESTES DIRETOS")
    print("=" * 50)
    
    total_testes = len(resultados)
    testes_passou = sum(resultados)
    testes_falhou = total_testes - testes_passou
    
    print(f"✅ Testes que passaram: {testes_passou}/{total_testes}")
    print(f"❌ Testes que falharam: {testes_falhou}/{total_testes}")
    print(f"📈 Taxa de sucesso: {(testes_passou/total_testes)*100:.1f}%")
    
    if testes_passou == total_testes:
        print("\n🎉 TODOS OS TESTES DIRETOS PASSARAM!")
        print("✅ Sistema de cálculos funcionando perfeitamente!")
        return True
    else:
        print(f"\n⚠️ {testes_falhou} TESTE(S) FALHARAM!")
        print("🔧 Verificar implementação dos cálculos.")
        return False

if __name__ == "__main__":
    sucesso = executar_todos_testes()
    sys.exit(0 if sucesso else 1)
