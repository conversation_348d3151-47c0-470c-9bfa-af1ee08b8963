#!/bin/bash

echo "🔄 Reiniciando servidor Flask..."

# Parar processos Flask existentes
echo "⏹️ Parando processos Flask..."
pkill -f "python.*app.py" || true
pkill -f "flask" || true

# Aguardar um momento
sleep 2

# Verificar se ainda há processos rodando
if pgrep -f "python.*app.py" > /dev/null; then
    echo "⚠️ Forçando parada de processos Flask..."
    pkill -9 -f "python.*app.py" || true
fi

# Aguardar mais um momento
sleep 2

# Iniciar o servidor Flask
echo "🚀 Iniciando servidor Flask..."
cd /var/www/controle-ponto

# Verificar se o arquivo app.py existe
if [ ! -f "app.py" ]; then
    echo "❌ Arquivo app.py não encontrado!"
    exit 1
fi

# Iniciar o servidor em background
nohup python3 app.py > flask.log 2>&1 &

# Aguardar o servidor iniciar
sleep 5

# Verificar se o servidor está rodando
if pgrep -f "python.*app.py" > /dev/null; then
    echo "✅ Servidor Flask iniciado com sucesso!"
    echo "📋 PID do processo: $(pgrep -f 'python.*app.py')"
    echo "📄 Logs em: /var/www/controle-ponto/flask.log"
    echo "🌐 Acesse: http://10.19.208.31/"
else
    echo "❌ Falha ao iniciar o servidor Flask!"
    echo "📄 Verificar logs em: /var/www/controle-ponto/flask.log"
    exit 1
fi

echo "🎉 Reinicialização concluída!"
