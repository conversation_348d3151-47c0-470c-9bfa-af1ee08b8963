#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug Específico do Erro de Alocações
=====================================

Script para identificar exatamente qual consulta SQL está causando
o erro "erro ao carregar alocações".

Data: 07/07/2025
"""

import pymysql
from datetime import datetime

# Configurações do banco
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def testar_consulta_principal():
    """Testa a consulta principal de alocações"""
    print("🔍 TESTANDO CONSULTA PRINCIPAL DE ALOCAÇÕES")
    print("=" * 60)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        with conn.cursor() as cursor:
            
            # Consulta principal corrigida
            sql_principal = """
            SELECT fa.*, 
                   f.nome_completo as nome, f.cargo, f.cpf, f.telefone1 as telefone,
                   e.razao_social, e.nome_fantasia, e.cnpj,
                   jt.nome_jornada as jornada_nome, NULL as carga_horaria,
                   ec.nome_contrato, ec.codigo_contrato
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            JOIN empresas e ON fa.empresa_cliente_id = e.id
            JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
            LEFT JOIN empresa_clientes ec ON fa.contrato_id = ec.id
            ORDER BY fa.created_at DESC
            """
            
            print("1️⃣ Testando consulta principal...")
            cursor.execute(sql_principal)
            resultados = cursor.fetchall()
            
            print(f"   ✅ Consulta executada com sucesso!")
            print(f"   📊 Resultados: {len(resultados)} alocações")
            
            if resultados:
                print(f"   📋 Primeira alocação:")
                primeiro = resultados[0]
                print(f"      ID: {primeiro.get('id', 'N/A')}")
                print(f"      Funcionário: {primeiro.get('nome', 'N/A')}")
                print(f"      Cliente: {primeiro.get('razao_social', 'N/A')}")
                print(f"      Jornada: {primeiro.get('jornada_nome', 'N/A')}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ ERRO na consulta principal: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def testar_consultas_estatisticas():
    """Testa as consultas de estatísticas"""
    print(f"\n🔍 TESTANDO CONSULTAS DE ESTATÍSTICAS")
    print("-" * 40)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        with conn.cursor() as cursor:
            
            # Teste 1: Total de alocações
            print("2️⃣ Testando total de alocações...")
            cursor.execute("SELECT COUNT(*) as total FROM funcionario_alocacoes")
            total = cursor.fetchone()
            print(f"   ✅ Total de alocações: {total['total']}")
            
            # Teste 2: Alocações ativas
            print("3️⃣ Testando alocações ativas...")
            cursor.execute("SELECT COUNT(*) as ativas FROM funcionario_alocacoes WHERE ativo = TRUE")
            ativas = cursor.fetchone()
            print(f"   ✅ Alocações ativas: {ativas['ativas']}")
            
            # Teste 3: Funcionários únicos
            print("4️⃣ Testando funcionários únicos...")
            cursor.execute("SELECT COUNT(DISTINCT funcionario_id) as unicos FROM funcionario_alocacoes")
            unicos = cursor.fetchone()
            print(f"   ✅ Funcionários únicos: {unicos['unicos']}")
            
            # Teste 4: Consulta de funcionários alocados
            print("5️⃣ Testando consulta de funcionários alocados...")
            sql_funcionarios = """
            SELECT f.nome_completo as nome, fa.jornada_trabalho_id, j.nome_jornada,
                   CASE WHEN fa.jornada_trabalho_id IS NOT NULL THEN 1 ELSE 0 END as jornada_aplicada
            FROM funcionario_alocacoes fa
            JOIN funcionarios f ON fa.funcionario_id = f.id
            LEFT JOIN jornadas_trabalho j ON fa.jornada_trabalho_id = j.id
            WHERE fa.ativo = TRUE
            """
            
            cursor.execute(sql_funcionarios)
            funcionarios = cursor.fetchall()
            print(f"   ✅ Funcionários alocados: {len(funcionarios)}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ ERRO nas consultas de estatísticas: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def testar_consultas_dropdowns():
    """Testa as consultas para popular dropdowns"""
    print(f"\n🔍 TESTANDO CONSULTAS DOS DROPDOWNS")
    print("-" * 40)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        with conn.cursor() as cursor:
            
            # Teste 1: Funcionários para dropdown
            print("6️⃣ Testando funcionários para dropdown...")
            cursor.execute("SELECT id, nome_completo FROM funcionarios WHERE ativo = TRUE ORDER BY nome_completo")
            funcionarios = cursor.fetchall()
            print(f"   ✅ Funcionários disponíveis: {len(funcionarios)}")
            
            # Teste 2: Clientes para dropdown
            print("7️⃣ Testando clientes para dropdown...")
            cursor.execute("SELECT id, razao_social, nome_fantasia FROM empresas WHERE ativa = TRUE ORDER BY razao_social")
            clientes = cursor.fetchall()
            print(f"   ✅ Clientes disponíveis: {len(clientes)}")
            
            # Teste 3: Jornadas para dropdown
            print("8️⃣ Testando jornadas para dropdown...")
            cursor.execute("SELECT id, nome_jornada FROM jornadas_trabalho WHERE ativa = TRUE ORDER BY nome_jornada")
            jornadas = cursor.fetchall()
            print(f"   ✅ Jornadas disponíveis: {len(jornadas)}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ ERRO nas consultas dos dropdowns: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def verificar_estrutura_tabelas():
    """Verifica se todas as tabelas e campos existem"""
    print(f"\n🔍 VERIFICANDO ESTRUTURA DAS TABELAS")
    print("-" * 40)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        with conn.cursor() as cursor:
            
            tabelas_necessarias = [
                'funcionario_alocacoes',
                'funcionarios', 
                'empresas',
                'jornadas_trabalho',
                'empresa_clientes'
            ]
            
            for tabela in tabelas_necessarias:
                cursor.execute(f"SELECT COUNT(*) as total FROM {tabela}")
                result = cursor.fetchone()
                print(f"   ✅ {tabela}: {result['total']} registros")
            
            return True
            
    except Exception as e:
        print(f"   ❌ ERRO na verificação de estrutura: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Função principal de debug"""
    print("🚀 DEBUG ESPECÍFICO DO ERRO DE ALOCAÇÕES")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    # Verificar estrutura
    if not verificar_estrutura_tabelas():
        print("\n❌ PROBLEMA NA ESTRUTURA DAS TABELAS")
        return
    
    # Testar consulta principal
    if not testar_consulta_principal():
        print("\n❌ PROBLEMA NA CONSULTA PRINCIPAL")
        return
    
    # Testar estatísticas
    if not testar_consultas_estatisticas():
        print("\n❌ PROBLEMA NAS CONSULTAS DE ESTATÍSTICAS")
        return
    
    # Testar dropdowns
    if not testar_consultas_dropdowns():
        print("\n❌ PROBLEMA NAS CONSULTAS DOS DROPDOWNS")
        return
    
    print(f"\n🎉 TODAS AS CONSULTAS FUNCIONANDO!")
    print("✅ O problema pode estar em outro lugar")
    print("💡 Verificar logs do servidor ou JavaScript da página")

if __name__ == "__main__":
    main()
