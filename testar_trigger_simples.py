#!/usr/bin/env python3
"""
Teste simples: Por que o trigger não está funcionando?
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def testar_trigger_simples():
    """Teste simples do trigger"""
    
    print("🔍 TESTE: POR QUE O TRIGGER NÃO FUNCIONA?")
    print("=" * 50)
    
    db = DatabaseManager()
    
    # 1. Buscar empresa AiNexus diretamente
    print("\n📋 PASSO 1: Buscando empresa AiNexus...")
    
    empresa = db.execute_query("""
        SELECT id, nome_fantasia FROM empresas 
        WHERE id = 11
    """, fetch_one=True)
    
    if empresa:
        print(f"✅ Empresa: {empresa['nome_fantasia']} (ID: {empresa['id']})")
    else:
        print("❌ Empresa não encontrada")
        return
    
    # 2. Verificar jornada padrão
    print(f"\n📋 PASSO 2: Verificando jornada padrão...")
    
    jornada = db.execute_query("""
        SELECT id, nome_jornada, seg_qui_entrada, seg_qui_saida, 
               tolerancia_entrada_minutos, padrao, ativa
        FROM jornadas_trabalho 
        WHERE empresa_id = 11
        ORDER BY padrao DESC, ativa DESC
    """)
    
    if jornada:
        for j in jornada:
            status = "PADRÃO" if j['padrao'] else "NORMAL"
            ativa = "ATIVA" if j['ativa'] else "INATIVA"
            print(f"   📋 {j['nome_jornada']} (ID: {j['id']}) - {status}, {ativa}")
            print(f"      • Entrada: {j['seg_qui_entrada']}")
            print(f"      • Tolerância: {j['tolerancia_entrada_minutos']} min")
    
    # 3. Verificar funcionários
    print(f"\n📋 PASSO 3: Verificando funcionários...")
    
    funcionarios = db.execute_query("""
        SELECT id, nome_completo, jornada_trabalho_id, usa_horario_empresa
        FROM funcionarios 
        WHERE empresa_id = 11 AND ativo = 1
    """)
    
    if funcionarios:
        for func in funcionarios:
            print(f"   👤 {func['nome_completo']} (ID: {func['id']})")
            print(f"      • Jornada ID: {func['jornada_trabalho_id']}")
            print(f"      • Usa herança: {func['usa_horario_empresa']}")
            
            # PROBLEMA POTENCIAL: usa_horario_empresa pode estar FALSE
            if not func['usa_horario_empresa']:
                print(f"      ❌ PROBLEMA: usa_horario_empresa = FALSE!")
                print(f"      🔧 Corrigindo...")
                
                db.execute_query("""
                    UPDATE funcionarios 
                    SET usa_horario_empresa = TRUE
                    WHERE id = %s
                """, (func['id'],), fetch_all=False)
                
                print(f"      ✅ Corrigido!")
    
    # 4. Simular mudança na jornada para testar trigger
    print(f"\n📋 PASSO 4: Testando trigger com mudança simulada...")
    
    jornada_padrao = db.execute_query("""
        SELECT id FROM jornadas_trabalho 
        WHERE empresa_id = 11 AND padrao = 1 AND ativa = 1
    """, fetch_one=True)
    
    if jornada_padrao:
        print(f"📋 Jornada padrão ID: {jornada_padrao['id']}")
        
        # Fazer uma mudança pequena para disparar o trigger
        print(f"🧪 Fazendo mudança na tolerância para disparar trigger...")
        
        try:
            # Alterar tolerância (vai disparar o trigger)
            db.execute_query("""
                UPDATE jornadas_trabalho 
                SET tolerancia_entrada_minutos = tolerancia_entrada_minutos + 1
                WHERE id = %s
            """, (jornada_padrao['id'],), fetch_all=False)
            
            print(f"✅ Mudança aplicada - trigger deveria ter disparado!")
            
            # Verificar se funcionários foram atualizados
            funcionarios_apos = db.execute_query("""
                SELECT id, nome_completo, jornada_trabalho_id
                FROM funcionarios 
                WHERE empresa_id = 11 AND ativo = 1
            """)
            
            print(f"📊 Verificando se funcionários foram atualizados:")
            for func in funcionarios_apos:
                print(f"   👤 {func['nome_completo']}: Jornada ID {func['jornada_trabalho_id']}")
                
                if func['jornada_trabalho_id'] == jornada_padrao['id']:
                    print(f"      ✅ Funcionário está com jornada correta!")
                else:
                    print(f"      ❌ Funcionário NÃO foi atualizado pelo trigger!")
            
            # Reverter mudança
            db.execute_query("""
                UPDATE jornadas_trabalho 
                SET tolerancia_entrada_minutos = tolerancia_entrada_minutos - 1
                WHERE id = %s
            """, (jornada_padrao['id'],), fetch_all=False)
            
            print(f"🔄 Mudança revertida")
        
        except Exception as e:
            print(f"❌ Erro ao testar trigger: {e}")
    
    print(f"\n🎯 DIAGNÓSTICO:")
    print(f"Se os funcionários NÃO foram atualizados, o problema pode ser:")
    print(f"1. ❌ Trigger com erro nas tabelas de histórico")
    print(f"2. ❌ Condição usa_horario_empresa = FALSE")
    print(f"3. ❌ Trigger não está sendo executado")

if __name__ == "__main__":
    testar_trigger_simples()
