#!/bin/bash

echo "🔧 Iniciando a instalação completa do sistema de controle de ponto..."

# --- Configurações Iniciais e Dependências ---
echo "[INFO] Atualizando lista de pacotes e sistema..."
sudo apt update && sudo apt upgrade -y

echo "[INFO] Instalando dependências base: Nginx, Python, PIP, MySQL Server, Unzip, Curl..."
sudo apt install -y nginx python3 python3-pip mysql-server unzip curl

echo "[INFO] Instalando bibliotecas Python necessárias via PIP..."
sudo pip3 install flask pymysql cryptography

# --- Configuração do Fuso Horário e NTP ---
echo "[INFO] Configurando fuso horário para America/Manaus..."
sudo timedatectl set-timezone America/Manaus

echo "[INFO] Tentando ativar a sincronização de horário pela rede (NTP)..."
sudo timedatectl set-ntp true
echo "[NOTA] Em containers LXC, o serviço NTP (systemd-timesyncd) pode permanecer 'inactive'"
echo "       pois o host (Proxmox) geralmente gerencia a sincronização de tempo."
echo "       Verifique 'timedatectl status' para confirmar se a hora local e o fuso estão corretos."

# --- Estrutura de Pastas ---
BASE_DIR="/var/www/controle-ponto"
LOG_DIR="/var/log/controle-ponto"
UPDATE_DIR="/root/atualizacoes"

echo "[INFO] Criando estrutura de diretórios em $BASE_DIR, $LOG_DIR e $UPDATE_DIR..."
sudo mkdir -p "$BASE_DIR/static"
sudo mkdir -p "$BASE_DIR/templates"
sudo mkdir -p "$LOG_DIR"
sudo mkdir -p "$UPDATE_DIR"

sudo chown www-data:www-data "$LOG_DIR"
sudo chmod 755 "$LOG_DIR"

# --- Configuração do MySQL ---
echo "[INFO] Configurando o banco de dados MySQL 'controle_ponto'..."

# Comandos SQL extraídos diretamente do controle_ponto.sql fornecido
SQL_COMMANDS=""
SQL_COMMANDS+="DROP DATABASE IF EXISTS controle_ponto;"
SQL_COMMANDS+="CREATE DATABASE IF NOT EXISTS controle_ponto CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;"
SQL_COMMANDS+="USE controle_ponto;"
SQL_COMMANDS+="CREATE TABLE IF NOT EXISTS usuarios ( id int NOT NULL AUTO_INCREMENT, usuario varchar(50) NOT NULL, senha varchar(255) NOT NULL, PRIMARY KEY (id), UNIQUE INDEX usuario (usuario) ) ENGINE = INNODB AUTO_INCREMENT = 2 CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;"
SQL_COMMANDS+="CREATE TABLE IF NOT EXISTS permissoes ( usuario_id int NOT NULL, nivel_acesso enum ('admin', 'usuario') DEFAULT 'usuario', PRIMARY KEY (usuario_id), CONSTRAINT permissoes_ibfk_1 FOREIGN KEY (usuario_id) REFERENCES usuarios (id) ) ENGINE = INNODB CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;"
SQL_COMMANDS+="CREATE TABLE IF NOT EXISTS funcionarios ( id int NOT NULL AUTO_INCREMENT, nome_completo varchar(100) NOT NULL, cpf varchar(14) NOT NULL, rg varchar(20) NOT NULL, data_nascimento date NOT NULL, sexo enum ('M', 'F', 'Outro') NOT NULL, estado_civil enum ('Solteiro', 'Casado', 'Divorciado', 'Viuvo') NOT NULL, nacionalidade varchar(50) NOT NULL, ctps_numero varchar(20) NOT NULL, ctps_serie_uf varchar(20) NOT NULL, pis_pasep varchar(20) NOT NULL, endereco_rua varchar(100) DEFAULT NULL, endereco_bairro varchar(50) DEFAULT NULL, endereco_cidade varchar(50) DEFAULT NULL, endereco_cep varchar(10) NOT NULL, endereco_estado varchar(2) NOT NULL, telefone1 varchar(15) NOT NULL, telefone2 varchar(15) DEFAULT NULL, email varchar(100) DEFAULT NULL, cargo varchar(50) NOT NULL, setor_obra varchar(50) NOT NULL, matricula_empresa varchar(20) NOT NULL, data_admissao date NOT NULL, tipo_contrato enum ('CLT', 'PJ', 'Estagio', 'Temporario') NOT NULL, jornada_entrada time NOT NULL, jornada_saida time NOT NULL, digital_dedo1 blob DEFAULT NULL, digital_dedo2 blob DEFAULT NULL, foto_3x4 blob DEFAULT NULL, assinatura blob DEFAULT NULL, nivel_acesso enum ('Funcionario', 'Supervisao', 'Gerencia') NOT NULL, turno enum ('Diurno', 'Noturno', 'Misto') NOT NULL, tolerancia_ponto int NOT NULL DEFAULT 5, banco_horas tinyint DEFAULT 0, hora_extra tinyint DEFAULT 0, status_cadastro enum ('Ativo', 'Inativo') NOT NULL DEFAULT 'Ativo', data_cadastro datetime DEFAULT CURRENT_TIMESTAMP, PRIMARY KEY (id), UNIQUE INDEX cpf (cpf), UNIQUE INDEX matricula_empresa (matricula_empresa) ) ENGINE = INNODB CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;"
SQL_COMMANDS+="CREATE TABLE IF NOT EXISTS registros_ponto ( id int NOT NULL AUTO_INCREMENT, funcionario_id int DEFAULT NULL, data_hora datetime DEFAULT CURRENT_TIMESTAMP, sincronizado tinyint DEFAULT 0, PRIMARY KEY (id), CONSTRAINT registros_ponto_ibfk_1 FOREIGN KEY (funcionario_id) REFERENCES funcionarios (id) ) ENGINE = INNODB CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;"
SQL_COMMANDS+="CREATE TABLE IF NOT EXISTS epis ( id int NOT NULL AUTO_INCREMENT, funcionario_id int DEFAULT NULL, epi_nome varchar(255) DEFAULT NULL, epi_ca varchar(50) DEFAULT NULL, epi_data_entrega date DEFAULT NULL, epi_data_validade date DEFAULT NULL, epi_observacoes text DEFAULT NULL, PRIMARY KEY (id), CONSTRAINT epis_ibfk_1 FOREIGN KEY (funcionario_id) REFERENCES funcionarios (id) ) ENGINE = INNODB CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;"
SQL_COMMANDS+="CREATE USER IF NOT EXISTS 'controle_user'@'localhost' IDENTIFIED BY 'controle123';"
SQL_COMMANDS+="GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'localhost';"
SQL_COMMANDS+="FLUSH PRIVILEGES;"
SQL_COMMANDS+="INSERT INTO usuarios VALUES (1, 'admin', 'admin123');"
SQL_COMMANDS+="INSERT INTO permissoes VALUES (1, 'admin');"

sudo mysql -e "${SQL_COMMANDS}"

if [ $? -eq 0 ]; then
    echo "[SUCCESS] Banco de dados 'controle_ponto' e tabelas configurados com sucesso."
else
    echo "[ERROR] Falha ao configurar o banco de dados MySQL. Verifique os logs."
    exit 1
fi

# --- Arquivo Flask App (app.py) Placeholder ---
echo "[INFO] Criando um arquivo app.py placeholder em $BASE_DIR/app.py..."
echo "[NOTA] Este app.py é básico. Substitua pelo app.py final fornecido pela IA."

sudo tee "$BASE_DIR/app.py" > /dev/null <<EOF
from flask import Flask
app = Flask(__name__)

@app.route('/')
def hello_world():
    return 'Controle de Ponto - App Placeholder. Substitua este arquivo!'

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
EOF

sudo chown www-data:www-data "$BASE_DIR/app.py"
sudo chmod 644 "$BASE_DIR/app.py"

# --- Configuração do Serviço Systemd para Flask ---
echo "[INFO] Configurando o serviço systemd 'controle-ponto.service'..."
SERVICE_FILE="/etc/systemd/system/controle-ponto.service"

sudo tee "$SERVICE_FILE" > /dev/null <<EOF
[Unit]
Description=Serviço da Aplicação de Controle de Ponto Flask
After=network.target mysql.service

[Service]
User=www-data
Group=www-data
WorkingDirectory=$BASE_DIR
Environment="PATH=/usr/local/bin:/usr/bin:/bin:$BASE_DIR/venv/bin" # Ajuste se não usar venv globalmente
ExecStart=/usr/bin/python3 $BASE_DIR/app.py
Restart=always
RestartSec=5s
StandardOutput=append:$LOG_DIR/app_stdout.log
StandardError=append:$LOG_DIR/app_stderr.log

[Install]
WantedBy=multi-user.target
EOF

sudo chmod 644 "$SERVICE_FILE"
sudo systemctl daemon-reload
sudo systemctl enable controle-ponto.service

# --- Configuração do Nginx como Proxy Reverso ---
echo "[INFO] Configurando o Nginx como proxy reverso..."
NGINX_CONF="/etc/nginx/sites-available/controle-ponto"

sudo tee "$NGINX_CONF" > /dev/null <<EOF
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_read_timeout 600;
    }

    location /static {
        alias $BASE_DIR/static;
    }
}
EOF

if [ -L /etc/nginx/sites-enabled/default ]; then
    echo "[INFO] Removendo link simbólico default do Nginx..."
    sudo rm /etc/nginx/sites-enabled/default
fi

sudo ln -sf "$NGINX_CONF" /etc/nginx/sites-enabled/

echo "[INFO] Testando configuração do Nginx..."
sudo nginx -t
if [ $? -ne 0 ]; then
    echo "[ERROR] Configuração do Nginx falhou. Verifique os erros acima."
    exit 1
fi

# --- Reiniciando Serviços ---
echo "[INFO] Reiniciando Nginx e iniciando o serviço controle-ponto..."
sudo systemctl restart nginx
sudo systemctl start controle-ponto.service

echo "[INFO] Verificando status dos serviços (nginx e controle-ponto)..."
sleep 5 # Aguarda um pouco para os serviços iniciarem
sudo systemctl status nginx --no-pager
sudo systemctl status controle-ponto.service --no-pager

echo "[SUCCESS] Instalação e configuração básicas concluídas!"
echo "Lembre-se de substituir o $BASE_DIR/app.py pelo arquivo final fornecido."
echo "Acesse a aplicação pelo IP do servidor."

