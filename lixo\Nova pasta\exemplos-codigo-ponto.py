#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EXEMPLOS DE CÓDIGO - SISTEMA DE CONTROLE DE PONTO
Data: 12/07/2025
Objetivo: Exemplos práticos para consulta e debugging

Este arquivo contém exemplos de como usar as funções principais
do sistema de controle de ponto.
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from datetime import datetime, timedelta
from app_ponto_admin import get_registros_ponto_funcionario

# =============================================================================
# EXEMPLO 1: BUSCAR REGISTROS DE UM FUNCIONÁRIO
# =============================================================================

def exemplo_buscar_registros():
    """Exemplo de como buscar registros de um funcionário"""
    
    funcionario_id = 26  # João Silva Santos
    
    print("=" * 60)
    print("📋 EXEMPLO 1: BUSCAR REGISTROS")
    print("=" * 60)
    
    # Buscar todos os registros
    registros = get_registros_ponto_funcionario(funcionario_id)
    
    print(f"Funcionário ID: {funcionario_id}")
    print(f"Total de registros: {len(registros)}")
    
    # Mostrar cada registro
    for i, registro in enumerate(registros, 1):
        print(f"\n📅 Registro {i}:")
        print(f"   Data: {registro.get('data')}")
        print(f"   Entrada: {registro.get('entrada')}")
        print(f"   Saída Almoço: {registro.get('saida_almoco')}")
        print(f"   Retorno: {registro.get('retorno_almoco')}")
        print(f"   Saída: {registro.get('saida')}")
        print(f"   Horas Normais: {registro.get('horas_normais')}h")
        print(f"   Horas Extras: {registro.get('horas_extras')}h")
        print(f"   Horas Negativas: {registro.get('horas_negativas')}h")
        print(f"   Total: {registro.get('total_horas_dia')}h")
        
        # Mostrar alertas se houver
        alertas = registro.get('alertas_ajustes_jornada', [])
        if alertas:
            print(f"   🚨 Alertas:")
            for alerta in alertas:
                print(f"      {alerta}")

# =============================================================================
# EXEMPLO 2: ANALISAR CASO ESPECÍFICO
# =============================================================================

def exemplo_analisar_caso_especifico():
    """Exemplo de como analisar um caso específico"""
    
    funcionario_id = 26
    data_especifica = '2025-07-11'
    
    print("=" * 60)
    print("📊 EXEMPLO 2: ANALISAR CASO ESPECÍFICO")
    print("=" * 60)
    
    registros = get_registros_ponto_funcionario(funcionario_id)
    
    # Procurar data específica
    caso_encontrado = None
    for registro in registros:
        if data_especifica in str(registro.get('data', '')):
            caso_encontrado = registro
            break
    
    if caso_encontrado:
        print(f"📅 Caso encontrado: {data_especifica}")
        
        # Análise detalhada
        entrada = caso_encontrado.get('entrada')
        saida = caso_encontrado.get('saida')
        horas_normais = caso_encontrado.get('horas_normais', 0)
        horas_negativas = caso_encontrado.get('horas_negativas', 0)
        
        print(f"\n🔍 ANÁLISE:")
        print(f"   Horário registrado: {entrada} - {saida}")
        print(f"   Horas calculadas: {horas_normais}h")
        print(f"   Déficit: {horas_negativas}h")
        
        # Verificar se é caso de trabalho parcial
        if horas_normais > 0 and horas_negativas > 0:
            print(f"   ✅ TRABALHO PARCIAL RECONHECIDO")
            print(f"   ⚖️ Sistema foi justo: reconheceu {horas_normais}h trabalhadas")
        elif horas_normais == 8.0 and horas_negativas == 0:
            print(f"   ✅ JORNADA COMPLETA")
        else:
            print(f"   ⚠️ Caso especial para análise")
    else:
        print(f"❌ Caso não encontrado para data: {data_especifica}")

# =============================================================================
# EXEMPLO 3: DETECTAR MALANDRAGEMS
# =============================================================================

def exemplo_detectar_malandragems():
    """Exemplo de como detectar tentativas de malandragem"""
    
    funcionario_id = 26
    
    print("=" * 60)
    print("🕵️ EXEMPLO 3: DETECTAR MALANDRAGEMS")
    print("=" * 60)
    
    registros = get_registros_ponto_funcionario(funcionario_id)
    
    malandragems_detectadas = []
    
    for registro in registros:
        alertas = registro.get('alertas_ajustes_jornada', [])
        
        # Verificar se há alertas de limitação (indicam malandragem)
        tem_limitacao = any('limitada' in alerta for alerta in alertas)
        
        if tem_limitacao:
            malandragems_detectadas.append({
                'data': registro.get('data'),
                'alertas': alertas,
                'horas_calculadas': registro.get('horas_normais')
            })
    
    print(f"🚨 Malandragems detectadas: {len(malandragems_detectadas)}")
    
    for i, malandragem in enumerate(malandragems_detectadas, 1):
        print(f"\n📅 Malandragem {i}: {malandragem['data']}")
        print(f"   Horas calculadas: {malandragem['horas_calculadas']}h")
        print(f"   Ajustes aplicados:")
        for alerta in malandragem['alertas']:
            if 'limitada' in alerta:
                print(f"      🔒 {alerta}")

# =============================================================================
# EXEMPLO 4: CALCULAR ESTATÍSTICAS
# =============================================================================

def exemplo_calcular_estatisticas():
    """Exemplo de como calcular estatísticas do funcionário"""
    
    funcionario_id = 26
    
    print("=" * 60)
    print("📈 EXEMPLO 4: ESTATÍSTICAS DO FUNCIONÁRIO")
    print("=" * 60)
    
    registros = get_registros_ponto_funcionario(funcionario_id)
    
    # Calcular estatísticas
    total_dias = len(registros)
    total_horas_trabalhadas = sum(r.get('horas_normais', 0) for r in registros)
    total_horas_extras = sum(r.get('horas_extras', 0) for r in registros)
    total_deficit = sum(r.get('horas_negativas', 0) for r in registros)
    
    dias_completos = len([r for r in registros if r.get('horas_negativas', 0) == 0])
    dias_com_deficit = len([r for r in registros if r.get('horas_negativas', 0) > 0])
    
    # Detectar tentativas de malandragem
    dias_com_ajustes = 0
    for registro in registros:
        alertas = registro.get('alertas_ajustes_jornada', [])
        if any('limitada' in alerta for alerta in alertas):
            dias_com_ajustes += 1
    
    print(f"📊 ESTATÍSTICAS GERAIS:")
    print(f"   Total de dias: {total_dias}")
    print(f"   Horas trabalhadas: {total_horas_trabalhadas:.2f}h")
    print(f"   Horas extras: {total_horas_extras:.2f}h")
    print(f"   Déficit total: {total_deficit:.2f}h")
    
    print(f"\n📊 PERFORMANCE:")
    print(f"   Dias completos: {dias_completos}/{total_dias}")
    print(f"   Dias com déficit: {dias_com_deficit}/{total_dias}")
    print(f"   Taxa de pontualidade: {(dias_completos/total_dias)*100:.1f}%")
    
    print(f"\n🚨 COMPORTAMENTO:")
    print(f"   Tentativas de malandragem: {dias_com_ajustes}")
    if dias_com_ajustes > 0:
        print(f"   ⚠️ Funcionário tenta burlar sistema regularmente")
    else:
        print(f"   ✅ Funcionário pontual e honesto")

# =============================================================================
# EXEMPLO 5: TESTE DE VALIDAÇÃO
# =============================================================================

def exemplo_teste_validacao():
    """Exemplo de teste para validar se sistema está funcionando"""
    
    print("=" * 60)
    print("🧪 EXEMPLO 5: TESTE DE VALIDAÇÃO")
    print("=" * 60)
    
    funcionario_id = 26
    
    # Teste 1: Sistema retorna dados
    registros = get_registros_ponto_funcionario(funcionario_id)
    assert len(registros) > 0, "❌ Sistema não retorna dados"
    print("✅ Teste 1: Sistema retorna dados")
    
    # Teste 2: Lógica justa funcionando (caso 11/07)
    caso_11_07 = None
    for registro in registros:
        if '2025-07-11' in str(registro.get('data', '')):
            caso_11_07 = registro
            break
    
    if caso_11_07:
        horas_normais = caso_11_07.get('horas_normais', 0)
        assert horas_normais > 0, "❌ Lógica justa não funcionando"
        print("✅ Teste 2: Lógica justa funcionando")
    
    # Teste 3: Regra rigorosa funcionando (caso 10/07)
    caso_10_07 = None
    for registro in registros:
        if '2025-07-10' in str(registro.get('data', '')):
            caso_10_07 = registro
            break
    
    if caso_10_07:
        horas_normais = caso_10_07.get('horas_normais', 0)
        alertas = caso_10_07.get('alertas_ajustes_jornada', [])
        tem_limitacao = any('limitada' in alerta for alerta in alertas)
        
        assert horas_normais <= 8.0, "❌ Regra rigorosa não funcionando"
        print("✅ Teste 3: Regra rigorosa funcionando")
    
    print("\n🎉 TODOS OS TESTES PASSARAM!")
    print("Sistema funcionando corretamente!")

# =============================================================================
# FUNÇÃO PRINCIPAL
# =============================================================================

def main():
    """Função principal - executa todos os exemplos"""
    
    print("🎯 EXEMPLOS DE CÓDIGO - SISTEMA DE CONTROLE DE PONTO")
    print("Data: 12/07/2025")
    print("Versão: 2.0 - Anti-Malandragem + Lógica Justa")
    
    try:
        exemplo_buscar_registros()
        exemplo_analisar_caso_especifico()
        exemplo_detectar_malandragems()
        exemplo_calcular_estatisticas()
        exemplo_teste_validacao()
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        print("Verifique se o sistema está funcionando corretamente")

if __name__ == "__main__":
    main()
