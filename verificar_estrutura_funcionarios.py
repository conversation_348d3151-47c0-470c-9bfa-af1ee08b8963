#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar estrutura da tabela funcionários
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def verificar_estrutura_funcionarios():
    """Verificar estrutura da tabela funcionários"""
    print("🔍 VERIFICANDO ESTRUTURA DA TABELA FUNCIONÁRIOS")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar estrutura da tabela
        print("\n1. Verificando estrutura da tabela funcionários...")
        sql_estrutura = "DESCRIBE funcionarios"
        
        estrutura = db.execute_query(sql_estrutura)
        
        print(f"📊 Campos da tabela funcionários:")
        campos_relevantes = ['turno', 'tolerancia_ponto', 'jornada_trabalho_id', 'horario_trabalho_id']
        
        for campo in estrutura:
            nome_campo = campo['Field']
            if nome_campo in campos_relevantes:
                print(f"   ⭐ {campo['Field']}: {campo['Type']} (Null: {campo['Null']}, Default: {campo['Default']})")
            elif 'jornada' in nome_campo.lower() or 'turno' in nome_campo.lower() or 'tolerancia' in nome_campo.lower():
                print(f"   📋 {campo['Field']}: {campo['Type']} (Null: {campo['Null']}, Default: {campo['Default']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a verificação: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    verificar_estrutura_funcionarios()
