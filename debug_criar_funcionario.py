#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para debugar especificamente a função _criar_funcionario
"""

import sys
import os
import traceback
from datetime import datetime

# Adicionar o diretório do projeto ao path
sys.path.insert(0, '/var/www/controle-ponto')

# Importar apenas o necessário
try:
    from utils.database import DatabaseManager
    print("✅ DatabaseManager importado")
    
    # Importar função específica
    from app_funcionarios import _criar_funcionario
    print("✅ _criar_funcionario importado")
    
except ImportError as e:
    print(f"❌ Erro ao importar: {e}")
    print(f"   Traceback: {traceback.format_exc()}")
    sys.exit(1)

def criar_dados_teste():
    """Cria dados de teste válidos"""
    timestamp = datetime.now().strftime("%H%M%S")
    return {
        'nome_completo': 'JOÃO TESTE FUNÇÃO',
        'cpf': f'222.333.444-{timestamp[-2:]}',
        'rg': '12.345.678-9',
        'data_nascimento': '1990-01-01',
        'sexo': 'M',
        'estado_civil': 'Solteiro',
        'nacionalidade': 'Brasileira',
        'ctps_numero': '1234567',
        'ctps_serie_uf': '001/MG',
        'pis_pasep': '123.45678.90-1',
        'endereco_rua': 'RUA TESTE, 123',
        'endereco_bairro': 'CENTRO',
        'endereco_cidade': 'BELO HORIZONTE',
        'endereco_cep': '30000-000',
        'endereco_estado': 'MG',
        'telefone1': '(31) 99999-9999',
        'telefone2': '(31) 3333-4444',
        'email': '<EMAIL>',
        'cargo': 'ANALISTA DE TESTE',
        'setor_obra': 'DESENVOLVIMENTO',
        'matricula_empresa': f'FUNC{timestamp}',
        'data_admissao': '2025-01-01',
        'tipo_contrato': 'CLT',
        'nivel_acesso': 'Funcionario',
        'turno': 'Diurno',
        'tolerancia_ponto': 5,
        'banco_horas': True,
        'hora_extra': True,
        'status_cadastro': 'Ativo',
        'horas_semanais_obrigatorias': 44.0,
        'empresa_id': 11,
        'jornada_trabalho_id': 1,
        'digital_dedo1': None,
        'digital_dedo2': None,
        'foto_3x4': None,
        'epis': []  # Lista vazia para evitar processamento
    }

def testar_criar_funcionario():
    """Testa a função _criar_funcionario"""
    print("\n🔍 TESTE FUNÇÃO _criar_funcionario")
    print("-" * 50)
    
    try:
        dados = criar_dados_teste()
        print(f"📊 Dados criados: {len(dados)} campos")
        
        # Executar função
        funcionario_id = _criar_funcionario(dados)
        
        if funcionario_id:
            print(f"✅ Funcionário criado com ID: {funcionario_id}")
            
            # Limpar teste
            try:
                DatabaseManager.execute_query(
                    "DELETE FROM funcionarios WHERE id = %s", 
                    (funcionario_id,), 
                    fetch_all=False
                )
                print(f"🧹 Funcionário de teste removido (ID: {funcionario_id})")
            except Exception as cleanup_error:
                print(f"⚠️ Erro ao limpar teste: {cleanup_error}")
            
            return True
        else:
            print("❌ Função não retornou ID")
            return False
            
    except Exception as e:
        print(f"❌ ERRO na função: {e}")
        print(f"   Tipo: {type(e).__name__}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Função principal"""
    print("🔧 DEBUG FUNÇÃO _criar_funcionario")
    print("=" * 60)
    print(f"⏰ Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Teste de conexão
    try:
        result = DatabaseManager.execute_query("SELECT 1 as test", fetch_one=True)
        if result and result.get('test') == 1:
            print("✅ Conexão com banco funcionando")
        else:
            print("❌ Conexão com banco falhou")
            return
    except Exception as e:
        print(f"❌ ERRO de conexão: {e}")
        return
    
    # Teste da função
    if testar_criar_funcionario():
        print("\n🎉 FUNÇÃO _criar_funcionario PASSOU!")
        print("✅ A função está funcionando corretamente")
    else:
        print("\n❌ FUNÇÃO _criar_funcionario FALHOU!")
        print("❌ Há um problema na função específica")

if __name__ == "__main__":
    main()
