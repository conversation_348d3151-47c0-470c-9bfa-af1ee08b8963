async function buscarCep(input) {
    const cep = input.value.replace(/\D/g, ''); // Remove caracteres não numéricos
    const ruaInput = document.getElementById('rua');
    const bairroInput = document.getElementById('bairro');
    const cidadeInput = document.getElementById('cidade');
    const estadoInput = document.querySelector('select[name="endereco_estado"]');

    // Limpa os campos caso o CEP esteja vazio ou inválido
    if (cep.length !== 8) {
        ruaInput.value = '';
        bairroInput.value = '';
        cidadeInput.value = '';
        estadoInput.value = '';
        return;
    }

    try {
        const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
        if (!response.ok) throw new Error('Erro na requisição ao ViaCEP');
        const data = await response.json();

        if (data.erro) {
            alert('CEP não encontrado. Verifique o código digitado.');
            ruaInput.value = '';
            bairroInput.value = '';
            cidadeInput.value = '';
            estadoInput.value = '';
            return;
        }

        // Preenche os campos com os dados retornados
        if (!data.logradouro) {
            alert('Rua não encontrada para este CEP. Preencha manualmente.');
            ruaInput.value = '';
        } else {
            ruaInput.value = data.logradouro.toUpperCase();
        }

        bairroInput.value = data.bairro ? data.bairro.toUpperCase() : '';
        cidadeInput.value = data.localidade ? data.localidade.toUpperCase() : '';
        if (data.uf) estadoInput.value = data.uf; // Preenche o estado automaticamente

    } catch (error) {
        console.error('Erro ao buscar CEP:', error.message);
        alert('Erro ao consultar o CEP. Tente novamente ou verifique sua conexão.');
        ruaInput.value = '';
        bairroInput.value = '';
        cidadeInput.value = '';
        estadoInput.value = '';
    }
}
