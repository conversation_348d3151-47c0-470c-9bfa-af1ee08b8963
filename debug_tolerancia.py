#!/usr/bin/env python3
"""
Debug: Por que a tolerância não está sendo exibida?
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager, FuncionarioQueries

def debug_tolerancia():
    """Debug da tolerância"""
    
    print("🔍 DEBUG: TOLERÂNCIA DO FUNCIONÁRIO")
    print("=" * 50)
    
    db = DatabaseManager()
    
    # 1. Verificar tolerância na jornada
    print("\n📋 PASSO 1: Verificando tolerância na jornada...")
    
    jornada = db.execute_query("""
        SELECT id, nome_jornada, tolerancia_entrada_minutos
        FROM jornadas_trabalho 
        WHERE empresa_id = 11 AND padrao = 1 AND ativa = 1
    """, fetch_one=True)
    
    if jornada:
        print(f"   📋 Jornada: {jornada['nome_jornada']} (ID: {jornada['id']})")
        print(f"   🕐 Tolerância na jornada: {jornada['tolerancia_entrada_minutos']} min")
    
    # 2. Verificar funcionário Richardson
    print(f"\n📋 PASSO 2: Verificando funcionário Richardson...")
    
    funcionario_direto = db.execute_query("""
        SELECT f.id, f.nome_completo, f.jornada_trabalho_id,
               jt.tolerancia_entrada_minutos
        FROM funcionarios f
        INNER JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.id = 35
    """, fetch_one=True)
    
    if funcionario_direto:
        print(f"   👤 {funcionario_direto['nome_completo']}")
        print(f"   📋 Jornada ID: {funcionario_direto['jornada_trabalho_id']}")
        print(f"   🕐 Tolerância (query direta): {funcionario_direto['tolerancia_entrada_minutos']} min")
    
    # 3. Verificar get_with_epis
    print(f"\n📋 PASSO 3: Verificando get_with_epis...")
    
    funcionario_completo = FuncionarioQueries.get_with_epis(35)
    
    if funcionario_completo:
        print(f"   👤 Nome: {funcionario_completo.get('nome_completo')}")
        
        # Verificar todos os campos relacionados à tolerância
        campos_tolerancia = [
            'tolerancia_entrada_minutos',
            'jornada_tolerancia_entrada_minutos',
            'tolerancia',
            'jornada_tolerancia'
        ]
        
        print(f"   🔍 Campos de tolerância retornados:")
        for campo in campos_tolerancia:
            valor = funcionario_completo.get(campo)
            if valor is not None:
                print(f"      • {campo}: {valor}")
            else:
                print(f"      • {campo}: None")
        
        # Verificar TODOS os campos que contêm 'tolerancia'
        print(f"\n   🔍 TODOS os campos com 'tolerancia':")
        for key, value in funcionario_completo.items():
            if 'tolerancia' in key.lower():
                print(f"      • {key}: {value}")
    
    # 4. Verificar query da função get_with_epis
    print(f"\n📋 PASSO 4: Verificando query específica...")
    
    # Simular a query que get_with_epis faz
    funcionario_query = db.execute_query("""
        SELECT 
            f.id, f.nome_completo,
            COALESCE(jt_alocacao.tolerancia_entrada_minutos, jt.tolerancia_entrada_minutos) as jornada_tolerancia_entrada_minutos,
            jt.tolerancia_entrada_minutos as tolerancia_jornada_principal
        FROM funcionarios f
        LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        LEFT JOIN funcionario_alocacoes fa ON (
            f.id = fa.funcionario_id 
            AND fa.ativo = 1 
            AND fa.data_inicio <= CURDATE() 
            AND (fa.data_fim IS NULL OR fa.data_fim >= CURDATE())
        )
        LEFT JOIN jornadas_trabalho jt_alocacao ON fa.jornada_trabalho_id = jt_alocacao.id
        WHERE f.id = 35
    """, fetch_one=True)
    
    if funcionario_query:
        print(f"   👤 {funcionario_query['nome_completo']}")
        print(f"   🕐 jornada_tolerancia_entrada_minutos: {funcionario_query['jornada_tolerancia_entrada_minutos']}")
        print(f"   🕐 tolerancia_jornada_principal: {funcionario_query['tolerancia_jornada_principal']}")
    
    # 5. Verificar template
    print(f"\n📋 PASSO 5: O que o template está buscando...")
    print(f"   📺 Template busca: funcionario.tolerancia_entrada_minutos")
    
    if funcionario_completo:
        template_value = funcionario_completo.get('tolerancia_entrada_minutos')
        print(f"   📺 Valor encontrado: {template_value}")
        
        if template_value is None:
            print(f"   ❌ PROBLEMA: Campo não existe!")
            print(f"   💡 Template deveria buscar: jornada_tolerancia_entrada_minutos")
        else:
            print(f"   ✅ Campo existe com valor: {template_value}")
    
    print(f"\n🎯 DIAGNÓSTICO:")
    if funcionario_completo and funcionario_completo.get('tolerancia_entrada_minutos') is None:
        print("❌ PROBLEMA: Template busca campo que não existe")
        print("💡 SOLUÇÃO: Corrigir template ou função get_with_epis")
    else:
        print("✅ Campos estão corretos - verificar outro problema")

if __name__ == "__main__":
    debug_tolerancia()
