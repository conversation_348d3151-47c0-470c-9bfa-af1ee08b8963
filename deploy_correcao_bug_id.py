#!/usr/bin/env python3
"""
Deploy da Correção do Bug 'id' no Formulário de Funcionários
Sistema: RLPONTO-WEB v1.0
Data: 04/07/2025
Autor: <PERSON> - Full Stack Developer
"""

import paramiko
import os
import sys
from datetime import datetime

def log_message(message, level="INFO"):
    """Log com timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

def deploy_correcao_bug_id():
    """Deploy da correção do bug 'id' no formulário de funcionários"""
    
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    remote_path = '/var/www/controle-ponto'
    
    log_message("🚀 Iniciando deploy da correção do bug 'id' no formulário de funcionários")
    
    try:
        # Conectar via SSH usando chave privada
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        # Tentar conexão com chave SSH primeiro
        try:
            private_key_path = os.path.expanduser('~/.ssh/id_rsa')
            if os.path.exists(private_key_path):
                log_message("🔐 Conectando via chave SSH...")
                private_key = paramiko.RSAKey.from_private_key_file(private_key_path)
                ssh.connect(hostname, username=username, pkey=private_key)
                log_message("✅ Conectado via chave SSH com sucesso!")
            else:
                raise FileNotFoundError("Chave SSH não encontrada")
        except Exception as e:
            log_message(f"⚠️ Falha na conexão SSH com chave: {e}")
            log_message("🔐 Tentando conexão com senha...")
            ssh.connect(hostname, username=username, password='@Ric6109')
            log_message("✅ Conectado via senha com sucesso!")
        
        # 1. Fazer backup do arquivo atual
        log_message("📦 Criando backup do arquivo atual...")
        backup_cmd = f"""
        cd {remote_path}
        cp app_funcionarios.py app_funcionarios.py.backup_bug_id_$(date +%Y%m%d_%H%M%S)
        """
        stdin, stdout, stderr = ssh.exec_command(backup_cmd)
        error = stderr.read().decode()
        if error:
            log_message(f"⚠️ Aviso no backup: {error}", "WARN")
        else:
            log_message("✅ Backup criado com sucesso!")
        
        # 2. Ler o arquivo local corrigido
        log_message("📖 Lendo arquivo corrigido local...")
        try:
            with open('var/www/controle-ponto/app_funcionarios.py', 'r', encoding='utf-8') as f:
                arquivo_corrigido = f.read()
            log_message(f"✅ Arquivo lido: {len(arquivo_corrigido)} caracteres")
        except Exception as e:
            log_message(f"❌ Erro ao ler arquivo local: {e}", "ERROR")
            return False
        
        # 3. Transferir arquivo corrigido
        log_message("📤 Transferindo arquivo corrigido...")
        sftp = ssh.open_sftp()
        
        # Criar arquivo temporário no servidor
        temp_file = f"{remote_path}/app_funcionarios_temp.py"
        with sftp.open(temp_file, 'w') as remote_file:
            remote_file.write(arquivo_corrigido)
        
        log_message("✅ Arquivo transferido com sucesso!")
        
        # 4. Substituir arquivo original
        log_message("🔄 Substituindo arquivo original...")
        replace_cmd = f"""
        cd {remote_path}
        mv app_funcionarios_temp.py app_funcionarios.py
        chown www-data:www-data app_funcionarios.py
        chmod 644 app_funcionarios.py
        """
        stdin, stdout, stderr = ssh.exec_command(replace_cmd)
        error = stderr.read().decode()
        if error:
            log_message(f"⚠️ Aviso na substituição: {error}", "WARN")
        else:
            log_message("✅ Arquivo substituído com sucesso!")
        
        # 5. Verificar sintaxe Python
        log_message("🔍 Verificando sintaxe do arquivo...")
        syntax_cmd = f"cd {remote_path} && python3 -m py_compile app_funcionarios.py"
        stdin, stdout, stderr = ssh.exec_command(syntax_cmd)
        error = stderr.read().decode()
        if error:
            log_message(f"❌ Erro de sintaxe: {error}", "ERROR")
            return False
        else:
            log_message("✅ Sintaxe verificada com sucesso!")
        
        # 6. Reiniciar aplicação
        log_message("🔄 Reiniciando aplicação Flask...")
        restart_cmd = """
        # Parar processos Flask existentes
        pkill -f "python.*app.py" || true
        
        # Aguardar um momento
        sleep 2
        
        # Iniciar aplicação em background
        cd /var/www/controle-ponto
        nohup python3 app.py > /var/log/rlponto-web/app.log 2>&1 &
        
        # Aguardar inicialização
        sleep 5
        
        # Verificar se está rodando
        pgrep -f "python.*app.py" || echo "ERRO: Aplicação não iniciou"
        """
        stdin, stdout, stderr = ssh.exec_command(restart_cmd)
        output = stdout.read().decode()
        error = stderr.read().decode()
        
        if "ERRO" in output:
            log_message(f"❌ Erro ao reiniciar aplicação: {output}", "ERROR")
            return False
        else:
            log_message("✅ Aplicação reiniciada com sucesso!")
        
        # 7. Verificar status da aplicação
        log_message("🔍 Verificando status da aplicação...")
        status_cmd = """
        # Verificar processo
        ps aux | grep "python.*app.py" | grep -v grep
        
        # Verificar porta
        netstat -tlnp | grep :5000 || ss -tlnp | grep :5000
        """
        stdin, stdout, stderr = ssh.exec_command(status_cmd)
        status_output = stdout.read().decode()
        
        if ":5000" in status_output:
            log_message("✅ Aplicação está rodando na porta 5000!")
        else:
            log_message("⚠️ Aplicação pode não estar respondendo na porta 5000", "WARN")
        
        # 8. Teste básico de conectividade
        log_message("🌐 Testando conectividade HTTP...")
        test_cmd = "curl -s -o /dev/null -w '%{http_code}' http://localhost:5000/ || echo 'ERRO'"
        stdin, stdout, stderr = ssh.exec_command(test_cmd)
        http_status = stdout.read().decode().strip()
        
        if http_status in ['200', '302', '301']:
            log_message(f"✅ Aplicação respondendo com status HTTP {http_status}")
        else:
            log_message(f"⚠️ Status HTTP inesperado: {http_status}", "WARN")
        
        # Fechar conexões
        sftp.close()
        ssh.close()
        
        log_message("🎉 Deploy da correção concluído com sucesso!")
        log_message("📋 Resumo das correções aplicadas:")
        log_message("   ✅ Filtro de campos de EPIs na extração de dados")
        log_message("   ✅ Verificação e remoção de campo 'id' isolado")
        log_message("   ✅ Proteção contra validação de campo 'id'")
        log_message("   ✅ Logs detalhados para debug")
        log_message("   ✅ Correção no fluxo de erros de validação")
        
        return True
        
    except Exception as e:
        log_message(f"❌ Erro durante o deploy: {e}", "ERROR")
        return False

def main():
    """Função principal"""
    log_message("=" * 60)
    log_message("🔧 DEPLOY - CORREÇÃO BUG 'ID' FORMULÁRIO FUNCIONÁRIOS")
    log_message("=" * 60)
    
    success = deploy_correcao_bug_id()
    
    if success:
        log_message("=" * 60)
        log_message("✅ DEPLOY CONCLUÍDO COM SUCESSO!")
        log_message("🌐 Acesse: http://************:5000")
        log_message("👤 Teste a edição de funcionários")
        log_message("=" * 60)
        sys.exit(0)
    else:
        log_message("=" * 60)
        log_message("❌ DEPLOY FALHOU!")
        log_message("🔍 Verifique os logs acima para detalhes")
        log_message("=" * 60)
        sys.exit(1)

if __name__ == "__main__":
    main()
