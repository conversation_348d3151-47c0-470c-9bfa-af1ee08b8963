# 🔍 MCP Check - Verificação dos Servidores MCP

## Objetivo
Este documento fornece um checklist rápido para verificar se todos os servidores MCP estão funcionando corretamente antes de iniciar as atividades de desenvolvimento.

## ✅ Checklist de Verificação

### 1. Pré-requisitos do Sistema

#### Node.js e npm
```powershell
# Verificar versão do Node.js (deve ser >= 18.0.0)
node --version

# Verificar versão do npm (deve ser >= 8.0.0)
npm --version

# Verificar versão do npx
npx --version
```

**Resultado Esperado:**
- Node.js: v22.17.0 ou superior
- npm: v10.9.2 ou superior
- npx: v10.9.2 ou superior

#### Política de Execução (Windows)
```powershell
# Verificar política atual
Get-ExecutionPolicy -Scope CurrentUser
```

**Resultado Esperado:** `RemoteSigned` ou `Unrestricted`

### 2. Verificação dos Servidores MCP

#### GitHub MCP (Smithery CLI)
```powershell
# Testar comando básico
npx @smithery/cli@latest --help
```

**Resultado Esperado:** Menu de ajuda do Smithery CLI

#### Browser Tools MCP
```powershell
# Testar disponibilidade (Ctrl+C para sair após iniciar)
npx @agentdeskai/browser-tools-mcp@1.2.0
```

**Resultado Esperado:** Mensagem de "Starting server discovery process"

#### Context7 MCP
```powershell
# Testar comando de ajuda
npx @upstash/context7-mcp --help
```

**Resultado Esperado:** Menu de opções com transport, port, etc.

#### 21st-dev Magic MCP
```powershell
# Testar disponibilidade (Ctrl+C para sair após iniciar)
npx @21st-dev/magic@latest
```

**Resultado Esperado:** Mensagens JSON-RPC de inicialização do servidor

### 3. Verificação da Configuração do VS Code

#### Arquivo de Configuração
```powershell
# Verificar se o arquivo existe
Test-Path ".vscode\settings.json"
```

**Resultado Esperado:** `True`

#### Conteúdo da Configuração
```powershell
# Visualizar conteúdo do arquivo
Get-Content ".vscode\settings.json" | ConvertFrom-Json | ConvertTo-Json -Depth 10
```

**Resultado Esperado:** JSON válido com seção `mcpServers` contendo 4 servidores

### 4. Verificação de Conectividade

#### Registro npm
```powershell
# Testar conectividade com npm registry
Test-NetConnection -ComputerName "registry.npmjs.org" -Port 443
```

**Resultado Esperado:** `TcpTestSucceeded: True`

#### Cache npm
```powershell
# Verificar integridade do cache
npm cache verify
```

**Resultado Esperado:** Relatório de verificação sem erros críticos

### 5. Verificação no VS Code

#### Comandos MCP Disponíveis
1. Abra o VS Code
2. Pressione `Ctrl+Shift+P`
3. Digite "MCP"
4. Verifique se aparecem comandos relacionados ao MCP

**Resultado Esperado:** Lista de comandos MCP disponíveis

#### Logs do VS Code
1. No VS Code: `Help > Toggle Developer Tools`
2. Vá para a aba `Console`
3. Procure por mensagens relacionadas ao MCP

**Resultado Esperado:** Sem erros críticos relacionados ao MCP

## 🚨 Problemas Comuns e Soluções

### ❌ Node.js não encontrado
**Problema:** `node : O termo 'node' não é reconhecido`

**Solução:**
1. Reinicie o terminal/VS Code
2. Verifique se Node.js está no PATH
3. Reinstale o Node.js se necessário

### ❌ Erro de política de execução
**Problema:** `não pode ser carregado porque a execução de scripts está desabilitada`

**Solução:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### ❌ Pacotes MCP não encontrados
**Problema:** Erros ao executar comandos npx

**Solução:**
```powershell
# Limpar cache npm
npm cache clean --force

# Reinstalar pacotes globalmente
npm install -g @smithery/cli @agentdeskai/browser-tools-mcp @upstash/context7-mcp @21st-dev/magic
```

### ❌ Arquivo de configuração ausente
**Problema:** `.vscode\settings.json` não existe

**Solução:**
1. Execute o script de instalação novamente
2. Ou crie manualmente o arquivo com a configuração correta

### ❌ VS Code não reconhece MCP
**Problema:** Comandos MCP não aparecem no VS Code

**Solução:**
1. Feche completamente o VS Code
2. Reabra o VS Code
3. Verifique se há extensões MCP instaladas
4. Consulte os logs do Developer Tools

## 📋 Script de Verificação Rápida

### PowerShell Script
```powershell
# Script de verificação rápida dos MCPs
Write-Host "=== MCP Check - Verificação Rápida ===" -ForegroundColor Green
Write-Host ""

# 1. Verificar Node.js
Write-Host "1. Verificando Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "   ✓ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "   ✗ Node.js não encontrado!" -ForegroundColor Red
}

# 2. Verificar npm
Write-Host "2. Verificando npm..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version
    Write-Host "   ✓ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "   ✗ npm não encontrado!" -ForegroundColor Red
}

# 3. Verificar arquivo de configuração
Write-Host "3. Verificando configuração..." -ForegroundColor Yellow
if (Test-Path ".vscode\settings.json") {
    Write-Host "   ✓ Arquivo de configuração encontrado" -ForegroundColor Green
} else {
    Write-Host "   ✗ Arquivo de configuração não encontrado!" -ForegroundColor Red
}

# 4. Testar servidores MCP
Write-Host "4. Testando servidores MCP..." -ForegroundColor Yellow

$servers = @(
    @{name="GitHub MCP"; command="npx @smithery/cli@latest --help"},
    @{name="Context7 MCP"; command="npx @upstash/context7-mcp --help"}
)

foreach ($server in $servers) {
    try {
        $result = Invoke-Expression "$($server.command) 2>$null"
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✓ $($server.name) funcionando" -ForegroundColor Green
        } else {
            Write-Host "   ⚠ $($server.name) pode precisar de configuração" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   ✗ $($server.name) com problemas" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Verificação concluída!" -ForegroundColor Green
```

## 📝 Log de Verificação

### Template de Log
```
Data: ___________
Hora: ___________

✅ Pré-requisitos:
[ ] Node.js v22.17.0+
[ ] npm v10.9.2+
[ ] npx v10.9.2+
[ ] Política de execução configurada

✅ Servidores MCP:
[ ] GitHub MCP (Smithery CLI)
[ ] Browser Tools MCP
[ ] Context7 MCP
[ ] 21st-dev Magic MCP

✅ Configuração VS Code:
[ ] Arquivo .vscode/settings.json existe
[ ] Configuração JSON válida
[ ] 4 servidores configurados

✅ Conectividade:
[ ] npm registry acessível
[ ] Cache npm íntegro

✅ VS Code:
[ ] Comandos MCP disponíveis
[ ] Sem erros nos logs

Observações:
_________________________________
_________________________________
_________________________________

Verificado por: _________________
```

## 🔄 Frequência de Verificação

### Quando Executar o Check
- **Diariamente:** Antes de iniciar atividades de desenvolvimento
- **Após atualizações:** Do Node.js, npm ou VS Code
- **Após problemas:** Quando houver erros relacionados ao MCP
- **Semanalmente:** Verificação preventiva completa

### Tempo Estimado
- **Verificação rápida:** 2-3 minutos
- **Verificação completa:** 5-10 minutos
- **Resolução de problemas:** 10-30 minutos

## 📞 Suporte

### Em caso de problemas persistentes:
1. Consulte `docs/mcp-instalacao-concluida.md`
2. Execute o script de verificação completo
3. Verifique os logs detalhados do VS Code
4. Reinstale os pacotes MCP se necessário

---

**Última atualização:** 11/07/2025  
**Versão do documento:** 1.0  
**Compatível com:** Node.js v22.17.0+, VS Code
