# 🚀 INSTRUÇÕES PARA DEPLOY MANUAL - CORREÇÃO BUG USUÁRIOS

## ⚠️ IMPORTANTE
**Execute estes comandos no seu terminal do Windows (PowerShell ou CMD), NÃO através da IA!**

A IA não consegue processar entrada interativa de senha, por isso você precisa executar manualmente.

---

## 📋 PASSO A PASSO

### **SENHA SSH:** `@Ric6109`

### **1. Abrir Terminal**
- Pressione `Win + R`
- Digite `powershell` ou `cmd`
- Pressione Enter
- Navegue até a pasta do projeto:
```bash
cd "C:\Users\<USER>\OneDrive\Documentos\RLPONTO-WEB"
```

### **2. Executar Comandos de Deploy**

Execute cada comando abaixo **UM POR VEZ** e digite a senha `@Ric6109` quando solicitado:

#### **2.1. <PERSON>azer Backup**
```bash
ssh root@************ "cp /var/www/controle-ponto/app.py /var/www/controle-ponto/backup-build/app_backup_bug_usuarios_20250707.py"
```
*Digite a senha: `@Ric6109`*

#### **2.2. Enviar Arquivo Corrigido**
```bash
scp "var\www\controle-ponto\app.py" root@************:/var/www/controle-ponto/app.py
```
*Digite a senha: `@Ric6109`*

#### **2.3. Enviar Script SQL**
```bash
scp "corrigir_permissoes_usuarios.sql" root@************:/tmp/corrigir_permissoes.sql
```
*Digite a senha: `@Ric6109`*

#### **2.4. Executar Correções SQL**
```bash
ssh root@************ "mysql -u cavalcrod -p200381 controle_ponto < /tmp/corrigir_permissoes.sql"
```
*Digite a senha: `@Ric6109`*

#### **2.5. Reiniciar Apache**
```bash
ssh root@************ "systemctl restart apache2"
```
*Digite a senha: `@Ric6109`*

#### **2.6. Reiniciar MySQL**
```bash
ssh root@************ "systemctl restart mysql"
```
*Digite a senha: `@Ric6109`*

#### **2.7. Verificar Status**
```bash
ssh root@************ "systemctl status apache2 | grep Active"
```
*Digite a senha: `@Ric6109`*

---

## ✅ TESTE DA CORREÇÃO

Após executar todos os comandos:

1. **Acesse:** http://************/configurar_usuarios
2. **Teste:** Altere o nível de um usuário de 'usuario' para 'admin'
3. **Verifique:** Se a mudança é aplicada e permanece após reload da página
4. **Teste reverso:** Altere de 'admin' para 'usuario'

---

## 🔧 RESUMO DAS CORREÇÕES APLICADAS

### **Código Corrigido (app.py):**
- ✅ Verifica se usuário tem registro na tabela `permissoes`
- ✅ Se não tem, cria o registro automaticamente
- ✅ Se tem, faz UPDATE e verifica se foi bem-sucedido
- ✅ Logs melhorados para debug

### **Banco de Dados Corrigido (SQL):**
- ✅ Adiciona permissões para usuários sem registro
- ✅ Usuário cavalcrod (ID 5) recebe permissão 'admin'
- ✅ Outros usuários recebem permissão 'usuario' por padrão
- ✅ Backup das tabelas criado automaticamente

### **Problema Original:**
- ❌ Usuário ID 5 (cavalcrod) não tinha registro na tabela `permissoes`
- ❌ UPDATE não afetava nenhuma linha
- ❌ Interface não verificava se UPDATE foi bem-sucedido

---

## 🆘 SE HOUVER PROBLEMAS

### **Erro de Conexão SSH:**
```bash
# Teste a conectividade
ping ************

# Teste SSH básico
ssh root@************ "echo 'teste'"
```

### **Verificar Logs do Sistema:**
```bash
ssh root@************ "tail -f /var/log/apache2/error.log"
```

### **Verificar Status dos Serviços:**
```bash
ssh root@************ "systemctl status apache2"
ssh root@************ "systemctl status mysql"
```

---

## 📞 PRÓXIMOS PASSOS

1. **Execute os comandos manualmente** no seu terminal
2. **Teste a funcionalidade** no sistema web
3. **Confirme se o bug foi corrigido**
4. **Reporte o resultado** para validação final

---

**💡 DICA:** Copie e cole cada comando individualmente no terminal para evitar erros de digitação.
