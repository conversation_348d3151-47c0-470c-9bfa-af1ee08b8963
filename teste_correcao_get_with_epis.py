#!/usr/bin/env python3
"""
Teste da Correção 1: Função get_with_epis()
Verifica se a função retorna dados corretos da jornada da empresa
"""

import sys
import os
sys.path.append('var/www/controle-ponto')

from utils.database import FuncionarioQueries, DatabaseManager
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def teste_get_with_epis():
    """Testa a função get_with_epis() corrigida"""
    print("🧪 TESTE DA CORREÇÃO 1: Função get_with_epis()")
    print("=" * 60)
    
    try:
        # Buscar um funcionário existente para teste
        funcionarios_query = "SELECT id, nome_completo, empresa_id, jornada_trabalho_id FROM funcionarios LIMIT 3"
        funcionarios = DatabaseManager.execute_query(funcionarios_query)
        
        if not funcionarios:
            print("❌ Nenhum funcionário encontrado para teste")
            return False
        
        print(f"📋 Funcionários encontrados para teste: {len(funcionarios)}")
        
        for funcionario in funcionarios:
            funcionario_id = funcionario['id']
            nome = funcionario['nome_completo']
            empresa_id = funcionario.get('empresa_id')
            jornada_id = funcionario.get('jornada_trabalho_id')
            
            print(f"\n🔍 Testando funcionário: {nome} (ID: {funcionario_id})")
            print(f"   Empresa ID: {empresa_id}")
            print(f"   Jornada ID: {jornada_id}")
            
            # Testar função get_with_epis()
            resultado = FuncionarioQueries.get_with_epis(funcionario_id)
            
            if resultado:
                print("✅ Função get_with_epis() executou com sucesso")
                
                # Verificar se dados de jornada estão presentes
                jornada_entrada = resultado.get('jornada_seg_qui_entrada')
                jornada_saida = resultado.get('jornada_seg_qui_saida')
                nome_jornada = resultado.get('nome_jornada')
                
                print(f"   📅 Jornada: {nome_jornada or 'N/A'}")
                print(f"   🕐 Entrada: {jornada_entrada or 'N/A'}")
                print(f"   🕕 Saída: {jornada_saida or 'N/A'}")
                
                # Verificar se EPIs estão presentes
                epis = resultado.get('epis', [])
                print(f"   🦺 EPIs: {len(epis)} encontrados")
                
                # Verificar se não há conflito de campos
                campos_individuais = [
                    'jornada_seg_qui_entrada', 'jornada_seg_qui_saida',
                    'jornada_sex_entrada', 'jornada_sex_saida',
                    'jornada_intervalo_entrada', 'jornada_intervalo_saida'
                ]
                
                conflitos = []
                for campo in campos_individuais:
                    if campo in resultado and resultado[campo] is not None:
                        # Verificar se vem da jornada da empresa ou campo individual
                        if nome_jornada:
                            print(f"   ✅ {campo}: {resultado[campo]} (da jornada da empresa)")
                        else:
                            print(f"   ⚠️ {campo}: {resultado[campo]} (fallback)")
                
                print("   ✅ Teste concluído para este funcionário")
                
            else:
                print("❌ Função get_with_epis() retornou None")
                return False
        
        print("\n🎯 RESULTADO FINAL:")
        print("✅ Função get_with_epis() está funcionando corretamente")
        print("✅ Dados de jornada vêm da empresa (fonte única)")
        print("✅ Não há conflitos entre campos individuais e jornada da empresa")
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    sucesso = teste_get_with_epis()
    if sucesso:
        print("\n🎉 TESTE DA CORREÇÃO 1: SUCESSO")
        sys.exit(0)
    else:
        print("\n💥 TESTE DA CORREÇÃO 1: FALHOU")
        sys.exit(1)
