#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para aplicar a correção da função excluir_empresa no arquivo completo
Data: 03/07/2025
"""

import re
import sys
import os
import shutil
from datetime import datetime

# Configuração
ARQUIVO_ORIGINAL = "/var/www/controle-ponto/app_configuracoes.py"
ARQUIVO_BACKUP = f"/var/www/controle-ponto/backup-build/app_configuracoes_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
ARQUIVO_TEMP = "/var/www/controle-ponto/app_configuracoes.py.tmp"

# Função corrigida
FUNCAO_CORRIGIDA = '''
@configuracoes_bp.route('/empresas/<int:empresa_id>/excluir', methods=['POST'])
@require_admin
def excluir_empresa(empresa_id):
    """
    Exclui uma empresa (soft delete). 
    Suporta tanto requisições AJAX (JSON) quanto formulários tradicionais.
    """
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Primeiro verificar se a empresa existe
        cursor.execute('SELECT id, razao_social, ativa FROM empresas WHERE id = %s', (empresa_id,))
        empresa_existe = cursor.fetchone()
        
        if not empresa_existe:
            message = 'Empresa não encontrada'
            logger.warning(f"[DEBUG EXCLUSÃO] Tentativa de excluir empresa inexistente: {empresa_id}")
            
            if is_ajax:
                conn.close()
                return jsonify({
                    'success': False,
                    'error': message
                }), 404
            else:
                flash(message, 'error')
                conn.close()
                return redirect(url_for('configuracoes.listar_empresas'))
        
        # Correção: Usar formato de dicionário para acessar dados
        if isinstance(empresa_existe, dict):
            razao_social = empresa_existe.get('razao_social', 'Desconhecida')
            ativa = empresa_existe.get('ativa', False)
        else:
            # Fallback para formato de tupla (caso necessário)
            razao_social = empresa_existe[1] if len(empresa_existe) > 1 else 'Desconhecida'
            ativa = empresa_existe[2] if len(empresa_existe) > 2 else False
            
        logger.info(f"[DEBUG EXCLUSÃO] Empresa encontrada - ID: {empresa_id}, Razão: {razao_social}, Ativa: {ativa}")
        
        # Verificar se a empresa tem funcionários ativos com debug
        cursor.execute("""
            SELECT COUNT(*) as total FROM funcionarios
            WHERE empresa_id = %s AND ativo = TRUE
        """, (empresa_id,))
        
        resultado_consulta = cursor.fetchone()
        
        # CORREÇÃO: O cursor retorna dicionário, não tupla
        if isinstance(resultado_consulta, dict):
            funcionarios_ativos = resultado_consulta.get('total', 0)
        else:
            funcionarios_ativos = resultado_consulta[0] if resultado_consulta else 0
        
        # Debug log detalhado para investigar problema
        logger.info(f"[DEBUG EXCLUSÃO] Empresa ID: {empresa_id}")
        logger.info(f"[DEBUG EXCLUSÃO] Resultado consulta: {resultado_consulta}")
        logger.info(f"[DEBUG EXCLUSÃO] Funcionários ativos: {funcionarios_ativos}")
        logger.info(f"[DEBUG EXCLUSÃO] Tipo do resultado: {type(resultado_consulta)}")
        
        if funcionarios_ativos > 0:
            message = f'Não é possível excluir a empresa. Há {funcionarios_ativos} funcionário(s) ativo(s) vinculado(s).'
            
            if is_ajax:
                conn.close()
                return jsonify({
                    'success': False,
                    'error': message
                }), 400
            else:
                flash(message, 'error')
                conn.close()
                return redirect(url_for('configuracoes.listar_empresas'))
        
        # Verificar e remover relações dependentes
        try:
            # Verificar jornadas de trabalho
            cursor.execute("SELECT COUNT(*) as total FROM jornadas_trabalho WHERE empresa_id = %s", (empresa_id,))
            jornadas = cursor.fetchone()
            jornadas_count = jornadas.get('total', 0) if isinstance(jornadas, dict) else (jornadas[0] if jornadas else 0)
            
            if jornadas_count > 0:
                logger.info(f"[DEBUG EXCLUSÃO] Removendo {jornadas_count} jornadas de trabalho da empresa {empresa_id}")
                cursor.execute("DELETE FROM jornadas_trabalho WHERE empresa_id = %s", (empresa_id,))
            
            # Verificar horários de trabalho
            cursor.execute("SELECT COUNT(*) as total FROM horarios_trabalho WHERE empresa_id = %s", (empresa_id,))
            horarios = cursor.fetchone()
            horarios_count = horarios.get('total', 0) if isinstance(horarios, dict) else (horarios[0] if horarios else 0)
            
            if horarios_count > 0:
                logger.info(f"[DEBUG EXCLUSÃO] Removendo {horarios_count} horários de trabalho da empresa {empresa_id}")
                cursor.execute("DELETE FROM horarios_trabalho WHERE empresa_id = %s", (empresa_id,))
            
            # Verificar clientes
            cursor.execute("SELECT COUNT(*) as total FROM clientes WHERE empresa_id = %s", (empresa_id,))
            clientes = cursor.fetchone()
            clientes_count = clientes.get('total', 0) if isinstance(clientes, dict) else (clientes[0] if clientes else 0)
            
            if clientes_count > 0:
                logger.info(f"[DEBUG EXCLUSÃO] Removendo {clientes_count} clientes da empresa {empresa_id}")
                cursor.execute("DELETE FROM clientes WHERE empresa_id = %s", (empresa_id,))
        
        except Exception as e:
            logger.error(f"[DEBUG EXCLUSÃO] Erro ao remover relações da empresa {empresa_id}: {str(e)}")
            conn.rollback()
            
            if is_ajax:
                return jsonify({
                    'success': False,
                    'error': f'Erro ao remover relações: {str(e)}'
                }), 500
            else:
                flash(f'Erro ao remover relações: {str(e)}', 'error')
                conn.close()
                return redirect(url_for('configuracoes.listar_empresas'))
        
        # Soft delete - marcar como inativa
        try:
            cursor.execute("""
                UPDATE empresas SET ativa = FALSE
                WHERE id = %s
            """, (empresa_id,))
            
            conn.commit()
        except Exception as e:
            logger.error(f"[DEBUG EXCLUSÃO] Erro ao atualizar status da empresa {empresa_id}: {str(e)}")
            conn.rollback()
            
            if is_ajax:
                return jsonify({
                    'success': False,
                    'error': f'Erro ao atualizar status: {str(e)}'
                }), 500
            else:
                flash(f'Erro ao atualizar status: {str(e)}', 'error')
                conn.close()
                return redirect(url_for('configuracoes.listar_empresas'))
        
        conn.close()
        
        logger.info(f"Empresa excluída (soft delete) - ID: {empresa_id}, Razão Social: {razao_social}")
        message = 'Empresa excluída com sucesso!'
        
        if is_ajax:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            flash(message, 'success')
            return redirect(url_for('configuracoes.listar_empresas'))
        
    except Exception as e:
        logger.error(f"Erro ao excluir empresa {empresa_id}: {str(e)}")
        message = f'Erro ao excluir empresa: {str(e)}'
        
        if is_ajax:
            return jsonify({
                'success': False,
                'error': message
            }), 500
        else:
            flash(message, 'error')
            return redirect(url_for('configuracoes.listar_empresas'))
'''

def main():
    print(f"Aplicando correção na função excluir_empresa...")
    
    # Verificar se o arquivo existe
    if not os.path.exists(ARQUIVO_ORIGINAL):
        print(f"Erro: Arquivo {ARQUIVO_ORIGINAL} não encontrado!")
        return False
    
    # Fazer backup do arquivo original
    try:
        shutil.copy2(ARQUIVO_ORIGINAL, ARQUIVO_BACKUP)
        print(f"Backup criado: {ARQUIVO_BACKUP}")
    except Exception as e:
        print(f"Erro ao criar backup: {str(e)}")
        return False
    
    # Ler o conteúdo do arquivo
    try:
        with open(ARQUIVO_ORIGINAL, 'r', encoding='utf-8') as f:
            conteudo = f.read()
    except Exception as e:
        print(f"Erro ao ler arquivo: {str(e)}")
        return False
    
    # Padrão para encontrar a função excluir_empresa
    padrao = r'@configuracoes_bp\.route\([\'\"]\/empresas\/\<int:empresa_id\>\/excluir[\'\"].*?def excluir_empresa\(.*?\).*?(?=@configuracoes_bp\.route|# ={10,})'
    
    # Substituir a função
    try:
        novo_conteudo = re.sub(padrao, FUNCAO_CORRIGIDA, conteudo, flags=re.DOTALL)
        
        # Verificar se a substituição foi feita
        if novo_conteudo == conteudo:
            print("Aviso: Nenhuma substituição foi feita. A função pode não ter sido encontrada.")
            return False
        
        # Escrever o novo conteúdo em um arquivo temporário
        with open(ARQUIVO_TEMP, 'w', encoding='utf-8') as f:
            f.write(novo_conteudo)
        
        # Substituir o arquivo original pelo temporário
        shutil.move(ARQUIVO_TEMP, ARQUIVO_ORIGINAL)
        print("Correção aplicada com sucesso!")
        
        return True
    except Exception as e:
        print(f"Erro ao aplicar correção: {str(e)}")
        return False

if __name__ == "__main__":
    if main():
        print("✅ Correção aplicada com sucesso!")
        sys.exit(0)
    else:
        print("❌ Falha ao aplicar correção!")
        sys.exit(1)