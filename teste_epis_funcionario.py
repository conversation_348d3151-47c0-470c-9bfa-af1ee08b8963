#!/usr/bin/env python3
"""
Script para testar o cadastro de EPIs em funcionários.
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def testar_cadastro_epi():
    """
    Testa o cadastro de EPI para um funcionário.
    """
    try:
        print("🔍 TESTE: Cadastro de EPI para funcionário")
        print("=" * 50)
        
        # 1. Verificar se há funcionários cadastrados
        funcionarios = DatabaseManager.execute_query(
            "SELECT id, nome_completo FROM funcionarios ORDER BY id DESC LIMIT 5"
        )
        
        if not funcionarios:
            print("❌ Nenhum funcionário encontrado")
            return False
            
        print(f"📋 Funcionários disponíveis:")
        for func in funcionarios:
            print(f"   - ID {func['id']}: {func['nome_completo']}")
        
        # Usar o primeiro funcionário
        funcionario_id = funcionarios[0]['id']
        funcionario_nome = funcionarios[0]['nome_completo']
        
        print(f"\n🎯 Testando com funcionário: {funcionario_nome} (ID: {funcionario_id})")
        
        # 2. Verificar EPIs existentes
        epis_existentes = DatabaseManager.execute_query(
            "SELECT * FROM epis WHERE funcionario_id = %s",
            (funcionario_id,)
        )
        
        print(f"📦 EPIs existentes: {len(epis_existentes)}")
        for epi in epis_existentes:
            print(f"   - {epi['epi_nome']} (CA: {epi['epi_ca']})")
        
        # 3. Cadastrar um EPI de teste
        print(f"\n🦺 Cadastrando EPI de teste...")
        
        sql_insert = """
        INSERT INTO epis (funcionario_id, epi_nome, epi_ca, epi_data_entrega, epi_data_validade, epi_observacoes)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        params = (
            funcionario_id,
            "Capacete de Teste",
            "12345",
            "2025-07-08",
            "2026-07-08",
            "EPI de teste criado automaticamente"
        )
        
        epi_id = DatabaseManager.execute_query(sql_insert, params, fetch_all=False)
        
        if epi_id:
            print(f"✅ EPI cadastrado com sucesso! ID: {epi_id}")
            
            # 4. Verificar se aparece na consulta
            epis_atualizados = DatabaseManager.execute_query(
                "SELECT * FROM epis WHERE funcionario_id = %s",
                (funcionario_id,)
            )
            
            print(f"📦 EPIs após inserção: {len(epis_atualizados)}")
            for epi in epis_atualizados:
                print(f"   - {epi['epi_nome']} (CA: {epi['epi_ca']}) - ID: {epi['id']}")
            
            # 5. Testar função get_with_epis
            print(f"\n🔍 Testando função get_with_epis...")
            from utils.database import FuncionarioQueries
            
            funcionario_com_epis = FuncionarioQueries.get_with_epis(funcionario_id)
            
            if funcionario_com_epis and funcionario_com_epis.get('epis'):
                print(f"✅ Função get_with_epis funcionando!")
                print(f"📋 EPIs retornados: {len(funcionario_com_epis['epis'])}")
                for epi in funcionario_com_epis['epis']:
                    print(f"   - {epi['epi_nome']} (CA: {epi['epi_ca']})")
            else:
                print(f"❌ Função get_with_epis não retornou EPIs")
                print(f"   Funcionário: {funcionario_com_epis.get('nome_completo', 'N/A')}")
                print(f"   EPIs: {funcionario_com_epis.get('epis', 'NENHUM')}")
            
            return True
        else:
            print(f"❌ Erro ao cadastrar EPI")
            return False
            
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def limpar_epis_teste():
    """
    Remove EPIs de teste.
    """
    try:
        print("\n🧹 Limpando EPIs de teste...")
        result = DatabaseManager.execute_query(
            "DELETE FROM epis WHERE epi_nome LIKE '%Teste%'",
            fetch_all=False
        )
        print(f"✅ EPIs de teste removidos")
        
    except Exception as e:
        print(f"❌ Erro ao limpar EPIs de teste: {e}")

if __name__ == "__main__":
    print("🚀 Iniciando teste de EPIs...")
    
    # Executar teste
    sucesso = testar_cadastro_epi()
    
    if sucesso:
        print(f"\n✅ TESTE CONCLUÍDO COM SUCESSO!")
        
        # Perguntar se deve limpar
        resposta = input("\n🧹 Deseja remover os EPIs de teste? (s/n): ")
        if resposta.lower() == 's':
            limpar_epis_teste()
    else:
        print(f"\n❌ TESTE FALHOU!")
    
    print(f"\n🏁 Teste finalizado.")
