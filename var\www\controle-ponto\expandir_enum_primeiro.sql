-- =====================================================
-- ESTRATÉGIA: Expandir ENUM primeiro, depois corrigir dados
-- Data: 13/07/2025
-- =====================================================

-- 1. Expandir ENUM para incluir todos os valores possíveis
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN turno ENUM('Diurno','Noturno','Misto','Integral','Manha','Tarde','Noite') NOT NULL;

-- 2. Agora corrigir os dados
UPDATE funcionarios_desligados SET turno = 'Diurno' WHERE turno = 'Integral';
UPDATE funcionarios_desligados SET turno = 'Diurno' WHERE turno = 'Manha';
UPDATE funcionarios_desligados SET turno = 'Diurno' WHERE turno = 'Tarde';
UPDATE funcionarios_desligados SET turno = 'Noturno' WHERE turno = 'Noite';

-- 3. Verificar se os dados foram corrigidos
SELECT DISTINCT turno FROM funcionarios_desligados;

-- 4. Agora reduzir o ENUM para os valores corretos
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN turno ENUM('Diurno','Noturno','Misto') NOT NULL;

-- 5. Fazer o mesmo para tipo_contrato
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN tipo_contrato ENUM('CLT','PJ','Estagio','Temporario','Terceirizado','Estagiario') NOT NULL;

-- Corrigir dados
UPDATE funcionarios_desligados SET tipo_contrato = 'CLT' WHERE tipo_contrato = 'Terceirizado';
UPDATE funcionarios_desligados SET tipo_contrato = 'Estagio' WHERE tipo_contrato = 'Estagiario';

-- Reduzir ENUM
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN tipo_contrato ENUM('CLT','PJ','Estagio','Temporario') NOT NULL;

-- 6. Fazer o mesmo para status_cadastro
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN status_cadastro ENUM('Ativo','Inativo','Pendente') DEFAULT 'Ativo';

-- Corrigir dados
UPDATE funcionarios_desligados SET status_cadastro = 'Inativo' WHERE status_cadastro = 'Pendente';

-- Reduzir ENUM
ALTER TABLE funcionarios_desligados 
MODIFY COLUMN status_cadastro ENUM('Ativo','Inativo') DEFAULT 'Ativo';

SELECT 'Correção completa concluída!' as resultado;
