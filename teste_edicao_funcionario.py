#!/usr/bin/env python3
"""
Teste para verificar carregamento de dados na edição de funcionário
"""
import sys
import os
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager, FuncionarioQueries

def testar_edicao_funcionario():
    print("🧪 TESTE DE EDIÇÃO DE FUNCIONÁRIO")
    print("=" * 60)
    
    try:
        # Teste com funcionário ID 35 (Richardson)
        funcionario_id = 35
        
        print(f"\n1. 🔍 TESTE DIRETO NO BANCO DE DADOS")
        db = DatabaseManager()
        
        # Consulta direta
        resultado_direto = db.execute_query("""
            SELECT id, nome_completo, horas_semanais_obrigatorias 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,), fetch_one=True)
        
        if resultado_direto:
            print(f"   ✅ Banco direto: {resultado_direto['nome_completo']}")
            print(f"   📊 Horas semanais: {resultado_direto['horas_semanais_obrigatorias']}")
        else:
            print(f"   ❌ Funcionário {funcionario_id} não encontrado")
            return
        
        print(f"\n2. 🔧 TESTE FuncionarioQueries.get_by_id()")
        funcionario_query = FuncionarioQueries.get_by_id(funcionario_id)
        
        if funcionario_query:
            print(f"   ✅ Query: {funcionario_query['nome_completo']}")
            print(f"   📊 Horas semanais: {funcionario_query.get('horas_semanais_obrigatorias', 'CAMPO NÃO ENCONTRADO')}")
            
            # Verificar todos os campos relacionados a horas
            campos_horas = [k for k in funcionario_query.keys() if 'hora' in k.lower()]
            print(f"   🔍 Campos com 'hora': {campos_horas}")
            
            for campo in campos_horas:
                valor = funcionario_query.get(campo)
                print(f"      - {campo}: {valor}")
                
        else:
            print(f"   ❌ FuncionarioQueries.get_by_id() retornou None")
        
        print(f"\n3. 🔧 TESTE FuncionarioQueries.get_with_epis()")
        funcionario_epis = FuncionarioQueries.get_with_epis(funcionario_id)
        
        if funcionario_epis:
            print(f"   ✅ Query com EPIs: {funcionario_epis['nome_completo']}")
            print(f"   📊 Horas semanais: {funcionario_epis.get('horas_semanais_obrigatorias', 'CAMPO NÃO ENCONTRADO')}")
        else:
            print(f"   ❌ FuncionarioQueries.get_with_epis() retornou None")
        
        print(f"\n4. 🧪 TESTE DE ATUALIZAÇÃO")
        # Vamos tentar atualizar para 48 horas e verificar
        print("   Atualizando para 48 horas...")
        
        db.execute_query("""
            UPDATE funcionarios 
            SET horas_semanais_obrigatorias = 48.00 
            WHERE id = %s
        """, (funcionario_id,))
        
        # Verificar se atualizou
        verificacao = db.execute_query("""
            SELECT horas_semanais_obrigatorias 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,), fetch_one=True)
        
        if verificacao:
            print(f"   ✅ Após update: {verificacao['horas_semanais_obrigatorias']}h")
        
        # Voltar para 40 horas
        db.execute_query("""
            UPDATE funcionarios 
            SET horas_semanais_obrigatorias = 40.00 
            WHERE id = %s
        """, (funcionario_id,))
        
        print("   ✅ Voltou para 40 horas")
        
        print("\n" + "=" * 60)
        print("✅ TESTE CONCLUÍDO")
        
    except Exception as e:
        print(f"❌ ERRO GERAL NO TESTE: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    testar_edicao_funcionario()
