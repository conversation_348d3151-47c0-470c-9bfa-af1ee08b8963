#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug avançado para erro 500 no controle de período
Sistema: RLPONTO-WEB v1.0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_flask_app():
    """Testa se o app Flask está funcionando"""
    print("🔍 Testando Flask App...")
    
    try:
        from flask import Flask
        from app import app
        
        with app.test_client() as client:
            # Teste 1: Página de login
            response = client.get('/login')
            print(f"✅ Login page: {response.status_code}")
            
            # Teste 2: Login com admin
            response = client.post('/login', data={
                'usuario': 'admin',
                'senha': '@Ric6109'
            }, follow_redirects=False)
            print(f"✅ Login admin: {response.status_code}")
            
            # Teste 3: Verificar se há cookies de sessão
            if 'Set-Cookie' in response.headers:
                print("✅ Sessão criada com sucesso")
                
                # Teste 4: Tentar acessar controle de período
                response = client.get('/controle-periodo/test', follow_redirects=False)
                print(f"✅ Controle período test: {response.status_code}")
                
                if response.status_code == 500:
                    print("❌ ERRO 500 DETECTADO!")
                    print(f"Response data: {response.get_data(as_text=True)[:500]}")
                
            else:
                print("❌ Falha na criação da sessão")
                
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste Flask: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_function_call():
    """Testa chamada direta das funções"""
    print("\n🔍 Testando chamada direta das funções...")
    
    try:
        # Simular contexto Flask
        from flask import Flask
        from app import app
        
        with app.app_context():
            # Simular request context
            with app.test_request_context('/controle-periodo/'):
                # Simular sessão
                from flask import session
                session['usuario'] = 'admin'
                session['nivel_acesso'] = 'admin'
                
                # Tentar importar e executar função dashboard
                from app_controle_periodo import dashboard
                
                print("✅ Função dashboard importada")
                
                # Tentar executar
                result = dashboard()
                print(f"✅ Função dashboard executada: {type(result)}")
                
                return True
                
    except Exception as e:
        print(f"❌ Erro na chamada direta: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_rendering():
    """Testa renderização do template"""
    print("\n🔍 Testando renderização do template...")
    
    try:
        from flask import Flask, render_template
        from app import app
        
        with app.app_context():
            # Tentar renderizar template com dados mínimos
            dados_teste = {
                'periodo': {
                    'inicio': '2025-06-21',
                    'fim': '2025-07-20',
                    'dias_totais': 30,
                    'dias_decorridos': 21,
                    'dias_restantes': 9,
                    'percentual_concluido': 70.0
                },
                'estatisticas': {
                    'funcionarios_ativos': 4,
                    'dias_registrados': 21,
                    'horas_extras_formatadas': '0h 0min',
                    'atrasos_formatados': '0h 0min'
                },
                'decisoes_pendentes': []
            }
            
            result = render_template('controle_periodo/dashboard.html', **dados_teste)
            print(f"✅ Template renderizado: {len(result)} caracteres")
            
            return True
            
    except Exception as e:
        print(f"❌ Erro na renderização do template: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_functions():
    """Testa funções específicas do banco"""
    print("\n🔍 Testando funções específicas do banco...")
    
    try:
        # Importar funções auxiliares
        sys.path.append('/var/www/controle-ponto')
        from app_controle_periodo import calcular_periodo_apuracao, obter_estatisticas_periodo, obter_decisoes_pendentes
        
        # Teste 1: Calcular período
        periodo = calcular_periodo_apuracao()
        print(f"✅ Período calculado: {periodo}")
        
        # Teste 2: Estatísticas
        estatisticas = obter_estatisticas_periodo()
        print(f"✅ Estatísticas: {estatisticas}")
        
        # Teste 3: Decisões pendentes
        decisoes = obter_decisoes_pendentes()
        print(f"✅ Decisões pendentes: {len(decisoes)} itens")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro nas funções do banco: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal de debug"""
    print("=" * 60)
    print("🔧 DEBUG AVANÇADO - ERRO 500")
    print("=" * 60)
    
    success = True
    
    # Teste 1: Flask App
    if not test_flask_app():
        success = False
    
    # Teste 2: Chamada direta
    if not test_direct_function_call():
        success = False
    
    # Teste 3: Template
    if not test_template_rendering():
        success = False
    
    # Teste 4: Banco de dados
    if not test_database_functions():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ TODOS OS TESTES PASSARAM!")
        print("O erro 500 pode estar em outro lugar.")
    else:
        print("❌ ALGUNS TESTES FALHARAM!")
        print("Verifique os erros acima.")
    print("=" * 60)

if __name__ == "__main__":
    main()
