#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste das Rotas com Hífen - Empresa Principal
=============================================

Script para testar as rotas corretas com hífen.

Data: 07/07/2025
"""

import requests

# Configurações
BASE_URL = "http://************"
LOGIN_URL = f"{BASE_URL}/login"

def testar_rotas_com_hifen():
    """Testa as rotas corretas com hífen"""
    print("🧪 TESTE DAS ROTAS COM HÍFEN (/empresa-principal)")
    print("=" * 60)
    
    # Criar sessão e fazer login
    session = requests.Session()
    login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    
    if response.status_code not in [200, 302]:
        print(f"❌ Falha no login: {response.status_code}")
        return
    
    print("✅ Login realizado com sucesso")
    
    # Rotas corretas com hífen
    urls_para_testar = [
        ("/empresa-principal/teste", "Teste do blueprint"),
        ("/empresa-principal/", "Dashboard empresa principal"),
        ("/empresa-principal", "Dashboard empresa principal (sem barra)"),
        ("/empresa-principal/clientes", "Página de clientes"),
        ("/empresa-principal/alocacoes", "🎯 PÁGINA DE ALOCAÇÕES"),
        ("/empresa-principal/api/empresa-principal", "API empresa principal"),
    ]
    
    print(f"\n📋 TESTANDO ROTAS COM HÍFEN:")
    print("-" * 40)
    
    rotas_funcionando = []
    url_alocacoes_encontrada = None
    
    for url, descricao in urls_para_testar:
        try:
            full_url = BASE_URL + url
            response = session.get(full_url)
            
            status_icon = "✅" if response.status_code == 200 else "❌"
            print(f"   {status_icon} {url}: {response.status_code} - {descricao}")
            
            if response.status_code == 200:
                rotas_funcionando.append((url, descricao))
                
                # Verificar conteúdo específico
                content = response.text.lower()
                
                if "alocação" in content or "alocacoes" in content:
                    print(f"      🎯 PÁGINA DE ALOCAÇÕES ENCONTRADA!")
                    url_alocacoes_encontrada = url
                    
                    # Verificar se há erro
                    if "erro ao carregar" in content:
                        print(f"      ❌ ERRO DETECTADO: 'erro ao carregar alocações'")
                        print(f"      🔧 Precisa corrigir a consulta SQL")
                    else:
                        print(f"      ✅ Página carregou sem erros aparentes")
                        
                elif "blueprint" in content and "funcionando" in content:
                    print(f"      🔧 Rota de teste funcionando")
                elif "empresa principal" in content:
                    print(f"      🏢 Dashboard empresa principal")
                elif "cliente" in content:
                    print(f"      👥 Página de clientes")
            
        except Exception as e:
            print(f"   ❌ {url}: Erro - {e}")
    
    print(f"\n📊 RESUMO:")
    print(f"   Rotas funcionando: {len(rotas_funcionando)}")
    
    if url_alocacoes_encontrada:
        print(f"\n🎉 PÁGINA DE ALOCAÇÕES ENCONTRADA!")
        print(f"✅ URL correta: {url_alocacoes_encontrada}")
        return url_alocacoes_encontrada
    else:
        print(f"\n❌ PÁGINA DE ALOCAÇÕES AINDA NÃO ENCONTRADA")
        return None

if __name__ == "__main__":
    url_alocacoes = testar_rotas_com_hifen()
    
    if url_alocacoes:
        print(f"\n🎯 PRÓXIMO PASSO:")
        print(f"   Corrigir a consulta SQL na rota de alocações")
        print(f"   URL funcionando: {url_alocacoes}")
    else:
        print(f"\n💡 INVESTIGAR:")
        print(f"   Verificar se o blueprint está sendo registrado corretamente")
        print(f"   Verificar logs do servidor para erros")
