-- =====================================================
-- ATUALIZAÇÃO: JORNADA PADRÃO PARA EMPRESAS
-- =====================================================
-- Adiciona campos para configuração de jornada padrão diretamente na empresa
-- Data: 04/07/2025
-- Autor: AiNexus Tecnologia

-- 1. Adicionar campos de jornada padrão na tabela empresas_config (ignorar erros se já existem)
ALTER TABLE empresas_config
ADD COLUMN jornada_segunda_entrada TIME DEFAULT '08:00:00' COMMENT 'Entrada segunda-feira',
ADD COLUMN jornada_segunda_saida_almoco TIME DEFAULT '12:00:00' COMMENT 'Saída almoço segunda-feira',
ADD COLUMN jornada_segunda_entrada_almoco TIME DEFAULT '13:00:00' COMMENT 'Entrada almoço segunda-feira',
ADD COLUMN jornada_segunda_saida TIME DEFAULT '17:00:00' COMMENT 'Saída segunda-feira',

ADD COLUMN jornada_sexta_entrada TIME DEFAULT '08:00:00' COMMENT 'Entrada sexta-feira',
ADD COLUMN jornada_sexta_saida_almoco TIME DEFAULT '12:00:00' COMMENT 'Saída almoço sexta-feira',
ADD COLUMN jornada_sexta_entrada_almoco TIME DEFAULT '13:00:00' COMMENT 'Entrada almoço sexta-feira',
ADD COLUMN jornada_sexta_saida TIME DEFAULT '16:30:00' COMMENT 'Saída sexta-feira',

ADD COLUMN intervalo_obrigatorio BOOLEAN DEFAULT TRUE COMMENT 'Se o intervalo de almoço é obrigatório',
ADD COLUMN tolerancia_empresa_minutos INT DEFAULT 15 COMMENT 'Tolerância da empresa em minutos';

-- 2. Atualizar registros existentes com valores padrão
UPDATE empresas_config SET
    jornada_segunda_entrada = '08:00:00',
    jornada_segunda_saida_almoco = '12:00:00',
    jornada_segunda_entrada_almoco = '13:00:00',
    jornada_segunda_saida = '17:00:00',
    jornada_sexta_entrada = '08:00:00',
    jornada_sexta_saida_almoco = '12:00:00',
    jornada_sexta_entrada_almoco = '13:00:00',
    jornada_sexta_saida = '16:30:00',
    intervalo_obrigatorio = TRUE,
    tolerancia_empresa_minutos = 15
WHERE jornada_segunda_entrada IS NULL;

-- 3. Inserir configurações padrão para empresas que não têm configuração
INSERT INTO empresas_config (
    empresa_id, 
    jornada_segunda_entrada, jornada_segunda_saida_almoco, jornada_segunda_entrada_almoco, jornada_segunda_saida,
    jornada_sexta_entrada, jornada_sexta_saida_almoco, jornada_sexta_entrada_almoco, jornada_sexta_saida,
    intervalo_obrigatorio, tolerancia_empresa_minutos
)
SELECT 
    e.id,
    '08:00:00', '12:00:00', '13:00:00', '17:00:00',
    '08:00:00', '12:00:00', '13:00:00', '16:30:00',
    TRUE, 15
FROM empresas e
WHERE e.ativa = 1 
AND NOT EXISTS (SELECT 1 FROM empresas_config ec WHERE ec.empresa_id = e.id)
ON DUPLICATE KEY UPDATE 
    data_atualizacao = CURRENT_TIMESTAMP;

-- 4. Verificar estrutura atualizada
SELECT 
    'Campos de jornada padrão adicionados com sucesso!' as status,
    COUNT(*) as total_configuracoes
FROM empresas_config;

-- 5. Mostrar configurações atuais
SELECT 
    e.razao_social,
    ec.jornada_segunda_entrada,
    ec.jornada_segunda_saida,
    ec.jornada_sexta_entrada,
    ec.jornada_sexta_saida,
    ec.tolerancia_empresa_minutos,
    ec.intervalo_obrigatorio
FROM empresas e
INNER JOIN empresas_config ec ON e.id = ec.empresa_id
WHERE e.ativa = 1
ORDER BY e.razao_social
LIMIT 5;
