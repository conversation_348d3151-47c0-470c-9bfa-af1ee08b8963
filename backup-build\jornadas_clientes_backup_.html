{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON> por Cliente - {{ empresa_principal.razao_social }}{% endblock %}

{% block extra_css %}
<style>
    .cliente-jornada-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        background: white;
    }
    
    .cliente-jornada-card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 15px rgba(0,123,255,0.1);
        transform: translateY(-2px);
    }
    
    .cliente-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .jornada-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        border-left: 4px solid #28a745;
    }
    
    .jornada-padrao {
        border-left-color: #007bff;
        background: #e3f2fd;
    }
    
    .horario-badge {
        background: #e9ecef;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        margin: 2px;
        display: inline-block;
    }
    
    .stats-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .stats-box {
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        margin: 0 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header com Estatísticas -->
    <div class="stats-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-clock"></i> Jornadas por Cliente</h2>
                <p class="mb-0">Gestão de horários de trabalho por empresa cliente</p>
            </div>
            <div class="col-md-4">
                <div class="row">
                    <div class="col-4">
                        <div class="stats-box">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.total_clientes }}</div>
                            <small>Clientes</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-box">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.total_jornadas }}</div>
                            <small>Jornadas</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-box">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.funcionarios_afetados }}</div>
                            <small>Funcionários</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ações e Navegação -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h4><i class="fas fa-list"></i> Jornadas Configuradas</h4>
        </div>
        <div class="col-md-6 text-right">
            <button class="btn btn-success" onclick="mostrarModalNovaJornada()">
                <i class="fas fa-plus"></i> Nova Jornada
            </button>
            <button class="btn btn-info" onclick="aplicarJornadasAutomaticamente()">
                <i class="fas fa-sync"></i> Aplicar Automaticamente
            </button>
            <a href="{{ url_for('empresa_principal.alocacoes') }}" class="btn btn-warning">
                <i class="fas fa-users-cog"></i> Alocações
            </a>
            <a href="{{ url_for('empresa_principal.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Dashboard
            </a>
        </div>
    </div>

    <!-- Lista de Clientes e suas Jornadas -->
    <div id="listaClientesJornadas">
        {% if clientes_jornadas %}
            {% for cliente in clientes_jornadas %}
            <div class="cliente-jornada-card">
                <!-- Header do Cliente -->
                <div class="cliente-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5><i class="fas fa-building"></i> {{ cliente.razao_social }}</h5>
                            {% if cliente.nome_fantasia %}
                                <p class="mb-0">{{ cliente.nome_fantasia }}</p>
                            {% endif %}
                        </div>
                        <div class="col-md-4 text-right">
                            <span class="badge badge-light">{{ cliente.funcionarios_alocados }} funcionários</span>
                            <span class="badge badge-light">{{ cliente.jornadas|length }} jornadas</span>
                        </div>
                    </div>
                </div>

                <!-- Jornadas do Cliente -->
                <div class="row">
                    <div class="col-md-8">
                        {% if cliente.jornadas %}
                            {% for jornada in cliente.jornadas %}
                            <div class="jornada-item {{ 'jornada-padrao' if jornada.padrao else '' }}">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>
                                            {{ jornada.nome_jornada }}
                                            {% if jornada.padrao %}
                                                <span class="badge badge-primary">Padrão</span>
                                            {% endif %}
                                        </h6>
                                        <p class="mb-1"><strong>Tipo:</strong> {{ jornada.tipo_jornada }}</p>
                                        {% if jornada.categoria_funcionario %}
                                            <p class="mb-1"><strong>Categoria:</strong> {{ jornada.categoria_funcionario }}</p>
                                        {% endif %}
                                        <p class="mb-0"><strong>Funcionários usando:</strong> {{ jornada.funcionarios_usando }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>Horários:</strong><br>
                                            <span class="horario-badge">Seg-Qui: {{ jornada.seg_qui_entrada }} - {{ jornada.seg_qui_saida }}</span>
                                            {% if jornada.sexta_entrada %}
                                                <span class="horario-badge">Sex: {{ jornada.sexta_entrada }} - {{ jornada.sexta_saida }}</span>
                                            {% endif %}
                                            {% if jornada.sabado_entrada %}
                                                <span class="horario-badge">Sáb: {{ jornada.sabado_entrada }} - {{ jornada.sabado_saida }}</span>
                                            {% endif %}
                                        </div>
                                        {% if jornada.intervalo_inicio %}
                                            <div class="mb-2">
                                                <strong>Intervalo:</strong> {{ jornada.intervalo_inicio }} - {{ jornada.intervalo_fim }}
                                            </div>
                                        {% endif %}
                                        <div>
                                            <button class="btn btn-sm btn-primary" onclick="editarJornada({{ jornada.id }})">
                                                <i class="fas fa-edit"></i> Editar
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="aplicarJornada({{ cliente.empresa_cliente_id }}, {{ jornada.id }})">
                                                <i class="fas fa-sync"></i> Aplicar
                                            </button>
                                            {% if not jornada.padrao %}
                                                <button class="btn btn-sm btn-warning" onclick="definirPadrao({{ jornada.id }})">
                                                    <i class="fas fa-star"></i> Padrão
                                                </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> Nenhuma jornada específica configurada para este cliente.
                                <br>Os funcionários usarão as jornadas padrão da empresa principal.
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Ações do Cliente -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-cogs"></i> Ações</h6>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-success btn-sm w-100 mb-2" onclick="criarJornadaCliente({{ cliente.empresa_cliente_id }})">
                                    <i class="fas fa-plus"></i> Nova Jornada
                                </button>
                                <button class="btn btn-info btn-sm w-100 mb-2" onclick="copiarJornada({{ cliente.empresa_cliente_id }})">
                                    <i class="fas fa-copy"></i> Copiar Jornada
                                </button>
                                <button class="btn btn-warning btn-sm w-100 mb-2" onclick="verFuncionariosCliente({{ cliente.empresa_cliente_id }})">
                                    <i class="fas fa-users"></i> Ver Funcionários
                                </button>
                                <button class="btn btn-primary btn-sm w-100" onclick="aplicarJornadaPadrao({{ cliente.empresa_cliente_id }})">
                                    <i class="fas fa-sync"></i> Aplicar Padrão
                                </button>
                            </div>
                        </div>
                        
                        <!-- Resumo de Funcionários -->
                        {% if cliente.funcionarios_resumo %}
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6><i class="fas fa-users"></i> Funcionários Alocados</h6>
                            </div>
                            <div class="card-body">
                                {% for func in cliente.funcionarios_resumo %}
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small>{{ func.nome }}</small>
                                    <span class="badge badge-{{ 'success' if func.jornada_aplicada else 'warning' }}">
                                        {{ func.jornada_nome if func.jornada_aplicada else 'Sem jornada' }}
                                    </span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">Nenhum cliente com jornadas configuradas</h4>
                <p class="text-muted">Configure jornadas específicas para seus clientes para melhor controle de horários.</p>
                <button class="btn btn-primary btn-lg" onclick="mostrarModalNovaJornada()">
                    <i class="fas fa-plus"></i> Configurar Primeira Jornada
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal Nova Jornada -->
<div class="modal fade" id="modalNovaJornada" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Nova Jornada para Cliente</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="formNovaJornada" onsubmit="criarJornada(event)">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Cliente *</label>
                                <select class="form-control" name="empresa_cliente_id" required>
                                    <option value="">Selecione um cliente...</option>
                                    {% for cliente in clientes_disponiveis %}
                                        <option value="{{ cliente.empresa_cliente_id }}">{{ cliente.razao_social }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nome da Jornada *</label>
                                <input type="text" class="form-control" name="nome_jornada" required placeholder="Ex: Horário Comercial Cliente">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Tipo de Jornada</label>
                                <select class="form-control" name="tipo_jornada">
                                    <option value="Diurno">Diurno</option>
                                    <option value="Noturno">Noturno</option>
                                    <option value="Misto">Misto</option>
                                    <option value="Especial">Especial</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Categoria de Funcionário</label>
                                <input type="text" class="form-control" name="categoria_funcionario" placeholder="Ex: Profissionais, Serventes">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Tolerância (minutos)</label>
                                <input type="number" class="form-control" name="tolerancia_entrada_minutos" value="15" min="0" max="60">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Horários Segunda a Quinta -->
                    <h6><i class="fas fa-calendar-week"></i> Horários Segunda a Quinta</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Entrada *</label>
                                <input type="time" class="form-control" name="seg_qui_entrada" value="08:00" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Saída *</label>
                                <input type="time" class="form-control" name="seg_qui_saida" value="17:00" required>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Horários Sexta -->
                    <h6><i class="fas fa-calendar-day"></i> Horários Sexta-feira (opcional)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Entrada</label>
                                <input type="time" class="form-control" name="sexta_entrada">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Saída</label>
                                <input type="time" class="form-control" name="sexta_saida">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Horários Sábado -->
                    <h6><i class="fas fa-calendar-day"></i> Horários Sábado (opcional)</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Entrada</label>
                                <input type="time" class="form-control" name="sabado_entrada">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Saída</label>
                                <input type="time" class="form-control" name="sabado_saida">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Intervalo -->
                    <h6><i class="fas fa-coffee"></i> Intervalo</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Início do Intervalo</label>
                                <input type="time" class="form-control" name="intervalo_inicio" value="12:00">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Fim do Intervalo</label>
                                <input type="time" class="form-control" name="intervalo_fim" value="13:00">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="padrao" id="jornadaPadrao">
                                    <label class="form-check-label" for="jornadaPadrao">
                                        Definir como jornada padrão para este cliente
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="aplicar_automaticamente" id="aplicarAuto" checked>
                                    <label class="form-check-label" for="aplicarAuto">
                                        Aplicar automaticamente aos funcionários alocados
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Descrição</label>
                        <textarea class="form-control" name="descricao" rows="2" placeholder="Descrição detalhada da jornada..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Criar Jornada
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function mostrarModalNovaJornada() {
    $('#modalNovaJornada').modal('show');
}

function criarJornadaCliente(clienteId) {
    document.querySelector('#modalNovaJornada select[name="empresa_cliente_id"]').value = clienteId;
    $('#modalNovaJornada').modal('show');
}

function criarJornada(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    
    fetch('{{ url_for("empresa_principal.criar_jornada_cliente") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#modalNovaJornada').modal('hide');
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao criar jornada');
    });
}

function aplicarJornada(clienteId, jornadaId) {
    if (confirm('Aplicar esta jornada a todos os funcionários alocados neste cliente?')) {
        fetch('{{ url_for("empresa_principal.aplicar_jornada_cliente") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                cliente_id: clienteId,
                jornada_id: jornadaId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Jornada aplicada com sucesso!');
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao aplicar jornada');
        });
    }
}

function aplicarJornadasAutomaticamente() {
    if (confirm('Aplicar automaticamente as jornadas padrão de cada cliente aos seus funcionários?')) {
        fetch('{{ url_for("empresa_principal.aplicar_jornadas_automaticamente") }}', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Jornadas aplicadas automaticamente! ${data.funcionarios_afetados} funcionários atualizados.`);
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao aplicar jornadas automaticamente');
        });
    }
}

function editarJornada(jornadaId) {
    alert('Editar jornada ' + jornadaId + ' - será implementado');
}

function definirPadrao(jornadaId) {
    if (confirm('Definir esta jornada como padrão para o cliente?')) {
        fetch('{{ url_for("empresa_principal.definir_jornada_padrao") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jornada_id: jornadaId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao definir jornada padrão');
        });
    }
}

function copiarJornada(clienteId) {
    alert('Copiar jornada para cliente ' + clienteId + ' - será implementado');
}

function verFuncionariosCliente(clienteId) {
    window.open('{{ url_for("empresa_principal.alocacoes") }}?cliente=' + clienteId, '_blank');
}

function aplicarJornadaPadrao(clienteId) {
    if (confirm('Aplicar a jornada padrão deste cliente a todos os funcionários alocados?')) {
        aplicarJornada(clienteId, 'padrao');
    }
}

// Animações
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.cliente-jornada-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
