def excluir_empresa(empresa_id):
    """
    Exclui uma empresa (soft delete). 
    Suporta tanto requisições AJAX (JSON) quanto formulários tradicionais.
    """
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Primeiro verificar se a empresa existe
        cursor.execute('SELECT id, razao_social, ativa FROM empresas WHERE id = %s', (empresa_id,))
        empresa_existe = cursor.fetchone()
        
        if not empresa_existe:
            message = 'Empresa não encontrada'
            logger.warning(f"[DEBUG EXCLUSÃO] Tentativa de excluir empresa inexistente: {empresa_id}")
            
            if is_ajax:
                conn.close()
                return jsonify({
                    'success': False,
                    'error': message
                }), 404
            else:
                flash(message, 'error')
                conn.close()
                return redirect(url_for('configuracoes.listar_empresas'))
        
        # Correção: Usar formato de dicionário para acessar dados
        if isinstance(empresa_existe, dict):
            razao_social = empresa_existe.get('razao_social', 'Desconhecida')
            ativa = empresa_existe.get('ativa', False)
        else:
            # Fallback para formato de tupla (caso necessário)
            razao_social = empresa_existe[1] if len(empresa_existe) > 1 else 'Desconhecida'
            ativa = empresa_existe[2] if len(empresa_existe) > 2 else False
            
        logger.info(f"[DEBUG EXCLUSÃO] Empresa encontrada - ID: {empresa_id}, Razão: {razao_social}, Ativa: {ativa}")
        
        # Verificar se a empresa tem funcionários ativos com debug
        cursor.execute("""
            SELECT COUNT(*) as total FROM funcionarios
            WHERE empresa_id = %s AND ativo = TRUE
        """, (empresa_id,))
        
        resultado_consulta = cursor.fetchone()
        
        # CORREÇÃO: O cursor retorna dicionário, não tupla
        if isinstance(resultado_consulta, dict):
            funcionarios_ativos = resultado_consulta.get('total', 0)
        else:
            funcionarios_ativos = resultado_consulta[0] if resultado_consulta else 0
        
        # Debug log detalhado para investigar problema
        logger.info(f"[DEBUG EXCLUSÃO] Empresa ID: {empresa_id}")
        logger.info(f"[DEBUG EXCLUSÃO] Resultado consulta: {resultado_consulta}")
        logger.info(f"[DEBUG EXCLUSÃO] Funcionários ativos: {funcionarios_ativos}")
        logger.info(f"[DEBUG EXCLUSÃO] Tipo do resultado: {type(funcionarios_ativos)}")
        
        if funcionarios_ativos > 0:
            message = f'Não é possível excluir a empresa. Há {funcionarios_ativos} funcionário(s) ativo(s) vinculado(s).'
            
            if is_ajax:
                conn.close()
                return jsonify({
                    'success': False,
                    'error': message
                }), 400
            else:
                flash(message, 'error')
                conn.close()
                return redirect(url_for('configuracoes.listar_empresas'))
        
        # Verificar e remover relações dependentes
        try:
            # Verificar jornadas de trabalho
            cursor.execute("SELECT COUNT(*) as total FROM jornadas_trabalho WHERE empresa_id = %s", (empresa_id,))
            jornadas = cursor.fetchone()
            jornadas_count = jornadas.get('total', 0) if isinstance(jornadas, dict) else (jornadas[0] if jornadas else 0)
            
            if jornadas_count > 0:
                logger.info(f"[DEBUG EXCLUSÃO] Removendo {jornadas_count} jornadas de trabalho da empresa {empresa_id}")
                cursor.execute("DELETE FROM jornadas_trabalho WHERE empresa_id = %s", (empresa_id,))
            
            # Verificar horários de trabalho
            cursor.execute("SELECT COUNT(*) as total FROM horarios_trabalho WHERE empresa_id = %s", (empresa_id,))
            horarios = cursor.fetchone()
            horarios_count = horarios.get('total', 0) if isinstance(horarios, dict) else (horarios[0] if horarios else 0)
            
            if horarios_count > 0:
                logger.info(f"[DEBUG EXCLUSÃO] Removendo {horarios_count} horários de trabalho da empresa {empresa_id}")
                cursor.execute("DELETE FROM horarios_trabalho WHERE empresa_id = %s", (empresa_id,))
            
            # Verificar clientes
            cursor.execute("SELECT COUNT(*) as total FROM clientes WHERE empresa_id = %s", (empresa_id,))
            clientes = cursor.fetchone()
            clientes_count = clientes.get('total', 0) if isinstance(clientes, dict) else (clientes[0] if clientes else 0)
            
            if clientes_count > 0:
                logger.info(f"[DEBUG EXCLUSÃO] Removendo {clientes_count} clientes da empresa {empresa_id}")
                cursor.execute("DELETE FROM clientes WHERE empresa_id = %s", (empresa_id,))
        
        except Exception as e:
            logger.error(f"[DEBUG EXCLUSÃO] Erro ao remover relações da empresa {empresa_id}: {str(e)}")
            conn.rollback()
            
            if is_ajax:
                return jsonify({
                    'success': False,
                    'error': f'Erro ao remover relações: {str(e)}'
                }), 500
            else:
                flash(f'Erro ao remover relações: {str(e)}', 'error')
                conn.close()
                return redirect(url_for('configuracoes.listar_empresas'))
        
        # Soft delete - marcar como inativa
        try:
            cursor.execute("""
                UPDATE empresas SET ativa = FALSE
                WHERE id = %s
            """, (empresa_id,))
            
            conn.commit()
        except Exception as e:
            logger.error(f"[DEBUG EXCLUSÃO] Erro ao atualizar status da empresa {empresa_id}: {str(e)}")
            conn.rollback()
            
            if is_ajax:
                return jsonify({
                    'success': False,
                    'error': f'Erro ao atualizar status: {str(e)}'
                }), 500
            else:
                flash(f'Erro ao atualizar status: {str(e)}', 'error')
                conn.close()
                return redirect(url_for('configuracoes.listar_empresas'))
        
        conn.close()
        
        logger.info(f"Empresa excluída (soft delete) - ID: {empresa_id}, Razão Social: {razao_social}")
        message = 'Empresa excluída com sucesso!'
        
        if is_ajax:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            flash(message, 'success')
            return redirect(url_for('configuracoes.listar_empresas'))
        
    except Exception as e:
        logger.error(f"Erro ao excluir empresa {empresa_id}: {str(e)}")
        message = f'Erro ao excluir empresa: {str(e)}'
        
        if is_ajax:
            return jsonify({
                'success': False,
                'error': message
            }), 500
        else:
            flash(message, 'error')
            return redirect(url_for('configuracoes.listar_empresas')) 