@echo off
echo Configurando SSH sem senha para 10.19.208.31...
echo.

REM Criar diretorio .ssh se nao existir
if not exist "%USERPROFILE%\.ssh" mkdir "%USERPROFILE%\.ssh"

REM Gerar chave SSH se nao existir
if not exist "%USERPROFILE%\.ssh\id_rsa" (
    echo Gerando chave SSH...
    ssh-keygen -t rsa -b 2048 -f "%USERPROFILE%\.ssh\id_rsa" -N ""
)

echo Copiando chave para servidor...
echo Digite a senha: @Ric6109
type "%USERPROFILE%\.ssh\id_rsa.pub" | ssh root@10.19.208.31 "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys && chmod 600 ~/.ssh/authorized_keys && chmod 700 ~/.ssh"

echo.
echo Testando conexao...
ssh root@10.19.208.31 "echo 'SSH sem senha configurado!'"

echo.
echo Pronto! Agora execute: deploy_agora.bat
pause
