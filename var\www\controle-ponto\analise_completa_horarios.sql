-- ========================================
-- ANÁLISE COMPLETA - SISTEMA DE HORÁRIOS
-- Data: 11/07/2025
-- Objetivo: Identificar inconsistências na configuração de horários
-- ========================================

-- 1. VERIFICAR ESTRUTURA DA TABELA FUNCIONARIOS
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'funcionarios' 
AND TABLE_SCHEMA = 'controle_ponto'
AND COLUMN_NAME LIKE '%jornada%' OR COLUMN_NAME LIKE '%tolerancia%'
ORDER BY ORDINAL_POSITION;

-- 2. VERIFICAR DADOS DO TESTE 5
SELECT 
    id,
    nome_completo,
    empresa_id,
    horario_trabalho_id,
    jornada_seg_qui_entrada,
    jornada_seg_qui_saida,
    jornada_sex_entrada,
    jornada_sex_saida,
    jornada_intervalo_entrada,
    jornada_intervalo_saida,
    tolerancia_ponto,
    status_cadastro
FROM funcionarios 
WHERE nome_completo LIKE '%TESTE 5%';

-- 3. VERIFICAR TODOS OS FUNCIONÁRIOS COM HORÁRIOS CONFIGURADOS
SELECT 
    COUNT(*) as total_funcionarios,
    COUNT(CASE WHEN jornada_seg_qui_entrada IS NOT NULL THEN 1 END) as com_horario_seg_qui,
    COUNT(CASE WHEN jornada_sex_entrada IS NOT NULL THEN 1 END) as com_horario_sex,
    COUNT(CASE WHEN horario_trabalho_id IS NOT NULL THEN 1 END) as com_horario_trabalho_id
FROM funcionarios 
WHERE status_cadastro = 'Ativo';

-- 4. VERIFICAR TABELA HORARIOS_TRABALHO
SELECT 
    ht.id,
    ht.empresa_id,
    ht.nome_horario,
    ht.entrada_manha,
    ht.saida_almoco,
    ht.entrada_tarde,
    ht.saida,
    ht.tolerancia_minutos,
    ht.ativo,
    e.razao_social as empresa_nome
FROM horarios_trabalho ht
LEFT JOIN empresas e ON ht.empresa_id = e.id
WHERE ht.ativo = 1;

-- 5. VERIFICAR FUNCIONÁRIOS SEM HORÁRIO CONFIGURADO
SELECT 
    f.id,
    f.nome_completo,
    f.empresa_id,
    e.razao_social as empresa_nome,
    f.horario_trabalho_id,
    f.jornada_seg_qui_entrada,
    f.status_cadastro
FROM funcionarios f
LEFT JOIN empresas e ON f.empresa_id = e.id
WHERE f.status_cadastro = 'Ativo'
AND (f.jornada_seg_qui_entrada IS NULL OR f.jornada_seg_qui_entrada = '')
AND (f.horario_trabalho_id IS NULL);

-- 6. VERIFICAR REGISTROS PROBLEMÁTICOS (FORA DE SEQUÊNCIA)
SELECT 
    f.nome_completo,
    DATE(rp.data_hora) as data_registro,
    GROUP_CONCAT(
        CONCAT(TIME(rp.data_hora), ':', rp.tipo_registro) 
        ORDER BY rp.data_hora SEPARATOR ' | '
    ) as sequencia_batidas,
    COUNT(*) as total_batidas
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
WHERE DATE(rp.data_hora) >= '2025-07-10'
GROUP BY f.nome_completo, DATE(rp.data_hora)
HAVING COUNT(*) > 0
ORDER BY data_registro DESC, f.nome_completo;

-- 7. VERIFICAR REGISTROS MUITO FORA DO HORÁRIO
SELECT 
    f.nome_completo,
    rp.data_hora,
    rp.tipo_registro,
    TIME(rp.data_hora) as hora_registro,
    f.jornada_seg_qui_entrada,
    f.jornada_seg_qui_saida,
    CASE 
        WHEN TIME(rp.data_hora) < '06:00:00' OR TIME(rp.data_hora) > '22:00:00' 
        THEN 'HORÁRIO SUSPEITO'
        ELSE 'OK'
    END as status_horario
FROM registros_ponto rp
INNER JOIN funcionarios f ON rp.funcionario_id = f.id
WHERE DATE(rp.data_hora) >= '2025-07-10'
AND (TIME(rp.data_hora) < '06:00:00' OR TIME(rp.data_hora) > '22:00:00')
ORDER BY rp.data_hora DESC;
