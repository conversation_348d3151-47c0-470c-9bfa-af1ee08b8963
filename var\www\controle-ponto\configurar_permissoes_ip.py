#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para configurar permissões MySQL para o IP atual
e adicionar campos de biometria à tabela funcionários.
"""

import pymysql
import socket
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_current_ip():
    """Obtém o IP atual da máquina"""
    try:
        # Conecta a um endereço externo para descobrir o IP local usado
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("************", 3306))  # IP do servidor MySQL
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "localhost"

def main():
    print("=" * 60)
    print("🔧 CONFIGURAÇÃO BIOMETRIA + PERMISSÕES MYSQL")
    print("=" * 60)
    
    # Obter IP atual
    ip_atual = get_current_ip()
    print(f"🌐 IP atual detectado: {ip_atual}")
    
    # Tentar conectar com root (sem senha)
    try:
        print("🔌 Conectando ao MySQL como root...")
        connection = pymysql.connect(
            host='************',
            user='root',
            password='',  # MySQL geralmente vem sem senha para root
            database='controle_ponto',
            charset='utf8mb4'
        )
        cursor = connection.cursor()
        print("✅ Conectado com sucesso!")
        
        # Configurar permissões para o IP atual
        print(f"🔐 Configurando permissões para IP {ip_atual}...")
        
        # Criar usuários
        usuarios_sql = [
            f"CREATE USER IF NOT EXISTS 'controle_user'@'{ip_atual}' IDENTIFIED BY 'controle123';",
            f"CREATE USER IF NOT EXISTS 'controle_user'@'10.19.208.%' IDENTIFIED BY 'controle123';",
            f"CREATE USER IF NOT EXISTS 'controle_user'@'localhost' IDENTIFIED BY 'controle123';"
        ]
        
        for sql in usuarios_sql:
            try:
                cursor.execute(sql)
                print(f"✅ {sql}")
            except Exception as e:
                print(f"ℹ️ {sql} (já existe ou erro: {e})")
        
        # Conceder permissões
        permissoes_sql = [
            f"GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'{ip_atual}';",
            f"GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'10.19.208.%';",
            f"GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_user'@'localhost';"
        ]
        
        for sql in permissoes_sql:
            try:
                cursor.execute(sql)
                print(f"✅ {sql}")
            except Exception as e:
                print(f"⚠️ {sql} (erro: {e})")
        
        # Atualizar privilégios
        cursor.execute("FLUSH PRIVILEGES;")
        print("✅ Privilégios atualizados")
        
        # Adicionar campos de biometria
        print("🔬 Adicionando campos de biometria...")
        campos_sql = [
            "ALTER TABLE funcionarios ADD COLUMN biometria_template_1 LONGTEXT COMMENT 'Template biométrico do primeiro dedo';",
            "ALTER TABLE funcionarios ADD COLUMN biometria_template_2 LONGTEXT COMMENT 'Template biométrico do segundo dedo';",
            "ALTER TABLE funcionarios ADD COLUMN biometria_qualidade_1 INT DEFAULT 0 COMMENT 'Qualidade da captura do primeiro dedo';",
            "ALTER TABLE funcionarios ADD COLUMN biometria_qualidade_2 INT DEFAULT 0 COMMENT 'Qualidade da captura do segundo dedo';",
            "ALTER TABLE funcionarios ADD COLUMN biometria_data_cadastro TIMESTAMP NULL COMMENT 'Data de cadastro da biometria';"
        ]
        
        for sql in campos_sql:
            try:
                cursor.execute(sql)
                campo_nome = sql.split("ADD COLUMN ")[1].split(" ")[0]
                print(f"✅ Campo adicionado: {campo_nome}")
            except Exception as e:
                if "Duplicate column name" in str(e):
                    campo_nome = sql.split("ADD COLUMN ")[1].split(" ")[0]
                    print(f"ℹ️ Campo já existe: {campo_nome}")
                else:
                    print(f"❌ Erro ao adicionar campo: {e}")
        
        # Confirmar alterações
        connection.commit()
        
        # Verificar estrutura da tabela
        cursor.execute("DESCRIBE funcionarios;")
        colunas = cursor.fetchall()
        colunas_biometria = [col for col in colunas if 'biometria' in col[0]]
        
        print(f"\n📋 Colunas de biometria encontradas: {len(colunas_biometria)}")
        for col in colunas_biometria:
            print(f"   - {col[0]} ({col[1]})")
        
        connection.close()
        
        print("\n" + "=" * 60)
        print("🎉 CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!")
        print("=" * 60)
        print(f"✅ IP configurado: {ip_atual}")
        print("✅ Campos de biometria adicionados")
        print("✅ Permissões atualizadas")
        
    except Exception as e:
        print(f"\n💥 Erro: {e}")
        print("\n🔧 Soluções:")
        print("1. Verifique se o MySQL está rodando no servidor")
        print("2. Configure manualmente as permissões")
        print("3. Use SSH para acessar o servidor: ssh root@************")

if __name__ == "__main__":
    main() 