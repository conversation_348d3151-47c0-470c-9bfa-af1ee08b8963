#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para Diagnosticar e Corrigir Permissões do Diretório de Fotos
Sistema: RLPONTO-WEB v1.0
Data: 08/06/2025
Autor: <PERSON> / AiNexus Tecnologia

Resolve o erro: [Errno 13] Permission denied: '/var/www/controle-ponto/static/fotos_funcionarios'
"""

import os
import stat
import subprocess
import sys
from pathlib import Path

# Configurações
PROJETO_PATH = "/var/www/controle-ponto"
FOTOS_DIR = f"{PROJETO_PATH}/static/fotos_funcionarios"
APACHE_USER = "www-data"  # Usuario padrão do Apache
APACHE_GROUP = "www-data"  # Grupo padrão do Apache

def log_info(msg):
    """Log de informações"""
    print(f"ℹ️  {msg}")

def log_success(msg):
    """Log de sucesso"""
    print(f"✅ {msg}")

def log_error(msg):
    """Log de erro"""
    print(f"❌ {msg}")

def log_warning(msg):
    """Log de aviso"""
    print(f"⚠️  {msg}")

def verificar_usuario_atual():
    """Verifica se o script está sendo executado como root ou com sudo"""
    if os.geteuid() != 0:
        log_error("Execute como root: sudo python3 corrigir_permissoes_fotos.py")
        sys.exit(1)
    log_success("Executando como root/sudo")

def verificar_diretorio_projeto():
    """Verifica se o diretório do projeto existe"""
    if not os.path.exists(PROJETO_PATH):
        log_error(f"Diretório do projeto não encontrado: {PROJETO_PATH}")
        return False
    log_success(f"Diretório do projeto encontrado: {PROJETO_PATH}")
    return True

def verificar_diretorio_static():
    """Verifica e cria o diretório static se necessário"""
    static_dir = f"{PROJETO_PATH}/static"
    
    if not os.path.exists(static_dir):
        log_warning(f"Diretório static não existe: {static_dir}")
        try:
            os.makedirs(static_dir, exist_ok=True)
            log_success(f"Diretório static criado: {static_dir}")
        except Exception as e:
            log_error(f"Erro ao criar diretório static: {e}")
            return False
    else:
        log_success(f"Diretório static existe: {static_dir}")
    return True

def verificar_diretorio_fotos():
    """Verifica e cria o diretório de fotos se necessário"""
    if not os.path.exists(FOTOS_DIR):
        log_warning(f"Diretório de fotos não existe: {FOTOS_DIR}")
        try:
            os.makedirs(FOTOS_DIR, exist_ok=True)
            log_success(f"Diretório de fotos criado: {FOTOS_DIR}")
        except Exception as e:
            log_error(f"Erro ao criar diretório de fotos: {e}")
            return False
    else:
        log_success(f"Diretório de fotos existe: {FOTOS_DIR}")
    return True

def verificar_permissoes_atuais():
    """Verifica as permissões atuais dos diretórios"""
    log_info("=== Verificando permissões atuais ===")
    
    diretorios = [PROJETO_PATH, f"{PROJETO_PATH}/static", FOTOS_DIR]
    
    for diretorio in diretorios:
        if os.path.exists(diretorio):
            stat_info = os.stat(diretorio)
            permissions = oct(stat_info.st_mode)[-3:]
            owner = stat_info.st_uid
            group = stat_info.st_gid
            
            log_info(f"📁 {diretorio}")
            log_info(f"   Permissões: {permissions}")
            log_info(f"   Proprietário UID: {owner}")
            log_info(f"   Grupo GID: {group}")
        else:
            log_warning(f"📁 {diretorio} - NÃO EXISTE")

def verificar_usuario_apache():
    """Verifica se o usuário Apache existe"""
    try:
        import pwd
        pwd.getpwnam(APACHE_USER)
        log_success(f"Usuário Apache encontrado: {APACHE_USER}")
        return True
    except KeyError:
        log_error(f"Usuário Apache não encontrado: {APACHE_USER}")
        # Tentar alternativas comuns
        usuarios_apache = ["apache", "httpd", "nginx"]
        for user in usuarios_apache:
            try:
                pwd.getpwnam(user)
                global APACHE_USER, APACHE_GROUP
                APACHE_USER = user
                APACHE_GROUP = user
                log_success(f"Usuário web server encontrado: {user}")
                return True
            except KeyError:
                continue
        return False

def corrigir_proprietario():
    """Corrige o proprietário dos diretórios"""
    log_info("=== Corrigindo proprietário dos diretórios ===")
    
    diretorios = [f"{PROJETO_PATH}/static", FOTOS_DIR]
    
    for diretorio in diretorios:
        if os.path.exists(diretorio):
            try:
                # Alterar proprietário para o usuário do Apache
                subprocess.run([
                    "chown", "-R", f"{APACHE_USER}:{APACHE_GROUP}", diretorio
                ], check=True)
                log_success(f"Proprietário alterado: {diretorio} -> {APACHE_USER}:{APACHE_GROUP}")
            except subprocess.CalledProcessError as e:
                log_error(f"Erro ao alterar proprietário de {diretorio}: {e}")
        else:
            log_warning(f"Diretório não existe: {diretorio}")

def corrigir_permissoes():
    """Corrige as permissões dos diretórios"""
    log_info("=== Corrigindo permissões dos diretórios ===")
    
    try:
        subprocess.run(["chown", "-R", f"{APACHE_USER}:{APACHE_GROUP}", f"{PROJETO_PATH}/static"], check=True)
        log_success("Proprietário alterado para www-data")
        
        subprocess.run(["chmod", "-R", "755", f"{PROJETO_PATH}/static"], check=True)
        log_success("Permissões alteradas para 755")
        
        return True
    except Exception as e:
        log_error(f"Erro ao corrigir permissões: {e}")
        return False

def testar_escrita():
    """Testa a escrita no diretório de fotos"""
    log_info("=== Testando escrita no diretório de fotos ===")
    
    arquivo_teste = f"{FOTOS_DIR}/teste.txt"
    
    try:
        # Criar arquivo de teste
        with open(arquivo_teste, 'w') as f:
            f.write("teste")
        
        log_success("✅ Escrita no diretório funcionando corretamente!")
        
        # Remover arquivo de teste
        os.remove(arquivo_teste)
        log_success("Arquivo de teste removido")
        
        return True
        
    except Exception as e:
        log_error(f"Erro ao testar escrita: {e}")
        return False

def verificar_selinux():
    """Verifica se SELinux está interferindo"""
    log_info("=== Verificando SELinux ===")
    
    try:
        # Verificar status do SELinux
        result = subprocess.run(["getenforce"], capture_output=True, text=True)
        if result.returncode == 0:
            status = result.stdout.strip()
            if status == "Enforcing":
                log_warning(f"SELinux está ativo (Enforcing) - pode estar bloqueando")
                log_info("Considere executar: setsebool -P httpd_unified 1")
                log_info("Ou definir contexto SELinux: semanage fcontext -a -t httpd_exec_t '/var/www/controle-ponto/static(/.*)?'")
            else:
                log_success(f"SELinux: {status}")
        else:
            log_info("SELinux não detectado ou não instalado")
    except FileNotFoundError:
        log_info("SELinux não instalado")

def main():
    """Função principal"""
    print("🔧 CORREÇÃO DE PERMISSÕES - FOTOS DOS FUNCIONÁRIOS")
    print("=" * 60)
    
    # Verificações iniciais
    verificar_usuario_atual()
    
    if not verificar_diretorio_projeto():
        sys.exit(1)
    
    if not verificar_usuario_apache():
        log_error("Não foi possível encontrar o usuário do servidor web")
        sys.exit(1)
    
    # Verificar situação atual
    verificar_permissoes_atuais()
    verificar_selinux()
    
    # Criar diretórios se necessário
    verificar_diretorio_static()
    verificar_diretorio_fotos()
    
    # Corrigir permissões
    corrigir_proprietario()
    corrigir_permissoes()
    
    # Testar
    if testar_escrita():
        print("\n" + "=" * 60)
        log_success("✅ PROBLEMA RESOLVIDO! O diretório de fotos está funcionando.")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        log_error("❌ PROBLEMA PERSISTE. Verifique logs do Apache/Nginx.")
        log_info("Comandos úteis para debug:")
        log_info("- tail -f /var/log/apache2/error.log")
        log_info("- tail -f /var/log/nginx/error.log")
        log_info("- ls -la /var/www/controle-ponto/static/")
        print("=" * 60)

if __name__ == "__main__":
    main() 