#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de Correção Imediata - Configurações Biométricas
======================================================

Aplica correções imediatas para problemas comuns:
- Múltiplos processos Flask
- Cache Python/Template
- Permissões de arquivo
- Template corrupto
- Blueprint não registrado

Desenvolvido por: Richardson Rodrigues - AiNexus Tecnologia
"""

import os
import sys
import shutil
import subprocess
import signal
from datetime import datetime
from pathlib import Path

def log_message(message, level="INFO"):
    """Log com timestamp"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] [{level}] {message}")

def kill_flask_processes():
    """Mata todos os processos Flask"""
    log_message("🔄 Matando processos Flask existentes...", "INFO")
    try:
        # Encontrar todos os processos Flask
        result = subprocess.run(['pgrep', '-f', 'python.*app.py'], 
                              capture_output=True, text=True)
        
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            log_message(f"   Encontrados {len(pids)} processos Flask", "INFO")
            
            for pid in pids:
                try:
                    pid_int = int(pid.strip())
                    os.kill(pid_int, signal.SIGTERM)
                    log_message(f"   Processo {pid_int} finalizado", "SUCCESS")
                except:
                    log_message(f"   Erro ao finalizar processo {pid}", "ERROR")
        else:
            log_message("   Nenhum processo Flask encontrado", "INFO")
            
    except Exception as e:
        log_message(f"   Erro: {e}", "ERROR")

def clear_python_cache():
    """Limpa cache Python"""
    log_message("🧹 Limpando cache Python...", "INFO")
    
    cache_patterns = [
        "/var/www/controle-ponto/__pycache__",
        "/var/www/controle-ponto/*/__pycache__",
        "/var/www/controle-ponto/**/__pycache__",
        "/tmp/flask_cache"
    ]
    
    for pattern in cache_patterns:
        try:
            if '*' in pattern:
                # Usar find para padrões complexos
                subprocess.run(['find', '/var/www/controle-ponto', '-name', '__pycache__', '-type', 'd', '-exec', 'rm', '-rf', '{}', '+'], 
                             capture_output=True)
            else:
                cache_path = Path(pattern)
                if cache_path.exists():
                    shutil.rmtree(cache_path)
                    log_message(f"   Removido: {cache_path}", "SUCCESS")
        except Exception as e:
            log_message(f"   Erro ao limpar {pattern}: {e}", "ERROR")

def fix_template_if_needed():
    """Verifica e corrige template se necessário"""
    log_message("🎨 Verificando template de configurações...", "INFO")
    
    template_path = Path("/var/www/controle-ponto/templates/configuracoes/index.html")
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        has_biometric = 'fas fa-fingerprint' in content and 'Biometria' in content
        
        if has_biometric:
            log_message("   ✅ Template já contém elementos biométricos", "SUCCESS")
            return True
        else:
            log_message("   ❌ Template NÃO contém elementos biométricos", "ERROR")
            return False
            
    except Exception as e:
        log_message(f"   Erro ao ler template: {e}", "ERROR")
        return False

def verify_app_py_config():
    """Verifica configuração do app.py"""
    log_message("⚙️ Verificando app.py...", "INFO")
    
    app_path = Path("/var/www/controle-ponto/app.py")
    
    try:
        with open(app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        has_import = 'app_biometric_config' in content
        has_register = 'biometric_config_bp' in content
        
        if has_import and has_register:
            log_message("   ✅ app.py configurado corretamente", "SUCCESS")
            return True
        else:
            log_message("   ❌ app.py PRECISA de correção", "ERROR")
            log_message(f"      Import: {has_import}, Register: {has_register}", "DEBUG")
            return False
            
    except Exception as e:
        log_message(f"   Erro ao ler app.py: {e}", "ERROR")
        return False

def restart_flask_service():
    """Reinicia serviço Flask"""
    log_message("🔄 Reiniciando Flask...", "INFO")
    
    try:
        # Primeiro, matar todos os processos
        kill_flask_processes()
        
        # Aguardar um pouco
        import time
        time.sleep(2)
        
        # Navegar para o diretório correto
        os.chdir('/var/www/controle-ponto')
        
        # Iniciar Flask em background
        log_message("   Iniciando novo processo Flask...", "INFO")
        
        # Usar nohup para garantir que o processo continue rodando
        subprocess.Popen([
            'nohup', 'python3', 'app.py'
        ], stdout=open('/var/www/controle-ponto/logs/flask.log', 'w'),
           stderr=subprocess.STDOUT,
           cwd='/var/www/controle-ponto')
        
        time.sleep(3)  # Aguardar inicialização
        
        # Verificar se o processo foi iniciado
        result = subprocess.run(['pgrep', '-f', 'python.*app.py'], 
                              capture_output=True, text=True)
        
        if result.stdout.strip():
            pid = result.stdout.strip().split('\n')[0]
            log_message(f"   ✅ Flask iniciado com sucesso (PID: {pid})", "SUCCESS")
            return True
        else:
            log_message("   ❌ Falha ao iniciar Flask", "ERROR")
            return False
            
    except Exception as e:
        log_message(f"   Erro: {e}", "ERROR")
        return False

def fix_file_permissions():
    """Corrige permissões de arquivos"""
    log_message("🔐 Verificando permissões...", "INFO")
    
    try:
        # Ajustar permissões do diretório principal
        subprocess.run(['chmod', '-R', '755', '/var/www/controle-ponto'], 
                      capture_output=True)
        
        # Ajustar proprietário se necessário (executar como root)
        subprocess.run(['chown', '-R', 'www-data:www-data', '/var/www/controle-ponto'], 
                      capture_output=True)
        
        log_message("   ✅ Permissões ajustadas", "SUCCESS")
        return True
        
    except Exception as e:
        log_message(f"   Erro: {e}", "ERROR")
        return False

def test_page_access():
    """Testa acesso à página de configurações"""
    log_message("🌐 Testando acesso à página...", "INFO")
    
    try:
        import requests
        import time
        
        # Aguardar Flask inicializar
        time.sleep(5)
        
        # Testar a página
        response = requests.get('http://localhost:5000/configuracoes/', timeout=10)
        
        if response.status_code == 200:
            if 'Biometria' in response.text:
                log_message("   ✅ Página carregou COM conteúdo biométrico!", "SUCCESS")
                return True
            else:
                log_message("   ⚠️ Página carregou mas SEM conteúdo biométrico", "WARNING")
                return False
        else:
            log_message(f"   ❌ Erro HTTP: {response.status_code}", "ERROR")
            return False
            
    except Exception as e:
        log_message(f"   Erro no teste: {e}", "ERROR")
        return False

def create_emergency_template():
    """Cria template de emergência se necessário"""
    log_message("🚨 Criando template de emergência...", "INFO")
    
    template_path = Path("/var/www/controle-ponto/templates/configuracoes/index.html")
    backup_path = Path("/var/www/controle-ponto/templates/configuracoes/index_backup.html")
    
    try:
        # Fazer backup do template atual
        if template_path.exists():
            shutil.copy2(template_path, backup_path)
            log_message("   Backup criado", "INFO")
        
        # Template de emergência com conteúdo biométrico
        emergency_template = '''{% extends "base.html" %}

{% block title %}Configurações do Sistema{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-cog me-2"></i>Configurações do Sistema</h4>
                    <p class="mb-0 opacity-75">Gerencie as configurações e funcionalidades do sistema</p>
                </div>
                
                <div class="card-body p-4">
                    <div class="row g-4">
                        <!-- Configurações de Biometria -->
                        <div class="col-md-6 col-lg-4">
                            <div class="config-card h-100 border border-success border-opacity-25 bg-success bg-opacity-5 rounded-3 p-4 text-center">
                                <div class="config-icon mb-3">
                                    <i class="fas fa-fingerprint text-success" style="font-size: 3rem;"></i>
                                </div>
                                <h5 class="fw-bold text-dark mb-2">Biometria</h5>
                                <p class="text-muted mb-3">Configure leitores biométricos, teste dispositivos e gerencie as configurações de autenticação biométrica</p>
                                <a href="/configuracoes/biometria" class="btn btn-success btn-sm">
                                    <i class="fas fa-cog me-1"></i>Configurar
                                </a>
                            </div>
                        </div>
                        
                        <!-- Backup -->
                        <div class="col-md-6 col-lg-4">
                            <div class="config-card h-100 border border-info border-opacity-25 bg-info bg-opacity-5 rounded-3 p-4 text-center">
                                <div class="config-icon mb-3">
                                    <i class="fas fa-database text-info" style="font-size: 3rem;"></i>
                                </div>
                                <h5 class="fw-bold text-dark mb-2">Backup</h5>
                                <p class="text-muted mb-3">Realize backup do banco de dados e gerencie a segurança dos dados do sistema</p>
                                <button id="btn-backup" class="btn btn-info btn-sm">
                                    <i class="fas fa-download me-1"></i>Fazer Backup
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('btn-backup').addEventListener('click', function() {
    alert('Funcionalidade de backup ativada!');
});
</script>
{% endblock %}'''
        
        # Escrever template de emergência
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(emergency_template)
        
        log_message("   ✅ Template de emergência criado", "SUCCESS")
        return True
        
    except Exception as e:
        log_message(f"   Erro: {e}", "ERROR")
        return False

def main():
    """Função principal de correção"""
    log_message("🚀 INICIANDO CORREÇÃO IMEDIATA...", "INFO")
    print("="*60)
    
    steps = [
        ("Matando processos Flask", kill_flask_processes),
        ("Limpando cache Python", clear_python_cache),
        ("Verificando template", fix_template_if_needed),
        ("Verificando app.py", verify_app_py_config),
        ("Corrigindo permissões", fix_file_permissions),
        ("Reiniciando Flask", restart_flask_service),
        ("Testando acesso", test_page_access)
    ]
    
    results = {}
    
    for step_name, step_func in steps:
        log_message(f"📍 ETAPA: {step_name}", "INFO")
        try:
            result = step_func()
            results[step_name] = result
            
            if result:
                log_message(f"   ✅ {step_name} - SUCESSO", "SUCCESS")
            else:
                log_message(f"   ❌ {step_name} - FALHOU", "ERROR")
                
                # Se template falhou, tentar template de emergência
                if "template" in step_name.lower():
                    log_message("   Tentando template de emergência...", "INFO")
                    emergency_result = create_emergency_template()
                    if emergency_result:
                        log_message("   ✅ Template de emergência criado", "SUCCESS")
                        # Reiniciar Flask após criar template
                        restart_flask_service()
                        
        except Exception as e:
            log_message(f"   ❌ ERRO CRÍTICO em {step_name}: {e}", "CRITICAL")
            results[step_name] = False
    
    # Relatório final
    print("\n" + "="*60)
    log_message("📊 RELATÓRIO FINAL DE CORREÇÕES", "INFO")
    print("="*60)
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for step, result in results.items():
        status = "✅ OK" if result else "❌ FALHOU"
        log_message(f"   {step}: {status}", "INFO")
    
    log_message(f"📊 RESULTADO: {success_count}/{total_count} etapas concluídas", "INFO")
    
    if success_count >= total_count - 1:  # Permitir 1 falha
        log_message("🎉 CORREÇÃO CONCLUÍDA COM SUCESSO!", "SUCCESS")
        log_message("   Acesse: http://10.19.208.31:5000/configuracoes/", "INFO")
        log_message("   Limpe o cache do navegador (Ctrl+F5)", "INFO")
    else:
        log_message("⚠️ CORREÇÃO PARCIAL - Verificar logs acima", "WARNING")
    
    print("="*60)

if __name__ == "__main__":
    main() 