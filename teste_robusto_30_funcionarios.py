#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏭 TESTE ROBUSTO - 30 FUNCIONÁRIOS COM CENÁRIOS COMPLEXOS
========================================================

Sistema: RLPONTO-WEB v1.0
Data: 18/07/2025
Objetivo: Testar robustez com 30 funcionários, 900 batidas, cenários complexos

CENÁRIOS TESTADOS:
- 30 funcionários (João01 a João30)
- 30 batidas por funcionário (900 total)
- 15 funcionários com horários corretos
- 15 funcionários com horários problemáticos
- Teste de cadastro, batidas, cálculos
- Horas negativas, positivas, banco de horas, extras
- Cálculo total de horas do período
"""

import sys
import os
import random
from datetime import datetime, time, date, timedelta
import logging
import json
from decimal import Decimal
import uuid

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('teste_robusto')

# Adicionar o diretório do projeto ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'var', 'www', 'controle-ponto'))

class TesteRobusto30Funcionarios:
    """
    Classe para teste robusto com 30 funcionários e cenários complexos.
    """
    
    def __init__(self):
        self.funcionarios = []
        self.registros_ponto = []
        self.resultados_calculos = []
        self.estatisticas = {
            'total_funcionarios': 30,
            'total_batidas': 0,
            'funcionarios_corretos': 15,
            'funcionarios_problematicos': 15,
            'horas_totais': 0.0,
            'banco_horas_total': 0.0,
            'horas_extras_total': 0.0,
            'erros_detectados': 0
        }
        
        # Importar módulos de cálculo
        try:
            from calculos_ponto_corrigido import (
                calcular_horas_trabalhadas,
                validar_horarios_jornada,
                calcular_banco_horas,
                calcular_horas_extras_b5_b6
            )
            self.calcular_horas = calcular_horas_trabalhadas
            self.validar_horarios = validar_horarios_jornada
            self.calcular_banco = calcular_banco_horas
            self.calcular_extras = calcular_horas_extras_b5_b6
            logger.info("✅ Módulos de cálculo importados com sucesso")
        except ImportError as e:
            logger.error(f"❌ Erro na importação: {e}")
            raise

    def gerar_funcionarios(self):
        """
        Gera 30 funcionários de teste (João01 a João30).
        """
        logger.info("🔧 Gerando 30 funcionários de teste...")
        
        for i in range(1, 31):
            funcionario = {
                'id': i,
                'nome': f'João{i:02d}',
                'matricula': f'MAT{i:04d}',
                'empresa_id': 1,
                'status': 'ativo',
                'jornada': {
                    'entrada_manha': time(8, 0),
                    'saida_almoco': time(12, 0),
                    'entrada_tarde': time(13, 0),
                    'saida': time(17, 0),
                    'horas_obrigatorias': 8.0,
                    'tolerancia_minutos': 10
                },
                'banco_horas_anterior': random.uniform(-5.0, 5.0),  # Saldo anterior aleatório
                'tipo_teste': 'correto' if i <= 15 else 'problematico'
            }
            self.funcionarios.append(funcionario)
        
        logger.info(f"✅ {len(self.funcionarios)} funcionários gerados")
        logger.info(f"   - 15 funcionários com horários corretos")
        logger.info(f"   - 15 funcionários com horários problemáticos")

    def gerar_horarios_corretos(self):
        """
        Gera horários corretos para funcionários 1-15.
        """
        horarios_corretos = [
            # Jornadas normais
            (time(8, 0), time(12, 0), time(13, 0), time(17, 0)),    # Pontual
            (time(7, 55), time(12, 5), time(13, 0), time(17, 0)),   # Chegou cedo
            (time(8, 5), time(12, 0), time(13, 0), time(17, 5)),    # Compensou atraso
            (time(8, 0), time(12, 0), time(13, 0), time(17, 30)),   # Horas extras
            (time(7, 50), time(12, 0), time(13, 0), time(18, 0)),   # Muito dedicado
            
            # Jornadas com pequenas variações
            (time(8, 3), time(12, 0), time(13, 0), time(17, 0)),    # Atraso tolerável
            (time(8, 0), time(12, 10), time(13, 0), time(17, 0)),   # Almoço estendido
            (time(8, 0), time(12, 0), time(12, 55), time(17, 0)),   # Retorno cedo
            (time(8, 0), time(12, 0), time(13, 0), time(16, 50)),   # Saída antecipada
            (time(8, 10), time(12, 0), time(13, 0), time(17, 10)),  # Compensação
            
            # Jornadas sem intervalo (6h)
            (time(8, 0), None, None, time(14, 0)),                  # 6h corridas
            (time(9, 0), None, None, time(15, 0)),                  # 6h tarde
            (time(7, 0), None, None, time(13, 0)),                  # 6h cedo
            
            # Jornadas especiais
            (time(8, 0), time(12, 0), time(13, 0), time(17, 0)),    # Normal
            (time(8, 0), time(12, 0), time(13, 0), time(17, 0)),    # Normal
        ]
        
        return horarios_corretos

    def gerar_horarios_problematicos(self):
        """
        Gera horários problemáticos para funcionários 16-30.
        """
        horarios_problematicos = [
            # Atrasos significativos
            (time(8, 30), time(12, 0), time(13, 0), time(17, 0)),   # Atraso 30min
            (time(9, 0), time(12, 0), time(13, 0), time(17, 0)),    # Atraso 1h
            (time(8, 45), time(12, 0), time(13, 30), time(17, 0)),  # Atrasos múltiplos
            
            # Saídas antecipadas
            (time(8, 0), time(12, 0), time(13, 0), time(16, 0)),    # Saída 1h cedo
            (time(8, 0), time(12, 0), time(13, 0), time(15, 30)),   # Saída muito cedo
            
            # Almoços longos
            (time(8, 0), time(12, 0), time(14, 0), time(17, 0)),    # Almoço 2h
            (time(8, 0), time(12, 0), time(14, 30), time(17, 0)),   # Almoço 2.5h
            
            # Jornadas muito curtas
            (time(10, 0), time(12, 0), time(13, 0), time(15, 0)),   # Apenas 4h
            (time(9, 0), None, None, time(12, 0)),                  # Apenas 3h
            
            # Horários inconsistentes (para testar validação)
            (time(8, 0), time(11, 0), time(10, 30), time(17, 0)),   # Retorno antes saída
            (time(8, 0), time(12, 0), time(13, 0), time(12, 30)),   # Saída antes entrada
            
            # Jornadas excessivas
            (time(6, 0), time(12, 0), time(13, 0), time(20, 0)),    # 13h de trabalho
            (time(7, 0), None, None, time(19, 0)),                  # 12h corridas
            
            # Casos extremos
            (time(8, 0), time(8, 30), time(9, 0), time(17, 0)),     # Intervalo muito curto
            (time(8, 0), time(12, 0), time(13, 0), time(17, 0)),    # Normal (controle)
        ]
        
        return horarios_problematicos

    def gerar_batidas_funcionario(self, funcionario, dia_base):
        """
        Gera 30 batidas para um funcionário ao longo de 30 dias.
        """
        batidas = []
        horarios_corretos = self.gerar_horarios_corretos()
        horarios_problematicos = self.gerar_horarios_problematicos()
        
        for dia in range(30):
            data_batida = dia_base + timedelta(days=dia)
            
            # Pular fins de semana (opcional)
            if data_batida.weekday() >= 5:  # Sábado=5, Domingo=6
                continue
            
            # Escolher horário baseado no tipo do funcionário
            if funcionario['tipo_teste'] == 'correto':
                horario = random.choice(horarios_corretos)
            else:
                horario = random.choice(horarios_problematicos)
            
            entrada, saida_almoco, entrada_tarde, saida = horario
            
            # Criar registros de batida
            batida_dia = {
                'funcionario_id': funcionario['id'],
                'funcionario_nome': funcionario['nome'],
                'data': data_batida,
                'entrada_manha': entrada,
                'saida_almoco': saida_almoco,
                'entrada_tarde': entrada_tarde,
                'saida': saida,
                'tipo_teste': funcionario['tipo_teste']
            }
            
            batidas.append(batida_dia)
        
        return batidas

    def processar_calculos_funcionario(self, funcionario, batidas):
        """
        Processa cálculos de horas para um funcionário.
        """
        resultado_funcionario = {
            'funcionario_id': funcionario['id'],
            'funcionario_nome': funcionario['nome'],
            'tipo_teste': funcionario['tipo_teste'],
            'total_dias': len(batidas),
            'horas_trabalhadas_total': 0.0,
            'horas_obrigatorias_total': 0.0,
            'banco_horas_final': funcionario['banco_horas_anterior'],
            'horas_extras_total': 0.0,
            'dias_com_erro': 0,
            'detalhes_dias': []
        }
        
        for batida in batidas:
            try:
                # Calcular horas do dia
                horas_dia = self.calcular_horas(
                    batida['entrada_manha'],
                    batida['saida_almoco'],
                    batida['entrada_tarde'],
                    batida['saida']
                )
                
                # Validar horários
                valido, erros = self.validar_horarios(
                    batida['entrada_manha'],
                    batida['saida_almoco'],
                    batida['entrada_tarde'],
                    batida['saida']
                )
                
                if not valido:
                    resultado_funcionario['dias_com_erro'] += 1
                    self.estatisticas['erros_detectados'] += 1
                
                # Calcular banco de horas
                banco_resultado = self.calcular_banco(
                    horas_dia,
                    funcionario['jornada']['horas_obrigatorias'],
                    resultado_funcionario['banco_horas_final']
                )
                
                # Calcular horas extras se houver
                horas_extras_dia = 0.0
                if batida['saida'] and batida['saida'] > time(17, 30):
                    # Simular horas extras após 17:30
                    inicio_extra = time(17, 30)
                    horas_extras_dia = self.calcular_extras(inicio_extra, batida['saida'])
                
                # Atualizar totais
                resultado_funcionario['horas_trabalhadas_total'] += horas_dia
                resultado_funcionario['horas_obrigatorias_total'] += funcionario['jornada']['horas_obrigatorias']
                resultado_funcionario['banco_horas_final'] = banco_resultado['novo_saldo']
                resultado_funcionario['horas_extras_total'] += horas_extras_dia
                
                # Detalhe do dia
                detalhe_dia = {
                    'data': batida['data'].strftime('%d/%m/%Y'),
                    'horas_trabalhadas': horas_dia,
                    'horas_obrigatorias': funcionario['jornada']['horas_obrigatorias'],
                    'diferenca': banco_resultado['diferenca'],
                    'banco_saldo': banco_resultado['novo_saldo'],
                    'horas_extras': horas_extras_dia,
                    'valido': valido,
                    'erros': erros if not valido else []
                }
                
                resultado_funcionario['detalhes_dias'].append(detalhe_dia)
                
            except Exception as e:
                logger.error(f"❌ Erro ao processar dia {batida['data']} para {funcionario['nome']}: {e}")
                resultado_funcionario['dias_com_erro'] += 1
                self.estatisticas['erros_detectados'] += 1
        
        return resultado_funcionario

    def executar_teste_completo(self):
        """
        Executa o teste completo com 30 funcionários.
        """
        logger.info("🚀 INICIANDO TESTE ROBUSTO - 30 FUNCIONÁRIOS")
        logger.info("=" * 80)
        
        # 1. Gerar funcionários
        self.gerar_funcionarios()
        
        # 2. Data base para os testes (último mês)
        data_base = date.today() - timedelta(days=30)
        
        # 3. Processar cada funcionário
        logger.info("🔄 Processando batidas e cálculos...")
        
        for funcionario in self.funcionarios:
            logger.info(f"   Processando {funcionario['nome']} ({funcionario['tipo_teste']})...")
            
            # Gerar batidas do funcionário
            batidas = self.gerar_batidas_funcionario(funcionario, data_base)
            self.registros_ponto.extend(batidas)
            self.estatisticas['total_batidas'] += len(batidas)
            
            # Processar cálculos
            resultado = self.processar_calculos_funcionario(funcionario, batidas)
            self.resultados_calculos.append(resultado)
            
            # Atualizar estatísticas gerais
            self.estatisticas['horas_totais'] += resultado['horas_trabalhadas_total']
            self.estatisticas['banco_horas_total'] += resultado['banco_horas_final']
            self.estatisticas['horas_extras_total'] += resultado['horas_extras_total']
        
        logger.info("✅ Processamento concluído!")

    def gerar_relatorio_detalhado(self):
        """
        Gera relatório detalhado dos resultados.
        """
        logger.info("\n" + "=" * 80)
        logger.info("📊 RELATÓRIO DETALHADO - TESTE ROBUSTO 30 FUNCIONÁRIOS")
        logger.info("=" * 80)
        
        # Estatísticas gerais
        logger.info(f"📈 ESTATÍSTICAS GERAIS:")
        logger.info(f"   Total de funcionários: {self.estatisticas['total_funcionarios']}")
        logger.info(f"   Total de batidas: {self.estatisticas['total_batidas']}")
        logger.info(f"   Funcionários corretos: {self.estatisticas['funcionarios_corretos']}")
        logger.info(f"   Funcionários problemáticos: {self.estatisticas['funcionarios_problematicos']}")
        logger.info(f"   Erros detectados: {self.estatisticas['erros_detectados']}")
        
        logger.info(f"\n💰 TOTAIS CALCULADOS:")
        logger.info(f"   Horas trabalhadas total: {self.estatisticas['horas_totais']:.2f}h")
        logger.info(f"   Banco de horas total: {self.estatisticas['banco_horas_total']:.2f}h")
        logger.info(f"   Horas extras total: {self.estatisticas['horas_extras_total']:.2f}h")
        
        # Top 5 funcionários com mais horas
        top_horas = sorted(self.resultados_calculos, 
                          key=lambda x: x['horas_trabalhadas_total'], reverse=True)[:5]
        
        logger.info(f"\n🏆 TOP 5 FUNCIONÁRIOS - MAIS HORAS:")
        for i, func in enumerate(top_horas, 1):
            logger.info(f"   {i}. {func['funcionario_nome']}: {func['horas_trabalhadas_total']:.2f}h")
        
        # Top 5 funcionários com mais problemas
        top_problemas = sorted(self.resultados_calculos, 
                              key=lambda x: x['dias_com_erro'], reverse=True)[:5]
        
        logger.info(f"\n⚠️  TOP 5 FUNCIONÁRIOS - MAIS PROBLEMAS:")
        for i, func in enumerate(top_problemas, 1):
            logger.info(f"   {i}. {func['funcionario_nome']}: {func['dias_com_erro']} dias com erro")
        
        # Análise por tipo
        corretos = [r for r in self.resultados_calculos if r['tipo_teste'] == 'correto']
        problematicos = [r for r in self.resultados_calculos if r['tipo_teste'] == 'problematico']
        
        logger.info(f"\n📊 ANÁLISE POR TIPO:")
        logger.info(f"   FUNCIONÁRIOS CORRETOS:")
        logger.info(f"     Média horas/funcionário: {sum(r['horas_trabalhadas_total'] for r in corretos)/len(corretos):.2f}h")
        logger.info(f"     Média erros/funcionário: {sum(r['dias_com_erro'] for r in corretos)/len(corretos):.1f}")
        
        logger.info(f"   FUNCIONÁRIOS PROBLEMÁTICOS:")
        logger.info(f"     Média horas/funcionário: {sum(r['horas_trabalhadas_total'] for r in problematicos)/len(problematicos):.2f}h")
        logger.info(f"     Média erros/funcionário: {sum(r['dias_com_erro'] for r in problematicos)/len(problematicos):.1f}")

    def salvar_resultados_json(self):
        """
        Salva resultados detalhados em arquivo JSON.
        """
        resultado_completo = {
            'metadata': {
                'data_teste': datetime.now().isoformat(),
                'sistema': 'RLPONTO-WEB v1.0',
                'tipo_teste': 'Robusto 30 Funcionários',
                'total_funcionarios': len(self.funcionarios),
                'total_batidas': len(self.registros_ponto)
            },
            'estatisticas': self.estatisticas,
            'funcionarios': self.funcionarios,
            'resultados_calculos': self.resultados_calculos
        }
        
        # Converter objetos datetime e time para string
        def converter_datetime(obj):
            if isinstance(obj, (datetime, date)):
                return obj.isoformat()
            elif isinstance(obj, time):
                return obj.strftime('%H:%M:%S')
            return obj
        
        # Salvar arquivo
        nome_arquivo = f"resultados_teste_robusto_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(nome_arquivo, 'w', encoding='utf-8') as f:
                json.dump(resultado_completo, f, indent=2, default=converter_datetime, ensure_ascii=False)
            
            logger.info(f"💾 Resultados salvos em: {nome_arquivo}")
            return nome_arquivo
        except Exception as e:
            logger.error(f"❌ Erro ao salvar resultados: {e}")
            return None

def main():
    """
    Função principal para executar o teste robusto.
    """
    print("🏭 TESTE ROBUSTO - 30 FUNCIONÁRIOS COM CENÁRIOS COMPLEXOS")
    print("=" * 70)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"Sistema: RLPONTO-WEB v1.0")
    print("=" * 70)
    
    try:
        # Criar e executar teste
        teste = TesteRobusto30Funcionarios()
        teste.executar_teste_completo()
        teste.gerar_relatorio_detalhado()
        
        # Salvar resultados
        arquivo_salvo = teste.salvar_resultados_json()
        
        # Resultado final
        print("\n" + "=" * 70)
        print("🎉 TESTE ROBUSTO CONCLUÍDO COM SUCESSO!")
        print("=" * 70)
        print(f"✅ {teste.estatisticas['total_funcionarios']} funcionários testados")
        print(f"✅ {teste.estatisticas['total_batidas']} batidas processadas")
        print(f"✅ {teste.estatisticas['horas_totais']:.2f}h totais calculadas")
        print(f"⚠️  {teste.estatisticas['erros_detectados']} erros detectados e tratados")
        
        if arquivo_salvo:
            print(f"💾 Resultados detalhados salvos em: {arquivo_salvo}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro durante execução do teste: {e}")
        return False

class TesteIntegracaoBancoDados:
    """
    Classe para testar integração com banco de dados real.
    """

    def __init__(self):
        self.conexao = None
        self.funcionarios_criados = []

    def conectar_banco(self):
        """
        Conecta ao banco de dados MySQL.
        """
        try:
            import pymysql

            self.conexao = pymysql.connect(
                host='localhost',
                user='root',
                password='',  # Configurar conforme necessário
                database='controle_ponto',
                charset='utf8mb4'
            )

            logger.info("✅ Conexão com banco de dados estabelecida")
            return True

        except Exception as e:
            logger.warning(f"⚠️ Não foi possível conectar ao banco: {e}")
            logger.info("   Continuando com testes sem banco de dados...")
            return False

    def criar_funcionarios_banco(self, funcionarios):
        """
        Cria funcionários no banco de dados para teste.
        """
        if not self.conexao:
            return False

        try:
            cursor = self.conexao.cursor()

            for funcionario in funcionarios:
                # Verificar se funcionário já existe
                cursor.execute(
                    "SELECT id FROM funcionarios WHERE matricula = %s",
                    (funcionario['matricula'],)
                )

                if cursor.fetchone():
                    logger.info(f"   Funcionário {funcionario['nome']} já existe")
                    continue

                # Inserir funcionário
                sql = """
                INSERT INTO funcionarios
                (nome_completo, matricula, empresa_id, status, data_admissao, horas_trabalho_obrigatorias)
                VALUES (%s, %s, %s, %s, %s, %s)
                """

                cursor.execute(sql, (
                    funcionario['nome'],
                    funcionario['matricula'],
                    funcionario['empresa_id'],
                    funcionario['status'],
                    date.today(),
                    funcionario['jornada']['horas_obrigatorias']
                ))

                funcionario_id = cursor.lastrowid
                funcionario['id_banco'] = funcionario_id
                self.funcionarios_criados.append(funcionario_id)

                logger.info(f"   ✅ {funcionario['nome']} criado (ID: {funcionario_id})")

            self.conexao.commit()
            logger.info(f"✅ {len(self.funcionarios_criados)} funcionários criados no banco")
            return True

        except Exception as e:
            logger.error(f"❌ Erro ao criar funcionários: {e}")
            self.conexao.rollback()
            return False

    def inserir_registros_ponto(self, registros):
        """
        Insere registros de ponto no banco de dados.
        """
        if not self.conexao:
            return False

        try:
            cursor = self.conexao.cursor()

            for registro in registros[:100]:  # Limitar para teste
                sql = """
                INSERT INTO registros_ponto
                (funcionario_id, data_registro, hora_registro, tipo_registro, metodo_registro, observacoes)
                VALUES (%s, %s, %s, %s, %s, %s)
                """

                # Inserir cada batida do dia
                if registro['entrada_manha']:
                    cursor.execute(sql, (
                        registro.get('id_banco', registro['funcionario_id']),
                        registro['data'],
                        registro['entrada_manha'],
                        'entrada_manha',
                        'manual',
                        f"Teste robusto - {registro['tipo_teste']}"
                    ))

                if registro['saida_almoco']:
                    cursor.execute(sql, (
                        registro.get('id_banco', registro['funcionario_id']),
                        registro['data'],
                        registro['saida_almoco'],
                        'saida_almoco',
                        'manual',
                        f"Teste robusto - {registro['tipo_teste']}"
                    ))

                if registro['entrada_tarde']:
                    cursor.execute(sql, (
                        registro.get('id_banco', registro['funcionario_id']),
                        registro['data'],
                        registro['entrada_tarde'],
                        'entrada_tarde',
                        'manual',
                        f"Teste robusto - {registro['tipo_teste']}"
                    ))

                if registro['saida']:
                    cursor.execute(sql, (
                        registro.get('id_banco', registro['funcionario_id']),
                        registro['data'],
                        registro['saida'],
                        'saida',
                        'manual',
                        f"Teste robusto - {registro['tipo_teste']}"
                    ))

            self.conexao.commit()
            logger.info("✅ Registros de ponto inseridos no banco")
            return True

        except Exception as e:
            logger.error(f"❌ Erro ao inserir registros: {e}")
            self.conexao.rollback()
            return False

    def limpar_dados_teste(self):
        """
        Limpa dados de teste do banco.
        """
        if not self.conexao or not self.funcionarios_criados:
            return

        try:
            cursor = self.conexao.cursor()

            # Deletar registros de ponto
            for func_id in self.funcionarios_criados:
                cursor.execute(
                    "DELETE FROM registros_ponto WHERE funcionario_id = %s AND observacoes LIKE 'Teste robusto%'",
                    (func_id,)
                )

            # Deletar funcionários
            for func_id in self.funcionarios_criados:
                cursor.execute(
                    "DELETE FROM funcionarios WHERE id = %s",
                    (func_id,)
                )

            self.conexao.commit()
            logger.info("🧹 Dados de teste removidos do banco")

        except Exception as e:
            logger.error(f"❌ Erro ao limpar dados: {e}")

        finally:
            if self.conexao:
                self.conexao.close()

def executar_teste_completo_com_banco():
    """
    Executa teste completo incluindo integração com banco de dados.
    """
    print("🏭 TESTE ROBUSTO COMPLETO - COM INTEGRAÇÃO BANCO DE DADOS")
    print("=" * 80)

    # Teste principal
    teste_principal = TesteRobusto30Funcionarios()
    teste_principal.executar_teste_completo()

    # Teste de integração com banco
    teste_banco = TesteIntegracaoBancoDados()

    if teste_banco.conectar_banco():
        logger.info("🔄 Executando testes de integração com banco...")

        # Criar funcionários no banco
        teste_banco.criar_funcionarios_banco(teste_principal.funcionarios)

        # Inserir alguns registros de ponto
        teste_banco.inserir_registros_ponto(teste_principal.registros_ponto)

        # Gerar relatório
        teste_principal.gerar_relatorio_detalhado()

        # Limpar dados de teste
        resposta = input("\n🗑️  Deseja limpar os dados de teste do banco? (s/N): ")
        if resposta.lower() == 's':
            teste_banco.limpar_dados_teste()
    else:
        # Apenas teste sem banco
        teste_principal.gerar_relatorio_detalhado()

    # Salvar resultados
    teste_principal.salvar_resultados_json()

    return True

if __name__ == "__main__":
    print("Escolha o tipo de teste:")
    print("1. Teste robusto básico (sem banco)")
    print("2. Teste robusto completo (com banco)")

    escolha = input("Digite sua escolha (1 ou 2): ").strip()

    if escolha == "2":
        sucesso = executar_teste_completo_com_banco()
    else:
        sucesso = main()

    sys.exit(0 if sucesso else 1)
