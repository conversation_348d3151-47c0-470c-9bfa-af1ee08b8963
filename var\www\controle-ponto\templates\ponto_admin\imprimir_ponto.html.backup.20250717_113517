<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Ponto - {{ funcionario.nome_completo }}</title>
    <style>
        /* CSS Version: 2025-07-17-v2 - Cabeçalho compacto para impressão */
        /* ========================================
           LAYOUT ORIGINAL - DESIGN MODERNO
           ======================================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.5;
        }

        /* Container principal */
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* Cabeçalho roxo gradiente */
        .header-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            position: relative;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-icon {
            font-size: 24px;
        }

        .header-text h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .header-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .header-buttons {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }

        .btn-print {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-print:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .btn-back {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-back:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Card do funcionário */
        .employee-card {
            margin: 24px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .employee-header {
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .employee-avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 600;
        }

        .employee-info {
            flex: 1;
        }

        .employee-name {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .employee-role {
            color: #718096;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .employee-period {
            color: #4a5568;
            font-size: 13px;
        }

        .employee-details {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            padding: 20px;
            background: #f7fafc;
            border-top: 1px solid #e2e8f0;
        }

        .detail-item {
            text-align: center;
        }

        .detail-label {
            font-size: 12px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
        }

        .signature-line {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
        }

        .signature-text {
            color: #718096;
            font-size: 12px;
            margin-bottom: 40px;
        }

        .signature-border {
            width: 300px;
            height: 1px;
            background: #2d3748;
            margin: 0 auto;
        }

        /* Seção de registros */
        .records-section {
            margin: 24px;
        }

        .records-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .records-icon {
            font-size: 20px;
        }

        .records-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
        }

        /* Tabela moderna */
        .records-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .records-table th {
            background: #f7fafc;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            color: #4a5568;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 2px solid #e2e8f0;
        }

        .records-table td {
            padding: 12px 8px;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
            font-size: 14px;
            color: #2d3748;
        }

        .records-table tr:hover {
            background: #f7fafc;
        }

        .records-table tr:last-child td {
            border-bottom: none;
        }

        /* Badges de justificativa */
        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-abonado {
            background: #c6f6d5;
            color: #22543d;
        }

        .badge-reprovado {
            background: #fed7d7;
            color: #742a2a;
        }

        .badge-pendente {
            background: #fef5e7;
            color: #744210;
        }

        .badge-vazio {
            color: #a0aec0;
            font-style: italic;
            background: none;
        }

        /* Data destacada */
        .date-cell {
            font-weight: 600;
            color: #2d3748;
        }

        .day-cell {
            color: #718096;
            font-size: 12px;
        }

        .hours-cell {
            font-weight: 600;
            color: #2b6cb0;
        }

        /* Estilos para impressão - VERSÃO COMPACTA FORÇADA */
        @media print {
            /* RESET TOTAL E FORÇADO */
            * {
                box-sizing: border-box !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            html, body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                font-size: 12px !important;
            }

            .container {
                box-shadow: none !important;
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .header-buttons {
                display: none !important;
            }

            /* CABEÇALHO SUPER COMPACTO - FORÇADO */
            .header-gradient {
                padding: 4px 10px !important;
                margin: 0 !important;
                height: 35px !important;
                max-height: 35px !important;
                min-height: 35px !important;
                background: #667eea !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                display: flex !important;
                align-items: center !important;
            }

            .header-content {
                margin: 0 !important;
                padding: 0 !important;
                height: auto !important;
                display: flex !important;
                align-items: center !important;
                width: 100% !important;
            }

            .header-title {
                margin: 0 !important;
                padding: 0 !important;
                display: flex !important;
                align-items: center !important;
                gap: 6px !important;
            }

            .header-icon {
                font-size: 12px !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .header-text {
                margin: 0 !important;
                padding: 0 !important;
                line-height: 1 !important;
            }

            .header-text h1 {
                font-size: 13px !important;
                margin: 0 !important;
                padding: 0 !important;
                line-height: 1 !important;
                font-weight: 600 !important;
                color: white !important;
            }

            .header-subtitle {
                font-size: 9px !important;
                margin: 0 !important;
                padding: 0 !important;
                line-height: 1 !important;
                color: rgba(255,255,255,0.9) !important;
                margin-top: 1px !important;
            }

            /* CARD DO FUNCIONÁRIO COMPACTO */
            .employee-card {
                margin: 8px 0 !important;
                padding: 10px !important;
                box-shadow: none !important;
            }

            .employee-header {
                margin-bottom: 8px !important;
            }

            .employee-avatar {
                width: 30px !important;
                height: 30px !important;
                font-size: 12px !important;
            }

            .employee-name {
                font-size: 14px !important;
                margin-bottom: 1px !important;
            }

            .employee-role {
                font-size: 10px !important;
                margin-bottom: 2px !important;
            }

            .employee-period {
                font-size: 9px !important;
            }

            .employee-details {
                margin: 8px 0 !important;
                gap: 10px !important;
            }

            .detail-label {
                font-size: 8px !important;
                margin-bottom: 1px !important;
            }

            .detail-value {
                font-size: 10px !important;
            }

            .signature-line {
                margin-top: 10px !important;
            }

            .signature-text {
                font-size: 9px !important;
                margin-bottom: 6px !important;
            }

            /* SEÇÃO DE REGISTROS COMPACTA */
            .records-section {
                margin: 8px 0 !important;
                padding: 0 !important;
            }

            .records-header {
                margin-bottom: 6px !important;
            }

            .records-title {
                font-size: 12px !important;
            }

            .records-icon {
                font-size: 12px !important;
            }

            .records-table {
                box-shadow: none !important;
                font-size: 9px !important;
                width: 100% !important;
            }

            .records-table th,
            .records-table td {
                border: 1px solid #333 !important;
                padding: 3px 2px !important;
                font-size: 9px !important;
            }

            .records-table th {
                background: #f0f0f0 !important;
                font-size: 8px !important;
                font-weight: 600 !important;
                padding: 2px 1px !important;
            }

            /* CONFIGURAÇÃO DE PÁGINA */
            @page {
                margin: 0.4in !important;
                size: A4 portrait !important;
            }

            /* QUEBRA DE PÁGINA E FINALIZAÇÃO */
            .page-break {
                page-break-before: always !important;
            }

            .employee-card,
            .records-section {
                page-break-inside: avoid !important;
            }
        }



        /* Estado vazio */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #4a5568;
        }

        .empty-state p {
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Cabeçalho com gradiente roxo -->
        <div class="header-gradient" style="padding: 8px 15px !important; height: auto !important; min-height: auto !important;">
            <div class="header-content">
                <div class="header-title">
                    <div class="header-icon">📊</div>
                    <div class="header-text" style="line-height: 1.1 !important;">
                        <h1 style="font-size: 16px !important; margin: 0 !important; margin-bottom: 2px !important; line-height: 1.1 !important;">Relatório de Ponto</h1>
                        <div class="header-subtitle" style="font-size: 11px !important; margin: 0 !important; line-height: 1.1 !important;">Gerado em {{ data_atual.strftime('%d/%m/%Y às %H:%M') if data_atual else 'Data não disponível' }}</div>
                    </div>
                </div>
                <div class="header-buttons">
                    <button onclick="window.print()" class="btn btn-print">
                        📄 Imprimir
                    </button>
                    <a href="{{ url_for('ponto_admin.detalhes_funcionario', funcionario_id=funcionario.id) }}" class="btn btn-back">
                        ← Voltar
                    </a>
                </div>
            </div>
        </div>

        <!-- Card do funcionário -->
        <div class="employee-card">
            <div class="employee-header">
                <div class="employee-avatar">
                    {{ funcionario.nome_completo[0].upper() if funcionario.nome_completo else 'F' }}
                </div>
                <div class="employee-info">
                    <div class="employee-name">{{ funcionario.nome_completo or 'Nome não informado' }}</div>
                    <div class="employee-role">{{ funcionario.cargo or 'DESENVOLVEDOR SÊNIOR' }}</div>
                    <div class="employee-period">
                        {% if data_inicio and data_fim %}
                            Período do relatório: {{ data_inicio }} até {{ data_fim }}
                        {% else %}
                            Período do relatório: 16/06/2025 até 16/07/2025
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="employee-details">
                <div class="detail-item">
                    <div class="detail-label">Matrícula</div>
                    <div class="detail-value">{{ funcionario.matricula_empresa or '001' }}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Empresa</div>
                    <div class="detail-value">{{ funcionario.empresa_nome or 'AiNexus Tecnologia' }}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">CNPJ</div>
                    <div class="detail-value">{{ funcionario.empresa_cnpj or '48.061.684/0001-68' }}</div>
                </div>
            </div>

            <div class="signature-line">
                <div class="signature-text">Assinatura do Funcionário</div>
                <div class="signature-border"></div>
            </div>
        </div>

        <!-- Seção de registros -->
        <div class="records-section">
            <div class="records-header">
                <div class="records-icon">📋</div>
                <div class="records-title">Registros de Ponto</div>
            </div>

            {% if registros %}
            <table class="records-table">
                <thead>
                    <tr>
                        <th>Data</th>
                        <th>Dia</th>
                        <th>Entrada</th>
                        <th>Saída Almoço</th>
                        <th>Retorno</th>
                        <th>Saída</th>
                        <th>Horas</th>
                        <th>Justificativa</th>
                    </tr>
                </thead>
                <tbody>
                    {% for registro in registros %}
                    <tr>
                        <td class="date-cell">
                            {% if registro.data_registro %}
                                {{ registro.data_registro.strftime('%d/%m/%Y') }}
                            {% elif registro.data %}
                                {{ registro.data.strftime('%d/%m/%Y') }}
                            {% else %}
                                N/A
                            {% endif %}
                        </td>
                        <td class="day-cell">{{ registro.dia_semana or 'N/A' }}</td>
                        <td>{{ registro.entrada.strftime('%H:%M') if registro.entrada else '-' }}</td>
                        <td>{{ registro.saida_almoco.strftime('%H:%M') if registro.saida_almoco else '-' }}</td>
                        <td>{{ registro.retorno_almoco.strftime('%H:%M') if registro.retorno_almoco else '-' }}</td>
                        <td>{{ registro.saida.strftime('%H:%M') if registro.saida else '-' }}</td>
                        <td class="hours-cell">{{ registro.horas_trabalhadas or '-' }}</td>
                        <td>
                            {% if registro.status_justificativa == 'abonado' %}
                                <span class="badge badge-abonado">Abonado</span>
                            {% elif registro.status_justificativa == 'reprovado' %}
                                <span class="badge badge-reprovado">Reprovado</span>
                            {% elif registro.status_justificativa == 'pendente' %}
                                <span class="badge badge-pendente">Pendente</span>
                            {% else %}
                                <span class="badge badge-vazio">- -</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">📋</div>
                <h3>Nenhum registro encontrado</h3>
                <p>Não há registros de ponto para o período selecionado.</p>
            </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
