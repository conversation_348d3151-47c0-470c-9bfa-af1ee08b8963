#!/usr/bin/env python3
"""
Script de Diagnóstico - Problema Exclusão de Empresas
Data: 02/07/2025
Objetivo: Diagnosticar por que empresas "excluídas" ainda aparecem na listagem
"""

import requests
import json

def diagnosticar_exclusao_empresas():
    """
    Testa o problema de exclusão de empresas
    """
    
    print("🧪 DIAGNÓSTICO - PROBLEMA EXCLUSÃO DE EMPRESAS")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    try:
        # 1. LOGIN NO SISTEMA
        print("1️⃣ Fazendo login no sistema...")
        login_data = {
            'usuario': 'admin',
            'senha': '@Ric6109'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code == 200:
            print("   ✅ Login realizado com sucesso")
        else:
            print(f"   ❌ Erro no login: {login_response.status_code}")
            return False
            
        # 2. VERIFICAR EMPRESAS ATUAIS
        print("\n2️⃣ Verificando empresas atuais via API...")
        
        api_response = session.get(f"{base_url}/configuracoes/api/empresas")
        
        if api_response.status_code == 200:
            empresas = api_response.json()
            print(f"   📊 Total de empresas retornadas pela API: {len(empresas)}")
            
            # Verificar se a resposta é uma lista ou dict com empresas
            if isinstance(empresas, list):
                ativas = sum(1 for emp in empresas if isinstance(emp, dict) and emp.get('ativa', True))
                inativas = len(empresas) - ativas
                
                print(f"   ✅ Empresas ativas: {ativas}")
                print(f"   ❌ Empresas inativas: {inativas}")
                
                if inativas > 0:
                    print("\n   🔍 Empresas inativas encontradas:")
                    for emp in empresas:
                        if isinstance(emp, dict) and not emp.get('ativa', True):
                            print(f"      - ID: {emp.get('id', 'N/A')}, Nome: {emp.get('razao_social', 'N/A')}, Ativa: {emp.get('ativa', 'N/A')}")
            else:
                print(f"   ⚠️ Formato inesperado da resposta da API: {type(empresas)}")
                print(f"   📋 Conteúdo: {empresas}")
                ativas = 0
                inativas = 0
                        
        else:
            print(f"   ❌ Erro na API: {api_response.status_code}")
            return False
            
        # 3. VERIFICAR PÁGINA DE LISTAGEM
        print("\n3️⃣ Verificando página de listagem...")
        
        listagem_response = session.get(f"{base_url}/configuracoes/empresas")
        
        if listagem_response.status_code == 200:
            # Contar empresas na página
            html_content = listagem_response.text
            empresa_items = html_content.count('class="empresa-item"')
            print(f"   📊 Empresas exibidas na página: {empresa_items}")
            
            # Verificar se há empresas inativas sendo exibidas
            if 'status-inativa' in html_content:
                print("   ⚠️ PROBLEMA ENCONTRADO: Empresas inativas sendo exibidas na listagem!")
                
                # Contar quantas inativas
                inativas_na_pagina = html_content.count('status-inativa')
                print(f"      📋 Empresas inativas na página: {inativas_na_pagina}")
            else:
                print("   ✅ Apenas empresas ativas sendo exibidas")
                
        else:
            print(f"   ❌ Erro ao acessar listagem: {listagem_response.status_code}")
            
        # 4. TESTAR EXCLUSÃO SE POSSÍVEL
        print("\n4️⃣ Analisando lógica de exclusão...")
        
        if empresas:
            # Procurar empresa sem funcionários para testar
            empresa_teste = None
            if isinstance(empresas, list):
                for emp in empresas:
                    if isinstance(emp, dict) and emp.get('ativa', True) and emp.get('total_funcionarios', 0) == 0:
                        empresa_teste = emp
                        break
                    
            if empresa_teste:
                print(f"   📋 Empresa candidata a teste: {empresa_teste['razao_social']}")
                print(f"      - ID: {empresa_teste['id']}")
                print(f"      - Funcionários: {empresa_teste.get('total_funcionarios', 0)}")
                print(f"      - Ativa: {empresa_teste.get('ativa', True)}")
                print("   ⚠️ TESTE MANUAL NECESSÁRIO via interface")
            else:
                print("   ℹ️ Nenhuma empresa disponível para teste (todas com funcionários)")
                
        # 5. DIAGNÓSTICO DA QUERY
        print("\n5️⃣ Análise da consulta de listagem...")
        print("   🔍 PROBLEMA IDENTIFICADO na query SQL:")
        print("   ❌ Query atual: SELECT * FROM empresas (sem filtro WHERE ativa = TRUE)")
        print("   ✅ Query correta: WHERE e.ativa = TRUE deve ser adicionado")
        
        # 6. RESULTADO FINAL
        print("\n" + "=" * 60)
        print("📋 ANÁLISE FINAL DO PROBLEMA")
        print("=" * 60)
        
        problemas_encontrados = []
        
        if inativas > 0:
            problemas_encontrados.append("Empresas inativas sendo retornadas pela API")
            
        if 'status-inativa' in html_content:
            problemas_encontrados.append("Empresas inativas sendo exibidas na listagem")
            
        if problemas_encontrados:
            print("❌ PROBLEMAS CONFIRMADOS:")
            for problema in problemas_encontrados:
                print(f"   - {problema}")
                
            print("\n🔧 CORREÇÃO NECESSÁRIA:")
            print("   1. Adicionar filtro WHERE e.ativa = TRUE na query de listagem")
            print("   2. Verificar se exclusão está fazendo soft delete corretamente")
            print("   3. Garantir que apenas empresas ativas sejam exibidas")
        else:
            print("✅ SISTEMA FUNCIONANDO CORRETAMENTE")
            
        return len(problemas_encontrados) == 0
            
    except Exception as e:
        print(f"❌ Erro durante o diagnóstico: {str(e)}")
        return False

if __name__ == "__main__":
    resultado = diagnosticar_exclusao_empresas()
    exit(0 if resultado else 1) 