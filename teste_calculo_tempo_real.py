#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 TESTE DE CÁLCULO EM TEMPO REAL
=================================

Registra pontos reais e verifica os cálculos imediatamente.
"""

import requests
import time
from datetime import datetime

def testar_calculo_tempo_real():
    """
    Testa cálculo registrando pontos reais e verificando resultado.
    """
    print("🔥 TESTE DE CÁLCULO EM TEMPO REAL")
    print("=" * 50)
    
    session = requests.Session()
    
    try:
        # 1. Fazer login
        login_response = session.post(
            "http://************:5000/login",
            data={'usuario': 'admin', 'senha': '@Ric6109'}
        )
        
        if login_response.status_code != 200:
            print("❌ Falha no login")
            return False
        
        print("✅ Login realizado")
        
        # 2. Registrar entrada para funcionário 44 (<PERSON><PERSON>)
        print("\n🕐 Registrando entrada...")
        entrada_response = session.post(
            "http://************:5000/registro-ponto/api/registrar-manual",
            data={
                'funcionario_id': 44,
                'tipo_registro': 'entrada_manha',
                'observacoes': 'TESTE_CALCULO_TEMPO_REAL'
            }
        )
        
        if entrada_response.status_code == 200:
            entrada_result = entrada_response.json()
            print(f"✅ Entrada registrada: {entrada_result}")
        else:
            print(f"❌ Erro na entrada: {entrada_response.status_code}")
            return False
        
        # 3. Aguardar 2 segundos
        print("⏳ Aguardando 2 segundos...")
        time.sleep(2)
        
        # 4. Registrar saída do almoço
        print("\n🕐 Registrando saída almoço...")
        saida_response = session.post(
            "http://************:5000/registro-ponto/api/registrar-manual",
            data={
                'funcionario_id': 44,
                'tipo_registro': 'saida_almoco',
                'observacoes': 'TESTE_CALCULO_TEMPO_REAL'
            }
        )
        
        if saida_response.status_code == 200:
            saida_result = saida_response.json()
            print(f"✅ Saída registrada: {saida_result}")
            
            # Extrair horas calculadas
            if 'horas_trabalhadas' in saida_result:
                horas_calculadas = saida_result['horas_trabalhadas']
                print(f"📊 Horas calculadas pelo sistema: {horas_calculadas}")
                
                # Verificar se é aproximadamente 0.0006h (2 segundos)
                if isinstance(horas_calculadas, (int, float)):
                    if 0.0005 <= horas_calculadas <= 0.001:
                        print("✅ Cálculo parece correto para 2 segundos")
                    else:
                        print(f"⚠️ Cálculo inesperado: {horas_calculadas}h para 2 segundos")
                
        else:
            print(f"❌ Erro na saída: {saida_response.status_code}")
            return False
        
        # 5. Verificar no relatório
        print("\n📊 Verificando relatório...")
        relatorio_response = session.get(
            f"http://************:5000/ponto-admin/funcionario/44/imprimir",
            params={'data_inicio': datetime.now().strftime('%Y-%m-%d'), 
                   'data_fim': datetime.now().strftime('%Y-%m-%d')}
        )
        
        if relatorio_response.status_code == 200:
            html_content = relatorio_response.text
            
            # Procurar pelo total no rodapé
            import re
            total_match = re.search(r'<span id="total-horas"[^>]*>(.*?)</span>', html_content, re.DOTALL)
            
            if total_match:
                total_content = total_match.group(1)
                total_clean = re.sub(r'<[^>]+>', '', total_content).strip()
                total_clean = re.sub(r'\s+', ' ', total_clean)
                
                print(f"📊 Total no rodapé: '{total_clean}'")
                
                # Verificar se mostra 0h 00min (correto para 2 segundos)
                if "0h 00min" in total_clean:
                    print("✅ Rodapé mostra 0h 00min (correto para 2 segundos)")
                    resultado = True
                else:
                    print(f"⚠️ Rodapé inesperado: {total_clean}")
                    resultado = False
            else:
                print("❌ Não encontrou total no rodapé")
                resultado = False
        else:
            print(f"❌ Erro no relatório: {relatorio_response.status_code}")
            resultado = False
        
        # 6. Limpar dados de teste
        print("\n🧹 Limpando dados de teste...")
        # Aqui você pode implementar limpeza se necessário
        
        return resultado
        
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        return False

def main():
    """
    Função principal.
    """
    print("🔥 TESTE DE CÁLCULO EM TEMPO REAL")
    print("=" * 50)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 50)
    
    sucesso = testar_calculo_tempo_real()
    
    print("\n" + "=" * 50)
    if sucesso:
        print("🎉 TESTE CONCLUÍDO COM SUCESSO!")
        print("✅ Cálculos funcionando corretamente")
    else:
        print("⚠️ TESTE APRESENTOU PROBLEMAS!")
        print("❌ Verificar cálculos do sistema")
    
    return sucesso

if __name__ == "__main__":
    sucesso = main()
    exit(0 if sucesso else 1)
