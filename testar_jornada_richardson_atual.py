#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar a jornada atual do Richardson após as correções
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def testar_jornada_richardson():
    """Testar a jornada atual do Richardson"""
    print("🔍 TESTANDO JORNADA ATUAL DO RICHARDSON")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Dados diretos do banco - funcionário
        print("\n1. Dados diretos do funcionário Richardson no banco...")
        sql_richardson_direto = """
        SELECT 
            f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id, f.horario_trabalho_id,
            f.turno, f.tolerancia_ponto,
            f.jornada_seg_qui_entrada, f.jornada_seg_qui_saida,
            f.jornada_sex_entrada, f.jornada_sex_saida,
            f.jornada_intervalo_entrada, f.jornada_intervalo_saida,
            e.razao_social as empresa_nome
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id
        WHERE f.id = 1
        """
        
        richardson_direto = db.execute_query(sql_richardson_direto, fetch_one=True)
        
        if richardson_direto:
            print(f"📋 Funcionário Richardson (dados diretos):")
            print(f"   Nome: {richardson_direto['nome_completo']}")
            print(f"   Empresa: {richardson_direto['empresa_nome']} (ID: {richardson_direto['empresa_id']})")
            print(f"   Jornada Trabalho ID: {richardson_direto['jornada_trabalho_id']}")
            print(f"   Horário Trabalho ID: {richardson_direto['horario_trabalho_id']}")
            print(f"   Turno Individual: {richardson_direto['turno']}")
            print(f"   Tolerância Individual: {richardson_direto['tolerancia_ponto']}")
            print(f"   Jornada Individual Seg-Qui: {richardson_direto['jornada_seg_qui_entrada']} às {richardson_direto['jornada_seg_qui_saida']}")
            print(f"   Jornada Individual Sexta: {richardson_direto['jornada_sex_entrada']} às {richardson_direto['jornada_sex_saida']}")
            print(f"   Intervalo Individual: {richardson_direto['jornada_intervalo_entrada']} às {richardson_direto['jornada_intervalo_saida']}")
        
        # 2. Dados da jornada da empresa AiNexus
        print("\n2. Dados da jornada da empresa AiNexus...")
        sql_jornada_ainexus = """
        SELECT 
            jt.id, jt.nome_jornada, jt.tipo_jornada,
            jt.seg_qui_entrada, jt.seg_qui_saida,
            jt.sexta_entrada, jt.sexta_saida,
            jt.intervalo_inicio, jt.intervalo_fim,
            jt.tolerancia_entrada_minutos, jt.padrao, jt.ativa,
            e.razao_social as empresa_nome
        FROM jornadas_trabalho jt
        JOIN empresas e ON jt.empresa_id = e.id
        WHERE jt.id = %s
        """
        
        jornada_ainexus = db.execute_query(sql_jornada_ainexus, (richardson_direto['jornada_trabalho_id'],), fetch_one=True)
        
        if jornada_ainexus:
            print(f"📋 Jornada da empresa (ID {jornada_ainexus['id']}):")
            print(f"   Nome: {jornada_ainexus['nome_jornada']}")
            print(f"   Empresa: {jornada_ainexus['empresa_nome']}")
            print(f"   Tipo: {jornada_ainexus['tipo_jornada']}")
            print(f"   Seg-Qui: {jornada_ainexus['seg_qui_entrada']} às {jornada_ainexus['seg_qui_saida']}")
            print(f"   Sexta: {jornada_ainexus['sexta_entrada']} às {jornada_ainexus['sexta_saida']}")
            print(f"   Intervalo: {jornada_ainexus['intervalo_inicio']} às {jornada_ainexus['intervalo_fim']}")
            print(f"   Tolerância: {jornada_ainexus['tolerancia_entrada_minutos']} minutos")
            print(f"   Padrão: {jornada_ainexus['padrao']}")
            print(f"   Ativa: {jornada_ainexus['ativa']}")
        
        # 3. Dados via get_with_epis (como o template recebe)
        print("\n3. Dados via get_with_epis (como o template recebe)...")
        from utils.database import FuncionarioQueries
        
        funcionario_completo = FuncionarioQueries.get_with_epis(1)
        
        if funcionario_completo:
            print(f"📋 Dados via get_with_epis:")
            print(f"   Nome: {funcionario_completo.get('nome_completo')}")
            print(f"   Empresa: {funcionario_completo.get('empresa_nome')}")
            
            # Campos que o template DEVERIA usar (corretos)
            print(f"\n   🎯 CAMPOS QUE O TEMPLATE DEVERIA USAR:")
            print(f"   tipo_jornada: {funcionario_completo.get('tipo_jornada')}")
            print(f"   tolerancia_entrada_minutos: {funcionario_completo.get('tolerancia_entrada_minutos')}")
            print(f"   jornada_seg_qui_entrada: {funcionario_completo.get('jornada_seg_qui_entrada')}")
            print(f"   jornada_seg_qui_saida: {funcionario_completo.get('jornada_seg_qui_saida')}")
            print(f"   jornada_sex_entrada: {funcionario_completo.get('jornada_sex_entrada')}")
            print(f"   jornada_sex_saida: {funcionario_completo.get('jornada_sex_saida')}")
            print(f"   jornada_intervalo_entrada: {funcionario_completo.get('jornada_intervalo_entrada')}")
            print(f"   jornada_intervalo_saida: {funcionario_completo.get('jornada_intervalo_saida')}")
            
            # Campos individuais (que NÃO deveriam ser usados)
            print(f"\n   ⚠️ CAMPOS INDIVIDUAIS (NÃO DEVERIAM SER USADOS):")
            print(f"   turno: {funcionario_completo.get('turno')}")
            print(f"   tolerancia_ponto: {funcionario_completo.get('tolerancia_ponto')}")
            
            # Verificar se há alocação
            print(f"\n   📋 INFORMAÇÕES DE ALOCAÇÃO:")
            print(f"   alocacao_id: {funcionario_completo.get('alocacao_id')}")
            print(f"   cliente_nome: {funcionario_completo.get('cliente_nome')}")
            print(f"   empresa_cliente_id: {funcionario_completo.get('empresa_cliente_id')}")
        
        # 4. Verificar se há alocações ativas
        print("\n4. Verificando alocações ativas...")
        sql_alocacoes = """
        SELECT 
            fa.id, fa.funcionario_id, fa.empresa_cliente_id, fa.jornada_trabalho_id, fa.ativo,
            ec.razao_social as cliente_nome,
            jt.nome_jornada as jornada_alocacao_nome,
            jt.tipo_jornada as jornada_alocacao_tipo,
            jt.tolerancia_entrada_minutos as jornada_alocacao_tolerancia
        FROM funcionario_alocacoes fa
        LEFT JOIN empresas ec ON fa.empresa_cliente_id = ec.id
        LEFT JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
        WHERE fa.funcionario_id = 1
        ORDER BY fa.ativo DESC, fa.id DESC
        """
        
        alocacoes = db.execute_query(sql_alocacoes)
        
        if alocacoes:
            print(f"📊 Alocações encontradas: {len(alocacoes)}")
            for alocacao in alocacoes:
                status = "ATIVA" if alocacao['ativo'] else "INATIVA"
                print(f"   - Alocação ID {alocacao['id']}: {status}")
                print(f"     Cliente: {alocacao['cliente_nome']}")
                print(f"     Jornada da Alocação: {alocacao['jornada_alocacao_nome']}")
                print(f"     Tipo: {alocacao['jornada_alocacao_tipo']}")
                print(f"     Tolerância: {alocacao['jornada_alocacao_tolerancia']}")
        else:
            print(f"📊 Nenhuma alocação encontrada")
        
        # 5. Conclusão
        print(f"\n5. CONCLUSÃO:")
        if funcionario_completo:
            if funcionario_completo.get('alocacao_id'):
                print(f"   🎯 Richardson está ALOCADO - deveria usar jornada da alocação")
                print(f"   📋 Jornada da Alocação: {funcionario_completo.get('nome_jornada')}")
            else:
                print(f"   🎯 Richardson NÃO está alocado - deveria usar jornada da empresa")
                print(f"   📋 Jornada da Empresa: {funcionario_completo.get('nome_jornada')}")
            
            print(f"\n   ✅ VALORES CORRETOS PARA O TEMPLATE:")
            print(f"   Turno: {funcionario_completo.get('tipo_jornada')}")
            print(f"   Tolerância: {funcionario_completo.get('tolerancia_entrada_minutos')} minutos")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    testar_jornada_richardson()
