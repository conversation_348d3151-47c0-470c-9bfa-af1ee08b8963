{% extends "base.html" %}

{% block title %}{{ funcionario.nome_completo }} - Detal<PERSON> de Ponto{% endblock %}

{% block extra_css %}
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <style>
        /* ========================================
           RLPONTO-WEB VISUAL IDENTITY - v1.0
           SEGUINDO ESPECIFICAÇÕES DO VISUAL.MD
           ======================================== */

        :root {
            /* Cor primária - Verde-azulado (igual sidebar) */
            --primary-color: #4fbdba;
            --primary-hover: #26a69a;
            --primary-light: #80cbc4;
            --primary-dark: #00695c;

            /* Backgrounds */
            --background-color: #f9fafb;        /* Background principal */
            --card-background: #ffffff;         /* Fundo de cards */
            --hover-bg: #f3f4f6;               /* Hover states */
            --sidebar-bg: #ffffff;             /* Sidebar background */

            /* Textos */
            --text-primary: #1f2937;           /* Texto principal (preto) */
            --text-secondary: #6b7280;         /* Texto secundário (cinza médio) */
            --text-muted: #9ca3af;             /* Texto desabilitado */
            --text-white: #ffffff;             /* Texto branco */

            /* Bordas e divisores */
            --border-color: #e5e7eb;           /* Bordas padrão */
            --border-light: #f3f4f6;           /* Bordas claras */
            --border-dark: #d1d5db;            /* Bordas escuras */

            /* Estados */
            --success-color: #10b981;          /* Verde sucesso */
            --success-bg: #dcfce7;             /* Background sucesso */
            --success-text: #166534;           /* Texto sucesso */

            --warning-color: #f59e0b;          /* Amarelo aviso */
            --warning-bg: #fef3c7;             /* Background aviso */
            --warning-text: #92400e;           /* Texto aviso */

            --danger-color: #ef4444;           /* Vermelho erro */
            --danger-bg: #fee2e2;              /* Background erro */
            --danger-text: #dc2626;            /* Texto erro */

            --info-color: #3b82f6;             /* Azul informação */
            --info-bg: #dbeafe;                /* Background info */
            --info-text: #1e40af;              /* Texto info */

            /* Tamanhos de fonte */
            --font-size-xs: 0.75rem;      /* 12px - Textos muito pequenos */
            --font-size-sm: 0.875rem;     /* 14px - Textos pequenos */
            --font-size-base: 1rem;       /* 16px - Texto base */
            --font-size-lg: 1.125rem;     /* 18px - Textos grandes */
            --font-size-xl: 1.25rem;      /* 20px - Subtítulos */
            --font-size-2xl: 1.5rem;      /* 24px - Títulos */
            --font-size-3xl: 1.875rem;    /* 30px - Títulos principais */

            /* Espaçamentos */
            --spacing-xs: 0.25rem;    /* 4px */
            --spacing-sm: 0.5rem;     /* 8px */
            --spacing-md: 1rem;       /* 16px */
            --spacing-lg: 1.5rem;     /* 24px */
            --spacing-xl: 2rem;       /* 32px */
            --spacing-2xl: 3rem;      /* 48px */

            /* Border radius */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
        }

        /* ========================================
           GLOBAL STYLES - PADRÃO RLPONTO-WEB
           ======================================== */

        body {
            background-color: var(--background-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-primary);
            line-height: 1.5;
        }

        .main-container {
            background-color: var(--background-color);
            min-height: 100vh;
            padding: 2rem;
        }

        /* ========================================
           HEADER PADRÃO - SEGUINDO VISUAL.MD
           ======================================== */

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            border-radius: var(--radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            color: var(--text-white);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 60%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: rotate(15deg);
        }

        .page-header h1 {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            position: relative;
            z-index: 2;
        }

        .page-header p {
            font-size: var(--font-size-lg);
            margin: 0;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        /* ========================================
           CARDS PADRÃO - SEGUINDO VISUAL.MD
           ======================================== */

        .info-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border-color: var(--border-dark);
        }

        .info-card h5 {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid var(--border-light);
        }

        .info-card h5 i {
            color: var(--primary-color);
            font-size: 1.25rem;
        }

        /* ========================================
           TABELA PADRÃO - SEGUINDO VISUAL.MD
           ======================================== */

        .registros-table {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            margin-bottom: var(--spacing-xl);
        }

        .registros-table .card-header {
            background: var(--hover-bg);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-lg);
        }

        .registros-table .card-header h5 {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        /* ========================================
           TABELA - ESTILOS PADRÃO RLPONTO-WEB
           ======================================== */

        .table {
            margin: 0;
        }

        .table th {
            background-color: var(--hover-bg);
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: var(--font-size-xs);
            letter-spacing: 0.5px;
            padding: var(--spacing-md);
            color: var(--text-secondary);
        }

        .table td {
            border: none;
            padding: var(--spacing-md);
            vertical-align: middle;
            border-bottom: 1px solid var(--border-light);
        }

        .table tbody tr:hover {
            background-color: var(--hover-bg);
        }

        /* ========================================
           HORÁRIOS - CÉLULAS INTERATIVAS
           ======================================== */

        .horario-cell {
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: var(--radius-sm);
            padding: 0.25rem 0.5rem;
        }

        .horario-cell:hover {
            background-color: var(--hover-bg);
        }

        .horario-editavel {
            background-color: var(--warning-bg);
            border: 1px dashed var(--warning-color);
        }

        /* ========================================
           BADGES DE STATUS - PADRÃO RLPONTO-WEB
           ======================================== */

        .badge-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: var(--font-size-xs);
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-success {
            background: var(--success-bg);
            color: var(--success-text);
            border: 1px solid var(--success-color);
        }

        .status-warning {
            background: var(--warning-bg);
            color: var(--warning-text);
            border: 1px solid var(--warning-color);
        }

        .status-danger {
            background: var(--danger-bg);
            color: var(--danger-text);
            border: 1px solid var(--danger-color);
        }

        .status-info {
            background: var(--info-bg);
            color: var(--info-text);
            border: 1px solid var(--info-color);
        }

        /* ========================================
           MODAIS - PADRÃO RLPONTO-WEB
           ======================================== */

        .modal-content {
            border-radius: var(--radius-lg);
            border: none;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: var(--text-white);
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
            padding: var(--spacing-lg);
        }

        .modal-title {
            font-weight: 600;
            font-size: var(--font-size-xl);
            margin: 0;
        }

        .modal-body {
            padding: var(--spacing-lg);
        }

        .modal-footer {
            padding: var(--spacing-lg);
            border-top: 1px solid var(--border-color);
        }
        }

        /* ========================================
           FORMULÁRIOS - PADRÃO RLPONTO-WEB
           ======================================== */

        .form-control {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            padding: 0.5rem 0.75rem;
            background: var(--card-background);
            color: var(--text-primary);
            font-size: var(--font-size-base);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
            outline: none;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            margin-bottom: 0.5rem;
            display: block;
        }

        /* ========================================
           TIMELINE - LOGS DE ATIVIDADES
           ======================================== */

        .logs-timeline {
            max-height: 400px;
            overflow-y: auto;
        }

        .timeline-item {
            border-left: 2px solid var(--border-color);
            padding-left: 1rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--primary-color);
        }

        /* ========================================
           UPLOAD AREA - DRAG & DROP
           ======================================== */

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-md);
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background-color: rgba(79, 189, 186, 0.05);
        }

        .upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(79, 189, 186, 0.1);
        }

        /* ========================================
           BOTÕES - PADRÃO RLPONTO-WEB
           ======================================== */

        .btn-primary {
            background: var(--primary-color);
            border: 1px solid var(--primary-color);
            color: var(--text-white);
            border-radius: var(--radius-md);
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: var(--radius-md);
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: var(--hover-bg);
            border-color: var(--border-dark);
        }

        .btn-light {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: var(--radius-md);
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-light:hover {
            background: var(--hover-bg);
            border-color: var(--border-dark);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
        }

        /* ========================================
           RESPONSIVIDADE - MOBILE FIRST
           ======================================== */

        @media (max-width: 767px) {
            .main-container {
                padding: 1rem;
            }

            .page-header {
                padding: 1.5rem;
                text-align: center;
            }

            .page-header h1 {
                font-size: var(--font-size-2xl);
            }

            .info-card {
                margin-bottom: 1rem;
            }

            .table th,
            .table td {
                padding: 0.5rem;
                font-size: var(--font-size-sm);
            }
        }

        @media (min-width: 768px) and (max-width: 1023px) {
            .main-container {
                padding: 1.5rem;
            }
        }

        /* ========================================
           FINALIZAÇÕES - PADRÃO RLPONTO-WEB
           ======================================== */

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Garantir que os ícones tenham a cor primária */
        .fa-user, .fa-clock, .fa-building, .fa-chart-bar {
            color: var(--primary-color);
        }

        /* Melhorar legibilidade dos textos */
        .text-muted {
            color: var(--text-muted) !important;
        }

        .text-primary {
            color: var(--text-primary) !important;
        }

        .text-secondary {
            color: var(--text-secondary) !important;
        }

        .btn-light:hover {
            background: rgba(255, 255, 255, 0.3);
            color: var(--text-white);
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            border: 1px solid var(--primary-color);
            color: var(--text-white);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            color: var(--text-white);
        }

        .btn-sm {
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-xs);
        }

        /* ========================================
           BADGES MODERNOS
           ======================================== */
        .badge {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-success {
            background: var(--success-bg);
            color: var(--success-text);
            border: 1px solid var(--success-color);
        }

        .badge-warning {
            background: var(--warning-bg);
            color: var(--warning-text);
            border: 1px solid var(--warning-color);
        }

        .badge-danger {
            background: var(--danger-bg);
            color: var(--danger-text);
            border: 1px solid var(--danger-color);
        }

        .badge-info {
            background: var(--info-bg);
            color: var(--info-text);
            border: 1px solid var(--info-color);
        }

        /* ========================================
           HISTÓRICO DE ATIVIDADES MODERNO
           ======================================== */
        .historico-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .historico-card h5 {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid var(--border-light);
        }

        .logs-timeline {
            max-height: 400px;
            overflow-y: auto;
            padding-right: var(--spacing-sm);
        }

        .logs-timeline::-webkit-scrollbar {
            width: 6px;
        }

        .logs-timeline::-webkit-scrollbar-track {
            background: var(--border-light);
            border-radius: 3px;
        }

        .logs-timeline::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .logs-timeline::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        /* ========================================
           RESPONSIVIDADE APRIMORADA
           ======================================== */
        @media (max-width: 1200px) {
            .container-fluid {
                padding: var(--spacing-lg);
            }

            .display-6 {
                font-size: 2rem;
            }
        }

        @media (max-width: 992px) {
            .page-header {
                padding: var(--spacing-lg);
            }

            .page-header h1 {
                font-size: var(--font-size-2xl);
            }

            .info-card {
                margin-bottom: var(--spacing-md);
            }
        }

        @media (max-width: 768px) {
            .container-fluid {
                padding: var(--spacing-md);
            }

            .page-header {
                text-align: center;
                padding: var(--spacing-md);
            }

            .page-header .row {
                flex-direction: column;
                gap: var(--spacing-md);
            }

            .page-header .col-md-4 {
                text-align: center;
            }

            .btn {
                font-size: var(--font-size-sm);
                padding: var(--spacing-sm) var(--spacing-md);
            }

            .table-responsive {
                font-size: var(--font-size-sm);
            }

            .display-6 {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 576px) {
            .page-header h1 {
                font-size: var(--font-size-xl);
            }

            .info-card {
                padding: var(--spacing-md);
            }

            .table th,
            .table td {
                padding: var(--spacing-sm);
                font-size: var(--font-size-xs);
            }

            .btn {
                width: 100%;
                margin-bottom: var(--spacing-sm);
            }

            .btn:last-child {
                margin-bottom: 0;
            }
        }

        /* ========================================
           ANIMAÇÕES E TRANSIÇÕES
           ======================================== */
        .info-card,
        .registros-table,
        .historico-card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .table tbody tr {
            transition: background-color 0.2s ease;
        }

        .horario-cell {
            transition: all 0.2s ease;
        }

        .badge {
            transition: all 0.2s ease;
        }

        /* ========================================
           CONTROLES DA TABELA MODERNIZADOS
           ======================================== */
        .table-controls-container {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
        }

        /* Seção de Filtro de Período */
        .date-filter-section {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .date-filter-header {
            display: flex;
            align-items: center;
            color: var(--text-primary);
            font-weight: 600;
            font-size: var(--font-size-base);
        }

        .filter-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .date-range-inputs {
            display: flex;
            align-items: end;
            gap: var(--spacing-md);
            flex-wrap: wrap;
        }

        .date-input-group {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            min-width: 140px;
        }

        .date-input-label {
            font-size: var(--font-size-xs);
            color: var(--text-muted);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .date-input {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 0.5rem 0.75rem;
            background: var(--card-background);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            transition: all 0.3s ease;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .date-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
            outline: none;
            transform: translateY(-1px);
        }

        .date-separator {
            color: var(--primary-color);
            font-size: 1rem;
            opacity: 0.7;
            margin-bottom: 0.5rem;
        }

        /* Botões de Período Rápido */
        .quick-period-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .quick-btn {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            border-radius: var(--radius-sm);
            padding: 0.375rem 0.75rem;
            font-size: var(--font-size-xs);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .quick-btn:hover,
        .quick-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--text-white);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px -1px rgba(79, 189, 186, 0.3);
        }

        /* Seção de Botões de Ação */
        .action-buttons-section {
            display: flex;
            gap: var(--spacing-sm);
            flex-wrap: wrap;
            justify-content: flex-end;
        }

        .btn-action {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            font-weight: 500;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            white-space: nowrap;
            min-width: fit-content;
        }

        .btn-print {
            background: var(--primary-color);
            color: var(--text-white);
            box-shadow: 0 2px 4px -1px rgba(79, 189, 186, 0.3);
        }

        .btn-print:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(79, 189, 186, 0.4);
        }

        .btn-export {
            background: var(--success-color);
            color: var(--text-white);
            box-shadow: 0 2px 4px -1px rgba(16, 185, 129, 0.3);
        }

        .btn-export:hover {
            background: #059669;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.4);
        }

        .btn-filter {
            background: var(--info-color);
            color: var(--text-white);
            box-shadow: 0 2px 4px -1px rgba(59, 130, 246, 0.3);
        }

        .btn-filter:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.4);
        }

        /* Responsividade dos Controles Modernos */
        @media (max-width: 768px) {
            .table-controls-container {
                padding: var(--spacing-md);
                gap: var(--spacing-md);
            }

            .date-range-inputs {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-sm);
            }

            .date-input-group {
                min-width: 100%;
            }

            .date-separator {
                transform: rotate(90deg);
                align-self: center;
                margin: 0.25rem 0;
            }

            .quick-period-buttons {
                justify-content: center;
            }

            .action-buttons-section {
                justify-content: center;
                flex-direction: column;
            }

            .btn-action {
                width: 100%;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .quick-period-buttons {
                flex-direction: column;
            }

            .quick-btn {
                width: 100%;
                text-align: center;
            }
        }

        /* ========================================
           MELHORIAS DE ACESSIBILIDADE
           ======================================== */
        .btn:focus,
        .form-control:focus,
        .date-input:focus,
        .quick-btn:focus,
        .btn-action:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .table th {
            position: sticky;
            top: 0;
            z-index: 10;
        }
    </style>
{% endblock %}

{% block content %}
    <!-- Container Principal Moderno -->
    <div class="container-fluid">
        <!-- Header Moderno - Inspirado Shadcn UI -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-user me-3"></i>
                        {{ funcionario.nome_completo }}
                    </h1>
                    <p class="mb-0 opacity-90">
                        {{ funcionario.cargo }} - {{ funcionario.empresa_nome }}
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light me-2" onclick="window.history.back()">
                        <i class="fas fa-arrow-left me-2"></i>Voltar
                    </button>
                    <button class="btn btn-light" onclick="gerarRelatorio()">
                        <i class="fas fa-file-pdf me-2"></i>Relatório
                    </button>
                </div>
            </div>
        </div>

        <!-- Cards de Informações Modernos -->
        <div class="row mb-4">
            <!-- Informações Pessoais -->
            <div class="col-lg-8">
                <div class="info-card">
                    <h5>
                        <i class="fas fa-user-circle text-primary"></i>
                        Informações Pessoais
                    </h5>
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted small">CPF</label>
                                <div class="fw-medium">{{ funcionario.cpf or 'Não informado' }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted small">Setor</label>
                                <div class="fw-medium">{{ funcionario.setor or 'Não informado' }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted small">Status</label>
                                <div>
                                    {% if funcionario.status_cadastro == 'Ativo' %}
                                        <span class="badge badge-success">
                                            <i class="fas fa-check-circle me-1"></i>{{ funcionario.status_cadastro }}
                                        </span>
                                    {% else %}
                                        <span class="badge badge-danger">
                                            <i class="fas fa-times-circle me-1"></i>{{ funcionario.status_cadastro }}
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted small">Data de Admissão</label>
                                <div class="fw-medium">
                                    {% if funcionario.data_admissao %}
                                        {{ funcionario.data_admissao.strftime('%d/%m/%Y') }}
                                    {% else %}
                                        Não informado
                                    {% endif %}
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted small">Jornada de Trabalho</label>
                                <div class="fw-medium">{{ funcionario.nome_jornada or 'Não definida' }}</div>
                            </div>
                            {% if funcionario.seg_qui_entrada and funcionario.seg_qui_saida %}
                            <div class="mb-3">
                                <label class="form-label text-muted small">Horário de Trabalho</label>
                                <div class="fw-medium">
                                    <i class="fas fa-clock text-primary me-1"></i>
                                    {{ funcionario.seg_qui_entrada.strftime('%H:%M') }} às
                                    {{ funcionario.seg_qui_saida.strftime('%H:%M') }}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resumo Estatístico -->
            <div class="col-lg-4">
                <div class="info-card">
                    <h5>
                        <i class="fas fa-chart-bar text-primary"></i>
                        Resumo do Período
                    </h5>
                    <div class="text-center">
                        <div class="mb-4">
                            <div class="display-6 fw-bold text-primary mb-1">{{ registros|length }}</div>
                            <small class="text-muted text-uppercase">Registros de Ponto</small>
                        </div>
                        <div class="mb-4">
                            <div class="display-6 fw-bold text-success mb-1">
                                {% set total_horas = 0 %}
                                {% for registro in registros %}
                                    {% if registro.total_horas_decimal %}
                                        {% set total_horas = total_horas + registro.total_horas_decimal %}
                                    {% endif %}
                                {% endfor %}
                                {{ "%.1f"|format(total_horas) }}h
                            </div>
                            <small class="text-muted text-uppercase">Total de Horas</small>
                        </div>
                        <div class="mb-0">
                            <div class="display-6 fw-bold text-warning mb-1">
                                {% set presentes = registros|selectattr('status_trabalho', 'equalto', 'PRESENTE')|list|length %}
                                {{ presentes }}
                            </div>
                            <small class="text-muted text-uppercase">Dias Presentes</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtros de Data -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Data Início</label>
                        <input type="date" class="form-control" id="dataInicio" 
                               value="{{ (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Data Fim</label>
                        <input type="date" class="form-control" id="dataFim" 
                               value="{{ datetime.now().strftime('%Y-%m-%d') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-primary" onclick="filtrarRegistros()">
                                <i class="fas fa-search me-2"></i>Filtrar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Histórico de Alocações -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    Histórico de Alocações em Clientes/Obras
                </h5>
            </div>
            <div class="card-body">
                {% if historico_alocacoes %}
                    <div class="row">
                        {% for alocacao in historico_alocacoes %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-start border-primary border-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="card-title mb-1">{{ alocacao.cliente_nome }}</h6>
                                            <small class="text-muted">{{ alocacao.cliente_fantasia }}</small>
                                        </div>
                                        <span class="badge {{ 'bg-success' if alocacao.ativo else 'bg-secondary' }}">
                                            {{ 'Ativo' if alocacao.ativo else 'Finalizado' }}
                                        </span>
                                    </div>
                                    <hr class="my-2">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="h6 text-primary mb-0">{{ alocacao.dias_trabalhados or 0 }}</div>
                                            <small class="text-muted">Dias</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h6 text-success mb-0">{{ "%.1f"|format(alocacao.total_horas_periodo or 0) }}h</div>
                                            <small class="text-muted">Horas</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h6 text-warning mb-0">{{ alocacao.faltas or 0 }}</div>
                                            <small class="text-muted">Faltas</small>
                                        </div>
                                    </div>
                                    <hr class="my-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ alocacao.data_inicio.strftime('%d/%m/%Y') }} -
                                        {{ alocacao.data_fim.strftime('%d/%m/%Y') if alocacao.data_fim else 'Atual' }}
                                    </small>
                                    {% if alocacao.nome_jornada %}
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ alocacao.nome_jornada }}
                                        ({{ alocacao.seg_qui_entrada.strftime('%H:%M') if alocacao.seg_qui_entrada else '' }} -
                                         {{ alocacao.seg_qui_saida.strftime('%H:%M') if alocacao.seg_qui_saida else '' }})
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p>Nenhuma alocação encontrada para este funcionário.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Tabela de Registros Moderna -->
        <div class="registros-table">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock me-2"></i>
                        <h5 class="mb-0">Registros de Ponto com Histórico de Alocações</h5>
                    </div>

                    <!-- Controles da Tabela Modernizados -->
                    <div class="table-controls-container">
                        <!-- Filtro de Período -->
                        <div class="date-filter-section">
                            <div class="date-filter-header">
                                <i class="fas fa-calendar-alt me-2"></i>
                                <span class="filter-label">Período:</span>
                            </div>
                            <div class="date-range-inputs">
                                <div class="date-input-group">
                                    <label class="date-input-label">De:</label>
                                    <input type="date"
                                           class="form-control date-input"
                                           id="dataInicio"
                                           value="{{ (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d') }}">
                                </div>
                                <div class="date-separator">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                                <div class="date-input-group">
                                    <label class="date-input-label">Até:</label>
                                    <input type="date"
                                           class="form-control date-input"
                                           id="dataFim"
                                           value="{{ datetime.now().strftime('%Y-%m-%d') }}">
                                </div>
                            </div>
                            <!-- Botões de Período Rápido -->
                            <div class="quick-period-buttons">
                                <button type="button" class="quick-btn" onclick="setPeriodoRapido('hoje')">Hoje</button>
                                <button type="button" class="quick-btn" onclick="setPeriodoRapido('semana')">7 dias</button>
                                <button type="button" class="quick-btn" onclick="setPeriodoRapido('mes')">30 dias</button>
                                <button type="button" class="quick-btn" onclick="setPeriodoRapido('todos')">Todos</button>
                            </div>
                        </div>

                        <!-- Botões de Ação -->
                        <div class="action-buttons-section">
                            <button class="btn-action btn-print" onclick="imprimirPonto()" title="Imprimir registros filtrados">
                                <i class="fas fa-print"></i>
                                <span>Imprimir Ponto</span>
                            </button>
                            <button class="btn-action btn-export" onclick="exportarExcel()" title="Exportar para Excel">
                                <i class="fas fa-file-excel"></i>
                                <span>Excel</span>
                            </button>
                            <button class="btn-action btn-filter" onclick="aplicarFiltro()" title="Aplicar filtro de período">
                                <i class="fas fa-filter"></i>
                                <span>Filtrar</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover" id="registrosTable">
                    <thead>
                        <tr>
                            <th>Data</th>
                            <th>Dia</th>
                            <th>Cliente/Obra</th>
                            <th>Entrada</th>
                            <th>Saída Almoço</th>
                            <th>Retorno Almoço</th>
                            <th>Saída</th>
                            <th>Total Horas</th>
                            <th>Status</th>
                            <th>Editar</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for registro in registros %}
                        <tr data-registro-id="{{ registro.id }}">
                            <td>{{ registro.data.strftime('%d/%m/%Y') }}</td>
                            <td>{{ registro.dia_semana }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if registro.status_trabalho == 'ALOCADO' %}
                                        <span class="badge bg-primary me-2">Cliente</span>
                                        <div>
                                            <div class="fw-semibold">{{ registro.cliente_nome }}</div>
                                            {% if registro.cliente_fantasia and registro.cliente_fantasia != registro.cliente_nome %}
                                                <small class="text-muted">{{ registro.cliente_fantasia }}</small>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <span class="badge bg-secondary me-2">Sede</span>
                                        <span>Empresa Principal</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'entrada', '{{ registro.entrada or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.entrada.strftime('%H:%M') if registro.entrada else '-' }}
                                </span>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'saida_almoco', '{{ registro.saida_almoco or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.saida_almoco.strftime('%H:%M') if registro.saida_almoco else '-' }}
                                </span>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'retorno_almoco', '{{ registro.retorno_almoco or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.retorno_almoco.strftime('%H:%M') if registro.retorno_almoco else '-' }}
                                </span>
                            </td>
                            <td>
                                <span class="horario-cell"
                                      onclick="editarHorario({{ registro.id }}, 'saida', '{{ registro.saida or '' }}')"
                                      title="Clique para editar">
                                    {{ registro.saida.strftime('%H:%M') if registro.saida else '-' }}
                                </span>
                            </td>
                            <td>
                                <strong class="text-primary">{{ "%.2f"|format(registro.total_horas_dia) }}h</strong>
                            </td>
                            <td>
                                {% if registro.entrada %}
                                    <span class="badge bg-success">Presente</span>
                                {% else %}
                                    <span class="badge bg-danger">Falta</span>
                                {% endif %}
                                {% if registro.justificativa %}
                                    <span class="badge bg-info ms-1" title="{{ registro.justificativa }}">Justificado</span>
                                {% endif %}
                                {% if registro.editado_por %}
                                    <span class="badge bg-warning ms-1">Editado</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-primary"
                                        onclick="abrirModalEdicao('{{ registro.data.strftime('%Y-%m-%d') }}', {{ registro.id }})"
                                        title="Editar registro e justificativas">
                                    <i class="fas fa-edit me-1"></i>
                                    Editar
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Histórico de Atividades Moderno -->
        <div class="historico-card mt-4">
            <h5>
                <i class="fas fa-history text-primary"></i>
                Histórico de Atividades
            </h5>
            <div class="logs-timeline">
                {% if logs %}
                    {% for log in logs %}
                    <div class="timeline-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="fw-semibold text-primary">
                                {{ log.acao.replace('_', ' ').title() }}
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ log.data_acao.strftime('%d/%m/%Y %H:%M') }}
                            </small>
                        </div>
                        <p class="mb-2 text-dark">{{ log.detalhes }}</p>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user text-muted me-1"></i>
                            <small class="text-muted">{{ log.usuario_nome or 'Sistema Automático' }}</small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-history text-muted mb-3" style="font-size: 3rem;"></i>
                        <p class="text-muted">Nenhuma atividade registrada ainda.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div> <!-- Fechamento do container-fluid -->
    </div>

    <!-- Modal Editar Horário -->
    <div class="modal fade" id="modalEditarHorario" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        Editar Horário
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="formEditarHorario">
                        <input type="hidden" id="registroId">
                        <input type="hidden" id="campoHorario">
                        
                        <div class="mb-3">
                            <label class="form-label">Novo Horário</label>
                            <input type="time" class="form-control" id="novoHorario" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Justificativa (obrigatória)</label>
                            <textarea class="form-control" id="justificativa" rows="3" 
                                      placeholder="Descreva o motivo da alteração..." required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="salvarEdicao()">
                        <i class="fas fa-save me-2"></i>Salvar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Edição de Registro de Ponto - MODERNIZADO RLPONTO-WEB -->
    <div class="modal fade" id="modalEdicaoRegistro" tabindex="-1" aria-labelledby="modalEdicaoRegistroLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <!-- Header Modernizado -->
                <div class="modal-header">
                    <h5 class="modal-title" id="modalEdicaoRegistroLabel">
                        <i class="fas fa-edit me-2"></i>
                        Editar Registro de Ponto
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <form id="formEdicaoRegistro">
                        <input type="hidden" id="registroData" name="data_registro">
                        <input type="hidden" id="funcionarioId" name="funcionario_id" value="{{ funcionario.id }}">

                        <!-- Card de Informações do Funcionário -->
                        <div class="info-card mb-4">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user me-3"></i>
                                        <div>
                                            <div class="text-muted small">Funcionário</div>
                                            <div class="fw-semibold">{{ funcionario.nome_completo }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-id-badge me-3"></i>
                                        <div>
                                            <div class="text-muted small">Matrícula</div>
                                            <div class="fw-semibold">{{ funcionario.matricula_empresa or '001' }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-calendar me-3"></i>
                                        <div>
                                            <div class="text-muted small">Data</div>
                                            <div class="fw-semibold" id="dataRegistroDisplay">11/07/2025</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Card de Registro de Ponto Atual -->
                        <div class="info-card mb-4">
                            <h5>
                                <i class="fas fa-clock"></i>
                                Registro de Ponto
                            </h5>

                            <!-- Badges de Status -->
                            <div class="d-flex align-items-center justify-content-between mb-4">
                                <div class="d-flex gap-2">
                                    <span id="diaSemanaBadge" class="badge-status status-info">QUI</span>
                                    <span id="statusBadge" class="badge-status status-success">PRESENTE</span>
                                </div>
                                <div>
                                    <span id="empresaBadge" class="badge-status status-info">EMPRESA PRINCIPAL</span>
                                </div>
                            </div>

                            <!-- Horários em Grid -->
                            <div class="row text-center mb-4" id="registroAtualDisplay">
                                <div class="col-md-2 col-6 mb-3">
                                    <div class="p-3 border rounded">
                                        <div class="text-muted small mb-2">Entrada</div>
                                        <div class="fw-bold h4" id="entradaDisplay">08:06</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-6 mb-3">
                                    <div class="p-3 border rounded">
                                        <div class="text-muted small mb-2">Intervalo</div>
                                        <div class="fw-bold h4" id="saidaAlmocoDisplay">12:12</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-6 mb-3">
                                    <div class="p-3 border rounded">
                                        <div class="text-muted small mb-2">Retorno</div>
                                        <div class="fw-bold h4" id="retornoAlmocoDisplay">14:06</div>
                                    </div>
                                </div>
                                <div class="col-md-2 col-6 mb-3">
                                    <div class="p-3 border rounded">
                                        <div class="text-muted small mb-2">Saída</div>
                                        <div class="fw-bold h4" id="saidaDisplay">--:--</div>
                                    </div>
                                </div>
                                <div class="col-md-4 col-12 mb-3">
                                    <div class="p-3 border rounded" style="background-color: var(--hover-bg);">
                                        <div class="text-muted small mb-2">Total</div>
                                        <div class="fw-bold h3" style="color: var(--primary-color);" id="totalHorasDisplay">8.0h</div>
                                        <div class="text-muted small" id="horasTrabalhadasDisplay">Trabalhadas: 4h 6min</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Área de Justificativa -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-3">
                                    <span id="justificadoBadge" class="badge-status status-warning me-3">SAÍDA ANTECIPADA</span>
                                    <span class="fw-semibold">Justificativa:</span>
                                </div>
                                <div class="border rounded p-3 position-relative" style="min-height: 80px; background-color: var(--card-background);">
                                    <div class="text-muted" id="areaJustificativa">
                                        Aqui aparece a justificativa dada no momento da batida do ponto antecipado.
                                    </div>
                                    <!-- Botão posicionado dentro da área de justificativa -->
                                    <div class="position-absolute" style="bottom: 15px; right: 15px;">
                                        <button class="btn-primary" id="btnEnviarDocumento" style="padding: 0.5rem 1rem; font-size: 0.875rem;">
                                            <i class="fas fa-upload me-2"></i>
                                            Enviar Documentos
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Card de Edição de Horários -->
                        <div class="info-card mb-4">
                            <h5>
                                <i class="fas fa-edit"></i>
                                Editar Horários
                            </h5>

                            <!-- Grid de Horários -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-3 col-6">
                                    <label for="entrada" class="form-label">Entrada</label>
                                    <input type="time" class="form-control text-center fw-semibold"
                                           id="entrada" name="entrada" style="font-size: 1.1rem; padding: 0.75rem;">
                                </div>
                                <div class="col-md-3 col-6">
                                    <label for="saida_almoco" class="form-label">Intervalo</label>
                                    <input type="time" class="form-control text-center fw-semibold"
                                           id="saida_almoco" name="saida_almoco" style="font-size: 1.1rem; padding: 0.75rem;">
                                </div>
                                <div class="col-md-3 col-6">
                                    <label for="retorno_almoco" class="form-label">Retorno</label>
                                    <input type="time" class="form-control text-center fw-semibold"
                                           id="retorno_almoco" name="retorno_almoco" style="font-size: 1.1rem; padding: 0.75rem;">
                                </div>
                                <div class="col-md-3 col-6">
                                    <label for="saida" class="form-label">Saída</label>
                                    <input type="time" class="form-control text-center fw-semibold"
                                           id="saida" name="saida" style="font-size: 1.1rem; padding: 0.75rem;">
                                </div>
                            </div>

                            <!-- Configurações -->
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="metodo_registro" class="form-label">Método de Registro</label>
                                    <select class="form-control" id="metodo_registro" name="metodo_registro">
                                        <option value="biometrico">Biométrico</option>
                                        <option value="manual">Manual</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="status_pontualidade" class="form-label">Status Pontualidade</label>
                                    <select class="form-control" id="status_pontualidade" name="status_pontualidade">
                                        <option value="Pontual">Pontual</option>
                                        <option value="Atrasado">Atrasado</option>
                                        <option value="Saída Antecipada">Saída Antecipada</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Card de Justificativa -->
                        <div class="info-card mb-4" id="cardJustificativa" style="display: none; border-left: 4px solid var(--warning-color);">
                            <h5 style="color: var(--warning-color);">
                                <i class="fas fa-exclamation-triangle"></i>
                                Justificativa Existente
                            </h5>

                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <div class="text-muted small">Tipo</div>
                                    <div class="fw-semibold" id="tipoJustificativaDisplay">-</div>
                                </div>
                                <div class="col-md-6">
                                    <div class="text-muted small">Status</div>
                                    <div><span id="statusJustificativaDisplay" class="badge-status status-warning">Pendente</span></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="text-muted small">Motivo</div>
                                <div class="p-3 rounded border" style="background-color: var(--card-background);" id="motivoJustificativaDisplay">-</div>
                            </div>
                            <div id="documentoJustificativa" style="display: none;">
                                <div class="text-muted small mb-2">Documento Anexado</div>
                                <a href="#" id="linkDocumentoJustificativa" target="_blank" class="btn-secondary">
                                    <i class="fas fa-file-pdf me-1"></i>Ver Documento
                                </a>
                            </div>
                        </div>

                        <!-- Card de Aprovação -->
                        <div class="info-card mb-4" id="cardAprovacao" style="display: none; border-left: 4px solid var(--success-color);">
                            <h5 style="color: var(--success-color);">
                                <i class="fas fa-check-circle"></i>
                                Aprovação da Justificativa
                            </h5>

                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="status_aprovacao" class="form-label">Status da Aprovação</label>
                                    <select class="form-control" id="status_aprovacao" name="status_aprovacao">
                                        <option value="pendente">Pendente</option>
                                        <option value="aprovada">Aprovada</option>
                                        <option value="reprovada">Reprovada</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="data_aprovacao" class="form-label">Data de Aprovação</label>
                                    <input type="datetime-local" class="form-control" id="data_aprovacao" name="data_aprovacao">
                                </div>
                            </div>
                            <div>
                                <label for="observacoes_aprovador" class="form-label">Observações do Aprovador</label>
                                <textarea class="form-control" id="observacoes_aprovador" name="observacoes_aprovador" rows="3"
                                          placeholder="Observações sobre a aprovação/reprovação..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Footer Modernizado -->
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn-primary" onclick="salvarRegistro()">
                        <i class="fas fa-save me-2"></i>Salvar Alterações
                    </button>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_js %}
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // Variáveis globais
        let registroAtual = null;
        let justificativaAtual = null;

        // ✅ CORREÇÃO: Função para carregar dados do registro atual automaticamente
        function carregarRegistroAtual() {
            console.log('=== CARREGANDO REGISTRO ATUAL ===');

            // Obter data atual no formato YYYY-MM-DD
            const hoje = new Date();
            const dataAtual = hoje.toISOString().split('T')[0];

            console.log('Data atual:', dataAtual);

            // Buscar dados do registro de hoje
            const funcionarioId = {{ funcionario.id }};
            const url = `/ponto-admin/api/registro-detalhes/${funcionarioId}/${dataAtual}`;

            console.log('Buscando registro atual em:', url);

            fetch(url, {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(data => {
                console.log('✅ Dados do registro atual recebidos:', data);

                if (data && data.success && data.registro) {
                    // Atualizar display com dados reais
                    preencherDisplayRegistro(data.registro);

                    // Preencher campos de edição também
                    preencherCamposEdicao(data.registro);

                    // Preencher justificativa se existir
                    if (data.justificativa) {
                        preencherJustificativa(data.justificativa);
                    }
                } else {
                    console.log('ℹ️ Nenhum registro encontrado para hoje');

                    // ✅ CORREÇÃO: Limpar campos quando não há dados
                    const registroVazio = {
                        entrada: null,
                        saida_almoco: null,
                        retorno_almoco: null,
                        saida: null,
                        metodo_registro: null,
                        status_pontualidade: 'Pontual'  // ✅ CORREÇÃO: Pontual para ocultar badge
                    };

                    // Atualizar display com valores vazios
                    preencherDisplayRegistro(registroVazio);

                    // Limpar campos de edição
                    preencherCamposEdicao(registroVazio);
                }
            })
            .catch(error => {
                console.warn('⚠️ Erro ao carregar registro atual:', error);
                // Manter valores padrão --:-- em caso de erro
            });
        }

        // Inicializar DataTable quando jQuery estiver disponível
        document.addEventListener('DOMContentLoaded', function() {
            // ✅ CORREÇÃO: Carregar registro atual automaticamente
            carregarRegistroAtual();

            // Aguardar jQuery carregar
            function initDataTable() {
                if (typeof $ !== 'undefined' && typeof $.fn.DataTable !== 'undefined') {
                    $('#registrosTable').DataTable({
                        language: {
                            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/pt-BR.json'
                        },
                        order: [[0, 'desc']], // Ordenar por data decrescente
                        pageLength: 10,
                        responsive: true,
                        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                             '<"row"<"col-sm-12"tr>>' +
                             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                        initComplete: function() {
                            // Ajustar alinhamento do texto "Exibir"
                            $('.dataTables_length').css('margin-left', '1rem');
                        }
                    });
                } else {
                    // Tentar novamente em 100ms
                    setTimeout(initDataTable, 100);
                }
            }

            initDataTable();
        });

        // ✅ CORREÇÃO: Função para abrir modal de edição com sincronização forçada
        function abrirModalEdicao(data, registroId) {
            console.log('=== ABRINDO MODAL DE EDIÇÃO ===');
            console.log('Data:', data);
            console.log('Registro ID:', registroId);

            registroAtual = registroId;

            // Definir data no modal
            document.getElementById('registroData').value = data;
            document.getElementById('dataRegistroDisplay').textContent = formatarData(data);

            console.log('Dados definidos no modal, iniciando busca...');

            // ✅ CORREÇÃO: Forçar sincronização com dados atuais do display ANTES de abrir modal
            sincronizarCamposComDisplay();

            // Abrir modal primeiro
            const modal = new bootstrap.Modal(document.getElementById('modalEdicaoRegistro'));
            modal.show();

            // Aguardar o modal estar completamente aberto antes de buscar dados
            setTimeout(() => {
                console.log('Modal aberto, buscando dados...');
                buscarDadosRegistro(data);
            }, 500);

            console.log('Modal sendo aberto...');
        }

        // ✅ NOVA FUNÇÃO: Sincronizar campos de edição com o display atual
        function sincronizarCamposComDisplay() {
            console.log('=== SINCRONIZANDO CAMPOS COM DISPLAY ===');

            // Obter valores atuais do display
            const entradaDisplay = document.getElementById('entradaDisplay').textContent;
            const saidaAlmocoDisplay = document.getElementById('saidaAlmocoDisplay').textContent;
            const retornoAlmocoDisplay = document.getElementById('retornoAlmocoDisplay').textContent;
            const saidaDisplay = document.getElementById('saidaDisplay').textContent;

            // Criar objeto de registro baseado no display
            const registroAtualDisplay = {
                entrada: entradaDisplay !== '--:--' ? entradaDisplay : null,
                saida_almoco: saidaAlmocoDisplay !== '--:--' ? saidaAlmocoDisplay : null,
                retorno_almoco: retornoAlmocoDisplay !== '--:--' ? retornoAlmocoDisplay : null,
                saida: saidaDisplay !== '--:--' ? saidaDisplay : null,
                metodo_registro: 'Manual',
                status_pontualidade: 'Pontual'
            };

            console.log('Dados do display para sincronização:', registroAtualDisplay);

            // Preencher campos de edição com dados do display
            preencherCamposEdicao(registroAtualDisplay);

            console.log('✅ Sincronização concluída');
        }

        // Função para buscar dados do registro
        function buscarDadosRegistro(data) {
            console.log('=== INICIANDO BUSCA DE DADOS ===');

            const funcionarioId = {{ funcionario.id }};
            const url = `/ponto-admin/api/registro-detalhes/${funcionarioId}/${data}`;

            console.log('Funcionário ID:', funcionarioId);
            console.log('Data:', data);
            console.log('URL da API:', url);

            // Fazer requisição para buscar dados reais da API
            console.log('🔄 Fazendo requisição para:', url);

            // Fazer requisição para buscar dados reais
            fetch(url, {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => {
                    console.log('=== RESPOSTA RECEBIDA ===');
                    console.log('Status:', response.status);
                    console.log('Status Text:', response.statusText);
                    console.log('Content-Type:', response.headers.get('content-type'));

                    if (response.status === 200) {
                        // Verificar se é JSON ou HTML (página de login)
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            return response.json();
                        } else {
                            throw new Error('Resposta não é JSON - possível redirecionamento para login');
                        }
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                })
                .then(data => {
                    console.log('=== DADOS RECEBIDOS ===');
                    console.log('Dados completos:', data);

                    if (data && data.success) {
                        console.log('✅ API retornou sucesso! Dados recebidos:', data);
                        console.log('📝 Registro:', data.registro);
                        console.log('📄 Justificativa:', data.justificativa);
                        preencherFormulario(data.registro, data.justificativa);
                    } else {
                        console.error('❌ API retornou erro:', data.message || 'Erro desconhecido');
                        console.error('📊 Dados completos da resposta:', data);
                        alert('Erro ao carregar dados do registro: ' + (data.message || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    console.error('=== ERRO NA REQUISIÇÃO ===');
                    console.error('Erro:', error);

                    // Verificar se é problema de autenticação
                    if (error.message.includes('login') || error.message.includes('JSON')) {
                        console.error('❌ PROBLEMA DE AUTENTICAÇÃO DETECTADO');
                        alert('Sessão expirada. Por favor, faça login novamente.');
                        window.location.reload();
                    } else {
                        alert('Erro ao carregar dados do registro: ' + error.message);
                    }
                });
        }

        // Função para preencher formulário
        function preencherFormulario(registro, justificativa) {
            console.log('=== PREENCHENDO FORMULÁRIO ===');
            console.log('Registro recebido:', registro);
            console.log('Justificativa recebida:', justificativa);

            if (!registro) {
                console.error('❌ Registro é null ou undefined!');
                return;
            }

            // Preencher display do registro atual
            preencherDisplayRegistro(registro);

            // Preencher campos de edição
            preencherCamposEdicao(registro);

            // Preencher justificativa se existir
            if (justificativa) {
                preencherJustificativa(justificativa);
            }
        }

        // Função para preencher o display do registro atual
        function preencherDisplayRegistro(registro) {
            console.log('=== PREENCHENDO DISPLAY DO REGISTRO ===');

            // Atualizar horários no display (formato simples)
            document.getElementById('entradaDisplay').textContent = registro.entrada || '--:--';
            document.getElementById('saidaAlmocoDisplay').textContent = registro.saida_almoco || '--:--';
            document.getElementById('retornoAlmocoDisplay').textContent = registro.retorno_almoco || '--:--';
            document.getElementById('saidaDisplay').textContent = registro.saida || '--:--';

            // Calcular horas trabalhadas
            const horasTrabalhadas = calcularHorasTrabalhadas(registro);
            document.getElementById('totalHorasDisplay').textContent = '8.0h';
            document.getElementById('horasTrabalhadasDisplay').textContent = `Trabalhadas: ${horasTrabalhadas}`;

            // Atualizar status
            const statusBadge = document.getElementById('statusBadge');
            statusBadge.textContent = 'PRESENTE';
            statusBadge.className = 'badge bg-success ms-2 px-3 py-2';

            // ✅ CORREÇÃO: Atualizar badge de justificativa sempre (mostrar ou ocultar)
            const justificadoBadge = document.getElementById('justificadoBadge');
            if (registro.status_pontualidade && registro.status_pontualidade !== 'Pontual') {
                // Mostrar badge com status de irregularidade
                justificadoBadge.style.display = 'inline-block';
                justificadoBadge.textContent = registro.status_pontualidade.toUpperCase();
                justificadoBadge.className = 'badge bg-warning px-3 py-2 me-3';
                console.log('✅ Badge atualizado:', registro.status_pontualidade);
            } else {
                // ✅ CORREÇÃO: Ocultar badge quando status é Pontual
                justificadoBadge.style.display = 'none';
                console.log('✅ Badge ocultado - status Pontual');
            }
        }

        // Função para calcular horas trabalhadas
        function calcularHorasTrabalhadas(registro) {
            try {
                if (!registro.entrada) return '0h 0min';

                let totalMinutos = 0;

                // Cenário 1: Tem entrada e saída do almoço (B1 e B2)
                // Calcular período trabalhado antes do intervalo
                if (registro.entrada && registro.saida_almoco) {
                    const entrada = new Date(`2000-01-01 ${registro.entrada}`);
                    const saidaAlmoco = new Date(`2000-01-01 ${registro.saida_almoco}`);
                    const minutosAntes = (saidaAlmoco - entrada) / (1000 * 60);
                    totalMinutos += minutosAntes;
                    console.log(`Período antes do intervalo: ${minutosAntes} minutos`);
                }

                // Cenário 2: Tem retorno do almoço e saída (B3 e B4)
                // Calcular período trabalhado após o intervalo
                if (registro.retorno_almoco && registro.saida) {
                    const retornoAlmoco = new Date(`2000-01-01 ${registro.retorno_almoco}`);
                    const saida = new Date(`2000-01-01 ${registro.saida}`);
                    const minutosDepois = (saida - retornoAlmoco) / (1000 * 60);
                    totalMinutos += minutosDepois;
                    console.log(`Período após o intervalo: ${minutosDepois} minutos`);
                }

                // Cenário 3: Só tem entrada e saída (sem intervalo)
                // Calcular período trabalhado direto
                if (registro.entrada && registro.saida && !registro.saida_almoco && !registro.retorno_almoco) {
                    const entrada = new Date(`2000-01-01 ${registro.entrada}`);
                    const saida = new Date(`2000-01-01 ${registro.saida}`);
                    totalMinutos = (saida - entrada) / (1000 * 60);
                    console.log(`Período direto (sem intervalo): ${totalMinutos} minutos`);
                }

                // Cenário 4: Só tem entrada (ainda trabalhando ou saiu sem bater)
                // Neste caso, não podemos calcular horas trabalhadas
                if (registro.entrada && !registro.saida && !registro.saida_almoco) {
                    console.log('Só tem entrada, não é possível calcular');
                    return '0h 0min';
                }

                console.log(`Total de minutos trabalhados: ${totalMinutos}`);

                // Converter para horas e minutos
                const horas = Math.floor(totalMinutos / 60);
                const minutos = Math.round(totalMinutos % 60);

                return `${horas}h ${minutos}min`;
            } catch (error) {
                console.error('Erro ao calcular horas:', error);
                return '0h 0min';
            }
        }

        // ✅ CORREÇÃO: Função para preencher campos de edição com limpeza
        function preencherCamposEdicao(registro) {
            console.log('=== PREENCHENDO CAMPOS DE EDIÇÃO ===');
            console.log('Registro recebido para edição:', registro);

            // ✅ CORREÇÃO: SEMPRE limpar e preencher campos (não apenas se existir)
            // Isso garante que campos antigos sejam removidos quando não há dados

            // Preencher campos de horário (sempre definir, mesmo que vazio)
            document.getElementById('entrada').value = registro.entrada || '';
            document.getElementById('saida_almoco').value = registro.saida_almoco || '';
            document.getElementById('retorno_almoco').value = registro.retorno_almoco || '';
            document.getElementById('saida').value = registro.saida || '';

            // Preencher outros campos (sempre definir, mesmo que vazio)
            document.getElementById('metodo_registro').value = registro.metodo_registro || 'Manual';
            document.getElementById('status_pontualidade').value = registro.status_pontualidade || 'Pontual';

            console.log('✅ Campos de edição preenchidos e sincronizados');
            console.log('Entrada:', document.getElementById('entrada').value);
            console.log('Saída Almoço:', document.getElementById('saida_almoco').value);
            console.log('Retorno Almoço:', document.getElementById('retorno_almoco').value);
            console.log('Saída:', document.getElementById('saida').value);
        }

        // Função para preencher justificativa
        function preencherJustificativa(justificativa) {
            console.log('=== PREENCHENDO JUSTIFICATIVA ===');

            const areaJustificativa = document.getElementById('areaJustificativa');
            const cardJustificativa = document.getElementById('cardJustificativa');
            const cardAprovacao = document.getElementById('cardAprovacao');

            if (justificativa && justificativa.motivo) {
                // Preencher área de justificativa no layout principal
                areaJustificativa.textContent = justificativa.motivo;
                areaJustificativa.className = 'text-dark';

                // Mostrar cards de justificativa e aprovação se existirem
                if (cardJustificativa) {
                    cardJustificativa.style.display = 'block';
                    document.getElementById('tipoJustificativaDisplay').textContent = justificativa.tipo_justificativa || '-';
                    document.getElementById('motivoJustificativaDisplay').textContent = justificativa.motivo || '-';

                    // Status da justificativa
                    const statusJust = document.getElementById('statusJustificativaDisplay');
                    statusJust.textContent = justificativa.status_aprovacao || 'Pendente';
                    statusJust.className = `badge fs-6 px-2 py-1 ${justificativa.status_aprovacao === 'aprovada' ? 'bg-success' :
                                                    justificativa.status_aprovacao === 'reprovada' ? 'bg-danger' : 'bg-warning'}`;

                    // Documento se existir
                    if (justificativa.documento_nome) {
                        document.getElementById('documentoJustificativa').style.display = 'block';
                        document.getElementById('linkDocumentoJustificativa').href = justificativa.documento_caminho || '#';
                    }
                }

                // Mostrar card de aprovação se for admin
                if (cardAprovacao) {
                    cardAprovacao.style.display = 'block';
                    if (justificativa.status_aprovacao) document.getElementById('status_aprovacao').value = justificativa.status_aprovacao;
                    if (justificativa.data_aprovacao) document.getElementById('data_aprovacao').value = justificativa.data_aprovacao;
                    if (justificativa.observacoes_aprovador) document.getElementById('observacoes_aprovador').value = justificativa.observacoes_aprovador;
                }
            } else {
                // Manter texto padrão se não houver justificativa
                areaJustificativa.textContent = 'Aqui aparece a justificativa dada no momento da batida do ponto antecipado.';
                areaJustificativa.className = 'text-muted';
            }

            console.log('✅ Justificativa preenchida');
        }








        // Função para salvar registro
        function salvarRegistro() {
            const form = document.getElementById('formEdicaoRegistro');
            const formData = new FormData(form);

            // Adicionar dados adicionais
            formData.append('registro_id', registroAtual);
            if (justificativaAtual) {
                formData.append('justificativa_id', justificativaAtual.id);
            }

            // Mostrar loading
            const btnSalvar = event.target;
            const textoOriginal = btnSalvar.innerHTML;
            btnSalvar.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Salvando...';
            btnSalvar.disabled = true;

            fetch('/ponto-admin/salvar-registro', {
                method: 'POST',
                credentials: 'same-origin',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Registro salvo com sucesso!');
                    location.reload(); // Recarregar página para mostrar alterações
                } else {
                    alert('Erro ao salvar: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro de comunicação com o servidor');
            })
            .finally(() => {
                btnSalvar.innerHTML = textoOriginal;
                btnSalvar.disabled = false;
            });
        }

        // Função auxiliar para formatar data
        function formatarData(data) {
            const [ano, mes, dia] = data.split('-');
            return `${dia}/${mes}/${ano}`;
        }

        // Função para enviar documento
        function enviarDocumento() {
            // Criar input file oculto
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.pdf,.jpg,.jpeg,.png,.doc,.docx';
            input.style.display = 'none';

            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    // Verificar tamanho (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('Arquivo muito grande. Máximo 5MB permitido.');
                        return;
                    }

                    // Verificar tipo
                    const tiposPermitidos = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg',
                                           'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
                    if (!tiposPermitidos.includes(file.type)) {
                        alert('Tipo de arquivo não permitido. Use PDF, JPG, PNG, DOC ou DOCX.');
                        return;
                    }

                    // Aqui você pode implementar o upload do arquivo
                    alert(`Arquivo selecionado: ${file.name}\nTamanho: ${(file.size / 1024 / 1024).toFixed(2)} MB`);

                    // TODO: Implementar upload real
                    console.log('Arquivo para upload:', file);
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        // Adicionar evento ao botão de enviar documento
        document.addEventListener('DOMContentLoaded', function() {
            const btnEnviarDocumento = document.getElementById('btnEnviarDocumento');
            if (btnEnviarDocumento) {
                btnEnviarDocumento.addEventListener('click', enviarDocumento);
            }

            // ========================================
            // NOVAS FUNCIONALIDADES - CONTROLES MODERNOS
            // ========================================

            // Configurar controles modernos
            configurarControlesModernos();
        });

        // ========================================
        // CONFIGURAÇÃO DOS CONTROLES MODERNOS
        // ========================================
        function configurarControlesModernos() {
            // Configurar eventos dos inputs de data
            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');

            if (dataInicio && dataFim) {
                dataInicio.addEventListener('change', validarPeriodo);
                dataFim.addEventListener('change', validarPeriodo);
            }

            // Aplicar filtro automaticamente quando as datas mudarem
            if (dataInicio && dataFim) {
                dataInicio.addEventListener('change', () => {
                    setTimeout(aplicarFiltro, 300);
                });
                dataFim.addEventListener('change', () => {
                    setTimeout(aplicarFiltro, 300);
                });
            }
        }

        // ========================================
        // FUNÇÕES DE PERÍODO RÁPIDO
        // ========================================
        function setPeriodoRapido(periodo) {
            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');
            const hoje = new Date();

            let inicio, fim;

            switch(periodo) {
                case 'hoje':
                    inicio = fim = hoje.toISOString().split('T')[0];
                    break;

                case 'semana':
                    // Últimos 7 dias
                    const semanaAtras = new Date(hoje);
                    semanaAtras.setDate(hoje.getDate() - 7);
                    inicio = semanaAtras.toISOString().split('T')[0];
                    fim = hoje.toISOString().split('T')[0];
                    break;

                case 'mes':
                    // Últimos 30 dias
                    const mesAtras = new Date(hoje);
                    mesAtras.setDate(hoje.getDate() - 30);
                    inicio = mesAtras.toISOString().split('T')[0];
                    fim = hoje.toISOString().split('T')[0];
                    break;

                case 'todos':
                    // Período amplo para mostrar todos os registros
                    const anoAtras = new Date(hoje);
                    anoAtras.setFullYear(hoje.getFullYear() - 1);
                    inicio = anoAtras.toISOString().split('T')[0];
                    fim = hoje.toISOString().split('T')[0];
                    break;
            }

            if (dataInicio && dataFim) {
                dataInicio.value = inicio;
                dataFim.value = fim;

                // Atualizar botões ativos
                const buttons = document.querySelectorAll('.quick-btn');
                buttons.forEach(btn => btn.classList.remove('active'));
                event.target.classList.add('active');

                // Aplicar filtro automaticamente
                setTimeout(aplicarFiltro, 100);
            }
        }

        // ========================================
        // VALIDAÇÃO DE PERÍODO
        // ========================================
        function validarPeriodo() {
            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');

            if (dataInicio && dataFim && dataInicio.value && dataFim.value) {
                const inicio = new Date(dataInicio.value);
                const fim = new Date(dataFim.value);

                if (inicio > fim) {
                    // Trocar as datas se início for maior que fim
                    const temp = dataInicio.value;
                    dataInicio.value = dataFim.value;
                    dataFim.value = temp;

                    // Mostrar feedback visual
                    mostrarFeedback('Datas ajustadas automaticamente', 'info');
                }
            }
        }

        // ========================================
        // APLICAR FILTRO DE PERÍODO
        // ========================================
        function aplicarFiltro() {
            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');
            const table = document.getElementById('registrosTable');

            if (!table || !dataInicio || !dataFim) return;

            const inicio = dataInicio.value ? new Date(dataInicio.value) : null;
            const fim = dataFim.value ? new Date(dataFim.value) : null;

            const tbody = table.querySelector('tbody');
            const rows = tbody.querySelectorAll('tr');
            let visibleCount = 0;

            rows.forEach(row => {
                const dataCell = row.querySelector('td:first-child');
                if (!dataCell) return;

                const dataTexto = dataCell.textContent.trim();
                const dataRegistro = parseDataBrasileira(dataTexto);

                let mostrar = true;

                if (inicio && dataRegistro < inicio) mostrar = false;
                if (fim && dataRegistro > fim) mostrar = false;

                if (mostrar) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // Atualizar contador
            atualizarContadorFiltro(visibleCount, rows.length);

            // Feedback visual
            const btnFiltrar = document.querySelector('.btn-filter');
            if (btnFiltrar) {
                const originalText = btnFiltrar.innerHTML;
                btnFiltrar.innerHTML = '<i class="fas fa-check"></i><span>Filtrado!</span>';
                btnFiltrar.style.background = '#10b981';

                setTimeout(() => {
                    btnFiltrar.innerHTML = originalText;
                    btnFiltrar.style.background = '';
                }, 1500);
            }
        }

        // Função auxiliar para converter data brasileira
        function parseDataBrasileira(dataStr) {
            // Formato esperado: DD/MM/YYYY
            const partes = dataStr.split('/');
            if (partes.length === 3) {
                return new Date(partes[2], partes[1] - 1, partes[0]);
            }
            return new Date(dataStr);
        }

        // Atualizar contador de filtro
        function atualizarContadorFiltro(visible, total) {
            const infoElement = document.querySelector('.dataTables_info');
            if (infoElement) {
                if (visible === total) {
                    infoElement.textContent = `Mostrando ${total} registros`;
                } else {
                    infoElement.textContent = `Mostrando ${visible} de ${total} registros (filtrado por período)`;
                }
            }
        }

        // Função para mostrar feedback
        function mostrarFeedback(mensagem, tipo = 'success') {
            // Implementar toast ou alert conforme necessário
            console.log(`[${tipo.toUpperCase()}] ${mensagem}`);
        }

        // ========================================
        // FUNÇÃO DE IMPRESSÃO MODERNIZADA
        // ========================================
        function imprimirPonto() {
            const funcionarioNome = '{{ funcionario.nome_completo }}';
            const funcionarioId = '{{ funcionario.id }}';
            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');

            // Feedback visual
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Preparando...</span>';
            btn.disabled = true;

            // Construir URL de impressão com filtros
            let printUrl = `/ponto-admin/funcionario/${funcionarioId}/imprimir`;

            // Adicionar parâmetros de filtro se existirem
            const params = new URLSearchParams();
            if (dataInicio && dataInicio.value) params.append('data_inicio', dataInicio.value);
            if (dataFim && dataFim.value) params.append('data_fim', dataFim.value);

            if (params.toString()) {
                printUrl += '?' + params.toString();
            }

            console.log('🖨️ Abrindo página de impressão para:', funcionarioNome, 'URL:', printUrl);

            // Abrir em nova janela otimizada para impressão
            const printWindow = window.open(printUrl, '_blank',
                'width=800,height=600,scrollbars=yes,resizable=yes,toolbar=no,menubar=no');

            // Restaurar botão após delay
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;

                // Focar na janela de impressão se ainda estiver aberta
                if (printWindow && !printWindow.closed) {
                    printWindow.focus();
                }
            }, 2000);
        }

        // ========================================
        // FUNÇÃO DE EXPORTAÇÃO EXCEL MODERNIZADA
        // ========================================
        function exportarExcel() {
            const funcionarioNome = '{{ funcionario.nome_completo }}';
            const funcionarioId = '{{ funcionario.id }}';
            const dataInicio = document.getElementById('dataInicio');
            const dataFim = document.getElementById('dataFim');

            // Feedback visual
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Gerando...</span>';
            btn.disabled = true;

            // Construir URL de exportação com filtros
            let exportUrl = `/ponto-admin/funcionario/${funcionarioId}/excel`;

            // Adicionar parâmetros de filtro se existirem
            const params = new URLSearchParams();
            if (dataInicio && dataInicio.value) params.append('data_inicio', dataInicio.value);
            if (dataFim && dataFim.value) params.append('data_fim', dataFim.value);

            if (params.toString()) {
                exportUrl += '?' + params.toString();
            }

            console.log('📊 Exportando Excel para:', funcionarioNome, 'URL:', exportUrl);

            // Download direto
            window.open(exportUrl, '_blank');

            // Restaurar botão após delay
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 2000);
        }

        // ========================================
        // MELHORIAS NO DATATABLES
        // ========================================

        // Sobrescrever configuração do DataTable para incluir pesquisa personalizada
        document.addEventListener('DOMContentLoaded', function() {
            // Aguardar DataTable ser inicializado
            setTimeout(() => {
                const dataTable = $('#registrosTable').DataTable();
                if (dataTable) {
                    // Desabilitar pesquisa padrão do DataTable
                    $('.dataTables_filter').hide();

                    // Conectar nossa pesquisa personalizada
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.addEventListener('input', function() {
                            dataTable.search(this.value).draw();
                        });
                    }
                }
            }, 1000);
        });


    </script>
{% endblock %}
