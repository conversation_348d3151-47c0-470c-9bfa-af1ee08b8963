#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DEPLOY SIMPLES PARA ENDPOINT DE TESTE
====================================
"""

import paramiko
import os

def deploy():
    """Deploy simples"""
    hostname = '************'
    username = 'admin'
    password = '@Ric6109'
    
    # Arquivos para deploy
    files_to_deploy = [
        ('var/www/controle-ponto/executar_testes_dados_existentes.py', '/var/www/controle-ponto/executar_testes_dados_existentes.py'),
        ('var/www/controle-ponto/app_ponto_admin.py', '/var/www/controle-ponto/app_ponto_admin.py'),
        ('var/www/controle-ponto/RELATORIO_COMPLETO_TESTES_SISTEMA.md', '/var/www/controle-ponto/RELATORIO_COMPLETO_TESTES_SISTEMA.md')
    ]
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)

        print("🚀 DEPLOY DOS TESTES E RELATÓRIOS")
        print("✅ Conectado ao servidor com sucesso!")

        # Timestamp para backup
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # Upload via SFTP
        sftp = ssh.open_sftp()

        for local_file, remote_path in files_to_deploy:
            # Verificar se arquivo existe
            if not os.path.exists(local_file):
                print(f"❌ Arquivo não encontrado: {local_file}")
                continue

            print(f"📤 Fazendo upload de {os.path.basename(local_file)}...")

            # Criar backup se arquivo existir
            try:
                backup_cmd = f"cp {remote_path} {remote_path}.backup.{timestamp} 2>/dev/null || echo 'Arquivo novo'"
                ssh.exec_command(backup_cmd)
            except:
                pass

            # Upload
            sftp.put(local_file, remote_path)
            print(f"✅ {os.path.basename(local_file)} enviado!")

        sftp.close()
        print("✅ Todos os arquivos enviados com sucesso!")

        # Reiniciar serviço para aplicar mudanças
        print("\n🔄 Reiniciando serviço...")
        restart_cmd = "sudo systemctl restart controle-ponto"
        ssh.exec_command(restart_cmd)

        # Aguardar um pouco
        import time
        time.sleep(3)

        print("✅ Deploy concluído com sucesso!")
        print("\n🌐 ACESSE OS TESTES:")
        print("   • Testes do Sistema: http://************/ponto-admin/executar-testes-sistema")
        print("   • Criar Funcionários Teste: http://************/ponto-admin/executar-teste-funcionarios")
        print("   • Painel Admin: http://************/ponto-admin/")

    except Exception as e:
        print(f"❌ Erro durante o deploy: {e}")
    finally:
        ssh.close()

if __name__ == "__main__":
    deploy()
