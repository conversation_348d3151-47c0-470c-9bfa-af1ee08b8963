#!/usr/bin/env python3
"""
TESTE COMPLETO - ADICIONAR CLIENTE
Testa todos os cenários possíveis para comprovar a correção
"""

import requests
import json
import sys
import time
from datetime import datetime

# Configurações
BASE_URL = "http://************:5000"
LOGIN_URL = f"{BASE_URL}/login"
ADICIONAR_URL = f"{BASE_URL}/empresa-principal/clientes/adicionar"

def fazer_login():
    """Fazer login no sistema"""
    print("🔐 Fazendo login...")
    
    session = requests.Session()
    
    # Fazer login
    login_data = {
        'username': 'admin',
        'password': '@Ric6109'
    }
    
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    
    if response.status_code in [200, 302]:
        print("✅ Login realizado com sucesso")
        return session
    else:
        print(f"❌ Erro no login: {response.status_code}")
        return None

def testar_adicionar_cliente(session, empresa_id, nome_teste, dados_extras=None):
    """Testar adição de cliente"""
    print(f"\n🧪 TESTE: {nome_teste}")
    print(f"   Empresa ID: {empresa_id}")
    
    # Dados básicos
    dados = {
        'empresa_cliente_id': empresa_id,
        'data_inicio': '2025-07-18',
        'nome_contrato': f'Contrato Teste {empresa_id}',
        'codigo_contrato': f'TEST{empresa_id}',
        'descricao_projeto': f'Projeto teste para empresa {empresa_id}',
        'status_contrato': 'ativo'
    }
    
    # Adicionar dados extras se fornecidos
    if dados_extras:
        dados.update(dados_extras)
    
    print(f"   Dados: {dados}")
    
    try:
        response = session.post(ADICIONAR_URL, data=dados, timeout=10)
        
        print(f"   Status HTTP: {response.status_code}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            resultado = response.json()
            print(f"   Resposta JSON: {json.dumps(resultado, indent=2, ensure_ascii=False)}")
            
            if resultado.get('success'):
                print("   ✅ SUCESSO: Cliente adicionado")
            else:
                print(f"   ❌ ERRO: {resultado.get('message', 'Erro desconhecido')}")
                print(f"   🔧 Tipo: {resultado.get('error_type', 'N/A')}")
                print(f"   🪟 Modal aberto: {resultado.get('keep_modal_open', False)}")
            
            return resultado
        else:
            print(f"   ⚠️ Resposta não-JSON: {response.text[:200]}...")
            return None
            
    except Exception as e:
        print(f"   💥 EXCEÇÃO: {e}")
        return None

def verificar_banco(empresa_id):
    """Verificar estado do banco após teste"""
    print(f"\n🔍 Verificando banco para empresa {empresa_id}...")
    
    import subprocess
    
    cmd = f'ssh rlponto-server "mysql -u cavalcrod -p200381 controle_ponto -e \'SELECT id, empresa_principal_id, empresa_cliente_id, ativo, status_contrato FROM empresa_clientes WHERE empresa_cliente_id = {empresa_id};\'"'
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            output = result.stdout
            print(f"   📊 Resultado do banco:")
            for line in output.split('\n'):
                if line.strip():
                    print(f"      {line}")
        else:
            print(f"   ❌ Erro ao consultar banco: {result.stderr}")
            
    except Exception as e:
        print(f"   💥 Erro na verificação: {e}")

def main():
    """Executar bateria completa de testes"""
    print("🚀 INICIANDO BATERIA COMPLETA DE TESTES")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    # Fazer login
    session = fazer_login()
    if not session:
        print("❌ Falha no login - Abortando testes")
        return False
    
    # TESTE 1: Tentar adicionar Ocrim (duplicata ativa)
    print("\n" + "="*60)
    print("TESTE 1: DUPLICATA ATIVA (Ocrim - ID 14)")
    print("="*60)
    
    resultado1 = testar_adicionar_cliente(session, 14, "Duplicata Ativa - Ocrim")
    verificar_banco(14)
    
    # TESTE 2: Tentar adicionar empresa que não existe como cliente
    print("\n" + "="*60)
    print("TESTE 2: EMPRESA NOVA (AiNexus - ID 1)")
    print("="*60)
    
    # Primeiro verificar se AiNexus já é cliente
    verificar_banco(1)
    resultado2 = testar_adicionar_cliente(session, 1, "Empresa Nova - AiNexus")
    verificar_banco(1)
    
    # TESTE 3: Tentar adicionar empresa inexistente
    print("\n" + "="*60)
    print("TESTE 3: EMPRESA INEXISTENTE (ID 999)")
    print("="*60)
    
    resultado3 = testar_adicionar_cliente(session, 999, "Empresa Inexistente")
    verificar_banco(999)
    
    # RESUMO DOS TESTES
    print("\n" + "="*60)
    print("📋 RESUMO DOS TESTES")
    print("="*60)
    
    testes = [
        ("TESTE 1 - Duplicata Ativa", resultado1),
        ("TESTE 2 - Empresa Nova", resultado2),
        ("TESTE 3 - Empresa Inexistente", resultado3)
    ]
    
    for nome, resultado in testes:
        if resultado:
            status = "✅ SUCESSO" if resultado.get('success') else "❌ ERRO"
            modal = "🪟 MODAL ABERTO" if resultado.get('keep_modal_open') else "🚪 MODAL FECHADO"
            print(f"{nome}: {status} | {modal}")
        else:
            print(f"{nome}: 💥 FALHA NA EXECUÇÃO")
    
    print("\n🎯 TESTES CONCLUÍDOS!")
    return True

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
