#!/usr/bin/env python3
"""
Corrigir jornada dos funcionários da Ainexus Tecnologia
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def corrigir_jornada_ainexus():
    """Corrigir jornada dos funcionários da Ainexus"""
    try:
        from utils.database import DatabaseManager
        from app_funcionarios import get_jornada_padrao_empresa
        import logging
        
        logging.basicConfig(level=logging.INFO)
        
        print("🔧 CORRIGINDO JORNADA DOS FUNCIONÁRIOS DA AINEXUS")
        print("=" * 60)
        
        # Buscar empresa Ainexus
        empresa = DatabaseManager.execute_query("""
            SELECT id, nome_fantasia, razao_social 
            FROM empresas 
            WHERE nome_fantasia LIKE %s OR nome_fantasia LIKE %s
            LIMIT 1
        """, ('%Ainexus%', '%ainexus%'), fetch_one=True)
        
        if not empresa:
            print("❌ Empresa Ainexus não encontrada")
            return False
            
        empresa_id = empresa['id']
        print(f"🏢 Empresa: {empresa['nome_fantasia']} (ID: {empresa_id})")
        
        # Obter jornada correta da empresa
        jornada_empresa = get_jornada_padrao_empresa(empresa_id)
        if not jornada_empresa:
            print("❌ Jornada da empresa não encontrada")
            return False
            
        print("\n📋 JORNADA CORRETA DA EMPRESA:")
        print("-" * 40)
        print(f"   Segunda-Quinta: {jornada_empresa['entrada_manha']} - {jornada_empresa['saida']}")
        print(f"   Almoço: {jornada_empresa['saida_almoco']} - {jornada_empresa['entrada_tarde']}")
        print(f"   Sexta entrada: {jornada_empresa['jornada_sexta_entrada']}")
        print(f"   Sexta saída: {jornada_empresa['jornada_sexta_saida']}")
        print(f"   Tolerância: {jornada_empresa['tolerancia_minutos']} min")
        
        # Buscar funcionários da empresa
        funcionarios = DatabaseManager.execute_query("""
            SELECT id, nome_completo,
                   jornada_seg_qui_entrada, jornada_seg_qui_saida,
                   jornada_sex_entrada, jornada_sex_saida,
                   jornada_intervalo_entrada, jornada_intervalo_saida,
                   tolerancia_ponto
            FROM funcionarios 
            WHERE empresa_id = %s AND ativo = 1
        """, (empresa_id,))
        
        if not funcionarios:
            print("❌ Nenhum funcionário encontrado")
            return False
            
        print(f"\n👥 FUNCIONÁRIOS A SEREM ATUALIZADOS: {len(funcionarios)}")
        print("-" * 50)
        
        funcionarios_atualizados = 0
        
        for func in funcionarios:
            print(f"\n👤 {func['nome_completo']} (ID: {func['id']})")
            
            # Verificar se precisa atualizar
            precisa_atualizar = False
            mudancas = []
            
            # Verificar cada campo
            if str(func['jornada_seg_qui_entrada']) != str(jornada_empresa['entrada_manha']):
                mudancas.append(f"Seg-Qui entrada: {func['jornada_seg_qui_entrada']} → {jornada_empresa['entrada_manha']}")
                precisa_atualizar = True
                
            if str(func['jornada_seg_qui_saida']) != str(jornada_empresa['saida']):
                mudancas.append(f"Seg-Qui saída: {func['jornada_seg_qui_saida']} → {jornada_empresa['saida']}")
                precisa_atualizar = True
                
            if str(func['jornada_sex_entrada']) != str(jornada_empresa['jornada_sexta_entrada']):
                mudancas.append(f"Sexta entrada: {func['jornada_sex_entrada']} → {jornada_empresa['jornada_sexta_entrada']}")
                precisa_atualizar = True
                
            if str(func['jornada_sex_saida']) != str(jornada_empresa['jornada_sexta_saida']):
                mudancas.append(f"Sexta saída: {func['jornada_sex_saida']} → {jornada_empresa['jornada_sexta_saida']}")
                precisa_atualizar = True
                
            if str(func['jornada_intervalo_entrada']) != str(jornada_empresa['saida_almoco']):
                mudancas.append(f"Almoço entrada: {func['jornada_intervalo_entrada']} → {jornada_empresa['saida_almoco']}")
                precisa_atualizar = True
                
            if str(func['jornada_intervalo_saida']) != str(jornada_empresa['entrada_tarde']):
                mudancas.append(f"Almoço saída: {func['jornada_intervalo_saida']} → {jornada_empresa['entrada_tarde']}")
                precisa_atualizar = True
                
            if func['tolerancia_ponto'] != jornada_empresa['tolerancia_minutos']:
                mudancas.append(f"Tolerância: {func['tolerancia_ponto']} → {jornada_empresa['tolerancia_minutos']}")
                precisa_atualizar = True
            
            if precisa_atualizar:
                print("   🔧 ATUALIZANDO:")
                for mudanca in mudancas:
                    print(f"      • {mudanca}")
                
                # Atualizar funcionário
                sql_update = """
                UPDATE funcionarios SET
                    jornada_seg_qui_entrada = %s,
                    jornada_seg_qui_saida = %s,
                    jornada_sex_entrada = %s,
                    jornada_sex_saida = %s,
                    jornada_intervalo_entrada = %s,
                    jornada_intervalo_saida = %s,
                    tolerancia_ponto = %s
                WHERE id = %s
                """
                
                params = (
                    jornada_empresa['entrada_manha'],
                    jornada_empresa['saida'],
                    jornada_empresa['jornada_sexta_entrada'],
                    jornada_empresa['jornada_sexta_saida'],
                    jornada_empresa['saida_almoco'],
                    jornada_empresa['entrada_tarde'],
                    jornada_empresa['tolerancia_minutos'],
                    func['id']
                )
                
                resultado = DatabaseManager.execute_query(sql_update, params, fetch_all=False)
                if resultado is not None:
                    print("   ✅ ATUALIZADO COM SUCESSO!")
                    funcionarios_atualizados += 1
                else:
                    print("   ❌ ERRO AO ATUALIZAR!")
            else:
                print("   ✅ Já está correto")
        
        print(f"\n" + "=" * 60)
        print(f"🎉 CORREÇÃO CONCLUÍDA!")
        print(f"📊 Funcionários atualizados: {funcionarios_atualizados}/{len(funcionarios)}")
        print(f"✅ Todos os funcionários da Ainexus agora usam a jornada correta da empresa")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante correção: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    corrigir_jornada_ainexus()
