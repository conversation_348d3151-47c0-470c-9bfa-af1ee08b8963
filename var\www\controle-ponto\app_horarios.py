# ========================================
# BLUEPRINT HORÁRIOS DE TRABALHO - RLPONTO-WEB
# Data: 08/01/2025
# Descrição: Sistema de gerenciamento de horários de trabalho
# ========================================

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash
from datetime import datetime, time
from utils.auth import require_login, require_admin
from utils.database import get_db_connection
import logging
import re

# Configurar logger
logger = logging.getLogger(__name__)

# Criar Blueprint
horarios_bp = Blueprint('horarios', __name__, url_prefix='/horarios')

# ========================================
# FUNÇÕES AUXILIARES
# ========================================

def validar_horario(horario_str):
    """
    Valida se uma string está no formato HH:MM válido.
    
    Args:
        horario_str (str): String do horário a validar
        
    Returns:
        bool: True se válido
    """
    if not horario_str:
        return True  # Horário opcional pode ser vazio
    
    pattern = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
    return bool(re.match(pattern, horario_str))

def validar_dados_horario(dados):
    """
    Valida os dados de um horário de trabalho.
    
    Args:
        dados (dict): Dados do horário
        
    Returns:
        dict: Resultado da validação
    """
    erros = []
    
    # Validar nome do horário
    if not dados.get('nome_horario'):
        erros.append('Nome do horário é obrigatório')
    elif len(dados['nome_horario']) < 3:
        erros.append('Nome do horário deve ter pelo menos 3 caracteres')
    elif len(dados['nome_horario']) > 100:
        erros.append('Nome do horário deve ter no máximo 100 caracteres')
    
    # Validar empresa
    if not dados.get('empresa_id'):
        erros.append('Empresa é obrigatória')
    
    # Validar horários
    horarios_campos = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']
    for campo in horarios_campos:
        horario = dados.get(campo, '').strip()
        if horario and not validar_horario(horario):
            nome_campo = campo.replace('_', ' ').title()
            erros.append(f'{nome_campo} deve estar no formato HH:MM')
        dados[campo] = horario if horario else None
    
    # Validar tolerância
    tolerancia = dados.get('tolerancia_minutos', 0)
    try:
        tolerancia = int(tolerancia) if tolerancia else 0
        if tolerancia < 0 or tolerancia > 60:
            erros.append('Tolerância deve estar entre 0 e 60 minutos')
        dados['tolerancia_minutos'] = tolerancia
    except ValueError:
        erros.append('Tolerância deve ser um número válido')
    
    # Validar lógica dos horários
    if dados.get('entrada_manha') and dados.get('saida_almoco'):
        try:
            entrada_manha = datetime.strptime(dados['entrada_manha'], '%H:%M').time()
            saida_almoco = datetime.strptime(dados['saida_almoco'], '%H:%M').time()
            if entrada_manha >= saida_almoco:
                erros.append('Saída para almoço deve ser após a entrada da manhã')
        except ValueError:
            pass
    
    if dados.get('saida_almoco') and dados.get('entrada_tarde'):
        try:
            saida_almoco = datetime.strptime(dados['saida_almoco'], '%H:%M').time()
            entrada_tarde = datetime.strptime(dados['entrada_tarde'], '%H:%M').time()
            if saida_almoco >= entrada_tarde:
                erros.append('Entrada da tarde deve ser após a saída para almoço')
        except ValueError:
            pass
    
    if dados.get('entrada_tarde') and dados.get('saida'):
        try:
            entrada_tarde = datetime.strptime(dados['entrada_tarde'], '%H:%M').time()
            saida = datetime.strptime(dados['saida'], '%H:%M').time()
            if entrada_tarde >= saida:
                erros.append('Saída deve ser após a entrada da tarde')
        except ValueError:
            pass
    
    return {
        'valido': len(erros) == 0,
        'erros': erros,
        'dados': dados
    }

def verificar_horario_duplicado(nome_horario, empresa_id, horario_id=None):
    """
    Verifica se já existe um horário com o mesmo nome na empresa.
    
    Args:
        nome_horario (str): Nome do horário
        empresa_id (int): ID da empresa
        horario_id (int, optional): ID do horário para excluir da verificação
        
    Returns:
        bool: True se existe duplicata
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if horario_id:
            cursor.execute("""
                SELECT COUNT(*) FROM horarios_trabalho 
                WHERE nome_horario = %s AND empresa_id = %s AND id != %s
            """, (nome_horario, empresa_id, horario_id))
        else:
            cursor.execute("""
                SELECT COUNT(*) FROM horarios_trabalho 
                WHERE nome_horario = %s AND empresa_id = %s
            """, (nome_horario, empresa_id))
        
        count = cursor.fetchone()[0]
        conn.close()
        
        return count > 0
        
    except Exception as e:
        logger.error(f"Erro ao verificar horário duplicado: {str(e)}")
        return False

def obter_empresas_ativas():
    """
    Obtém lista de empresas ativas para seleção.
    
    Returns:
        list: Lista de empresas
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, razao_social, nome_fantasia 
            FROM empresas 
            WHERE ativa = TRUE 
            ORDER BY razao_social
        """)
        
        empresas = cursor.fetchall()
        conn.close()
        
        return empresas
        
    except Exception as e:
        logger.error(f"Erro ao obter empresas: {str(e)}")
        return []

# ========================================
# ROTAS PRINCIPAIS
# ========================================

@horarios_bp.route('/')
@require_admin
def index():
    """
    Lista todos os horários de trabalho cadastrados.
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Buscar horários com informações da empresa
        cursor.execute("""
            SELECT 
                h.id,
                h.nome_horario,
                h.entrada_manha,
                h.saida_almoco,
                h.entrada_tarde,
                h.saida,
                h.tolerancia_minutos,
                h.ativo,
                h.data_criacao,
                e.razao_social,
                e.nome_fantasia,
                COUNT(f.id) as funcionarios_vinculados
            FROM horarios_trabalho h
            INNER JOIN empresas e ON h.empresa_id = e.id
            LEFT JOIN funcionarios f ON h.id = f.horario_trabalho_id AND f.ativo = TRUE
            GROUP BY h.id, h.nome_horario, h.entrada_manha, h.saida_almoco, 
                     h.entrada_tarde, h.saida, h.tolerancia_minutos, h.ativo, 
                     h.data_criacao, e.razao_social, e.nome_fantasia
            ORDER BY e.razao_social, h.nome_horario
        """)
        
        horarios_raw = cursor.fetchall()
        conn.close()
        
        # Processar dados dos horários
        horarios = []
        for horario in horarios_raw:
            # Formatear horários para exibição
            entrada_manha = horario['entrada_manha'].strftime('%H:%M') if horario['entrada_manha'] else '-'
            saida_almoco = horario['saida_almoco'].strftime('%H:%M') if horario['saida_almoco'] else '-'
            entrada_tarde = horario['entrada_tarde'].strftime('%H:%M') if horario['entrada_tarde'] else '-'
            saida = horario['saida'].strftime('%H:%M') if horario['saida'] else '-'
            
            # Montar descrição do horário
            horario_desc = []
            if horario['entrada_manha']:
                horario_desc.append(f"Manhã: {entrada_manha}")
                if horario['saida_almoco']:
                    horario_desc.append(f"às {saida_almoco}")
            if horario['entrada_tarde']:
                horario_desc.append(f"Tarde: {entrada_tarde}")
                if horario['saida']:
                    horario_desc.append(f"às {saida}")
            
            horarios.append({
                'id': horario['id'],
                'nome_horario': horario['nome_horario'],
                'empresa': horario['nome_fantasia'] or horario['razao_social'],
                'entrada_manha': entrada_manha,
                'saida_almoco': saida_almoco,
                'entrada_tarde': entrada_tarde,
                'saida': saida,
                'tolerancia_minutos': horario['tolerancia_minutos'],
                'horario_descricao': ' | '.join(horario_desc) if horario_desc else 'Sem horários definidos',
                'funcionarios_vinculados': horario['funcionarios_vinculados'],
                'ativo': horario['ativo'],
                'data_criacao': horario['data_criacao'].strftime('%d/%m/%Y') if horario['data_criacao'] else '',
                'pode_excluir': horario['funcionarios_vinculados'] == 0
            })
        
        context = {
            'titulo': 'Horários de Trabalho',
            'horarios': horarios,
            'total_horarios': len(horarios)
        }
        
        return render_template('horarios/index.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao listar horários: {str(e)}")
        flash('Erro ao carregar horários de trabalho', 'error')
        return redirect(url_for('configuracoes.index'))

@horarios_bp.route('/novo', methods=['GET', 'POST'])
@require_admin
def novo_horario():
    """
    Cadastra um novo horário de trabalho.
    """
    if request.method == 'POST':
        try:
            # Obter dados do formulário
            dados = {
                'nome_horario': request.form.get('nome_horario', '').strip(),
                'empresa_id': request.form.get('empresa_id'),
                'entrada_manha': request.form.get('entrada_manha', '').strip(),
                'saida_almoco': request.form.get('saida_almoco', '').strip(),
                'entrada_tarde': request.form.get('entrada_tarde', '').strip(),
                'saida': request.form.get('saida', '').strip(),
                'tolerancia_minutos': request.form.get('tolerancia_minutos', 0),
                'ativo': bool(request.form.get('ativo'))
            }
            
            # Validar dados
            validacao = validar_dados_horario(dados)
            
            if not validacao['valido']:
                for erro in validacao['erros']:
                    flash(erro, 'error')
                return render_template('horarios/form.html',
                                     titulo='Novo Horário de Trabalho',
                                     horario=dados,
                                     empresas=obter_empresas_ativas(),
                                     acao='novo')
            
            # Verificar duplicata
            if verificar_horario_duplicado(dados['nome_horario'], dados['empresa_id']):
                flash('Já existe um horário com este nome nesta empresa', 'error')
                return render_template('horarios/form.html',
                                     titulo='Novo Horário de Trabalho',
                                     horario=dados,
                                     empresas=obter_empresas_ativas(),
                                     acao='novo')
            
            # Inserir no banco
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO horarios_trabalho 
                (empresa_id, nome_horario, entrada_manha, saida_almoco, 
                 entrada_tarde, saida, tolerancia_minutos, ativo, data_criacao)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW())
            """, (
                dados['empresa_id'],
                dados['nome_horario'],
                dados['entrada_manha'] if dados['entrada_manha'] else None,
                dados['saida_almoco'] if dados['saida_almoco'] else None,
                dados['entrada_tarde'] if dados['entrada_tarde'] else None,
                dados['saida'] if dados['saida'] else None,
                dados['tolerancia_minutos'],
                dados['ativo']
            ))
            
            conn.commit()
            horario_id = cursor.lastrowid
            conn.close()
            
            logger.info(f"Horário '{dados['nome_horario']}' criado com ID {horario_id}")
            flash(f"Horário '{dados['nome_horario']}' cadastrado com sucesso!", 'success')
            
            return redirect(url_for('horarios.index'))
            
        except Exception as e:
            logger.error(f"Erro ao criar horário: {str(e)}")
            flash('Erro ao cadastrar horário', 'error')
            return render_template('horarios/form.html',
                                 titulo='Novo Horário de Trabalho',
                                 horario=dados if 'dados' in locals() else {},
                                 empresas=obter_empresas_ativas(),
                                 acao='novo')
    
    # GET - Mostrar formulário
    context = {
        'titulo': 'Novo Horário de Trabalho',
        'horario': {},
        'empresas': obter_empresas_ativas(),
        'acao': 'novo'
    }
    
    return render_template('horarios/form.html', **context)

@horarios_bp.route('/<int:horario_id>/editar', methods=['GET', 'POST'])
@require_admin
def editar_horario(horario_id):
    """
    Edita um horário de trabalho existente.
    """
    if request.method == 'POST':
        try:
            # Obter dados do formulário
            dados = {
                'nome_horario': request.form.get('nome_horario', '').strip(),
                'empresa_id': request.form.get('empresa_id'),
                'entrada_manha': request.form.get('entrada_manha', '').strip(),
                'saida_almoco': request.form.get('saida_almoco', '').strip(),
                'entrada_tarde': request.form.get('entrada_tarde', '').strip(),
                'saida': request.form.get('saida', '').strip(),
                'tolerancia_minutos': request.form.get('tolerancia_minutos', 0),
                'ativo': bool(request.form.get('ativo'))
            }
            
            # Validar dados
            validacao = validar_dados_horario(dados)
            
            if not validacao['valido']:
                for erro in validacao['erros']:
                    flash(erro, 'error')
                return render_template('horarios/form.html',
                                     titulo='Editar Horário de Trabalho',
                                     horario=dados,
                                     empresas=obter_empresas_ativas(),
                                     acao='editar',
                                     horario_id=horario_id)
            
            # Verificar duplicata
            if verificar_horario_duplicado(dados['nome_horario'], dados['empresa_id'], horario_id):
                flash('Já existe um horário com este nome nesta empresa', 'error')
                return render_template('horarios/form.html',
                                     titulo='Editar Horário de Trabalho',
                                     horario=dados,
                                     empresas=obter_empresas_ativas(),
                                     acao='editar',
                                     horario_id=horario_id)
            
            # Atualizar no banco
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE horarios_trabalho SET
                    empresa_id = %s,
                    nome_horario = %s,
                    entrada_manha = %s,
                    saida_almoco = %s,
                    entrada_tarde = %s,
                    saida = %s,
                    tolerancia_minutos = %s,
                    ativo = %s,
                    data_atualizacao = NOW()
                WHERE id = %s
            """, (
                dados['empresa_id'],
                dados['nome_horario'],
                dados['entrada_manha'] if dados['entrada_manha'] else None,
                dados['saida_almoco'] if dados['saida_almoco'] else None,
                dados['entrada_tarde'] if dados['entrada_tarde'] else None,
                dados['saida'] if dados['saida'] else None,
                dados['tolerancia_minutos'],
                dados['ativo'],
                horario_id
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Horário ID {horario_id} atualizado")
            flash(f"Horário '{dados['nome_horario']}' atualizado com sucesso!", 'success')
            
            return redirect(url_for('horarios.index'))
            
        except Exception as e:
            logger.error(f"Erro ao atualizar horário: {str(e)}")
            flash('Erro ao atualizar horário', 'error')
    
    # GET - Buscar horário e mostrar formulário
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT h.*, e.razao_social
            FROM horarios_trabalho h
            INNER JOIN empresas e ON h.empresa_id = e.id
            WHERE h.id = %s
        """, (horario_id,))
        
        horario = cursor.fetchone()
        conn.close()
        
        if not horario:
            flash('Horário não encontrado', 'error')
            return redirect(url_for('horarios.index'))
        
        # Formatar horários para o formulário
        horario_data = {
            'nome_horario': horario['nome_horario'],
            'empresa_id': horario['empresa_id'],
            'entrada_manha': horario['entrada_manha'].strftime('%H:%M') if horario['entrada_manha'] else '',
            'saida_almoco': horario['saida_almoco'].strftime('%H:%M') if horario['saida_almoco'] else '',
            'entrada_tarde': horario['entrada_tarde'].strftime('%H:%M') if horario['entrada_tarde'] else '',
            'saida': horario['saida'].strftime('%H:%M') if horario['saida'] else '',
            'tolerancia_minutos': horario['tolerancia_minutos'],
            'ativo': horario['ativo']
        }
        
        context = {
            'titulo': f'Editar Horário: {horario["nome_horario"]}',
            'horario': horario_data,
            'empresas': obter_empresas_ativas(),
            'acao': 'editar',
            'horario_id': horario_id
        }
        
        return render_template('horarios/form.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao buscar horário: {str(e)}")
        flash('Erro ao carregar horário', 'error')
        return redirect(url_for('horarios.index'))

@horarios_bp.route('/<int:horario_id>/excluir', methods=['POST'])
@require_admin
def excluir_horario(horario_id):
    """
    Exclui um horário de trabalho.
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Verificar se há funcionários vinculados
        cursor.execute("""
            SELECT COUNT(*) as count, h.nome_horario
            FROM horarios_trabalho h
            LEFT JOIN funcionarios f ON h.id = f.horario_trabalho_id AND f.ativo = TRUE
            WHERE h.id = %s
            GROUP BY h.nome_horario
        """, (horario_id,))
        
        result = cursor.fetchone()
        
        if not result:
            flash('Horário não encontrado', 'error')
            return redirect(url_for('horarios.index'))
        
        if result['count'] > 0:
            flash(f'Não é possível excluir o horário "{result["nome_horario"]}" pois há {result["count"]} funcionário(s) vinculado(s)', 'error')
            return redirect(url_for('horarios.index'))
        
        # Excluir horário
        cursor.execute("DELETE FROM horarios_trabalho WHERE id = %s", (horario_id,))
        conn.commit()
        conn.close()
        
        logger.info(f"Horário ID {horario_id} excluído")
        flash(f'Horário "{result["nome_horario"]}" excluído com sucesso!', 'success')
        
    except Exception as e:
        logger.error(f"Erro ao excluir horário: {str(e)}")
        flash('Erro ao excluir horário', 'error')
    
    return redirect(url_for('horarios.index'))

@horarios_bp.route('/api/por-empresa/<int:empresa_id>')
@require_login
def api_horarios_por_empresa(empresa_id):
    """
    API para obter horários de uma empresa específica.
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, nome_horario, entrada_manha, saida_almoco, 
                   entrada_tarde, saida, tolerancia_minutos
            FROM horarios_trabalho 
            WHERE empresa_id = %s AND ativo = TRUE
            ORDER BY nome_horario
        """, (empresa_id,))
        
        horarios_raw = cursor.fetchall()
        conn.close()
        
        horarios = []
        for horario in horarios_raw:
            horarios.append({
                'id': horario['id'],
                'nome_horario': horario['nome_horario'],
                'entrada_manha': horario['entrada_manha'].strftime('%H:%M') if horario['entrada_manha'] else None,
                'saida_almoco': horario['saida_almoco'].strftime('%H:%M') if horario['saida_almoco'] else None,
                'entrada_tarde': horario['entrada_tarde'].strftime('%H:%M') if horario['entrada_tarde'] else None,
                'saida': horario['saida'].strftime('%H:%M') if horario['saida'] else None,
                'tolerancia_minutos': horario['tolerancia_minutos']
            })
        
        return jsonify({
            'success': True,
            'horarios': horarios
        })
        
    except Exception as e:
        logger.error(f"Erro ao buscar horários por empresa: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao buscar horários'
        }), 500

# ========================================
# HANDLERS DE ERRO
# ========================================

@horarios_bp.errorhandler(404)
def handle_404(error):
    return render_template('erro.html',
                         titulo="Página não encontrada",
                         mensagem="A página de horários que você procura não existe.",
                         codigo=404), 404

@horarios_bp.errorhandler(500)
def handle_500(error):
    logger.error(f"Erro 500 em horários: {error}")
    return render_template('erro.html',
                         titulo="Erro interno",
                         mensagem="Erro interno no módulo de horários.",
                         codigo=500), 500 