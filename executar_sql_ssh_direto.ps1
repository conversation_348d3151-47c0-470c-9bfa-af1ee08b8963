# Script PowerShell para executar SQL via SSH com senha
# RLPONTO-WEB - Sistema de Controle de Ponto

Write-Host "RLPONTO-WEB - Execucao SQL via SSH" -ForegroundColor Green
Write-Host "=================================="

# Configurações
$servidor = "************"
$usuario = "cavalcrod"
$senha = "200381"
$banco = "controle_ponto"
$arquivoSQL = "sql\empresa_principal_clientes.sql"

# Verificar se arquivo SQL existe
if (-not (Test-Path $arquivoSQL)) {
    Write-Host "Arquivo SQL nao encontrado: $arquivoSQL" -ForegroundColor Red
    exit 1
}

Write-Host "Arquivo SQL encontrado: $arquivoSQL" -ForegroundColor Green

# Ler conteúdo do arquivo SQL
$sqlContent = Get-Content $arquivoSQL -Raw -Encoding UTF8
Write-Host "Conteudo SQL carregado: $($sqlContent.Length) caracteres" -ForegroundColor Green

# Criar script temporário para execução no servidor
$scriptRemoto = @"
#!/bin/bash
echo "Executando SQL de empresa principal..."

# Criar arquivo SQL temporário
cat > /tmp/empresa_principal.sql << 'EOF'
$sqlContent
EOF

echo "Arquivo SQL criado em /tmp/empresa_principal.sql"

# Executar SQL
echo "Conectando ao MySQL..."
mysql -u $usuario -p$senha $banco < /tmp/empresa_principal.sql

if [ `$? -eq 0 ]; then
    echo "SQL executado com sucesso!"
    
    # Verificar estrutura criada
    echo "Verificando estrutura..."
    mysql -u $usuario -p$senha $banco -e "
    SELECT 'Verificacao da estrutura...' as status;
    SELECT COUNT(*) as colunas_empresas FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = '$banco' AND TABLE_NAME = 'empresas' 
    AND COLUMN_NAME IN ('empresa_principal', 'empresa_matriz_id', 'tipo_empresa');
    SELECT COUNT(*) as tabelas_criadas FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = '$banco' 
    AND TABLE_NAME IN ('empresa_clientes', 'funcionario_alocacoes', 'historico_alocacoes');
    "
else
    echo "Erro na execucao do SQL"
    exit 1
fi

# Limpar arquivo temporário
rm -f /tmp/empresa_principal.sql
echo "Arquivo temporario removido"
echo "Processo concluido!"
"@

# Salvar script temporário
$scriptFile = "temp_script_remoto.sh"
$scriptRemoto | Out-File -FilePath $scriptFile -Encoding UTF8

Write-Host "Script remoto criado: $scriptFile" -ForegroundColor Green

# Verificar se temos plink (PuTTY)
$plinkPath = Get-Command plink -ErrorAction SilentlyContinue
if ($plinkPath) {
    Write-Host "Usando plink para execucao..." -ForegroundColor Cyan
    
    try {
        # Copiar e executar script
        Get-Content $scriptFile | & plink -ssh -batch -pw $senha $usuario@$servidor "cat > /tmp/script_empresa.sh && chmod +x /tmp/script_empresa.sh && /tmp/script_empresa.sh && rm -f /tmp/script_empresa.sh"
        
        Write-Host "Script executado via plink!" -ForegroundColor Green
    } catch {
        Write-Host "Erro ao executar via plink: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "plink nao encontrado. Tentando metodo alternativo..." -ForegroundColor Yellow
    
    # Verificar se temos ssh nativo
    $sshPath = Get-Command ssh -ErrorAction SilentlyContinue
    if ($sshPath) {
        Write-Host "Usando ssh nativo..." -ForegroundColor Cyan
        
        try {
            # Usar sshpass se disponível, senão usar expect
            Write-Host "Executando comandos via SSH..." -ForegroundColor Yellow
            
            # Criar comando único para executar tudo
            $comandoCompleto = @"
echo '$sqlContent' > /tmp/empresa_principal.sql && 
mysql -u $usuario -p$senha $banco < /tmp/empresa_principal.sql && 
echo 'SQL executado com sucesso!' && 
mysql -u $usuario -p$senha $banco -e "SELECT COUNT(*) as colunas FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$banco' AND TABLE_NAME = 'empresas' AND COLUMN_NAME IN ('empresa_principal', 'tipo_empresa');" && 
rm -f /tmp/empresa_principal.sql
"@
            
            # Executar via SSH (vai pedir senha)
            Write-Host "ATENCAO: Digite a senha quando solicitado: $senha" -ForegroundColor Yellow
            & ssh $usuario@$servidor $comandoCompleto
            
        } catch {
            Write-Host "Erro ao executar via ssh: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "Nem plink nem ssh estao disponiveis!" -ForegroundColor Red
        Write-Host "Instale PuTTY ou OpenSSH para continuar" -ForegroundColor Yellow
        exit 1
    }
}

# Limpar arquivos temporários
if (Test-Path $scriptFile) {
    Remove-Item $scriptFile -Force
    Write-Host "Script temporario removido" -ForegroundColor Gray
}

Write-Host "Processo de execucao SQL concluido!" -ForegroundColor Green
