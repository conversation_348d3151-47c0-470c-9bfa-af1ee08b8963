#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de debug para testar a funcionalidade de clientes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database import DatabaseManager
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_connection():
    """Testar conexão com o banco de dados"""
    try:
        db = DatabaseManager()
        result = db.execute_query("SELECT 1 as test")
        print("✅ Conexão com banco de dados: OK")
        return True
    except Exception as e:
        print(f"❌ Erro na conexão com banco: {e}")
        return False

def test_tables_exist():
    """Verificar se as tabelas necessárias existem"""
    tables_to_check = [
        'empresas',
        'empresa_clientes', 
        'funcionario_alocacoes',
        'jornadas_trabalho'
    ]
    
    try:
        db = DatabaseManager()
        for table in tables_to_check:
            result = db.execute_query(f"SHOW TABLES LIKE '{table}'")
            if result:
                print(f"✅ Tabela {table}: Existe")
            else:
                print(f"❌ Tabela {table}: NÃO EXISTE")
                
    except Exception as e:
        print(f"❌ Erro ao verificar tabelas: {e}")

def test_empresa_principal():
    """Testar função get_empresa_principal"""
    try:
        from app_empresa_principal import get_empresa_principal
        empresa = get_empresa_principal()
        if empresa:
            print(f"✅ Empresa principal encontrada: {empresa.get('razao_social', 'N/A')}")
            return empresa
        else:
            print("❌ Nenhuma empresa principal definida")
            return None
    except Exception as e:
        print(f"❌ Erro ao buscar empresa principal: {e}")
        return None

def test_clientes():
    """Testar função get_clientes_da_empresa_principal"""
    try:
        from app_empresa_principal import get_clientes_da_empresa_principal
        clientes = get_clientes_da_empresa_principal()
        print(f"✅ Clientes encontrados: {len(clientes) if clientes else 0}")
        if clientes:
            for i, cliente in enumerate(clientes[:3]):  # Mostrar apenas os 3 primeiros
                print(f"   Cliente {i+1}: {cliente.get('razao_social', 'N/A')}")
        return clientes
    except Exception as e:
        print(f"❌ Erro ao buscar clientes: {e}")
        return None

def test_empresas_disponiveis():
    """Testar função get_empresas_disponiveis_para_cliente"""
    try:
        from app_empresa_principal import get_empresas_disponiveis_para_cliente
        empresas = get_empresas_disponiveis_para_cliente()
        print(f"✅ Empresas disponíveis: {len(empresas) if empresas else 0}")
        if empresas:
            for i, empresa in enumerate(empresas[:3]):  # Mostrar apenas as 3 primeiras
                print(f"   Empresa {i+1}: {empresa.get('razao_social', 'N/A')}")
        return empresas
    except Exception as e:
        print(f"❌ Erro ao buscar empresas disponíveis: {e}")
        return None

def test_template_render():
    """Testar renderização do template"""
    try:
        from flask import Flask
        from app_empresa_principal import empresa_principal_bp
        
        app = Flask(__name__)
        app.register_blueprint(empresa_principal_bp)
        
        with app.test_client() as client:
            # Simular uma requisição
            response = client.get('/empresa-principal/clientes')
            print(f"✅ Template response status: {response.status_code}")
            if response.status_code != 200:
                print(f"❌ Erro no template: {response.data.decode()}")
        
    except Exception as e:
        print(f"❌ Erro ao testar template: {e}")

def main():
    """Função principal de teste"""
    print("🔍 INICIANDO DIAGNÓSTICO DO BOTÃO GERENCIAR CLIENTES")
    print("=" * 60)
    
    # Teste 1: Conexão com banco
    print("\n1. Testando conexão com banco de dados...")
    if not test_database_connection():
        return
    
    # Teste 2: Verificar tabelas
    print("\n2. Verificando estrutura das tabelas...")
    test_tables_exist()
    
    # Teste 3: Empresa principal
    print("\n3. Testando busca da empresa principal...")
    empresa = test_empresa_principal()
    
    # Teste 4: Clientes
    print("\n4. Testando busca de clientes...")
    clientes = test_clientes()
    
    # Teste 5: Empresas disponíveis
    print("\n5. Testando busca de empresas disponíveis...")
    empresas = test_empresas_disponiveis()
    
    # Teste 6: Template
    print("\n6. Testando renderização do template...")
    test_template_render()
    
    print("\n" + "=" * 60)
    print("🏁 DIAGNÓSTICO CONCLUÍDO")

if __name__ == '__main__':
    main()
