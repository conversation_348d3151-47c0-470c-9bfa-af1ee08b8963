#!/usr/bin/env python3
"""
TRACE FINAL - Identificar o erro real das configurações
Vamos testar step by step
"""

import sys
import os

print("="*60)
print("🔍 RASTREAMENTO FINAL DO ERRO DAS CONFIGURAÇÕES")
print("="*60)

# Adicionar o diretório atual ao path
sys.path.insert(0, os.getcwd())

step = 1
def log_step(desc):
    global step
    print(f"\n{step}. {desc}")
    step += 1

try:
    log_step("Importando módulos...")
    
    # Importar app_configuracoes primeiro
    print("   📦 Importando app_configuracoes...")
    try:
        import app_configuracoes
        print("   ✅ app_configuracoes importado com sucesso")
    except Exception as e:
        print(f"   ❌ Erro ao importar app_configuracoes: {e}")
        raise
    
    # Importar app principal
    print("   📦 Importando app...")
    try:
        from app import app
        print("   ✅ app importado com sucesso")
    except Exception as e:
        print(f"   ❌ Erro ao importar app: {e}")
        raise
    
    log_step("Testando função diretamente...")
    
    # Testar função obter_configuracoes_por_categoria
    try:
        configs = app_configuracoes.obter_configuracoes_por_categoria('sistema')
        print(f"   ✅ Função retornou {len(configs)} configurações")
    except Exception as e:
        print(f"   ❌ Erro na função: {e}")
        import traceback
        traceback.print_exc()
    
    log_step("Testando rota via test client...")
    
    # Criar test client
    client = app.test_client()
    
    # Configurar contexto de aplicação
    with app.app_context():
        # Configurar sessão admin
        with client.session_transaction() as sess:
            sess['usuario'] = 'admin'
            sess['nivel_acesso'] = 'admin'
            sess['force_password_change'] = False
        
        print("   🌐 Fazendo requisição GET /configuracoes/...")
        
        # Fazer requisição
        response = client.get('/configuracoes/')
        
        print(f"   📊 Status: {response.status_code}")
        print(f"   📏 Tamanho: {len(response.data)} bytes")
        
        if response.status_code == 500:
            print("\n❌ ERRO 500 CONFIRMADO!")
            print("\n📜 CONTEÚDO DO ERRO:")
            print("-" * 50)
            content = response.data.decode('utf-8', errors='ignore')
            # Procurar por traceback
            if 'Traceback' in content:
                lines = content.split('\n')
                in_traceback = False
                for line in lines:
                    if 'Traceback' in line:
                        in_traceback = True
                    if in_traceback:
                        print(line)
                        if line.strip() and not line.startswith(' ') and 'Traceback' not in line:
                            break
            else:
                print(content[:1500])
            print("-" * 50)
            
        elif response.status_code == 200:
            print("\n✅ SUCESSO! Página carregada sem erros!")
            
        elif response.status_code == 302:
            print(f"\n🔄 Redirecionamento para: {response.headers.get('Location', 'N/A')}")
            
        else:
            print(f"\n⚠️ Status inesperado: {response.status_code}")

    log_step("Verificando logs da aplicação...")
    
    # Verificar se há logs recentes
    if os.path.exists('logs/app.log'):
        print("   📄 Verificando logs recentes...")
        with open('logs/app.log', 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            recent_logs = lines[-10:]  # Últimas 10 linhas
            for line in recent_logs:
                if 'ERROR' in line or 'configurações' in line:
                    print(f"   🔍 Log: {line.strip()}")
    
    print("\n" + "="*60)
    print("🏁 TRACE CONCLUÍDO")
    print("="*60)

except Exception as e:
    print(f"\n❌ ERRO CRÍTICO NO TRACE: {e}")
    import traceback
    traceback.print_exc() 