# -*- coding: utf-8 -*-
"""
Sistema de Administração de Ponto - RLPONTO-WEB
Módulo para gerenciamento completo de registros de ponto

Desenvolvido por: AiNexus Tecnologia
Autor: <PERSON> - Full Stack Developer
Data: 08/07/2025
Versão: 1.0
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, session, send_file
from werkzeug.utils import secure_filename
import logging
import os
from datetime import datetime, date, time, timedelta
from decimal import Decimal
import json
import uuid
from utils.database import DatabaseManager
from utils.auth import require_admin, require_login
import uuid

# Configuração do logger
logger = logging.getLogger('controle-ponto.ponto_admin')

# Criar blueprint
ponto_admin_bp = Blueprint('ponto_admin', __name__, url_prefix='/ponto-admin')

# Configurações de upload
UPLOAD_FOLDER = 'uploads/documentos_ponto'
ALLOWED_EXTENSIONS = {'pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'}

def allowed_file(filename):
    """Verificar se o arquivo é permitido"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_folder():
    """Garantir que a pasta de upload existe"""
    if not os.path.exists(UPLOAD_FOLDER):
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# ================================================================
# ROTAS PRINCIPAIS
# ================================================================

@ponto_admin_bp.route('/')
@require_login
def index():
    """Página principal de administração de ponto"""
    try:
        # Limpar mensagens flash antigas que podem estar causando problemas
        session.pop('_flashes', None)

        # Buscar estatísticas gerais
        stats = get_estatisticas_ponto()

        # Buscar funcionários com resumo de ponto
        funcionarios = get_funcionarios_resumo_ponto()

        return render_template('ponto_admin/index.html',
                             stats=stats,
                             funcionarios=funcionarios)
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Erro na página principal de ponto admin: {e}")
        logger.error(f"Detalhes do erro: {error_details}")
        flash(f'Erro ao carregar dados de ponto: {str(e)}', 'error')
        return redirect('/')

@ponto_admin_bp.route('/funcionario/<int:funcionario_id>')
@require_login
def detalhes_funcionario(funcionario_id):
    """Detalhes completos de ponto de um funcionário com histórico de alocações"""
    try:
        # Buscar dados do funcionário diretamente do banco
        db = DatabaseManager()

        sql = """
        SELECT
            f.*,
            e.razao_social as empresa_nome
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        WHERE f.id = %s
        """

        result = db.execute_query(sql, (funcionario_id,))
        funcionario = result[0] if result else None

        if not funcionario:
            flash('Funcionário não encontrado', 'error')
            return redirect(url_for('ponto_admin.index'))

        # Buscar dados reais do funcionário
        registros = get_registros_ponto_funcionario(funcionario_id)
        historico_alocacoes = get_historico_alocacoes_funcionario(funcionario_id)
        logs = get_logs_atividades_funcionario(funcionario_id)

        # Importar datetime e timedelta para o template
        from datetime import datetime, timedelta

        return render_template('ponto_admin/detalhes_funcionario.html',
                             funcionario=funcionario,
                             registros=registros,
                             historico_alocacoes=historico_alocacoes,
                             logs=logs,
                             datetime=datetime,
                             timedelta=timedelta)
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()

        logger.error(f"ERRO ao buscar detalhes do funcionário {funcionario_id}: {e}")
        logger.error(f"Detalhes completos do erro: {error_details}")
        flash('Erro ao carregar detalhes do funcionário', 'error')
        return redirect(url_for('ponto_admin.index'))

# ================================================================
# API ENDPOINTS
# ================================================================

@ponto_admin_bp.route('/api/funcionarios')
@require_admin
def api_funcionarios():
    """API: Lista de funcionários com dados de ponto"""
    try:
        funcionarios = get_funcionarios_resumo_ponto()
        
        # Converter tipos não serializáveis
        for funcionario in funcionarios:
            for key, value in funcionario.items():
                if isinstance(value, (date, datetime, time)):
                    funcionario[key] = value.isoformat() if hasattr(value, 'isoformat') else str(value)
                elif isinstance(value, Decimal):
                    funcionario[key] = float(value)
        
        return jsonify({'success': True, 'funcionarios': funcionarios})
    except Exception as e:
        logger.error(f"Erro na API funcionários: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@ponto_admin_bp.route('/api/funcionario/<int:funcionario_id>/registros')
@require_admin
def api_registros_funcionario(funcionario_id):
    """API: Registros de ponto de um funcionário"""
    try:
        # Parâmetros de filtro
        data_inicio = request.args.get('data_inicio')
        data_fim = request.args.get('data_fim')
        
        registros = get_registros_ponto_funcionario(funcionario_id, data_inicio, data_fim)
        
        # Converter tipos não serializáveis
        for registro in registros:
            for key, value in registro.items():
                if isinstance(value, (date, datetime, time)):
                    registro[key] = value.isoformat() if hasattr(value, 'isoformat') else str(value)
                elif isinstance(value, Decimal):
                    registro[key] = float(value)
        
        return jsonify({'success': True, 'registros': registros})
    except Exception as e:
        logger.error(f"Erro na API registros funcionário {funcionario_id}: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@ponto_admin_bp.route('/api/editar-registro', methods=['POST'])
@require_admin
def api_editar_registro():
    """API: Editar registro de ponto"""
    try:
        data = request.get_json()
        registro_id = data.get('registro_id')
        campo = data.get('campo')
        valor = data.get('valor')
        justificativa = data.get('justificativa', '')
        
        if not all([registro_id, campo, valor]):
            return jsonify({'success': False, 'message': 'Dados incompletos'})
        
        # Validar campo permitido
        campos_permitidos = ['entrada', 'saida_almoco', 'retorno_almoco', 'saida']
        if campo not in campos_permitidos:
            return jsonify({'success': False, 'message': 'Campo não permitido'})
        
        # Editar registro
        sucesso = editar_registro_ponto(registro_id, campo, valor, justificativa)
        
        if sucesso:
            # Registrar log de atividade
            registrar_log_atividade(
                funcionario_id=data.get('funcionario_id'),
                acao='EDICAO_PONTO',
                detalhes=f"Campo {campo} alterado para {valor}. Justificativa: {justificativa}",
                usuario_id=session.get('user_id')
            )
            
            return jsonify({'success': True, 'message': 'Registro editado com sucesso'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao editar registro'})
            
    except Exception as e:
        logger.error(f"Erro ao editar registro: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

# ================================================================
# FUNÇÕES AUXILIARES
# ================================================================

def get_estatisticas_ponto():
    """Obter estatísticas gerais de ponto"""
    try:
        db = DatabaseManager()
        
        # Estatísticas do mês atual
        sql = """
        SELECT
            COUNT(DISTINCT rp.funcionario_id) as funcionarios_com_ponto,
            COUNT(rp.id) as total_registros,
            COUNT(CASE WHEN rp.tipo_registro = 'entrada_manha' AND rp.data_registro IS NULL THEN 1 END) as faltas,
            COUNT(CASE WHEN rp.observacoes IS NOT NULL THEN 1 END) as registros_justificados
        FROM registros_ponto rp
        WHERE MONTH(rp.data_registro) = MONTH(CURRENT_DATE())
        AND YEAR(rp.data_registro) = YEAR(CURRENT_DATE())
        """
        
        result = db.execute_query(sql)
        return result[0] if result else {}
        
    except Exception as e:
        logger.error(f"Erro ao obter estatísticas: {e}")
        return {}

def get_funcionarios_resumo_ponto():
    """Obter lista de funcionários com resumo de ponto incluindo histórico de alocações"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT
            f.id,
            f.nome_completo,
            f.cargo,
            f.setor,
            f.status_cadastro,
            e.razao_social as empresa_nome,
            COUNT(rp.id) as registros_mes,
            COUNT(CASE WHEN rp.tipo_registro = 'entrada_manha' AND rp.data_registro IS NULL THEN 1 END) as faltas_mes,
            COUNT(CASE WHEN rp.observacoes IS NOT NULL THEN 1 END) as justificativas_mes,
            MAX(rp.data_registro) as ultimo_ponto,
            0 as clientes_trabalhados,
            '' as clientes_recentes
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN registros_ponto rp ON f.id = rp.funcionario_id
            AND MONTH(rp.data_registro) = MONTH(CURRENT_DATE())
            AND YEAR(rp.data_registro) = YEAR(CURRENT_DATE())
        WHERE f.status_cadastro = 'Ativo'
        GROUP BY f.id, f.nome_completo, f.cargo, f.setor, f.status_cadastro, e.razao_social
        ORDER BY f.nome_completo
        """

        return db.execute_query(sql)

    except Exception as e:
        logger.error(f"Erro ao obter funcionários: {e}")
        return []

def get_funcionario_detalhes(funcionario_id):
    """Obter detalhes completos de um funcionário"""
    try:
        db = DatabaseManager()
        
        sql = """
        SELECT 
            f.*,
            e.razao_social as empresa_nome,
            jt.nome_jornada,
            jt.seg_qui_entrada,
            jt.seg_qui_saida,
            jt.sexta_entrada,
            jt.sexta_saida,
            jt.intervalo_inicio,
            jt.intervalo_fim
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.id = %s
        """
        
        result = db.execute_query(sql, (funcionario_id,))
        return result[0] if result else None
        
    except Exception as e:
        logger.error(f"Erro ao obter detalhes do funcionário {funcionario_id}: {e}")
        return None

def get_registros_ponto_funcionario(funcionario_id, data_inicio=None, data_fim=None):
    """Obter registros de ponto de um funcionário agrupados por dia"""
    try:
        db = DatabaseManager()

        # Definir período padrão (últimos 30 dias para mostrar histórico)
        if not data_inicio:
            data_inicio = (datetime.now() - timedelta(days=30)).date()
        if not data_fim:
            data_fim = datetime.now().date()

        # Buscar todos os registros primeiro
        sql_registros = """
        SELECT
            DATE(data_hora) as data_trabalho,
            tipo_registro,
            TIME(data_hora) as hora_registro,
            observacoes,
            id
        FROM registros_ponto
        WHERE funcionario_id = %s
        AND DATE(data_hora) BETWEEN %s AND %s
        ORDER BY data_hora ASC
        """

        registros_raw = db.execute_query(sql_registros, (funcionario_id, data_inicio, data_fim))

        # Agrupar registros por data
        registros_agrupados = {}
        for registro in registros_raw:
            data = registro['data_trabalho']
            if data not in registros_agrupados:
                registros_agrupados[data] = {
                    'data': data,
                    'dia_semana': ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo'][data.weekday()],
                    'entrada': None,
                    'saida_almoco': None,
                    'retorno_almoco': None,
                    'saida': None,
                    'justificativa': None,
                    'status_trabalho': 'EMPRESA_PRINCIPAL',
                    'cliente_nome': 'Empresa Principal',
                    'cliente_fantasia': 'Sede',
                    'total_horas_dia': 8.0,
                    'id': registro['id']
                }

            # Mapear tipos de registro para campos
            # Converter timedelta para datetime.time se necessário
            hora = registro['hora_registro']
            if isinstance(hora, timedelta):
                # Converter timedelta para time
                total_seconds = int(hora.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                seconds = total_seconds % 60
                hora = time(hours, minutes, seconds)

            if registro['tipo_registro'] == 'entrada_manha':
                registros_agrupados[data]['entrada'] = hora
            elif registro['tipo_registro'] == 'saida_almoco':
                registros_agrupados[data]['saida_almoco'] = hora
            elif registro['tipo_registro'] == 'entrada_tarde':
                registros_agrupados[data]['retorno_almoco'] = hora
            elif registro['tipo_registro'] == 'saida':
                registros_agrupados[data]['saida'] = hora

            # Adicionar observações se houver
            if registro['observacoes']:
                if registros_agrupados[data]['justificativa']:
                    registros_agrupados[data]['justificativa'] += '; ' + registro['observacoes']
                else:
                    registros_agrupados[data]['justificativa'] = registro['observacoes']

        # Converter para lista e ordenar por data decrescente
        resultado = list(registros_agrupados.values())
        resultado.sort(key=lambda x: x['data'], reverse=True)

        return resultado

    except Exception as e:
        logger.error(f"Erro ao obter registros do funcionário {funcionario_id}: {e}")
        return []

def get_historico_alocacoes_funcionario(funcionario_id):
    """Obter histórico de alocações de um funcionário"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT
            fa.id,
            fa.data_inicio,
            fa.data_fim,
            fa.ativo,
            e.razao_social as cliente_nome,
            e.nome_fantasia as cliente_fantasia,
            fa.observacoes
        FROM funcionario_alocacoes fa
        LEFT JOIN empresas e ON fa.empresa_cliente_id = e.id
        WHERE fa.funcionario_id = %s
        ORDER BY fa.data_inicio DESC
        """

        return db.execute_query(sql, (funcionario_id,))

    except Exception as e:
        logger.error(f"Erro ao obter histórico de alocações do funcionário {funcionario_id}: {e}")
        return []

def get_historico_alocacoes_funcionario(funcionario_id):
    """Obter histórico completo de alocações de um funcionário"""
    try:
        # Retornar lista vazia por enquanto para evitar erros
        return []
    except Exception as e:
        logger.error(f"Erro ao obter histórico de alocações do funcionário {funcionario_id}: {e}")
        return []

def editar_registro_ponto(registro_id, campo, valor, justificativa):
    """Editar um registro de ponto"""
    try:
        db = DatabaseManager()

        # Validar e converter valor
        if valor and valor != '':
            try:
                # Converter para time se for horário
                if ':' in str(valor):
                    valor = datetime.strptime(str(valor), '%H:%M').time()
            except ValueError:
                logger.error(f"Formato de hora inválido: {valor}")
                return False
        else:
            valor = None

        # Atualizar registro
        sql = f"""
        UPDATE registros_ponto
        SET {campo} = %s,
            justificativa = CASE
                WHEN justificativa IS NULL OR justificativa = '' THEN %s
                ELSE CONCAT(justificativa, ' | ', %s)
            END,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """

        db.execute_query(sql, (valor, justificativa, justificativa, registro_id), fetch_all=False)
        return True

    except Exception as e:
        logger.error(f"Erro ao editar registro {registro_id}: {e}")
        return False

def get_logs_atividades_funcionario(funcionario_id, limite=50):
    """Obter logs de atividades de um funcionário"""
    try:
        # Retornar lista vazia por enquanto para evitar erros
        return []
    except Exception as e:
        logger.error(f"Erro ao obter logs do funcionário {funcionario_id}: {e}")
        return []

def registrar_log_atividade(funcionario_id, acao, detalhes, usuario_id, registro_ponto_id=None):
    """Registrar log de atividade"""
    try:
        db = DatabaseManager()

        # Buscar nome do usuário
        sql_usuario = "SELECT nome FROM usuarios WHERE id = %s"
        usuario_result = db.execute_query(sql_usuario, (usuario_id,))
        usuario_nome = usuario_result[0]['nome'] if usuario_result else 'Sistema'

        sql = """
        INSERT INTO logs_atividades
        (funcionario_id, acao, detalhes, usuario_id, usuario_nome, registro_ponto_id, data_acao)
        VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
        """

        db.execute_query(sql, (funcionario_id, acao, detalhes, usuario_id, usuario_nome, registro_ponto_id), fetch_all=False)
        return True

    except Exception as e:
        logger.error(f"Erro ao registrar log: {e}")
        return False

# ================================================================
# ROTAS DE UPLOAD E DOCUMENTOS
# ================================================================

@ponto_admin_bp.route('/upload-documento', methods=['POST'])
@require_admin
def upload_documento():
    """Upload de documento comprobatório"""
    try:
        ensure_upload_folder()

        if 'documento' not in request.files:
            return jsonify({'success': False, 'message': 'Nenhum arquivo selecionado'})

        file = request.files['documento']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'Nenhum arquivo selecionado'})

        if file and allowed_file(file.filename):
            # Gerar nome único
            filename = secure_filename(file.filename)
            unique_filename = f"{uuid.uuid4()}_{filename}"
            filepath = os.path.join(UPLOAD_FOLDER, unique_filename)

            file.save(filepath)

            # Salvar referência no banco
            registro_id = request.form.get('registro_id')
            funcionario_id = request.form.get('funcionario_id')
            descricao = request.form.get('descricao', '')

            sucesso = salvar_documento_banco(registro_id, funcionario_id, unique_filename, descricao)

            if sucesso:
                # Registrar log
                registrar_log_atividade(
                    funcionario_id=funcionario_id,
                    acao='UPLOAD_DOCUMENTO',
                    detalhes=f"Documento enviado: {filename}. Descrição: {descricao}",
                    usuario_id=session.get('user_id'),
                    registro_ponto_id=registro_id
                )

                return jsonify({'success': True, 'message': 'Documento enviado com sucesso', 'filename': unique_filename})
            else:
                # Remover arquivo se falhou ao salvar no banco
                os.remove(filepath)
                return jsonify({'success': False, 'message': 'Erro ao salvar documento no banco'})
        else:
            return jsonify({'success': False, 'message': 'Tipo de arquivo não permitido'})

    except Exception as e:
        logger.error(f"Erro no upload de documento: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

def salvar_documento_banco(registro_id, funcionario_id, filename, descricao):
    """Salvar referência do documento no banco"""
    try:
        db = DatabaseManager()

        sql = """
        INSERT INTO documentos_ponto
        (registro_ponto_id, funcionario_id, nome_arquivo, descricao, usuario_upload_id, data_upload)
        VALUES (%s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
        """

        db.execute_query(sql, (registro_id, funcionario_id, filename, descricao, session.get('user_id')), fetch_all=False)
        return True

    except Exception as e:
        logger.error(f"Erro ao salvar documento no banco: {e}")
        return False

# ================================================================
# ROTAS DE RELATÓRIOS
# ================================================================

@ponto_admin_bp.route('/relatorio-funcionario/<int:funcionario_id>')
@require_admin
def relatorio_funcionario(funcionario_id):
    """Gerar relatório de ponto de um funcionário"""
    try:
        funcionario = get_funcionario_detalhes(funcionario_id)
        if not funcionario:
            flash('Funcionário não encontrado', 'error')
            return redirect(url_for('ponto_admin.index'))

        # Parâmetros de filtro
        data_inicio = request.args.get('data_inicio')
        data_fim = request.args.get('data_fim')

        registros = get_registros_ponto_funcionario(funcionario_id, data_inicio, data_fim)

        return render_template('ponto_admin/relatorio_funcionario.html',
                             funcionario=funcionario,
                             registros=registros,
                             data_inicio=data_inicio,
                             data_fim=data_fim)
    except Exception as e:
        logger.error(f"Erro ao gerar relatório do funcionário {funcionario_id}: {e}")
        flash('Erro ao gerar relatório', 'error')
        return redirect(url_for('ponto_admin.index'))

# ================================================================
# ROTAS PARA SISTEMA DE JUSTIFICATIVAS
# ================================================================

@ponto_admin_bp.route('/api/registro-detalhes/<int:funcionario_id>/<data_registro>')
@require_login
def api_registro_detalhes(funcionario_id, data_registro):
    """API: Buscar detalhes completos de um registro de ponto"""
    try:
        logger.info(f"🔍 API chamada: funcionario_id={funcionario_id}, data_registro={data_registro}")
        db = DatabaseManager()
        logger.info("✅ DatabaseManager inicializado")

        # Buscar registros do dia
        sql_registros = """
        SELECT
            id,
            tipo_registro,
            TIME(data_hora) as hora_registro,
            metodo_registro,
            status_pontualidade,
            observacoes
        FROM registros_ponto
        WHERE funcionario_id = %s
        AND DATE(data_hora) = %s
        ORDER BY data_hora ASC
        """

        logger.info(f"📊 Executando query: {sql_registros}")
        logger.info(f"📊 Parâmetros: funcionario_id={funcionario_id}, data_registro={data_registro}")
        registros_raw = db.execute_query(sql_registros, (funcionario_id, data_registro))
        logger.info(f"📊 Registros encontrados: {len(registros_raw) if registros_raw else 0}")
        logger.info(f"📊 Dados brutos: {registros_raw}")

        # Agrupar registros
        registro = {
            'entrada': None,
            'saida_almoco': None,
            'retorno_almoco': None,
            'saida': None,
            'metodo_registro': 'manual',
            'status_pontualidade': 'Pontual'
        }

        # ✅ CORREÇÃO: Variáveis para controlar status específico por tipo
        tem_saida = False
        status_saida = None
        status_geral = 'Pontual'
        justificativa_observacoes = None  # ✅ NOVO: Para capturar justificativa das observações

        for reg in registros_raw:
            hora = reg['hora_registro']
            if isinstance(hora, timedelta):
                total_seconds = int(hora.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                hora = f"{hours:02d}:{minutes:02d}"
            else:
                hora = str(hora)[:5]  # HH:MM

            if reg['tipo_registro'] == 'entrada_manha':
                registro['entrada'] = hora
            elif reg['tipo_registro'] == 'saida_almoco':
                registro['saida_almoco'] = hora
            elif reg['tipo_registro'] == 'entrada_tarde':
                registro['retorno_almoco'] = hora
            elif reg['tipo_registro'] == 'saida':
                registro['saida'] = hora
                tem_saida = True
                # ✅ CORREÇÃO: Só considerar "Saída Antecipada" se for registro de SAÍDA
                if reg['status_pontualidade'] == 'Saída Antecipada':
                    status_saida = 'Saída Antecipada'
                    # ✅ NOVO: Extrair justificativa das observações
                    if reg['observacoes'] and 'SAÍDA ANTECIPADA -' in reg['observacoes']:
                        justificativa_observacoes = reg['observacoes'].replace('SAÍDA ANTECIPADA - ', '').strip()

            # Pegar dados do último registro (exceto status_pontualidade)
            if reg['metodo_registro']:
                registro['metodo_registro'] = reg['metodo_registro']

            # ✅ CORREÇÃO: Acumular status de irregularidades (exceto saída antecipada sem saída)
            if reg['status_pontualidade'] and reg['status_pontualidade'] != 'Pontual':
                if reg['tipo_registro'] != 'saida' or reg['status_pontualidade'] != 'Saída Antecipada':
                    # Para outros tipos de irregularidade (atraso, etc.)
                    if reg['status_pontualidade'] == 'Atrasado':
                        status_geral = 'Atrasado'

        # ✅ CORREÇÃO: Determinar status final baseado na lógica correta
        if tem_saida and status_saida == 'Saída Antecipada':
            registro['status_pontualidade'] = 'Saída Antecipada'
        elif status_geral != 'Pontual':
            registro['status_pontualidade'] = status_geral
        else:
            registro['status_pontualidade'] = 'Pontual'

        # Buscar justificativa se existir
        sql_justificativa = """
        SELECT
            id,
            tipo_justificativa,
            motivo,
            descricao_funcionario,
            documento_nome,
            documento_caminho,
            status_aprovacao,
            observacoes_aprovador,
            data_aprovacao
        FROM justificativas_ponto
        WHERE funcionario_id = %s
        AND data_registro = %s
        ORDER BY criado_em DESC
        LIMIT 1
        """

        justificativa_result = db.execute_query(sql_justificativa, (funcionario_id, data_registro))
        justificativa = justificativa_result[0] if justificativa_result else None

        # ✅ NOVO: Se não há justificativa na tabela, mas há nas observações, criar justificativa temporária
        if not justificativa and justificativa_observacoes:
            justificativa = {
                'id': None,
                'tipo_justificativa': 'saida_antecipada',
                'motivo': justificativa_observacoes,
                'descricao_funcionario': None,
                'documento_nome': None,
                'documento_caminho': None,
                'status_aprovacao': 'pendente',
                'observacoes_aprovador': None,
                'data_aprovacao': None
            }
            logger.info(f"✅ Justificativa extraída das observações: {justificativa_observacoes}")

        # Converter data_aprovacao se existir
        if justificativa and justificativa.get('data_aprovacao'):
            justificativa['data_aprovacao'] = justificativa['data_aprovacao'].strftime('%Y-%m-%dT%H:%M')

        logger.info(f"✅ Retornando dados: registro={registro}, justificativa={justificativa}")
        return jsonify({
            'success': True,
            'registro': registro,
            'justificativa': justificativa
        })

    except Exception as e:
        import traceback
        logger.error(f"❌ Erro ao buscar detalhes do registro: {e}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'Erro interno: {str(e)}'})

@ponto_admin_bp.route('/salvar-registro', methods=['POST'])
@require_login
def salvar_registro():
    """Salvar alterações no registro de ponto e justificativas"""
    try:
        db = DatabaseManager()

        # Dados do formulário
        funcionario_id = request.form.get('funcionario_id')
        data_registro = request.form.get('data_registro')

        # Horários
        entrada = request.form.get('entrada')
        saida_almoco = request.form.get('saida_almoco')
        retorno_almoco = request.form.get('retorno_almoco')
        saida = request.form.get('saida')

        # Outros dados
        metodo_registro = request.form.get('metodo_registro', 'manual')
        status_pontualidade = request.form.get('status_pontualidade', 'Pontual')

        # Dados da justificativa
        tipo_justificativa = request.form.get('tipo_justificativa')
        motivo = request.form.get('motivo')
        descricao_funcionario = request.form.get('descricao_funcionario')
        status_aprovacao = request.form.get('status_aprovacao', 'pendente')
        observacoes_aprovador = request.form.get('observacoes_aprovador')
        data_aprovacao = request.form.get('data_aprovacao')

        # Processar upload de documento
        documento_info = None
        if 'documento' in request.files:
            file = request.files['documento']
            if file and file.filename and allowed_file(file.filename):
                documento_info = processar_upload_documento(file, funcionario_id)

        # Atualizar/criar registros de ponto
        sucesso_registros = atualizar_registros_ponto(
            funcionario_id, data_registro, entrada, saida_almoco,
            retorno_almoco, saida, metodo_registro, status_pontualidade
        )

        # Salvar justificativa se fornecida
        if tipo_justificativa or motivo:
            sucesso_justificativa = salvar_justificativa(
                funcionario_id, data_registro, tipo_justificativa, motivo,
                descricao_funcionario, status_aprovacao, observacoes_aprovador,
                data_aprovacao, documento_info
            )
        else:
            # ✅ NOVO: Verificar se há justificativa nas observações que precisa ser migrada
            sucesso_justificativa = verificar_e_migrar_justificativa_observacoes(
                funcionario_id, data_registro
            )

        if sucesso_registros and sucesso_justificativa:
            # Registrar log de atividade
            registrar_log_atividade(
                funcionario_id=funcionario_id,
                acao='EDICAO_REGISTRO_COMPLETA',
                detalhes=f"Registro de {data_registro} editado completamente",
                usuario_id=session.get('user_id')
            )

            return jsonify({'success': True, 'message': 'Registro salvo com sucesso'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao salvar algumas informações'})

    except Exception as e:
        logger.error(f"Erro ao salvar registro: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

def processar_upload_documento(file, funcionario_id):
    """Processar upload de documento"""
    try:
        # Criar diretório se não existir
        upload_dir = '/var/www/controle-ponto/uploads/justificativas'
        os.makedirs(upload_dir, exist_ok=True)

        # Gerar nome único
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        filepath = os.path.join(upload_dir, unique_filename)

        # Salvar arquivo
        file.save(filepath)

        return {
            'nome_original': filename,
            'nome_arquivo': unique_filename,
            'caminho': filepath,
            'tipo': file.content_type,
            'tamanho': os.path.getsize(filepath)
        }

    except Exception as e:
        logger.error(f"Erro no upload de documento: {e}")
        return None

def atualizar_registros_ponto(funcionario_id, data_registro, entrada, saida_almoco, retorno_almoco, saida, metodo_registro, status_pontualidade):
    """Atualizar registros de ponto de um dia"""
    try:
        db = DatabaseManager()

        # Mapear horários para tipos de registro
        horarios = {
            'entrada_manha': entrada,
            'saida_almoco': saida_almoco,
            'entrada_tarde': retorno_almoco,
            'saida': saida
        }

        for tipo_registro, horario in horarios.items():
            if horario:  # Se horário foi fornecido
                # Verificar se registro já existe
                sql_check = """
                SELECT id FROM registros_ponto
                WHERE funcionario_id = %s
                AND DATE(data_hora) = %s
                AND tipo_registro = %s
                """

                existing = db.execute_query(sql_check, (funcionario_id, data_registro, tipo_registro))

                # Criar datetime completo
                data_hora = f"{data_registro} {horario}:00"

                if existing:
                    # Atualizar registro existente
                    sql_update = """
                    UPDATE registros_ponto
                    SET data_hora = %s,
                        metodo_registro = %s,
                        status_pontualidade = %s,
                        atualizado_em = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """
                    db.execute_query(sql_update, (data_hora, metodo_registro, status_pontualidade, existing[0]['id']), fetch_all=False)
                else:
                    # Criar novo registro
                    sql_insert = """
                    INSERT INTO registros_ponto
                    (funcionario_id, data_hora, tipo_registro, metodo_registro, status_pontualidade, criado_em)
                    VALUES (%s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                    """
                    db.execute_query(sql_insert, (funcionario_id, data_hora, tipo_registro, metodo_registro, status_pontualidade), fetch_all=False)

        return True

    except Exception as e:
        logger.error(f"Erro ao atualizar registros de ponto: {e}")
        return False

def salvar_justificativa(funcionario_id, data_registro, tipo_justificativa, motivo, descricao_funcionario, status_aprovacao, observacoes_aprovador, data_aprovacao, documento_info):
    """Salvar ou atualizar justificativa com processamento de banco de horas"""
    try:
        db = DatabaseManager()

        # Verificar se justificativa já existe
        sql_check = """
        SELECT id, status_aprovacao FROM justificativas_ponto
        WHERE funcionario_id = %s AND data_registro = %s
        """

        existing = db.execute_query(sql_check, (funcionario_id, data_registro))

        # ✅ NOVO: Detectar mudança no status de aprovação
        status_anterior = existing[0]['status_aprovacao'] if existing else None
        mudou_status = status_anterior != status_aprovacao and status_aprovacao in ['aprovada', 'reprovada']

        logger.info(f"🔄 Status anterior: {status_anterior}, novo: {status_aprovacao}, mudou: {mudou_status}")

        # Preparar dados do documento
        documento_nome = documento_info['nome_original'] if documento_info else None
        documento_caminho = documento_info['caminho'] if documento_info else None
        documento_tipo = documento_info['tipo'] if documento_info else None
        documento_tamanho = documento_info['tamanho'] if documento_info else None

        # Preparar data de aprovação
        data_aprovacao_formatted = None
        if data_aprovacao:
            try:
                data_aprovacao_formatted = datetime.strptime(data_aprovacao, '%Y-%m-%dT%H:%M').strftime('%Y-%m-%d %H:%M:%S')
            except:
                data_aprovacao_formatted = None

        if existing:
            # Atualizar justificativa existente
            sql_update = """
            UPDATE justificativas_ponto
            SET tipo_justificativa = %s,
                motivo = %s,
                descricao_funcionario = %s,
                status_aprovacao = %s,
                observacoes_aprovador = %s,
                data_aprovacao = %s,
                aprovado_por = %s,
                atualizado_em = CURRENT_TIMESTAMP
            """

            params = [tipo_justificativa, motivo, descricao_funcionario, status_aprovacao,
                     observacoes_aprovador, data_aprovacao_formatted, session.get('user_id')]

            # Adicionar campos de documento se fornecido
            if documento_info:
                sql_update += """,
                    documento_nome = %s,
                    documento_caminho = %s,
                    documento_tipo = %s,
                    documento_tamanho = %s
                """
                params.extend([documento_nome, documento_caminho, documento_tipo, documento_tamanho])

            sql_update += " WHERE id = %s"
            params.append(existing[0]['id'])

            db.execute_query(sql_update, params, fetch_all=False)
        else:
            # Criar nova justificativa
            sql_insert = """
            INSERT INTO justificativas_ponto
            (funcionario_id, data_registro, tipo_justificativa, motivo, descricao_funcionario,
             status_aprovacao, observacoes_aprovador, data_aprovacao, aprovado_por,
             documento_nome, documento_caminho, documento_tipo, documento_tamanho,
             criado_por, criado_em)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
            """

            db.execute_query(sql_insert, (
                funcionario_id, data_registro, tipo_justificativa, motivo, descricao_funcionario,
                status_aprovacao, observacoes_aprovador, data_aprovacao_formatted, session.get('user_id'),
                documento_nome, documento_caminho, documento_tipo, documento_tamanho,
                session.get('user_id')
            ), fetch_all=False)

        # ✅ NOVO: Processar impacto no banco de horas se status mudou para aprovada/reprovada
        if mudou_status:
            logger.info(f"🔄 Processando impacto no banco de horas: {status_aprovacao}")
            try:
                from app_banco_horas import processar_impacto_banco_horas_direto
                resultado = processar_impacto_banco_horas_direto(
                    funcionario_id, data_registro, tipo_justificativa, status_aprovacao
                )
                logger.info(f"✅ Resultado do processamento: {resultado}")
            except Exception as e:
                logger.error(f"❌ Erro ao processar banco de horas: {e}")

        return True

    except Exception as e:
        logger.error(f"Erro ao salvar justificativa: {e}")
        return False

def verificar_e_migrar_justificativa_observacoes(funcionario_id, data_registro):
    """
    Verifica se há justificativa nas observações e migra para tabela justificativas_ponto
    """
    try:
        db = DatabaseManager()

        # Buscar registros com justificativa nas observações
        sql_buscar = """
        SELECT id, tipo_registro, observacoes, status_pontualidade
        FROM registros_ponto
        WHERE funcionario_id = %s
        AND DATE(data_hora) = %s
        AND observacoes IS NOT NULL
        AND observacoes LIKE '%SAÍDA ANTECIPADA -%'
        """

        registros_com_justificativa = db.execute_query(sql_buscar, (funcionario_id, data_registro))

        if registros_com_justificativa:
            for registro in registros_com_justificativa:
                observacoes = registro['observacoes']
                tipo_registro = registro['tipo_registro']

                # Extrair justificativa das observações
                if 'SAÍDA ANTECIPADA -' in observacoes:
                    justificativa_texto = observacoes.replace('SAÍDA ANTECIPADA - ', '').strip()

                    # Verificar se já existe na tabela justificativas_ponto
                    sql_check = """
                    SELECT id FROM justificativas_ponto
                    WHERE funcionario_id = %s AND data_registro = %s
                    """

                    existing = db.execute_query(sql_check, (funcionario_id, data_registro))

                    if not existing:
                        # Criar justificativa na tabela
                        sql_insert = """
                        INSERT INTO justificativas_ponto
                        (funcionario_id, data_registro, tipo_justificativa, motivo,
                         status_aprovacao, criado_por, criado_em)
                        VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                        """

                        db.execute_query(sql_insert, (
                            funcionario_id, data_registro, 'saida_antecipada', justificativa_texto,
                            'pendente', session.get('user_id')
                        ), fetch_all=False)

                        logger.info(f"✅ Justificativa migrada das observações para tabela: {justificativa_texto}")

        return True

    except Exception as e:
        logger.error(f"Erro ao verificar/migrar justificativa das observações: {e}")
        return True  # Não falhar o processo principal

@ponto_admin_bp.route('/api/historico-aprovacoes/<int:justificativa_id>')
@require_login
def api_historico_aprovacoes(justificativa_id):
    """API: Buscar histórico de aprovações de uma justificativa"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT
            ha.campo_alterado,
            ha.valor_anterior,
            ha.valor_novo,
            ha.motivo_alteracao,
            ha.data_alteracao,
            u.nome as aprovador
        FROM historico_alteracoes_ponto ha
        LEFT JOIN usuarios u ON ha.alterado_por = u.id
        WHERE ha.justificativa_id = %s
        ORDER BY ha.data_alteracao DESC
        """

        historico = db.execute_query(sql, (justificativa_id,))

        # Formatar dados
        historico_formatado = []
        for item in historico:
            historico_formatado.append({
                'status': f"{item['campo_alterado']}: {item['valor_anterior']} → {item['valor_novo']}",
                'data': item['data_alteracao'].strftime('%d/%m/%Y %H:%M'),
                'aprovador': item['aprovador'] or 'Sistema',
                'observacoes': item['motivo_alteracao']
            })

        return jsonify({
            'success': True,
            'historico': historico_formatado
        })

    except Exception as e:
        logger.error(f"Erro ao buscar histórico de aprovações: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@ponto_admin_bp.route('/download-documento/<int:justificativa_id>')
@require_login
def download_documento(justificativa_id):
    """Download de documento de justificativa"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT documento_nome, documento_caminho
        FROM justificativas_ponto
        WHERE id = %s
        """

        result = db.execute_query(sql, (justificativa_id,))

        if not result or not result[0]['documento_caminho']:
            flash('Documento não encontrado', 'error')
            return redirect(request.referrer or url_for('ponto_admin.index'))

        documento = result[0]

        if os.path.exists(documento['documento_caminho']):
            return send_file(
                documento['documento_caminho'],
                as_attachment=True,
                download_name=documento['documento_nome']
            )
        else:
            flash('Arquivo não encontrado no servidor', 'error')
            return redirect(request.referrer or url_for('ponto_admin.index'))

    except Exception as e:
        logger.error(f"Erro no download de documento: {e}")
        flash('Erro ao baixar documento', 'error')
        return redirect(request.referrer or url_for('ponto_admin.index'))
