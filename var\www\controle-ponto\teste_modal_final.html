<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>TESTE FINAL - Modal Biometria</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .btn { padding: 15px 30px; font-size: 18px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 10px; }
        .btn:hover { background: #0056b3; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <h1>🧪 TESTE FINAL - Modal de Biometria</h1>
    
    <button class="btn" onclick="abrirModalBiometria()">
        🔐 TESTAR MODAL (Função Original)
    </button>
    
    <button class="btn" onclick="abrirModalBiometriaUniversal()">
        🌐 TESTAR MODAL (Função Universal)
    </button>
    
    <div id="resultado" style="margin-top: 20px;"></div>

    <!-- JavaScript -->
    <script src="static/js/biometria-zkagent.js"></script>
    
    <script>
        function mostrarResultado(mensagem, tipo) {
            const div = document.getElementById('resultado');
            div.innerHTML = `<div class="alert alert-${tipo}">${mensagem}</div>`;
        }
        
        // Teste automático quando carrega
        window.addEventListener('load', function() {
            console.log('🚀 Página carregada');
            
            if (typeof abrirModalBiometria === 'function') {
                mostrarResultado('✅ Função abrirModalBiometria() está disponível!', 'success');
            } else {
                mostrarResultado('❌ Função abrirModalBiometria() NÃO encontrada!', 'danger');
            }
        });
        
        // Override para capturar tentativas
        const originalAbrirModal = window.abrirModalBiometria;
        if (originalAbrirModal) {
            window.abrirModalBiometria = function() {
                console.log('🔄 Tentando abrir modal...');
                mostrarResultado('🔄 Tentando abrir modal de biometria...', 'info');
                
                try {
                    const resultado = originalAbrirModal();
                    mostrarResultado('✅ Modal deve ter aberto com sucesso!', 'success');
                    return resultado;
                } catch (error) {
                    mostrarResultado('❌ ERRO ao abrir modal: ' + error.message, 'danger');
                    console.error('Erro:', error);
                }
            };
        }
    </script>
</body>
</html> 