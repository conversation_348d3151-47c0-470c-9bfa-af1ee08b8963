#!/usr/bin/env python3
"""
Script temporário para limpar mensagens flash persistentes
"""
import sys
sys.path.insert(0, '/var/www/controle-ponto')

from flask import Flask, session, flash, redirect, url_for
from app import app

# Criar contexto da aplicação
with app.app_context():
    with app.test_request_context():
        # Limpar todas as mensagens flash
        session.clear()
        print("✅ Sessão limpa com sucesso!")
        print("🔄 Reinicie o navegador e acesse novamente a página de funcionários.")
