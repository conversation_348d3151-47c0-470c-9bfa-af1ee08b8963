{% extends "base.html" %}

{% block title %}{{ funcionario.nome_completo }} - <PERSON>e de Ponto{% endblock %}

{% block content %}
<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
    <h2>Detalhes do Funcionário</h2>
    <div style="display: flex; gap: 10px;">
        {% if current_user.is_admin %}
        <a href="/funcionarios/{{ funcionario.id }}/editar" class="btn-sm btn-edit" style="padding: 10px 20px;">
            ✏️ Editar
        </a>
        <button type="button" 
                class="btn-sm btn-delete" 
                style="padding: 10px 20px;"
                onclick="confirmarExclusao({{ funcionario.id }}, {{ funcionario.nome_completo | tojson | safe }})">
            🗑️ Excluir
        </button>
        {% endif %}
        <a href="/funcionarios" class="btn-sm" style="background: #6c757d; color: white; padding: 10px 20px;">
            ← Voltar à Lista
        </a>
    </div>
</div>

<!-- Status e informações gerais -->
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 15px;">
        <div style="display: flex; gap: 20px; align-items: start;">
            <!-- Foto do funcionário -->
            <div>
                <img src="{% if funcionario.foto_3x4 %}/static/{{ funcionario.foto_3x4 }}{% else %}/static/images/funcionario_sem_foto.svg{% endif %}" alt="Foto de {{ funcionario.nome_completo }}" style="max-width: 120px; max-height: 160px; border: 1px solid #dee2e6; border-radius: 6px; object-fit: cover;">
            </div>
            
            <!-- Informações básicas -->
            <div>
                <h3 style="margin: 0; color: #495057;">{{ funcionario.nome_completo }}</h3>
                <p style="margin: 5px 0; color: #6c757d;">Matrícula: {{ funcionario.matricula_empresa }}</p>
                <p style="margin: 5px 0; color: #6c757d;">ID: {{ funcionario.id }}</p>
                <p style="margin: 5px 0; color: #6c757d;">Cadastrado: {{ funcionario.data_cadastro | format_date }}</p>
            </div>
        </div>
        
        <div style="text-align: right;">
            {% if funcionario.status_cadastro == 'Ativo' %}
                <span class="badge badge-success" style="font-size: 14px; padding: 8px 12px;">{{ funcionario.status_cadastro }}</span>
            {% elif funcionario.status_cadastro == 'Inativo' %}
                <span class="badge badge-danger" style="font-size: 14px; padding: 8px 12px;">{{ funcionario.status_cadastro }}</span>
            {% else %}
                <span class="badge badge-secondary" style="font-size: 14px; padding: 8px 12px;">{{ funcionario.status_cadastro }}</span>
            {% endif %}
        </div>
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <div>
            <strong>Cargo:</strong><br>
            {{ funcionario.cargo }}
        </div>
        <div>
            <strong>Setor/Obra:</strong><br>
            {{ funcionario.setor_obra }}
        </div>
        <div>
            <strong>Data Admissão:</strong><br>
            {{ funcionario.data_admissao | format_date }}
        </div>
        <div>
            <strong>Tipo Contrato:</strong><br>
            {{ funcionario.tipo_contrato }}
        </div>
    </div>
</div>

<!-- Seções organizadas em tabs/acordeão -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
    <!-- Dados Pessoais -->
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
            👤 Dados Pessoais
        </h4>
        
        <div style="display: grid; gap: 12px;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                <div>
                    <strong>CPF:</strong><br>
                    {{ funcionario.cpf | format_cpf }}
                </div>
                <div>
                    <strong>RG:</strong><br>
                    {{ funcionario.rg }}
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                <div>
                    <strong>Data Nascimento:</strong><br>
                    {{ funcionario.data_nascimento | format_date }}
                </div>
                <div>
                    <strong>Sexo:</strong><br>
                    {{ funcionario.sexo }}
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                <div>
                    <strong>Estado Civil:</strong><br>
                    {{ funcionario.estado_civil }}
                </div>
                <div>
                    <strong>Nacionalidade:</strong><br>
                    {{ funcionario.nacionalidade }}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Documentos Trabalhistas -->
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
            📋 Documentos Trabalhistas
        </h4>
        
        <div style="display: grid; gap: 12px;">
            <div>
                <strong>CTPS Número:</strong><br>
                {{ funcionario.ctps_numero }}
            </div>
            <div>
                <strong>CTPS Série/UF:</strong><br>
                {{ funcionario.ctps_serie_uf }}
            </div>
            <div>
                <strong>PIS/PASEP:</strong><br>
                {{ funcionario.pis_pasep }}
            </div>
        </div>
    </div>
</div>

<!-- Contato e Endereço -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
    <!-- Contato -->
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
            📞 Contato
        </h4>
        
        <div style="display: grid; gap: 12px;">
            <div>
                <strong>Telefone Principal:</strong><br>
                {{ funcionario.telefone1 | format_telefone }}
            </div>
            {% if funcionario.telefone2 %}
            <div>
                <strong>Telefone Secundário:</strong><br>
                {{ funcionario.telefone2 | format_telefone }}
            </div>
            {% endif %}
            {% if funcionario.email %}
            <div>
                <strong>E-mail:</strong><br>
                <a href="mailto:{{ funcionario.email }}" style="color: #4fbdba;">{{ funcionario.email }}</a>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Endereço -->
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
            🏠 Endereço
        </h4>
        
        <div style="display: grid; gap: 8px;">
            {% if funcionario.endereco_rua %}
            <div>{{ funcionario.endereco_rua }}</div>
            {% endif %}
            {% if funcionario.endereco_bairro %}
            <div>{{ funcionario.endereco_bairro }}</div>
            {% endif %}
            {% if funcionario.endereco_cidade %}
            <div>{{ funcionario.endereco_cidade }} - {{ funcionario.endereco_estado }}</div>
            {% endif %}
            <div><strong>CEP:</strong> {{ funcionario.endereco_cep }}</div>
        </div>
    </div>
</div>

<!-- Jornada de Trabalho -->
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
        ⏰ Jornada de Trabalho
    </h4>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
        <div>
            <h5 style="margin: 0 0 10px 0; color: #495057;">Segunda a Quinta</h5>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                <div>
                    <strong>Entrada:</strong><br>
                    {{ funcionario.jornada_seg_qui_entrada }}
                </div>
                <div>
                    <strong>Saída:</strong><br>
                    {{ funcionario.jornada_seg_qui_saida }}
                </div>
            </div>
        </div>
        
        <div>
            <h5 style="margin: 0 0 10px 0; color: #495057;">Sexta-feira</h5>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                <div>
                    <strong>Entrada:</strong><br>
                    {{ funcionario.jornada_sex_entrada }}
                </div>
                <div>
                    <strong>Saída:</strong><br>
                    {{ funcionario.jornada_sex_saida }}
                </div>
            </div>
        </div>
        
        <div>
            <h5 style="margin: 0 0 10px 0; color: #495057;">Intervalo</h5>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                <div>
                    <strong>Início:</strong><br>
                    {{ funcionario.jornada_intervalo_entrada }}
                </div>
                <div>
                    <strong>Fim:</strong><br>
                    {{ funcionario.jornada_intervalo_saida }}
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6;">
        <div>
            <strong>Nível Acesso:</strong><br>
            {{ funcionario.nivel_acesso }}
        </div>
        <div>
            <strong>Turno:</strong><br>
            {{ funcionario.turno }}
        </div>
        <div>
            <strong>Tolerância Ponto:</strong><br>
            {{ funcionario.tolerancia_ponto }} minutos
        </div>
        <div>
            <strong>Banco de Horas:</strong><br>
            {% if funcionario.banco_horas %}✅ Sim{% else %}❌ Não{% endif %}
        </div>
        <div>
            <strong>Hora Extra:</strong><br>
            {% if funcionario.hora_extra %}✅ Sim{% else %}❌ Não{% endif %}
        </div>
    </div>
</div>

<!-- EPIs -->
{% if funcionario.epis and funcionario.epis|length > 0 %}
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
        <h4 style="margin: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
            🦺 EPIs (Equipamentos de Proteção Individual)
        </h4>
        <a href="/epis/funcionario/{{ funcionario.id }}" class="btn-sm" style="background: #4fbdba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">
            📋 Gerenciar EPIs
        </a>
    </div>
    
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background: #f8f9fa;">
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">EPI</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">CA</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">Data Entrega</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">Validade</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">Status</th>
                </tr>
            </thead>
            <tbody>
                {% for epi in funcionario.epis[:3] %}
                <tr>
                    <td style="padding: 10px; border: 1px solid #dee2e6;"><strong>{{ epi.epi_nome }}</strong></td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">{{ epi.epi_ca or '-' }}</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">{{ epi.epi_data_entrega | format_date if epi.epi_data_entrega else '-' }}</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">
                        {% if epi.epi_data_validade %}
                            {{ epi.epi_data_validade | format_date }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">
                        {% if epi.epi_data_validade %}
                            <span style="color: #28a745; font-weight: bold;">Válido</span>
                        {% else %}
                            <span style="color: #6c757d;">Sem validade</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        {% if funcionario.epis|length > 3 %}
            <p style="margin: 10px 0 0 0; color: #6c757d; font-size: 14px;">
                Mostrando 3 de {{ funcionario.epis|length }} EPIs. 
                <a href="/epis/funcionario/{{ funcionario.id }}" style="color: #4fbdba;">Ver todos</a>
            </p>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- Informações do Sistema -->
<div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="margin-top: 0; color: #495057; border-bottom: 2px solid #4fbdba; padding-bottom: 10px;">
        ⚙️ Informações do Sistema
    </h4>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <div>
            <strong>Data Cadastro:</strong><br>
            {{ funcionario.data_cadastro | format_date }}
        </div>
        <div>
            <strong>Biometria Dedo 1:</strong><br>
            {% if funcionario.digital_dedo1 %}
                <span class="badge badge-success">Configurada</span>
            {% else %}
                <span class="badge badge-secondary">Não configurada</span>
            {% endif %}
        </div>
        <div>
            <strong>Biometria Dedo 2:</strong><br>
            {% if funcionario.digital_dedo2 %}
                <span class="badge badge-success">Configurada</span>
            {% else %}
                <span class="badge badge-secondary">Não configurada</span>
            {% endif %}
        </div>
    </div>
</div>

<!-- Ações rápidas -->
<div style="display: flex; gap: 10px; justify-content: center; margin-top: 30px;">
    <a href="/funcionarios" class="btn-sm" style="background: #6c757d; color: white; padding: 12px 24px;">
        ← Voltar à Lista
    </a>
    {% if current_user.is_admin %}
    <a href="/funcionarios/{{ funcionario.id }}/editar" class="btn-sm btn-edit" style="padding: 12px 24px;">
        ✏️ Editar Funcionário
    </a>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
@media (max-width: 768px) {
    div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
    div[style*="display: flex"] {
        flex-direction: column !important;
        gap: 10px !important;
    }
}

/* Estilo para campos vazios */
.empty-field {
    color: #6c757d;
    font-style: italic;
}

/* Melhorias para badges */
.badge {
    white-space: nowrap;
}
</style>
{% endblock %}