<!DOCTYPE html>
<html>
<head>
    <title>Teste Cálculo JavaScript</title>
</head>
<body>
    <h1>Teste de Cálculo de Horas - JavaScript</h1>
    <div id="resultado"></div>

    <script>
        // Função JavaScript atual do sistema
        function calcularTotalHorasJornada(registro) {
            try {
                if (!registro.entrada) {
                    return '0.0h';
                }

                let totalHoras = 0;
                let observacoes = [];

                // Período manhã (sempre calcula se tem entrada e saída do almoço)
                if (registro.entrada && registro.saida_almoco) {
                    const entrada = new Date(`2000-01-01T${registro.entrada}`);
                    const saidaAlmoco = new Date(`2000-01-01T${registro.saida_almoco}`);
                    const horasManha = (saidaAlmoco - entrada) / (1000 * 60 * 60);
                    totalHoras += Math.max(0, horasManha);
                    observacoes.push(`Manhã: ${horasManha.toFixed(3)}h`);
                }

                // Período tarde (só calcula se tem RETORNO do almoço E SAÍDA)
                if (registro.retorno_almoco && registro.saida) {
                    const retornoAlmoco = new Date(`2000-01-01T${registro.retorno_almoco}`);
                    const saida = new Date(`2000-01-01T${registro.saida}`);
                    const horasTarde = (saida - retornoAlmoco) / (1000 * 60 * 60);
                    totalHoras += Math.max(0, horasTarde);
                    observacoes.push(`Tarde: ${horasTarde.toFixed(3)}h`);
                }

                // CORREÇÃO: Detectar registros incompletos
                let statusRegistro = '';
                if (registro.entrada && registro.saida_almoco && !registro.retorno_almoco) {
                    statusRegistro = ' (só manhã)';
                } else if (registro.entrada && registro.saida_almoco && registro.retorno_almoco && !registro.saida) {
                    statusRegistro = ' (sem saída)';
                } else if (registro.entrada && !registro.saida_almoco) {
                    statusRegistro = ' (incompleto)';
                }

                // Horas extras (só se tem início e fim)
                if (registro.inicio_extra && registro.fim_extra) {
                    const inicioExtra = new Date(`2000-01-01T${registro.inicio_extra}`);
                    const fimExtra = new Date(`2000-01-01T${registro.fim_extra}`);
                    const horasExtras = (fimExtra - inicioExtra) / (1000 * 60 * 60);
                    totalHoras += Math.max(0, horasExtras);
                    observacoes.push(`Extra: ${horasExtras.toFixed(3)}h`);
                }

                // Log para debug
                console.log(`Cálculo detalhado: ${observacoes.join(' + ')} = ${totalHoras.toFixed(3)}h${statusRegistro}`);
                
                return {
                    total: totalHoras,
                    formatado: `${totalHoras.toFixed(1)}h${statusRegistro}`,
                    detalhes: observacoes
                };

            } catch (error) {
                console.error('Erro ao calcular total de horas:', error);
                return '0.0h';
            }
        }

        // Dados de teste
        const registro = {
            entrada: '08:43',
            saida_almoco: '12:56',
            retorno_almoco: '14:10',
            saida: '17:55'
        };

        // Executar teste
        console.log('=== TESTE DE CÁLCULO JAVASCRIPT ===');
        console.log('Dados:', registro);
        
        const resultado = calcularTotalHorasJornada(registro);
        console.log('Resultado:', resultado);

        // Cálculo manual para comparação
        const entrada = new Date(`2000-01-01T${registro.entrada}`);
        const saidaAlmoco = new Date(`2000-01-01T${registro.saida_almoco}`);
        const retornoAlmoco = new Date(`2000-01-01T${registro.retorno_almoco}`);
        const saida = new Date(`2000-01-01T${registro.saida}`);

        const horasManha = (saidaAlmoco - entrada) / (1000 * 60 * 60);
        const horasTarde = (saida - retornoAlmoco) / (1000 * 60 * 60);
        const totalManual = horasManha + horasTarde;

        console.log('=== CÁLCULO MANUAL ===');
        console.log(`Manhã: ${horasManha.toFixed(3)}h`);
        console.log(`Tarde: ${horasTarde.toFixed(3)}h`);
        console.log(`Total: ${totalManual.toFixed(3)}h`);

        // Exibir resultado na página
        document.getElementById('resultado').innerHTML = `
            <h2>Resultado do Teste</h2>
            <p><strong>Dados:</strong> ${JSON.stringify(registro)}</p>
            <p><strong>Função JS:</strong> ${resultado.formatado}</p>
            <p><strong>Total decimal:</strong> ${resultado.total.toFixed(3)}h</p>
            <p><strong>Detalhes:</strong> ${resultado.detalhes.join(' + ')}</p>
            <hr>
            <p><strong>Cálculo Manual:</strong> ${totalManual.toFixed(3)}h</p>
            <p><strong>Diferença:</strong> ${Math.abs(resultado.total - totalManual).toFixed(3)}h</p>
            <p><strong>Status:</strong> ${Math.abs(resultado.total - totalManual) < 0.001 ? '✅ Correto' : '❌ Incorreto'}</p>
        `;
    </script>
</body>
</html>
