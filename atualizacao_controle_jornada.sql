-- =====================================================
-- ATUALIZAÇÃO BANCO DE DADOS - CONTROLE DE JORNADA
-- Sistema: RLPONTO-WEB v1.0
-- Data: 25/06/2025
-- Objetivo: Implementar novas regras de banco de horas personalizado
-- =====================================================

USE controle_ponto;

-- =====================================================
-- 1. ADICIONAR NOVOS CAMPOS NA TABELA FUNCIONÁRIOS
-- =====================================================

-- Adicionar campos de configuração de jornada personalizada
-- Verificar e adicionar colunas uma por vez para evitar erros

-- Adicionar inicio_expediente
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'inicio_expediente') = 0,
    'ALTER TABLE funcionarios ADD COLUMN inicio_expediente TIME DEFAULT ''07:00:00'' COMMENT ''Horário programado de entrada''',
    'SELECT ''Coluna inicio_expediente já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar horario_saida_seg_qui
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'horario_saida_seg_qui') = 0,
    'ALTER TABLE funcionarios ADD COLUMN horario_saida_seg_qui TIME DEFAULT ''17:00:00'' COMMENT ''Horário de saída segunda a quinta''',
    'SELECT ''Coluna horario_saida_seg_qui já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar horario_saida_sexta
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'horario_saida_sexta') = 0,
    'ALTER TABLE funcionarios ADD COLUMN horario_saida_sexta TIME DEFAULT ''16:30:00'' COMMENT ''Horário de saída na sexta-feira''',
    'SELECT ''Coluna horario_saida_sexta já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar periodo_almoco_inicio
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'periodo_almoco_inicio') = 0,
    'ALTER TABLE funcionarios ADD COLUMN periodo_almoco_inicio TIME DEFAULT ''11:00:00'' COMMENT ''Início da janela de almoço''',
    'SELECT ''Coluna periodo_almoco_inicio já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar periodo_almoco_fim
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'periodo_almoco_fim') = 0,
    'ALTER TABLE funcionarios ADD COLUMN periodo_almoco_fim TIME DEFAULT ''14:00:00'' COMMENT ''Fim da janela de almoço''',
    'SELECT ''Coluna periodo_almoco_fim já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar duracao_minima_almoco
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'duracao_minima_almoco') = 0,
    'ALTER TABLE funcionarios ADD COLUMN duracao_minima_almoco INT DEFAULT 60 COMMENT ''Duração mínima do almoço em minutos''',
    'SELECT ''Coluna duracao_minima_almoco já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar tolerancia_entrada
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'tolerancia_entrada') = 0,
    'ALTER TABLE funcionarios ADD COLUMN tolerancia_entrada INT DEFAULT 15 COMMENT ''Tolerância para entrada em minutos''',
    'SELECT ''Coluna tolerancia_entrada já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar permite_banco_horas_positivo
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'permite_banco_horas_positivo') = 0,
    'ALTER TABLE funcionarios ADD COLUMN permite_banco_horas_positivo BOOLEAN DEFAULT FALSE COMMENT ''Permite acumular horas extras como crédito''',
    'SELECT ''Coluna permite_banco_horas_positivo já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar data_atualizacao_jornada
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'funcionarios' AND COLUMN_NAME = 'data_atualizacao_jornada') = 0,
    'ALTER TABLE funcionarios ADD COLUMN data_atualizacao_jornada TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    'SELECT ''Coluna data_atualizacao_jornada já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- =====================================================
-- 2. CRIAR TABELA DE BANCO DE HORAS
-- =====================================================

CREATE TABLE IF NOT EXISTS banco_horas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT UNSIGNED NOT NULL,
    data_referencia DATE NOT NULL,
    
    -- Detalhamento dos cálculos
    atraso_entrada_minutos INT DEFAULT 0 COMMENT 'Minutos de atraso na entrada',
    excesso_almoco_minutos INT DEFAULT 0 COMMENT 'Minutos excedentes no almoço',
    saida_antecipada_minutos INT DEFAULT 0 COMMENT 'Minutos de saída antecipada',
    horas_extras_minutos INT DEFAULT 0 COMMENT 'Minutos de horas extras',
    
    -- Saldos
    saldo_devedor_minutos INT DEFAULT 0 COMMENT 'Total de minutos devidos',
    saldo_credor_minutos INT DEFAULT 0 COMMENT 'Total de minutos de crédito',
    saldo_liquido_minutos INT DEFAULT 0 COMMENT 'Saldo líquido (positivo=crédito, negativo=devedor)',
    
    -- Status e observações
    status_dia ENUM('completo', 'incompleto', 'ausente_parcial', 'ausente_total', 'feriado', 'folga') DEFAULT 'incompleto',
    observacoes TEXT NULL,
    justificativa TEXT NULL,
    aprovado_por INT NULL COMMENT 'ID do usuário que aprovou justificativas',
    data_aprovacao TIMESTAMP NULL,
    
    -- Auditoria
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    UNIQUE KEY uk_funcionario_data (funcionario_id, data_referencia),
    INDEX idx_funcionario (funcionario_id),
    INDEX idx_data_referencia (data_referencia),
    INDEX idx_status (status_dia),
    
    -- Chaves estrangeiras
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
    FOREIGN KEY (aprovado_por) REFERENCES usuarios(id) ON DELETE SET NULL
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci 
COMMENT='Controle de banco de horas por funcionário e data';

-- =====================================================
-- 3. CRIAR TABELA DE JUSTIFICATIVAS
-- =====================================================

CREATE TABLE IF NOT EXISTS justificativas_ponto (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT UNSIGNED NOT NULL,
    data_referencia DATE NOT NULL,
    tipo_justificativa ENUM('atraso', 'saida_antecipada', 'ausencia_parcial', 'ausencia_total', 'horas_extras') NOT NULL,
    
    -- Detalhes da justificativa
    motivo TEXT NOT NULL,
    documento_anexo VARCHAR(255) NULL COMMENT 'Caminho para documento anexo (atestado, etc.)',
    minutos_justificados INT NOT NULL DEFAULT 0,
    
    -- Aprovação
    status_aprovacao ENUM('pendente', 'aprovado', 'rejeitado') DEFAULT 'pendente',
    aprovado_por INT NULL,
    data_aprovacao TIMESTAMP NULL,
    observacoes_aprovacao TEXT NULL,
    
    -- Auditoria
    solicitado_por INT NOT NULL COMMENT 'Usuário que fez a solicitação',
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_funcionario (funcionario_id),
    INDEX idx_data_referencia (data_referencia),
    INDEX idx_status (status_aprovacao),
    INDEX idx_tipo (tipo_justificativa),
    
    -- Chaves estrangeiras
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
    FOREIGN KEY (aprovado_por) REFERENCES usuarios(id) ON DELETE SET NULL,
    FOREIGN KEY (solicitado_por) REFERENCES usuarios(id) ON DELETE CASCADE
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci 
COMMENT='Justificativas para irregularidades no ponto';

-- =====================================================
-- 4. CRIAR TABELA DE CONFIGURAÇÕES GLOBAIS
-- =====================================================

CREATE TABLE IF NOT EXISTS configuracoes_jornada (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chave VARCHAR(100) NOT NULL UNIQUE,
    valor TEXT NOT NULL,
    descricao TEXT NULL,
    tipo ENUM('string', 'int', 'float', 'boolean', 'time', 'json') DEFAULT 'string',
    categoria VARCHAR(50) DEFAULT 'geral',
    
    -- Auditoria
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    atualizado_por INT NULL,
    
    -- Índices
    INDEX idx_categoria (categoria),
    
    -- Chaves estrangeiras
    FOREIGN KEY (atualizado_por) REFERENCES usuarios(id) ON DELETE SET NULL
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci 
COMMENT='Configurações globais do sistema de jornada';

-- =====================================================
-- 5. INSERIR CONFIGURAÇÕES PADRÃO
-- =====================================================

INSERT INTO configuracoes_jornada (chave, valor, descricao, tipo, categoria) VALUES
('jornada_padrao_horas', '8', 'Jornada padrão em horas por dia', 'int', 'jornada'),
('jornada_semanal_horas', '44', 'Jornada semanal em horas', 'int', 'jornada'),
('tolerancia_padrao_minutos', '15', 'Tolerância padrão para entrada em minutos', 'int', 'tolerancia'),
('duracao_minima_almoco_minutos', '60', 'Duração mínima obrigatória do almoço em minutos', 'int', 'almoco'),
('permite_banco_horas_global', 'true', 'Permite banco de horas no sistema', 'boolean', 'banco_horas'),
('limite_banco_horas_mensal', '10', 'Limite de horas no banco por mês (positivo ou negativo)', 'int', 'banco_horas'),
('adicional_hora_extra_normal', '50', 'Adicional de hora extra normal em %', 'int', 'horas_extras'),
('adicional_hora_extra_domingo', '100', 'Adicional de hora extra domingo/feriado em %', 'int', 'horas_extras'),
('limite_horas_extras_diarias', '2', 'Limite de horas extras por dia', 'int', 'horas_extras')
ON DUPLICATE KEY UPDATE 
    valor = VALUES(valor),
    atualizado_em = CURRENT_TIMESTAMP;

-- =====================================================
-- 6. ATUALIZAR TABELA DE REGISTROS DE PONTO
-- =====================================================

-- Adicionar novos campos para melhor controle
-- Verificar e adicionar colunas uma por vez

-- Adicionar periodo_dia
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'registros_ponto' AND COLUMN_NAME = 'periodo_dia') = 0,
    'ALTER TABLE registros_ponto ADD COLUMN periodo_dia ENUM(''entrada_manha'', ''saida_almoco'', ''entrada_tarde'', ''saida_final'') NULL COMMENT ''Período do dia para melhor classificação''',
    'SELECT ''Coluna periodo_dia já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar minutos_atraso
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'registros_ponto' AND COLUMN_NAME = 'minutos_atraso') = 0,
    'ALTER TABLE registros_ponto ADD COLUMN minutos_atraso INT DEFAULT 0 COMMENT ''Minutos de atraso calculados''',
    'SELECT ''Coluna minutos_atraso já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar dentro_tolerancia
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'registros_ponto' AND COLUMN_NAME = 'dentro_tolerancia') = 0,
    'ALTER TABLE registros_ponto ADD COLUMN dentro_tolerancia BOOLEAN DEFAULT TRUE COMMENT ''Se o registro está dentro da tolerância''',
    'SELECT ''Coluna dentro_tolerancia já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar justificado
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'registros_ponto' AND COLUMN_NAME = 'justificado') = 0,
    'ALTER TABLE registros_ponto ADD COLUMN justificado BOOLEAN DEFAULT FALSE COMMENT ''Se o registro foi justificado''',
    'SELECT ''Coluna justificado já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Adicionar processado_banco_horas
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'controle_ponto' AND TABLE_NAME = 'registros_ponto' AND COLUMN_NAME = 'processado_banco_horas') = 0,
    'ALTER TABLE registros_ponto ADD COLUMN processado_banco_horas BOOLEAN DEFAULT FALSE COMMENT ''Se já foi processado no banco de horas''',
    'SELECT ''Coluna processado_banco_horas já existe'' as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- =====================================================
-- 7. CRIAR VIEWS PARA RELATÓRIOS
-- =====================================================

-- View para relatório de banco de horas
CREATE OR REPLACE VIEW vw_banco_horas_resumo AS
SELECT 
    f.id as funcionario_id,
    f.nome_completo,
    f.setor,
    YEAR(bh.data_referencia) as ano,
    MONTH(bh.data_referencia) as mes,
    
    -- Totais mensais
    SUM(bh.saldo_devedor_minutos) as total_devedor_minutos,
    SUM(bh.saldo_credor_minutos) as total_credor_minutos,
    SUM(bh.saldo_liquido_minutos) as saldo_liquido_minutos,
    
    -- Formatação em horas
    CONCAT(
        FLOOR(SUM(bh.saldo_liquido_minutos) / 60), 'h ',
        ABS(SUM(bh.saldo_liquido_minutos) % 60), 'min'
    ) as saldo_formatado,
    
    -- Estatísticas
    COUNT(*) as dias_trabalhados,
    SUM(CASE WHEN bh.status_dia = 'completo' THEN 1 ELSE 0 END) as dias_completos,
    SUM(CASE WHEN bh.atraso_entrada_minutos > 0 THEN 1 ELSE 0 END) as dias_com_atraso,
    
    -- Última atualização
    MAX(bh.atualizado_em) as ultima_atualizacao
    
FROM funcionarios f
LEFT JOIN banco_horas bh ON f.id = bh.funcionario_id
WHERE f.status_cadastro = 'Ativo'
GROUP BY f.id, f.nome_completo, f.setor, YEAR(bh.data_referencia), MONTH(bh.data_referencia)
ORDER BY f.nome_completo, ano DESC, mes DESC;

-- =====================================================
-- 8. CRIAR ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices adicionais para otimização
CREATE INDEX IF NOT EXISTS idx_registros_ponto_periodo ON registros_ponto(periodo_dia);
CREATE INDEX IF NOT EXISTS idx_registros_ponto_processado ON registros_ponto(processado_banco_horas);
CREATE INDEX IF NOT EXISTS idx_funcionarios_jornada ON funcionarios(inicio_expediente, horario_saida_seg_qui);

-- =====================================================
-- FINALIZAÇÃO
-- =====================================================

-- Atualizar estatísticas das tabelas
ANALYZE TABLE funcionarios, banco_horas, justificativas_ponto, configuracoes_jornada, registros_ponto;

-- Log de conclusão
SELECT 'Atualização do banco de dados concluída com sucesso!' as status,
       NOW() as data_execucao;
