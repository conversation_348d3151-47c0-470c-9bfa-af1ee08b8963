#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXECUTOR DE TESTES EM DADOS EXISTENTES
=====================================

Este script executa testes nos dados já existentes no sistema
para identificar problemas reais sem precisar criar dados de teste.

Autor: Sistema RLPONTO-WEB
Data: 17/07/2025
"""

import mysql.connector
import json
from datetime import datetime, timedelta
from decimal import Decimal
import re

# Configuração do banco de dados
DB_CONFIG = {
    'host': 'localhost',
    'user': 'controle_ponto_user',
    'password': 'senha_segura_2024',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

class TestadorDadosExistentes:
    def __init__(self):
        self.conn = None
        self.problemas_encontrados = []
        self.estatisticas = {}
    
    def conectar_banco(self):
        """Conecta ao banco de dados"""
        try:
            self.conn = mysql.connector.connect(**DB_CONFIG)
            return True
        except Exception as e:
            print(f"❌ Erro ao conectar ao banco: {e}")
            return False
    
    def adicionar_problema(self, categoria, descricao, severidade="MEDIA", evidencia=""):
        """Adiciona um problema encontrado"""
        self.problemas_encontrados.append({
            'categoria': categoria,
            'descricao': descricao,
            'severidade': severidade,
            'evidencia': evidencia,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })
    
    def teste_integridade_funcionarios(self):
        """Testa integridade dos dados de funcionários"""
        print("🧪 Testando integridade dos funcionários...")
        
        cursor = self.conn.cursor(dictionary=True)
        
        # 1. Funcionários duplicados por CPF
        cursor.execute("""
            SELECT cpf, COUNT(*) as total, GROUP_CONCAT(nome SEPARATOR ', ') as nomes
            FROM funcionarios 
            WHERE status = 'ativo' AND cpf IS NOT NULL
            GROUP BY cpf 
            HAVING COUNT(*) > 1
        """)
        duplicados = cursor.fetchall()
        
        for dup in duplicados:
            self.adicionar_problema(
                "FUNCIONARIOS_DUPLICADOS",
                f"CPF {dup['cpf']} duplicado em {dup['total']} funcionários: {dup['nomes']}",
                "ALTA",
                f"CPF: {dup['cpf']}"
            )
        
        # 2. CPFs inválidos
        cursor.execute("""
            SELECT id, nome, cpf
            FROM funcionarios
            WHERE status = 'ativo' 
            AND (cpf IS NULL OR cpf = '' OR cpf NOT REGEXP '^[0-9]{3}\\.[0-9]{3}\\.[0-9]{3}-[0-9]{2}$')
        """)
        cpfs_invalidos = cursor.fetchall()
        
        for func in cpfs_invalidos:
            self.adicionar_problema(
                "CPF_INVALIDO",
                f"Funcionário {func['nome']} (ID: {func['id']}) com CPF inválido: '{func['cpf']}'",
                "MEDIA",
                f"ID: {func['id']}, CPF: {func['cpf']}"
            )
        
        # 3. Funcionários sem jornada
        cursor.execute("""
            SELECT f.id, f.nome
            FROM funcionarios f
            WHERE f.status = 'ativo'
            AND (f.jornada_seg_qui_entrada IS NULL OR f.jornada_seg_qui_saida IS NULL)
        """)
        sem_jornada = cursor.fetchall()
        
        for func in sem_jornada:
            self.adicionar_problema(
                "SEM_JORNADA",
                f"Funcionário {func['nome']} (ID: {func['id']}) sem jornada definida",
                "ALTA",
                f"ID: {func['id']}"
            )
        
        cursor.close()
        print(f"   ✅ Encontrados {len(duplicados)} CPFs duplicados")
        print(f"   ✅ Encontrados {len(cpfs_invalidos)} CPFs inválidos")
        print(f"   ✅ Encontrados {len(sem_jornada)} funcionários sem jornada")
    
    def teste_registros_ponto(self):
        """Testa consistência dos registros de ponto"""
        print("🧪 Testando registros de ponto...")
        
        cursor = self.conn.cursor(dictionary=True)
        
        # 1. Registros com datas futuras
        cursor.execute("""
            SELECT id, funcionario_id, data, entrada, saida
            FROM registro_ponto
            WHERE data > CURDATE()
        """)
        datas_futuras = cursor.fetchall()
        
        for reg in datas_futuras:
            self.adicionar_problema(
                "DATA_FUTURA",
                f"Registro ID {reg['id']} com data futura: {reg['data']}",
                "MEDIA",
                f"Funcionário: {reg['funcionario_id']}, Data: {reg['data']}"
            )
        
        # 2. Registros com saída antes da entrada
        cursor.execute("""
            SELECT id, funcionario_id, data, entrada, saida
            FROM registro_ponto
            WHERE entrada IS NOT NULL AND saida IS NOT NULL
            AND saida < entrada
        """)
        saida_antes_entrada = cursor.fetchall()
        
        for reg in saida_antes_entrada:
            self.adicionar_problema(
                "SAIDA_ANTES_ENTRADA",
                f"Registro ID {reg['id']}: saída ({reg['saida']}) antes da entrada ({reg['entrada']})",
                "ALTA",
                f"Funcionário: {reg['funcionario_id']}, Data: {reg['data']}"
            )
        
        # 3. Registros com intervalos inconsistentes
        cursor.execute("""
            SELECT id, funcionario_id, data, saida_almoco, retorno_almoco
            FROM registro_ponto
            WHERE saida_almoco IS NOT NULL AND retorno_almoco IS NOT NULL
            AND retorno_almoco <= saida_almoco
        """)
        intervalos_inconsistentes = cursor.fetchall()
        
        for reg in intervalos_inconsistentes:
            self.adicionar_problema(
                "INTERVALO_INCONSISTENTE",
                f"Registro ID {reg['id']}: retorno ({reg['retorno_almoco']}) antes/igual à saída para almoço ({reg['saida_almoco']})",
                "ALTA",
                f"Funcionário: {reg['funcionario_id']}, Data: {reg['data']}"
            )
        
        # 4. Registros órfãos (funcionário inexistente)
        cursor.execute("""
            SELECT rp.id, rp.funcionario_id, rp.data
            FROM registro_ponto rp
            LEFT JOIN funcionarios f ON rp.funcionario_id = f.id
            WHERE f.id IS NULL
        """)
        registros_orfaos = cursor.fetchall()
        
        for reg in registros_orfaos:
            self.adicionar_problema(
                "REGISTRO_ORFAO",
                f"Registro ID {reg['id']} referencia funcionário inexistente: {reg['funcionario_id']}",
                "ALTA",
                f"Funcionário: {reg['funcionario_id']}, Data: {reg['data']}"
            )
        
        cursor.close()
        print(f"   ✅ Encontrados {len(datas_futuras)} registros com datas futuras")
        print(f"   ✅ Encontrados {len(saida_antes_entrada)} registros com saída antes da entrada")
        print(f"   ✅ Encontrados {len(intervalos_inconsistentes)} registros com intervalos inconsistentes")
        print(f"   ✅ Encontrados {len(registros_orfaos)} registros órfãos")
    
    def teste_justificativas(self):
        """Testa sistema de justificativas"""
        print("🧪 Testando justificativas...")
        
        cursor = self.conn.cursor(dictionary=True)
        
        # 1. Justificativas órfãs
        cursor.execute("""
            SELECT j.id, j.funcionario_id, j.data, j.motivo
            FROM justificativas_ponto j
            LEFT JOIN registro_ponto r ON j.funcionario_id = r.funcionario_id AND j.data = r.data
            WHERE r.id IS NULL
        """)
        justificativas_orfas = cursor.fetchall()
        
        for just in justificativas_orfas:
            self.adicionar_problema(
                "JUSTIFICATIVA_ORFA",
                f"Justificativa ID {just['id']} sem registro de ponto correspondente",
                "MEDIA",
                f"Funcionário: {just['funcionario_id']}, Data: {just['data']}, Motivo: {just['motivo']}"
            )
        
        # 2. Justificativas pendentes há muito tempo
        cursor.execute("""
            SELECT id, funcionario_id, data, motivo, data_criacao
            FROM justificativas_ponto
            WHERE status = 'pendente'
            AND data_criacao < DATE_SUB(NOW(), INTERVAL 30 DAY)
        """)
        pendentes_antigas = cursor.fetchall()
        
        for just in pendentes_antigas:
            dias_pendente = (datetime.now() - just['data_criacao']).days
            self.adicionar_problema(
                "JUSTIFICATIVA_PENDENTE_ANTIGA",
                f"Justificativa ID {just['id']} pendente há {dias_pendente} dias",
                "MEDIA",
                f"Funcionário: {just['funcionario_id']}, Data: {just['data']}"
            )
        
        cursor.close()
        print(f"   ✅ Encontradas {len(justificativas_orfas)} justificativas órfãs")
        print(f"   ✅ Encontradas {len(pendentes_antigas)} justificativas pendentes antigas")
    
    def teste_empresas(self):
        """Testa integridade das empresas"""
        print("🧪 Testando empresas...")
        
        cursor = self.conn.cursor(dictionary=True)
        
        # 1. Empresas ativas sem funcionários
        cursor.execute("""
            SELECT e.id, e.nome_empresa
            FROM empresas e
            LEFT JOIN funcionarios f ON e.id = f.empresa_id AND f.status = 'ativo'
            WHERE e.ativa = 1 AND f.id IS NULL
        """)
        empresas_vazias = cursor.fetchall()
        
        for emp in empresas_vazias:
            self.adicionar_problema(
                "EMPRESA_SEM_FUNCIONARIOS",
                f"Empresa '{emp['nome_empresa']}' (ID: {emp['id']}) ativa mas sem funcionários",
                "BAIXA",
                f"ID: {emp['id']}"
            )
        
        # 2. Funcionários em empresas inativas
        cursor.execute("""
            SELECT f.id, f.nome, e.nome_empresa
            FROM funcionarios f
            JOIN empresas e ON f.empresa_id = e.id
            WHERE f.status = 'ativo' AND e.ativa = 0
        """)
        funcionarios_empresa_inativa = cursor.fetchall()
        
        for func in funcionarios_empresa_inativa:
            self.adicionar_problema(
                "FUNCIONARIO_EMPRESA_INATIVA",
                f"Funcionário {func['nome']} (ID: {func['id']}) ativo em empresa inativa: {func['nome_empresa']}",
                "ALTA",
                f"Funcionário ID: {func['id']}"
            )
        
        cursor.close()
        print(f"   ✅ Encontradas {len(empresas_vazias)} empresas vazias")
        print(f"   ✅ Encontrados {len(funcionarios_empresa_inativa)} funcionários em empresas inativas")
    
    def gerar_estatisticas(self):
        """Gera estatísticas gerais"""
        print("📊 Gerando estatísticas...")
        
        cursor = self.conn.cursor(dictionary=True)
        
        # Estatísticas básicas
        cursor.execute("SELECT COUNT(*) as total FROM funcionarios WHERE status = 'ativo'")
        total_funcionarios = cursor.fetchone()['total']
        
        cursor.execute("SELECT COUNT(*) as total FROM empresas WHERE ativa = 1")
        total_empresas = cursor.fetchone()['total']
        
        cursor.execute("SELECT COUNT(*) as total FROM registro_ponto WHERE data >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)")
        registros_mes = cursor.fetchone()['total']
        
        cursor.execute("SELECT COUNT(*) as total FROM justificativas_ponto WHERE status = 'pendente'")
        justificativas_pendentes = cursor.fetchone()['total']
        
        # Contar problemas por severidade
        problemas_criticos = len([p for p in self.problemas_encontrados if p['severidade'] == 'ALTA'])
        problemas_medios = len([p for p in self.problemas_encontrados if p['severidade'] == 'MEDIA'])
        problemas_baixos = len([p for p in self.problemas_encontrados if p['severidade'] == 'BAIXA'])
        
        self.estatisticas = {
            'funcionarios_ativos': total_funcionarios,
            'empresas_ativas': total_empresas,
            'registros_ultimo_mes': registros_mes,
            'justificativas_pendentes': justificativas_pendentes,
            'problemas_criticos': problemas_criticos,
            'problemas_medios': problemas_medios,
            'problemas_baixos': problemas_baixos,
            'total_problemas': len(self.problemas_encontrados)
        }
        
        cursor.close()
    
    def executar_todos_testes(self):
        """Executa todos os testes"""
        print("🚀 INICIANDO TESTES EM DADOS EXISTENTES")
        print("=" * 60)
        
        if not self.conectar_banco():
            return False
        
        try:
            self.teste_integridade_funcionarios()
            self.teste_registros_ponto()
            self.teste_justificativas()
            self.teste_empresas()
            self.gerar_estatisticas()
            
            return True
            
        except Exception as e:
            print(f"❌ Erro durante execução dos testes: {e}")
            return False
        finally:
            if self.conn:
                self.conn.close()
    
    def gerar_relatorio_resumido(self):
        """Gera relatório resumido dos problemas"""
        print("\n" + "=" * 60)
        print("📋 RELATÓRIO DE PROBLEMAS ENCONTRADOS")
        print("=" * 60)
        
        stats = self.estatisticas
        print(f"📊 ESTATÍSTICAS:")
        print(f"   • Funcionários Ativos: {stats['funcionarios_ativos']}")
        print(f"   • Empresas Ativas: {stats['empresas_ativas']}")
        print(f"   • Registros (30 dias): {stats['registros_ultimo_mes']}")
        print(f"   • Justificativas Pendentes: {stats['justificativas_pendentes']}")
        
        print(f"\n🚨 PROBLEMAS ENCONTRADOS:")
        print(f"   🔴 Críticos: {stats['problemas_criticos']}")
        print(f"   🟡 Médios: {stats['problemas_medios']}")
        print(f"   🟢 Baixos: {stats['problemas_baixos']}")
        print(f"   📊 Total: {stats['total_problemas']}")
        
        if self.problemas_encontrados:
            print(f"\n📝 DETALHES DOS PROBLEMAS:")
            
            # Agrupar por categoria
            categorias = {}
            for problema in self.problemas_encontrados:
                cat = problema['categoria']
                if cat not in categorias:
                    categorias[cat] = []
                categorias[cat].append(problema)
            
            for categoria, problemas in categorias.items():
                print(f"\n   📂 {categoria} ({len(problemas)} problemas):")
                for i, problema in enumerate(problemas[:3]):  # Mostrar apenas os 3 primeiros
                    severidade_emoji = "🔴" if problema['severidade'] == 'ALTA' else "🟡" if problema['severidade'] == 'MEDIA' else "🟢"
                    print(f"      {severidade_emoji} {problema['descricao']}")
                
                if len(problemas) > 3:
                    print(f"      ... e mais {len(problemas) - 3} problemas")
        
        # Status geral
        total_problemas = stats['total_problemas']
        if total_problemas > 10:
            status = "🔴 CRÍTICO - Ação imediata necessária!"
        elif total_problemas > 5:
            status = "🟡 ATENÇÃO - Correções recomendadas"
        else:
            status = "🟢 ESTÁVEL - Monitoramento contínuo"
        
        print(f"\n🎯 STATUS DO SISTEMA: {status}")
        
        return True

def main():
    """Função principal"""
    testador = TestadorDadosExistentes()
    
    if testador.executar_todos_testes():
        testador.gerar_relatorio_resumido()
        
        # Salvar relatório detalhado
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'teste_dados_existentes_{timestamp}.json'
        
        relatorio_completo = {
            'timestamp': timestamp,
            'estatisticas': testador.estatisticas,
            'problemas': testador.problemas_encontrados
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(relatorio_completo, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 Relatório detalhado salvo em: {filename}")
        print("✅ Análise concluída!")
    else:
        print("❌ Falha na execução dos testes!")

if __name__ == "__main__":
    main()
