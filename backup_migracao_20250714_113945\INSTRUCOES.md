# INSTRUÇÕES DE INSTALAÇÃO - RLPONTO-WEB

## 📦 Arquivos do Backup

- `controle_ponto_backup_20250714_113945.sql` - Backup completo do banco de dados
- `sistema_completo_20250714_113945.tar.gz` - Arquivos completos do sistema
- `requirements.txt` - Dependências Python
- `instalar_sistema.sh` - Script de instalação automatizada
- `INSTRUCOES.md` - Este arquivo

## 🚀 Instalação no Novo Container

### 1. Copiar arquivos para o servidor
```bash
scp -r backup_migracao_20250714_113945/* root@NOVO_IP:/root/
```

### 2. Conectar ao servidor e executar instalação
```bash
ssh root@NOVO_IP
cd /root
chmod +x instalar_sistema.sh
./instalar_sistema.sh
```

### 3. Verificar instalação
```bash
curl http://NOVO_IP/
supervisorctl status
```

## ✅ Pós-instalação

1. **Acesse:** http://NOVO_IP/
2. **Login:** admin
3. **Senha:** @Ric6109
4. **Teste:** Todas as funcionalidades

## 🔧 Troubleshooting

### Verificar logs
```bash
tail -f /var/log/controle-ponto.log
supervisorctl status
systemctl status nginx
systemctl status mysql
```

### Reiniciar serviços
```bash
supervisorctl restart controle-ponto
systemctl restart nginx
systemctl restart mysql
```

### Verificar banco de dados
```bash
mysql -u root -p@Ric6109 -e "SHOW DATABASES;"
mysql -u root -p@Ric6109 -e "USE controle_ponto; SHOW TABLES;"
```

## 📊 Informações do Sistema

- **Data do backup:** 2025-07-14 11:39:49.981142
- **Versão:** Sistema completo com herança dinâmica
- **Banco:** MySQL/MariaDB
- **Web Server:** Nginx + Supervisor
- **Python:** 3.x com Flask

## 🎯 Funcionalidades Incluídas

✅ Sistema de herança dinâmica de jornadas
✅ Trigger automático para atualização de funcionários  
✅ Interface completa de gestão
✅ Relatórios e controle de ponto
✅ Gestão de empresas e funcionários
✅ Sistema de alocações
✅ Tolerância automática

## 🔒 Segurança

- Banco com senha: @Ric6109
- Usuário admin: admin / @Ric6109
- Firewall: Configure conforme necessário
- SSL: Configure certificado se necessário
