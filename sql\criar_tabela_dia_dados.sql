-- ========================================
-- CRIAÇÃO DA TABELA DIA_DADOS
-- Data: 09/07/2025
-- Descrição: Tabela para controle de turnos e faixas horárias
-- Baseado em: docs/1-logica_controle_ponto.md
-- ========================================

USE controle_ponto;

-- ========================================
-- 1. CRIAR TABELA DIA_DADOS
-- ========================================

CREATE TABLE IF NOT EXISTS dia_dados (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    turno ENUM('Manha', 'Tarde', 'Noite') NOT NULL,
    horario_inicio TIME NOT NULL COMMENT 'Horário de início do turno',
    horario_fim TIME NOT NULL COMMENT 'Horário de fim do turno',
    descricao VARCHAR(255) NULL COMMENT 'Descrição do turno',
    ativo BOOLEAN DEFAULT TRUE COMMENT 'Se o turno está ativo',
    ordem_prioridade INT DEFAULT 1 COMMENT 'Ordem de prioridade para determinação automática',
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_turno (turno),
    INDEX idx_horarios (horario_inicio, horario_fim),
    INDEX idx_ativo (ativo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Tabela para controle de turnos e faixas horárias do sistema de ponto';

-- ========================================
-- 2. INSERIR DADOS DOS TURNOS PADRÃO
-- ========================================

-- Limpar dados existentes (se houver)
DELETE FROM dia_dados;

-- Inserir turnos conforme especificação
INSERT INTO dia_dados (turno, horario_inicio, horario_fim, descricao, ordem_prioridade) VALUES
('Manha', '05:00:00', '13:00:00', 'Turno da manhã - 05:00 às 13:00', 1),
('Tarde', '13:00:00', '21:00:00', 'Turno da tarde - 13:00 às 21:00', 2),
('Noite', '21:00:00', '05:00:00', 'Turno da noite - 21:00 às 05:00 (dia seguinte)', 3);

-- ========================================
-- 3. VERIFICAR DADOS INSERIDOS
-- ========================================

SELECT 
    id,
    turno,
    TIME_FORMAT(horario_inicio, '%H:%i') as inicio,
    TIME_FORMAT(horario_fim, '%H:%i') as fim,
    descricao,
    ativo,
    ordem_prioridade
FROM dia_dados 
ORDER BY ordem_prioridade;

-- ========================================
-- 4. CRIAR FUNÇÃO PARA DETERMINAR TURNO
-- ========================================

DELIMITER //

DROP FUNCTION IF EXISTS determinar_turno_por_horario//

CREATE FUNCTION determinar_turno_por_horario(hora_batida TIME)
RETURNS VARCHAR(10)
READS SQL DATA
DETERMINISTIC
COMMENT 'Determina o turno baseado no horário da batida'
BEGIN
    DECLARE turno_resultado VARCHAR(10) DEFAULT 'Manha';
    
    -- Verificar turno da manhã (05:00 - 13:00)
    IF hora_batida >= '05:00:00' AND hora_batida < '13:00:00' THEN
        SET turno_resultado = 'Manha';
    
    -- Verificar turno da tarde (13:00 - 21:00)
    ELSEIF hora_batida >= '13:00:00' AND hora_batida < '21:00:00' THEN
        SET turno_resultado = 'Tarde';
    
    -- Verificar turno da noite (21:00 - 05:00 do dia seguinte)
    ELSEIF hora_batida >= '21:00:00' OR hora_batida < '05:00:00' THEN
        SET turno_resultado = 'Noite';
    
    END IF;
    
    RETURN turno_resultado;
END//

DELIMITER ;

-- ========================================
-- 5. TESTAR FUNÇÃO
-- ========================================

-- Testes da função
SELECT 
    '07:30:00' as horario,
    determinar_turno_por_horario('07:30:00') as turno_determinado
UNION ALL
SELECT 
    '15:45:00' as horario,
    determinar_turno_por_horario('15:45:00') as turno_determinado
UNION ALL
SELECT 
    '23:15:00' as horario,
    determinar_turno_por_horario('23:15:00') as turno_determinado
UNION ALL
SELECT 
    '02:30:00' as horario,
    determinar_turno_por_horario('02:30:00') as turno_determinado;

-- ========================================
-- 6. CRIAR VIEW PARA CONSULTAS FACILITADAS
-- ========================================

CREATE OR REPLACE VIEW v_turnos_ativos AS
SELECT 
    id,
    turno,
    horario_inicio,
    horario_fim,
    descricao,
    CASE 
        WHEN turno = 'Noite' THEN 
            CONCAT(TIME_FORMAT(horario_inicio, '%H:%i'), ' - ', TIME_FORMAT(horario_fim, '%H:%i'), ' (dia seguinte)')
        ELSE 
            CONCAT(TIME_FORMAT(horario_inicio, '%H:%i'), ' - ', TIME_FORMAT(horario_fim, '%H:%i'))
    END as faixa_horaria,
    ordem_prioridade
FROM dia_dados 
WHERE ativo = TRUE
ORDER BY ordem_prioridade;

-- Testar view
SELECT * FROM v_turnos_ativos;

-- ========================================
-- 7. DOCUMENTAÇÃO E COMENTÁRIOS
-- ========================================

/*
DOCUMENTAÇÃO DA TABELA DIA_DADOS:

OBJETIVO:
- Controlar os turnos de trabalho do sistema
- Permitir determinação automática do turno baseado no horário da primeira batida
- Facilitar a implementação da nova lógica flexível de ponto

TURNOS CONFIGURADOS:
1. Manhã: 05:00 - 13:00
2. Tarde: 13:00 - 21:00  
3. Noite: 21:00 - 05:00 (dia seguinte)

FUNÇÃO CRIADA:
- determinar_turno_por_horario(TIME): Retorna o turno baseado no horário

VIEW CRIADA:
- v_turnos_ativos: Visualização facilitada dos turnos ativos

PRÓXIMOS PASSOS:
1. Integrar com app_registro_ponto.py
2. Implementar lógica de inferência de batidas
3. Criar sistema de alertas e pendências
*/

-- ========================================
-- FIM DO SCRIPT
-- ========================================
