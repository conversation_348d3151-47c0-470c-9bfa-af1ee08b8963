#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
sys.path.append('.')

print('🔍 DEBUG: Testando função get_with_epis')

try:
    from utils.database import FuncionarioQueries, DatabaseManager
    
    # Testar funcionário com EPI (ID 9)
    print('🔍 Testando funcionário ID 9 (deve ter EPI):')
    funcionario = FuncionarioQueries.get_with_epis(9)
    if funcionario:
        print('✅ Função get_with_epis: FUNCIONANDO')
        print(f'📊 Nome: {funcionario.get("nome_completo", "N/A")}')
        print(f'📦 EPIs: {len(funcionario.get("epis", []))}')
        if funcionario.get('epis'):
            for epi in funcionario['epis']:
                print(f'   - {epi.get("epi_nome", "N/A")} (CA: {epi.get("epi_ca", "N/A")})')
        else:
            print('   (Nenhum EPI encontrado)')
    else:
        print('⚠️  Funcionário ID 9 não encontrado')
        
    # Verificar EPIs diretamente no banco
    print('\n🔍 Verificando EPIs direto no banco:')
    epis = DatabaseManager.execute_query("SELECT * FROM epis WHERE funcionario_id = 9")
    if epis:
        print(f'✅ {len(epis)} EPIs encontrados direto no banco:')
        for epi in epis:
            print(f'   - {epi["epi_nome"]} (CA: {epi.get("epi_ca", "N/A")})')
    else:
        print('❌ Nenhum EPI encontrado no banco para funcionário ID 9')
        
    # Verificar todos os EPIs
    print('\n🔍 Verificando todos os EPIs no sistema:')
    todos_epis = DatabaseManager.execute_query("SELECT e.*, f.nome_completo FROM epis e JOIN funcionarios f ON e.funcionario_id = f.id")
    if todos_epis:
        print(f'✅ {len(todos_epis)} EPIs total no sistema:')
        for epi in todos_epis:
            print(f'   - {epi["epi_nome"]} (Funcionário: {epi["nome_completo"]})')
    else:
        print('❌ Nenhum EPI encontrado no sistema')
        
except Exception as e:
    print(f'❌ ERRO: {e}')
    import traceback
    traceback.print_exc() 