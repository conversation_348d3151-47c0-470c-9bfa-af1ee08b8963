#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para testar a exclusão de uma empresa com autenticação
Data: 03/07/2025
"""

import requests
import json
import sys
from bs4 import BeautifulSoup

def main():
    try:
        # Credenciais
        username = "admin"
        password = "@Ric6109"
        
        # ID da empresa a ser excluída
        empresa_id = 7  # ID da empresa criada anteriormente
        
        # Criar uma sessão para manter cookies
        session = requests.Session()
        
        # Passo 1: Obter o formulário de login para extrair qualquer token CSRF
        print("Obtendo página de login...")
        login_url = "http://************/login"
        login_page = session.get(login_url)
        
        # Passo 2: Fazer login
        print("Fazendo login...")
        login_data = {
            "usuario": username,
            "senha": password
        }
        
        login_response = session.post(login_url, data=login_data)
        
        if "Controle de Ponto" in login_response.text and "Faça login" in login_response.text:
            print("❌ Falha no login! Verifique as credenciais.")
            sys.exit(1)
        else:
            print("✅ Login bem-sucedido!")
        
        # Passo 3: Verificar se a empresa existe antes da exclusão
        print(f"Verificando empresa ID {empresa_id} antes da exclusão...")
        from utils.database import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, razao_social, ativa FROM empresas WHERE id = %s", (empresa_id,))
        empresa_antes = cursor.fetchone()
        
        if empresa_antes:
            ativa_antes = empresa_antes['ativa'] if isinstance(empresa_antes, dict) else empresa_antes[2]
            print(f"Empresa antes da exclusão - ID: {empresa_id}, Ativa: {ativa_antes}")
        else:
            print(f"❌ Empresa ID {empresa_id} não encontrada!")
            conn.close()
            sys.exit(1)
        
        # Passo 4: Excluir a empresa
        print(f"Enviando requisição para excluir a empresa ID {empresa_id}...")
        url = f"http://************/configuracoes/empresas/{empresa_id}/excluir"
        
        # Cabeçalhos
        headers = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # Dados
        data = {
            'is_ajax': True
        }
        
        # Fazer a requisição
        response = session.post(url, headers=headers, data=json.dumps(data))
        
        # Verificar a resposta
        print(f"Status code: {response.status_code}")
        print(f"Resposta: {response.text}")
        
        # Passo 5: Verificar se a empresa foi realmente excluída (marcada como inativa)
        cursor.execute("SELECT id, razao_social, ativa FROM empresas WHERE id = %s", (empresa_id,))
        empresa_depois = cursor.fetchone()
        
        if empresa_depois:
            ativa_depois = empresa_depois['ativa'] if isinstance(empresa_depois, dict) else empresa_depois[2]
            print(f"Empresa depois da exclusão - ID: {empresa_id}, Ativa: {ativa_depois}")
            
            if ativa_antes and not ativa_depois:
                print(f"✅ Empresa ID {empresa_id} foi marcada como inativa com sucesso!")
            elif not ativa_depois:
                print(f"ℹ️ Empresa ID {empresa_id} já estava inativa.")
            else:
                print(f"❌ Empresa ID {empresa_id} ainda está ativa no banco de dados!")
        else:
            print(f"❌ Empresa ID {empresa_id} não encontrada após a exclusão!")
            
        conn.close()
        
    except Exception as e:
        print(f"Erro: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 