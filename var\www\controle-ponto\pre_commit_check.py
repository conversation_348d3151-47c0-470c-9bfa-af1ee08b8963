#!/usr/bin/env python3
"""
PRÉ-COMMIT CHECK - RLPONTO-WEB

Execute antes de qualquer commit para detectar regressões.
Uso: python pre_commit_check.py
"""

import sys
import shutil
from datetime import datetime
from pathlib import Path

def quick_backup():
    """Backup rápido dos arquivos principais"""
    print("📁 Criando backup de segurança...")
    
    timestamp = datetime.now().strftime("%H%M%S")
    backup_dir = Path("backup-build")
    backup_dir.mkdir(exist_ok=True)
    
    # Arquivos críticos
    files = ["app.py", "app_funcionarios.py", "app_configuracoes.py"]
    
    for file in files:
        if Path(file).exists():
            backup_name = f"{Path(file).stem}_precommit_{timestamp}{Path(file).suffix}"
            shutil.copy2(file, backup_dir / backup_name)
            print(f"   ✅ {file} → {backup_name}")

def test_system():
    """Testar se sistema está funcionando"""
    print("🧪 Testando sistema...")
    
    issues = 0
    
    # Teste 1: Módulos carregam?
    modules = [
        ("app_configuracoes", "Configurações"),
        ("app_funcionarios", "Funcionários"),
        ("app", "Aplicação principal")
    ]
    
    for module, name in modules:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except Exception as e:
            print(f"   ❌ {name}: {e}")
            issues += 1
    
    # Teste 2: Banco conecta?
    try:
        from utils.database import get_db_connection
        conn = get_db_connection()
        if conn and hasattr(conn, 'open') and conn.open:
            print(f"   ✅ Banco de dados")
            conn.close()
        else:
            print(f"   ❌ Banco de dados: Falha na conexão")
            issues += 1
    except Exception as e:
        print(f"   ❌ Banco de dados: {e}")
        issues += 1
    
    return issues == 0

def main():
    """Verificação principal"""
    print("🛡️ PRÉ-COMMIT CHECK - RLPONTO-WEB")
    print("=" * 40)
    print(f"⏰ {datetime.now().strftime('%H:%M:%S')}")
    print()
    
    # Backup
    quick_backup()
    print()
    
    # Teste
    success = test_system()
    print()
    
    # Resultado
    if success:
        print("✅ TODOS OS TESTES PASSARAM")
        print("✅ Seguro para fazer commit!")
        return True
    else:
        print("❌ PROBLEMAS DETECTADOS")
        print("⚠️  NÃO faça commit ainda!")
        print("💡 Corrija os problemas acima primeiro")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 