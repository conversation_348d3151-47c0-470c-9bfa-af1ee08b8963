﻿--
-- Script was generated by Devart dbForge Studio for MySQL, Version **********
-- Product home page: http://www.devart.com/dbforge/mysql/studio
-- Script date 16/06/2025 11:56:57
-- Server version: 8.0.42
--

--
-- Disable foreign keys
--
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;

--
-- Set SQL mode
--
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;

--
-- Set character set the client will use to send SQL statements to the server
--
SET NAMES 'utf8';

DROP DATABASE IF EXISTS controle_ponto;

CREATE DATABASE IF NOT EXISTS controle_ponto
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

--
-- Set default database
--
USE controle_ponto;

--
-- Create table `funcionarios`
--
CREATE TABLE IF NOT EXISTS funcionarios (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  nome_completo varchar(100) NOT NULL,
  cpf varchar(14) NOT NULL,
  rg varchar(20) DEFAULT NULL,
  data_nascimento date DEFAULT NULL,
  sexo enum ('Masculino', 'Feminino', 'Outro') DEFAULT NULL,
  estado_civil enum ('Solteiro(a)', 'Casado(a)', 'Divorciado(a)', 'Viúvo(a)', 'União Estável') DEFAULT NULL,
  nacionalidade varchar(50) DEFAULT NULL,
  endereco varchar(150) DEFAULT NULL,
  numero varchar(10) DEFAULT NULL,
  complemento varchar(50) DEFAULT NULL,
  bairro varchar(50) DEFAULT NULL,
  cidade varchar(50) DEFAULT NULL,
  estado varchar(2) DEFAULT NULL,
  cep varchar(10) DEFAULT NULL,
  telefone varchar(15) DEFAULT NULL,
  celular varchar(15) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  cargo varchar(50) DEFAULT NULL,
  setor varchar(50) DEFAULT NULL,
  matricula varchar(20) DEFAULT NULL,
  data_admissao date DEFAULT NULL,
  tipo_contrato enum ('CLT', 'PJ', 'Temporário', 'Estágio', 'Outro') DEFAULT NULL,
  foto_3x4 varchar(255) DEFAULT NULL,
  digital_dedo1 longblob DEFAULT NULL,
  digital_dedo2 longblob DEFAULT NULL,
  biometria_qualidade_1 tinyint UNSIGNED DEFAULT NULL,
  biometria_qualidade_2 tinyint UNSIGNED DEFAULT NULL,
  biometria_data_cadastro datetime DEFAULT NULL,
  jornada_seg_qui_entrada time DEFAULT NULL,
  jornada_seg_qui_saida time DEFAULT NULL,
  jornada_sex_entrada time DEFAULT NULL,
  jornada_sex_saida time DEFAULT NULL,
  jornada_intervalo_entrada time DEFAULT NULL,
  jornada_intervalo_saida time DEFAULT NULL,
  nivel_acesso enum ('Funcionário', 'Supervisor', 'Administrador') DEFAULT 'Funcionário',
  turno enum ('Integral', 'Manhã', 'Tarde', 'Noite') DEFAULT 'Integral',
  tolerancia_ponto int NOT NULL DEFAULT 5,
  banco_horas tinyint(1) DEFAULT 0,
  hora_extra tinyint(1) DEFAULT 0,
  acumulado_horas_extras decimal(8,2) DEFAULT 0.00 COMMENT 'Total de horas extras acumuladas',
  acumulado_banco_horas decimal(8,2) DEFAULT 0.00 COMMENT 'Total de horas no banco de horas',
  status_cadastro enum ('Ativo', 'Inativo') NOT NULL DEFAULT 'Ativo',
  data_cadastro datetime DEFAULT CURRENT_TIMESTAMP,
  data_atualizacao datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  ativo tinyint(1) GENERATED ALWAYS AS ((case when (`status_cadastro` = 'Ativo') then 1 else 0 end)) STORED,
  salario_base decimal(10,2) DEFAULT NULL,
  tipo_pagamento enum ('Mensal', 'Quinzenal', 'Semanal', 'Por hora') DEFAULT 'Mensal',
  valor_hora decimal(10,2) DEFAULT NULL,
  valor_hora_extra decimal(10,2) DEFAULT NULL,
  percentual_hora_extra decimal(5,2) DEFAULT 50.00,
  vale_transporte decimal(10,2) DEFAULT NULL,
  vale_alimentacao decimal(10,2) DEFAULT NULL,
  outros_beneficios decimal(10,2) DEFAULT NULL,
  desconto_inss tinyint(1) DEFAULT 1,
  desconto_irrf tinyint(1) DEFAULT 1,
  observacoes_pagamento text DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE INDEX uk_cpf (cpf),
  UNIQUE INDEX uk_matricula (matricula),
  INDEX idx_funcionarios_nome (nome_completo),
  INDEX idx_funcionarios_setor (setor),
  INDEX idx_funcionarios_cargo (cargo),
  INDEX idx_funcionarios_status (status_cadastro),
  INDEX idx_funcionarios_ativo (ativo)
)
ENGINE = INNODB,
AUTO_INCREMENT = 7,
AVG_ROW_LENGTH = 2730,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Cadastro de funcionários - RLPONTO-WEB v1.2';

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_cpf_format CHECK (regexp_like(`cpf`, _utf8mb4 '^[0-9]{3}\\.[0-9]{3}\\.[0-9]{3}-[0-9]{2}$'));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_email_format CHECK ((`email` IS NULL) OR regexp_like(`email`, _utf8mb4 '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_percentual_range CHECK ((`percentual_hora_extra` IS NULL) OR (`percentual_hora_extra` BETWEEN 0 AND 200));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_qualidade_range_1 CHECK ((`biometria_qualidade_1` IS NULL) OR (`biometria_qualidade_1` BETWEEN 0 AND 100));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_qualidade_range_2 CHECK ((`biometria_qualidade_2` IS NULL) OR (`biometria_qualidade_2` BETWEEN 0 AND 100));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_salario_positivo CHECK ((`salario_base` IS NULL) OR (`salario_base` >= 0));

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_tolerancia_range CHECK (`tolerancia_ponto` BETWEEN 0 AND 60);

--
-- Create check constraint
--
ALTER TABLE funcionarios
ADD CONSTRAINT chk_valor_hora_positivo CHECK ((`valor_hora` IS NULL) OR (`valor_hora` >= 0));

--
-- Create index `cpf` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD UNIQUE INDEX cpf (cpf);

--
-- Create index `idx_cpf` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_cpf (cpf);

--
-- Create index `idx_data_admissao` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_data_admissao (data_admissao);

--
-- Create index `idx_funcionarios_biometria_status` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_funcionarios_biometria_status (status_biometria);

--
-- Create index `idx_matricula` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_matricula (matricula_empresa);

--
-- Create index `idx_nome` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_nome (nome_completo);

--
-- Create index `idx_salario_base` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_salario_base (salario_base);

--
-- Create index `idx_setor` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_setor (setor_obra);

--
-- Create index `idx_status` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_status (status_cadastro);

--
-- Create index `idx_tipo_pagamento` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD INDEX idx_tipo_pagamento (tipo_pagamento);

--
-- Create index `matricula_empresa` on table `funcionarios`
--
ALTER TABLE funcionarios
ADD UNIQUE INDEX matricula_empresa (matricula_empresa);

--
-- Create view `vw_funcionarios_biometria`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_funcionarios_biometria
AS
SELECT
  `f`.`id` AS `id`,
  `f`.`nome_completo` AS `nome_completo`,
  `f`.`cpf` AS `cpf`,
  `f`.`setor_obra` AS `setor`,
  `f`.`cargo` AS `cargo`,
  `f`.`matricula_empresa` AS `matricula_empresa`,
  `f`.`empresa` AS `empresa`,
  `f`.`status_cadastro` AS `status_cadastro`,
  (CASE WHEN ((`f`.`digital_dedo1` IS NOT NULL) OR
      (`f`.`digital_dedo2` IS NOT NULL)) THEN 'Configurado' ELSE 'Não Configurado' END) AS `status_biometria`,
  `f`.`biometria_qualidade_1` AS `biometria_qualidade_1`,
  `f`.`biometria_qualidade_2` AS `biometria_qualidade_2`,
  `f`.`foto_url` AS `foto_url`,
  `f`.`data_cadastro` AS `data_cadastro`,
  `f`.`data_atualizacao` AS `data_atualizacao`
FROM `funcionarios` `f`
WHERE (`f`.`status_cadastro` = 'Ativo')
ORDER BY `f`.`nome_completo`;

--
-- Create table `epis`
--
CREATE TABLE IF NOT EXISTS epis (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL,
  epi_nome varchar(255) NOT NULL,
  epi_ca varchar(50) DEFAULT NULL,
  epi_data_entrega date DEFAULT NULL,
  epi_data_validade date DEFAULT NULL,
  epi_observacoes text DEFAULT NULL,
  status_epi enum ('entregue', 'vencido', 'devolvido') DEFAULT 'entregue',
  criado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  atualizado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Controle de EPIs dos funcionários',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_data_validade` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_data_validade (epi_data_validade);

--
-- Create index `idx_funcionario_epi` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_funcionario_epi (funcionario_id);

--
-- Create index `idx_status_epi` on table `epis`
--
ALTER TABLE epis
ADD INDEX idx_status_epi (status_epi);

--
-- Create foreign key
--
ALTER TABLE epis
ADD CONSTRAINT epis_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create table `usuarios`
--
CREATE TABLE IF NOT EXISTS usuarios (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  usuario varchar(50) NOT NULL,
  nome_completo varchar(100) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  ativo tinyint(1) DEFAULT 1,
  ultimo_login timestamp NULL DEFAULT NULL,
  senha varchar(255) NOT NULL,
  nivel_acesso enum ('usuario', 'admin') DEFAULT 'usuario',
  data_criacao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  data_ultimo_acesso timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 7,
AVG_ROW_LENGTH = 16384,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Usuários do sistema de controle de ponto',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_usuario` on table `usuarios`
--
ALTER TABLE usuarios
ADD INDEX idx_usuario (usuario);

--
-- Create index `usuario` on table `usuarios`
--
ALTER TABLE usuarios
ADD UNIQUE INDEX usuario (usuario);

--
-- Create table `registros_ponto`
--
CREATE TABLE IF NOT EXISTS registros_ponto (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  funcionario_id int UNSIGNED NOT NULL,
  tipo_registro enum ('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida') NOT NULL,
  data_hora timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  data_registro date GENERATED ALWAYS AS (CAST(`data_hora` AS date)) STORED COMMENT 'Data extraída para índices',
  metodo_registro enum ('biometrico', 'manual') NOT NULL,
  criado_por int UNSIGNED DEFAULT NULL COMMENT 'ID do usuário que fez registro manual',
  template_biometrico longblob DEFAULT NULL COMMENT 'Template biométrico usado',
  digital_capturada longblob DEFAULT NULL COMMENT 'Compatibilidade com código antigo',
  qualidade_biometria tinyint UNSIGNED DEFAULT NULL COMMENT 'Qualidade 0-100',
  observacoes text DEFAULT NULL,
  ip_origem varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  sincronizado tinyint(1) DEFAULT 0 COMMENT 'Compatibilidade',
  criado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  atualizado_em timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  biometria_verificada tinyint(1) DEFAULT 0,
  device_hash varchar(64) DEFAULT NULL,
  security_score int DEFAULT 100,
  ip_address varchar(45) DEFAULT NULL,
  horas_trabalhadas decimal(5,2) DEFAULT NULL COMMENT 'Total de horas trabalhadas no dia',
  horas_extras decimal(5,2) DEFAULT NULL COMMENT 'Horas extras calculadas no dia',
  banco_horas decimal(5,2) DEFAULT NULL COMMENT 'Horas para banco de horas no dia',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 27,
AVG_ROW_LENGTH = 682,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Registros de ponto biométrico e manual - RLPONTO-WEB v1.2',
ROW_FORMAT = DYNAMIC;

--
-- Create check constraint
--
ALTER TABLE registros_ponto
ADD CONSTRAINT chk_qualidade_biometria CHECK ((`qualidade_biometria` IS NULL) OR (`qualidade_biometria` BETWEEN 0 AND 100));

--
-- Create index `idx_criado_por` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_criado_por (criado_por);

--
-- Create index `idx_data_hora` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_data_hora (data_hora);

--
-- Create index `idx_data_registro` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_data_registro (data_registro);

--
-- Create index `idx_funcionario_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_funcionario_data (funcionario_id, data_hora);

--
-- Create index `idx_funcionario_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_funcionario_tipo_data (funcionario_id, tipo_registro, data_hora);

--
-- Create index `idx_metodo` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_metodo (metodo_registro);

--
-- Create index `idx_registros_ponto_biometria` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_registros_ponto_biometria (biometria_verificada);

--
-- Create index `idx_registros_ponto_data_funcionario` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_registros_ponto_data_funcionario (data_hora, funcionario_id);

--
-- Create index `idx_registros_ponto_metodo` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_registros_ponto_metodo (metodo_registro);

--
-- Create index `idx_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD INDEX idx_tipo_data (tipo_registro, data_hora);

--
-- Create index `uk_funcionario_tipo_data` on table `registros_ponto`
--
ALTER TABLE registros_ponto
ADD UNIQUE INDEX uk_funcionario_tipo_data (funcionario_id, tipo_registro, data_registro);

--
-- Create foreign key
--
ALTER TABLE registros_ponto
ADD CONSTRAINT registros_ponto_ibfk_1 FOREIGN KEY (funcionario_id)
REFERENCES funcionarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create foreign key
--
ALTER TABLE registros_ponto
ADD CONSTRAINT registros_ponto_ibfk_2 FOREIGN KEY (criado_por)
REFERENCES usuarios (id) ON DELETE SET NULL ON UPDATE CASCADE;

DELIMITER $$

--
-- Create function `VerificarIntegridadeBiometrica`
--
CREATE
DEFINER = 'cavalcrod'@'%'
FUNCTION IF NOT EXISTS VerificarIntegridadeBiometrica (p_funcionario_id int UNSIGNED)
RETURNS json
DETERMINISTIC
READS SQL DATA
BEGIN
  DECLARE resultado json;
  DECLARE tem_dedo1 boolean DEFAULT FALSE;
  DECLARE tem_dedo2 boolean DEFAULT FALSE;
  DECLARE total_registros int DEFAULT 0;
  DECLARE nome_funcionario varchar(100) DEFAULT '';

  -- Verificar se funcionário existe
  SELECT
    (digital_dedo1 IS NOT NULL) AS dedo1,
    (digital_dedo2 IS NOT NULL) AS dedo2,
    nome_completo INTO tem_dedo1, tem_dedo2, nome_funcionario
  FROM funcionarios
  WHERE id = p_funcionario_id
  AND status_cadastro = 'Ativo'
  LIMIT 1;

  -- Se funcionário não existe, retornar erro
  IF nome_funcionario = '' THEN
    SET resultado = JSON_OBJECT('erro', TRUE,
    'mensagem', 'Funcionário não encontrado ou inativo');
    RETURN resultado;
  END IF;

  SELECT
    COUNT(*) INTO total_registros
  FROM registros_ponto
  WHERE funcionario_id = p_funcionario_id;

  SET resultado = JSON_OBJECT('funcionario_id', p_funcionario_id,
  'nome_funcionario', nome_funcionario,
  'tem_biometria_dedo1', tem_dedo1,
  'tem_biometria_dedo2', tem_dedo2,
  'total_registros_ponto', total_registros,
  'status_biometrico', CASE WHEN tem_dedo1 OR
      tem_dedo2 THEN 'configurado' ELSE 'nao_configurado' END,
  'erro', FALSE);

  RETURN resultado;
END
$$

DELIMITER ;

--
-- Create view `vw_horas_trabalhadas`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_horas_trabalhadas
AS
WITH `registros_diarios`
AS
(SELECT
    `f`.`id` AS `funcionario_id`,
    `f`.`nome_completo` AS `nome_completo`,
    `f`.`setor` AS `setor`,
    `f`.`cargo` AS `cargo`,
    CAST(`rp`.`data_hora` AS date) AS `data_registro`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN `rp`.`data_hora` END)) AS `entrada_manha`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN `rp`.`data_hora` END)) AS `saida_almoco`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN `rp`.`data_hora` END)) AS `entrada_tarde`,
    MAX((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN `rp`.`data_hora` END)) AS `saida`
  FROM (`registros_ponto` `rp`
    JOIN `funcionarios` `f`
      ON ((`rp`.`funcionario_id` = `f`.`id`)))
  WHERE (`f`.`ativo` = 1)
  GROUP BY `f`.`id`,
           `f`.`nome_completo`,
           `f`.`setor`,
           `f`.`cargo`,
           CAST(`rp`.`data_hora` AS date))
SELECT
  `rd`.`funcionario_id` AS `funcionario_id`,
  `rd`.`nome_completo` AS `nome_completo`,
  `rd`.`setor` AS `setor`,
  `rd`.`cargo` AS `cargo`,
  `rd`.`data_registro` AS `data_registro`,
  `rd`.`entrada_manha` AS `entrada_manha`,
  `rd`.`saida_almoco` AS `saida_almoco`,
  `rd`.`entrada_tarde` AS `entrada_tarde`,
  `rd`.`saida` AS `saida`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NOT NULL) AND
      (`rd`.`saida_almoco` IS NOT NULL)) THEN TIMEDIFF(`rd`.`saida_almoco`, `rd`.`entrada_manha`) ELSE NULL END) AS `periodo_manha`,
  (CASE WHEN ((`rd`.`entrada_tarde` IS NOT NULL) AND
      (`rd`.`saida` IS NOT NULL)) THEN TIMEDIFF(`rd`.`saida`, `rd`.`entrada_tarde`) ELSE NULL END) AS `periodo_tarde`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NOT NULL) AND
      (`rd`.`saida_almoco` IS NOT NULL) AND
      (`rd`.`entrada_tarde` IS NOT NULL) AND
      (`rd`.`saida` IS NOT NULL)) THEN ADDTIME(TIMEDIFF(`rd`.`saida_almoco`, `rd`.`entrada_manha`), TIMEDIFF(`rd`.`saida`, `rd`.`entrada_tarde`)) ELSE NULL END) AS `total_horas_trabalhadas`,
  (CASE WHEN ((`rd`.`entrada_manha` IS NULL) OR
      (`rd`.`saida_almoco` IS NULL) OR
      (`rd`.`entrada_tarde` IS NULL) OR
      (`rd`.`saida` IS NULL)) THEN 'Incompleto' ELSE 'Completo' END) AS `status_dia`
FROM `registros_diarios` `rd`;

--
-- Create view `vw_estatisticas_sistema`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_sistema
AS
SELECT
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (`funcionarios`.`status_cadastro` = 'Ativo')) AS `funcionarios_ativos`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (`funcionarios`.`status_cadastro` = 'Inativo')) AS `funcionarios_inativos`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE (((`funcionarios`.`digital_dedo1` IS NOT NULL)
    OR (`funcionarios`.`digital_dedo2` IS NOT NULL))
    AND (`funcionarios`.`status_cadastro` = 'Ativo'))) AS `funcionarios_com_biometria`,
  (SELECT
      COUNT(0)
    FROM `funcionarios`
    WHERE ((`funcionarios`.`digital_dedo1` IS NULL)
    AND (`funcionarios`.`digital_dedo2` IS NULL)
    AND (`funcionarios`.`status_cadastro` = 'Ativo'))) AS `funcionarios_sem_biometria`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE (CAST(`registros_ponto`.`data_hora` AS date) = CURDATE())) AS `registros_hoje`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE (YEARWEEK(`registros_ponto`.`data_hora`, 1) = YEARWEEK(NOW(), 1))) AS `registros_semana_atual`,
  (SELECT
      COUNT(0)
    FROM `registros_ponto`
    WHERE ((YEAR(`registros_ponto`.`data_hora`) = YEAR(NOW()))
    AND (MONTH(`registros_ponto`.`data_hora`) = MONTH(NOW())))) AS `registros_mes_atual`;

--
-- Create view `vw_estatisticas_pontos`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_pontos
AS
SELECT
  CAST(`rp`.`data_hora` AS date) AS `data_registro`,
  COUNT(0) AS `total_registros`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'biometrico') THEN 1 ELSE 0 END)) AS `registros_biometricos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'manual') THEN 1 ELSE 0 END)) AS `registros_manuais`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN 1 ELSE 0 END)) AS `entradas_manha`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN 1 ELSE 0 END)) AS `saidas_almoco`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN 1 ELSE 0 END)) AS `entradas_tarde`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN 1 ELSE 0 END)) AS `saidas`,
  SUM((CASE WHEN (((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > '08:10:00')) OR
      ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > '13:10:00'))) THEN 1 ELSE 0 END)) AS `atrasos`,
  COUNT(DISTINCT `rp`.`funcionario_id`) AS `funcionarios_registraram`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
WHERE (`f`.`ativo` = 1)
GROUP BY CAST(`rp`.`data_hora` AS date)
ORDER BY `data_registro` DESC;

--
-- Create view `vw_estatisticas_ponto_setor`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_ponto_setor
AS
SELECT
  `f`.`setor` AS `setor`,
  CAST(`rp`.`data_hora` AS date) AS `data_registro`,
  COUNT(0) AS `total_registros`,
  COUNT(DISTINCT `f`.`id`) AS `funcionarios_unicos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'biometrico') THEN 1 ELSE 0 END)) AS `registros_biometricos`,
  SUM((CASE WHEN (`rp`.`metodo_registro` = 'manual') THEN 1 ELSE 0 END)) AS `registros_manuais`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_manha') THEN 1 ELSE 0 END)) AS `entradas_manha`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida_almoco') THEN 1 ELSE 0 END)) AS `saidas_almoco`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'entrada_tarde') THEN 1 ELSE 0 END)) AS `entradas_tarde`,
  SUM((CASE WHEN (`rp`.`tipo_registro` = 'saida') THEN 1 ELSE 0 END)) AS `saidas`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > ADDTIME(`f`.`jornada_seg_qui_entrada`, SEC_TO_TIME((`f`.`tolerancia_ponto` * 60))))) THEN 1 ELSE 0 END)) AS `atrasos_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > ADDTIME(`f`.`jornada_intervalo_saida`, SEC_TO_TIME((`f`.`tolerancia_ponto` * 60))))) THEN 1 ELSE 0 END)) AS `atrasos_tarde`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
GROUP BY `f`.`setor`,
         CAST(`rp`.`data_hora` AS date);

--
-- Create view `vw_analise_pontualidade`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_analise_pontualidade
AS
SELECT
  `f`.`id` AS `funcionario_id`,
  `f`.`nome_completo` AS `nome_completo`,
  `f`.`setor` AS `setor`,
  `f`.`cargo` AS `cargo`,
  DATE_FORMAT(`rp`.`data_hora`, '%Y-%m') AS `mes_ano`,
  COUNT(DISTINCT CAST(`rp`.`data_hora` AS date)) AS `dias_trabalhados`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) <= '08:10:00')) THEN 1 ELSE 0 END)) AS `entradas_pontuais_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) > '08:10:00')) THEN 1 ELSE 0 END)) AS `atrasos_manha`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) <= '13:10:00')) THEN 1 ELSE 0 END)) AS `entradas_pontuais_tarde`,
  SUM((CASE WHEN ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) > '13:10:00')) THEN 1 ELSE 0 END)) AS `atrasos_tarde`,
  ROUND(((SUM((CASE WHEN ((`rp`.`tipo_registro` IN ('entrada_manha', 'entrada_tarde')) AND
      (((`rp`.`tipo_registro` = 'entrada_manha') AND
      (CAST(`rp`.`data_hora` AS time) <= '08:10:00')) OR
      ((`rp`.`tipo_registro` = 'entrada_tarde') AND
      (CAST(`rp`.`data_hora` AS time) <= '13:10:00')))) THEN 1 ELSE 0 END)) * 100.0) / NULLIF(SUM((CASE WHEN (`rp`.`tipo_registro` IN ('entrada_manha', 'entrada_tarde')) THEN 1 ELSE 0 END)), 0)), 2) AS `percentual_pontualidade`
FROM (`registros_ponto` `rp`
  JOIN `funcionarios` `f`
    ON ((`rp`.`funcionario_id` = `f`.`id`)))
WHERE (`f`.`ativo` = 1)
GROUP BY `f`.`id`,
         `f`.`nome_completo`,
         `f`.`setor`,
         `f`.`cargo`,
         DATE_FORMAT(`rp`.`data_hora`, '%Y-%m');

--
-- Create table `permissoes`
--
CREATE TABLE IF NOT EXISTS permissoes (
  usuario_id int UNSIGNED NOT NULL,
  nivel_acesso enum ('admin', 'usuario') DEFAULT 'usuario',
  data_atribuicao timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (usuario_id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Permissões e níveis de acesso dos usuários',
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE permissoes
ADD CONSTRAINT permissoes_ibfk_1 FOREIGN KEY (usuario_id)
REFERENCES usuarios (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create table `logs_sistema`
--
CREATE TABLE IF NOT EXISTS logs_sistema (
  id int UNSIGNED NOT NULL AUTO_INCREMENT,
  usuario_id int UNSIGNED DEFAULT NULL,
  acao varchar(100) NOT NULL,
  tabela_afetada varchar(50) DEFAULT NULL,
  registro_id int UNSIGNED DEFAULT NULL,
  detalhes json DEFAULT NULL,
  ip_origem varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  data_hora timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 69,
AVG_ROW_LENGTH = 1489,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
COMMENT = 'Logs de auditoria para segurança do sistema',
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_acao_data` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_acao_data (acao, data_hora);

--
-- Create index `idx_data_hora` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_data_hora (data_hora);

--
-- Create index `idx_usuario_data` on table `logs_sistema`
--
ALTER TABLE logs_sistema
ADD INDEX idx_usuario_data (usuario_id, data_hora);

--
-- Create foreign key
--
ALTER TABLE logs_sistema
ADD CONSTRAINT logs_sistema_ibfk_1 FOREIGN KEY (usuario_id)
REFERENCES usuarios (id) ON DELETE SET NULL ON UPDATE CASCADE;

DELIMITER $$

--
-- Create procedure `LimparLogsAntigos`
--
CREATE
DEFINER = 'cavalcrod'@'%'
PROCEDURE IF NOT EXISTS LimparLogsAntigos (IN dias_para_manter int UNSIGNED)
BEGIN
  DECLARE registros_removidos int DEFAULT 0;

  -- Validar parâmetro
  IF dias_para_manter < 7 THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Mínimo de 7 dias deve ser mantido nos logs';
  END IF;

  DELETE
    FROM logs_sistema
  WHERE data_hora < DATE_SUB(NOW(), INTERVAL dias_para_manter DAY);

  SET registros_removidos = ROW_COUNT();

  SELECT
    registros_removidos AS registros_removidos,
    dias_para_manter AS dias_mantidos,
    NOW() AS data_limpeza;

  -- Log da limpeza
  INSERT INTO logs_sistema (acao, tabela_afetada, detalhes, data_hora)
    VALUES ('LIMPEZA_LOGS', 'logs_sistema', JSON_OBJECT('registros_removidos', registros_removidos, 'dias_mantidos', dias_para_manter), NOW());
END
$$

DELIMITER ;

--
-- Create table `empresas`
--
CREATE TABLE IF NOT EXISTS empresas (
  id int NOT NULL AUTO_INCREMENT,
  razao_social varchar(200) NOT NULL,
  nome_fantasia varchar(200) DEFAULT NULL,
  cnpj varchar(18) NOT NULL,
  telefone varchar(15) DEFAULT NULL,
  email varchar(100) DEFAULT NULL,
  ativa tinyint(1) DEFAULT 1,
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `cnpj` on table `empresas`
--
ALTER TABLE empresas
ADD UNIQUE INDEX cnpj (cnpj);

--
-- Create table `horarios_trabalho`
--
CREATE TABLE IF NOT EXISTS horarios_trabalho (
  id int NOT NULL AUTO_INCREMENT,
  empresa_id int NOT NULL,
  nome_horario varchar(100) NOT NULL,
  entrada_manha time NOT NULL DEFAULT '08:00:00',
  saida_almoco time DEFAULT '12:00:00',
  entrada_tarde time DEFAULT '13:00:00',
  saida time NOT NULL DEFAULT '17:00:00',
  tolerancia_minutos int NOT NULL DEFAULT 10,
  ativo tinyint(1) DEFAULT 1,
  data_cadastro timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE horarios_trabalho
ADD CONSTRAINT horarios_trabalho_ibfk_1 FOREIGN KEY (empresa_id)
REFERENCES empresas (id) ON DELETE CASCADE;

--
-- Create table `tentativas_biometria`
--
CREATE TABLE IF NOT EXISTS tentativas_biometria (
  id int NOT NULL AUTO_INCREMENT,
  template_hash varchar(64) DEFAULT NULL,
  timestamp datetime DEFAULT CURRENT_TIMESTAMP,
  device_vendor_id varchar(10) DEFAULT NULL,
  device_product_id varchar(10) DEFAULT NULL,
  success tinyint(1) DEFAULT 0,
  funcionario_id int DEFAULT NULL,
  ip_address varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_funcionario` on table `tentativas_biometria`
--
ALTER TABLE tentativas_biometria
ADD INDEX idx_funcionario (funcionario_id);

--
-- Create index `idx_success` on table `tentativas_biometria`
--
ALTER TABLE tentativas_biometria
ADD INDEX idx_success (success);

--
-- Create index `idx_timestamp` on table `tentativas_biometria`
--
ALTER TABLE tentativas_biometria
ADD INDEX idx_timestamp (timestamp);

--
-- Create table `logs_seguranca`
--
CREATE TABLE IF NOT EXISTS logs_seguranca (
  id int NOT NULL AUTO_INCREMENT,
  tipo_evento varchar(50) NOT NULL,
  funcionario_id int DEFAULT NULL,
  detalhes json DEFAULT NULL,
  timestamp datetime DEFAULT CURRENT_TIMESTAMP,
  ip_address varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  nivel_risco enum ('baixo', 'medio', 'alto') DEFAULT 'baixo',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_funcionario_timestamp` on table `logs_seguranca`
--
ALTER TABLE logs_seguranca
ADD INDEX idx_funcionario_timestamp (funcionario_id, timestamp);

--
-- Create index `idx_nivel_risco` on table `logs_seguranca`
--
ALTER TABLE logs_seguranca
ADD INDEX idx_nivel_risco (nivel_risco);

--
-- Create index `idx_tipo_timestamp` on table `logs_seguranca`
--
ALTER TABLE logs_seguranca
ADD INDEX idx_tipo_timestamp (tipo_evento, timestamp);

--
-- Create table `logs_biometria`
--
CREATE TABLE IF NOT EXISTS logs_biometria (
  id int NOT NULL AUTO_INCREMENT,
  funcionario_id int DEFAULT NULL,
  timestamp datetime DEFAULT CURRENT_TIMESTAMP,
  similarity_score decimal(5, 4) DEFAULT 0.0000,
  device_info json DEFAULT NULL,
  status enum ('success', 'failed') DEFAULT 'failed',
  ip_address varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_funcionario_timestamp` on table `logs_biometria`
--
ALTER TABLE logs_biometria
ADD INDEX idx_funcionario_timestamp (funcionario_id, timestamp);

--
-- Create index `idx_status_timestamp` on table `logs_biometria`
--
ALTER TABLE logs_biometria
ADD INDEX idx_status_timestamp (status, timestamp);

DELIMITER $$

--
-- Create procedure `sp_cleanup_old_logs`
--
CREATE
DEFINER = 'cavalcrod'@'%'
PROCEDURE IF NOT EXISTS sp_cleanup_old_logs ()
BEGIN
  DECLARE EXIT HANDLER FOR SQLEXCEPTION
  BEGIN
    ROLLBACK;
    RESIGNAL;
  END;

  START TRANSACTION;

    DELETE
      FROM logs_biometria
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY);
    DELETE
      FROM logs_seguranca
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 180 DAY)
      AND nivel_risco != 'alto';
    DELETE
      FROM tentativas_biometria
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY);

  COMMIT;
END
$$

DELIMITER ;

--
-- Create view `vw_estatisticas_biometria`
--
CREATE
DEFINER = 'cavalcrod'@'%'
VIEW vw_estatisticas_biometria
AS
SELECT
  CAST(`lb`.`timestamp` AS date) AS `data`,
  COUNT(0) AS `total_tentativas`,
  SUM((CASE WHEN (`lb`.`status` = 'success') THEN 1 ELSE 0 END)) AS `sucessos`,
  SUM((CASE WHEN (`lb`.`status` = 'failed') THEN 1 ELSE 0 END)) AS `falhas`,
  ROUND(AVG(`lb`.`similarity_score`), 4) AS `score_medio`,
  COUNT(DISTINCT `lb`.`funcionario_id`) AS `funcionarios_unicos`
FROM `logs_biometria` `lb`
WHERE (`lb`.`timestamp` >= (CURDATE() - INTERVAL 30 DAY))
GROUP BY CAST(`lb`.`timestamp` AS date)
ORDER BY `data` DESC;

--
-- Create table `dispositivos_biometricos`
--
CREATE TABLE IF NOT EXISTS dispositivos_biometricos (
  id int NOT NULL AUTO_INCREMENT,
  nome_dispositivo varchar(100) NOT NULL,
  fabricante varchar(50) NOT NULL,
  device_id varchar(255) NOT NULL,
  vendor_id varchar(10) DEFAULT NULL,
  product_id varchar(10) DEFAULT NULL,
  serial_number varchar(100) DEFAULT NULL,
  versao_driver varchar(50) DEFAULT NULL,
  porta_usb varchar(20) DEFAULT NULL,
  status_dispositivo enum ('ativo', 'inativo', 'erro', 'manutencao') NOT NULL DEFAULT 'ativo',
  data_registro timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  data_ultima_deteccao timestamp NULL DEFAULT NULL,
  data_desinstalacao timestamp NULL DEFAULT NULL,
  configuracao_json json DEFAULT NULL,
  observacoes text DEFAULT NULL,
  ativo tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `uk_device_id` on table `dispositivos_biometricos`
--
ALTER TABLE dispositivos_biometricos
ADD UNIQUE INDEX uk_device_id (device_id);

--
-- Create table `configuracoes_sistema`
--
CREATE TABLE IF NOT EXISTS configuracoes_sistema (
  id int NOT NULL AUTO_INCREMENT,
  chave varchar(100) NOT NULL,
  valor text DEFAULT NULL,
  descricao text DEFAULT NULL,
  tipo enum ('string', 'integer', 'boolean', 'time', 'json') DEFAULT 'string',
  categoria varchar(50) DEFAULT 'geral',
  criado_em datetime DEFAULT CURRENT_TIMESTAMP,
  atualizado_em datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  editavel tinyint(1) DEFAULT 1,
  data_atualizacao timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 34,
AVG_ROW_LENGTH = 528,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_0900_ai_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `chave` on table `configuracoes_sistema`
--
ALTER TABLE configuracoes_sistema
ADD UNIQUE INDEX chave (chave);

--
-- Create index `idx_categoria` on table `configuracoes_sistema`
--
ALTER TABLE configuracoes_sistema
ADD INDEX idx_categoria (categoria);

--
-- Create index `idx_chave` on table `configuracoes_sistema`
--
ALTER TABLE configuracoes_sistema
ADD INDEX idx_chave (chave);

-- 
-- Dumping data for table usuarios
--
INSERT INTO usuarios VALUES
(1, 'admin', 'Administrador do Sistema', '<EMAIL>', 1, NULL, 'pbkdf2:sha256:600000$IiIqilzPoADLAQ4v$edb07fc69adef3aa4eb1ef608eea4d41d5f272200c900fc252efc5c6085dea89', 'admin', '2025-06-05 16:06:48', '2025-06-05 16:06:48'),
(2, 'teste', NULL, NULL, 1, NULL, 'pbkdf2:sha256:600000$OmXNQh5XtS7BNSri$e174ced16e67d04869383d125a0665498d51b7bc82b6bbc0a6e00d20cb5bc2c0', 'usuario', '2025-06-06 10:56:33', NULL),
(3, 'status', 'Usuário Status do Sistema', '<EMAIL>', 1, NULL, 'pbkdf2:sha256:600000$x5KZTJwcBg7xA6HP$b084242370e60ff03f0e061dcb5c9f6170383dc014dd06fcab8e9ac1bed748d1', 'usuario', '2025-06-08 19:20:11', NULL),
(5, 'cavalcrod', 'Quality Control Manager', '<EMAIL>', 1, NULL, 'pbkdf2:sha256:600000$G5lErTBgGugN2iXS$fdbebe2c4fd4f939c7d9161136d74713e4e89a22d6898dfd87142cf97a5a3a54', 'usuario', '2025-06-10 12:49:17', NULL),
(6, ' cavalcrod ', NULL, NULL, 1, NULL, ' @Ric6109 ', 'usuario', '2025-06-10 14:48:51', NULL);

-- 
-- Dumping data for table empresas
--
INSERT INTO empresas VALUES
(1, 'Empresa Padrão Ltda', 'Empresa Padrão', '00.000.000/0000-00', NULL, NULL, 1, '2025-06-05 17:20:07');

-- 
-- Dumping data for table funcionarios
--
INSERT INTO funcionarios(id, empresa_id, horario_trabalho_id, nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade, ctps_numero, ctps_serie_uf, pis_pasep, endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado, telefone1, telefone2, email, cargo, setor, setor_obra, empresa, matricula_empresa, data_admissao, tipo_contrato, jornada_seg_qui_entrada, jornada_seg_qui_saida, jornada_sex_entrada, jornada_sex_saida, jornada_intervalo_entrada, jornada_intervalo_saida, digital_dedo1, digital_dedo2, foto_3x4, foto_url, nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, ativo, status_cadastro, data_cadastro, data_atualizacao, biometria_qualidade_1, biometria_qualidade_2, biometria_data_cadastro, salario_base, tipo_pagamento, valor_hora, valor_hora_extra, percentual_hora_extra, vale_transporte, vale_alimentacao, outros_beneficios, desconto_inss, desconto_irrf, observacoes_pagamento, data_ultima_alteracao_salario, biometria_qualidade, ultimo_login, tentativas_biometria, status_biometria, status) VALUES
(1, 1, 1, 'RICHARDSON CARDOSO RODRIGUES', '711.256.042-04', '31.799.841', '1981-03-20', 'M', 'Casado', 'Brasileiro', '0000000', '0000000000', '000.00000.00-0', 'RUA GERMÂNIO', 'VILA DA PRATA', 'MANAUS', '69030-685', 'AM', '(92) 99245-5278', NULL, '<EMAIL>', 'ANALISTA', 'Administrativo', 'TI', 'Empresa Principal', '0001', '2025-01-01', 'PJ', '08:00:00', '18:00:00', '08:00:00', '18:00:00', '13:00:00', '14:00:00', x'3038313632373843394342303635343632344641454343434136383330373339323635303037314231394539303746413042413532363741304339313146373331324534314639323232423932354432303738463135353530363036314538313043363332354142313633333139313231453538313735323146354230443734314441313144443131353034303935373043374430343042313230443234334631374538314238353045424430373838323546303145324430394441313430313139363931433132304137343034384232333135304137303034393730393434323537443234464430394631313934353045344630464139313345433035383831373342323446333136363832303645304343433232383531393341313833333131303930463645323543313038354130443334304142423134434230343835303831343046393931313932323534463234344131393732314537463037324531324235313644363038444631364330313446443138393932363645323233383144374630414445314238463145463031373539303442443044443930394541323036323230443131313842313938363041433730383238313242323138343930423830314532313143323132333038304530453043303831453338313038453145323331303135304646443231374431303239314230313046453430363544', x'3543383044374432433732363831363142314544443839424637333244393646313444393235314131324644304541443046313132364241313938363145313030424143313631363042443931304434313739453136373231333241304131423135343931324142323136363034334432343631304546383130353131323132304542453035374330344632313745373131303931433145314332423038333330394236304442373136364331324137304339393136323531423731323138433235314230453243304641373041353431334337313935463144314432314236323245353134363630433139313346323144453330443537303437333034393431324437303741443138424331463746304439313136333631443444303534343236384531354138303936433230363132373046314343423232463531423335304139343130363031344443314237353230383730383731314239313039313031364632304642413141444432304542303939363041363031384646314346313230303530434235303635373235304230373136304439383131343130394238304243393134314331434546313833463043454532344130323231323143343431453336323534383143434432323239303645453043443131334439323730443036324430414338313731443046453631433245313046423142343831394436', 'fotos_funcionarios/funcionario_1_20250613T093126.jpg', NULL, 'Funcionario', 'Diurno', 5, 1, 1, 1, 'Ativo', '2025-06-05 16:27:38', '2025-06-13 09:31:26', NULL, NULL, NULL, NULL, 'Mensal', NULL, NULL, 50.00, NULL, NULL, NULL, 1, 1, NULL, NULL, 0, NULL, 0, 'inativo', 'ativo'),
(6, NULL, NULL, 'FUNCIONARIO TESTE', '589.654.125-78', '00000000000000000000', '1990-01-01', 'M', 'Casado', 'Brasileiro', '0000000', '0000000000', '000.00000.00-0', 'RUA MENDELÉVIO', 'VILA DA PRATA', 'MANAUS', '69030-170', 'AM', '(92) 99245-5893', NULL, NULL, 'TÉCNICO EM SEGURANÇA', NULL, 'RECURSOS HUMANOS', 'Empresa Principal', '0002', '2025-01-01', 'CLT', '07:00:00', '17:00:00', '07:00:00', '16:00:00', '11:00:00', '12:00:00', NULL, NULL, 'fotos_funcionarios/funcionario_6_20250616T115152.jpg', NULL, 'Funcionario', 'Diurno', 10, 1, 1, 1, 'Ativo', '2025-06-16 11:51:28', '2025-06-16 11:51:52', NULL, NULL, NULL, NULL, 'Mensal', NULL, NULL, 50.00, NULL, NULL, NULL, 1, 1, NULL, NULL, 0, NULL, 0, 'inativo', 'ativo');

-- Table controle_ponto.tentativas_biometria does not contain any data (it is empty)

-- 
-- Dumping data for table registros_ponto
--
INSERT INTO registros_ponto(id, funcionario_id, tipo_registro, data_hora, metodo_registro, criado_por, template_biometrico, digital_capturada, qualidade_biometria, observacoes, ip_origem, user_agent, sincronizado, criado_em, atualizado_em, biometria_verificada, device_hash, security_score, ip_address) VALUES
(1, 1, 'entrada_manha', '2025-06-05 08:05:00', 'manual', NULL, NULL, NULL, NULL, 'Teste para relatórios', NULL, NULL, 0, '2025-06-05 22:57:37', '2025-06-05 22:57:37', 0, NULL, 100, NULL),
(2, 1, 'saida', '2025-06-05 17:10:00', 'biometrico', NULL, NULL, NULL, NULL, 'Teste para relatórios', NULL, NULL, 0, '2025-06-05 22:57:37', '2025-06-05 22:57:37', 0, NULL, 100, NULL),
(5, 1, 'entrada_manha', '2025-06-04 07:58:00', 'biometrico', NULL, NULL, NULL, NULL, 'Teste para relatórios', NULL, NULL, 0, '2025-06-05 22:57:37', '2025-06-05 22:57:37', 0, NULL, 100, NULL),
(7, 1, 'saida_almoco', '2025-06-06 01:20:04', 'manual', NULL, NULL, NULL, NULL, NULL, '***********', NULL, 0, '2025-06-06 01:20:04', '2025-06-06 01:20:04', 0, NULL, 100, NULL),
(8, 1, 'entrada_tarde', '2025-06-06 01:22:30', 'manual', NULL, NULL, NULL, NULL, NULL, '***********', NULL, 0, '2025-06-06 01:22:30', '2025-06-06 01:22:30', 0, NULL, 100, NULL),
(9, 1, 'saida', '2025-06-06 01:23:22', 'manual', NULL, NULL, NULL, NULL, 'teste saida', '***********', NULL, 0, '2025-06-06 01:23:22', '2025-06-06 01:23:22', 0, NULL, 100, NULL),
(10, 1, 'entrada_manha', '2025-06-06 09:02:37', 'manual', NULL, NULL, NULL, NULL, NULL, '************', NULL, 0, '2025-06-06 09:02:37', '2025-06-06 09:02:37', 0, NULL, 100, NULL),
(12, 1, 'entrada_manha', '2025-06-07 19:40:10', 'manual', NULL, NULL, NULL, NULL, 'teste', '***********', NULL, 0, '2025-06-07 19:40:10', '2025-06-07 19:40:10', 0, NULL, 100, NULL),
(13, 1, 'entrada_manha', '2025-06-08 18:00:48', 'manual', NULL, NULL, NULL, NULL, NULL, '***********', NULL, 0, '2025-06-08 18:00:48', '2025-06-08 18:00:48', 0, NULL, 100, NULL),
(14, 1, 'entrada_manha', '2025-06-09 00:04:24', 'biometrico', NULL, NULL, NULL, 85, NULL, '***********', NULL, 0, '2025-06-09 00:04:24', '2025-06-09 00:04:24', 0, NULL, 100, NULL),
(15, 1, 'saida_almoco', '2025-06-09 00:04:47', 'biometrico', NULL, NULL, NULL, 85, NULL, '***********', NULL, 0, '2025-06-09 00:04:47', '2025-06-09 00:04:47', 0, NULL, 100, NULL),
(16, 1, 'entrada_tarde', '2025-06-09 00:07:26', 'biometrico', NULL, NULL, NULL, 85, NULL, '***********', NULL, 0, '2025-06-09 00:07:26', '2025-06-09 00:07:26', 0, NULL, 100, NULL),
(17, 1, 'entrada_manha', '2025-06-10 12:33:43', 'manual', NULL, NULL, NULL, NULL, 'teste', '************', NULL, 0, '2025-06-10 12:33:43', '2025-06-10 12:33:43', 0, NULL, 100, NULL),
(18, 1, 'saida_almoco', '2025-06-10 14:43:21', 'manual', NULL, NULL, NULL, NULL, NULL, '************', NULL, 0, '2025-06-10 14:43:21', '2025-06-10 14:43:21', 0, NULL, 100, NULL),
(19, 1, 'saida', '2025-06-10 19:06:17', 'manual', NULL, NULL, NULL, NULL, 'teste saida', '************', NULL, 0, '2025-06-10 19:06:17', '2025-06-10 19:06:17', 0, NULL, 100, NULL),
(20, 1, 'entrada_manha', '2025-06-11 11:16:19', 'manual', NULL, NULL, NULL, NULL, NULL, '************', NULL, 0, '2025-06-11 11:16:19', '2025-06-11 11:16:19', 0, NULL, 100, NULL),
(21, 1, 'entrada_tarde', '2025-06-11 16:03:56', 'manual', NULL, NULL, NULL, NULL, NULL, '10.19.208.189', NULL, 0, '2025-06-11 16:03:56', '2025-06-11 16:03:56', 0, NULL, 100, NULL),
(22, 1, 'entrada_manha', '2025-06-13 11:34:04', 'manual', NULL, NULL, NULL, NULL, NULL, '***********', NULL, 0, '2025-06-13 11:34:04', '2025-06-13 11:34:04', 0, NULL, 100, NULL),
(23, 1, 'saida_almoco', '2025-06-13 11:35:48', 'manual', NULL, NULL, NULL, NULL, 'ocrim', '***********', NULL, 0, '2025-06-13 11:35:48', '2025-06-13 11:35:48', 0, NULL, 100, NULL),
(24, 1, 'entrada_tarde', '2025-06-13 12:36:36', 'manual', NULL, NULL, NULL, NULL, NULL, '***********', NULL, 0, '2025-06-13 12:36:36', '2025-06-13 12:36:36', 0, NULL, 100, NULL),
(25, 1, 'saida_almoco', '2025-06-16 09:21:59', 'manual', NULL, NULL, NULL, NULL, NULL, '************', NULL, 0, '2025-06-16 09:21:59', '2025-06-16 09:21:59', 0, NULL, 100, NULL),
(26, 1, 'saida', '2025-06-16 10:17:33', 'manual', NULL, NULL, NULL, NULL, 'teste', '************', NULL, 0, '2025-06-16 10:17:33', '2025-06-16 10:17:33', 0, NULL, 100, NULL);

-- 
-- Dumping data for table permissoes
--
INSERT INTO permissoes(usuario_id, nivel_acesso, data_atribuicao) VALUES
(1, 'admin', '2025-06-05 16:06:48'),
(2, 'usuario', '2025-06-06 10:56:33'),
(3, 'usuario', '2025-06-08 19:20:33'),
(6, 'admin', '2025-06-10 14:49:26');

-- 
-- Dumping data for table logs_sistema
--
INSERT INTO logs_sistema(id, usuario_id, acao, tabela_afetada, registro_id, detalhes, ip_origem, user_agent, data_hora) VALUES
(1, NULL, 'INICIALIZACAO_BANCO', 'sistema', NULL, '{"status": "sucesso", "versao": "1.2", "data_criacao": "2025-06-05 20:06:48.000000"}', NULL, NULL, '2025-06-05 16:06:48'),
(2, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 1, '{"data_hora": "2025-06-05 22:29:09.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:29:09'),
(3, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 1, '{"data_hora": "2025-06-05 08:05:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(4, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 2, '{"data_hora": "2025-06-05 17:10:00.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(5, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 3, '{"data_hora": "2025-06-05 08:05:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 2, "metodo_registro": "manual", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(6, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 4, '{"data_hora": "2025-06-05 17:10:00.000000", "tipo_registro": "saida", "funcionario_id": 2, "metodo_registro": "biometrico", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(7, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 5, '{"data_hora": "2025-06-04 07:58:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(8, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 6, '{"data_hora": "2025-06-04 07:58:00.000000", "tipo_registro": "entrada_manha", "funcionario_id": 2, "metodo_registro": "biometrico", "nome_funcionario": "João Silva Santos", "qualidade_biometria": null}', NULL, NULL, '2025-06-05 22:57:37'),
(9, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 7, '{"data_hora": "2025-06-06 01:20:04.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:20:04'),
(10, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 8, '{"data_hora": "2025-06-06 01:22:30.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:22:30'),
(11, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 9, '{"data_hora": "2025-06-06 01:23:22.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 01:23:22'),
(12, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 10, '{"data_hora": "2025-06-06 09:02:37.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 09:02:37'),
(13, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 11, '{"data_hora": "2025-06-06 14:13:20.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 3, "metodo_registro": "manual", "nome_funcionario": "Maria Oliveira Costa", "qualidade_biometria": null}', NULL, NULL, '2025-06-06 14:13:20'),
(14, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 12, '{"data_hora": "2025-06-07 19:40:10.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-07 19:40:10'),
(15, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 13, '{"data_hora": "2025-06-08 18:00:48.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-08 18:00:48'),
(16, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 14, '{"data_hora": "2025-06-09 00:04:24.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": 85}', NULL, NULL, '2025-06-09 00:04:24'),
(17, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 15, '{"data_hora": "2025-06-09 00:04:47.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": 85}', NULL, NULL, '2025-06-09 00:04:47'),
(18, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 16, '{"data_hora": "2025-06-09 00:07:26.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "biometrico", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": 85}', NULL, NULL, '2025-06-09 00:07:26'),
(19, NULL, 'CRIACAO_TABELA_CONFIGURACOES', 'configuracoes_sistema', NULL, '{"operacao": "correcao_critica", "responsavel": "Claude AI Assistant", "tabela_criada": "configuracoes_sistema", "documento_referencia": "Correção de Falhas do Sistema.markdown", "configuracoes_inseridas": 15}', NULL, NULL, '2025-06-09 16:29:24'),
(20, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T22:54:43.035102", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "c1595090815e454e", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 22:54:43'),
(21, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:11:56.757323", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "bccdffaffc286b31", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:11:57'),
(22, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:16:03.609032", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "b21d38e74d319904", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:16:04'),
(23, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:27:36.807910", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "0eb7dfb22da9dc81", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:27:37'),
(24, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:54:05.768789", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "eaa985a0b05e6d8c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:54:06'),
(25, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:55:03.681970", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/configuracoes/", "ip_origem": "***********", "session_id": "eaa985a0b05e6d8c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:55:04'),
(26, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:56:38.642329", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "35897cc7bc0b505a", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:56:39'),
(27, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:56:45.825609", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/funcionarios/", "ip_origem": "***********", "session_id": "35897cc7bc0b505a", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:56:46'),
(28, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-09T23:57:34.806182", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "35897cc7bc0b505a", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-09 23:57:35'),
(29, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T00:14:01.872003", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "d98a5642d8c60b0c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 00:14:02'),
(30, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T00:18:35.866717", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/funcionarios/", "ip_origem": "***********", "session_id": "d98a5642d8c60b0c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 00:18:36'),
(31, NULL, '[LOGIN:INFO] Tentativa de login: status', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T00:18:48.950638", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "***********", "session_id": "d98a5642d8c60b0c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "status"}}', '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 00:18:49'),
(32, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T08:52:37.501327", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "8f96b1ea1d8f70f0", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 08:52:38'),
(33, NULL, '[LOGIN:INFO] Logout realizado', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T08:53:41.277687", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "GET", "usuario": "admin", "endpoint": "logout", "is_admin": false, "referrer": "http://************/funcionarios/", "ip_origem": "************", "session_id": "8f96b1ea1d8f70f0", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"usuario_logout": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 08:53:41'),
(34, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:00:25.913665", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "1cde18f92f10a7e0", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:00:26'),
(35, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:11:18.258902", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:11:18'),
(36, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:27:44.012921", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:27:44'),
(37, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:27:44.946649", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:27:45'),
(38, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:30:18.282472", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:30:18'),
(39, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:30:19.110784", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:30:19'),
(40, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:32:40.344872", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:32:40'),
(41, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:36:42.178963", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:36:42'),
(42, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:37:29.274996", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:37:29'),
(43, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:38:52.184760", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:38:52'),
(44, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:39:49.266399", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:39:49'),
(45, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:40:51.249143", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:40:51'),
(46, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:41:16.921365", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:41:17'),
(47, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:48:30.584236", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "87ca9c8ef618c9c7", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 09:48:31'),
(48, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:54:06.616742", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "595c40ab92027e1c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:54:07'),
(49, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T09:55:02.441801", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "595c40ab92027e1c", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 09:55:02'),
(50, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:03:08.824593", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "7e2cb865c3a31b14", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:03:09'),
(51, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:06:51.146595", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "7e2cb865c3a31b14", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:06:51'),
(52, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:08:49.517407", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "de4a1d3357ef8065", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:08:50'),
(53, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:09:00.389321", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": "admin", "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "de4a1d3357ef8065", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": "admin"}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:09:00'),
(54, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:11:23.115108", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "ec15c5627341d603", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:11:23'),
(55, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:11:45.553938", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": null, "ip_origem": "************", "session_id": "ec15c5627341d603", "user_agent": "python-requests/2.31.0", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'python-requests/2.31.0', '2025-06-10 10:11:46'),
(56, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:18:50.672069", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "b803533a43808d88", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:18:51'),
(57, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:20:36.909630", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "60df3a52ab109440", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:20:37'),
(58, NULL, '[LOGIN:INFO] Tentativa de login: admin', NULL, NULL, '{"erro": null, "sucesso": true, "categoria": "LOGIN", "timestamp": "2025-06-10T10:32:09.372911", "criticidade": "INFO", "stack_trace": null, "session_context": {"method": "POST", "usuario": null, "endpoint": "login", "is_admin": false, "referrer": "http://************/login", "ip_origem": "************", "session_id": "9aa91e335bae0f50", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "usuario_id": null, "nivel_acesso": null}, "detalhes_operacao": {"motivo_falha": null, "usuario_tentativa": "admin"}}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-10 10:32:09'),
(59, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 17, '{"data_hora": "2025-06-10 12:33:43.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-10 12:33:43'),
(60, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 18, '{"data_hora": "2025-06-10 14:43:21.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-10 14:43:21'),
(61, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 19, '{"data_hora": "2025-06-10 19:06:17.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-10 19:06:17'),
(62, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 20, '{"data_hora": "2025-06-11 11:16:19.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-11 11:16:19'),
(63, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 21, '{"data_hora": "2025-06-11 16:03:56.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-11 16:03:56'),
(64, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 22, '{"data_hora": "2025-06-13 11:34:04.000000", "tipo_registro": "entrada_manha", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-13 11:34:04'),
(65, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 23, '{"data_hora": "2025-06-13 11:35:48.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-13 11:35:48'),
(66, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 24, '{"data_hora": "2025-06-13 12:36:36.000000", "tipo_registro": "entrada_tarde", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-13 12:36:36'),
(67, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 25, '{"data_hora": "2025-06-16 09:21:59.000000", "tipo_registro": "saida_almoco", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 09:21:59'),
(68, NULL, 'INSERT_REGISTRO_PONTO', 'registros_ponto', 26, '{"data_hora": "2025-06-16 10:17:33.000000", "tipo_registro": "saida", "funcionario_id": 1, "metodo_registro": "manual", "nome_funcionario": "RICHARDSON CARDOSO RODRIGUES", "qualidade_biometria": null}', NULL, NULL, '2025-06-16 10:17:33');

-- 
-- Dumping data for table logs_seguranca
--
INSERT INTO logs_seguranca(id, tipo_evento, funcionario_id, detalhes, timestamp, ip_address, user_agent, nivel_risco) VALUES
(1, 'database_update', NULL, '{"version": "2.0", "features": ["WebUSB API direct hardware access", "ZK4500 biometric device support", "Real-time template comparison", "Automatic attendance type detection", "Enhanced security audit logs"], "description": "Sistema biométrico WebUSB integrado"}', '2025-06-09 01:40:29', NULL, NULL, 'baixo');

-- Table controle_ponto.logs_biometria does not contain any data (it is empty)

-- 
-- Dumping data for table horarios_trabalho
--
INSERT INTO horarios_trabalho(id, empresa_id, nome_horario, entrada_manha, saida_almoco, entrada_tarde, saida, tolerancia_minutos, ativo, data_cadastro) VALUES
(1, 1, 'Horário Administrativo', '08:00:00', '12:00:00', '13:00:00', '17:00:00', 10, 1, '2025-06-05 17:20:07');

-- Table controle_ponto.epis does not contain any data (it is empty)

-- 
-- Dumping data for table dispositivos_biometricos
--
INSERT INTO dispositivos_biometricos(id, nome_dispositivo, fabricante, device_id, vendor_id, product_id, serial_number, versao_driver, porta_usb, status_dispositivo, data_registro, data_ultima_deteccao, data_desinstalacao, configuracao_json, observacoes, ativo) VALUES
(1, 'Dispositivo Teste RLPONTO', 'Genérico', 'test_device_001', NULL, NULL, NULL, NULL, NULL, 'ativo', '2025-06-11 11:39:21', NULL, NULL, NULL, 'Dispositivo de teste criado automaticamente durante instalação', 1);

-- 
-- Dumping data for table configuracoes_sistema
--
INSERT INTO configuracoes_sistema(id, chave, valor, descricao, tipo, categoria, criado_em, atualizado_em, editavel, data_atualizacao) VALUES
(1, 'biometric_threshold', '0.7', 'Limite mínimo de similaridade biométrica', 'string', 'biometria', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(2, 'security_enabled', 'true', 'Habilitar verificações de segurança', 'boolean', 'seguranca', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(3, 'max_failed_attempts', '5', 'Máximo de tentativas falhadas por hora', 'integer', 'seguranca', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(4, 'device_whitelist', '["1b55:4500"]', 'Lista de dispositivos autorizados', 'json', 'hardware', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(5, 'attendance_tolerance_minutes', '15', 'Tolerância em minutos para pontualidade', 'integer', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(6, 'morning_start', '07:00', 'Início do período matutino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(7, 'morning_end', '09:30', 'Fim do período matutino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(8, 'lunch_out_start', '11:30', 'Início da saída para almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(9, 'lunch_out_end', '13:30', 'Fim da saída para almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(10, 'lunch_return_start', '13:30', 'Início da volta do almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(11, 'lunch_return_end', '15:00', 'Fim da volta do almoço', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(12, 'evening_start', '17:00', 'Início do período vespertino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(13, 'evening_end', '19:00', 'Fim do período vespertino', 'string', 'horarios', '2025-06-09 01:40:28', '2025-06-09 01:40:28', 1, '2025-06-09 23:25:05'),
(14, 'tema_sistema', 'claro', 'Tema do sistema', 'string', 'interface', '2025-06-09 11:03:09', '2025-06-09 23:58:37', 1, '2025-06-09 23:58:37'),
(15, 'mostrar_fotos_funcionarios', 'true', 'Mostrar fotos', 'boolean', 'interface', '2025-06-09 11:03:09', '2025-06-09 23:58:36', 1, '2025-06-09 23:58:36'),
(16, 'registros_por_pagina', '50', 'Registros por página', 'integer', 'interface', '2025-06-09 11:03:09', '2025-06-09 23:58:36', 1, '2025-06-09 23:58:36'),
(17, 'versao_sistema', '1.0', 'Versão', 'string', 'tecnico', '2025-06-09 11:03:09', '2025-06-09 11:03:09', 0, '2025-06-09 23:25:05'),
(18, 'formato_data', 'dd/mm/yyyy', 'Formato de data', 'string', 'interface', '2025-06-09 12:43:07', '2025-06-09 23:58:36', 1, '2025-06-09 23:58:36'),
(20, 'fuso_horario', 'America/Manaus', 'Fuso horário padrão do sistema', 'string', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(21, 'timeout_sessao', '86400', 'Tempo limite de sessão em segundos', 'integer', 'seguranca', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(22, 'max_tentativas_login', '5', 'Máximo de tentativas de login', 'integer', 'seguranca', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(23, 'backup_automatico', 'true', 'Habilitar backup automático', 'boolean', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(24, 'horario_backup', '02:00:00', 'Horário para execução do backup automático', 'time', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(25, 'tolerancia_ponto_minutos', '15', 'Tolerância em minutos para registro de ponto', 'integer', 'ponto', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(26, 'email_notificacoes', 'true', 'Habilitar notificações por email', 'boolean', 'notificacoes', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(27, 'smtp_servidor', 'localhost', 'Servidor SMTP para envio de emails', 'string', 'email', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(28, 'smtp_porta', '587', 'Porta do servidor SMTP', 'integer', 'email', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(30, 'modo_debug', 'false', 'Modo de depuração ativo', 'boolean', 'desenvolvimento', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(31, 'cache_habilitado', 'true', 'Cache de consultas habilitado', 'boolean', 'performance', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(32, 'log_nivel', 'INFO', 'Nível de log do sistema', 'string', 'sistema', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05'),
(33, 'biometria_qualidade_minima', '60', 'Qualidade mínima exigida para biometria', 'integer', 'biometria', '2025-06-09 16:29:22', '2025-06-09 16:29:22', 1, '2025-06-09 23:25:05');

--
-- Set default database
--
USE controle_ponto;

DELIMITER $$

--
-- Create trigger `tr_registros_ponto_audit_insert`
--
CREATE
DEFINER = 'cavalcrod'@'%'
TRIGGER IF NOT EXISTS tr_registros_ponto_audit_insert
AFTER INSERT
ON registros_ponto
FOR EACH ROW
BEGIN
  DECLARE nome_funcionario varchar(100);

  -- Buscar nome do funcionário
  SELECT
    nome_completo INTO nome_funcionario
  FROM funcionarios
  WHERE id = NEW.funcionario_id
  LIMIT 1;

  INSERT INTO logs_sistema (acao, tabela_afetada, registro_id, usuario_id, detalhes, data_hora)
    VALUES ('INSERT_REGISTRO_PONTO', 'registros_ponto', NEW.id, NEW.criado_por, JSON_OBJECT('funcionario_id', NEW.funcionario_id, 'nome_funcionario', COALESCE(nome_funcionario, 'N/A'), 'tipo_registro', NEW.tipo_registro, 'metodo_registro', NEW.metodo_registro, 'data_hora', NEW.data_hora, 'qualidade_biometria', NEW.qualidade_biometria), NOW());
END
$$

--
-- Create trigger `tr_funcionarios_audit_update`
--
CREATE
DEFINER = 'cavalcrod'@'%'
TRIGGER IF NOT EXISTS tr_funcionarios_audit_update
AFTER UPDATE
ON funcionarios
FOR EACH ROW
BEGIN
  -- Só registra se houve mudança significativa
  IF (OLD.nome_completo != NEW.nome_completo
    OR OLD.status_cadastro != NEW.status_cadastro
    OR (OLD.digital_dedo1 IS NULL) != (NEW.digital_dedo1 IS NULL)
    OR (OLD.digital_dedo2 IS NULL) != (NEW.digital_dedo2 IS NULL)) THEN

    INSERT INTO logs_sistema (acao, tabela_afetada, registro_id, detalhes, data_hora)
      VALUES ('UPDATE_FUNCIONARIO', 'funcionarios', NEW.id, JSON_OBJECT('funcionario_id', NEW.id, 'nome', NEW.nome_completo, 'mudancas', JSON_OBJECT('nome_alterado', OLD.nome_completo != NEW.nome_completo, 'status_alterado', OLD.status_cadastro != NEW.status_cadastro, 'biometria_dedo1_alterado', (OLD.digital_dedo1 IS NULL) != (NEW.digital_dedo1 IS NULL), 'biometria_dedo2_alterado', (OLD.digital_dedo2 IS NULL) != (NEW.digital_dedo2 IS NULL))), NOW());
  END IF;
END
$$

DELIMITER ;

--
-- Restore previous SQL mode
--
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;

--
-- Enable foreign keys
--
/*!40014 SET FOREIGN_KEY_CHECKS = @OLD_FOREIGN_KEY_CHECKS */;