#!/usr/bin/env python3
import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

db = DatabaseManager()

print("=== ESTRUTURA DA TABELA EMPRESAS ===")
result = db.execute_query("DESCRIBE empresas")
for col in result:
    print(f"  {col['Field']} - {col['Type']}")

print("\n=== DADOS DA EMPRESA ID 4 ===")
empresa = db.execute_query("SELECT * FROM empresas WHERE id = 4", fetch_one=True)
if empresa:
    for key, value in empresa.items():
        print(f"  {key}: {value}")
else:
    print("Empresa não encontrada!")
