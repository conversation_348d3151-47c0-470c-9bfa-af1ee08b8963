// ===================================================
// RLPONTO-WEB - Sistema Biométrico de Produção v2.0
// PRODUÇÃO: Zero Tolerância a Simulação
// Atualizado: 2025-01-08
// ===================================================

class BiometricSystem {
    constructor() {
        this.isInitialized = false;
        this.zkAgentConnected = false;
        this.currentCapture = null;
        this.captureInProgress = false;
        this.usbDevice = null;
        this.websocket = null;
        
        // Anti-simulação: Verificações rigorosas
        this.securityChecks = {
            hardwarePresent: false,
            zkAgentRunning: false,
            usbDeviceConnected: false,
            validFingerprint: false
        };
    }

    async init() {
        console.log('[BIOMETRIC] Inicializando sistema biométrico...');
        
        try {
            // Conectar ao WebSocket do ZKAgent
            await this.connectToZKAgent();
            
            // Detectar dispositivos USB
            await this.detectUSBDevices();
            
            // Configurar event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('[BIOMETRIC] Sistema inicializado com sucesso');
            
        } catch (error) {
            console.error('[BIOMETRIC] Erro na inicialização:', error);
            this.showError('Falha na inicialização do sistema biométrico');
        }
    }

    async connectToZKAgent() {
        return new Promise((resolve, reject) => {
            try {
                // Tentar conectar ao ZKAgent via WebSocket
                this.websocket = new WebSocket('ws://localhost:8765');
                
                this.websocket.onopen = () => {
                    console.log('[BIOMETRIC] Conectado ao ZKAgent');
                    this.zkAgentConnected = true;
                    this.securityChecks.zkAgentRunning = true;
                    resolve();
                };
                
                this.websocket.onerror = (error) => {
                    console.error('[BIOMETRIC] Erro de WebSocket:', error);
                    this.zkAgentConnected = false;
                    this.securityChecks.zkAgentRunning = false;
                    reject(new Error('ZKAgent não encontrado'));
                };
                
                this.websocket.onmessage = (event) => {
                    this.handleZKAgentMessage(JSON.parse(event.data));
                };
                
                this.websocket.onclose = () => {
                    console.log('[BIOMETRIC] Conexão com ZKAgent perdida');
                    this.zkAgentConnected = false;
                    this.securityChecks.zkAgentRunning = false;
                };
                
                // Timeout de 5 segundos
                setTimeout(() => {
                    if (!this.zkAgentConnected) {
                        reject(new Error('Timeout na conexão com ZKAgent'));
                    }
                }, 5000);
                
            } catch (error) {
                reject(error);
            }
        });
    }

    async detectUSBDevices() {
        try {
            console.log('[USB] Detectando dispositivos USB...');
            
            // Verificar se WebUSB está disponível
            if (!navigator.usb) {
                throw new Error('WebUSB não suportado neste navegador');
            }
            
            // Tentar detectar ZK4500
            const devices = await navigator.usb.getDevices();
            const zk4500 = devices.find(device => 
                device.vendorId === 0x1b55 && // ZKTeco Vendor ID
                device.productId === 0x4500    // ZK4500 Product ID
            );
            
            if (zk4500) {
                this.usbDevice = zk4500;
                this.securityChecks.usbDeviceConnected = true;
                this.securityChecks.hardwarePresent = true;
                this.updateUSBStatus(true);
                console.log('[USB] ZK4500 detectado:', zk4500);
            } else {
                this.updateUSBStatus(false);
                console.log('[USB] ZK4500 não encontrado');
            }
            
            // Solicitar permissão para novos dispositivos
            navigator.usb.addEventListener('connect', this.onUSBConnect.bind(this));
            navigator.usb.addEventListener('disconnect', this.onUSBDisconnect.bind(this));
            
        } catch (error) {
            console.error('[USB] Erro na detecção:', error);
            this.updateUSBStatus(false);
        }
    }

    async requestUSBDevice() {
        try {
            const device = await navigator.usb.requestDevice({
                filters: [
                    { vendorId: 0x1b55, productId: 0x4500 }, // ZK4500
                    { vendorId: 0x1b55 } // Outros dispositivos ZKTeco
                ]
            });
            
            this.usbDevice = device;
            this.securityChecks.usbDeviceConnected = true;
            this.securityChecks.hardwarePresent = true;
            this.updateUSBStatus(true);
            
        } catch (error) {
            console.error('[USB] Erro ao solicitar dispositivo:', error);
            this.showError('Falha ao conectar dispositivo USB');
        }
    }

    onUSBConnect(event) {
        console.log('[USB] Dispositivo conectado:', event.device);
        if (event.device.vendorId === 0x1b55 && event.device.productId === 0x4500) {
            this.usbDevice = event.device;
            this.securityChecks.usbDeviceConnected = true;
            this.securityChecks.hardwarePresent = true;
            this.updateUSBStatus(true);
        }
    }

    onUSBDisconnect(event) {
        console.log('[USB] Dispositivo desconectado:', event.device);
        if (this.usbDevice && event.device === this.usbDevice) {
            this.usbDevice = null;
            this.securityChecks.usbDeviceConnected = false;
            this.securityChecks.hardwarePresent = false;
            this.updateUSBStatus(false);
        }
    }

    updateUSBStatus(connected) {
        const statusIndicator = document.getElementById('usbStatus');
        const statusText = document.getElementById('usbStatusText');
        const startBtn = document.getElementById('startScanBtn');
        
        if (connected && this.zkAgentConnected) {
            statusIndicator.className = 'w-3 h-3 rounded-full usb-connected';
            statusText.textContent = 'Leitor ZK4500 Conectado';
            startBtn.disabled = false;
        } else {
            statusIndicator.className = 'w-3 h-3 rounded-full usb-disconnected';
            statusText.textContent = connected ? 'ZKAgent Desconectado' : 'Leitor Desconectado';
            startBtn.disabled = true;
        }
    }

    setupEventListeners() {
        // Botão de conectar leitor
        const connectBtn = document.getElementById('connectReaderBtn');
        if (connectBtn) {
            connectBtn.addEventListener('click', async () => {
                await this.requestUSBDevice();
            });
        }

        // Botão de iniciar captura
        const startBtn = document.getElementById('startScanBtn');
        if (startBtn) {
            startBtn.addEventListener('click', async () => {
                await this.startBiometricCapture();
            });
        }
    }

    async startBiometricCapture() {
        // VALIDAÇÃO DE SEGURANÇA CRÍTICA
        if (!this.validateSecurityChecks()) {
            this.showError('Sistema de segurança: Hardware biométrico necessário');
            return false;
        }

        if (this.captureInProgress) {
            console.log('[BIOMETRIC] Captura já em andamento');
            return false;
        }

        try {
            this.captureInProgress = true;
            this.startScanAnimation();

            console.log('[BIOMETRIC] Iniciando captura biométrica...');

            // Enviar comando para ZKAgent
            if (this.websocket && this.zkAgentConnected) {
                this.websocket.send(JSON.stringify({
                    command: 'capture_fingerprint',
                    timeout: 30000
                }));
            } else {
                throw new Error('ZKAgent não conectado');
            }

        } catch (error) {
            console.error('[BIOMETRIC] Erro na captura:', error);
            this.showError('Erro ao iniciar captura biométrica');
            this.captureInProgress = false;
            this.stopScanAnimation();
        }
    }

    handleZKAgentMessage(data) {
        console.log('[ZKAGENT] Mensagem recebida:', data);

        switch (data.type) {
            case 'fingerprint_captured':
                this.handleFingerprintCaptured(data);
                break;
                
            case 'capture_failed':
                this.handleCaptureFailed(data);
                break;
                
            case 'device_status':
                this.handleDeviceStatus(data);
                break;
                
            default:
                console.log('[ZKAGENT] Tipo de mensagem desconhecido:', data.type);
        }
    }

    async handleFingerprintCaptured(data) {
        try {
            this.stopScanAnimation();
            
            // VALIDAÇÃO ANTI-SIMULAÇÃO
            if (!data.template || !data.quality || data.quality < 60) {
                throw new Error('Captura biométrica inválida');
            }

            console.log('[BIOMETRIC] Biometria capturada, qualidade:', data.quality);
            
            // Enviar para verificação no servidor
            const result = await this.verifyBiometric(data.template);
            
            if (result.success) {
                await this.registerAttendance(result.employee);
            } else {
                this.showError('Biometria não reconhecida no sistema');
            }

        } catch (error) {
            console.error('[BIOMETRIC] Erro no processamento:', error);
            this.showError('Erro no processamento da biometria');
        } finally {
            this.captureInProgress = false;
        }
    }

    handleCaptureFailed(data) {
        console.error('[BIOMETRIC] Falha na captura:', data.error);
        this.showError('Falha na captura: ' + data.error);
        this.captureInProgress = false;
        this.stopScanAnimation();
    }

    handleDeviceStatus(data) {
        console.log('[BIOMETRIC] Status do dispositivo:', data.status);
        this.securityChecks.hardwarePresent = data.status === 'connected';
        this.updateUSBStatus(data.status === 'connected');
    }

    async verifyBiometric(template) {
        try {
            const response = await fetch('/api/biometric/verify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    template: template,
                    timestamp: new Date().toISOString(),
                    security_hash: this.generateSecurityHash(template)
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();

        } catch (error) {
            console.error('[BIOMETRIC] Erro na verificação:', error);
            throw error;
        }
    }

    async registerAttendance(employee) {
        try {
            const registrationType = this.determineRegistrationType();
            
            const response = await fetch('/api/attendance/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    employee_id: employee.id,
                    type: registrationType.type,
                    timestamp: new Date().toISOString(),
                    biometric_verified: true,
                    device_info: {
                        user_agent: navigator.userAgent,
                        hardware_hash: this.generateHardwareHash()
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            this.showSuccess(
                `Ponto registrado para ${employee.name}!\n` +
                `Tipo: ${registrationType.description}\n` +
                `Horário: ${new Date().toLocaleTimeString()}`
            );

            console.log('[ATTENDANCE] Registro efetuado:', result);

        } catch (error) {
            console.error('[ATTENDANCE] Erro no registro:', error);
            this.showError('Erro ao registrar presença: ' + error.message);
        }
    }

    determineRegistrationType() {
        const now = new Date();
        const hour = now.getHours();
        const minute = now.getMinutes();
        
        // Entrada matinal (7:00-9:30)
        if (hour >= 7 && (hour < 9 || (hour === 9 && minute <= 30))) {
            return {
                type: 'entrada',
                description: hour > 8 ? 'Entrada (Atraso)' : 'Entrada (No Horário)'
            };
        }
        // Saída para almoço (11:30-13:30)
        else if ((hour === 11 && minute >= 30) || hour === 12 || (hour === 13 && minute <= 30)) {
            return {
                type: 'saida_almoco',
                description: 'Saída para Almoço'
            };
        }
        // Retorno do almoço (13:30-15:00)
        else if ((hour === 13 && minute >= 30) || hour === 14 || (hour === 15 && minute === 0)) {
            return {
                type: 'retorno_almoco',
                description: hour >= 14 ? 'Retorno do Almoço (Atraso)' : 'Retorno do Almoço'
            };
        }
        // Saída (17:00-19:00)
        else if (hour >= 17 && hour < 19) {
            return {
                type: 'saida',
                description: 'Saída'
            };
        }
        // Fora do horário
        else {
            return {
                type: 'extraordinario',
                description: 'Registro Extraordinário'
            };
        }
    }

    validateSecurityChecks() {
        // ANTI-SIMULAÇÃO: Todas as verificações devem passar
        const required = [
            this.securityChecks.hardwarePresent,
            this.securityChecks.zkAgentRunning,
            this.securityChecks.usbDeviceConnected
        ];

        const passed = required.every(check => check === true);
        
        if (!passed) {
            console.error('[SECURITY] Verificações de segurança falharam:', this.securityChecks);
        }

        return passed;
    }

    generateSecurityHash(template) {
        // Gerar hash de segurança para validação no servidor
        const data = template + navigator.userAgent + Date.now();
        return this.simpleHash(data);
    }

    generateHardwareHash() {
        // Gerar hash baseado no hardware para rastreamento
        const hardware = [
            navigator.userAgent,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            navigator.language
        ].join('|');
        
        return this.simpleHash(hardware);
    }

    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16);
    }

    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }

    startScanAnimation() {
        const scanLine = document.getElementById('scanLine');
        const title = document.getElementById('scannerTitle');
        const description = document.getElementById('scannerDescription');
        
        if (scanLine) scanLine.style.display = 'block';
        if (title) title.textContent = 'Capturando Biometria...';
        if (description) description.textContent = 'Mantenha o dedo firme no leitor';
    }

    stopScanAnimation() {
        const scanLine = document.getElementById('scanLine');
        const title = document.getElementById('scannerTitle');
        const description = document.getElementById('scannerDescription');
        
        if (scanLine) scanLine.style.display = 'none';
        if (title) title.textContent = 'Aguardando Leitura';
        if (description) description.textContent = 'Posicione seu dedo no leitor biométrico';
    }

    showSuccess(message) {
        if (typeof showSuccessModal === 'function') {
            showSuccessModal(message);
        } else {
            alert('✅ ' + message);
        }
    }

    showError(message) {
        console.error('[BIOMETRIC] Erro:', message);
        if (typeof showErrorModal === 'function') {
            showErrorModal(message);
        } else {
            alert('❌ ' + message);
        }
    }
}

// Instância global do sistema biométrico
window.BiometricSystem = new BiometricSystem();

// Inicialização automática quando o DOM estiver pronto
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.BiometricSystem.init();
    });
} else {
    window.BiometricSystem.init();
}

// ===================================================
// SISTEMA DE SEGURANÇA: ZERO TOLERÂNCIA A SIMULAÇÃO
// ===================================================
console.log('🔒 RLPONTO-WEB: Sistema Biométrico Seguro Carregado');
console.log('⚠️  AVISO: Sistema configurado para PRODUÇÃO - Zero simulação permitida'); 