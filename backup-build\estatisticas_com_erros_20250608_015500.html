{% extends "base.html" %}

{% block content %}
<!-- Corporate Dashboard Container -->
<div class="min-h-screen bg-slate-50">
    <!-- Professional Header -->
    <div class="bg-white border-b border-slate-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-8">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold tracking-tight text-slate-900">{{ titulo }}</h1>
                            <p class="text-base text-slate-600 leading-relaxed">Dashboard empresarial de controle de ponto • {{ periodo }}</p>
                        </div>
                    </div>
                    <div class="flex items-center gap-3">
                        <div class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-emerald-100 text-emerald-800 border border-emerald-200">
                            <div class="w-2 h-2 bg-emerald-500 rounded-full mr-2 animate-pulse"></div>
                            Sistema Operacional
                        </div>
                        <div class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-slate-100 text-slate-700 border border-slate-200">
                            Última atualização: hoje
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- KPI Grid - Professional Stats Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
            
            <!-- Total Registros Card -->
            <div class="group relative overflow-hidden rounded-xl bg-white border border-slate-200 shadow-sm hover:shadow-md transition-all duration-300">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-8">
                        <div class="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="text-right">
                            <svg class="w-4 h-4 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                            </svg>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <h3 class="text-4xl font-bold tracking-tighter text-slate-900">
                            {{ "{:,}".format(stats.total_registros) }}
                            <span class="text-sm font-medium text-emerald-600 ml-2">+12.3%</span>
                        </h3>
                        <p class="text-sm font-medium text-slate-500 tracking-wide">Total de Registros</p>
                    </div>
                </div>
            </div>

            <!-- Funcionários Ativos Card -->
            <div class="group relative overflow-hidden rounded-xl bg-white border border-slate-200 shadow-sm hover:shadow-md transition-all duration-300">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-8">
                        <div class="w-10 h-10 rounded-lg bg-emerald-100 flex items-center justify-center">
                            <svg class="w-5 h-5 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                            </svg>
                        </div>
                        <div class="text-right">
                            <svg class="w-4 h-4 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                            </svg>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <h3 class="text-4xl font-bold tracking-tighter text-slate-900">
                            {{ "{:,}".format(stats.funcionarios_ativos) }}
                            <span class="text-sm font-medium text-emerald-600 ml-2">+5.2%</span>
                        </h3>
                        <p class="text-sm font-medium text-slate-500 tracking-wide">Funcionários Ativos</p>
                    </div>
                </div>
            </div>

            <!-- Biométricos Card -->
            <div class="group relative overflow-hidden rounded-xl bg-white border border-slate-200 shadow-sm hover:shadow-md transition-all duration-300">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-8">
                        <div class="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center">
                            <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6.625 2.655A9 9 0 0119 11a1 1 0 11-2 0 7 7 0 00-9.625-6.492 1 1 0 11-.75-1.853zM4.662 4.959A1 1 0 014.75 6.37 6.97 6.97 0 003 11a1 1 0 11-2 0 8.97 8.97 0 012.25-5.953 1 1 0 011.412-.088z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="text-right">
                            <div class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-purple-100 text-purple-700">
                                {{ stats.percentual_biometrico }}%
                            </div>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <h3 class="text-4xl font-bold tracking-tighter text-slate-900">
                            {{ "{:,}".format(stats.registros_biometricos) }}
                            <span class="text-sm font-medium text-emerald-600 ml-2">+8.7%</span>
                        </h3>
                        <p class="text-sm font-medium text-slate-500 tracking-wide">Registros Biométricos</p>
                    </div>
                </div>
            </div>

            <!-- Manuais Card -->
            <div class="group relative overflow-hidden rounded-xl bg-white border border-slate-200 shadow-sm hover:shadow-md transition-all duration-300">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-8">
                        <div class="w-10 h-10 rounded-lg bg-amber-100 flex items-center justify-center">
                            <svg class="w-5 h-5 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                            </svg>
                        </div>
                        <div class="text-right">
                            <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                            </svg>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <h3 class="text-4xl font-bold tracking-tighter text-slate-900">
                            {{ "{:,}".format(stats.registros_manuais) }}
                            <span class="text-sm font-medium text-red-600 ml-2">-2.1%</span>
                        </h3>
                        <p class="text-sm font-medium text-slate-500 tracking-wide">Registros Manuais</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Section -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            
            <!-- Registros Diários Chart -->
            <div class="lg:col-span-2 bg-white rounded-xl border border-slate-200 shadow-sm">
                <div class="p-6 border-b border-slate-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-slate-900">Registros por Período</h3>
                            <p class="text-sm text-slate-600 mt-1">Análise dos últimos 7 dias</p>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded-full bg-blue-500"></div>
                            <span class="text-xs font-medium text-slate-600">Registros diários</span>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div style="height: 320px;">
                        <canvas id="chartRegistrosDiarios"></canvas>
                    </div>
                </div>
            </div>

            <!-- Métodos Distribution -->
            <div class="bg-white rounded-xl border border-slate-200 shadow-sm">
                <div class="p-6 border-b border-slate-100">
                    <h3 class="text-lg font-semibold text-slate-900">Distribuição de Métodos</h3>
                    <p class="text-sm text-slate-600 mt-1">Biométrico vs Manual</p>
                </div>
                <div class="p-6">
                    <div style="height: 280px;" class="flex items-center justify-center">
                        <canvas id="chartMetodos"></canvas>
                    </div>
                    <!-- Stats Summary -->
                    <div class="mt-6 space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <div class="w-3 h-3 rounded-full bg-purple-500"></div>
                                <span class="text-sm font-medium text-slate-700">Biométrico</span>
                            </div>
                            <span class="text-sm font-semibold text-slate-900">{{ stats.percentual_biometrico }}%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <div class="w-3 h-3 rounded-full bg-amber-500"></div>
                                <span class="text-sm font-medium text-slate-700">Manual</span>
                            </div>
                            <span class="text-sm font-semibold text-slate-900">{{ 100 - stats.percentual_biometrico }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pontualidade Analysis -->
        <div class="bg-white rounded-xl border border-slate-200 shadow-sm mb-8">
            <div class="p-6 border-b border-slate-100">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-slate-900">Análise de Pontualidade</h3>
                        <p class="text-sm text-slate-600 mt-1">Controle de atrasos e frequência</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="w-3 h-3 rounded-full bg-red-500"></div>
                        <span class="text-xs font-medium text-slate-600">Atrasos por dia</span>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div style="height: 300px;">
                    <canvas id="chartPontualidade"></canvas>
                </div>
            </div>
        </div>

        <!-- Action Center -->
        <div class="bg-white rounded-xl border border-slate-200 shadow-sm">
            <div class="p-6 border-b border-slate-100">
                <h3 class="text-lg font-semibold text-slate-900">Centro de Ações</h3>
                <p class="text-sm text-slate-600 mt-1">Acesso rápido às principais funcionalidades</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <a href="/relatorios/pontos" class="group flex items-center justify-center gap-3 p-4 rounded-lg border-2 border-slate-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200">
                        <div class="w-10 h-10 rounded-lg bg-blue-100 group-hover:bg-blue-200 flex items-center justify-center transition-colors">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <div class="text-left">
                            <p class="font-semibold text-slate-900 group-hover:text-blue-900">Relatórios</p>
                            <p class="text-xs text-slate-600">Ver detalhados</p>
                        </div>
                    </a>
                    
                    <a href="/registro-ponto/manual" class="group flex items-center justify-center gap-3 p-4 rounded-lg border-2 border-slate-200 hover:border-emerald-300 hover:bg-emerald-50 transition-all duration-200">
                        <div class="w-10 h-10 rounded-lg bg-emerald-100 group-hover:bg-emerald-200 flex items-center justify-center transition-colors">
                            <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                        </div>
                        <div class="text-left">
                            <p class="font-semibold text-slate-900 group-hover:text-emerald-900">Ponto Manual</p>
                            <p class="text-xs text-slate-600">Registrar agora</p>
                        </div>
                    </a>
                    
                    <a href="/registro-ponto/biometrico" class="group flex items-center justify-center gap-3 p-4 rounded-lg border-2 border-slate-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200">
                        <div class="w-10 h-10 rounded-lg bg-purple-100 group-hover:bg-purple-200 flex items-center justify-center transition-colors">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0h-2m8-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="text-left">
                            <p class="font-semibold text-slate-900 group-hover:text-purple-900">Biométrico</p>
                            <p class="text-xs text-slate-600">Capturar digital</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Data configuration
    var labelsData = {{ graficos.labels_dias | tojson }};
    var registrosDiariosData = {{ graficos.dados_registros_diarios | tojson }};
    var pontualidadeData = {{ graficos.dados_pontualidade | tojson }};
    var biometricosData = {{ stats.registros_biometricos }};
    var manuaisData = {{ stats.registros_manuais }};

    // Professional chart styling
    Chart.defaults.font.family = 'Inter, system-ui, sans-serif';
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#64748b';

    // Bar Chart - Registros Diários
    var ctx1 = document.getElementById('chartRegistrosDiarios').getContext('2d');
    new Chart(ctx1, {
        type: 'bar',
        data: {
            labels: labelsData,
            datasets: [{
                label: 'Registros',
                data: registrosDiariosData,
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 0,
                borderRadius: 6,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    border: {
                        display: false
                    },
                    grid: {
                        color: 'rgba(148, 163, 184, 0.1)'
                    },
                    ticks: {
                        color: '#64748b',
                        font: {
                            size: 11
                        }
                    }
                },
                x: {
                    border: {
                        display: false
                    },
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#64748b',
                        font: {
                            size: 11
                        }
                    }
                }
            }
        }
    });

    // Doughnut Chart - Métodos
    var ctx2 = document.getElementById('chartMetodos').getContext('2d');
    new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: ['Biométrico', 'Manual'],
            datasets: [{
                data: [biometricosData, manuaisData],
                backgroundColor: ['#8b5cf6', '#f59e0b'],
                borderColor: ['#ffffff', '#ffffff'],
                borderWidth: 3,
                hoverOffset: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '65%',
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Line Chart - Pontualidade
    var ctx3 = document.getElementById('chartPontualidade').getContext('2d');
    new Chart(ctx3, {
        type: 'line',
        data: {
            labels: labelsData,
            datasets: [{
                label: 'Atrasos',
                data: pontualidadeData,
                fill: true,
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderColor: '#ef4444',
                borderWidth: 3,
                tension: 0.4,
                pointBackgroundColor: '#ef4444',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 3,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    border: {
                        display: false
                    },
                    grid: {
                        color: 'rgba(148, 163, 184, 0.1)'
                    },
                    ticks: {
                        color: '#64748b',
                        font: {
                            size: 11
                        }
                    }
                },
                x: {
                    border: {
                        display: false
                    },
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#64748b',
                        font: {
                            size: 11
                        }
                    }
                }
            }
        }
    });
});
</script>

<style>
/* Professional Typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

body {
    font-family: 'Inter', system-ui, sans-serif;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
}

/* Enhanced animations */
.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

/* Improved hover states */
.group:hover .group-hover\:bg-blue-200 {
    background-color: rgb(191 219 254);
}

.group:hover .group-hover\:bg-emerald-200 {
    background-color: rgb(167 243 208);
}

.group:hover .group-hover\:bg-purple-200 {
    background-color: rgb(221 214 254);
}

/* Professional scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Typography enhancements */
.tracking-tighter {
    letter-spacing: -0.05em;
}

.tracking-wide {
    letter-spacing: 0.025em;
}

/* Responsive improvements */
@media (max-width: 640px) {
    .grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-4 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .text-4xl {
        font-size: 2rem;
        line-height: 2.5rem;
    }
    
    .p-6 {
        padding: 1rem;
    }
}
</style>
{% endblock %} 