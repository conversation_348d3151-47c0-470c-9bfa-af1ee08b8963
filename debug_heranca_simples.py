#!/usr/bin/env python3
"""
Debug: Por que a herança não funciona automaticamente?
"""

import sys
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def debug_heranca_simples():
    """Debug simples do problema de herança"""
    
    print("🔍 DEBUG: POR QUE A HERANÇA NÃO FUNCIONA AUTOMATICAMENTE?")
    print("=" * 70)
    
    db = DatabaseManager()
    
    # 1. Verificar como a função get_with_epis está buscando dados
    print("\n📋 PROBLEMA: A função get_with_epis não está aplicando herança dinâmica")
    
    # Buscar funcionário da empresa 4
    funcionario = db.execute_query("""
        SELECT f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id, f.usa_horario_empresa
        FROM funcionarios f 
        WHERE f.empresa_id = 4 AND f.ativo = 1 
        LIMIT 1
    """, fetch_one=True)
    
    if not funcionario:
        print("❌ Nenhum funcionário encontrado")
        return
    
    print(f"👤 Funcionário: {funcionario['nome_completo']} (ID: {funcionario['id']})")
    print(f"   • Empresa ID: {funcionario['empresa_id']}")
    print(f"   • Jornada ID: {funcionario['jornada_trabalho_id']}")
    print(f"   • Usa herança: {funcionario['usa_horario_empresa']}")
    
    # 2. Verificar jornada da empresa
    jornada_empresa = db.execute_query("""
        SELECT id, nome_jornada, seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida
        FROM jornadas_trabalho 
        WHERE empresa_id = 4 AND padrao = 1 AND ativa = 1
    """, fetch_one=True)
    
    if jornada_empresa:
        print(f"\n🏢 JORNADA DA EMPRESA:")
        print(f"   • ID: {jornada_empresa['id']}")
        print(f"   • Nome: {jornada_empresa['nome_jornada']}")
        print(f"   • Seg-Qui: {jornada_empresa['seg_qui_entrada']} às {jornada_empresa['seg_qui_saida']}")
        print(f"   • Sexta: {jornada_empresa['sexta_entrada']} às {jornada_empresa['sexta_saida']}")
    else:
        print("❌ Empresa não tem jornada padrão")
        return
    
    # 3. Verificar jornada do funcionário
    jornada_funcionario = db.execute_query("""
        SELECT id, nome_jornada, seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida
        FROM jornadas_trabalho 
        WHERE id = %s
    """, (funcionario['jornada_trabalho_id'],), fetch_one=True)
    
    if jornada_funcionario:
        print(f"\n👤 JORNADA DO FUNCIONÁRIO:")
        print(f"   • ID: {jornada_funcionario['id']}")
        print(f"   • Nome: {jornada_funcionario['nome_jornada']}")
        print(f"   • Seg-Qui: {jornada_funcionario['seg_qui_entrada']} às {jornada_funcionario['seg_qui_saida']}")
        print(f"   • Sexta: {jornada_funcionario['sexta_entrada']} às {jornada_funcionario['sexta_saida']}")
    
    # 4. Comparar
    print(f"\n🔍 COMPARAÇÃO:")
    if jornada_funcionario and jornada_empresa:
        if jornada_funcionario['id'] == jornada_empresa['id']:
            print("✅ Funcionário está usando a jornada da empresa")
        else:
            print("❌ PROBLEMA: Funcionário NÃO está usando a jornada da empresa")
            print(f"   • Funcionário usa jornada ID: {jornada_funcionario['id']}")
            print(f"   • Empresa tem jornada padrão ID: {jornada_empresa['id']}")
    
    # 5. O PROBLEMA REAL: A interface mostra dados incorretos
    print(f"\n🎯 PROBLEMA IDENTIFICADO:")
    print(f"A interface está mostrando:")
    print(f"   • Sexta: --:-- (campos vazios)")
    print(f"   • Intervalo: --:-- (campos vazios)")
    
    print(f"\nMas a jornada tem:")
    print(f"   • Sexta: {jornada_empresa['sexta_entrada']} às {jornada_empresa['sexta_saida']}")
    
    # 6. Verificar o que a função get_with_epis retorna
    print(f"\n📋 TESTANDO get_with_epis:")
    
    try:
        from utils.database import FuncionarioQueries
        funcionario_completo = FuncionarioQueries.get_with_epis(funcionario['id'])
        
        if funcionario_completo:
            print(f"✅ get_with_epis retornou dados:")
            print(f"   • jornada_sexta_entrada: {funcionario_completo.get('jornada_sexta_entrada')}")
            print(f"   • jornada_sexta_saida: {funcionario_completo.get('jornada_sexta_saida')}")
            print(f"   • jornada_intervalo_inicio: {funcionario_completo.get('jornada_intervalo_inicio')}")
            print(f"   • jornada_intervalo_fim: {funcionario_completo.get('jornada_intervalo_fim')}")
            
            # Verificar se os dados estão None
            if funcionario_completo.get('jornada_sexta_entrada') is None:
                print("❌ PROBLEMA: jornada_sexta_entrada é None!")
            if funcionario_completo.get('jornada_intervalo_inicio') is None:
                print("❌ PROBLEMA: jornada_intervalo_inicio é None!")
        
    except Exception as e:
        print(f"❌ Erro ao testar get_with_epis: {e}")
    
    print(f"\n💡 SOLUÇÃO:")
    print(f"O problema não é herança - é que a interface não está exibindo")
    print(f"os dados corretos que já existem no banco!")

if __name__ == "__main__":
    debug_heranca_simples()
