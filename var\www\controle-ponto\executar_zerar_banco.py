#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SCRIPT PARA ZERAR BANCO COMPLETO RLPONTO-WEB
Data: 11/07/2025
Versão: 1.0

ATENÇÃO: ESTE SCRIPT REMOVE TODOS OS DADOS!
Execute apenas se tiver certeza absoluta!
"""

import pymysql
import sys
import json
from datetime import datetime

class Config:
    @staticmethod
    def get_database_url():
        return {
            'host': 'localhost',
            'user': 'cavalcrod',
            'password': '200381',
            'database': 'controle_ponto',
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }

def confirmar_operacao():
    """Solicita confirmação do usuário antes de executar"""
    print("=" * 60)
    print("⚠️  ATENÇÃO: OPERAÇÃO DESTRUTIVA!")
    print("=" * 60)
    print("Este script irá REMOVER TODOS OS DADOS do banco, exceto:")
    print("✅ Usuários do sistema (tabela usuarios)")
    print("✅ Permissões (tabela permissoes)")
    print("✅ Configurações do sistema (tabela configuracoes_sistema)")
    print("✅ Logs dos últimos 7 dias")
    print()
    print("❌ SERÁ REMOVIDO:")
    print("- Todas as empresas")
    print("- Todos os funcionários")
    print("- Todos os registros de ponto")
    print("- Todos os clientes")
    print("- Todos os EPIs")
    print("- Todas as justificativas")
    print("- Todos os alertas")
    print("- Todas as alocações")
    print("- Todos os horários de trabalho")
    print("- E muito mais...")
    print()
    
    resposta1 = input("Digite 'CONFIRMO' para continuar: ").strip()
    if resposta1 != 'CONFIRMO':
        print("❌ Operação cancelada pelo usuário.")
        return False
    
    resposta2 = input("Tem certeza ABSOLUTA? Digite 'SIM TENHO CERTEZA': ").strip()
    if resposta2 != 'SIM TENHO CERTEZA':
        print("❌ Operação cancelada pelo usuário.")
        return False
    
    print("✅ Confirmação recebida. Iniciando limpeza...")
    return True

def fazer_backup_usuarios(connection):
    """Faz backup dos usuários antes da operação"""
    try:
        with connection.cursor() as cursor:
            print("📦 Fazendo backup dos usuários...")
            
            # Backup usuários
            cursor.execute("DROP TABLE IF EXISTS usuarios_backup_zerar")
            cursor.execute("CREATE TABLE usuarios_backup_zerar AS SELECT * FROM usuarios")
            
            # Backup permissões
            cursor.execute("DROP TABLE IF EXISTS permissoes_backup_zerar")
            cursor.execute("CREATE TABLE permissoes_backup_zerar AS SELECT * FROM permissoes")
            
            # Verificar backup
            cursor.execute("SELECT COUNT(*) as total FROM usuarios_backup_zerar")
            usuarios_backup = cursor.fetchone()['total']
            
            cursor.execute("SELECT COUNT(*) as total FROM permissoes_backup_zerar")
            permissoes_backup = cursor.fetchone()['total']
            
            print(f"✅ Backup criado: {usuarios_backup} usuários, {permissoes_backup} permissões")
            return True
            
    except Exception as e:
        print(f"❌ Erro ao fazer backup: {e}")
        return False

def executar_limpeza_completa():
    """Executa a limpeza completa do banco"""
    
    if not confirmar_operacao():
        return False
    
    try:
        config = Config.get_database_url()
        connection = pymysql.connect(**config)
        
        # Fazer backup dos usuários
        if not fazer_backup_usuarios(connection):
            print("❌ Falha no backup. Operação cancelada.")
            return False
        
        with connection.cursor() as cursor:
            print("\n🔧 Iniciando limpeza completa do banco...")
            
            # 1. Desabilitar verificações
            print("[1/8] Desabilitando verificações de chaves estrangeiras...")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO'")
            
            # 2. Limpar tabelas principais
            print("[2/8] Removendo dados das tabelas principais...")
            tabelas_principais = [
                'registros_ponto', 'epis', 'funcionarios', 'empresas', 'clientes',
                'empresa_clientes', 'funcionario_alocacoes', 'funcionario_cliente_alocacao',
                'horarios_trabalho', 'jornadas_trabalho'
            ]
            
            for tabela in tabelas_principais:
                try:
                    cursor.execute(f"TRUNCATE TABLE {tabela}")
                    print(f"  ✅ {tabela}")
                except Exception as e:
                    print(f"  ⚠️ {tabela}: {e}")
            
            # 3. Limpar tabelas de justificativas e alertas
            print("[3/8] Removendo justificativas e alertas...")
            tabelas_secundarias = [
                'justificativas_ponto', 'alertas_ponto', 'historico_alocacoes',
                'historico_alteracoes_ponto', 'historico_inferencias'
            ]
            
            for tabela in tabelas_secundarias:
                try:
                    cursor.execute(f"TRUNCATE TABLE {tabela}")
                    print(f"  ✅ {tabela}")
                except Exception as e:
                    print(f"  ⚠️ {tabela}: {e}")
            
            # 4. Limpar logs antigos (manter últimos 7 dias)
            print("[4/8] Limpando logs antigos (mantendo últimos 7 dias)...")
            logs_queries = [
                "DELETE FROM logs_sistema WHERE DATE(data_hora) < DATE_SUB(NOW(), INTERVAL 7 DAYS)",
                "DELETE FROM logs_seguranca WHERE DATE(timestamp) < DATE_SUB(NOW(), INTERVAL 7 DAYS)",
                "DELETE FROM logs_biometria WHERE DATE(timestamp) < DATE_SUB(NOW(), INTERVAL 7 DAYS)"
            ]
            
            for query in logs_queries:
                try:
                    cursor.execute(query)
                    print(f"  ✅ Logs antigos removidos")
                except Exception as e:
                    print(f"  ⚠️ Erro nos logs: {e}")
            
            # 5. Limpar outras tabelas
            print("[5/8] Removendo dados de outras tabelas...")
            outras_tabelas = [
                'banco_horas', 'backup_jornada_funcionarios', 'cad_empresas',
                'dia_dados', 'dispositivos_biometricos', 'empresas_config',
                'log_exclusao_empresas', 'tentativas_biometria'
            ]
            
            for tabela in outras_tabelas:
                try:
                    cursor.execute(f"TRUNCATE TABLE {tabela}")
                    print(f"  ✅ {tabela}")
                except Exception as e:
                    print(f"  ⚠️ {tabela}: {e}")
            
            # 6. Remover views
            print("[6/8] Removendo views...")
            views = [
                'v_alertas_pendentes', 'v_estatisticas_alertas', 'v_turnos_ativos',
                'vw_analise_pontualidade', 'vw_clientes_detalhados', 'vw_empresa_principal',
                'vw_estatisticas_biometria', 'vw_estatisticas_ponto_setor', 'vw_estatisticas_pontos',
                'vw_estatisticas_sistema', 'vw_funcionarios_alocados', 'vw_funcionarios_biometria',
                'vw_horas_trabalhadas', 'vw_relatorio_pontos'
            ]
            
            for view in views:
                try:
                    cursor.execute(f"DROP VIEW IF EXISTS {view}")
                    print(f"  ✅ {view}")
                except Exception as e:
                    print(f"  ⚠️ {view}: {e}")
            
            # 7. Resetar AUTO_INCREMENT
            print("[7/8] Resetando AUTO_INCREMENT...")
            tabelas_reset = [
                'empresas', 'funcionarios', 'clientes', 'registros_ponto', 'epis',
                'horarios_trabalho', 'jornadas_trabalho', 'justificativas_ponto',
                'alertas_ponto', 'empresa_clientes', 'funcionario_alocacoes'
            ]
            
            for tabela in tabelas_reset:
                try:
                    cursor.execute(f"ALTER TABLE {tabela} AUTO_INCREMENT = 1")
                    print(f"  ✅ {tabela}")
                except Exception as e:
                    print(f"  ⚠️ {tabela}: {e}")
            
            # 8. Reabilitar verificações
            print("[8/8] Reabilitando verificações...")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
            
            # Log da operação
            cursor.execute("""
                INSERT INTO logs_sistema (acao, tabela_afetada, detalhes, data_hora)
                VALUES ('ZERAR_BANCO_COMPLETO', 'TODAS_TABELAS', %s, NOW())
            """, (json.dumps({
                'operacao': 'LIMPEZA_COMPLETA_BANCO',
                'preservado': 'usuarios,permissoes,configuracoes_sistema',
                'removido': 'empresas,funcionarios,registros_ponto,clientes,epis,etc',
                'data_execucao': datetime.now().isoformat()
            }),))
            
        connection.commit()
        
        # Verificação final
        print("\n📊 Verificação final:")
        with connection.cursor() as cursor:
            # Verificar preservados
            cursor.execute("SELECT COUNT(*) as total FROM usuarios")
            usuarios = cursor.fetchone()['total']
            
            cursor.execute("SELECT COUNT(*) as total FROM permissoes")
            permissoes = cursor.fetchone()['total']
            
            cursor.execute("SELECT COUNT(*) as total FROM configuracoes_sistema")
            configs = cursor.fetchone()['total']
            
            # Verificar removidos
            cursor.execute("SELECT COUNT(*) as total FROM empresas")
            empresas = cursor.fetchone()['total']
            
            cursor.execute("SELECT COUNT(*) as total FROM funcionarios")
            funcionarios = cursor.fetchone()['total']
            
            cursor.execute("SELECT COUNT(*) as total FROM registros_ponto")
            registros = cursor.fetchone()['total']
            
            print(f"✅ PRESERVADOS:")
            print(f"   - Usuários: {usuarios}")
            print(f"   - Permissões: {permissoes}")
            print(f"   - Configurações: {configs}")
            
            print(f"❌ REMOVIDOS:")
            print(f"   - Empresas: {empresas}")
            print(f"   - Funcionários: {funcionarios}")
            print(f"   - Registros: {registros}")
        
        connection.close()
        
        print("\n" + "=" * 60)
        print("✅ LIMPEZA COMPLETA REALIZADA COM SUCESSO!")
        print("🔒 USUÁRIOS E CONFIGURAÇÕES PRESERVADOS")
        print("🗑️ TODOS OS DADOS OPERACIONAIS REMOVIDOS")
        print("📊 BANCO PRONTO PARA NOVO INÍCIO")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante a limpeza: {e}")
        return False

if __name__ == "__main__":
    print("🔧 SCRIPT DE LIMPEZA COMPLETA DO BANCO RLPONTO-WEB")
    print("Data:", datetime.now().strftime("%d/%m/%Y %H:%M:%S"))
    print()
    
    if executar_limpeza_completa():
        print("\n✅ Operação concluída com sucesso!")
        sys.exit(0)
    else:
        print("\n❌ Operação falhou ou foi cancelada.")
        sys.exit(1)
