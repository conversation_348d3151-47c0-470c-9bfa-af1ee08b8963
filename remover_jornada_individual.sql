-- =====================================================
-- REMOÇÃO DE JORNADA INDIVIDUAL DOS FUNCIONÁRIOS
-- Sistema: RLPONTO-WEB v1.0
-- Data: 25/06/2025
-- Objetivo: Migrar para jornada hierárquica por empresa
-- =====================================================

USE controle_ponto;

-- =====================================================
-- 1. BACKUP DOS DADOS EXISTENTES (OPCIONAL)
-- =====================================================

-- Criar tabela de backup dos dados de jornada antes da remoção
CREATE TABLE IF NOT EXISTS backup_jornada_funcionarios AS
SELECT 
    id,
    nome_completo,
    jornada_seg_qui_entrada,
    jornada_seg_qui_saida,
    jornada_sex_entrada,
    jornada_sex_saida,
    jornada_intervalo_entrada,
    jornada_intervalo_saida,
    tolerancia_ponto,
    inicio_expediente,
    horario_saida_seg_qui,
    horario_saida_sexta,
    periodo_almoco_inicio,
    periodo_almoco_fim,
    duracao_minima_almoco,
    tolerancia_entrada,
    permite_banco_horas_positivo,
    NOW() as data_backup
FROM funcionarios
WHERE jornada_seg_qui_entrada IS NOT NULL 
   OR jornada_seg_qui_saida IS NOT NULL;

SELECT CONCAT('✅ Backup criado com ', COUNT(*), ' registros') as status 
FROM backup_jornada_funcionarios;

-- =====================================================
-- 2. ADICIONAR NOVO CAMPO PARA HORAS OBRIGATÓRIAS
-- =====================================================

-- Adicionar campo para horas de trabalho obrigatórias por dia
ALTER TABLE funcionarios 
ADD COLUMN IF NOT EXISTS horas_trabalho_obrigatorias DECIMAL(4,2) DEFAULT 8.00 
COMMENT 'Horas de trabalho obrigatórias por dia (ex: 8.00 para 8 horas)';

-- Adicionar campo para vincular funcionário ao horário da empresa
ALTER TABLE funcionarios 
ADD COLUMN IF NOT EXISTS usa_horario_empresa BOOLEAN DEFAULT TRUE 
COMMENT 'Se TRUE, usa horário da empresa; se FALSE, usa configuração individual';

-- =====================================================
-- 3. MIGRAR DADOS EXISTENTES
-- =====================================================

-- Calcular horas obrigatórias baseado na jornada atual
UPDATE funcionarios 
SET horas_trabalho_obrigatorias = CASE
    WHEN jornada_seg_qui_entrada IS NOT NULL AND jornada_seg_qui_saida IS NOT NULL THEN
        -- Calcular diferença em horas (assumindo 1h de almoço)
        GREATEST(
            TIMESTAMPDIFF(MINUTE, jornada_seg_qui_entrada, jornada_seg_qui_saida) / 60.0 - 1.0,
            6.0  -- Mínimo 6 horas
        )
    ELSE 8.00  -- Padrão 8 horas
END
WHERE jornada_seg_qui_entrada IS NOT NULL;

-- Definir que funcionários usarão horário da empresa
UPDATE funcionarios 
SET usa_horario_empresa = TRUE;

SELECT 'Dados migrados para novo formato' as status;

-- =====================================================
-- 4. REMOVER CAMPOS DE JORNADA INDIVIDUAL
-- =====================================================

-- Remover campos antigos de jornada individual
ALTER TABLE funcionarios 
DROP COLUMN IF EXISTS jornada_seg_qui_entrada,
DROP COLUMN IF EXISTS jornada_seg_qui_saida,
DROP COLUMN IF EXISTS jornada_sex_entrada,
DROP COLUMN IF EXISTS jornada_sex_saida,
DROP COLUMN IF EXISTS jornada_intervalo_entrada,
DROP COLUMN IF EXISTS jornada_intervalo_saida;

-- Remover campos das novas regras (que serão definidos na empresa)
ALTER TABLE funcionarios 
DROP COLUMN IF EXISTS inicio_expediente,
DROP COLUMN IF EXISTS horario_saida_seg_qui,
DROP COLUMN IF EXISTS horario_saida_sexta,
DROP COLUMN IF EXISTS periodo_almoco_inicio,
DROP COLUMN IF EXISTS periodo_almoco_fim,
DROP COLUMN IF EXISTS duracao_minima_almoco,
DROP COLUMN IF EXISTS tolerancia_entrada,
DROP COLUMN IF EXISTS permite_banco_horas_positivo,
DROP COLUMN IF EXISTS data_atualizacao_jornada;

SELECT 'Campos de jornada individual removidos' as status;

-- =====================================================
-- 5. GARANTIR ESTRUTURA DE HORÁRIOS NA EMPRESA
-- =====================================================

-- Verificar se tabela horarios_trabalho existe e tem estrutura adequada
CREATE TABLE IF NOT EXISTS horarios_trabalho (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_id INT NOT NULL,
    nome_horario VARCHAR(100) NOT NULL COMMENT 'Nome do horário (ex: Administrativo, Produção)',
    
    -- Horários básicos
    entrada_manha TIME NOT NULL DEFAULT '08:00:00',
    saida_almoco TIME DEFAULT '12:00:00',
    entrada_tarde TIME DEFAULT '13:00:00',
    saida TIME NOT NULL DEFAULT '17:00:00',
    
    -- Horários diferenciados
    saida_sexta TIME DEFAULT '16:30:00' COMMENT 'Horário de saída na sexta-feira',
    
    -- Configurações de intervalo
    periodo_almoco_inicio TIME DEFAULT '11:00:00',
    periodo_almoco_fim TIME DEFAULT '14:00:00',
    duracao_minima_almoco INT DEFAULT 60 COMMENT 'Duração mínima em minutos',
    
    -- Tolerâncias
    tolerancia_entrada INT DEFAULT 15 COMMENT 'Tolerância para entrada em minutos',
    tolerancia_saida INT DEFAULT 10 COMMENT 'Tolerância para saída em minutos',
    
    -- Configurações de banco de horas
    permite_banco_horas BOOLEAN DEFAULT FALSE,
    limite_banco_horas_mensal INT DEFAULT 10 COMMENT 'Limite em horas por mês',
    
    -- Configurações gerais
    ativo BOOLEAN DEFAULT TRUE,
    turno ENUM('Diurno', 'Noturno', 'Misto') DEFAULT 'Diurno',
    
    -- Auditoria
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_empresa (empresa_id),
    INDEX idx_ativo (ativo),
    
    -- Chave estrangeira
    FOREIGN KEY (empresa_id) REFERENCES empresas(id) ON DELETE CASCADE
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci 
COMMENT='Horários de trabalho definidos por empresa';

-- =====================================================
-- 6. CRIAR HORÁRIO PADRÃO SE NÃO EXISTIR
-- =====================================================

-- Inserir horário padrão para empresa principal
INSERT INTO horarios_trabalho (
    empresa_id, nome_horario, entrada_manha, saida_almoco, entrada_tarde, saida, saida_sexta,
    periodo_almoco_inicio, periodo_almoco_fim, duracao_minima_almoco,
    tolerancia_entrada, tolerancia_saida, permite_banco_horas, ativo
) VALUES (
    1, 'Horário Administrativo Padrão', 
    '08:00:00', '12:00:00', '13:00:00', '17:00:00', '16:30:00',
    '11:00:00', '14:00:00', 60,
    15, 10, TRUE, TRUE
) ON DUPLICATE KEY UPDATE 
    nome_horario = VALUES(nome_horario),
    data_atualizacao = CURRENT_TIMESTAMP;

-- =====================================================
-- 7. VINCULAR FUNCIONÁRIOS AO HORÁRIO PADRÃO
-- =====================================================

-- Atualizar funcionários para usar horário padrão da empresa
UPDATE funcionarios 
SET horario_trabalho_id = 1,
    empresa_id = COALESCE(empresa_id, 1)
WHERE horario_trabalho_id IS NULL;

SELECT CONCAT('✅ ', COUNT(*), ' funcionários vinculados ao horário padrão') as status
FROM funcionarios 
WHERE horario_trabalho_id = 1;

-- =====================================================
-- 8. ATUALIZAR CAMPOS OBRIGATÓRIOS
-- =====================================================

-- Garantir que tolerancia_ponto seja mantida como configuração individual
-- (pode ser sobrescrita pelo horário da empresa)
UPDATE funcionarios 
SET tolerancia_ponto = COALESCE(tolerancia_ponto, 10)
WHERE tolerancia_ponto IS NULL OR tolerancia_ponto = 0;

-- =====================================================
-- 9. CRIAR VIEW PARA COMPATIBILIDADE
-- =====================================================

-- View para manter compatibilidade com código existente
CREATE OR REPLACE VIEW vw_funcionarios_jornada AS
SELECT 
    f.id,
    f.nome_completo,
    f.horas_trabalho_obrigatorias,
    f.tolerancia_ponto,
    f.usa_horario_empresa,
    
    -- Dados do horário da empresa
    ht.nome_horario,
    ht.entrada_manha,
    ht.saida_almoco,
    ht.entrada_tarde,
    ht.saida,
    ht.saida_sexta,
    ht.periodo_almoco_inicio,
    ht.periodo_almoco_fim,
    ht.duracao_minima_almoco,
    ht.tolerancia_entrada,
    ht.tolerancia_saida,
    ht.permite_banco_horas,
    
    -- Dados da empresa
    e.razao_social as empresa_nome,
    e.nome_fantasia
    
FROM funcionarios f
LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
LEFT JOIN empresas e ON f.empresa_id = e.id
WHERE f.status_cadastro = 'Ativo';

-- =====================================================
-- 10. VERIFICAÇÕES FINAIS
-- =====================================================

-- Verificar estrutura final
SELECT 'Verificação da estrutura final:' as status;

SELECT 
    COUNT(*) as total_funcionarios,
    COUNT(CASE WHEN horario_trabalho_id IS NOT NULL THEN 1 END) as com_horario_empresa,
    COUNT(CASE WHEN horas_trabalho_obrigatorias IS NOT NULL THEN 1 END) as com_horas_obrigatorias
FROM funcionarios 
WHERE status_cadastro = 'Ativo';

-- Verificar horários disponíveis
SELECT 
    ht.id,
    ht.nome_horario,
    ht.entrada_manha,
    ht.saida,
    ht.tolerancia_entrada,
    COUNT(f.id) as funcionarios_vinculados
FROM horarios_trabalho ht
LEFT JOIN funcionarios f ON ht.id = f.horario_trabalho_id
WHERE ht.ativo = TRUE
GROUP BY ht.id, ht.nome_horario, ht.entrada_manha, ht.saida, ht.tolerancia_entrada;

-- =====================================================
-- FINALIZAÇÃO
-- =====================================================

-- Atualizar estatísticas das tabelas
ANALYZE TABLE funcionarios, horarios_trabalho, empresas;

-- Log de conclusão
SELECT 'Migração de jornada individual para empresarial concluída com sucesso!' as status,
       NOW() as data_execucao;
