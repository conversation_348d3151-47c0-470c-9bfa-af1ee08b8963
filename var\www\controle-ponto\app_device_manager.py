"""
RLPONTO-WEB v1.0 - Gerenciador Avançado de Dispositivos Biométricos

Implementação dos Parâmetros Mínimos de Configuração (MCP) para detecção
automática de dispositivos biométricos via Windows Biometric Framework (WBF)
e PyUSB, com suporte multi-vendor.

Desenvolvido por: AiNexus Tecnologia
Autor: <PERSON> - Full Stack Developer
Data: 11/01/2025
© 2025 AiNexus Tecnologia. Todos os direitos reservados.
"""

import os
import sys
import logging
import subprocess
import json
import platform
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from flask import Blueprint, render_template, request, jsonify, session
from utils.auth import require_admin
from utils.database import get_db_connection
import threading
import time

# Configurar logger
logger = logging.getLogger('controle-ponto.device-manager')

# Criar Blueprint
device_manager_bp = Blueprint('device_manager', __name__, url_prefix='/configuracoes/dispositivos')

# Configurações globais
DEVICE_SCAN_INTERVAL = 30  # segundos
MAX_SCAN_TIMEOUT = 10  # segundos
SUPPORTED_VENDORS = {
    '147E': 'SecuGen',
    '16D1': 'Suprema', 
    '0BDB': 'Nitgen',
    '1162': 'Integrated Biometrics',
    '27C6': 'Goodix',
    '06CB': 'Synaptics'
}

class BiometricDeviceManager:
    """Gerenciador de dispositivos biométricos com detecção automática"""
    
    def __init__(self):
        self.is_scanning = False
        self.last_scan = None
        self.detected_devices = []
        
    def get_windows_biometric_devices(self) -> List[Dict]:
        """Detecta dispositivos via Windows Biometric Framework"""
        devices = []
        
        try:
            if platform.system() != 'Windows':
                logger.warning("Windows Biometric Framework apenas disponível no Windows")
                return devices
            
            # PowerShell script para detectar dispositivos WBF
            powershell_script = """
            Get-WmiObject -Class Win32_PnPEntity | Where-Object {
                $_.Name -like "*finger*" -or 
                $_.Name -like "*biometric*" -or
                $_.DeviceID -like "*VID_147E*" -or
                $_.DeviceID -like "*VID_16D1*" -or
                $_.DeviceID -like "*VID_0BDB*" -or
                $_.DeviceID -like "*VID_1162*" -or
                $_.DeviceID -like "*VID_27C6*" -or
                $_.DeviceID -like "*VID_06CB*"
            } | Select-Object Name, DeviceID, Manufacturer, Status | ConvertTo-Json
            """
            
            # Executar PowerShell
            result = subprocess.run([
                'powershell', '-Command', powershell_script
            ], capture_output=True, text=True, timeout=MAX_SCAN_TIMEOUT)
            
            if result.returncode == 0 and result.stdout:
                try:
                    wmi_devices = json.loads(result.stdout)
                    if not isinstance(wmi_devices, list):
                        wmi_devices = [wmi_devices]
                    
                    for device in wmi_devices:
                        device_info = self._parse_wmi_device(device)
                        if device_info:
                            devices.append(device_info)
                            
                except json.JSONDecodeError as e:
                    logger.error(f"Erro ao processar saída PowerShell: {e}")
                    
        except subprocess.TimeoutExpired:
            logger.warning("Timeout na detecção de dispositivos via WBF")
        except Exception as e:
            logger.error(f"Erro na detecção via Windows Biometric Framework: {e}")
            
        return devices
    
    def get_usb_biometric_devices(self) -> List[Dict]:
        """Detecta dispositivos via PyUSB"""
        devices = []
        
        try:
            # PowerShell script para detectar dispositivos USB biométricos
            powershell_script = """
            Get-WmiObject -Class Win32_USBDevice | Where-Object {
                $_.DeviceID -like "*VID_147E*" -or
                $_.DeviceID -like "*VID_16D1*" -or
                $_.DeviceID -like "*VID_0BDB*" -or
                $_.DeviceID -like "*VID_1162*" -or
                $_.DeviceID -like "*VID_27C6*" -or
                $_.DeviceID -like "*VID_06CB*"
            } | Select-Object Name, DeviceID, Manufacturer, Status | ConvertTo-Json
            """
            
            result = subprocess.run([
                'powershell', '-Command', powershell_script
            ], capture_output=True, text=True, timeout=MAX_SCAN_TIMEOUT)
            
            if result.returncode == 0 and result.stdout:
                try:
                    usb_devices = json.loads(result.stdout)
                    if not isinstance(usb_devices, list):
                        usb_devices = [usb_devices]
                    
                    for device in usb_devices:
                        device_info = self._parse_usb_device(device)
                        if device_info:
                            devices.append(device_info)
                            
                except json.JSONDecodeError as e:
                    logger.error(f"Erro ao processar dispositivos USB: {e}")
                    
        except Exception as e:
            logger.error(f"Erro na detecção de dispositivos USB: {e}")
            
        return devices
    
    def _parse_wmi_device(self, device: Dict) -> Optional[Dict]:
        """Parse dispositivo WMI para formato padrão"""
        try:
            device_id = device.get('DeviceID', '')
            name = device.get('Name', 'Dispositivo Biométrico')
            
            # Extrair Vendor ID e Product ID
            vendor_id, product_id = self._extract_ids(device_id)
            fabricante = SUPPORTED_VENDORS.get(vendor_id, 'Outros')
            
            return {
                'device_id': device_id,
                'nome_dispositivo': name,
                'fabricante': fabricante,
                'vendor_id': vendor_id,
                'product_id': product_id,
                'manufacturer': device.get('Manufacturer', ''),
                'status': device.get('Status', 'Unknown'),
                'detection_method': 'WBF',
                'porta_usb': self._extract_usb_port(device_id),
                'data_deteccao': datetime.now()
            }
        except Exception as e:
            logger.error(f"Erro ao processar dispositivo WMI: {e}")
            return None
    
    def _parse_usb_device(self, device: Dict) -> Optional[Dict]:
        """Parse dispositivo USB para formato padrão"""
        try:
            device_id = device.get('DeviceID', '')
            name = device.get('Name', 'Dispositivo USB Biométrico')
            
            # Extrair Vendor ID e Product ID
            vendor_id, product_id = self._extract_ids(device_id)
            fabricante = SUPPORTED_VENDORS.get(vendor_id, 'Outros')
            
            return {
                'device_id': device_id,
                'nome_dispositivo': name,
                'fabricante': fabricante,
                'vendor_id': vendor_id,
                'product_id': product_id,
                'manufacturer': device.get('Manufacturer', ''),
                'status': device.get('Status', 'Unknown'),
                'detection_method': 'USB',
                'porta_usb': self._extract_usb_port(device_id),
                'data_deteccao': datetime.now()
            }
        except Exception as e:
            logger.error(f"Erro ao processar dispositivo USB: {e}")
            return None
    
    def _extract_ids(self, device_id: str) -> Tuple[str, str]:
        """Extrai Vendor ID e Product ID do device ID"""
        try:
            vendor_id = ''
            product_id = ''
            
            # Buscar padrões VID e PID
            parts = device_id.upper().split('&')
            for part in parts:
                if part.startswith('VID_'):
                    vendor_id = part[4:]
                elif part.startswith('PID_'):
                    product_id = part[4:]
                    
            return vendor_id, product_id
        except Exception:
            return '', ''
    
    def _extract_usb_port(self, device_id: str) -> str:
        """Extrai informação da porta USB"""
        try:
            # Procurar por padrões de porta USB no device ID
            if 'USB\\' in device_id.upper():
                parts = device_id.split('\\')
                if len(parts) > 2:
                    return parts[2]
            return ''
        except Exception:
            return ''
    
    def scan_devices(self) -> List[Dict]:
        """Executa scan completo de dispositivos"""
        self.is_scanning = True
        all_devices = []
        
        try:
            logger.info("Iniciando scan de dispositivos biométricos...")
            
            # Detectar via Windows Biometric Framework
            wbf_devices = self.get_windows_biometric_devices()
            logger.info(f"Detectados {len(wbf_devices)} dispositivos via WBF")
            
            # Detectar via USB
            usb_devices = self.get_usb_biometric_devices()
            logger.info(f"Detectados {len(usb_devices)} dispositivos via USB")
            
            # Combinar e remover duplicatas
            all_devices = wbf_devices + usb_devices
            unique_devices = []
            seen_ids = set()
            
            for device in all_devices:
                device_key = f"{device['vendor_id']}_{device['product_id']}"
                if device_key not in seen_ids:
                    unique_devices.append(device)
                    seen_ids.add(device_key)
            
            self.detected_devices = unique_devices
            self.last_scan = datetime.now()
            
            logger.info(f"Scan concluído: {len(unique_devices)} dispositivos únicos detectados")
            
        except Exception as e:
            logger.error(f"Erro durante scan de dispositivos: {e}")
        finally:
            self.is_scanning = False
            
        return self.detected_devices

# Instância global do gerenciador
device_manager = BiometricDeviceManager()

def get_registered_devices():
    """Obtém dispositivos registrados no banco"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT * FROM dispositivos_biometricos 
            WHERE removido = FALSE 
            ORDER BY data_registro DESC
        """)
        
        devices = cursor.fetchall()
        conn.close()
        
        # Converter para lista de dicts
        if devices:
            columns = ['id', 'device_id', 'nome_dispositivo', 'fabricante', 'modelo',
                      'numero_serie', 'vendor_id', 'product_id', 'porta_usb',
                      'firmware_version', 'driver_version', 'resolucao_sensor',
                      'area_captura', 'status_dispositivo', 'data_registro',
                      'data_ultima_deteccao', 'data_ultima_utilizacao',
                      'configuracoes_json', 'observacoes', 'removido',
                      'data_remocao', 'usuario_registro']
            
            return [dict(zip(columns, device)) for device in devices]
        
        return []
        
    except Exception as e:
        logger.error(f"Erro ao obter dispositivos registrados: {e}")
        return []

def register_device(device_data: Dict) -> bool:
    """Registra dispositivo no banco"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Verificar se dispositivo já existe
        cursor.execute("""
            SELECT id FROM dispositivos_biometricos 
            WHERE device_id = %s AND removido = FALSE
        """, (device_data['device_id'],))
        
        existing = cursor.fetchone()
        
        if existing:
            # Atualizar dispositivo existente
            cursor.execute("""
                UPDATE dispositivos_biometricos SET
                    nome_dispositivo = %s,
                    fabricante = %s,
                    vendor_id = %s,
                    product_id = %s,
                    porta_usb = %s,
                    status_dispositivo = 'Ativo',
                    data_ultima_deteccao = CURRENT_TIMESTAMP,
                    usuario_registro = %s
                WHERE device_id = %s
            """, (
                device_data['nome_dispositivo'],
                device_data['fabricante'],
                device_data['vendor_id'],
                device_data['product_id'],
                device_data['porta_usb'],
                session.get('usuario', 'Sistema'),
                device_data['device_id']
            ))
            device_id = existing[0]
        else:
            # Inserir novo dispositivo
            cursor.execute("""
                INSERT INTO dispositivos_biometricos 
                (device_id, nome_dispositivo, fabricante, vendor_id, product_id,
                 porta_usb, status_dispositivo, usuario_registro)
                VALUES (%s, %s, %s, %s, %s, %s, 'Ativo', %s)
            """, (
                device_data['device_id'],
                device_data['nome_dispositivo'],
                device_data['fabricante'],
                device_data['vendor_id'],
                device_data['product_id'],
                device_data['porta_usb'],
                session.get('usuario', 'Sistema')
            ))
            device_id = cursor.lastrowid
        
        # Registrar evento no histórico
        cursor.execute("""
            INSERT INTO historico_deteccoes 
            (dispositivo_id, evento, porta_usb, detalhes_evento, usuario)
            VALUES (%s, 'Conectado', %s, %s, %s)
        """, (
            device_id,
            device_data['porta_usb'],
            f"Dispositivo registrado via {device_data.get('detection_method', 'Sistema')}",
            session.get('usuario', 'Sistema')
        ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Dispositivo registrado: {device_data['nome_dispositivo']}")
        return True
        
    except Exception as e:
        logger.error(f"Erro ao registrar dispositivo: {e}")
        return False

# ========================================
# ROTAS PRINCIPAIS
# ========================================

@device_manager_bp.route('/')
@require_admin
def index():
    """Página principal de gerenciamento de dispositivos"""
    try:
        # Obter dispositivos registrados
        registered_devices = get_registered_devices()
        
        # Estatísticas
        total_devices = len(registered_devices)
        active_devices = len([d for d in registered_devices if d['status_dispositivo'] == 'Ativo'])
        
        context = {
            'titulo': 'Gerenciamento de Dispositivos Biométricos',
            'dispositivos': registered_devices,
            'total_dispositivos': total_devices,
            'dispositivos_ativos': active_devices,
            'ultima_varredura': device_manager.last_scan,
            'varredura_ativa': device_manager.is_scanning
        }
        
        return render_template('configuracoes/dispositivos.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar dispositivos: {e}")
        return render_template('errors/500.html'), 500

@device_manager_bp.route('/api/scan', methods=['POST'])
@require_admin
def api_scan_devices():
    """API para scan de dispositivos"""
    try:
        if device_manager.is_scanning:
            return jsonify({
                'success': False,
                'message': 'Scan já em andamento'
            })
        
        # Executar scan em thread separada para não bloquear
        def scan_thread():
            device_manager.scan_devices()
        
        thread = threading.Thread(target=scan_thread)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': 'Scan iniciado com sucesso'
        })
        
    except Exception as e:
        logger.error(f"Erro na API de scan: {e}")
        return jsonify({
            'success': False,
            'message': 'Erro interno do sistema'
        })

@device_manager_bp.route('/api/detected', methods=['GET'])
@require_admin
def api_detected_devices():
    """API para obter dispositivos detectados"""
    try:
        # Converter datetime para string
        detected = []
        for device in device_manager.detected_devices:
            device_copy = device.copy()
            if 'data_deteccao' in device_copy:
                device_copy['data_deteccao'] = device_copy['data_deteccao'].isoformat()
            detected.append(device_copy)
        
        return jsonify({
            'success': True,
            'dispositivos': detected,
            'total': len(detected),
            'ultima_varredura': device_manager.last_scan.isoformat() if device_manager.last_scan else None,
            'varredura_ativa': device_manager.is_scanning
        })
        
    except Exception as e:
        logger.error(f"Erro na API de dispositivos detectados: {e}")
        return jsonify({
            'success': False,
            'message': 'Erro interno do sistema'
        })

@device_manager_bp.route('/api/register', methods=['POST'])
@require_admin
def api_register_device():
    """API para registrar dispositivo"""
    try:
        data = request.get_json()
        
        if not data or not data.get('device_id'):
            return jsonify({
                'success': False,
                'message': 'Dados do dispositivo obrigatórios'
            })
        
        # Registrar dispositivo
        success = register_device(data)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Dispositivo registrado com sucesso'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Erro ao registrar dispositivo'
            })
            
    except Exception as e:
        logger.error(f"Erro na API de registro: {e}")
        return jsonify({
            'success': False,
            'message': 'Erro interno do sistema'
        })

@device_manager_bp.route('/api/remove/<int:device_id>', methods=['POST'])
@require_admin
def api_remove_device(device_id):
    """API para remover dispositivo (soft delete)"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Marcar como removido
        cursor.execute("""
            UPDATE dispositivos_biometricos SET
                removido = TRUE,
                data_remocao = CURRENT_TIMESTAMP,
                status_dispositivo = 'Desconectado'
            WHERE id = %s
        """, (device_id,))
        
        # Registrar evento
        cursor.execute("""
            INSERT INTO historico_deteccoes 
            (dispositivo_id, evento, detalhes_evento, usuario)
            VALUES (%s, 'Desconectado', 'Dispositivo removido pelo administrador', %s)
        """, (device_id, session.get('usuario', 'Sistema')))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Dispositivo removido: ID {device_id}")
        
        return jsonify({
            'success': True,
            'message': 'Dispositivo removido com sucesso'
        })
        
    except Exception as e:
        logger.error(f"Erro ao remover dispositivo: {e}")
        return jsonify({
            'success': False,
            'message': 'Erro interno do sistema'
        })

@device_manager_bp.route('/api/status')
@require_admin
def api_device_status():
    """API para status do sistema de dispositivos"""
    try:
        registered_devices = get_registered_devices()
        
        stats = {
            'total_dispositivos': len(registered_devices),
            'dispositivos_ativos': len([d for d in registered_devices if d['status_dispositivo'] == 'Ativo']),
            'dispositivos_inativos': len([d for d in registered_devices if d['status_dispositivo'] != 'Ativo']),
            'fabricantes': list(set([d['fabricante'] for d in registered_devices])),
            'ultima_varredura': device_manager.last_scan.isoformat() if device_manager.last_scan else None,
            'varredura_ativa': device_manager.is_scanning,
            'dispositivos_detectados': len(device_manager.detected_devices)
        }
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Erro na API de status: {e}")
        return jsonify({
            'success': False,
            'message': 'Erro interno do sistema'
        })

# ========================================
# HANDLERS DE ERRO
# ========================================

@device_manager_bp.errorhandler(404)
def handle_404(error):
    """Handler para erro 404"""
    return render_template('errors/404.html'), 404

@device_manager_bp.errorhandler(500)
def handle_500(error):
    """Handler para erro 500"""
    logger.error(f"Erro 500 no device_manager: {error}")
    return render_template('errors/500.html'), 500 