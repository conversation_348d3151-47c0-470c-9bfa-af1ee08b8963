// biometria-service.js
// Serviço para comunicação com o leitor biométrico ZK4500 via WebSocket

class BiometriaService {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.deviceInfo = null;
        this.serviceUrl = "ws://localhost:8765"; // Endereço padrão do serviço local
        this.callbacks = {
            onConnect: null,
            onDisconnect: null,
            onError: null,
            onCapture: null,
            onStatus: null
        };
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 2000; // 2 segundos
        this.simulationMode = false; // Modo de simulação para testes sem hardware
    }

    // Inicializa a conexão com o serviço local
    async connect() {
        try {
            if (this.socket && this.isConnected) {
                console.log("Já conectado ao serviço de biometria");
                return true;
            }

            return new Promise((resolve, reject) => {
                try {
                    this.socket = new WebSocket(this.serviceUrl);
                    
                    this.socket.onopen = () => {
                        console.log("Conectado ao serviço de biometria");
                        this.isConnected = true;
                        this.reconnectAttempts = 0;
                        
                        // Solicita informações do dispositivo
                        this._sendCommand("GET_DEVICE_INFO");
                        
                        if (this.callbacks.onConnect) {
                            this.callbacks.onConnect();
                        }
                        
                        resolve(true);
                    };
                    
                    this.socket.onclose = (event) => {
                        console.log(`Conexão com serviço de biometria fechada: ${event.code} - ${event.reason}`);
                        this.isConnected = false;
                        this.deviceInfo = null;
                        
                        if (this.callbacks.onDisconnect) {
                            this.callbacks.onDisconnect(event);
                        }
                        
                        // Tenta reconectar automaticamente
                        this._attemptReconnect();
                    };
                    
                    this.socket.onerror = (error) => {
                        console.error("Erro na conexão com serviço de biometria:", error);
                        
                        if (this.callbacks.onError) {
                            this.callbacks.onError(error);
                        }
                        
                        // Se não conseguir conectar ao WebSocket, ativa o modo de simulação
                        this._activateSimulationMode();
                        reject(error);
                    };
                    
                    this.socket.onmessage = (event) => {
                        this._handleMessage(event.data);
                    };
                    
                    // Timeout para a conexão
                    setTimeout(() => {
                        if (!this.isConnected) {
                            this._activateSimulationMode();
                            reject(new Error("Timeout ao conectar ao serviço de biometria"));
                        }
                    }, 5000);
                } catch (error) {
                    console.error("Erro ao criar WebSocket:", error);
                    this._activateSimulationMode();
                    reject(error);
                }
            });
        } catch (error) {
            console.error("Erro ao conectar ao serviço de biometria:", error);
            this._activateSimulationMode();
            if (this.callbacks.onError) {
                this.callbacks.onError(error);
            }
            return false;
        }
    }

    // Ativa o modo de simulação quando não há conexão com o serviço real
    _activateSimulationMode() {
        console.log("Ativando modo de simulação para testes");
        this.simulationMode = true;
        this.isConnected = true;
        this.deviceInfo = {
            name: "ZK4500 (Simulação)",
            serial: "SIM12345678",
            version: "1.0.0",
            status: "simulation"
        };
        
        if (this.callbacks.onConnect) {
            this.callbacks.onConnect();
        }
    }

    // Desconecta do serviço local
    disconnect() {
        if (this.simulationMode) {
            this.simulationMode = false;
            this.isConnected = false;
            this.deviceInfo = null;
            console.log("Desconectado do modo de simulação");
            
            if (this.callbacks.onDisconnect) {
                this.callbacks.onDisconnect({code: 1000, reason: "Simulação encerrada"});
            }
            return;
        }
        
        if (this.socket && this.isConnected) {
            this.socket.close();
            this.isConnected = false;
            this.deviceInfo = null;
            console.log("Desconectado do serviço de biometria");
        }
    }

    // Inicia a captura de uma impressão digital
    async capturarDigital(dedoIndex) {
        if (!this.isConnected) {
            throw new Error("Não conectado ao serviço de biometria");
        }
        
        // Se estiver em modo de simulação, simula a captura
        if (this.simulationMode) {
            return this._simulateCapture(dedoIndex);
        }
        
        return new Promise((resolve, reject) => {
            // Registra callbacks temporários para esta captura
            const captureTimeout = setTimeout(() => {
                reject(new Error("Timeout ao capturar impressão digital"));
            }, 30000); // 30 segundos para timeout
            
            const originalCaptureCallback = this.callbacks.onCapture;
            
            this.callbacks.onCapture = (data) => {
                clearTimeout(captureTimeout);
                this.callbacks.onCapture = originalCaptureCallback;
                
                if (data.error) {
                    reject(new Error(data.error));
                } else {
                    resolve(data);
                }
            };
            
            // Envia comando para iniciar captura
            this._sendCommand("CAPTURE", { dedoIndex });
        });
    }

    // Simula a captura de uma impressão digital (para testes sem hardware)
    _simulateCapture(dedoIndex) {
        return new Promise((resolve) => {
            console.log(`Simulando captura do dedo ${dedoIndex}`);
            
            // Simula um delay de captura
            setTimeout(() => {
                // Gera um template aleatório para simulação
                const template = this._generateRandomTemplate();
                const quality = Math.floor(Math.random() * 30) + 70; // Qualidade entre 70% e 99%
                
                const result = {
                    success: true,
                    template: template,
                    image: null, // Não simulamos imagem
                    quality: quality,
                    timestamp: new Date().toISOString(),
                    dedoIndex: dedoIndex
                };
                
                // Notifica o callback de captura
                if (this.callbacks.onCapture) {
                    this.callbacks.onCapture(result);
                }
                
                resolve(result);
            }, 2000); // Simula 2 segundos de captura
        });
    }

    // Gera um template aleatório para simulação
    _generateRandomTemplate() {
        const length = 512;
        let result = '';
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return result;
    }

    // Verifica a qualidade da impressão digital
    async verificarQualidade(templateData) {
        if (!this.isConnected) {
            throw new Error("Não conectado ao serviço de biometria");
        }
        
        // Se estiver em modo de simulação, simula a verificação
        if (this.simulationMode) {
            return Math.floor(Math.random() * 30) + 70; // Qualidade entre 70% e 99%
        }
        
        return new Promise((resolve, reject) => {
            const originalStatusCallback = this.callbacks.onStatus;
            
            this.callbacks.onStatus = (data) => {
                this.callbacks.onStatus = originalStatusCallback;
                
                if (data.error) {
                    reject(new Error(data.error));
                } else {
                    resolve(data.qualidade);
                }
            };
            
            // Envia comando para verificar qualidade
            this._sendCommand("CHECK_QUALITY", { template: templateData });
        });
    }

    // Verifica se duas impressões digitais são da mesma pessoa
    async compararDigitais(template1, template2) {
        if (!this.isConnected) {
            throw new Error("Não conectado ao serviço de biometria");
        }
        
        // Se estiver em modo de simulação, simula a comparação
        if (this.simulationMode) {
            // Em modo de simulação, considera match se os primeiros 5 caracteres forem iguais
            const match = template1.substring(0, 5) === template2.substring(0, 5);
            return match ? Math.floor(Math.random() * 30) + 70 : Math.floor(Math.random() * 30) + 20;
        }
        
        return new Promise((resolve, reject) => {
            const originalStatusCallback = this.callbacks.onStatus;
            
            this.callbacks.onStatus = (data) => {
                this.callbacks.onStatus = originalStatusCallback;
                
                if (data.error) {
                    reject(new Error(data.error));
                } else {
                    resolve(data.match);
                }
            };
            
            // Envia comando para comparar templates
            this._sendCommand("MATCH", { template1, template2 });
        });
    }

    // Registra callbacks para eventos
    on(event, callback) {
        if (this.callbacks.hasOwnProperty(event)) {
            this.callbacks[event] = callback;
        } else {
            console.error(`Evento desconhecido: ${event}`);
        }
    }

    // Métodos privados
    _sendCommand(command, params = {}) {
        if (this.simulationMode) {
            console.log(`Comando simulado: ${command}`, params);
            return true;
        }
        
        if (!this.isConnected || !this.socket) {
            console.error("Não é possível enviar comando: não conectado");
            return false;
        }
        
        const message = JSON.stringify({
            command,
            params
        });
        
        try {
            this.socket.send(message);
            return true;
        } catch (error) {
            console.error("Erro ao enviar comando:", error);
            return false;
        }
    }

    _handleMessage(data) {
        try {
            const message = JSON.parse(data);
            
            switch (message.type) {
                case "DEVICE_INFO":
                    this.deviceInfo = message.data;
                    console.log("Informações do dispositivo:", this.deviceInfo);
                    break;
                    
                case "CAPTURE_RESULT":
                    if (this.callbacks.onCapture) {
                        this.callbacks.onCapture(message.data);
                    }
                    break;
                    
                case "STATUS":
                    if (this.callbacks.onStatus) {
                        this.callbacks.onStatus(message.data);
                    }
                    break;
                    
                case "READER_STATUS":
                    if (message.data.connected) {
                        if (this.callbacks.onConnect) {
                            this.callbacks.onConnect();
                        }
                    } else {
                        if (this.callbacks.onDisconnect) {
                            this.callbacks.onDisconnect();
                        }
                    }
                    break;
                    
                case "ERROR":
                    console.error("Erro do serviço de biometria:", message.data);
                    if (this.callbacks.onError) {
                        this.callbacks.onError(new Error(message.data.message));
                    }
                    break;
                    
                default:
                    console.log("Mensagem desconhecida do serviço de biometria:", message);
            }
        } catch (error) {
            console.error("Erro ao processar mensagem do serviço de biometria:", error);
        }
    }

    _attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log("Número máximo de tentativas de reconexão atingido");
            this._activateSimulationMode();
            return;
        }
        
        this.reconnectAttempts++;
        console.log(`Tentativa de reconexão ${this.reconnectAttempts}/${this.maxReconnectAttempts} em ${this.reconnectInterval}ms`);
        
        setTimeout(() => {
            console.log("Tentando reconectar...");
            this.connect().catch(error => {
                console.error("Falha na tentativa de reconexão:", error);
            });
        }, this.reconnectInterval);
    }
}

// Exporta a classe para uso global
window.BiometriaService = BiometriaService;
