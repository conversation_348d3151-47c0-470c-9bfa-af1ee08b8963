#!/usr/bin/env python3
"""
Script para debugar o erro que ocorre ao salvar edição sem alterações.
"""

import sys
import os
import traceback
import pymysql

# Adicionar o diretório do projeto ao path
sys.path.append('/var/www/controle-ponto')

try:
    from app_funcionarios import _extrair_dados_formulario, _validar_dados_funcionario, _processar_formulario_funcionario
    from utils.helpers import FormValidator
    from flask import Flask, request
    
    print("✅ Imports realizados com sucesso")
    
    # Criar uma aplicação Flask de teste
    app = Flask(__name__)
    
    def buscar_funcionario_real():
        """
        Busca dados reais de um funcionário para simular edição.
        """
        try:
            connection = pymysql.connect(
                host='localhost',
                user='cavalcrod',
                password='200381',
                database='controle_ponto',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT * FROM funcionarios WHERE id = 1")
                funcionario = cursor.fetchone()
                
                if funcionario:
                    print(f"✅ Funcionário encontrado: {funcionario['nome_completo']}")
                    return funcionario
                else:
                    print("❌ Funcionário não encontrado")
                    return None
                    
        except Exception as e:
            print(f"❌ Erro ao buscar funcionário: {e}")
            return None
        finally:
            if 'connection' in locals():
                connection.close()
    
    def simular_edicao_sem_alteracao():
        """
        Simula exatamente o que acontece quando você clica em "Salvar" sem alterar nada.
        """
        print("\n🔍 SIMULANDO EDIÇÃO SEM ALTERAÇÃO")
        print("=" * 60)
        
        funcionario = buscar_funcionario_real()
        if not funcionario:
            return False
        
        # Converter dados do banco para formato de formulário (como seria enviado pelo navegador)
        form_data = {}
        
        # Mapear todos os campos do funcionário
        campos_mapeamento = {
            'nome_completo': funcionario.get('nome_completo', ''),
            'cpf': funcionario.get('cpf', ''),
            'rg': funcionario.get('rg', ''),
            'data_nascimento': funcionario.get('data_nascimento', ''),
            'sexo': funcionario.get('sexo', ''),
            'estado_civil': funcionario.get('estado_civil', ''),
            'nacionalidade': funcionario.get('nacionalidade', ''),
            'ctps_numero': funcionario.get('ctps_numero', ''),
            'ctps_serie_uf': funcionario.get('ctps_serie_uf', ''),
            'pis_pasep': funcionario.get('pis_pasep', ''),
            'endereco_cep': funcionario.get('endereco_cep', ''),
            'endereco_estado': funcionario.get('endereco_estado', ''),
            'endereco_rua': funcionario.get('endereco_rua', ''),
            'endereco_bairro': funcionario.get('endereco_bairro', ''),
            'endereco_cidade': funcionario.get('endereco_cidade', ''),
            'telefone1': funcionario.get('telefone1', ''),
            'telefone2': funcionario.get('telefone2', ''),
            'email': funcionario.get('email', ''),
            'empresa_id': str(funcionario.get('empresa_id', '')),
            'cargo': funcionario.get('cargo', ''),
            'setor_obra': funcionario.get('setor_obra', ''),
            'matricula_empresa': funcionario.get('matricula_empresa', ''),
            'data_admissao': funcionario.get('data_admissao', ''),
            'tipo_contrato': funcionario.get('tipo_contrato', ''),
            'nivel_acesso': funcionario.get('nivel_acesso', ''),
            'turno': funcionario.get('turno', ''),
            'tolerancia_ponto': str(funcionario.get('tolerancia_ponto', 10)),
            'status_cadastro': funcionario.get('status_cadastro', ''),
            'salario_base': str(funcionario.get('salario_base', '') or ''),
            'horas_trabalho_obrigatorias': str(funcionario.get('horas_trabalho_obrigatorias', 8.0))
        }
        
        # Converter datas para string se necessário
        for campo, valor in campos_mapeamento.items():
            if valor and hasattr(valor, 'strftime'):
                form_data[campo] = valor.strftime('%Y-%m-%d')
            else:
                form_data[campo] = str(valor) if valor is not None else ''
        
        print("📋 Dados do formulário (primeiros 10 campos):")
        for i, (key, value) in enumerate(form_data.items()):
            if i < 10:
                print(f"  {key}: '{value}'")
            elif i == 10:
                print("  ... (mais campos)")
                break
        
        # Simular requisição POST
        with app.test_request_context('/', method='POST', data=form_data):
            try:
                print("\n🔍 Extraindo dados do formulário...")
                dados_extraidos = _extrair_dados_formulario()
                
                print("✅ Dados extraídos com sucesso")
                
                print("\n🔍 Validando dados...")
                validator = FormValidator()
                _validar_dados_funcionario(dados_extraidos, validator)
                
                if validator.has_errors():
                    print("❌ ERROS ENCONTRADOS (não deveria haver!):")
                    errors_dict = validator.get_errors()
                    
                    # Converter para lista como na correção
                    errors_list = []
                    for field, field_errors in errors_dict.items():
                        for error_msg in field_errors:
                            field_name = field.replace('_', ' ').title()
                            errors_list.append(f"{field_name}: {error_msg}")
                    
                    print(f"📊 Total de erros: {len(errors_list)}")
                    
                    # Mostrar apenas os primeiros 5 erros
                    for i, error in enumerate(errors_list[:5], 1):
                        print(f"  {i}. {error}")
                    
                    if len(errors_list) > 5:
                        print(f"  ... e mais {len(errors_list) - 5} erros")
                    
                    # Verificar erros específicos problemáticos
                    erros_criticos = [e for e in errors_list if any(palavra in e.lower() for palavra in ['id', 'obrigatório', 'inválido'])]
                    if erros_criticos:
                        print(f"\n⚠️ Erros críticos ({len(erros_criticos)}):")
                        for erro in erros_criticos[:3]:
                            print(f"  - {erro}")
                    
                    return False
                else:
                    print("✅ Validação passou sem erros!")
                    print("🎯 Edição sem alteração funcionaria corretamente")
                    return True
                    
            except Exception as e:
                print(f"❌ ERRO durante processamento: {e}")
                print(f"Traceback: {traceback.format_exc()}")
                return False
    
    def verificar_dados_obrigatorios_ausentes():
        """
        Verifica se há campos obrigatórios que podem estar ausentes.
        """
        print("\n🔍 VERIFICANDO CAMPOS OBRIGATÓRIOS")
        print("=" * 50)
        
        funcionario = buscar_funcionario_real()
        if not funcionario:
            return
        
        from app_funcionarios import REQUIRED_FIELDS
        
        print(f"📋 Campos obrigatórios definidos: {len(REQUIRED_FIELDS)}")
        
        campos_ausentes = []
        campos_vazios = []
        
        for campo in REQUIRED_FIELDS:
            valor = funcionario.get(campo)
            if valor is None:
                campos_ausentes.append(campo)
            elif isinstance(valor, str) and not valor.strip():
                campos_vazios.append(campo)
        
        if campos_ausentes:
            print(f"❌ Campos ausentes ({len(campos_ausentes)}):")
            for campo in campos_ausentes[:5]:
                print(f"  - {campo}")
        
        if campos_vazios:
            print(f"⚠️ Campos vazios ({len(campos_vazios)}):")
            for campo in campos_vazios[:5]:
                print(f"  - {campo}")
        
        if not campos_ausentes and not campos_vazios:
            print("✅ Todos os campos obrigatórios estão preenchidos")
    
    if __name__ == "__main__":
        print("🐛 DEBUG: EDIÇÃO SEM ALTERAÇÃO")
        print("=" * 60)
        
        verificar_dados_obrigatorios_ausentes()
        sucesso = simular_edicao_sem_alteracao()
        
        print("\n" + "=" * 60)
        print("📊 DIAGNÓSTICO")
        print("=" * 60)
        
        if sucesso:
            print("✅ SIMULAÇÃO PASSOU!")
            print("🤔 O erro pode estar em outro lugar:")
            print("  - Processamento após validação")
            print("  - Interação com banco de dados")
            print("  - Renderização do template")
        else:
            print("❌ SIMULAÇÃO REPRODUZIU O ERRO!")
            print("🎯 Problema identificado na validação")
            print("📋 Verificar campos obrigatórios ausentes/vazios")

except ImportError as e:
    print(f"❌ Erro de import: {e}")
except Exception as e:
    print(f"❌ Erro inesperado: {e}")
    print(f"Traceback: {traceback.format_exc()}")
