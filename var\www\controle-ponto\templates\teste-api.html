<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Teste Biometria</title>
    <style>
        body { font-family: Arial; padding: 20px; }
        .log { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>Teste do Leitor Biométrico</h1>
    
    <div>
        <button onclick="testarInicializacao()">1. Testar Inicialização</button>
        <button onclick="testarStatus()">2. Testar Status</button>
        <button onclick="testarCaptura()">3. Testar Captura</button>
        <button onclick="testarFinalizacao()">4. Testar Finalização</button>
    </div>

    <div id="log" class="log">Log de operações aparecerá aqui...</div>

    <script>
        const BASE_URL = 'http://localhost:8081/biometria/api/biometria';
        const log = document.getElementById('log');

        function addLog(message) {
            const time = new Date().toLocaleTimeString();
            log.innerHTML = `[${time}] ${message}<br>` + log.innerHTML;
        }

        async function fazerRequisicao(endpoint) {
            try {
                addLog(`Fazendo requisição para ${endpoint}...`);
                const response = await fetch(`${BASE_URL}${endpoint}`);
                const data = await response.json();
                addLog(`Resposta: ${JSON.stringify(data, null, 2)}`);
                return data;
            } catch (error) {
                addLog(`ERRO: ${error.message}`);
                throw error;
            }
        }

        async function testarInicializacao() {
            try {
                const data = await fazerRequisicao('/inicializar');
                if (data.sucesso) {
                    addLog('✅ Inicialização bem sucedida!');
                } else {
                    addLog('❌ Falha na inicialização');
                }
            } catch (error) {
                addLog('❌ Erro ao inicializar');
            }
        }

        async function testarStatus() {
            try {
                const data = await fazerRequisicao('/status');
                if (data.sucesso) {
                    addLog('✅ Status verificado com sucesso!');
                } else {
                    addLog('❌ Falha ao verificar status');
                }
            } catch (error) {
                addLog('❌ Erro ao verificar status');
            }
        }

        async function testarCaptura() {
            try {
                const data = await fazerRequisicao('/capturar');
                if (data.sucesso) {
                    addLog('✅ Digital capturada com sucesso!');
                    // Mostra a imagem se disponível
                    if (data.imageBase64) {
                        const img = document.createElement('img');
                        img.src = `data:image/png;base64,${data.imageBase64}`;
                        img.style.maxWidth = '200px';
                        img.style.border = '1px solid #ccc';
                        img.style.margin = '10px 0';
                        log.insertBefore(img, log.firstChild);
                    }
                } else {
                    addLog('❌ Falha na captura');
                }
            } catch (error) {
                addLog('❌ Erro ao capturar');
            }
        }

        async function testarFinalizacao() {
            try {
                const data = await fazerRequisicao('/finalizar');
                if (data.sucesso) {
                    addLog('✅ Finalização bem sucedida!');
                } else {
                    addLog('❌ Falha na finalização');
                }
            } catch (error) {
                addLog('❌ Erro ao finalizar');
            }
        }
    </script>
</body>
</html> 