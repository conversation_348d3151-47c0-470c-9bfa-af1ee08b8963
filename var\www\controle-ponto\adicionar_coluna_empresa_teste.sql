-- Script para adicionar a coluna empresa_teste na tabela empresas
-- Data: 03/07/2025
-- Autor: Claude 3.7 Sonnet
-- Descrição: Adiciona coluna para identificar empresas de teste que podem ser excluídas fisicamente

-- Verificar se a coluna já existe
SET @exists = 0;
SELECT COUNT(*) INTO @exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'empresas' AND COLUMN_NAME = 'empresa_teste';

-- Adicionar a coluna se não existir
SET @query = IF(@exists = 0, 
    'ALTER TABLE empresas ADD COLUMN empresa_teste BOOLEAN DEFAULT FALSE AFTER ativa',
    'SELECT "Coluna empresa_teste já existe na tabela empresas" AS message');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- <PERSON><PERSON><PERSON> tabel<PERSON> de log de exclusão se não existir
CREATE TABLE IF NOT EXISTS log_exclusao_empresas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    empresa_id INT NOT NULL,
    razao_social VARCHAR(200) NOT NULL,
    cnpj VARCHAR(18) NOT NULL,
    usuario_id INT NOT NULL,
    data_exclusao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    motivo TEXT
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Mostrar estrutura atualizada
SHOW CREATE TABLE empresas;
SHOW CREATE TABLE log_exclusao_empresas; 