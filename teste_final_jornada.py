#!/usr/bin/env python3
"""
Teste final para verificar se a jornada é preservada durante edição
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def teste_preservacao_jornada():
    """Teste final de preservação de jornada"""
    print("🧪 TESTE FINAL: PRESERVAÇÃO DE JORNADA DURANTE EDIÇÃO")
    print("=" * 70)
    
    try:
        # 1. Estado inicial do <PERSON>
        print("📋 1. ESTADO INICIAL DO RICHARDSON:")
        funcionario_antes = DatabaseManager.execute_query(
            "SELECT id, nome_completo, horario_trabalho_id, jornada_seg_qui_entrada, jornada_seg_qui_saida FROM funcionarios WHERE id = 1"
        )[0]
        
        print(f"   horario_trabalho_id: {funcionario_antes['horario_trabalho_id']}")
        print(f"   jornada_seg_qui_entrada: {funcionario_antes['jornada_seg_qui_entrada']}")
        print(f"   jornada_seg_qui_saida: {funcionario_antes['jornada_seg_qui_saida']}")
        
        # 2. Vamos alterar manualmente a jornada do Richardson para algo diferente
        print(f"\n🔧 2. ALTERANDO JORNADA DO RICHARDSON PARA TESTE:")
        print("   Definindo horário personalizado: 09:00 - 18:00")
        
        sql_personalizar = """
        UPDATE funcionarios SET 
            jornada_seg_qui_entrada = '09:00:00',
            jornada_seg_qui_saida = '18:00:00',
            jornada_sex_entrada = '09:00:00',
            jornada_sex_saida = '18:00:00'
        WHERE id = 1
        """
        DatabaseManager.execute_query(sql_personalizar, fetch_all=False)
        print("   ✅ Jornada personalizada aplicada")
        
        # 3. Verificar se foi alterado
        funcionario_personalizado = DatabaseManager.execute_query(
            "SELECT id, horario_trabalho_id, jornada_seg_qui_entrada, jornada_seg_qui_saida FROM funcionarios WHERE id = 1"
        )[0]
        
        print(f"\n📋 3. ESTADO APÓS PERSONALIZAÇÃO:")
        print(f"   horario_trabalho_id: {funcionario_personalizado['horario_trabalho_id']}")
        print(f"   jornada_seg_qui_entrada: {funcionario_personalizado['jornada_seg_qui_entrada']}")
        print(f"   jornada_seg_qui_saida: {funcionario_personalizado['jornada_seg_qui_saida']}")
        
        # 4. Simular edição usando a nova lógica (sem campos de jornada no SQL)
        print(f"\n🔄 4. SIMULANDO EDIÇÃO COM NOVA LÓGICA:")
        print("   Atualizando apenas dados pessoais (sem tocar na jornada)")
        
        # SQL da nova lógica (sem campos de jornada)
        sql_nova_logica = """
        UPDATE funcionarios SET
            nome_completo=%s, cpf=%s, rg=%s, data_nascimento=%s, sexo=%s, estado_civil=%s, nacionalidade=%s,
            ctps_numero=%s, ctps_serie_uf=%s, pis_pasep=%s,
            endereco_rua=%s, endereco_bairro=%s, endereco_cidade=%s, endereco_cep=%s, endereco_estado=%s,
            telefone1=%s, telefone2=%s, email=%s,
            cargo=%s, setor_obra=%s, matricula_empresa=%s, data_admissao=%s, tipo_contrato=%s,
            nivel_acesso=%s, turno=%s, tolerancia_ponto=%s, banco_horas=%s, hora_extra=%s, status_cadastro=%s,
            empresa_id=%s, digital_dedo1=%s, digital_dedo2=%s, foto_3x4=%s
        WHERE id=%s
        """
        
        params = (
            'RICHARDSON CARDOSO RODRIGUES EDITADO',  # Mudança no nome para testar
            '123.456.789-00', '1234567', '1990-01-01', 'M', 'SOLTEIRO', 'BRASILEIRO',
            '1234567', '001/AC', '123.45678.90-1',
            'RUA TESTE EDITADA', 'BAIRRO TESTE', 'MANAUS', '69000-000', 'AM',
            '(92) 99999-9999', '', '<EMAIL>',
            'DESENVOLVEDOR SENIOR', 'TI', '001', '2024-01-01', 'CLT',
            'FUNCIONARIO', 'DIURNO', 15, 1, 1, 'ATIVO',
            11, '', '', None,
            1  # ID do funcionário
        )
        
        DatabaseManager.execute_query(sql_nova_logica, params, fetch_all=False)
        print("   ✅ Edição simulada executada")
        
        # 5. Verificar se a jornada foi preservada
        funcionario_apos_edicao = DatabaseManager.execute_query(
            "SELECT id, nome_completo, horario_trabalho_id, jornada_seg_qui_entrada, jornada_seg_qui_saida FROM funcionarios WHERE id = 1"
        )[0]
        
        print(f"\n📋 5. ESTADO APÓS EDIÇÃO:")
        print(f"   nome_completo: {funcionario_apos_edicao['nome_completo']}")
        print(f"   horario_trabalho_id: {funcionario_apos_edicao['horario_trabalho_id']}")
        print(f"   jornada_seg_qui_entrada: {funcionario_apos_edicao['jornada_seg_qui_entrada']}")
        print(f"   jornada_seg_qui_saida: {funcionario_apos_edicao['jornada_seg_qui_saida']}")
        
        # 6. Análise do resultado
        print(f"\n🔍 6. ANÁLISE DO RESULTADO:")
        
        jornada_preservada = (
            funcionario_personalizado['jornada_seg_qui_entrada'] == funcionario_apos_edicao['jornada_seg_qui_entrada'] and
            funcionario_personalizado['jornada_seg_qui_saida'] == funcionario_apos_edicao['jornada_seg_qui_saida']
        )
        
        nome_alterado = funcionario_apos_edicao['nome_completo'] == 'RICHARDSON CARDOSO RODRIGUES EDITADO'
        
        if jornada_preservada and nome_alterado:
            print("   ✅ SUCESSO! Jornada foi preservada e outros dados foram atualizados")
            print("   ✅ A nova lógica está funcionando corretamente!")
        elif jornada_preservada and not nome_alterado:
            print("   ⚠️ Jornada preservada, mas outros dados não foram atualizados")
        elif not jornada_preservada and nome_alterado:
            print("   ❌ FALHA! Outros dados foram atualizados, mas jornada foi sobrescrita")
        else:
            print("   ❌ FALHA TOTAL! Nem jornada foi preservada nem outros dados atualizados")
            
        # 7. Restaurar estado original
        print(f"\n🔄 7. RESTAURANDO ESTADO ORIGINAL:")
        sql_restaurar = """
        UPDATE funcionarios SET 
            nome_completo = 'RICHARDSON CARDOSO RODRIGUES',
            jornada_seg_qui_entrada = %s,
            jornada_seg_qui_saida = %s
        WHERE id = 1
        """
        DatabaseManager.execute_query(sql_restaurar, (
            funcionario_antes['jornada_seg_qui_entrada'],
            funcionario_antes['jornada_seg_qui_saida']
        ), fetch_all=False)
        print("   ✅ Estado original restaurado")
        
        return jornada_preservada and nome_alterado
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    sucesso = teste_preservacao_jornada()
    print(f"\n{'🎉 TESTE PASSOU!' if sucesso else '❌ TESTE FALHOU!'}")
