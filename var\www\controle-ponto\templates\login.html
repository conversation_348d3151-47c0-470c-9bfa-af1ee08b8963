<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <PERSON>e <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-wrapper {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px;
            margin: 20px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #495057;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .login-header p {
            color: #6c757d;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: #f8f9fa;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4fbdba;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
        }
        
        .btn-login {
            width: 100%;
            background: #4fbdba;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 10px;
        }
        
        .btn-login:hover {
            background: #3da8a6;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(79, 189, 186, 0.3);
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 12px 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-size: 14px;
        }
        
        .system-info {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 12px;
        }
        
        @media (max-width: 480px) {
            .login-wrapper {
                margin: 10px;
                padding: 30px 20px;
            }
        }
        
        /* Animação de entrada */
        .login-wrapper {
            animation: slideUp 0.3s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="login-wrapper">
        <div class="login-header">
            <h1>🕐 Controle de Ponto</h1>
            <p>Faça login para acessar o sistema</p>
        </div>
        
        <form method="POST">
            <div class="form-group">
                <label for="usuario">Usuário</label>
                <input type="text" 
                       id="usuario" 
                       name="usuario" 
                       required 
                       autocomplete="username"
                       placeholder="Digite seu usuário">
            </div>
            
            <div class="form-group">
                <label for="senha">Senha</label>
                <input type="password" 
                       id="senha" 
                       name="senha" 
                       required 
                       autocomplete="current-password"
                       placeholder="Digite sua senha">
            </div>
            
            <button type="submit" class="btn-login">
                Entrar no Sistema
            </button>
            
            {% if erro %}
            <div class="error-message">
                {{ erro }}
            </div>
            {% endif %}
        </form>
        
        <div class="system-info">
            Sistema de Controle de Ponto v2.0
        </div>
    </div>
    
    <script>
        // Foco automático no campo usuário
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('usuario').focus();
        });
        
        // Enter para submeter formulário
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('form').submit();
            }
        });
    </script>
</body>
</html>
