#!/usr/bin/env python3
"""
Investigar problemas críticos: EPIs não aparecem e jornada sendo sobrescrita
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def investigar_problemas():
    """Investigar problemas de EPI e jornada"""
    try:
        from utils.database import DatabaseManager
        from app_funcionarios import _buscar_epis_funcionario
        import logging
        
        logging.basicConfig(level=logging.INFO)
        
        print("🔍 INVESTIGANDO PROBLEMAS CRÍTICOS")
        print("=" * 60)
        
        # Buscar funcionário da Ainexus para testar
        funcionario = DatabaseManager.execute_query("""
            SELECT f.id, f.nome_completo, f.empresa_id, f.horario_trabalho_id,
                   f.jornada_seg_qui_entrada, f.jornada_seg_qui_saida,
                   e.nome_fantasia as empresa_nome
            FROM funcionarios f
            JOIN empresas e ON f.empresa_id = e.id
            WHERE e.nome_fantasia LIKE %s
            LIMIT 1
        """, ('%Ainexus%',), fetch_one=True)
        
        if not funcionario:
            print("❌ Funcionário da Ainexus não encontrado")
            return False
            
        func_id = funcionario['id']
        print(f"👤 Funcionário: {funcionario['nome_completo']} (ID: {func_id})")
        print(f"🏢 Empresa: {funcionario['empresa_nome']} (ID: {funcionario['empresa_id']})")
        print(f"⏰ Horário trabalho ID: {funcionario['horario_trabalho_id']}")
        print(f"📅 Jornada atual: {funcionario['jornada_seg_qui_entrada']} - {funcionario['jornada_seg_qui_saida']}")
        
        # PROBLEMA 1: Verificar EPIs no banco
        print("\n🦺 PROBLEMA 1: VERIFICANDO EPIs NO BANCO:")
        print("-" * 50)
        epis_banco = DatabaseManager.execute_query("""
            SELECT id, epi_nome, epi_ca, epi_data_entrega, epi_data_validade, epi_observacoes
            FROM epis 
            WHERE funcionario_id = %s
            ORDER BY id DESC
        """, (func_id,))
        
        if epis_banco:
            print(f"✅ {len(epis_banco)} EPIs encontrados no banco:")
            for epi in epis_banco:
                print(f"   • ID {epi['id']}: {epi['epi_nome']} - CA: {epi['epi_ca']}")
                print(f"     Entrega: {epi['epi_data_entrega']} | Validade: {epi['epi_data_validade']}")
        else:
            print("❌ Nenhum EPI encontrado no banco!")
        
        # Testar função _buscar_epis_funcionario
        print("\n🔍 TESTANDO FUNÇÃO _buscar_epis_funcionario:")
        print("-" * 50)
        epis_funcao = _buscar_epis_funcionario(func_id)
        
        if epis_funcao:
            print(f"✅ Função retornou {len(epis_funcao)} EPIs:")
            for epi in epis_funcao:
                print(f"   • {epi.get('epi_nome')} (ID: {epi.get('id')})")
        else:
            print("❌ Função retornou lista vazia!")
        
        # PROBLEMA 2: Verificar lógica de jornada
        print("\n⏰ PROBLEMA 2: VERIFICANDO LÓGICA DE JORNADA:")
        print("-" * 50)
        
        # Simular dados de edição (como se viesse do formulário)
        dados_simulados = {
            'empresa_id': funcionario['empresa_id'],
            'horario_trabalho_id': funcionario['horario_trabalho_id'],  # JÁ TEM JORNADA!
            'nome_completo': funcionario['nome_completo']
        }
        
        print(f"📋 Dados simulados (como se viesse do formulário):")
        print(f"   empresa_id: {dados_simulados['empresa_id']}")
        print(f"   horario_trabalho_id: {dados_simulados['horario_trabalho_id']}")
        
        # Testar a condição problemática
        empresa_id = dados_simulados.get('empresa_id')
        horario_trabalho_id = dados_simulados.get('horario_trabalho_id')
        
        print(f"\n🔍 TESTANDO CONDIÇÃO PROBLEMÁTICA:")
        print(f"   empresa_id: {empresa_id} (bool: {bool(empresa_id)})")
        print(f"   horario_trabalho_id: {horario_trabalho_id} (bool: {bool(horario_trabalho_id)})")
        print(f"   not data.get('horario_trabalho_id'): {not dados_simulados.get('horario_trabalho_id')}")
        
        condicao_problematica = empresa_id and not dados_simulados.get('horario_trabalho_id')
        print(f"   CONDIÇÃO FINAL: {condicao_problematica}")
        
        if condicao_problematica:
            print("🚨 PROBLEMA CONFIRMADO: A condição vai aplicar jornada automática!")
            print("   Isso vai SOBRESCREVER a jornada existente do funcionário!")
        else:
            print("✅ Condição está correta - não vai sobrescrever")
        
        # Verificar se horario_trabalho_id está sendo enviado no formulário
        print("\n🔍 INVESTIGANDO FORMULÁRIO:")
        print("-" * 40)
        
        # Simular extração de dados do formulário (sem horario_trabalho_id)
        dados_formulario_sem_jornada = {
            'empresa_id': funcionario['empresa_id'],
            'nome_completo': funcionario['nome_completo']
            # NOTA: horario_trabalho_id NÃO está sendo enviado!
        }
        
        print("📋 Dados como provavelmente vêm do formulário:")
        for key, value in dados_formulario_sem_jornada.items():
            print(f"   {key}: {value}")
        
        print(f"\n🚨 PROBLEMA IDENTIFICADO:")
        print(f"   O formulário NÃO está enviando 'horario_trabalho_id'")
        print(f"   Por isso a condição 'not data.get('horario_trabalho_id')' é sempre True")
        print(f"   E a jornada é sempre sobrescrita!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante investigação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    investigar_problemas()
