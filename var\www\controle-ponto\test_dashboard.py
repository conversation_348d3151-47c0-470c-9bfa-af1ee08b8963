#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste isolado do dashboard de controle de período
Sistema: RLPONTO-WEB v1.0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database import get_db_connection
from pymysql.cursors import DictCursor
from datetime import datetime, date, timedelta
import logging

def test_calcular_periodo():
    """Testa função de cálculo de período"""
    print("🔍 Testando cálculo de período...")
    
    try:
        hoje = date.today()
        
        # Calcular período 21-20
        if hoje.day >= 21:
            inicio = date(hoje.year, hoje.month, 21)
            if hoje.month == 12:
                fim = date(hoje.year + 1, 1, 20)
            else:
                fim = date(hoje.year, hoje.month + 1, 20)
        else:
            if hoje.month == 1:
                inicio = date(hoje.year - 1, 12, 21)
            else:
                inicio = date(hoje.year, hoje.month - 1, 21)
            fim = date(hoje.year, hoje.month, 20)
        
        # Calcular dias
        dias_totais = (fim - inicio).days + 1
        dias_decorridos = (hoje - inicio).days + 1 if hoje >= inicio else 0
        dias_restantes = (fim - hoje).days if hoje <= fim else 0
        percentual_concluido = min(100, max(0, (dias_decorridos / dias_totais) * 100))
        
        periodo = {
            'inicio': inicio,
            'fim': fim,
            'dias_totais': dias_totais,
            'dias_decorridos': dias_decorridos,
            'dias_restantes': dias_restantes,
            'percentual_concluido': round(percentual_concluido, 1)
        }
        
        print(f"✅ Período calculado: {inicio} a {fim}")
        print(f"   Dias totais: {dias_totais}")
        print(f"   Dias decorridos: {dias_decorridos}")
        print(f"   Dias restantes: {dias_restantes}")
        print(f"   Percentual: {percentual_concluido}%")
        
        return periodo
        
    except Exception as e:
        print(f"❌ Erro no cálculo de período: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_database_queries():
    """Testa consultas básicas ao banco"""
    print("\n🔍 Testando consultas ao banco...")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Teste 1: Contar funcionários ativos
        cursor.execute("SELECT COUNT(*) as total FROM funcionarios WHERE ativo = 1")
        funcionarios = cursor.fetchone()
        print(f"✅ Funcionários ativos: {funcionarios['total']}")
        
        # Teste 2: Verificar tabela de registros de ponto
        cursor.execute("SHOW TABLES LIKE 'registros_ponto'")
        tabela_existe = cursor.fetchone()
        if tabela_existe:
            print("✅ Tabela registros_ponto existe")
            
            # Teste 3: Contar registros recentes
            cursor.execute("""
                SELECT COUNT(*) as total 
                FROM registros_ponto 
                WHERE data_registro >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            """)
            registros = cursor.fetchone()
            print(f"✅ Registros últimos 30 dias: {registros['total']}")
        else:
            print("⚠️ Tabela registros_ponto não existe")
        
        # Teste 4: Verificar estrutura de tabelas
        cursor.execute("SHOW TABLES")
        tabelas = cursor.fetchall()
        print(f"✅ Tabelas no banco: {len(tabelas)}")
        for tabela in tabelas[:5]:  # Mostrar apenas as primeiras 5
            print(f"   - {list(tabela.values())[0]}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro nas consultas: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_estatisticas():
    """Testa geração de estatísticas"""
    print("\n🔍 Testando estatísticas...")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Estatísticas básicas
        estatisticas = {
            'funcionarios_ativos': 0,
            'dias_registrados': 0,
            'horas_extras_formatadas': '0h 0min',
            'atrasos_formatados': '0h 0min'
        }
        
        # Contar funcionários ativos
        cursor.execute("SELECT COUNT(*) as total FROM funcionarios WHERE ativo = 1")
        result = cursor.fetchone()
        estatisticas['funcionarios_ativos'] = result['total'] if result else 0
        
        print(f"✅ Estatísticas geradas:")
        print(f"   Funcionários ativos: {estatisticas['funcionarios_ativos']}")
        print(f"   Horas extras: {estatisticas['horas_extras_formatadas']}")
        print(f"   Atrasos: {estatisticas['atrasos_formatados']}")
        
        cursor.close()
        conn.close()
        return estatisticas
        
    except Exception as e:
        print(f"❌ Erro nas estatísticas: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Função principal de teste"""
    print("=" * 60)
    print("🔧 TESTE ISOLADO DO DASHBOARD")
    print("=" * 60)
    
    # Teste 1: Cálculo de período
    periodo = test_calcular_periodo()
    if not periodo:
        print("❌ Falha no teste de período")
        return
    
    # Teste 2: Consultas ao banco
    if not test_database_queries():
        print("❌ Falha no teste de banco")
        return
    
    # Teste 3: Estatísticas
    estatisticas = test_estatisticas()
    if not estatisticas:
        print("❌ Falha no teste de estatísticas")
        return
    
    print("\n" + "=" * 60)
    print("✅ TODOS OS TESTES PASSARAM!")
    print("O dashboard deveria funcionar normalmente.")
    print("=" * 60)

if __name__ == "__main__":
    main()
