<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Real do Modal - Adicionar Cliente</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .teste { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .sucesso { background-color: #d4edda; border-color: #c3e6cb; }
        .erro { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .resultado { margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 3px; }
        pre { background-color: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 TESTE REAL DO MODAL - ADICIONAR CLIENTE</h1>
    <p><strong>Este teste simula exatamente o que acontece quando o usuário clica no botão "Adicionar Cliente" no modal.</strong></p>

    <div class="teste info">
        <h3>📋 Configuração do Teste</h3>
        <p><strong>URL do Sistema:</strong> http://************:5000</p>
        <p><strong>Endpoint:</strong> /empresa-principal/clientes/adicionar</p>
        <p><strong>Método:</strong> POST (FormData)</p>
    </div>

    <div class="teste">
        <h3>🧪 TESTE 1: Duplicata Ativa (Ocrim - ID 14)</h3>
        <p>Simula tentar adicionar Ocrim que já é cliente ativo</p>
        <button onclick="testarDuplicataAtiva()">🔴 Executar Teste Duplicata</button>
        <div id="resultado1" class="resultado" style="display:none;"></div>
    </div>

    <div class="teste">
        <h3>🧪 TESTE 2: Empresa Nova (AiNexus - ID 11)</h3>
        <p>Simula adicionar empresa que não é cliente ainda</p>
        <button onclick="testarEmpresaNova()">🟢 Executar Teste Nova Empresa</button>
        <div id="resultado2" class="resultado" style="display:none;"></div>
    </div>

    <div class="teste">
        <h3>🧪 TESTE 3: Empresa Inexistente (ID 999)</h3>
        <p>Simula tentar adicionar empresa que não existe</p>
        <button onclick="testarEmpresaInexistente()">🟡 Executar Teste Inexistente</button>
        <div id="resultado3" class="resultado" style="display:none;"></div>
    </div>

    <script>
        const BASE_URL = 'http://************:5000';
        const ADICIONAR_URL = `${BASE_URL}/empresa-principal/clientes/adicionar`;

        // Função que simula exatamente o código do modal
        function adicionarClienteReal(dados, resultadoElementId) {
            console.log('🧪 Iniciando teste real via FormData...');
            
            // Criar FormData exatamente como o modal faz
            const formData = new FormData();
            Object.keys(dados).forEach(key => {
                formData.append(key, dados[key]);
            });

            console.log('📤 Enviando dados:', dados);

            // Fazer requisição exatamente como o modal faz
            fetch(ADICIONAR_URL, {
                method: 'POST',
                body: formData,
                credentials: 'include' // Incluir cookies de sessão
            })
            .then(response => {
                console.log('📥 Status da resposta:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('📊 Dados recebidos:', data);
                
                const resultadoDiv = document.getElementById(resultadoElementId);
                resultadoDiv.style.display = 'block';
                
                let html = '<h4>📊 Resultado do Teste:</h4>';
                html += `<p><strong>✅ Sucesso:</strong> ${data.success}</p>`;
                html += `<p><strong>📝 Mensagem:</strong> ${data.message}</p>`;
                html += `<p><strong>🔧 Tipo de Erro:</strong> ${data.error_type || 'N/A'}</p>`;
                html += `<p><strong>🪟 Modal Aberto:</strong> ${data.keep_modal_open || false}</p>`;
                html += '<h4>📋 Resposta Completa:</h4>';
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                // Simular comportamento do modal
                if (data.success) {
                    html += '<p class="sucesso">✅ <strong>MODAL FECHARIA</strong> e página recarregaria</p>';
                    resultadoDiv.className = 'resultado sucesso';
                } else {
                    if (data.keep_modal_open) {
                        html += '<p class="erro">🪟 <strong>MODAL PERMANECERIA ABERTO</strong> para correção</p>';
                    } else {
                        html += '<p class="erro">🚪 <strong>MODAL FECHARIA</strong> mesmo com erro</p>';
                    }
                    resultadoDiv.className = 'resultado erro';
                }
                
                resultadoDiv.innerHTML = html;
            })
            .catch(error => {
                console.error('💥 Erro na requisição:', error);
                
                const resultadoDiv = document.getElementById(resultadoElementId);
                resultadoDiv.style.display = 'block';
                resultadoDiv.className = 'resultado erro';
                resultadoDiv.innerHTML = `
                    <h4>💥 Erro na Requisição:</h4>
                    <p><strong>Erro:</strong> ${error.message}</p>
                    <p><strong>Comportamento:</strong> Modal fecharia (catch do JavaScript)</p>
                `;
            });
        }

        function testarDuplicataAtiva() {
            const dados = {
                empresa_cliente_id: '14',
                data_inicio: '2025-07-18',
                nome_contrato: 'Teste Duplicata Modal',
                codigo_contrato: 'MODAL14',
                descricao_projeto: 'Teste via modal - duplicata',
                status_contrato: 'ativo'
            };
            
            adicionarClienteReal(dados, 'resultado1');
        }

        function testarEmpresaNova() {
            const dados = {
                empresa_cliente_id: '11',
                data_inicio: '2025-07-18',
                nome_contrato: 'Teste Nova Modal',
                codigo_contrato: 'MODAL11',
                descricao_projeto: 'Teste via modal - nova empresa',
                status_contrato: 'ativo'
            };
            
            adicionarClienteReal(dados, 'resultado2');
        }

        function testarEmpresaInexistente() {
            const dados = {
                empresa_cliente_id: '999',
                data_inicio: '2025-07-18',
                nome_contrato: 'Teste Inexistente Modal',
                codigo_contrato: 'MODAL999',
                descricao_projeto: 'Teste via modal - empresa inexistente',
                status_contrato: 'ativo'
            };
            
            adicionarClienteReal(dados, 'resultado3');
        }

        // Executar teste automático ao carregar
        window.onload = function() {
            console.log('🚀 Página de teste carregada');
            console.log('📋 Pronto para executar testes reais via modal');
        };
    </script>
</body>
</html>
