#!/usr/bin/env python3
"""
Script para corrigir o erro 'id' no formulário de funcionários.

PROBLEMA IDENTIFICADO:
- O template espera 'errors' como uma lista
- O FormValidator.get_errors() retorna um dicionário
- Quando há erro no campo 'id', o template tenta iterar sobre o dicionário
- Isso causa "Erro ao processar formulário: 'id'"

SOLUÇÃO:
- Converter o dicionário de erros para uma lista de mensagens
- Manter compatibilidade com o template existente
"""

import os
import shutil
from datetime import datetime

def fazer_backup():
    """
    Faz backup do arquivo original.
    """
    arquivo_original = '/var/www/controle-ponto/app_funcionarios.py'
    backup_nome = f'/var/www/controle-ponto/app_funcionarios_backup_erro_id_{datetime.now().strftime("%Y%m%d_%H%M%S")}.py'
    
    shutil.copy2(arquivo_original, backup_nome)
    print(f"✅ Backup criado: {backup_nome}")
    return backup_nome

def corrigir_conversao_erros():
    """
    Corrige a conversão de erros do dicionário para lista.
    """
    arquivo = '/var/www/controle-ponto/app_funcionarios.py'
    
    # Ler o arquivo atual
    with open(arquivo, 'r', encoding='utf-8') as f:
        conteudo = f.read()
    
    # Encontrar o bloco onde os erros são passados para o template
    bloco_antigo = """            context.update({
                'errors': validator.get_errors(),
                'data': form_data,"""
    
    # Novo bloco com conversão de erros
    bloco_novo = """            # ✅ CORREÇÃO: Converter dicionário de erros para lista
            errors_dict = validator.get_errors()
            errors_list = []
            for field, field_errors in errors_dict.items():
                for error_msg in field_errors:
                    errors_list.append(f"{field.replace('_', ' ').title()}: {error_msg}")
            
            context.update({
                'errors': errors_list,  # ✅ CORRIGIDO: Lista em vez de dicionário
                'data': form_data,"""
    
    # Aplicar correção
    if bloco_antigo in conteudo:
        conteudo_novo = conteudo.replace(bloco_antigo, bloco_novo)
        
        # Salvar arquivo corrigido
        with open(arquivo, 'w', encoding='utf-8') as f:
            f.write(conteudo_novo)
        
        print("✅ Correção aplicada com sucesso!")
        print("🔧 Conversão de erros dicionário → lista implementada")
        return True
    else:
        print("❌ Bloco de código não encontrado para correção")
        print("🔍 Verificando padrões alternativos...")
        
        # Tentar padrão alternativo
        bloco_alt = """                'errors': validator.get_errors(),"""
        bloco_novo_alt = """                'errors': _converter_erros_para_lista(validator.get_errors()),"""
        
        if bloco_alt in conteudo:
            # Adicionar função auxiliar
            funcao_auxiliar = '''
def _converter_erros_para_lista(errors_dict):
    """
    Converte dicionário de erros para lista de mensagens.
    
    Args:
        errors_dict (dict): Dicionário de erros do FormValidator
        
    Returns:
        list: Lista de mensagens de erro formatadas
    """
    errors_list = []
    for field, field_errors in errors_dict.items():
        for error_msg in field_errors:
            # Formatar nome do campo de forma amigável
            field_name = field.replace('_', ' ').title()
            errors_list.append(f"{field_name}: {error_msg}")
    return errors_list

'''
            
            # Encontrar local para inserir a função (antes da primeira função)
            pos_primeira_funcao = conteudo.find('def ')
            if pos_primeira_funcao != -1:
                conteudo_novo = (
                    conteudo[:pos_primeira_funcao] + 
                    funcao_auxiliar + 
                    conteudo[pos_primeira_funcao:]
                )
                
                # Aplicar substituição
                conteudo_novo = conteudo_novo.replace(bloco_alt, bloco_novo_alt)
                
                # Salvar arquivo
                with open(arquivo, 'w', encoding='utf-8') as f:
                    f.write(conteudo_novo)
                
                print("✅ Correção alternativa aplicada!")
                print("🔧 Função auxiliar _converter_erros_para_lista() adicionada")
                return True
        
        return False

def testar_correcao():
    """
    Testa se a correção foi aplicada corretamente.
    """
    print("\n🧪 TESTANDO CORREÇÃO")
    print("=" * 50)
    
    # Verificar se a função auxiliar foi adicionada
    arquivo = '/var/www/controle-ponto/app_funcionarios.py'
    with open(arquivo, 'r', encoding='utf-8') as f:
        conteudo = f.read()
    
    if '_converter_erros_para_lista' in conteudo:
        print("✅ Função auxiliar encontrada")
    else:
        print("❌ Função auxiliar não encontrada")
    
    if 'errors_list.append' in conteudo:
        print("✅ Conversão de erros implementada")
    else:
        print("❌ Conversão de erros não encontrada")
    
    print("\n📋 PRÓXIMOS PASSOS:")
    print("1. Reiniciar o servidor Flask")
    print("2. Testar edição de funcionário")
    print("3. Verificar se erro 'id' foi resolvido")

def mostrar_explicacao():
    """
    Mostra explicação detalhada do problema e solução.
    """
    print("\n" + "=" * 60)
    print("📚 EXPLICAÇÃO DO PROBLEMA E SOLUÇÃO")
    print("=" * 60)
    
    print("""
🔍 PROBLEMA IDENTIFICADO:
   O template funcionarios/cadastrar.html espera 'errors' como uma lista:
   
   {% for error in errors %}
       <li>{{ error }}</li>
   {% endfor %}
   
   Mas o FormValidator.get_errors() retorna um dicionário:
   {
       'campo1': ['erro1', 'erro2'],
       'campo2': ['erro3']
   }

❌ RESULTADO:
   Quando há erro no campo 'id' (ou qualquer campo), o template tenta
   iterar sobre o dicionário, causando erro de renderização.

✅ SOLUÇÃO IMPLEMENTADA:
   Converter o dicionário para lista antes de passar ao template:
   
   errors_dict = validator.get_errors()
   errors_list = []
   for field, field_errors in errors_dict.items():
       for error_msg in field_errors:
           errors_list.append(f"{field}: {error_msg}")

🎯 RESULTADO ESPERADO:
   Template recebe lista de strings:
   ['Nome Completo: é obrigatório', 'CPF: formato inválido']
   
   E pode iterar normalmente sem erros.
    """)

if __name__ == "__main__":
    print("🔧 CORREÇÃO DO ERRO 'id' NO FORMULÁRIO DE FUNCIONÁRIOS")
    print("=" * 60)
    
    # Fazer backup
    backup_file = fazer_backup()
    
    # Aplicar correção
    if corrigir_conversao_erros():
        testar_correcao()
        mostrar_explicacao()
        
        print(f"\n💾 Backup salvo em: {backup_file}")
        print("🔄 Reinicie o servidor Flask para aplicar as mudanças")
        print("✅ Correção concluída!")
    else:
        print("❌ Falha ao aplicar correção")
        print("🔄 Restaurando backup...")
        shutil.copy2(backup_file, '/var/www/controle-ponto/app_funcionarios.py')
        print("✅ Backup restaurado")
