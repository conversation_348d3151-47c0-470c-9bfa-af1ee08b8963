#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CORREÇÃO DEFINITIVA DAS FOTOS DOS FUNCIONÁRIOS
==============================================
"""

import os
import sys
sys.path.append('var/www/controle-ponto')

from utils.database import get_db_connection
from pymysql.cursors import DictCursor
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def criar_imagens_jpg_reais():
    """
    Cria imagens JPG simples para substituir os SVGs que não funcionam.
    """
    try:
        from PIL import Image, ImageDraw, ImageFont
        logger.info("📸 Criando imagens JPG reais...")
        
        # Configurações das imagens
        size = (100, 100)
        avatars_dir = "var/www/controle-ponto/static/images/avatars"
        os.makedirs(avatars_dir, exist_ok=True)
        
        # Cores para cada tipo de avatar
        avatars_config = [
            ("funcionario_masculino_1.jpg", "#4A90E2", "#FFFFFF", "M"),   # Azul
            ("funcionario_feminino_1.jpg", "#E24A90", "#FFFFFF", "F"),    # Rosa
            ("funcionario_masculino_2.jpg", "#28A745", "#FFFFFF", "M"),   # Verde
            ("funcionario_feminino_2.jpg", "#FD7E14", "#FFFFFF", "F"),    # Laranja
            ("funcionario_neutro_1.jpg", "#6C757D", "#FFFFFF", "U")       # Cinza
        ]
        
        for nome_arquivo, cor_fundo, cor_texto, letra in avatars_config:
            arquivo_path = os.path.join(avatars_dir, nome_arquivo)
            
            # Criar imagem com fundo colorido
            img = Image.new('RGB', size, cor_fundo)
            draw = ImageDraw.Draw(img)
            
            # Tentar usar fonte, se não conseguir usar fonte padrão
            try:
                font = ImageFont.truetype("arial.ttf", 40)
            except:
                font = ImageFont.load_default()
            
            # Calcular posição do texto centralizado
            text_bbox = draw.textbbox((0, 0), letra, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            x = (size[0] - text_width) // 2
            y = (size[1] - text_height) // 2
            
            # Desenhar texto
            draw.text((x, y), letra, fill=cor_texto, font=font)
            
            # Salvar imagem
            img.save(arquivo_path, 'JPEG', quality=85)
            logger.info(f"   ✅ Criado: {nome_arquivo}")
        
        logger.info("✅ Imagens JPG criadas com sucesso!")
        return True
        
    except ImportError:
        logger.warning("⚠️ PIL não disponível, criando imagens simples...")
        return criar_imagens_simples()
    except Exception as e:
        logger.error(f"❌ Erro ao criar imagens JPG: {str(e)}")
        return criar_imagens_simples()

def criar_imagens_simples():
    """
    Cria arquivos de placeholder simples se PIL não estiver disponível.
    """
    try:
        logger.info("📁 Criando placeholders simples...")
        
        avatars_dir = "var/www/controle-ponto/static/images/avatars"
        os.makedirs(avatars_dir, exist_ok=True)
        
        # Criar arquivos placeholder
        arquivos = [
            "funcionario_masculino_1.jpg",
            "funcionario_feminino_1.jpg", 
            "funcionario_masculino_2.jpg",
            "funcionario_feminino_2.jpg",
            "funcionario_neutro_1.jpg"
        ]
        
        for arquivo in arquivos:
            arquivo_path = os.path.join(avatars_dir, arquivo)
            with open(arquivo_path, 'w') as f:
                f.write(f"# Placeholder para {arquivo}")
            logger.info(f"   📁 Placeholder: {arquivo}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao criar placeholders: {str(e)}")
        return False

def corrigir_caminhos_banco():
    """
    Corrige todos os caminhos de foto no banco de dados.
    """
    try:
        logger.info("🔧 Corrigindo caminhos das fotos no banco...")
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # 1. Atualizar caminhos .svg para .jpg
        logger.info("   🔄 Convertendo .svg para .jpg...")
        cursor.execute("""
            UPDATE funcionarios 
            SET foto_3x4 = REPLACE(foto_3x4, '.svg', '.jpg')
            WHERE foto_3x4 LIKE '%.svg'
        """)
        
        # 2. Corrigir caminhos sem /static/
        logger.info("   🔄 Corrigindo caminhos sem /static/...")
        cursor.execute("""
            UPDATE funcionarios 
            SET foto_3x4 = '/static/images/avatars/funcionario_masculino_1.jpg'
            WHERE foto_3x4 LIKE 'fotos_funcionarios%'
               OR foto_3x4 NOT LIKE '/static/%'
               OR foto_3x4 IS NULL
               OR foto_3x4 = ''
        """)
        
        # 3. Verificar funcionários com caminhos problemáticos
        cursor.execute("""
            SELECT id, nome_completo, foto_3x4
            FROM funcionarios 
            WHERE ativo = TRUE
              AND (foto_3x4 IS NULL 
                   OR foto_3x4 = '' 
                   OR foto_3x4 = '/static/images/funcionario_sem_foto.svg')
        """)
        
        funcionarios_sem_foto = cursor.fetchall()
        
        if funcionarios_sem_foto:
            logger.info(f"   🔄 Corrigindo {len(funcionarios_sem_foto)} funcionários sem foto...")
            for func in funcionarios_sem_foto:
                # Alternar entre avatars baseado no ID
                avatar_num = (func['id'] % 4) + 1
                if avatar_num == 1:
                    foto = '/static/images/avatars/funcionario_masculino_1.jpg'
                elif avatar_num == 2:
                    foto = '/static/images/avatars/funcionario_feminino_1.jpg'
                elif avatar_num == 3:
                    foto = '/static/images/avatars/funcionario_masculino_2.jpg'
                else:
                    foto = '/static/images/avatars/funcionario_feminino_2.jpg'
                
                cursor.execute("""
                    UPDATE funcionarios 
                    SET foto_3x4 = %s
                    WHERE id = %s
                """, (foto, func['id']))
                
                logger.info(f"     ✅ {func['nome_completo']}: {foto}")
        
        conn.commit()
        logger.info("✅ Caminhos do banco corrigidos!")
        
        # 4. Relatório final
        cursor.execute("""
            SELECT DISTINCT foto_3x4, COUNT(*) as qtd
            FROM funcionarios 
            WHERE ativo = TRUE
            GROUP BY foto_3x4
            ORDER BY qtd DESC
        """)
        
        caminhos = cursor.fetchall()
        logger.info("\n📊 CAMINHOS APÓS CORREÇÃO:")
        for caminho in caminhos:
            logger.info(f"   {caminho['foto_3x4']:50s} | Qtd: {caminho['qtd']}")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao corrigir banco: {str(e)}")
        return False

def verificar_resultado():
    """
    Verifica se a correção funcionou.
    """
    try:
        logger.info("🔍 Verificando resultado...")
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Buscar funcionários para teste
        cursor.execute("""
            SELECT id, nome_completo, foto_3x4
            FROM funcionarios 
            WHERE ativo = TRUE
            ORDER BY nome_completo
            LIMIT 5
        """)
        
        funcionarios = cursor.fetchall()
        
        logger.info("\n📋 FUNCIONÁRIOS APÓS CORREÇÃO:")
        for func in funcionarios:
            foto_path = func['foto_3x4']
            if foto_path and foto_path.startswith('/static/'):
                arquivo_local = foto_path.replace('/static/', 'var/www/controle-ponto/static/')
                existe = os.path.exists(arquivo_local)
                status = "✅" if existe else "❌"
                logger.info(f"   {status} {func['nome_completo']:25s} | {foto_path}")
            else:
                logger.info(f"   ❌ {func['nome_completo']:25s} | {foto_path} (caminho inválido)")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro na verificação: {str(e)}")
        return False

def main():
    """
    Função principal.
    """
    logger.info("🚀 CORREÇÃO DEFINITIVA DAS FOTOS")
    logger.info("=" * 60)
    
    try:
        # 1. Criar imagens JPG
        if not criar_imagens_jpg_reais():
            logger.error("❌ Falha ao criar imagens")
            return False
        
        # 2. Corrigir caminhos no banco
        if not corrigir_caminhos_banco():
            logger.error("❌ Falha ao corrigir banco")
            return False
        
        # 3. Verificar resultado
        if not verificar_resultado():
            logger.error("❌ Falha na verificação")
            return False
        
        logger.info("=" * 60)
        logger.info("🎉 CORREÇÃO CONCLUÍDA COM SUCESSO!")
        logger.info("📋 Próximos passos:")
        logger.info("   1. Reiniciar servidor web")
        logger.info("   2. Testar página de registro manual")
        logger.info("   3. As fotos devem aparecer agora!")
        
        return True
        
    except Exception as e:
        logger.error(f"💥 ERRO CRÍTICO: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 