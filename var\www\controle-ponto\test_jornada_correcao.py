#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste da correção de jornada de empresa
"""

import sys
sys.path.append('/var/www/controle-ponto')

def test_timedelta_conversion():
    """Testa a conversão de timedelta para HH:MM"""
    import datetime
    
    def timedelta_to_time(td, default='08:00'):
        if td is None:
            return default
        if isinstance(td, str):
            return td[:5] if len(td) >= 5 else default
        if hasattr(td, 'total_seconds'):
            # É um timedelta
            total_seconds = int(td.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            return f"{hours:02d}:{minutes:02d}"
        return str(td)[:5] if td else default
    
    print("🧪 TESTE DE CONVERSÃO TIMEDELTA")
    print("=" * 40)
    
    # Casos de teste
    test_cases = [
        (None, '08:00'),
        ('', '08:00'),
        ('08:00:00', '08:00'),
        ('08:00', '08:00'),
        (datetime.timedelta(seconds=28800), '08:00'),  # 8 horas
        (datetime.timedelta(seconds=43200), '12:00'),  # 12 horas
        (datetime.timedelta(seconds=46800), '13:00'),  # 13 horas
        (datetime.timedelta(seconds=61200), '17:00'),  # 17 horas
        (datetime.timedelta(seconds=59400), '16:30'),  # 16:30
    ]
    
    for input_val, expected in test_cases:
        result = timedelta_to_time(input_val)
        status = "✅" if result == expected else "❌"
        print(f"{status} Input: {repr(input_val)} -> Output: '{result}' (Expected: '{expected}')")
    
    return True

def test_empresa_config():
    """Testa carregamento de configuração de empresa"""
    try:
        from utils.database import DatabaseManager
        
        print("\n🏢 TESTE DE CONFIGURAÇÃO DE EMPRESA")
        print("=" * 40)
        
        # Buscar uma empresa para teste
        empresa = DatabaseManager.execute_query("""
            SELECT id, razao_social FROM empresas WHERE ativa = 1 LIMIT 1
        """, fetch_one=True)
        
        if not empresa:
            print("❌ Nenhuma empresa encontrada")
            return False
        
        empresa_id = empresa['id']
        print(f"📋 Testando empresa: {empresa['razao_social']} (ID: {empresa_id})")
        
        # Buscar configuração
        config_result = DatabaseManager.execute_query("""
            SELECT jornada_segunda_entrada, jornada_segunda_saida_almoco,
                   jornada_segunda_entrada_almoco, jornada_segunda_saida,
                   jornada_sexta_entrada, jornada_sexta_saida_almoco,
                   jornada_sexta_entrada_almoco, jornada_sexta_saida,
                   intervalo_obrigatorio, tolerancia_empresa_minutos
            FROM empresas_config
            WHERE empresa_id = %s
        """, (empresa_id,), fetch_one=True)
        
        if not config_result:
            print("❌ Nenhuma configuração encontrada")
            return False
        
        print("\n📊 Dados brutos do banco:")
        for i, value in enumerate(config_result.values() if hasattr(config_result, 'values') else config_result):
            print(f"   [{i}]: {repr(value)} (tipo: {type(value)})")
        
        # Aplicar conversão
        def timedelta_to_time(td, default='08:00'):
            if td is None:
                return default
            if isinstance(td, str):
                return td[:5] if len(td) >= 5 else default
            if hasattr(td, 'total_seconds'):
                total_seconds = int(td.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                return f"{hours:02d}:{minutes:02d}"
            return str(td)[:5] if td else default
        
        if isinstance(config_result, dict):
            converted = {
                'jornada_segunda_entrada': timedelta_to_time(config_result.get('jornada_segunda_entrada'), '08:00'),
                'jornada_segunda_saida_almoco': timedelta_to_time(config_result.get('jornada_segunda_saida_almoco'), '12:00'),
                'jornada_segunda_entrada_almoco': timedelta_to_time(config_result.get('jornada_segunda_entrada_almoco'), '13:00'),
                'jornada_segunda_saida': timedelta_to_time(config_result.get('jornada_segunda_saida'), '17:00'),
            }
        else:
            converted = {
                'jornada_segunda_entrada': timedelta_to_time(config_result[0], '08:00'),
                'jornada_segunda_saida_almoco': timedelta_to_time(config_result[1], '12:00'),
                'jornada_segunda_entrada_almoco': timedelta_to_time(config_result[2], '13:00'),
                'jornada_segunda_saida': timedelta_to_time(config_result[3], '17:00'),
            }
        
        print("\n✅ Dados convertidos:")
        for key, value in converted.items():
            print(f"   {key}: '{value}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_timedelta_conversion()
    test_empresa_config()
