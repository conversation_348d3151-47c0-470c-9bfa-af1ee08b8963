#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar funcionários de teste para o sistema RLPONTO-WEB
Data: 05/06/2025
"""

import os
import sys
from datetime import datetime, date
from utils.database import get_db_connection

def criar_funcionarios_teste():
    """Cria funcionários de teste para validar a página de registro manual."""
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Funcionários de teste com TODOS os campos obrigatórios
        funcionarios_teste = [
            {
                'nome_completo': '<PERSON>',
                'cpf': '111.111.111-11',
                'rg': '1111111',
                'data_nascimento': date(1990, 5, 15),
                'sexo': 'M',
                'estado_civil': 'Solteiro',
                'nacionalidade': 'Brasileiro',
                'ctps_numero': '11111111111',
                'ctps_serie_uf': '001/AM',
                'pis_pasep': '111.11111.11-1',
                'endereco_cep': '69000-000',
                'endereco_estado': 'AM',
                'telefone1': '(92) 99999-1111',
                'cargo': 'Desenvolvedor',
                'setor_obra': 'TI',
                'matricula_empresa': 'EMP001',
                'data_admissao': date(2025, 1, 1),
                'tipo_contrato': 'CLT',
                'nivel_acesso': 'Funcionario',
                'turno': 'Diurno',
                'status_cadastro': 'Ativo'
            },
            {
                'nome_completo': 'Maria Oliveira Costa',
                'cpf': '222.222.222-22',
                'rg': '2222222',
                'data_nascimento': date(1988, 8, 20),
                'sexo': 'F',
                'estado_civil': 'Casado',
                'nacionalidade': 'Brasileiro',
                'ctps_numero': '22222222222',
                'ctps_serie_uf': '002/AM',
                'pis_pasep': '222.22222.22-2',
                'endereco_cep': '69000-000',
                'endereco_estado': 'AM',
                'telefone1': '(92) 99999-2222',
                'cargo': 'Analista',
                'setor_obra': 'Financeiro',
                'matricula_empresa': 'EMP002',
                'data_admissao': date(2025, 1, 15),
                'tipo_contrato': 'CLT',
                'nivel_acesso': 'Funcionario',
                'turno': 'Diurno',
                'status_cadastro': 'Ativo'
            },
            {
                'nome_completo': 'Pedro Almeida Souza',
                'cpf': '333.333.333-33',
                'rg': '3333333',
                'data_nascimento': date(1985, 12, 10),
                'sexo': 'M',
                'estado_civil': 'Casado',
                'nacionalidade': 'Brasileiro',
                'ctps_numero': '33333333333',
                'ctps_serie_uf': '003/AM',
                'pis_pasep': '333.33333.33-3',
                'endereco_cep': '69000-000',
                'endereco_estado': 'AM',
                'telefone1': '(92) 99999-3333',
                'cargo': 'Gerente',
                'setor_obra': 'Operações',
                'matricula_empresa': 'EMP003',
                'data_admissao': date(2024, 6, 1),
                'tipo_contrato': 'CLT',
                'nivel_acesso': 'Gerencia',
                'turno': 'Diurno',
                'status_cadastro': 'Ativo'
            },
            {
                'nome_completo': 'Ana Carolina Lima',
                'cpf': '444.444.444-44',
                'rg': '4444444',
                'data_nascimento': date(1992, 3, 25),
                'sexo': 'F',
                'estado_civil': 'Solteiro',
                'nacionalidade': 'Brasileiro',
                'ctps_numero': '44444444444',
                'ctps_serie_uf': '004/AM',
                'pis_pasep': '444.44444.44-4',
                'endereco_cep': '69000-000',
                'endereco_estado': 'AM',
                'telefone1': '(92) 99999-4444',
                'cargo': 'Coordenadora',
                'setor_obra': 'RH',
                'matricula_empresa': 'EMP004',
                'data_admissao': date(2024, 8, 15),
                'tipo_contrato': 'CLT',
                'nivel_acesso': 'Supervisao',
                'turno': 'Diurno',
                'status_cadastro': 'Ativo'
            }
        ]
        
        print("🔄 Criando funcionários de teste...")
        
        for i, func in enumerate(funcionarios_teste, 1):
            try:
                # Verificar se já existe
                cursor.execute("SELECT id FROM funcionarios WHERE cpf = %s", (func['cpf'],))
                if cursor.fetchone():
                    print(f"   ⚠️  Funcionário {func['nome_completo']} já existe")
                    continue
                
                # Inserir novo funcionário com TODOS os campos obrigatórios
                cursor.execute("""
                    INSERT INTO funcionarios 
                    (nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
                     ctps_numero, ctps_serie_uf, pis_pasep, endereco_cep, endereco_estado,
                     telefone1, cargo, setor_obra, matricula_empresa, data_admissao, tipo_contrato,
                     nivel_acesso, turno, status_cadastro, data_cadastro, foto_3x4,
                     jornada_seg_qui_entrada, jornada_seg_qui_saida, jornada_intervalo_entrada, 
                     jornada_intervalo_saida)
                    VALUES 
                    (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                     %s, %s, %s, %s)
                """, (
                    func['nome_completo'],
                    func['cpf'],
                    func['rg'],
                    func['data_nascimento'],
                    func['sexo'],
                    func['estado_civil'],
                    func['nacionalidade'],
                    func['ctps_numero'],
                    func['ctps_serie_uf'],
                    func['pis_pasep'],
                    func['endereco_cep'],
                    func['endereco_estado'],
                    func['telefone1'],
                    func['cargo'],
                    func['setor_obra'],
                    func['matricula_empresa'],
                    func['data_admissao'],
                    func['tipo_contrato'],
                    func['nivel_acesso'],
                    func['turno'],
                    func['status_cadastro'],
                    datetime.now(),
                    '/static/images/funcionario_sem_foto.svg',
                    '08:00:00',  # jornada_seg_qui_entrada
                    '17:00:00',  # jornada_seg_qui_saida
                    '12:00:00',  # jornada_intervalo_entrada (almoço)
                    '13:00:00'   # jornada_intervalo_saida (volta almoço)
                ))
                
                funcionario_id = cursor.lastrowid
                print(f"   ✅ Funcionário {func['nome_completo']} criado com ID {funcionario_id}")
                
            except Exception as e:
                print(f"   ❌ Erro ao criar {func['nome_completo']}: {str(e)}")
                continue
        
        conn.commit()
        conn.close()
        
        print("\n🎉 Funcionários de teste criados com sucesso!")
        print("\n📋 Para testar a página de registro manual:")
        print("   1. Acesse: http://10.19.208.31/registro-ponto/manual")
        print("   2. Faça login com: admin / @Ric6109")
        print("   3. Teste o registro de ponto para os funcionários criados")
        
        print("\n🔍 Verificação das alterações implementadas:")
        print("   ✅ Botão 'Registrar Ponto' adicionado em cada card de funcionário")
        print("   ✅ Modal de registro melhorado com validações")
        print("   ✅ API de horários robusta com fallback para horários padrão")
        print("   ✅ Validação de duplicatas mais informativa")
        print("   ✅ Interface responsiva e profissional")
        
    except Exception as e:
        print(f"❌ Erro geral: {str(e)}")
        return False
    
    return True

def limpar_funcionarios_teste():
    """Remove funcionários de teste criados."""
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cpfs_teste = ['111.111.111-11', '222.222.222-22', '333.333.333-33', '444.444.444-44']
        
        print("🗑️  Removendo funcionários de teste...")
        
        for cpf in cpfs_teste:
            cursor.execute("DELETE FROM funcionarios WHERE cpf = %s", (cpf,))
            if cursor.rowcount > 0:
                print(f"   ✅ Funcionário com CPF {cpf} removido")
        
        conn.commit()
        conn.close()
        
        print("🎉 Funcionários de teste removidos com sucesso!")
        
    except Exception as e:
        print(f"❌ Erro ao remover funcionários de teste: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 SCRIPT DE TESTE - FUNCIONÁRIOS RLPONTO-WEB")
    print("=" * 60)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--limpar":
        limpar_funcionarios_teste()
    else:
        criar_funcionarios_teste()
        
        print("\n💡 Para remover os funcionários de teste execute:")
        print("   python criar_funcionarios_teste.py --limpar") 