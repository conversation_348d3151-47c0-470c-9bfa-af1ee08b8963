#!/usr/bin/env python3
"""
RLPONTO-WEB - Correção do Erro 500 nas Estatísticas

Script para corrigir o erro das estatísticas criando a view vw_estatisticas_pontos
que está faltando no banco de dados.

Desenvolvido por: AiNexus Tecnologia
Autor: <PERSON> - Full Stack Developer
"""

import pymysql
import logging
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def criar_view_estatisticas_pontos():
    """
    Cria a view vw_estatisticas_pontos que está faltando no banco de dados.
    """
    try:
        logger.info("=" * 60)
        logger.info("CORREÇÃO DO ERRO 500 - ESTATÍSTICAS")
        logger.info(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        logger.info("=" * 60)
        
        # Conectar ao banco
        logger.info("Conectando ao banco de dados...")
        conn = pymysql.connect(
            host='************',
            user='cavalcrod',
            password='200381',
            database='controle_ponto',
            charset='utf8mb4'
        )
        
        cursor = conn.cursor()
        
        # Verificar se a view já existe
        logger.info("Verificando se a view já existe...")
        cursor.execute("SHOW TABLES LIKE 'vw_estatisticas_pontos'")
        exists = cursor.fetchone()
        
        if exists:
            logger.info("⚠️ View vw_estatisticas_pontos já existe. Recriando...")
            cursor.execute("DROP VIEW vw_estatisticas_pontos")
        
        # Criar a view
        logger.info("Criando view vw_estatisticas_pontos...")
        sql_view = """
            CREATE VIEW vw_estatisticas_pontos AS
            SELECT 
                DATE(rp.data_hora) as data_registro,
                COUNT(*) as total_registros,
                SUM(CASE WHEN rp.metodo_registro = 'biometrico' THEN 1 ELSE 0 END) as registros_biometricos,
                SUM(CASE WHEN rp.metodo_registro = 'manual' THEN 1 ELSE 0 END) as registros_manuais,
                SUM(CASE WHEN rp.tipo_registro = 'entrada_manha' THEN 1 ELSE 0 END) as entradas_manha,
                SUM(CASE WHEN rp.tipo_registro = 'saida_almoco' THEN 1 ELSE 0 END) as saidas_almoco,
                SUM(CASE WHEN rp.tipo_registro = 'entrada_tarde' THEN 1 ELSE 0 END) as entradas_tarde,
                SUM(CASE WHEN rp.tipo_registro = 'saida' THEN 1 ELSE 0 END) as saidas,
                SUM(CASE 
                    WHEN (rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00') OR
                         (rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00')
                    THEN 1 ELSE 0 
                END) as atrasos,
                COUNT(DISTINCT rp.funcionario_id) as funcionarios_registraram
            FROM registros_ponto rp
            INNER JOIN funcionarios f ON rp.funcionario_id = f.id
            WHERE f.ativo = 1
            GROUP BY DATE(rp.data_hora)
            ORDER BY data_registro DESC
        """
        
        cursor.execute(sql_view)
        logger.info("✅ View vw_estatisticas_pontos criada com sucesso!")
        
        # Testar a view
        logger.info("Testando a view...")
        cursor.execute("SELECT COUNT(*) FROM vw_estatisticas_pontos LIMIT 1")
        count = cursor.fetchone()
        logger.info(f"✅ Teste executado com sucesso. Registros disponíveis: {count[0] if count else 0}")
        
        # Fazer um teste mais completo
        logger.info("Executando teste completo da view...")
        cursor.execute("""
            SELECT * FROM vw_estatisticas_pontos 
            WHERE data_registro BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND CURDATE()
            ORDER BY data_registro DESC
            LIMIT 5
        """)
        
        resultados = cursor.fetchall()
        logger.info(f"✅ Query das estatísticas executada com sucesso. {len(resultados)} registros retornados")
        
        # Commit das mudanças
        conn.commit()
        logger.info("✅ Mudanças commitadas no banco de dados")
        
        # Verificar views existentes
        logger.info("\nVerificando todas as views disponíveis:")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        views = [table[0] for table in tables if table[0].startswith('vw_')]
        
        for view in sorted(views):
            logger.info(f"  ✓ {view}")
        
        conn.close()
        logger.info("\nConexão fechada.")
        
        logger.info("=" * 60)
        logger.info("✅ CORREÇÃO CONCLUÍDA COM SUCESSO!")
        logger.info("A página de estatísticas agora deve funcionar corretamente.")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao criar view: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def main():
    """Função principal."""
    success = criar_view_estatisticas_pontos()
    
    if success:
        print("\n🎯 RESUMO DA CORREÇÃO:")
        print("• Problema identificado: View vw_estatisticas_pontos não existia")
        print("• Solução aplicada: View criada com sucesso")
        print("• Status: Erro 500 das estatísticas corrigido")
        print("• Próximos passos: Testar a página de estatísticas novamente")
        return 0
    else:
        print("\n❌ FALHA NA CORREÇÃO:")
        print("• Não foi possível criar a view necessária")
        print("• Verifique os logs para mais detalhes")
        print("• Pode ser necessário verificar permissões do banco")
        return 1

if __name__ == "__main__":
    exit(main()) 