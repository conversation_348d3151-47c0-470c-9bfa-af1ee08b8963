#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simulação da Página de Alocações
================================

Script para simular exatamente o que acontece quando a página de alocações
é carregada, incluindo a renderização do template.

Data: 07/07/2025
"""

import sys
import os
sys.path.append('var/www/controle-ponto')

def simular_pagina_alocacoes():
    """Simula exatamente o que acontece na página de alocações"""
    print("🎭 SIMULAÇÃO DA PÁGINA DE ALOCAÇÕES")
    print("=" * 60)
    
    try:
        # Importar módulos necessários
        print("1️⃣ Importando módulos...")
        from app_empresa_principal import get_empresa_principal, get_todas_alocacoes, get_clientes_da_empresa_principal
        from flask import Flask, render_template
        print("   ✅ Módulos importados")
        
        # Criar app Flask temporário
        print("2️⃣ Criando app Flask temporário...")
        app = Flask(__name__, template_folder='var/www/controle-ponto/templates')
        app.secret_key = 'test_key'
        print("   ✅ App Flask criado")
        
        with app.app_context():
            # Simular exatamente a função alocacoes()
            print("3️⃣ Executando lógica da página...")
            
            # Passo 1: Buscar empresa principal
            print("   Passo 1: Buscando empresa principal...")
            empresa_principal = get_empresa_principal()
            if not empresa_principal:
                print("   ❌ Empresa principal não definida")
                return False
            print(f"   ✅ Empresa principal: {empresa_principal.get('razao_social', 'N/A')}")

            # Passo 2: Buscar todas as alocações
            print("   Passo 2: Buscando alocações...")
            alocacoes = get_todas_alocacoes()
            print(f"   ✅ Alocações encontradas: {len(alocacoes) if alocacoes else 0}")

            # Passo 3: Buscar clientes
            print("   Passo 3: Buscando clientes...")
            clientes = get_clientes_da_empresa_principal()
            print(f"   ✅ Clientes encontrados: {len(clientes) if clientes else 0}")

            # Passo 4: Calcular estatísticas
            print("   Passo 4: Calculando estatísticas...")
            stats = {
                'total_alocacoes': len(alocacoes) if alocacoes else 0,
                'alocacoes_ativas': len([a for a in alocacoes if a['ativo']]) if alocacoes else 0,
                'funcionarios_unicos': len(set([a['funcionario_id'] for a in alocacoes])) if alocacoes else 0
            }
            print(f"   ✅ Estatísticas: {stats}")

            # Passo 5: Criar contexto
            print("   Passo 5: Criando contexto...")
            context = {
                'titulo': 'Alocação de Funcionários',
                'empresa_principal': empresa_principal,
                'alocacoes': alocacoes,
                'clientes': clientes,
                'stats': stats
            }
            print("   ✅ Contexto criado")

            # Passo 6: Renderizar template
            print("   Passo 6: Renderizando template...")
            try:
                resultado = render_template('empresa_principal/alocacoes.html', **context)
                print("   ✅ Template renderizado com sucesso!")
                
                # Verificar se há conteúdo
                if len(resultado) > 1000:
                    print(f"   📄 Template gerou {len(resultado)} caracteres")
                    
                    # Verificar se há erro no HTML
                    if "erro ao carregar" in resultado.lower():
                        print("   ❌ ERRO ENCONTRADO NO HTML GERADO!")
                        print("   🔍 Procurando por mensagens de erro...")
                        
                        # Extrair contexto do erro
                        linhas = resultado.split('\n')
                        for i, linha in enumerate(linhas):
                            if "erro ao carregar" in linha.lower():
                                print(f"   📋 Linha {i+1}: {linha.strip()}")
                                # Mostrar contexto
                                for j in range(max(0, i-2), min(len(linhas), i+3)):
                                    print(f"      {j+1}: {linhas[j].strip()}")
                                break
                    else:
                        print("   ✅ Nenhum erro encontrado no HTML")
                        
                        # Verificar se as alocações aparecem
                        if alocacoes and len(alocacoes) > 0:
                            nome_funcionario = alocacoes[0].get('nome', '')
                            if nome_funcionario and nome_funcionario in resultado:
                                print(f"   ✅ Alocação encontrada no HTML: {nome_funcionario}")
                            else:
                                print(f"   ⚠️ Alocação não encontrada no HTML")
                
                return True
                
            except Exception as e:
                print(f"   ❌ ERRO ao renderizar template: {e}")
                import traceback
                print(f"   📋 Traceback: {traceback.format_exc()}")
                return False
        
    except Exception as e:
        print(f"❌ ERRO na simulação: {e}")
        import traceback
        print(f"📋 Traceback completo: {traceback.format_exc()}")
        return False

def verificar_template():
    """Verifica se o template existe e está acessível"""
    print("\n🔍 VERIFICANDO TEMPLATE")
    print("-" * 30)
    
    template_path = 'var/www/controle-ponto/templates/empresa_principal/alocacoes.html'
    
    if os.path.exists(template_path):
        print(f"✅ Template encontrado: {template_path}")
        
        # Verificar tamanho
        size = os.path.getsize(template_path)
        print(f"📄 Tamanho: {size} bytes")
        
        # Verificar se é legível
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"📋 Linhas: {len(content.split('\n'))}")
                
                # Verificar se há problemas óbvios
                if "{% if alocacoes %}" in content:
                    print("✅ Lógica de alocações encontrada no template")
                else:
                    print("⚠️ Lógica de alocações não encontrada")
                    
                return True
        except Exception as e:
            print(f"❌ Erro ao ler template: {e}")
            return False
    else:
        print(f"❌ Template não encontrado: {template_path}")
        return False

def main():
    """Função principal"""
    # Verificar template primeiro
    if not verificar_template():
        print("\n❌ PROBLEMA COM O TEMPLATE")
        return
    
    # Simular página
    if simular_pagina_alocacoes():
        print(f"\n🎉 SIMULAÇÃO CONCLUÍDA COM SUCESSO!")
        print("✅ A lógica da página está funcionando")
        print("💡 O problema pode estar na configuração do Flask no servidor")
    else:
        print(f"\n❌ SIMULAÇÃO FALHOU!")
        print("🔧 Verificar erros identificados acima")

if __name__ == "__main__":
    main()
