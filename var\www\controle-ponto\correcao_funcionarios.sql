-- ========================================
-- DIAGNÓSTICO E CORREÇÃO - FUNCIONÁRIOS
-- Data: 05/06/2025
-- Problema: Página registro manual não mostra funcionários
-- ========================================

-- 1. DIAGNÓSTICO - Verificar dados existentes
SELECT 'DIAGNÓSTICO - Total de funcionários:' as info, COUNT(*) as total FROM funcionarios;
SELECT 'DIAGNÓSTICO - Funcionários ativos (ativo=1):' as info, COUNT(*) as total FROM funcionarios WHERE ativo = 1;
SELECT 'DIAGNÓSTICO - Funcionários status Ativo:' as info, COUNT(*) as total FROM funcionarios WHERE status_cadastro = 'Ativo';

-- 2. VERIFICAR INCONSISTÊNCIAS
SELECT 
    f.id,
    f.nome_completo,
    f.ativo,
    f.status_cadastro,
    'PROBLEMA: ativo=0 mas status=Ativo' as problema
FROM funcionarios f 
WHERE f.ativo = 0 AND f.status_cadastro = 'Ativo';

-- 3. VERIFICAR SE CAMPOS OBRIGATÓRIOS ESTÃO PREENCHIDOS
SELECT 
    id,
    nome_completo,
    CASE WHEN cpf IS NULL OR cpf = '' THEN 'CPF_VAZIO' ELSE 'OK' END as cpf_status,
    CASE WHEN ativo IS NULL THEN 'ATIVO_NULL' ELSE CONCAT('ATIVO_', ativo) END as ativo_status,
    status_cadastro
FROM funcionarios 
WHERE ativo = 1 OR status_cadastro = 'Ativo'
ORDER BY nome_completo;

-- 4. CORRIGIR INCONSISTÊNCIAS (se houver)
UPDATE funcionarios 
SET ativo = 1 
WHERE status_cadastro = 'Ativo' AND (ativo = 0 OR ativo IS NULL);

-- 5. GARANTIR QUE FUNCIONÁRIOS ATIVOS TENHAM STATUS CONSISTENTE
UPDATE funcionarios 
SET status_cadastro = 'Ativo' 
WHERE ativo = 1 AND (status_cadastro IS NULL OR status_cadastro = '');

-- 6. VERIFICAR RESULTADO APÓS CORREÇÃO
SELECT 'APÓS CORREÇÃO - Funcionários ativos:' as info, COUNT(*) as total FROM funcionarios WHERE ativo = 1;
SELECT 'APÓS CORREÇÃO - Status Ativo:' as info, COUNT(*) as total FROM funcionarios WHERE status_cadastro = 'Ativo';

-- 7. TESTAR A QUERY EXATA DA PÁGINA MANUAL (ORIGINAL)
SELECT 
    'TESTE QUERY ORIGINAL' as teste,
    COUNT(*) as funcionarios_encontrados
FROM funcionarios f
LEFT JOIN empresas e ON f.empresa_id = e.id
LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
WHERE f.ativo = TRUE;

-- 8. TESTAR A QUERY CORRIGIDA
SELECT 
    'TESTE QUERY CORRIGIDA' as teste,
    COUNT(*) as funcionarios_encontrados
FROM funcionarios f
LEFT JOIN empresas e ON f.empresa_id = e.id
LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
WHERE (f.ativo = 1 OR f.status_cadastro = 'Ativo');

-- 9. LISTAR FUNCIONÁRIOS QUE SERÃO EXIBIDOS APÓS CORREÇÃO
SELECT 
    f.id,
    f.nome_completo,
    f.cpf,
    f.matricula_empresa,
    f.cargo,
    COALESCE(f.setor, f.setor_obra, 'N/A') as setor,
    CASE WHEN f.foto_3x4 IS NOT NULL THEN 'TEM_FOTO' ELSE 'SEM_FOTO' END as foto_status,
    COALESCE(e.nome_fantasia, 'Empresa Padrão') AS empresa,
    COALESCE(ht.nome_horario, 'Horário Padrão') AS nome_horario
FROM funcionarios f
LEFT JOIN empresas e ON f.empresa_id = e.id
LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
WHERE (f.ativo = 1 OR f.status_cadastro = 'Ativo')
ORDER BY f.nome_completo; 