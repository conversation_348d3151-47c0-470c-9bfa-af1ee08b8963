#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para executar SQL de empresa principal usando módulos do projeto
RLPONTO-WEB - Sistema de Controle de Ponto
"""

import sys
import os

# Adicionar o diretório atual ao path para importar módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import pymysql
    print("✅ PyMySQL disponível")
except ImportError:
    print("❌ PyMySQL não encontrado")
    sys.exit(1)

def conectar_banco():
    """Conecta ao banco usando as configurações do projeto"""
    try:
        # Configurações baseadas no projeto
        conexao = pymysql.connect(
            host='************',
            user='cavalcrod',
            password='200381',
            database='controle_ponto',
            charset='utf8mb4',
            autocommit=False
        )
        print("✅ Conectado ao banco de dados")
        return conexao
    except Exception as e:
        print(f"❌ Erro ao conectar: {e}")
        return None

def executar_sql_arquivo(conexao, arquivo_sql):
    """Executa comandos SQL de um arquivo"""
    try:
        cursor = conexao.cursor()
        
        # Ler arquivo SQL
        with open(arquivo_sql, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print(f"📝 Lendo arquivo: {arquivo_sql}")
        
        # Dividir em comandos (simples)
        comandos = []
        comando_atual = ""
        
        for linha in sql_content.split('\n'):
            linha = linha.strip()
            
            # Ignorar comentários e linhas vazias
            if not linha or linha.startswith('--'):
                continue
                
            # Ignorar DELIMITER
            if 'DELIMITER' in linha.upper():
                continue
                
            comando_atual += linha + " "
            
            # Se termina com ; é fim do comando
            if linha.endswith(';'):
                comandos.append(comando_atual.strip())
                comando_atual = ""
        
        print(f"📊 Total de comandos a executar: {len(comandos)}")
        
        # Executar comandos
        sucessos = 0
        erros = 0
        
        for i, comando in enumerate(comandos):
            if not comando:
                continue
                
            try:
                print(f"⚙️ Executando comando {i+1}/{len(comandos)}...")
                cursor.execute(comando)
                sucessos += 1
                
            except Exception as e:
                print(f"⚠️ Erro no comando {i+1}: {e}")
                erros += 1
                # Continuar com próximo comando
                continue
        
        # Commit
        conexao.commit()
        print(f"✅ Execução concluída: {sucessos} sucessos, {erros} erros")
        
        return sucessos > 0
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        conexao.rollback()
        return False
    finally:
        cursor.close()

def verificar_estrutura(conexao):
    """Verifica se a estrutura foi criada"""
    try:
        cursor = conexao.cursor()
        
        # Verificar colunas empresas
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'controle_ponto' 
            AND TABLE_NAME = 'empresas' 
            AND COLUMN_NAME IN ('empresa_principal', 'empresa_matriz_id', 'tipo_empresa')
        """)
        colunas = cursor.fetchone()[0]
        
        # Verificar tabelas
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'controle_ponto' 
            AND TABLE_NAME IN ('empresa_clientes', 'funcionario_alocacoes', 'historico_alocacoes')
        """)
        tabelas = cursor.fetchone()[0]
        
        print(f"📊 Verificação:")
        print(f"   - Colunas adicionadas: {colunas}/3")
        print(f"   - Tabelas criadas: {tabelas}/3")
        
        return colunas >= 2 and tabelas >= 2
        
    except Exception as e:
        print(f"❌ Erro na verificação: {e}")
        return False
    finally:
        cursor.close()

def main():
    """Função principal"""
    print("🚀 RLPONTO-WEB - Estrutura de Empresa Principal")
    print("=" * 50)
    
    # Verificar arquivo SQL
    arquivo_sql = "sql/empresa_principal_clientes.sql"
    if not os.path.exists(arquivo_sql):
        print(f"❌ Arquivo não encontrado: {arquivo_sql}")
        return False
    
    # Conectar
    conexao = conectar_banco()
    if not conexao:
        return False
    
    try:
        # Executar SQL
        print("📝 Executando comandos SQL...")
        sucesso = executar_sql_arquivo(conexao, arquivo_sql)
        
        if sucesso:
            print("✅ SQL executado!")
            
            # Verificar
            print("🔍 Verificando estrutura...")
            if verificar_estrutura(conexao):
                print("✅ Estrutura criada com sucesso!")
                return True
            else:
                print("⚠️ Estrutura parcialmente criada")
                return False
        else:
            print("❌ Falha na execução")
            return False
            
    finally:
        conexao.close()

if __name__ == "__main__":
    sucesso = main()
    print("🎉 Concluído!" if sucesso else "❌ Falhou!")
    sys.exit(0 if sucesso else 1)
