-- =========================================
-- CORREÇÃO CRÍTICA: FUNCIONÁRIOS AUSENTES EM RELATÓRIOS
-- Data: 25/06/2025
-- Problema: Relatórios não mostram funcionários ausentes (sem registros de ponto)
-- Solução: View híbrida + função para calcular ausências
-- =========================================

-- 1. BACKUP da view atual
DROP VIEW IF EXISTS vw_relatorio_pontos_backup;
CREATE VIEW vw_relatorio_pontos_backup AS
SELECT * FROM vw_relatorio_pontos;

-- 2. NOVA VIEW HÍBRIDA: Inclui TODOS os funcionários ativos
DROP VIEW IF EXISTS vw_relatorio_pontos;

CREATE VIEW vw_relatorio_pontos AS
SELECT 
    COALESCE(rp.id, CONCAT('AUS_', f.id, '_', CURDATE())) as id,
    f.id as funcionario_id,
    f.nome_completo,
    f.matricula_empresa,
    f.cpf,
    rp.data_hora,
    COALESCE(DATE(rp.data_hora), CURDATE()) as data_registro,
    COALESCE(TIME(rp.data_hora), '00:00:00') as hora_registro,
    COALESCE(rp.tipo_registro, 'ausente') as tipo_registro,
    CASE 
        WHEN rp.tipo_registro IS NULL THEN 'Ausente'
        WHEN rp.tipo_registro = 'entrada_manha' THEN 'Entrada Manhã'
        WHEN rp.tipo_registro = 'saida_almoco' THEN 'Saída Almoço'
        WHEN rp.tipo_registro = 'entrada_tarde' THEN 'Entrada Tarde'
        WHEN rp.tipo_registro = 'saida' THEN 'Saída'
        ELSE rp.tipo_registro
    END AS tipo_descricao,
    COALESCE(rp.metodo_registro, 'nao_registrado') as metodo_registro,
    CASE 
        WHEN rp.metodo_registro IS NULL THEN 'Não Registrado'
        WHEN rp.metodo_registro = 'biometrico' THEN 'Biométrico'
        WHEN rp.metodo_registro = 'manual' THEN 'Manual'
        ELSE rp.metodo_registro
    END AS metodo_descricao,
    COALESCE(f.setor, f.setor_obra, 'Não informado') as setor,
    f.cargo,
    COALESCE(f.empresa, 'Não informado') as empresa,
    rp.qualidade_biometria,
    CASE 
        WHEN rp.id IS NULL THEN 'Funcionário ausente - sem registros de ponto'
        ELSE rp.observacoes
    END as observacoes,
    rp.ip_origem,
    COALESCE(rp.criado_em, f.criado_em) as criado_em,
    u.usuario as criado_por_usuario,
    CASE 
        WHEN rp.id IS NULL THEN 'Ausente'
        WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN 'Atraso'
        WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN 'Atraso'
        ELSE 'Pontual'
    END AS status_pontualidade,
    -- Campo específico para identificar ausentes
    CASE 
        WHEN rp.id IS NULL THEN 1
        ELSE 0
    END as is_ausente
FROM funcionarios f
LEFT JOIN registros_ponto rp ON f.id = rp.funcionario_id
LEFT JOIN usuarios u ON rp.criado_por = u.id
WHERE f.status_cadastro = 'Ativo'
ORDER BY f.nome_completo, rp.data_hora DESC;

-- 3. FUNÇÃO PARA BUSCAR FUNCIONÁRIOS AUSENTES EM PERÍODO ESPECÍFICO
DELIMITER $$

DROP FUNCTION IF EXISTS buscar_funcionarios_ausentes_periodo$$

CREATE FUNCTION buscar_funcionarios_ausentes_periodo(
    p_data_inicio DATE,
    p_data_fim DATE
) RETURNS TEXT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE resultado TEXT DEFAULT '';
    DECLARE done INT DEFAULT FALSE;
    DECLARE funcionario_nome VARCHAR(255);
    
    DECLARE cur CURSOR FOR
        SELECT f.nome_completo
        FROM funcionarios f
        WHERE f.status_cadastro = 'Ativo'
        AND f.id NOT IN (
            SELECT DISTINCT rp.funcionario_id
            FROM registros_ponto rp
            WHERE DATE(rp.data_hora) BETWEEN p_data_inicio AND p_data_fim
        );
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO funcionario_nome;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        IF resultado = '' THEN
            SET resultado = funcionario_nome;
        ELSE
            SET resultado = CONCAT(resultado, ', ', funcionario_nome);
        END IF;
    END LOOP;
    
    CLOSE cur;
    
    IF resultado = '' THEN
        SET resultado = 'Nenhum funcionário ausente no período';
    END IF;
    
    RETURN resultado;
END$$

DELIMITER ;

-- 4. VIEW PARA ESTATÍSTICAS DE PRESENÇA (MELHORADA)
DROP VIEW IF EXISTS vw_estatisticas_presenca;

CREATE VIEW vw_estatisticas_presenca AS
SELECT 
    DATE(COALESCE(rp.data_hora, CURDATE())) as data_referencia,
    COUNT(DISTINCT f.id) as total_funcionarios,
    COUNT(DISTINCT CASE WHEN rp.id IS NOT NULL THEN f.id END) as funcionarios_presentes,
    COUNT(DISTINCT CASE WHEN rp.id IS NULL THEN f.id END) as funcionarios_ausentes,
    COUNT(DISTINCT CASE 
        WHEN rp.tipo_registro = 'entrada_manha' AND TIME(rp.data_hora) > '08:10:00' THEN f.id
        WHEN rp.tipo_registro = 'entrada_tarde' AND TIME(rp.data_hora) > '13:10:00' THEN f.id
    END) as funcionarios_atrasados,
    ROUND(
        (COUNT(DISTINCT CASE WHEN rp.id IS NOT NULL THEN f.id END) * 100.0) / 
        COUNT(DISTINCT f.id), 2
    ) as percentual_presenca,
    ROUND(
        (COUNT(DISTINCT CASE WHEN rp.id IS NULL THEN f.id END) * 100.0) / 
        COUNT(DISTINCT f.id), 2
    ) as percentual_ausencia
FROM funcionarios f
LEFT JOIN registros_ponto rp ON f.id = rp.funcionario_id
WHERE f.status_cadastro = 'Ativo'
GROUP BY DATE(COALESCE(rp.data_hora, CURDATE()));

-- 5. PROCEDURE PARA RELATÓRIO COMPLETO DE AUSÊNCIAS
DELIMITER $$

DROP PROCEDURE IF EXISTS sp_relatorio_ausencias_periodo$$

CREATE PROCEDURE sp_relatorio_ausencias_periodo(
    IN p_data_inicio DATE,
    IN p_data_fim DATE,
    IN p_setor VARCHAR(100)
)
BEGIN
    -- Funcionários ausentes no período
    SELECT 
        f.id as funcionario_id,
        f.nome_completo,
        f.matricula_empresa,
        COALESCE(f.setor, f.setor_obra, 'Não informado') as setor,
        f.cargo,
        p_data_inicio as data_inicio_ausencia,
        p_data_fim as data_fim_ausencia,
        'AUSENTE' as status_presenca,
        DATEDIFF(p_data_fim, p_data_inicio) + 1 as dias_ausencia_total
    FROM funcionarios f
    WHERE f.status_cadastro = 'Ativo'
    AND (p_setor IS NULL OR p_setor = '' OR COALESCE(f.setor, f.setor_obra) = p_setor)
    AND f.id NOT IN (
        SELECT DISTINCT rp.funcionario_id
        FROM registros_ponto rp
        WHERE DATE(rp.data_hora) BETWEEN p_data_inicio AND p_data_fim
    )
    ORDER BY f.nome_completo;
END$$

DELIMITER ;

-- 6. TESTE DA CORREÇÃO
SELECT 
    'TESTE: View vw_relatorio_pontos atualizada com sucesso!' as status,
    (SELECT COUNT(*) FROM information_schema.views 
     WHERE table_schema = DATABASE() AND table_name = 'vw_relatorio_pontos') as view_existe,
    (SELECT COUNT(*) FROM funcionarios WHERE status_cadastro = 'Ativo') as total_funcionarios_ativos,
    (SELECT COUNT(DISTINCT funcionario_id) FROM vw_relatorio_pontos) as funcionarios_na_view;

-- Verificar se há funcionários ausentes hoje
SELECT 
    data_referencia,
    total_funcionarios,
    funcionarios_presentes,
    funcionarios_ausentes,
    percentual_presenca,
    percentual_ausencia
FROM vw_estatisticas_presenca 
WHERE data_referencia = CURDATE();

-- Teste da função de ausentes
SELECT buscar_funcionarios_ausentes_periodo(CURDATE(), CURDATE()) as funcionarios_ausentes_hoje;

COMMIT; 