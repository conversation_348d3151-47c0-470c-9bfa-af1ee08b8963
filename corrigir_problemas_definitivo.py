#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CORREÇÃO DEFINITIVA DOS PROBLEMAS
=================================
1. Corrige fotos com caminhos incorretos
2. Corrige mascaramento de CPF
"""

import os
import sys
sys.path.append('var/www/controle-ponto')

from utils.database import get_db_connection
from pymysql.cursors import DictCursor
from utils.helpers import mascarar_cpf
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def corrigir_fotos_caminhos_incorretos():
    """
    Corrige funcionários com fotos em caminhos incorretos.
    """
    try:
        logger.info("🔧 Corrigindo fotos com caminhos incorretos...")
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Buscar funcionários com caminhos problemáticos
        cursor.execute("""
            SELECT id, nome_completo, foto_3x4
            FROM funcionarios 
            WHERE ativo = TRUE
              AND foto_3x4 NOT LIKE '/static/%'
              AND foto_3x4 IS NOT NULL
              AND foto_3x4 != ''
        """)
        
        funcionarios_problematicos = cursor.fetchall()
        
        if funcionarios_problematicos:
            logger.info(f"   👥 Encontrados {len(funcionarios_problematicos)} funcionários com caminhos incorretos:")
            
            for func in funcionarios_problematicos:
                logger.info(f"      ID {func['id']}: {func['nome_completo']} | Foto: {func['foto_3x4']}")
                
                # Atribuir avatar baseado no ID
                avatar_num = (func['id'] % 4) + 1
                if avatar_num == 1:
                    nova_foto = '/static/images/avatars/funcionario_masculino_1.jpg'
                elif avatar_num == 2:
                    nova_foto = '/static/images/avatars/funcionario_feminino_1.jpg'
                elif avatar_num == 3:
                    nova_foto = '/static/images/avatars/funcionario_masculino_2.jpg'
                else:
                    nova_foto = '/static/images/avatars/funcionario_feminino_2.jpg'
                
                # Atualizar no banco
                cursor.execute("""
                    UPDATE funcionarios 
                    SET foto_3x4 = %s
                    WHERE id = %s
                """, (nova_foto, func['id']))
                
                logger.info(f"      ✅ Atualizado para: {nova_foto}")
        else:
            logger.info("   ✅ Nenhum funcionário com caminho incorreto encontrado")
        
        conn.commit()
        conn.close()
        logger.info("✅ Correção de fotos concluída!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao corrigir fotos: {str(e)}")
        return False

def testar_mascaramento_cpf():
    """
    Testa a função de mascaramento de CPF.
    """
    try:
        logger.info("🧪 Testando mascaramento de CPF...")
        
        # CPFs de teste
        cpfs_teste = [
            "711.256.042-04",  # Richardson
            "123.456.789-00",  # Teste genérico
            "12345678900"      # Sem formatação
        ]
        
        for cpf in cpfs_teste:
            cpf_usuario = mascarar_cpf(cpf, 'usuario')
            cpf_admin = mascarar_cpf(cpf, 'admin')
            
            logger.info(f"   📋 CPF original: {cpf}")
            logger.info(f"      👤 Usuário vê: {cpf_usuario}")
            logger.info(f"      👑 Admin vê: {cpf_admin}")
            
            # Verificar se está correto (apenas 4 últimos dígitos)
            if cpf_usuario.endswith(cpf[-4:]) and cpf_usuario.count('*') >= 7:
                logger.info(f"      ✅ Mascaramento correto!")
            else:
                logger.warning(f"      ⚠️ Mascaramento pode estar incorreto!")
            
            logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao testar CPF: {str(e)}")
        return False

def verificar_funcao_mascarar_cpf():
    """
    Verifica se a função mascarar_cpf está implementada corretamente.
    """
    try:
        logger.info("🔍 Verificando implementação da função mascarar_cpf...")
        
        # Importar e examinar a função
        from utils.helpers import mascarar_cpf
        import inspect
        
        # Verificar código da função
        codigo = inspect.getsource(mascarar_cpf)
        logger.info("   📄 Código atual da função:")
        for i, linha in enumerate(codigo.split('\n'), 1):
            if linha.strip():
                logger.info(f"      {i:2d}: {linha}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao verificar função: {str(e)}")
        return False

def corrigir_mascaramento_cpf():
    """
    Corrige a função mascarar_cpf se necessário.
    """
    try:
        logger.info("🔧 Verificando se é necessário corrigir mascaramento...")
        
        # Testar CPF específico do Richardson
        cpf_richardson = "711.256.042-04"
        cpf_mascarado = mascarar_cpf(cpf_richardson, 'usuario')
        
        logger.info(f"   📋 CPF Richardson original: {cpf_richardson}")
        logger.info(f"   🔒 CPF mascarado atual: {cpf_mascarado}")
        
        # Verificar se mostra apenas 4 últimos dígitos
        if cpf_mascarado == "***.***.**-2-04":
            logger.info("   ✅ Mascaramento está correto!")
            return True
        else:
            logger.warning(f"   ⚠️ Mascaramento incorreto. Esperado: ***.***.**-2-04")
            logger.info("   🔧 A função mascarar_cpf precisa ser ajustada...")
            return False
        
    except Exception as e:
        logger.error(f"❌ Erro ao verificar mascaramento: {str(e)}")
        return False

def verificar_resultado_final():
    """
    Verificação final para confirmar que tudo está funcionando.
    """
    try:
        logger.info("🏁 Verificação final...")
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Buscar Richardson novamente
        cursor.execute("""
            SELECT 
                f.id,
                f.nome_completo,
                f.cpf,
                f.foto_3x4,
                e.nome_fantasia as empresa,
                ht.nome_horario
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
            WHERE f.nome_completo LIKE "%RICHARDSON%"
            AND f.ativo = TRUE
        """)
        
        funcionario = cursor.fetchone()
        
        if funcionario:
            logger.info(f"📋 RESULTADO FINAL - {funcionario['nome_completo']}:")
            
            # Testar CPF
            cpf_mascarado = mascarar_cpf(funcionario['cpf'], 'usuario')
            logger.info(f"   🔒 CPF: {cpf_mascarado}")
            
            # Testar foto
            foto_url = funcionario['foto_3x4']
            logger.info(f"   📸 Foto: {foto_url}")
            
            if foto_url and foto_url.startswith('/static/'):
                arquivo_local = foto_url.replace('/static/', 'var/www/controle-ponto/static/')
                existe = os.path.exists(arquivo_local)
                logger.info(f"   📁 Arquivo existe: {existe}")
            else:
                logger.warning(f"   ⚠️ Caminho de foto ainda problemático!")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro na verificação final: {str(e)}")
        return False

def main():
    """
    Função principal.
    """
    logger.info("🚀 CORREÇÃO DEFINITIVA DOS PROBLEMAS")
    logger.info("=" * 60)
    
    try:
        # 1. Corrigir fotos com caminhos incorretos
        if not corrigir_fotos_caminhos_incorretos():
            logger.error("❌ Falha ao corrigir fotos")
            return False
        
        # 2. Verificar função mascarar_cpf
        verificar_funcao_mascarar_cpf()
        
        # 3. Testar mascaramento
        if not testar_mascaramento_cpf():
            logger.error("❌ Falha no teste de CPF")
            return False
        
        # 4. Verificar se mascaramento está correto
        cpf_correto = corrigir_mascaramento_cpf()
        
        # 5. Verificação final
        if not verificar_resultado_final():
            logger.error("❌ Falha na verificação final")
            return False
        
        logger.info("=" * 60)
        if cpf_correto:
            logger.info("🎉 CORREÇÃO CONCLUÍDA COM SUCESSO!")
        else:
            logger.info("⚠️ FOTOS CORRIGIDAS - CPF PRECISA DE AJUSTE MANUAL")
        
        logger.info("📋 Próximos passos:")
        logger.info("   1. Reiniciar servidor web")
        logger.info("   2. Testar página de registro manual")
        logger.info("   3. Verificar se fotos e CPF aparecem corretos")
        
        return True
        
    except Exception as e:
        logger.error(f"💥 ERRO CRÍTICO: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 