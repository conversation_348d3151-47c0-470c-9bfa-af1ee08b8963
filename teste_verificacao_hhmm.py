#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 VERIFICAÇÃO DO FORMATO HH:MM NO SISTEMA
==========================================

Testa se o sistema está exibindo corretamente os valores em formato HH:MM
em vez de decimal após as correções.
"""

import requests
import logging
from datetime import datetime
from bs4 import BeautifulSoup
import re

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger('verificacao_hhmm')

def verificar_formato_hhmm():
    """
    Verifica se o sistema está usando formato HH:MM
    """
    print("🔍 VERIFICAÇÃO DO FORMATO HH:MM NO SISTEMA")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # Configurações
        base_url = "http://10.19.208.31"
        
        # Fazer login
        session = requests.Session()
        
        logger.info("🔐 Fazendo login...")
        login_data = {
            'username': 'admin',
            'password': '@Ric6109'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code != 200:
            logger.error(f"❌ Erro no login: {login_response.status_code}")
            return False
        
        logger.info("✅ Login realizado com sucesso")
        
        # Testar página de detalhes de funcionário
        funcionario_id = 45  # Kalebe
        url_detalhes = f"{base_url}/ponto-admin/funcionario/{funcionario_id}?data_inicio=2025-07-15&data_fim=2025-07-17"
        
        logger.info(f"📄 Acessando detalhes do funcionário: {url_detalhes}")
        
        response = session.get(url_detalhes)
        
        if response.status_code != 200:
            print(f"❌ Erro ao acessar página: {response.status_code}")
            return False
        
        # Analisar HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Verificar se há redirecionamento para login
        if "login" in response.text.lower() and "username" in response.text.lower():
            print(f"❌ Redirecionado para login")
            return False
        
        print(f"✅ Página carregada com sucesso")
        
        # Verificar formatos no resumo do período
        print(f"\n📊 VERIFICANDO RESUMO DO PERÍODO:")
        print("-" * 40)
        
        # Procurar por valores em formato decimal (ex: 2.9h, 14.5h)
        texto_completo = soup.get_text()
        
        # Padrões de formato decimal que NÃO deveriam aparecer
        padroes_decimais = [
            r'\d+\.\d+h',  # Ex: 2.9h, 14.5h
            r'-\d+\.\d+h', # Ex: -2.9h
        ]
        
        # Padrões de formato HH:MM que DEVERIAM aparecer
        padroes_hhmm = [
            r'\d{1,2}:\d{2}',  # Ex: 14:30, 2:54
            r'-\d{1,2}:\d{2}', # Ex: -2:54
        ]
        
        problemas_encontrados = []
        formatos_corretos = []
        
        # Verificar se ainda há formatos decimais
        for padrao in padroes_decimais:
            matches = re.findall(padrao, texto_completo)
            if matches:
                problemas_encontrados.extend(matches)
        
        # Verificar se há formatos HH:MM
        for padrao in padroes_hhmm:
            matches = re.findall(padrao, texto_completo)
            if matches:
                formatos_corretos.extend(matches)
        
        # Remover duplicatas
        problemas_encontrados = list(set(problemas_encontrados))
        formatos_corretos = list(set(formatos_corretos))
        
        # Filtrar formatos corretos que não são horários (ex: 07:37 é horário, não duração)
        formatos_duracao = []
        for formato in formatos_corretos:
            # Se tem mais de 12 horas, provavelmente é duração
            if ':' in formato:
                horas = int(formato.replace('-', '').split(':')[0])
                if horas > 12 or formato.startswith('-'):
                    formatos_duracao.append(formato)
        
        print(f"🔍 Formatos decimais encontrados (PROBLEMAS): {len(problemas_encontrados)}")
        for problema in problemas_encontrados[:10]:  # Mostrar apenas os primeiros 10
            print(f"   ❌ {problema}")
        
        print(f"\n✅ Formatos HH:MM encontrados (CORRETOS): {len(formatos_duracao)}")
        for correto in formatos_duracao[:10]:  # Mostrar apenas os primeiros 10
            print(f"   ✅ {correto}")
        
        # Verificar elementos específicos
        print(f"\n🎯 VERIFICANDO ELEMENTOS ESPECÍFICOS:")
        print("-" * 40)
        
        # Resumo do período
        resumo_elementos = soup.find_all('div', class_='summary-value')
        for elemento in resumo_elementos:
            texto = elemento.get_text(strip=True)
            if texto and ('h' in texto or ':' in texto):
                if re.match(r'\d+\.\d+h', texto):
                    print(f"   ❌ Resumo com formato decimal: '{texto}'")
                elif re.match(r'\d+:\d{2}', texto):
                    print(f"   ✅ Resumo com formato HH:MM: '{texto}'")
        
        # Tabela de registros
        tabela_cells = soup.find_all('td')
        for cell in tabela_cells:
            texto = cell.get_text(strip=True)
            if texto and re.match(r'-?\d+\.\d+h', texto):
                print(f"   ❌ Tabela com formato decimal: '{texto}'")
        
        # Resultado final
        print(f"\n" + "=" * 60)
        print(f"📊 RESULTADO DA VERIFICAÇÃO")
        print(f"=" * 60)
        
        if len(problemas_encontrados) == 0:
            print(f"🎉 SUCESSO COMPLETO!")
            print(f"✅ Nenhum formato decimal encontrado")
            print(f"✅ Sistema usando formato HH:MM corretamente")
            print(f"✅ {len(formatos_duracao)} formatos HH:MM detectados")
            return True
        else:
            print(f"⚠️ PROBLEMAS ENCONTRADOS!")
            print(f"❌ {len(problemas_encontrados)} formatos decimais ainda presentes")
            print(f"✅ {len(formatos_duracao)} formatos HH:MM detectados")
            print(f"\n💡 Formatos que precisam ser corrigidos:")
            for problema in problemas_encontrados:
                print(f"   • {problema}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Erro durante a verificação: {e}")
        return False

def main():
    sucesso = verificar_formato_hhmm()
    
    print(f"\n💡 INTERPRETAÇÃO:")
    if sucesso:
        print(f"   ✅ A conversão para formato HH:MM foi bem-sucedida")
        print(f"   ✅ Sistema exibindo valores como 02:54 em vez de 2.9h")
        print(f"   ✅ Interface mais intuitiva para os usuários")
    else:
        print(f"   ⚠️ Ainda há campos usando formato decimal")
        print(f"   🔧 Verificar templates e JavaScript que ainda não foram atualizados")
        print(f"   🔄 Pode ser necessário limpar cache do navegador")
    
    print(f"\n🎯 PRÓXIMOS PASSOS:")
    print(f"   1. Testar manualmente no navegador")
    print(f"   2. Verificar se cache foi limpo")
    print(f"   3. Confirmar se todas as páginas foram atualizadas")

if __name__ == "__main__":
    main()
