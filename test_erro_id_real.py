#!/usr/bin/env python3
"""
Script para testar o erro real do campo 'id' no servidor.
"""

import sys
import os
import traceback

# Adicionar o diretório do projeto ao path
sys.path.append('/var/www/controle-ponto')

try:
    from app_funcionarios import _extrair_dados_formulario, _validar_dados_funcionario, REQUIRED_FIELDS
    from utils.helpers import FormValidator
    from flask import Flask, request
    from werkzeug.test import EnvironBuilder
    from werkzeug.wrappers import Request
    
    print("✅ Imports realizados com sucesso")
    
    # Criar uma aplicação Flask de teste
    app = Flask(__name__)
    
    def simular_request_edicao():
        """
        Simula uma requisição de edição de funcionário.
        """
        print("\n🔍 SIMULANDO REQUISIÇÃO DE EDIÇÃO")
        print("=" * 50)
        
        # Dados simulados de um formulário de edição
        form_data = {
            'nome_completo': '<PERSON>',
            'cpf': '123.456.789-00',
            'rg': '12.345.678-9',
            'data_nascimento': '1990-01-01',
            'sexo': 'M',
            'estado_civil': 'Solteiro',
            'nacionalidade': 'Brasileira',
            'ctps_numero': '1234567',
            'ctps_serie_uf': '001/MG',
            'pis_pasep': '123.45678.90-1',
            'endereco_cep': '30000-000',
            'endereco_estado': 'MG',
            'telefone1': '(31) 99999-9999',
            'cargo': 'Analista',
            'setor_obra': 'Administrativo',
            'matricula_empresa': '2025001',
            'data_admissao': '2025-01-01',
            'tipo_contrato': 'CLT',
            'nivel_acesso': 'Funcionario',
            'turno': 'Diurno',
            'tolerancia_ponto': '10',
            'status_cadastro': 'Ativo',
            'empresa_id': '1',
            # EPIs com IDs (problema potencial)
            'epis[0][id]': '123',
            'epis[0][epi_nome]': 'Capacete',
            'epis[0][epi_ca]': '12345',
            'epis[1][id]': '124',
            'epis[1][epi_nome]': 'Luvas',
            'epis[1][epi_ca]': '67890'
        }
        
        # Criar um ambiente de requisição
        with app.test_request_context('/', method='POST', data=form_data):
            print("📋 Dados do formulário:")
            for key, value in form_data.items():
                print(f"  {key}: {value}")
            
            print("\n🔍 Extraindo dados do formulário...")
            try:
                dados_extraidos = _extrair_dados_formulario()
                print("✅ Dados extraídos com sucesso")
                
                print("\n📊 Campos extraídos:")
                for key, value in dados_extraidos.items():
                    if key != 'epis':  # EPIs são uma lista, vamos mostrar separadamente
                        print(f"  {key}: {value}")
                
                print(f"\n🦺 EPIs extraídos: {len(dados_extraidos.get('epis', []))} itens")
                for i, epi in enumerate(dados_extraidos.get('epis', [])):
                    print(f"  EPI {i}: {epi}")
                
                print("\n🔍 Validando dados...")
                validator = FormValidator()
                _validar_dados_funcionario(dados_extraidos, validator)
                
                if validator.has_errors():
                    print("❌ ERROS ENCONTRADOS:")
                    for field, errors in validator.get_errors().items():
                        print(f"  {field}: {errors}")
                else:
                    print("✅ Validação passou sem erros")
                
                # Verificar se 'id' está nos dados principais
                if 'id' in dados_extraidos:
                    print(f"\n❌ PROBLEMA: Campo 'id' encontrado nos dados principais: {dados_extraidos['id']}")
                else:
                    print("\n✅ Campo 'id' NÃO está nos dados principais")
                
                return dados_extraidos, validator
                
            except Exception as e:
                print(f"❌ ERRO durante extração/validação: {e}")
                print(f"Traceback: {traceback.format_exc()}")
                return None, None
    
    def verificar_campos_obrigatorios():
        """
        Verifica a lista de campos obrigatórios.
        """
        print("\n📋 CAMPOS OBRIGATÓRIOS DEFINIDOS:")
        print("=" * 50)
        for i, field in enumerate(REQUIRED_FIELDS, 1):
            print(f"  {i:2d}. {field}")
        
        if 'id' in REQUIRED_FIELDS:
            print("\n❌ PROBLEMA: Campo 'id' está na lista de obrigatórios!")
        else:
            print("\n✅ Campo 'id' NÃO está na lista de obrigatórios")
    
    if __name__ == "__main__":
        print("🚀 TESTE DO ERRO 'id' NO FORMULÁRIO DE FUNCIONÁRIOS")
        print("=" * 60)
        
        verificar_campos_obrigatorios()
        dados, validator = simular_request_edicao()
        
        print("\n" + "=" * 60)
        print("📊 RESUMO DO TESTE")
        print("=" * 60)
        
        if dados and validator:
            if validator.has_errors():
                print("❌ Teste reproduziu o erro")
                print("🔍 Investigar os erros encontrados acima")
            else:
                print("✅ Teste passou sem erros")
                print("🤔 O problema pode estar em outro lugar")
        else:
            print("❌ Teste falhou durante execução")

except ImportError as e:
    print(f"❌ Erro de import: {e}")
    print("Verifique se o script está sendo executado no diretório correto")
except Exception as e:
    print(f"❌ Erro inesperado: {e}")
    print(f"Traceback: {traceback.format_exc()}")
