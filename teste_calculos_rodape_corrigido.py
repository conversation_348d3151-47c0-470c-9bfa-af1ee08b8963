#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 TESTE DOS CÁLCULOS CORRIGIDOS NO RODAPÉ
==========================================

Sistema: RLPONTO-WEB v1.0
Data: 18/07/2025
Objetivo: Verificar se os cálculos do rodapé foram corrigidos

PROBLEMAS IDENTIFICADOS E CORRIGIDOS:
- ❌ Arredondamento incorreto de minutos
- ❌ Conversão imprecisa de decimal para horas:minutos
- ❌ Formatação inconsistente
- ✅ Correções aplicadas no template
"""

import requests
import re
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('teste_calculos')

def testar_calculos_rodape():
    """
    Testa os cálculos corrigidos no rodapé do sistema.
    """
    logger.info("🔧 TESTANDO CÁLCULOS CORRIGIDOS NO RODAPÉ")
    logger.info("=" * 60)
    
    # Fazer login
    session = requests.Session()
    
    try:
        # Login no sistema
        login_response = session.post(
            "http://10.19.208.31:5000/login",
            data={'usuario': 'admin', 'senha': '@Ric6109'}
        )
        
        if login_response.status_code != 200:
            logger.error("❌ Falha no login")
            return False
        
        logger.info("✅ Login realizado com sucesso")
        
        # Obter página de impressão do Richardson (que tem dados reais)
        relatorio_response = session.get(
            "http://10.19.208.31:5000/ponto-admin/funcionario/35/imprimir",
            params={'data_inicio': '2025-07-17', 'data_fim': '2025-07-17'}
        )
        
        if relatorio_response.status_code != 200:
            logger.error("❌ Falha ao obter relatório")
            return False
        
        html_content = relatorio_response.text
        
        # Extrair dados dos registros
        logger.info("🔍 Analisando dados extraídos...")
        
        # Buscar horários na tabela
        entrada_match = re.search(r'<td>(\d{2}:\d{2})</td>', html_content)
        saida_almoco_match = re.search(r'<td>(\d{2}:\d{2})</td>.*?<td>(\d{2}:\d{2})</td>', html_content)
        
        if entrada_match and saida_almoco_match:
            entrada = entrada_match.group(1)
            saida_almoco = saida_almoco_match.group(2)
            
            logger.info(f"📅 Dados encontrados:")
            logger.info(f"   Entrada: {entrada}")
            logger.info(f"   Saída Almoço: {saida_almoco}")
            
            # Calcular horas manualmente
            entrada_h, entrada_m = map(int, entrada.split(':'))
            saida_h, saida_m = map(int, saida_almoco.split(':'))
            
            entrada_total_min = entrada_h * 60 + entrada_m
            saida_total_min = saida_h * 60 + saida_m
            
            diferenca_min = saida_total_min - entrada_total_min
            horas_calculadas = diferenca_min // 60
            minutos_calculados = diferenca_min % 60
            
            logger.info(f"🧮 Cálculo manual:")
            logger.info(f"   {entrada} até {saida_almoco}")
            logger.info(f"   Diferença: {diferenca_min} minutos")
            logger.info(f"   Resultado: {horas_calculadas}h {minutos_calculados:02d}min")
        
        # Extrair total do rodapé
        total_match = re.search(r'<span id="total-horas"[^>]*>(.*?)</span>', html_content, re.DOTALL)
        
        if total_match:
            total_content = total_match.group(1)
            # Limpar HTML e espaços
            total_clean = re.sub(r'<[^>]+>', '', total_content).strip()
            total_clean = re.sub(r'\s+', ' ', total_clean)
            
            logger.info(f"📊 Total exibido no rodapé: '{total_clean}'")
            
            # Verificar se está correto
            if entrada_match and saida_almoco_match:
                esperado = f"{horas_calculadas}h {minutos_calculados:02d}min"
                
                if esperado in total_clean:
                    logger.info("✅ CÁLCULO CORRETO! Rodapé exibe valor esperado")
                    return True
                else:
                    logger.error(f"❌ CÁLCULO INCORRETO!")
                    logger.error(f"   Esperado: {esperado}")
                    logger.error(f"   Encontrado: {total_clean}")
                    return False
        else:
            logger.error("❌ Não foi possível extrair total do rodapé")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro durante teste: {e}")
        return False

def verificar_template_corrigido():
    """
    Verifica se as correções foram aplicadas no template.
    """
    logger.info("\n🔍 VERIFICANDO CORREÇÕES NO TEMPLATE")
    logger.info("=" * 50)
    
    try:
        with open('var/www/controle-ponto/templates/ponto_admin/imprimir_ponto.html', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Verificar se as correções estão presentes
        correcoes = [
            ('Conversão via segundos com round', 'segundos_totais = (total_decimal * 3600)|round|int' in template_content),
            ('Formatação com %02d', '"%02d"|format(minutos_inteiros)' in template_content),
            ('Correção extras', 'extras_mins = ((ns_extras.total - extras_horas) * 60)|int' in template_content),
            ('Correção banco', 'banco_mins = ((ns_banco.total - banco_horas) * 60)|int' in template_content),
            ('Correção atrasos', 'atrasos_mins = ((ns_atrasos.total - atrasos_horas) * 60)|int' in template_content)
        ]
        
        logger.info("📋 Status das correções:")
        todas_corretas = True
        
        for nome, presente in correcoes:
            status = "✅" if presente else "❌"
            logger.info(f"   {status} {nome}")
            if not presente:
                todas_corretas = False
        
        if todas_corretas:
            logger.info("🎉 Todas as correções foram aplicadas!")
        else:
            logger.error("⚠️ Algumas correções estão faltando!")
        
        return todas_corretas
        
    except Exception as e:
        logger.error(f"❌ Erro ao verificar template: {e}")
        return False

def main():
    """
    Função principal para executar todos os testes.
    """
    print("🔧 TESTE DOS CÁLCULOS CORRIGIDOS NO RODAPÉ")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    # 1. Verificar se template foi corrigido
    template_ok = verificar_template_corrigido()
    
    # 2. Testar cálculos reais
    calculos_ok = testar_calculos_rodape()
    
    # 3. Resultado final
    print("\n" + "=" * 60)
    print("📊 RESULTADO FINAL DOS TESTES")
    print("=" * 60)
    
    print(f"✅ Template corrigido: {'Sim' if template_ok else 'Não'}")
    print(f"✅ Cálculos funcionando: {'Sim' if calculos_ok else 'Não'}")
    
    if template_ok and calculos_ok:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("✅ Cálculos do rodapé corrigidos com sucesso")
        return True
    else:
        print("\n⚠️ ALGUNS TESTES FALHARAM!")
        print("❌ Verificar correções necessárias")
        return False

if __name__ == "__main__":
    sucesso = main()
    exit(0 if sucesso else 1)
