
# Lógica de Controle de Ponto Flexível

## PARTE 1 — LÓGICA COMPLETA PASSO A PASSO

Essa lógica visa registrar batidas de ponto com flexibilidade, identificando automaticamente o turno com base no **horário da batida** e organizando os 
eventos da jornada (entrada, início de intervalo, retorno, saída).

---

## 🔢 Etapas da Lógica de Jornada

### 1. Registro da Primeira Batida do Dia
- Primeiro checar se há batidas no dia, não havendo, segue..
- A **primeira batida registrada** define o **início da jornada** e determina o **turno** do funcionário.
- O sistema compara o horário da batida com a **tabela de faixas horárias do dia** para definir se é:
  - Entrada Manhã
  - Entrada Tarde
  - Entrada Noite

---

### 2. Classificação da Batida com Base no Horário
- O sistema verifica em qual faixa de horário a batida se encaixa:
  - Se for a 1ª batida: classifica como **entrada**
  - Se for a 2ª: **início de intervalo**
  - Se for a 3ª: **retorno do intervalo**
  - Se for a 4ª: **saída**
- Se houver **batidas faltando**, aplicar lógica de inferência (ex: batida 4 usada como retorno do intervalo se a batida 3 estiver ausente e intervalo ≥ 60min)

---

### 3. Intervalo Obrigatório
- A partir da 2ª batida (início do intervalo), o sistema exige **mínimo de 1h de pausa**.
- A batida 3 (retorno) deve ser no mínimo **1h após a batida 2**
- Com tolerancia configurável (ex: atraso até +10min)

---

### 4. Saída
- A batida final (4ª batida) indica o **fim do expediente**.
- Se a batida 3 foi interpretada automaticamente a partir da 4, um alerta é gerado: "Fim de expediente ausente"
- Permitir tolerância:
  - **-10min** antes do horário previsto
  - **+120min** depois, caso permitido (ex: horas extras)

---

### 5. Turno Define Regras
- O turno (manhã/tarde/noite) pode definir:
  - Faixa de entrada permitida
  - Faixa válida para saída
  - Janelas aceitáveis de batidas
  - Regras trabalhistas específicas
  
### 6. crie um banco de dados com esses turnos chamado dia_dados :

- Turno	Início	Fim
- Manhã	05:00	13:00
- Tarde	13:00	21:00
- Noite	21:00	05:00 (dia seguinte)
