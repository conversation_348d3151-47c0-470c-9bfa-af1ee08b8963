#!/usr/bin/env python3
"""
Script para reiniciar o servidor RLPONTO-WEB após deploy
Data: 14/07/2025
Objetivo: Aplicar mudanças da modernização da página de funcionários
"""

import subprocess
import sys
import time

def restart_server():
    """Reinicia o servidor RLPONTO-WEB"""
    
    print("🔄 Iniciando processo de restart do servidor RLPONTO-WEB...")
    print("📅 Data: 14/07/2025")
    print("🎯 Objetivo: Aplicar modernização da página /funcionarios/")
    print("-" * 60)
    
    # Configurações do servidor
    server_ip = "************"
    username = "root"
    password = "@Ric6109"
    
    try:
        # Comandos para executar no servidor
        commands = [
            # Verificar se o servidor está rodando
            "ps aux | grep python | grep app.py",
            
            # Parar processos Flask existentes
            "pkill -f 'python.*app.py' || true",
            
            # Aguardar um momento
            "sleep 3",
            
            # Verificar se ainda há processos
            "pgrep -f 'python.*app.py' || echo 'Nenhum processo Flask encontrado'",
            
            # Navegar para o diretório
            "cd /var/www/controle-ponto",
            
            # Iniciar o servidor em background
            "nohup python3 app.py > flask.log 2>&1 &",
            
            # Aguardar inicialização
            "sleep 5",
            
            # Verificar se está rodando
            "ps aux | grep python | grep app.py | grep -v grep || echo 'Servidor não iniciado'",
            
            # Verificar logs recentes
            "tail -n 10 /var/www/controle-ponto/flask.log || echo 'Log não encontrado'"
        ]
        
        print("🔧 Executando comandos no servidor...")
        
        # Executar cada comando via SSH
        for i, cmd in enumerate(commands, 1):
            print(f"\n📋 Passo {i}: {cmd}")
            
            # Construir comando SSH
            ssh_cmd = f'sshpass -p "{password}" ssh -o StrictHostKeyChecking=no {username}@{server_ip} "{cmd}"'
            
            try:
                # Executar comando
                result = subprocess.run(
                    ssh_cmd,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.stdout:
                    print(f"✅ Saída: {result.stdout.strip()}")
                if result.stderr:
                    print(f"⚠️ Erro: {result.stderr.strip()}")
                    
            except subprocess.TimeoutExpired:
                print("⏰ Timeout - comando demorou muito para executar")
            except Exception as e:
                print(f"❌ Erro ao executar comando: {e}")
        
        print("\n" + "="*60)
        print("🎉 PROCESSO DE RESTART CONCLUÍDO!")
        print("="*60)
        
        # Verificação final
        print("\n🔍 Verificação final do servidor...")
        
        # Testar se o servidor está respondendo
        test_cmd = f'sshpass -p "{password}" ssh -o StrictHostKeyChecking=no {username}@{server_ip} "curl -s -o /dev/null -w \'%{{http_code}}\' http://localhost:5000/ || echo \'Erro na conexão\'"'
        
        try:
            result = subprocess.run(test_cmd, shell=True, capture_output=True, text=True, timeout=10)
            if "200" in result.stdout:
                print("✅ Servidor respondendo corretamente!")
            else:
                print(f"⚠️ Resposta do servidor: {result.stdout.strip()}")
        except:
            print("❌ Não foi possível testar a conexão")
        
        print(f"\n🌐 Acesse: http://{server_ip}/funcionarios/")
        print("📱 Teste a nova interface modernizada!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o restart: {e}")
        return False

def check_dependencies():
    """Verifica se as dependências estão instaladas"""
    
    print("🔍 Verificando dependências...")
    
    # Verificar se sshpass está disponível
    try:
        subprocess.run(["sshpass", "-V"], capture_output=True, check=True)
        print("✅ sshpass encontrado")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ sshpass não encontrado")
        print("💡 Instale com: sudo apt-get install sshpass (Linux) ou brew install hudochenkov/sshpass/sshpass (Mac)")
        
        # Tentar alternativa sem sshpass
        print("🔄 Tentando método alternativo...")
        return False

def main():
    """Função principal"""
    
    print("🚀 SCRIPT DE RESTART - RLPONTO-WEB")
    print("🎨 Modernização da Página de Funcionários")
    print("=" * 60)
    
    # Verificar dependências
    has_sshpass = check_dependencies()
    
    if not has_sshpass:
        print("\n⚠️ Executando sem sshpass - pode solicitar senha manualmente")
        
        # Método alternativo usando SSH direto
        server_ip = "************"
        print(f"\n📋 Execute manualmente no servidor {server_ip}:")
        print("ssh root@************")
        print("cd /var/www/controle-ponto")
        print("pkill -f 'python.*app.py' || true")
        print("sleep 3")
        print("nohup python3 app.py > flask.log 2>&1 &")
        print("sleep 5")
        print("ps aux | grep python | grep app.py")
        
        return
    
    # Executar restart
    success = restart_server()
    
    if success:
        print("\n🎉 RESTART CONCLUÍDO COM SUCESSO!")
        print("✅ A página /funcionarios/ foi modernizada")
        print("🎨 Novo design aplicado conforme layout-rlponto.md")
    else:
        print("\n❌ RESTART FALHOU")
        print("🔧 Verifique os logs e tente novamente")
    
    print("\n📋 Próximos passos:")
    print("1. Acesse http://************/funcionarios/")
    print("2. Teste a nova interface modernizada")
    print("3. Verifique se todas as funcionalidades estão funcionando")
    print("4. Reporte qualquer problema encontrado")

if __name__ == "__main__":
    main()
