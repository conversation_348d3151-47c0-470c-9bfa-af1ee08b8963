#!/usr/bin/env python3
import paramiko
import os

def fix_tabs_definitivo():
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🔧 CORREÇÃO DEFINITIVA DO SISTEMA DE ABAS")
        print("=" * 50)
        
        # 1. Fazer backup do arquivo atual
        print("1. Fazendo backup do arquivo atual...")
        stdin, stdout, stderr = ssh.exec_command('''
        cd /var/www/controle-ponto/templates/configuracoes
        cp index.html index.html.backup_conflito_$(date +%Y%m%d_%H%M%S)
        ''')
        
        # 2. Remover JavaScript conflitante e usar apenas Bootstrap nativo
        print("2. Removendo JavaScript conflitante...")
        
        fix_script = '''
        cd /var/www/controle-ponto/templates/configuracoes
        
        # Remover todo o JavaScript customizado conflitante
        sed -i '/JAVASCRIPT - FORÇAR FUNCIONAMENTO DAS ABAS/,/✅ JAVASCRIPT CUSTOMIZADO REMOVIDO/d' index.html
        
        # Adicionar JavaScript Bootstrap simples e funcional
        cat >> index.html << 'EOF'

<script>
// ===== SISTEMA DE ABAS BOOTSTRAP NATIVO - SEM CONFLITOS =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Inicializando sistema de abas Bootstrap...');
    
    // Inicializar todas as abas Bootstrap
    const triggerTabList = [].slice.call(document.querySelectorAll('#configTabs button[data-bs-toggle="tab"]'));
    triggerTabList.forEach(function (triggerEl) {
        const tabTrigger = new bootstrap.Tab(triggerEl);
        
        triggerEl.addEventListener('click', function (event) {
            event.preventDefault();
            tabTrigger.show();
            console.log('✅ Aba ativada:', triggerEl.getAttribute('data-bs-target'));
        });
    });
    
    // Ativar primeira aba por padrão
    const firstTab = document.querySelector('#geral-tab');
    if (firstTab) {
        const tab = new bootstrap.Tab(firstTab);
        tab.show();
        console.log('✅ Primeira aba ativada automaticamente');
    }
    
    console.log('🎉 Sistema de abas inicializado com sucesso!');
});
</script>
EOF
        
        echo "JavaScript corrigido com sucesso!"
        '''
        
        stdin, stdout, stderr = ssh.exec_command(fix_script)
        output = stdout.read().decode()
        error = stderr.read().decode()
        
        if error:
            print(f"Erro: {error}")
        else:
            print("✅ JavaScript corrigido!")
            print(output)
        
        # 3. Verificar se Bootstrap está carregado no template base
        print("3. Verificando Bootstrap no template base...")
        stdin, stdout, stderr = ssh.exec_command('grep -n "bootstrap" /var/www/controle-ponto/templates/base.html')
        bootstrap_check = stdout.read().decode()
        print("Bootstrap encontrado:")
        print(bootstrap_check)
        
        # 4. Verificar estrutura das abas
        print("4. Verificando estrutura das abas...")
        stdin, stdout, stderr = ssh.exec_command('grep -A 5 -B 5 "empresas-tab" /var/www/controle-ponto/templates/configuracoes/index.html')
        tab_structure = stdout.read().decode()
        print("Estrutura da aba Empresas:")
        print(tab_structure)
        
        # 5. Verificar conteúdo da aba empresas
        print("5. Verificando conteúdo da aba empresas...")
        stdin, stdout, stderr = ssh.exec_command('grep -A 10 "id=\\"empresas\\"" /var/www/controle-ponto/templates/configuracoes/index.html')
        empresas_content = stdout.read().decode()
        print("Conteúdo da aba Empresas:")
        print(empresas_content)
        
        # 6. Reiniciar serviço
        print("6. Reiniciando serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl restart controle-ponto')
        
        # 7. Verificar status
        print("7. Verificando status do serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl is-active controle-ponto')
        status = stdout.read().decode().strip()
        print(f"Status do serviço: {status}")
        
        if status == "active":
            print("✅ Serviço está rodando!")
        else:
            print("❌ Problema com o serviço!")
            
        # 8. Teste final
        print("8. Testando acesso à página...")
        stdin, stdout, stderr = ssh.exec_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/configuracoes/')
        http_code = stdout.read().decode().strip()
        print(f"Código HTTP: {http_code}")
        
        ssh.close()
        
        print("\n" + "=" * 50)
        print("🎉 CORREÇÃO CONCLUÍDA!")
        print("=" * 50)
        print("✅ JavaScript conflitante removido")
        print("✅ Sistema Bootstrap nativo implementado")
        print("✅ Serviço reiniciado")
        print("✅ Testes realizados")
        print("\n🔥 AGORA A ABA EMPRESAS DEVE FUNCIONAR!")
        print("Acesse: http://************:5000/configuracoes/")
        
    except Exception as e:
        print(f"❌ Erro durante a correção: {e}")

if __name__ == "__main__":
    fix_tabs_definitivo()
