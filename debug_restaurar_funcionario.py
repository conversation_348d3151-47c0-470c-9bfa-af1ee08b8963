#!/usr/bin/env python3
"""
Debug da função de restaurar funcionário
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from utils.database import DatabaseManager, FuncionarioQueries

def debug_restaurar_funcionario():
    """Debug completo da função de restaurar"""
    print("🔍 DEBUG: FUNÇÃO RESTAURAR FUNCIONÁRIO")
    print("=" * 50)
    
    try:
        # 1. Verificar funcionários desligados
        print("📋 1. VERIFICANDO FUNCIONÁRIOS DESLIGADOS:")
        db = DatabaseManager()
        funcionarios_desligados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa, data_desligamento
            FROM funcionarios_desligados 
            ORDER BY data_desligamento DESC
            LIMIT 5
        """)
        
        if funcionarios_desligados:
            print(f"   ✅ {len(funcionarios_desligados)} funcionários desligados encontrados:")
            for func in funcionarios_desligados:
                print(f"      - {func['nome_completo']} (ID: {func['funcionario_id_original']}, Matrícula: {func['matricula_empresa']})")
        else:
            print("   ❌ Nenhum funcionário desligado encontrado")
            return False
        
        # 2. Testar função de restauração diretamente
        funcionario_teste = funcionarios_desligados[0]
        funcionario_id_original = funcionario_teste['funcionario_id_original']
        
        print(f"\n📋 2. TESTANDO RESTAURAÇÃO DO FUNCIONÁRIO {funcionario_teste['nome_completo']}:")
        print(f"   ID Original: {funcionario_id_original}")
        
        # Testar a função diretamente
        resultado = FuncionarioQueries.restaurar_funcionario(funcionario_id_original)
        
        print(f"   Resultado: {resultado}")
        
        if resultado['success']:
            print("   ✅ SUCESSO: Função de restauração funcionou!")
            
            # Verificar se funcionário foi restaurado
            funcionario_restaurado = db.execute_query(
                "SELECT id, nome_completo, matricula_empresa, status_cadastro FROM funcionarios WHERE nome_completo = %s",
                (funcionario_teste['nome_completo'],)
            )
            
            if funcionario_restaurado:
                print(f"   ✅ Funcionário encontrado na tabela principal: ID {funcionario_restaurado[0]['id']}")
            else:
                print("   ❌ Funcionário não encontrado na tabela principal após restauração")
                
        else:
            print(f"   ❌ FALHA: {resultado['message']}")
            return False
        
        # 3. Testar via HTTP
        print(f"\n📋 3. TESTANDO VIA HTTP:")
        
        # Configurar sessão
        session = requests.Session()
        retry_strategy = Retry(total=3, backoff_factor=1, status_forcelist=[429, 500, 502, 503, 504])
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        base_url = "http://************:5000"
        
        # Login
        login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=True)
        
        if login_response.status_code != 200:
            print("   ❌ Falha no login")
            return False
        
        print("   ✅ Login realizado")
        
        # Buscar outro funcionário para testar (já que o primeiro foi restaurado)
        funcionarios_desligados_atualizados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa
            FROM funcionarios_desligados 
            ORDER BY data_desligamento DESC
            LIMIT 1
        """)
        
        if not funcionarios_desligados_atualizados:
            print("   ⚠️ Não há mais funcionários desligados para testar via HTTP")
            return True
        
        funcionario_teste_http = funcionarios_desligados_atualizados[0]
        funcionario_id_http = funcionario_teste_http['funcionario_id_original']
        
        print(f"   Testando restauração via HTTP do funcionário: {funcionario_teste_http['nome_completo']}")
        
        # Fazer requisição de restauração
        restaurar_response = session.post(f"{base_url}/funcionarios/restaurar/{funcionario_id_http}")
        
        print(f"   Status Code: {restaurar_response.status_code}")
        print(f"   Response: {restaurar_response.text[:200]}...")
        
        if restaurar_response.status_code == 200:
            print("   ✅ Requisição HTTP bem-sucedida")
        else:
            print(f"   ❌ Falha na requisição HTTP: {restaurar_response.status_code}")
            
        return True
        
    except Exception as e:
        print(f"\n❌ ERRO NO DEBUG: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def verificar_estrutura_tabelas():
    """Verifica se as tabelas têm a estrutura correta"""
    print("\n🔍 VERIFICANDO ESTRUTURA DAS TABELAS:")
    print("-" * 40)
    
    try:
        db = DatabaseManager()
        
        # Verificar tabela funcionarios_desligados
        print("📋 Estrutura da tabela funcionarios_desligados:")
        colunas_desligados = db.execute_query("DESCRIBE funcionarios_desligados")
        
        campos_importantes = ['funcionario_id_original', 'nome_completo', 'matricula_empresa', 'status_cadastro']
        for campo in campos_importantes:
            encontrado = any(col['Field'] == campo for col in colunas_desligados)
            status = "✅" if encontrado else "❌"
            print(f"   {status} {campo}")
        
        # Verificar tabela funcionarios
        print("\n📋 Estrutura da tabela funcionarios:")
        colunas_funcionarios = db.execute_query("DESCRIBE funcionarios")
        
        campos_funcionarios = ['id', 'nome_completo', 'matricula_empresa', 'status_cadastro', 'ativo']
        for campo in campos_funcionarios:
            encontrado = any(col['Field'] == campo for col in colunas_funcionarios)
            status = "✅" if encontrado else "❌"
            print(f"   {status} {campo}")
            
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar estrutura: {e}")
        return False

if __name__ == "__main__":
    print("🎯 DEBUG COMPLETO: FUNÇÃO RESTAURAR FUNCIONÁRIO")
    print("=" * 60)
    
    # Verificar estrutura das tabelas
    estrutura_ok = verificar_estrutura_tabelas()
    
    if estrutura_ok:
        # Testar função de restauração
        teste_ok = debug_restaurar_funcionario()
        
        if teste_ok:
            print("\n🎉 DEBUG CONCLUÍDO!")
            print("✅ Função de restauração analisada")
        else:
            print("\n❌ PROBLEMAS ENCONTRADOS!")
            print("❌ Função de restauração com falhas")
    else:
        print("\n❌ PROBLEMAS NA ESTRUTURA DAS TABELAS!")
