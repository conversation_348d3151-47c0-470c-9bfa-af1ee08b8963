-- Investigar por que aparece "JUSTIFICADO" para funcionário 35
SELECT 
    f.nome_completo,
    rp.data_registro,
    rp.tipo_registro,
    TIME(rp.data_hora) as hora,
    rp.observacoes,
    jp.motivo as justificativa_tabela,
    jp.status_aprovacao
FROM funcionarios f
LEFT JOIN registros_ponto rp ON f.id = rp.funcionario_id
LEFT JOIN justificativas_ponto jp ON f.id = jp.funcionario_id AND rp.data_registro = jp.data_registro
WHERE f.id = 35 
AND rp.data_registro >= '2025-07-10'
ORDER BY rp.data_registro DESC, rp.data_hora ASC;
