#!/usr/bin/env python3
import paramiko

def create_emergency_user():
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🚨 CRIANDO USUÁRIO DE EMERGÊNCIA")
        print("=" * 40)
        
        # Criar usuário com senha em texto plano
        stdin, stdout, stderr = ssh.exec_command('''
        mysql -u root -p@Ric6109 controle_ponto -e "
        DELETE FROM usuarios WHERE usuario = 'admin';
        INSERT INTO usuarios (usuario, nome_completo, email, senha, nivel_acesso, ativo) 
        VALUES ('admin', 'Admin Sistema', '<EMAIL>', '123456', 'admin', 1);"
        ''')
        
        result = stdout.read().decode()
        error = stderr.read().decode()
        
        if "ERROR" not in error:
            print("✅ Usuário de emergência criado!")
            print("🔑 CREDENCIAIS SIMPLES:")
            print("   Usuário: admin")
            print("   Senha: 123456")
        else:
            print(f"Erro: {error}")
        
        ssh.close()
        
    except Exception as e:
        print(f"Erro: {e}")

if __name__ == "__main__":
    create_emergency_user()
