#!/usr/bin/env python3
"""
Teste final da função de restaurar corrigida
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager, FuncionarioQueries

def test_final_corrigido():
    """Teste final da função corrigida"""
    print("🔍 TESTE FINAL: FUNÇÃO RESTAURAR CORRIGIDA")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar funcionários desligados disponíveis
        print("📋 1. VERIFICANDO FUNCIONÁRIOS DESLIGADOS DISPONÍVEIS:")
        funcionarios_desligados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa, data_desligamento
            FROM funcionarios_desligados 
            ORDER BY data_desligamento DESC
        """)
        
        if not funcionarios_desligados:
            print("   ❌ Nenhum funcionário desligado encontrado")
            return False
        
        print(f"   ✅ {len(funcionarios_desligados)} funcionários desligados encontrados:")
        for func in funcionarios_desligados:
            print(f"      - {func['nome_completo']} (ID: {func['funcionario_id_original']}, Matrícula: {func['matricula_empresa']})")
        
        # 2. Testar restauração do primeiro funcionário
        funcionario_teste = funcionarios_desligados[0]
        funcionario_id = funcionario_teste['funcionario_id_original']
        
        print(f"\n📋 2. TESTANDO RESTAURAÇÃO DO FUNCIONÁRIO {funcionario_teste['nome_completo']}:")
        print(f"   ID Original: {funcionario_id}")
        print(f"   Matrícula: {funcionario_teste['matricula_empresa']}")
        
        # Verificar estado antes da restauração
        funcionarios_ativos_antes = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE ativo = TRUE")
        total_antes = funcionarios_ativos_antes[0]['total']
        print(f"   Funcionários ativos antes: {total_antes}")
        
        # Executar restauração
        resultado = FuncionarioQueries.restaurar_funcionario(funcionario_id)
        
        print(f"\n   📊 RESULTADO DA RESTAURAÇÃO:")
        print(f"   Success: {resultado['success']}")
        print(f"   Message: {resultado['message']}")
        
        if resultado['success']:
            print("   ✅ SUCESSO: Função de restauração funcionou!")
            
            # 3. Verificar resultado
            print(f"\n📋 3. VERIFICANDO RESULTADO:")
            
            # Verificar funcionários ativos após restauração
            funcionarios_ativos_depois = db.execute_query("SELECT COUNT(*) as total FROM funcionarios WHERE ativo = TRUE")
            total_depois = funcionarios_ativos_depois[0]['total']
            print(f"   Funcionários ativos depois: {total_depois}")
            
            if total_depois > total_antes:
                print(f"   ✅ SUCESSO: {total_depois - total_antes} funcionário(s) restaurado(s)!")
            else:
                print(f"   ⚠️ Número de funcionários ativos não aumentou")
            
            # Verificar se funcionário específico foi restaurado
            funcionario_restaurado = db.execute_query("""
                SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
                FROM funcionarios 
                WHERE matricula_empresa = %s AND ativo = TRUE
            """, (funcionario_teste['matricula_empresa'],))
            
            if funcionario_restaurado:
                func = funcionario_restaurado[0]
                print(f"   ✅ Funcionário restaurado encontrado:")
                print(f"      ID: {func['id']}, Nome: {func['nome_completo']}")
                print(f"      Status: {func['status_cadastro']}, Ativo: {func['ativo']}")
                
                # Verificar se foi removido da tabela de desligados
                ainda_desligado = db.execute_query("""
                    SELECT COUNT(*) as total
                    FROM funcionarios_desligados 
                    WHERE funcionario_id_original = %s
                """, (funcionario_id,))
                
                if ainda_desligado[0]['total'] == 0:
                    print(f"   ✅ Funcionário removido da tabela de desligados")
                else:
                    print(f"   ⚠️ Funcionário ainda está na tabela de desligados")
                
                return True
            else:
                print(f"   ❌ Funcionário não encontrado na tabela principal")
                return False
        else:
            print(f"   ❌ FALHA: {resultado['message']}")
            return False
        
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def verificar_interface_web():
    """Verificar se a interface web mostra os funcionários restaurados"""
    print(f"\n📋 4. VERIFICANDO INTERFACE WEB:")
    
    try:
        import requests
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        # Configurar sessão
        session = requests.Session()
        retry_strategy = Retry(total=3, backoff_factor=1, status_forcelist=[429, 500, 502, 503, 504])
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        base_url = "http://************:5000"
        
        # Login
        login_data = {'usuario': 'admin', 'senha': '@Ric6109'}
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=True)
        
        if login_response.status_code == 200:
            print("   ✅ Login realizado")
            
            # Verificar lista de funcionários
            funcionarios_response = session.get(f"{base_url}/funcionarios/")
            
            if funcionarios_response.status_code == 200:
                content = funcionarios_response.text
                
                # Contar funcionários na página
                if "Nenhum funcionário encontrado" in content:
                    print("   ❌ Interface mostra 'Nenhum funcionário encontrado'")
                    return False
                else:
                    # Verificar se há funcionários listados
                    if "RICHARDSON" in content or "KALEBE" in content or "SUELEN" in content:
                        print("   ✅ Interface mostra funcionários restaurados!")
                        return True
                    else:
                        print("   ⚠️ Interface carregou mas não mostra funcionários conhecidos")
                        return False
            else:
                print(f"   ❌ Erro ao carregar lista de funcionários: {funcionarios_response.status_code}")
                return False
        else:
            print(f"   ❌ Falha no login: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro ao verificar interface: {e}")
        return False

if __name__ == "__main__":
    print("🎯 TESTE FINAL COMPLETO: FUNÇÃO RESTAURAR FUNCIONÁRIO CORRIGIDA")
    print("=" * 80)
    
    # Teste 1: Função de restauração
    teste1_ok = test_final_corrigido()
    
    # Teste 2: Interface web
    teste2_ok = verificar_interface_web()
    
    print(f"\n📊 RESULTADOS FINAIS:")
    print(f"   Função de restauração: {'✅ OK' if teste1_ok else '❌ FALHA'}")
    print(f"   Interface web: {'✅ OK' if teste2_ok else '❌ FALHA'}")
    
    if teste1_ok and teste2_ok:
        print("\n🎉 SUCESSO TOTAL!")
        print("✅ Função de restaurar funcionário funcionando perfeitamente")
        print("✅ Interface web mostrando funcionários restaurados")
        print("✅ Sistema de restauração completamente funcional")
    else:
        print("\n⚠️ SUCESSO PARCIAL!")
        if teste1_ok:
            print("✅ Função de restauração funcionando")
        if teste2_ok:
            print("✅ Interface web funcionando")
        if not teste1_ok:
            print("❌ Função de restauração ainda tem problemas")
        if not teste2_ok:
            print("❌ Interface web não mostra funcionários restaurados")
