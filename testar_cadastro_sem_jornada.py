#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar cadastro de funcionário em empresa sem jornada
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def testar_cadastro_sem_jornada():
    """Testar cadastro de funcionário em empresa sem jornada"""
    try:
        print("=" * 80)
        print("🧪 TESTANDO CADASTRO EM EMPRESA SEM JORNADA")
        print("=" * 80)
        
        # Importar dependências
        from utils.database import DatabaseManager
        from app_funcionarios import get_jornada_padrao_empresa
        
        # Conectar ao banco
        db = DatabaseManager()
        print("✅ Conexão com banco de dados estabelecida")
        
        # 1. Verificar empresa de teste (ID 13)
        empresa_teste = db.execute_query("""
            SELECT id, nome_fantasia, razao_social
            FROM empresas 
            WHERE id = 13
        """)
        
        if not empresa_teste:
            print("❌ Empresa de teste (ID 13) não encontrada")
            return False
            
        empresa = empresa_teste[0]
        print(f"\n🏢 EMPRESA DE TESTE:")
        print(f"   ID: {empresa['id']}")
        print(f"   Nome: {empresa['nome_fantasia']}")
        print(f"   Razão Social: {empresa['razao_social']}")
        
        # 2. Verificar se tem jornadas
        jornadas = db.execute_query("""
            SELECT id, nome_horario
            FROM horarios_trabalho 
            WHERE empresa_id = 13 AND ativo = 1
        """)
        
        print(f"\n📅 JORNADAS DA EMPRESA:")
        if jornadas:
            print(f"   ✅ {len(jornadas)} jornada(s) encontrada(s):")
            for j in jornadas:
                print(f"      - {j['nome_horario']} (ID: {j['id']})")
        else:
            print("   ❌ Nenhuma jornada cadastrada (perfeito para o teste!)")
        
        # 3. Testar função de busca de jornada
        print(f"\n🔍 TESTANDO FUNÇÃO get_jornada_padrao_empresa(13):")
        jornada_resultado = get_jornada_padrao_empresa(13)
        
        if jornada_resultado:
            print(f"   ✅ Jornada encontrada: {jornada_resultado['nome_horario']}")
        else:
            print("   ❌ Nenhuma jornada encontrada (esperado para empresa sem jornada)")
        
        # 4. Simular dados de funcionário
        print(f"\n👤 SIMULANDO CADASTRO DE FUNCIONÁRIO:")
        dados_funcionario = {
            'empresa_id': 13,
            'nome_completo': 'Funcionário Teste Sem Jornada',
            'cpf': '123.456.789-00',
            'rg': '12.345.678-9',
            'data_nascimento': '1990-01-01',
            'sexo': 'M',
            'estado_civil': 'Solteiro',
            'nacionalidade': 'Brasileira',
            'ctps_numero': '12345',
            'ctps_serie_uf': '001/SP',
            'pis_pasep': '12345678901',
            'endereco_cep': '01234-567',
            'endereco_estado': 'SP',
            'telefone1': '(11) 99999-9999',
            'cargo': 'Teste',
            'setor_obra': 'Teste',
            'matricula_empresa': 'TEST001',
            'data_admissao': '2025-01-01',
            'tipo_contrato': 'CLT',
            'nivel_acesso': 'funcionario',
            'turno': 'diurno',
            'tolerancia_ponto': 10,
            'status_cadastro': 'ativo'
        }
        
        # 5. Testar validação
        print(f"   📋 Dados do funcionário preparados")
        print(f"   🏢 Empresa: {dados_funcionario['empresa_id']} ({empresa['nome_fantasia']})")
        print(f"   👤 Nome: {dados_funcionario['nome_completo']}")
        
        # 6. Verificar se o sistema aplicaria jornada padrão
        print(f"\n🔧 TESTANDO APLICAÇÃO DE JORNADA PADRÃO:")
        
        # Buscar primeira jornada disponível no sistema
        jornada_sistema = db.execute_query(
            "SELECT id, nome_horario FROM horarios_trabalho WHERE ativo = 1 ORDER BY id ASC LIMIT 1"
        )
        
        if jornada_sistema:
            print(f"   ✅ Jornada padrão do sistema disponível:")
            print(f"      - {jornada_sistema[0]['nome_horario']} (ID: {jornada_sistema[0]['id']})")
            print(f"   📝 O sistema aplicaria esta jornada como fallback")
        else:
            print("   ❌ Nenhuma jornada disponível no sistema!")
        
        # 7. Resultado esperado
        print(f"\n📊 RESULTADO ESPERADO:")
        print(f"   ✅ Cadastro deve ser permitido")
        print(f"   ⚠️ Aviso deve ser exibido após o cadastro")
        print(f"   🔧 Jornada padrão deve ser aplicada automaticamente")
        print(f"   📝 Mensagem deve orientar sobre cadastro de jornada na empresa")
        
        print("\n" + "=" * 80)
        print("✅ TESTE DE SIMULAÇÃO CONCLUÍDO!")
        print("=" * 80)
        print("\n💡 PRÓXIMOS PASSOS:")
        print("1. Acesse o formulário de cadastro de funcionários")
        print("2. Selecione a empresa 'Teste Sem Jornada'")
        print("3. Preencha os dados e submeta o formulário")
        print("4. Verifique se o aviso sobre jornada aparece após o cadastro")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    print("Iniciando teste de cadastro sem jornada...")
    testar_cadastro_sem_jornada()
