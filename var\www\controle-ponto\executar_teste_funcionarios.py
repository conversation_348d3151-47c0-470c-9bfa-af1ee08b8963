#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCRIPT PARA EXECUTAR CRIAÇÃO DE FUNCIONÁRIOS DE TESTE VIA SSH
============================================================
"""

import paramiko
import os

def executar_teste():
    """Executa o script de teste no servidor"""
    hostname = '************'
    username = 'admin'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("🚀 EXECUTANDO CRIAÇÃO DE FUNCIONÁRIOS DE TESTE")
        print("✅ Conectado ao servidor com sucesso!")
        
        # Executar script de teste
        print("🧪 Iniciando criação de 30 funcionários de teste...")
        exec_cmd = "cd /var/www/controle-ponto && python3 criar_funcionarios_teste_completo.py"
        stdin, stdout, stderr = ssh.exec_command(exec_cmd)
        
        # Ler output em tempo real
        print("📊 Output do script:")
        print("-" * 50)
        
        while True:
            line = stdout.readline()
            if not line:
                break
            print(line.strip())
        
        # Verificar erros
        error_output = stderr.read().decode('utf-8')
        if error_output:
            print("-" * 50)
            print(f"⚠️ Erros/Avisos:")
            print(error_output)
        
        print("-" * 50)
        print("✅ Script executado com sucesso!")
        
    except Exception as e:
        print(f"❌ Erro durante a execução: {e}")
    finally:
        ssh.close()

if __name__ == "__main__":
    executar_teste()
