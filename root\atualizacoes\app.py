import logging
from datetime import datetime
import json
import os
import re
import time
import pymysql
from pymysql.cursors import DictCursor
from flask import Flask, render_template, request, redirect, url_for, session, jsonify, flash
from werkzeug.security import generate_password_hash, check_password_hash
import asyncio
import websockets

# Configuração de logging
try:
    os.makedirs('/var/log/controle-ponto', exist_ok=True)
    logging.basicConfig(
        filename='/var/log/controle-ponto/app.log',
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
except Exception as e:
    print(f"Erro ao configurar logging: {e}")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

logger = logging.getLogger('controle-ponto')

app = Flask(__name__)
app.secret_key = 'chave_secreta_controle_ponto'
app.config['PERMANENT_SESSION_LIFETIME'] = 86400  # 24 horas

# Constantes
ADMIN_DEFAULT_USERNAME = "admin"

# Função para conectar ao banco de dados
def get_db_connection():
    try:
        connection = pymysql.connect(
            host='localhost',
            user='controle_user',
            password='controle_password',
            database='controle_ponto',
            charset='utf8mb4',
            cursorclass=DictCursor
        )
        return connection
    except Exception as e:
        logger.error(f"Erro ao conectar ao banco de dados: {e}")
        raise

# Função para verificar se a senha é um hash
def is_password_hash(password):
    return ":" in password and len(password) > 50

# Função para obter a próxima matrícula disponível
def get_next_matricula():
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT MAX(CAST(matricula_empresa AS UNSIGNED)) as max_matricula FROM funcionarios")
            result = cursor.fetchone()
            
            if result['max_matricula'] is None:
                next_num = 1
            else:
                next_num = result['max_matricula'] + 1
                
            return f"{next_num:04d}"
    except Exception as e:
        logger.error(f"Erro ao obter próxima matrícula: {e}")
        return "0001"
    finally:
        if conn:
            conn.close()

# Middleware para verificar autenticação e forçar troca de senha
@app.before_request
def check_auth_and_force_password_change():
    public_routes = ['login', 'static', 'trocar_senha_obrigatoria']
    if request.endpoint not in public_routes and 'usuario' not in session:
        return redirect(url_for('login'))
    # Se o usuário está logado e precisa trocar a senha, redireciona
    if request.endpoint not in public_routes and 'force_password_change' in session and session['force_password_change']:
        return redirect(url_for('trocar_senha_obrigatoria'))

# Rota para a página inicial
@app.route('/')
def index():
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT * FROM funcionarios ORDER BY nome_completo")
            funcionarios = cursor.fetchall()
        is_admin = session.get('nivel_acesso') == 'admin'
        return render_template('index.html', funcionarios=funcionarios, is_admin=is_admin, admin_default_username=ADMIN_DEFAULT_USERNAME)
    except Exception as e:
        logger.error(f"Erro na rota index: {e}")
        return render_template('erro.html', mensagem="Erro ao carregar funcionários")
    finally:
        if conn:
            conn.close()

# Rota para login
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        usuario = request.form.get('usuario')
        senha = request.form.get('senha')
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM usuarios WHERE usuario = %s", (usuario,))
                user = cursor.fetchone()
            
            if not user:
                return render_template('login.html', erro="Usuário ou senha incorretos")

            # Verifica se a senha no banco é texto plano ou hash
            senha_db = user['senha']
            senha_valida = False
            force_password_change = False

            if is_password_hash(senha_db):
                # Senha é um hash, verificar com check_password_hash
                senha_valida = check_password_hash(senha_db, senha)
            else:
                # Senha é texto plano, comparar diretamente
                senha_valida = senha_db == senha
                if senha_valida:
                    logger.info(f"Senha em texto plano detectada para o usuário {usuario}. Forçando troca de senha.")
                    force_password_change = True

            if senha_valida:
                session['usuario'] = user['usuario']
                session['force_password_change'] = force_password_change
                # Busca o nível de acesso na tabela permissoes
                try:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT nivel_acesso FROM permissoes WHERE usuario_id = %s", (user['id'],))
                        permissao = cursor.fetchone()
                    session['nivel_acesso'] = permissao['nivel_acesso'] if permissao else 'usuario'
                except Exception as e:
                    logger.error(f"Erro ao buscar nível de acesso: {e}")
                    session['nivel_acesso'] = 'usuario'
                
                if force_password_change:
                    return redirect(url_for('trocar_senha_obrigatoria'))
                return redirect(url_for('index'))
            else:
                return render_template('login.html', erro="Usuário ou senha incorretos")
        except Exception as e:
            logger.error(f"Erro no login: {e}")
            return render_template('login.html', erro="Erro ao processar login")
        finally:
            if conn:
                conn.close()
    
    return render_template('login.html')

# Rota para página de troca de senha obrigatória
@app.route('/trocar_senha_obrigatoria', methods=['GET', 'POST'])
def trocar_senha_obrigatoria():
    if 'usuario' not in session:
        return redirect(url_for('login'))

    if request.method == 'POST':
        nova_senha = request.form.get('nova_senha')
        
        if not nova_senha:
            return render_template('trocar_senha_obrigatoria.html', erro="Nova senha é obrigatória")
        
        if len(nova_senha) < 8:
            return render_template('trocar_senha_obrigatoria.html', erro="A nova senha deve ter pelo menos 8 caracteres")

        # Gera o hash da nova senha
        senha_hash = generate_password_hash(nova_senha)
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("UPDATE usuarios SET senha = %s WHERE usuario = %s", (senha_hash, session['usuario']))
                conn.commit()
            
            logger.info(f"Senha do usuário {session['usuario']} atualizada para hash com sucesso.")
            session['force_password_change'] = False
            return redirect(url_for('index'))
        except Exception as e:
            logger.error(f"Erro ao atualizar senha: {e}")
            return render_template('trocar_senha_obrigatoria.html', erro="Erro ao atualizar senha")
        finally:
            if conn:
                conn.close()
    
    return render_template('trocar_senha_obrigatoria.html')

# Rota para logout
@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

# Rota para cadastrar funcionário
@app.route('/cadastrar', methods=['GET', 'POST'])
def cadastrar():
    if request.method == 'POST':
        try:
            nome_completo = request.form.get('nome_completo', '').strip()
            cpf = request.form.get('cpf', '').strip()
            rg = request.form.get('rg', '').strip()
            data_nascimento = request.form.get('data_nascimento', '').strip()
            sexo = request.form.get('sexo', '').strip()
            estado_civil = request.form.get('estado_civil', '').strip()
            nacionalidade = request.form.get('nacionalidade', '').strip()
            
            ctps_numero = request.form.get('ctps_numero', '').strip()
            ctps_serie_uf = request.form.get('ctps_serie_uf', '').strip()
            pis_pasep = request.form.get('pis_pasep', '').strip()
            
            endereco_cep = request.form.get('endereco_cep', '').strip()
            endereco_rua = request.form.get('endereco_rua', '').strip()
            endereco_bairro = request.form.get('endereco_bairro', '').strip()
            endereco_cidade = request.form.get('endereco_cidade', '').strip()
            endereco_estado = request.form.get('endereco_estado', '').strip()
            telefone1 = request.form.get('telefone1', '').strip()
            telefone2 = request.form.get('telefone2', '').strip()
            email = request.form.get('email', '').strip()
            
            cargo = request.form.get('cargo', '').strip()
            setor_obra = request.form.get('setor_obra', '').strip()
            matricula_empresa = request.form.get('matricula_empresa', '').strip()
            data_admissao = request.form.get('data_admissao', '').strip()
            tipo_contrato = request.form.get('tipo_contrato', '').strip()
            
            jornada_seg_qui_entrada = request.form.get('jornada_seg_qui_entrada', '07:00').strip()
            jornada_seg_qui_saida = request.form.get('jornada_seg_qui_saida', '17:00').strip()
            jornada_sex_entrada = request.form.get('jornada_sex_entrada', '07:00').strip()
            jornada_sex_saida = request.form.get('jornada_sex_saida', '16:00').strip()
            jornada_intervalo_entrada = request.form.get('jornada_intervalo_entrada', '12:00').strip()
            jornada_intervalo_saida = request.form.get('jornada_intervalo_saida', '13:00').strip()
            
            nivel_acesso = request.form.get('nivel_acesso', '').strip()
            turno = request.form.get('turno', '').strip()
            tolerancia_ponto = request.form.get('tolerancia_ponto', '5').strip()
            banco_horas = 1 if request.form.get('banco_horas') else 0
            hora_extra = 1 if request.form.get('hora_extra') else 0
            status_cadastro = request.form.get('status_cadastro', 'Ativo').strip()
            
            epi_nome = request.form.get('epi_nome', '').strip()
            epi_ca = request.form.get('epi_ca', '').strip()
            epi_data_entrega = request.form.get('epi_data_entrega', '').strip()
            epi_data_validade = request.form.get('epi_data_validade', '').strip()
            epi_observacoes = request.form.get('epi_observacoes', '').strip()
            
            digital_dedo1 = request.form.get('digital_dedo1', '').strip()
            digital_dedo2 = request.form.get('digital_dedo2', '').strip()
            
            required_html_fields = [
                'nome_completo', 'cpf', 'rg', 'data_nascimento', 'sexo', 'estado_civil', 'nacionalidade',
                'ctps_numero', 'ctps_serie_uf', 'pis_pasep', 'endereco_cep', 'endereco_estado', 'telefone1',
                'cargo', 'setor_obra', 'matricula_empresa', 'data_admissao', 'tipo_contrato',
                'jornada_seg_qui_entrada', 'jornada_seg_qui_saida', 'jornada_sex_entrada', 'jornada_sex_saida',
                'jornada_intervalo_entrada', 'jornada_intervalo_saida',
                'nivel_acesso', 'turno', 'tolerancia_ponto', 'status_cadastro'
            ]
            
            errors = []
            for field in required_html_fields:
                if not request.form.get(field):
                    errors.append(f"Campo {field} é obrigatório.")
            
            valid_sexo = ['M', 'F', 'Outro']
            valid_estado_civil = ['Solteiro', 'Casado', 'Divorciado', 'Viúvo']
            valid_tipo_contrato = ['CLT', 'PJ', 'Estágio', 'Temporário']
            valid_nivel_acesso = ['Funcionario', 'Supervisao', 'Gerencia']
            valid_turno = ['Diurno', 'Noturno', 'Misto']
            valid_status_cadastro = ['Ativo', 'Inativo']
            
            if sexo and sexo not in valid_sexo:
                errors.append(f"Valor inválido para sexo: {sexo}")
            if estado_civil and estado_civil not in valid_estado_civil:
                errors.append(f"Valor inválido para estado civil: {estado_civil}")
            if tipo_contrato and tipo_contrato not in valid_tipo_contrato:
                errors.append(f"Valor inválido para tipo de contrato: {tipo_contrato}")
            if nivel_acesso and nivel_acesso not in valid_nivel_acesso:
                errors.append(f"Valor inválido para nível de acesso: {nivel_acesso}")
            if turno and turno not in valid_turno:
                errors.append(f"Valor inválido para turno: {turno}")
            if status_cadastro and status_cadastro not in valid_status_cadastro:
                errors.append(f"Valor inválido para status do cadastro: {status_cadastro}")
            
            if digital_dedo1:
                try:
                    digital_dedo1_json = json.loads(digital_dedo1)
                    if not isinstance(digital_dedo1_json, dict) or 'template' not in digital_dedo1_json:
                        errors.append("Formato inválido para biometria do dedo 1")
                except json.JSONDecodeError:
                    errors.append("Formato inválido para biometria do dedo 1")
            
            if digital_dedo2:
                try:
                    digital_dedo2_json = json.loads(digital_dedo2)
                    if not isinstance(digital_dedo2_json, dict) or 'template' not in digital_dedo2_json:
                        errors.append("Formato inválido para biometria do dedo 2")
                except json.JSONDecodeError:
                    errors.append("Formato inválido para biometria do dedo 2")
            
            if errors:
                data = {
                    'nome_completo': nome_completo,
                    'cpf': cpf,
                    'rg': rg,
                    'data_nascimento': data_nascimento,
                    'sexo': sexo,
                    'estado_civil': estado_civil,
                    'nacionalidade': nacionalidade,
                    'ctps_numero': ctps_numero,
                    'ctps_serie_uf': ctps_serie_uf,
                    'pis_pasep': pis_pasep,
                    'endereco_cep': endereco_cep,
                    'endereco_rua': endereco_rua,
                    'endereco_bairro': endereco_bairro,
                    'endereco_cidade': endereco_cidade,
                    'endereco_estado': endereco_estado,
                    'telefone1': telefone1,
                    'telefone2': telefone2,
                    'email': email,
                    'cargo': cargo,
                    'setor_obra': setor_obra,
                    'data_admissao': data_admissao,
                    'tipo_contrato': tipo_contrato,
                    'jornada_seg_qui_entrada': jornada_seg_qui_entrada,
                    'jornada_seg_qui_saida': jornada_seg_qui_saida,
                    'jornada_sex_entrada': jornada_sex_entrada,
                    'jornada_sex_saida': jornada_sex_saida,
                    'jornada_intervalo_entrada': jornada_intervalo_entrada,
                    'jornada_intervalo_saida': jornada_intervalo_saida,
                    'nivel_acesso': nivel_acesso,
                    'turno': turno,
                    'tolerancia_ponto': tolerancia_ponto,
                    'banco_horas': banco_horas,
                    'hora_extra': hora_extra,
                    'status_cadastro': status_cadastro,
                    'epi_nome': epi_nome,
                    'epi_ca': epi_ca,
                    'epi_data_entrega': epi_data_entrega,
                    'epi_data_validade': epi_data_validade,
                    'epi_observacoes': epi_observacoes,
                    'digital_dedo1': digital_dedo1,
                    'digital_dedo2': digital_dedo2
                }
                
                proxima_matricula = get_next_matricula()
                return render_template('cadastrar.html', errors=errors, data=data, proxima_matricula=proxima_matricula)
            
            mapeamento_nivel_acesso = {
                'Funcionário': 'Funcionario',
                'Supervisão': 'Supervisao',
                'Gerência': 'Gerencia'
            }
            
            mapeamento_estado_civil = {
                'Viúvo': 'Viuvo'
            }
            
            mapeamento_tipo_contrato = {
                'Estágio': 'Estagio',
                'Temporário': 'Temporario'
            }
            
            nivel_acesso_db = mapeamento_nivel_acesso.get(nivel_acesso, nivel_acesso)
            estado_civil_db = mapeamento_estado_civil.get(estado_civil, estado_civil)
            tipo_contrato_db = mapeamento_tipo_contrato.get(tipo_contrato, tipo_contrato)
            
            digital_dedo1_template = None
            digital_dedo2_template = None
            
            if digital_dedo1:
                try:
                    digital_dedo1_json = json.loads(digital_dedo1)
                    digital_dedo1_template = digital_dedo1_json.get('template')
                except:
                    pass
            
            if digital_dedo2:
                try:
                    digital_dedo2_json = json.loads(digital_dedo2)
                    digital_dedo2_template = digital_dedo2_json.get('template')
                except:
                    pass
            
            conn = None
            try:
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    sql = """
                    INSERT INTO funcionarios (
                        nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
                        ctps_numero, ctps_serie_uf, pis_pasep,
                        endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
                        telefone1, telefone2, email,
                        cargo, setor_obra, matricula_empresa, data_admissao, tipo_contrato,
                        jornada_entrada, jornada_saida,
                        nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, status_cadastro,
                        digital_dedo1, digital_dedo2
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s,
                        %s, %s, %s, %s, %s,
                        %s, %s, %s,
                        %s, %s, %s, %s, %s,
                        %s, %s,
                        %s, %s, %s, %s, %s, %s,
                        %s, %s
                    )
                    """
                    cursor.execute(sql, (
                        nome_completo, cpf, rg, data_nascimento, sexo, estado_civil_db, nacionalidade,
                        ctps_numero, ctps_serie_uf, pis_pasep,
                        endereco_rua or None, endereco_bairro or None, endereco_cidade or None, endereco_cep, endereco_estado,
                        telefone1, telefone2 or None, email or None,
                        cargo, setor_obra, matricula_empresa, data_admissao, tipo_contrato_db,
                        jornada_seg_qui_entrada, jornada_seg_qui_saida,
                        nivel_acesso_db, turno, tolerancia_ponto, banco_horas, hora_extra, status_cadastro,
                        digital_dedo1_template, digital_dedo2_template
                    ))
                    
                    funcionario_id = cursor.lastrowid
                    
                    if epi_nome:
                        sql_epi = """
                        INSERT INTO epis (
                            funcionario_id, epi_nome, epi_ca, epi_data_entrega, epi_data_validade, epi_observacoes
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s
                        )
                        """
                        cursor.execute(sql_epi, (
                            funcionario_id, epi_nome, epi_ca or None,
                            epi_data_entrega or None, epi_data_validade or None,
                            epi_observacoes or None
                        ))
                
                conn.commit()
                flash("Funcionário cadastrado com sucesso!", "success")
                return redirect(url_for('index'))
                
            except Exception as e:
                if conn:
                    conn.rollback()
                logger.error(f"Erro ao cadastrar funcionário: {e}")
                data = {
                    'nome_completo': nome_completo,
                    'cpf': cpf,
                    'rg': rg,
                    'data_nascimento': data_nascimento,
                    'sexo': sexo,
                    'estado_civil': estado_civil,
                    'nacionalidade': nacionalidade,
                    'ctps_numero': ctps_numero,
                    'ctps_serie_uf': ctps_serie_uf,
                    'pis_pasep': pis_pasep,
                    'endereco_cep': endereco_cep,
                    'endereco_rua': endereco_rua,
                    'endereco_bairro': endereco_bairro,
                    'endereco_cidade': endereco_cidade,
                    'endereco_estado': endereco_estado,
                    'telefone1': telefone1,
                    'telefone2': telefone2,
                    'email': email,
                    'cargo': cargo,
                    'setor_obra': setor_obra,
                    'data_admissao': data_admissao,
                    'tipo_contrato': tipo_contrato,
                    'jornada_seg_qui_entrada': jornada_seg_qui_entrada,
                    'jornada_seg_qui_saida': jornada_seg_qui_saida,
                    'jornada_sex_entrada': jornada_sex_entrada,
                    'jornada_sex_saida': jornada_sex_saida,
                    'jornada_intervalo_entrada': jornada_intervalo_entrada,
                    'jornada_intervalo_saida': jornada_intervalo_saida,
                    'nivel_acesso': nivel_acesso,
                    'turno': turno,
                    'tolerancia_ponto': tolerancia_ponto,
                    'banco_horas': banco_horas,
                    'hora_extra': hora_extra,
                    'status_cadastro': status_cadastro,
                    'epi_nome': epi_nome,
                    'epi_ca': epi_ca,
                    'epi_data_entrega': epi_data_entrega,
                    'epi_data_validade': epi_data_validade,
                    'epi_observacoes': epi_observacoes,
                    'digital_dedo1': digital_dedo1,
                    'digital_dedo2': digital_dedo2
                }
                
                errors = [f"Erro nos dados fornecidos: {str(e)}. Verifique formatos de data, números, etc."]
                proxima_matricula = get_next_matricula()
                return render_template('cadastrar.html', errors=errors, data=data, proxima_matricula=proxima_matricula)
            finally:
                if conn:
                    conn.close()
                
        except Exception as e:
            logger.error(f"Erro geral ao cadastrar funcionário: {e}")
            return render_template('erro.html', mensagem=f"Erro ao processar cadastro: {str(e)}")
    
    try:
        proxima_matricula = get_next_matricula()
        return render_template('cadastrar.html', data={}, proxima_matricula=proxima_matricula)
    except Exception as e:
        logger.error(f"Erro ao exibir formulário de cadastro: {e}")
        return render_template('erro.html', mensagem="Erro ao carregar formulário de cadastro")

# Rota para configurar usuários
@app.route('/configurar_usuarios', methods=['GET', 'POST'])
def configurar_usuarios():
    if session.get('nivel_acesso') != 'admin':
        return redirect(url_for('index'))
    
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT u.*, p.nivel_acesso 
                FROM usuarios u 
                LEFT JOIN permissoes p ON u.id = p.usuario_id 
                ORDER BY u.id
            """)
            usuarios = cursor.fetchall()
        return render_template('configurar_usuarios.html', usuarios=usuarios, admin_default_username=ADMIN_DEFAULT_USERNAME)
    except Exception as e:
        logger.error(f"Erro na rota configurar_usuarios: {e}")
        return render_template('erro.html', mensagem="Erro ao carregar usuários")
    finally:
        if conn:
            conn.close()

# Rota para adicionar novo usuário
@app.route('/adicionar_usuario', methods=['POST'])
def adicionar_usuario():
    if session.get('nivel_acesso') != 'admin':
        return redirect(url_for('index'))
    
    try:
        usuario = request.form.get('usuario')
        senha = request.form.get('senha')
        nivel_acesso = request.form.get('nivel_acesso')
        
        if not usuario or not senha or not nivel_acesso:
            conn = None
            try:
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT u.*, p.nivel_acesso 
                        FROM usuarios u 
                        LEFT JOIN permissoes p ON u.id = p.usuario_id 
                        ORDER BY u.id
                    """)
                    usuarios = cursor.fetchall()
                return render_template('configurar_usuarios.html', usuarios=usuarios, admin_default_username=ADMIN_DEFAULT_USERNAME, error_message="Todos os campos são obrigatórios")
            finally:
                if conn:
                    conn.close()
        
        if len(senha) < 8:
            conn = None
            try:
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT u.*, p.nivel_acesso 
                        FROM usuarios u 
                        LEFT JOIN permissoes p ON u.id = p.usuario_id 
                        ORDER BY u.id
                    """)
                    usuarios = cursor.fetchall()
                return render_template('configurar_usuarios.html', usuarios=usuarios, admin_default_username=ADMIN_DEFAULT_USERNAME, error_message="A senha deve ter pelo menos 8 caracteres")
            finally:
                if conn:
                    conn.close()
        
        if nivel_acesso not in ['admin', 'usuario']:
            conn = None
            try:
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT u.*, p.nivel_acesso 
                        FROM usuarios u 
                        LEFT JOIN permissoes p ON u.id = p.usuario_id 
                        ORDER BY u.id
                    """)
                    usuarios = cursor.fetchall()
                return render_template('configurar_usuarios.html', usuarios=usuarios, admin_default_username=ADMIN_DEFAULT_USERNAME, error_message="Nível de acesso inválido")
            finally:
                if conn:
                    conn.close()
        
        # Gera o hash da senha
        senha_hash = generate_password_hash(senha)
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                # Insere o novo usuário na tabela usuarios
                cursor.execute("INSERT INTO usuarios (usuario, senha) VALUES (%s, %s)", (usuario, senha_hash))
                usuario_id = cursor.lastrowid
                # Insere o nível de acesso na tabela permissoes
                cursor.execute("INSERT INTO permissoes (usuario_id, nivel_acesso) VALUES (%s, %s)", (usuario_id, nivel_acesso))
                conn.commit()
            
            logger.info(f"Novo usuário {usuario} adicionado com sucesso.")
            
            try:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT u.*, p.nivel_acesso 
                        FROM usuarios u 
                        LEFT JOIN permissoes p ON u.id = p.usuario_id 
                        ORDER BY u.id
                    """)
                    usuarios = cursor.fetchall()
                return render_template('configurar_usuarios.html', usuarios=usuarios, admin_default_username=ADMIN_DEFAULT_USERNAME, message="Usuário adicionado com sucesso")
            finally:
                if conn:
                    conn.close()
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Erro ao adicionar usuário: {e}")
            conn = None
            try:
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT u.*, p.nivel_acesso 
                        FROM usuarios u 
                        LEFT JOIN permissoes p ON u.id = p.usuario_id 
                        ORDER BY u.id
                    """)
                    usuarios = cursor.fetchall()
                return render_template('configurar_usuarios.html', usuarios=usuarios, admin_default_username=ADMIN_DEFAULT_USERNAME, error_message=f"Erro ao adicionar usuário: {str(e)}")
            finally:
                if conn:
                    conn.close()
    except Exception as e:
        logger.error(f"Erro geral ao adicionar usuário: {e}")
        return render_template('erro.html', mensagem=f"Erro ao processar cadastro de usuário: {str(e)}")

# Rota para trocar senha de usuário
@app.route('/trocar_senha', methods=['POST'])
def trocar_senha():
    if session.get('nivel_acesso') != 'admin':
        return jsonify({'success': False, 'message': 'Acesso negado'})
    
    try:
        usuario_id = request.form.get('id')
        nova_senha = request.form.get('senha')
        
        if not usuario_id or not nova_senha:
            return jsonify({'success': False, 'message': 'Dados incompletos'})
        
        if len(nova_senha) < 8:
            return jsonify({'success': False, 'message': 'A senha deve ter pelo menos 8 caracteres'})
        
        senha_hash = generate_password_hash(nova_senha)
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT usuario FROM usuarios WHERE id = %s", (usuario_id,))
                user_result = cursor.fetchone()
                if not user_result:
                    return jsonify({'success': False, 'message': 'Usuário não encontrado'})
                
                username = user_result['usuario']
                
                cursor.execute("UPDATE usuarios SET senha = %s WHERE id = %s", (senha_hash, usuario_id))
                conn.commit()
                
                kick_user = username == session.get('usuario')
                
                return jsonify({
                    'success': True, 
                    'message': 'Senha alterada com sucesso',
                    'kick_user': kick_user
                })
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Erro ao trocar senha: {e}")
            return jsonify({'success': False, 'message': f'Erro ao trocar senha: {str(e)}'})
        finally:
            if conn:
                conn.close()
    except Exception as e:
        logger.error(f"Erro geral ao trocar senha: {e}")
        return jsonify({'success': False, 'message': f'Erro: {str(e)}'})

# Rota para alterar nível de acesso
@app.route('/alterar_nivel', methods=['POST'])
def alterar_nivel():
    if session.get('nivel_acesso') != 'admin':
        return jsonify({'success': False, 'message': 'Acesso negado'})
    
    try:
        usuario_id = request.form.get('id')
        novo_nivel = request.form.get('nivel')
        
        if not usuario_id or not novo_nivel:
            return jsonify({'success': False, 'message': 'Dados incompletos'})
        
        if novo_nivel not in ['admin', 'usuario']:
            return jsonify({'success': False, 'message': 'Nível de acesso inválido'})
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT usuario FROM usuarios WHERE id = %s", (usuario_id,))
                user_result = cursor.fetchone()
                if not user_result:
                    return jsonify({'success': False, 'message': 'Usuário não encontrado'})
                
                username = user_result['usuario']
                
                if username == ADMIN_DEFAULT_USERNAME and novo_nivel != 'admin':
                    return jsonify({
                        'success': False, 
                        'message': 'Não é permitido alterar o nível de acesso do usuário admin padrão'
                    })
                
                cursor.execute("UPDATE permissoes SET nivel_acesso = %s WHERE usuario_id = %s", (novo_nivel, usuario_id))
                conn.commit()
                
                return jsonify({'success': True, 'message': 'Nível de acesso alterado com sucesso'})
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Erro ao alterar nível de acesso: {e}")
            return jsonify({'success': False, 'message': f'Erro ao alterar nível de acesso: {str(e)}'})
        finally:
            if conn:
                conn.close()
    except Exception as e:
        logger.error(f"Erro geral ao alterar nível de acesso: {e}")
        return jsonify({'success': False, 'message': f'Erro: {str(e)}'})

# Rota para excluir usuário
@app.route('/excluir_usuario', methods=['POST'])
def excluir_usuario():
    if session.get('nivel_acesso') != 'admin':
        return jsonify({'success': False, 'message': 'Acesso negado'})
    
    try:
        usuario_id = request.form.get('id')
        
        if not usuario_id:
            return jsonify({'success': False, 'message': 'ID de usuário não fornecido'})
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT usuario FROM usuarios WHERE id = %s", (usuario_id,))
                user_result = cursor.fetchone()
                if not user_result:
                    return jsonify({'success': False, 'message': 'Usuário não encontrado'})
                
                username = user_result['usuario']
                
                if username == ADMIN_DEFAULT_USERNAME:
                    return jsonify({
                        'success': False, 
                        'message': 'Não é permitido excluir o usuário admin padrão'
                    })
                
                cursor.execute("DELETE FROM permissoes WHERE usuario_id = %s", (usuario_id,))
                cursor.execute("DELETE FROM usuarios WHERE id = %s", (usuario_id,))
                conn.commit()
                
                return jsonify({'success': True, 'message': 'Usuário excluído com sucesso'})
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Erro ao excluir usuário: {e}")
            return jsonify({'success': False, 'message': f'Erro ao excluir usuário: {str(e)}'})
        finally:
            if conn:
                conn.close()
    except Exception as e:
        logger.error(f"Erro geral ao excluir usuário: {e}")
        return jsonify({'success': False, 'message': f'Erro: {str(e)}'})

# Função para comparar templates biométricos via WebSocket
async def comparar_templates(template1, template2):
    async with websockets.connect("ws://localhost:8765") as websocket:
        await websocket.send(json.dumps({
            "command": "MATCH",
            "params": {
                "template1": template1,
                "template2": template2
            }
        }))
        response = await websocket.recv()
        data = json.loads(response)
        if data["type"] == "STATUS":
            return data["data"]["score"] >= 55  # Usa o MATCH_THRESHOLD de biometria_service.py
        else:
            raise Exception("Erro ao comparar templates: " + data.get("data", {}).get("message", "Desconhecido"))

# Rota para verificar biometria
@app.route('/api/verificar_biometria', methods=['POST'])
def verificar_biometria():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': 'Dados não fornecidos'})
        
        template = data.get('template')
        if not template:
            return jsonify({'success': False, 'message': 'Template biométrico não fornecido'})
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT id, nome_completo, digital_dedo1, digital_dedo2 
                    FROM funcionarios 
                    WHERE digital_dedo1 IS NOT NULL OR digital_dedo2 IS NOT NULL
                """)
                funcionarios = cursor.fetchall()
                
                for funcionario in funcionarios:
                    if funcionario['digital_dedo1']:
                        try:
                            match = asyncio.run(comparar_templates(template, funcionario['digital_dedo1']))
                            if match:
                                return jsonify({
                                    'success': False, 
                                    'message': f'Biometria já cadastrada para o funcionário {funcionario["nome_completo"]} (Dedo 1)'
                                })
                        except Exception as e:
                            logger.error(f"Erro ao comparar biometria (dedo 1): {e}")
                            continue
                    
                    if funcionario['digital_dedo2']:
                        try:
                            match = asyncio.run(comparar_templates(template, funcionario['digital_dedo2']))
                            if match:
                                return jsonify({
                                    'success': False, 
                                    'message': f'Biometria já cadastrada para o funcionário {funcionario["nome_completo"]} (Dedo 2)'
                                })
                        except Exception as e:
                            logger.error(f"Erro ao comparar biometria (dedo 2): {e}")
                            continue
                
                return jsonify({'success': True, 'message': 'Biometria não cadastrada anteriormente'})
                
        except Exception as e:
            logger.error(f"Erro ao verificar biometria: {e}")
            return jsonify({'success': False, 'message': f'Erro ao verificar biometria: {str(e)}'})
        finally:
            if conn:
                conn.close()
    except Exception as e:
        logger.error(f"Erro geral na rota /api/verificar_biometria: {e}")
        return jsonify({'success': False, 'message': f'Erro inesperado: {str(e)}'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
