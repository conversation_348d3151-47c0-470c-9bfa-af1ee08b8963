#!/usr/bin/env python3
"""
Teste final para verificar se o erro 'id' foi corrigido.
"""

import sys
import os
import traceback

# Adicionar o diretório do projeto ao path
sys.path.append('/var/www/controle-ponto')

try:
    from app_funcionarios import _extrair_dados_formulario, _validar_dados_funcionario, _extrair_dados_epis
    from utils.helpers import FormValidator
    from flask import Flask, request
    
    print("✅ Imports realizados com sucesso")
    
    # Criar uma aplicação Flask de teste
    app = Flask(__name__)
    
    def testar_extracao_epis_com_id():
        """
        Testa especificamente a extração de EPIs com campo 'id'.
        """
        print("\n🦺 TESTANDO EXTRAÇÃO DE EPIs COM CAMPO 'id'")
        print("=" * 60)
        
        # Simular dados de formulário com EPIs que têm ID (edição)
        form_data = {
            'nome_completo': '<PERSON>',
            'cpf': '123.456.789-00',
            'empresa_id': '11',
            # EPIs com IDs (simulando edição)
            'epis[0][id]': '123',  # ❌ Este era o problema!
            'epis[0][epi_nome]': 'Capacete',
            'epis[0][epi_ca]': '12345',
            'epis[1][id]': '124',  # ❌ Este também!
            'epis[1][epi_nome]': 'Luvas',
            'epis[1][epi_ca]': '67890'
        }
        
        print("📋 Dados do formulário:")
        for key, value in form_data.items():
            print(f"  {key}: {value}")
        
        # Testar extração de EPIs
        with app.test_request_context('/', method='POST', data=form_data):
            epis_extraidos = _extrair_dados_epis()
            
            print(f"\n🦺 EPIs extraídos: {len(epis_extraidos)}")
            for i, epi in enumerate(epis_extraidos):
                print(f"  EPI {i+1}: {epi}")
                
                # Verificar se o campo 'id' está presente
                if 'id' in epi:
                    print(f"    ✅ Campo 'id' presente: {epi['id']}")
                else:
                    print(f"    ⚠️ Campo 'id' ausente (EPI novo)")
        
        return epis_extraidos
    
    def testar_validacao_completa():
        """
        Testa a validação completa com dados que anteriormente causavam erro.
        """
        print("\n🔍 TESTANDO VALIDAÇÃO COMPLETA")
        print("=" * 60)
        
        # Dados que anteriormente causavam o erro 'id'
        form_data = {
            'nome_completo': 'João da Silva',
            'cpf': '123.456.789-00',
            'rg': '12.345.678-9',
            'data_nascimento': '1990-01-01',
            'sexo': 'M',
            'estado_civil': 'Solteiro',
            'nacionalidade': 'Brasileira',
            'ctps_numero': '1234567',
            'ctps_serie_uf': '001/MG',
            'pis_pasep': '123.45678.90-1',
            'endereco_cep': '30000-000',
            'endereco_estado': 'MG',
            'telefone1': '(31) 99999-9999',
            'cargo': 'Analista',
            'setor_obra': 'Administrativo',
            'matricula_empresa': '2025001',
            'data_admissao': '2025-01-01',
            'tipo_contrato': 'CLT',
            'nivel_acesso': 'Funcionario',
            'turno': 'Diurno',
            'tolerancia_ponto': '10',
            'status_cadastro': 'Ativo',
            'empresa_id': '11',
            # EPIs com IDs que causavam o problema
            'epis[0][id]': '123',
            'epis[0][epi_nome]': 'Capacete',
            'epis[0][epi_ca]': '12345',
            'epis[1][id]': '124',
            'epis[1][epi_nome]': 'Luvas',
            'epis[1][epi_ca]': '67890'
        }
        
        with app.test_request_context('/', method='POST', data=form_data):
            try:
                print("📋 Extraindo dados do formulário...")
                dados_extraidos = _extrair_dados_formulario()
                
                print("✅ Dados extraídos com sucesso")
                
                # Verificar se 'id' está nos dados principais (não deveria estar)
                if 'id' in dados_extraidos:
                    print(f"❌ PROBLEMA: Campo 'id' ainda está nos dados principais: {dados_extraidos['id']}")
                    return False
                else:
                    print("✅ Campo 'id' NÃO está nos dados principais")
                
                print("\n🔍 Validando dados...")
                validator = FormValidator()
                dados_extraidos['funcionario_id'] = 1  # Simular edição
                
                _validar_dados_funcionario(dados_extraidos, validator)
                
                if validator.has_errors():
                    print("❌ ERROS ENCONTRADOS:")
                    errors_dict = validator.get_errors()
                    
                    for field, field_errors in errors_dict.items():
                        print(f"  {field}: {field_errors}")
                        
                        # Verificar se ainda há erro do campo 'id'
                        if field == 'id':
                            print(f"    ❌ ERRO 'id' AINDA PRESENTE!")
                            return False
                    
                    # Se há outros erros (não relacionados a 'id'), pode ser normal
                    if 'id' not in errors_dict:
                        print("✅ Erro 'id' NÃO está presente nos erros")
                        print("⚠️ Outros erros podem ser normais (empresa, campos obrigatórios, etc.)")
                        return True
                else:
                    print("✅ Validação passou sem erros!")
                    return True
                    
            except Exception as e:
                print(f"❌ ERRO durante processamento: {e}")
                print(f"Traceback: {traceback.format_exc()}")
                return False
    
    if __name__ == "__main__":
        print("🧪 TESTE FINAL: CORREÇÃO DO ERRO 'id'")
        print("=" * 60)
        
        # Testar extração de EPIs
        epis = testar_extracao_epis_com_id()
        
        # Testar validação completa
        sucesso = testar_validacao_completa()
        
        print("\n" + "=" * 60)
        print("📊 RESULTADO FINAL")
        print("=" * 60)
        
        if sucesso:
            print("🎉 SUCESSO! ERRO 'id' CORRIGIDO!")
            print("✅ A correção está funcionando corretamente")
            print("🎯 Edição de funcionários sem alterações deve funcionar agora")
            print("\n📋 RESUMO DA CORREÇÃO:")
            print("  1. ✅ Campo 'id' dos EPIs não vai mais para dados principais")
            print("  2. ✅ Validação filtra campos indevidos")
            print("  3. ✅ Template recebe lista de erros em vez de dicionário")
        else:
            print("❌ FALHA! Erro 'id' ainda está presente")
            print("🔍 Verificar logs para mais detalhes")
        
        print("\n📋 PRÓXIMOS PASSOS:")
        if sucesso:
            print("✅ Testar no navegador: editar funcionário sem alterar nada")
            print("✅ Verificar se mensagem de erro 'id' desapareceu")
        else:
            print("🔧 Investigar causa raiz adicional")
            print("📋 Verificar logs do servidor")

except ImportError as e:
    print(f"❌ Erro de import: {e}")
except Exception as e:
    print(f"❌ Erro inesperado: {e}")
    print(f"Traceback: {traceback.format_exc()}")
