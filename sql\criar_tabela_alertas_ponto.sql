-- ========================================
-- CRIAÇÃO DA TABELA ALERTAS_PONTO
-- Data: 09/07/2025
-- Descrição: Tabela para armazenar alertas e pendências do sistema de ponto
-- ========================================

USE controle_ponto;

-- ========================================
-- 1. CRIAR TABELA ALERTAS_PONTO
-- ========================================

CREATE TABLE IF NOT EXISTS alertas_ponto (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    funcionario_id INT UNSIGNED NOT NULL,
    data_referencia DATE NOT NULL COMMENT 'Data da jornada que gerou o alerta',
    tipo_alerta ENUM(
        'entrada_ausente',
        'saida_ausente', 
        'intervalo_ausente',
        'retorno_ausente',
        'batidas_extras',
        'sequencia_invalida',
        'intervalo_obrigatorio',
        'jornada_incompleta',
        'batida_fora_ordem',
        'jornada_longa_sem_intervalo'
    ) NOT NULL,
    descricao TEXT NOT NULL COMMENT 'Descrição detalhada do alerta',
    dados_contexto JSON NULL COMMENT 'Dados adicionais em formato JSON',
    prioridade ENUM('baixa', 'media', 'alta', 'critica') DEFAULT 'media',
    status_alerta ENUM('pendente', 'resolvido', 'ignorado') DEFAULT 'pendente',
    requer_acao BOOLEAN DEFAULT FALSE COMMENT 'Se requer ação manual obrigatória',
    resolvido_por INT UNSIGNED NULL COMMENT 'ID do usuário que resolveu',
    data_resolucao TIMESTAMP NULL COMMENT 'Data/hora da resolução',
    observacoes_resolucao TEXT NULL COMMENT 'Observações da resolução',
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    atualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_funcionario_data (funcionario_id, data_referencia),
    INDEX idx_tipo_alerta (tipo_alerta),
    INDEX idx_status (status_alerta),
    INDEX idx_prioridade (prioridade),
    INDEX idx_requer_acao (requer_acao),
    INDEX idx_criado_em (criado_em),
    
    -- Chaves estrangeiras
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
    FOREIGN KEY (resolvido_por) REFERENCES usuarios(id) ON DELETE SET NULL
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Tabela para controle de alertas e pendências do sistema de ponto';

-- ========================================
-- 2. CRIAR TABELA HISTORICO_INFERENCIAS
-- ========================================

CREATE TABLE IF NOT EXISTS historico_inferencias (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    funcionario_id INT UNSIGNED NOT NULL,
    data_referencia DATE NOT NULL,
    batidas_originais JSON NOT NULL COMMENT 'Batidas originais antes da inferência',
    batidas_inferidas JSON NOT NULL COMMENT 'Batidas após inferência',
    turno_determinado VARCHAR(20) NOT NULL,
    alertas_gerados JSON NULL COMMENT 'Alertas gerados durante a inferência',
    processado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices
    INDEX idx_funcionario_data (funcionario_id, data_referencia),
    INDEX idx_turno (turno_determinado),
    INDEX idx_processado (processado_em),
    
    -- Chave estrangeira
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
    
    -- Evitar duplicatas
    UNIQUE KEY unique_funcionario_data (funcionario_id, data_referencia)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Histórico de inferências realizadas pelo sistema';

-- ========================================
-- 3. CRIAR VIEWS PARA CONSULTAS FACILITADAS
-- ========================================

-- View para alertas pendentes
CREATE OR REPLACE VIEW v_alertas_pendentes AS
SELECT
    a.id,
    a.funcionario_id,
    f.nome_completo as funcionario_nome,
    COALESCE(f.matricula_empresa, f.id) as matricula,
    a.data_referencia,
    a.tipo_alerta,
    a.descricao,
    a.prioridade,
    a.requer_acao,
    a.criado_em,
    DATEDIFF(CURDATE(), a.data_referencia) as dias_pendente
FROM alertas_ponto a
JOIN funcionarios f ON a.funcionario_id = f.id
WHERE a.status_alerta = 'pendente'
ORDER BY a.prioridade DESC, a.criado_em ASC;

-- View para estatísticas de alertas
CREATE OR REPLACE VIEW v_estatisticas_alertas AS
SELECT 
    tipo_alerta,
    COUNT(*) as total,
    SUM(CASE WHEN status_alerta = 'pendente' THEN 1 ELSE 0 END) as pendentes,
    SUM(CASE WHEN status_alerta = 'resolvido' THEN 1 ELSE 0 END) as resolvidos,
    SUM(CASE WHEN requer_acao = TRUE THEN 1 ELSE 0 END) as requer_acao,
    AVG(CASE 
        WHEN status_alerta = 'resolvido' AND data_resolucao IS NOT NULL 
        THEN TIMESTAMPDIFF(HOUR, criado_em, data_resolucao) 
        ELSE NULL 
    END) as tempo_medio_resolucao_horas
FROM alertas_ponto
GROUP BY tipo_alerta
ORDER BY total DESC;

-- ========================================
-- 4. INSERIR DADOS DE TESTE (OPCIONAL)
-- ========================================

-- Comentar as linhas abaixo em produção
/*
INSERT INTO alertas_ponto (
    funcionario_id, data_referencia, tipo_alerta, descricao, 
    prioridade, requer_acao, dados_contexto
) VALUES 
(1, CURDATE(), 'jornada_incompleta', 'Funcionário registrou apenas a entrada', 'alta', TRUE, 
 JSON_OBJECT('num_batidas', 1, 'primeira_batida', '08:00:00')),
(1, DATE_SUB(CURDATE(), INTERVAL 1 DAY), 'intervalo_obrigatorio', 'Jornada de 8.5h sem intervalo registrado', 'critica', TRUE,
 JSON_OBJECT('horas_trabalhadas', 8.5, 'batidas', 2));
*/

-- ========================================
-- 5. TESTAR ESTRUTURA
-- ========================================

-- Verificar tabelas criadas
SHOW TABLES LIKE '%alerta%';
SHOW TABLES LIKE '%inferencia%';

-- Verificar estrutura da tabela alertas_ponto
DESCRIBE alertas_ponto;

-- Testar views
SELECT 'Alertas Pendentes' as consulta, COUNT(*) as total FROM v_alertas_pendentes
UNION ALL
SELECT 'Estatísticas Alertas' as consulta, COUNT(*) as total FROM v_estatisticas_alertas;

-- ========================================
-- 6. FUNÇÕES AUXILIARES
-- ========================================

DELIMITER //

-- Função para calcular prioridade automática
DROP FUNCTION IF EXISTS calcular_prioridade_alerta//

CREATE FUNCTION calcular_prioridade_alerta(
    tipo_alerta VARCHAR(50),
    dias_pendente INT,
    requer_acao BOOLEAN
)
RETURNS VARCHAR(10)
READS SQL DATA
DETERMINISTIC
COMMENT 'Calcula prioridade do alerta baseado no tipo e tempo pendente'
BEGIN
    DECLARE prioridade VARCHAR(10) DEFAULT 'media';
    
    -- Alertas críticos por natureza
    IF tipo_alerta IN ('intervalo_obrigatorio', 'jornada_longa_sem_intervalo') THEN
        SET prioridade = 'critica';
    
    -- Alertas de alta prioridade
    ELSEIF tipo_alerta IN ('jornada_incompleta', 'sequencia_invalida') THEN
        SET prioridade = 'alta';
    
    -- Alertas que requerem ação
    ELSEIF requer_acao = TRUE THEN
        SET prioridade = 'alta';
    
    -- Alertas antigos ganham prioridade
    ELSEIF dias_pendente > 7 THEN
        SET prioridade = 'alta';
    ELSEIF dias_pendente > 3 THEN
        SET prioridade = 'media';
    ELSE
        SET prioridade = 'baixa';
    END IF;
    
    RETURN prioridade;
END//

DELIMITER ;

-- Testar função
SELECT 
    calcular_prioridade_alerta('jornada_incompleta', 1, TRUE) as teste1,
    calcular_prioridade_alerta('batidas_extras', 5, FALSE) as teste2,
    calcular_prioridade_alerta('intervalo_obrigatorio', 0, TRUE) as teste3;

-- ========================================
-- FIM DO SCRIPT
-- ========================================
