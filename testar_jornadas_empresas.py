#!/usr/bin/env python3
"""
Script de teste para jornadas dinâmicas nas empresas
Sistema RLPONTO-WEB
Data: 05/07/2025
"""

import requests
import json

def testar_jornadas_empresas():
    """Testar funcionalidades de jornadas dinâmicas nas empresas"""
    
    base_url = "http://************:5000"
    
    print("🧪 Iniciando testes das jornadas dinâmicas nas empresas...")
    
    # 1. Testar página de empresas
    print("\n📋 1. Testando página de empresas...")
    try:
        response = requests.get(f"{base_url}/empresas/")
        if response.status_code == 200:
            print("   ✅ Página de empresas carregada com sucesso")
        else:
            print(f"   ❌ Erro ao carregar página: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erro de conexão: {e}")
    
    # 2. Testar página de detalhes de uma empresa (assumindo ID 1)
    print("\n🏢 2. Testando página de detalhes da empresa...")
    try:
        response = requests.get(f"{base_url}/empresas/1")
        if response.status_code == 200:
            print("   ✅ Página de detalhes da empresa carregada com sucesso")
            # Verificar se contém elementos de jornadas
            if "Jornadas de Trabalho" in response.text:
                print("   ✅ Seção de jornadas encontrada na página")
            if "Criar Jornada" in response.text:
                print("   ✅ Botão 'Criar Jornada' encontrado")
            if "excluirJornada" in response.text:
                print("   ✅ Função JavaScript de exclusão encontrada")
        else:
            print(f"   ❌ Erro ao carregar página: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erro de conexão: {e}")
    
    # 3. Testar API de jornadas de uma empresa
    print("\n🔗 3. Testando API de jornadas da empresa...")
    try:
        response = requests.get(f"{base_url}/empresas/api/jornadas/1")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                jornadas = data.get('jornadas', [])
                print(f"   ✅ API funcionando - {len(jornadas)} jornadas encontradas")
                for jornada in jornadas[:3]:  # Mostrar apenas as 3 primeiras
                    padrao = "PADRÃO" if jornada.get('padrao') else "NORMAL"
                    print(f"      - {jornada.get('nome_jornada', 'N/A')} ({padrao})")
            else:
                print("   ❌ API retornou erro")
        else:
            print(f"   ❌ Erro na API: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erro de conexão: {e}")
    
    # 4. Testar formulário de cadastro de jornada
    print("\n➕ 4. Testando formulário de cadastro de jornada...")
    try:
        response = requests.get(f"{base_url}/empresas/1/jornadas/cadastrar")
        if response.status_code == 200:
            print("   ✅ Formulário de cadastro carregado com sucesso")
        else:
            print(f"   ❌ Erro ao carregar formulário: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erro de conexão: {e}")
    
    # 5. Testar validações de exclusão (simulação com ID inexistente)
    print("\n🗑️ 5. Testando validações de exclusão...")
    try:
        # Tentar excluir jornada inexistente
        response = requests.post(
            f"{base_url}/empresas/1/jornadas/99999/excluir",
            json={}
        )
        if response.status_code == 200:
            data = response.json()
            if not data.get('success'):
                print("   ✅ Validação de jornada inexistente funcionando")
                print(f"      Mensagem: {data.get('message', 'N/A')}")
            else:
                print("   ⚠️  Validação não funcionou como esperado")
        else:
            print(f"   ❌ Erro HTTP: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erro de conexão: {e}")
    
    print("\n🎉 Testes concluídos!")
    print("\n📋 Resumo das funcionalidades implementadas:")
    print("   ✅ Endpoint de exclusão de jornadas com validações")
    print("   ✅ Interface melhorada com botão 'Criar Jornada' dinâmico")
    print("   ✅ Botões de exclusão com proteção para jornada padrão")
    print("   ✅ Validações de segurança implementadas")
    print("   ✅ CSS melhorado com animações")
    
    print("\n🔒 Validações de segurança implementadas:")
    print("   - Jornada padrão não pode ser excluída")
    print("   - Verificação de funcionários usando a jornada")
    print("   - Empresa deve ter pelo menos uma jornada ativa")
    print("   - Soft delete (marca como inativa)")
    
    print("\n🎨 Melhorias na interface:")
    print("   - Botão 'Criar Jornada' com animação pulse")
    print("   - Botões de exclusão apenas para jornadas não-padrão")
    print("   - Badge 'Protegida' para jornadas padrão")
    print("   - CSS melhorado com gradientes e animações")
    
    print("\n🏢 Localização correta:")
    print("   - Implementado em /empresas/ (local correto)")
    print("   - Cada empresa gerencia suas próprias jornadas")
    print("   - Removido da área de empresa principal (estava errado)")

if __name__ == "__main__":
    testar_jornadas_empresas()
