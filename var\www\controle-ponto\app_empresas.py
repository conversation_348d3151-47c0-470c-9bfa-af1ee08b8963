# ========================================
# BLUEPRINT EMPRESAS - RLPONTO-WEB
# Data: 25/06/2025
# Descrição: Gestão de empresas e jornadas de trabalho
# ========================================

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from datetime import datetime, time
from utils.auth import require_admin
from utils.database import DatabaseManager
import logging
import re
import os
from werkzeug.utils import secure_filename

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Criar Blueprint
empresas_bp = Blueprint('empresas', __name__, url_prefix='/empresas')

# Configurações de upload
UPLOAD_FOLDER = 'static/logos_empresas'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'svg'}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

# ========================================
# FUNÇÕES AUXILIARES
# ========================================

def allowed_file(filename):
    """Verifica se o arquivo é permitido"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validar_cnpj(cnpj):
    """Validação básica de CNPJ"""
    if not cnpj:
        return False
    # Remove caracteres especiais
    cnpj = re.sub(r'[^0-9]', '', cnpj)
    return len(cnpj) == 14

def formatar_cnpj(cnpj):
    """Formata CNPJ"""
    if not cnpj:
        return ''
    cnpj = re.sub(r'[^0-9]', '', cnpj)
    if len(cnpj) == 14:
        return f"{cnpj[:2]}.{cnpj[2:5]}.{cnpj[5:8]}/{cnpj[8:12]}-{cnpj[12:]}"
    return cnpj

def registrar_log_exclusao(empresa_id, razao_social, cnpj, usuario_id, motivo="Exclusão de empresa de teste"):
    """Registra log de exclusão de empresa"""
    try:
        sql = """
        INSERT INTO log_exclusao_empresas (
            empresa_id, razao_social, cnpj, usuario_id, 
            data_exclusao, motivo
        ) VALUES (%s, %s, %s, %s, NOW(), %s)
        """
        
        params = (empresa_id, razao_social, cnpj, usuario_id, motivo)
        
        # Verificar se a tabela existe, se não, criar
        try:
            DatabaseManager.execute_query("""
            CREATE TABLE IF NOT EXISTS log_exclusao_empresas (
                id INT AUTO_INCREMENT PRIMARY KEY,
                empresa_id INT NOT NULL,
                razao_social VARCHAR(200) NOT NULL,
                cnpj VARCHAR(18) NOT NULL,
                usuario_id INT NOT NULL,
                data_exclusao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                motivo TEXT
            ) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
            """)
        except Exception as e:
            logger.error(f"Erro ao criar tabela de log: {e}")
        
        DatabaseManager.execute_query(sql, params)
        logger.info(f"Log de exclusão registrado para empresa ID {empresa_id}")
        return True
    except Exception as e:
        logger.error(f"Erro ao registrar log de exclusão: {e}")
        return False

# ========================================
# ROTAS PRINCIPAIS
# ========================================

@empresas_bp.route('/')
@require_admin
def index():
    """Lista todas as empresas"""
    try:
        sql = """
        SELECT 
            e.id, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, 
            e.email, e.ativa, e.data_cadastro, e.empresa_teste,
            COUNT(j.id) as total_jornadas,
            COUNT(f.id) as total_funcionarios
        FROM empresas e
        LEFT JOIN jornadas_trabalho j ON e.id = j.empresa_id AND j.ativa = TRUE
        LEFT JOIN funcionarios f ON e.id = f.empresa_id AND f.status_cadastro = 'Ativo'
        GROUP BY e.id, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email, e.ativa, e.data_cadastro, e.empresa_teste
        ORDER BY e.ativa DESC, e.razao_social
        """
        
        empresas = DatabaseManager.execute_query(sql, fetch_all=True)
        
        return render_template('empresas/index.html', empresas=empresas)
        
    except Exception as e:
        logger.error(f"Erro ao listar empresas: {e}")
        flash('Erro ao carregar lista de empresas', 'error')
        return redirect(url_for('configuracoes.index'))

@empresas_bp.route('/cadastrar')
@require_admin
def cadastrar():
    """Formulário de cadastro de empresa"""
    return render_template('empresas/cadastrar.html')

@empresas_bp.route('/cadastrar', methods=['POST'])
@require_admin
def cadastrar_post():
    """Processa cadastro de empresa"""
    try:
        # Coletar dados do formulário
        dados = {
            'razao_social': request.form.get('razao_social', '').strip(),
            'nome_fantasia': request.form.get('nome_fantasia', '').strip(),
            'cnpj': request.form.get('cnpj', '').strip(),
            'telefone': request.form.get('telefone', '').strip(),
            'email': request.form.get('email', '').strip(),
            'ativa': 1 if request.form.get('ativa') == 'on' else 0,
            'empresa_teste': 1 if request.form.get('empresa_teste') == 'on' else 0
        }
        
        # Validações
        erros = []
        
        if not dados['razao_social']:
            erros.append('Razão social é obrigatória')
        
        if not dados['cnpj']:
            erros.append('CNPJ é obrigatório')
        elif not validar_cnpj(dados['cnpj']):
            erros.append('CNPJ inválido')
        
        if erros:
            for erro in erros:
                flash(erro, 'error')
            return render_template('empresas/cadastrar.html', dados=dados)
        
        # Formatar CNPJ
        dados['cnpj'] = formatar_cnpj(dados['cnpj'])
        
        # Inserir no banco
        sql = """
        INSERT INTO empresas (razao_social, nome_fantasia, cnpj, telefone, email, ativa, empresa_teste)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            dados['razao_social'], dados['nome_fantasia'], dados['cnpj'],
            dados['telefone'], dados['email'], dados['ativa'], dados['empresa_teste']
        )
        
        empresa_id = DatabaseManager.execute_query(sql, params, fetch_all=False)
        
        if empresa_id:
            flash('Empresa cadastrada com sucesso!', 'success')
            return redirect(url_for('empresas.detalhes', id=empresa_id))
        else:
            flash('Erro ao cadastrar empresa', 'error')
            return render_template('empresas/cadastrar.html', dados=dados)
            
    except Exception as e:
        logger.error(f"Erro ao cadastrar empresa: {e}")
        flash('Erro interno do servidor', 'error')
        return render_template('empresas/cadastrar.html', dados=dados)

@empresas_bp.route('/<int:id>')
@require_admin
def detalhes(id):
    """Detalhes de uma empresa"""
    try:
        # Buscar dados da empresa
        sql_empresa = """
        SELECT * FROM empresas WHERE id = %s
        """
        empresa = DatabaseManager.execute_query(sql_empresa, (id,), fetch_one=True)
        
        if not empresa:
            flash('Empresa não encontrada', 'error')
            return redirect(url_for('empresas.index'))
        
        # Buscar jornadas da empresa
        sql_jornadas = """
        SELECT 
            j.*, 
            COUNT(f.id) as total_funcionarios
        FROM jornadas_trabalho j
        LEFT JOIN funcionarios f ON j.id = f.jornada_trabalho_id AND f.status_cadastro = 'Ativo'
        WHERE j.empresa_id = %s
        GROUP BY j.id
        ORDER BY j.padrao DESC, j.nome_jornada
        """
        jornadas = DatabaseManager.execute_query(sql_jornadas, (id,), fetch_all=True)
        
        # Verificar se a empresa tem funcionários
        sql_funcionarios = """
        SELECT COUNT(id) as total FROM funcionarios 
        WHERE empresa_id = %s AND status_cadastro = 'Ativo'
        """
        result = DatabaseManager.execute_query(sql_funcionarios, (id,), fetch_one=True)
        total_funcionarios = result['total'] if result else 0
        
        return render_template('empresas/detalhes.html', 
                              empresa=empresa, 
                              jornadas=jornadas,
                              total_funcionarios=total_funcionarios)
        
    except Exception as e:
        logger.error(f"Erro ao buscar detalhes da empresa: {e}")
        flash('Erro ao carregar detalhes da empresa', 'error')
        return redirect(url_for('empresas.index'))

@empresas_bp.route('/<int:id>/excluir', methods=['POST'])
@require_admin
def excluir(id):
    """Exclui ou inativa uma empresa"""
    try:
        # Verificar se a empresa existe
        sql_empresa = "SELECT * FROM empresas WHERE id = %s"
        empresa = DatabaseManager.execute_query(sql_empresa, (id,), fetch_one=True)
        
        if not empresa:
            flash('Empresa não encontrada', 'error')
            return redirect(url_for('empresas.index'))
        
        # Verificar se tem funcionários
        sql_funcionarios = """
        SELECT COUNT(id) as total FROM funcionarios 
        WHERE empresa_id = %s AND status_cadastro = 'Ativo'
        """
        result = DatabaseManager.execute_query(sql_funcionarios, (id,), fetch_one=True)
        total_funcionarios = result['total'] if result else 0
        
        # Verificar se é empresa de teste
        if empresa['empresa_teste'] and total_funcionarios == 0:
            # É empresa de teste e não tem funcionários: exclusão física
            
            # 1. Registrar log de exclusão
            from flask import session
            usuario_id = session.get('user_id', 0)
            registrar_log_exclusao(
                empresa_id=id,
                razao_social=empresa['razao_social'],
                cnpj=empresa['cnpj'],
                usuario_id=usuario_id,
                motivo=f"Exclusão física de empresa de teste (ID: {id})"
            )
            
            # 2. Excluir jornadas (cascade já faria isso, mas por garantia)
            sql_delete_jornadas = "DELETE FROM jornadas_trabalho WHERE empresa_id = %s"
            DatabaseManager.execute_query(sql_delete_jornadas, (id,), fetch_all=False)
            
            # 3. Excluir a empresa
            sql_delete = "DELETE FROM empresas WHERE id = %s"
            DatabaseManager.execute_query(sql_delete, (id,), fetch_all=False)
            
            flash('Empresa de teste excluída com sucesso!', 'success')
            return redirect(url_for('empresas.index'))
        else:
            # É empresa real ou tem funcionários: apenas inativar
            sql_inativar = "UPDATE empresas SET ativa = 0 WHERE id = %s"
            DatabaseManager.execute_query(sql_inativar, (id,), fetch_all=False)
            
            if empresa['empresa_teste']:
                msg = 'Empresa de teste possui funcionários e não pode ser excluída. Foi inativada.'
            else:
                msg = 'Empresa real não pode ser excluída. Foi inativada para preservar histórico.'
                
            flash(msg, 'warning')
            return redirect(url_for('empresas.detalhes', id=id))
            
    except Exception as e:
        logger.error(f"Erro ao excluir empresa: {e}")
        flash('Erro ao processar exclusão da empresa', 'error')
        return redirect(url_for('empresas.detalhes', id=id))

# ========================================
# ROTAS DE JORNADAS
# ========================================

@empresas_bp.route('/<int:empresa_id>/jornadas/cadastrar')
@require_admin
def cadastrar_jornada(empresa_id):
    """Formulário de cadastro de jornada"""
    try:
        # Verificar se empresa existe
        sql = "SELECT id, razao_social FROM empresas WHERE id = %s"
        empresa = DatabaseManager.execute_query(sql, (empresa_id,), fetch_one=True)
        
        if not empresa:
            flash('Empresa não encontrada', 'error')
            return redirect(url_for('empresas.index'))
        
        return render_template('empresas/cadastrar_jornada.html', empresa=empresa)
        
    except Exception as e:
        logger.error(f"Erro ao carregar formulário de jornada: {e}")
        flash('Erro interno do servidor', 'error')
        return redirect(url_for('empresas.detalhes', id=empresa_id))

@empresas_bp.route('/<int:empresa_id>/jornadas/cadastrar', methods=['POST'])
@require_admin
def cadastrar_jornada_post(empresa_id):
    """Processa cadastro de jornada"""
    try:
        # Coletar dados do formulário
        dados = {
            'nome_jornada': request.form.get('nome_jornada', '').strip(),
            'tipo_jornada': request.form.get('tipo_jornada', 'Diurno'),
            'categoria_funcionario': request.form.get('categoria_funcionario', '').strip(),
            'seg_qui_entrada': request.form.get('seg_qui_entrada'),
            'seg_qui_saida': request.form.get('seg_qui_saida'),
            'sexta_entrada': request.form.get('sexta_entrada'),
            'sexta_saida': request.form.get('sexta_saida'),
            'sabado_entrada': request.form.get('sabado_entrada'),
            'sabado_saida': request.form.get('sabado_saida'),
            'intervalo_inicio': request.form.get('intervalo_inicio'),
            'intervalo_fim': request.form.get('intervalo_fim'),
            'tolerancia_entrada_minutos': int(request.form.get('tolerancia_entrada_minutos', 15)),
            'ativa': 1 if request.form.get('ativa') == 'on' else 0,
            'padrao': 1 if request.form.get('padrao') == 'on' else 0
        }
        
        # Validações
        erros = []
        
        if not dados['nome_jornada']:
            erros.append('Nome da jornada é obrigatório')
        
        if not dados['seg_qui_entrada'] or not dados['seg_qui_saida']:
            erros.append('Horários de segunda a quinta são obrigatórios')
        
        if erros:
            for erro in erros:
                flash(erro, 'error')
            empresa = DatabaseManager.execute_query("SELECT id, razao_social FROM empresas WHERE id = %s", (empresa_id,), fetch_one=True)
            return render_template('empresas/cadastrar_jornada.html', empresa=empresa, dados=dados)
        
        # Se for padrão, remover padrão das outras jornadas
        if dados['padrao']:
            sql_remove_padrao = "UPDATE jornadas_trabalho SET padrao = 0 WHERE empresa_id = %s"
            DatabaseManager.execute_query(sql_remove_padrao, (empresa_id,), fetch_all=False)
        
        # Inserir jornada
        sql = """
        INSERT INTO jornadas_trabalho (
            empresa_id, nome_jornada, tipo_jornada, categoria_funcionario,
            seg_qui_entrada, seg_qui_saida, sexta_entrada, sexta_saida,
            sabado_entrada, sabado_saida, intervalo_inicio, intervalo_fim,
            tolerancia_entrada_minutos, ativa, padrao
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            empresa_id, dados['nome_jornada'], dados['tipo_jornada'], dados['categoria_funcionario'],
            dados['seg_qui_entrada'], dados['seg_qui_saida'], dados['sexta_entrada'], dados['sexta_saida'],
            dados['sabado_entrada'], dados['sabado_saida'], dados['intervalo_inicio'], dados['intervalo_fim'],
            dados['tolerancia_entrada_minutos'], dados['ativa'], dados['padrao']
        )
        
        jornada_id = DatabaseManager.execute_query(sql, params, fetch_all=False)
        
        if jornada_id:
            flash('Jornada cadastrada com sucesso!', 'success')
            return redirect(url_for('empresas.detalhes', id=empresa_id))
        else:
            flash('Erro ao cadastrar jornada', 'error')
            empresa = DatabaseManager.execute_query("SELECT id, razao_social FROM empresas WHERE id = %s", (empresa_id,), fetch_one=True)
            return render_template('empresas/cadastrar_jornada.html', empresa=empresa, dados=dados)
            
    except Exception as e:
        logger.error(f"Erro ao cadastrar jornada: {e}")
        flash('Erro interno do servidor', 'error')
        return redirect(url_for('empresas.detalhes', id=empresa_id))

@empresas_bp.route('/<int:empresa_id>/jornadas/<int:jornada_id>/excluir', methods=['POST'])
@require_admin
def excluir_jornada(empresa_id, jornada_id):
    """Excluir jornada com validações de segurança"""
    try:
        # 1. Verificar se a jornada existe e pertence à empresa
        sql_jornada = """
        SELECT id, empresa_id, nome_jornada, padrao, ativa
        FROM jornadas_trabalho
        WHERE id = %s AND empresa_id = %s
        """
        jornada = DatabaseManager.execute_query(sql_jornada, (jornada_id, empresa_id), fetch_one=True)

        if not jornada:
            return jsonify({'success': False, 'message': 'Jornada não encontrada'})

        # 2. VALIDAÇÃO: Não permitir exclusão de jornada padrão
        if jornada['padrao']:
            return jsonify({
                'success': False,
                'message': 'Não é possível excluir a jornada padrão. Defina outra jornada como padrão primeiro.'
            })

        # 3. VALIDAÇÃO: Verificar se há funcionários usando esta jornada
        sql_funcionarios = """
        SELECT COUNT(*) as total
        FROM funcionarios f
        WHERE f.jornada_trabalho_id = %s AND f.status_cadastro = 'Ativo'
        """
        result_func = DatabaseManager.execute_query(sql_funcionarios, (jornada_id,), fetch_one=True)
        funcionarios_usando = result_func['total'] if result_func else 0

        if funcionarios_usando > 0:
            return jsonify({
                'success': False,
                'message': f'Esta jornada está sendo usada por {funcionarios_usando} funcionário(s). Remova-os primeiro.'
            })

        # 4. VALIDAÇÃO: Verificar se é a última jornada ativa da empresa
        sql_jornadas_ativas = """
        SELECT COUNT(*) as total
        FROM jornadas_trabalho
        WHERE empresa_id = %s AND ativa = TRUE AND id != %s
        """
        result_ativas = DatabaseManager.execute_query(sql_jornadas_ativas, (empresa_id, jornada_id), fetch_one=True)
        jornadas_ativas = result_ativas['total'] if result_ativas else 0

        if jornadas_ativas == 0:
            return jsonify({
                'success': False,
                'message': 'A empresa deve ter pelo menos uma jornada ativa. Esta é a última jornada ativa.'
            })

        # 5. EXCLUSÃO: Marcar como inativa (soft delete) em vez de excluir
        sql_desativar = """
        UPDATE jornadas_trabalho
        SET ativa = FALSE,
            data_atualizacao = CURRENT_TIMESTAMP,
            atualizado_por = 'sistema_exclusao'
        WHERE id = %s
        """

        resultado = DatabaseManager.execute_query(sql_desativar, (jornada_id,), fetch_all=False)

        if resultado:
            return jsonify({
                'success': True,
                'message': f'Jornada "{jornada["nome_jornada"]}" excluída com sucesso'
            })
        else:
            return jsonify({'success': False, 'message': 'Erro ao excluir jornada'})

    except Exception as e:
        logger.error(f"Erro ao excluir jornada: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do servidor'})

# ========================================
# API ENDPOINTS
# ========================================

@empresas_bp.route('/api/jornadas/<int:empresa_id>')
def api_jornadas_empresa(empresa_id):
    """API para buscar jornadas de uma empresa"""
    try:
        sql = """
        SELECT id, nome_jornada, tipo_jornada, categoria_funcionario, ativa, padrao
        FROM jornadas_trabalho
        WHERE empresa_id = %s AND ativa = TRUE
        ORDER BY padrao DESC, nome_jornada
        """

        jornadas = DatabaseManager.execute_query(sql, (empresa_id,), fetch_all=True)

        return jsonify({
            'success': True,
            'jornadas': jornadas
        })

    except Exception as e:
        logger.error(f"Erro na API de jornadas: {e}")
        return jsonify({
            'success': False,
            'error': 'Erro interno do servidor'
        }), 500
