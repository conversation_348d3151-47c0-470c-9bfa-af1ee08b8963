{% extends "base.html" %}

{% block title %}Detalhes do Funcionário Desligado - {{ funcionario.nome_completo }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/relatorios.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='style-cadastrar.css') }}">
<style>
    .detalhes-header {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 2rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
    }

    .funcionario-info {
        display: grid;
        grid-template-columns: auto 1fr auto;
        gap: 1.5rem;
        align-items: center;
    }

    .funcionario-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
    }

    .funcionario-dados h1 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 600;
    }

    .funcionario-dados p {
        margin: 0.3rem 0 0 0;
        opacity: 0.9;
    }

    .status-badge {
        padding: 0.8rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        background: rgba(255,255,255,0.2);
        border: 2px solid rgba(255,255,255,0.3);
    }

    .info-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .info-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .info-card-header {
        background: #f8f9fa;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-card-body {
        padding: 1.5rem;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f1f3f4;
    }

    .info-row:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
    }

    .info-value {
        color: #6c757d;
        text-align: right;
    }

    .historico-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
    }

    .historico-table th {
        background: #f8f9fa;
        padding: 0.8rem;
        text-align: left;
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
        font-size: 0.9rem;
    }

    .historico-table td {
        padding: 0.8rem;
        border-bottom: 1px solid #f1f3f4;
    }

    .historico-table tr:hover {
        background-color: #f8f9fa;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
    }

    .btn-primary {
        background: #28a745;
        border-color: #28a745;
        padding: 0.8rem 2rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: #218838;
        border-color: #218838;
        transform: translateY(-1px);
    }

    .btn-secondary {
        background: #6c757d;
        border-color: #6c757d;
        padding: 0.8rem 2rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: #545b62;
        border-color: #545b62;
        transform: translateY(-1px);
    }

    .empty-state {
        text-align: center;
        padding: 2rem;
        color: #6c757d;
    }

    .badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .badge-demissao { background: #ffeaa7; color: #d63031; }
    .badge-pedido { background: #74b9ff; color: #0984e3; }
    .badge-termino { background: #fd79a8; color: #e84393; }
    .badge-aposentadoria { background: #00b894; color: #00a085; }
    .badge-outros { background: #ddd; color: #636e72; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="detalhes-header">
        <div class="funcionario-info">
            <div class="funcionario-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="funcionario-dados">
                <h1>{{ funcionario.nome_completo }}</h1>
                <p><strong>Matrícula:</strong> {{ funcionario.matricula_empresa }} | <strong>CPF:</strong> {{ funcionario.cpf }}</p>
                <p><strong>Cargo:</strong> {{ funcionario.cargo }} | <strong>Setor:</strong> {{ funcionario.setor or 'Não informado' }}</p>
            </div>
            <div class="status-badge">
                <i class="fas fa-user-times"></i> Desligado
            </div>
        </div>
    </div>

    <!-- Cards de Informações -->
    <div class="info-cards">
        <!-- Dados Pessoais -->
        <div class="info-card">
            <div class="info-card-header">
                <i class="fas fa-user"></i> Dados Pessoais
            </div>
            <div class="info-card-body">
                <div class="info-row">
                    <span class="info-label">Nome Completo:</span>
                    <span class="info-value">{{ funcionario.nome_completo }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">CPF:</span>
                    <span class="info-value">{{ funcionario.cpf }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">RG:</span>
                    <span class="info-value">{{ funcionario.rg }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Data Nascimento:</span>
                    <span class="info-value">{{ funcionario.data_nascimento.strftime('%d/%m/%Y') if funcionario.data_nascimento else '-' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Sexo:</span>
                    <span class="info-value">{{ funcionario.sexo }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Estado Civil:</span>
                    <span class="info-value">{{ funcionario.estado_civil }}</span>
                </div>
            </div>
        </div>

        <!-- Dados Profissionais -->
        <div class="info-card">
            <div class="info-card-header">
                <i class="fas fa-briefcase"></i> Dados Profissionais
            </div>
            <div class="info-card-body">
                <div class="info-row">
                    <span class="info-label">Matrícula:</span>
                    <span class="info-value">{{ funcionario.matricula_empresa }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Cargo:</span>
                    <span class="info-value">{{ funcionario.cargo }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Setor:</span>
                    <span class="info-value">{{ funcionario.setor or 'Não informado' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Data Admissão:</span>
                    <span class="info-value">{{ funcionario.data_admissao.strftime('%d/%m/%Y') if funcionario.data_admissao else '-' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Tipo Contrato:</span>
                    <span class="info-value">{{ funcionario.tipo_contrato }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Nível Acesso:</span>
                    <span class="info-value">{{ funcionario.nivel_acesso }}</span>
                </div>
            </div>
        </div>

        <!-- Dados do Desligamento -->
        <div class="info-card">
            <div class="info-card-header">
                <i class="fas fa-calendar-times"></i> Dados do Desligamento
            </div>
            <div class="info-card-body">
                <div class="info-row">
                    <span class="info-label">Data Desligamento:</span>
                    <span class="info-value">{{ funcionario.data_desligamento.strftime('%d/%m/%Y às %H:%M') if funcionario.data_desligamento else '-' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Motivo:</span>
                    <span class="info-value">
                        {% set motivo_class = {
                            'Demissao_sem_justa_causa': 'badge-demissao',
                            'Demissao_com_justa_causa': 'badge-demissao', 
                            'Pedido_demissao': 'badge-pedido',
                            'Termino_contrato': 'badge-termino',
                            'Aposentadoria': 'badge-aposentadoria'
                        }.get(funcionario.motivo_desligamento, 'badge-outros') %}
                        <span class="badge {{ motivo_class }}">
                            {{ funcionario.motivo_desligamento.replace('_', ' ').title() }}
                        </span>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">Responsável:</span>
                    <span class="info-value">{{ funcionario.usuario_responsavel or 'Sistema' }}</span>
                </div>
                {% if funcionario.observacoes_desligamento %}
                <div class="info-row">
                    <span class="info-label">Observações:</span>
                    <span class="info-value">{{ funcionario.observacoes_desligamento }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Estatísticas -->
        <div class="info-card">
            <div class="info-card-header">
                <i class="fas fa-chart-bar"></i> Estatísticas
            </div>
            <div class="info-card-body">
                <div class="info-row">
                    <span class="info-label">Registros de Ponto:</span>
                    <span class="info-value">{{ estatisticas.total_pontos or 0 }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">EPIs Recebidos:</span>
                    <span class="info-value">{{ estatisticas.total_epis or 0 }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Banco de Horas:</span>
                    <span class="info-value">{{ estatisticas.total_banco_horas or 0 }} registros</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Tempo na Empresa:</span>
                    <span class="info-value">{{ estatisticas.tempo_empresa or 'Não calculado' }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Histórico Recente -->
    <div class="info-card">
        <div class="info-card-header">
            <i class="fas fa-history"></i> Últimos Registros de Ponto
        </div>
        <div class="info-card-body">
            {% if registros_ponto %}
            <table class="historico-table">
                <thead>
                    <tr>
                        <th>Data</th>
                        <th>Tipo</th>
                        <th>Horário</th>
                        <th>Método</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {% for registro in registros_ponto %}
                    <tr>
                        <td>{{ registro.data_hora.strftime('%d/%m/%Y') if registro.data_hora else '-' }}</td>
                        <td>{{ registro.tipo_registro.replace('_', ' ').title() }}</td>
                        <td>{{ registro.data_hora.strftime('%H:%M') if registro.data_hora else '-' }}</td>
                        <td>{{ registro.metodo_registro.title() }}</td>
                        <td>{{ registro.status_pontualidade or 'Normal' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <div class="empty-state">
                <i class="fas fa-clock"></i>
                <p>Nenhum registro de ponto encontrado</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Botões de Ação -->
    <div class="action-buttons">
        <button class="btn btn-primary" onclick="restaurarFuncionario({{ funcionario.funcionario_id_original }}, '{{ funcionario.nome_completo }}')">
            <i class="fas fa-undo"></i> Restaurar Funcionário
        </button>
        <a href="/funcionarios-desligados" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar à Lista
        </a>
    </div>
</div>

<script>
function restaurarFuncionario(funcionarioId, nome) {
    if (confirm(`Tem certeza que deseja restaurar o funcionário ${nome}?\n\nEsta ação irá:\n• Reativar o funcionário\n• Restaurar todos os dados históricos\n• Remover da lista de desligados`)) {
        fetch(`/funcionarios/restaurar/${funcionarioId}`, {
            method: 'POST',
            credentials: 'same-origin'
        })
        .then(response => {
            if (response.ok) {
                alert('Funcionário restaurado com sucesso!');
                window.location.href = '/funcionarios-desligados';
            } else {
                alert('Erro ao restaurar funcionário. Tente novamente.');
            }
        })
        .catch(error => {
            alert('Erro na requisição: ' + error.message);
        });
    }
}
</script>
{% endblock %}
