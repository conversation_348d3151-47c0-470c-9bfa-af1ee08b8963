#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de diagnóstico para verificar rotas do blueprint empresa_principal
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def debug_empresa_principal_routes():
    """Diagnosticar rotas do blueprint empresa_principal"""
    try:
        print("=== DIAGNÓSTICO ROTAS EMPRESA PRINCIPAL ===")
        
        # Tentar importar o blueprint
        print("\n1. Testando importação do blueprint...")
        try:
            from app_empresa_principal import empresa_principal_bp
            print("✅ Blueprint empresa_principal importado com sucesso")
            print(f"   - Nome: {empresa_principal_bp.name}")
            print(f"   - URL Prefix: {empresa_principal_bp.url_prefix}")
        except Exception as e:
            print(f"❌ Erro ao importar blueprint: {e}")
            return False
        
        # Listar todas as rotas do blueprint
        print("\n2. Listando rotas do blueprint...")
        try:
            for rule in empresa_principal_bp.url_map.iter_rules():
                print(f"   - {rule.rule} -> {rule.endpoint}")
        except Exception as e:
            print(f"❌ Erro ao listar rotas: {e}")
        
        # Tentar criar uma aplicação Flask de teste
        print("\n3. Testando registro do blueprint...")
        try:
            from flask import Flask
            test_app = Flask(__name__)
            test_app.register_blueprint(empresa_principal_bp)
            
            with test_app.app_context():
                # Listar todas as rotas registradas
                print("   Rotas registradas:")
                for rule in test_app.url_map.iter_rules():
                    if 'empresa_principal' in rule.endpoint:
                        print(f"   - {rule.rule} -> {rule.endpoint}")
                
                # Testar url_for específico
                from flask import url_for
                try:
                    url_alocacoes = url_for('empresa_principal.alocacoes')
                    print(f"✅ URL para alocações: {url_alocacoes}")
                except Exception as e:
                    print(f"❌ Erro ao gerar URL para alocações: {e}")
                    
        except Exception as e:
            print(f"❌ Erro ao testar registro: {e}")
        
        # Verificar se a função alocacoes existe
        print("\n4. Verificando função alocacoes...")
        try:
            import app_empresa_principal
            if hasattr(app_empresa_principal, 'alocacoes'):
                print("✅ Função alocacoes encontrada")
            else:
                print("❌ Função alocacoes não encontrada")
        except Exception as e:
            print(f"❌ Erro ao verificar função: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro geral no diagnóstico: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def debug_main_app():
    """Diagnosticar aplicação principal"""
    try:
        print("\n=== DIAGNÓSTICO APLICAÇÃO PRINCIPAL ===")
        
        # Verificar se o app principal está funcionando
        print("\n1. Testando importação do app principal...")
        try:
            import app
            print("✅ App principal importado")
            
            # Verificar se o blueprint está registrado
            if hasattr(app, 'app'):
                print("✅ Objeto Flask encontrado")
                
                # Listar blueprints registrados
                print("\n2. Blueprints registrados:")
                for blueprint_name, blueprint in app.app.blueprints.items():
                    print(f"   - {blueprint_name}: {blueprint.url_prefix}")
                
                # Verificar rotas específicas
                print("\n3. Rotas empresa_principal:")
                for rule in app.app.url_map.iter_rules():
                    if 'empresa_principal' in rule.endpoint:
                        print(f"   - {rule.rule} -> {rule.endpoint}")
                        
            else:
                print("❌ Objeto Flask não encontrado")
                
        except Exception as e:
            print(f"❌ Erro ao importar app: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")

if __name__ == '__main__':
    print("Iniciando diagnóstico...")
    debug_empresa_principal_routes()
    debug_main_app()
    print("\nDiagnóstico concluído.")
