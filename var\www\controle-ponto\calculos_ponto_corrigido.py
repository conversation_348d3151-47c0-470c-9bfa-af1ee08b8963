# -*- coding: utf-8 -*-
"""
MÓDULO DE CÁLCULOS DE PONTO CORRIGIDO
====================================

Funções corrigidas para cálculo de horas trabalhadas, banco de horas e validações.
"""

from datetime import datetime, timedelta, time
import logging

logger = logging.getLogger('controle-ponto.calculos')

def calcular_horas_trabalhadas(entrada_manha, saida_almoco, entrada_tarde, saida):
    """
    FUNÇÃO CORRIGIDA: Calcula horas trabalhadas com base nos registros.
    
    Args:
        entrada_manha (time): Hor<PERSON><PERSON> de entrada da manhã
        saida_almoco (time): Hor<PERSON>rio de saída para almoço
        entrada_tarde (time): Hor<PERSON>rio de entrada da tarde
        saida (time): <PERSON><PERSON><PERSON><PERSON> de saída
    
    Returns:
        float: Total de horas trabalhadas
    """
    try:
        total_horas = 0.0
        
        # Validar se temos pelo menos entrada e saída
        if not entrada_manha or not saida:
            logger.warning("Entrada da manhã ou saída não informadas")
            return 0.0
        
        # Converter para datetime para facilitar cálculos
        hoje = datetime.now().date()
        dt_entrada_manha = datetime.combine(hoje, entrada_manha)
        dt_saida = datetime.combine(hoje, saida)
        
        # Caso 1: Jornada sem intervalo (entrada_manha -> saida)
        if not saida_almoco or not entrada_tarde:
            total_segundos = (dt_saida - dt_entrada_manha).total_seconds()
            total_horas = total_segundos / 3600
            logger.info(f"Jornada sem intervalo: {total_horas:.2f}h")
            return round(total_horas, 2)
        
        # Caso 2: Jornada com intervalo
        dt_saida_almoco = datetime.combine(hoje, saida_almoco)
        dt_entrada_tarde = datetime.combine(hoje, entrada_tarde)
        
        # Período da manhã
        periodo_manha = (dt_saida_almoco - dt_entrada_manha).total_seconds() / 3600
        
        # Período da tarde
        periodo_tarde = (dt_saida - dt_entrada_tarde).total_seconds() / 3600
        
        # Total
        total_horas = periodo_manha + periodo_tarde
        
        logger.info(f"Jornada com intervalo: Manhã {periodo_manha:.2f}h + Tarde {periodo_tarde:.2f}h = {total_horas:.2f}h")
        return round(total_horas, 2)
        
    except Exception as e:
        logger.error(f"Erro ao calcular horas trabalhadas: {e}")
        return 0.0

def calcular_banco_horas(horas_trabalhadas, horas_obrigatorias, saldo_anterior=0.0):
    """
    FUNÇÃO CORRIGIDA: Calcula saldo do banco de horas.
    
    Args:
        horas_trabalhadas (float): Horas trabalhadas no período
        horas_obrigatorias (float): Horas obrigatórias do período
        saldo_anterior (float): Saldo anterior do banco de horas
    
    Returns:
        dict: Informações do banco de horas
    """
    try:
        # Calcular diferença
        diferenca = horas_trabalhadas - horas_obrigatorias
        
        # Novo saldo
        novo_saldo = saldo_anterior + diferenca
        
        # Classificar tipo
        if diferenca > 0:
            tipo = 'credito'
            status = 'Horas extras'
        elif diferenca < 0:
            tipo = 'debito'
            status = 'Horas devidas'
        else:
            tipo = 'neutro'
            status = 'Em dia'
        
        resultado = {
            'horas_trabalhadas': round(horas_trabalhadas, 2),
            'horas_obrigatorias': round(horas_obrigatorias, 2),
            'diferenca': round(diferenca, 2),
            'saldo_anterior': round(saldo_anterior, 2),
            'novo_saldo': round(novo_saldo, 2),
            'tipo': tipo,
            'status': status
        }
        
        logger.info(f"Banco de horas calculado: {resultado}")
        return resultado
        
    except Exception as e:
        logger.error(f"Erro ao calcular banco de horas: {e}")
        return {
            'horas_trabalhadas': 0.0,
            'horas_obrigatorias': 0.0,
            'diferenca': 0.0,
            'saldo_anterior': 0.0,
            'novo_saldo': 0.0,
            'tipo': 'erro',
            'status': 'Erro no cálculo'
        }

def validar_horarios_jornada(entrada_manha, saida_almoco, entrada_tarde, saida):
    """
    FUNÇÃO NOVA: Valida se os horários estão em sequência lógica.
    
    Args:
        entrada_manha (time): Horário de entrada da manhã
        saida_almoco (time): Horário de saída para almoço
        entrada_tarde (time): Horário de entrada da tarde
        saida (time): Horário de saída
    
    Returns:
        tuple: (bool, list) - (é_válido, lista_de_erros)
    """
    erros = []
    
    try:
        # Validação 1: Entrada da manhã deve ser antes da saída
        if entrada_manha and saida and entrada_manha >= saida:
            erros.append("Entrada da manhã deve ser antes da saída")
        
        # Validação 2: Saída para almoço deve ser depois da entrada da manhã
        if entrada_manha and saida_almoco and saida_almoco <= entrada_manha:
            erros.append("Saída para almoço deve ser depois da entrada da manhã")
        
        # Validação 3: Entrada da tarde deve ser depois da saída para almoço
        if saida_almoco and entrada_tarde and entrada_tarde <= saida_almoco:
            erros.append("Entrada da tarde deve ser depois da saída para almoço")
        
        # Validação 4: Saída deve ser depois da entrada da tarde
        if entrada_tarde and saida and saida <= entrada_tarde:
            erros.append("Saída deve ser depois da entrada da tarde")
        
        # Validação 5: Intervalo mínimo de almoço (30 minutos)
        if saida_almoco and entrada_tarde:
            intervalo = datetime.combine(datetime.today(), entrada_tarde) - datetime.combine(datetime.today(), saida_almoco)
            if intervalo.total_seconds() < 1800:  # 30 minutos
                erros.append("Intervalo de almoço deve ser de pelo menos 30 minutos")
        
        return len(erros) == 0, erros
        
    except Exception as e:
        logger.error(f"Erro ao validar horários: {e}")
        return False, [f"Erro na validação: {str(e)}"]

def calcular_horas_extras_b5_b6(inicio_extra, fim_extra):
    """
    FUNÇÃO CORRIGIDA: Calcula horas extras B5/B6.
    
    Args:
        inicio_extra (time): Horário de início das horas extras
        fim_extra (time): Horário de fim das horas extras
    
    Returns:
        float: Total de horas extras
    """
    try:
        if not inicio_extra or not fim_extra:
            return 0.0
        
        # Validar sequência
        if inicio_extra >= fim_extra:
            logger.warning("Início das horas extras deve ser antes do fim")
            return 0.0
        
        # Calcular diferença
        hoje = datetime.now().date()
        dt_inicio = datetime.combine(hoje, inicio_extra)
        dt_fim = datetime.combine(hoje, fim_extra)
        
        # Se fim for no dia seguinte (após meia-noite)
        if dt_fim < dt_inicio:
            dt_fim += timedelta(days=1)
        
        total_segundos = (dt_fim - dt_inicio).total_seconds()
        total_horas = total_segundos / 3600
        
        logger.info(f"Horas extras B5/B6: {total_horas:.2f}h")
        return round(total_horas, 2)
        
    except Exception as e:
        logger.error(f"Erro ao calcular horas extras B5/B6: {e}")
        return 0.0

def gerar_relatorio_horas(funcionario_id, data_inicio, data_fim):
    """
    FUNÇÃO NOVA: Gera relatório completo de horas para um período.
    
    Args:
        funcionario_id (int): ID do funcionário
        data_inicio (str): Data de início (YYYY-MM-DD)
        data_fim (str): Data de fim (YYYY-MM-DD)
    
    Returns:
        dict: Relatório completo de horas
    """
    try:
        # Esta função seria implementada com consultas ao banco
        # Por enquanto, retorna estrutura do relatório
        
        relatorio = {
            'funcionario_id': funcionario_id,
            'periodo': {
                'inicio': data_inicio,
                'fim': data_fim
            },
            'resumo': {
                'dias_trabalhados': 0,
                'total_horas_trabalhadas': 0.0,
                'total_horas_obrigatorias': 0.0,
                'total_horas_extras': 0.0,
                'saldo_banco_horas': 0.0
            },
            'detalhes_diarios': [],
            'observacoes': []
        }
        
        logger.info(f"Relatório de horas gerado para funcionário {funcionario_id}")
        return relatorio
        
    except Exception as e:
        logger.error(f"Erro ao gerar relatório de horas: {e}")
        return None

# Função de teste para validar as correções
def testar_calculos():
    """
    Função para testar os cálculos corrigidos.
    """
    print("🧪 TESTANDO CÁLCULOS CORRIGIDOS")
    print("=" * 50)
    
    # Teste 1: Jornada normal
    entrada = time(8, 0)
    saida_almoco = time(12, 0)
    entrada_tarde = time(13, 0)
    saida = time(17, 0)
    
    horas = calcular_horas_trabalhadas(entrada, saida_almoco, entrada_tarde, saida)
    print(f"Teste 1 - Jornada normal: {horas}h (esperado: 8.0h)")
    
    # Teste 2: Banco de horas
    banco = calcular_banco_horas(8.5, 8.0, 2.0)
    print(f"Teste 2 - Banco de horas: {banco['novo_saldo']}h (esperado: 2.5h)")
    
    # Teste 3: Validação
    valido, erros = validar_horarios_jornada(entrada, saida_almoco, entrada_tarde, saida)
    print(f"Teste 3 - Validação: {valido} (esperado: True)")
    
    # Teste 4: Horas extras
    inicio_extra = time(17, 30)
    fim_extra = time(19, 0)
    extras = calcular_horas_extras_b5_b6(inicio_extra, fim_extra)
    print(f"Teste 4 - Horas extras: {extras}h (esperado: 1.5h)")
    
    print("=" * 50)
    print("✅ TESTES CONCLUÍDOS")

if __name__ == "__main__":
    testar_calculos()
