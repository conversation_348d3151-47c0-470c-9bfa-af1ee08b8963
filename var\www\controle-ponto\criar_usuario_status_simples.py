import pymysql

try:
    conn = pymysql.connect(
        host='************',
        user='cavalcrod',
        password='200381',
        database='controle_ponto',
        charset='utf8mb4'
    )
    
    cursor = conn.cursor()
    
    # Inserir usuário status
    cursor.execute("""
        INSERT IGNORE INTO usuarios (usuario, senha, nome_completo, email, ativo, nivel_acesso) 
        VALUES ('status', '12345678', 'Usuário Status do Sistema', '<EMAIL>', 1, 'usuario')
    """)
    
    conn.commit()
    print("✅ Usuário 'status' criado com sucesso!")
    print("📋 Credenciais:")
    print("   Usuário: status")
    print("   Senha: 12345678")
    
    conn.close()
    
except Exception as e:
    print(f"❌ Erro: {e}") 