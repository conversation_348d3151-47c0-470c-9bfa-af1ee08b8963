#!/usr/bin/env python3
import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import FuncionarioQueries
import traceback

print("=== DEBUG FUNÇÃO INDEX FUNCIONÁRIOS ===")

try:
    print("\n1. Testando FuncionarioQueries.get_all()...")
    
    result = FuncionarioQueries.get_all(page=1, per_page=10)
    
    print(f"✅ Resultado obtido: {type(result)}")
    print(f"   Chaves: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
    
    if isinstance(result, dict) and 'data' in result:
        print(f"   Total funcionários: {len(result['data'])}")
        if result['data']:
            print(f"   Primeiro funcionário: {result['data'][0].get('nome_completo', 'N/A')}")
    
except Exception as e:
    print(f"❌ ERRO: {e}")
    print(f"📋 Traceback:")
    print(traceback.format_exc())
