{% extends "base.html" %}

{% block title %}Relatórios - {{ empresa_principal.razao_social }}{% endblock %}

{% block extra_css %}
<style>
    .relatorio-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        background: white;
        cursor: pointer;
    }
    
    .relatorio-card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 15px rgba(0,123,255,0.1);
        transform: translateY(-2px);
    }
    
    .relatorio-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #007bff;
    }
    
    .stats-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .stats-box {
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        margin: 0 5px;
    }
    
    .filtros-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .btn-relatorio {
        width: 100%;
        height: 120px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 2px solid #e9ecef;
        background: white;
        transition: all 0.3s ease;
    }
    
    .btn-relatorio:hover {
        border-color: #007bff;
        background: #f8f9fa;
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header com Estatísticas -->
    <div class="stats-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-chart-bar"></i> Relatórios e Análises</h2>
                <p class="mb-0">Visão completa do sistema de empresa principal</p>
            </div>
            <div class="col-md-4">
                <div class="row">
                    <div class="col-4">
                        <div class="stats-box">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.total_clientes }}</div>
                            <small>Clientes</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-box">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.total_funcionarios }}</div>
                            <small>Funcionários</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-box">
                            <div style="font-size: 1.5rem; font-weight: bold;">{{ stats.total_alocacoes }}</div>
                            <small>Alocações</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navegação -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h4><i class="fas fa-file-alt"></i> Relatórios Disponíveis</h4>
        </div>
        <div class="col-md-6 text-right">
            <a href="{{ url_for('empresa_principal.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar ao Dashboard
            </a>
        </div>
    </div>

    <!-- Filtros Globais -->
    <div class="filtros-card">
        <h5><i class="fas fa-filter"></i> Filtros</h5>
        <div class="row">
            <div class="col-md-3">
                <label>Período:</label>
                <select class="form-control" id="filtroPeriodo" onchange="atualizarRelatorios()">
                    <option value="mes_atual">Mês Atual</option>
                    <option value="mes_anterior">Mês Anterior</option>
                    <option value="trimestre">Último Trimestre</option>
                    <option value="semestre">Último Semestre</option>
                    <option value="ano">Ano Atual</option>
                    <option value="personalizado">Personalizado</option>
                </select>
            </div>
            <div class="col-md-3">
                <label>Cliente:</label>
                <select class="form-control" id="filtroCliente" onchange="atualizarRelatorios()">
                    <option value="">Todos os clientes</option>
                    {% for cliente in clientes %}
                        <option value="{{ cliente.empresa_cliente_id }}">{{ cliente.razao_social }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label>Data Início:</label>
                <input type="date" class="form-control" id="dataInicio" onchange="atualizarRelatorios()">
            </div>
            <div class="col-md-3">
                <label>Data Fim:</label>
                <input type="date" class="form-control" id="dataFim" onchange="atualizarRelatorios()">
            </div>
        </div>
    </div>

    <!-- Relatórios Rápidos -->
    <div class="row mb-4">
        <div class="col-md-3">
            <button class="btn btn-relatorio" onclick="gerarRelatorio('clientes')">
                <i class="fas fa-building relatorio-icon"></i>
                <strong>Relatório de Clientes</strong>
                <small>Contratos e status</small>
            </button>
        </div>
        <div class="col-md-3">
            <button class="btn btn-relatorio" onclick="gerarRelatorio('alocacoes')">
                <i class="fas fa-users-cog relatorio-icon"></i>
                <strong>Alocações</strong>
                <small>Funcionários por cliente</small>
            </button>
        </div>
        <div class="col-md-3">
            <button class="btn btn-relatorio" onclick="gerarRelatorio('jornadas')">
                <i class="fas fa-clock relatorio-icon"></i>
                <strong>Jornadas</strong>
                <small>Horários por cliente</small>
            </button>
        </div>
        <div class="col-md-3">
            <button class="btn btn-relatorio" onclick="gerarRelatorio('financeiro')">
                <i class="fas fa-dollar-sign relatorio-icon"></i>
                <strong>Financeiro</strong>
                <small>Valores e custos</small>
            </button>
        </div>
    </div>

    <!-- Gráficos e Análises -->
    <div class="row">
        <!-- Gráfico de Clientes por Status -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-pie-chart"></i> Clientes por Status</h5>
                <canvas id="chartClientesStatus" width="400" height="200"></canvas>
            </div>
        </div>
        
        <!-- Gráfico de Funcionários por Cliente -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-bar-chart"></i> Funcionários por Cliente</h5>
                <canvas id="chartFuncionariosCliente" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Gráfico de Evolução Temporal -->
        <div class="col-md-12">
            <div class="chart-container">
                <h5><i class="fas fa-line-chart"></i> Evolução de Alocações</h5>
                <canvas id="chartEvolucao" width="800" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Tabelas de Dados -->
    <div class="row">
        <!-- Top Clientes -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-trophy"></i> Top Clientes por Funcionários</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Cliente</th>
                                    <th>Funcionários</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="topClientesTable">
                                {% for cliente in top_clientes %}
                                <tr>
                                    <td>{{ cliente.razao_social }}</td>
                                    <td><span class="badge badge-primary">{{ cliente.funcionarios_alocados }}</span></td>
                                    <td><span class="badge badge-{{ 'success' if cliente.status_contrato == 'ativo' else 'warning' }}">{{ cliente.status_contrato.title() }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Resumo Financeiro -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-money-bill-wave"></i> Resumo Financeiro</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-success">R$ {{ "%.2f"|format(resumo_financeiro.valor_total_contratos) }}</h4>
                                <small>Valor Total Contratos</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-info">R$ {{ "%.2f"|format(resumo_financeiro.valor_medio_hora) }}</h4>
                                <small>Valor Médio/Hora</small>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-warning">{{ resumo_financeiro.contratos_ativos }}</h4>
                                <small>Contratos Ativos</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-primary">{{ resumo_financeiro.horas_mes_estimadas }}</h4>
                                <small>Horas/Mês Estimadas</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alertas e Notificações -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle"></i> Alertas e Notificações</h5>
                </div>
                <div class="card-body">
                    <div id="alertasContainer">
                        {% if alertas %}
                            {% for alerta in alertas %}
                            <div class="alert alert-{{ alerta.tipo }} alert-dismissible fade show">
                                <strong>{{ alerta.titulo }}:</strong> {{ alerta.mensagem }}
                                <button type="button" class="close" data-dismiss="alert">
                                    <span>&times;</span>
                                </button>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> Nenhum alerta no momento. Sistema funcionando normalmente.
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Relatório Personalizado -->
<div class="modal fade" id="modalRelatorioPersonalizado" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-file-export"></i> Gerar Relatório Personalizado</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="formRelatorioPersonalizado">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Tipo de Relatório:</label>
                                <select class="form-control" name="tipo_relatorio" required>
                                    <option value="clientes">Relatório de Clientes</option>
                                    <option value="alocacoes">Relatório de Alocações</option>
                                    <option value="jornadas">Relatório de Jornadas</option>
                                    <option value="financeiro">Relatório Financeiro</option>
                                    <option value="completo">Relatório Completo</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Formato:</label>
                                <select class="form-control" name="formato" required>
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                    <option value="csv">CSV</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data Início:</label>
                                <input type="date" class="form-control" name="data_inicio" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data Fim:</label>
                                <input type="date" class="form-control" name="data_fim" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Clientes (opcional):</label>
                        <select class="form-control" name="clientes[]" multiple>
                            {% for cliente in clientes %}
                                <option value="{{ cliente.empresa_cliente_id }}">{{ cliente.razao_social }}</option>
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">Deixe vazio para incluir todos os clientes</small>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="incluir_graficos" id="incluirGraficos" checked>
                            <label class="form-check-label" for="incluirGraficos">
                                Incluir gráficos no relatório
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="enviar_email" id="enviarEmail">
                            <label class="form-check-label" for="enviarEmail">
                                Enviar por e-mail após geração
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="gerarRelatorioPersonalizado()">
                    <i class="fas fa-download"></i> Gerar Relatório
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Dados dos gráficos (vindos do backend)
const dadosGraficos = {{ dados_graficos|tojsonfilter }};

// Inicializar gráficos
document.addEventListener('DOMContentLoaded', function() {
    inicializarGraficos();
    configurarFiltros();
});

function inicializarGraficos() {
    // Gráfico de Clientes por Status
    const ctxStatus = document.getElementById('chartClientesStatus').getContext('2d');
    new Chart(ctxStatus, {
        type: 'doughnut',
        data: {
            labels: dadosGraficos.clientes_status.labels,
            datasets: [{
                data: dadosGraficos.clientes_status.data,
                backgroundColor: ['#28a745', '#ffc107', '#dc3545', '#6c757d']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // Gráfico de Funcionários por Cliente
    const ctxFuncionarios = document.getElementById('chartFuncionariosCliente').getContext('2d');
    new Chart(ctxFuncionarios, {
        type: 'bar',
        data: {
            labels: dadosGraficos.funcionarios_cliente.labels,
            datasets: [{
                label: 'Funcionários',
                data: dadosGraficos.funcionarios_cliente.data,
                backgroundColor: '#007bff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Gráfico de Evolução
    const ctxEvolucao = document.getElementById('chartEvolucao').getContext('2d');
    new Chart(ctxEvolucao, {
        type: 'line',
        data: {
            labels: dadosGraficos.evolucao.labels,
            datasets: [{
                label: 'Alocações Ativas',
                data: dadosGraficos.evolucao.data,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function configurarFiltros() {
    // Configurar datas padrão
    const hoje = new Date();
    const primeiroDiaMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
    
    document.getElementById('dataInicio').value = primeiroDiaMes.toISOString().split('T')[0];
    document.getElementById('dataFim').value = hoje.toISOString().split('T')[0];
}

function atualizarRelatorios() {
    const periodo = document.getElementById('filtroPeriodo').value;
    const cliente = document.getElementById('filtroCliente').value;
    const dataInicio = document.getElementById('dataInicio').value;
    const dataFim = document.getElementById('dataFim').value;
    
    // Atualizar gráficos com novos filtros
    fetch('{{ url_for("empresa_principal.dados_graficos") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            periodo: periodo,
            cliente: cliente,
            data_inicio: dataInicio,
            data_fim: dataFim
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Atualizar gráficos com novos dados
            atualizarGraficosComDados(data.dados);
        }
    })
    .catch(error => {
        console.error('Erro ao atualizar relatórios:', error);
    });
}

function gerarRelatorio(tipo) {
    // Configurar modal com tipo específico
    document.querySelector('#modalRelatorioPersonalizado select[name="tipo_relatorio"]').value = tipo;
    $('#modalRelatorioPersonalizado').modal('show');
}

function gerarRelatorioPersonalizado() {
    const formData = new FormData(document.getElementById('formRelatorioPersonalizado'));
    
    // Mostrar loading
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gerando...';
    btn.disabled = true;
    
    fetch('{{ url_for("empresa_principal.gerar_relatorio") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.blob())
    .then(blob => {
        // Download do arquivo
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'relatorio_empresa_principal.pdf';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        
        $('#modalRelatorioPersonalizado').modal('hide');
    })
    .catch(error => {
        console.error('Erro ao gerar relatório:', error);
        alert('Erro ao gerar relatório');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function atualizarGraficosComDados(dados) {
    // Implementar atualização dos gráficos
    console.log('Atualizando gráficos com:', dados);
}
</script>
{% endblock %}
