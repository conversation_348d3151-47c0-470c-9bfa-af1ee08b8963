#!/bin/bash

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_header() {
    echo -e "${GREEN}=== $1 ===${NC}"
}

print_subheader() {
    echo -e "${YELLOW}$1${NC}"
}

print_header "Verificação do Ambiente do Servidor"
echo "Data: $(date)"

print_header "Sistema Operacional"
cat /etc/os-release
echo -e "\nKernel: $(uname -r)"

print_header "Diretórios e Permissões"
print_subheader "Verificando /var/www/controle-ponto:"
ls -la /var/www/controle-ponto
print_subheader "\nVerificando /var/log/controle-ponto:"
ls -la /var/log/controle-ponto 2>/dev/null

print_header "Serviços Ativos"
systemctl list-units --type=service --state=active | grep -E 'biometria|apache|nginx|mysql'

print_header "Python e Pip"
print_subheader "Python Version:"
python3 --version
print_subheader "Pip Version:"
pip3 --version
print_subheader "Pacotes Python Instalados:"
pip3 list

print_header "Portas em Uso"
netstat -tulpn | grep -E ':80|:443|:8765|:3306'

print_header "Firewall Status"
if command -v ufw &>/dev/null; then
    ufw status
else
    echo "UFW não instalado"
    if command -v iptables &>/dev/null; then
        iptables -L
    fi
fi

print_header "Usuários e Grupos"
print_subheader "Grupo www-data:"
id www-data
print_subheader "\nGrupo plugdev:"
getent group plugdev

print_header "Dispositivos USB"
lsusb

print_header "Espaço em Disco"
df -h

print_header "Memória"
free -h

print_header "Processos Relevantes"
ps aux | grep -E 'python|apache|nginx|mysql|biometria'

print_header "Arquivos de Log"
echo "Últimas 5 linhas de logs relevantes:"
for log in /var/log/controle-ponto/*.log; do
    if [ -f "$log" ]; then
        print_subheader "\n=== $log ==="
        tail -n 5 "$log" 2>/dev/null
    fi
done

print_header "Arquivos de Configuração"
print_subheader "Verificando /etc/systemd/system/biometria.service:"
cat /etc/systemd/system/biometria.service 2>/dev/null

print_header "DLLs do ZK4500"
print_subheader "Procurando zkfp.dll:"
find /var/www/controle-ponto -name "zkfp.dll" -ls 2>/dev/null

# Salva o resultado em um arquivo
if [ "$1" = "--save" ]; then
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    OUTPUT_FILE="server_check_${TIMESTAMP}.txt"
    $0 | tee "$OUTPUT_FILE"
    echo -e "\n${GREEN}Resultado salvo em: $OUTPUT_FILE${NC}"
fi 