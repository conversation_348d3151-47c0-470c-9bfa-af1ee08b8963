#!/usr/bin/env python3
"""
Debug real do problema de EPIs.
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

def testar_com_dados_reais():
    """
    Testa com dados que deveriam vir do formulário real.
    """
    print("🔍 TESTE: Dados reais do formulário")
    print("=" * 60)
    
    # Simular dados exatos como viriam do formulário web
    form_data = {
        # Dados básicos
        'nome_completo': 'Teste EPI Real',
        'cpf': '123.456.789-00',
        'empresa_id': '1',
        
        # EPI com índice 0 (primeiro EPI adicionado)
        'epis[0][epi_nome]': 'Capacete de Segurança',
        'epis[0][epi_ca]': '12345',
        'epis[0][epi_data_entrega]': '2025-07-08',
        'epis[0][epi_data_validade]': '2026-07-08',
        'epis[0][epi_observacoes]': 'Teste real',
    }
    
    print("📋 Dados simulados (como viriam do formulário):")
    for key, value in form_data.items():
        print(f"   {key}: '{value}'")
    
    # Testar função real
    print(f"\n🔄 Testando função _extrair_dados_epis...")
    
    try:
        from app_funcionarios import _extrair_dados_epis
        
        # Executar função real
        epis_extraidos = _extrair_dados_epis(form_data)
        
        print(f"\n📦 Resultado da extração:")
        print(f"   Total de EPIs: {len(epis_extraidos)}")
        
        for i, epi in enumerate(epis_extraidos):
            print(f"\n   EPI {i+1}:")
            for campo, valor in epi.items():
                print(f"      {campo}: '{valor}'")
        
        if epis_extraidos:
            print(f"\n✅ SUCESSO: EPIs extraídos corretamente!")
            return epis_extraidos
        else:
            print(f"\n❌ FALHA: Nenhum EPI extraído!")
            return []
            
    except Exception as e:
        print(f"\n❌ ERRO na extração: {e}")
        import traceback
        traceback.print_exc()
        return []

def testar_salvamento_direto():
    """
    Testa salvamento direto no banco.
    """
    print(f"\n🔄 TESTE: Salvamento direto no banco")
    print("=" * 60)
    
    try:
        from utils.database import DatabaseManager
        
        # Usar funcionário existente
        funcionario_id = 11
        
        print(f"👤 Testando com funcionário ID: {funcionario_id}")
        
        # Dados do EPI
        epi_data = {
            'epi_nome': 'Capacete Teste Direto',
            'epi_ca': '99999',
            'epi_data_entrega': '2025-07-08',
            'epi_data_validade': '2026-07-08',
            'epi_observacoes': 'Teste direto no banco'
        }
        
        print(f"🦺 Dados do EPI: {epi_data}")
        
        # Inserir diretamente
        sql = """
        INSERT INTO epis (funcionario_id, epi_nome, epi_ca, epi_data_entrega, epi_data_validade, epi_observacoes)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        params = (
            funcionario_id,
            epi_data['epi_nome'],
            epi_data['epi_ca'],
            epi_data['epi_data_entrega'],
            epi_data['epi_data_validade'],
            epi_data['epi_observacoes']
        )
        
        print(f"🔄 Executando INSERT...")
        epi_id = DatabaseManager.execute_query(sql, params, fetch_all=False)
        
        if epi_id:
            print(f"✅ EPI inserido com sucesso! ID: {epi_id}")
            
            # Verificar se aparece na consulta
            epis_funcionario = DatabaseManager.execute_query(
                "SELECT * FROM epis WHERE funcionario_id = %s",
                (funcionario_id,)
            )
            
            print(f"📋 EPIs do funcionário {funcionario_id}: {len(epis_funcionario)}")
            for epi in epis_funcionario:
                print(f"   - {epi['epi_nome']} (ID: {epi['id']})")
            
            return True
        else:
            print(f"❌ Falha ao inserir EPI")
            return False
            
    except Exception as e:
        print(f"❌ Erro no salvamento: {e}")
        import traceback
        traceback.print_exc()
        return False

def testar_get_with_epis():
    """
    Testa a função get_with_epis.
    """
    print(f"\n🔄 TESTE: Função get_with_epis")
    print("=" * 60)
    
    try:
        from utils.database import FuncionarioQueries
        
        funcionario_id = 11
        print(f"👤 Testando com funcionário ID: {funcionario_id}")
        
        # Testar função
        funcionario = FuncionarioQueries.get_with_epis(funcionario_id)
        
        if funcionario:
            print(f"✅ Funcionário encontrado: {funcionario.get('nome_completo', 'N/A')}")
            
            epis = funcionario.get('epis', [])
            print(f"📦 EPIs retornados: {len(epis)}")
            
            for i, epi in enumerate(epis):
                print(f"   EPI {i+1}: {epi.get('epi_nome', 'N/A')} (ID: {epi.get('id', 'N/A')})")
            
            return len(epis) > 0
        else:
            print(f"❌ Funcionário não encontrado")
            return False
            
    except Exception as e:
        print(f"❌ Erro na consulta: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 DEBUG COMPLETO - Problema de EPIs")
    print("=" * 80)
    
    # Teste 1: Extração de dados
    epis = testar_com_dados_reais()
    
    # Teste 2: Salvamento direto
    salvou = testar_salvamento_direto()
    
    # Teste 3: Consulta
    if salvou:
        consultou = testar_get_with_epis()
        
        if consultou:
            print(f"\n🎉 TODOS OS TESTES PASSARAM!")
            print(f"   ✅ Extração: {'OK' if epis else 'FALHA'}")
            print(f"   ✅ Salvamento: OK")
            print(f"   ✅ Consulta: OK")
        else:
            print(f"\n❌ FALHA na consulta")
    else:
        print(f"\n❌ FALHA no salvamento")
    
    print(f"\n🏁 Debug finalizado.")
