class BiometriaService {
    constructor() {
        this.baseUrl = 'http://localhost:8081/biometria/api/biometria';
        this.connected = false;
        this.readerConnected = false;
        this.eventHandlers = {
            onConnect: [],
            onDisconnect: [],
            onReaderStatus: [],
            onError: []
        };
        
        // Inicia o polling de status
        this.startStatusPolling();
    }

    // Registra handlers de eventos
    on(event, handler) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].push(handler);
        }
    }

    // Dispara eventos
    emit(event, data) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(handler => handler(data));
        }
    }

    // Conecta ao serviço
    async connect() {
        try {
            const response = await fetch(`${this.baseUrl}/inicializar`);
            const data = await response.json();
            
            if (data.sucesso) {
                this.connected = true;
                this.readerConnected = true;
                this.emit('onConnect');
                this.emit('onReaderStatus', true);
                return true;
            } else {
                throw new Error(data.mensagem);
            }
        } catch (error) {
            this.connected = false;
            this.readerConnected = false;
            this.emit('onError', error.message);
            throw error;
        }
    }

    // Desconecta do serviço
    async disconnect() {
        try {
            const response = await fetch(`${this.baseUrl}/finalizar`);
            const data = await response.json();
            
            this.connected = false;
            this.readerConnected = false;
            this.emit('onDisconnect');
            this.emit('onReaderStatus', false);
            
            return data.sucesso;
        } catch (error) {
            this.emit('onError', error.message);
            throw error;
        }
    }

    // Verifica o status do leitor
    async checkStatus() {
        try {
            const response = await fetch(`${this.baseUrl}/status`);
            const data = await response.json();
            
            const newStatus = data.sucesso;
            if (newStatus !== this.readerConnected) {
                this.readerConnected = newStatus;
                this.emit('onReaderStatus', newStatus);
            }
            
            return newStatus;
        } catch (error) {
            this.readerConnected = false;
            this.emit('onReaderStatus', false);
            this.emit('onError', error.message);
            return false;
        }
    }

    // Captura a digital
    async capturarDigital() {
        try {
            const response = await fetch(`${this.baseUrl}/capturar`);
            const data = await response.json();
            
            if (!data.sucesso) {
                throw new Error(data.mensagem);
            }
            
            return {
                template: data.templateBase64,
                image: data.imageBase64,
                quality: data.qualidade
            };
        } catch (error) {
            this.emit('onError', error.message);
            throw error;
        }
    }

    // Polling de status
    startStatusPolling() {
        setInterval(() => {
            if (this.connected) {
                this.checkStatus().catch(() => {});
            }
        }, 2000); // Verifica a cada 2 segundos
    }

    // Getters de estado
    isConnected() {
        return this.connected;
    }

    isReaderConnected() {
        return this.readerConnected;
    }
} 