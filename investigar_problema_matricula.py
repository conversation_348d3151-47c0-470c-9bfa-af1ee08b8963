#!/usr/bin/env python3
"""
Investigar problema de matrícula duplicada
"""

import sys
sys.path.insert(0, '/var/www/controle-ponto')

from utils.database import DatabaseManager

def investigar_problema():
    """Investigar o problema de matrícula duplicada"""
    print("🔍 INVESTIGAÇÃO: PROBLEMA DE MATRÍCULA DUPLICADA")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        
        # 1. Verificar funcionários ativos com matrícula 001
        print("📋 1. FUNCIONÁRIOS ATIVOS COM MATRÍCULA 001:")
        funcionarios_ativos_001 = db.execute_query("""
            SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
            FROM funcionarios 
            WHERE matricula_empresa = '001'
        """)
        
        if funcionarios_ativos_001:
            print(f"   ✅ {len(funcionarios_ativos_001)} funcionários encontrados:")
            for func in funcionarios_ativos_001:
                status_ativo = "ATIVO" if func['ativo'] else "INATIVO"
                print(f"      - ID: {func['id']}, Nome: {func['nome_completo']}")
                print(f"        Status: {func['status_cadastro']}, Ativo: {status_ativo}")
        else:
            print("   ❌ Nenhum funcionário ativo encontrado com matrícula 001")
        
        # 2. Verificar funcionários desligados com matrícula 001
        print("\n📋 2. FUNCIONÁRIOS DESLIGADOS COM MATRÍCULA 001:")
        funcionarios_desligados_001 = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa, data_desligamento
            FROM funcionarios_desligados 
            WHERE matricula_empresa = '001'
        """)
        
        if funcionarios_desligados_001:
            print(f"   ✅ {len(funcionarios_desligados_001)} funcionários desligados encontrados:")
            for func in funcionarios_desligados_001:
                print(f"      - ID Original: {func['funcionario_id_original']}, Nome: {func['nome_completo']}")
                print(f"        Data Desligamento: {func['data_desligamento']}")
        else:
            print("   ❌ Nenhum funcionário desligado encontrado com matrícula 001")
        
        # 3. Verificar se Richardson está duplicado
        print("\n📋 3. VERIFICANDO DUPLICAÇÃO DO RICHARDSON:")
        
        # Na tabela principal
        richardson_principal = db.execute_query("""
            SELECT id, nome_completo, matricula_empresa, status_cadastro, ativo
            FROM funcionarios
            WHERE nome_completo LIKE %s
        """, ('%RICHARDSON%',))

        print(f"   Richardson na tabela principal: {len(richardson_principal) if richardson_principal else 0} registros")
        if richardson_principal:
            for func in richardson_principal:
                status_ativo = "ATIVO" if func['ativo'] else "INATIVO"
                print(f"      - ID: {func['id']}, Status: {func['status_cadastro']}, Ativo: {status_ativo}")

        # Na tabela de desligados
        richardson_desligados = db.execute_query("""
            SELECT funcionario_id_original, nome_completo, matricula_empresa, data_desligamento
            FROM funcionarios_desligados
            WHERE nome_completo LIKE %s
        """, ('%RICHARDSON%',))
        
        print(f"   Richardson na tabela de desligados: {len(richardson_desligados) if richardson_desligados else 0} registros")
        if richardson_desligados:
            for func in richardson_desligados:
                print(f"      - ID Original: {func['funcionario_id_original']}, Data: {func['data_desligamento']}")
        
        # 4. Propor solução
        print("\n📋 4. ANÁLISE E SOLUÇÃO:")
        
        if funcionarios_ativos_001 and funcionarios_desligados_001:
            print("   🚨 PROBLEMA IDENTIFICADO:")
            print("   ❌ Richardson existe TANTO na tabela principal quanto na de desligados")
            print("   ❌ Isso impede a restauração porque a matrícula já está 'ocupada'")
            print("\n   💡 SOLUÇÕES POSSÍVEIS:")
            print("   1. Remover Richardson da tabela principal (se ele foi realmente desligado)")
            print("   2. Atualizar a matrícula na tabela de desligados")
            print("   3. Corrigir o processo de desligamento para evitar duplicação")
            
            # Verificar qual Richardson é o correto
            if richardson_principal:
                func_principal = richardson_principal[0]
                if not func_principal['ativo']:
                    print(f"\n   🔧 CORREÇÃO RECOMENDADA:")
                    print(f"   ✅ Richardson na tabela principal está INATIVO (ativo = {func_principal['ativo']})")
                    print(f"   ✅ Pode ser removido da tabela principal para permitir restauração")
                    
                    return {
                        'problema': 'duplicacao',
                        'solucao': 'remover_inativo',
                        'id_remover': func_principal['id'],
                        'nome': func_principal['nome_completo']
                    }
        
        return {'problema': 'outro', 'detalhes': 'Investigação concluída'}
        
    except Exception as e:
        print(f"\n❌ ERRO NA INVESTIGAÇÃO: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return {'problema': 'erro', 'erro': str(e)}

def corrigir_problema(solucao_info):
    """Corrigir o problema identificado"""
    if solucao_info.get('problema') == 'duplicacao' and solucao_info.get('solucao') == 'remover_inativo':
        print(f"\n🔧 APLICANDO CORREÇÃO:")
        print(f"   Removendo funcionário inativo ID {solucao_info['id_remover']} da tabela principal")
        
        try:
            db = DatabaseManager()
            
            # Remover funcionário inativo da tabela principal
            db.execute_query(
                "DELETE FROM funcionarios WHERE id = %s AND ativo = FALSE",
                (solucao_info['id_remover'],)
            )
            
            print(f"   ✅ Funcionário {solucao_info['nome']} removido da tabela principal")
            print(f"   ✅ Agora a restauração deve funcionar")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Erro ao aplicar correção: {e}")
            return False
    
    return False

if __name__ == "__main__":
    print("🎯 INVESTIGAÇÃO COMPLETA: PROBLEMA DE RESTAURAÇÃO")
    print("=" * 70)
    
    # Investigar problema
    resultado = investigar_problema()
    
    if resultado.get('problema') == 'duplicacao':
        print(f"\n🤔 DESEJA APLICAR A CORREÇÃO AUTOMÁTICA? (s/n)")
        print(f"   Isso removerá o funcionário inativo da tabela principal")
        
        # Para automação, vamos aplicar a correção
        print("   Aplicando correção automática...")
        
        if corrigir_problema(resultado):
            print("\n🎉 CORREÇÃO APLICADA COM SUCESSO!")
            print("✅ Problema de duplicação resolvido")
            print("✅ Restauração deve funcionar agora")
        else:
            print("\n❌ FALHA NA CORREÇÃO!")
    else:
        print(f"\n📊 RESULTADO: {resultado}")
