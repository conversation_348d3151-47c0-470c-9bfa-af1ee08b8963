#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para executar a criação da estrutura de empresa principal
RLPONTO-WEB - Sistema de Controle de Ponto
"""

import pymysql
import sys
import os
from datetime import datetime

def conectar_banco():
    """Conecta ao banco de dados MySQL"""
    try:
        conexao = pymysql.connect(
            host='************',
            user='cavalcrod',
            password='200381',
            database='controle_ponto',
            charset='utf8mb4',
            autocommit=False
        )
        return conexao
    except pymysql.Error as e:
        print(f"❌ Erro ao conectar ao banco: {e}")
        return None

def executar_sql_arquivo(conexao, arquivo_sql):
    """Executa comandos SQL de um arquivo"""
    try:
        cursor = conexao.cursor()
        
        # Ler o arquivo SQL
        with open(arquivo_sql, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # Dividir em comandos individuais
        comandos = []
        comando_atual = ""
        
        for linha in sql_content.split('\n'):
            linha = linha.strip()
            
            # Ignorar comentários e linhas vazias
            if not linha or linha.startswith('--'):
                continue
                
            comando_atual += linha + " "
            
            # Se termina com ; é fim do comando
            if linha.endswith(';'):
                comandos.append(comando_atual.strip())
                comando_atual = ""
        
        # Executar cada comando
        resultados = []
        for i, comando in enumerate(comandos):
            if not comando:
                continue
                
            try:
                print(f"📝 Executando comando {i+1}/{len(comandos)}...")
                
                # Comandos especiais que precisam de tratamento diferente
                if 'DELIMITER' in comando.upper():
                    continue
                    
                cursor.execute(comando)
                
                # Se há resultados, capturar
                if cursor.with_rows:
                    resultado = cursor.fetchall()
                    if resultado:
                        resultados.append(resultado)
                        
                print(f"✅ Comando {i+1} executado com sucesso")

            except pymysql.Error as e:
                print(f"⚠️ Erro no comando {i+1}: {e}")
                print(f"Comando: {comando[:100]}...")
                # Continuar com próximo comando
                continue
        
        # Commit das alterações
        conexao.commit()
        print("✅ Todas as alterações foram confirmadas no banco")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao executar SQL: {e}")
        conexao.rollback()
        return False
    finally:
        cursor.close()

def verificar_estrutura(conexao):
    """Verifica se a estrutura foi criada corretamente"""
    try:
        cursor = conexao.cursor()
        
        # Verificar colunas adicionadas na tabela empresas
        cursor.execute("""
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'controle_ponto' 
            AND TABLE_NAME = 'empresas' 
            AND COLUMN_NAME IN ('empresa_principal', 'empresa_matriz_id', 'tipo_empresa', 'configuracoes_cliente')
        """)
        colunas_empresas = [row[0] for row in cursor.fetchall()]
        
        # Verificar tabelas criadas
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'controle_ponto' 
            AND TABLE_NAME IN ('empresa_clientes', 'funcionario_alocacoes', 'historico_alocacoes')
        """)
        tabelas_criadas = [row[0] for row in cursor.fetchall()]
        
        # Verificar views criadas
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.VIEWS 
            WHERE TABLE_SCHEMA = 'controle_ponto' 
            AND TABLE_NAME IN ('vw_empresa_principal', 'vw_clientes_detalhados', 'vw_funcionarios_alocados')
        """)
        views_criadas = [row[0] for row in cursor.fetchall()]
        
        print("\n📊 VERIFICAÇÃO DA ESTRUTURA:")
        print(f"✅ Colunas adicionadas em 'empresas': {len(colunas_empresas)}/4")
        for coluna in colunas_empresas:
            print(f"   - {coluna}")
            
        print(f"✅ Tabelas criadas: {len(tabelas_criadas)}/3")
        for tabela in tabelas_criadas:
            print(f"   - {tabela}")
            
        print(f"✅ Views criadas: {len(views_criadas)}/3")
        for view in views_criadas:
            print(f"   - {view}")
        
        return len(colunas_empresas) >= 3 and len(tabelas_criadas) >= 3
        
    except Exception as e:
        print(f"❌ Erro na verificação: {e}")
        return False
    finally:
        cursor.close()

def main():
    """Função principal"""
    print("🚀 RLPONTO-WEB - Criação da Estrutura de Empresa Principal")
    print("=" * 60)
    
    # Verificar se arquivo SQL existe
    arquivo_sql = "sql/empresa_principal_clientes.sql"
    if not os.path.exists(arquivo_sql):
        print(f"❌ Arquivo SQL não encontrado: {arquivo_sql}")
        return False
    
    # Conectar ao banco
    print("🔌 Conectando ao banco de dados...")
    conexao = conectar_banco()
    if not conexao:
        return False
    
    try:
        # Executar SQL
        print("📝 Executando script SQL...")
        sucesso = executar_sql_arquivo(conexao, arquivo_sql)
        
        if sucesso:
            print("✅ Script executado com sucesso!")
            
            # Verificar estrutura
            print("🔍 Verificando estrutura criada...")
            if verificar_estrutura(conexao):
                print("✅ Estrutura de empresa principal criada com sucesso!")
                return True
            else:
                print("⚠️ Estrutura criada parcialmente")
                return False
        else:
            print("❌ Falha na execução do script")
            return False
            
    finally:
        conexao.close()
        print("🔌 Conexão com banco encerrada")

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
