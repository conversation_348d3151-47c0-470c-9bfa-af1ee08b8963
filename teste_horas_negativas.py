#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 TESTE ESPECÍFICO PARA HORAS NEGATIVAS (ATRASOS)
==================================================

Testa se as horas negativas estão sendo calculadas corretamente.
Horas negativas = quando trabalhou menos que a jornada esperada.
"""

import pymysql
from datetime import datetime, time, timedelta

def conectar_banco():
    """Conecta ao banco de dados"""
    return pymysql.connect(
        host='************',
        user='cavalcrod',
        password='200381',
        database='controle_ponto',
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )

def calcular_horas_trabalhadas(registros_dia):
    """
    Calcula horas trabalhadas baseado nos registros do dia
    """
    entrada_manha = None
    saida_almoco = None
    entrada_tarde = None
    saida = None
    
    for registro in registros_dia:
        tipo = registro['tipo_registro']
        hora = registro['hora_registro']
        
        if tipo == 'entrada_manha':
            entrada_manha = hora
        elif tipo == 'saida_almoco':
            saida_almoco = hora
        elif tipo == 'entrada_tarde':
            entrada_tarde = hora
        elif tipo == 'saida':
            saida = hora
    
    total_horas = 0.0
    
    # Período manhã
    if entrada_manha and saida_almoco:
        # Converter timedelta para time se necessário
        if isinstance(entrada_manha, timedelta):
            entrada_manha = (datetime.min + entrada_manha).time()
        if isinstance(saida_almoco, timedelta):
            saida_almoco = (datetime.min + saida_almoco).time()

        dt_entrada = datetime.combine(datetime.today(), entrada_manha)
        dt_saida = datetime.combine(datetime.today(), saida_almoco)
        periodo_manha = (dt_saida - dt_entrada).total_seconds() / 3600
        total_horas += periodo_manha
        print(f"   Período manhã: {entrada_manha} - {saida_almoco} = {periodo_manha:.2f}h")

    # Período tarde
    if entrada_tarde and saida:
        # Converter timedelta para time se necessário
        if isinstance(entrada_tarde, timedelta):
            entrada_tarde = (datetime.min + entrada_tarde).time()
        if isinstance(saida, timedelta):
            saida = (datetime.min + saida).time()

        dt_entrada = datetime.combine(datetime.today(), entrada_tarde)
        dt_saida = datetime.combine(datetime.today(), saida)
        periodo_tarde = (dt_saida - dt_entrada).total_seconds() / 3600
        total_horas += periodo_tarde
        print(f"   Período tarde: {entrada_tarde} - {saida} = {periodo_tarde:.2f}h")
    
    return total_horas

def testar_horas_negativas():
    """
    Testa cálculo de horas negativas
    """
    print("🔍 TESTE DE HORAS NEGATIVAS (ATRASOS)")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    try:
        conn = conectar_banco()
        cursor = conn.cursor()
        
        # Buscar funcionários com registros recentes
        print("📊 Buscando funcionários com registros recentes...")
        
        cursor.execute("""
            SELECT DISTINCT 
                f.id,
                f.nome_completo,
                DATE(rp.data_hora) as data_registro
            FROM registros_ponto rp 
            JOIN funcionarios f ON rp.funcionario_id = f.id 
            WHERE DATE(rp.data_hora) >= '2025-07-14'
            ORDER BY data_registro DESC, f.nome_completo
            LIMIT 10
        """)
        
        funcionarios_datas = cursor.fetchall()
        
        if not funcionarios_datas:
            print("❌ Nenhum registro encontrado")
            return False
        
        print(f"✅ Encontrados {len(funcionarios_datas)} registros de funcionários")
        
        # Analisar cada funcionário/data
        for func_data in funcionarios_datas:
            funcionario_id = func_data['id']
            nome = func_data['nome_completo']
            data = func_data['data_registro']
            
            print(f"\n📋 ANALISANDO: {nome} - {data}")
            print("-" * 50)
            
            # Buscar registros do dia
            cursor.execute("""
                SELECT 
                    tipo_registro,
                    TIME(data_hora) as hora_registro
                FROM registros_ponto 
                WHERE funcionario_id = %s 
                AND DATE(data_hora) = %s
                ORDER BY data_hora
            """, (funcionario_id, data))
            
            registros_dia = cursor.fetchall()
            
            if not registros_dia:
                print("   ❌ Nenhum registro encontrado para este dia")
                continue
            
            print(f"   📅 Registros do dia:")
            for reg in registros_dia:
                print(f"      {reg['tipo_registro']}: {reg['hora_registro']}")
            
            # Calcular horas trabalhadas
            horas_trabalhadas = calcular_horas_trabalhadas(registros_dia)
            print(f"\n   🧮 CÁLCULOS:")
            print(f"      Total trabalhado: {horas_trabalhadas:.2f}h")
            
            # Jornada esperada (8 horas padrão)
            jornada_esperada = 8.0
            print(f"      Jornada esperada: {jornada_esperada:.2f}h")
            
            # Calcular horas negativas
            if horas_trabalhadas < jornada_esperada:
                horas_negativas = jornada_esperada - horas_trabalhadas
                print(f"      🔴 HORAS NEGATIVAS: {horas_negativas:.2f}h")
                print(f"         (Trabalhou {horas_negativas:.2f}h a menos que o esperado)")
                
                # Converter para formato HH:MM
                horas_int = int(horas_negativas)
                minutos_int = int((horas_negativas - horas_int) * 60)
                print(f"         Formato HH:MM: {horas_int:02d}:{minutos_int:02d}")
                
            elif horas_trabalhadas > jornada_esperada:
                horas_extras = horas_trabalhadas - jornada_esperada
                print(f"      🟢 HORAS EXTRAS: {horas_extras:.2f}h")
                print(f"         (Trabalhou {horas_extras:.2f}h a mais que o esperado)")
            else:
                print(f"      ✅ JORNADA COMPLETA: Sem horas negativas ou extras")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao conectar ao banco: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    sucesso = testar_horas_negativas()
    
    print(f"\n" + "=" * 60)
    print(f"📊 RESULTADO FINAL DO TESTE")
    print(f"=" * 60)
    
    if sucesso:
        print(f"✅ Análise de horas negativas: CONCLUÍDA")
        print(f"   Dados analisados e cálculos realizados")
    else:
        print(f"❌ Análise de horas negativas: FALHOU")
        print(f"   Erro ao acessar dados ou realizar cálculos")
    
    print(f"\n💡 INTERPRETAÇÃO:")
    print(f"   🔴 HORAS NEGATIVAS = Trabalhou menos que 8h = ATRASOS/FALTAS")
    print(f"   🟢 HORAS EXTRAS = Trabalhou mais que 8h = HORAS EXTRAS")
    print(f"   ✅ JORNADA COMPLETA = Trabalhou exatamente 8h = NORMAL")
    
    print(f"\n🎯 PRÓXIMO PASSO:")
    print(f"   Verificar se esses valores aparecem corretamente no relatório")

if __name__ == "__main__":
    main()
