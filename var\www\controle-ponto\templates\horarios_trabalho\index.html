{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON><PERSON><PERSON> Trabalho{% endblock %}

{% block extra_css %}
<style>
    .horarios-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 2rem 0;
    }

    .horarios-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        color: white;
        padding: 2.5rem 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .horarios-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 50%);
        transform: rotate(45deg);
        pointer-events: none;
    }

    .horarios-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .horarios-header p {
        font-size: 1.1rem;
        margin: 0;
        opacity: 0.9;
    }

    .actions-bar {
        background: white;
        border-radius: 16px;
        padding: 1.5rem 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 25px rgba(0,0,0,0.08);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .stats-info {
        display: flex;
        align-items: center;
        gap: 1rem;
        color: #4a5568;
        font-weight: 600;
    }

    .stats-info .stat-number {
        font-size: 2rem;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -moz-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 800;
    }

    .btn-novo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border: none;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
    }

    .btn-novo:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
        color: white;
    }

    .horarios-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 1.5rem;
    }

    .horario-card {
        background: white;
        border-radius: 16px;
        padding: 0;
        box-shadow: 0 10px 25px rgba(0,0,0,0.08);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(255,255,255,0.8);
        overflow: hidden;
        position: relative;
    }

    .horario-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.12);
    }

    .horario-card.inativo {
        opacity: 0.7;
        background: #f7fafc;
    }

    .horario-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .horario-card.inativo::before {
        background: #e2e8f0;
    }

    .card-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .card-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2d3748;
        margin: 0 0 0.5rem 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .status-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-badge.ativo {
        background: #d4edda;
        color: #155724;
    }

    .status-badge.inativo {
        background: #f8d7da;
        color: #721c24;
    }

    .empresa-info {
        color: #718096;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .horarios-detalhes {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .horario-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.8rem;
        background: #f7fafc;
        border-radius: 8px;
        font-size: 0.9rem;
    }

    .horario-item .icon {
        color: #667eea;
        font-size: 1rem;
    }

    .horario-item .label {
        color: #718096;
        font-weight: 500;
    }

    .horario-item .time {
        color: #2d3748;
        font-weight: 600;
        margin-left: auto;
    }

    .tolerancia-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.05));
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .funcionarios-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #4a5568;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .card-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }

    .btn-action {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        font-size: 0.8rem;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.3rem;
    }

    .btn-editar {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }

    .btn-editar:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(250, 112, 154, 0.3);
        color: white;
    }

    .btn-excluir {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        color: #721c24;
    }

    .btn-excluir:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255, 154, 158, 0.3);
        color: #721c24;
    }

    .btn-excluir:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #718096;
    }

    .empty-state .icon {
        font-size: 4rem;
        color: #e2e8f0;
        margin-bottom: 1rem;
    }

    .empty-state h3 {
        font-size: 1.5rem;
        color: #4a5568;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
        .horarios-container {
            padding: 1rem 0;
        }
        
        .horarios-header {
            padding: 2rem 1rem;
        }
        
        .horarios-header h1 {
            font-size: 2rem;
        }
        
        .actions-bar {
            flex-direction: column;
            text-align: center;
        }
        
        .horarios-grid {
            grid-template-columns: 1fr;
        }
        
        .horarios-detalhes {
            grid-template-columns: 1fr;
        }
    }

    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-clock"></i> Horários de Trabalho
                    </h4>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="badge badge-info">{{ horarios|length }} horário(s) cadastrado(s)</span>
                        </div>
                        <a href="#" class="btn btn-success" onclick="alert('Funcionalidade em desenvolvimento')">
                            <i class="fas fa-plus"></i> Novo Horário
                        </a>
                    </div>

                    {% if horarios %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Nome do Horário</th>
                                    <th>Empresa</th>
                                    <th>Entrada Manhã</th>
                                    <th>Saída Almoço</th>
                                    <th>Retorno Almoço</th>
                                    <th>Saída</th>
                                    <th>Tolerância</th>
                                    <th>Funcionários</th>
                                    <th>Status</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for horario in horarios %}
                                <tr>
                                    <td><strong>{{ horario.nome_horario }}</strong></td>
                                    <td>{{ horario.razao_social }}</td>
                                    <td>{{ horario.entrada_manha.strftime('%H:%M') if horario.entrada_manha else '-' }}</td>
                                    <td>{{ horario.saida_almoco.strftime('%H:%M') if horario.saida_almoco else '-' }}</td>
                                    <td>{{ horario.entrada_tarde.strftime('%H:%M') if horario.entrada_tarde else '-' }}</td>
                                    <td>{{ horario.saida.strftime('%H:%M') if horario.saida else '-' }}</td>
                                    <td>{{ horario.tolerancia_minutos }}min</td>
                                    <td>
                                        <span class="badge badge-secondary">{{ horario.funcionarios_vinculados }}</span>
                                    </td>
                                    <td>
                                        {% if horario.ativo %}
                                            <span class="badge badge-success">Ativo</span>
                                        {% else %}
                                            <span class="badge badge-secondary">Inativo</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="#" class="btn btn-warning btn-sm" onclick="alert('Funcionalidade em desenvolvimento')">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if horario.funcionarios_vinculados == 0 %}
                                            <button class="btn btn-danger btn-sm" onclick="confirmarExclusao('{{ horario.nome_horario|e }}', {{ horario.id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% else %}
                                            <button class="btn btn-secondary btn-sm" disabled title="Horário possui funcionários vinculados">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-clock fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum horário de trabalho cadastrado</h5>
                        <p class="text-muted">Comece criando o primeiro horário para sua empresa</p>
                        <a href="#" class="btn btn-primary" onclick="alert('Funcionalidade em desenvolvimento')">
                            <i class="fas fa-plus"></i> Criar Primeiro Horário
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmarExclusao(nomeHorario, horarioId) {
    if (confirm('Tem certeza que deseja excluir o horário "' + nomeHorario + '"?\n\nEsta ação não pode ser desfeita.')) {
        fetch('/horarios-trabalho/' + horarioId + '/excluir', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Erro ao excluir horário');
            }
        }).catch(error => {
            alert('Erro ao excluir horário');
            console.error('Error:', error);
        });
    }
}
</script>
{% endblock %} 