"""
Módulo de Detecção Real de Dispositivos Biométricos
Sistema: RLPONTO-WEB v1.0
Desenvolvido por: <PERSON> - AiNexus Tecnologia
Data: 12/06/2025

Implementa detecção real de leitores biométricos usando WMIC
RESTAURADO: Baseado na versão que detectou com sucesso o ZK4500 (VID:1B55 PID:0840)
"""

import subprocess
import json
import re
import logging
import platform
from typing import Dict, List, Optional

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BiometricDeviceDetector:
    """
    Detector real de dispositivos biométricos
    Windows: WMIC (COMPROVADO FUNCIONANDO!)
    Linux: lsusb
    """
    
    def __init__(self):
        self.known_vendors = {
            '1B55': 'ZKTeco Inc.',
            '05E3': 'Genesys Logic',
            '046D': 'Logitech',
            '27C6': 'Goodix Technology',
            '138A': 'Validity Sensors',
            '04F3': 'Elan Microelectronics'
        }
        
        self.known_devices = {
            '1B55:0840': {
                'name': 'ZK4500 Fingerprint Reader',
                'type': 'fingerprint',
                'manufacturer': 'ZKTeco Inc.',
                'supported': True
            }
        }

    def detect_biometric_devices(self) -> List[Dict]:
        """
        Detecta dispositivos biométricos baseado no sistema operacional
        """
        sistema = platform.system()
        
        logger.info(f"🚀 [DEBUG] Iniciando detecção - Sistema: {sistema}")
        
        if sistema == 'Windows':
            return self._detect_windows_wmic()
        elif sistema == 'Linux':
            return self._detect_linux_lsusb()
        else:
            logger.error(f"❌ Sistema operacional não suportado: {sistema}")
            return []

    def _detect_windows_wmic(self) -> List[Dict]:
        """
        DETECÇÃO WINDOWS USANDO WMIC - MÉTODO QUE FUNCIONOU!
        """
        devices = []
        
        logger.info(f"🪟 [DEBUG WINDOWS] Iniciando detecção WMIC...")
        
        try:
            # COMANDO WMIC QUE DETECTOU O ZK4500 COM SUCESSO!
            cmd = [
                'wmic', 'path', 'Win32_PnPEntity', 
                'where', "DeviceID like '%VID_1B55%' OR Name like '%ZK%'",
                'get', 'Name,DeviceID', '/format:csv'
            ]
            
            logger.info(f"🔍 [DEBUG WINDOWS] Executando: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0 and result.stdout.strip():
                logger.info(f"✅ [DEBUG WINDOWS] WMIC executado com sucesso")
                logger.info(f"📋 [DEBUG WINDOWS] Output WMIC:\n{result.stdout}")
                
                lines = result.stdout.strip().split('\n')
                
                if len(lines) > 1:  # Primeira linha é header
                    for line in lines[1:]:
                        if line.strip():
                            parts = line.split(',')
                            if len(parts) >= 3:
                                device_id = parts[1].strip() if len(parts) > 1 else ''
                                name = parts[2].strip() if len(parts) > 2 else 'Unknown Device'
                                
                                logger.info(f"🔍 [DEBUG WINDOWS] Analisando: {name} | {device_id}")
                                
                                # Verificar se é dispositivo biométrico válido
                                if device_id and ('VID_1B55' in device_id.upper() or 
                                                'ZK' in name.upper() or 
                                                'fingerprint' in name.lower() or 
                                                'biometric' in name.lower()):
                                    
                                    # Extrair VID e PID
                                    vid_match = re.search(r'VID_([0-9A-F]{4})', device_id, re.IGNORECASE)
                                    pid_match = re.search(r'PID_([0-9A-F]{4})', device_id, re.IGNORECASE)
                                    
                                    vid = vid_match.group(1).upper() if vid_match else ''
                                    pid = pid_match.group(1).upper() if pid_match else ''
                                    
                                    logger.info(f"🎯 [DEBUG WINDOWS] VID: {vid}, PID: {pid}")
                                    
                                    # Identificar ZK4500 especificamente
                                    if vid == '1B55' and pid == '0840':
                                        device_info = {
                                            'friendly_name': 'ZK4500 Fingerprint Reader',
                                            'instance_id': device_id,
                                            'status': 'OK',
                                            'class': 'Biometric',
                                            'manufacturer': 'ZKTeco Inc.',
                                            'device_type': 'fingerprint',
                                            'vendor_id': vid,
                                            'product_id': pid,
                                            'supported': True,
                                            'detection_method': 'WMIC_WINDOWS'
                                        }
                                        devices.append(device_info)
                                        logger.info(f"✅ [DEBUG WINDOWS] ZK4500 DETECTADO: {device_id}")
                                    else:
                                        # Outros dispositivos biométricos
                                        device_info = {
                                            'friendly_name': name,
                                            'instance_id': device_id,
                                            'status': 'OK',
                                            'class': 'Biometric',
                                            'manufacturer': 'Unknown',
                                            'device_type': 'biometric',
                                            'vendor_id': vid,
                                            'product_id': pid,
                                            'supported': vid in self.known_vendors,
                                            'detection_method': 'WMIC_WINDOWS'
                                        }
                                        devices.append(device_info)
                                        logger.info(f"✅ [DEBUG WINDOWS] Dispositivo detectado: {name}")
                
                logger.info(f"🎯 [DEBUG WINDOWS] TOTAL DETECTADO: {len(devices)} dispositivos via WMIC")
                return devices
            else:
                logger.warning(f"❌ [DEBUG WINDOWS] WMIC não retornou dispositivos")
                logger.warning(f"❌ [DEBUG WINDOWS] stderr: {result.stderr}")
                return []
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ [DEBUG WINDOWS] Timeout na detecção via WMIC")
            return []
        except Exception as e:
            logger.error(f"❌ [DEBUG WINDOWS] Erro na detecção: {str(e)}")
            return []

    def _detect_linux_lsusb(self) -> List[Dict]:
        """
        Detecção Linux usando lsusb
        """
        devices = []
        
        logger.info(f"🐧 [DEBUG LINUX] Iniciando detecção lsusb...")
        
        try:
            result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                logger.info(f"✅ [DEBUG LINUX] lsusb executado com sucesso")
                logger.info(f"📋 [DEBUG LINUX] Output lsusb:\n{result.stdout}")
                
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    logger.info(f"🔍 [DEBUG LINUX] Analisando linha: {line}")
                    
                    if '1b55:' in line.lower():
                        logger.info(f"🎯 [DEBUG LINUX] ENCONTROU DISPOSITIVO ZKTECO!")
                        
                        vid_pid_match = re.search(r'ID\s+([0-9a-f]{4}):([0-9a-f]{4})', line, re.IGNORECASE)
                        if vid_pid_match:
                            vid = vid_pid_match.group(1).upper()
                            pid = vid_pid_match.group(2).upper()
                            
                            logger.info(f"🎯 [DEBUG LINUX] VID: {vid}, PID: {pid}")
                            
                            if vid == '1B55' and pid == '0840':
                                device_info = {
                                    'friendly_name': 'ZK4500 Fingerprint Reader',
                                    'instance_id': f'USB\\VID_{vid}&PID_{pid}',
                                    'status': 'OK',
                                    'class': 'Biometric',
                                    'manufacturer': 'ZKTeco Inc.',
                                    'device_type': 'fingerprint',
                                    'vendor_id': vid,
                                    'product_id': pid,
                                    'supported': True,
                                    'detection_method': 'lsusb_LINUX'
                                }
                                devices.append(device_info)
                                logger.info(f"✅ [DEBUG LINUX] ZK4500 DETECTADO: VID:{vid} PID:{pid}")
                
                logger.info(f"🎯 [DEBUG LINUX] TOTAL DETECTADO: {len(devices)} dispositivos")
                return devices
            else:
                logger.error(f"❌ [DEBUG LINUX] lsusb falhou: {result.stderr}")
                return []
                
        except FileNotFoundError:
            logger.error(f"❌ [DEBUG LINUX] lsusb não encontrado no sistema")
            return []
        except Exception as e:
            logger.error(f"❌ [DEBUG LINUX] Erro na detecção: {str(e)}")
            return []

# Instanciar detector
detector = BiometricDeviceDetector()

def detect_biometric_devices():
    """Função pública para detecção de dispositivos"""
    logger.info(f"🚀 [DEBUG] detect_biometric_devices() chamada - Sistema: {platform.system()}")
    return detector.detect_biometric_devices()

def get_device_details(instance_id: str):
    """Obter detalhes de um dispositivo específico"""
    return {"info": "Detalhes não implementados ainda"}

def test_device_communication(instance_id: str):
    """Testar comunicação com dispositivo específico"""
    return {"info": "Teste de comunicação não implementado ainda"} 