#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de Atualização do Banco de Dados - RLPONTO-WEB
----------------------------------------------------
Executa atualizações para funcionalidades de registro de ponto.
"""

import pymysql
import os
import sys
from utils.config import Config

def executar_atualizacao():
    """
    Executa o script de atualização do banco de dados.
    """
    try:
        print('🔄 Conectando ao MySQL...')
        config = Config.get_database_url()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        print('📂 Lendo script de atualização...')
        script_path = 'atualizacao_banco_registro_ponto_v2.sql'
        
        if not os.path.exists(script_path):
            print(f'❌ Arquivo {script_path} não encontrado!')
            return False
            
        with open(script_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        print('⚡ Executando comandos SQL...')
        
        # Separar comandos por ponto e vírgula
        commands = sql_script.split(';')
        success_count = 0
        warning_count = 0
        error_count = 0
        
        for i, command in enumerate(commands):
            command = command.strip()
            
            # Pular comandos vazios, comentários e USE
            if (not command or 
                command.startswith('--') or 
                command.upper().startswith('USE ') or
                command == ''):
                continue
                
            try:
                cursor.execute(command)
                success_count += 1
                
                # Mostrar progresso a cada 5 comandos
                if success_count % 5 == 0:
                    print(f'   ⏳ Processados {success_count} comandos...')
                    
            except Exception as e:
                error_msg = str(e).lower()
                
                # Categorizar erros
                if any(msg in error_msg for msg in [
                    'already exists', 'duplicate column', 'duplicate key name',
                    'multiple primary key'
                ]):
                    warning_count += 1
                    print(f'⚠️ Aviso comando {i+1}: Estrutura já existe')
                elif 'unknown column' in error_msg:
                    error_count += 1
                    print(f'❌ Erro comando {i+1}: Coluna não encontrada - {str(e)[:80]}...')
                elif 'constraint' in error_msg and 'foreign key' in error_msg:
                    warning_count += 1
                    print(f'⚠️ Aviso comando {i+1}: Constraint já existe')
                else:
                    error_count += 1
                    print(f'❌ Erro comando {i+1}: {str(e)[:100]}...')
                    
        # Confirmar transações
        conn.commit()
        
        print(f'\n✅ Atualização concluída!')
        print(f'   📊 Comandos executados: {success_count}')
        print(f'   ⚠️ Avisos: {warning_count}')
        print(f'   ❌ Erros: {error_count}')
        
        # Verificar estruturas criadas
        print('\n🔍 Verificando estruturas criadas...')
        
        cursor.execute('SHOW TABLES')
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f'📋 Total de tabelas: {len(tables)}')
        
        # Verificar tabelas específicas
        tabelas_importantes = ['empresas', 'horarios_trabalho', 'registros_ponto', 'funcionarios']
        
        for tabela in tabelas_importantes:
            if tabela in tables:
                cursor.execute(f'SELECT COUNT(*) FROM {tabela}')
                count = cursor.fetchone()[0]
                print(f'   ✅ {tabela}: {count} registros')
            else:
                print(f'   ❌ {tabela}: não encontrada')
        
        # Verificar campos adicionados em registros_ponto
        print('\n🔍 Verificando campos de registros_ponto...')
        cursor.execute('DESCRIBE registros_ponto')
        campos = [campo[0] for campo in cursor.fetchall()]
        
        campos_necessarios = ['metodo_registro', 'ip_origem', 'criado_por', 'criado_em']
        for campo in campos_necessarios:
            if campo in campos:
                print(f'   ✅ Campo {campo}: existe')
            else:
                print(f'   ❌ Campo {campo}: não encontrado')
        
        # Verificar views
        print('\n🔍 Verificando views...')
        cursor.execute('SHOW FULL TABLES WHERE Table_type = "VIEW"')
        views = [view[0] for view in cursor.fetchall()]
        
        for view in views:
            print(f'   📊 View: {view}')
        
        cursor.close()
        conn.close()
        
        print('\n🎯 Banco de dados atualizado com sucesso!')
        return True
        
    except Exception as e:
        print(f'❌ Erro na atualização: {e}')
        return False

if __name__ == '__main__':
    print('='*60)
    print('🚀 ATUALIZAÇÃO BANCO DE DADOS - RLPONTO-WEB v2')
    print('='*60)
    
    if executar_atualizacao():
        print('\n✅ Processo concluído com sucesso!')
        sys.exit(0)
    else:
        print('\n❌ Processo falhou!')
        sys.exit(1) 