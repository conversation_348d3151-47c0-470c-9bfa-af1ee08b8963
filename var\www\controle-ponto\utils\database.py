#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Utilitários de Banco de Dados - Controle de Ponto
--------------------------------------------------

Este módulo centraliza todas as operações de banco de dados,
proporcionando uma interface consistente e reutilizável.
"""

import logging
import pymysql
import traceback
from pymysql.cursors import DictCursor
from contextlib import contextmanager

# Importa configurações seguras
try:
    from .config import Config
    logger = logging.getLogger('controle-ponto.database')
    logger.info("✅ Usando configurações seguras do módulo config")
except ImportError:
    logger = logging.getLogger('controle-ponto.database')
    logger.warning("⚠️ Módulo config não encontrado - usando configurações de fallback")
    Config = None

# ✅ CONFIGURAÇÕES ATUALIZADAS - CONFORME GUIA.MARKDOWN
# Configurações corrigidas com credenciais do servidor remoto
LEGACY_DB_CONFIGS = [
    {
        'host': '************',
        'user': 'cavalcrod',
        'password': '200381',
        'database': 'controle_ponto',
        'charset': 'utf8mb4',
        'cursorclass': DictCursor,
        'name': 'config_principal_corrigida'
    },
    {
        'host': '************', 
        'user': 'root',
        'password': '200381',
        'database': 'controle_ponto',
        'charset': 'utf8mb4',
        'cursorclass': DictCursor,
        'name': 'config_root_servidor'
    },
    {
        'host': '************',
        'user': 'controle_user',
        'password': 'controle123',
        'database': 'controle_ponto',
        'charset': 'utf8mb4',
        'cursorclass': DictCursor,
        'name': 'config_fallback_legacy'
    }
]

def get_db_configs():
    """
    Retorna lista de configurações de banco de dados.
    Prioriza configurações seguras do config.py, depois usa fallbacks legacy.
    
    Returns:
        list: Lista de configurações de banco de dados
    """
    configs = []
    
    if Config:
        # ✅ CONFIGURAÇÃO SEGURA - Prioridade máxima
        safe_config = Config.get_db_config()
        safe_config.update({
            'cursorclass': DictCursor,
            'name': 'config_segura_env'
        })
        configs.append(safe_config)
        logger.info("✅ Adicionada configuração segura baseada em variáveis de ambiente")
    
    # ⚠️ FALLBACKS LEGACY - Apenas para compatibilidade
    configs.extend(LEGACY_DB_CONFIGS)
    logger.warning("⚠️ Adicionadas configurações legacy - migre para .env o mais rápido possível!")
    
    return configs

def get_db_connection():
    """
    Cria uma conexão com o banco de dados.
    Tenta múltiplas configurações até encontrar uma que funcione.
    
    Returns:
        pymysql.Connection: Conexão ativa com o banco
    
    Raises:
        Exception: Erro ao conectar ao banco de dados
    """
    last_error = None
    configs = get_db_configs()
    
    for config in configs:
        try:
            config_copy = config.copy()
            name = config_copy.pop('name')
            connection = pymysql.connect(**config_copy)
            
            # Log diferenciado para configurações seguras vs legacy
            if 'segura' in name:
                logger.info(f"✅ Conectado ao banco usando {name}")
            else:
                logger.warning(f"⚠️ Conectado usando config legacy: {name}")
                
            return connection
        except Exception as e:
            last_error = e
            logger.warning(f"Falha na conexão com {config['name']}: {e}")
            continue
    
    # Se chegou aqui, nenhuma configuração funcionou
    logger.error(f"Erro ao conectar ao banco de dados com todas as configurações: {last_error}")
    raise Exception(f"Não foi possível conectar ao banco: {last_error}")

@contextmanager
def get_db_cursor(auto_commit=True):
    """
    Context manager para obter cursor do banco de dados.
    Gerencia automaticamente conexão e transações.

    Args:
        auto_commit (bool): Se deve fazer commit automático (padrão: True)

    Yields:
        tuple: (connection, cursor)
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        yield conn, cursor

        # COMMIT CONDICIONAL - apenas se auto_commit=True
        if auto_commit:
            conn.commit()
            logger.debug("✅ Transação commitada automaticamente")
        else:
            logger.debug("⏸️ Transação SEM commit automático - controle manual necessário")

    except Exception as e:
        if conn:
            conn.rollback()
            logger.error(f"❌ Rollback realizado devido ao erro: {e}")
        logger.error(f"Erro na operação de banco: {e}")
        raise
    finally:
        if conn:
            conn.close()

class DatabaseManager:
    """
    Classe para gerenciar operações avançadas do banco de dados.
    """
    
    @staticmethod
    def execute_query(query, params=None, fetch_one=False, fetch_all=True, manual_commit=False):
        """
        Executa uma query no banco de dados.

        Args:
            query (str): Query SQL a ser executada
            params (tuple): Parâmetros da query
            fetch_one (bool): Se deve retornar apenas um resultado
            fetch_all (bool): Se deve retornar todos os resultados
            manual_commit (bool): Se True, NÃO faz commit automático (para controle manual)

        Returns:
            dict/list/None: Resultado da query

        Raises:
            Exception: Erro de conexão ou execução da query
        """
        try:
            # DETECTAR TIPO DE OPERAÇÃO
            query_upper = query.strip().upper()
            is_select = query_upper.startswith('SELECT')

            logger.debug(f"🔍 execute_query: manual_commit={manual_commit}, is_select={is_select}, query={query[:50]}...")

            # USAR CONEXÃO MANUAL PARA CONTROLE TOTAL
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)

            try:
                cursor.execute(query, params or ())

                if fetch_one:
                    result = cursor.fetchone()
                    # SELECT sempre faz commit (seguro)
                    if is_select or not manual_commit:
                        conn.commit()
                    return result
                elif fetch_all:
                    result = cursor.fetchall()
                    # SELECT sempre faz commit (seguro)
                    if is_select or not manual_commit:
                        conn.commit()
                    return result
                else:
                    # INSERT/UPDATE/DELETE
                    result = cursor.lastrowid

                    if manual_commit:
                        logger.warning("⚠️ INSERT/UPDATE/DELETE executado SEM commit automático - commit manual necessário!")
                    else:
                        conn.commit()
                        logger.debug("✅ INSERT/UPDATE/DELETE commitado automaticamente")

                    return result

            except Exception as e:
                conn.rollback()
                logger.error(f"❌ Rollback realizado na execute_query: {e}")
                raise
            finally:
                cursor.close()
                conn.close()
        except Exception as e:
            # Melhor tratamento de erros específicos
            error_msg = str(e)
            if "Can't connect" in error_msg or "Connection refused" in error_msg:
                logger.error(f"Erro de conexão com banco de dados: {e}")
                raise Exception("Banco de dados indisponível. Verifique se o MySQL está rodando.")
            elif "Access denied" in error_msg:
                logger.error(f"Erro de autenticação no banco: {e}")
                if "***********" in error_msg:
                    raise Exception("Acesso negado: IP *********** não tem permissão para conectar ao MySQL. Configure as permissões do usuário no servidor.")
                else:
                    raise Exception("Erro de autenticação no banco de dados.")
            elif "Unknown database" in error_msg:
                logger.error(f"Database não encontrada: {e}")
                raise Exception("Base de dados 'controle_ponto' não encontrada.")
            else:
                logger.error(f"Erro na query SQL: {e}")
                raise Exception(f"Erro na operação do banco: {e}")
    
    @staticmethod
    def execute_transaction(queries_and_params):
        """
        Executa múltiplas queries em uma transação.
        
        Args:
            queries_and_params (list): Lista de tuplas (query, params)
            
        Returns:
            bool: True se a transação foi bem-sucedida
        """
        with get_db_cursor() as (conn, cursor):
            try:
                for query, params in queries_and_params:
                    cursor.execute(query, params or ())
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                logger.error(f"Erro na transação: {e}")
                raise
    
    @staticmethod
    def get_paginated_results(query, params=None, page=1, per_page=10):
        """
        Executa uma query com paginação.
        
        Args:
            query (str): Query SQL base
            params (tuple): Parâmetros da query
            page (int): Número da página (1-indexed)
            per_page (int): Registros por página
            
        Returns:
            dict: Dicionário com dados paginados
        """
        # Calcula offset
        offset = (page - 1) * per_page
        
        # Query para contar total de registros
        count_query = f"SELECT COUNT(*) as total FROM ({query}) as count_table"
        total_count = DatabaseManager.execute_query(count_query, params, fetch_one=True)['total']
        
        # Query paginada
        paginated_query = f"{query} LIMIT %s OFFSET %s"
        paginated_params = list(params or ()) + [per_page, offset]
        
        results = DatabaseManager.execute_query(paginated_query, tuple(paginated_params))
        
        # Calcula informações de paginação
        total_pages = (total_count + per_page - 1) // per_page
        has_prev = page > 1
        has_next = page < total_pages
        
        return {
            'data': results,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'total_pages': total_pages,
                'has_prev': has_prev,
                'has_next': has_next,
                'prev_num': page - 1 if has_prev else None,
                'next_num': page + 1 if has_next else None
            }
        }

# Funções específicas para funcionários
class FuncionarioQueries:
    """Queries específicas para a tabela de funcionários."""
    
    @staticmethod
    def get_all(page=1, per_page=10, search=None, status=None):
        """
        Obtém todos os funcionários ATIVOS com paginação e filtros.
        CORREÇÃO: Funcionários desligados (ativo = FALSE) não aparecem na lista.

        Args:
            page (int): Página atual
            per_page (int): Registros por página
            search (str): Termo de busca
            status (str): Filtro por status

        Returns:
            dict: Dados paginados dos funcionários
        """
        # ✅ CORREÇÃO: Especificar campos para evitar problemas com migração
        base_query = """
        SELECT
            id, empresa_id, cliente_atual_id, horario_trabalho_id,
            nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
            ctps_numero, ctps_serie_uf, pis_pasep,
            endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
            telefone1, telefone2, email,
            cargo, setor, setor_obra, empresa, matricula_empresa, data_admissao, tipo_contrato,
            digital_dedo1, digital_dedo2, foto_3x4, foto_url,
            nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, ativo, status_cadastro,
            data_cadastro, data_atualizacao,
            horas_semanais_obrigatorias,
            jornada_trabalho_id, usa_horario_empresa
        FROM funcionarios"""
        where_conditions = ["ativo = TRUE"]  # CORREÇÃO: Apenas funcionários ativos
        params = []

        if search:
            where_conditions.append("(nome_completo LIKE %s OR cpf LIKE %s OR matricula_empresa LIKE %s)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param])

        if status:
            where_conditions.append("status_cadastro = %s")
            params.append(status)

        # Sempre aplicar condições WHERE (pelo menos ativo = TRUE)
        base_query += " WHERE " + " AND ".join(where_conditions)

        base_query += " ORDER BY nome_completo"

        return DatabaseManager.get_paginated_results(base_query, tuple(params), page, per_page)
    
    @staticmethod
    def get_by_id(funcionario_id):
        """
        Obtém um funcionário por ID.
        
        Args:
            funcionario_id (int): ID do funcionário
            
        Returns:
            dict: Dados do funcionário
        """
        # ✅ CORREÇÃO: Especificar campos para evitar problemas com migração
        query = """
        SELECT
            id, empresa_id, cliente_atual_id, horario_trabalho_id,
            nome_completo, cpf, rg, data_nascimento, sexo, estado_civil, nacionalidade,
            ctps_numero, ctps_serie_uf, pis_pasep,
            endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
            telefone1, telefone2, email,
            cargo, setor, setor_obra, empresa, matricula_empresa, data_admissao, tipo_contrato,
            digital_dedo1, digital_dedo2, foto_3x4, foto_url,
            nivel_acesso, turno, tolerancia_ponto, banco_horas, hora_extra, ativo, status_cadastro,
            data_cadastro, data_atualizacao,
            horas_semanais_obrigatorias,
            jornada_trabalho_id, usa_horario_empresa
        FROM funcionarios
        WHERE id = %s"""
        return DatabaseManager.execute_query(query, (funcionario_id,), fetch_one=True)
    
    @staticmethod
    def get_with_epis(funcionario_id):
        """
        Obtém funcionário com seus EPIs e dados de jornada da empresa.

        ✅ CORREÇÃO CRÍTICA: Usa apenas dados da jornada da empresa como fonte única.
        Remove conflito entre campos individuais e jornada da empresa.

        Args:
            funcionario_id (int): ID do funcionário

        Returns:
            dict: Funcionário com EPIs e jornada da empresa
        """
        # ✅ CORREÇÃO CRÍTICA: Query considerando alocações ativas
        funcionario_query = """
        SELECT
            -- ✅ APENAS dados básicos do funcionário (SEM campos de jornada individual)
            f.id, f.empresa_id, f.cliente_atual_id, f.jornada_trabalho_id,
            f.nome_completo, f.cpf, f.rg, f.data_nascimento, f.sexo, f.estado_civil, f.nacionalidade,
            f.ctps_numero, f.ctps_serie_uf, f.pis_pasep,
            f.endereco_rua, f.endereco_bairro, f.endereco_cidade, f.endereco_cep, f.endereco_estado,
            f.telefone1, f.telefone2, f.email,
            f.cargo, f.setor, f.setor_obra, f.empresa, f.matricula_empresa, f.data_admissao, f.tipo_contrato,
            f.digital_dedo1, f.digital_dedo2, f.foto_3x4, f.foto_url,
            f.nivel_acesso, f.turno, f.tolerancia_ponto, f.banco_horas, f.hora_extra,
            f.ativo, f.status_cadastro, f.data_cadastro, f.data_atualizacao,
            f.biometria_qualidade_1, f.biometria_qualidade_2, f.biometria_data_cadastro,
            f.salario_base, f.tipo_pagamento, f.valor_hora, f.valor_hora_extra, f.percentual_hora_extra,
            f.vale_transporte, f.vale_alimentacao, f.outros_beneficios,
            f.desconto_inss, f.desconto_irrf, f.observacoes_pagamento, f.data_ultima_alteracao_salario,
            f.biometria_qualidade, f.ultimo_login, f.tentativas_biometria, f.status_biometria, f.status,
            f.epi_obrigatorio_json, f.epi_treinamento_data, f.epi_responsavel, f.epi_observacoes, f.epi_termo_assinado,
            f.horas_semanais_obrigatorias,

            -- Dados da empresa
            e.nome_fantasia as empresa_nome,
            e.razao_social as empresa_razao_social,

            -- ✅ PRIORIDADE 1: Jornada da alocação ativa (se existir)
            COALESCE(jt_alocacao.nome_jornada, jt.nome_jornada) as jornada_nome_jornada,
            COALESCE(jt_alocacao.tipo_jornada, jt.tipo_jornada) as tipo_jornada,
            COALESCE(jt_alocacao.seg_qui_entrada, jt.seg_qui_entrada) as jornada_seg_qui_entrada,
            COALESCE(jt_alocacao.seg_qui_saida, jt.seg_qui_saida) as jornada_seg_qui_saida,
            COALESCE(jt_alocacao.sexta_entrada, jt.sexta_entrada) as jornada_sexta_entrada,
            COALESCE(jt_alocacao.sexta_saida, jt.sexta_saida) as jornada_sexta_saida,
            COALESCE(jt_alocacao.intervalo_inicio, jt.intervalo_inicio) as jornada_intervalo_inicio,
            COALESCE(jt_alocacao.intervalo_fim, jt.intervalo_fim) as jornada_intervalo_fim,
            COALESCE(jt_alocacao.tolerancia_entrada_minutos, jt.tolerancia_entrada_minutos) as jornada_tolerancia_entrada_minutos,

            -- Dados do horário (tabela horarios_trabalho) - fallback apenas se necessário
            ht.nome_horario,
            ht.entrada_manha,
            ht.saida_almoco,
            ht.entrada_tarde,
            ht.saida,
            ht.tolerancia_minutos,

            -- ✅ Informações de alocação
            fa.id as alocacao_id,
            fa.empresa_cliente_id,
            ec.razao_social as cliente_nome,
            fa.cargo_no_cliente

        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id

        -- ✅ PRIORIDADE: Buscar alocação ativa primeiro
        LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id AND fa.ativo = TRUE
        LEFT JOIN empresas ec ON fa.empresa_cliente_id = ec.id
        LEFT JOIN jornadas_trabalho jt_alocacao ON fa.jornada_trabalho_id = jt_alocacao.id

        -- ✅ FALLBACK: Jornada da empresa do funcionário (SEMPRE busca jornada padrão da empresa)
        LEFT JOIN jornadas_trabalho jt ON (
            f.jornada_trabalho_id = jt.id OR
            (f.jornada_trabalho_id IS NULL AND jt.empresa_id = f.empresa_id AND jt.padrao = 1 AND jt.ativa = 1)
        )
        LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id

        WHERE f.id = %s
        """

        funcionario = DatabaseManager.execute_query(funcionario_query, (funcionario_id,), fetch_one=True)

        if funcionario:
            # ✅ LÓGICA DE PRIORIDADE PARA JORNADAS
            if funcionario.get('alocacao_id'):
                # Funcionário ALOCADO - jornada vem da alocação
                logger.info(f"✅ Funcionário {funcionario_id}: ALOCADO para '{funcionario.get('cliente_nome', 'N/A')}' - usando jornada da alocação")
                logger.info(f"   📋 Jornada: '{funcionario.get('jornada_nome_jornada', 'N/A')}'")
            elif funcionario.get('jornada_seg_qui_entrada'):
                # Funcionário NÃO alocado - jornada vem da empresa
                logger.info(f"✅ Funcionário {funcionario_id}: NÃO alocado - usando jornada da empresa '{funcionario.get('jornada_nome_jornada', 'N/A')}'")
            elif funcionario.get('entrada_manha'):
                # Fallback para horários antigos - NÃO USAR MAIS
                logger.warning(f"🔄 Funcionário {funcionario_id}: FALLBACK DETECTADO - aplicando jornada da empresa")

                # Buscar jornada padrão da empresa e aplicar
                jornada_empresa = DatabaseManager.execute_query("""
                    SELECT id, nome_jornada, seg_qui_entrada, seg_qui_saida,
                           sexta_entrada, sexta_saida, intervalo_inicio, intervalo_fim,
                           tolerancia_entrada_minutos
                    FROM jornadas_trabalho
                    WHERE empresa_id = %s AND padrao = 1 AND ativa = 1
                    LIMIT 1
                """, (funcionario['empresa_id'],), fetch_one=True)

                if jornada_empresa:
                    # Aplicar jornada da empresa
                    DatabaseManager.execute_query("""
                        UPDATE funcionarios
                        SET jornada_trabalho_id = %s, usa_horario_empresa = TRUE
                        WHERE id = %s
                    """, (jornada_empresa['id'], funcionario_id), fetch_all=False)

                    # Atualizar dados do funcionário
                    funcionario['jornada_trabalho_id'] = jornada_empresa['id']
                    funcionario['jornada_nome_jornada'] = jornada_empresa['nome_jornada']
                    funcionario['jornada_seg_qui_entrada'] = jornada_empresa['seg_qui_entrada']
                    funcionario['jornada_seg_qui_saida'] = jornada_empresa['seg_qui_saida']
                    funcionario['jornada_sexta_entrada'] = jornada_empresa['sexta_entrada']
                    funcionario['jornada_sexta_saida'] = jornada_empresa['sexta_saida']
                    funcionario['jornada_intervalo_inicio'] = jornada_empresa['intervalo_inicio']
                    funcionario['jornada_intervalo_fim'] = jornada_empresa['intervalo_fim']
                    funcionario['jornada_tolerancia_entrada_minutos'] = jornada_empresa['tolerancia_entrada_minutos']

                    logger.info(f"✅ Jornada da empresa aplicada automaticamente: {jornada_empresa['nome_jornada']}")
                else:
                    logger.error(f"❌ Empresa {funcionario['empresa_id']} não tem jornada padrão!")
            else:
                logger.warning(f"⚠️ Funcionário {funcionario_id}: Nenhuma jornada encontrada")

            # Buscar EPIs do funcionário
            try:
                # ✅ CORREÇÃO: Ordenar EPIs por ID DESC para mostrar os mais recentes primeiro
                epis_query = "SELECT * FROM epis WHERE funcionario_id = %s ORDER BY id DESC"
                funcionario['epis'] = DatabaseManager.execute_query(epis_query, (funcionario_id,))
            except Exception as e:
                # Se tabela EPIs não existir ou houver erro, inicializa lista vazia
                logger.warning(f"Erro ao buscar EPIs para funcionário {funcionario_id}: {e}")
                funcionario['epis'] = []
        return funcionario
    
    @staticmethod
    def delete_funcionario(funcionario_id):
        """
        VERSÃO PROFISSIONAL: Deslica funcionário em vez de excluir.
        Mantém histórico para auditoria e compliance.

        Args:
            funcionario_id (int): ID do funcionário

        Returns:
            bool: True se desligado com sucesso
        """
        # Redireciona para a função profissional de desligamento
        return FuncionarioQueries.desligar_funcionario(
            funcionario_id=funcionario_id,
            motivo_desligamento='Demissao_sem_justa_causa',  # Padrão
            observacoes='Desligamento via sistema - função delete_funcionario()',
            usuario_responsavel=None  # Será definido no contexto da aplicação
        )
    
    @staticmethod
    def get_next_matricula():
        """
        Obtém a próxima matrícula disponível.
        VERSÃO PROFISSIONAL: Consulta funcionários ativos E desligados
        para garantir que nenhuma matrícula seja reutilizada.

        Returns:
            str: Próxima matrícula formatada
        """
        try:
            # Consulta TODAS as matrículas (ativas + desligadas) para evitar reutilização
            query = """
            SELECT MAX(max_matricula) as max_matricula FROM (
                SELECT MAX(CAST(matricula_empresa AS UNSIGNED)) as max_matricula
                FROM funcionarios
                WHERE matricula_empresa REGEXP '^[0-9]+$'

                UNION ALL

                SELECT MAX(CAST(matricula_empresa AS UNSIGNED)) as max_matricula
                FROM funcionarios_desligados
                WHERE matricula_empresa REGEXP '^[0-9]+$'
            ) as todas_matriculas
            """
            result = DatabaseManager.execute_query(query, fetch_one=True)

            if result['max_matricula'] is None:
                next_num = 1
            else:
                next_num = result['max_matricula'] + 1

            # Log para auditoria
            logger.info(f"🔍 [MATRÍCULA] Próxima matrícula gerada: {next_num:04d} (baseada em consulta unificada)")

            return f"{next_num:04d}"
        except Exception as e:
            logger.error(f"Erro ao obter próxima matrícula: {e}")
            return "0001"

    @staticmethod
    def restaurar_funcionario(funcionario_id_original):
        """
        Restaura funcionário da tabela de desligados de volta para a tabela principal.
        INCLUI restauração de registros de ponto e dados relacionados.

        Args:
            funcionario_id_original (int): ID original do funcionário na tabela funcionarios_desligados

        Returns:
            dict: {'success': bool, 'message': str, 'funcionario_id': int}
        """
        try:
            with get_db_cursor() as (conn, cursor):
                # 1. Buscar dados do funcionário desligado
                cursor.execute("SELECT * FROM funcionarios_desligados WHERE funcionario_id_original = %s", (funcionario_id_original,))
                funcionario_desligado = cursor.fetchone()

                if not funcionario_desligado:
                    return {'success': False, 'message': f'Funcionário com ID original {funcionario_id_original} não encontrado na tabela de desligados'}

                logger.info(f"🔄 Iniciando restauração do funcionário {funcionario_desligado['nome_completo']} (ID original: {funcionario_id_original})")

                # 2. Verificar se já existe funcionário com a mesma matrícula
                cursor.execute("SELECT id, ativo, status_cadastro FROM funcionarios WHERE matricula_empresa = %s", (funcionario_desligado['matricula_empresa'],))
                funcionario_existente = cursor.fetchone()

                if funcionario_existente:
                    # Se existe mas está inativo, podemos atualizar em vez de criar novo
                    if not funcionario_existente['ativo']:
                        logger.info(f"🔄 Funcionário inativo encontrado com matrícula {funcionario_desligado['matricula_empresa']}. Atualizando em vez de criar novo.")

                        # CORREÇÃO SIMPLES: Apenas ativar o funcionário existente
                        update_query = """
                        UPDATE funcionarios
                        SET status_cadastro = 'Ativo', ativo = TRUE
                        WHERE id = %s
                        """

                        valores = [funcionario_existente['id']]

                        cursor.execute(update_query, valores)
                        novo_funcionario_id = funcionario_existente['id']
                        logger.info(f"✅ Funcionário reativado com ID: {novo_funcionario_id}")

                        # Pular para a etapa de limpeza (não precisa atualizar IDs relacionados)
                        skip_id_update = True
                    else:
                        return {'success': False, 'message': f'Já existe um funcionário ativo com a matrícula {funcionario_desligado["matricula_empresa"]}'}
                else:
                    skip_id_update = False

                # 3. Se não foi atualização, criar novo funcionário
                if not skip_id_update:
                    # Restaurar funcionário na tabela principal (sem os campos específicos de desligamento)
                    # CORREÇÃO: Apenas campos essenciais obrigatórios
                    campos_funcionario = [
                        'nome_completo', 'cpf', 'rg', 'data_nascimento', 'sexo', 'estado_civil', 'nacionalidade',
                        'pis_pasep', 'endereco_cep', 'endereco_estado', 'telefone1',
                        'cargo', 'setor_obra', 'matricula_empresa', 'data_admissao', 'tipo_contrato',
                        'nivel_acesso', 'turno', 'tolerancia_ponto', 'status_cadastro', 'empresa_id'
                    ]

                    # Adicionar campos opcionais apenas se não forem NULL
                    campos_opcionais = [
                        'ctps_numero', 'ctps_serie_uf', 'endereco_rua', 'endereco_bairro', 'endereco_cidade',
                        'telefone2', 'email', 'setor', 'banco_horas', 'hora_extra', 'horas_trabalho_obrigatorias',
                        'jornada_trabalho_id', 'horario_trabalho_id', 'digital_dedo1', 'digital_dedo2', 'foto_3x4'
                    ]

                    for campo in campos_opcionais:
                        if funcionario_desligado.get(campo) is not None:
                            campos_funcionario.append(campo)

                    placeholders = ', '.join(['%s'] * len(campos_funcionario))
                    campos_str = ', '.join(campos_funcionario)

                    insert_query = f"""
                    INSERT INTO funcionarios ({campos_str})
                    VALUES ({placeholders})
                    """

                    # CORREÇÃO: Garantir valores padrão para campos NOT NULL
                    valores_padrao = {
                        'status_cadastro': 'Ativo',
                        'rg': 'N/A',
                        'pis_pasep': '00000000000',
                        'endereco_cep': '00000-000',
                        'endereco_estado': 'SP',
                        'telefone1': '(00) 00000-0000',
                        'setor_obra': 'N/A',
                        'tipo_contrato': 'CLT',
                        'email': None  # Email pode ser NULL, não usar valor inválido
                    }

                    valores = []
                    for campo in campos_funcionario:
                        valor = funcionario_desligado[campo]

                        # Se valor é NULL e campo tem padrão, usar padrão
                        if valor is None and campo in valores_padrao:
                            valor = valores_padrao[campo]
                        # Se valor é NULL e campo é obrigatório, usar valor genérico
                        elif valor is None:
                            if campo == 'status_cadastro':
                                valor = 'Ativo'
                            elif 'data' in campo:
                                valor = '1900-01-01'
                            elif campo in ['sexo']:
                                valor = 'M'
                            elif campo in ['estado_civil']:
                                valor = 'Solteiro'
                            elif campo in ['nacionalidade']:
                                valor = 'Brasileiro'
                            elif '_id' in campo:  # Campos de ID (INTEGER)
                                valor = None  # Manter NULL para campos de ID opcionais
                            else:
                                valor = 'N/A'

                        valores.append(valor)

                    logger.info(f"🔄 Executando INSERT com {len(valores)} valores...")
                    cursor.execute(insert_query, valores)

                    novo_funcionario_id = cursor.lastrowid
                    logger.info(f"✅ INSERT executado. Novo ID: {novo_funcionario_id}")

                    # CORREÇÃO ADICIONAL: Garantir que o campo ativo seja TRUE
                    cursor.execute("UPDATE funcionarios SET ativo = TRUE WHERE id = %s", (novo_funcionario_id,))
                    logger.info(f"✅ Campo ativo atualizado para TRUE")

                    # Verificar se foi realmente inserido
                    cursor.execute("SELECT id, nome_completo, ativo FROM funcionarios WHERE id = %s", (novo_funcionario_id,))
                    verificacao = cursor.fetchone()
                    if verificacao:
                        logger.info(f"✅ Verificação: Funcionário {verificacao['nome_completo']} inserido com ID {verificacao['id']}, ativo: {verificacao['ativo']}")
                    else:
                        logger.error(f"❌ ERRO: Funcionário não encontrado após INSERT!")
                        return {'success': False, 'message': 'Funcionário não foi inserido corretamente'}

                    logger.info(f"✅ Funcionário restaurado na tabela principal com novo ID: {novo_funcionario_id}")

                    # 4. CRÍTICO: Atualizar TODOS os dados relacionados para o novo ID
                    logger.info(f"🔄 Atualizando dados relacionados do ID {funcionario_id_original} para {novo_funcionario_id}...")

                    tabelas_para_atualizar = [
                        'registros_ponto', 'epis', 'banco_horas', 'aprovacoes_horas_extras',
                        'justificativas_ponto', 'historico_funcionario', 'funcionario_cliente_alocacao',
                        'funcionario_alocacoes', 'historico_alocacoes', 'alertas_ponto',
                        'historico_inferencias', 'configuracoes_hora_extra'
                    ]

                    dados_atualizados = {}
                    for tabela in tabelas_para_atualizar:
                        try:
                            cursor.execute(f"UPDATE {tabela} SET funcionario_id = %s WHERE funcionario_id = %s",
                                         (novo_funcionario_id, funcionario_id_original))
                            if cursor.rowcount > 0:
                                dados_atualizados[tabela] = cursor.rowcount
                                logger.info(f"✅ Atualizados {cursor.rowcount} registros na tabela {tabela}")
                        except Exception as e:
                            logger.warning(f"⚠️ Erro ao atualizar tabela {tabela}: {e}")
                else:
                    dados_atualizados = {'funcionario_atualizado': 1}

                # 5. Remover da tabela de desligados (restauração completa)
                cursor.execute("DELETE FROM funcionarios_desligados WHERE funcionario_id_original = %s", (funcionario_id_original,))
                logger.info(f"✅ Funcionário removido da tabela de desligados")

                # 6. Registrar log de restauração
                log_query = """
                INSERT INTO log_desligamentos (
                    funcionario_id_original, nome_funcionario, matricula, motivo_desligamento,
                    data_desligamento, usuario_responsavel, observacoes
                ) VALUES (%s, %s, %s, %s, NOW(), %s, %s)
                """

                # Buscar ID do usuário atual (com proteção para contexto)
                usuario_responsavel_id = None
                try:
                    from flask import session
                    if 'usuario' in session:
                        cursor.execute("SELECT id FROM usuarios WHERE usuario = %s", (session['usuario'],))
                        user_result = cursor.fetchone()
                        if user_result:
                            usuario_responsavel_id = user_result['id']
                except (RuntimeError, ImportError):
                    # Fora do contexto de requisição - usar usuário padrão
                    cursor.execute("SELECT id FROM usuarios WHERE usuario = 'admin'")
                    user_result = cursor.fetchone()
                    if user_result:
                        usuario_responsavel_id = user_result['id']

                cursor.execute(log_query, (
                    novo_funcionario_id, funcionario_desligado['nome_completo'],
                    funcionario_desligado['matricula_empresa'], 'RESTAURACAO',
                    usuario_responsavel_id, f'Funcionário restaurado. Dados atualizados: {dados_atualizados}'
                ))

                resumo_dados = f"Registros restaurados: {sum(dados_atualizados.values())} em {len(dados_atualizados)} tabelas"
                return {'success': True, 'message': f'Funcionário {funcionario_desligado["nome_completo"]} restaurado com sucesso. {resumo_dados}', 'funcionario_id': novo_funcionario_id}

        except Exception as e:
            logger.error(f"🚨 [ERRO CRÍTICO] Erro ao restaurar funcionário {funcionario_id_original}: {e}")
            logger.error(f"🚨 [TRACEBACK] {traceback.format_exc()}")
            return {'success': False, 'message': f'Erro ao restaurar funcionário: {str(e)}'}

    @staticmethod
    def desligar_funcionario(funcionario_id, motivo_desligamento, observacoes=None, usuario_responsavel=None):
        """
        Deslica funcionário de forma profissional (soft delete).
        Move o funcionário para a tabela de histórico em vez de deletar.
        CORRIGIDO: Lida com dependências de chave estrangeira.

        Args:
            funcionario_id (int): ID do funcionário
            motivo_desligamento (str): Motivo do desligamento
            observacoes (str, optional): Observações sobre o desligamento
            usuario_responsavel (int, optional): ID do usuário responsável

        Returns:
            bool: True se desligado com sucesso
        """
        try:
            # CRÍTICO: Capturar o ID do usuário real da sessão para auditoria
            if usuario_responsavel is None:
                try:
                    from flask import session
                    if 'usuario' in session:
                        # Buscar o ID do usuário na tabela usuarios
                        with get_db_cursor() as (temp_conn, temp_cursor):
                            temp_cursor.execute("SELECT id FROM usuarios WHERE usuario = %s", (session['usuario'],))
                            user_result = temp_cursor.fetchone()
                            if user_result:
                                usuario_responsavel = user_result['id']
                            else:
                                raise ValueError(f"ERRO CRÍTICO: Usuário '{session['usuario']}' não encontrado na tabela usuarios. Operação cancelada por segurança.")
                    else:
                        raise ValueError("ERRO CRÍTICO: Não foi possível identificar o usuário responsável pelo desligamento. Operação cancelada por segurança.")
                except (RuntimeError, ImportError):
                    # Contexto de request não disponível (ex: testes ou scripts)
                    # Usar usuário admin padrão
                    with get_db_cursor() as (temp_conn, temp_cursor):
                        temp_cursor.execute("SELECT id FROM usuarios WHERE usuario = 'admin'")
                        user_result = temp_cursor.fetchone()
                        if user_result:
                            usuario_responsavel = user_result['id']
                        else:
                            # Se não encontrar admin, usar ID 1 como fallback
                            usuario_responsavel = 1

            with get_db_cursor() as (conn, cursor):
                # 1. Buscar dados completos do funcionário
                cursor.execute("SELECT * FROM funcionarios WHERE id = %s", (funcionario_id,))
                funcionario = cursor.fetchone()

                if not funcionario:
                    logger.warning(f"Funcionário {funcionario_id} não encontrado para desligamento")
                    return False

                logger.info(f"🔄 Iniciando desligamento do funcionário {funcionario['nome_completo']} (ID: {funcionario_id})")

                # 2. Inserir na tabela de desligados
                insert_query = """
                INSERT INTO funcionarios_desligados (
                    funcionario_id_original, nome_completo, cpf, rg, data_nascimento, sexo,
                    estado_civil, nacionalidade, ctps_numero, ctps_serie_uf, pis_pasep,
                    endereco_rua, endereco_bairro, endereco_cidade, endereco_cep, endereco_estado,
                    telefone1, telefone2, email, cargo, setor, setor_obra, matricula_empresa,
                    data_admissao, tipo_contrato, nivel_acesso, turno, tolerancia_ponto,
                    banco_horas, hora_extra, status_cadastro, horas_trabalho_obrigatorias,
                    empresa_id, jornada_trabalho_id, horario_trabalho_id,
                    digital_dedo1, digital_dedo2, foto_3x4,
                    motivo_desligamento, observacoes_desligamento, usuario_responsavel_desligamento,
                    data_criacao_original, data_ultima_atualizacao
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
                """

                cursor.execute(insert_query, (
                    funcionario['id'], funcionario['nome_completo'], funcionario['cpf'],
                    funcionario.get('rg', 'N/A'), funcionario['data_nascimento'], funcionario['sexo'],
                    funcionario['estado_civil'], funcionario.get('nacionalidade', 'Brasileiro'),
                    funcionario.get('ctps_numero', 'N/A'), funcionario.get('ctps_serie_uf', 'N/A'), funcionario.get('pis_pasep', 'N/A'),
                    funcionario.get('endereco_rua'), funcionario.get('endereco_bairro'),
                    funcionario.get('endereco_cidade'), funcionario.get('endereco_cep', '00000-000'), funcionario.get('endereco_estado', 'N/A'),
                    funcionario.get('telefone1', 'N/A'), funcionario.get('telefone2'), funcionario.get('email'),
                    funcionario.get('cargo', 'N/A'), funcionario.get('setor'), funcionario.get('setor_obra'),
                    funcionario['matricula_empresa'], funcionario['data_admissao'],
                    # Mapear tipo_contrato para valores compatíveis
                    'CLT' if funcionario.get('tipo_contrato') not in ['CLT','PJ','Estagio','Temporario'] else funcionario.get('tipo_contrato', 'CLT'),
                    # Mapear nivel_acesso para valores compatíveis
                    funcionario.get('nivel_acesso', 'Funcionario'),
                    # Mapear turno para valores compatíveis
                    'Diurno' if funcionario.get('turno') not in ['Diurno','Noturno','Misto'] else funcionario.get('turno', 'Diurno'),
                    funcionario.get('tolerancia_ponto', 10),
                    funcionario.get('banco_horas', 1), funcionario.get('hora_extra', 1), funcionario.get('status_cadastro', 'Ativo'),
                    funcionario.get('horas_trabalho_obrigatorias', 8.00), funcionario.get('empresa_id'),
                    funcionario.get('jornada_trabalho_id'), funcionario.get('horario_trabalho_id'),
                    funcionario.get('digital_dedo1'), funcionario.get('digital_dedo2'), funcionario.get('foto_3x4'),
                    motivo_desligamento, observacoes, usuario_responsavel,
                    funcionario.get('data_criacao'), funcionario.get('data_ultima_atualizacao')
                ))

                logger.info(f"✅ Funcionário copiado para tabela de desligados")

                # 3. Registrar log de auditoria
                log_query = """
                INSERT INTO log_desligamentos (
                    funcionario_id_original, nome_funcionario, matricula, motivo_desligamento,
                    data_desligamento, usuario_responsavel, observacoes
                ) VALUES (%s, %s, %s, %s, NOW(), %s, %s)
                """

                cursor.execute(log_query, (
                    funcionario['id'], funcionario['nome_completo'], funcionario['matricula_empresa'],
                    motivo_desligamento, usuario_responsavel, observacoes
                ))

                logger.info(f"✅ Log de auditoria registrado")

                # 4. CRÍTICO: PRESERVAR TODOS OS DADOS PARA RESTAURAÇÃO FUTURA
                logger.info(f"🔄 Preservando dados históricos para possível restauração...")

                # APENAS remover notificações temporárias (não são dados históricos importantes)
                cursor.execute("DELETE FROM notificacoes_rh WHERE funcionario_id = %s", (funcionario_id,))
                if cursor.rowcount > 0:
                    logger.info(f"✅ Removidas {cursor.rowcount} notificações RH temporárias")

                # PRESERVAR TODOS OS OUTROS DADOS:
                # - registros_ponto: PRESERVADOS para histórico de trabalho
                # - epis: PRESERVADOS para histórico de equipamentos
                # - banco_horas: PRESERVADOS para cálculos trabalhistas
                # - aprovacoes_horas_extras: PRESERVADAS para auditoria
                # - justificativas_ponto: PRESERVADAS para histórico
                # - historico_funcionario: PRESERVADO para auditoria
                # - funcionario_alocacoes: PRESERVADAS para histórico de projetos
                # - historico_alocacoes: PRESERVADO para auditoria
                # - alertas_ponto: PRESERVADOS para análise
                # - historico_inferencias: PRESERVADO para auditoria
                # - configuracoes_hora_extra: PRESERVADAS para histórico

                logger.info(f"✅ TODOS OS DADOS HISTÓRICOS PRESERVADOS para possível restauração")

                # 5. NOVA ABORDAGEM: Marcar como inativo em vez de deletar
                # Isso preserva a integridade referencial e mantém os registros de ponto
                cursor.execute("""
                    UPDATE funcionarios
                    SET ativo = FALSE,
                        status_cadastro = 'Inativo'
                    WHERE id = %s
                """, (funcionario_id,))

                if cursor.rowcount == 0:
                    logger.warning(f"Nenhum funcionário atualizado para inativo (ID {funcionario_id})")
                    return False

                conn.commit()
                logger.info(f"✅ Funcionário {funcionario['nome_completo']} (matrícula {funcionario['matricula_empresa']}) desligado com sucesso. Motivo: {motivo_desligamento}")
                return True

        except Exception as e:
            logger.error(f"🚨 [ERRO CRÍTICO] Erro ao desligar funcionário {funcionario_id}: {e}")
            logger.error(f"🚨 [TRACEBACK] {traceback.format_exc()}")
            return False