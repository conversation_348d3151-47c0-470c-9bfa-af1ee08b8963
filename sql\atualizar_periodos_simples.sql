USE controle_ponto;

-- Alterar estrutura da tabela para aceitar novos valores
ALTER TABLE dia_dados MODIFY COLUMN turno VARCHAR(20) NOT NULL;

-- Limpar dados existentes
DELETE FROM dia_dados;

-- Inserir períodos conforme suas especificações
INSERT INTO dia_dados (turno, horario_inicio, horario_fim, descricao, ordem_prioridade, ativo) VALUES
('Manha', '06:00:00', '11:00:00', 'Período da manhã - 06:00 às 11:00', 1, TRUE),
('Intervalo', '11:00:00', '14:00:00', 'Período de intervalo - 11:00 às 14:00', 2, TRUE),
('Tarde', '14:00:00', '18:00:00', 'Período da tarde - 14:00 às 18:00', 3, TRUE),
('Fim_Diurno', '18:00:00', '21:00:00', 'Fim da jornada diurna - 18:00 às 21:00', 4, TRUE),
('Noite_Inicio', '21:00:00', '00:00:00', 'In<PERSON>cio período noturno - 21:00 às 00:00', 5, TRUE),
('Noite_Intervalo', '00:00:00', '02:00:00', 'Intervalo noturno - 00:00 às 02:00', 6, TRUE),
('Noite_Fim', '02:00:00', '05:59:00', 'Fim período noturno - 02:00 às 05:59', 7, TRUE);

-- Verificar dados inseridos
SELECT turno, TIME_FORMAT(horario_inicio, '%H:%i') as inicio, TIME_FORMAT(horario_fim, '%H:%i') as fim, descricao FROM dia_dados ORDER BY ordem_prioridade;
