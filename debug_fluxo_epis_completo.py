#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para debugar o fluxo completo de EPIs
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
import json

def debug_fluxo_epis():
    """Debug completo do fluxo de EPIs"""
    print("🔍 DEBUG COMPLETO DO FLUXO DE EPIs")
    print("=" * 70)
    
    try:
        db = DatabaseManager()
        
        # 1. Simular dados que vêm do formulário
        print("\n1. SIMULANDO DADOS DO FORMULÁRIO...")
        
        # Dados como viriam do formulário HTML
        dados_formulario_simulado = {
            'nome_completo': 'RICHARDSON CARDOSO RODRIGUES',
            'empresa_id': '11',
            'cpf': '123.456.789-00',
            'cargo': 'DESENVOLVEDOR',
            
            # EPIs como viriam do formulário
            'epis[0][epi_nome]': 'Capacete de Segurança DEBUG',
            'epis[0][epi_ca]': '12345',
            'epis[0][epi_data_entrega]': '2025-07-05',
            'epis[0][epi_data_validade]': '2026-07-05',
            'epis[0][epi_observacoes]': 'EPI de teste via debug',
            
            'epis[1][epi_nome]': 'Luvas de Proteção DEBUG',
            'epis[1][epi_ca]': '54321',
            'epis[1][epi_data_entrega]': '2025-07-05',
            'epis[1][epi_data_validade]': '2026-07-05',
            'epis[1][epi_observacoes]': 'Segundo EPI de teste'
        }
        
        print(f"📋 Dados simulados do formulário:")
        for key, value in dados_formulario_simulado.items():
            if 'epis[' in key:
                print(f"   {key}: {value}")
        
        # 2. Testar função _extrair_dados_epis
        print(f"\n2. TESTANDO EXTRAÇÃO DE EPIs...")
        
        # Simular a função _extrair_dados_epis
        epis_extraidos = []
        
        # Buscar todos os campos com padrão epis[index][campo]
        for key in dados_formulario_simulado.keys():
            if key.startswith('epis[') and '][' in key:
                try:
                    start = key.find('[') + 1
                    end = key.find(']')
                    index = int(key[start:end])
                    
                    campo_start = key.find('][') + 2
                    campo_end = key.rfind(']')
                    campo = key[campo_start:campo_end]
                    
                    # Garantir que existe EPI no índice
                    while len(epis_extraidos) <= index:
                        epis_extraidos.append({})
                    
                    # Adicionar valor ao EPI
                    valor = dados_formulario_simulado.get(key, '').strip()
                    epis_extraidos[index][campo] = valor
                    
                except (ValueError, IndexError):
                    print(f"   ⚠️ Campo EPI inválido: {key}")
                    continue
        
        print(f"📊 EPIs extraídos: {len(epis_extraidos)}")
        for i, epi in enumerate(epis_extraidos):
            print(f"   EPI {i+1}: {epi}")
        
        # 3. Testar função _processar_epis_funcionario
        print(f"\n3. TESTANDO PROCESSAMENTO DE EPIs...")
        
        funcionario_id = 1  # Richardson
        
        print(f"   📋 Processando {len(epis_extraidos)} EPIs para funcionário {funcionario_id}")
        
        # Buscar EPIs existentes
        epis_existentes = db.execute_query(
            "SELECT id FROM epis WHERE funcionario_id = %s",
            (funcionario_id,)
        )
        ids_existentes = {str(epi['id']) for epi in epis_existentes}
        print(f"   📊 EPIs existentes no banco: {len(ids_existentes)} - IDs: {ids_existentes}")
        
        # Processar cada EPI
        for i, epi in enumerate(epis_extraidos):
            epi_id = epi.get('id', '').strip()
            epi_nome = epi.get('epi_nome', '').strip()
            
            print(f"\n   📦 Processando EPI {i+1}: '{epi_nome}'")
            print(f"      ID: '{epi_id}'")
            print(f"      Dados: {epi}")
            
            if epi_id and epi_id in ids_existentes:
                print(f"      ✏️ Seria ATUALIZADO (EPI existente)")
            else:
                print(f"      ➕ Seria CRIADO (novo EPI)")
                
                # Simular criação
                sql_criar = """
                INSERT INTO epis (funcionario_id, epi_nome, epi_ca, epi_data_entrega, epi_data_validade, epi_observacoes, status_epi)
                VALUES (%s, %s, %s, %s, %s, %s, 'entregue')
                """
                
                params = (
                    funcionario_id,
                    epi.get('epi_nome'),
                    epi.get('epi_ca') or None,
                    epi.get('epi_data_entrega') or None,
                    epi.get('epi_data_validade') or None,
                    epi.get('epi_observacoes') or None
                )
                
                print(f"      📋 SQL: {sql_criar}")
                print(f"      📋 Params: {params}")
                
                try:
                    novo_id = db.execute_query(sql_criar, params, fetch_all=False)
                    print(f"      ✅ EPI criado com sucesso! ID: {novo_id}")
                except Exception as e:
                    print(f"      ❌ Erro ao criar EPI: {e}")
        
        # 4. Verificar resultado final
        print(f"\n4. VERIFICANDO RESULTADO FINAL...")
        
        from utils.database import FuncionarioQueries
        funcionario_final = FuncionarioQueries.get_with_epis(1)
        
        if funcionario_final and funcionario_final.get('epis'):
            epis_finais = funcionario_final['epis']
            print(f"📊 EPIs do Richardson após processamento: {len(epis_finais)}")
            for epi in epis_finais:
                print(f"   - {epi.get('epi_nome')} (CA: {epi.get('epi_ca')}, Status: {epi.get('status_epi')})")
        else:
            print(f"❌ Nenhum EPI encontrado para Richardson")
        
        # 5. Testar template
        print(f"\n5. SIMULANDO DADOS PARA O TEMPLATE...")
        
        template_data = {
            'funcionario': funcionario_final,
            'epis': funcionario_final.get('epis', []) if funcionario_final else []
        }
        
        print(f"📋 Dados que o template receberia:")
        print(f"   funcionario.epis: {len(template_data['funcionario'].get('epis', []))} EPIs")
        print(f"   epis (variável): {len(template_data['epis'])} EPIs")
        
        if template_data['epis']:
            print(f"   📦 EPIs para exibição:")
            for epi in template_data['epis']:
                print(f"      - {epi.get('epi_nome')} (CA: {epi.get('epi_ca')})")
                print(f"        Status: {epi.get('status_epi')}")
                print(f"        Data entrega: {epi.get('epi_data_entrega')}")
        
        # 6. Limpar EPIs de teste
        print(f"\n6. LIMPANDO EPIs DE TESTE...")
        sql_limpar = "DELETE FROM epis WHERE funcionario_id = 1 AND epi_nome LIKE '%DEBUG%'"
        result = db.execute_query(sql_limpar, fetch_all=False)
        print(f"   🧹 EPIs de teste removidos")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o debug: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    debug_fluxo_epis()
