USE controle_ponto;

-- Verificar registros do funcionário 35 no dia 2025-07-14
SELECT 
    id, 
    DATE(data_hora) as data, 
    TIME(data_hora) as hora,
    tipo_registro, 
    status_justificativa 
FROM registros_ponto 
WHERE funcionario_id = 35 
AND DATE(data_hora) = '2025-07-14' 
ORDER BY data_hora;

-- Verificar todos os status de justificativa
SELECT 
    DATE(data_hora) as data,
    status_justificativa,
    COUNT(*) as registros
FROM registros_ponto 
WHERE funcionario_id = 35 
GROUP BY DATE(data_hora), status_justificativa 
ORDER BY data DESC;
