#!/usr/bin/env python3
"""
Preparar migração completa do sistema para novo container
"""

import os
import sys
import subprocess
import datetime
from pathlib import Path

def preparar_migracao():
    """Preparar todos os arquivos para migração"""
    
    print("🚀 PREPARAÇÃO PARA MIGRAÇÃO DO SISTEMA")
    print("=" * 60)
    
    # Data/hora para backup
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_migracao_{timestamp}"
    
    print(f"📁 Criando diretório de backup: {backup_dir}")
    os.makedirs(backup_dir, exist_ok=True)
    
    # 1. Backup do banco de dados
    print(f"\n📊 PASSO 1: Backup do banco de dados...")
    
    db_backup_file = f"{backup_dir}/controle_ponto_backup_{timestamp}.sql"
    
    # Comando para backup do MySQL
    backup_cmd = [
        "ssh", "rlponto-server",
        f"mysqldump -u root -p@Ric6109 controle_ponto > /tmp/backup_{timestamp}.sql && cat /tmp/backup_{timestamp}.sql"
    ]
    
    try:
        print(f"   📤 Executando backup do banco...")
        result = subprocess.run(backup_cmd, capture_output=True, text=True, check=True)
        
        with open(db_backup_file, 'w', encoding='utf-8') as f:
            f.write(result.stdout)
        
        print(f"   ✅ Backup do banco salvo: {db_backup_file}")
        
        # Verificar tamanho do backup
        size = os.path.getsize(db_backup_file)
        print(f"   📊 Tamanho do backup: {size / 1024 / 1024:.2f} MB")
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Erro no backup do banco: {e}")
        return False
    
    # 2. Backup dos arquivos do sistema
    print(f"\n📁 PASSO 2: Backup dos arquivos do sistema...")
    
    system_backup_file = f"{backup_dir}/sistema_completo_{timestamp}.tar.gz"
    
    # Comando para backup dos arquivos
    files_cmd = [
        "ssh", "rlponto-server",
        "cd /var/www && tar -czf - controle-ponto"
    ]
    
    try:
        print(f"   📤 Executando backup dos arquivos...")
        result = subprocess.run(files_cmd, capture_output=True, check=True)
        
        with open(system_backup_file, 'wb') as f:
            f.write(result.stdout)
        
        print(f"   ✅ Backup dos arquivos salvo: {system_backup_file}")
        
        # Verificar tamanho do backup
        size = os.path.getsize(system_backup_file)
        print(f"   📊 Tamanho do backup: {size / 1024 / 1024:.2f} MB")
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Erro no backup dos arquivos: {e}")
        return False
    
    # 3. Criar script de instalação
    print(f"\n🔧 PASSO 3: Criando script de instalação...")
    
    install_script = f"{backup_dir}/instalar_sistema.sh"
    
    script_content = f"""#!/bin/bash
# Script de instalação automática do sistema RLPONTO-WEB
# Gerado em: {datetime.datetime.now()}

set -e  # Parar em caso de erro

echo "🚀 INSTALAÇÃO DO SISTEMA RLPONTO-WEB"
echo "===================================="

# Verificar se é root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Este script deve ser executado como root"
    exit 1
fi

# Atualizar sistema
echo "📦 Atualizando sistema..."
apt update && apt upgrade -y

# Instalar dependências
echo "📦 Instalando dependências..."
apt install -y python3 python3-pip python3-venv mysql-server nginx supervisor curl wget

# Configurar MySQL
echo "🗄️ Configurando MySQL..."
mysql -e "CREATE DATABASE IF NOT EXISTS controle_ponto CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -e "CREATE USER IF NOT EXISTS 'controle_ponto'@'localhost' IDENTIFIED BY '@Ric6109';"
mysql -e "GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_ponto'@'localhost';"
mysql -e "FLUSH PRIVILEGES;"

# Criar diretório do sistema
echo "📁 Criando estrutura de diretórios..."
mkdir -p /var/www/controle-ponto
cd /var/www

# Extrair arquivos do sistema
echo "📁 Extraindo arquivos do sistema..."
tar -xzf sistema_completo_{timestamp}.tar.gz

# Configurar permissões
echo "🔧 Configurando permissões..."
chown -R www-data:www-data /var/www/controle-ponto
chmod -R 755 /var/www/controle-ponto

# Instalar dependências Python
echo "🐍 Instalando dependências Python..."
cd /var/www/controle-ponto
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Restaurar banco de dados
echo "🗄️ Restaurando banco de dados..."
mysql controle_ponto < controle_ponto_backup_{timestamp}.sql

# Configurar Nginx
echo "🌐 Configurando Nginx..."
cat > /etc/nginx/sites-available/controle-ponto << 'EOF'
server {{
    listen 80;
    server_name _;
    
    location / {{
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \\$host;
        proxy_set_header X-Real-IP \\$remote_addr;
        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\$scheme;
    }}
    
    location /static {{
        alias /var/www/controle-ponto/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }}
}}
EOF

# Ativar site
ln -sf /etc/nginx/sites-available/controle-ponto /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default
nginx -t && systemctl restart nginx

# Configurar Supervisor
echo "⚙️ Configurando Supervisor..."
cat > /etc/supervisor/conf.d/controle-ponto.conf << 'EOF'
[program:controle-ponto]
command=/var/www/controle-ponto/venv/bin/python app.py
directory=/var/www/controle-ponto
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/controle-ponto.log
environment=FLASK_ENV=production
EOF

# Iniciar serviços
echo "🚀 Iniciando serviços..."
systemctl enable supervisor
systemctl restart supervisor
supervisorctl reread
supervisorctl update
supervisorctl start controle-ponto

# Verificar status
echo "✅ Verificando instalação..."
sleep 5
curl -s -o /dev/null -w "Status HTTP: %{{http_code}}\\n" http://localhost/

echo ""
echo "🎉 INSTALAÇÃO CONCLUÍDA!"
echo "========================"
echo "✅ Sistema instalado em: /var/www/controle-ponto"
echo "✅ Banco de dados: controle_ponto"
echo "✅ Nginx configurado na porta 80"
echo "✅ Supervisor gerenciando o processo"
echo ""
echo "🌐 Acesse: http://SEU_IP/"
echo "👤 Login: admin"
echo "🔑 Senha: @Ric6109"
"""

    with open(install_script, 'w') as f:
        f.write(script_content)
    
    # Tornar executável
    os.chmod(install_script, 0o755)
    
    print(f"   ✅ Script de instalação criado: {install_script}")
    
    # 4. Criar arquivo de instruções
    print(f"\n📋 PASSO 4: Criando instruções...")
    
    instructions_file = f"{backup_dir}/INSTRUCOES.md"
    
    instructions = f"""# INSTRUÇÕES DE INSTALAÇÃO - RLPONTO-WEB

## 📦 Arquivos do Backup

- `controle_ponto_backup_{timestamp}.sql` - Backup completo do banco de dados
- `sistema_completo_{timestamp}.tar.gz` - Arquivos completos do sistema
- `instalar_sistema.sh` - Script de instalação automatizada
- `INSTRUCOES.md` - Este arquivo

## 🚀 Instalação no Novo Container

### 1. Copiar arquivos para o servidor
```bash
scp -r {backup_dir}/* root@NOVO_IP:/root/
```

### 2. Executar instalação
```bash
ssh root@NOVO_IP
cd /root
chmod +x instalar_sistema.sh
./instalar_sistema.sh
```

### 3. Verificar instalação
```bash
curl http://NOVO_IP/
```

## ✅ Pós-instalação

1. **Acesse:** http://NOVO_IP/
2. **Login:** admin
3. **Senha:** @Ric6109
4. **Teste:** Todas as funcionalidades

## 🔧 Troubleshooting

### Verificar logs
```bash
tail -f /var/log/controle-ponto.log
supervisorctl status
systemctl status nginx
```

### Reiniciar serviços
```bash
supervisorctl restart controle-ponto
systemctl restart nginx
```

## 📊 Informações do Sistema

- **Data do backup:** {datetime.datetime.now()}
- **Versão:** Sistema completo com herança dinâmica
- **Banco:** MySQL/MariaDB
- **Web Server:** Nginx + Supervisor
- **Python:** 3.x com Flask

## 🎯 Funcionalidades Incluídas

✅ Sistema de herança dinâmica de jornadas
✅ Trigger automático para atualização de funcionários
✅ Interface completa de gestão
✅ Relatórios e controle de ponto
✅ Gestão de empresas e funcionários
✅ Sistema de alocações
"""

    with open(instructions_file, 'w') as f:
        f.write(instructions)
    
    print(f"   ✅ Instruções criadas: {instructions_file}")
    
    # 5. Resumo final
    print(f"\n🎉 PREPARAÇÃO CONCLUÍDA!")
    print(f"=" * 60)
    print(f"📁 Diretório: {backup_dir}")
    print(f"📊 Backup DB: controle_ponto_backup_{timestamp}.sql")
    print(f"📁 Backup Sistema: sistema_completo_{timestamp}.tar.gz")
    print(f"🔧 Script: instalar_sistema.sh")
    print(f"📋 Instruções: INSTRUCOES.md")
    print(f"")
    print(f"🚀 PRÓXIMOS PASSOS:")
    print(f"1. Criar novo container")
    print(f"2. Copiar pasta '{backup_dir}' para o servidor")
    print(f"3. Executar './instalar_sistema.sh'")
    print(f"4. Testar sistema")
    
    return True

if __name__ == "__main__":
    preparar_migracao()
