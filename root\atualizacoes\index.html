<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Controle de Ponto</title>
    <link rel="stylesheet" href="/static/style-index.css">
</head>
<body>
    <div class="container">
        <h2>Controle de Ponto</h2>
        <div class="nav">
            <a href="/cadastrar">Cadastro de Funcionários</a> |
            {% if is_admin %}
            <a href="/configurar_usuarios">Configurar Usuários</a> |
            {% endif %}
            <a href="/logout">Sair</a>
        </div>
        <h3>Funcionários Cadastrados</h3>
        <table>
            <tr>
                <th>ID</th>
                <th>Nome Completo</th>
                <th>CPF</th>
                <th>Matrícula</th>
                <th>Setor/Obra</th>
                <th>Status</th>
            </tr>
            {% for func in funcionarios %}
            <tr>
                <td>{{ func.id }}</td>
                <td>{{ func.nome_completo }}</td>
                <td>{{ func.cpf }}</td>
                <td>{{ func.matricula_empresa }}</td>
                <td>{{ func.setor_obra }}</td>
                <td>{{ func.status_cadastro }}</td>
            </tr>
            {% endfor %}
        </table>
    </div>
</body>
</html>

