#!/usr/bin/env python3
"""
Preparar migração completa do sistema para novo container - Versão 2
"""

import os
import sys
import subprocess
import datetime
from pathlib import Path

def preparar_migracao():
    """Preparar todos os arquivos para migração"""
    
    print("🚀 PREPARAÇÃO PARA MIGRAÇÃO DO SISTEMA")
    print("=" * 60)
    
    # Data/hora para backup
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_migracao_{timestamp}"
    
    print(f"📁 Criando diretório de backup: {backup_dir}")
    os.makedirs(backup_dir, exist_ok=True)
    
    # 1. Criar backup no servidor e baixar
    print(f"\n📊 PASSO 1: Criando backup no servidor...")
    
    try:
        # Criar backup no servidor
        print(f"   📤 Criando backup do banco no servidor...")
        backup_cmd = [
            "ssh", "rlponto-server",
            f"mysqldump -u root -p@Ric6109 --single-transaction --routines --triggers controle_ponto > /tmp/backup_{timestamp}.sql"
        ]
        
        result = subprocess.run(backup_cmd, capture_output=True, text=True, check=True)
        print(f"   ✅ Backup criado no servidor")
        
        # Baixar backup
        print(f"   📥 Baixando backup...")
        download_cmd = [
            "scp", f"rlponto-server:/tmp/backup_{timestamp}.sql", f"{backup_dir}/controle_ponto_backup_{timestamp}.sql"
        ]
        
        result = subprocess.run(download_cmd, check=True)
        print(f"   ✅ Backup baixado")
        
        # Verificar tamanho
        backup_file = f"{backup_dir}/controle_ponto_backup_{timestamp}.sql"
        if os.path.exists(backup_file):
            size = os.path.getsize(backup_file)
            print(f"   📊 Tamanho do backup: {size / 1024 / 1024:.2f} MB")
        
        # Limpar arquivo temporário no servidor
        cleanup_cmd = ["ssh", "rlponto-server", f"rm -f /tmp/backup_{timestamp}.sql"]
        subprocess.run(cleanup_cmd)
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Erro no backup do banco: {e}")
        return False
    
    # 2. Backup dos arquivos do sistema
    print(f"\n📁 PASSO 2: Backup dos arquivos do sistema...")
    
    try:
        # Criar tar no servidor
        print(f"   📤 Criando arquivo no servidor...")
        tar_cmd = [
            "ssh", "rlponto-server",
            f"cd /var/www && tar -czf /tmp/sistema_{timestamp}.tar.gz controle-ponto"
        ]
        
        result = subprocess.run(tar_cmd, check=True)
        print(f"   ✅ Arquivo criado no servidor")
        
        # Baixar arquivo
        print(f"   📥 Baixando arquivos...")
        download_cmd = [
            "scp", f"rlponto-server:/tmp/sistema_{timestamp}.tar.gz", f"{backup_dir}/sistema_completo_{timestamp}.tar.gz"
        ]
        
        result = subprocess.run(download_cmd, check=True)
        print(f"   ✅ Arquivos baixados")
        
        # Verificar tamanho
        system_file = f"{backup_dir}/sistema_completo_{timestamp}.tar.gz"
        if os.path.exists(system_file):
            size = os.path.getsize(system_file)
            print(f"   📊 Tamanho do backup: {size / 1024 / 1024:.2f} MB")
        
        # Limpar arquivo temporário no servidor
        cleanup_cmd = ["ssh", "rlponto-server", f"rm -f /tmp/sistema_{timestamp}.tar.gz"]
        subprocess.run(cleanup_cmd)
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Erro no backup dos arquivos: {e}")
        return False
    
    # 3. Criar requirements.txt
    print(f"\n📦 PASSO 3: Criando requirements.txt...")
    
    requirements_content = """Flask==2.3.3
Flask-Login==0.6.3
PyMySQL==1.1.0
cryptography==41.0.7
Werkzeug==2.3.7
python-dotenv==1.0.0
openpyxl==3.1.2
xlsxwriter==3.1.9
reportlab==4.0.4
Pillow==10.0.1
gunicorn==21.2.0
"""
    
    with open(f"{backup_dir}/requirements.txt", 'w') as f:
        f.write(requirements_content)
    
    print(f"   ✅ requirements.txt criado")
    
    # 4. Criar script de instalação
    print(f"\n🔧 PASSO 4: Criando script de instalação...")
    
    install_script = f"{backup_dir}/instalar_sistema.sh"
    
    script_content = f"""#!/bin/bash
# Script de instalação automática do sistema RLPONTO-WEB
# Gerado em: {datetime.datetime.now()}

set -e  # Parar em caso de erro

echo "🚀 INSTALAÇÃO DO SISTEMA RLPONTO-WEB"
echo "===================================="

# Verificar se é root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Este script deve ser executado como root"
    exit 1
fi

# Atualizar sistema
echo "📦 Atualizando sistema..."
apt update && apt upgrade -y

# Instalar dependências
echo "📦 Instalando dependências..."
apt install -y python3 python3-pip python3-venv mysql-server nginx supervisor curl wget unzip

# Configurar MySQL
echo "🗄️ Configurando MySQL..."
systemctl start mysql
systemctl enable mysql

# Configurar senha root do MySQL se necessário
mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '@Ric6109';" || true
mysql -u root -p@Ric6109 -e "CREATE DATABASE IF NOT EXISTS controle_ponto CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -u root -p@Ric6109 -e "CREATE USER IF NOT EXISTS 'controle_ponto'@'localhost' IDENTIFIED BY '@Ric6109';"
mysql -u root -p@Ric6109 -e "GRANT ALL PRIVILEGES ON controle_ponto.* TO 'controle_ponto'@'localhost';"
mysql -u root -p@Ric6109 -e "FLUSH PRIVILEGES;"

# Criar diretório do sistema
echo "📁 Criando estrutura de diretórios..."
mkdir -p /var/www
cd /var/www

# Extrair arquivos do sistema
echo "📁 Extraindo arquivos do sistema..."
tar -xzf /root/sistema_completo_{timestamp}.tar.gz

# Configurar permissões
echo "🔧 Configurando permissões..."
chown -R www-data:www-data /var/www/controle-ponto
chmod -R 755 /var/www/controle-ponto

# Instalar dependências Python
echo "🐍 Instalando dependências Python..."
cd /var/www/controle-ponto
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r /root/requirements.txt

# Restaurar banco de dados
echo "🗄️ Restaurando banco de dados..."
mysql -u root -p@Ric6109 controle_ponto < /root/controle_ponto_backup_{timestamp}.sql

# Configurar Nginx
echo "🌐 Configurando Nginx..."
cat > /etc/nginx/sites-available/controle-ponto << 'EOF'
server {{
    listen 80;
    server_name _;
    client_max_body_size 100M;
    
    location / {{
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \\$host;
        proxy_set_header X-Real-IP \\$remote_addr;
        proxy_set_header X-Forwarded-For \\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\$scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }}
    
    location /static {{
        alias /var/www/controle-ponto/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }}
}}
EOF

# Ativar site
ln -sf /etc/nginx/sites-available/controle-ponto /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default
nginx -t && systemctl restart nginx
systemctl enable nginx

# Configurar Supervisor
echo "⚙️ Configurando Supervisor..."
cat > /etc/supervisor/conf.d/controle-ponto.conf << 'EOF'
[program:controle-ponto]
command=/var/www/controle-ponto/venv/bin/python app.py
directory=/var/www/controle-ponto
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/controle-ponto.log
environment=FLASK_ENV=production,SECRET_KEY=sua_chave_secreta_aqui
EOF

# Iniciar serviços
echo "🚀 Iniciando serviços..."
systemctl enable supervisor
systemctl restart supervisor
supervisorctl reread
supervisorctl update
supervisorctl start controle-ponto

# Aguardar inicialização
echo "⏳ Aguardando inicialização..."
sleep 10

# Verificar status
echo "✅ Verificando instalação..."
curl -s -o /dev/null -w "Status HTTP: %{{http_code}}\\n" http://localhost/ || echo "Serviço ainda inicializando..."

echo ""
echo "🎉 INSTALAÇÃO CONCLUÍDA!"
echo "========================"
echo "✅ Sistema instalado em: /var/www/controle-ponto"
echo "✅ Banco de dados: controle_ponto"
echo "✅ Nginx configurado na porta 80"
echo "✅ Supervisor gerenciando o processo"
echo ""
echo "🌐 Acesse: http://$(hostname -I | awk '{{print $1}}')"
echo "👤 Login: admin"
echo "🔑 Senha: @Ric6109"
echo ""
echo "🔧 Comandos úteis:"
echo "   supervisorctl status"
echo "   tail -f /var/log/controle-ponto.log"
echo "   systemctl status nginx"
"""

    with open(install_script, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # Tornar executável
    os.chmod(install_script, 0o755)
    
    print(f"   ✅ Script de instalação criado: {install_script}")
    
    # 5. Criar arquivo de instruções
    print(f"\n📋 PASSO 5: Criando instruções...")
    
    instructions_file = f"{backup_dir}/INSTRUCOES.md"
    
    instructions = f"""# INSTRUÇÕES DE INSTALAÇÃO - RLPONTO-WEB

## 📦 Arquivos do Backup

- `controle_ponto_backup_{timestamp}.sql` - Backup completo do banco de dados
- `sistema_completo_{timestamp}.tar.gz` - Arquivos completos do sistema
- `requirements.txt` - Dependências Python
- `instalar_sistema.sh` - Script de instalação automatizada
- `INSTRUCOES.md` - Este arquivo

## 🚀 Instalação no Novo Container

### 1. Copiar arquivos para o servidor
```bash
scp -r {backup_dir}/* root@NOVO_IP:/root/
```

### 2. Conectar ao servidor e executar instalação
```bash
ssh root@NOVO_IP
cd /root
chmod +x instalar_sistema.sh
./instalar_sistema.sh
```

### 3. Verificar instalação
```bash
curl http://NOVO_IP/
supervisorctl status
```

## ✅ Pós-instalação

1. **Acesse:** http://NOVO_IP/
2. **Login:** admin
3. **Senha:** @Ric6109
4. **Teste:** Todas as funcionalidades

## 🔧 Troubleshooting

### Verificar logs
```bash
tail -f /var/log/controle-ponto.log
supervisorctl status
systemctl status nginx
systemctl status mysql
```

### Reiniciar serviços
```bash
supervisorctl restart controle-ponto
systemctl restart nginx
systemctl restart mysql
```

### Verificar banco de dados
```bash
mysql -u root -p@Ric6109 -e "SHOW DATABASES;"
mysql -u root -p@Ric6109 -e "USE controle_ponto; SHOW TABLES;"
```

## 📊 Informações do Sistema

- **Data do backup:** {datetime.datetime.now()}
- **Versão:** Sistema completo com herança dinâmica
- **Banco:** MySQL/MariaDB
- **Web Server:** Nginx + Supervisor
- **Python:** 3.x com Flask

## 🎯 Funcionalidades Incluídas

✅ Sistema de herança dinâmica de jornadas
✅ Trigger automático para atualização de funcionários  
✅ Interface completa de gestão
✅ Relatórios e controle de ponto
✅ Gestão de empresas e funcionários
✅ Sistema de alocações
✅ Tolerância automática

## 🔒 Segurança

- Banco com senha: @Ric6109
- Usuário admin: admin / @Ric6109
- Firewall: Configure conforme necessário
- SSL: Configure certificado se necessário
"""

    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"   ✅ Instruções criadas: {instructions_file}")
    
    # 6. Resumo final
    print(f"\n🎉 PREPARAÇÃO CONCLUÍDA!")
    print(f"=" * 60)
    print(f"📁 Diretório: {backup_dir}")
    print(f"📊 Backup DB: controle_ponto_backup_{timestamp}.sql")
    print(f"📁 Backup Sistema: sistema_completo_{timestamp}.tar.gz")
    print(f"📦 Dependências: requirements.txt")
    print(f"🔧 Script: instalar_sistema.sh")
    print(f"📋 Instruções: INSTRUCOES.md")
    print(f"")
    print(f"🚀 PRÓXIMOS PASSOS:")
    print(f"1. Criar novo container com SSH")
    print(f"2. scp -r {backup_dir}/* root@NOVO_IP:/root/")
    print(f"3. ssh root@NOVO_IP")
    print(f"4. cd /root && chmod +x instalar_sistema.sh && ./instalar_sistema.sh")
    print(f"5. Testar: http://NOVO_IP/")
    
    return True

if __name__ == "__main__":
    preparar_migracao()
