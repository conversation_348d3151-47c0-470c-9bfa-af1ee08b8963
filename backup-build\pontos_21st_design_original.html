{% extends "base.html" %}

{% block title %}{{ titulo }} - <PERSON>e de <PERSON>{% endblock %}

{% block extra_css %}
<!-- Importação da biblioteca Chart.js para gráficos interativos -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.css">
<style>
    /* ========================================
       SEÇÃO 1: ESTILIZAÇÃO DO CARD DE FILTROS
       ======================================== */
    
    /* Card principal que contém todos os filtros de busca */
    .filtros-card {
        /* Gradiente moderno com tons de roxo/azul para visual profissional */
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px; /* <PERSON><PERSON>s arredondadas para design moderno */
        padding: 25px; /* Espaçamento interno generoso */
        margin-bottom: 25px; /* Espaçamento inferior */
        /* Sombra sutil para efeito de elevação */
        box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        border: none; /* Remove bordas padrão */
    }
    
    /* Título do card de filtros com ícone */
    .filtros-card h5 {
        color: white;
        margin-bottom: 20px;
        font-weight: 600; /* Fonte semi-negrito */
        display: flex; /* Layout flexível para alinhar ícone e texto */
        align-items: center; /* Alinhamento vertical central */
        gap: 10px; /* Espaço entre ícone e texto */
    }
    
    /* Estilização do ícone no título */
    .filtros-card h5 i {
        background: rgba(255, 255, 255, 0.2); /* Fundo semi-transparente */
        padding: 8px;
        border-radius: 50%; /* Formato circular */
    }
    
    /* ========================================
       SEÇÃO 2: CAMPOS DE FORMULÁRIO
       ======================================== */
    
    /* Estilização personalizada para todos os campos do formulário */
    .form-control-white {
        background: rgba(255, 255, 255, 0.95); /* Fundo branco quase opaco */
        border: 2px solid rgba(255, 255, 255, 0.3); /* Borda transparente */
        color: #2c3e50; /* Cor do texto */
        border-radius: 12px; /* Bordas bem arredondadas */
        transition: all 0.3s ease; /* Transição suave para todas as propriedades */
        padding: 12px 16px; /* Padding interno confortável */
        font-size: 14px; /* Tamanho da fonte otimizado */
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Sombra sutil */
        width: 100%; /* Garante largura total do container */
        min-width: 200px; /* Largura mínima para evitar truncamento */
        overflow: visible; /* Permite que o texto seja visível */
        text-overflow: clip; /* Remove reticências */
        white-space: nowrap; /* Impede quebra de linha */
        appearance: none; /* Remove estilo padrão do navegador */
        -webkit-appearance: none; /* Remove estilo padrão do webkit */
        -moz-appearance: none; /* Remove estilo padrão do firefox */
    }
    
    /* Estilização específica para campos select */
    .form-control-white select,
    select.form-control-white {
        background: rgba(255, 255, 255, 0.95) url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") no-repeat right 12px center;
        background-size: 16px 16px;
        padding-right: 40px !important; /* Espaço para a seta customizada */
        cursor: pointer;
        font-weight: 500;
        min-height: 48px; /* Altura mínima adequada */
        display: flex;
        align-items: center;
    }
    
    /* Placeholder para campos select - primeira opção */
    .form-control-white option:first-child {
        color: #6c757d;
        font-style: italic;
    }
    
    /* Opções dos selects */
    .form-control-white option {
        background: white;
        color: #2c3e50;
        padding: 8px 12px;
        font-weight: 500;
    }
    
    /* Estado de foco dos campos - quando usuário clica no campo */
    .form-control-white:focus {
        background: white; /* Fundo totalmente branco */
        border-color: #28a745; /* Borda verde para indicar foco */
        box-shadow: 0 0 0 0.3rem rgba(40, 167, 69, 0.25); /* Glow verde */
        transform: translateY(-1px); /* Pequena elevação visual */
        outline: none; /* Remove outline padrão */
    }
    
    /* Estado de hover dos campos - quando mouse passa por cima */
    .form-control-white:hover {
        background: white;
        border-color: rgba(255, 255, 255, 0.6); /* Borda mais visível */
    }
    
    /* Labels dos campos de formulário */
    .form-label {
        font-weight: 600; /* Fonte semi-negrito */
        margin-bottom: 8px;
        color: white; /* Cor branca para contraste com fundo escuro */
        font-size: 13px; /* Tamanho pequeno mas legível */
        text-transform: uppercase; /* Texto em maiúsculas */
        letter-spacing: 0.5px; /* Espaçamento entre letras */
        display: block; /* Garante que ocupe linha completa */
    }
    
    /* ========================================
       SEÇÃO 3: CAMPOS DE DATA ESPECIAIS
       ======================================== */
    
    /* Container especial para campos de data com ícone */
    .date-input-group {
        position: relative; /* Permite posicionamento absoluto do ícone */
        width: 100%; /* Largura total */
    }
    
    /* Ajuste de padding para campos de data (espaço para o ícone) */
    .date-input-group .form-control-white {
        padding-right: 45px; /* Espaço à direita para o ícone */
    }
    
    /* Ícone de calendário posicionado sobre o campo de data */
    .date-input-group::after {
        content: '📅'; /* Emoji de calendário */
        position: absolute; /* Posicionamento absoluto */
        right: 12px; /* Distância da borda direita */
        top: 50%; /* Centralizado verticalmente */
        transform: translateY(-50%); /* Ajuste fino da centralização */
        pointer-events: none; /* Não interfere com cliques */
        font-size: 16px;
        z-index: 2; /* Fica acima do campo */
    }
    
    /* Oculta o ícone padrão do navegador para campos de data */
    .form-control-white::-webkit-calendar-picker-indicator {
        opacity: 0; /* Invisível mas ainda clicável */
        cursor: pointer; /* Mantém cursor de ponteiro */
    }
    
    /* ========================================
       SEÇÃO 4: BOTÕES DE AÇÃO
       ======================================== */
    
    /* Botão principal de busca com gradiente verde */
    .btn-buscar {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 14px 24px; /* Padding ligeiramente maior */
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease; /* Transição suave para efeitos */
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3); /* Sombra verde */
        text-transform: uppercase; /* Texto em maiúsculas */
        letter-spacing: 0.5px; /* Espaçamento entre letras */
        font-size: 14px; /* Fonte ligeiramente maior */
        display: flex; /* Layout flexível */
        align-items: center; /* Alinhamento vertical */
        justify-content: center; /* Centralização horizontal */
        gap: 8px; /* Espaço entre ícone e texto */
        min-height: 52px; /* Altura mínima para toque fácil */
        width: 100%; /* Largura total do container */
        margin-top: 8px; /* Margem superior para alinhamento com campos de data */
    }
    
    /* Efeito hover do botão buscar - animação 3D */
    .btn-buscar:hover {
        background: linear-gradient(135deg, #218838 0%, #1abc9c 100%);
        transform: translateY(-2px) scale(1.02); /* Elevação e pequeno aumento */
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4); /* Sombra mais intensa */
        color: white;
    }
    
    /* Estado ativo do botão (quando pressionado) */
    .btn-buscar:active {
        transform: translateY(0) scale(1); /* Volta ao estado normal */
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }
    
    /* Ícone dentro do botão buscar */
    .btn-buscar i {
        font-size: 16px;
    }
    
    /* Botão de exportar CSV com gradiente azul */
    .btn-exportar {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px; /* Bem arredondado */
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3); /* Sombra azul */
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 13px;
        display: inline-flex; /* Layout flexível inline */
        align-items: center;
        gap: 8px; /* Espaço entre ícone e texto */
    }
    
    /* Efeito hover do botão exportar */
    .btn-exportar:hover {
        transform: translateY(-2px) scale(1.02); /* Animação 3D */
        box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
        color: white;
        background: linear-gradient(135deg, #0056b3 0%, #003d82 100%); /* Gradiente mais escuro */
    }
    
    /* Ícone dentro do botão exportar */
    .btn-exportar i {
        font-size: 14px;
    }
    
    /* ========================================
       SEÇÃO 5: RESPONSIVIDADE MOBILE
       ======================================== */
    
    /* Adaptações para telas pequenas (tablets e celulares) */
    @media (max-width: 768px) {
        .filtros-card {
            padding: 20px 15px; /* Reduz padding em mobile */
        }
        
        .filtros-card .row {
            gap: 15px; /* Maior espaçamento entre elementos */
        }
        
        /* Margin inferior para colunas em mobile */
        .filtros-card .col-md-3,
        .filtros-card .col-md-4,
        .filtros-card .col-12 {
            margin-bottom: 15px;
        }
        
        /* Remove margin do último elemento */
        .filtros-card .col-md-3:last-child,
        .filtros-card .col-md-4:last-child,
        .filtros-card .col-12:last-child {
            margin-bottom: 0;
        }
        
        /* Formulário empilhado em mobile */
        .filtros-card form.row {
            flex-direction: column;
        }
        
        .filtros-card form .col-md-3,
        .filtros-card form .col-md-4,
        .filtros-card form .col-12 {
            max-width: 100%;
            flex: 0 0 100%;
        }

        /* Botão de busca centralizado em mobile */
        .btn-buscar {
            font-size: 16px;
            padding: 16px 24px;
            min-height: 56px;
        }
    }
    
    /* ========================================
       SEÇÃO 5.1: MELHORIAS DE LAYOUT
       ======================================== */
    
    /* Container dos filtros com melhor alinhamento */
    .filtros-container {
        width: 100%;
        max-width: 100%;
    }

    /* Alinhamento perfeito dos campos de formulário */
    .form-group-aligned {
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: flex-start;
    }

    /* Assegura que todos os campos tenham a mesma altura */
    .form-control-white,
    select.form-control-white {
        min-height: 48px;
        line-height: 1.5;
        box-sizing: border-box;
    }

    /* Container do botão com alinhamento melhorado */
    .button-container {
        display: flex;
        align-items: flex-end;
        justify-content: center;
        width: 100%;
        padding-top: 8px;
    }

    /* Garante que o botão mantenha proporção adequada */
    .button-container .btn-buscar {
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* ========================================
       SEÇÃO 5.2: CORREÇÕES ESPECÍFICAS DROPDOWNS
       ======================================== */

    /* Correção para dropdowns que não exibem texto corretamente - VERSÃO CONSOLIDADA */
    select.form-control-white {
        background: rgba(255, 255, 255, 0.95) url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") no-repeat right 12px center !important;
        background-size: 16px 16px !important;
        padding-right: 40px !important;
        cursor: pointer;
        font-weight: 500;
        min-height: 48px;
        line-height: 1.5;
        box-sizing: border-box;
    }

    /* Força a visibilidade do texto nos selects */
    select.form-control-white option {
        color: #2c3e50 !important;
        background: white !important;
        font-size: 14px;
        padding: 8px 12px;
    }

    /* Primeira opção (placeholder) com estilo diferenciado */
    select.form-control-white option[value=""] {
        color: #6c757d !important;
        font-style: italic;
        font-weight: 400;
    }

    /* Hover state para opções do select */
    select.form-control-white option:hover {
        background: #f8f9fa !important;
        color: #2c3e50 !important;
    }
    
    /* ========================================
       SEÇÃO 6: BOTÕES DE CONVENIÊNCIA
       ======================================== */
    
    /* Pequenos botões "Hoje" que aparecem abaixo dos campos de data */
    .data-hoje {
        background: rgba(255, 255, 255, 0.15); /* Fundo semi-transparente */
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 11px; /* Fonte pequena */
        cursor: pointer; /* Indica que é clicável */
        transition: all 0.2s ease; /* Transição rápida */
        margin-top: 4px;
        display: inline-block;
    }
    
    /* Hover dos botões "Hoje" */
    .data-hoje:hover {
        background: rgba(255, 255, 255, 0.25); /* Mais opaco */
        border-color: rgba(255, 255, 255, 0.5);
    }
    
    /* Botões de período rápido (Hoje, Esta Semana, Este Mês) */
    .btn-outline-light {
        border: 1px solid rgba(255, 255, 255, 0.4);
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.1); /* Fundo translúcido */
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.3s ease;
        backdrop-filter: blur(5px); /* Efeito de desfoque no fundo */
    }
    
    /* Hover dos botões de período */
    .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.6);
        color: white;
        transform: translateY(-1px); /* Pequena elevação */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    /* Estado ativo dos botões de período */
    .btn-outline-light:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    /* Ícones nos botões de período */
    .btn-outline-light i {
        margin-right: 4px;
    }
    
    /* ========================================
       SEÇÃO 7: CARDS DE RESULTADOS
       ======================================== */
    
    /* Card que contém a tabela de resultados */
    .resultados-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Sombra sutil */
        margin-bottom: 25px;
    }
    
    /* Container responsivo da tabela */
    .table-responsive {
        border-radius: 10px;
        overflow: hidden; /* Esconde overflow para manter bordas arredondadas */
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    /* Estilização customizada da tabela */
    .table-custom {
        margin-bottom: 0;
        border-collapse: separate; /* Permite bordas arredondadas */
        border-spacing: 0;
    }
    
    /* Cabeçalho da tabela com gradiente escuro */
    .table-custom thead {
        background: linear-gradient(135deg, #495057 0%, #343a40 100%);
        color: white;
    }
    
    /* Células do cabeçalho */
    .table-custom thead th {
        border: none;
        padding: 15px 12px;
        font-weight: 600;
        text-transform: uppercase; /* Texto em maiúsculas */
        font-size: 0.85em; /* Fonte menor */
        letter-spacing: 0.5px; /* Espaçamento entre letras */
    }
    
    /* Linhas do corpo da tabela */
    .table-custom tbody tr {
        border-bottom: 1px solid #e9ecef; /* Separador sutil */
        transition: all 0.3s ease; /* Transição para hover */
    }
    
    /* Efeito hover nas linhas da tabela */
    .table-custom tbody tr:hover {
        background-color: #f8f9fa; /* Fundo cinza claro */
        transform: scale(1.001); /* Pequeno aumento */
    }
    
    /* Células do corpo da tabela */
    .table-custom tbody td {
        padding: 12px;
        vertical-align: middle; /* Alinhamento vertical central */
        border: none;
    }
    
    /* ========================================
       SEÇÃO 8: BADGES E INDICADORES
       ======================================== */
    
    /* Badge base para tipos de registro */
    .badge-tipo {
        padding: 6px 12px;
        border-radius: 15px; /* Bem arredondado */
        font-size: 0.8em;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    /* Badge para entrada da manhã - verde */
    .badge-entrada-manha {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }
    
    /* Badge para saída do almoço - amarelo/laranja */
    .badge-saida-almoco {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: #212529; /* Texto escuro para contraste */
    }
    
    /* Badge para entrada da tarde - azul */
    .badge-entrada-tarde {
        background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
        color: white;
    }
    
    /* Badge para saída - vermelho */
    .badge-saida {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }
    
    /* Badge para método de registro */
    .badge-metodo {
        padding: 4px 8px;
        border-radius: 10px;
        font-size: 0.75em;
        font-weight: 500;
    }
    
    /* Badge para registro biométrico - verde claro */
    .badge-biometrico {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    /* Badge para registro manual - amarelo claro */
    .badge-manual {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    /* ========================================
       SEÇÃO 9: CARDS DE ESTATÍSTICAS
       ======================================== */
    
    /* Container das estatísticas */
    .stats-row {
        margin-bottom: 25px;
    }
    
    /* Card individual de estatística */
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease; /* Transição para hover */
        border-left: 4px solid; /* Borda colorida à esquerda */
    }
    
    /* Efeito hover nos cards de estatística */
    .stat-card:hover {
        transform: translateY(-5px); /* Elevação */
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15); /* Sombra mais intensa */
    }
    
    /* Cores das bordas para diferentes tipos de cards */
    .stat-card.total {
        border-left-color: #007bff; /* Azul */
    }
    
    .stat-card.biometrico {
        border-left-color: #28a745; /* Verde */
    }
    
    .stat-card.manual {
        border-left-color: #ffc107; /* Amarelo */
    }
    
    .stat-card.funcionarios {
        border-left-color: #17a2b8; /* Azul claro */
    }
    
    /* Número da estatística */
    .stat-number {
        font-size: 2.5em; /* Fonte grande */
        font-weight: 300; /* Fonte leve */
        color: #2c3e50;
        margin: 0;
    }
    
    /* Label da estatística */
    .stat-label {
        color: #6c757d;
        font-size: 0.9em;
        margin-top: 5px;
    }
    
    /* ========================================
       SEÇÃO 10: SISTEMA DE ABAS
       ======================================== */
    
    /* Container das abas */
    .nav-tabs {
        border-bottom: 2px solid #dee2e6;
        margin-bottom: 20px;
    }
    
    /* Links das abas */
    .nav-tabs .nav-link {
        border: none;
        color: #495057;
        font-weight: 600;
        padding: 12px 20px;
        margin-right: 10px;
        transition: all 0.3s ease;
    }
    
    /* Hover nos links das abas */
    .nav-tabs .nav-link:hover {
        border: none;
        color: #007bff;
    }
    
    /* Aba ativa */
    .nav-tabs .nav-link.active {
        border: none;
        color: #007bff;
        position: relative; /* Para o indicador */
    }
    
    /* Indicador visual da aba ativa */
    .nav-tabs .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 2px;
        background: #007bff; /* Linha azul */
    }
    
    /* Conteúdo das abas */
    .tab-content {
        padding: 20px 0;
    }
    
    /* ========================================
       SEÇÃO 11: CONTAINERS DE GRÁFICOS
       ======================================== */
    
    /* Container para gráficos Chart.js */
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* ========================================
       SEÇÃO 12: CARDS DE RESUMO
       ======================================== */
    
    /* Card de resumo para gráficos */
    .resumo-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* Título do card de resumo */
    .resumo-card h6 {
        color: #495057;
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    /* Linha de informação no resumo */
    .resumo-info {
        display: flex;
        justify-content: space-between; /* Espaço entre label e valor */
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #e9ecef; /* Separador */
    }
    
    /* Remove borda da última linha */
    .resumo-info:last-child {
        border-bottom: none;
    }
    
    /* Label da informação */
    .resumo-label {
        color: #6c757d;
        font-size: 0.9em;
    }
    
    /* Valor da informação */
    .resumo-valor {
        font-weight: 600;
        color: #212529;
    }
    
    /* ========================================
       SEÇÃO 13: BARRAS DE PROGRESSO
       ======================================== */
    
    /* Barra de progresso base */
    .progress {
        height: 8px;
        border-radius: 4px;
        margin-top: 5px;
    }
    
    /* Barra de progresso para pontualidade - verde */
    .progress-bar-pontualidade {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }
    
    /* Barra de progresso para atrasos - vermelho */
    .progress-bar-atrasos {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }
    
    /* ========================================
       SEÇÃO 14: ELEMENTOS DE LOADING
       ======================================== */
    
    /* Spinner de carregamento */
    .loading-spinner {
        display: none; /* Inicialmente oculto */
        text-align: center;
        padding: 40px;
    }
    
    /* Configuração do spinner Bootstrap */
    .loading-spinner .spinner-border {
        width: 3rem;
        height: 3rem;
        color: #007bff;
    }
    
    /* ========================================
       SEÇÃO 15: CLASSES UTILITÁRIAS
       ======================================== */
    
    /* Classes de espaçamento para flexbox */
    .gap-2 {
        gap: 0.5rem !important; /* Espaço pequeno entre elementos */
    }
    
    .me-3 {
        margin-right: 1rem !important; /* Margem à direita */
    }
    
    /* Texto com transparência */
    .text-white-50 {
        color: rgba(255, 255, 255, 0.7) !important;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<!-- ========================================
     CONTAINER PRINCIPAL DA PÁGINA
     ======================================== -->
<div class="container-fluid py-4">
    
    <!-- ========================================
         SEÇÃO DE FILTROS DE BUSCA
         Card com gradiente roxo contendo todos os filtros
         ======================================== -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filtros-card">
                <!-- Título com ícone -->
                <h5 class="mb-4"><i class="fas fa-filter"></i> Filtros de Busca</h5>
                
                <!-- Primeira linha do formulário: campos principais -->
                <form id="filtrosForm" method="POST" class="filtros-container">
                    <div class="row g-3 mb-3">
                        <!-- Campo: Seleção de funcionário -->
                        <div class="col-md-3 col-12">
                            <div class="form-group-aligned">
                                <label for="funcionario_id" class="form-label">Funcionário</label>
                                <select class="form-control-white" id="funcionario_id" name="funcionario_id">
                                    <option value="" style="color: #6c757d;">Todos os funcionários</option>
                                    {% for f in funcionarios %}
                                    <option value="{{ f.id }}">{{ f.nome_completo }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <!-- Campo: Filtro por setor -->
                        <div class="col-md-3 col-12">
                            <div class="form-group-aligned">
                                <label for="setor" class="form-label">Setor</label>
                                <select class="form-control-white" id="setor" name="setor">
                                    <option value="" style="color: #6c757d;">Todos os setores</option>
                                    <!-- Lista única de setores (Jinja2 filter) -->
                                    {% for f in funcionarios|unique(attribute='setor') %}
                                    <option value="{{ f.setor }}">{{ f.setor }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <!-- Campo: Tipo de registro (entrada/saída) -->
                        <div class="col-md-3 col-12">
                            <div class="form-group-aligned">
                                <label for="tipo_registro" class="form-label">Tipo</label>
                                <select class="form-control-white" id="tipo_registro" name="tipo_registro">
                                    <option value="" style="color: #6c757d;">Todos os tipos</option>
                                    <!-- Opcões vindas do backend -->
                                    {% for tipo in tipos_registro %}
                                    <option value="{{ tipo.value }}">{{ tipo.text }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <!-- Campo: Método de registro (biométrico/manual) -->
                        <div class="col-md-3 col-12">
                            <div class="form-group-aligned">
                                <label for="metodo_registro" class="form-label">Método</label>
                                <select class="form-control-white" id="metodo_registro" name="metodo_registro">
                                    <option value="" style="color: #6c757d;">Todos os métodos</option>
                                    <!-- Opcões vindas do backend -->
                                    {% for metodo in metodos_registro %}
                                    <option value="{{ metodo.value }}">{{ metodo.text }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Segunda linha do formulário: datas e botão -->
                    <div class="row g-3 align-items-end">
                        <!-- Campo: Data inicial com ícone personalizado -->
                        <div class="col-md-4 col-12">
                            <div class="form-group-aligned">
                                <label for="data_inicio" class="form-label">
                                    <i class="fas fa-calendar-alt"></i> Data Inicial
                                </label>
                                <div class="date-input-group">
                                    <!-- Campo de data com validação HTML5 -->
                                    <input type="date" class="form-control-white" id="data_inicio" name="data_inicio" required>
                                </div>
                                <!-- Botão rápido para definir data atual -->
                                <small class="data-hoje" onclick="definirDataAtual('data_inicio')">
                                    <i class="fas fa-clock"></i> Hoje
                                </small>
                            </div>
                        </div>
                        
                        <!-- Campo: Data final com ícone personalizado -->
                        <div class="col-md-4 col-12">
                            <div class="form-group-aligned">
                                <label for="data_fim" class="form-label">
                                    <i class="fas fa-calendar-check"></i> Data Final
                                </label>
                                <div class="date-input-group">
                                    <!-- Campo de data com validação HTML5 -->
                                    <input type="date" class="form-control-white" id="data_fim" name="data_fim" required>
                                </div>
                                <!-- Botão rápido para definir data atual -->
                                <small class="data-hoje" onclick="definirDataAtual('data_fim')">
                                    <i class="fas fa-clock"></i> Hoje
                                </small>
                            </div>
                        </div>
                        
                        <!-- Botão de submissão do formulário -->
                        <div class="col-md-4 col-12">
                            <div class="button-container">
                                <button type="submit" class="btn-buscar w-100">
                                    <i class="fas fa-search"></i>
                                    <span>Buscar Registros</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                
                <!-- ========================================
                     BOTÕES DE CONVENIÊNCIA PARA PERÍODOS
                     Linha de botões para seleção rápida de períodos
                     ======================================== -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex flex-wrap gap-2 align-items-center">
                            <!-- Label explicativo -->
                            <small class="text-white-50 me-3">
                                <i class="fas fa-magic"></i> Períodos rápidos:
                            </small>
                            
                            <!-- Botão: Definir hoje em ambas as datas -->
                            <button type="button" class="btn btn-sm btn-outline-light" onclick="definirDataAtual('data_inicio'); definirDataAtual('data_fim');">
                                <i class="fas fa-calendar-day"></i> Hoje
                            </button>
                            
                            <!-- Botão: Definir período da semana atual -->
                            <button type="button" class="btn btn-sm btn-outline-light" onclick="definirPeriodoSemana()">
                                <i class="fas fa-calendar-week"></i> Esta Semana
                            </button>
                            
                            <!-- Botão: Definir período do mês atual -->
                            <button type="button" class="btn btn-sm btn-outline-light" onclick="definirPeriodoMes()">
                                <i class="fas fa-calendar-alt"></i> Este Mês
                            </button>
                            
                            <!-- Botão: Limpar todos os filtros -->
                            <button type="button" class="btn btn-sm btn-outline-light" onclick="limparFiltros()">
                                <i class="fas fa-eraser"></i> Limpar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========================================
         SISTEMA DE ABAS PARA DIFERENTES VISÕES
         ======================================== -->
    <ul class="nav nav-tabs" role="tablist">
        <!-- Aba: Lista de registros de ponto -->
        <li class="nav-item">
            <a class="nav-link active" id="registros-tab" data-bs-toggle="tab" href="#registros" role="tab">
                <i class="fas fa-list"></i> Registros
            </a>
        </li>
        
        <!-- Aba: Gráfico de horas trabalhadas -->
        <li class="nav-item">
            <a class="nav-link" id="horas-tab" data-bs-toggle="tab" href="#horas" role="tab">
                <i class="fas fa-clock"></i> Horas Trabalhadas
            </a>
        </li>
        
        <!-- Aba: Análise de pontualidade -->
        <li class="nav-item">
            <a class="nav-link" id="pontualidade-tab" data-bs-toggle="tab" href="#pontualidade" role="tab">
                <i class="fas fa-chart-line"></i> Pontualidade
            </a>
        </li>
    </ul>

    <!-- ========================================
         CONTEÚDO DAS ABAS
         ======================================== -->
    <div class="tab-content">
        
        <!-- ========================================
             ABA 1: REGISTROS DE PONTO
             ======================================== -->
        <div class="tab-pane fade show active" id="registros" role="tabpanel">
            
            <!-- Cards com estatísticas resumidas -->
            <div class="row stats-row" id="statsContainer" style="display: none;">
                <!-- Card: Total de registros -->
                <div class="col-md-3 col-6">
                    <div class="stat-card total">
                        <h6>Total de Registros</h6>
                        <!-- Valor preenchido via JavaScript -->
                        <h3 id="statTotal">0</h3>
                    </div>
                </div>
                
                <!-- Card: Registros biométricos -->
                <div class="col-md-3 col-6">
                    <div class="stat-card biometrico">
                        <h6>Registros Biométricos</h6>
                        <h3 id="statBiometrico">0</h3>
                    </div>
                </div>
                
                <!-- Card: Registros manuais -->
                <div class="col-md-3 col-6">
                    <div class="stat-card manual">
                        <h6>Registros Manuais</h6>
                        <h3 id="statManual">0</h3>
                    </div>
                </div>
                
                <!-- Card: Funcionários únicos -->
                <div class="col-md-3 col-6">
                    <div class="stat-card funcionarios">
                        <h6>Funcionários Ativos</h6>
                        <h3 id="statFuncionarios">0</h3>
                    </div>
                </div>
            </div>

            <!-- ========================================
                 TABELA DE RESULTADOS
                 ======================================== -->
            <div class="resultados-card">
                <!-- Cabeçalho com título e botão de exportação -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5>Registros de Ponto</h5>
                    <!-- Botão para exportar dados em CSV -->
                    <button class="btn btn-exportar" onclick="exportarCSV()">
                        <i class="fas fa-download"></i> Exportar CSV
                    </button>
                </div>
                
                <!-- Container responsivo para a tabela -->
                <div class="table-responsive">
                    <table class="table table-custom" id="tabelaRegistros">
                        <!-- Cabeçalho da tabela -->
                        <thead>
                            <tr>
                                <th>Funcionário</th>
                                <th>Data/Hora</th>
                                <th>Tipo</th>
                                <th>Método</th>
                                <th>Setor</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <!-- Corpo da tabela preenchido via JavaScript -->
                        <tbody id="tabelaResultados">
                            <!-- Preenchido via JavaScript -->
                        </tbody>
                    </table>
                </div>
                
                <!-- ========================================
                     FOOTER DA TABELA: CONTADORES E PAGINAÇÃO
                     ======================================== -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <!-- Contador de registros exibidos -->
                    <div>
                        <span class="text-muted">
                            Mostrando <span id="registrosExibidos">0</span> de <span id="totalRegistrosGeral">0</span> registros
                        </span>
                    </div>
                    
                    <!-- Container de paginação (oculto inicialmente) -->
                    <div id="paginacaoContainer" style="display: none;">
                        <nav>
                            <!-- Lista de páginas preenchida via JavaScript -->
                            <ul class="pagination" id="paginacao">
                                <!-- Preenchido via JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- ========================================
             ABA 2: HORAS TRABALHADAS
             Layout com gráfico e resumo lateral
             ======================================== -->
        <div class="tab-pane fade" id="horas" role="tabpanel">
            <div class="row">
                <!-- Gráfico de horas trabalhadas -->
                <div class="col-md-8">
                    <div class="chart-container">
                        <!-- Canvas para Chart.js -->
                        <canvas id="graficoHoras"></canvas>
                    </div>
                </div>
                
                <!-- Card com resumo de horas -->
                <div class="col-md-4">
                    <div class="resumo-card">
                        <h6>Resumo de Horas</h6>
                        
                        <!-- Total de horas do mês -->
                        <div class="resumo-info">
                            <span class="resumo-label">Total de Horas (Mês)</span>
                            <span class="resumo-valor" id="totalHorasMes">0h</span>
                        </div>
                        
                        <!-- Média de horas por dia -->
                        <div class="resumo-info">
                            <span class="resumo-label">Média Diária</span>
                            <span class="resumo-valor" id="mediaHorasDia">0h</span>
                        </div>
                        
                        <!-- Dias trabalhados no período -->
                        <div class="resumo-info">
                            <span class="resumo-label">Dias Trabalhados</span>
                            <span class="resumo-valor" id="diasTrabalhados">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ========================================
             ABA 3: ANÁLISE DE PONTUALIDADE
             Layout com gráfico e métricas de pontualidade
             ======================================== -->
        <div class="tab-pane fade" id="pontualidade" role="tabpanel">
            <div class="row">
                <!-- Gráfico de pontualidade -->
                <div class="col-md-8">
                    <div class="chart-container">
                        <!-- Canvas para Chart.js -->
                        <canvas id="graficoPontualidade"></canvas>
                    </div>
                </div>
                
                <!-- Card com análise de pontualidade -->
                <div class="col-md-4">
                    <div class="resumo-card">
                        <h6>Análise de Pontualidade</h6>
                        
                        <!-- Taxa de pontualidade com barra de progresso -->
                        <div class="resumo-info">
                            <span class="resumo-label">Taxa de Pontualidade</span>
                            <div class="w-100">
                                <div class="d-flex justify-content-between">
                                    <span class="resumo-valor" id="taxaPontualidade">0%</span>
                                </div>
                                <!-- Barra de progresso verde -->
                                <div class="progress">
                                    <div class="progress-bar progress-bar-pontualidade" id="progressPontualidade" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Total de atrasos com barra de progresso -->
                        <div class="resumo-info">
                            <span class="resumo-label">Atrasos (Mês)</span>
                            <div class="w-100">
                                <div class="d-flex justify-content-between">
                                    <span class="resumo-valor" id="totalAtrasos">0</span>
                                </div>
                                <!-- Barra de progresso vermelha -->
                                <div class="progress">
                                    <div class="progress-bar progress-bar-atrasos" id="progressAtrasos" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Importação da biblioteca Chart.js para gráficos interativos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
<script>
/* ========================================
   VARIÁVEIS GLOBAIS DO SISTEMA
   ======================================== */

// Array para armazenar os dados atuais da consulta (usado para exportação)
let dadosAtuais = [];

// Controle de paginação
let paginaAtual = 1;                    // Página atual sendo exibida
let registrosPorPagina = 20;            // Número de registros por página

// Instâncias dos gráficos Chart.js (inicialmente null)
let graficoHoras = null;                // Gráfico de horas trabalhadas
let graficoPontualidade = null;         // Gráfico de análise de pontualidade

/* ========================================
   FUNÇÕES DE INTERFACE E FEEDBACK
   ======================================== */

/**
 * Exibe indicador de carregamento na tabela
 * Substitui o conteúdo da tabela por um spinner animado
 */
function mostrarLoading() {
    const tbody = document.getElementById('tabelaResultados');
    tbody.innerHTML = `
        <tr>
            <td colspan="6" class="text-center">
                <div class="d-flex justify-content-center align-items-center py-4">
                    <!-- Spinner Bootstrap -->
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <span class="ms-2">Carregando registros...</span>
                </div>
            </td>
        </tr>
    `;
}

/**
 * Exibe mensagem de erro com alerta Bootstrap
 * @param {string} mensagem - Texto da mensagem de erro
 */
function mostrarErro(mensagem) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        <strong>Erro!</strong> ${mensagem}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Insere o alerta no topo da página
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Remove automaticamente após 5 segundos
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

/**
 * Exibe mensagem de sucesso com alerta Bootstrap
 * @param {string} mensagem - Texto da mensagem de sucesso
 */
function mostrarSucesso(mensagem) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        <strong>Sucesso!</strong> ${mensagem}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Insere o alerta no topo da página
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Remove automaticamente após 3 segundos
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

/* ========================================
   FUNÇÃO PRINCIPAL DE BUSCA DE REGISTROS
   ======================================== */

/**
 * Busca registros de ponto com filtros aplicados
 * Função assíncrona que faz requisição AJAX para o backend
 * @param {number} pagina - Número da página a ser carregada (padrão: 1)
 */
async function buscarRegistros(pagina = 1) {
    try {
        // Exibe indicador de carregamento
        mostrarLoading();
        
        // Obtém dados do formulário de filtros
        const form = document.getElementById('filtrosForm');
        const formData = new FormData(form);
        
        // Converte FormData para objeto JavaScript com validação de tipos
        const filtros = {
            funcionario_id: formData.get('funcionario_id') ? parseInt(formData.get('funcionario_id')) : null,
            setor: formData.get('setor') || null,
            tipo_registro: formData.get('tipo_registro') || null,
            metodo_registro: formData.get('metodo_registro') || null,
            data_inicio: formData.get('data_inicio') || null,
            data_fim: formData.get('data_fim') || null,
            pagina: pagina,                           // Página atual
            registros_por_pagina: registrosPorPagina  // Tamanho da página
        };

        // Validação client-side: verificar ordem das datas
        if (filtros.data_inicio && filtros.data_fim && new Date(filtros.data_fim) < new Date(filtros.data_inicio)) {
            throw new Error('A data final não pode ser menor que a data inicial');
        }

        // Requisição AJAX para o endpoint de busca
        const response = await fetch('/relatorios/api/buscar-registros', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filtros)
        });

        // Verificação de resposta HTTP
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Erro ao buscar registros');
        }

        const data = await response.json();
        
        // Verificação de sucesso na resposta JSON
        if (!data.success) {
            throw new Error(data.message || 'Erro ao processar registros');
        }

        // Armazena dados para funcionalidades como exportação
        dadosAtuais = data.registros;
        
        // Atualiza interface com os dados recebidos
        atualizarTabela(data.registros);                    // Popula a tabela
        atualizarPaginacao(data.total_paginas, data.pagina_atual);  // Atualiza paginação
        atualizarEstatisticas(data.estatisticas);           // Atualiza cards de estatísticas

        // Atualiza gráficos se dados estiverem disponíveis
        if (data.graficos) {
            atualizarGraficos(data.graficos);
        }
    } catch (error) {
        // Exibe erro para o usuário
        mostrarErro(error.message);
    } finally {
        // Remove classe de loading independentemente do resultado
        const tbody = document.getElementById('tabelaResultados');
        tbody.classList.remove('loading');
    }
}

/* ========================================
   CONFIGURAÇÃO E INICIALIZAÇÃO DOS GRÁFICOS
   ======================================== */

/**
 * Inicializa os gráficos Chart.js com configurações padrão
 * Cria instâncias globais dos gráficos para uso posterior
 */
function inicializarGraficos() {
    // GRÁFICO DE HORAS TRABALHADAS
    const ctxHoras = document.getElementById('graficoHoras').getContext('2d');
    graficoHoras = new Chart(ctxHoras, {
        type: 'line',                           // Gráfico de linha
        data: {
            labels: [],                         // Rótulos do eixo X (datas)
            datasets: [{
                label: 'Horas Trabalhadas',
                data: [],                       // Dados do eixo Y (horas)
                borderColor: '#28a745',         // Cor da linha (verde)
                backgroundColor: 'rgba(40, 167, 69, 0.1)',  // Cor de preenchimento
                borderWidth: 2,
                fill: true,                     // Preenche área sob a linha
                tension: 0.4                    // Suavização da linha
            }]
        },
        options: {
            responsive: true,                   // Responsivo ao container
            maintainAspectRatio: false,         // Não mantém proporção fixa
            plugins: {
                legend: {
                    display: false              // Oculta legenda (desnecessária com 1 dataset)
                }
            },
            scales: {
                y: {
                    beginAtZero: true,          // Eixo Y começa em zero
                    ticks: {
                        // Formatação personalizada: adiciona "h" após os valores
                        callback: function(value) {
                            return value + 'h';
                        }
                    }
                }
            }
        }
    });

    // GRÁFICO DE PONTUALIDADE
    const ctxPontualidade = document.getElementById('graficoPontualidade').getContext('2d');
    graficoPontualidade = new Chart(ctxPontualidade, {
        type: 'line',                           // Gráfico de linha
        data: {
            labels: [],                         // Rótulos do eixo X (meses)
            datasets: [{
                label: 'Taxa de Pontualidade',
                data: [],                       // Dados do eixo Y (percentuais)
                borderColor: '#17a2b8',         // Cor da linha (azul)
                backgroundColor: 'rgba(23, 162, 184, 0.1)',  // Cor de preenchimento
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,                   // Máximo 100% para pontualidade
                    ticks: {
                        // Formatação: adiciona "%" após os valores
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
}

/* ========================================
   INICIALIZAÇÃO DA PÁGINA E EVENTOS
   ======================================== */

/**
 * Event listener executado quando o DOM está carregado
 * Configura valores iniciais e eventos da página
 */
document.addEventListener('DOMContentLoaded', function() {
    // CONFIGURAÇÃO INICIAL DAS DATAS
    // Define data inicial como primeiro dia do mês atual
    const hoje = new Date();
    const primeiroDiaMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
    
    // Formata datas para o formato HTML5 (YYYY-MM-DD)
    const dataInicial = primeiroDiaMes.toISOString().split('T')[0];
    const dataFinal = hoje.toISOString().split('T')[0];
    
    // Define valores padrão nos campos de data
    document.querySelector('input[name="data_inicio"]').value = dataInicial;
    document.querySelector('input[name="data_fim"]').value = dataFinal;
    
    // INICIALIZAÇÃO DOS GRÁFICOS
    inicializarGraficos();
    
    // CONFIGURAÇÃO DE EVENTOS
    
    // Evento de submissão do formulário (previne submit padrão)
    document.getElementById('filtrosForm').addEventListener('submit', function(e) {
        e.preventDefault();                     // Previne reload da página
        buscarRegistros(1);                     // Busca registros da primeira página
    });
    
    // Evento de mudança de abas (carrega gráficos quando necessário)
    document.addEventListener('shown.bs.tab', function(e) {
        console.log('Aba ativada:', e.target.id);
        
        // Se mudou para aba de horas ou pontualidade, carrega dados dos gráficos
        if (e.target.id === 'horas-tab' || e.target.id === 'pontualidade-tab') {
            buscarDadosGraficos();
        }
    });
    
    // BUSCA INICIAL
    // Carrega registros iniciais com filtros padrão
    buscarRegistros(1);
});

/* ========================================
   FUNÇÕES DE ATUALIZAÇÃO DA INTERFACE
   ======================================== */

/**
 * Atualiza a tabela de resultados com novos dados
 * @param {Array} registros - Array de objetos com dados dos registros
 */
function atualizarTabela(registros) {
    const tbody = document.getElementById('tabelaResultados');
    
    // Verifica se há registros para exibir
    if (!registros || registros.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">Nenhum registro encontrado</td></tr>';
        return;
    }
    
    // Limpa conteúdo anterior
    tbody.innerHTML = '';
    
    // Itera sobre os registros e cria linhas da tabela
    registros.forEach(registro => {
        const linha = document.createElement('tr');
        
        // Formatação de data para padrão brasileiro
        const dataHora = new Date(registro.data_hora).toLocaleString('pt-BR');
        
        // Monta HTML da linha com badges coloridos
        linha.innerHTML = `
            <td>${registro.nome_completo}</td>
            <td>${dataHora}</td>
            <td><span class="badge badge-tipo badge-${registro.tipo_registro}">${registro.tipo_descricao}</span></td>
            <td><span class="badge badge-metodo badge-${registro.metodo_registro}">${registro.metodo_descricao}</span></td>
            <td>${registro.setor}</td>
            <td>${registro.status_pontualidade}</td>
        `;
        
        tbody.appendChild(linha);
    });
}

/**
 * Atualiza os cards de estatísticas com novos dados
 * @param {Object} stats - Objeto com estatísticas calculadas
 */
function atualizarEstatisticas(stats) {
    if (!stats) return;
    
    // Atualiza cada card com os valores recebidos (usa 0 como fallback)
    document.getElementById('statTotal').textContent = stats.total_registros || 0;
    document.getElementById('statBiometrico').textContent = stats.registros_biometricos || 0;
    document.getElementById('statManual').textContent = stats.registros_manuais || 0;
    document.getElementById('statFuncionarios').textContent = stats.funcionarios_distintos || 0;
    
    // Torna o container de estatísticas visível
    document.getElementById('statsContainer').style.display = 'block';
}

/**
 * Gera e exibe a paginação baseada nos resultados
 * @param {number} totalPaginas - Total de páginas disponíveis
 * @param {number} paginaAtual - Página atualmente ativa
 */
function atualizarPaginacao(totalPaginas, paginaAtual) {
    const container = document.getElementById('paginacaoContainer');
    const paginacao = document.getElementById('paginacao');
    
    // Oculta paginação se há apenas uma página
    if (totalPaginas <= 1) {
        container.style.display = 'none';
        return;
    }
    
    // Limpa paginação anterior
    paginacao.innerHTML = '';
    
    // BOTÃO ANTERIOR
    if (paginaAtual > 1) {
        paginacao.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="buscarRegistros(${paginaAtual - 1})">&laquo;</a>
            </li>
        `;
    }
    
    // PÁGINAS NUMERADAS
    // Calcula range de páginas a exibir (máximo 5: atual ±2)
    const inicio = Math.max(1, paginaAtual - 2);
    const fim = Math.min(totalPaginas, paginaAtual + 2);
    
    for (let i = inicio; i <= fim; i++) {
        const ativo = i === paginaAtual ? 'active' : '';
        paginacao.innerHTML += `
            <li class="page-item ${ativo}">
                <a class="page-link" href="#" onclick="buscarRegistros(${i})">${i}</a>
            </li>
        `;
    }
    
    // BOTÃO PRÓXIMO
    if (paginaAtual < totalPaginas) {
        paginacao.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="buscarRegistros(${paginaAtual + 1})">&raquo;</a>
            </li>
        `;
    }
    
    // Torna paginação visível
    container.style.display = 'block';
}

/* ========================================
   FUNÇÃO DE EXPORTAÇÃO CSV
   ======================================== */

/**
 * Exporta os dados atuais para arquivo CSV
 * Gera download automático do arquivo
 */
async function exportarCSV() {
    try {
        // Verifica se há dados para exportar
        if (dadosAtuais.length === 0) {
            alert('Não há dados para exportar. Faça uma busca primeiro.');
            return;
        }
        
        // Obter filtros atuais do formulário
        const filtros = {
            funcionario_id: document.getElementById('funcionario_id').value || null,
            setor: document.getElementById('setor').value || null,
            tipo_registro: document.getElementById('tipo_registro').value || null,
            metodo_registro: document.getElementById('metodo_registro').value || null,
            data_inicio: document.getElementById('data_inicio').value,
            data_fim: document.getElementById('data_fim').value
        };
        
        // Requisição para endpoint de exportação
        const response = await fetch('/relatorios/api/exportar-csv', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filtros)
        });
        
        if (response.ok) {
            // Converte resposta para blob (arquivo binário)
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            
            // Cria elemento <a> temporário para download
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `relatorio_pontos_${new Date().toISOString().split('T')[0]}.csv`;
            
            // Executa download e limpa recursos
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            
            mostrarSucesso('Relatório exportado com sucesso!');
        } else {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Erro ao exportar relatório');
        }
        
    } catch (error) {
        console.error('Erro ao exportar CSV:', error);
        mostrarErro(error.message || 'Erro interno do sistema');
    }
}

/* ========================================
   FUNÇÕES DE ATUALIZAÇÃO DOS GRÁFICOS
   ======================================== */

/**
 * Atualiza os gráficos Chart.js com novos dados
 * @param {Object} dados - Objeto contendo dados para gráficos e resumos
 */
function atualizarGraficos(dados) {
    if (!dados) return;

    // ATUALIZAÇÃO DO GRÁFICO DE HORAS TRABALHADAS
    if (graficoHoras && dados.horas) {
        graficoHoras.data.labels = dados.horas.labels;
        
        // Converte strings de horas (HH:MM:SS) para números decimais
        graficoHoras.data.datasets[0].data = dados.horas.valores.map(horas => {
            const [horas_str, minutos_str, segundos_str] = horas.split(':');
            return parseFloat(horas_str) + (parseFloat(minutos_str || 0) / 60);
        });
        graficoHoras.update();  // Redesenha o gráfico
    }

    // ATUALIZAÇÃO DO GRÁFICO DE PONTUALIDADE
    if (graficoPontualidade && dados.pontualidade) {
        // Formata labels de mês/ano (YYYY-MM para MM/YYYY)
        graficoPontualidade.data.labels = dados.pontualidade.labels.map(mes => {
            const [ano, mes] = mes.split('-');
            return `${mes}/${ano}`;
        });
        graficoPontualidade.data.datasets[0].data = dados.pontualidade.valores;
        graficoPontualidade.update();
    }

    // ATUALIZAÇÃO DOS RESUMOS LATERAIS
    if (dados.resumo) {
        // Resumo de horas
        document.getElementById('totalHorasMes').textContent = dados.resumo.total_horas_mes;
        document.getElementById('mediaHorasDia').textContent = dados.resumo.media_horas_dia;
        document.getElementById('diasTrabalhados').textContent = dados.resumo.dias_trabalhados;

        // Resumo de pontualidade com barras de progresso
        const taxaPontualidade = dados.resumo.taxa_pontualidade;
        document.getElementById('taxaPontualidade').textContent = `${taxaPontualidade.toFixed(1)}%`;
        document.getElementById('progressPontualidade').style.width = `${taxaPontualidade}%`;

        const totalAtrasos = dados.resumo.total_atrasos;
        document.getElementById('totalAtrasos').textContent = totalAtrasos;
        
        // Calcula porcentagem de atrasos (máximo 100%)
        const porcentagemAtrasos = Math.min((totalAtrasos / dados.resumo.dias_trabalhados) * 100, 100);
        document.getElementById('progressAtrasos').style.width = `${porcentagemAtrasos}%`;
    }
}

/**
 * Busca dados específicos para os gráficos
 * Função chamada quando usuário acessa abas de gráficos
 */
async function buscarDadosGraficos() {
    try {
        console.log('Buscando dados dos gráficos...');
        
        // Obter filtros atuais do formulário
        const filtros = {
            funcionario_id: document.getElementById('funcionario_id').value || null,
            setor: document.getElementById('setor').value || null,
            tipo_registro: document.getElementById('tipo_registro').value || null,
            metodo_registro: document.getElementById('metodo_registro').value || null,
            data_inicio: document.getElementById('data_inicio').value,
            data_fim: document.getElementById('data_fim').value
        };

        // Requisição para endpoint específico de gráficos
        const response = await fetch('/relatorios/api/dados-graficos', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filtros)
        });

        if (!response.ok) {
            throw new Error('Erro ao buscar dados dos gráficos');
        }

        const data = await response.json();
        console.log('Dados dos gráficos recebidos:', data);
        
        if (data.success && data.dados) {
            atualizarGraficos(data.dados);
        } else {
            console.warn('Dados dos gráficos não disponíveis:', data.message);
        }
        
    } catch (error) {
        console.error('Erro ao buscar dados dos gráficos:', error);
        // Não exibe erro para usuário - função é opcional
    }
}

/* ========================================
   FUNÇÕES DE UX E CONVENIÊNCIA
   ======================================== */

/**
 * Define a data atual em um campo específico
 * @param {string} campoId - ID do campo de input date
 */
function definirDataAtual(campoId) {
    const hoje = new Date();
    const dataFormatada = hoje.toISOString().split('T')[0];
    document.getElementById(campoId).value = dataFormatada;
    
    // Efeito visual de feedback (animação de escala)
    const campo = document.getElementById(campoId);
    campo.style.transform = 'scale(1.05)';
    campo.style.transition = 'transform 0.2s ease';
    setTimeout(() => {
        campo.style.transform = 'scale(1)';
    }, 200);
}

/**
 * Define o período da semana atual nos campos de data
 * Calcula primeiro e último dia da semana (domingo a sábado)
 */
function definirPeriodoSemana() {
    const hoje = new Date();
    const primeiroDiaSemana = new Date(hoje.setDate(hoje.getDate() - hoje.getDay()));
    const ultimoDiaSemana = new Date(hoje.setDate(hoje.getDate() - hoje.getDay() + 6));
    
    document.getElementById('data_inicio').value = primeiroDiaSemana.toISOString().split('T')[0];
    document.getElementById('data_fim').value = ultimoDiaSemana.toISOString().split('T')[0];
}

/**
 * Define o período do mês atual nos campos de data
 * Calcula primeiro e último dia do mês corrente
 */
function definirPeriodoMes() {
    const hoje = new Date();
    const primeiroDiaDoMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
    const ultimoDiaDoMes = new Date(hoje.getFullYear(), hoje.getMonth() + 1, 0);
    
    document.getElementById('data_inicio').value = primeiroDiaDoMes.toISOString().split('T')[0];
    document.getElementById('data_fim').value = ultimoDiaDoMes.toISOString().split('T')[0];
}

/* ========================================
   INICIALIZAÇÃO AUTOMÁTICA E VALIDAÇÃO
   ======================================== */

/**
 * Event listener adicional para configuração automática
 * Executa após o DOM estar carregado
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando página de relatórios...');
    
    // CONFIGURAÇÃO AUTOMÁTICA DE DATAS
    const hoje = new Date().toISOString().split('T')[0];
    
    // Define data atual se campos estão vazios
    const dataInicio = document.getElementById('data_inicio');
    const dataFim = document.getElementById('data_fim');
    
    if (!dataInicio.value) {
        dataInicio.value = hoje;
    }
    
    if (!dataFim.value) {
        dataFim.value = hoje;
    }
    
    // CONFIGURAÇÃO DE TOOLTIPS (se Bootstrap disponível)
    adicionarTooltips();
    
    // VALIDAÇÃO AVANÇADA DO FORMULÁRIO
    const form = document.getElementById('filtrosForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validarFormulario()) {
                e.preventDefault();  // Impede submissão se inválido
                return false;
            }
        });
    }
    
    console.log('Página de relatórios inicializada com sucesso');
});

/**
 * Adiciona tooltips Bootstrap aos elementos da página
 * Funciona apenas se Bootstrap estiver carregado
 */
function adicionarTooltips() {
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

/**
 * Valida os dados do formulário antes da submissão
 * @returns {boolean} - true se válido, false caso contrário
 */
function validarFormulario() {
    const dataInicio = document.getElementById('data_inicio').value;
    const dataFim = document.getElementById('data_fim').value;
    
    // Verifica se as datas foram preenchidas
    if (!dataInicio || !dataFim) {
        mostrarErro('Por favor, preencha as datas de início e fim.');
        return false;
    }
    
    // Verifica ordem das datas
    if (new Date(dataInicio) > new Date(dataFim)) {
        mostrarErro('A data inicial não pode ser maior que a data final.');
        return false;
    }
    
    // Verifica se período não é excessivamente longo (performance)
    const diffTime = Math.abs(new Date(dataFim) - new Date(dataInicio));
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 365) {
        if (!confirm('O período selecionado é maior que 1 ano. Isso pode impactar a performance. Deseja continuar?')) {
            return false;
        }
    }
    
    return true;
}

/**
 * Limpa todos os filtros e reseta para valores padrão
 * Útil para "começar do zero" na busca
 */
function limparFiltros() {
    // Limpa campos de seleção
    document.getElementById('funcionario_id').value = '';
    document.getElementById('setor').value = '';
    document.getElementById('tipo_registro').value = '';
    document.getElementById('metodo_registro').value = '';
    
    // Reseta datas para hoje
    const hoje = new Date().toISOString().split('T')[0];
    document.getElementById('data_inicio').value = hoje;
    document.getElementById('data_fim').value = hoje;
    
    // Limpa resultados da interface
    document.getElementById('tabelaResultados').innerHTML = '';
    document.getElementById('statsContainer').style.display = 'none';
    document.getElementById('paginacaoContainer').style.display = 'none';
}
</script>
{% endblock %}