#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para Adicionar Coluna empresa_teste - RLPONTO-WEB
-------------------------------------------------------
Adiciona a coluna empresa_teste na tabela empresas e cria a tabela de log.
"""

import pymysql
from utils.config import Config
import os
import sys

def executar_sql_arquivo(arquivo):
    """
    Executa comandos SQL de um arquivo
    """
    try:
        # Verificar se o arquivo existe
        if not os.path.exists(arquivo):
            print(f"❌ Arquivo {arquivo} não encontrado!")
            return False
            
        # Ler conteúdo do arquivo
        with open(arquivo, 'r', encoding='utf-8') as f:
            sql_content = f.read()
            
        # Conectar ao banco
        config = Config.get_database_url()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        print(f"🔍 Executando SQL do arquivo: {arquivo}")
        
        # Executar comandos SQL
        # Dividir por ponto e vírgula, exceto dentro de strings
        # Esta é uma versão simplificada, não lida com todos os casos
        statements = []
        current = ""
        for line in sql_content.split('\n'):
            # Pular comentários
            if line.strip().startswith('--') or line.strip() == '':
                continue
                
            current += line + " "
            if line.strip().endswith(';'):
                statements.append(current.strip())
                current = ""
        
        # Executar cada statement
        for i, stmt in enumerate(statements):
            if stmt.strip():
                try:
                    cursor.execute(stmt)
                    print(f"✅ Comando {i+1}/{len(statements)} executado com sucesso")
                except Exception as e:
                    print(f"⚠️ Erro no comando {i+1}: {e}")
        
        # Verificar se a coluna foi adicionada
        cursor.execute("""
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'empresas' AND COLUMN_NAME = 'empresa_teste'
        """)
        
        if cursor.fetchone()[0] > 0:
            print("✅ Coluna empresa_teste existe na tabela empresas")
        else:
            print("❌ Coluna empresa_teste NÃO foi adicionada!")
            
        # Verificar se a tabela de log foi criada
        cursor.execute("""
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'log_exclusao_empresas'
        """)
        
        if cursor.fetchone()[0] > 0:
            print("✅ Tabela log_exclusao_empresas existe")
        else:
            print("❌ Tabela log_exclusao_empresas NÃO foi criada!")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("🎉 Processo concluído!")
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def adicionar_coluna_manualmente():
    """
    Adiciona a coluna empresa_teste manualmente
    """
    try:
        # Conectar ao banco
        config = Config.get_database_url()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        print("🔧 Adicionando coluna empresa_teste manualmente...")
        
        # Verificar se a coluna já existe
        cursor.execute("""
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'empresas' AND COLUMN_NAME = 'empresa_teste'
        """)
        
        if cursor.fetchone()[0] > 0:
            print("✅ Coluna empresa_teste já existe")
        else:
            # Adicionar a coluna
            cursor.execute("""
            ALTER TABLE empresas ADD COLUMN empresa_teste BOOLEAN DEFAULT FALSE AFTER ativa
            """)
            print("✅ Coluna empresa_teste adicionada com sucesso")
            
        # Criar tabela de log se não existir
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS log_exclusao_empresas (
            id INT AUTO_INCREMENT PRIMARY KEY,
            empresa_id INT NOT NULL,
            razao_social VARCHAR(200) NOT NULL,
            cnpj VARCHAR(18) NOT NULL,
            usuario_id INT NOT NULL,
            data_exclusao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            motivo TEXT
        ) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
        """)
        print("✅ Tabela log_exclusao_empresas criada/verificada")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("🎉 Processo manual concluído!")
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == '__main__':
    print('='*50)
    print('🚀 ADIÇÃO DE COLUNA EMPRESA_TESTE')
    print('='*50)
    
    # Tentar executar o SQL do arquivo
    arquivo_sql = os.path.join(os.path.dirname(__file__), 'adicionar_coluna_empresa_teste.sql')
    
    if os.path.exists(arquivo_sql):
        print(f"📄 Arquivo SQL encontrado: {arquivo_sql}")
        if not executar_sql_arquivo(arquivo_sql):
            print("⚠️ Falha ao executar SQL do arquivo. Tentando método manual...")
            adicionar_coluna_manualmente()
    else:
        print(f"⚠️ Arquivo SQL não encontrado: {arquivo_sql}")
        print("🔧 Executando método manual...")
        adicionar_coluna_manualmente()
    
    print('\n✅ Processo finalizado!') 