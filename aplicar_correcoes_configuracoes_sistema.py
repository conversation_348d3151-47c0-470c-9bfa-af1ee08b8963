#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para Correção da Tabela configuracoes_sistema
RLPONTO-WEB v1.0 - Correções Urgentes

Desenvolvido por: AiNexus Tecnologia
Data: 10/01/2025
Objetivo: Implementar correções críticas da tabela configuracoes_sistema
"""

import pymysql
import json
import logging
from datetime import datetime
import sys
import os

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('correcoes_configuracoes.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configurações do banco
DB_CONFIG = {
    'host': '************',
    'user': 'cavalcrod',
    'password': '200381',
    'database': 'controle_ponto',
    'charset': 'utf8mb4'
}

def conectar_banco():
    """Conecta ao banco de dados"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        logger.info("✅ Conexão com banco estabelecida")
        return conn
    except Exception as e:
        logger.error(f"❌ Erro ao conectar ao banco: {e}")
        return None

def main():
    """Função principal"""
    logger.info("🚀 Iniciando correções da tabela configuracoes_sistema")
    
    conn = conectar_banco()
    if not conn:
        return False
        
    try:
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # Verificar se tabela existe e tem dados
        cursor.execute("SELECT COUNT(*) as total FROM configuracoes_sistema")
        total = cursor.fetchone()['total']
        logger.info(f"✅ Configurações existentes: {total}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    main() 