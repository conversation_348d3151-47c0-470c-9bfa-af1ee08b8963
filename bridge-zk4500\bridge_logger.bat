@echo off
:: =====================================================================
:: SISTEMA DE LOG BRIDGE ZK4500 - RLPONTO-WEB v1.0
:: Sistema centralizado de logging para operações do bridge
:: Desenvolvido por: <PERSON> Rodrigues - AiNexus Tecnologia
:: =====================================================================

:: Definir variáveis de log
set LOG_DIR=C:\RLPonto-Bridge\logs
set LOG_FILE=%LOG_DIR%\bridge_operations.log
set ERROR_LOG=%LOG_DIR%\bridge_errors.log
set INSTALL_LOG=%LOG_DIR%\installation_history.log

:: Função para criar timestamp
for /f "tokens=2 delims==" %%i in ('wmic os get localdatetime /value') do set datetime=%%i
set TIMESTAMP=%datetime:~0,4%-%datetime:~4,2%-%datetime:~6,2% %datetime:~8,2%:%datetime:~10,2%:%datetime:~12,2%

:: Função para log geral (uso: call :LOG "mensagem")
:LOG
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%" >nul 2>&1
echo [%TIMESTAMP%] %~1 >> "%LOG_FILE%"
exit /b

:: Função para log de erro (uso: call :ERROR "mensagem de erro")
:ERROR
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%" >nul 2>&1
echo [%TIMESTAMP%] ERROR: %~1 >> "%ERROR_LOG%"
echo [%TIMESTAMP%] ERROR: %~1 >> "%LOG_FILE%"
exit /b

:: Função para log de instalação (uso: call :INSTALL_LOG "evento" "status" "detalhes")
:INSTALL_LOG
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%" >nul 2>&1
echo [%TIMESTAMP%] INSTALL: %~1 - %~2 - %~3 >> "%INSTALL_LOG%"
echo [%TIMESTAMP%] INSTALL: %~1 - %~2 - %~3 >> "%LOG_FILE%"
exit /b

:: Função para iniciar seção de log (uso: call :START_SECTION "nome_operacao")
:START_SECTION
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%" >nul 2>&1
echo. >> "%LOG_FILE%"
echo ======================================== >> "%LOG_FILE%"
echo [%TIMESTAMP%] INICIANDO: %~1 >> "%LOG_FILE%"
echo ======================================== >> "%LOG_FILE%"
exit /b

:: Função para finalizar seção de log (uso: call :END_SECTION "nome_operacao" "status")
:END_SECTION
echo ======================================== >> "%LOG_FILE%"
echo [%TIMESTAMP%] FINALIZADO: %~1 - %~2 >> "%LOG_FILE%"
echo ======================================== >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"
exit /b

:: Função para log de sistema (uso: call :SYSTEM_INFO)
:SYSTEM_INFO
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%" >nul 2>&1
echo [%TIMESTAMP%] === INFORMACOES DO SISTEMA === >> "%LOG_FILE%"
echo [%TIMESTAMP%] Computador: %COMPUTERNAME% >> "%LOG_FILE%"
echo [%TIMESTAMP%] Usuario: %USERNAME% >> "%LOG_FILE%"
echo [%TIMESTAMP%] Sistema: %OS% >> "%LOG_FILE%"
for /f "tokens=2 delims=:" %%i in ('systeminfo ^| findstr /C:"OS Version"') do echo [%TIMESTAMP%] Versao: %%i >> "%LOG_FILE%"
echo [%TIMESTAMP%] =================================== >> "%LOG_FILE%"
exit /b

:: Função para limpar logs antigos (uso: call :CLEANUP_LOGS)
:CLEANUP_LOGS
if exist "%LOG_DIR%" (
    forfiles /p "%LOG_DIR%" /s /m *.log /d -30 /c "cmd /c del @path" >nul 2>&1
    echo [%TIMESTAMP%] Logs antigos (30+ dias) removidos >> "%LOG_FILE%"
)
exit /b

:: Se chamado diretamente, mostrar ajuda
if "%1"=="" (
    echo.
    echo SISTEMA DE LOG BRIDGE ZK4500
    echo =============================
    echo.
    echo Uso: call bridge_logger.bat :FUNCAO "parametros"
    echo.
    echo Funcoes disponiveis:
    echo   :LOG "mensagem"                    - Log geral
    echo   :ERROR "erro"                      - Log de erro
    echo   :INSTALL_LOG "evento" "status"     - Log de instalacao
    echo   :START_SECTION "operacao"          - Iniciar secao
    echo   :END_SECTION "operacao" "status"   - Finalizar secao
    echo   :SYSTEM_INFO                       - Info do sistema
    echo   :CLEANUP_LOGS                      - Limpar logs antigos
    echo.
    echo Logs salvos em: %LOG_DIR%
    echo.
    pause
)

goto :EOF 