-- =====================================================
-- IMPLEMENTAÇÃO SIMPLIFICADA: HERANÇA DINÂMICA DE JORNADAS
-- Sistema de Controle de Ponto - RLPONTO-WEB
-- Data: 14/07/2025
-- =====================================================

-- 1. EXPANDIR ENUM DO HISTÓRICO
ALTER TABLE historico_funcionario 
MODIFY COLUMN tipo_evento ENUM(
    'HORA_EXTRA_SOLICITADA',
    'HORA_EXTRA_APROVADA', 
    'HORA_EXTRA_REJEITADA',
    'BANCO_HORAS_CREDITADO',
    'BANCO_HORAS_DEBITADO',
    'AUSENCIA_REGISTRADA',
    'ATRASO_REGISTRADO',
    'JORNADA_ALTERADA',
    'JORNADA_HERDADA_EMPRESA',
    'JORNADA_HERDADA_CLIENTE',
    'ALOCACAO_CRIADA',
    'ALOCACAO_FINALIZADA',
    'EMPRESA_MUDOU_JORNADA'
) NOT NULL;

-- 2. ADICIONAR CAMPOS (apenas se não existirem)
ALTER TABLE funcionarios 
ADD COLUMN usa_horario_empresa BOOLEAN DEFAULT TRUE 
COMMENT 'Se TRUE, funcionário herda automaticamente mudanças da empresa';

ALTER TABLE funcionarios 
ADD COLUMN data_atualizacao_jornada TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
COMMENT 'Data da última atualização de jornada';

ALTER TABLE funcionarios 
ADD COLUMN jornada_alterada_por INT NULL
COMMENT 'ID do usuário que fez a última alteração de jornada';

-- 3. CRIAR TABELA DE LOG
CREATE TABLE log_mudancas_jornada (
    id INT AUTO_INCREMENT PRIMARY KEY,
    funcionario_id INT UNSIGNED NOT NULL,
    jornada_anterior_id INT NULL,
    jornada_nova_id INT NOT NULL,
    tipo_mudanca ENUM(
        'CADASTRO_INICIAL',
        'ALOCACAO_CLIENTE',
        'EMPRESA_ALTEROU',
        'ALTERACAO_MANUAL',
        'RETORNO_EMPRESA'
    ) NOT NULL,
    motivo TEXT NULL,
    dados_jornada_anterior JSON NULL,
    dados_jornada_nova JSON NOT NULL,
    usuario_responsavel INT NULL,
    data_mudanca TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
    FOREIGN KEY (jornada_anterior_id) REFERENCES jornadas_trabalho(id) ON DELETE SET NULL,
    FOREIGN KEY (jornada_nova_id) REFERENCES jornadas_trabalho(id) ON DELETE CASCADE,
    
    INDEX idx_funcionario_data (funcionario_id, data_mudanca),
    INDEX idx_tipo_mudanca (tipo_mudanca),
    INDEX idx_data_mudanca (data_mudanca)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. ATUALIZAR FUNCIONÁRIOS EXISTENTES
UPDATE funcionarios 
SET usa_horario_empresa = TRUE,
    data_atualizacao_jornada = CURRENT_TIMESTAMP
WHERE usa_horario_empresa IS NULL;

-- 5. CRIAR ÍNDICES
CREATE INDEX idx_funcionarios_usa_horario_empresa 
ON funcionarios(empresa_id, usa_horario_empresa, jornada_trabalho_id);

CREATE INDEX idx_jornadas_trabalho_empresa_padrao 
ON jornadas_trabalho(empresa_id, padrao, ativa);

CREATE INDEX idx_funcionario_alocacoes_ativo 
ON funcionario_alocacoes(funcionario_id, ativo, empresa_cliente_id);

SELECT 'Implementação básica concluída!' as status;
