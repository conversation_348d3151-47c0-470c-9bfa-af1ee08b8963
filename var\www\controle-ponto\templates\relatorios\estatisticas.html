{% extends "base.html" %}

{% block title %}Estatísticas - Controle de <PERSON>o{% endblock %}

{% block extra_css %}
<style>
    /* ========================================
       RLPONTO-WEB VISUAL IDENTITY - v1.0
       SEGUINDO ESPECIFICAÇÕES DO VISUAL.MD
       ======================================== */

    :root {
        /* Cor primária - Verde-azulado (igual sidebar) */
        --primary-color: #4fbdba;
        --primary-hover: #26a69a;
        --primary-light: #80cbc4;
        --primary-dark: #00695c;

        /* Backgrounds */
        --background-color: #f9fafb;        /* Background principal */
        --card-background: #ffffff;         /* Fundo de cards */
        --hover-bg: #f3f4f6;               /* Hover states */
        --sidebar-bg: #ffffff;             /* Sidebar background */

        /* Textos */
        --text-primary: #1f2937;           /* Texto principal (preto) */
        --text-secondary: #6b7280;         /* Texto secundário (cinza médio) */
        --text-muted: #9ca3af;             /* Texto desabilitado */
        --text-white: #ffffff;             /* Texto branco */

        /* Bordas e divisores */
        --border-color: #e5e7eb;           /* Bordas padrão */
        --border-light: #f3f4f6;           /* Bordas claras */
        --border-dark: #d1d5db;            /* Bordas escuras */

        /* Estados */
        --success-color: #10b981;          /* Verde sucesso */
        --success-bg: #dcfce7;             /* Background sucesso */
        --success-text: #166534;           /* Texto sucesso */

        --warning-color: #f59e0b;          /* Amarelo aviso */
        --warning-bg: #fef3c7;             /* Background aviso */
        --warning-text: #92400e;           /* Texto aviso */

        --danger-color: #ef4444;           /* Vermelho erro */
        --danger-bg: #fee2e2;              /* Background erro */
        --danger-text: #dc2626;            /* Texto erro */

        --info-color: #3b82f6;             /* Azul informação */
        --info-bg: #dbeafe;                /* Background info */
        --info-text: #1e40af;              /* Texto info */

        /* Tamanhos de fonte */
        --font-size-xs: 0.75rem;      /* 12px - Textos muito pequenos */
        --font-size-sm: 0.875rem;     /* 14px - Textos pequenos */
        --font-size-base: 1rem;       /* 16px - Texto base */
        --font-size-lg: 1.125rem;     /* 18px - Textos grandes */
        --font-size-xl: 1.25rem;      /* 20px - Subtítulos */
        --font-size-2xl: 1.5rem;      /* 24px - Títulos */
        --font-size-3xl: 1.875rem;    /* 30px - Títulos principais */

        /* Espaçamentos */
        --spacing-xs: 0.25rem;    /* 4px */
        --spacing-sm: 0.5rem;     /* 8px */
        --spacing-md: 1rem;       /* 16px */
        --spacing-lg: 1.5rem;     /* 24px */
        --spacing-xl: 2rem;       /* 32px */
        --spacing-2xl: 3rem;      /* 48px */

        /* Border radius */
        --radius-sm: 6px;
        --radius-md: 8px;
        --radius-lg: 12px;
    }

    /* ========================================
       GLOBAL STYLES - PADRÃO RLPONTO-WEB
       ======================================== */

    body {
        background-color: var(--background-color);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        color: var(--text-primary);
        line-height: 1.5;
    }

    .main-container {
        background-color: var(--background-color);
        min-height: 100vh;
        padding: 2rem;
    }

    /* ========================================
       HEADER PADRÃO - SEGUINDO VISUAL.MD
       ======================================== */

    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        border-radius: var(--radius-lg);
        padding: 2rem;
        margin-bottom: 2rem;
        color: var(--text-white);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 60%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        transform: rotate(15deg);
    }

    .page-header h1 {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        position: relative;
        z-index: 2;
    }

    .page-header p {
        font-size: var(--font-size-lg);
        margin: 0;
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }

    /* ========================================
       KPI CARDS - PADRÃO RLPONTO-WEB
       ======================================== */
    .kpi-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        height: 100%;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .kpi-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border-color: var(--border-dark);
    }

    .kpi-icon {
        width: 48px !important;
        height: 48px !important;
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: var(--spacing-md);
        font-size: 1.5rem;
    }

    .kpi-icon svg {
        width: 24px !important;
        height: 24px !important;
        max-width: 24px !important;
        max-height: 24px !important;
    }

    .kpi-icon.blue { background: var(--info-bg); color: var(--info-color); }
    .kpi-icon.green { background: var(--success-bg); color: var(--success-color); }
    .kpi-icon.purple { background: var(--info-bg); color: var(--primary-color); }
    .kpi-icon.orange { background: var(--warning-bg); color: var(--warning-color); }

    .kpi-label {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;
        margin-bottom: var(--spacing-sm);
    }

    .kpi-value {
        color: var(--text-primary);
        font-size: var(--font-size-3xl);
        font-weight: 700;
        margin-bottom: var(--spacing-xs);
        line-height: 1;
    }

    .kpi-trend {
        font-size: var(--font-size-sm);
        font-weight: 500;
    }

    .kpi-trend.positive { color: var(--success-color); }
    .kpi-trend.negative { color: var(--danger-color); }

    /* ========================================
       CHART CARDS - PADRÃO RLPONTO-WEB
       ======================================== */
    .chart-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .chart-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border-color: var(--border-dark);
    }

    .chart-header {
        background-color: var(--hover-bg);
        border-bottom: 1px solid var(--border-color);
        padding: var(--spacing-lg);
    }

    .chart-title {
        color: var(--text-primary);
        font-size: var(--font-size-lg);
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .chart-subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        margin: 0.25rem 0 0 0;
    }

    .chart-body {
        padding: var(--spacing-lg);
    }

    .legend-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-sm);
    }

    .legend-label {
        display: flex;
        align-items: center;
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: var(--spacing-sm);
    }

    .legend-color.purple { background: var(--primary-color); }
    .legend-color.orange { background: var(--warning-color); }

    .legend-value {
        color: var(--text-primary);
        font-weight: 500;
        font-size: var(--font-size-sm);
    }

    /* ========================================
       ACTION CARDS - PADRÃO RLPONTO-WEB
       ======================================== */
    .action-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        text-decoration: none;
        color: inherit;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: block;
    }

    .action-card:hover {
        border-color: var(--primary-color);
        background: rgba(79, 189, 186, 0.05);
        text-decoration: none;
        color: inherit;
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .action-card:hover .action-icon.blue {
        background: var(--info-bg);
    }

    .action-card:hover.green-hover {
        border-color: var(--success-color);
        background: rgba(16, 185, 129, 0.05);
    }

    .action-card:hover.green-hover .action-icon.green {
        background: var(--success-bg);
    }

    .action-card:hover.purple-hover {
        border-color: var(--primary-color);
        background: rgba(79, 189, 186, 0.05);
    }

    .action-card:hover.purple-hover .action-icon.purple {
        background: var(--info-bg);
    }

    .action-icon {
        width: 48px !important;
        height: 48px !important;
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--spacing-md);
        transition: all 0.3s ease;
    }

    .action-icon svg {
        width: 24px !important;
        height: 24px !important;
        max-width: 24px !important;
        max-height: 24px !important;
    }

    .action-title {
        color: var(--text-primary);
        font-weight: 500;
        margin: 0;
        font-size: var(--font-size-base);
    }

    .action-subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-xs);
        margin: 0;
    }

    .chart-container {
        position: relative;
        height: 300px;
    }

    .chart-container canvas {
        max-width: 100%;
        height: auto;
    }

    /* ========================================
       BADGES DE STATUS - PADRÃO RLPONTO-WEB
       ======================================== */
    .status-badge {
        background: var(--success-bg);
        color: var(--success-text);
        border: 1px solid var(--success-color);
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: var(--font-size-xs);
        font-weight: 600;
        display: inline-flex;
        align-items: center;
    }

    .status-indicator {
        width: 6px;
        height: 6px;
        background: var(--success-color);
        border-radius: 50%;
        margin-right: var(--spacing-sm);
    }

    /* ========================================
       RESPONSIVIDADE - MOBILE FIRST
       ======================================== */

    @media (max-width: 767px) {
        .main-container {
            padding: 1rem;
        }

        .page-header {
            padding: 1.5rem;
            text-align: center;
        }

        .page-header h1 {
            font-size: var(--font-size-2xl);
        }

        .kpi-card {
            margin-bottom: 1rem;
        }

        .chart-container {
            height: 250px;
        }
    }

    @media (min-width: 768px) and (max-width: 1023px) {
        .main-container {
            padding: 1.5rem;
        }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Header da Página -->
    <div class="page-header">
        <h1>
            <i class="fas fa-chart-bar me-3"></i>
            {{ titulo }}
        </h1>
        <p>Dashboard de estatísticas e métricas • {{ periodo }}</p>
    </div>

    <!-- KPI Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="kpi-card">
                <div class="kpi-icon blue">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="kpi-label">Total de Registros</div>
                <div class="d-flex align-items-end">
                    <div class="kpi-value me-2">{{ "{:,}".format(stats.total_registros) }}</div>
                    <div class="kpi-trend positive">+12.3%</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="kpi-card">
                <div class="kpi-icon green">
                    <i class="fas fa-users"></i>
                </div>
                <div class="kpi-label">Funcionários Ativos</div>
                <div class="d-flex align-items-end">
                    <div class="kpi-value me-2">{{ "{:,}".format(stats.funcionarios_ativos) }}</div>
                    <div class="kpi-trend positive">+5.2%</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="kpi-card">
                <div class="kpi-icon purple">
                    <i class="fas fa-fingerprint"></i>
                </div>
                <div class="kpi-label">Registros Biométricos</div>
                <div class="d-flex align-items-end">
                    <div class="kpi-value me-2">{{ "{:,}".format(stats.registros_biometricos) }}</div>
                    <div class="kpi-trend positive">{{ stats.percentual_biometrico }}%</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="kpi-card">
                <div class="kpi-icon orange">
                    <i class="fas fa-edit"></i>
                </div>
                <div class="kpi-label">Registros Manuais</div>
                <div class="d-flex align-items-end">
                    <div class="kpi-value me-2">{{ "{:,}".format(stats.registros_manuais) }}</div>
                    <div class="kpi-trend negative">-2.1%</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-area"></i>
                        Registros por Período
                    </h3>
                    <p class="chart-subtitle">Últimos 7 dias de atividade</p>
                </div>
                <div class="chart-body">
                    <div class="chart-container">
                        <canvas id="chartRegistrosDiarios"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-3">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-pie"></i>
                        Distribuição de Métodos
                    </h3>
                    <p class="chart-subtitle">Biométrico vs Manual</p>
                </div>
                <div class="chart-body">
                    <div class="chart-container" style="height: 200px;">
                        <canvas id="chartMetodos"></canvas>
                    </div>
                    <div class="mt-3">
                        <div class="legend-item">
                            <div class="legend-label">
                                <div class="legend-color purple"></div>
                                Biométrico
                            </div>
                            <div class="legend-value">{{ stats.percentual_biometrico }}%</div>
                        </div>
                        <div class="legend-item">
                                <div class="legend-label">
                                    <div class="legend-color orange"></div>
                                    Manual
                                </div>
                                <div class="legend-value">{{ 100 - stats.percentual_biometrico }}%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pontualidade Chart -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Análise de Pontualidade</h3>
                        <p class="chart-subtitle">Controle de atrasos por período</p>
                    </div>
                    <div class="chart-body">
                        <div class="chart-container">
                            <canvas id="chartPontualidade"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Ações Rápidas</h3>
                        <p class="chart-subtitle">Acesso direto às principais funcionalidades</p>
                    </div>
                    <div class="chart-body">
                        <div class="row">
                            <div class="col-lg-4 col-md-6 mb-3">
                                <a href="/relatorios/pontos" class="action-card d-flex align-items-center">
                                    <div class="action-icon blue">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="action-title">Relatórios</div>
                                        <div class="action-subtitle">Ver detalhados</div>
                                    </div>
                                </a>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <a href="/registro-ponto/manual" class="action-card green-hover d-flex align-items-center">
                                    <div class="action-icon green">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="action-title">Ponto Manual</div>
                                        <div class="action-subtitle">Registrar agora</div>
                                    </div>
                                </a>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <a href="/registro-ponto/biometrico" class="action-card purple-hover d-flex align-items-center">
                                    <div class="action-icon purple">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0h-2m8-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="action-title">Ponto Biométrico</div>
                                        <div class="action-subtitle">Capturar digital</div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Chart data preparation
    var chartsData = {
        labels: {{ graficos.labels_dias | tojson }},
        registrosDiarios: {{ graficos.dados_registros_diarios | tojson }},
        pontualidade: {{ graficos.dados_pontualidade | tojson }},
        biometricos: {{ stats.registros_biometricos }},
        manuais: {{ stats.registros_manuais }}
    };

    // Chart.js configuration
    Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#6c757d';

    // Base chart options
    var baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                border: {
                    display: false
                },
                grid: {
                    color: '#f3f4f6'
                },
                ticks: {
                    color: '#6c757d'
                }
            },
            x: {
                border: {
                    display: false
                },
                grid: {
                    display: false
                },
                ticks: {
                    color: '#6c757d'
                }
            }
        }
    };

    // Bar Chart - Daily Records
    var ctxBar = document.getElementById('chartRegistrosDiarios');
    if (ctxBar) {
        new Chart(ctxBar, {
            type: 'bar',
            data: {
                labels: chartsData.labels,
                datasets: [{
                    label: 'Registros',
                    data: chartsData.registrosDiarios,
                    backgroundColor: '#0d6efd',
                    borderRadius: 4,
                    borderSkipped: false
                }]
            },
            options: baseOptions
        });
    }

    // Doughnut Chart - Methods
    var ctxDoughnut = document.getElementById('chartMetodos');
    if (ctxDoughnut) {
        new Chart(ctxDoughnut, {
            type: 'doughnut',
            data: {
                labels: ['Biométrico', 'Manual'],
                datasets: [{
                    data: [chartsData.biometricos, chartsData.manuais],
                    backgroundColor: ['#8b5cf6', '#f59e0b'],
                    borderColor: '#ffffff',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    // Line Chart - Punctuality
    var ctxLine = document.getElementById('chartPontualidade');
    if (ctxLine) {
        new Chart(ctxLine, {
            type: 'line',
            data: {
                labels: chartsData.labels,
                datasets: [{
                    label: 'Atrasos',
                    data: chartsData.pontualidade,
                    fill: true,
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderColor: '#dc3545',
                    borderWidth: 2,
                    tension: 0.3,
                    pointBackgroundColor: '#dc3545',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 3
                }]
            },
            options: baseOptions
        });
    }
});
</script>
{% endblock %} 