#!/usr/bin/env python3
"""
Teste completo da edição de funcionário
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager, FuncionarioQueries
from app_funcionarios import _buscar_jornada_empresa_funcionario, _buscar_epis_funcionario

def teste_edicao_completo():
    """Teste completo da função de edição"""
    print("🧪 TESTE COMPLETO: EDIÇÃO DE FUNCIONÁRIO")
    print("=" * 60)
    
    try:
        funcionario_id = 1
        
        # 1. Buscar funcionário (como no código real)
        print("📋 1. BUSCANDO FUNCIONÁRIO:")
        funcionario = FuncionarioQueries.get_by_id(funcionario_id)
        
        if not funcionario:
            print("   ❌ Funcionário não encontrado")
            return False
            
        print(f"   ✅ Funcionário: {funcionario['nome_completo']}")
        print(f"   🏢 Empresa ID: {funcionario.get('empresa_id')}")
        
        # 2. Conversão de campos de horário (como no código real)
        print(f"\n🕐 2. CONVERTENDO CAMPOS DE HORÁRIO:")
        campos_horario = ['jornada_seg_qui_entrada', 'jornada_seg_qui_saida', 
                         'jornada_sex_entrada', 'jornada_sex_saida',
                         'jornada_intervalo_entrada', 'jornada_intervalo_saida']
        
        for campo in campos_horario:
            valor = funcionario.get(campo)
            if valor is not None:
                try:
                    if hasattr(valor, 'strftime'):
                        funcionario[campo] = valor.strftime('%H:%M')
                    else:
                        funcionario[campo] = str(valor)
                    print(f"   ✅ {campo}: {funcionario[campo]}")
                except Exception as e:
                    print(f"   ❌ Erro ao converter {campo}: {e}")
                    funcionario[campo] = ''
            elif valor is None:
                funcionario[campo] = ''
                print(f"   ⚠️ {campo}: definido como vazio")
                
        # 3. Buscar EPIs (como no código real)
        print(f"\n🦺 3. BUSCANDO EPIs:")
        try:
            funcionario['epis'] = _buscar_epis_funcionario(funcionario_id)
            print(f"   ✅ EPIs carregados: {len(funcionario['epis'])}")
        except Exception as e:
            print(f"   ❌ Erro ao buscar EPIs: {e}")
            funcionario['epis'] = []
            
        # 4. Buscar jornada da empresa (como no código real)
        print(f"\n🕐 4. BUSCANDO JORNADA DA EMPRESA:")
        jornada_empresa = None
        if funcionario.get('empresa_id'):
            try:
                jornada_empresa = _buscar_jornada_empresa_funcionario(funcionario['empresa_id'])
                if jornada_empresa:
                    print(f"   ✅ Jornada encontrada: {jornada_empresa['nome_jornada']}")
                else:
                    print(f"   ⚠️ Nenhuma jornada encontrada para empresa {funcionario['empresa_id']}")
            except Exception as e:
                print(f"   ❌ Erro ao buscar jornada: {e}")
                jornada_empresa = None
        else:
            print(f"   ⚠️ Funcionário sem empresa definida")
            
        # 5. Verificar dados para template
        print(f"\n📄 5. VERIFICANDO DADOS PARA TEMPLATE:")
        print(f"   👤 Nome: {funcionario.get('nome_completo', 'N/A')}")
        print(f"   📧 Email: {funcionario.get('email', 'N/A')}")
        print(f"   📱 Telefone: {funcionario.get('telefone1', 'N/A')}")
        print(f"   🏢 Empresa ID: {funcionario.get('empresa_id', 'N/A')}")
        print(f"   📷 Foto: {'Presente' if funcionario.get('foto_3x4') else 'Ausente'}")
        print(f"   🦺 EPIs: {len(funcionario.get('epis', []))}")
        print(f"   🕐 Jornada Empresa: {'Presente' if jornada_empresa else 'Ausente'}")
        
        # 6. Simular context do template
        print(f"\n🎨 6. SIMULANDO CONTEXT DO TEMPLATE:")
        context = {
            'data': funcionario,
            'funcionario_id': funcionario_id,
            'jornada_empresa': jornada_empresa,
            'modo_edicao': True
        }
        
        print(f"   ✅ Context preparado com {len(context)} chaves")
        for key, value in context.items():
            if key == 'data':
                print(f"   📋 {key}: dict com {len(value)} campos")
            elif key == 'jornada_empresa' and value:
                print(f"   🕐 {key}: {value.get('nome_jornada', 'N/A')}")
            else:
                print(f"   📝 {key}: {value}")
                
        print(f"\n✅ TESTE COMPLETO EXECUTADO SEM ERROS")
        return True
        
    except Exception as e:
        print(f"❌ ERRO NO TESTE: {e}")
        import traceback
        traceback.print_exc()
        return False

def verificar_template_jornada():
    """Verificar se o template pode processar a jornada"""
    print(f"\n🎨 VERIFICANDO TEMPLATE DE JORNADA:")
    print("=" * 40)
    
    try:
        # Simular dados de jornada como seriam passados para o template
        jornada_empresa = {
            'nome_jornada': 'Jornada Padrão AiNexus',
            'seg_qui_entrada': '08:00:00',
            'seg_qui_saida': '17:00:00',
            'sexta_entrada': '08:00:00',
            'sexta_saida': '17:00:00',
            'intervalo_inicio': '12:00:00',
            'intervalo_fim': '13:00:00',
            'tolerancia_entrada_minutos': 15
        }
        
        print(f"   📋 Dados de jornada simulados:")
        for key, value in jornada_empresa.items():
            print(f"      {key}: {value}")
            
        # Simular conversões que o template faria
        print(f"\n   🔄 Simulando conversões do template:")
        
        # Verificar se os campos de tempo podem ser processados
        campos_tempo = ['seg_qui_entrada', 'seg_qui_saida', 'sexta_entrada', 'sexta_saida', 'intervalo_inicio', 'intervalo_fim']
        
        for campo in campos_tempo:
            valor = jornada_empresa.get(campo)
            if valor:
                try:
                    # Simular strftime que seria usado no template
                    if isinstance(valor, str):
                        # Se já é string, tentar converter para time e depois formatar
                        from datetime import datetime
                        time_obj = datetime.strptime(valor, '%H:%M:%S').time()
                        formatado = time_obj.strftime('%H:%M')
                        print(f"      ✅ {campo}: {valor} → {formatado}")
                    else:
                        print(f"      ⚠️ {campo}: {valor} (tipo: {type(valor)})")
                except Exception as e:
                    print(f"      ❌ {campo}: Erro na conversão - {e}")
            else:
                print(f"      ⚠️ {campo}: vazio")
                
        print(f"   ✅ Template pode processar os dados")
        
    except Exception as e:
        print(f"   ❌ Erro na verificação do template: {e}")

if __name__ == "__main__":
    sucesso = teste_edicao_completo()
    verificar_template_jornada()
    print(f"\n{'🎉 TODOS OS TESTES PASSARAM!' if sucesso else '❌ TESTES COM ERROS!'}")
