@echo off
echo ===================================
echo Configuracao MCP - RLPONTO-WEB
echo ===================================
echo.

echo 1. Verificando Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERRO: Node.js nao encontrado!
    echo Por favor, verifique se o Node.js foi instalado corretamente.
    pause
    exit /b 1
)

echo 2. Verificando npm...
npm --version
if %errorlevel% neq 0 (
    echo ERRO: npm nao encontrado!
    pause
    exit /b 1
)

echo 3. Verificando npx...
npx --version
if %errorlevel% neq 0 (
    echo ERRO: npx nao encontrado!
    pause
    exit /b 1
)

echo.
echo ===================================
echo Instalando servidores MCP...
echo ===================================

echo 4. Instalando GitHub MCP (Smithery CLI)...
call npm install -g @smithery/cli

echo 5. Instalando Browser Tools MCP...
call npm install -g @agentdeskai/browser-tools-mcp

echo 6. Instalando Context7 MCP...
call npm install -g @upstash/context7-mcp

echo 7. Instalando 21st-dev Magic MCP...
call npm install -g @21st-dev/magic

echo.
echo ===================================
echo Testando instalacao...
echo ===================================

echo 8. Testando GitHub MCP...
call npx @smithery/cli@latest --version

echo 9. Testando Browser Tools MCP...
call npx @agentdeskai/browser-tools-mcp@1.2.0 --help

echo 10. Testando Context7 MCP...
call npx @upstash/context7-mcp --help

echo 11. Testando 21st-dev Magic MCP...
call npx @21st-dev/magic@latest --help

echo.
echo ===================================
echo Configuracao concluida!
echo ===================================
echo.
echo Proximos passos:
echo 1. Feche completamente o VS Code
echo 2. Reabra o VS Code
echo 3. Abra o projeto RLPONTO-WEB
echo 4. Pressione Ctrl+Shift+P e digite "MCP"
echo.
echo O arquivo .vscode/settings.json ja foi criado com as configuracoes.
echo.
pause
