#!/usr/bin/env python3
"""
Demonstração: Sistema de Herança Dinâmica de Jornadas Funcionando
Sistema de Controle de Ponto - RLPONTO-WEB
Data: 14/07/2025

Demonstra o sistema funcionando com uma mudança real de jornada.
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager
from sistema_heranca_jornadas import SistemaHerancaJornadas
import logging
from datetime import datetime, time

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demonstrar_propagacao_automatica():
    """Demonstra a propagação automática de mudanças de jornada."""
    logger.info("🚀 DEMONSTRAÇÃO: PROPAGAÇÃO AUTOMÁTICA DE JORNADAS")
    logger.info("=" * 80)
    
    try:
        db = DatabaseManager()
        
        # 1. Buscar uma empresa com jornada padrão e funcionários
        logger.info("📋 PASSO 1: Identificando empresa e funcionários...")
        
        empresa_info = db.execute_query("""
            SELECT 
                e.id as empresa_id,
                e.nome_fantasia,
                jt.id as jornada_id,
                jt.nome_jornada,
                jt.seg_qui_entrada,
                jt.seg_qui_saida,
                COUNT(f.id) as total_funcionarios
            FROM empresas e
            LEFT JOIN jornadas_trabalho jt ON e.id = jt.empresa_id AND jt.padrao = TRUE AND jt.ativa = TRUE
            LEFT JOIN funcionarios f ON e.id = f.empresa_id AND f.ativo = TRUE AND f.usa_horario_empresa = TRUE
            WHERE e.ativa = TRUE AND jt.id IS NOT NULL
            GROUP BY e.id, e.nome_fantasia, jt.id, jt.nome_jornada, jt.seg_qui_entrada, jt.seg_qui_saida
            HAVING total_funcionarios > 0
            LIMIT 1
        """, fetch_one=True)
        
        if not empresa_info:
            logger.error("❌ Nenhuma empresa com jornada padrão e funcionários encontrada")
            return False
        
        logger.info(f"🏢 Empresa: {empresa_info['nome_fantasia']}")
        logger.info(f"⏰ Jornada atual: {empresa_info['nome_jornada']}")
        logger.info(f"🕐 Horário atual: {empresa_info['seg_qui_entrada']} às {empresa_info['seg_qui_saida']}")
        logger.info(f"👥 Funcionários afetados: {empresa_info['total_funcionarios']}")
        
        # 2. Listar funcionários que serão afetados
        logger.info("\n📋 PASSO 2: Funcionários que serão afetados...")
        
        funcionarios = db.execute_query("""
            SELECT id, nome_completo, jornada_trabalho_id
            FROM funcionarios 
            WHERE empresa_id = %s 
            AND ativo = TRUE 
            AND usa_horario_empresa = TRUE
            LIMIT 5
        """, (empresa_info['empresa_id'],))
        
        for func in funcionarios:
            logger.info(f"   👤 {func['nome_completo']} (Jornada ID: {func['jornada_trabalho_id']})")
        
        # 3. Verificar estado antes da mudança
        logger.info("\n📋 PASSO 3: Estado antes da mudança...")
        
        log_count_antes = db.execute_query("""
            SELECT COUNT(*) as total FROM log_mudancas_jornada
        """, fetch_one=True)
        
        historico_count_antes = db.execute_query("""
            SELECT COUNT(*) as total FROM historico_funcionario 
            WHERE tipo_evento = 'EMPRESA_MUDOU_JORNADA'
        """, fetch_one=True)
        
        logger.info(f"📊 Logs de mudança antes: {log_count_antes['total']}")
        logger.info(f"📊 Histórico de empresa antes: {historico_count_antes['total']}")
        
        # 4. Fazer uma pequena alteração na jornada (alterar 1 minuto na entrada)
        logger.info("\n🔄 PASSO 4: Alterando jornada da empresa...")
        
        # Calcular novo horário (adicionar 1 minuto)
        horario_atual = empresa_info['seg_qui_entrada']

        # Converter para string se necessário
        if hasattr(horario_atual, 'total_seconds'):
            # É um timedelta
            total_seconds = int(horario_atual.total_seconds())
            horas = total_seconds // 3600
            minutos = (total_seconds % 3600) // 60
            horario_str = f"{horas:02d}:{minutos:02d}:00"
        elif isinstance(horario_atual, str):
            horario_str = horario_atual
        else:
            # É um time object
            horario_str = horario_atual.strftime('%H:%M:%S')

        # Parse do horário atual
        from datetime import datetime
        horario_obj = datetime.strptime(horario_str, '%H:%M:%S').time()

        # Adicionar 1 minuto
        minutos_totais = horario_obj.hour * 60 + horario_obj.minute + 1
        nova_hora = minutos_totais // 60
        novo_minuto = minutos_totais % 60

        novo_horario = f"{nova_hora:02d}:{novo_minuto:02d}:00"
        
        logger.info(f"⏰ Alterando entrada de {horario_str} para {novo_horario}")
        
        # Executar a alteração
        db.execute_query("""
            UPDATE jornadas_trabalho 
            SET seg_qui_entrada = %s
            WHERE id = %s
        """, (novo_horario, empresa_info['jornada_id']), fetch_all=False)
        
        logger.info("✅ Jornada alterada! Triggers devem ter sido executados...")
        
        # 5. Verificar estado após a mudança
        logger.info("\n📋 PASSO 5: Verificando propagação automática...")
        
        # Aguardar um momento para os triggers processarem
        import time
        time.sleep(1)
        
        log_count_depois = db.execute_query("""
            SELECT COUNT(*) as total FROM log_mudancas_jornada
        """, fetch_one=True)
        
        historico_count_depois = db.execute_query("""
            SELECT COUNT(*) as total FROM historico_funcionario 
            WHERE tipo_evento = 'EMPRESA_MUDOU_JORNADA'
        """, fetch_one=True)
        
        logger.info(f"📊 Logs de mudança depois: {log_count_depois['total']}")
        logger.info(f"📊 Histórico de empresa depois: {historico_count_depois['total']}")
        
        novos_logs = log_count_depois['total'] - log_count_antes['total']
        novos_historicos = historico_count_depois['total'] - historico_count_antes['total']
        
        logger.info(f"🆕 Novos logs criados: {novos_logs}")
        logger.info(f"🆕 Novos históricos criados: {novos_historicos}")
        
        # 6. Verificar se funcionários foram atualizados
        logger.info("\n📋 PASSO 6: Verificando funcionários atualizados...")
        
        funcionarios_atualizados = db.execute_query("""
            SELECT f.nome_completo, f.data_atualizacao_jornada, jt.seg_qui_entrada
            FROM funcionarios f
            LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
            WHERE f.empresa_id = %s 
            AND f.ativo = TRUE 
            AND f.usa_horario_empresa = TRUE
            AND f.data_atualizacao_jornada >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
            LIMIT 5
        """, (empresa_info['empresa_id'],))
        
        logger.info(f"👥 Funcionários atualizados: {len(funcionarios_atualizados)}")
        for func in funcionarios_atualizados:
            logger.info(f"   ✅ {func['nome_completo']} - Novo horário: {func['seg_qui_entrada']} (Atualizado: {func['data_atualizacao_jornada']})")
        
        # 7. Mostrar últimos logs criados
        logger.info("\n📋 PASSO 7: Últimos logs de mudança...")
        
        ultimos_logs = db.execute_query("""
            SELECT lmj.tipo_mudanca, lmj.motivo, lmj.data_mudanca, f.nome_completo
            FROM log_mudancas_jornada lmj
            LEFT JOIN funcionarios f ON lmj.funcionario_id = f.id
            WHERE lmj.data_mudanca >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
            ORDER BY lmj.data_mudanca DESC
            LIMIT 3
        """)
        
        for log in ultimos_logs:
            logger.info(f"   📝 {log['data_mudanca']} - {log['nome_completo']} - {log['tipo_mudanca']}")
            logger.info(f"      Motivo: {log['motivo']}")
        
        # 8. Reverter a mudança para não afetar o sistema
        logger.info("\n🔄 PASSO 8: Revertendo mudança...")
        
        db.execute_query("""
            UPDATE jornadas_trabalho
            SET seg_qui_entrada = %s
            WHERE id = %s
        """, (horario_str, empresa_info['jornada_id']), fetch_all=False)
        
        logger.info("✅ Jornada revertida para o estado original")
        
        # 9. Resultado final
        logger.info("\n" + "=" * 80)
        logger.info("🎉 DEMONSTRAÇÃO CONCLUÍDA COM SUCESSO!")
        logger.info("=" * 80)
        
        if novos_logs > 0 and novos_historicos > 0:
            logger.info("✅ SISTEMA DE HERANÇA DINÂMICA FUNCIONANDO PERFEITAMENTE!")
            logger.info("✅ Triggers executaram automaticamente")
            logger.info("✅ Funcionários foram atualizados automaticamente")
            logger.info("✅ Histórico foi registrado corretamente")
            logger.info("✅ Logs de mudança foram criados")
            return True
        else:
            logger.warning("⚠️ Sistema funcionando, mas triggers podem não ter executado")
            return False
        
    except Exception as e:
        logger.error(f"❌ Erro na demonstração: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal."""
    sucesso = demonstrar_propagacao_automatica()
    
    if sucesso:
        logger.info("\n🎯 CONCLUSÃO:")
        logger.info("O Sistema de Herança Dinâmica de Jornadas está TOTALMENTE OPERACIONAL!")
        logger.info("As jornadas dos funcionários SEMPRE obedecerão à empresa onde estão alocados.")
        logger.info("Mudanças nos horários da empresa são propagadas AUTOMATICAMENTE.")
        logger.info("Todo o histórico de mudanças é registrado COMPLETAMENTE.")
    else:
        logger.error("\n❌ Problemas detectados na demonstração")
    
    return sucesso

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
