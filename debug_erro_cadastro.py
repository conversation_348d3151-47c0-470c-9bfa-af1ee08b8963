#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para debugar o erro "Erro ao processar dados do funcionário"
Simula o processo de cadastro para identificar a causa raiz
"""

import sys
import os
import traceback
from datetime import datetime

# Adicionar o diretório do projeto ao path
sys.path.insert(0, '/var/www/controle-ponto')

try:
    from utils.database import DatabaseManager
    from utils.helpers import FormValidator
    from app_funcionarios import (
        _validar_dados_funcionario, 
        _processar_dados_funcionario,
        _criar_funcionario,
        REQUIRED_FIELDS,
        OPTIONAL_FIELDS_WITH_DEFAULTS
    )
    print("✅ Módulos importados com sucesso")
except ImportError as e:
    print(f"❌ Erro ao importar módulos: {e}")
    sys.exit(1)

def criar_dados_teste():
    """Cria dados de teste válidos para cadastro"""
    return {
        # Dados pessoais obrigatórios
        'nome_completo': 'JOÃO DA SILVA TESTE',
        'cpf': f'111.222.333-{datetime.now().strftime("%S")}',  # CPF único baseado no segundo
        'rg': '12.345.678-9',
        'data_nascimento': '1990-01-01',
        'sexo': 'M',
        'estado_civil': 'Solteiro',
        'nacionalidade': 'Brasileira',
        
        # Documentos trabalhistas (opcionais)
        'ctps_numero': '1234567',
        'ctps_serie_uf': '001/MG',
        'pis_pasep': '123.45678.90-1',
        
        # Endereço
        'endereco_cep': '30000-000',
        'endereco_estado': 'MG',
        'endereco_rua': 'RUA TESTE, 123',
        'endereco_bairro': 'CENTRO',
        'endereco_cidade': 'BELO HORIZONTE',
        
        # Contato
        'telefone1': '(31) 99999-9999',
        'telefone2': '(31) 3333-4444',
        'email': '<EMAIL>',
        
        # Dados profissionais
        'empresa_id': 11,  # ID da empresa principal
        'cargo': 'ANALISTA DE TESTE',
        'setor_obra': 'DESENVOLVIMENTO',
        'matricula_empresa': f'TEST{datetime.now().strftime("%H%M%S")}',
        'data_admissao': '2025-01-01',
        'tipo_contrato': 'CLT',
        'status_cadastro': 'Ativo',
        
        # Configurações
        'horas_semanais_obrigatorias': 44.00,
        'nivel_acesso': 'Funcionario',
        'banco_horas': True,
        'hora_extra': True,
        
        # Campos opcionais
        'tolerancia_ponto': 5,
        'turno': 'Diurno',

        # Não incluir EPIs no teste para evitar problemas
        'epis': []
    }

def testar_validacao():
    """Testa a validação de dados"""
    print("\n🔍 TESTE 1: Validação de Dados")
    print("-" * 50)
    
    try:
        dados = criar_dados_teste()
        validator = FormValidator()
        
        print(f"📊 Dados criados: {len(dados)} campos")
        print(f"📋 Campos obrigatórios: {len(REQUIRED_FIELDS)}")
        
        # Executar validação
        _validar_dados_funcionario(dados, validator)
        
        if validator.has_errors():
            print("❌ ERROS DE VALIDAÇÃO ENCONTRADOS:")
            for field, errors in validator.get_errors().items():
                print(f"  {field}: {errors}")
            return False
        else:
            print("✅ Validação passou sem erros")
            return True
            
    except Exception as e:
        print(f"❌ ERRO na validação: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def testar_processamento():
    """Testa o processamento de dados"""
    print("\n🔍 TESTE 2: Processamento de Dados")
    print("-" * 50)
    
    try:
        dados = criar_dados_teste()
        
        print(f"📊 Dados de entrada: {len(dados)} campos")
        
        # Executar processamento
        dados_processados = _processar_dados_funcionario(dados)
        
        if dados_processados:
            print(f"✅ Processamento concluído: {len(dados_processados)} campos")
            print(f"📋 Campos processados: {list(dados_processados.keys())[:10]}...")
            return dados_processados
        else:
            print("❌ Processamento retornou dados vazios")
            return None
            
    except Exception as e:
        print(f"❌ ERRO no processamento: {e}")
        print(f"   Tipo: {type(e).__name__}")
        print(f"   Traceback: {traceback.format_exc()}")
        return None

def testar_criacao(dados_processados):
    """Testa a criação no banco de dados"""
    print("\n🔍 TESTE 3: Criação no Banco")
    print("-" * 50)

    try:
        if not dados_processados:
            print("❌ Dados processados não fornecidos")
            return False

        print(f"📊 Dados para inserção: {len(dados_processados)} campos")

        # Remover campos que podem causar problemas de contexto
        dados_teste = dados_processados.copy()
        dados_teste.pop('foto_3x4', None)  # Remover foto para evitar problemas de contexto
        dados_teste.pop('epis', None)      # Remover EPIs para evitar problemas

        print(f"📊 Dados limpos para teste: {len(dados_teste)} campos")

        # Executar criação
        funcionario_id = _criar_funcionario(dados_teste)

        if funcionario_id:
            print(f"✅ Funcionário criado com ID: {funcionario_id}")

            # Limpar teste
            try:
                DatabaseManager.execute_query(
                    "DELETE FROM funcionarios WHERE id = %s",
                    (funcionario_id,),
                    fetch_all=False
                )
                print(f"🧹 Funcionário de teste removido (ID: {funcionario_id})")
            except Exception as cleanup_error:
                print(f"⚠️ Erro ao limpar teste: {cleanup_error}")

            return True
        else:
            print("❌ Criação não retornou ID")
            return False

    except Exception as e:
        print(f"❌ ERRO na criação: {e}")
        print(f"   Tipo: {type(e).__name__}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def testar_conexao_banco():
    """Testa a conexão com o banco de dados"""
    print("\n🔍 TESTE 0: Conexão com Banco")
    print("-" * 50)
    
    try:
        # Teste simples de conexão
        result = DatabaseManager.execute_query("SELECT 1 as test", fetch_one=True)
        if result and result.get('test') == 1:
            print("✅ Conexão com banco funcionando")
            return True
        else:
            print("❌ Conexão com banco falhou")
            return False
    except Exception as e:
        print(f"❌ ERRO de conexão: {e}")
        return False

def main():
    """Função principal de debug"""
    print("🔧 DEBUG DO ERRO DE CADASTRO DE FUNCIONÁRIOS")
    print("=" * 60)
    print(f"⏰ Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Teste 0: Conexão
    if not testar_conexao_banco():
        print("\n❌ FALHA CRÍTICA: Problema de conexão com banco")
        return
    
    # Teste 1: Validação
    if not testar_validacao():
        print("\n❌ FALHA: Problema na validação")
        return
    
    # Teste 2: Processamento
    dados_processados = testar_processamento()
    if not dados_processados:
        print("\n❌ FALHA: Problema no processamento")
        return
    
    # Teste 3: Criação
    if not testar_criacao(dados_processados):
        print("\n❌ FALHA: Problema na criação")
        return
    
    print("\n🎉 TODOS OS TESTES PASSARAM!")
    print("✅ O sistema de cadastro está funcionando corretamente")
    print("\n💡 PRÓXIMOS PASSOS:")
    print("   1. Verificar logs em tempo real durante tentativa de cadastro")
    print("   2. Analisar dados específicos do formulário que está falhando")
    print("   3. Verificar se há problemas de encoding ou caracteres especiais")

if __name__ == "__main__":
    main()
