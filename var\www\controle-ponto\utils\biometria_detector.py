"""
Módulo de Detecção Real de Dispositivos Biométricos
Sistema: RLPONTO-WEB v1.0
Desenvolvido por: <PERSON> - AiNexus Tecnologia
Data: 12/06/2025

Implementa detecção real de leitores biométricos usando lsusb no Linux
Baseado na detecção bem-sucedida do ZK4500 (VID:1B55 PID:0840)
"""

import subprocess
import json
import re
import logging
import platform
from typing import Dict, List, Optional

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BiometricDeviceDetector:
    """
    Detector real de dispositivos biométricos para Linux
    Usa lsusb para detectar dispositivos USB biométricos
    """
    
    def __init__(self):
        self.known_vendors = {
            '1b55': 'ZKTeco Inc.',
            '05e3': 'Genesys Logic',
            '046d': 'Logitech',
            '27c6': 'Goodix Technology',
            '138a': 'Validity Sensors',
            '04f3': 'Elan Microelectronics'
        }
        
        self.known_devices = {
            '1b55:0840': {
                'name': 'ZK4500 Fingerprint Reader',
                'type': 'fingerprint',
                'manufacturer': 'ZKTeco Inc.',
                'supported': True
            },
            '1b55:0841': {
                'name': 'ZK4500 (Alternative)',
                'type': 'fingerprint', 
                'manufacturer': 'ZKTeco Inc.',
                'supported': True
            }
        }

    def detect_biometric_devices(self) -> List[Dict]:
        """
        Detecta dispositivos biométricos usando lsusb no Linux
        Procura especificamente pelo ZK4500 (VID:1B55 PID:0840)
        """
        devices = []
        
        logger.info(f"🐧 [DEBUG LINUX] Iniciando detecção no sistema Linux...")
        logger.info(f"🐧 [DEBUG LINUX] Sistema operacional: {platform.system()}")
        
        try:
            # COMANDO 1: lsusb para listar todos os dispositivos USB
            logger.info(f"🔍 [DEBUG LINUX] Executando: lsusb")
            result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                logger.info(f"✅ [DEBUG LINUX] lsusb executado com sucesso")
                logger.info(f"📋 [DEBUG LINUX] Output lsusb:\n{result.stdout}")
                
                # Procurar por dispositivos ZKTeco (VID: 1b55)
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    logger.info(f"🔍 [DEBUG LINUX] Analisando linha: {line}")
                    
                    # Formato lsusb: Bus 001 Device 002: ID 1b55:0840 ZKTeco Inc ZK4500
                    if '1b55:' in line.lower():
                        logger.info(f"🎯 [DEBUG LINUX] ENCONTROU DISPOSITIVO ZKTECO!")
                        
                        # Extrair informações
                        vid_pid_match = re.search(r'ID\s+([0-9a-f]{4}):([0-9a-f]{4})', line, re.IGNORECASE)
                        if vid_pid_match:
                            vid = vid_pid_match.group(1).lower()
                            pid = vid_pid_match.group(2).lower()
                            
                            logger.info(f"🎯 [DEBUG LINUX] VID: {vid}, PID: {pid}")
                            
                            # Verificar se é ZK4500
                            if vid == '1b55' and pid == '0840':
                                device_info = {
                                    'friendly_name': 'ZK4500 Fingerprint Reader',
                                    'instance_id': f'USB\\VID_{vid.upper()}&PID_{pid.upper()}',
                                    'status': 'OK',
                                    'class': 'Biometric',
                                    'manufacturer': 'ZKTeco Inc.',
                                    'device_type': 'fingerprint',
                                    'vendor_id': vid.upper(),
                                    'product_id': pid.upper(),
                                    'supported': True,
                                    'detection_method': 'Linux_lsusb'
                                }
                                devices.append(device_info)
                                logger.info(f"✅ [DEBUG LINUX] ZK4500 DETECTADO: VID:{vid.upper()} PID:{pid.upper()}")
                            else:
                                # Outros dispositivos ZKTeco
                                device_info = {
                                    'friendly_name': f'ZKTeco Device {vid.upper()}:{pid.upper()}',
                                    'instance_id': f'USB\\VID_{vid.upper()}&PID_{pid.upper()}',
                                    'status': 'OK',
                                    'class': 'Biometric',
                                    'manufacturer': 'ZKTeco Inc.',
                                    'device_type': 'biometric',
                                    'vendor_id': vid.upper(),
                                    'product_id': pid.upper(),
                                    'supported': True,
                                    'detection_method': 'Linux_lsusb'
                                }
                                devices.append(device_info)
                                logger.info(f"✅ [DEBUG LINUX] Dispositivo ZKTeco detectado: {vid.upper()}:{pid.upper()}")
                
                # COMANDO 2: Verificar se há dispositivos em /dev/input para biometria
                logger.info(f"🔍 [DEBUG LINUX] Verificando /dev/input/...")
                try:
                    ls_result = subprocess.run(['ls', '/dev/input/'], capture_output=True, text=True)
                    if ls_result.returncode == 0:
                        logger.info(f"📋 [DEBUG LINUX] Dispositivos em /dev/input/: {ls_result.stdout.strip()}")
                except Exception as e:
                    logger.warning(f"⚠️ [DEBUG LINUX] Erro ao verificar /dev/input/: {e}")
                
                logger.info(f"🎯 [DEBUG LINUX] TOTAL DETECTADO: {len(devices)} dispositivos")
                return devices
            else:
                logger.error(f"❌ [DEBUG LINUX] lsusb falhou: {result.stderr}")
                return []
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ [DEBUG LINUX] Timeout na detecção via lsusb")
            return []
        except FileNotFoundError:
            logger.error(f"❌ [DEBUG LINUX] lsusb não encontrado no sistema")
            return []
        except Exception as e:
            logger.error(f"❌ [DEBUG LINUX] Erro na detecção: {str(e)}")
            return []

    def _parse_device_info(self, device_data: dict, detection_method: str) -> dict:
        """
        Analisa informações do dispositivo
        """
        device_info = {
            'friendly_name': device_data.get('FriendlyName', 'Dispositivo Desconhecido'),
            'status': device_data.get('Status', 'Unknown'),
            'class': device_data.get('Class', 'Unknown'),
            'instance_id': device_data.get('InstanceId', ''),
            'detection_method': detection_method,
            'vendor_id': None,
            'product_id': None,
            'manufacturer': 'Desconhecido',
            'device_type': 'unknown',
            'supported': False
        }

        # Extrair VID e PID do InstanceId
        instance_id = device_info['instance_id']
        vid_match = re.search(r'VID_([0-9A-F]{4})', instance_id, re.IGNORECASE)
        pid_match = re.search(r'PID_([0-9A-F]{4})', instance_id, re.IGNORECASE)
        
        if vid_match:
            device_info['vendor_id'] = vid_match.group(1).upper()
        if pid_match:
            device_info['product_id'] = pid_match.group(1).upper()

        # Identificar ZK4500
        if device_info['vendor_id'] == '1B55' and device_info['product_id'] == '0840':
            device_info.update({
                'device_type': 'fingerprint',
                'supported': True,
                'manufacturer': 'ZKTeco Inc.'
            })

        return device_info

# Instanciar detector
detector = BiometricDeviceDetector()

def detect_biometric_devices():
    """Função pública para detecção de dispositivos"""
    logger.info(f"🚀 [DEBUG] detect_biometric_devices() chamada - Sistema: {platform.system()}")
    return detector.detect_biometric_devices()

def get_device_details(instance_id: str):
    """Obter detalhes de um dispositivo específico"""
    return {"error": "Não implementado no Linux"}

def test_device_communication(instance_id: str):
    """Testar comunicação com dispositivo específico"""
    return {"error": "Teste de comunicação não implementado no Linux"}

if __name__ == "__main__":
    # Teste do detector
    print("🔍 Iniciando detecção de dispositivos biométricos...")
    devices = detect_biometric_devices()
    
    print(f"\n✅ Encontrados {len(devices)} dispositivos:")
    for device in devices:
        print(f"- {device['friendly_name']} ({device['status']})")
        if device['vendor_id'] and device['product_id']:
            print(f"  VID:PID = {device['vendor_id']}:{device['product_id']}")
        print(f"  Suportado: {'✅' if device['supported'] else '❌'}")
        print() 