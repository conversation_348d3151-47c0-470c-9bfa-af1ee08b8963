# ZKFinger SDK for Java

**Version**: 2.0  
**Date**: Sep 2016

## Revision Records

| Date       | Version | Description                              | Author           |
|------------|---------|------------------------------------------|------------------|
| 2016-05-21 | 1.0.0   | Basic version                            | Chen Jianxing    |
| 2016-06-01 | 1.0.1   | Added external image interfaces          | Chen Jianxing    |
| 2016-09-18 | 2.0.0   | Added 2.0 interface, keep old interface  | Chen Jianxing    |

## Contents

1. [Overview of ZKFinger SDK](#overview-of-zkfinger-sdk)
2. [Development Environment Setup](#development-environment-setup)
   - 2.1 [Importing ZKFingerReader.jar](#importing-zkfingerreaderjar)
   - 2.2 [Deploying SDK](#deploying-sdk)
3. [ZKFinger SDK](#zkfinger-sdk)
   - 3.1 [FingerprintSensor.class](#fingerprintsensorclass)
     - 3.1.1 [Init](#init)
     - 3.1.2 [Terminate](#terminate)
     - 3.1.3 [OpenDevice](#opendevice)
     - 3.1.4 [CloseDevice](#closedevice)
     - 3.1.5 [SetParameters](#setparameters)
     - 3.1.6 [GetParameters](#getparameters)
     - 3.1.7 [AcquireFingerprint](#acquirefingerprint)
     - 3.1.8 [AcquireFingerprintImage](#acquirefingerprintimage)
     - 3.1.9 [DBInit](#dbinit)
     - 3.1.10 [DBFree](#dbfree)
     - 3.1.11 [DBAdd](#dbadd)
     - 3.1.12 [DBDel](#dbdel)
     - 3.1.13 [DBCount](#dbcount)
     - 3.1.14 [DBMatch](#dbmatch)
     - 3.1.15 [DBIdentify](#dbidentify)
     - 3.1.16 [DBMerge](#dbmerge)
     - 3.1.17 [ExtractFromImage](#extractfromimage)
     - 3.1.18 [BlobToBase64](#blobtobase64)
     - 3.1.19 [Base64ToBlob](#base64toblob)
4. [Appendixes](#appendixes)
   - 4.1 [Parameter Codes](#parameter-codes)
   - 4.2 [Error Code](#error-code)

## Thank You for Using ZKFinger SDK

Please read this document carefully before use to quickly learn how to use ZKFinger SDK.

### Privacy Policy
No part of this document may be reproduced or transmitted in any form or by any means without prior written consent of ZKTeco. The product described in this manual may include copyrighted software of ZKTeco and possible licensors. Customers shall not reproduce, distribute, modify, decompile, disassemble, decrypt, extract, reverse engineer, lease, assign, or sublicense the said software in any manner, unless such restrictions are prohibited by applicable laws or such actions are approved by respective copyright holders under license.

### Usage Description
As functions of the ZKFinger SDK software are constantly expanded, ZKFinger SDK documentations will be upgraded. Therefore, please read ZKFinger SDK documents carefully when using the ZKFinger SDK software. We apologize for any inconvenience caused by the preceding reasons. You can also contact the authors of the documentations.

**Company**: ZKTech (Xiamen) Software  
**Address**: Room 403-02, No.32, Guanri Road, Phase 2 of Xiamen Software Park  
**Telephone**: 0592-5961369-8023  
**Website**: [www.zkteco.com](http://www.zkteco.com)  
**Email**: [<EMAIL>](mailto:<EMAIL>)

## Overview of ZKFinger SDK

ZKFinger SDK is a set of application programming interfaces (APIs) developed by ZKTeco for development engineers. It is capable of managing ZKTeco fingerprint readers in a unified manner. Development engineers can use functions in different classes to develop Java-based applications.

ZKFinger SDK supports the following functions:
- **FingerprintReaders**: Supports fingerprint capture and algorithm operations, including device initialization, device startup, device shutdown, 1:1 comparison, and 1:N comparison.

## Development Environment Setup

### Importing ZKFingerReader.jar
Open the SDK folder and import `ZKFingerReader.jar` in the `java/lib` directory to the application development tool (the following uses Eclipse as an example).

**Steps**:
1. Create the `lib` directory in the directory of a project.
2. Copy `ZKFingerReader.jar`, right-click the `lib` directory, and choose **Paste** to copy `ZKFingerReader.jar` into the `lib` directory.

**Example Structure**:
```
ZKFinger Demo
├── src
├── JRE System Library [JavaSE-1.7]
├── Referenced Libraries
├── lib
│   ├── ZKFingerReader.jar
│   ├── fingerprint.bmp
```

### Deploying SDK
Install ZKFinger SDK 5.x/ZKOnline SDK 5.x.

## ZKFinger SDK

ZKFinger SDK abstracts function modules as classes. Users can call methods in classes to complete underlying hardware operations and processing of the fingerprint algorithm. ZKFinger SDK includes the fingerprint reader class and algorithm handling class.

| Class Name                        | Type                                    |
|-----------------------------------|-----------------------------------------|
| com.zkteco.biometric.FingerprintSensorEx | Fingerprint reader class, algorithm handling class |

**SDK Package Structure**:
```
ZKFingerReader.jar
└── com.zkteco.biometric
    ├── FingerprintCaptureListener.class
    ├── FingerprintCaptureThread.class
    ├── FingerprintCaptureThreadPool.class
    ├── FingerprintInterface.class
    ├── FingerprintSensor.class
    ├── FingerprintSensorErrorCode.class
    ├── FingerprintSensorEx.class
    ├── ZKFPService.class
```

### FingerprintSensor.class
`FingerprintSensor.class` is a class for controlling fingerprint readers, which can be used to start and shut down a fingerprint reader, verify, and identify.

#### Init
**Function**:  
```java
public static int Init()
```

**Purpose**:  
Initialize resource.

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

#### Terminate
**Function**:  
```java
public static int Terminate()
```

**Purpose**:  
Release resource.

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

#### OpenDevice
**Function**:  
```java
public static long OpenDevice(int index)
```

**Purpose**:  
Connect to a device.

**Parameter Description**:  
- `index`: Device index number. The value is determined based on the total number of connected fingerprint readers.  
  **Example**:  
  - When one fingerprint reader is connected, the index is `0`.  
  - When two fingerprint readers are connected, the index is `0` or `1`.

**Return Value**:  
- `0`: Fail  
- Device handle: Success

#### CloseDevice
**Function**:  
```java
public static int CloseDevice(long devHandle)
```

**Purpose**:  
Disconnect from a device.

**Parameter Description**:  
- `devHandle`: Device handle

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

#### SetParameters
**Function**:  
```java
public static int SetParameters(long devHandle, int code, byte[] paramValue, int size)
```

**Purpose**:  
Set a parameter.

**Parameter Description**:  
- `devHandle`: Device handle  
- `code`: Parameter code (See Appendixes)  
- `paramValue`: Parameter value  
- `size`: Parameter data length

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

**Example**:  
```java
byte[] value = new byte[4];
int len = 4; // sizeof int
int FakeFunOn = 1;
value[0] = FakeFunOn & 0xFF;
value[1] = (FakeFunOn & 0xFF00) >> 8;
value[2] = (FakeFunOn & 0xFF0000) >> 16;
value[3] = (FakeFunOn & 0xFF00000) >> 24;
int ret = SetParameters(2002, value, len); // set FakeFunOn
```

#### GetParameters
**Function**:  
```java
public static int GetParameters(long devHandle, int code, byte[] paramValue, int[] size)
```

**Purpose**:  
Acquire a parameter.

**Parameter Description**:  
- `devHandle`: Device handle  
- `code`: Parameter code (See Appendixes)  
- `paramValue`: Parameter value  
- `size`: Parameter data length

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

**Example**:  
```java
byte[] value = new byte[4];
int[] len = new int[1];
len[0] = 4;
int ret = GetParameters(1, value, len); // image width
if (0 == ret) {
    // convert byte array to int
}
```

#### AcquireFingerprint
**Function**:  
```java
public static int AcquireFingerprint(long devHandle, byte[] imgBuffer, byte[] template, int[] size)
```

**Purpose**:  
Extract a fingerprint image and template.

**Parameter Description**:  
- `devHandle`: Device handle  
- `imgBuffer`: Image data (width * height bytes)  
- `template`: Template data (2048 Bytes)  
- `size`: Length of the returned fingerprint template

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

#### AcquireFingerprintImage
**Function**:  
```java
public static int AcquireFingerprintImage(long devHandle, byte[] imgBuffer)
```

**Purpose**:  
Extract a fingerprint image.

**Parameter Description**:  
- `devHandle`: Device handle  
- `imgBuffer`: Image data (width * height Bytes)

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

#### DBInit
**Function**:  
```java
public static long DBInit()
```

**Purpose**:  
Initialize algorithm library.

**Return Value**:  
- Catch handle

#### DBFree
**Function**:  
```java
public static int DBFree(long dbHandle)
```

**Purpose**:  
Release algorithm library.

**Parameter Description**:  
- `dbHandle`: Catch handle

**Return Value**:  
- Catch handle: Success  
- `0`: Fail

#### DBAdd
**Function**:  
```java
public int DBAdd(long dbHandle, int fid, byte[] regTemplate)
```

**Purpose**:  
Add a registered template to the memory.

**Parameter Description**:  
- `dbHandle`: Catch handle  
- `fid`: Fingerprint ID  
- `regTemplate`: Registered template

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

#### DBDel
**Function**:  
```java
public int DBDel(long dbHandle, int fid)
```

**Purpose**:  
Delete a registered template from the memory.

**Parameter Description**:  
- `dbHandle`: Catch handle  
- `fid`: Fingerprint ID

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

#### DBCount
**Function**:  
```java
public int DBCount(long dbHandle)
```

**Purpose**:  
Acquire the number of fingerprint images in the memory.

**Parameter Description**:  
- `dbHandle`: Catch handle

**Return Value**:  
- `>= 0`: Fingerprint template count  
- `< 0`: See the error code description.

#### DBMatch
**Function**:  
```java
public int DBMatch(long dbHandle, byte[] temp1, byte[] temp2)
```

**Purpose**:  
Compare two fingerprint templates.

**Parameter Description**:  
- `dbHandle`: Catch handle  
- `temp1`: Fingerprint template 1  
- `temp2`: Fingerprint template 2

**Return Value**:  
- Comparison score  
- `< 0`: See the error code description.

#### DBIdentify
**Function**:  
```java
public int DBIdentify(long dbHandle, byte[] template, int[] fid, int[] score)
```

**Purpose**:  
Conduct 1:N comparison.

**Parameter Description**:  
- `dbHandle`: Catch handle  
- `template`: Fingerprint template  
- `fid`: Returned fingerprint ID  
- `score`: Returned comparison score

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

#### DBMerge
**Function**:  
```java
public int DBMerge(long dbHandle, byte[] temp1, byte[] temp2, byte[] temp3, byte[] regTemp, int[] regTempLen)
```

**Purpose**:  
Combine registered fingerprint templates.

**Parameter Description**:  
- `dbHandle`: Catch handle  
- `temp1`: Preregistered template 1  
- `temp2`: Preregistered template 2  
- `temp3`: Preregistered template 3  
- `regTemp`: Returned registered template  
- `regTempLen`: Length of the returned registered template

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

#### ExtractFromImage
**Function**:  
```java
public int ExtractFromImage(long dbHandle, String filePath, int DPI, byte[] template, int[] size)
```

**Purpose**:  
Extract a fingerprint template from a BMP or JPG file.

**Parameter Description**:  
- `dbHandle`: Catch handle  
- `filePath`: Full path of a picture file  
- `DPI`: Image DPI  
- `template`: Returned fingerprint template  
- `size`: Length of the returned fingerprint template

**Return Value**:  
- `0`: Succeeded  
- Others: See the error code description.

**Note**:  
Only the SDK of the standard version supports this function.

#### BlobToBase64
**Function**:  
```java
public static String BlobToBase64(byte[] buf, int cbBuf)
```

**Purpose**:  
Convert byte[] to Base64 string.

**Parameter Description**:  
- `buf`: Blob data  
- `cbBuf`: Data length

**Return Value**:  
- Base64 string

#### Base64ToBlob
**Function**:  
```java
public static int Base64ToBlob(String strBase64, byte[] buf, int cbBuf)
```

**Purpose**:  
Convert Base64 string to byte[].

**Parameter Description**:  
- `strBase64`: Base64 string  
- `buf`: Returned blob data  
- `cbBuf`: The length of buf

**Return Value**:  
- `0`: Fail  
- Length of binary data: Success

## Appendixes

### Parameter Codes

| Parameter Code | Property | Data Type | Description |
|----------------|----------|-----------|-------------|
| 1 | Read-only | Int | Image width |
| 2 | Read-only | Int | Image height |
| 3 | Read-write (supported only by the LIVEID20R currently) | Int | Image DPI (750/1000 is recommended for children) |
| 106 | Read-only | Int | Image data size |
| 1015 | Read-only | 4-byte array | VID&PID (The former two bytes indicate VID and the latter two bytes indicate PID) |
| 2002 | Read-write (supported only by the LIVEID20R currently) | Int | Anti-fake function (1: enable; 0: disable) |
| 2004 | Read-only | Int | A fingerprint image is true if the lower five bits are all 1's (value&31==31) |
| 1101 | Read-only | String | Vendor information |
| 1102 | Read-only | String | Product name |
| 1103 | Read-only | String | Device SN |
| 101 | Write-only (For devices other than the LIVE20R, a function needs to be called to turn off the light) | Int | 1 indicates that the white light blinks; 0 indicates that the white light is off |
| 102 | Write-only (For devices other than the LIVE20R, a function needs to be called to turn off the light) | Int | 1 indicates that the green light blinks; 0 indicates that the green light is off |
| 103 | Write-only (For devices other than the LIVE20R, a function needs to be called to turn off the light) | Int | 1 indicates that the red light blinks; 0 indicates that the red light is off |
| 104 | Write-only (not supported by the LIVE20R) | Int | 1 indicates that buzzing is started; 0 indicates that buzzing is turned off |
| 10001 | Read-write (only supported by ISO/ANSI Version) | Int | 0 ANSI378; 1 ISO 19794-2 |

### Error Code

| Error Code | Description |
|------------|-------------|
| 0 | Succeeded |
| 1 | Initialized |
| -1001 | Failed |
| -1002 | Failed to connect to the device |
| -1003 | Device not connected |
| -1 | Failed to initialize the algorithm library |
| -2 | Failed to initialize the capture library |
| -3 | No device connected |
| -4 | Not supported by the interface |
| -5 | Invalid parameter |
| -6 | Failed to start the device |
| -7 | Invalid handle |
| -8 | Failed to capture the image |
| -9 | Failed to extract the fingerprint template |
| -10 | Suspension operation |
| -11 | Insufficient memory |
| -12 | The fingerprint is being captured |
| -13 | Failed to add the fingerprint template to the memory |
| -14 | Failed to add the fingerprint template |
| -17 | Operation failed |
| -18 | Capture cancelled |
| -20 | Fingerprint comparison failed |
| -22 | Failed to combine registered fingerprint templates |
| -24 | Image processing failed |

**Copyright © 1998-2016 ZKTeco Inc. All rights reserved.**  
[www.zkteco.com](http://www.zkteco.com)